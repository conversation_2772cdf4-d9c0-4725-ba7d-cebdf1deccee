name: Code owner review checker

on:
  pull_request_review:
    types: [ submitted ]
  pull_request:
    types: [ labeled ]

jobs:
  code-owner-review-checker:
    if: ${{ github.event.pull_request.base.ref == 'master' && (github.event.review.state == 'approved' || contains(github.event.pull_request.labels.*.name, 'RunWorkflows')) }}
    runs-on: [ self-hosted, gamma-small ]
    steps:
      - name: Get Token
        id: get_workflow_token
        uses: peter-murray/workflow-application-token-action@dc0413987a085fa17d19df9e47d4677cf81ffef3
        with:
          application_id: ${{ secrets.FI_GITHUB_APP_ID }}
          application_private_key: ${{ secrets.FI_GITHUB_APP_PRIVATE_KEY }}

      # Step 2: Get the list of files changed in the PR using GitHub API
      - name: Get changed files using GitHub API
        id: changes
        run: |
          PR_NUMBER=${{ github.event.pull_request.number }}
          REPO_NAME=${{ github.repository }}
          CHANGED_FILES=$(gh api repos/$REPO_NAME/pulls/$PR_NUMBER/files --paginate --jq '.[].filename')
          CLEANED_CHANGED_FILES=$(echo "$CHANGED_FILES" | tr '\n' ' ')
          echo "changed_files=$CLEANED_CHANGED_FILES" >> $GITHUB_OUTPUT

          # Check if any go.* files are changed
          if echo "$CLEANED_CHANGED_FILES" | grep -q "go\."; then
            echo "go_mod_changed=true" >> $GITHUB_OUTPUT
          else
            echo "go_mod_changed=false" >> $GITHUB_OUTPUT
            exit 0  # Exit successfully since no go.* files were changed
          fi
        env:
          GITHUB_TOKEN: ${{ steps.get_workflow_token.outputs.token }}

      # Step 3: Check if code owner review is required based on go.mod and go.sum changes
      - name: Check if code owner review is required based on go.mod and go.sum changes
        id: review_required
        run: |
          PR_NUMBER=${{ github.event.pull_request.number }}
          REPO_NAME=${{ github.repository }}

          REVIEW_REQUIRED=false

          # Check if go.mod or go.sum file is modified
          if [ "${{ steps.changes.outputs.go_mod_changed }}" = "true" ]; then
            echo "go.mod or go.sum file is modified."
            PATCH=$(gh api repos/$REPO_NAME/pulls/$PR_NUMBER/files --paginate --jq '.[] | select(.filename == "go.mod" or .filename == "go.sum").patch')
            BE_COMMON_CHANGED=false
            OTHER_DEPS_CHANGED=false

            # Loop through each line in the patch to check for changes
            while IFS= read -r line; do
              if [[ "$line" == -* ]] || [[ "$line" == +* ]]; then
                if echo "$line" | grep -q "be-common"; then
                  BE_COMMON_CHANGED=true
                else
                  OTHER_DEPS_CHANGED=true
                fi
              fi
            done <<< "$PATCH"

            # Determine if code owner review is required
            if [[ "$OTHER_DEPS_CHANGED" == true ]]; then
              echo "Other dependencies have been modified. Code owner review required."
              REVIEW_REQUIRED=true
            elif [[ "$BE_COMMON_CHANGED" == true && "$OTHER_DEPS_CHANGED" == false ]]; then
              echo "Only be-common was upgraded. No code owner review required."
            else
              echo "No changes that require code-owner review detected."
            fi
          else
            echo "Neither go.mod nor go.sum modified. No code owner review required."
          fi
          echo "review_required=$REVIEW_REQUIRED" >> $GITHUB_OUTPUT
        env:
          GITHUB_TOKEN: ${{ steps.get_workflow_token.outputs.token }}

      # Step 4: Request review if required and not already requested or approved
      - name: Check if review is already requested or approved
        if: steps.review_required.outputs.review_required == 'true'
        run: |
          PR_NUMBER=${{ github.event.pull_request.number }}
          PR_AUTHOR=${{ github.event.pull_request.user.login }}
          TEAM=("neeraj-epifi" "askprasanna" "ihtkas" "vikas1107" "adi69")

          # Get the list of requested reviewers for the PR
          REQUESTED_REVIEWERS=$(gh pr view $PR_NUMBER --repo epiFi/gamma --json reviewRequests --jq '.reviewRequests[].login')

          # Get the list of approvals for the PR
          APPROVED_REVIEWS=$(gh api \
          -H "Accept: application/vnd.github+json" \
          /repos/epiFi/gamma/pulls/"$PR_NUMBER"/reviews \
          --jq '.[] | select(.state == "APPROVED") | .user.login')

          # Loop through the team to check if review is already requested or approved
          for MEMBER in "${TEAM[@]}"; do
            if [ "$MEMBER" = "$PR_AUTHOR" ]; then
              continue
            fi
            if echo "$REQUESTED_REVIEWERS" | grep -qw "$MEMBER"; then
              echo "Review already requested from $MEMBER. No action needed."
            elif echo "$APPROVED_REVIEWS" | grep -qw "$MEMBER"; then
              echo "Review already approved by $MEMBER. No action needed."
            else
              echo "Requesting review from $MEMBER."
               gh api \
                 --method POST \
                 -H "Accept: application/vnd.github+json" \
                 -H "X-GitHub-Api-Version: 2022-11-28" \
                 /repos/epiFi/gamma/pulls/"$PR_NUMBER"/requested_reviewers \
                 -f "reviewers[]=$MEMBER"
            fi
          done
        env:
          GITHUB_TOKEN: ${{ steps.get_workflow_token.outputs.token }}

      # Step 5: Check if any code owner has approved and pass the workflow immediately if so
      - name: Pass workflow if any code owner has approved
        if: steps.review_required.outputs.review_required == 'true'
        run: |
          PR_NUMBER=${{ github.event.pull_request.number }}
          TEAM=("neeraj-epifi" "askprasanna" "ihtkas" "vikas1107" "adi69")
          APPROVED_REVIEWS=$(gh api \
          -H "Accept: application/vnd.github+json" \
          /repos/epiFi/gamma/pulls/"$PR_NUMBER"/reviews \
          --jq '.[] | select(.state == "APPROVED") | .user.login')

          # Check if any team member has approved the PR
          for MEMBER in "${TEAM[@]}"; do
            if echo "$APPROVED_REVIEWS" | grep -qw "$MEMBER"; then
              echo "Review approved by $MEMBER. Workflow will proceed."
              exit 0  # Exit successfully as the review is approved
            fi
          done
          echo "No code owner has approved. Failing the workflow."
          exit 1
        env:
          GITHUB_TOKEN: ${{ steps.get_workflow_token.outputs.token }}
