name: securities.yml

on:
  # Trigger the workflow manually only through UI
  # https://github.blog/changelog/2020-07-06-github-actions-manual-triggers-with-workflow_dispatch/
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true
jobs:
  run_checks:
    uses: ./.github/workflows/base-workflow.yml
    with:
      runner_type: "go.crdb.postgres"
      services: "securities"
      start_postgres: true
      run_test_parallel: true
      lci_stamp_key: "securities"
      paths_to_trigger: |
         impacted_paths:
            - 'gamma/securities/**
            - 'gamma/cmd/securities/config/securities-test.yml'
            - 'gamma/cmd/securities/config/securities-params.yml'
            - 'gamma/db/stocks/fixture.sql'
            - 'gamma/db/stocks/latest.sql'
            - 'gamma/.github/workflows/securities.yml'
            - '!gamma/securities/config/values/securities-{qa,uat,prod,demo,development,staging,teamspace-params}.yml'
    secrets: inherit
