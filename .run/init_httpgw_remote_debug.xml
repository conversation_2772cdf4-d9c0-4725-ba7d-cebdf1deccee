<component name="ProjectRunConfigurationManager">
	<configuration default="false" name="init_httpgw_remote_debug" type="ShConfigurationType" singleton="false">
		<option name="SCRIPT_TEXT" value=""/>
		<option name="INDEPENDENT_SCRIPT_PATH" value="true"/>
		<option name="SCRIPT_PATH" value="$PROJECT_DIR$/scripts/remote_debug/init.sh"/>
		<option name="SCRIPT_OPTIONS" value="setup_app"/>
		<option name="INDEPENDENT_SCRIPT_WORKING_DIRECTORY" value="true"/>
		<option name="SCRIPT_WORKING_DIRECTORY" value="$PROJECT_DIR$"/>
		<option name="INDEPENDENT_INTERPRETER_PATH" value="true"/>
		<option name="INTERPRETER_PATH" value="/bin/zsh"/>
		<option name="INTERPRETER_OPTIONS" value=""/>
		<option name="EXECUTE_IN_TERMINAL" value="false"/>
		<option name="EXECUTE_SCRIPT_FILE" value="true"/>
		<envs>
			<env name="APP" value="httpgw"/>
			<env name="AWS_PROFILE" value="epifi-staging"/>
			<env name="ENVIRONMENT" value="staging"/>
		</envs>
		<method v="2"/>
	</configuration>
</component>
