package balance

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	mockevents "github.com/epifi/be-common/pkg/events/mocks"
	mockLock "github.com/epifi/be-common/pkg/lock/mocks"
	"github.com/epifi/be-common/pkg/queue/mocks"
	payServerConfig "github.com/epifi/gamma/accounts/config"
	"github.com/epifi/gamma/accounts/config/genconf"
	MockDao "github.com/epifi/gamma/accounts/dao/mocks"
	actorPbMock "github.com/epifi/gamma/api/actor/mocks"
	authPbMock "github.com/epifi/gamma/api/auth/mocks"
	bcPbMock "github.com/epifi/gamma/api/bankcust/mocks"
	savingsPbMock "github.com/epifi/gamma/api/savings/mocks"
	userGroupPbMock "github.com/epifi/gamma/api/user/group/mocks"
	userPbMock "github.com/epifi/gamma/api/user/mocks"
	vgSavingsPbMock "github.com/epifi/gamma/api/vendorgateway/openbanking/savings/mocks"
	mockDowntime "github.com/epifi/gamma/pkg/downtime/mocks"
	releaseEvaluatorMocks "github.com/epifi/gamma/pkg/feature/release/mocks"

	"github.com/epifi/gamma/accounts/test"
)

var (
	conf      *payServerConfig.Config
	mockILock *mockLock.MockILock
	genConf   *genconf.Config
)

type mockService struct {
	mockSavingsAccountBalanceDao *MockDao.MockSavingsAccountBalanceDao
	mockSavingsClient            *savingsPbMock.MockSavingsClient
	mockVgSavingsClient          *vgSavingsPbMock.MockSavingsClient
	mockAuthClient               *authPbMock.MockAuthClient
	mockActorClient              *actorPbMock.MockActorClient
	mockBcPbClient               *bcPbMock.MockBankCustomerServiceClient
	mockUserClient               *userPbMock.MockUsersClient
	mockUserGrpClient            *userGroupPbMock.MockGroupClient
	mockLockManager              *mockLock.MockRwLockManager
	mockPublisher                *mocks.MockPublisher
	mockReleaseEvaluator         *releaseEvaluatorMocks.MockIEvaluator
	mockEventsBroker             *mockevents.MockBroker
	mockDownTimeChecker          *mockDowntime.MockIDowntimeCheck
	mockILock                    *mockLock.MockILock
}

// nolint:dogsled
func TestMain(m *testing.M) {
	var teardown func()
	conf, genConf, _, teardown = test.InitTestServer()

	exitCode := m.Run()

	teardown()
	os.Exit(exitCode)
}

func getPaySavingsAccountServiceWithMock(t *testing.T) (*Service, *mockService, func()) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockSavingsAccountBalanceDao := MockDao.NewMockSavingsAccountBalanceDao(ctr)
	mockSavingsClient := savingsPbMock.NewMockSavingsClient(ctr)
	mockVgSavingsClient := vgSavingsPbMock.NewMockSavingsClient(ctr)
	mockAuthClient := authPbMock.NewMockAuthClient(ctr)
	mockActorClient := actorPbMock.NewMockActorClient(ctr)
	mockBankCustomerClient := bcPbMock.NewMockBankCustomerServiceClient(ctr)
	mockUserClient := userPbMock.NewMockUsersClient(ctr)
	mockUserGrpClient := userGroupPbMock.NewMockGroupClient(ctr)
	mockLockManager := mockLock.NewMockRwLockManager(ctr)
	mockILock = mockLock.NewMockILock(ctr)
	mockPublisher := mocks.NewMockPublisher(ctr)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
	mockEventsBroker := mockevents.NewMockBroker(ctr)
	mockDowntimeChecker := mockDowntime.NewMockIDowntimeCheck(ctr)

	mockService := &mockService{
		mockSavingsAccountBalanceDao: mockSavingsAccountBalanceDao,
		mockSavingsClient:            mockSavingsClient,
		mockVgSavingsClient:          mockVgSavingsClient,
		mockAuthClient:               mockAuthClient,
		mockActorClient:              mockActorClient,
		mockBcPbClient:               mockBankCustomerClient,
		mockUserClient:               mockUserClient,
		mockUserGrpClient:            mockUserGrpClient,
		mockLockManager:              mockLockManager,
		mockPublisher:                mockPublisher,
		mockReleaseEvaluator:         mockReleaseEvaluator,
		mockEventsBroker:             mockEventsBroker,
		mockDownTimeChecker:          mockDowntimeChecker,
		mockILock:                    mockILock,
	}
	service := NewService(conf, mockSavingsAccountBalanceDao, mockSavingsClient, mockVgSavingsClient, mockAuthClient, mockActorClient, mockBankCustomerClient, mockUserClient, mockUserGrpClient, mockPublisher, mockLockManager, genConf, mockReleaseEvaluator, mockEventsBroker, mockDowntimeChecker)

	return service, mockService, func() {
		ctr.Finish()
	}
}
