package balance

import (
	"context"
	"errors"
	"fmt"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/jinzhu/now"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	money "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/accounts/config"
	"github.com/epifi/gamma/accounts/config/genconf"
	"github.com/epifi/gamma/accounts/dao"
	accountEvents "github.com/epifi/gamma/accounts/events"
	"github.com/epifi/gamma/accounts/metrics"
	accountTypes "github.com/epifi/gamma/accounts/wire/types"
	saPb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/balance/enums"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	bcPb "github.com/epifi/gamma/api/bankcust"
	consumerPb "github.com/epifi/gamma/api/pay/savings_account/consumer"
	pb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	vgSavingsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	"github.com/epifi/gamma/pkg/downtime"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseConf "github.com/epifi/gamma/pkg/feature/release/config"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

type Service struct {
	saPb.UnimplementedBalanceServer
	savingsAccountBalanceDao dao.SavingsAccountBalanceDao
	savingsClient            pb.SavingsClient
	vgSavingsClient          vgSavingsPb.SavingsClient
	authClient               authPb.AuthClient
	actorClient              actorPb.ActorClient
	bcClient                 bcPb.BankCustomerServiceClient
	userClient               userPb.UsersClient
	userGroupClient          userGroupPb.GroupClient
	config                   *config.Config
	balanceChangeEventPub    accountTypes.BalanceChangeEventPublisher
	lockManager              lock.RwLockManager
	genConf                  *genconf.Config
	evaluator                release.IEvaluator
	eventBroker              events.Broker
	downTimeChecker          downtime.IDowntimeCheck
}

func NewService(
	config *config.Config,
	savingsAccountBalanceDao dao.SavingsAccountBalanceDao,
	savingsClient pb.SavingsClient,
	vgSavingsClient vgSavingsPb.SavingsClient,
	authClient authPb.AuthClient,
	actorClient actorPb.ActorClient,
	bcClient bcPb.BankCustomerServiceClient,
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	balanceChangeEventPub accountTypes.BalanceChangeEventPublisher,
	lockManager lock.RwLockManager,
	genConf *genconf.Config,
	evaluator release.IEvaluator,
	eventBroker events.Broker,
	downTimeChecker downtime.IDowntimeCheck,
) *Service {
	return &Service{
		config:                   config,
		savingsAccountBalanceDao: savingsAccountBalanceDao,
		savingsClient:            savingsClient,
		vgSavingsClient:          vgSavingsClient,
		authClient:               authClient,
		actorClient:              actorClient,
		bcClient:                 bcClient,
		userGroupClient:          userGroupClient,
		userClient:               userClient,
		balanceChangeEventPub:    balanceChangeEventPub,
		lockManager:              lockManager,
		genConf:                  genConf,
		evaluator:                evaluator,
		eventBroker:              eventBroker,
		downTimeChecker:          downTimeChecker,
	}
}

const (
	getBalanceApiV0Version = "v0"
	getBalanceApiV1Version = "v1"
)

var (
	errOpeningBalUnavailable = errors.New("opening balance RPC unavailable")
)

// nolint:funlen
func (s *Service) GetAccountBalance(ctx context.Context, req *saPb.GetAccountBalanceRequest) (*saPb.GetAccountBalanceResponse, error) {
	var (
		res                    = &saPb.GetAccountBalanceResponse{}
		availableBalance       *money.Money
		ledgerBalance          *money.Money
		balanceUpdatedAt       *timestamp.Timestamp
		lienBalance            *money.Money
		freezeRawCode          string
		freezeReason           string
		isBalanceUpdatedFromDb bool
	)

	getAccountReq, err := s.getAccountRequestFromGetBalanceReq(req)
	if err != nil {
		logger.Error(ctx, "error in forming account request", zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetId()))
	}

	account, err := s.getAccount(ctx, getAccountReq)
	if err != nil {
		logger.Error(ctx, "Failed to fetch account", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	savingsAccountBalance, err := s.savingsAccountBalanceDao.GetAccountBalance(ctx, account.GetId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in Getting account Balance from DB", zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if savingsAccountBalance != nil && s.isDataFromDbSufficientForBalance(req.GetDataFreshness(), savingsAccountBalance.GetBalanceFromPartnerUpdatedAt()) {
		res.LedgerBalance = savingsAccountBalance.GetLedgerBalanceFromPartner()
		res.AvailableBalance = savingsAccountBalance.GetAvailableBalanceFromPartner()
		res.LienBalance, err = moneyPkg.Subtract(res.GetLedgerBalance(), res.GetAvailableBalance())
		if err != nil {
			logger.Error(ctx, "error calculating lien balance from db fetched balance", zap.Error(err))
		}
		res.BalanceAt = savingsAccountBalance.GetUpdatedAt()
		res.IsBalanceProvidedFromDb = true
		res.Status = rpc.StatusOk()
		return res, nil
	}

	availableBalance, ledgerBalance, lienBalance, freezeRawCode, freezeReason, balanceUpdatedAt, isBalanceUpdatedFromDb, err = s.fetchLatestSavingsAccountBalanceForActor(ctx, account, savingsAccountBalance, req.GetActorId(), req.GetForceBalanceUpdate())
	if err != nil {
		logger.Error(ctx, "error in getting balance from vendor", zap.String(logger.ACCOUNT_ID, account.GetId()), zap.Error(err))
	}

	if err == nil {
		if savingsAccountBalance != nil {
			// Publish balance change event on best effort basis
			goroutine.RunWithDefaultTimeout(ctx, func(fnCtx context.Context) {
				s.publishBalanceUpdateEvent(fnCtx, req.GetActorId(), savingsAccountBalance, availableBalance, ledgerBalance, balanceUpdatedAt)
			})
		}
		res.LedgerBalance = ledgerBalance
		res.AvailableBalance = availableBalance
		res.BalanceAt = balanceUpdatedAt
		res.LienBalance = lienBalance
		res.FreezeRawCode = freezeRawCode
		res.FreezeReason = freezeReason
		res.IsBalanceProvidedFromDb = isBalanceUpdatedFromDb
		res.Status = rpc.StatusOk()
	} else {
		res.LedgerBalance = savingsAccountBalance.GetLedgerBalanceFromPartner()
		res.AvailableBalance = savingsAccountBalance.GetAvailableBalanceFromPartner()
		res.LienBalance, err = moneyPkg.Subtract(savingsAccountBalance.GetLedgerBalanceFromPartner(), savingsAccountBalance.GetAvailableBalanceFromPartner())
		if err != nil {
			logger.Error(ctx, "error calculating lien balance from db fetched balance", zap.Error(err))
		}
		res.BalanceAt = savingsAccountBalance.GetUpdatedAt()
		res.IsBalanceProvidedFromDb = true
		res.Status = rpc.StatusOk()
	}

	return res, nil
}

// publishBalanceUpdateEvent publishes balance update event to a topic
// if there is any difference in the balance we have in the database and balance we get from the vendor
func (s *Service) publishBalanceUpdateEvent(ctx context.Context, actorId string, savingsAccBalance *saPb.SavingsAccountBalances,
	availableBalance, ledgerBalance *money.Money, balUpdatedAt *timestamp.Timestamp) {
	diffBalance, diffBalanceErr := moneyPkg.Subtract(availableBalance, savingsAccBalance.GetAvailableBalanceFromPartner())
	if diffBalanceErr != nil {
		logger.Error(ctx, "error calculating diff balance", zap.Error(diffBalanceErr))
		return
	}
	if moneyPkg.IsZero(diffBalance) {
		logger.Debug(ctx, "not publishing event since there's no diff", zap.Error(diffBalanceErr))
		return
	}
	// publish balance change event
	msgId, pubErr := payPkg.PublishBalanceUpdate(ctx, s.balanceChangeEventPub, &consumerPb.BalanceUpdate{
		ActorId:            actorId,
		AccountId:          savingsAccBalance.GetAccountId(),
		PreviousBalance:    savingsAccBalance.GetAvailableBalanceFromPartner(),
		AvailableBalance:   availableBalance,
		DiffBalance:        diffBalance,
		LedgerBalance:      ledgerBalance,
		BalanceAt:          balUpdatedAt,
		PublishedTimestamp: timestamp.Now(),
	})
	if pubErr != nil {
		logger.Error(ctx, "error publishing balance update event", zap.Error(pubErr))
		return
	}
	// publish rudder event on balance update
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.eventBroker.AddToBatch(
			epificontext.WithEventAttributes(ctx),
			accountEvents.NewBalanceUpdate(
				actorId,
				moneyPkg.IsPositive(diffBalance),
			),
		)
	})

	logger.Debug(ctx, "successfully published balance update event", zap.Any(logger.QUEUE_MESSAGE_ID, msgId))
	return
}

func (s *Service) fetchAndUpdateBalanceFromVendorV0Api(ctx context.Context, account *pb.Account, actorId string, savingsAccountBalance *saPb.SavingsAccountBalances) (*money.Money, *money.Money, *timestamp.Timestamp, error) {
	deviceId, deviceToken, err := s.getDeviceAuthDetails(ctx, actorId)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to get device details for actor id: %s, %v", actorId, err)
	}

	isDownTime, _, downCheckErr := s.downTimeChecker.IsDowntime(ctx, nil)
	if downCheckErr != nil {
		return nil, nil, nil, fmt.Errorf("unable to check csis down time for get balance rpc: %s, %v", account.GetId(), downCheckErr)

	}
	if isDownTime {
		return nil, nil, nil, fmt.Errorf("unable to call GetBalance RPC as downtime is going on: %s", account.GetId())
	}

	balanceRes, err := s.vgSavingsClient.GetBalance(ctx, &vgSavingsPb.GetBalanceRequest{
		Header: &commonvgpb.RequestHeader{Vendor: account.GetPartnerBank()},
		Auth: &header.Auth{
			DeviceId:    deviceId,
			DeviceToken: deviceToken,
		},
		AccountNumber: account.GetAccountNo(),
		PhoneNumber:   account.GetPhoneNumber(),
	})
	if rpcErr := epifigrpc.RPCError(balanceRes, err); rpcErr != nil {
		return nil, nil, nil, fmt.Errorf("error in getting balance through GetBalance api for accountId: %s, %v", account.GetId(), rpcErr)
	}

	err = s.updateBalanceDataInDb(ctx, account.GetId(), balanceRes.GetAvailableBalance(), balanceRes.GetLedgerBalance(), balanceRes.GetBalanceAt(), savingsAccountBalance)
	if err != nil {
		logger.Error(ctx, "error in updating balance data in DB", zap.Error(err), zap.String(logger.ACCOUNT_ID, account.GetId()))
	}

	return balanceRes.GetAvailableBalance(), balanceRes.GetLedgerBalance(), balanceRes.GetBalanceAt(), nil
}

//nolint:unparam
func (s *Service) fetchAndUpdateBalanceFromVendorV1Api(ctx context.Context, account *pb.Account, savingsAccountBalance *saPb.SavingsAccountBalances) (*money.Money, *money.Money, *money.Money, string, string, *timestamp.Timestamp, bool, error) {
	balanceRes, err := s.vgSavingsClient.GetBalanceV1(ctx, &vgSavingsPb.GetBalanceV1Request{
		Header:        &commonvgpb.RequestHeader{Vendor: account.GetPartnerBank()},
		AccountNumber: account.GetAccountNo(),
	})

	if rpcErr := epifigrpc.RPCError(balanceRes, err); rpcErr != nil {
		return nil, nil, nil, "", "", nil, false, fmt.Errorf("error in getting balance through GetBalanceV1 api for accountId: %s, %v", account.GetId(), rpcErr)
	}

	if datetime.IsBefore(balanceRes.GetBalanceAt(), savingsAccountBalance.GetBalanceFromPartnerUpdatedAt()) {
		lienBalance, lienErr := moneyPkg.Subtract(savingsAccountBalance.GetLedgerBalanceFromPartner(), savingsAccountBalance.GetAvailableBalanceFromPartner())
		if lienErr != nil {
			logger.Error(ctx, "error calculating lien balance from db fetched balance", zap.Error(lienErr), zap.String(logger.ACCOUNT_ID, account.GetId()))
		}
		return savingsAccountBalance.GetAvailableBalanceFromPartner(), savingsAccountBalance.GetLedgerBalanceFromPartner(), lienBalance, "", "", savingsAccountBalance.GetBalanceFromPartnerUpdatedAt(), true, nil
	}

	err = s.updateBalanceDataInDb(ctx, account.GetId(), balanceRes.GetAvailableBalance(), balanceRes.GetLedgerBalance(), balanceRes.GetBalanceAt(), savingsAccountBalance)
	if err != nil {
		logger.Error(ctx, "error in updating balance data in DB", zap.Error(err), zap.String(logger.ACCOUNT_ID, account.GetId()))
	}

	return balanceRes.GetAvailableBalance(), balanceRes.GetLedgerBalance(), balanceRes.GetLienBalance(), balanceRes.GetFreezeStatus(), balanceRes.GetFreezeReason(), balanceRes.GetBalanceAt(), false, nil
}

func (s *Service) isDataFromDbSufficientForBalance(dataFreshness enums.DataFreshness, lastUpdatedAt *timestamp.Timestamp) bool {
	var maxStaleness time.Duration
	switch dataFreshness {
	// if balance was updated in last 10 seconds, return from db
	case enums.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME, enums.DataFreshness_DATA_FRESHNESS_UNSPECIFIED:
		maxStaleness = s.config.GetBalanceParams.DataFreshnessNearRealTime
		// maxStaleness = s.config.GetBalanceParams.DataFreshnessNearRealTime
	// if balance was updated in last 60 seconds, return from db
	case enums.DataFreshness_DATA_FRESHNESS_STALE, enums.DataFreshness_HISTORICAL:
		maxStaleness = s.config.GetBalanceParams.DataFreshnessNearRealTime
	// maxStaleness = s.config.GetBalanceParams.DataFreshnessStale
	case enums.DataFreshness_LAST_KNOWN_BALANCE_FROM_DB:
		return true
	default:
		return false
	}

	return time.Since(lastUpdatedAt.AsTime()) <= maxStaleness
}

func (s *Service) getAccountRequestFromGetBalanceReq(req *saPb.GetAccountBalanceRequest) (*pb.GetAccountRequest, error) {
	switch req.GetIdentifier().(type) {
	case *saPb.GetAccountBalanceRequest_Id:
		return &pb.GetAccountRequest{Identifier: &pb.GetAccountRequest_Id{Id: req.GetId()}}, nil
	case *saPb.GetAccountBalanceRequest_ExternalId:
		// TODO(Rahul): Add request in GetBalance for AccountNumBankFilter also
		return &pb.GetAccountRequest{Identifier: &pb.GetAccountRequest_AccountNumBankFilter{
			AccountNumBankFilter: &pb.AccountNumberBankFilter{
				AccountNumber: req.GetExternalId().GetAccountNo(),
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
		}, nil
	default:
		return nil, fmt.Errorf("unexpected identifier in request %v", req)
	}
}

// getDeviceAuthDetails fetches device details for a given actorId
// Returns deviceId, deviceToken, userProfileId and error
func (s *Service) getDeviceAuthDetails(ctx context.Context, actorId string) (deviceId, deviceToken string, err error) {
	getDeviceAuthResponse, err := s.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	switch {
	case err != nil:
		return "", "", fmt.Errorf("error in getting device details for card %v", err)
	case getDeviceAuthResponse.GetStatus().IsRecordNotFound():
		return "", "", fmt.Errorf("device details not found for actorId: %s, status: %s",
			actorId, getDeviceAuthResponse.GetStatus())
	case !getDeviceAuthResponse.Status.IsSuccess():
		return "", "", fmt.Errorf("authClient.GetDeviceAuth() failed for actorId: %s, status: %s",
			actorId, getDeviceAuthResponse.GetStatus())
	default:
		return getDeviceAuthResponse.GetDevice().GetDeviceId(), getDeviceAuthResponse.GetDeviceToken(),
			nil
	}
}

// updateBalanceDataInDb will update available, ledger balance and balance updated at column in DB
func (s *Service) updateBalanceDataInDb(ctx context.Context, accountId string, availableBalance, ledgerBalance *money.Money, balanceAt *timestamp.Timestamp, savingsAccountBalance *saPb.SavingsAccountBalances) error {
	var isEligibleForUpdateInDB bool

	if savingsAccountBalance == nil {
		isEligibleForUpdateInDB = true
	} else if datetime.IsAfter(balanceAt, savingsAccountBalance.GetBalanceFromPartnerUpdatedAt()) {
		isEligibleForUpdateInDB = true
	}

	if isEligibleForUpdateInDB {
		releaseLock, lockErr := s.acquireGetBalanceLock(ctx, accountId)
		if lockErr != nil {
			if errors.Is(lockErr, lock.LockAlreadyAcquired) {
				// If the thread is unable to acquire a lock, assumption is that a parallel call is trying to update
				// the latest balance with the same values
				//
				// Hence, we need not do balance update as they would be redundant
				//
				// Ideally, this handling should be replaced by using request collapser pattern
				logger.Info(ctx, "lock acquired by another thread. Skipping update balance",
					zap.String(logger.ACCOUNT_ID, accountId))
				return nil
			}
			logger.Error(ctx, "failed to acquire lock. Continuing call without lock", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(lockErr))
		}
		defer func() {
			if releaseLock != nil {
				releaseLock()
			}
		}()

		err := s.savingsAccountBalanceDao.Upsert(ctx, &saPb.SavingsAccountBalances{
			AccountId:                   accountId,
			AvailableBalanceFromPartner: availableBalance,
			LedgerBalanceFromPartner:    ledgerBalance,
			BalanceFromPartnerUpdatedAt: balanceAt,
		}, []saPb.SavingsAccountBalancesFieldMask{saPb.SavingsAccountBalancesFieldMask_AVAILABLE_BALANCE_FROM_PARTNER,
			saPb.SavingsAccountBalancesFieldMask_LEDGER_BALANCE_FROM_PARTNER, saPb.SavingsAccountBalancesFieldMask_BALANCE_FROM_PARTNER_UPDATED_AT})

		if err != nil {
			return err
		}
	}

	return nil
}

// GetOpeningBalance returns opening balance for account(s) based on identifiers in the request
// If exact account id or account no + ifsc is passed then opening balance corresponding to the account is returned
// For primary account holder actor is passed as identifier then summation of all the account's opening balance is returned
func (s *Service) GetOpeningBalance(ctx context.Context, req *saPb.GetOpeningBalanceRequest) (*saPb.GetOpeningBalanceResponse, error) {
	var (
		res        = &saPb.GetOpeningBalanceResponse{}
		fetchedAcc *pb.Account
		err        error
	)
	getAccountReq, err := s.getAccountRequestFromOpeningBalanceReq(req)
	if err != nil {
		logger.Error(ctx, "error in forming account request", zap.Error(err), zap.String(logger.ACCOUNT_ID, req.GetId()))
	}

	fetchedAcc, err = s.getAccount(ctx, getAccountReq)
	if err != nil {
		logger.Error(ctx, "Failed to fetch account", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	bal, err := s.getAndUpdateOpeningBalance(ctx, fetchedAcc, req.GetDate(), req.GetActorId())
	if err != nil {
		if rpc.StatusFromError(err).GetCode() == uint32(vgSavingsPb.GetOpeningBalanceResponse_DEVICE_TEMPORARILY_DEACTIVATED) {
			res.Status = rpc.NewStatus(uint32(saPb.GetOpeningBalanceResponse_DEVICE_TEMPORARILY_DEACTIVATED), "", "")
			return res, nil
		}
		logger.Error(ctx, "failed to fetch opening balance", zap.String(logger.ACCOUNT_ID, fetchedAcc.GetId()),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.OpeningBalance = bal
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getAccountRequestFromOpeningBalanceReq(req *saPb.GetOpeningBalanceRequest) (*pb.GetAccountRequest, error) {
	switch req.GetIdentifier().(type) {
	case *saPb.GetOpeningBalanceRequest_Id:
		return &pb.GetAccountRequest{Identifier: &pb.GetAccountRequest_Id{Id: req.GetId()}}, nil
	case *saPb.GetOpeningBalanceRequest_ExternalId:
		return &pb.GetAccountRequest{Identifier: &pb.GetAccountRequest_AccountNumBankFilter{
			AccountNumBankFilter: &pb.AccountNumberBankFilter{
				AccountNumber: req.GetExternalId().GetAccountNo(),
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
		}, nil
	case *saPb.GetOpeningBalanceRequest_PrimaryAccountHolderActor:
		return &pb.GetAccountRequest{Identifier: &pb.GetAccountRequest_ActorId{ActorId: req.GetPrimaryAccountHolderActor()}}, nil
	default:
		return nil, fmt.Errorf("unexpected identifier in request %v", req)
	}
}

func (s *Service) getAccount(ctx context.Context, req *pb.GetAccountRequest) (*pb.Account, error) {
	accountRes, err := s.savingsClient.GetAccount(ctx, req)
	if err != nil || accountRes == nil {
		return nil, fmt.Errorf("error in getting account entity: %w", err)
	}

	return accountRes.GetAccount(), nil
}

// getAndUpdateOpeningBalance returns opening balance for given account on a specified date (as of 00:00:00+5:30)
// returns errOpeningBalUnavailable in case opening balance enquiry api is not available
// nolint:funlen
func (s *Service) getAndUpdateOpeningBalance(ctx context.Context, fetchedAcc *pb.Account, queryDate *date.Date, actorId string) (*money.Money, error) {
	var (
		bom       = now.With(time.Now().In(datetime.IST)).BeginningOfMonth().UTC()
		queryTime = datetime.DateToTime(queryDate, datetime.IST).UTC()
	)

	// opening balance is zero on the month in which account is created
	if fetchedAcc.GetCreatedAt() != nil &&
		fetchedAcc.GetCreatedAt().AsTime().In(datetime.IST).Month() == queryTime.In(datetime.IST).Month() &&
		fetchedAcc.GetCreatedAt().AsTime().In(datetime.IST).Year() == queryTime.In(datetime.IST).Year() {
		logger.Info(ctx, "Account has opened in current month", zap.String(logger.ACCOUNT_ID, fetchedAcc.GetId()))
		return moneyPkg.ZeroINR().GetPb(), nil
	}

	savingsAccountBalance, err := s.savingsAccountBalanceDao.GetAccountBalance(ctx, fetchedAcc.GetId())
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("error in getting savings account balances: %w", err)
	}

	// check and return opening balance for given query date if present already.
	if s.genConf.OpeningBalanceFetchFromDB() && savingsAccountBalance.GetOpeningBalanceInfoFromPartner().GetUpdatedAt().AsTime().Equal(queryTime) {
		return savingsAccountBalance.GetOpeningBalanceInfoFromPartner().GetOpeningBalance(), nil
	}

	deviceId, deviceToken, err := s.getDeviceAuthDetails(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get device details for actor id: %s, %w", actorId, err)
	}

	custInfo, errResp := s.bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor:     fetchedAcc.GetPartnerBank(),
		Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if err = epifigrpc.RPCError(custInfo, errResp); err != nil {
		return nil, fmt.Errorf("failed to get customer details for the user: %s: for partner bank: %s",
			fetchedAcc.GetPrimaryAccountHolder(), fetchedAcc.GetPartnerBank().String())
	}
	req := &vgSavingsPb.GetOpeningBalanceRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: fetchedAcc.GetPartnerBank(),
		},
		AccountNumber:             fetchedAcc.GetAccountNo(),
		CustomerId:                custInfo.GetBankCustomer().GetVendorCustomerId(),
		DeviceId:                  deviceId,
		DeviceToken:               deviceToken,
		PhoneNumber:               fetchedAcc.GetPhoneNumber(),
		Date:                      queryDate,
		BalanceAt:                 datetime.DateToTimestamp(queryDate, datetime.IST),
		IsClosingBalanceUserGroup: true,
	}

	rpcCtx := ctx
	if req.IsClosingBalanceUserGroup {
		var cancel func()
		rpcCtx, cancel = context.WithTimeout(ctx, time.Second*10)
		defer cancel()
	}

	var vgOpeningBalanceErrToBeReturned error
	res, err := s.vgSavingsClient.GetOpeningBalance(rpcCtx, req)

	switch {
	case err != nil:
		vgOpeningBalanceErrToBeReturned = fmt.Errorf("vgSavingsClient.GetOpeningBalance RPC failed: %w", err)
	case res.GetStatus().IsUnavailable():
		vgOpeningBalanceErrToBeReturned = errOpeningBalUnavailable
	case res.GetStatus().GetCode() == uint32(vgSavingsPb.GetOpeningBalanceResponse_DEVICE_TEMPORARILY_DEACTIVATED):
		vgOpeningBalanceErrToBeReturned = rpc.StatusAsError(res.GetStatus())
	case !res.GetStatus().IsSuccess() && !res.GetStatus().IsRecordNotFound():
		vgOpeningBalanceErrToBeReturned = fmt.Errorf("vgSavingsClient.GetOpeningBalance returned unexpected response: %v", res.GetStatus())
	}

	if vgOpeningBalanceErrToBeReturned != nil {
		return nil, vgOpeningBalanceErrToBeReturned
	}

	// Bank EOD closing is subject to external factors. Hence, there is always a possibility of delay bank EOD closing
	// The closing delay margin can be as big as 5-10h in worst cases. Hence, to handle such scenarios we don't want to
	// cache the balance in DB until EOD has finished.
	// e.g. for query time: 01/08/2022 00:15:00+05:30, the expected bank EOD date should be 31/07/2022 00:00:00+05:30
	// However, due to EOD delay it may happen that we receive 30/07/2022 00:00:00+05:30 till EOD closure finished at bank's
	// side. We would want to cache the balance only when EOD data of bank is > 31/07/2022 00:00:00+05:30
	if res.BankEodAt != nil {
		// expected bank EOD = query date - 24h
		expectedBankEOD := now.With(queryTime.In(datetime.IST).AddDate(0, 0, -1)).BeginningOfDay()
		actualBankEOD := res.GetBankEodAt().AsTime().In(datetime.IST)
		// if actualBankEOD < expectedBankEOD then return the older cached balance and wait for next user balance fetch
		// retry to populate the opening balance eventually.
		if actualBankEOD.Before(expectedBankEOD) {
			return fetchedAcc.GetOpeningBalanceInfo().GetOpeningBalance(), nil
		}
	}

	// update opening balance in case beginning of the month has changed and current opening balance corresponds to the
	// the beginning of the month. This balance will then be used to optimized the opening balance calls with Federal
	if savingsAccountBalance.GetOpeningBalanceInfoFromPartner() == nil ||
		(!bom.Equal(savingsAccountBalance.GetOpeningBalanceInfoFromPartner().GetUpdatedAt().AsTime()) && queryTime.Equal(bom)) {
		savingsAccountBalance.OpeningBalanceInfoFromPartner = &saPb.OpeningBalanceInfo{
			OpeningBalance: res.GetOpeningBalance(),
			UpdatedAt:      timestamp.New(queryTime),
		}
		err = s.savingsAccountBalanceDao.Upsert(ctx, savingsAccountBalance, []saPb.SavingsAccountBalancesFieldMask{saPb.SavingsAccountBalancesFieldMask_OPENING_BALANCE_FROM_PARTNER})
		if err != nil {
			logger.Error(ctx, "failed to cache opening balance", logger.AccountId(fetchedAcc.GetId()), zap.Error(err))
		}
	}

	return res.GetOpeningBalance(), nil
}

func (s *Service) acquireGetBalanceLock(ctx context.Context, accountId string) (func(), error) {
	lk, err := s.lockManager.Lock(ctx, s.config.GetBalanceParams.LockPrefix+accountId, s.config.GetBalanceParams.LockLeaseDuration)
	if err != nil {
		return nil, fmt.Errorf("failed to acquire write lock in GetAccountBalance: %w", err)
	}

	return func() {
		relErr := lk.Release(ctx)
		if relErr != nil {
			logger.Error(ctx, "failed to release read lock in GetAccountBalance", zap.Error(relErr))
		}
	}, nil
}

func (s *Service) fetchLatestSavingsAccountBalanceForActor(ctx context.Context, account *pb.Account, savingsAccountBalance *saPb.SavingsAccountBalances, actorId string, balanceUpdate saPb.ForceBalanceUpdate) (*money.Money, *money.Money, *money.Money, string, string, *timestamp.Timestamp, bool, error) {
	var (
		availableBalance *money.Money
		ledgerBalance    *money.Money
		lienBalance      *money.Money
		freezeRawCode    string
		freezeReason     string
		balanceUpdatedAt *timestamp.Timestamp
		err              error
		// in fetchAndUpdateBalanceFromVendorV1Api method we are fetching from db if we are not getting balance from the vendor,
		// so created a flag so that we can send in response to the client if the balance is fetched from db or vendor
		isBalanceUpdatedFromDb              bool
		balanceUpdateSourceVendorApiVersion string
	)
	if balanceUpdate == saPb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_NEEDED {
		availableBalance, ledgerBalance, _, err = s.fetchAndUpdateBalanceFromVendorV0Api(ctx, account, actorId, savingsAccountBalance)
		if err != nil {
			logger.Error(ctx, "error in getting balance from GetBalance API", zap.Error(err), zap.String(logger.ACCOUNT_ID, account.GetId()))
			availableBalance, ledgerBalance, lienBalance, freezeRawCode, freezeReason, _, isBalanceUpdatedFromDb, err = s.fetchAndUpdateBalanceFromVendorV1Api(ctx, account, savingsAccountBalance)
			balanceUpdateSourceVendorApiVersion = getBalanceApiV1Version
			if err != nil {
				return nil, nil, nil, "", "", nil, false, fmt.Errorf("error in getting balance from GetBalanceV1 API: %s: %w", account.GetId(), err)
			}
		} else {
			lienBalance, err = moneyPkg.Subtract(ledgerBalance, availableBalance)
			if err != nil {
				logger.Error(ctx, "error calculating lien balance from vendor fetched balance", zap.Error(err))
			}
			balanceUpdateSourceVendorApiVersion = getBalanceApiV0Version
		}

		// Here, we will check if there is any difference between balance from GetBalance and GetBalanceV1 and will publish a
		// rudder event if any difference is found.
		// We will be only publishing event if we are fetching balance from v0 api because originally v1 API will be called when
		// we get error from v0 API. So, there is very high chance that we will get error 2nd time as well to find balance difference
		// from 2 APIs as this calls will be made with a difference of milliseconds.
		if balanceUpdateSourceVendorApiVersion == getBalanceApiV0Version {
			//nocustomlint:goroutine
			go s.checkAndPublishEventForBalanceDiffBwVendorApis(epificontext.CloneCtx(ctx), actorId, balanceUpdateSourceVendorApiVersion, availableBalance, account, accountEvents.BalanceDiffDueToForceUpdate)
		}
	} else {
		availableBalance, ledgerBalance, lienBalance, freezeRawCode, freezeReason, _, isBalanceUpdatedFromDb, err = s.fetchAndUpdateBalanceFromVendorV1Api(ctx, account, savingsAccountBalance)
		balanceUpdateSourceVendorApiVersion = getBalanceApiV1Version
		if err != nil {
			logger.Error(ctx, "error in getting balance from GetBalanceV1 API", zap.Error(err), zap.String(logger.ACCOUNT_ID, account.GetId()))
			availableBalance, ledgerBalance, balanceUpdatedAt, err = s.fetchAndUpdateBalanceFromVendorV0Api(ctx, account, actorId, savingsAccountBalance)
			if err != nil {
				return nil, nil, nil, "", "", nil, false, fmt.Errorf("error in getting balance from GetBalance API: %s: %w", account.GetId(), err)
			}
			lienBalance, err = moneyPkg.Subtract(ledgerBalance, availableBalance)
			if err != nil {
				logger.Error(ctx, "error calculating lien balance from vendor fetched balance", zap.Error(err))
			}
			balanceUpdateSourceVendorApiVersion = getBalanceApiV0Version
		}
	}

	if balanceUpdateSourceVendorApiVersion == getBalanceApiV1Version {
		// we will call balanceV0 api to evaluate the diff between two vendors(v0 & v1) API.
		goroutine.RunWithCtx(epificontext.CloneCtx(ctx), func(ctx context.Context) {
			s.getAccountBalanceFromVendorV0AndCompare(ctx, actorId, account, availableBalance, savingsAccountBalance, balanceUpdatedAt, accountEvents.BalanceDiffDueToInternalEvaluation)
		})
	}
	// as vendor send last txn time here at balanceAt field,
	// so changing it to time.now(), as this is our expected behaviour in current flow
	balanceUpdatedAt = timestamp.New(time.Now())
	return availableBalance, ledgerBalance, lienBalance, freezeRawCode, freezeReason, balanceUpdatedAt, isBalanceUpdatedFromDb, nil
}

func (s *Service) checkAndPublishEventForBalanceDiffBwVendorApis(ctx context.Context, actorId, balanceUpdateSourceVendorApiVersion string,
	balanceFetchedFromSourceVendorAPI *money.Money, userAccount *pb.Account, provenance accountEvents.BalanceUpdateProvenance) {
	var (
		fetchedBalanceFromOtherVendorApi *money.Money
		balanceUpdatedV1Time             *timestamp.Timestamp
		getBalanceV1Resp                 *vgSavingsPb.GetBalanceV1Response
		err                              error
	)

	// nolint:gocritic
	switch balanceUpdateSourceVendorApiVersion {
	case getBalanceApiV0Version:
		getBalanceV1Resp, err = s.vgSavingsClient.GetBalanceV1(ctx, &vgSavingsPb.GetBalanceV1Request{
			Header:        &commonvgpb.RequestHeader{Vendor: userAccount.GetPartnerBank()},
			AccountNumber: userAccount.GetAccountNo(),
		})
		if rpcErr := epifigrpc.RPCError(getBalanceV1Resp, err); rpcErr != nil {
			logger.Error(ctx, "unable to fetch balance for GetBalanceV1 API", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return
		}
		fetchedBalanceFromOtherVendorApi = getBalanceV1Resp.GetAvailableBalance()
		balanceUpdatedV1Time = getBalanceV1Resp.GetBalanceAt()
	}

	diffBalance, diffBalanceErr := moneyPkg.Subtract(balanceFetchedFromSourceVendorAPI, fetchedBalanceFromOtherVendorApi)
	if diffBalanceErr != nil {
		logger.Error(ctx, "error calculating diff balance", zap.Error(diffBalanceErr))
		return
	}
	if moneyPkg.IsZero(diffBalance) {
		logger.Info(ctx, "not publishing event since there's no diff", zap.String(logger.ACTOR_ID_V2, actorId))
		return
	}

	logger.Info(ctx, "publishing event since there is diff in balance", zap.String(logger.ACTOR_ID_V2, actorId))
	balanceFetchDiffBwVendorApisEvent := accountEvents.NewBalanceFetchDiffBwVendorApis(actorId, balanceUpdatedV1Time, accountEvents.Success, provenance)
	s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), balanceFetchDiffBwVendorApisEvent)
}

// getAccountBalanceFromVendorV0AndCompare will compare the amount and in case of diff , it will publish the counter metrics for diff
// optimize (v0 balance enquiry API)
func (s *Service) getAccountBalanceFromVendorV0AndCompare(ctx context.Context, currentActor string, account *pb.Account, availableBalance *money.Money, savingsAccountBalance *saPb.SavingsAccountBalances, balanceUpdatedAtV1 *timestamp.Timestamp, provenance accountEvents.BalanceUpdateProvenance) {

	// return as we do not want to compare and disabled the comparison feature
	if !s.genConf.Flags().PercentageRollOutForDiffCheckBtwnBalanceApi().IsFeatureEnabled() {
		return
	}

	stickinessConstraintData, err := release.NewStickinessConstraintDataWithStaticConf(&releaseConf.StickinessConstraintConfig{
		RolloutPercentage: s.genConf.Flags().PercentageRollOutForDiffCheckBtwnBalanceApi().StickinessConstraintConfig().RolloutPercentage(),
	})
	if err != nil {
		logger.Error(ctx, "Failed to get sticky constraint data", zap.String(logger.ACTOR_ID_V2, currentActor), zap.Error(err))
		return
	}
	stickinessConstraint := release.NewStickinessConstraint()
	isAllowed, err := stickinessConstraint.Evaluate(ctx, stickinessConstraintData, release.NewCommonConstraintData(types.Feature_FEATURE_UNSPECIFIED).WithActorId(currentActor))
	if err != nil {
		logger.Error(ctx, "Failed to check the percentage roll out for the given actor ", zap.String(logger.ACTOR_ID, currentActor), zap.Error(err))
		return
	}

	// if current actor not qualify the percentage, blocking the call
	if !isAllowed {
		return
	}

	availableBalanceFromV0Api, _, _, err := s.fetchAndUpdateBalanceFromVendorV0Api(ctx, account, currentActor, savingsAccountBalance)
	if err != nil {
		logger.Error(ctx, "error in getting balance from v0 for comparison", zap.String(logger.ACTOR_ID_V2, currentActor), zap.String(logger.ACCOUNT_ID, account.GetId()), zap.Error(err))
		return
	}

	if !moneyPkg.AreEquals(availableBalance, availableBalanceFromV0Api) {
		diff, _ := moneyPkg.Subtract(availableBalanceFromV0Api, availableBalance)
		metrics.IncrementNumberOfBalanceDiffCount(metrics.Difference)
		logger.Info(ctx, "Balance diff b/w v0 and v1 api", zap.String(logger.ACCOUNT_ID, account.GetId()), zap.String("balance_diff", diff.String()))
		logger.SecureInfo(ctx, commonvgpb.Vendor_FEDERAL_BANK, "Balance diff b/w v0 and v1 api", zap.String(logger.ACCOUNT_ID, account.GetId()), zap.String("Balance_from_V1", availableBalance.String()), zap.String("Balance_from_V0", availableBalanceFromV0Api.String()))
		balanceFetchDiffBwVendorApisEvent := accountEvents.NewBalanceFetchDiffBwVendorApis(currentActor, balanceUpdatedAtV1, accountEvents.Success, provenance)
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), balanceFetchDiffBwVendorApisEvent)
		return
	}
	metrics.IncrementNumberOfBalanceDiffCount(metrics.NoDifference)
	return
}
