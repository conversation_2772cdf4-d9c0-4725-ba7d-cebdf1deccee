package developer

import (
	"fmt"

	"github.com/epifi/gamma/accounts/developer/processor"
	"github.com/epifi/gamma/api/accounts/developer"
)

type DevFactory struct {
	savingsAccountBalanceProcessor *processor.SavingsAccountBalanceProcessor
}

func NewDevFactory(
	savingsAccountBalanceProcessor *processor.SavingsAccountBalanceProcessor,
) *DevFactory {
	return &DevFactory{
		savingsAccountBalanceProcessor: savingsAccountBalanceProcessor,
	}
}

// Since it is a single switch statement lint check is ignored to change it to if
//
//nolint:gocritic
func (d *DevFactory) getParameterListProcessor(entity developer.AccountsEntity) (ParameterFetcher, error) {
	switch entity {
	case developer.AccountsEntity_SAVINGS_ACCOUNT_BALANCES:
		return d.savingsAccountBalanceProcessor, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}

// Since it is a single switch statement lint check is ignored to change it to if
//
//nolint:gocritic
func (d *DevFactory) getDataProcessor(entity developer.AccountsEntity) (DataFetcher, error) {
	switch entity {
	case developer.AccountsEntity_SAVINGS_ACCOUNT_BALANCES:
		return d.savingsAccountBalanceProcessor, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}
