package developer

import (
	"context"

	"github.com/epifi/gamma/api/accounts/developer"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
)

type ParameterFetcher interface {
	FetchParamList(ctx context.Context, entity developer.AccountsEntity) ([]*cxDsPb.ParameterMeta, error)
}

type DataFetcher interface {
	FetchData(ctx context.Context, entity developer.AccountsEntity, filters []*cxDsPb.Filter) (string, error)
}
