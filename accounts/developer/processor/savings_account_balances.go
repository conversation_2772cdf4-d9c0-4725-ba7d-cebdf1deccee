package processor

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/proto/json"
	"github.com/epifi/be-common/pkg/storage"
	"github.com/epifi/gamma/accounts/dao"
	devpb "github.com/epifi/gamma/api/accounts/developer"
	"github.com/epifi/gamma/api/cx/developer/db_state"
)

const (
	accountIDKey   = "account_id"
	accountIDLabel = "Account ID"
)

type SavingsAccountBalanceProcessor struct {
	savingsAccountBalanceDao dao.SavingsAccountBalanceDao
}

func NewSavingsAccountBalanceProcessor(savingsAccountBalanceDao dao.SavingsAccountBalanceDao) *SavingsAccountBalanceProcessor {
	return &SavingsAccountBalanceProcessor{
		savingsAccountBalanceDao: savingsAccountBalanceDao,
	}
}

func (p *SavingsAccountBalanceProcessor) FetchParamList(ctx context.Context, entity devpb.AccountsEntity) ([]*db_state.ParameterMeta, error) {
	return []*db_state.ParameterMeta{
		{
			Name:            accountIDKey,
			Label:           accountIDLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}, nil
}

func (p *SavingsAccountBalanceProcessor) FetchData(ctx context.Context, entity devpb.AccountsEntity, filters []*db_state.Filter) (string, error) {
	accountID := ""
	for _, filter := range filters {
		if filter.GetParameterName() == accountIDKey {
			accountID = filter.GetStringValue()
			break
		}
	}

	balance, err := p.savingsAccountBalanceDao.GetAccountBalance(ctx, accountID)
	if err != nil {
		if storage.IsRecordNotFoundError(err) {
			return "[]", nil
		}
		return "", fmt.Errorf("error fetching savings account balance: %w", err)
	}

	balance.AvailableBalanceFromPartner.Units, balance.AvailableBalanceFromPartner.Nanos = 0, 0
	balance.LedgerBalanceFromPartner.Units, balance.LedgerBalanceFromPartner.Nanos = 0, 0

	resp, err := json.Marshal(balance)
	if err != nil {
		return "", err
	}
	return string(resp), nil

}
