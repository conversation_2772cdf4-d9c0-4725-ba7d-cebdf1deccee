//go:build wireinject
// +build wireinject

package wire

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"errors"

	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/lock"

	"github.com/epifi/gamma/accounts/balance"
	healthEnginePb "github.com/epifi/gamma/api/health_engine"
	downtimePkg "github.com/epifi/gamma/pkg/downtime"
	"github.com/epifi/gamma/pkg/feature/release"

	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	"github.com/epifi/be-common/pkg/idgen"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/accounts/config"
	"github.com/epifi/gamma/accounts/config/genconf"
	"github.com/epifi/gamma/accounts/dao"
	"github.com/epifi/gamma/accounts/developer"
	"github.com/epifi/gamma/accounts/developer/processor"
	"github.com/epifi/gamma/accounts/internal/savings"
	"github.com/epifi/gamma/accounts/internal/user"
	"github.com/epifi/gamma/accounts/operstatus"
	"github.com/epifi/gamma/accounts/statement"
	"github.com/epifi/gamma/accounts/statement/consumer"
	"github.com/epifi/gamma/accounts/statement/statement_processor"
	accountsTypes "github.com/epifi/gamma/accounts/wire/types"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	accountsStatmentPb "github.com/epifi/gamma/api/accounts/statement"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	bcPb "github.com/epifi/gamma/api/bankcust"
	depositPb "github.com/epifi/gamma/api/deposit"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	vgAccountPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgSavingsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	docstypes "github.com/epifi/gamma/docs/wire/types"
	"github.com/epifi/gamma/pkg/changefeed"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
)

// config: {"awsClient": "SftpStatementAwsBucket().AwsBucket"}
func InitialiseStatementService(savingsClient savingsPb.SavingsClient, accountStmtPublisher accountsTypes.AccountStmtPublisher,
	userClient userPb.UsersClient, authClient authPb.AuthClient, depositClient depositPb.DepositClient,
	actorClient actorPb.ActorClient, db types.EpifiCRDB, userGrpClient userGroupPb.GroupClient, accountConf *config.Config,
	awsClient accountsTypes.SftpStatementS3Client, accountMonthlyStmtPublisher accountsTypes.AccountMonthlyStmtPublisher,
	vgAccountClient vgAccountPb.AccountsClient, dynAccountConf *genconf.Config, bcClient bcPb.BankCustomerServiceClient) (*statement.Service, error) {
	wire.Build(
		statement.NewService,
		wire.NewSet(statement_processor.NewFederalStatementProcessor, wire.Bind(new(statement_processor.StatementProcessor), new(*statement_processor.FederalStatementProcessor))),
		idgen.NewClock,
		idgen.WireSet,
		savings.WireSet,
		dao.WireSet,
		user.WireSet,
		InitFederalCryptors,
	)
	return &statement.Service{}, nil
}

func InitFederalCryptors(conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {
	// create a pgp cryptor to be used for vendor communication encryption
	pgpCryptor := pgp.New(conf.Secrets.Ids[config.FederalPgpPublicKey],
		conf.Secrets.Ids[config.EpifiFederalPgpPrivateKey], conf.Secrets.Ids[config.EpifiFederalPgpPassphrase])
	if pgpCryptor == nil {
		return nil, errors.New("failed to create PGP cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP, pgpCryptor)

	return cryptorStore, nil
}

// config: {"awsClient": "SftpStatementAwsBucket().AwsBucket"}
func InitialiseStatementConsumerService(
	commsClient accountsTypes.AccountCommsClientWithInterceptors,
	vgAccountClient vgAccountPb.AccountsClient,
	docsClient docstypes.DocsClientWithInterceptors,
	userClient userPb.UsersClient,
	conf *config.Config,
	db types.EpifiCRDB,
	userGrpClient userGroupPb.GroupClient,
	savingsClient savingsPb.SavingsClient,
	accountStatementClient accountsStatmentPb.AccountStatementClient,
	awsClient accountsTypes.SftpStatementS3Client,
	actorClient actorPb.ActorClient,
	celestialClient celestial.CelestialClient,
) (*consumer.StatementConsumerService, error) {
	wire.Build(
		accountsTypes.CommsClientProvider,
		docstypes.DocsClientProvider,
		consumer.NewConsumerService,
		user.WireSet,
		savings.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
		InitFederalCryptors,
	)
	return &consumer.StatementConsumerService{}, nil
}

func InitialiseOperationalStatusService(dynConf *genconf.Config, db types.EpifiCRDB, savClient savingsPb.SavingsClient,
	vgAccountsClient vgAccountPb.AccountsClient, usersClient userPb.UsersClient, operationalStatusPublisher accountsTypes.AccountOperationalStatusPublisher,
	eventBroker events.Broker) *operstatus.Service {
	wire.Build(
		dao.OperationalStatusWireSet,
		changefeed.ChangefeedWireSet,
		operstatus.NewOperationalStatusService,
		types.EpifiCRDBGormDBProvider,
		storage.IdempotentTxnExecutorWireSet,
	)
	return &operstatus.Service{}
}

func InitialiseOperStatusConsumerService(savingClient savingsPb.SavingsClient, accountStatusPub accountsTypes.AccountOperationalStatusPublisher,
	operStatusClient operStatusPb.OperationalStatusServiceClient, dynConf *genconf.Config, db types.EpifiCRDB,
	eventBroker events.Broker) *operstatus.ConsumerService {
	wire.Build(
		dao.OperationalStatusWireSet,
		changefeed.ChangefeedWireSet,
		types.EpifiCRDBGormDBProvider,
		storage.IdempotentTxnExecutorWireSet,
		operstatus.NewConsumerService,
	)
	return &operstatus.ConsumerService{}
}

func InitialiseBalanceService(db types.PayPGDB,
	savingsClient savingsPb.SavingsClient,
	vgSavingsClient vgSavingsPb.SavingsClient,
	authClient authPb.AuthClient,
	actorClient actorPb.ActorClient,
	bcClient bcPb.BankCustomerServiceClient,
	payCacheStorage accountsTypes.PayCacheStorage,
	payRedisClient accountsTypes.PayRedisStore,
	accountConfig *config.Config,
	userGrpClient userGroupPb.GroupClient,
	userClient userPb.UsersClient,
	balanceChangeEventPub accountsTypes.BalanceChangeEventPublisher,
	genConf *genconf.Config,
	eventBroker events.Broker,
	healthEngineClient healthEnginePb.HealthEngineServiceClient,
) *balance.Service {
	wire.Build(
		dao.WireSet,
		balance.NewService,
		accountsTypes.PayCacheStorageProvider,
		accountsTypes.PayRedisStoreProvider,
		release.EvaluatorWireSet,
		FeatureReleaseConfigProvider,
		downtimePkg.CsisDowntimeCheckWireSet,
		lock.DefaultLockMangerWireSet,
	)
	return &balance.Service{}
}

func FeatureReleaseConfigProvider(conf *genconf.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func InitialiseAccountsDevService(db types.PayPGDB,
	payCacheStorage accountsTypes.PayCacheStorage,
	accountConfig *config.Config,
	genConf *genconf.Config,
) *developer.AccountsDevService {
	wire.Build(
		dao.WireSet,
		accountsTypes.PayCacheStorageProvider,
		processor.NewSavingsAccountBalanceProcessor,
		developer.NewDevFactory,
		developer.NewAccountsDevService,
	)
	return &developer.AccountsDevService{}
}
