// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"errors"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/vendorgateway"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/accounts/balance"
	"github.com/epifi/gamma/accounts/config"
	"github.com/epifi/gamma/accounts/config/genconf"
	"github.com/epifi/gamma/accounts/dao"
	"github.com/epifi/gamma/accounts/developer"
	"github.com/epifi/gamma/accounts/developer/processor"
	savings2 "github.com/epifi/gamma/accounts/internal/savings"
	user2 "github.com/epifi/gamma/accounts/internal/user"
	"github.com/epifi/gamma/accounts/operstatus"
	"github.com/epifi/gamma/accounts/statement"
	"github.com/epifi/gamma/accounts/statement/consumer"
	"github.com/epifi/gamma/accounts/statement/statement_processor"
	"github.com/epifi/gamma/accounts/wire/types"
	operstatus2 "github.com/epifi/gamma/api/accounts/operstatus"
	statement2 "github.com/epifi/gamma/api/accounts/statement"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/health_engine"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	savings3 "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	types3 "github.com/epifi/gamma/docs/wire/types"
	"github.com/epifi/gamma/pkg/changefeed"
	"github.com/epifi/gamma/pkg/downtime"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf2 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
)

// Injectors from wire.go:

// config: {"awsClient": "SftpStatementAwsBucket().AwsBucket"}
func InitialiseStatementService(savingsClient savings.SavingsClient, accountStmtPublisher types.AccountStmtPublisher, userClient user.UsersClient, authClient auth.AuthClient, depositClient deposit.DepositClient, actorClient actor.ActorClient, db types2.EpifiCRDB, userGrpClient group.GroupClient, accountConf *config.Config, awsClient types.SftpStatementS3Client, accountMonthlyStmtPublisher types.AccountMonthlyStmtPublisher, vgAccountClient accounts.AccountsClient, dynAccountConf *genconf.Config, bcClient bankcust.BankCustomerServiceClient) (*statement.Service, error) {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	accountStatementRequestDaoCRDB := dao.NewAccountStatementRequestDao(db, domainIdGenerator)
	accountStatementRequestMetadataDaoCRDB := dao.NewAccountStatementRequestMetadataDao(db)
	processor := user2.NewProcessor(userClient, userGrpClient)
	inMemoryCryptorStore, err := InitFederalCryptors(accountConf)
	if err != nil {
		return nil, err
	}
	federalStatementDataProcessor := savings2.NewFederalStatementDataProcessor(awsClient, vgAccountClient, accountConf, inMemoryCryptorStore)
	federalStatementProcessor := statement_processor.NewFederalStatementProcessor(vgAccountClient, depositClient, savingsClient, actorClient, userClient, bcClient, authClient)
	service := statement.NewService(savingsClient, accountStmtPublisher, userClient, authClient, depositClient, actorClient, accountStatementRequestDaoCRDB, accountStatementRequestMetadataDaoCRDB, userGrpClient, dynAccountConf, processor, accountMonthlyStmtPublisher, federalStatementDataProcessor, bcClient, federalStatementProcessor)
	return service, nil
}

// config: {"awsClient": "SftpStatementAwsBucket().AwsBucket"}
func InitialiseStatementConsumerService(commsClient types.AccountCommsClientWithInterceptors, vgAccountClient accounts.AccountsClient, docsClient types3.DocsClientWithInterceptors, userClient user.UsersClient, conf *config.Config, db types2.EpifiCRDB, userGrpClient group.GroupClient, savingsClient savings.SavingsClient, accountStatementClient statement2.AccountStatementClient, awsClient types.SftpStatementS3Client, actorClient actor.ActorClient, celestialClient celestial.CelestialClient) (*consumer.StatementConsumerService, error) {
	commsCommsClient := types.CommsClientProvider(commsClient)
	docsDocsClient := types3.DocsClientProvider(docsClient)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	accountStatementRequestDaoCRDB := dao.NewAccountStatementRequestDao(db, domainIdGenerator)
	accountStatementRequestMetadataDaoCRDB := dao.NewAccountStatementRequestMetadataDao(db)
	processor := user2.NewProcessor(userClient, userGrpClient)
	inMemoryCryptorStore, err := InitFederalCryptors(conf)
	if err != nil {
		return nil, err
	}
	federalStatementDataProcessor := savings2.NewFederalStatementDataProcessor(awsClient, vgAccountClient, conf, inMemoryCryptorStore)
	statementConsumerService := consumer.NewConsumerService(commsCommsClient, vgAccountClient, docsDocsClient, userClient, conf, accountStatementRequestDaoCRDB, accountStatementRequestMetadataDaoCRDB, userGrpClient, savingsClient, accountStatementClient, processor, federalStatementDataProcessor, actorClient, celestialClient)
	return statementConsumerService, nil
}

func InitialiseOperationalStatusService(dynConf *genconf.Config, db types2.EpifiCRDB, savClient savings.SavingsClient, vgAccountsClient accounts.AccountsClient, usersClient user.UsersClient, operationalStatusPublisher types.AccountOperationalStatusPublisher, eventBroker events.Broker) *operstatus.Service {
	gormDB := types2.EpifiCRDBGormDBProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	changeFeed := changefeed.NewChangefeed(gormDB)
	operationalStatusDaoCRDB := dao.NewOperationalStatusDaoCRDB(dynConf, db, crdbIdempotentTxnExecutor, changeFeed)
	service := operstatus.NewOperationalStatusService(dynConf, operationalStatusDaoCRDB, savClient, vgAccountsClient, usersClient, crdbIdempotentTxnExecutor, operationalStatusPublisher, eventBroker)
	return service
}

func InitialiseOperStatusConsumerService(savingClient savings.SavingsClient, accountStatusPub types.AccountOperationalStatusPublisher, operStatusClient operstatus2.OperationalStatusServiceClient, dynConf *genconf.Config, db types2.EpifiCRDB, eventBroker events.Broker) *operstatus.ConsumerService {
	gormDB := types2.EpifiCRDBGormDBProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	changeFeed := changefeed.NewChangefeed(gormDB)
	operationalStatusDaoCRDB := dao.NewOperationalStatusDaoCRDB(dynConf, db, crdbIdempotentTxnExecutor, changeFeed)
	consumerService := operstatus.NewConsumerService(operStatusClient, savingClient, accountStatusPub, operationalStatusDaoCRDB, eventBroker)
	return consumerService
}

func InitialiseBalanceService(db types2.PayPGDB, savingsClient savings.SavingsClient, vgSavingsClient savings3.SavingsClient, authClient auth.AuthClient, actorClient actor.ActorClient, bcClient bankcust.BankCustomerServiceClient, payCacheStorage types.PayCacheStorage, payRedisClient types.PayRedisStore, accountConfig *config.Config, userGrpClient group.GroupClient, userClient user.UsersClient, balanceChangeEventPub types.BalanceChangeEventPublisher, genConf *genconf.Config, eventBroker events.Broker, healthEngineClient health_engine.HealthEngineServiceClient) *balance.Service {
	savingsAccountBalancesDao := dao.NewSavingsAccountBalancesDao(db)
	cacheStorage := types.PayCacheStorageProvider(payCacheStorage)
	savingsAccountBalancesCache := dao.NewSavingsAccountBalancesCache(cacheStorage, genConf, savingsAccountBalancesDao)
	savingsAccountBalanceDao := dao.ProvideSavingsAccountBalancesDao(savingsAccountBalancesDao, savingsAccountBalancesCache, accountConfig)
	client := types.PayRedisStoreProvider(payRedisClient)
	clock := lock.NewRealClockProvider()
	redisRwLock := lock.NewRedisRwLock(client, clock)
	featureReleaseConfig := FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	csisDownTimeCheck := downtime.NewCsisDownTimeCheck(healthEngineClient, clock)
	service := balance.NewService(accountConfig, savingsAccountBalanceDao, savingsClient, vgSavingsClient, authClient, actorClient, bcClient, userClient, userGrpClient, balanceChangeEventPub, redisRwLock, genConf, evaluator, eventBroker, csisDownTimeCheck)
	return service
}

func InitialiseAccountsDevService(db types2.PayPGDB, payCacheStorage types.PayCacheStorage, accountConfig *config.Config, genConf *genconf.Config) *developer.AccountsDevService {
	savingsAccountBalancesDao := dao.NewSavingsAccountBalancesDao(db)
	cacheStorage := types.PayCacheStorageProvider(payCacheStorage)
	savingsAccountBalancesCache := dao.NewSavingsAccountBalancesCache(cacheStorage, genConf, savingsAccountBalancesDao)
	savingsAccountBalanceDao := dao.ProvideSavingsAccountBalancesDao(savingsAccountBalancesDao, savingsAccountBalancesCache, accountConfig)
	savingsAccountBalanceProcessor := processor.NewSavingsAccountBalanceProcessor(savingsAccountBalanceDao)
	devFactory := developer.NewDevFactory(savingsAccountBalanceProcessor)
	accountsDevService := developer.NewAccountsDevService(devFactory)
	return accountsDevService
}

// wire.go:

func InitFederalCryptors(conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {

	pgpCryptor := pgp.New(conf.Secrets.Ids[config.FederalPgpPublicKey],
		conf.Secrets.Ids[config.EpifiFederalPgpPrivateKey], conf.Secrets.Ids[config.EpifiFederalPgpPassphrase])
	if pgpCryptor == nil {
		return nil, errors.New("failed to create PGP cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_PGP, pgpCryptor)

	return cryptorStore, nil
}

func FeatureReleaseConfigProvider(conf *genconf.Config) *genconf2.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}
