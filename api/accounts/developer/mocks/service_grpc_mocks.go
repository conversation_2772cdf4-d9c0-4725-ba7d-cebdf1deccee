// Code generated by MockGen. DO NOT EDIT.
// Source: api/accounts/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAccountsDbStatesClient is a mock of AccountsDbStatesClient interface.
type MockAccountsDbStatesClient struct {
	ctrl     *gomock.Controller
	recorder *MockAccountsDbStatesClientMockRecorder
}

// MockAccountsDbStatesClientMockRecorder is the mock recorder for MockAccountsDbStatesClient.
type MockAccountsDbStatesClientMockRecorder struct {
	mock *MockAccountsDbStatesClient
}

// NewMockAccountsDbStatesClient creates a new mock instance.
func NewMockAccountsDbStatesClient(ctrl *gomock.Controller) *MockAccountsDbStatesClient {
	mock := &MockAccountsDbStatesClient{ctrl: ctrl}
	mock.recorder = &MockAccountsDbStatesClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountsDbStatesClient) EXPECT() *MockAccountsDbStatesClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockAccountsDbStatesClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockAccountsDbStatesClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockAccountsDbStatesClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockAccountsDbStatesClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockAccountsDbStatesClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockAccountsDbStatesClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockAccountsDbStatesClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockAccountsDbStatesClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockAccountsDbStatesClient)(nil).GetParameterList), varargs...)
}

// MockAccountsDbStatesServer is a mock of AccountsDbStatesServer interface.
type MockAccountsDbStatesServer struct {
	ctrl     *gomock.Controller
	recorder *MockAccountsDbStatesServerMockRecorder
}

// MockAccountsDbStatesServerMockRecorder is the mock recorder for MockAccountsDbStatesServer.
type MockAccountsDbStatesServerMockRecorder struct {
	mock *MockAccountsDbStatesServer
}

// NewMockAccountsDbStatesServer creates a new mock instance.
func NewMockAccountsDbStatesServer(ctrl *gomock.Controller) *MockAccountsDbStatesServer {
	mock := &MockAccountsDbStatesServer{ctrl: ctrl}
	mock.recorder = &MockAccountsDbStatesServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountsDbStatesServer) EXPECT() *MockAccountsDbStatesServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockAccountsDbStatesServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockAccountsDbStatesServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockAccountsDbStatesServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockAccountsDbStatesServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockAccountsDbStatesServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockAccountsDbStatesServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockAccountsDbStatesServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockAccountsDbStatesServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockAccountsDbStatesServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeAccountsDbStatesServer is a mock of UnsafeAccountsDbStatesServer interface.
type MockUnsafeAccountsDbStatesServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAccountsDbStatesServerMockRecorder
}

// MockUnsafeAccountsDbStatesServerMockRecorder is the mock recorder for MockUnsafeAccountsDbStatesServer.
type MockUnsafeAccountsDbStatesServerMockRecorder struct {
	mock *MockUnsafeAccountsDbStatesServer
}

// NewMockUnsafeAccountsDbStatesServer creates a new mock instance.
func NewMockUnsafeAccountsDbStatesServer(ctrl *gomock.Controller) *MockUnsafeAccountsDbStatesServer {
	mock := &MockUnsafeAccountsDbStatesServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAccountsDbStatesServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAccountsDbStatesServer) EXPECT() *MockUnsafeAccountsDbStatesServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAccountsDbStatesServer mocks base method.
func (m *MockUnsafeAccountsDbStatesServer) mustEmbedUnimplementedAccountsDbStatesServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAccountsDbStatesServer")
}

// mustEmbedUnimplementedAccountsDbStatesServer indicates an expected call of mustEmbedUnimplementedAccountsDbStatesServer.
func (mr *MockUnsafeAccountsDbStatesServerMockRecorder) mustEmbedUnimplementedAccountsDbStatesServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAccountsDbStatesServer", reflect.TypeOf((*MockUnsafeAccountsDbStatesServer)(nil).mustEmbedUnimplementedAccountsDbStatesServer))
}
