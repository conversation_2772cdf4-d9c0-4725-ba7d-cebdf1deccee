// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/auth/session/service.proto

package session

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetSessionLoginUrlRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSessionLoginUrlRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSessionLoginUrlRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSessionLoginUrlRequestMultiError, or nil if none found.
func (m *GetSessionLoginUrlRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSessionLoginUrlRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetSessionId()) < 1 {
		err := GetSessionLoginUrlRequestValidationError{
			field:  "SessionId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSessionLoginUrlRequestMultiError(errors)
	}

	return nil
}

// GetSessionLoginUrlRequestMultiError is an error wrapping multiple validation
// errors returned by GetSessionLoginUrlRequest.ValidateAll() if the
// designated constraints aren't met.
type GetSessionLoginUrlRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSessionLoginUrlRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSessionLoginUrlRequestMultiError) AllErrors() []error { return m }

// GetSessionLoginUrlRequestValidationError is the validation error returned by
// GetSessionLoginUrlRequest.Validate if the designated constraints aren't met.
type GetSessionLoginUrlRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSessionLoginUrlRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSessionLoginUrlRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSessionLoginUrlRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSessionLoginUrlRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSessionLoginUrlRequestValidationError) ErrorName() string {
	return "GetSessionLoginUrlRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSessionLoginUrlRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSessionLoginUrlRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSessionLoginUrlRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSessionLoginUrlRequestValidationError{}

// Validate checks the field values on GetSessionLoginUrlResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSessionLoginUrlResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSessionLoginUrlResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSessionLoginUrlResponseMultiError, or nil if none found.
func (m *GetSessionLoginUrlResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSessionLoginUrlResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSessionLoginUrlResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSessionLoginUrlResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSessionLoginUrlResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SessionLoginUrl

	if len(errors) > 0 {
		return GetSessionLoginUrlResponseMultiError(errors)
	}

	return nil
}

// GetSessionLoginUrlResponseMultiError is an error wrapping multiple
// validation errors returned by GetSessionLoginUrlResponse.ValidateAll() if
// the designated constraints aren't met.
type GetSessionLoginUrlResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSessionLoginUrlResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSessionLoginUrlResponseMultiError) AllErrors() []error { return m }

// GetSessionLoginUrlResponseValidationError is the validation error returned
// by GetSessionLoginUrlResponse.Validate if the designated constraints aren't met.
type GetSessionLoginUrlResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSessionLoginUrlResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSessionLoginUrlResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSessionLoginUrlResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSessionLoginUrlResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSessionLoginUrlResponseValidationError) ErrorName() string {
	return "GetSessionLoginUrlResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSessionLoginUrlResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSessionLoginUrlResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSessionLoginUrlResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSessionLoginUrlResponseValidationError{}

// Validate checks the field values on CreateSessionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSessionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSessionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSessionRequestMultiError, or nil if none found.
func (m *CreateSessionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSessionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := CreateSessionRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetSessionId()) < 1 {
		err := CreateSessionRequestValidationError{
			field:  "SessionId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateSessionRequestMultiError(errors)
	}

	return nil
}

// CreateSessionRequestMultiError is an error wrapping multiple validation
// errors returned by CreateSessionRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateSessionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSessionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSessionRequestMultiError) AllErrors() []error { return m }

// CreateSessionRequestValidationError is the validation error returned by
// CreateSessionRequest.Validate if the designated constraints aren't met.
type CreateSessionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSessionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSessionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSessionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSessionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSessionRequestValidationError) ErrorName() string {
	return "CreateSessionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSessionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSessionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSessionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSessionRequestValidationError{}

// Validate checks the field values on CreateSessionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSessionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSessionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSessionResponseMultiError, or nil if none found.
func (m *CreateSessionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSessionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSessionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSessionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSessionResponseValidationError{
					field:  "SessionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSessionResponseValidationError{
					field:  "SessionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSessionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSessionResponseValidationError{
				field:  "SessionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateSessionResponseMultiError(errors)
	}

	return nil
}

// CreateSessionResponseMultiError is an error wrapping multiple validation
// errors returned by CreateSessionResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateSessionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSessionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSessionResponseMultiError) AllErrors() []error { return m }

// CreateSessionResponseValidationError is the validation error returned by
// CreateSessionResponse.Validate if the designated constraints aren't met.
type CreateSessionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSessionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSessionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSessionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSessionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSessionResponseValidationError) ErrorName() string {
	return "CreateSessionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSessionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSessionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSessionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSessionResponseValidationError{}

// Validate checks the field values on ValidateSessionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateSessionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateSessionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateSessionRequestMultiError, or nil if none found.
func (m *ValidateSessionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateSessionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetSessionId()) < 1 {
		err := ValidateSessionRequestValidationError{
			field:  "SessionId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ValidateSessionRequestMultiError(errors)
	}

	return nil
}

// ValidateSessionRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateSessionRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateSessionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateSessionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateSessionRequestMultiError) AllErrors() []error { return m }

// ValidateSessionRequestValidationError is the validation error returned by
// ValidateSessionRequest.Validate if the designated constraints aren't met.
type ValidateSessionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateSessionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateSessionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateSessionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateSessionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateSessionRequestValidationError) ErrorName() string {
	return "ValidateSessionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateSessionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateSessionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateSessionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateSessionRequestValidationError{}

// Validate checks the field values on ValidateSessionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateSessionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateSessionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateSessionResponseMultiError, or nil if none found.
func (m *ValidateSessionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateSessionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateSessionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateSessionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSessionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateSessionResponseValidationError{
					field:  "SessionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateSessionResponseValidationError{
					field:  "SessionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSessionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateSessionResponseValidationError{
				field:  "SessionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateSessionResponseMultiError(errors)
	}

	return nil
}

// ValidateSessionResponseMultiError is an error wrapping multiple validation
// errors returned by ValidateSessionResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateSessionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateSessionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateSessionResponseMultiError) AllErrors() []error { return m }

// ValidateSessionResponseValidationError is the validation error returned by
// ValidateSessionResponse.Validate if the designated constraints aren't met.
type ValidateSessionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateSessionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateSessionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateSessionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateSessionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateSessionResponseValidationError) ErrorName() string {
	return "ValidateSessionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateSessionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateSessionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateSessionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateSessionResponseValidationError{}
