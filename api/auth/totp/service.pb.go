// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/auth/totp/service.proto

package totp

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	enums "github.com/epifi/gamma/api/auth/totp/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VerifyTotpResponse_Status int32

const (
	VerifyTotpResponse_STATUS_UNSPECIFIED  VerifyTotpResponse_Status = 0
	VerifyTotpResponse_STATUS_INVALID_CODE VerifyTotpResponse_Status = 101
)

// Enum value maps for VerifyTotpResponse_Status.
var (
	VerifyTotpResponse_Status_name = map[int32]string{
		0:   "STATUS_UNSPECIFIED",
		101: "STATUS_INVALID_CODE",
	}
	VerifyTotpResponse_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED":  0,
		"STATUS_INVALID_CODE": 101,
	}
)

func (x VerifyTotpResponse_Status) Enum() *VerifyTotpResponse_Status {
	p := new(VerifyTotpResponse_Status)
	*p = x
	return p
}

func (x VerifyTotpResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyTotpResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_auth_totp_service_proto_enumTypes[0].Descriptor()
}

func (VerifyTotpResponse_Status) Type() protoreflect.EnumType {
	return &file_api_auth_totp_service_proto_enumTypes[0]
}

func (x VerifyTotpResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyTotpResponse_Status.Descriptor instead.
func (VerifyTotpResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_auth_totp_service_proto_rawDescGZIP(), []int{3, 0}
}

type GenerateTotpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string        `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Purpose enums.Purpose `protobuf:"varint,2,opt,name=purpose,proto3,enum=auth.totp.enums.Purpose" json:"purpose,omitempty"`
}

func (x *GenerateTotpRequest) Reset() {
	*x = GenerateTotpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_totp_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateTotpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateTotpRequest) ProtoMessage() {}

func (x *GenerateTotpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_totp_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateTotpRequest.ProtoReflect.Descriptor instead.
func (*GenerateTotpRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_totp_service_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateTotpRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GenerateTotpRequest) GetPurpose() enums.Purpose {
	if x != nil {
		return x.Purpose
	}
	return enums.Purpose(0)
}

type GenerateTotpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status          *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Totp            string                 `protobuf:"bytes,2,opt,name=totp,proto3" json:"totp,omitempty"`
	ExpiryTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expiry_timestamp,json=expiryTimestamp,proto3" json:"expiry_timestamp,omitempty"`
}

func (x *GenerateTotpResponse) Reset() {
	*x = GenerateTotpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_totp_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateTotpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateTotpResponse) ProtoMessage() {}

func (x *GenerateTotpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_totp_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateTotpResponse.ProtoReflect.Descriptor instead.
func (*GenerateTotpResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_totp_service_proto_rawDescGZIP(), []int{1}
}

func (x *GenerateTotpResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateTotpResponse) GetTotp() string {
	if x != nil {
		return x.Totp
	}
	return ""
}

func (x *GenerateTotpResponse) GetExpiryTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTimestamp
	}
	return nil
}

type VerifyTotpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string        `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Purpose enums.Purpose `protobuf:"varint,2,opt,name=purpose,proto3,enum=auth.totp.enums.Purpose" json:"purpose,omitempty"`
	Totp    string        `protobuf:"bytes,3,opt,name=totp,proto3" json:"totp,omitempty"`
}

func (x *VerifyTotpRequest) Reset() {
	*x = VerifyTotpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_totp_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyTotpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTotpRequest) ProtoMessage() {}

func (x *VerifyTotpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_totp_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTotpRequest.ProtoReflect.Descriptor instead.
func (*VerifyTotpRequest) Descriptor() ([]byte, []int) {
	return file_api_auth_totp_service_proto_rawDescGZIP(), []int{2}
}

func (x *VerifyTotpRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *VerifyTotpRequest) GetPurpose() enums.Purpose {
	if x != nil {
		return x.Purpose
	}
	return enums.Purpose(0)
}

func (x *VerifyTotpRequest) GetTotp() string {
	if x != nil {
		return x.Totp
	}
	return ""
}

type VerifyTotpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *VerifyTotpResponse) Reset() {
	*x = VerifyTotpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_auth_totp_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyTotpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyTotpResponse) ProtoMessage() {}

func (x *VerifyTotpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_auth_totp_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyTotpResponse.ProtoReflect.Descriptor instead.
func (*VerifyTotpResponse) Descriptor() ([]byte, []int) {
	return file_api_auth_totp_service_proto_rawDescGZIP(), []int{3}
}

func (x *VerifyTotpResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_auth_totp_service_proto protoreflect.FileDescriptor

var file_api_auth_totp_service_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x74, 0x6f, 0x74, 0x70, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x61,
	0x75, 0x74, 0x68, 0x2e, 0x74, 0x6f, 0x74, 0x70, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75,
	0x74, 0x68, 0x2f, 0x74, 0x6f, 0x74, 0x70, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x77, 0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54,
	0x6f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3c,
	0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x18, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x74, 0x6f, 0x74, 0x70, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x20, 0x00, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x22, 0x96, 0x01, 0x0a,
	0x14, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x74, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x6f,
	0x74, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x6f, 0x74, 0x70, 0x12, 0x45,
	0x0a, 0x10, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x92, 0x01, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x54, 0x6f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x3c, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x74, 0x6f, 0x74, 0x70, 0x2e, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82,
	0x01, 0x02, 0x20, 0x00, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x12, 0x1b, 0x0a,
	0x04, 0x74, 0x6f, 0x74, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x06, 0x52, 0x04, 0x74, 0x6f, 0x74, 0x70, 0x22, 0x74, 0x0a, 0x12, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x54, 0x6f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x39, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x16, 0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x65,
	0x32, 0xa2, 0x01, 0x0a, 0x04, 0x54, 0x6f, 0x74, 0x70, 0x12, 0x4f, 0x0a, 0x0c, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x74, 0x70, 0x12, 0x1e, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x74, 0x6f, 0x74, 0x70, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x74, 0x6f, 0x74, 0x70, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x49, 0x0a, 0x0a, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x54, 0x6f, 0x74, 0x70, 0x12, 0x1c, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e,
	0x74, 0x6f, 0x74, 0x70, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x6f, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x74, 0x6f,
	0x74, 0x70, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x54, 0x6f, 0x74, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x4c, 0x0a, 0x24, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x74, 0x6f, 0x74, 0x70, 0x5a, 0x24, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x74,
	0x6f, 0x74, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_auth_totp_service_proto_rawDescOnce sync.Once
	file_api_auth_totp_service_proto_rawDescData = file_api_auth_totp_service_proto_rawDesc
)

func file_api_auth_totp_service_proto_rawDescGZIP() []byte {
	file_api_auth_totp_service_proto_rawDescOnce.Do(func() {
		file_api_auth_totp_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_auth_totp_service_proto_rawDescData)
	})
	return file_api_auth_totp_service_proto_rawDescData
}

var file_api_auth_totp_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_auth_totp_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_auth_totp_service_proto_goTypes = []interface{}{
	(VerifyTotpResponse_Status)(0), // 0: auth.totp.VerifyTotpResponse.Status
	(*GenerateTotpRequest)(nil),    // 1: auth.totp.GenerateTotpRequest
	(*GenerateTotpResponse)(nil),   // 2: auth.totp.GenerateTotpResponse
	(*VerifyTotpRequest)(nil),      // 3: auth.totp.VerifyTotpRequest
	(*VerifyTotpResponse)(nil),     // 4: auth.totp.VerifyTotpResponse
	(enums.Purpose)(0),             // 5: auth.totp.enums.Purpose
	(*rpc.Status)(nil),             // 6: rpc.Status
	(*timestamppb.Timestamp)(nil),  // 7: google.protobuf.Timestamp
}
var file_api_auth_totp_service_proto_depIdxs = []int32{
	5, // 0: auth.totp.GenerateTotpRequest.purpose:type_name -> auth.totp.enums.Purpose
	6, // 1: auth.totp.GenerateTotpResponse.status:type_name -> rpc.Status
	7, // 2: auth.totp.GenerateTotpResponse.expiry_timestamp:type_name -> google.protobuf.Timestamp
	5, // 3: auth.totp.VerifyTotpRequest.purpose:type_name -> auth.totp.enums.Purpose
	6, // 4: auth.totp.VerifyTotpResponse.status:type_name -> rpc.Status
	1, // 5: auth.totp.Totp.GenerateTotp:input_type -> auth.totp.GenerateTotpRequest
	3, // 6: auth.totp.Totp.VerifyTotp:input_type -> auth.totp.VerifyTotpRequest
	2, // 7: auth.totp.Totp.GenerateTotp:output_type -> auth.totp.GenerateTotpResponse
	4, // 8: auth.totp.Totp.VerifyTotp:output_type -> auth.totp.VerifyTotpResponse
	7, // [7:9] is the sub-list for method output_type
	5, // [5:7] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_auth_totp_service_proto_init() }
func file_api_auth_totp_service_proto_init() {
	if File_api_auth_totp_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_auth_totp_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateTotpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_totp_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateTotpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_totp_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyTotpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_auth_totp_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyTotpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_auth_totp_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_auth_totp_service_proto_goTypes,
		DependencyIndexes: file_api_auth_totp_service_proto_depIdxs,
		EnumInfos:         file_api_auth_totp_service_proto_enumTypes,
		MessageInfos:      file_api_auth_totp_service_proto_msgTypes,
	}.Build()
	File_api_auth_totp_service_proto = out.File
	file_api_auth_totp_service_proto_rawDesc = nil
	file_api_auth_totp_service_proto_goTypes = nil
	file_api_auth_totp_service_proto_depIdxs = nil
}
