// Code generated by MockGen. DO NOT EDIT.
// Source: api/frontend/auth/totp/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	totp "github.com/epifi/gamma/api/frontend/auth/totp"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockTotpClient is a mock of TotpClient interface.
type MockTotpClient struct {
	ctrl     *gomock.Controller
	recorder *MockTotpClientMockRecorder
}

// MockTotpClientMockRecorder is the mock recorder for MockTotpClient.
type MockTotpClientMockRecorder struct {
	mock *MockTotpClient
}

// NewMockTotpClient creates a new mock instance.
func NewMockTotpClient(ctrl *gomock.Controller) *MockTotpClient {
	mock := &MockTotpClient{ctrl: ctrl}
	mock.recorder = &MockTotpClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTotpClient) EXPECT() *MockTotpClientMockRecorder {
	return m.recorder
}

// GenerateTotp mocks base method.
func (m *MockTotpClient) GenerateTotp(ctx context.Context, in *totp.GenerateTotpRequest, opts ...grpc.CallOption) (*totp.GenerateTotpResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenerateTotp", varargs...)
	ret0, _ := ret[0].(*totp.GenerateTotpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateTotp indicates an expected call of GenerateTotp.
func (mr *MockTotpClientMockRecorder) GenerateTotp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateTotp", reflect.TypeOf((*MockTotpClient)(nil).GenerateTotp), varargs...)
}

// MockTotpServer is a mock of TotpServer interface.
type MockTotpServer struct {
	ctrl     *gomock.Controller
	recorder *MockTotpServerMockRecorder
}

// MockTotpServerMockRecorder is the mock recorder for MockTotpServer.
type MockTotpServerMockRecorder struct {
	mock *MockTotpServer
}

// NewMockTotpServer creates a new mock instance.
func NewMockTotpServer(ctrl *gomock.Controller) *MockTotpServer {
	mock := &MockTotpServer{ctrl: ctrl}
	mock.recorder = &MockTotpServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTotpServer) EXPECT() *MockTotpServerMockRecorder {
	return m.recorder
}

// GenerateTotp mocks base method.
func (m *MockTotpServer) GenerateTotp(arg0 context.Context, arg1 *totp.GenerateTotpRequest) (*totp.GenerateTotpResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateTotp", arg0, arg1)
	ret0, _ := ret[0].(*totp.GenerateTotpResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateTotp indicates an expected call of GenerateTotp.
func (mr *MockTotpServerMockRecorder) GenerateTotp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateTotp", reflect.TypeOf((*MockTotpServer)(nil).GenerateTotp), arg0, arg1)
}

// MockUnsafeTotpServer is a mock of UnsafeTotpServer interface.
type MockUnsafeTotpServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTotpServerMockRecorder
}

// MockUnsafeTotpServerMockRecorder is the mock recorder for MockUnsafeTotpServer.
type MockUnsafeTotpServerMockRecorder struct {
	mock *MockUnsafeTotpServer
}

// NewMockUnsafeTotpServer creates a new mock instance.
func NewMockUnsafeTotpServer(ctrl *gomock.Controller) *MockUnsafeTotpServer {
	mock := &MockUnsafeTotpServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTotpServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTotpServer) EXPECT() *MockUnsafeTotpServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTotpServer mocks base method.
func (m *MockUnsafeTotpServer) mustEmbedUnimplementedTotpServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTotpServer")
}

// mustEmbedUnimplementedTotpServer indicates an expected call of mustEmbedUnimplementedTotpServer.
func (mr *MockUnsafeTotpServerMockRecorder) mustEmbedUnimplementedTotpServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTotpServer", reflect.TypeOf((*MockUnsafeTotpServer)(nil).mustEmbedUnimplementedTotpServer))
}
