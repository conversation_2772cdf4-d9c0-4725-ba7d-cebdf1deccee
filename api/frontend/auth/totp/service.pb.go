// RPCs related to TOTP frontend service

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/auth/totp/service.proto

package totp

import (
	_ "github.com/epifi/be-common/api/rpc"
	header "github.com/epifi/gamma/api/frontend/header"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GenerateTotpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// auth.totp.enums.Purpose.String()
	// eg: NET_WORTH_MCP
	Purpose string `protobuf:"bytes,2,opt,name=purpose,proto3" json:"purpose,omitempty"`
}

func (x *GenerateTotpRequest) Reset() {
	*x = GenerateTotpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_auth_totp_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateTotpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateTotpRequest) ProtoMessage() {}

func (x *GenerateTotpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_auth_totp_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateTotpRequest.ProtoReflect.Descriptor instead.
func (*GenerateTotpRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_auth_totp_service_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateTotpRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GenerateTotpRequest) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

type GenerateTotpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// totp as string. Eg: 123456
	Totp string `protobuf:"bytes,2,opt,name=totp,proto3" json:"totp,omitempty"`
	// Totp expiry timestamp
	ExpiryTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=expiry_timestamp,json=expiryTimestamp,proto3" json:"expiry_timestamp,omitempty"`
}

func (x *GenerateTotpResponse) Reset() {
	*x = GenerateTotpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_auth_totp_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateTotpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateTotpResponse) ProtoMessage() {}

func (x *GenerateTotpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_auth_totp_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateTotpResponse.ProtoReflect.Descriptor instead.
func (*GenerateTotpResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_auth_totp_service_proto_rawDescGZIP(), []int{1}
}

func (x *GenerateTotpResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GenerateTotpResponse) GetTotp() string {
	if x != nil {
		return x.Totp
	}
	return ""
}

func (x *GenerateTotpResponse) GetExpiryTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTimestamp
	}
	return nil
}

var File_api_frontend_auth_totp_service_proto protoreflect.FileDescriptor

var file_api_frontend_auth_totp_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2f, 0x74, 0x6f, 0x74, 0x70, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x74, 0x6f, 0x74, 0x70, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x61, 0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x74, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x75, 0x72,
	0x70, 0x6f, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70,
	0x6f, 0x73, 0x65, 0x22, 0xb3, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x54, 0x6f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x6f, 0x74, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x6f,
	0x74, 0x70, 0x12, 0x45, 0x0a, 0x10, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x32, 0x7f, 0x0a, 0x04, 0x54, 0x6f, 0x74,
	0x70, 0x12, 0x77, 0x0a, 0x0c, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x74,
	0x70, 0x12, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x2e, 0x74, 0x6f, 0x74, 0x70, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54,
	0x6f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x74, 0x6f, 0x74, 0x70, 0x2e,
	0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00,
	0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f,
	0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x2e, 0x74, 0x6f, 0x74, 0x70, 0x5a, 0x2d, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x74, 0x6f, 0x74, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_frontend_auth_totp_service_proto_rawDescOnce sync.Once
	file_api_frontend_auth_totp_service_proto_rawDescData = file_api_frontend_auth_totp_service_proto_rawDesc
)

func file_api_frontend_auth_totp_service_proto_rawDescGZIP() []byte {
	file_api_frontend_auth_totp_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_auth_totp_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_auth_totp_service_proto_rawDescData)
	})
	return file_api_frontend_auth_totp_service_proto_rawDescData
}

var file_api_frontend_auth_totp_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_frontend_auth_totp_service_proto_goTypes = []interface{}{
	(*GenerateTotpRequest)(nil),   // 0: frontend.auth.totp.GenerateTotpRequest
	(*GenerateTotpResponse)(nil),  // 1: frontend.auth.totp.GenerateTotpResponse
	(*header.RequestHeader)(nil),  // 2: frontend.header.RequestHeader
	(*header.ResponseHeader)(nil), // 3: frontend.header.ResponseHeader
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}
var file_api_frontend_auth_totp_service_proto_depIdxs = []int32{
	2, // 0: frontend.auth.totp.GenerateTotpRequest.req:type_name -> frontend.header.RequestHeader
	3, // 1: frontend.auth.totp.GenerateTotpResponse.resp_header:type_name -> frontend.header.ResponseHeader
	4, // 2: frontend.auth.totp.GenerateTotpResponse.expiry_timestamp:type_name -> google.protobuf.Timestamp
	0, // 3: frontend.auth.totp.Totp.GenerateTotp:input_type -> frontend.auth.totp.GenerateTotpRequest
	1, // 4: frontend.auth.totp.Totp.GenerateTotp:output_type -> frontend.auth.totp.GenerateTotpResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_frontend_auth_totp_service_proto_init() }
func file_api_frontend_auth_totp_service_proto_init() {
	if File_api_frontend_auth_totp_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_auth_totp_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateTotpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_auth_totp_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateTotpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_auth_totp_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_auth_totp_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_auth_totp_service_proto_depIdxs,
		MessageInfos:      file_api_frontend_auth_totp_service_proto_msgTypes,
	}.Build()
	File_api_frontend_auth_totp_service_proto = out.File
	file_api_frontend_auth_totp_service_proto_rawDesc = nil
	file_api_frontend_auth_totp_service_proto_goTypes = nil
	file_api_frontend_auth_totp_service_proto_depIdxs = nil
}
