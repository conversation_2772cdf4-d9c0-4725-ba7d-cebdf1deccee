// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/auth/totp/service.proto

package totp

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GenerateTotpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateTotpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateTotpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateTotpRequestMultiError, or nil if none found.
func (m *GenerateTotpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateTotpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateTotpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateTotpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateTotpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Purpose

	if len(errors) > 0 {
		return GenerateTotpRequestMultiError(errors)
	}

	return nil
}

// GenerateTotpRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateTotpRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateTotpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateTotpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateTotpRequestMultiError) AllErrors() []error { return m }

// GenerateTotpRequestValidationError is the validation error returned by
// GenerateTotpRequest.Validate if the designated constraints aren't met.
type GenerateTotpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateTotpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateTotpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateTotpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateTotpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateTotpRequestValidationError) ErrorName() string {
	return "GenerateTotpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateTotpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateTotpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateTotpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateTotpRequestValidationError{}

// Validate checks the field values on GenerateTotpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateTotpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateTotpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateTotpResponseMultiError, or nil if none found.
func (m *GenerateTotpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateTotpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateTotpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateTotpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateTotpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Totp

	if all {
		switch v := interface{}(m.GetExpiryTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateTotpResponseValidationError{
					field:  "ExpiryTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateTotpResponseValidationError{
					field:  "ExpiryTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateTotpResponseValidationError{
				field:  "ExpiryTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GenerateTotpResponseMultiError(errors)
	}

	return nil
}

// GenerateTotpResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateTotpResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateTotpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateTotpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateTotpResponseMultiError) AllErrors() []error { return m }

// GenerateTotpResponseValidationError is the validation error returned by
// GenerateTotpResponse.Validate if the designated constraints aren't met.
type GenerateTotpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateTotpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateTotpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateTotpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateTotpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateTotpResponseValidationError) ErrorName() string {
	return "GenerateTotpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateTotpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateTotpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateTotpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateTotpResponseValidationError{}
