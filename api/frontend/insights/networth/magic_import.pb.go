// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/insights/networth/magic_import.proto

package networth

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12516-8011&t=vjav20xKlpC3t1ui-4
type MagicImportedAssetsListScreen struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Toolbar
	HeaderBar                    *ui.HeaderBar                  `protobuf:"bytes,1,opt,name=header_bar,json=headerBar,proto3" json:"header_bar,omitempty"`
	AssetsSummary                *AssetsSummary                 `protobuf:"bytes,2,opt,name=assets_summary,json=assetsSummary,proto3" json:"assets_summary,omitempty"`
	ImportedAssetsListComponents []*ImportedAssetsListComponent `protobuf:"bytes,3,rep,name=imported_assets_list_components,json=importedAssetsListComponents,proto3" json:"imported_assets_list_components,omitempty"`
	FooterComponent              *FooterComponent               `protobuf:"bytes,4,opt,name=footer_component,json=footerComponent,proto3" json:"footer_component,omitempty"`
}

func (x *MagicImportedAssetsListScreen) Reset() {
	*x = MagicImportedAssetsListScreen{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MagicImportedAssetsListScreen) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagicImportedAssetsListScreen) ProtoMessage() {}

func (x *MagicImportedAssetsListScreen) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagicImportedAssetsListScreen.ProtoReflect.Descriptor instead.
func (*MagicImportedAssetsListScreen) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP(), []int{0}
}

func (x *MagicImportedAssetsListScreen) GetHeaderBar() *ui.HeaderBar {
	if x != nil {
		return x.HeaderBar
	}
	return nil
}

func (x *MagicImportedAssetsListScreen) GetAssetsSummary() *AssetsSummary {
	if x != nil {
		return x.AssetsSummary
	}
	return nil
}

func (x *MagicImportedAssetsListScreen) GetImportedAssetsListComponents() []*ImportedAssetsListComponent {
	if x != nil {
		return x.ImportedAssetsListComponents
	}
	return nil
}

func (x *MagicImportedAssetsListScreen) GetFooterComponent() *FooterComponent {
	if x != nil {
		return x.FooterComponent
	}
	return nil
}

type AssetsSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title             *common.Text             `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	HeroImage         *HeroImage               `protobuf:"bytes,2,opt,name=hero_image,json=heroImage,proto3" json:"hero_image,omitempty"`
	AssetsInfo        *ui.VerticalKeyValuePair `protobuf:"bytes,3,opt,name=assets_info,json=assetsInfo,proto3" json:"assets_info,omitempty"`
	FeedbackComponent *FeedbackComponent       `protobuf:"bytes,4,opt,name=feedback_component,json=feedbackComponent,proto3" json:"feedback_component,omitempty"`
}

func (x *AssetsSummary) Reset() {
	*x = AssetsSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetsSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetsSummary) ProtoMessage() {}

func (x *AssetsSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetsSummary.ProtoReflect.Descriptor instead.
func (*AssetsSummary) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP(), []int{1}
}

func (x *AssetsSummary) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AssetsSummary) GetHeroImage() *HeroImage {
	if x != nil {
		return x.HeroImage
	}
	return nil
}

func (x *AssetsSummary) GetAssetsInfo() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.AssetsInfo
	}
	return nil
}

func (x *AssetsSummary) GetFeedbackComponent() *FeedbackComponent {
	if x != nil {
		return x.FeedbackComponent
	}
	return nil
}

type HeroImage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// this is the background lottie for the hero image
	BgLottie *common.VisualElement `protobuf:"bytes,1,opt,name=bg_lottie,json=bgLottie,proto3" json:"bg_lottie,omitempty"`
	// Frame which gets displayed on top of hero image
	Frame *common.VisualElement `protobuf:"bytes,2,opt,name=frame,proto3" json:"frame,omitempty"`
}

func (x *HeroImage) Reset() {
	*x = HeroImage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HeroImage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeroImage) ProtoMessage() {}

func (x *HeroImage) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeroImage.ProtoReflect.Descriptor instead.
func (*HeroImage) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP(), []int{2}
}

func (x *HeroImage) GetBgLottie() *common.VisualElement {
	if x != nil {
		return x.BgLottie
	}
	return nil
}

func (x *HeroImage) GetFrame() *common.VisualElement {
	if x != nil {
		return x.Frame
	}
	return nil
}

type FeedbackComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ThumbsUp   *FeedbackView `protobuf:"bytes,1,opt,name=thumbs_up,json=thumbsUp,proto3" json:"thumbs_up,omitempty"`
	ThumbsDown *FeedbackView `protobuf:"bytes,2,opt,name=thumbs_down,json=thumbsDown,proto3" json:"thumbs_down,omitempty"`
}

func (x *FeedbackComponent) Reset() {
	*x = FeedbackComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedbackComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackComponent) ProtoMessage() {}

func (x *FeedbackComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackComponent.ProtoReflect.Descriptor instead.
func (*FeedbackComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP(), []int{3}
}

func (x *FeedbackComponent) GetThumbsUp() *FeedbackView {
	if x != nil {
		return x.ThumbsUp
	}
	return nil
}

func (x *FeedbackComponent) GetThumbsDown() *FeedbackView {
	if x != nil {
		return x.ThumbsDown
	}
	return nil
}

type FeedbackView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NormalView   *common.VisualElement `protobuf:"bytes,1,opt,name=normal_view,json=normalView,proto3" json:"normal_view,omitempty"`
	SelectedView *common.VisualElement `protobuf:"bytes,2,opt,name=selected_view,json=selectedView,proto3" json:"selected_view,omitempty"`
}

func (x *FeedbackView) Reset() {
	*x = FeedbackView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeedbackView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeedbackView) ProtoMessage() {}

func (x *FeedbackView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeedbackView.ProtoReflect.Descriptor instead.
func (*FeedbackView) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP(), []int{4}
}

func (x *FeedbackView) GetNormalView() *common.VisualElement {
	if x != nil {
		return x.NormalView
	}
	return nil
}

func (x *FeedbackView) GetSelectedView() *common.VisualElement {
	if x != nil {
		return x.SelectedView
	}
	return nil
}

type ImportedAssetsListComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title                   *common.Text              `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	ImportedAssetsListItems []*ImportedAssetsListItem `protobuf:"bytes,2,rep,name=imported_assets_list_items,json=importedAssetsListItems,proto3" json:"imported_assets_list_items,omitempty"`
	BorderColor             *widget.BackgroundColour  `protobuf:"bytes,3,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	BackgroundColour        *widget.BackgroundColour  `protobuf:"bytes,4,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	DividerColor            *widget.BackgroundColour  `protobuf:"bytes,5,opt,name=divider_color,json=dividerColor,proto3" json:"divider_color,omitempty"`
}

func (x *ImportedAssetsListComponent) Reset() {
	*x = ImportedAssetsListComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportedAssetsListComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportedAssetsListComponent) ProtoMessage() {}

func (x *ImportedAssetsListComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportedAssetsListComponent.ProtoReflect.Descriptor instead.
func (*ImportedAssetsListComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP(), []int{5}
}

func (x *ImportedAssetsListComponent) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ImportedAssetsListComponent) GetImportedAssetsListItems() []*ImportedAssetsListItem {
	if x != nil {
		return x.ImportedAssetsListItems
	}
	return nil
}

func (x *ImportedAssetsListComponent) GetBorderColor() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColor
	}
	return nil
}

func (x *ImportedAssetsListComponent) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *ImportedAssetsListComponent) GetDividerColor() *widget.BackgroundColour {
	if x != nil {
		return x.DividerColor
	}
	return nil
}

type ImportedAssetsListItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetName   *common.Text                       `protobuf:"bytes,1,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`
	AssetValue  *ui.IconTextComponent              `protobuf:"bytes,2,opt,name=asset_value,json=assetValue,proto3" json:"asset_value,omitempty"`
	ImportError *ui.IconTextComponent              `protobuf:"bytes,3,opt,name=import_error,json=importError,proto3" json:"import_error,omitempty"`
	EditDetails *ImportedAssetsListItemEditDetails `protobuf:"bytes,4,opt,name=edit_details,json=editDetails,proto3" json:"edit_details,omitempty"`
	IsSelected  bool                               `protobuf:"varint,5,opt,name=is_selected,json=isSelected,proto3" json:"is_selected,omitempty"`
	IsEditable  bool                               `protobuf:"varint,6,opt,name=is_editable,json=isEditable,proto3" json:"is_editable,omitempty"`
	// this will tell the client that some information related to asset is missing
	// it can't be added to networth without provided info
	HasMissingInfo bool `protobuf:"varint,7,opt,name=has_missing_info,json=hasMissingInfo,proto3" json:"has_missing_info,omitempty"`
	// this will be used to identify related image on client side
	FileName string `protobuf:"bytes,8,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
}

func (x *ImportedAssetsListItem) Reset() {
	*x = ImportedAssetsListItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportedAssetsListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportedAssetsListItem) ProtoMessage() {}

func (x *ImportedAssetsListItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportedAssetsListItem.ProtoReflect.Descriptor instead.
func (*ImportedAssetsListItem) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP(), []int{6}
}

func (x *ImportedAssetsListItem) GetAssetName() *common.Text {
	if x != nil {
		return x.AssetName
	}
	return nil
}

func (x *ImportedAssetsListItem) GetAssetValue() *ui.IconTextComponent {
	if x != nil {
		return x.AssetValue
	}
	return nil
}

func (x *ImportedAssetsListItem) GetImportError() *ui.IconTextComponent {
	if x != nil {
		return x.ImportError
	}
	return nil
}

func (x *ImportedAssetsListItem) GetEditDetails() *ImportedAssetsListItemEditDetails {
	if x != nil {
		return x.EditDetails
	}
	return nil
}

func (x *ImportedAssetsListItem) GetIsSelected() bool {
	if x != nil {
		return x.IsSelected
	}
	return false
}

func (x *ImportedAssetsListItem) GetIsEditable() bool {
	if x != nil {
		return x.IsEditable
	}
	return false
}

func (x *ImportedAssetsListItem) GetHasMissingInfo() bool {
	if x != nil {
		return x.HasMissingInfo
	}
	return false
}

func (x *ImportedAssetsListItem) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12384-4503&t=vjav20xKlpC3t1ui-4
// these are details to fill in to edit the import asset fetched details
type ImportedAssetsListItemEditDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title          *common.Text                       `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	ManualForm     *NetWorthManualForm                `protobuf:"bytes,2,opt,name=manual_form,json=manualForm,proto3" json:"manual_form,omitempty"`
	FormIdentifier *typesv2.ManualAssetFormIdentifier `protobuf:"bytes,3,opt,name=form_identifier,json=formIdentifier,proto3" json:"form_identifier,omitempty"`
}

func (x *ImportedAssetsListItemEditDetails) Reset() {
	*x = ImportedAssetsListItemEditDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImportedAssetsListItemEditDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportedAssetsListItemEditDetails) ProtoMessage() {}

func (x *ImportedAssetsListItemEditDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportedAssetsListItemEditDetails.ProtoReflect.Descriptor instead.
func (*ImportedAssetsListItemEditDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP(), []int{7}
}

func (x *ImportedAssetsListItemEditDetails) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ImportedAssetsListItemEditDetails) GetManualForm() *NetWorthManualForm {
	if x != nil {
		return x.ManualForm
	}
	return nil
}

func (x *ImportedAssetsListItemEditDetails) GetFormIdentifier() *typesv2.ManualAssetFormIdentifier {
	if x != nil {
		return x.FormIdentifier
	}
	return nil
}

type FooterComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ShareButton         *ui.IconTextComponent `protobuf:"bytes,1,opt,name=share_button,json=shareButton,proto3" json:"share_button,omitempty"`
	AddToNetworthButton *ui.IconTextComponent `protobuf:"bytes,2,opt,name=add_to_networth_button,json=addToNetworthButton,proto3" json:"add_to_networth_button,omitempty"`
}

func (x *FooterComponent) Reset() {
	*x = FooterComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FooterComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FooterComponent) ProtoMessage() {}

func (x *FooterComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_magic_import_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FooterComponent.ProtoReflect.Descriptor instead.
func (*FooterComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP(), []int{8}
}

func (x *FooterComponent) GetShareButton() *ui.IconTextComponent {
	if x != nil {
		return x.ShareButton
	}
	return nil
}

func (x *FooterComponent) GetAddToNetworthButton() *ui.IconTextComponent {
	if x != nil {
		return x.AddToNetworthButton
	}
	return nil
}

var File_api_frontend_insights_networth_magic_import_proto protoreflect.FileDescriptor

var file_api_frontend_insights_networth_magic_import_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x1a,
	0x30, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f,
	0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x69,
	0x74, 0x69, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f,
	0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x76, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f,
	0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x80, 0x03, 0x0a, 0x1d, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x35, 0x0a, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x5f, 0x62, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42,
	0x61, 0x72, 0x52, 0x09, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x12, 0x50, 0x0a,
	0x0e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x52, 0x0d, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12,
	0x7e, 0x0a, 0x1f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x1c, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x73, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12,
	0x56, 0x0a, 0x10, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x46, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0xaa, 0x02, 0x0a, 0x0d, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x44, 0x0a, 0x0a, 0x68, 0x65, 0x72,
	0x6f, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x48, 0x65, 0x72, 0x6f, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x52, 0x09, 0x68, 0x65, 0x72, 0x6f, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x45, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65,
	0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5c, 0x0a, 0x12, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61,
	0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x11, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x22, 0x84, 0x01, 0x0a, 0x09, 0x48, 0x65, 0x72, 0x6f, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x6c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x62, 0x67, 0x4c, 0x6f, 0x74, 0x74,
	0x69, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x66, 0x72, 0x61, 0x6d, 0x65, 0x22, 0xa5, 0x01, 0x0a, 0x11,
	0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x45, 0x0a, 0x09, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x73, 0x5f, 0x75, 0x70, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x52, 0x08,
	0x74, 0x68, 0x75, 0x6d, 0x62, 0x73, 0x55, 0x70, 0x12, 0x49, 0x0a, 0x0b, 0x74, 0x68, 0x75, 0x6d,
	0x62, 0x73, 0x5f, 0x64, 0x6f, 0x77, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x46, 0x65, 0x65, 0x64, 0x62,
	0x61, 0x63, 0x6b, 0x56, 0x69, 0x65, 0x77, 0x52, 0x0a, 0x74, 0x68, 0x75, 0x6d, 0x62, 0x73, 0x44,
	0x6f, 0x77, 0x6e, 0x22, 0x9a, 0x01, 0x0a, 0x0c, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b,
	0x56, 0x69, 0x65, 0x77, 0x12, 0x42, 0x0a, 0x0b, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x5f, 0x76,
	0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x6e, 0x6f,
	0x72, 0x6d, 0x61, 0x6c, 0x56, 0x69, 0x65, 0x77, 0x12, 0x46, 0x0a, 0x0d, 0x73, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x56, 0x69, 0x65, 0x77,
	0x22, 0xc3, 0x03, 0x0a, 0x1b, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x6f, 0x0a, 0x1a, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73,
	0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x17, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x51, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52,
	0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x12, 0x53, 0x0a, 0x0d, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69,
	0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0c, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xc6, 0x03, 0x0a, 0x16, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65,
	0x6d, 0x12, 0x37, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x0b, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x44,
	0x0a, 0x0c, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x60, 0x0a, 0x0c, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x64,
	0x69, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x65, 0x64, 0x69, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x65, 0x64,
	0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73,
	0x45, 0x64, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x68, 0x61, 0x73, 0x5f,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x68, 0x61, 0x73, 0x4d, 0x69, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xf5, 0x01, 0x0a, 0x21, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x45, 0x64, 0x69, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x4f, 0x0a, 0x0b, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f,
	0x66, 0x6f, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x0a, 0x6d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x12, 0x4f, 0x0a, 0x0f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xaf, 0x01, 0x0a, 0x0f, 0x46, 0x6f, 0x6f, 0x74,
	0x65, 0x72, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x44, 0x0a, 0x0c, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x65, 0x42, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x12, 0x56, 0x0a, 0x16, 0x61, 0x64, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x61, 0x64, 0x64, 0x54, 0x6f, 0x4e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x42, 0x6e, 0x0a, 0x35, 0x63, 0x6f, 0x6d,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_frontend_insights_networth_magic_import_proto_rawDescOnce sync.Once
	file_api_frontend_insights_networth_magic_import_proto_rawDescData = file_api_frontend_insights_networth_magic_import_proto_rawDesc
)

func file_api_frontend_insights_networth_magic_import_proto_rawDescGZIP() []byte {
	file_api_frontend_insights_networth_magic_import_proto_rawDescOnce.Do(func() {
		file_api_frontend_insights_networth_magic_import_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_insights_networth_magic_import_proto_rawDescData)
	})
	return file_api_frontend_insights_networth_magic_import_proto_rawDescData
}

var file_api_frontend_insights_networth_magic_import_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_frontend_insights_networth_magic_import_proto_goTypes = []interface{}{
	(*MagicImportedAssetsListScreen)(nil),     // 0: frontend.insights.networth.MagicImportedAssetsListScreen
	(*AssetsSummary)(nil),                     // 1: frontend.insights.networth.AssetsSummary
	(*HeroImage)(nil),                         // 2: frontend.insights.networth.HeroImage
	(*FeedbackComponent)(nil),                 // 3: frontend.insights.networth.FeedbackComponent
	(*FeedbackView)(nil),                      // 4: frontend.insights.networth.FeedbackView
	(*ImportedAssetsListComponent)(nil),       // 5: frontend.insights.networth.ImportedAssetsListComponent
	(*ImportedAssetsListItem)(nil),            // 6: frontend.insights.networth.ImportedAssetsListItem
	(*ImportedAssetsListItemEditDetails)(nil), // 7: frontend.insights.networth.ImportedAssetsListItemEditDetails
	(*FooterComponent)(nil),                   // 8: frontend.insights.networth.FooterComponent
	(*ui.HeaderBar)(nil),                      // 9: api.typesv2.HeaderBar
	(*common.Text)(nil),                       // 10: api.typesv2.common.Text
	(*ui.VerticalKeyValuePair)(nil),           // 11: api.typesv2.ui.VerticalKeyValuePair
	(*common.VisualElement)(nil),              // 12: api.typesv2.common.VisualElement
	(*widget.BackgroundColour)(nil),           // 13: api.typesv2.common.ui.widget.BackgroundColour
	(*ui.IconTextComponent)(nil),              // 14: api.typesv2.ui.IconTextComponent
	(*NetWorthManualForm)(nil),                // 15: frontend.insights.networth.NetWorthManualForm
	(*typesv2.ManualAssetFormIdentifier)(nil), // 16: api.typesv2.ManualAssetFormIdentifier
}
var file_api_frontend_insights_networth_magic_import_proto_depIdxs = []int32{
	9,  // 0: frontend.insights.networth.MagicImportedAssetsListScreen.header_bar:type_name -> api.typesv2.HeaderBar
	1,  // 1: frontend.insights.networth.MagicImportedAssetsListScreen.assets_summary:type_name -> frontend.insights.networth.AssetsSummary
	5,  // 2: frontend.insights.networth.MagicImportedAssetsListScreen.imported_assets_list_components:type_name -> frontend.insights.networth.ImportedAssetsListComponent
	8,  // 3: frontend.insights.networth.MagicImportedAssetsListScreen.footer_component:type_name -> frontend.insights.networth.FooterComponent
	10, // 4: frontend.insights.networth.AssetsSummary.title:type_name -> api.typesv2.common.Text
	2,  // 5: frontend.insights.networth.AssetsSummary.hero_image:type_name -> frontend.insights.networth.HeroImage
	11, // 6: frontend.insights.networth.AssetsSummary.assets_info:type_name -> api.typesv2.ui.VerticalKeyValuePair
	3,  // 7: frontend.insights.networth.AssetsSummary.feedback_component:type_name -> frontend.insights.networth.FeedbackComponent
	12, // 8: frontend.insights.networth.HeroImage.bg_lottie:type_name -> api.typesv2.common.VisualElement
	12, // 9: frontend.insights.networth.HeroImage.frame:type_name -> api.typesv2.common.VisualElement
	4,  // 10: frontend.insights.networth.FeedbackComponent.thumbs_up:type_name -> frontend.insights.networth.FeedbackView
	4,  // 11: frontend.insights.networth.FeedbackComponent.thumbs_down:type_name -> frontend.insights.networth.FeedbackView
	12, // 12: frontend.insights.networth.FeedbackView.normal_view:type_name -> api.typesv2.common.VisualElement
	12, // 13: frontend.insights.networth.FeedbackView.selected_view:type_name -> api.typesv2.common.VisualElement
	10, // 14: frontend.insights.networth.ImportedAssetsListComponent.title:type_name -> api.typesv2.common.Text
	6,  // 15: frontend.insights.networth.ImportedAssetsListComponent.imported_assets_list_items:type_name -> frontend.insights.networth.ImportedAssetsListItem
	13, // 16: frontend.insights.networth.ImportedAssetsListComponent.border_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	13, // 17: frontend.insights.networth.ImportedAssetsListComponent.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	13, // 18: frontend.insights.networth.ImportedAssetsListComponent.divider_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	10, // 19: frontend.insights.networth.ImportedAssetsListItem.asset_name:type_name -> api.typesv2.common.Text
	14, // 20: frontend.insights.networth.ImportedAssetsListItem.asset_value:type_name -> api.typesv2.ui.IconTextComponent
	14, // 21: frontend.insights.networth.ImportedAssetsListItem.import_error:type_name -> api.typesv2.ui.IconTextComponent
	7,  // 22: frontend.insights.networth.ImportedAssetsListItem.edit_details:type_name -> frontend.insights.networth.ImportedAssetsListItemEditDetails
	10, // 23: frontend.insights.networth.ImportedAssetsListItemEditDetails.title:type_name -> api.typesv2.common.Text
	15, // 24: frontend.insights.networth.ImportedAssetsListItemEditDetails.manual_form:type_name -> frontend.insights.networth.NetWorthManualForm
	16, // 25: frontend.insights.networth.ImportedAssetsListItemEditDetails.form_identifier:type_name -> api.typesv2.ManualAssetFormIdentifier
	14, // 26: frontend.insights.networth.FooterComponent.share_button:type_name -> api.typesv2.ui.IconTextComponent
	14, // 27: frontend.insights.networth.FooterComponent.add_to_networth_button:type_name -> api.typesv2.ui.IconTextComponent
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_api_frontend_insights_networth_magic_import_proto_init() }
func file_api_frontend_insights_networth_magic_import_proto_init() {
	if File_api_frontend_insights_networth_magic_import_proto != nil {
		return
	}
	file_api_frontend_insights_networth_manual_form_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_insights_networth_magic_import_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MagicImportedAssetsListScreen); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_magic_import_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetsSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_magic_import_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HeroImage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_magic_import_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedbackComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_magic_import_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeedbackView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_magic_import_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportedAssetsListComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_magic_import_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportedAssetsListItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_magic_import_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImportedAssetsListItemEditDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_magic_import_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FooterComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_insights_networth_magic_import_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_insights_networth_magic_import_proto_goTypes,
		DependencyIndexes: file_api_frontend_insights_networth_magic_import_proto_depIdxs,
		MessageInfos:      file_api_frontend_insights_networth_magic_import_proto_msgTypes,
	}.Build()
	File_api_frontend_insights_networth_magic_import_proto = out.File
	file_api_frontend_insights_networth_magic_import_proto_rawDesc = nil
	file_api_frontend_insights_networth_magic_import_proto_goTypes = nil
	file_api_frontend_insights_networth_magic_import_proto_depIdxs = nil
}
