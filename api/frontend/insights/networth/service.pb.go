// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/insights/networth/service.proto

package networth

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	file "github.com/epifi/be-common/api/typesv2/common/file"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	home "github.com/epifi/gamma/api/frontend/home"
	orchestrator "github.com/epifi/gamma/api/frontend/home/<USER>"
	ui1 "github.com/epifi/gamma/api/frontend/insights/networth/ui"
	secrets "github.com/epifi/gamma/api/frontend/insights/secrets"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	assetandanalysis "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/assetandanalysis"
	networth_refresh "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth_refresh"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Identifier for type of Deposit while manually adding a deposit.
type ManualAddDepositType int32

const (
	ManualAddDepositType_DEPOSIT_TYPE_UNSPECIFIED ManualAddDepositType = 0
	// Fixed Deposit.
	ManualAddDepositType_FD ManualAddDepositType = 1
	// Recurring deposits.
	ManualAddDepositType_RECURRING ManualAddDepositType = 2
)

// Enum value maps for ManualAddDepositType.
var (
	ManualAddDepositType_name = map[int32]string{
		0: "DEPOSIT_TYPE_UNSPECIFIED",
		1: "FD",
		2: "RECURRING",
	}
	ManualAddDepositType_value = map[string]int32{
		"DEPOSIT_TYPE_UNSPECIFIED": 0,
		"FD":                       1,
		"RECURRING":                2,
	}
)

func (x ManualAddDepositType) Enum() *ManualAddDepositType {
	p := new(ManualAddDepositType)
	*p = x
	return p
}

func (x ManualAddDepositType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ManualAddDepositType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_service_proto_enumTypes[0].Descriptor()
}

func (ManualAddDepositType) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_service_proto_enumTypes[0]
}

func (x ManualAddDepositType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ManualAddDepositType.Descriptor instead.
func (ManualAddDepositType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{0}
}

type VisualisationType int32

const (
	VisualisationType_VISUALISATION_TYPE_UNSPECIFIED VisualisationType = 0
	VisualisationType_VISUALISATION_TYPE_ISLAND      VisualisationType = 1
)

// Enum value maps for VisualisationType.
var (
	VisualisationType_name = map[int32]string{
		0: "VISUALISATION_TYPE_UNSPECIFIED",
		1: "VISUALISATION_TYPE_ISLAND",
	}
	VisualisationType_value = map[string]int32{
		"VISUALISATION_TYPE_UNSPECIFIED": 0,
		"VISUALISATION_TYPE_ISLAND":      1,
	}
)

func (x VisualisationType) Enum() *VisualisationType {
	p := new(VisualisationType)
	*p = x
	return p
}

func (x VisualisationType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VisualisationType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_service_proto_enumTypes[1].Descriptor()
}

func (VisualisationType) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_service_proto_enumTypes[1]
}

func (x VisualisationType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VisualisationType.Descriptor instead.
func (VisualisationType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{1}
}

type WidgetType int32

const (
	WidgetType_WIDGET_TYPE_UNSPECIFIED WidgetType = 0
	WidgetType_WIDGET_TYPE_CARD        WidgetType = 1
)

// Enum value maps for WidgetType.
var (
	WidgetType_name = map[int32]string{
		0: "WIDGET_TYPE_UNSPECIFIED",
		1: "WIDGET_TYPE_CARD",
	}
	WidgetType_value = map[string]int32{
		"WIDGET_TYPE_UNSPECIFIED": 0,
		"WIDGET_TYPE_CARD":        1,
	}
)

func (x WidgetType) Enum() *WidgetType {
	p := new(WidgetType)
	*p = x
	return p
}

func (x WidgetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WidgetType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_service_proto_enumTypes[2].Descriptor()
}

func (WidgetType) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_service_proto_enumTypes[2]
}

func (x WidgetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WidgetType.Descriptor instead.
func (WidgetType) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{2}
}

type CacheControl int32

const (
	// If cache control is unspecified then client should not use cache
	CacheControl_CACHE_CONTROL_UNSPECIFIED CacheControl = 0
	// Client is supposed to read from cache for overall computation.
	CacheControl_CACHE_CONTROL_USE_CACHE CacheControl = 1
)

// Enum value maps for CacheControl.
var (
	CacheControl_name = map[int32]string{
		0: "CACHE_CONTROL_UNSPECIFIED",
		1: "CACHE_CONTROL_USE_CACHE",
	}
	CacheControl_value = map[string]int32{
		"CACHE_CONTROL_UNSPECIFIED": 0,
		"CACHE_CONTROL_USE_CACHE":   1,
	}
)

func (x CacheControl) Enum() *CacheControl {
	p := new(CacheControl)
	*p = x
	return p
}

func (x CacheControl) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CacheControl) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_service_proto_enumTypes[3].Descriptor()
}

func (CacheControl) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_service_proto_enumTypes[3]
}

func (x CacheControl) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CacheControl.Descriptor instead.
func (CacheControl) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{3}
}

type WidgetState int32

const (
	WidgetState_WIDGET_STATE_UNSPECIFIED WidgetState = 0
	// This refers to the state where user had not successfully completed the category process.
	WidgetState_WIDGET_STATE_UNINITIALIZED WidgetState = 1
	// This refers to the state where the user has completed the category process successfully. The data can be zero or contain some value.
	// If a widget is in initialised state and client has to use cache, then it should count this widget in category added.
	WidgetState_WIDGET_STATE_INITIALIZED WidgetState = 2
)

// Enum value maps for WidgetState.
var (
	WidgetState_name = map[int32]string{
		0: "WIDGET_STATE_UNSPECIFIED",
		1: "WIDGET_STATE_UNINITIALIZED",
		2: "WIDGET_STATE_INITIALIZED",
	}
	WidgetState_value = map[string]int32{
		"WIDGET_STATE_UNSPECIFIED":   0,
		"WIDGET_STATE_UNINITIALIZED": 1,
		"WIDGET_STATE_INITIALIZED":   2,
	}
)

func (x WidgetState) Enum() *WidgetState {
	p := new(WidgetState)
	*p = x
	return p
}

func (x WidgetState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WidgetState) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_service_proto_enumTypes[4].Descriptor()
}

func (WidgetState) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_service_proto_enumTypes[4]
}

func (x WidgetState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WidgetState.Descriptor instead.
func (WidgetState) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{4}
}

type GetNetWorthDashboardResponse_Status int32

const (
	GetNetWorthDashboardResponse_Ok       GetNetWorthDashboardResponse_Status = 0
	GetNetWorthDashboardResponse_INTERNAL GetNetWorthDashboardResponse_Status = 13
)

// Enum value maps for GetNetWorthDashboardResponse_Status.
var (
	GetNetWorthDashboardResponse_Status_name = map[int32]string{
		0:  "Ok",
		13: "INTERNAL",
	}
	GetNetWorthDashboardResponse_Status_value = map[string]int32{
		"Ok":       0,
		"INTERNAL": 13,
	}
)

func (x GetNetWorthDashboardResponse_Status) Enum() *GetNetWorthDashboardResponse_Status {
	p := new(GetNetWorthDashboardResponse_Status)
	*p = x
	return p
}

func (x GetNetWorthDashboardResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetNetWorthDashboardResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_service_proto_enumTypes[5].Descriptor()
}

func (GetNetWorthDashboardResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_service_proto_enumTypes[5]
}

func (x GetNetWorthDashboardResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetNetWorthDashboardResponse_Status.Descriptor instead.
func (GetNetWorthDashboardResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{13, 0}
}

type MagicImportFilesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req   *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	Files []*file.File          `protobuf:"bytes,2,rep,name=files,proto3" json:"files,omitempty"`
}

func (x *MagicImportFilesRequest) Reset() {
	*x = MagicImportFilesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MagicImportFilesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagicImportFilesRequest) ProtoMessage() {}

func (x *MagicImportFilesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagicImportFilesRequest.ProtoReflect.Descriptor instead.
func (*MagicImportFilesRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{0}
}

func (x *MagicImportFilesRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *MagicImportFilesRequest) GetFiles() []*file.File {
	if x != nil {
		return x.Files
	}
	return nil
}

type MagicImportFilesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader               *header.ResponseHeader         `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	ImportedAssetsListScreen *MagicImportedAssetsListScreen `protobuf:"bytes,2,opt,name=imported_assets_list_screen,json=importedAssetsListScreen,proto3" json:"imported_assets_list_screen,omitempty"`
}

func (x *MagicImportFilesResponse) Reset() {
	*x = MagicImportFilesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MagicImportFilesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagicImportFilesResponse) ProtoMessage() {}

func (x *MagicImportFilesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagicImportFilesResponse.ProtoReflect.Descriptor instead.
func (*MagicImportFilesResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{1}
}

func (x *MagicImportFilesResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *MagicImportFilesResponse) GetImportedAssetsListScreen() *MagicImportedAssetsListScreen {
	if x != nil {
		return x.ImportedAssetsListScreen
	}
	return nil
}

type DeleteManualAssetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// external_id of asset to be deleted for actor
	ExternalId string `protobuf:"bytes,2,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
}

func (x *DeleteManualAssetRequest) Reset() {
	*x = DeleteManualAssetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteManualAssetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteManualAssetRequest) ProtoMessage() {}

func (x *DeleteManualAssetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteManualAssetRequest.ProtoReflect.Descriptor instead.
func (*DeleteManualAssetRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteManualAssetRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *DeleteManualAssetRequest) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

type DeleteManualAssetResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *DeleteManualAssetResponse) Reset() {
	*x = DeleteManualAssetResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteManualAssetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteManualAssetResponse) ProtoMessage() {}

func (x *DeleteManualAssetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteManualAssetResponse.ProtoReflect.Descriptor instead.
func (*DeleteManualAssetResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteManualAssetResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetManualAssetDashboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// This is referred from api/insights/networth/service.proto AssetType (converted to string)
	AssetType string `protobuf:"bytes,2,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`
}

func (x *GetManualAssetDashboardRequest) Reset() {
	*x = GetManualAssetDashboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetManualAssetDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetManualAssetDashboardRequest) ProtoMessage() {}

func (x *GetManualAssetDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetManualAssetDashboardRequest.ProtoReflect.Descriptor instead.
func (*GetManualAssetDashboardRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetManualAssetDashboardRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetManualAssetDashboardRequest) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

type GetManualAssetDashboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader  `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Dashboard  *NetWorthAssetDashboard `protobuf:"bytes,2,opt,name=dashboard,proto3" json:"dashboard,omitempty"`
}

func (x *GetManualAssetDashboardResponse) Reset() {
	*x = GetManualAssetDashboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetManualAssetDashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetManualAssetDashboardResponse) ProtoMessage() {}

func (x *GetManualAssetDashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetManualAssetDashboardResponse.ProtoReflect.Descriptor instead.
func (*GetManualAssetDashboardResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetManualAssetDashboardResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetManualAssetDashboardResponse) GetDashboard() *NetWorthAssetDashboard {
	if x != nil {
		return x.Dashboard
	}
	return nil
}

type SubmitManualFormRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req            *header.RequestHeader              `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	FormIdentifier *typesv2.ManualAssetFormIdentifier `protobuf:"bytes,2,opt,name=form_identifier,json=formIdentifier,proto3" json:"form_identifier,omitempty"`
	FormData       []*NetWorthManualInputData         `protobuf:"bytes,3,rep,name=form_data,json=formData,proto3" json:"form_data,omitempty"`
}

func (x *SubmitManualFormRequest) Reset() {
	*x = SubmitManualFormRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitManualFormRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitManualFormRequest) ProtoMessage() {}

func (x *SubmitManualFormRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitManualFormRequest.ProtoReflect.Descriptor instead.
func (*SubmitManualFormRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{6}
}

func (x *SubmitManualFormRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *SubmitManualFormRequest) GetFormIdentifier() *typesv2.ManualAssetFormIdentifier {
	if x != nil {
		return x.FormIdentifier
	}
	return nil
}

func (x *SubmitManualFormRequest) GetFormData() []*NetWorthManualInputData {
	if x != nil {
		return x.FormData
	}
	return nil
}

type SubmitManualFormResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *SubmitManualFormResponse) Reset() {
	*x = SubmitManualFormResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitManualFormResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitManualFormResponse) ProtoMessage() {}

func (x *SubmitManualFormResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitManualFormResponse.ProtoReflect.Descriptor instead.
func (*SubmitManualFormResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{7}
}

func (x *SubmitManualFormResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetManualFormConfigRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req            *header.RequestHeader              `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	FormIdentifier *typesv2.ManualAssetFormIdentifier `protobuf:"bytes,2,opt,name=form_identifier,json=formIdentifier,proto3" json:"form_identifier,omitempty"`
}

func (x *GetManualFormConfigRequest) Reset() {
	*x = GetManualFormConfigRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetManualFormConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetManualFormConfigRequest) ProtoMessage() {}

func (x *GetManualFormConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetManualFormConfigRequest.ProtoReflect.Descriptor instead.
func (*GetManualFormConfigRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetManualFormConfigRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetManualFormConfigRequest) GetFormIdentifier() *typesv2.ManualAssetFormIdentifier {
	if x != nil {
		return x.FormIdentifier
	}
	return nil
}

type GetManualFormConfigResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader   *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	FormResponse *NetWorthManualForm    `protobuf:"bytes,2,opt,name=form_response,json=formResponse,proto3" json:"form_response,omitempty"`
}

func (x *GetManualFormConfigResponse) Reset() {
	*x = GetManualFormConfigResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetManualFormConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetManualFormConfigResponse) ProtoMessage() {}

func (x *GetManualFormConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetManualFormConfigResponse.ProtoReflect.Descriptor instead.
func (*GetManualFormConfigResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetManualFormConfigResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetManualFormConfigResponse) GetFormResponse() *NetWorthManualForm {
	if x != nil {
		return x.FormResponse
	}
	return nil
}

type DepositDeclarationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Amount invested in the deposit.
	Amount *typesv2.Money `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	// Interest rate for the deposit.
	InterestRate float64 `protobuf:"fixed64,3,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	// Creation date timestamp for the deposit.
	StartDate *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// Closure date timestamp for the deposit.
	EndDate *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
	// Type of Deposit.
	DepositType ManualAddDepositType `protobuf:"varint,6,opt,name=deposit_type,json=depositType,proto3,enum=frontend.insights.networth.ManualAddDepositType" json:"deposit_type,omitempty"`
	// name of deposit
	DepositName string `protobuf:"bytes,7,opt,name=deposit_name,json=depositName,proto3" json:"deposit_name,omitempty"`
}

func (x *DepositDeclarationRequest) Reset() {
	*x = DepositDeclarationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositDeclarationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositDeclarationRequest) ProtoMessage() {}

func (x *DepositDeclarationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositDeclarationRequest.ProtoReflect.Descriptor instead.
func (*DepositDeclarationRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{10}
}

func (x *DepositDeclarationRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *DepositDeclarationRequest) GetAmount() *typesv2.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *DepositDeclarationRequest) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *DepositDeclarationRequest) GetStartDate() *timestamppb.Timestamp {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *DepositDeclarationRequest) GetEndDate() *timestamppb.Timestamp {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *DepositDeclarationRequest) GetDepositType() ManualAddDepositType {
	if x != nil {
		return x.DepositType
	}
	return ManualAddDepositType_DEPOSIT_TYPE_UNSPECIFIED
}

func (x *DepositDeclarationRequest) GetDepositName() string {
	if x != nil {
		return x.DepositName
	}
	return ""
}

type DepositDeclarationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *DepositDeclarationResponse) Reset() {
	*x = DepositDeclarationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositDeclarationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositDeclarationResponse) ProtoMessage() {}

func (x *DepositDeclarationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositDeclarationResponse.ProtoReflect.Descriptor instead.
func (*DepositDeclarationResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{11}
}

func (x *DepositDeclarationResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type GetNetWorthDashboardRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// dashboard_type is sent by backend through screen options
	DashboardType string `protobuf:"bytes,2,opt,name=dashboard_type,json=dashboardType,proto3" json:"dashboard_type,omitempty"`
}

func (x *GetNetWorthDashboardRequest) Reset() {
	*x = GetNetWorthDashboardRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthDashboardRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthDashboardRequest) ProtoMessage() {}

func (x *GetNetWorthDashboardRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthDashboardRequest.ProtoReflect.Descriptor instead.
func (*GetNetWorthDashboardRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetNetWorthDashboardRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetNetWorthDashboardRequest) GetDashboardType() string {
	if x != nil {
		return x.DashboardType
	}
	return ""
}

type GetNetWorthDashboardResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Page title
	Title string `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	// banner to show in case net worth assets need to refreshed
	// location for this is fixed to be above the 'sections' field
	// Ref: https://drive.google.com/file/d/1bBfd_cXfNFUlMHwF2OoX95HCKUW9nRhw/view?usp=drive_link
	RefreshBanner *ui.IconTextComponent `protobuf:"bytes,6,opt,name=refresh_banner,json=refreshBanner,proto3" json:"refresh_banner,omitempty"`
	// Visualisation will be used to give the total net worth of the user
	Visualisation *NetWorthVisualisation `protobuf:"bytes,2,opt,name=visualisation,proto3" json:"visualisation,omitempty"`
	// Sections will have a list of widgets giving details about the assets, liabilities etc.
	//
	// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
	Sections []*Section `protobuf:"bytes,3,rep,name=sections,proto3" json:"sections,omitempty"`
	// footer to show at the bottom of the page
	Footer *common.Text `protobuf:"bytes,4,opt,name=footer,proto3" json:"footer,omitempty"`
	// list of components
	Components []*Component `protobuf:"bytes,7,rep,name=components,proto3" json:"components,omitempty"`
	// this cta will always be placed above the refresh_banner
	// this cta will only be shown for dash_board_type assets
	// Ref: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-39671&t=xBx5ulnmaQR9chb6-4
	ConnectAssetsCta *ui.IconTextComponent `protobuf:"bytes,8,opt,name=connect_assets_cta,json=connectAssetsCta,proto3" json:"connect_assets_cta,omitempty"`
}

func (x *GetNetWorthDashboardResponse) Reset() {
	*x = GetNetWorthDashboardResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthDashboardResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthDashboardResponse) ProtoMessage() {}

func (x *GetNetWorthDashboardResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthDashboardResponse.ProtoReflect.Descriptor instead.
func (*GetNetWorthDashboardResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{13}
}

func (x *GetNetWorthDashboardResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetNetWorthDashboardResponse) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetNetWorthDashboardResponse) GetRefreshBanner() *ui.IconTextComponent {
	if x != nil {
		return x.RefreshBanner
	}
	return nil
}

func (x *GetNetWorthDashboardResponse) GetVisualisation() *NetWorthVisualisation {
	if x != nil {
		return x.Visualisation
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
func (x *GetNetWorthDashboardResponse) GetSections() []*Section {
	if x != nil {
		return x.Sections
	}
	return nil
}

func (x *GetNetWorthDashboardResponse) GetFooter() *common.Text {
	if x != nil {
		return x.Footer
	}
	return nil
}

func (x *GetNetWorthDashboardResponse) GetComponents() []*Component {
	if x != nil {
		return x.Components
	}
	return nil
}

func (x *GetNetWorthDashboardResponse) GetConnectAssetsCta() *ui.IconTextComponent {
	if x != nil {
		return x.ConnectAssetsCta
	}
	return nil
}

// Component represent a ui component spanning the width of screen in the net worth dashboard
type Component struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Component:
	//
	//	*Component_Section
	//	*Component_SecretsSection
	Component isComponent_Component `protobuf_oneof:"component"`
}

func (x *Component) Reset() {
	*x = Component{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Component) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Component) ProtoMessage() {}

func (x *Component) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Component.ProtoReflect.Descriptor instead.
func (*Component) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{14}
}

func (m *Component) GetComponent() isComponent_Component {
	if m != nil {
		return m.Component
	}
	return nil
}

func (x *Component) GetSection() *Section {
	if x, ok := x.GetComponent().(*Component_Section); ok {
		return x.Section
	}
	return nil
}

func (x *Component) GetSecretsSection() *SecretsSection {
	if x, ok := x.GetComponent().(*Component_SecretsSection); ok {
		return x.SecretsSection
	}
	return nil
}

type isComponent_Component interface {
	isComponent_Component()
}

type Component_Section struct {
	Section *Section `protobuf:"bytes,1,opt,name=section,proto3,oneof"`
}

type Component_SecretsSection struct {
	SecretsSection *SecretsSection `protobuf:"bytes,2,opt,name=secrets_section,json=secretsSection,proto3,oneof"`
}

func (*Component_Section) isComponent_Component() {}

func (*Component_SecretsSection) isComponent_Component() {}

// SecretsSection displays the secrets available for user in networth screen
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20786-38262&t=eH5MzOzTx9gt6HYB-4
type SecretsSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title           *ui.IconTextComponent    `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Cta             *ui.IconTextComponent    `protobuf:"bytes,2,opt,name=cta,proto3" json:"cta,omitempty"`
	SecretSummaries []*secrets.SecretSummary `protobuf:"bytes,3,rep,name=secret_summaries,json=secretSummaries,proto3" json:"secret_summaries,omitempty"`
	Subtitle        *ui.IconTextComponent    `protobuf:"bytes,4,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
}

func (x *SecretsSection) Reset() {
	*x = SecretsSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecretsSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretsSection) ProtoMessage() {}

func (x *SecretsSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretsSection.ProtoReflect.Descriptor instead.
func (*SecretsSection) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{15}
}

func (x *SecretsSection) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SecretsSection) GetCta() *ui.IconTextComponent {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *SecretsSection) GetSecretSummaries() []*secrets.SecretSummary {
	if x != nil {
		return x.SecretSummaries
	}
	return nil
}

func (x *SecretsSection) GetSubtitle() *ui.IconTextComponent {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

type NetWorthVisualisation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VisualisationType VisualisationType `protobuf:"varint,1,opt,name=visualisation_type,json=visualisationType,proto3,enum=frontend.insights.networth.VisualisationType" json:"visualisation_type,omitempty"`
	// Types that are assignable to Params:
	//
	//	*NetWorthVisualisation_IslandParams
	Params isNetWorthVisualisation_Params `protobuf_oneof:"params"`
}

func (x *NetWorthVisualisation) Reset() {
	*x = NetWorthVisualisation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetWorthVisualisation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetWorthVisualisation) ProtoMessage() {}

func (x *NetWorthVisualisation) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetWorthVisualisation.ProtoReflect.Descriptor instead.
func (*NetWorthVisualisation) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{16}
}

func (x *NetWorthVisualisation) GetVisualisationType() VisualisationType {
	if x != nil {
		return x.VisualisationType
	}
	return VisualisationType_VISUALISATION_TYPE_UNSPECIFIED
}

func (m *NetWorthVisualisation) GetParams() isNetWorthVisualisation_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *NetWorthVisualisation) GetIslandParams() *IslandParams {
	if x, ok := x.GetParams().(*NetWorthVisualisation_IslandParams); ok {
		return x.IslandParams
	}
	return nil
}

type isNetWorthVisualisation_Params interface {
	isNetWorthVisualisation_Params()
}

type NetWorthVisualisation_IslandParams struct {
	IslandParams *IslandParams `protobuf:"bytes,2,opt,name=island_params,json=islandParams,proto3,oneof"`
}

func (*NetWorthVisualisation_IslandParams) isNetWorthVisualisation_Params() {}

type IslandParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Asset's or Liabilities icons
	Categories []*CategoryVisualisationItem `protobuf:"bytes,1,rep,name=categories,proto3" json:"categories,omitempty"`
	// The background image over which the CategoryVisualisationItems will be plotted
	BackgroundImage *Image `protobuf:"bytes,2,opt,name=background_image,json=backgroundImage,proto3" json:"background_image,omitempty"`
	// Summary of users total assets and liabilities
	Summary *VisualisationSummary `protobuf:"bytes,3,opt,name=summary,proto3" json:"summary,omitempty"`
	// The message informing user on assets or liabilities status
	Message *common.Text `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *IslandParams) Reset() {
	*x = IslandParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IslandParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IslandParams) ProtoMessage() {}

func (x *IslandParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IslandParams.ProtoReflect.Descriptor instead.
func (*IslandParams) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{17}
}

func (x *IslandParams) GetCategories() []*CategoryVisualisationItem {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *IslandParams) GetBackgroundImage() *Image {
	if x != nil {
		return x.BackgroundImage
	}
	return nil
}

func (x *IslandParams) GetSummary() *VisualisationSummary {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *IslandParams) GetMessage() *common.Text {
	if x != nil {
		return x.Message
	}
	return nil
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImageUrl string `protobuf:"bytes,1,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	// This field will give the value of image width to screen width. Value will be between 0 and 1 as the image width should always be less than screen width.
	ImageToScreenWidthRatio float32 `protobuf:"fixed32,2,opt,name=image_to_screen_width_ratio,json=imageToScreenWidthRatio,proto3" json:"image_to_screen_width_ratio,omitempty"`
	// This field will give the ratio of image width to image_height. This will be used to calculate the height of the image.
	ImageAspectRatio float32 `protobuf:"fixed32,3,opt,name=image_aspect_ratio,json=imageAspectRatio,proto3" json:"image_aspect_ratio,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{18}
}

func (x *Image) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *Image) GetImageToScreenWidthRatio() float32 {
	if x != nil {
		return x.ImageToScreenWidthRatio
	}
	return 0
}

func (x *Image) GetImageAspectRatio() float32 {
	if x != nil {
		return x.ImageAspectRatio
	}
	return 0
}

// The summary shown below the visualisation
type VisualisationSummary struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title to show in the net worth visualisation summary
	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Total net worth value. The value should be overridden by client and formatting should be used as it is.
	Value *ui.IconTextComponent `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// Number of categories added by user. If client uses cache for some widget they will have to calculate this value.
	CategoriesAdded int32 `protobuf:"varint,3,opt,name=categories_added,json=categoriesAdded,proto3" json:"categories_added,omitempty"`
	// Total number of categories
	TotalCategories int32 `protobuf:"varint,4,opt,name=total_categories,json=totalCategories,proto3" json:"total_categories,omitempty"`
	// Border Color for the summary card
	BorderColor string `protobuf:"bytes,5,opt,name=borderColor,proto3" json:"borderColor,omitempty"`
	// Shadow color for the summary card
	ShadowColor string `protobuf:"bytes,6,opt,name=shadowColor,proto3" json:"shadowColor,omitempty"`
}

func (x *VisualisationSummary) Reset() {
	*x = VisualisationSummary{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisualisationSummary) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisualisationSummary) ProtoMessage() {}

func (x *VisualisationSummary) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisualisationSummary.ProtoReflect.Descriptor instead.
func (*VisualisationSummary) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{19}
}

func (x *VisualisationSummary) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *VisualisationSummary) GetValue() *ui.IconTextComponent {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *VisualisationSummary) GetCategoriesAdded() int32 {
	if x != nil {
		return x.CategoriesAdded
	}
	return 0
}

func (x *VisualisationSummary) GetTotalCategories() int32 {
	if x != nil {
		return x.TotalCategories
	}
	return 0
}

func (x *VisualisationSummary) GetBorderColor() string {
	if x != nil {
		return x.BorderColor
	}
	return ""
}

func (x *VisualisationSummary) GetShadowColor() string {
	if x != nil {
		return x.ShadowColor
	}
	return ""
}

// The icon for every category
type CategoryVisualisationItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CategoryIcon *common.VisualElement `protobuf:"bytes,1,opt,name=category_icon,json=categoryIcon,proto3" json:"category_icon,omitempty"`
	// X position of the top left of the item. This will be from the top left point and relative to the width of the island image. It's value will vary from 0 to 1.
	X float32 `protobuf:"fixed32,2,opt,name=x,proto3" json:"x,omitempty"`
	// Y position of the top left of the item. This will be from the top left point and relative to the height of the island image. It's value will vary from 0 to 1.
	Y float32 `protobuf:"fixed32,3,opt,name=y,proto3" json:"y,omitempty"`
	// relative width of the image. It will vary from 0 to 1.
	Width float32 `protobuf:"fixed32,4,opt,name=width,proto3" json:"width,omitempty"`
	// relative height of the image. It will vary from 0 to 1.
	Height float32 `protobuf:"fixed32,5,opt,name=height,proto3" json:"height,omitempty"`
}

func (x *CategoryVisualisationItem) Reset() {
	*x = CategoryVisualisationItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CategoryVisualisationItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryVisualisationItem) ProtoMessage() {}

func (x *CategoryVisualisationItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryVisualisationItem.ProtoReflect.Descriptor instead.
func (*CategoryVisualisationItem) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{20}
}

func (x *CategoryVisualisationItem) GetCategoryIcon() *common.VisualElement {
	if x != nil {
		return x.CategoryIcon
	}
	return nil
}

func (x *CategoryVisualisationItem) GetX() float32 {
	if x != nil {
		return x.X
	}
	return 0
}

func (x *CategoryVisualisationItem) GetY() float32 {
	if x != nil {
		return x.Y
	}
	return 0
}

func (x *CategoryVisualisationItem) GetWidth() float32 {
	if x != nil {
		return x.Width
	}
	return 0
}

func (x *CategoryVisualisationItem) GetHeight() float32 {
	if x != nil {
		return x.Height
	}
	return 0
}

type Section struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This will be the unique identifier for the section. Each widget will be specific to a section. Caching of a widget should be done with the section.
	// If section id is updated then all the widgets that are cached in the section should be invalidated.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Summary to show for the section. It will have title and value. Value will be empty string. It should be overwritten by client.
	//
	// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
	Summary *ui.VerticalKeyValuePair `protobuf:"bytes,2,opt,name=summary,proto3" json:"summary,omitempty"`
	// It will give text for add more button.
	//
	// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
	AddMoreButton *ui.IconTextComponent `protobuf:"bytes,3,opt,name=add_more_button,json=addMoreButton,proto3" json:"add_more_button,omitempty"`
	// The details required for add more widgets page such as list of widgets, search hints etc.
	AddMoreWidgets *AddMoreWidgets `protobuf:"bytes,4,opt,name=add_more_widgets,json=addMoreWidgets,proto3" json:"add_more_widgets,omitempty"`
	// List of widgets that will be displayed in the section. If cache is used then the ordering should be done by client. Else the ordering should be same as what is sent by backend.
	Widgets []*Widget `protobuf:"bytes,5,rep,name=widgets,proto3" json:"widgets,omitempty"`
	// Limit on the number of categories to show in section. Other widgets will be shown when user clicks on show more widget.
	Limit int32 `protobuf:"varint,6,opt,name=limit,proto3" json:"limit,omitempty"`
	// This will be used for the dash_board_type networth. For assets this will be nil
	SectionHeader *Section_SectionHeader `protobuf:"bytes,7,opt,name=section_header,json=sectionHeader,proto3" json:"section_header,omitempty"`
}

func (x *Section) Reset() {
	*x = Section{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Section) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Section) ProtoMessage() {}

func (x *Section) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Section.ProtoReflect.Descriptor instead.
func (*Section) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{21}
}

func (x *Section) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
func (x *Section) GetSummary() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.Summary
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
func (x *Section) GetAddMoreButton() *ui.IconTextComponent {
	if x != nil {
		return x.AddMoreButton
	}
	return nil
}

func (x *Section) GetAddMoreWidgets() *AddMoreWidgets {
	if x != nil {
		return x.AddMoreWidgets
	}
	return nil
}

func (x *Section) GetWidgets() []*Widget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

func (x *Section) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *Section) GetSectionHeader() *Section_SectionHeader {
	if x != nil {
		return x.SectionHeader
	}
	return nil
}

type AddMoreWidgets struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of add more page
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// The list of widgets to be shown when use clicks on add more button. These widgets will be the ones which user has not added yet to the net worth.
	// The ordering of the widgets should be maintained by backend.
	Widgets []*Widget `protobuf:"bytes,1,rep,name=widgets,proto3" json:"widgets,omitempty"`
}

func (x *AddMoreWidgets) Reset() {
	*x = AddMoreWidgets{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddMoreWidgets) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddMoreWidgets) ProtoMessage() {}

func (x *AddMoreWidgets) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddMoreWidgets.ProtoReflect.Descriptor instead.
func (*AddMoreWidgets) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{22}
}

func (x *AddMoreWidgets) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *AddMoreWidgets) GetWidgets() []*Widget {
	if x != nil {
		return x.Widgets
	}
	return nil
}

// Deprecated: Use WidgetV2 instead from api/frontend/insights/networth/ui/ui.proto
type Widget struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for the widget. This is to be used by client for caching.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// type of widget that should be rendered.
	Type WidgetType `protobuf:"varint,2,opt,name=type,proto3,enum=frontend.insights.networth.WidgetType" json:"type,omitempty"`
	// Types that are assignable to Params:
	//
	//	*Widget_CardWidgetParams
	Params isWidget_Params `protobuf_oneof:"params"`
	// This enum will tell if client should use the widget from cache or not.
	CacheControl CacheControl `protobuf:"varint,5,opt,name=cache_control,json=cacheControl,proto3,enum=frontend.insights.networth.CacheControl" json:"cache_control,omitempty"`
	// State of widget i.e if it is uninitialized, initialized etc.
	State WidgetState `protobuf:"varint,6,opt,name=state,proto3,enum=frontend.insights.networth.WidgetState" json:"state,omitempty"`
	// payload to be sent in analytics event from client
	// if cache_control is set to use cache, use the payload as well from cache
	WidgetAnalyticsPayload string `protobuf:"bytes,7,opt,name=widget_analytics_payload,json=widgetAnalyticsPayload,proto3" json:"widget_analytics_payload,omitempty"`
}

func (x *Widget) Reset() {
	*x = Widget{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Widget) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Widget) ProtoMessage() {}

func (x *Widget) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Widget.ProtoReflect.Descriptor instead.
func (*Widget) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{23}
}

func (x *Widget) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Widget) GetType() WidgetType {
	if x != nil {
		return x.Type
	}
	return WidgetType_WIDGET_TYPE_UNSPECIFIED
}

func (m *Widget) GetParams() isWidget_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *Widget) GetCardWidgetParams() *CardWidgetParams {
	if x, ok := x.GetParams().(*Widget_CardWidgetParams); ok {
		return x.CardWidgetParams
	}
	return nil
}

func (x *Widget) GetCacheControl() CacheControl {
	if x != nil {
		return x.CacheControl
	}
	return CacheControl_CACHE_CONTROL_UNSPECIFIED
}

func (x *Widget) GetState() WidgetState {
	if x != nil {
		return x.State
	}
	return WidgetState_WIDGET_STATE_UNSPECIFIED
}

func (x *Widget) GetWidgetAnalyticsPayload() string {
	if x != nil {
		return x.WidgetAnalyticsPayload
	}
	return ""
}

type isWidget_Params interface {
	isWidget_Params()
}

type Widget_CardWidgetParams struct {
	// Params for Card type widget
	CardWidgetParams *CardWidgetParams `protobuf:"bytes,3,opt,name=card_widget_params,json=cardWidgetParams,proto3,oneof"`
}

func (*Widget_CardWidgetParams) isWidget_Params() {}

type CardWidgetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Icon to show at the top of card
	Icon *common.VisualElement `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	// title to show below the icon
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// Primary text for the widget to show below title
	PrimaryText *ui.IconTextComponent `protobuf:"bytes,3,opt,name=primary_text,json=primaryText,proto3" json:"primary_text,omitempty"`
	// Value to be used for calculating the total section value and net worth. This can be negative in case of liabilities.
	Value *typesv2.Money `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	// deeplink to open when user clicks on the widget
	Deeplink *deeplink.Deeplink `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// background color of the card
	BackgroundColor string `protobuf:"bytes,6,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	// tag to show at the bottom of the widget
	Tag *ui.IconTextComponent `protobuf:"bytes,7,opt,name=tag,proto3" json:"tag,omitempty"`
}

func (x *CardWidgetParams) Reset() {
	*x = CardWidgetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardWidgetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardWidgetParams) ProtoMessage() {}

func (x *CardWidgetParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardWidgetParams.ProtoReflect.Descriptor instead.
func (*CardWidgetParams) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{24}
}

func (x *CardWidgetParams) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *CardWidgetParams) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CardWidgetParams) GetPrimaryText() *ui.IconTextComponent {
	if x != nil {
		return x.PrimaryText
	}
	return nil
}

func (x *CardWidgetParams) GetValue() *typesv2.Money {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *CardWidgetParams) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *CardWidgetParams) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *CardWidgetParams) GetTag() *ui.IconTextComponent {
	if x != nil {
		return x.Tag
	}
	return nil
}

type GetNetWorthSummaryForHomeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetNetWorthSummaryForHomeRequest) Reset() {
	*x = GetNetWorthSummaryForHomeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthSummaryForHomeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthSummaryForHomeRequest) ProtoMessage() {}

func (x *GetNetWorthSummaryForHomeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthSummaryForHomeRequest.ProtoReflect.Descriptor instead.
func (*GetNetWorthSummaryForHomeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetNetWorthSummaryForHomeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetNetWorthSummaryForHomeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// dashboard info in new home summary format
	DashboardInfo *home.HomeDashboard `protobuf:"bytes,2,opt,name=dashboard_info,json=dashboardInfo,proto3" json:"dashboard_info,omitempty"`
}

func (x *GetNetWorthSummaryForHomeResponse) Reset() {
	*x = GetNetWorthSummaryForHomeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNetWorthSummaryForHomeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNetWorthSummaryForHomeResponse) ProtoMessage() {}

func (x *GetNetWorthSummaryForHomeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNetWorthSummaryForHomeResponse.ProtoReflect.Descriptor instead.
func (*GetNetWorthSummaryForHomeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetNetWorthSummaryForHomeResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetNetWorthSummaryForHomeResponse) GetDashboardInfo() *home.HomeDashboard {
	if x != nil {
		return x.DashboardInfo
	}
	return nil
}

type GetNextNetWorthRefreshActionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req           *header.RequestHeader                                `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	RequestParams *networth_refresh.NetWorthGetNextActionRequestParams `protobuf:"bytes,2,opt,name=request_params,json=requestParams,proto3" json:"request_params,omitempty"`
}

func (x *GetNextNetWorthRefreshActionRequest) Reset() {
	*x = GetNextNetWorthRefreshActionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextNetWorthRefreshActionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextNetWorthRefreshActionRequest) ProtoMessage() {}

func (x *GetNextNetWorthRefreshActionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextNetWorthRefreshActionRequest.ProtoReflect.Descriptor instead.
func (*GetNextNetWorthRefreshActionRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetNextNetWorthRefreshActionRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetNextNetWorthRefreshActionRequest) GetRequestParams() *networth_refresh.NetWorthGetNextActionRequestParams {
	if x != nil {
		return x.RequestParams
	}
	return nil
}

type GetNextNetWorthRefreshActionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink for next action
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	// header for persisting throughout the refresh session
	RefreshHeader *networth_refresh.NetWorthRefreshHeader `protobuf:"bytes,3,opt,name=refresh_header,json=refreshHeader,proto3" json:"refresh_header,omitempty"`
}

func (x *GetNextNetWorthRefreshActionResponse) Reset() {
	*x = GetNextNetWorthRefreshActionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextNetWorthRefreshActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextNetWorthRefreshActionResponse) ProtoMessage() {}

func (x *GetNextNetWorthRefreshActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextNetWorthRefreshActionResponse.ProtoReflect.Descriptor instead.
func (*GetNextNetWorthRefreshActionResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetNextNetWorthRefreshActionResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetNextNetWorthRefreshActionResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *GetNextNetWorthRefreshActionResponse) GetRefreshHeader() *networth_refresh.NetWorthRefreshHeader {
	if x != nil {
		return x.RefreshHeader
	}
	return nil
}

type UpdateManualAssetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Map of 'asset_id -> InputOptionValue'
	// Asset id would correspond to 'ManualAssetsCurrentValueRefreshDetails -> asset_id'
	// current value would be whatever user inputs
	UpdatedAssetCurrentValuesV2 map[string]*InputOptionValue `protobuf:"bytes,3,rep,name=updated_asset_current_values_v2,json=updatedAssetCurrentValuesV2,proto3" json:"updated_asset_current_values_v2,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateManualAssetsRequest) Reset() {
	*x = UpdateManualAssetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateManualAssetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateManualAssetsRequest) ProtoMessage() {}

func (x *UpdateManualAssetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateManualAssetsRequest.ProtoReflect.Descriptor instead.
func (*UpdateManualAssetsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateManualAssetsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *UpdateManualAssetsRequest) GetUpdatedAssetCurrentValuesV2() map[string]*InputOptionValue {
	if x != nil {
		return x.UpdatedAssetCurrentValuesV2
	}
	return nil
}

type UpdateManualAssetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink for next action
	// to be shown in case of success status
	NextScreen *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_screen,json=nextScreen,proto3" json:"next_screen,omitempty"`
}

func (x *UpdateManualAssetsResponse) Reset() {
	*x = UpdateManualAssetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateManualAssetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateManualAssetsResponse) ProtoMessage() {}

func (x *UpdateManualAssetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateManualAssetsResponse.ProtoReflect.Descriptor instead.
func (*UpdateManualAssetsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateManualAssetsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *UpdateManualAssetsResponse) GetNextScreen() *deeplink.Deeplink {
	if x != nil {
		return x.NextScreen
	}
	return nil
}

type SearchAssetFormFieldOptionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// The identifier to use when searching for relevant choices based on user's search-text for the field.
	// Distinguishes one form field from another when both may have the same name.
	// This is a string form of the AssetFormFieldSearchIdentifier enum
	// kept client-independent for backward compatibility.
	SearchIdentifier string `protobuf:"bytes,2,opt,name=search_identifier,json=searchIdentifier,proto3" json:"search_identifier,omitempty"`
	SearchText       string `protobuf:"bytes,3,opt,name=search_text,json=searchText,proto3" json:"search_text,omitempty"`
}

func (x *SearchAssetFormFieldOptionsRequest) Reset() {
	*x = SearchAssetFormFieldOptionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAssetFormFieldOptionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssetFormFieldOptionsRequest) ProtoMessage() {}

func (x *SearchAssetFormFieldOptionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssetFormFieldOptionsRequest.ProtoReflect.Descriptor instead.
func (*SearchAssetFormFieldOptionsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{31}
}

func (x *SearchAssetFormFieldOptionsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *SearchAssetFormFieldOptionsRequest) GetSearchIdentifier() string {
	if x != nil {
		return x.SearchIdentifier
	}
	return ""
}

func (x *SearchAssetFormFieldOptionsRequest) GetSearchText() string {
	if x != nil {
		return x.SearchText
	}
	return ""
}

type SearchAssetFormFieldOptionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// A list of choices relevant to a form field and search-text
	// This list is expected to change based on user's search text, and thus can be empty if there are no relevant choices.
	// Note: The actual list of choices for an input can be long. Hence, only a few relevant options
	// are returned (thus, avoiding requirement of pagination).
	// deprecated in favour of search_response
	//
	// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
	Choices []*PresetChoice `protobuf:"bytes,2,rep,name=choices,proto3" json:"choices,omitempty"`
	// Types that are assignable to SearchResponse:
	//
	//	*SearchAssetFormFieldOptionsResponse_PresetChoicesList
	//	*SearchAssetFormFieldOptionsResponse_ErrorView_
	SearchResponse isSearchAssetFormFieldOptionsResponse_SearchResponse `protobuf_oneof:"search_response"`
}

func (x *SearchAssetFormFieldOptionsResponse) Reset() {
	*x = SearchAssetFormFieldOptionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAssetFormFieldOptionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssetFormFieldOptionsResponse) ProtoMessage() {}

func (x *SearchAssetFormFieldOptionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssetFormFieldOptionsResponse.ProtoReflect.Descriptor instead.
func (*SearchAssetFormFieldOptionsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{32}
}

func (x *SearchAssetFormFieldOptionsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
func (x *SearchAssetFormFieldOptionsResponse) GetChoices() []*PresetChoice {
	if x != nil {
		return x.Choices
	}
	return nil
}

func (m *SearchAssetFormFieldOptionsResponse) GetSearchResponse() isSearchAssetFormFieldOptionsResponse_SearchResponse {
	if m != nil {
		return m.SearchResponse
	}
	return nil
}

func (x *SearchAssetFormFieldOptionsResponse) GetPresetChoicesList() *PresetChoicesList {
	if x, ok := x.GetSearchResponse().(*SearchAssetFormFieldOptionsResponse_PresetChoicesList); ok {
		return x.PresetChoicesList
	}
	return nil
}

func (x *SearchAssetFormFieldOptionsResponse) GetErrorView() *SearchAssetFormFieldOptionsResponse_ErrorView {
	if x, ok := x.GetSearchResponse().(*SearchAssetFormFieldOptionsResponse_ErrorView_); ok {
		return x.ErrorView
	}
	return nil
}

type isSearchAssetFormFieldOptionsResponse_SearchResponse interface {
	isSearchAssetFormFieldOptionsResponse_SearchResponse()
}

type SearchAssetFormFieldOptionsResponse_PresetChoicesList struct {
	PresetChoicesList *PresetChoicesList `protobuf:"bytes,3,opt,name=preset_choices_list,json=presetChoicesList,proto3,oneof"`
}

type SearchAssetFormFieldOptionsResponse_ErrorView_ struct {
	// Error screen to be shown in case of no data found for the requested search text
	ErrorView *SearchAssetFormFieldOptionsResponse_ErrorView `protobuf:"bytes,4,opt,name=error_view,json=errorView,proto3,oneof"`
}

func (*SearchAssetFormFieldOptionsResponse_PresetChoicesList) isSearchAssetFormFieldOptionsResponse_SearchResponse() {
}

func (*SearchAssetFormFieldOptionsResponse_ErrorView_) isSearchAssetFormFieldOptionsResponse_SearchResponse() {
}

type PresetChoicesList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of choices relevant to a form field and search-text
	// This list is expected to change based on user's search text, and thus can be empty if there are no relevant choices.
	// Note: The actual list of choices for an input can be long. Hence, only a few relevant options
	// are returned (thus, avoiding requirement of pagination).
	PresetChoices []*PresetChoice `protobuf:"bytes,1,rep,name=preset_choices,json=presetChoices,proto3" json:"preset_choices,omitempty"`
}

func (x *PresetChoicesList) Reset() {
	*x = PresetChoicesList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PresetChoicesList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresetChoicesList) ProtoMessage() {}

func (x *PresetChoicesList) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresetChoicesList.ProtoReflect.Descriptor instead.
func (*PresetChoicesList) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{33}
}

func (x *PresetChoicesList) GetPresetChoices() []*PresetChoice {
	if x != nil {
		return x.PresetChoices
	}
	return nil
}

type GetCreditScoreSummaryForHomeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetCreditScoreSummaryForHomeRequest) Reset() {
	*x = GetCreditScoreSummaryForHomeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditScoreSummaryForHomeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditScoreSummaryForHomeRequest) ProtoMessage() {}

func (x *GetCreditScoreSummaryForHomeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditScoreSummaryForHomeRequest.ProtoReflect.Descriptor instead.
func (*GetCreditScoreSummaryForHomeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetCreditScoreSummaryForHomeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetCreditScoreSummaryForHomeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// dashboard info in new home summary format
	DashboardInfo *home.HomeDashboard `protobuf:"bytes,2,opt,name=dashboard_info,json=dashboardInfo,proto3" json:"dashboard_info,omitempty"`
}

func (x *GetCreditScoreSummaryForHomeResponse) Reset() {
	*x = GetCreditScoreSummaryForHomeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditScoreSummaryForHomeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditScoreSummaryForHomeResponse) ProtoMessage() {}

func (x *GetCreditScoreSummaryForHomeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditScoreSummaryForHomeResponse.ProtoReflect.Descriptor instead.
func (*GetCreditScoreSummaryForHomeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{35}
}

func (x *GetCreditScoreSummaryForHomeResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetCreditScoreSummaryForHomeResponse) GetDashboardInfo() *home.HomeDashboard {
	if x != nil {
		return x.DashboardInfo
	}
	return nil
}

type GetEpfSummaryForHomeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetEpfSummaryForHomeRequest) Reset() {
	*x = GetEpfSummaryForHomeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEpfSummaryForHomeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEpfSummaryForHomeRequest) ProtoMessage() {}

func (x *GetEpfSummaryForHomeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEpfSummaryForHomeRequest.ProtoReflect.Descriptor instead.
func (*GetEpfSummaryForHomeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetEpfSummaryForHomeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetEpfSummaryForHomeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// dashboard info in new home summary format
	DashboardInfo *home.HomeDashboard `protobuf:"bytes,2,opt,name=dashboard_info,json=dashboardInfo,proto3" json:"dashboard_info,omitempty"`
}

func (x *GetEpfSummaryForHomeResponse) Reset() {
	*x = GetEpfSummaryForHomeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEpfSummaryForHomeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEpfSummaryForHomeResponse) ProtoMessage() {}

func (x *GetEpfSummaryForHomeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEpfSummaryForHomeResponse.ProtoReflect.Descriptor instead.
func (*GetEpfSummaryForHomeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetEpfSummaryForHomeResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetEpfSummaryForHomeResponse) GetDashboardInfo() *home.HomeDashboard {
	if x != nil {
		return x.DashboardInfo
	}
	return nil
}

type GetMfSummaryForHomeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetMfSummaryForHomeRequest) Reset() {
	*x = GetMfSummaryForHomeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMfSummaryForHomeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMfSummaryForHomeRequest) ProtoMessage() {}

func (x *GetMfSummaryForHomeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMfSummaryForHomeRequest.ProtoReflect.Descriptor instead.
func (*GetMfSummaryForHomeRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{38}
}

func (x *GetMfSummaryForHomeRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetMfSummaryForHomeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// dashboard info in new home summary format
	DashboardInfo *home.HomeDashboard `protobuf:"bytes,2,opt,name=dashboard_info,json=dashboardInfo,proto3" json:"dashboard_info,omitempty"`
}

func (x *GetMfSummaryForHomeResponse) Reset() {
	*x = GetMfSummaryForHomeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMfSummaryForHomeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMfSummaryForHomeResponse) ProtoMessage() {}

func (x *GetMfSummaryForHomeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMfSummaryForHomeResponse.ProtoReflect.Descriptor instead.
func (*GetMfSummaryForHomeResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetMfSummaryForHomeResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetMfSummaryForHomeResponse) GetDashboardInfo() *home.HomeDashboard {
	if x != nil {
		return x.DashboardInfo
	}
	return nil
}

type GetWealthBuilderLandingPageRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard request header
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetWealthBuilderLandingPageRequest) Reset() {
	*x = GetWealthBuilderLandingPageRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWealthBuilderLandingPageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWealthBuilderLandingPageRequest) ProtoMessage() {}

func (x *GetWealthBuilderLandingPageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWealthBuilderLandingPageRequest.ProtoReflect.Descriptor instead.
func (*GetWealthBuilderLandingPageRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{40}
}

func (x *GetWealthBuilderLandingPageRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetWealthBuilderLandingPageResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// standard response header
	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// list of scrollable components
	// This will comprise of widgets that can be added in Home screen also
	ScrollableComponents []*orchestrator.ScrollableComponent `protobuf:"bytes,2,rep,name=scrollable_components,json=scrollableComponents,proto3" json:"scrollable_components,omitempty"`
}

func (x *GetWealthBuilderLandingPageResponse) Reset() {
	*x = GetWealthBuilderLandingPageResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWealthBuilderLandingPageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWealthBuilderLandingPageResponse) ProtoMessage() {}

func (x *GetWealthBuilderLandingPageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWealthBuilderLandingPageResponse.ProtoReflect.Descriptor instead.
func (*GetWealthBuilderLandingPageResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{41}
}

func (x *GetWealthBuilderLandingPageResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetWealthBuilderLandingPageResponse) GetScrollableComponents() []*orchestrator.ScrollableComponent {
	if x != nil {
		return x.ScrollableComponents
	}
	return nil
}

type GetAssetImportFlowStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req       *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	AssetType string                `protobuf:"bytes,2,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`
	FlowId    string                `protobuf:"bytes,3,opt,name=flow_id,json=flowId,proto3" json:"flow_id,omitempty"`
}

func (x *GetAssetImportFlowStatusRequest) Reset() {
	*x = GetAssetImportFlowStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetImportFlowStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetImportFlowStatusRequest) ProtoMessage() {}

func (x *GetAssetImportFlowStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetImportFlowStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAssetImportFlowStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{42}
}

func (x *GetAssetImportFlowStatusRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetAssetImportFlowStatusRequest) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

func (x *GetAssetImportFlowStatusRequest) GetFlowId() string {
	if x != nil {
		return x.FlowId
	}
	return ""
}

type GetAssetImportFlowStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Types that are assignable to StatusUpdate:
	//
	//	*GetAssetImportFlowStatusResponse_Status
	//	*GetAssetImportFlowStatusResponse_Results
	StatusUpdate isGetAssetImportFlowStatusResponse_StatusUpdate `protobuf_oneof:"status_update"`
}

func (x *GetAssetImportFlowStatusResponse) Reset() {
	*x = GetAssetImportFlowStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAssetImportFlowStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAssetImportFlowStatusResponse) ProtoMessage() {}

func (x *GetAssetImportFlowStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAssetImportFlowStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAssetImportFlowStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{43}
}

func (x *GetAssetImportFlowStatusResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (m *GetAssetImportFlowStatusResponse) GetStatusUpdate() isGetAssetImportFlowStatusResponse_StatusUpdate {
	if m != nil {
		return m.StatusUpdate
	}
	return nil
}

func (x *GetAssetImportFlowStatusResponse) GetStatus() *assetandanalysis.AssetImportStatusDetails {
	if x, ok := x.GetStatusUpdate().(*GetAssetImportFlowStatusResponse_Status); ok {
		return x.Status
	}
	return nil
}

func (x *GetAssetImportFlowStatusResponse) GetResults() *AssetImportResultsDetails {
	if x, ok := x.GetStatusUpdate().(*GetAssetImportFlowStatusResponse_Results); ok {
		return x.Results
	}
	return nil
}

type isGetAssetImportFlowStatusResponse_StatusUpdate interface {
	isGetAssetImportFlowStatusResponse_StatusUpdate()
}

type GetAssetImportFlowStatusResponse_Status struct {
	// while polling is in progress this property will return details which need to be displayed.
	// this would primarily be used if we wish to update footer details or title or lottie frames
	Status *assetandanalysis.AssetImportStatusDetails `protobuf:"bytes,2,opt,name=status,proto3,oneof"`
}

type GetAssetImportFlowStatusResponse_Results struct {
	// will return the results based on status of fetch
	Results *AssetImportResultsDetails `protobuf:"bytes,3,opt,name=results,proto3,oneof"`
}

func (*GetAssetImportFlowStatusResponse_Status) isGetAssetImportFlowStatusResponse_StatusUpdate() {}

func (*GetAssetImportFlowStatusResponse_Results) isGetAssetImportFlowStatusResponse_StatusUpdate() {}

type AssetImportResultsDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Title stating the current status of data fetch
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Types that are assignable to Results:
	//
	//	*AssetImportResultsDetails_ImportInProgressDetails
	//	*AssetImportResultsDetails_ImportSuccessDetails
	//	*AssetImportResultsDetails_ImportFailureDetails
	Results isAssetImportResultsDetails_Results `protobuf_oneof:"results"`
}

func (x *AssetImportResultsDetails) Reset() {
	*x = AssetImportResultsDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetImportResultsDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetImportResultsDetails) ProtoMessage() {}

func (x *AssetImportResultsDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetImportResultsDetails.ProtoReflect.Descriptor instead.
func (*AssetImportResultsDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{44}
}

func (x *AssetImportResultsDetails) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (m *AssetImportResultsDetails) GetResults() isAssetImportResultsDetails_Results {
	if m != nil {
		return m.Results
	}
	return nil
}

func (x *AssetImportResultsDetails) GetImportInProgressDetails() *assetandanalysis.AssetImportTerminalInProgressDetails {
	if x, ok := x.GetResults().(*AssetImportResultsDetails_ImportInProgressDetails); ok {
		return x.ImportInProgressDetails
	}
	return nil
}

func (x *AssetImportResultsDetails) GetImportSuccessDetails() *AssetImportSuccessDetails {
	if x, ok := x.GetResults().(*AssetImportResultsDetails_ImportSuccessDetails); ok {
		return x.ImportSuccessDetails
	}
	return nil
}

func (x *AssetImportResultsDetails) GetImportFailureDetails() *assetandanalysis.AssetImportTerminalFailureDetails {
	if x, ok := x.GetResults().(*AssetImportResultsDetails_ImportFailureDetails); ok {
		return x.ImportFailureDetails
	}
	return nil
}

type isAssetImportResultsDetails_Results interface {
	isAssetImportResultsDetails_Results()
}

type AssetImportResultsDetails_ImportInProgressDetails struct {
	ImportInProgressDetails *assetandanalysis.AssetImportTerminalInProgressDetails `protobuf:"bytes,2,opt,name=import_in_progress_details,json=importInProgressDetails,proto3,oneof"`
}

type AssetImportResultsDetails_ImportSuccessDetails struct {
	ImportSuccessDetails *AssetImportSuccessDetails `protobuf:"bytes,3,opt,name=import_success_details,json=importSuccessDetails,proto3,oneof"`
}

type AssetImportResultsDetails_ImportFailureDetails struct {
	ImportFailureDetails *assetandanalysis.AssetImportTerminalFailureDetails `protobuf:"bytes,4,opt,name=import_failure_details,json=importFailureDetails,proto3,oneof"`
}

func (*AssetImportResultsDetails_ImportInProgressDetails) isAssetImportResultsDetails_Results() {}

func (*AssetImportResultsDetails_ImportSuccessDetails) isAssetImportResultsDetails_Results() {}

func (*AssetImportResultsDetails_ImportFailureDetails) isAssetImportResultsDetails_Results() {}

// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=134-6218&t=66Wt92ulqSVdD9D2-4
// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=137-8345&t=66Wt92ulqSVdD9D2-4
type AssetImportSuccessDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// text to be shown above the animation eg Net Worth
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// the rolling animation should only be rendered if rolling_animation_details are not nil
	RollingAnimationDetails *NetworthRollingAnimationDetails `protobuf:"bytes,2,opt,name=rolling_animation_details,json=rollingAnimationDetails,proto3" json:"rolling_animation_details,omitempty"`
	// used to update how many funds are connected
	AssetsUpdateLabel          *ui.IconTextComponent       `protobuf:"bytes,3,opt,name=assets_update_label,json=assetsUpdateLabel,proto3" json:"assets_update_label,omitempty"`
	ConnectMoreAssetsComponent *ConnectMoreAssetsComponent `protobuf:"bytes,4,opt,name=connect_more_assets_component,json=connectMoreAssetsComponent,proto3" json:"connect_more_assets_component,omitempty"`
	ExitCta                    *deeplink.Cta               `protobuf:"bytes,5,opt,name=exit_cta,json=exitCta,proto3" json:"exit_cta,omitempty"`
	// based on the status this will pass frames to be loaded on the lottie
	LottieDetails *assetandanalysis.DataFetchLottieDetails `protobuf:"bytes,6,opt,name=lottie_details,json=lottieDetails,proto3" json:"lottie_details,omitempty"`
	// client will use this to show share cta in nav bar top right corner
	ShareCta *ui.IconTextComponent `protobuf:"bytes,7,opt,name=share_cta,json=shareCta,proto3" json:"share_cta,omitempty"`
}

func (x *AssetImportSuccessDetails) Reset() {
	*x = AssetImportSuccessDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AssetImportSuccessDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AssetImportSuccessDetails) ProtoMessage() {}

func (x *AssetImportSuccessDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AssetImportSuccessDetails.ProtoReflect.Descriptor instead.
func (*AssetImportSuccessDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{45}
}

func (x *AssetImportSuccessDetails) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *AssetImportSuccessDetails) GetRollingAnimationDetails() *NetworthRollingAnimationDetails {
	if x != nil {
		return x.RollingAnimationDetails
	}
	return nil
}

func (x *AssetImportSuccessDetails) GetAssetsUpdateLabel() *ui.IconTextComponent {
	if x != nil {
		return x.AssetsUpdateLabel
	}
	return nil
}

func (x *AssetImportSuccessDetails) GetConnectMoreAssetsComponent() *ConnectMoreAssetsComponent {
	if x != nil {
		return x.ConnectMoreAssetsComponent
	}
	return nil
}

func (x *AssetImportSuccessDetails) GetExitCta() *deeplink.Cta {
	if x != nil {
		return x.ExitCta
	}
	return nil
}

func (x *AssetImportSuccessDetails) GetLottieDetails() *assetandanalysis.DataFetchLottieDetails {
	if x != nil {
		return x.LottieDetails
	}
	return nil
}

func (x *AssetImportSuccessDetails) GetShareCta() *ui.IconTextComponent {
	if x != nil {
		return x.ShareCta
	}
	return nil
}

type NetworthRollingAnimationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// value to be used at the end of rolling animation.
	// updated networth value contains value of asset connected
	UpdatedNetworth     *typesv2.Money        `protobuf:"bytes,1,opt,name=updated_networth,json=updatedNetworth,proto3" json:"updated_networth,omitempty"`
	CurrencySymbol      *common.Text          `protobuf:"bytes,2,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	UpdatedNetworthText *ui.IconTextComponent `protobuf:"bytes,3,opt,name=updated_networth_text,json=updatedNetworthText,proto3" json:"updated_networth_text,omitempty"`
}

func (x *NetworthRollingAnimationDetails) Reset() {
	*x = NetworthRollingAnimationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworthRollingAnimationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworthRollingAnimationDetails) ProtoMessage() {}

func (x *NetworthRollingAnimationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworthRollingAnimationDetails.ProtoReflect.Descriptor instead.
func (*NetworthRollingAnimationDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{46}
}

func (x *NetworthRollingAnimationDetails) GetUpdatedNetworth() *typesv2.Money {
	if x != nil {
		return x.UpdatedNetworth
	}
	return nil
}

func (x *NetworthRollingAnimationDetails) GetCurrencySymbol() *common.Text {
	if x != nil {
		return x.CurrencySymbol
	}
	return nil
}

func (x *NetworthRollingAnimationDetails) GetUpdatedNetworthText() *ui.IconTextComponent {
	if x != nil {
		return x.UpdatedNetworthText
	}
	return nil
}

type ConnectMoreAssetsComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
	ConnectMoreCta *deeplink.Cta `protobuf:"bytes,2,opt,name=connect_more_cta,json=connectMoreCta,proto3" json:"connect_more_cta,omitempty"`
	// connect more
	ConnectMore *ui.IconTextComponent `protobuf:"bytes,3,opt,name=connect_more,json=connectMore,proto3" json:"connect_more,omitempty"`
	// background color for the component
	BackgroundColor string `protobuf:"bytes,4,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	CornerRadius    int32  `protobuf:"varint,5,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	// progress bar details
	ProgressBarDetails *ConnectMoreAssetsComponent_ProgressBarDetails `protobuf:"bytes,6,opt,name=progress_bar_details,json=progressBarDetails,proto3" json:"progress_bar_details,omitempty"`
}

func (x *ConnectMoreAssetsComponent) Reset() {
	*x = ConnectMoreAssetsComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectMoreAssetsComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectMoreAssetsComponent) ProtoMessage() {}

func (x *ConnectMoreAssetsComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectMoreAssetsComponent.ProtoReflect.Descriptor instead.
func (*ConnectMoreAssetsComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{47}
}

func (x *ConnectMoreAssetsComponent) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/insights/networth/service.proto.
func (x *ConnectMoreAssetsComponent) GetConnectMoreCta() *deeplink.Cta {
	if x != nil {
		return x.ConnectMoreCta
	}
	return nil
}

func (x *ConnectMoreAssetsComponent) GetConnectMore() *ui.IconTextComponent {
	if x != nil {
		return x.ConnectMore
	}
	return nil
}

func (x *ConnectMoreAssetsComponent) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

func (x *ConnectMoreAssetsComponent) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *ConnectMoreAssetsComponent) GetProgressBarDetails() *ConnectMoreAssetsComponent_ProgressBarDetails {
	if x != nil {
		return x.ProgressBarDetails
	}
	return nil
}

type GetWealthBuilderDashboardComponentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetWealthBuilderDashboardComponentRequest) Reset() {
	*x = GetWealthBuilderDashboardComponentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWealthBuilderDashboardComponentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWealthBuilderDashboardComponentRequest) ProtoMessage() {}

func (x *GetWealthBuilderDashboardComponentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWealthBuilderDashboardComponentRequest.ProtoReflect.Descriptor instead.
func (*GetWealthBuilderDashboardComponentRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{48}
}

func (x *GetWealthBuilderDashboardComponentRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetWealthBuilderDashboardComponentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader                    *header.ResponseHeader             `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	WealthBuilderLandingComponent *ui1.WealthBuilderLandingComponent `protobuf:"bytes,2,opt,name=wealth_builder_landing_component,json=wealthBuilderLandingComponent,proto3" json:"wealth_builder_landing_component,omitempty"`
}

func (x *GetWealthBuilderDashboardComponentResponse) Reset() {
	*x = GetWealthBuilderDashboardComponentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetWealthBuilderDashboardComponentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetWealthBuilderDashboardComponentResponse) ProtoMessage() {}

func (x *GetWealthBuilderDashboardComponentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetWealthBuilderDashboardComponentResponse.ProtoReflect.Descriptor instead.
func (*GetWealthBuilderDashboardComponentResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{49}
}

func (x *GetWealthBuilderDashboardComponentResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetWealthBuilderDashboardComponentResponse) GetWealthBuilderLandingComponent() *ui1.WealthBuilderLandingComponent {
	if x != nil {
		return x.WealthBuilderLandingComponent
	}
	return nil
}

type GetConnectMoreAssetsScreenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req           *header.RequestHeader                 `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	RequestParams *ConnectMoreAssetsScreenRequestParams `protobuf:"bytes,2,opt,name=request_params,json=requestParams,proto3" json:"request_params,omitempty"`
}

func (x *GetConnectMoreAssetsScreenRequest) Reset() {
	*x = GetConnectMoreAssetsScreenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectMoreAssetsScreenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectMoreAssetsScreenRequest) ProtoMessage() {}

func (x *GetConnectMoreAssetsScreenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectMoreAssetsScreenRequest.ProtoReflect.Descriptor instead.
func (*GetConnectMoreAssetsScreenRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{50}
}

func (x *GetConnectMoreAssetsScreenRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetConnectMoreAssetsScreenRequest) GetRequestParams() *ConnectMoreAssetsScreenRequestParams {
	if x != nil {
		return x.RequestParams
	}
	return nil
}

type GetConnectMoreAssetsScreenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader                   *header.ResponseHeader             `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Title                        *common.Text                       `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	CardImage                    *common.VisualElement              `protobuf:"bytes,3,opt,name=card_image,json=cardImage,proto3" json:"card_image,omitempty"`
	WealthBuilderLandingSections []*ui1.WealthBuilderLandingSection `protobuf:"bytes,4,rep,name=wealth_builder_landing_sections,json=wealthBuilderLandingSections,proto3" json:"wealth_builder_landing_sections,omitempty"`
}

func (x *GetConnectMoreAssetsScreenResponse) Reset() {
	*x = GetConnectMoreAssetsScreenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetConnectMoreAssetsScreenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConnectMoreAssetsScreenResponse) ProtoMessage() {}

func (x *GetConnectMoreAssetsScreenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConnectMoreAssetsScreenResponse.ProtoReflect.Descriptor instead.
func (*GetConnectMoreAssetsScreenResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{51}
}

func (x *GetConnectMoreAssetsScreenResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetConnectMoreAssetsScreenResponse) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *GetConnectMoreAssetsScreenResponse) GetCardImage() *common.VisualElement {
	if x != nil {
		return x.CardImage
	}
	return nil
}

func (x *GetConnectMoreAssetsScreenResponse) GetWealthBuilderLandingSections() []*ui1.WealthBuilderLandingSection {
	if x != nil {
		return x.WealthBuilderLandingSections
	}
	return nil
}

type ConnectMoreAssetsScreenRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// asset types which are not connected
	AssetTypes []string `protobuf:"bytes,2,rep,name=asset_types,json=assetTypes,proto3" json:"asset_types,omitempty"`
	// liability types which are not connected
	LiabilityTypes []string `protobuf:"bytes,3,rep,name=liability_types,json=liabilityTypes,proto3" json:"liability_types,omitempty"`
}

func (x *ConnectMoreAssetsScreenRequestParams) Reset() {
	*x = ConnectMoreAssetsScreenRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectMoreAssetsScreenRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectMoreAssetsScreenRequestParams) ProtoMessage() {}

func (x *ConnectMoreAssetsScreenRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectMoreAssetsScreenRequestParams.ProtoReflect.Descriptor instead.
func (*ConnectMoreAssetsScreenRequestParams) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{52}
}

func (x *ConnectMoreAssetsScreenRequestParams) GetAssetTypes() []string {
	if x != nil {
		return x.AssetTypes
	}
	return nil
}

func (x *ConnectMoreAssetsScreenRequestParams) GetLiabilityTypes() []string {
	if x != nil {
		return x.LiabilityTypes
	}
	return nil
}

type SubmitManualFormsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req                *header.RequestHeader                          `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	FormSubmissionData []*SubmitManualFormsRequest_FormSubmissionData `protobuf:"bytes,2,rep,name=form_submission_data,json=formSubmissionData,proto3" json:"form_submission_data,omitempty"`
}

func (x *SubmitManualFormsRequest) Reset() {
	*x = SubmitManualFormsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitManualFormsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitManualFormsRequest) ProtoMessage() {}

func (x *SubmitManualFormsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitManualFormsRequest.ProtoReflect.Descriptor instead.
func (*SubmitManualFormsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{53}
}

func (x *SubmitManualFormsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *SubmitManualFormsRequest) GetFormSubmissionData() []*SubmitManualFormsRequest_FormSubmissionData {
	if x != nil {
		return x.FormSubmissionData
	}
	return nil
}

type SubmitManualFormsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *SubmitManualFormsResponse) Reset() {
	*x = SubmitManualFormsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitManualFormsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitManualFormsResponse) ProtoMessage() {}

func (x *SubmitManualFormsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitManualFormsResponse.ProtoReflect.Descriptor instead.
func (*SubmitManualFormsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{54}
}

func (x *SubmitManualFormsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

type Section_SectionHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Summary to show for the section. It will have title and value. Value will be empty string. It should be overwritten by client.
	Summary *ui.VerticalKeyValuePair `protobuf:"bytes,1,opt,name=summary,proto3" json:"summary,omitempty"`
	// It will give text for add more button.
	AddMoreButton *ui.IconTextComponent `protobuf:"bytes,2,opt,name=add_more_button,json=addMoreButton,proto3" json:"add_more_button,omitempty"`
}

func (x *Section_SectionHeader) Reset() {
	*x = Section_SectionHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Section_SectionHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Section_SectionHeader) ProtoMessage() {}

func (x *Section_SectionHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Section_SectionHeader.ProtoReflect.Descriptor instead.
func (*Section_SectionHeader) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{21, 0}
}

func (x *Section_SectionHeader) GetSummary() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.Summary
	}
	return nil
}

func (x *Section_SectionHeader) GetAddMoreButton() *ui.IconTextComponent {
	if x != nil {
		return x.AddMoreButton
	}
	return nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=4999-42637&t=a9T3DDR2aPDrylRC-4
type SearchAssetFormFieldOptionsResponse_ErrorView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image        *common.VisualElement    `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	KeyValuePair *ui.VerticalKeyValuePair `protobuf:"bytes,2,opt,name=key_value_pair,json=keyValuePair,proto3" json:"key_value_pair,omitempty"`
	// redirection to bottom sheet explaining about the error occurred
	// e.g. for employer not found error screen
	InfoRedirection *ui.IconTextComponent `protobuf:"bytes,3,opt,name=info_redirection,json=infoRedirection,proto3" json:"info_redirection,omitempty"`
}

func (x *SearchAssetFormFieldOptionsResponse_ErrorView) Reset() {
	*x = SearchAssetFormFieldOptionsResponse_ErrorView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchAssetFormFieldOptionsResponse_ErrorView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAssetFormFieldOptionsResponse_ErrorView) ProtoMessage() {}

func (x *SearchAssetFormFieldOptionsResponse_ErrorView) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAssetFormFieldOptionsResponse_ErrorView.ProtoReflect.Descriptor instead.
func (*SearchAssetFormFieldOptionsResponse_ErrorView) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{32, 0}
}

func (x *SearchAssetFormFieldOptionsResponse_ErrorView) GetImage() *common.VisualElement {
	if x != nil {
		return x.Image
	}
	return nil
}

func (x *SearchAssetFormFieldOptionsResponse_ErrorView) GetKeyValuePair() *ui.VerticalKeyValuePair {
	if x != nil {
		return x.KeyValuePair
	}
	return nil
}

func (x *SearchAssetFormFieldOptionsResponse_ErrorView) GetInfoRedirection() *ui.IconTextComponent {
	if x != nil {
		return x.InfoRedirection
	}
	return nil
}

type ConnectMoreAssetsComponent_ProgressBarDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CompletedPercentage int32  `protobuf:"varint,1,opt,name=completed_percentage,json=completedPercentage,proto3" json:"completed_percentage,omitempty"`
	ForegroundColor     string `protobuf:"bytes,2,opt,name=foreground_color,json=foregroundColor,proto3" json:"foreground_color,omitempty"`
	BackgroundColor     string `protobuf:"bytes,3,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
}

func (x *ConnectMoreAssetsComponent_ProgressBarDetails) Reset() {
	*x = ConnectMoreAssetsComponent_ProgressBarDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectMoreAssetsComponent_ProgressBarDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectMoreAssetsComponent_ProgressBarDetails) ProtoMessage() {}

func (x *ConnectMoreAssetsComponent_ProgressBarDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectMoreAssetsComponent_ProgressBarDetails.ProtoReflect.Descriptor instead.
func (*ConnectMoreAssetsComponent_ProgressBarDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{47, 0}
}

func (x *ConnectMoreAssetsComponent_ProgressBarDetails) GetCompletedPercentage() int32 {
	if x != nil {
		return x.CompletedPercentage
	}
	return 0
}

func (x *ConnectMoreAssetsComponent_ProgressBarDetails) GetForegroundColor() string {
	if x != nil {
		return x.ForegroundColor
	}
	return ""
}

func (x *ConnectMoreAssetsComponent_ProgressBarDetails) GetBackgroundColor() string {
	if x != nil {
		return x.BackgroundColor
	}
	return ""
}

type SubmitManualFormsRequest_FormSubmissionData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FormIdentifier *typesv2.ManualAssetFormIdentifier `protobuf:"bytes,1,opt,name=form_identifier,json=formIdentifier,proto3" json:"form_identifier,omitempty"`
	FormData       []*NetWorthManualInputData         `protobuf:"bytes,2,rep,name=form_data,json=formData,proto3" json:"form_data,omitempty"`
}

func (x *SubmitManualFormsRequest_FormSubmissionData) Reset() {
	*x = SubmitManualFormsRequest_FormSubmissionData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitManualFormsRequest_FormSubmissionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitManualFormsRequest_FormSubmissionData) ProtoMessage() {}

func (x *SubmitManualFormsRequest_FormSubmissionData) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitManualFormsRequest_FormSubmissionData.ProtoReflect.Descriptor instead.
func (*SubmitManualFormsRequest_FormSubmissionData) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_service_proto_rawDescGZIP(), []int{53, 0}
}

func (x *SubmitManualFormsRequest_FormSubmissionData) GetFormIdentifier() *typesv2.ManualAssetFormIdentifier {
	if x != nil {
		return x.FormIdentifier
	}
	return nil
}

func (x *SubmitManualFormsRequest_FormSubmissionData) GetFormData() []*NetWorthManualInputData {
	if x != nil {
		return x.FormData
	}
	return nil
}

var File_api_frontend_insights_networth_service_proto protoreflect.FileDescriptor

var file_api_frontend_insights_networth_service_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x5f,
	0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x2f,
	0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x6f,
	0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x75, 0x69, 0x2f, 0x75, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x48,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x54, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x2f, 0x6e, 0x65, 0x74, 0x5f, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x6c, 0x65,
	0x2f, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x69,
	0x74, 0x69, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x80, 0x01, 0x0a, 0x17, 0x4d, 0x61, 0x67, 0x69, 0x63,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x12, 0x33, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x6c, 0x65, 0x2e, 0x46, 0x69,
	0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0xd6, 0x01, 0x0a, 0x18, 0x4d, 0x61,
	0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x78, 0x0a, 0x1b, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4d, 0x61, 0x67, 0x69, 0x63,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x18, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x22, 0x76, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x28, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x22, 0x5d, 0x0a, 0x19, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x7a, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x26, 0x0a,
	0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb5, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73,
	0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x09, 0x64,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4e, 0x65, 0x74, 0x57,
	0x6f, 0x72, 0x74, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x09, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x22, 0xf4, 0x01,
	0x0a, 0x17, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f,
	0x72, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x4f, 0x0a, 0x0f, 0x66,
	0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f,
	0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x52, 0x0e, 0x66, 0x6f,
	0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x09,
	0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x66, 0x6f, 0x72, 0x6d, 0x44, 0x61, 0x74, 0x61, 0x4a, 0x04,
	0x08, 0x04, 0x10, 0x05, 0x22, 0x5c, 0x0a, 0x18, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x22, 0x9f, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03,
	0x72, 0x65, 0x71, 0x12, 0x4f, 0x0a, 0x0f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x22, 0xba, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x0d, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f,
	0x72, 0x74, 0x68, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x0c, 0x66,
	0x6f, 0x72, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4a, 0x04, 0x08, 0x03, 0x10,
	0x04, 0x22, 0xd4, 0x03, 0x0a, 0x19, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x63,
	0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65,
	0x71, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x42, 0x17,
	0xfa, 0x42, 0x14, 0x12, 0x12, 0x11, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x59, 0x40, 0x21, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02,
	0x08, 0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x5d, 0x0a, 0x0c, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4d,
	0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x64,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x0c, 0x64, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x09, 0xfa, 0x42, 0x06, 0x72, 0x04, 0x10, 0x01, 0x18, 0x64, 0x52, 0x0b, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5e, 0x0a, 0x1a, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x76, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x25, 0x0a, 0x0e, 0x64, 0x61, 0x73,
	0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x22, 0xc8, 0x04, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68,
	0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x72, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x42, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x12, 0x57, 0x0a, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x08,
	0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x30, 0x0a, 0x06, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x6f,
	0x74, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0a,
	0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x4f, 0x0a, 0x12, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x74, 0x61,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x74, 0x61, 0x22, 0x1e, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x6b, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22, 0xb0, 0x01, 0x0a, 0x09,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x3f, 0x0a, 0x07, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x00, 0x52, 0x07, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x55, 0x0a, 0x0f, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x00, 0x52, 0x0e, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0x92,
	0x02, 0x0a, 0x0e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x73, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x03, 0x63, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12,
	0x53, 0x0a, 0x10, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x3d, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x22, 0xd0, 0x01, 0x0a, 0x15, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x5c, 0x0a,
	0x12, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x4f, 0x0a, 0x0d, 0x69,
	0x73, 0x6c, 0x61, 0x6e, 0x64, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x49, 0x73, 0x6c, 0x61, 0x6e, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x0c,
	0x69, 0x73, 0x6c, 0x61, 0x6e, 0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x08, 0x0a, 0x06,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xb3, 0x02, 0x0a, 0x0c, 0x49, 0x73, 0x6c, 0x61, 0x6e,
	0x64, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x55, 0x0a, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x0a, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12, 0x4c,
	0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x0f, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x4a, 0x0a, 0x07,
	0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61,
	0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x52,
	0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x32, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x90, 0x01, 0x0a,
	0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x3c, 0x0a, 0x1b, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x77, 0x69, 0x64, 0x74, 0x68, 0x5f, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x17, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x57, 0x69, 0x64, 0x74, 0x68, 0x52, 0x61, 0x74, 0x69,
	0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x73, 0x70, 0x65, 0x63,
	0x74, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x52, 0x10, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x41, 0x73, 0x70, 0x65, 0x63, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x22,
	0xa2, 0x02, 0x0a, 0x14, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x37, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75,
	0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x5f, 0x61, 0x64, 0x64, 0x65, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x41, 0x64, 0x64, 0x65, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x63,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c,
	0x6f, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x64, 0x6f, 0x77, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xad, 0x01, 0x0a, 0x19, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x74,
	0x65, 0x6d, 0x12, 0x46, 0x0a, 0x0d, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x5f, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x0c, 0x0a, 0x01, 0x78, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x01, 0x78, 0x12, 0x0c, 0x0a, 0x01, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x01, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x02, 0x52, 0x05, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x16, 0x0a, 0x06,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x68, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x22, 0xcd, 0x04, 0x0a, 0x07, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x42, 0x0a, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x4d, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x5f, 0x6d, 0x6f, 0x72, 0x65,
	0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x4d, 0x6f, 0x72, 0x65, 0x42, 0x75, 0x74,
	0x74, 0x6f, 0x6e, 0x12, 0x54, 0x0a, 0x10, 0x61, 0x64, 0x64, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x5f,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x41, 0x64, 0x64, 0x4d, 0x6f,
	0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x4d, 0x6f,
	0x72, 0x65, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x12, 0x3c, 0x0a, 0x07, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x52, 0x07,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x58, 0x0a,
	0x0e, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x1a, 0x9a, 0x01, 0x0a, 0x0d, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3e, 0x0a, 0x07, 0x73, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74,
	0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72,
	0x52, 0x07, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x12, 0x49, 0x0a, 0x0f, 0x61, 0x64, 0x64,
	0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x4d, 0x6f, 0x72, 0x65, 0x42, 0x75,
	0x74, 0x74, 0x6f, 0x6e, 0x22, 0x64, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x4d, 0x6f, 0x72, 0x65, 0x57,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3c, 0x0a, 0x07,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x52, 0x07, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x22, 0x84, 0x03, 0x0a, 0x06, 0x57,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3a, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x5c, 0x0a, 0x12, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x57,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x10, 0x63,
	0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x4d, 0x0a, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c,
	0x52, 0x0c, 0x63, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x3d,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x38, 0x0a,
	0x18, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x08, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0x82, 0x03, 0x0a, 0x10, 0x43, 0x61, 0x72, 0x64, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e, 0x12, 0x2e, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x44, 0x0a,
	0x0c, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x54,
	0x65, 0x78, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x37, 0x0a,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x33, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x03, 0x74, 0x61, 0x67, 0x22, 0x5e, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48,
	0x6f, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65,
	0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xaa, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x4e, 0x65,
	0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72,
	0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x43,
	0x0a, 0x0e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x6f, 0x6d, 0x65, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x52, 0x0d, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0xeb, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x87, 0x01, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x60, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x2e,
	0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x22, 0xa2, 0x02, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b,
	0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a,
	0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x7a, 0x0a, 0x0e, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x53, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xfa, 0x02, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x9c, 0x01, 0x0a, 0x1f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x5f, 0x76, 0x32, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x56, 0x32, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x1b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x56, 0x32, 0x1a,
	0x7c, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x73, 0x73, 0x65, 0x74, 0x43,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x56, 0x32, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x42, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x4a, 0x04, 0x08,
	0x02, 0x10, 0x03, 0x22, 0x9c, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61,
	0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x22, 0xae, 0x01, 0x0a, 0x22, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54,
	0x65, 0x78, 0x74, 0x22, 0xf0, 0x04, 0x0a, 0x23, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x46, 0x0a,
	0x07, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x50, 0x72, 0x65, 0x73,
	0x65, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x63, 0x68,
	0x6f, 0x69, 0x63, 0x65, 0x73, 0x12, 0x5f, 0x0a, 0x13, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f,
	0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x4c, 0x69, 0x73,
	0x74, 0x48, 0x00, 0x52, 0x11, 0x70, 0x72, 0x65, 0x73, 0x65, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63,
	0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6a, 0x0a, 0x0a, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x76, 0x69, 0x65, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x56, 0x69, 0x65, 0x77, 0x48, 0x00, 0x52, 0x09, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x56, 0x69,
	0x65, 0x77, 0x1a, 0xde, 0x01, 0x0a, 0x09, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x37, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x4a, 0x0a, 0x0e, 0x6b, 0x65, 0x79,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x70, 0x61, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x4b, 0x65, 0x79, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x50, 0x61, 0x69, 0x72, 0x52, 0x0c, 0x6b, 0x65, 0x79, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x50, 0x61, 0x69, 0x72, 0x12, 0x4c, 0x0a, 0x10, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x72, 0x65,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0f, 0x69, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x11, 0x0a, 0x0f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x64, 0x0a, 0x11, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74,
	0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x4f, 0x0a, 0x0e, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x63, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x50, 0x72, 0x65, 0x73, 0x65, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x52, 0x0d, 0x70,
	0x72, 0x65, 0x73, 0x65, 0x74, 0x43, 0x68, 0x6f, 0x69, 0x63, 0x65, 0x73, 0x22, 0x61, 0x0a, 0x23,
	0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x75,
	0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22,
	0xad, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x0e, 0x64, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x6f,
	0x6d, 0x65, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x0d, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x59, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x45, 0x70, 0x66, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xa5, 0x01, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x45, 0x70, 0x66, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48,
	0x6f, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x43, 0x0a,
	0x0e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x6f, 0x6d, 0x65, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x52, 0x0d, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x58, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4d, 0x66, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xa4, 0x01, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x4d, 0x66, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72,
	0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x43,
	0x0a, 0x0e, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x6f, 0x6d, 0x65, 0x2e, 0x48, 0x6f, 0x6d, 0x65, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x52, 0x0d, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0x60, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0xcd, 0x01, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x64, 0x0a, 0x15, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x6f, 0x6d, 0x65, 0x2e, 0x6f,
	0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x53, 0x63, 0x72, 0x6f,
	0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x14, 0x73, 0x63, 0x72, 0x6f, 0x6c, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x8b, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x6c, 0x6f,
	0x77, 0x49, 0x64, 0x22, 0xb1, 0x02, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x67, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4d, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x51, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x07, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0xf1, 0x03, 0x0a, 0x19, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x98, 0x01, 0x0a, 0x1a, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x59, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x65, 0x72, 0x6d,
	0x69, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x17, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x49,
	0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x6d, 0x0a, 0x16, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x14, 0x69, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x8e, 0x01, 0x0a, 0x16, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x56, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x2e, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x69, 0x6e, 0x61, 0x6c, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x14, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x42, 0x09, 0x0a, 0x07, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x73, 0x22, 0xf9, 0x04, 0x0a, 0x19,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x77, 0x0a, 0x19, 0x72, 0x6f, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x52, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x17, 0x72, 0x6f, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x51, 0x0a, 0x13, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x11, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x79, 0x0a, 0x1d, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x4d, 0x6f, 0x72, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x1a, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x72,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x12, 0x31, 0x0a, 0x08, 0x65, 0x78, 0x69, 0x74, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x07, 0x65, 0x78, 0x69, 0x74,
	0x43, 0x74, 0x61, 0x12, 0x72, 0x0a, 0x0e, 0x6c, 0x6f, 0x74, 0x74, 0x69, 0x65, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x61, 0x73, 0x73, 0x65, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4c, 0x6f, 0x74, 0x74, 0x69,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0d, 0x6c, 0x6f, 0x74, 0x74, 0x69, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x09, 0x73, 0x68, 0x61, 0x72, 0x65,
	0x5f, 0x63, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x43, 0x74, 0x61, 0x22, 0xfa, 0x01, 0x0a, 0x1f, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x52, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x6e, 0x69, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3d, 0x0a, 0x10, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x12, 0x41, 0x0a, 0x0f, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x63,
	0x75, 0x72, 0x72, 0x65, 0x6e, 0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x55, 0x0a,
	0x15, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x13, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x54, 0x65, 0x78, 0x74, 0x22, 0xce, 0x04, 0x0a, 0x1a, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x4d, 0x6f, 0x72, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x44, 0x0a, 0x10,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x72, 0x65, 0x43,
	0x74, 0x61, 0x12, 0x44, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x6d, 0x6f,
	0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x72, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61, 0x63, 0x6b,
	0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61,
	0x64, 0x69, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e,
	0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x7b, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x62, 0x61, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x72, 0x65, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x73, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x42, 0x61, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x9d, 0x01, 0x0a, 0x12, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x42, 0x61, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x31, 0x0a, 0x14,
	0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x13, 0x63, 0x6f, 0x6d, 0x70,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x29, 0x0a, 0x10, 0x66, 0x6f, 0x72, 0x65, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f,
	0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x6f, 0x72, 0x65, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x62, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x5d, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x22, 0xf6, 0x01, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x85, 0x01, 0x0a, 0x20, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69,
	0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x1d,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0xbe, 0x01,
	0x0a, 0x21, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x72, 0x65,
	0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x67, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x4d, 0x6f, 0x72, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xdc,
	0x02, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x72,
	0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x09,
	0x63, 0x61, 0x72, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x81, 0x01, 0x0a, 0x1f, 0x77, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6c, 0x61, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x75, 0x69, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65,
	0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x1c, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x70, 0x0a,
	0x24, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x72, 0x65, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0e, 0x6c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x73, 0x22,
	0x81, 0x03, 0x0a, 0x18, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03,
	0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x79,
	0x0a, 0x14, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x47, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74,
	0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x12, 0x66, 0x6f, 0x72, 0x6d, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x1a, 0xb7, 0x01, 0x0a, 0x12, 0x46, 0x6f,
	0x72, 0x6d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x4f, 0x0a, 0x0f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x52, 0x0e, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x12, 0x50, 0x0a, 0x09, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c,
	0x49, 0x6e, 0x70, 0x75, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x08, 0x66, 0x6f, 0x72, 0x6d, 0x44,
	0x61, 0x74, 0x61, 0x22, 0x5d, 0x0a, 0x19, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2a, 0x4b, 0x0a, 0x14, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x44, 0x45,
	0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x46, 0x44, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x2a,
	0x56, 0x0a, 0x11, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x1e, 0x56, 0x49, 0x53, 0x55, 0x41, 0x4c, 0x49, 0x53,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x56, 0x49, 0x53, 0x55,
	0x41, 0x4c, 0x49, 0x53, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49,
	0x53, 0x4c, 0x41, 0x4e, 0x44, 0x10, 0x01, 0x2a, 0x3f, 0x0a, 0x0a, 0x57, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x01, 0x2a, 0x4a, 0x0a, 0x0c, 0x43, 0x61, 0x63, 0x68,
	0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x43, 0x48,
	0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41, 0x43, 0x48, 0x45,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x43, 0x41, 0x43,
	0x48, 0x45, 0x10, 0x01, 0x2a, 0x69, 0x0a, 0x0b, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x55, 0x4e, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x1c, 0x0a, 0x18, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x02, 0x32,
	0xb4, 0x18, 0x0a, 0x08, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x12, 0x9a, 0x01, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x4e,
	0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88,
	0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x94, 0x01, 0x0a, 0x12, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x44, 0x65, 0x63, 0x6c,
	0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00,
	0x12, 0x97, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f,
	0x72, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46,
	0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x8e, 0x01, 0x0a, 0x10, 0x53,
	0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x12,
	0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67,
	0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f,
	0x72, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xa3, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x12, 0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0x91, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61,
	0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00,
	0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xa9, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48,
	0x6f, 0x6d, 0x65, 0x12, 0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47,
	0x65, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0xb2, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x65, 0x74,
	0x57, 0x6f, 0x72, 0x74, 0x68, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68, 0x52,
	0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x4e, 0x65, 0x74, 0x57, 0x6f, 0x72, 0x74, 0x68,
	0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x94, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x12, 0x35, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e,
	0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xaf, 0x01,
	0x0a, 0x1b, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72,
	0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x41, 0x73, 0x73, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f,
	0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12,
	0xb2, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65,
	0x12, 0x3f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69,
	0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x40, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47,
	0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0,
	0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x9a, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x45, 0x70, 0x66, 0x53,
	0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x12, 0x37, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x70,
	0x66, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x70, 0x66, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72,
	0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0x97, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x66, 0x53, 0x75, 0x6d, 0x6d, 0x61,
	0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x12, 0x36, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x66, 0x53, 0x75, 0x6d, 0x6d,
	0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x66, 0x53, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79, 0x46, 0x6f, 0x72, 0x48, 0x6f,
	0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xaf, 0x01, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72,
	0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x50, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e,
	0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xab, 0x01,
	0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74,
	0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x49, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xac, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x6f, 0x72, 0x65, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x3d, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x4d, 0x6f, 0x72, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x4d, 0x6f, 0x72, 0x65, 0x41, 0x73, 0x73, 0x65, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xc4, 0x01, 0x0a, 0x22, 0x47,
	0x65, 0x74, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x45, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47,
	0x65, 0x74, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x47, 0x65, 0x74, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42,
	0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x12, 0x8e, 0x01, 0x0a, 0x10, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x12, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46,
	0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0x91, 0x01, 0x0a, 0x11, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e,
	0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75,
	0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72, 0x6d, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a,
	0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x6e, 0x0a, 0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x5a,
	0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_insights_networth_service_proto_rawDescOnce sync.Once
	file_api_frontend_insights_networth_service_proto_rawDescData = file_api_frontend_insights_networth_service_proto_rawDesc
)

func file_api_frontend_insights_networth_service_proto_rawDescGZIP() []byte {
	file_api_frontend_insights_networth_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_insights_networth_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_insights_networth_service_proto_rawDescData)
	})
	return file_api_frontend_insights_networth_service_proto_rawDescData
}

var file_api_frontend_insights_networth_service_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_frontend_insights_networth_service_proto_msgTypes = make([]protoimpl.MessageInfo, 60)
var file_api_frontend_insights_networth_service_proto_goTypes = []interface{}{
	(ManualAddDepositType)(0),                                     // 0: frontend.insights.networth.ManualAddDepositType
	(VisualisationType)(0),                                        // 1: frontend.insights.networth.VisualisationType
	(WidgetType)(0),                                               // 2: frontend.insights.networth.WidgetType
	(CacheControl)(0),                                             // 3: frontend.insights.networth.CacheControl
	(WidgetState)(0),                                              // 4: frontend.insights.networth.WidgetState
	(GetNetWorthDashboardResponse_Status)(0),                      // 5: frontend.insights.networth.GetNetWorthDashboardResponse.Status
	(*MagicImportFilesRequest)(nil),                               // 6: frontend.insights.networth.MagicImportFilesRequest
	(*MagicImportFilesResponse)(nil),                              // 7: frontend.insights.networth.MagicImportFilesResponse
	(*DeleteManualAssetRequest)(nil),                              // 8: frontend.insights.networth.DeleteManualAssetRequest
	(*DeleteManualAssetResponse)(nil),                             // 9: frontend.insights.networth.DeleteManualAssetResponse
	(*GetManualAssetDashboardRequest)(nil),                        // 10: frontend.insights.networth.GetManualAssetDashboardRequest
	(*GetManualAssetDashboardResponse)(nil),                       // 11: frontend.insights.networth.GetManualAssetDashboardResponse
	(*SubmitManualFormRequest)(nil),                               // 12: frontend.insights.networth.SubmitManualFormRequest
	(*SubmitManualFormResponse)(nil),                              // 13: frontend.insights.networth.SubmitManualFormResponse
	(*GetManualFormConfigRequest)(nil),                            // 14: frontend.insights.networth.GetManualFormConfigRequest
	(*GetManualFormConfigResponse)(nil),                           // 15: frontend.insights.networth.GetManualFormConfigResponse
	(*DepositDeclarationRequest)(nil),                             // 16: frontend.insights.networth.DepositDeclarationRequest
	(*DepositDeclarationResponse)(nil),                            // 17: frontend.insights.networth.DepositDeclarationResponse
	(*GetNetWorthDashboardRequest)(nil),                           // 18: frontend.insights.networth.GetNetWorthDashboardRequest
	(*GetNetWorthDashboardResponse)(nil),                          // 19: frontend.insights.networth.GetNetWorthDashboardResponse
	(*Component)(nil),                                             // 20: frontend.insights.networth.Component
	(*SecretsSection)(nil),                                        // 21: frontend.insights.networth.SecretsSection
	(*NetWorthVisualisation)(nil),                                 // 22: frontend.insights.networth.NetWorthVisualisation
	(*IslandParams)(nil),                                          // 23: frontend.insights.networth.IslandParams
	(*Image)(nil),                                                 // 24: frontend.insights.networth.Image
	(*VisualisationSummary)(nil),                                  // 25: frontend.insights.networth.VisualisationSummary
	(*CategoryVisualisationItem)(nil),                             // 26: frontend.insights.networth.CategoryVisualisationItem
	(*Section)(nil),                                               // 27: frontend.insights.networth.Section
	(*AddMoreWidgets)(nil),                                        // 28: frontend.insights.networth.AddMoreWidgets
	(*Widget)(nil),                                                // 29: frontend.insights.networth.Widget
	(*CardWidgetParams)(nil),                                      // 30: frontend.insights.networth.CardWidgetParams
	(*GetNetWorthSummaryForHomeRequest)(nil),                      // 31: frontend.insights.networth.GetNetWorthSummaryForHomeRequest
	(*GetNetWorthSummaryForHomeResponse)(nil),                     // 32: frontend.insights.networth.GetNetWorthSummaryForHomeResponse
	(*GetNextNetWorthRefreshActionRequest)(nil),                   // 33: frontend.insights.networth.GetNextNetWorthRefreshActionRequest
	(*GetNextNetWorthRefreshActionResponse)(nil),                  // 34: frontend.insights.networth.GetNextNetWorthRefreshActionResponse
	(*UpdateManualAssetsRequest)(nil),                             // 35: frontend.insights.networth.UpdateManualAssetsRequest
	(*UpdateManualAssetsResponse)(nil),                            // 36: frontend.insights.networth.UpdateManualAssetsResponse
	(*SearchAssetFormFieldOptionsRequest)(nil),                    // 37: frontend.insights.networth.SearchAssetFormFieldOptionsRequest
	(*SearchAssetFormFieldOptionsResponse)(nil),                   // 38: frontend.insights.networth.SearchAssetFormFieldOptionsResponse
	(*PresetChoicesList)(nil),                                     // 39: frontend.insights.networth.PresetChoicesList
	(*GetCreditScoreSummaryForHomeRequest)(nil),                   // 40: frontend.insights.networth.GetCreditScoreSummaryForHomeRequest
	(*GetCreditScoreSummaryForHomeResponse)(nil),                  // 41: frontend.insights.networth.GetCreditScoreSummaryForHomeResponse
	(*GetEpfSummaryForHomeRequest)(nil),                           // 42: frontend.insights.networth.GetEpfSummaryForHomeRequest
	(*GetEpfSummaryForHomeResponse)(nil),                          // 43: frontend.insights.networth.GetEpfSummaryForHomeResponse
	(*GetMfSummaryForHomeRequest)(nil),                            // 44: frontend.insights.networth.GetMfSummaryForHomeRequest
	(*GetMfSummaryForHomeResponse)(nil),                           // 45: frontend.insights.networth.GetMfSummaryForHomeResponse
	(*GetWealthBuilderLandingPageRequest)(nil),                    // 46: frontend.insights.networth.GetWealthBuilderLandingPageRequest
	(*GetWealthBuilderLandingPageResponse)(nil),                   // 47: frontend.insights.networth.GetWealthBuilderLandingPageResponse
	(*GetAssetImportFlowStatusRequest)(nil),                       // 48: frontend.insights.networth.GetAssetImportFlowStatusRequest
	(*GetAssetImportFlowStatusResponse)(nil),                      // 49: frontend.insights.networth.GetAssetImportFlowStatusResponse
	(*AssetImportResultsDetails)(nil),                             // 50: frontend.insights.networth.AssetImportResultsDetails
	(*AssetImportSuccessDetails)(nil),                             // 51: frontend.insights.networth.AssetImportSuccessDetails
	(*NetworthRollingAnimationDetails)(nil),                       // 52: frontend.insights.networth.NetworthRollingAnimationDetails
	(*ConnectMoreAssetsComponent)(nil),                            // 53: frontend.insights.networth.ConnectMoreAssetsComponent
	(*GetWealthBuilderDashboardComponentRequest)(nil),             // 54: frontend.insights.networth.GetWealthBuilderDashboardComponentRequest
	(*GetWealthBuilderDashboardComponentResponse)(nil),            // 55: frontend.insights.networth.GetWealthBuilderDashboardComponentResponse
	(*GetConnectMoreAssetsScreenRequest)(nil),                     // 56: frontend.insights.networth.GetConnectMoreAssetsScreenRequest
	(*GetConnectMoreAssetsScreenResponse)(nil),                    // 57: frontend.insights.networth.GetConnectMoreAssetsScreenResponse
	(*ConnectMoreAssetsScreenRequestParams)(nil),                  // 58: frontend.insights.networth.ConnectMoreAssetsScreenRequestParams
	(*SubmitManualFormsRequest)(nil),                              // 59: frontend.insights.networth.SubmitManualFormsRequest
	(*SubmitManualFormsResponse)(nil),                             // 60: frontend.insights.networth.SubmitManualFormsResponse
	(*Section_SectionHeader)(nil),                                 // 61: frontend.insights.networth.Section.SectionHeader
	nil,                                                           // 62: frontend.insights.networth.UpdateManualAssetsRequest.UpdatedAssetCurrentValuesV2Entry
	(*SearchAssetFormFieldOptionsResponse_ErrorView)(nil),         // 63: frontend.insights.networth.SearchAssetFormFieldOptionsResponse.ErrorView
	(*ConnectMoreAssetsComponent_ProgressBarDetails)(nil),         // 64: frontend.insights.networth.ConnectMoreAssetsComponent.ProgressBarDetails
	(*SubmitManualFormsRequest_FormSubmissionData)(nil),           // 65: frontend.insights.networth.SubmitManualFormsRequest.FormSubmissionData
	(*header.RequestHeader)(nil),                                  // 66: frontend.header.RequestHeader
	(*file.File)(nil),                                             // 67: api.typesv2.common.file.File
	(*header.ResponseHeader)(nil),                                 // 68: frontend.header.ResponseHeader
	(*MagicImportedAssetsListScreen)(nil),                         // 69: frontend.insights.networth.MagicImportedAssetsListScreen
	(*NetWorthAssetDashboard)(nil),                                // 70: frontend.insights.networth.NetWorthAssetDashboard
	(*typesv2.ManualAssetFormIdentifier)(nil),                     // 71: api.typesv2.ManualAssetFormIdentifier
	(*NetWorthManualInputData)(nil),                               // 72: frontend.insights.networth.NetWorthManualInputData
	(*NetWorthManualForm)(nil),                                    // 73: frontend.insights.networth.NetWorthManualForm
	(*typesv2.Money)(nil),                                         // 74: api.typesv2.Money
	(*timestamppb.Timestamp)(nil),                                 // 75: google.protobuf.Timestamp
	(*ui.IconTextComponent)(nil),                                  // 76: api.typesv2.ui.IconTextComponent
	(*common.Text)(nil),                                           // 77: api.typesv2.common.Text
	(*secrets.SecretSummary)(nil),                                 // 78: frontend.insights.secrets.SecretSummary
	(*common.VisualElement)(nil),                                  // 79: api.typesv2.common.VisualElement
	(*ui.VerticalKeyValuePair)(nil),                               // 80: api.typesv2.ui.VerticalKeyValuePair
	(*deeplink.Deeplink)(nil),                                     // 81: frontend.deeplink.Deeplink
	(*home.HomeDashboard)(nil),                                    // 82: frontend.home.HomeDashboard
	(*networth_refresh.NetWorthGetNextActionRequestParams)(nil),   // 83: api.typesv2.deeplink_screen_option.insights.networth_refresh.NetWorthGetNextActionRequestParams
	(*networth_refresh.NetWorthRefreshHeader)(nil),                // 84: api.typesv2.deeplink_screen_option.insights.networth_refresh.NetWorthRefreshHeader
	(*PresetChoice)(nil),                                          // 85: frontend.insights.networth.PresetChoice
	(*orchestrator.ScrollableComponent)(nil),                      // 86: frontend.home.orchestrator.ScrollableComponent
	(*assetandanalysis.AssetImportStatusDetails)(nil),             // 87: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails
	(*assetandanalysis.AssetImportTerminalInProgressDetails)(nil), // 88: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails
	(*assetandanalysis.AssetImportTerminalFailureDetails)(nil),    // 89: api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails
	(*deeplink.Cta)(nil),                                          // 90: frontend.deeplink.Cta
	(*assetandanalysis.DataFetchLottieDetails)(nil),               // 91: api.typesv2.deeplink_screen_option.assetandanalysis.DataFetchLottieDetails
	(*ui1.WealthBuilderLandingComponent)(nil),                     // 92: frontend.insights.networth.ui.WealthBuilderLandingComponent
	(*ui1.WealthBuilderLandingSection)(nil),                       // 93: frontend.insights.networth.ui.WealthBuilderLandingSection
	(*InputOptionValue)(nil),                                      // 94: frontend.insights.networth.InputOptionValue
}
var file_api_frontend_insights_networth_service_proto_depIdxs = []int32{
	66,  // 0: frontend.insights.networth.MagicImportFilesRequest.req:type_name -> frontend.header.RequestHeader
	67,  // 1: frontend.insights.networth.MagicImportFilesRequest.files:type_name -> api.typesv2.common.file.File
	68,  // 2: frontend.insights.networth.MagicImportFilesResponse.resp_header:type_name -> frontend.header.ResponseHeader
	69,  // 3: frontend.insights.networth.MagicImportFilesResponse.imported_assets_list_screen:type_name -> frontend.insights.networth.MagicImportedAssetsListScreen
	66,  // 4: frontend.insights.networth.DeleteManualAssetRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 5: frontend.insights.networth.DeleteManualAssetResponse.resp_header:type_name -> frontend.header.ResponseHeader
	66,  // 6: frontend.insights.networth.GetManualAssetDashboardRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 7: frontend.insights.networth.GetManualAssetDashboardResponse.resp_header:type_name -> frontend.header.ResponseHeader
	70,  // 8: frontend.insights.networth.GetManualAssetDashboardResponse.dashboard:type_name -> frontend.insights.networth.NetWorthAssetDashboard
	66,  // 9: frontend.insights.networth.SubmitManualFormRequest.req:type_name -> frontend.header.RequestHeader
	71,  // 10: frontend.insights.networth.SubmitManualFormRequest.form_identifier:type_name -> api.typesv2.ManualAssetFormIdentifier
	72,  // 11: frontend.insights.networth.SubmitManualFormRequest.form_data:type_name -> frontend.insights.networth.NetWorthManualInputData
	68,  // 12: frontend.insights.networth.SubmitManualFormResponse.resp_header:type_name -> frontend.header.ResponseHeader
	66,  // 13: frontend.insights.networth.GetManualFormConfigRequest.req:type_name -> frontend.header.RequestHeader
	71,  // 14: frontend.insights.networth.GetManualFormConfigRequest.form_identifier:type_name -> api.typesv2.ManualAssetFormIdentifier
	68,  // 15: frontend.insights.networth.GetManualFormConfigResponse.resp_header:type_name -> frontend.header.ResponseHeader
	73,  // 16: frontend.insights.networth.GetManualFormConfigResponse.form_response:type_name -> frontend.insights.networth.NetWorthManualForm
	66,  // 17: frontend.insights.networth.DepositDeclarationRequest.req:type_name -> frontend.header.RequestHeader
	74,  // 18: frontend.insights.networth.DepositDeclarationRequest.amount:type_name -> api.typesv2.Money
	75,  // 19: frontend.insights.networth.DepositDeclarationRequest.start_date:type_name -> google.protobuf.Timestamp
	75,  // 20: frontend.insights.networth.DepositDeclarationRequest.end_date:type_name -> google.protobuf.Timestamp
	0,   // 21: frontend.insights.networth.DepositDeclarationRequest.deposit_type:type_name -> frontend.insights.networth.ManualAddDepositType
	68,  // 22: frontend.insights.networth.DepositDeclarationResponse.resp_header:type_name -> frontend.header.ResponseHeader
	66,  // 23: frontend.insights.networth.GetNetWorthDashboardRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 24: frontend.insights.networth.GetNetWorthDashboardResponse.resp_header:type_name -> frontend.header.ResponseHeader
	76,  // 25: frontend.insights.networth.GetNetWorthDashboardResponse.refresh_banner:type_name -> api.typesv2.ui.IconTextComponent
	22,  // 26: frontend.insights.networth.GetNetWorthDashboardResponse.visualisation:type_name -> frontend.insights.networth.NetWorthVisualisation
	27,  // 27: frontend.insights.networth.GetNetWorthDashboardResponse.sections:type_name -> frontend.insights.networth.Section
	77,  // 28: frontend.insights.networth.GetNetWorthDashboardResponse.footer:type_name -> api.typesv2.common.Text
	20,  // 29: frontend.insights.networth.GetNetWorthDashboardResponse.components:type_name -> frontend.insights.networth.Component
	76,  // 30: frontend.insights.networth.GetNetWorthDashboardResponse.connect_assets_cta:type_name -> api.typesv2.ui.IconTextComponent
	27,  // 31: frontend.insights.networth.Component.section:type_name -> frontend.insights.networth.Section
	21,  // 32: frontend.insights.networth.Component.secrets_section:type_name -> frontend.insights.networth.SecretsSection
	76,  // 33: frontend.insights.networth.SecretsSection.title:type_name -> api.typesv2.ui.IconTextComponent
	76,  // 34: frontend.insights.networth.SecretsSection.cta:type_name -> api.typesv2.ui.IconTextComponent
	78,  // 35: frontend.insights.networth.SecretsSection.secret_summaries:type_name -> frontend.insights.secrets.SecretSummary
	76,  // 36: frontend.insights.networth.SecretsSection.subtitle:type_name -> api.typesv2.ui.IconTextComponent
	1,   // 37: frontend.insights.networth.NetWorthVisualisation.visualisation_type:type_name -> frontend.insights.networth.VisualisationType
	23,  // 38: frontend.insights.networth.NetWorthVisualisation.island_params:type_name -> frontend.insights.networth.IslandParams
	26,  // 39: frontend.insights.networth.IslandParams.categories:type_name -> frontend.insights.networth.CategoryVisualisationItem
	24,  // 40: frontend.insights.networth.IslandParams.background_image:type_name -> frontend.insights.networth.Image
	25,  // 41: frontend.insights.networth.IslandParams.summary:type_name -> frontend.insights.networth.VisualisationSummary
	77,  // 42: frontend.insights.networth.IslandParams.message:type_name -> api.typesv2.common.Text
	76,  // 43: frontend.insights.networth.VisualisationSummary.title:type_name -> api.typesv2.ui.IconTextComponent
	76,  // 44: frontend.insights.networth.VisualisationSummary.value:type_name -> api.typesv2.ui.IconTextComponent
	79,  // 45: frontend.insights.networth.CategoryVisualisationItem.category_icon:type_name -> api.typesv2.common.VisualElement
	80,  // 46: frontend.insights.networth.Section.summary:type_name -> api.typesv2.ui.VerticalKeyValuePair
	76,  // 47: frontend.insights.networth.Section.add_more_button:type_name -> api.typesv2.ui.IconTextComponent
	28,  // 48: frontend.insights.networth.Section.add_more_widgets:type_name -> frontend.insights.networth.AddMoreWidgets
	29,  // 49: frontend.insights.networth.Section.widgets:type_name -> frontend.insights.networth.Widget
	61,  // 50: frontend.insights.networth.Section.section_header:type_name -> frontend.insights.networth.Section.SectionHeader
	29,  // 51: frontend.insights.networth.AddMoreWidgets.widgets:type_name -> frontend.insights.networth.Widget
	2,   // 52: frontend.insights.networth.Widget.type:type_name -> frontend.insights.networth.WidgetType
	30,  // 53: frontend.insights.networth.Widget.card_widget_params:type_name -> frontend.insights.networth.CardWidgetParams
	3,   // 54: frontend.insights.networth.Widget.cache_control:type_name -> frontend.insights.networth.CacheControl
	4,   // 55: frontend.insights.networth.Widget.state:type_name -> frontend.insights.networth.WidgetState
	79,  // 56: frontend.insights.networth.CardWidgetParams.icon:type_name -> api.typesv2.common.VisualElement
	77,  // 57: frontend.insights.networth.CardWidgetParams.title:type_name -> api.typesv2.common.Text
	76,  // 58: frontend.insights.networth.CardWidgetParams.primary_text:type_name -> api.typesv2.ui.IconTextComponent
	74,  // 59: frontend.insights.networth.CardWidgetParams.value:type_name -> api.typesv2.Money
	81,  // 60: frontend.insights.networth.CardWidgetParams.deeplink:type_name -> frontend.deeplink.Deeplink
	76,  // 61: frontend.insights.networth.CardWidgetParams.tag:type_name -> api.typesv2.ui.IconTextComponent
	66,  // 62: frontend.insights.networth.GetNetWorthSummaryForHomeRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 63: frontend.insights.networth.GetNetWorthSummaryForHomeResponse.resp_header:type_name -> frontend.header.ResponseHeader
	82,  // 64: frontend.insights.networth.GetNetWorthSummaryForHomeResponse.dashboard_info:type_name -> frontend.home.HomeDashboard
	66,  // 65: frontend.insights.networth.GetNextNetWorthRefreshActionRequest.req:type_name -> frontend.header.RequestHeader
	83,  // 66: frontend.insights.networth.GetNextNetWorthRefreshActionRequest.request_params:type_name -> api.typesv2.deeplink_screen_option.insights.networth_refresh.NetWorthGetNextActionRequestParams
	68,  // 67: frontend.insights.networth.GetNextNetWorthRefreshActionResponse.resp_header:type_name -> frontend.header.ResponseHeader
	81,  // 68: frontend.insights.networth.GetNextNetWorthRefreshActionResponse.next_action:type_name -> frontend.deeplink.Deeplink
	84,  // 69: frontend.insights.networth.GetNextNetWorthRefreshActionResponse.refresh_header:type_name -> api.typesv2.deeplink_screen_option.insights.networth_refresh.NetWorthRefreshHeader
	66,  // 70: frontend.insights.networth.UpdateManualAssetsRequest.req:type_name -> frontend.header.RequestHeader
	62,  // 71: frontend.insights.networth.UpdateManualAssetsRequest.updated_asset_current_values_v2:type_name -> frontend.insights.networth.UpdateManualAssetsRequest.UpdatedAssetCurrentValuesV2Entry
	68,  // 72: frontend.insights.networth.UpdateManualAssetsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	81,  // 73: frontend.insights.networth.UpdateManualAssetsResponse.next_screen:type_name -> frontend.deeplink.Deeplink
	66,  // 74: frontend.insights.networth.SearchAssetFormFieldOptionsRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 75: frontend.insights.networth.SearchAssetFormFieldOptionsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	85,  // 76: frontend.insights.networth.SearchAssetFormFieldOptionsResponse.choices:type_name -> frontend.insights.networth.PresetChoice
	39,  // 77: frontend.insights.networth.SearchAssetFormFieldOptionsResponse.preset_choices_list:type_name -> frontend.insights.networth.PresetChoicesList
	63,  // 78: frontend.insights.networth.SearchAssetFormFieldOptionsResponse.error_view:type_name -> frontend.insights.networth.SearchAssetFormFieldOptionsResponse.ErrorView
	85,  // 79: frontend.insights.networth.PresetChoicesList.preset_choices:type_name -> frontend.insights.networth.PresetChoice
	66,  // 80: frontend.insights.networth.GetCreditScoreSummaryForHomeRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 81: frontend.insights.networth.GetCreditScoreSummaryForHomeResponse.resp_header:type_name -> frontend.header.ResponseHeader
	82,  // 82: frontend.insights.networth.GetCreditScoreSummaryForHomeResponse.dashboard_info:type_name -> frontend.home.HomeDashboard
	66,  // 83: frontend.insights.networth.GetEpfSummaryForHomeRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 84: frontend.insights.networth.GetEpfSummaryForHomeResponse.resp_header:type_name -> frontend.header.ResponseHeader
	82,  // 85: frontend.insights.networth.GetEpfSummaryForHomeResponse.dashboard_info:type_name -> frontend.home.HomeDashboard
	66,  // 86: frontend.insights.networth.GetMfSummaryForHomeRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 87: frontend.insights.networth.GetMfSummaryForHomeResponse.resp_header:type_name -> frontend.header.ResponseHeader
	82,  // 88: frontend.insights.networth.GetMfSummaryForHomeResponse.dashboard_info:type_name -> frontend.home.HomeDashboard
	66,  // 89: frontend.insights.networth.GetWealthBuilderLandingPageRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 90: frontend.insights.networth.GetWealthBuilderLandingPageResponse.resp_header:type_name -> frontend.header.ResponseHeader
	86,  // 91: frontend.insights.networth.GetWealthBuilderLandingPageResponse.scrollable_components:type_name -> frontend.home.orchestrator.ScrollableComponent
	66,  // 92: frontend.insights.networth.GetAssetImportFlowStatusRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 93: frontend.insights.networth.GetAssetImportFlowStatusResponse.resp_header:type_name -> frontend.header.ResponseHeader
	87,  // 94: frontend.insights.networth.GetAssetImportFlowStatusResponse.status:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportStatusDetails
	50,  // 95: frontend.insights.networth.GetAssetImportFlowStatusResponse.results:type_name -> frontend.insights.networth.AssetImportResultsDetails
	77,  // 96: frontend.insights.networth.AssetImportResultsDetails.title:type_name -> api.typesv2.common.Text
	88,  // 97: frontend.insights.networth.AssetImportResultsDetails.import_in_progress_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalInProgressDetails
	51,  // 98: frontend.insights.networth.AssetImportResultsDetails.import_success_details:type_name -> frontend.insights.networth.AssetImportSuccessDetails
	89,  // 99: frontend.insights.networth.AssetImportResultsDetails.import_failure_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.AssetImportTerminalFailureDetails
	77,  // 100: frontend.insights.networth.AssetImportSuccessDetails.title:type_name -> api.typesv2.common.Text
	52,  // 101: frontend.insights.networth.AssetImportSuccessDetails.rolling_animation_details:type_name -> frontend.insights.networth.NetworthRollingAnimationDetails
	76,  // 102: frontend.insights.networth.AssetImportSuccessDetails.assets_update_label:type_name -> api.typesv2.ui.IconTextComponent
	53,  // 103: frontend.insights.networth.AssetImportSuccessDetails.connect_more_assets_component:type_name -> frontend.insights.networth.ConnectMoreAssetsComponent
	90,  // 104: frontend.insights.networth.AssetImportSuccessDetails.exit_cta:type_name -> frontend.deeplink.Cta
	91,  // 105: frontend.insights.networth.AssetImportSuccessDetails.lottie_details:type_name -> api.typesv2.deeplink_screen_option.assetandanalysis.DataFetchLottieDetails
	76,  // 106: frontend.insights.networth.AssetImportSuccessDetails.share_cta:type_name -> api.typesv2.ui.IconTextComponent
	74,  // 107: frontend.insights.networth.NetworthRollingAnimationDetails.updated_networth:type_name -> api.typesv2.Money
	77,  // 108: frontend.insights.networth.NetworthRollingAnimationDetails.currency_symbol:type_name -> api.typesv2.common.Text
	76,  // 109: frontend.insights.networth.NetworthRollingAnimationDetails.updated_networth_text:type_name -> api.typesv2.ui.IconTextComponent
	76,  // 110: frontend.insights.networth.ConnectMoreAssetsComponent.title:type_name -> api.typesv2.ui.IconTextComponent
	90,  // 111: frontend.insights.networth.ConnectMoreAssetsComponent.connect_more_cta:type_name -> frontend.deeplink.Cta
	76,  // 112: frontend.insights.networth.ConnectMoreAssetsComponent.connect_more:type_name -> api.typesv2.ui.IconTextComponent
	64,  // 113: frontend.insights.networth.ConnectMoreAssetsComponent.progress_bar_details:type_name -> frontend.insights.networth.ConnectMoreAssetsComponent.ProgressBarDetails
	66,  // 114: frontend.insights.networth.GetWealthBuilderDashboardComponentRequest.req:type_name -> frontend.header.RequestHeader
	68,  // 115: frontend.insights.networth.GetWealthBuilderDashboardComponentResponse.resp_header:type_name -> frontend.header.ResponseHeader
	92,  // 116: frontend.insights.networth.GetWealthBuilderDashboardComponentResponse.wealth_builder_landing_component:type_name -> frontend.insights.networth.ui.WealthBuilderLandingComponent
	66,  // 117: frontend.insights.networth.GetConnectMoreAssetsScreenRequest.req:type_name -> frontend.header.RequestHeader
	58,  // 118: frontend.insights.networth.GetConnectMoreAssetsScreenRequest.request_params:type_name -> frontend.insights.networth.ConnectMoreAssetsScreenRequestParams
	68,  // 119: frontend.insights.networth.GetConnectMoreAssetsScreenResponse.resp_header:type_name -> frontend.header.ResponseHeader
	77,  // 120: frontend.insights.networth.GetConnectMoreAssetsScreenResponse.title:type_name -> api.typesv2.common.Text
	79,  // 121: frontend.insights.networth.GetConnectMoreAssetsScreenResponse.card_image:type_name -> api.typesv2.common.VisualElement
	93,  // 122: frontend.insights.networth.GetConnectMoreAssetsScreenResponse.wealth_builder_landing_sections:type_name -> frontend.insights.networth.ui.WealthBuilderLandingSection
	66,  // 123: frontend.insights.networth.SubmitManualFormsRequest.req:type_name -> frontend.header.RequestHeader
	65,  // 124: frontend.insights.networth.SubmitManualFormsRequest.form_submission_data:type_name -> frontend.insights.networth.SubmitManualFormsRequest.FormSubmissionData
	68,  // 125: frontend.insights.networth.SubmitManualFormsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	80,  // 126: frontend.insights.networth.Section.SectionHeader.summary:type_name -> api.typesv2.ui.VerticalKeyValuePair
	76,  // 127: frontend.insights.networth.Section.SectionHeader.add_more_button:type_name -> api.typesv2.ui.IconTextComponent
	94,  // 128: frontend.insights.networth.UpdateManualAssetsRequest.UpdatedAssetCurrentValuesV2Entry.value:type_name -> frontend.insights.networth.InputOptionValue
	79,  // 129: frontend.insights.networth.SearchAssetFormFieldOptionsResponse.ErrorView.image:type_name -> api.typesv2.common.VisualElement
	80,  // 130: frontend.insights.networth.SearchAssetFormFieldOptionsResponse.ErrorView.key_value_pair:type_name -> api.typesv2.ui.VerticalKeyValuePair
	76,  // 131: frontend.insights.networth.SearchAssetFormFieldOptionsResponse.ErrorView.info_redirection:type_name -> api.typesv2.ui.IconTextComponent
	71,  // 132: frontend.insights.networth.SubmitManualFormsRequest.FormSubmissionData.form_identifier:type_name -> api.typesv2.ManualAssetFormIdentifier
	72,  // 133: frontend.insights.networth.SubmitManualFormsRequest.FormSubmissionData.form_data:type_name -> frontend.insights.networth.NetWorthManualInputData
	18,  // 134: frontend.insights.networth.NetWorth.GetNetWorthDashboard:input_type -> frontend.insights.networth.GetNetWorthDashboardRequest
	16,  // 135: frontend.insights.networth.NetWorth.DepositDeclaration:input_type -> frontend.insights.networth.DepositDeclarationRequest
	14,  // 136: frontend.insights.networth.NetWorth.GetManualFormConfig:input_type -> frontend.insights.networth.GetManualFormConfigRequest
	12,  // 137: frontend.insights.networth.NetWorth.SubmitManualForm:input_type -> frontend.insights.networth.SubmitManualFormRequest
	10,  // 138: frontend.insights.networth.NetWorth.GetManualAssetDashboard:input_type -> frontend.insights.networth.GetManualAssetDashboardRequest
	8,   // 139: frontend.insights.networth.NetWorth.DeleteManualAsset:input_type -> frontend.insights.networth.DeleteManualAssetRequest
	31,  // 140: frontend.insights.networth.NetWorth.GetNetWorthSummaryForHome:input_type -> frontend.insights.networth.GetNetWorthSummaryForHomeRequest
	33,  // 141: frontend.insights.networth.NetWorth.GetNextNetWorthRefreshAction:input_type -> frontend.insights.networth.GetNextNetWorthRefreshActionRequest
	35,  // 142: frontend.insights.networth.NetWorth.UpdateManualAssets:input_type -> frontend.insights.networth.UpdateManualAssetsRequest
	37,  // 143: frontend.insights.networth.NetWorth.SearchAssetFormFieldOptions:input_type -> frontend.insights.networth.SearchAssetFormFieldOptionsRequest
	40,  // 144: frontend.insights.networth.NetWorth.GetCreditScoreSummaryForHome:input_type -> frontend.insights.networth.GetCreditScoreSummaryForHomeRequest
	42,  // 145: frontend.insights.networth.NetWorth.GetEpfSummaryForHome:input_type -> frontend.insights.networth.GetEpfSummaryForHomeRequest
	44,  // 146: frontend.insights.networth.NetWorth.GetMfSummaryForHome:input_type -> frontend.insights.networth.GetMfSummaryForHomeRequest
	46,  // 147: frontend.insights.networth.NetWorth.GetWealthBuilderLandingPage:input_type -> frontend.insights.networth.GetWealthBuilderLandingPageRequest
	48,  // 148: frontend.insights.networth.NetWorth.GetAssetImportFlowStatus:input_type -> frontend.insights.networth.GetAssetImportFlowStatusRequest
	56,  // 149: frontend.insights.networth.NetWorth.GetConnectMoreAssetsScreen:input_type -> frontend.insights.networth.GetConnectMoreAssetsScreenRequest
	54,  // 150: frontend.insights.networth.NetWorth.GetWealthBuilderDashboardComponent:input_type -> frontend.insights.networth.GetWealthBuilderDashboardComponentRequest
	6,   // 151: frontend.insights.networth.NetWorth.MagicImportFiles:input_type -> frontend.insights.networth.MagicImportFilesRequest
	59,  // 152: frontend.insights.networth.NetWorth.SubmitManualForms:input_type -> frontend.insights.networth.SubmitManualFormsRequest
	19,  // 153: frontend.insights.networth.NetWorth.GetNetWorthDashboard:output_type -> frontend.insights.networth.GetNetWorthDashboardResponse
	17,  // 154: frontend.insights.networth.NetWorth.DepositDeclaration:output_type -> frontend.insights.networth.DepositDeclarationResponse
	15,  // 155: frontend.insights.networth.NetWorth.GetManualFormConfig:output_type -> frontend.insights.networth.GetManualFormConfigResponse
	13,  // 156: frontend.insights.networth.NetWorth.SubmitManualForm:output_type -> frontend.insights.networth.SubmitManualFormResponse
	11,  // 157: frontend.insights.networth.NetWorth.GetManualAssetDashboard:output_type -> frontend.insights.networth.GetManualAssetDashboardResponse
	9,   // 158: frontend.insights.networth.NetWorth.DeleteManualAsset:output_type -> frontend.insights.networth.DeleteManualAssetResponse
	32,  // 159: frontend.insights.networth.NetWorth.GetNetWorthSummaryForHome:output_type -> frontend.insights.networth.GetNetWorthSummaryForHomeResponse
	34,  // 160: frontend.insights.networth.NetWorth.GetNextNetWorthRefreshAction:output_type -> frontend.insights.networth.GetNextNetWorthRefreshActionResponse
	36,  // 161: frontend.insights.networth.NetWorth.UpdateManualAssets:output_type -> frontend.insights.networth.UpdateManualAssetsResponse
	38,  // 162: frontend.insights.networth.NetWorth.SearchAssetFormFieldOptions:output_type -> frontend.insights.networth.SearchAssetFormFieldOptionsResponse
	41,  // 163: frontend.insights.networth.NetWorth.GetCreditScoreSummaryForHome:output_type -> frontend.insights.networth.GetCreditScoreSummaryForHomeResponse
	43,  // 164: frontend.insights.networth.NetWorth.GetEpfSummaryForHome:output_type -> frontend.insights.networth.GetEpfSummaryForHomeResponse
	45,  // 165: frontend.insights.networth.NetWorth.GetMfSummaryForHome:output_type -> frontend.insights.networth.GetMfSummaryForHomeResponse
	47,  // 166: frontend.insights.networth.NetWorth.GetWealthBuilderLandingPage:output_type -> frontend.insights.networth.GetWealthBuilderLandingPageResponse
	49,  // 167: frontend.insights.networth.NetWorth.GetAssetImportFlowStatus:output_type -> frontend.insights.networth.GetAssetImportFlowStatusResponse
	57,  // 168: frontend.insights.networth.NetWorth.GetConnectMoreAssetsScreen:output_type -> frontend.insights.networth.GetConnectMoreAssetsScreenResponse
	55,  // 169: frontend.insights.networth.NetWorth.GetWealthBuilderDashboardComponent:output_type -> frontend.insights.networth.GetWealthBuilderDashboardComponentResponse
	7,   // 170: frontend.insights.networth.NetWorth.MagicImportFiles:output_type -> frontend.insights.networth.MagicImportFilesResponse
	60,  // 171: frontend.insights.networth.NetWorth.SubmitManualForms:output_type -> frontend.insights.networth.SubmitManualFormsResponse
	153, // [153:172] is the sub-list for method output_type
	134, // [134:153] is the sub-list for method input_type
	134, // [134:134] is the sub-list for extension type_name
	134, // [134:134] is the sub-list for extension extendee
	0,   // [0:134] is the sub-list for field type_name
}

func init() { file_api_frontend_insights_networth_service_proto_init() }
func file_api_frontend_insights_networth_service_proto_init() {
	if File_api_frontend_insights_networth_service_proto != nil {
		return
	}
	file_api_frontend_insights_networth_asset_dashboard_proto_init()
	file_api_frontend_insights_networth_magic_import_proto_init()
	file_api_frontend_insights_networth_manual_form_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_insights_networth_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MagicImportFilesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MagicImportFilesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteManualAssetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteManualAssetResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetManualAssetDashboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetManualAssetDashboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitManualFormRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitManualFormResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetManualFormConfigRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetManualFormConfigResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositDeclarationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositDeclarationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthDashboardRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthDashboardResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Component); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecretsSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetWorthVisualisation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IslandParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisualisationSummary); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CategoryVisualisationItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Section); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddMoreWidgets); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Widget); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardWidgetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthSummaryForHomeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNetWorthSummaryForHomeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextNetWorthRefreshActionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextNetWorthRefreshActionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateManualAssetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateManualAssetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAssetFormFieldOptionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAssetFormFieldOptionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PresetChoicesList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditScoreSummaryForHomeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditScoreSummaryForHomeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEpfSummaryForHomeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEpfSummaryForHomeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMfSummaryForHomeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMfSummaryForHomeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWealthBuilderLandingPageRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWealthBuilderLandingPageResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetImportFlowStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAssetImportFlowStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetImportResultsDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AssetImportSuccessDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworthRollingAnimationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectMoreAssetsComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWealthBuilderDashboardComponentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetWealthBuilderDashboardComponentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectMoreAssetsScreenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetConnectMoreAssetsScreenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectMoreAssetsScreenRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitManualFormsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitManualFormsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Section_SectionHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchAssetFormFieldOptionsResponse_ErrorView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectMoreAssetsComponent_ProgressBarDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitManualFormsRequest_FormSubmissionData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_insights_networth_service_proto_msgTypes[14].OneofWrappers = []interface{}{
		(*Component_Section)(nil),
		(*Component_SecretsSection)(nil),
	}
	file_api_frontend_insights_networth_service_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*NetWorthVisualisation_IslandParams)(nil),
	}
	file_api_frontend_insights_networth_service_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*Widget_CardWidgetParams)(nil),
	}
	file_api_frontend_insights_networth_service_proto_msgTypes[32].OneofWrappers = []interface{}{
		(*SearchAssetFormFieldOptionsResponse_PresetChoicesList)(nil),
		(*SearchAssetFormFieldOptionsResponse_ErrorView_)(nil),
	}
	file_api_frontend_insights_networth_service_proto_msgTypes[43].OneofWrappers = []interface{}{
		(*GetAssetImportFlowStatusResponse_Status)(nil),
		(*GetAssetImportFlowStatusResponse_Results)(nil),
	}
	file_api_frontend_insights_networth_service_proto_msgTypes[44].OneofWrappers = []interface{}{
		(*AssetImportResultsDetails_ImportInProgressDetails)(nil),
		(*AssetImportResultsDetails_ImportSuccessDetails)(nil),
		(*AssetImportResultsDetails_ImportFailureDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_insights_networth_service_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   60,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_insights_networth_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_insights_networth_service_proto_depIdxs,
		EnumInfos:         file_api_frontend_insights_networth_service_proto_enumTypes,
		MessageInfos:      file_api_frontend_insights_networth_service_proto_msgTypes,
	}.Build()
	File_api_frontend_insights_networth_service_proto = out.File
	file_api_frontend_insights_networth_service_proto_rawDesc = nil
	file_api_frontend_insights_networth_service_proto_goTypes = nil
	file_api_frontend_insights_networth_service_proto_depIdxs = nil
}
