// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/tiering/service.proto

package tiering

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	deeplink "github.com/epifi/gamma/api/frontend/deeplink"

	enum "github.com/epifi/gamma/api/frontend/tiering/enum"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = deeplink.Screen(0)

	_ = enum.DisplayComponent(0)
)

// Validate checks the field values on GetDetailedBenefitsBottomSheetRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetDetailedBenefitsBottomSheetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDetailedBenefitsBottomSheetRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetDetailedBenefitsBottomSheetRequestMultiError, or nil if none found.
func (m *GetDetailedBenefitsBottomSheetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailedBenefitsBottomSheetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedBenefitsBottomSheetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedBenefitsBottomSheetRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedBenefitsBottomSheetRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if len(errors) > 0 {
		return GetDetailedBenefitsBottomSheetRequestMultiError(errors)
	}

	return nil
}

// GetDetailedBenefitsBottomSheetRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetDetailedBenefitsBottomSheetRequest.ValidateAll() if the designated
// constraints aren't met.
type GetDetailedBenefitsBottomSheetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailedBenefitsBottomSheetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailedBenefitsBottomSheetRequestMultiError) AllErrors() []error { return m }

// GetDetailedBenefitsBottomSheetRequestValidationError is the validation error
// returned by GetDetailedBenefitsBottomSheetRequest.Validate if the
// designated constraints aren't met.
type GetDetailedBenefitsBottomSheetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailedBenefitsBottomSheetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailedBenefitsBottomSheetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailedBenefitsBottomSheetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailedBenefitsBottomSheetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailedBenefitsBottomSheetRequestValidationError) ErrorName() string {
	return "GetDetailedBenefitsBottomSheetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailedBenefitsBottomSheetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailedBenefitsBottomSheetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailedBenefitsBottomSheetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailedBenefitsBottomSheetRequestValidationError{}

// Validate checks the field values on GetDetailedBenefitsBottomSheetResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetDetailedBenefitsBottomSheetResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetDetailedBenefitsBottomSheetResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetDetailedBenefitsBottomSheetResponseMultiError, or nil if none found.
func (m *GetDetailedBenefitsBottomSheetResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDetailedBenefitsBottomSheetResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedBenefitsBottomSheetResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedBenefitsBottomSheetResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedBenefitsBottomSheetResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedBenefitsBottomSheetResponseValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedBenefitsBottomSheetResponseValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedBenefitsBottomSheetResponseValidationError{
				field:  "Section",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDetailedBenefitsBottomSheetResponseValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDetailedBenefitsBottomSheetResponseValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDetailedBenefitsBottomSheetResponseValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TierPlanMetaMap

	if len(errors) > 0 {
		return GetDetailedBenefitsBottomSheetResponseMultiError(errors)
	}

	return nil
}

// GetDetailedBenefitsBottomSheetResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetDetailedBenefitsBottomSheetResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDetailedBenefitsBottomSheetResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDetailedBenefitsBottomSheetResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDetailedBenefitsBottomSheetResponseMultiError) AllErrors() []error { return m }

// GetDetailedBenefitsBottomSheetResponseValidationError is the validation
// error returned by GetDetailedBenefitsBottomSheetResponse.Validate if the
// designated constraints aren't met.
type GetDetailedBenefitsBottomSheetResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDetailedBenefitsBottomSheetResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDetailedBenefitsBottomSheetResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDetailedBenefitsBottomSheetResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDetailedBenefitsBottomSheetResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDetailedBenefitsBottomSheetResponseValidationError) ErrorName() string {
	return "GetDetailedBenefitsBottomSheetResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDetailedBenefitsBottomSheetResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDetailedBenefitsBottomSheetResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDetailedBenefitsBottomSheetResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDetailedBenefitsBottomSheetResponseValidationError{}

// Validate checks the field values on RecordComponentShownToActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordComponentShownToActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordComponentShownToActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordComponentShownToActorRequestMultiError, or nil if none found.
func (m *RecordComponentShownToActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordComponentShownToActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordComponentShownToActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordComponentShownToActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordComponentShownToActorRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ComponentName

	if len(errors) > 0 {
		return RecordComponentShownToActorRequestMultiError(errors)
	}

	return nil
}

// RecordComponentShownToActorRequestMultiError is an error wrapping multiple
// validation errors returned by
// RecordComponentShownToActorRequest.ValidateAll() if the designated
// constraints aren't met.
type RecordComponentShownToActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordComponentShownToActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordComponentShownToActorRequestMultiError) AllErrors() []error { return m }

// RecordComponentShownToActorRequestValidationError is the validation error
// returned by RecordComponentShownToActorRequest.Validate if the designated
// constraints aren't met.
type RecordComponentShownToActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordComponentShownToActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordComponentShownToActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordComponentShownToActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordComponentShownToActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordComponentShownToActorRequestValidationError) ErrorName() string {
	return "RecordComponentShownToActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordComponentShownToActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordComponentShownToActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordComponentShownToActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordComponentShownToActorRequestValidationError{}

// Validate checks the field values on GetEarnedBenefitsHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEarnedBenefitsHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEarnedBenefitsHistoryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEarnedBenefitsHistoryRequestMultiError, or nil if none found.
func (m *GetEarnedBenefitsHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEarnedBenefitsHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarnedBenefitsHistoryRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	if len(errors) > 0 {
		return GetEarnedBenefitsHistoryRequestMultiError(errors)
	}

	return nil
}

// GetEarnedBenefitsHistoryRequestMultiError is an error wrapping multiple
// validation errors returned by GetEarnedBenefitsHistoryRequest.ValidateAll()
// if the designated constraints aren't met.
type GetEarnedBenefitsHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEarnedBenefitsHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEarnedBenefitsHistoryRequestMultiError) AllErrors() []error { return m }

// GetEarnedBenefitsHistoryRequestValidationError is the validation error
// returned by GetEarnedBenefitsHistoryRequest.Validate if the designated
// constraints aren't met.
type GetEarnedBenefitsHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEarnedBenefitsHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEarnedBenefitsHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEarnedBenefitsHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEarnedBenefitsHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEarnedBenefitsHistoryRequestValidationError) ErrorName() string {
	return "GetEarnedBenefitsHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEarnedBenefitsHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEarnedBenefitsHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEarnedBenefitsHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEarnedBenefitsHistoryRequestValidationError{}

// Validate checks the field values on GetEarnedBenefitsHistoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetEarnedBenefitsHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEarnedBenefitsHistoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEarnedBenefitsHistoryResponseMultiError, or nil if none found.
func (m *GetEarnedBenefitsHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEarnedBenefitsHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarnedBenefitsHistoryResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
					field:  "PageTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
					field:  "PageTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarnedBenefitsHistoryResponseValidationError{
				field:  "PageTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
					field:  "HeaderView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
					field:  "HeaderView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarnedBenefitsHistoryResponseValidationError{
				field:  "HeaderView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMonthlyRewardEarnedViews() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
						field:  fmt.Sprintf("MonthlyRewardEarnedViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
						field:  fmt.Sprintf("MonthlyRewardEarnedViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEarnedBenefitsHistoryResponseValidationError{
					field:  fmt.Sprintf("MonthlyRewardEarnedViews[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetViewMoreCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
					field:  "ViewMoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponseValidationError{
					field:  "ViewMoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetViewMoreCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarnedBenefitsHistoryResponseValidationError{
				field:  "ViewMoreCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BeforeToken

	// no validation rules for AfterToken

	if len(errors) > 0 {
		return GetEarnedBenefitsHistoryResponseMultiError(errors)
	}

	return nil
}

// GetEarnedBenefitsHistoryResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetEarnedBenefitsHistoryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetEarnedBenefitsHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEarnedBenefitsHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEarnedBenefitsHistoryResponseMultiError) AllErrors() []error { return m }

// GetEarnedBenefitsHistoryResponseValidationError is the validation error
// returned by GetEarnedBenefitsHistoryResponse.Validate if the designated
// constraints aren't met.
type GetEarnedBenefitsHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEarnedBenefitsHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEarnedBenefitsHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEarnedBenefitsHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEarnedBenefitsHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEarnedBenefitsHistoryResponseValidationError) ErrorName() string {
	return "GetEarnedBenefitsHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEarnedBenefitsHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEarnedBenefitsHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEarnedBenefitsHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEarnedBenefitsHistoryResponseValidationError{}

// Validate checks the field values on MonthlyRewardEarnedView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MonthlyRewardEarnedView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MonthlyRewardEarnedView with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MonthlyRewardEarnedViewMultiError, or nil if none found.
func (m *MonthlyRewardEarnedView) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyRewardEarnedView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeaderView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "HeaderView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "HeaderView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedViewValidationError{
				field:  "HeaderView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "CardBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "CardBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedViewValidationError{
				field:  "CardBackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalBenefitsEarned()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "TotalBenefitsEarned",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "TotalBenefitsEarned",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalBenefitsEarned()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedViewValidationError{
				field:  "TotalBenefitsEarned",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalBenefitsEarnedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "TotalBenefitsEarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "TotalBenefitsEarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalBenefitsEarnedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedViewValidationError{
				field:  "TotalBenefitsEarnedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBenefitBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "BenefitBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedViewValidationError{
					field:  "BenefitBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBenefitBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedViewValidationError{
				field:  "BenefitBackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBenefitCardItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MonthlyRewardEarnedViewValidationError{
						field:  fmt.Sprintf("BenefitCardItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MonthlyRewardEarnedViewValidationError{
						field:  fmt.Sprintf("BenefitCardItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MonthlyRewardEarnedViewValidationError{
					field:  fmt.Sprintf("BenefitCardItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MonthlyRewardEarnedViewMultiError(errors)
	}

	return nil
}

// MonthlyRewardEarnedViewMultiError is an error wrapping multiple validation
// errors returned by MonthlyRewardEarnedView.ValidateAll() if the designated
// constraints aren't met.
type MonthlyRewardEarnedViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyRewardEarnedViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyRewardEarnedViewMultiError) AllErrors() []error { return m }

// MonthlyRewardEarnedViewValidationError is the validation error returned by
// MonthlyRewardEarnedView.Validate if the designated constraints aren't met.
type MonthlyRewardEarnedViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyRewardEarnedViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MonthlyRewardEarnedViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MonthlyRewardEarnedViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonthlyRewardEarnedViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonthlyRewardEarnedViewValidationError) ErrorName() string {
	return "MonthlyRewardEarnedViewValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyRewardEarnedViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyRewardEarnedView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyRewardEarnedViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyRewardEarnedViewValidationError{}

// Validate checks the field values on RecordComponentShownToActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordComponentShownToActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordComponentShownToActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordComponentShownToActorResponseMultiError, or nil if none found.
func (m *RecordComponentShownToActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordComponentShownToActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordComponentShownToActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordComponentShownToActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordComponentShownToActorResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordComponentShownToActorResponseMultiError(errors)
	}

	return nil
}

// RecordComponentShownToActorResponseMultiError is an error wrapping multiple
// validation errors returned by
// RecordComponentShownToActorResponse.ValidateAll() if the designated
// constraints aren't met.
type RecordComponentShownToActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordComponentShownToActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordComponentShownToActorResponseMultiError) AllErrors() []error { return m }

// RecordComponentShownToActorResponseValidationError is the validation error
// returned by RecordComponentShownToActorResponse.Validate if the designated
// constraints aren't met.
type RecordComponentShownToActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordComponentShownToActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordComponentShownToActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordComponentShownToActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordComponentShownToActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordComponentShownToActorResponseValidationError) ErrorName() string {
	return "RecordComponentShownToActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordComponentShownToActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordComponentShownToActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordComponentShownToActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordComponentShownToActorResponseValidationError{}

// Validate checks the field values on GetTieringLaunchInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTieringLaunchInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTieringLaunchInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTieringLaunchInfoRequestMultiError, or nil if none found.
func (m *GetTieringLaunchInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTieringLaunchInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTieringLaunchInfoRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTieringLaunchInfoRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTieringLaunchInfoRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTieringLaunchInfoRequestMultiError(errors)
	}

	return nil
}

// GetTieringLaunchInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetTieringLaunchInfoRequest.ValidateAll() if
// the designated constraints aren't met.
type GetTieringLaunchInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTieringLaunchInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTieringLaunchInfoRequestMultiError) AllErrors() []error { return m }

// GetTieringLaunchInfoRequestValidationError is the validation error returned
// by GetTieringLaunchInfoRequest.Validate if the designated constraints
// aren't met.
type GetTieringLaunchInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTieringLaunchInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTieringLaunchInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTieringLaunchInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTieringLaunchInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTieringLaunchInfoRequestValidationError) ErrorName() string {
	return "GetTieringLaunchInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTieringLaunchInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTieringLaunchInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTieringLaunchInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTieringLaunchInfoRequestValidationError{}

// Validate checks the field values on GetTieringLaunchInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTieringLaunchInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTieringLaunchInfoResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTieringLaunchInfoResponseMultiError, or nil if none found.
func (m *GetTieringLaunchInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTieringLaunchInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTieringLaunchInfoResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTieringLaunchInfoResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTieringLaunchInfoResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsTieringEnabled

	// no validation rules for LaunchAnimationInactivitySeconds

	if all {
		switch v := interface{}(m.GetTierIntroductionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTieringLaunchInfoResponseValidationError{
					field:  "TierIntroductionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTieringLaunchInfoResponseValidationError{
					field:  "TierIntroductionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTierIntroductionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTieringLaunchInfoResponseValidationError{
				field:  "TierIntroductionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsLaunchAnimationEnabled

	// no validation rules for Identifier

	if len(errors) > 0 {
		return GetTieringLaunchInfoResponseMultiError(errors)
	}

	return nil
}

// GetTieringLaunchInfoResponseMultiError is an error wrapping multiple
// validation errors returned by GetTieringLaunchInfoResponse.ValidateAll() if
// the designated constraints aren't met.
type GetTieringLaunchInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTieringLaunchInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTieringLaunchInfoResponseMultiError) AllErrors() []error { return m }

// GetTieringLaunchInfoResponseValidationError is the validation error returned
// by GetTieringLaunchInfoResponse.Validate if the designated constraints
// aren't met.
type GetTieringLaunchInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTieringLaunchInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTieringLaunchInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTieringLaunchInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTieringLaunchInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTieringLaunchInfoResponseValidationError) ErrorName() string {
	return "GetTieringLaunchInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTieringLaunchInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTieringLaunchInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTieringLaunchInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTieringLaunchInfoResponseValidationError{}

// Validate checks the field values on GetDeeplinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeeplinkRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeeplinkRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeeplinkRequestMultiError, or nil if none found.
func (m *GetDeeplinkRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeeplinkRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeeplinkRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeeplinkRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeeplinkRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Screen

	if len(errors) > 0 {
		return GetDeeplinkRequestMultiError(errors)
	}

	return nil
}

// GetDeeplinkRequestMultiError is an error wrapping multiple validation errors
// returned by GetDeeplinkRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDeeplinkRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeeplinkRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeeplinkRequestMultiError) AllErrors() []error { return m }

// GetDeeplinkRequestValidationError is the validation error returned by
// GetDeeplinkRequest.Validate if the designated constraints aren't met.
type GetDeeplinkRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeeplinkRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeeplinkRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeeplinkRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeeplinkRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeeplinkRequestValidationError) ErrorName() string {
	return "GetDeeplinkRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeeplinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeeplinkRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeeplinkRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeeplinkRequestValidationError{}

// Validate checks the field values on GetDeeplinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDeeplinkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDeeplinkResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDeeplinkResponseMultiError, or nil if none found.
func (m *GetDeeplinkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDeeplinkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeeplinkResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeeplinkResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeeplinkResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDeeplinkResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDeeplinkResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDeeplinkResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDeeplinkResponseMultiError(errors)
	}

	return nil
}

// GetDeeplinkResponseMultiError is an error wrapping multiple validation
// errors returned by GetDeeplinkResponse.ValidateAll() if the designated
// constraints aren't met.
type GetDeeplinkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDeeplinkResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDeeplinkResponseMultiError) AllErrors() []error { return m }

// GetDeeplinkResponseValidationError is the validation error returned by
// GetDeeplinkResponse.Validate if the designated constraints aren't met.
type GetDeeplinkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDeeplinkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDeeplinkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDeeplinkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDeeplinkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDeeplinkResponseValidationError) ErrorName() string {
	return "GetDeeplinkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDeeplinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDeeplinkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDeeplinkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDeeplinkResponseValidationError{}

// Validate checks the field values on UpgradeRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpgradeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in UpgradeRequestMultiError,
// or nil if none found.
func (m *UpgradeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpgradeRequestMultiError(errors)
	}

	return nil
}

// UpgradeRequestMultiError is an error wrapping multiple validation errors
// returned by UpgradeRequest.ValidateAll() if the designated constraints
// aren't met.
type UpgradeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeRequestMultiError) AllErrors() []error { return m }

// UpgradeRequestValidationError is the validation error returned by
// UpgradeRequest.Validate if the designated constraints aren't met.
type UpgradeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeRequestValidationError) ErrorName() string { return "UpgradeRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpgradeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeRequestValidationError{}

// Validate checks the field values on UpgradeResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpgradeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpgradeResponseMultiError, or nil if none found.
func (m *UpgradeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpgradeResponseMultiError(errors)
	}

	return nil
}

// UpgradeResponseMultiError is an error wrapping multiple validation errors
// returned by UpgradeResponse.ValidateAll() if the designated constraints
// aren't met.
type UpgradeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeResponseMultiError) AllErrors() []error { return m }

// UpgradeResponseValidationError is the validation error returned by
// UpgradeResponse.Validate if the designated constraints aren't met.
type UpgradeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeResponseValidationError) ErrorName() string { return "UpgradeResponseValidationError" }

// Error satisfies the builtin error interface
func (e UpgradeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeResponseValidationError{}

// Validate checks the field values on GetTierAllPlansRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierAllPlansRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierAllPlansRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTierAllPlansRequestMultiError, or nil if none found.
func (m *GetTierAllPlansRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierAllPlansRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierAllPlansRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierAllPlansRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierAllPlansRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UiContext

	if len(errors) > 0 {
		return GetTierAllPlansRequestMultiError(errors)
	}

	return nil
}

// GetTierAllPlansRequestMultiError is an error wrapping multiple validation
// errors returned by GetTierAllPlansRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTierAllPlansRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierAllPlansRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierAllPlansRequestMultiError) AllErrors() []error { return m }

// GetTierAllPlansRequestValidationError is the validation error returned by
// GetTierAllPlansRequest.Validate if the designated constraints aren't met.
type GetTierAllPlansRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierAllPlansRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierAllPlansRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierAllPlansRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierAllPlansRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierAllPlansRequestValidationError) ErrorName() string {
	return "GetTierAllPlansRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierAllPlansRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierAllPlansRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierAllPlansRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierAllPlansRequestValidationError{}

// Validate checks the field values on GetTierAllPlansResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierAllPlansResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierAllPlansResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTierAllPlansResponseMultiError, or nil if none found.
func (m *GetTierAllPlansResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierAllPlansResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierAllPlansResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierAllPlansResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierAllPlansResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTierAllPlan()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierAllPlansResponseValidationError{
					field:  "TierAllPlan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierAllPlansResponseValidationError{
					field:  "TierAllPlan",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTierAllPlan()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierAllPlansResponseValidationError{
				field:  "TierAllPlan",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTierAllPlansResponseMultiError(errors)
	}

	return nil
}

// GetTierAllPlansResponseMultiError is an error wrapping multiple validation
// errors returned by GetTierAllPlansResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTierAllPlansResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierAllPlansResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierAllPlansResponseMultiError) AllErrors() []error { return m }

// GetTierAllPlansResponseValidationError is the validation error returned by
// GetTierAllPlansResponse.Validate if the designated constraints aren't met.
type GetTierAllPlansResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierAllPlansResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierAllPlansResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierAllPlansResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierAllPlansResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierAllPlansResponseValidationError) ErrorName() string {
	return "GetTierAllPlansResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierAllPlansResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierAllPlansResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierAllPlansResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierAllPlansResponseValidationError{}

// Validate checks the field values on GetTierEarnedBenefitsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierEarnedBenefitsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierEarnedBenefitsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTierEarnedBenefitsRequestMultiError, or nil if none found.
func (m *GetTierEarnedBenefitsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierEarnedBenefitsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierEarnedBenefitsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTierEarnedBenefitsRequestMultiError(errors)
	}

	return nil
}

// GetTierEarnedBenefitsRequestMultiError is an error wrapping multiple
// validation errors returned by GetTierEarnedBenefitsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetTierEarnedBenefitsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierEarnedBenefitsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierEarnedBenefitsRequestMultiError) AllErrors() []error { return m }

// GetTierEarnedBenefitsRequestValidationError is the validation error returned
// by GetTierEarnedBenefitsRequest.Validate if the designated constraints
// aren't met.
type GetTierEarnedBenefitsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierEarnedBenefitsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierEarnedBenefitsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierEarnedBenefitsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierEarnedBenefitsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierEarnedBenefitsRequestValidationError) ErrorName() string {
	return "GetTierEarnedBenefitsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierEarnedBenefitsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierEarnedBenefitsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierEarnedBenefitsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierEarnedBenefitsRequestValidationError{}

// Validate checks the field values on GetTierEarnedBenefitsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierEarnedBenefitsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierEarnedBenefitsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTierEarnedBenefitsResponseMultiError, or nil if none found.
func (m *GetTierEarnedBenefitsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierEarnedBenefitsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierEarnedBenefitsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "ScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "ScreenOptions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierEarnedBenefitsResponseValidationError{
				field:  "ScreenOptions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalBenefitEarned()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "TotalBenefitEarned",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "TotalBenefitEarned",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalBenefitEarned()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierEarnedBenefitsResponseValidationError{
				field:  "TotalBenefitEarned",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalBenefitEarnedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "TotalBenefitEarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "TotalBenefitEarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalBenefitEarnedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierEarnedBenefitsResponseValidationError{
				field:  "TotalBenefitEarnedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWarningCounterView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "WarningCounterView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "WarningCounterView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWarningCounterView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierEarnedBenefitsResponseValidationError{
				field:  "WarningCounterView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMonthlyBenefitView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "MonthlyBenefitView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "MonthlyBenefitView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyBenefitView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierEarnedBenefitsResponseValidationError{
				field:  "MonthlyBenefitView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBenefitOptions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
						field:  fmt.Sprintf("BenefitOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
						field:  fmt.Sprintf("BenefitOptions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTierEarnedBenefitsResponseValidationError{
					field:  fmt.Sprintf("BenefitOptions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TierIdentifier

	if all {
		switch v := interface{}(m.GetCelebrationPopup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "CelebrationPopup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "CelebrationPopup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCelebrationPopup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierEarnedBenefitsResponseValidationError{
				field:  "CelebrationPopup",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCelebrationPopupCooloffDuration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "CelebrationPopupCooloffDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierEarnedBenefitsResponseValidationError{
					field:  "CelebrationPopupCooloffDuration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCelebrationPopupCooloffDuration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierEarnedBenefitsResponseValidationError{
				field:  "CelebrationPopupCooloffDuration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PopupCacheKey

	if len(errors) > 0 {
		return GetTierEarnedBenefitsResponseMultiError(errors)
	}

	return nil
}

// GetTierEarnedBenefitsResponseMultiError is an error wrapping multiple
// validation errors returned by GetTierEarnedBenefitsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetTierEarnedBenefitsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierEarnedBenefitsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierEarnedBenefitsResponseMultiError) AllErrors() []error { return m }

// GetTierEarnedBenefitsResponseValidationError is the validation error
// returned by GetTierEarnedBenefitsResponse.Validate if the designated
// constraints aren't met.
type GetTierEarnedBenefitsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierEarnedBenefitsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierEarnedBenefitsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierEarnedBenefitsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierEarnedBenefitsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierEarnedBenefitsResponseValidationError) ErrorName() string {
	return "GetTierEarnedBenefitsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierEarnedBenefitsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierEarnedBenefitsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierEarnedBenefitsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierEarnedBenefitsResponseValidationError{}

// Validate checks the field values on BenefitsOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BenefitsOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BenefitsOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BenefitsOptionsMultiError, or nil if none found.
func (m *BenefitsOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *BenefitsOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BenefitsOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BenefitsOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BenefitsOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	// no validation rules for ShouldGrayscale

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BenefitsOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BenefitsOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BenefitsOptionsValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BenefitType

	// no validation rules for Priority

	switch v := m.Option.(type) {
	case *BenefitsOptions_ActiveStateListView:
		if v == nil {
			err := BenefitsOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActiveStateListView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "ActiveStateListView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "ActiveStateListView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActiveStateListView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitsOptionsValidationError{
					field:  "ActiveStateListView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BenefitsOptions_InActiveStateListView:
		if v == nil {
			err := BenefitsOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInActiveStateListView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "InActiveStateListView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "InActiveStateListView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInActiveStateListView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitsOptionsValidationError{
					field:  "InActiveStateListView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BenefitsOptions_UpgradeBenefits:
		if v == nil {
			err := BenefitsOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpgradeBenefits()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "UpgradeBenefits",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "UpgradeBenefits",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpgradeBenefits()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitsOptionsValidationError{
					field:  "UpgradeBenefits",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BenefitsOptions_MoreBenefits:
		if v == nil {
			err := BenefitsOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMoreBenefits()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "MoreBenefits",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "MoreBenefits",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMoreBenefits()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitsOptionsValidationError{
					field:  "MoreBenefits",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BenefitsOptions_RetryView:
		if v == nil {
			err := BenefitsOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRetryView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "RetryView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "RetryView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRetryView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitsOptionsValidationError{
					field:  "RetryView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BenefitsOptions_TransferSalaryView:
		if v == nil {
			err := BenefitsOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTransferSalaryView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "TransferSalaryView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "TransferSalaryView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTransferSalaryView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitsOptionsValidationError{
					field:  "TransferSalaryView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BenefitsOptions_MonthlyBenefitView:
		if v == nil {
			err := BenefitsOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMonthlyBenefitView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "MonthlyBenefitView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "MonthlyBenefitView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMonthlyBenefitView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitsOptionsValidationError{
					field:  "MonthlyBenefitView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BenefitsOptions_WarningView:
		if v == nil {
			err := BenefitsOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWarningView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "WarningView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "WarningView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWarningView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitsOptionsValidationError{
					field:  "WarningView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *BenefitsOptions_RewardView:
		if v == nil {
			err := BenefitsOptionsValidationError{
				field:  "Option",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRewardView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "RewardView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BenefitsOptionsValidationError{
						field:  "RewardView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRewardView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BenefitsOptionsValidationError{
					field:  "RewardView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return BenefitsOptionsMultiError(errors)
	}

	return nil
}

// BenefitsOptionsMultiError is an error wrapping multiple validation errors
// returned by BenefitsOptions.ValidateAll() if the designated constraints
// aren't met.
type BenefitsOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BenefitsOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BenefitsOptionsMultiError) AllErrors() []error { return m }

// BenefitsOptionsValidationError is the validation error returned by
// BenefitsOptions.Validate if the designated constraints aren't met.
type BenefitsOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BenefitsOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BenefitsOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BenefitsOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BenefitsOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BenefitsOptionsValidationError) ErrorName() string { return "BenefitsOptionsValidationError" }

// Error satisfies the builtin error interface
func (e BenefitsOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBenefitsOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BenefitsOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BenefitsOptionsValidationError{}

// Validate checks the field values on TransferSalaryView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TransferSalaryView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TransferSalaryView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TransferSalaryViewMultiError, or nil if none found.
func (m *TransferSalaryView) ValidateAll() error {
	return m.validate(true)
}

func (m *TransferSalaryView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeaderView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "HeaderView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "HeaderView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransferSalaryViewValidationError{
				field:  "HeaderView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransferSalaryViewValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEditAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "EditAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "EditAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEditAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransferSalaryViewValidationError{
				field:  "EditAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "RightCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "RightCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransferSalaryViewValidationError{
				field:  "RightCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "InfoView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "InfoView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransferSalaryViewValidationError{
				field:  "InfoView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransferSalaryViewValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountIconTextComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "AmountIconTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TransferSalaryViewValidationError{
					field:  "AmountIconTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountIconTextComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TransferSalaryViewValidationError{
				field:  "AmountIconTextComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TransferSalaryViewMultiError(errors)
	}

	return nil
}

// TransferSalaryViewMultiError is an error wrapping multiple validation errors
// returned by TransferSalaryView.ValidateAll() if the designated constraints
// aren't met.
type TransferSalaryViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TransferSalaryViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TransferSalaryViewMultiError) AllErrors() []error { return m }

// TransferSalaryViewValidationError is the validation error returned by
// TransferSalaryView.Validate if the designated constraints aren't met.
type TransferSalaryViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TransferSalaryViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TransferSalaryViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TransferSalaryViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TransferSalaryViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TransferSalaryViewValidationError) ErrorName() string {
	return "TransferSalaryViewValidationError"
}

// Error satisfies the builtin error interface
func (e TransferSalaryViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTransferSalaryView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TransferSalaryViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TransferSalaryViewValidationError{}

// Validate checks the field values on UpgradeBenefitsView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpgradeBenefitsView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeBenefitsView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpgradeBenefitsViewMultiError, or nil if none found.
func (m *UpgradeBenefitsView) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeBenefitsView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetComponentTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "ComponentTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "ComponentTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetComponentTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsViewValidationError{
				field:  "ComponentTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "LeftCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "LeftCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsViewValidationError{
				field:  "LeftCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "RightCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "RightCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsViewValidationError{
				field:  "RightCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVsItc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "VsItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "VsItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVsItc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsViewValidationError{
				field:  "VsItc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsViewValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsViewValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TierIdentifier

	if len(errors) > 0 {
		return UpgradeBenefitsViewMultiError(errors)
	}

	return nil
}

// UpgradeBenefitsViewMultiError is an error wrapping multiple validation
// errors returned by UpgradeBenefitsView.ValidateAll() if the designated
// constraints aren't met.
type UpgradeBenefitsViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeBenefitsViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeBenefitsViewMultiError) AllErrors() []error { return m }

// UpgradeBenefitsViewValidationError is the validation error returned by
// UpgradeBenefitsView.Validate if the designated constraints aren't met.
type UpgradeBenefitsViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeBenefitsViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeBenefitsViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeBenefitsViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeBenefitsViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeBenefitsViewValidationError) ErrorName() string {
	return "UpgradeBenefitsViewValidationError"
}

// Error satisfies the builtin error interface
func (e UpgradeBenefitsViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeBenefitsView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeBenefitsViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeBenefitsViewValidationError{}

// Validate checks the field values on MoreInfoView with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MoreInfoView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MoreInfoView with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in MoreInfoViewMultiError, or
// nil if none found.
func (m *MoreInfoView) ValidateAll() error {
	return m.validate(true)
}

func (m *MoreInfoView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIconTextComponents() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MoreInfoViewValidationError{
						field:  fmt.Sprintf("IconTextComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MoreInfoViewValidationError{
						field:  fmt.Sprintf("IconTextComponents[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MoreInfoViewValidationError{
					field:  fmt.Sprintf("IconTextComponents[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MoreInfoViewMultiError(errors)
	}

	return nil
}

// MoreInfoViewMultiError is an error wrapping multiple validation errors
// returned by MoreInfoView.ValidateAll() if the designated constraints aren't met.
type MoreInfoViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MoreInfoViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MoreInfoViewMultiError) AllErrors() []error { return m }

// MoreInfoViewValidationError is the validation error returned by
// MoreInfoView.Validate if the designated constraints aren't met.
type MoreInfoViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MoreInfoViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MoreInfoViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MoreInfoViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MoreInfoViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MoreInfoViewValidationError) ErrorName() string { return "MoreInfoViewValidationError" }

// Error satisfies the builtin error interface
func (e MoreInfoViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMoreInfoView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MoreInfoViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MoreInfoViewValidationError{}

// Validate checks the field values on RetryView with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RetryView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RetryView with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RetryViewMultiError, or nil
// if none found.
func (m *RetryView) ValidateAll() error {
	return m.validate(true)
}

func (m *RetryView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetryViewValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetryViewValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetryViewValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetryViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetryViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetryViewValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RetryViewValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RetryViewValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RetryViewValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RetryViewMultiError(errors)
	}

	return nil
}

// RetryViewMultiError is an error wrapping multiple validation errors returned
// by RetryView.ValidateAll() if the designated constraints aren't met.
type RetryViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RetryViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RetryViewMultiError) AllErrors() []error { return m }

// RetryViewValidationError is the validation error returned by
// RetryView.Validate if the designated constraints aren't met.
type RetryViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RetryViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RetryViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RetryViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RetryViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RetryViewValidationError) ErrorName() string { return "RetryViewValidationError" }

// Error satisfies the builtin error interface
func (e RetryViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRetryView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RetryViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RetryViewValidationError{}

// Validate checks the field values on TitleView with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TitleView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TitleView with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TitleViewMultiError, or nil
// if none found.
func (m *TitleView) ValidateAll() error {
	return m.validate(true)
}

func (m *TitleView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TitleViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TitleViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TitleViewValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TitleViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TitleViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TitleViewValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TitleViewValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TitleViewValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TitleViewValidationError{
				field:  "RightIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TitleViewMultiError(errors)
	}

	return nil
}

// TitleViewMultiError is an error wrapping multiple validation errors returned
// by TitleView.ValidateAll() if the designated constraints aren't met.
type TitleViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TitleViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TitleViewMultiError) AllErrors() []error { return m }

// TitleViewValidationError is the validation error returned by
// TitleView.Validate if the designated constraints aren't met.
type TitleViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TitleViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TitleViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TitleViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TitleViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TitleViewValidationError) ErrorName() string { return "TitleViewValidationError" }

// Error satisfies the builtin error interface
func (e TitleViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTitleView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TitleViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TitleViewValidationError{}

// Validate checks the field values on MonthlyBenefitView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MonthlyBenefitView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MonthlyBenefitView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MonthlyBenefitViewMultiError, or nil if none found.
func (m *MonthlyBenefitView) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyBenefitView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMonthlyBenefits()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitViewValidationError{
					field:  "MonthlyBenefits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitViewValidationError{
					field:  "MonthlyBenefits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMonthlyBenefits()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitViewValidationError{
				field:  "MonthlyBenefits",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BenefitType

	if len(errors) > 0 {
		return MonthlyBenefitViewMultiError(errors)
	}

	return nil
}

// MonthlyBenefitViewMultiError is an error wrapping multiple validation errors
// returned by MonthlyBenefitView.ValidateAll() if the designated constraints
// aren't met.
type MonthlyBenefitViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyBenefitViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyBenefitViewMultiError) AllErrors() []error { return m }

// MonthlyBenefitViewValidationError is the validation error returned by
// MonthlyBenefitView.Validate if the designated constraints aren't met.
type MonthlyBenefitViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyBenefitViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MonthlyBenefitViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MonthlyBenefitViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonthlyBenefitViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonthlyBenefitViewValidationError) ErrorName() string {
	return "MonthlyBenefitViewValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyBenefitViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyBenefitView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyBenefitViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyBenefitViewValidationError{}

// Validate checks the field values on WarningView with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *WarningView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WarningView with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in WarningViewMultiError, or
// nil if none found.
func (m *WarningView) ValidateAll() error {
	return m.validate(true)
}

func (m *WarningView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningViewValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningViewValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningViewValidationError{
				field:  "RightIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningViewValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningViewValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightTextComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "RightTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningViewValidationError{
					field:  "RightTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightTextComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningViewValidationError{
				field:  "RightTextComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.LeftView.(type) {
	case *WarningView_WarningCounterView_:
		if v == nil {
			err := WarningViewValidationError{
				field:  "LeftView",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetWarningCounterView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningViewValidationError{
						field:  "WarningCounterView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningViewValidationError{
						field:  "WarningCounterView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetWarningCounterView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningViewValidationError{
					field:  "WarningCounterView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *WarningView_VisualElement:
		if v == nil {
			err := WarningViewValidationError{
				field:  "LeftView",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVisualElement()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, WarningViewValidationError{
						field:  "VisualElement",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, WarningViewValidationError{
						field:  "VisualElement",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVisualElement()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return WarningViewValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return WarningViewMultiError(errors)
	}

	return nil
}

// WarningViewMultiError is an error wrapping multiple validation errors
// returned by WarningView.ValidateAll() if the designated constraints aren't met.
type WarningViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WarningViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WarningViewMultiError) AllErrors() []error { return m }

// WarningViewValidationError is the validation error returned by
// WarningView.Validate if the designated constraints aren't met.
type WarningViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WarningViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WarningViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WarningViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WarningViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WarningViewValidationError) ErrorName() string { return "WarningViewValidationError" }

// Error satisfies the builtin error interface
func (e WarningViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWarningView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WarningViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WarningViewValidationError{}

// Validate checks the field values on InActiveStateListView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InActiveStateListView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InActiveStateListView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InActiveStateListViewMultiError, or nil if none found.
func (m *InActiveStateListView) ValidateAll() error {
	return m.validate(true)
}

func (m *InActiveStateListView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTopIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "TopIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "TopIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InActiveStateListViewValidationError{
				field:  "TopIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InActiveStateListViewValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InActiveStateListViewValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "Action",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InActiveStateListViewValidationError{
				field:  "Action",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContainerView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "ContainerView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InActiveStateListViewValidationError{
					field:  "ContainerView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContainerView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InActiveStateListViewValidationError{
				field:  "ContainerView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InActiveStateListViewMultiError(errors)
	}

	return nil
}

// InActiveStateListViewMultiError is an error wrapping multiple validation
// errors returned by InActiveStateListView.ValidateAll() if the designated
// constraints aren't met.
type InActiveStateListViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InActiveStateListViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InActiveStateListViewMultiError) AllErrors() []error { return m }

// InActiveStateListViewValidationError is the validation error returned by
// InActiveStateListView.Validate if the designated constraints aren't met.
type InActiveStateListViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InActiveStateListViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InActiveStateListViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InActiveStateListViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InActiveStateListViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InActiveStateListViewValidationError) ErrorName() string {
	return "InActiveStateListViewValidationError"
}

// Error satisfies the builtin error interface
func (e InActiveStateListViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInActiveStateListView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InActiveStateListViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InActiveStateListViewValidationError{}

// Validate checks the field values on ActiveStateListView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActiveStateListView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActiveStateListView with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActiveStateListViewMultiError, or nil if none found.
func (m *ActiveStateListView) ValidateAll() error {
	return m.validate(true)
}

func (m *ActiveStateListView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBenefitList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActiveStateListViewValidationError{
						field:  fmt.Sprintf("BenefitList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActiveStateListViewValidationError{
						field:  fmt.Sprintf("BenefitList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActiveStateListViewValidationError{
					field:  fmt.Sprintf("BenefitList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActiveStateListViewValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActiveStateListViewValidationError{
					field:  "Info",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActiveStateListViewValidationError{
				field:  "Info",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActiveStateListViewMultiError(errors)
	}

	return nil
}

// ActiveStateListViewMultiError is an error wrapping multiple validation
// errors returned by ActiveStateListView.ValidateAll() if the designated
// constraints aren't met.
type ActiveStateListViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActiveStateListViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActiveStateListViewMultiError) AllErrors() []error { return m }

// ActiveStateListViewValidationError is the validation error returned by
// ActiveStateListView.Validate if the designated constraints aren't met.
type ActiveStateListViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActiveStateListViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActiveStateListViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActiveStateListViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActiveStateListViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActiveStateListViewValidationError) ErrorName() string {
	return "ActiveStateListViewValidationError"
}

// Error satisfies the builtin error interface
func (e ActiveStateListViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActiveStateListView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActiveStateListViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActiveStateListViewValidationError{}

// Validate checks the field values on GetTierFlowScreenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierFlowScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierFlowScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTierFlowScreenRequestMultiError, or nil if none found.
func (m *GetTierFlowScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierFlowScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierFlowScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierFlowScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierFlowScreenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if len(errors) > 0 {
		return GetTierFlowScreenRequestMultiError(errors)
	}

	return nil
}

// GetTierFlowScreenRequestMultiError is an error wrapping multiple validation
// errors returned by GetTierFlowScreenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTierFlowScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierFlowScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierFlowScreenRequestMultiError) AllErrors() []error { return m }

// GetTierFlowScreenRequestValidationError is the validation error returned by
// GetTierFlowScreenRequest.Validate if the designated constraints aren't met.
type GetTierFlowScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierFlowScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierFlowScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierFlowScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierFlowScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierFlowScreenRequestValidationError) ErrorName() string {
	return "GetTierFlowScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierFlowScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierFlowScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierFlowScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierFlowScreenRequestValidationError{}

// Validate checks the field values on GetTierFlowScreenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierFlowScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierFlowScreenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTierFlowScreenResponseMultiError, or nil if none found.
func (m *GetTierFlowScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierFlowScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierFlowScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierFlowScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierFlowScreenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierFlowScreenResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierFlowScreenResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierFlowScreenResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTierFlowScreenResponseMultiError(errors)
	}

	return nil
}

// GetTierFlowScreenResponseMultiError is an error wrapping multiple validation
// errors returned by GetTierFlowScreenResponse.ValidateAll() if the
// designated constraints aren't met.
type GetTierFlowScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierFlowScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierFlowScreenResponseMultiError) AllErrors() []error { return m }

// GetTierFlowScreenResponseValidationError is the validation error returned by
// GetTierFlowScreenResponse.Validate if the designated constraints aren't met.
type GetTierFlowScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierFlowScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierFlowScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierFlowScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierFlowScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierFlowScreenResponseValidationError) ErrorName() string {
	return "GetTierFlowScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierFlowScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierFlowScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierFlowScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierFlowScreenResponseValidationError{}

// Validate checks the field values on GetTierAllPlansV2Request with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierAllPlansV2Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierAllPlansV2Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTierAllPlansV2RequestMultiError, or nil if none found.
func (m *GetTierAllPlansV2Request) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierAllPlansV2Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierAllPlansV2RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierAllPlansV2RequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierAllPlansV2RequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MetaData

	if len(errors) > 0 {
		return GetTierAllPlansV2RequestMultiError(errors)
	}

	return nil
}

// GetTierAllPlansV2RequestMultiError is an error wrapping multiple validation
// errors returned by GetTierAllPlansV2Request.ValidateAll() if the designated
// constraints aren't met.
type GetTierAllPlansV2RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierAllPlansV2RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierAllPlansV2RequestMultiError) AllErrors() []error { return m }

// GetTierAllPlansV2RequestValidationError is the validation error returned by
// GetTierAllPlansV2Request.Validate if the designated constraints aren't met.
type GetTierAllPlansV2RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierAllPlansV2RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierAllPlansV2RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierAllPlansV2RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierAllPlansV2RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierAllPlansV2RequestValidationError) ErrorName() string {
	return "GetTierAllPlansV2RequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierAllPlansV2RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierAllPlansV2Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierAllPlansV2RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierAllPlansV2RequestValidationError{}

// Validate checks the field values on GetTierAllPlansV2Response with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTierAllPlansV2Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTierAllPlansV2Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTierAllPlansV2ResponseMultiError, or nil if none found.
func (m *GetTierAllPlansV2Response) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTierAllPlansV2Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierAllPlansV2ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierAllPlansV2ResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierAllPlansV2ResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTierAllPlansV2ResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTierAllPlansV2ResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTierAllPlansV2ResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TierIndexToFocus

	for idx, item := range m.GetTierPlans() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTierAllPlansV2ResponseValidationError{
						field:  fmt.Sprintf("TierPlans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTierAllPlansV2ResponseValidationError{
						field:  fmt.Sprintf("TierPlans[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTierAllPlansV2ResponseValidationError{
					field:  fmt.Sprintf("TierPlans[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTierAllPlansV2ResponseMultiError(errors)
	}

	return nil
}

// GetTierAllPlansV2ResponseMultiError is an error wrapping multiple validation
// errors returned by GetTierAllPlansV2Response.ValidateAll() if the
// designated constraints aren't met.
type GetTierAllPlansV2ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTierAllPlansV2ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTierAllPlansV2ResponseMultiError) AllErrors() []error { return m }

// GetTierAllPlansV2ResponseValidationError is the validation error returned by
// GetTierAllPlansV2Response.Validate if the designated constraints aren't met.
type GetTierAllPlansV2ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTierAllPlansV2ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTierAllPlansV2ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTierAllPlansV2ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTierAllPlansV2ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTierAllPlansV2ResponseValidationError) ErrorName() string {
	return "GetTierAllPlansV2ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTierAllPlansV2ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTierAllPlansV2Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTierAllPlansV2ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTierAllPlansV2ResponseValidationError{}

// Validate checks the field values on TierPlan with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TierPlan) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TierPlan with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TierPlanMultiError, or nil
// if none found.
func (m *TierPlan) ValidateAll() error {
	return m.validate(true)
}

func (m *TierPlan) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeaderBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "HeaderBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "HeaderBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TierPlanValidationError{
				field:  "HeaderBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderOverlayBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "HeaderOverlayBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "HeaderOverlayBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderOverlayBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TierPlanValidationError{
				field:  "HeaderOverlayBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContentBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "ContentBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "ContentBackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContentBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TierPlanValidationError{
				field:  "ContentBackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TierPlanValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSwipeIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "SwipeIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "SwipeIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSwipeIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TierPlanValidationError{
				field:  "SwipeIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPlanCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "PlanCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "PlanCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TierPlanValidationError{
				field:  "PlanCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetPlanBenefits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TierPlanValidationError{
						field:  fmt.Sprintf("PlanBenefits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TierPlanValidationError{
						field:  fmt.Sprintf("PlanBenefits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TierPlanValidationError{
					field:  fmt.Sprintf("PlanBenefits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TierPlanValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToolbarRightImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "ToolbarRightImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "ToolbarRightImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToolbarRightImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TierPlanValidationError{
				field:  "ToolbarRightImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToolbarRightImageDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "ToolbarRightImageDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TierPlanValidationError{
					field:  "ToolbarRightImageDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToolbarRightImageDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TierPlanValidationError{
				field:  "ToolbarRightImageDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PlanMeta

	// no validation rules for PlanMetaMap

	if len(errors) > 0 {
		return TierPlanMultiError(errors)
	}

	return nil
}

// TierPlanMultiError is an error wrapping multiple validation errors returned
// by TierPlan.ValidateAll() if the designated constraints aren't met.
type TierPlanMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TierPlanMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TierPlanMultiError) AllErrors() []error { return m }

// TierPlanValidationError is the validation error returned by
// TierPlan.Validate if the designated constraints aren't met.
type TierPlanValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TierPlanValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TierPlanValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TierPlanValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TierPlanValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TierPlanValidationError) ErrorName() string { return "TierPlanValidationError" }

// Error satisfies the builtin error interface
func (e TierPlanValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTierPlan.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TierPlanValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TierPlanValidationError{}

// Validate checks the field values on PlanBenefit with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PlanBenefit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PlanBenefit with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PlanBenefitMultiError, or
// nil if none found.
func (m *PlanBenefit) ValidateAll() error {
	return m.validate(true)
}

func (m *PlanBenefit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PlanBenefitValidationError{
					field:  "BackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PlanBenefitValidationError{
					field:  "BackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PlanBenefitValidationError{
				field:  "BackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PlanBenefitValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PlanBenefitValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PlanBenefitValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PlanBenefitValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PlanBenefitValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PlanBenefitValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BenefitMeta

	switch v := m.Benefit.(type) {
	case *PlanBenefit_BenefitCard:
		if v == nil {
			err := PlanBenefitValidationError{
				field:  "Benefit",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBenefitCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "BenefitCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "BenefitCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBenefitCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlanBenefitValidationError{
					field:  "BenefitCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PlanBenefit_SmallEntryBanner:
		if v == nil {
			err := PlanBenefitValidationError{
				field:  "Benefit",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetSmallEntryBanner()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "SmallEntryBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "SmallEntryBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetSmallEntryBanner()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlanBenefitValidationError{
					field:  "SmallEntryBanner",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PlanBenefit_EntryBanner:
		if v == nil {
			err := PlanBenefitValidationError{
				field:  "Benefit",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEntryBanner()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "EntryBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "EntryBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEntryBanner()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlanBenefitValidationError{
					field:  "EntryBanner",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PlanBenefit_InfoBanner:
		if v == nil {
			err := PlanBenefitValidationError{
				field:  "Benefit",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInfoBanner()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "InfoBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "InfoBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInfoBanner()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlanBenefitValidationError{
					field:  "InfoBanner",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *PlanBenefit_LearnMoreBanner:
		if v == nil {
			err := PlanBenefitValidationError{
				field:  "Benefit",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLearnMoreBanner()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "LearnMoreBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PlanBenefitValidationError{
						field:  "LearnMoreBanner",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLearnMoreBanner()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PlanBenefitValidationError{
					field:  "LearnMoreBanner",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PlanBenefitMultiError(errors)
	}

	return nil
}

// PlanBenefitMultiError is an error wrapping multiple validation errors
// returned by PlanBenefit.ValidateAll() if the designated constraints aren't met.
type PlanBenefitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PlanBenefitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PlanBenefitMultiError) AllErrors() []error { return m }

// PlanBenefitValidationError is the validation error returned by
// PlanBenefit.Validate if the designated constraints aren't met.
type PlanBenefitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PlanBenefitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PlanBenefitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PlanBenefitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PlanBenefitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PlanBenefitValidationError) ErrorName() string { return "PlanBenefitValidationError" }

// Error satisfies the builtin error interface
func (e PlanBenefitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPlanBenefit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PlanBenefitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PlanBenefitValidationError{}

// Validate checks the field values on GetAMBScreenDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAMBScreenDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAMBScreenDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAMBScreenDetailsRequestMultiError, or nil if none found.
func (m *GetAMBScreenDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAMBScreenDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBScreenDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBScreenDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBScreenDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAMBScreenDetailsRequestMultiError(errors)
	}

	return nil
}

// GetAMBScreenDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetAMBScreenDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetAMBScreenDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAMBScreenDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAMBScreenDetailsRequestMultiError) AllErrors() []error { return m }

// GetAMBScreenDetailsRequestValidationError is the validation error returned
// by GetAMBScreenDetailsRequest.Validate if the designated constraints aren't met.
type GetAMBScreenDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAMBScreenDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAMBScreenDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAMBScreenDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAMBScreenDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAMBScreenDetailsRequestValidationError) ErrorName() string {
	return "GetAMBScreenDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAMBScreenDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAMBScreenDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAMBScreenDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAMBScreenDetailsRequestValidationError{}

// Validate checks the field values on GetAMBScreenDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAMBScreenDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAMBScreenDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAMBScreenDetailsResponseMultiError, or nil if none found.
func (m *GetAMBScreenDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAMBScreenDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBScreenDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "Section",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBScreenDetailsResponseValidationError{
				field:  "Section",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAMBScreenDetailsResponseValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAMBScreenDetailsResponseValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAMBScreenDetailsResponseMultiError(errors)
	}

	return nil
}

// GetAMBScreenDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetAMBScreenDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAMBScreenDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAMBScreenDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAMBScreenDetailsResponseMultiError) AllErrors() []error { return m }

// GetAMBScreenDetailsResponseValidationError is the validation error returned
// by GetAMBScreenDetailsResponse.Validate if the designated constraints
// aren't met.
type GetAMBScreenDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAMBScreenDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAMBScreenDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAMBScreenDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAMBScreenDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAMBScreenDetailsResponseValidationError) ErrorName() string {
	return "GetAMBScreenDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAMBScreenDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAMBScreenDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAMBScreenDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAMBScreenDetailsResponseValidationError{}

// Validate checks the field values on
// GetEarnedBenefitsHistoryResponse_HeaderView with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetEarnedBenefitsHistoryResponse_HeaderView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetEarnedBenefitsHistoryResponse_HeaderView with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetEarnedBenefitsHistoryResponse_HeaderViewMultiError, or nil if none found.
func (m *GetEarnedBenefitsHistoryResponse_HeaderView) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEarnedBenefitsHistoryResponse_HeaderView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPlanIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
					field:  "PlanIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
					field:  "PlanIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlanIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
				field:  "PlanIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalBenefitsEarned()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
					field:  "TotalBenefitsEarned",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
					field:  "TotalBenefitsEarned",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalBenefitsEarned()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
				field:  "TotalBenefitsEarned",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalBenefitsEarnedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
					field:  "TotalBenefitsEarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
					field:  "TotalBenefitsEarnedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalBenefitsEarnedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
				field:  "TotalBenefitsEarnedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
					field:  "BottomText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
					field:  "BottomText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{
				field:  "BottomText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEarnedBenefitsHistoryResponse_HeaderViewMultiError(errors)
	}

	return nil
}

// GetEarnedBenefitsHistoryResponse_HeaderViewMultiError is an error wrapping
// multiple validation errors returned by
// GetEarnedBenefitsHistoryResponse_HeaderView.ValidateAll() if the designated
// constraints aren't met.
type GetEarnedBenefitsHistoryResponse_HeaderViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEarnedBenefitsHistoryResponse_HeaderViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEarnedBenefitsHistoryResponse_HeaderViewMultiError) AllErrors() []error { return m }

// GetEarnedBenefitsHistoryResponse_HeaderViewValidationError is the validation
// error returned by GetEarnedBenefitsHistoryResponse_HeaderView.Validate if
// the designated constraints aren't met.
type GetEarnedBenefitsHistoryResponse_HeaderViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEarnedBenefitsHistoryResponse_HeaderViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEarnedBenefitsHistoryResponse_HeaderViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEarnedBenefitsHistoryResponse_HeaderViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEarnedBenefitsHistoryResponse_HeaderViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEarnedBenefitsHistoryResponse_HeaderViewValidationError) ErrorName() string {
	return "GetEarnedBenefitsHistoryResponse_HeaderViewValidationError"
}

// Error satisfies the builtin error interface
func (e GetEarnedBenefitsHistoryResponse_HeaderViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEarnedBenefitsHistoryResponse_HeaderView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEarnedBenefitsHistoryResponse_HeaderViewValidationError{}

// Validate checks the field values on MonthlyRewardEarnedView_HeaderView with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *MonthlyRewardEarnedView_HeaderView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MonthlyRewardEarnedView_HeaderView
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// MonthlyRewardEarnedView_HeaderViewMultiError, or nil if none found.
func (m *MonthlyRewardEarnedView_HeaderView) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyRewardEarnedView_HeaderView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_HeaderViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_HeaderViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedView_HeaderViewValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_HeaderViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_HeaderViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedView_HeaderViewValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_HeaderViewValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_HeaderViewValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedView_HeaderViewValidationError{
				field:  "Tag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MonthlyRewardEarnedView_HeaderViewMultiError(errors)
	}

	return nil
}

// MonthlyRewardEarnedView_HeaderViewMultiError is an error wrapping multiple
// validation errors returned by
// MonthlyRewardEarnedView_HeaderView.ValidateAll() if the designated
// constraints aren't met.
type MonthlyRewardEarnedView_HeaderViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyRewardEarnedView_HeaderViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyRewardEarnedView_HeaderViewMultiError) AllErrors() []error { return m }

// MonthlyRewardEarnedView_HeaderViewValidationError is the validation error
// returned by MonthlyRewardEarnedView_HeaderView.Validate if the designated
// constraints aren't met.
type MonthlyRewardEarnedView_HeaderViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyRewardEarnedView_HeaderViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MonthlyRewardEarnedView_HeaderViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MonthlyRewardEarnedView_HeaderViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonthlyRewardEarnedView_HeaderViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonthlyRewardEarnedView_HeaderViewValidationError) ErrorName() string {
	return "MonthlyRewardEarnedView_HeaderViewValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyRewardEarnedView_HeaderViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyRewardEarnedView_HeaderView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyRewardEarnedView_HeaderViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyRewardEarnedView_HeaderViewValidationError{}

// Validate checks the field values on MonthlyRewardEarnedView_BenefitCardItem
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *MonthlyRewardEarnedView_BenefitCardItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MonthlyRewardEarnedView_BenefitCardItem with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// MonthlyRewardEarnedView_BenefitCardItemMultiError, or nil if none found.
func (m *MonthlyRewardEarnedView_BenefitCardItem) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyRewardEarnedView_BenefitCardItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_BenefitCardItemValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_BenefitCardItemValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedView_BenefitCardItemValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_BenefitCardItemValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyRewardEarnedView_BenefitCardItemValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyRewardEarnedView_BenefitCardItemValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MonthlyRewardEarnedView_BenefitCardItemMultiError(errors)
	}

	return nil
}

// MonthlyRewardEarnedView_BenefitCardItemMultiError is an error wrapping
// multiple validation errors returned by
// MonthlyRewardEarnedView_BenefitCardItem.ValidateAll() if the designated
// constraints aren't met.
type MonthlyRewardEarnedView_BenefitCardItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyRewardEarnedView_BenefitCardItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyRewardEarnedView_BenefitCardItemMultiError) AllErrors() []error { return m }

// MonthlyRewardEarnedView_BenefitCardItemValidationError is the validation
// error returned by MonthlyRewardEarnedView_BenefitCardItem.Validate if the
// designated constraints aren't met.
type MonthlyRewardEarnedView_BenefitCardItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyRewardEarnedView_BenefitCardItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MonthlyRewardEarnedView_BenefitCardItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MonthlyRewardEarnedView_BenefitCardItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonthlyRewardEarnedView_BenefitCardItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonthlyRewardEarnedView_BenefitCardItemValidationError) ErrorName() string {
	return "MonthlyRewardEarnedView_BenefitCardItemValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyRewardEarnedView_BenefitCardItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyRewardEarnedView_BenefitCardItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyRewardEarnedView_BenefitCardItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyRewardEarnedView_BenefitCardItemValidationError{}

// Validate checks the field values on UpgradeBenefitsView_Card with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpgradeBenefitsView_Card) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpgradeBenefitsView_Card with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpgradeBenefitsView_CardMultiError, or nil if none found.
func (m *UpgradeBenefitsView_Card) ValidateAll() error {
	return m.validate(true)
}

func (m *UpgradeBenefitsView_Card) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTag()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "Tag",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTag()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsView_CardValidationError{
				field:  "Tag",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsView_CardValidationError{
				field:  "VisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCashback()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "Cashback",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "Cashback",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCashback()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsView_CardValidationError{
				field:  "Cashback",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCoins()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "Coins",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "Coins",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCoins()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsView_CardValidationError{
				field:  "Coins",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpgradeBenefitsView_CardValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpgradeBenefitsView_CardValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	// no validation rules for TierIdentifier

	if len(errors) > 0 {
		return UpgradeBenefitsView_CardMultiError(errors)
	}

	return nil
}

// UpgradeBenefitsView_CardMultiError is an error wrapping multiple validation
// errors returned by UpgradeBenefitsView_Card.ValidateAll() if the designated
// constraints aren't met.
type UpgradeBenefitsView_CardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpgradeBenefitsView_CardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpgradeBenefitsView_CardMultiError) AllErrors() []error { return m }

// UpgradeBenefitsView_CardValidationError is the validation error returned by
// UpgradeBenefitsView_Card.Validate if the designated constraints aren't met.
type UpgradeBenefitsView_CardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpgradeBenefitsView_CardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpgradeBenefitsView_CardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpgradeBenefitsView_CardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpgradeBenefitsView_CardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpgradeBenefitsView_CardValidationError) ErrorName() string {
	return "UpgradeBenefitsView_CardValidationError"
}

// Error satisfies the builtin error interface
func (e UpgradeBenefitsView_CardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpgradeBenefitsView_Card.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpgradeBenefitsView_CardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpgradeBenefitsView_CardValidationError{}

// Validate checks the field values on MonthlyBenefitView_MonthlyBenefits with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *MonthlyBenefitView_MonthlyBenefits) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MonthlyBenefitView_MonthlyBenefits
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// MonthlyBenefitView_MonthlyBenefitsMultiError, or nil if none found.
func (m *MonthlyBenefitView_MonthlyBenefits) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyBenefitView_MonthlyBenefits) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeaderView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "HeaderView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "HeaderView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefitsValidationError{
				field:  "HeaderView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMonthlyBenefitTiles() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
						field:  fmt.Sprintf("MonthlyBenefitTiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
						field:  fmt.Sprintf("MonthlyBenefitTiles[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  fmt.Sprintf("MonthlyBenefitTiles[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInfoView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "InfoView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "InfoView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefitsValidationError{
				field:  "InfoView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CornerRadius

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefitsValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShouldGrayscale

	if all {
		switch v := interface{}(m.GetHeaderViewV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "HeaderViewV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "HeaderViewV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderViewV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefitsValidationError{
				field:  "HeaderViewV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOfferSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "OfferSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "OfferSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefitsValidationError{
				field:  "OfferSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmbEntryPoint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "AmbEntryPoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefitsValidationError{
					field:  "AmbEntryPoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmbEntryPoint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefitsValidationError{
				field:  "AmbEntryPoint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MonthlyBenefitView_MonthlyBenefitsMultiError(errors)
	}

	return nil
}

// MonthlyBenefitView_MonthlyBenefitsMultiError is an error wrapping multiple
// validation errors returned by
// MonthlyBenefitView_MonthlyBenefits.ValidateAll() if the designated
// constraints aren't met.
type MonthlyBenefitView_MonthlyBenefitsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyBenefitView_MonthlyBenefitsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyBenefitView_MonthlyBenefitsMultiError) AllErrors() []error { return m }

// MonthlyBenefitView_MonthlyBenefitsValidationError is the validation error
// returned by MonthlyBenefitView_MonthlyBenefits.Validate if the designated
// constraints aren't met.
type MonthlyBenefitView_MonthlyBenefitsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyBenefitView_MonthlyBenefitsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MonthlyBenefitView_MonthlyBenefitsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MonthlyBenefitView_MonthlyBenefitsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonthlyBenefitView_MonthlyBenefitsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonthlyBenefitView_MonthlyBenefitsValidationError) ErrorName() string {
	return "MonthlyBenefitView_MonthlyBenefitsValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyBenefitView_MonthlyBenefitsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyBenefitView_MonthlyBenefits.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyBenefitView_MonthlyBenefitsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyBenefitView_MonthlyBenefitsValidationError{}

// Validate checks the field values on
// MonthlyBenefitView_MonthlyBenefits_TitleView with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MonthlyBenefitView_MonthlyBenefits_TitleView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MonthlyBenefitView_MonthlyBenefits_TitleView with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// MonthlyBenefitView_MonthlyBenefits_TitleViewMultiError, or nil if none found.
func (m *MonthlyBenefitView_MonthlyBenefits_TitleView) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyBenefitView_MonthlyBenefits_TitleView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRightIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{
					field:  "RightIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRightIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{
				field:  "RightIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MonthlyBenefitView_MonthlyBenefits_TitleViewMultiError(errors)
	}

	return nil
}

// MonthlyBenefitView_MonthlyBenefits_TitleViewMultiError is an error wrapping
// multiple validation errors returned by
// MonthlyBenefitView_MonthlyBenefits_TitleView.ValidateAll() if the
// designated constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_TitleViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyBenefitView_MonthlyBenefits_TitleViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyBenefitView_MonthlyBenefits_TitleViewMultiError) AllErrors() []error { return m }

// MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError is the
// validation error returned by
// MonthlyBenefitView_MonthlyBenefits_TitleView.Validate if the designated
// constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError) ErrorName() string {
	return "MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyBenefitView_MonthlyBenefits_TitleView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyBenefitView_MonthlyBenefits_TitleViewValidationError{}

// Validate checks the field values on
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileMultiError, or nil if
// none found.
func (m *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
				field:  "LeftIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDividerView()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "DividerView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "DividerView",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDividerView()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
				field:  "DividerView",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProgressPercentage

	// no validation rules for ProgressActiveColor

	// no validation rules for ProgressBackgroundColor

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitleItc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "TitleItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "TitleItc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleItc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
				field:  "TitleItc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPlantIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "PlantIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  "PlantIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPlantIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
				field:  "PlantIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetEarningViews() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
						field:  fmt.Sprintf("EarningViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
						field:  fmt.Sprintf("EarningViews[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{
					field:  fmt.Sprintf("EarningViews[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NoOfRotations

	if len(errors) > 0 {
		return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileMultiError(errors)
	}

	return nil
}

// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileMultiError is an error
// wrapping multiple validation errors returned by
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile.ValidateAll() if the
// designated constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileMultiError) AllErrors() []error {
	return m
}

// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError is the
// validation error returned by
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile.Validate if the
// designated constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError) ErrorName() string {
	return "MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTileValidationError{}

// Validate checks the field values on
// MonthlyBenefitView_MonthlyBenefits_OfferSection with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *MonthlyBenefitView_MonthlyBenefits_OfferSection) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MonthlyBenefitView_MonthlyBenefits_OfferSection with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// MonthlyBenefitView_MonthlyBenefits_OfferSectionMultiError, or nil if none found.
func (m *MonthlyBenefitView_MonthlyBenefits_OfferSection) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyBenefitView_MonthlyBenefits_OfferSection) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.OfferContent.(type) {
	case *MonthlyBenefitView_MonthlyBenefits_OfferSection_VisualElement:
		if v == nil {
			err := MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
				field:  "OfferContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetVisualElement()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
						field:  "VisualElement",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
						field:  "VisualElement",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetVisualElement()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MonthlyBenefitView_MonthlyBenefits_OfferSection_OfferView:
		if v == nil {
			err := MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
				field:  "OfferContent",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetOfferView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
						field:  "OfferView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
						field:  "OfferView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetOfferView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{
					field:  "OfferView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return MonthlyBenefitView_MonthlyBenefits_OfferSectionMultiError(errors)
	}

	return nil
}

// MonthlyBenefitView_MonthlyBenefits_OfferSectionMultiError is an error
// wrapping multiple validation errors returned by
// MonthlyBenefitView_MonthlyBenefits_OfferSection.ValidateAll() if the
// designated constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_OfferSectionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyBenefitView_MonthlyBenefits_OfferSectionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyBenefitView_MonthlyBenefits_OfferSectionMultiError) AllErrors() []error { return m }

// MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError is the
// validation error returned by
// MonthlyBenefitView_MonthlyBenefits_OfferSection.Validate if the designated
// constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError) ErrorName() string {
	return "MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyBenefitView_MonthlyBenefits_OfferSection.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyBenefitView_MonthlyBenefits_OfferSectionValidationError{}

// Validate checks the field values on
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewMultiError,
// or nil if none found.
func (m *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetNumerator()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{
					field:  "Numerator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{
					field:  "Numerator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNumerator()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{
				field:  "Numerator",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDivider()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{
					field:  "Divider",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{
					field:  "Divider",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDivider()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{
				field:  "Divider",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDenominator()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{
					field:  "Denominator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{
					field:  "Denominator",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDenominator()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{
				field:  "Denominator",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewMultiError(errors)
	}

	return nil
}

// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewMultiError
// is an error wrapping multiple validation errors returned by
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView.ValidateAll()
// if the designated constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewMultiError) AllErrors() []error {
	return m
}

// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError
// is the validation error returned by
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView.Validate
// if the designated constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError) ErrorName() string {
	return "MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerViewValidationError{}

// Validate checks the field values on
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewMultiError,
// or nil if none found.
func (m *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) ValidateAll() error {
	return m.validate(true)
}

func (m *MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError{
						field:  fmt.Sprintf("Items[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError{
					field:  fmt.Sprintf("Items[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetContentColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError{
					field:  "ContentColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError{
					field:  "ContentColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContentColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError{
				field:  "ContentColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VisibleMillis

	if len(errors) > 0 {
		return MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewMultiError(errors)
	}

	return nil
}

// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewMultiError
// is an error wrapping multiple validation errors returned by
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView.ValidateAll()
// if the designated constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewMultiError) AllErrors() []error {
	return m
}

// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError
// is the validation error returned by
// MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView.Validate
// if the designated constraints aren't met.
type MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError) ErrorName() string {
	return "MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError"
}

// Error satisfies the builtin error interface
func (e MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningViewValidationError{}

// Validate checks the field values on WarningView_WarningCounterView with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WarningView_WarningCounterView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WarningView_WarningCounterView with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// WarningView_WarningCounterViewMultiError, or nil if none found.
func (m *WarningView_WarningCounterView) ValidateAll() error {
	return m.validate(true)
}

func (m *WarningView_WarningCounterView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningView_WarningCounterViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningView_WarningCounterViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningView_WarningCounterViewValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningView_WarningCounterViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningView_WarningCounterViewValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningView_WarningCounterViewValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WarningView_WarningCounterViewValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WarningView_WarningCounterViewValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WarningView_WarningCounterViewValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return WarningView_WarningCounterViewMultiError(errors)
	}

	return nil
}

// WarningView_WarningCounterViewMultiError is an error wrapping multiple
// validation errors returned by WarningView_WarningCounterView.ValidateAll()
// if the designated constraints aren't met.
type WarningView_WarningCounterViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WarningView_WarningCounterViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WarningView_WarningCounterViewMultiError) AllErrors() []error { return m }

// WarningView_WarningCounterViewValidationError is the validation error
// returned by WarningView_WarningCounterView.Validate if the designated
// constraints aren't met.
type WarningView_WarningCounterViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WarningView_WarningCounterViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WarningView_WarningCounterViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WarningView_WarningCounterViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WarningView_WarningCounterViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WarningView_WarningCounterViewValidationError) ErrorName() string {
	return "WarningView_WarningCounterViewValidationError"
}

// Error satisfies the builtin error interface
func (e WarningView_WarningCounterViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWarningView_WarningCounterView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WarningView_WarningCounterViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WarningView_WarningCounterViewValidationError{}

// Validate checks the field values on InActiveStateListView_ContainerView with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *InActiveStateListView_ContainerView) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InActiveStateListView_ContainerView
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// InActiveStateListView_ContainerViewMultiError, or nil if none found.
func (m *InActiveStateListView_ContainerView) ValidateAll() error {
	return m.validate(true)
}

func (m *InActiveStateListView_ContainerView) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InActiveStateListView_ContainerViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InActiveStateListView_ContainerViewValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InActiveStateListView_ContainerViewValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBenefitLists() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, InActiveStateListView_ContainerViewValidationError{
						field:  fmt.Sprintf("BenefitLists[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, InActiveStateListView_ContainerViewValidationError{
						field:  fmt.Sprintf("BenefitLists[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return InActiveStateListView_ContainerViewValidationError{
					field:  fmt.Sprintf("BenefitLists[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for CornerRadius

	if len(errors) > 0 {
		return InActiveStateListView_ContainerViewMultiError(errors)
	}

	return nil
}

// InActiveStateListView_ContainerViewMultiError is an error wrapping multiple
// validation errors returned by
// InActiveStateListView_ContainerView.ValidateAll() if the designated
// constraints aren't met.
type InActiveStateListView_ContainerViewMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InActiveStateListView_ContainerViewMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InActiveStateListView_ContainerViewMultiError) AllErrors() []error { return m }

// InActiveStateListView_ContainerViewValidationError is the validation error
// returned by InActiveStateListView_ContainerView.Validate if the designated
// constraints aren't met.
type InActiveStateListView_ContainerViewValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InActiveStateListView_ContainerViewValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InActiveStateListView_ContainerViewValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InActiveStateListView_ContainerViewValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InActiveStateListView_ContainerViewValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InActiveStateListView_ContainerViewValidationError) ErrorName() string {
	return "InActiveStateListView_ContainerViewValidationError"
}

// Error satisfies the builtin error interface
func (e InActiveStateListView_ContainerViewValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInActiveStateListView_ContainerView.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InActiveStateListView_ContainerViewValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InActiveStateListView_ContainerViewValidationError{}

// Validate checks the field values on
// InActiveStateListView_ContainerView_BenefitList with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InActiveStateListView_ContainerView_BenefitList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// InActiveStateListView_ContainerView_BenefitList with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// InActiveStateListView_ContainerView_BenefitListMultiError, or nil if none found.
func (m *InActiveStateListView_ContainerView_BenefitList) ValidateAll() error {
	return m.validate(true)
}

func (m *InActiveStateListView_ContainerView_BenefitList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InActiveStateListView_ContainerView_BenefitListValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InActiveStateListView_ContainerView_BenefitListValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InActiveStateListView_ContainerView_BenefitListValidationError{
				field:  "LeftIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InActiveStateListView_ContainerView_BenefitListValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InActiveStateListView_ContainerView_BenefitListValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InActiveStateListView_ContainerView_BenefitListValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InActiveStateListView_ContainerView_BenefitListValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InActiveStateListView_ContainerView_BenefitListValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InActiveStateListView_ContainerView_BenefitListValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InActiveStateListView_ContainerView_BenefitListMultiError(errors)
	}

	return nil
}

// InActiveStateListView_ContainerView_BenefitListMultiError is an error
// wrapping multiple validation errors returned by
// InActiveStateListView_ContainerView_BenefitList.ValidateAll() if the
// designated constraints aren't met.
type InActiveStateListView_ContainerView_BenefitListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InActiveStateListView_ContainerView_BenefitListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InActiveStateListView_ContainerView_BenefitListMultiError) AllErrors() []error { return m }

// InActiveStateListView_ContainerView_BenefitListValidationError is the
// validation error returned by
// InActiveStateListView_ContainerView_BenefitList.Validate if the designated
// constraints aren't met.
type InActiveStateListView_ContainerView_BenefitListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InActiveStateListView_ContainerView_BenefitListValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e InActiveStateListView_ContainerView_BenefitListValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e InActiveStateListView_ContainerView_BenefitListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InActiveStateListView_ContainerView_BenefitListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InActiveStateListView_ContainerView_BenefitListValidationError) ErrorName() string {
	return "InActiveStateListView_ContainerView_BenefitListValidationError"
}

// Error satisfies the builtin error interface
func (e InActiveStateListView_ContainerView_BenefitListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInActiveStateListView_ContainerView_BenefitList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InActiveStateListView_ContainerView_BenefitListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InActiveStateListView_ContainerView_BenefitListValidationError{}

// Validate checks the field values on ActiveStateListView_BenefitList with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActiveStateListView_BenefitList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActiveStateListView_BenefitList with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ActiveStateListView_BenefitListMultiError, or nil if none found.
func (m *ActiveStateListView_BenefitList) ValidateAll() error {
	return m.validate(true)
}

func (m *ActiveStateListView_BenefitList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetLeftIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActiveStateListView_BenefitListValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActiveStateListView_BenefitListValidationError{
					field:  "LeftIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActiveStateListView_BenefitListValidationError{
				field:  "LeftIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActiveStateListView_BenefitListValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActiveStateListView_BenefitListValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActiveStateListView_BenefitListValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.RightView.(type) {
	case *ActiveStateListView_BenefitList_RightAmount_:
		if v == nil {
			err := ActiveStateListView_BenefitListValidationError{
				field:  "RightView",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRightAmount()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActiveStateListView_BenefitListValidationError{
						field:  "RightAmount",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActiveStateListView_BenefitListValidationError{
						field:  "RightAmount",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRightAmount()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActiveStateListView_BenefitListValidationError{
					field:  "RightAmount",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ActiveStateListView_BenefitList_Icon:
		if v == nil {
			err := ActiveStateListView_BenefitListValidationError{
				field:  "RightView",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIcon()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActiveStateListView_BenefitListValidationError{
						field:  "Icon",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActiveStateListView_BenefitListValidationError{
						field:  "Icon",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActiveStateListView_BenefitListValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ActiveStateListView_BenefitListMultiError(errors)
	}

	return nil
}

// ActiveStateListView_BenefitListMultiError is an error wrapping multiple
// validation errors returned by ActiveStateListView_BenefitList.ValidateAll()
// if the designated constraints aren't met.
type ActiveStateListView_BenefitListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActiveStateListView_BenefitListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActiveStateListView_BenefitListMultiError) AllErrors() []error { return m }

// ActiveStateListView_BenefitListValidationError is the validation error
// returned by ActiveStateListView_BenefitList.Validate if the designated
// constraints aren't met.
type ActiveStateListView_BenefitListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActiveStateListView_BenefitListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActiveStateListView_BenefitListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActiveStateListView_BenefitListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActiveStateListView_BenefitListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActiveStateListView_BenefitListValidationError) ErrorName() string {
	return "ActiveStateListView_BenefitListValidationError"
}

// Error satisfies the builtin error interface
func (e ActiveStateListView_BenefitListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActiveStateListView_BenefitList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActiveStateListView_BenefitListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActiveStateListView_BenefitListValidationError{}

// Validate checks the field values on
// ActiveStateListView_BenefitList_RightAmount with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ActiveStateListView_BenefitList_RightAmount) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ActiveStateListView_BenefitList_RightAmount with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ActiveStateListView_BenefitList_RightAmountMultiError, or nil if none found.
func (m *ActiveStateListView_BenefitList_RightAmount) ValidateAll() error {
	return m.validate(true)
}

func (m *ActiveStateListView_BenefitList_RightAmount) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActiveStateListView_BenefitList_RightAmountValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActiveStateListView_BenefitList_RightAmountValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActiveStateListView_BenefitList_RightAmountValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStrikethroughAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ActiveStateListView_BenefitList_RightAmountValidationError{
					field:  "StrikethroughAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ActiveStateListView_BenefitList_RightAmountValidationError{
					field:  "StrikethroughAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStrikethroughAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ActiveStateListView_BenefitList_RightAmountValidationError{
				field:  "StrikethroughAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ActiveStateListView_BenefitList_RightAmountMultiError(errors)
	}

	return nil
}

// ActiveStateListView_BenefitList_RightAmountMultiError is an error wrapping
// multiple validation errors returned by
// ActiveStateListView_BenefitList_RightAmount.ValidateAll() if the designated
// constraints aren't met.
type ActiveStateListView_BenefitList_RightAmountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActiveStateListView_BenefitList_RightAmountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActiveStateListView_BenefitList_RightAmountMultiError) AllErrors() []error { return m }

// ActiveStateListView_BenefitList_RightAmountValidationError is the validation
// error returned by ActiveStateListView_BenefitList_RightAmount.Validate if
// the designated constraints aren't met.
type ActiveStateListView_BenefitList_RightAmountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActiveStateListView_BenefitList_RightAmountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActiveStateListView_BenefitList_RightAmountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActiveStateListView_BenefitList_RightAmountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActiveStateListView_BenefitList_RightAmountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActiveStateListView_BenefitList_RightAmountValidationError) ErrorName() string {
	return "ActiveStateListView_BenefitList_RightAmountValidationError"
}

// Error satisfies the builtin error interface
func (e ActiveStateListView_BenefitList_RightAmountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActiveStateListView_BenefitList_RightAmount.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActiveStateListView_BenefitList_RightAmountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActiveStateListView_BenefitList_RightAmountValidationError{}
