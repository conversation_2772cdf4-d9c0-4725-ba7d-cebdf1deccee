// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/networth/frontend/manual_form_identifiers.proto

package frontend

import (
	form "github.com/epifi/gamma/api/insights/user_declaration/form"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProfileDataFormIdentifierType int32

const (
	ProfileDataFormIdentifierType_PROFILE_DATA_FORM_IDENTIFIER_TYPE_UNSPECIFIED ProfileDataFormIdentifierType = 0
	ProfileDataFormIdentifierType_PROFILE_DATA_FORM_IDENTIFIER_TYPE_PAN         ProfileDataFormIdentifierType = 1
	ProfileDataFormIdentifierType_PROFILE_DATA_FORM_IDENTIFIER_TYPE_DOB         ProfileDataFormIdentifierType = 2
)

// Enum value maps for ProfileDataFormIdentifierType.
var (
	ProfileDataFormIdentifierType_name = map[int32]string{
		0: "PROFILE_DATA_FORM_IDENTIFIER_TYPE_UNSPECIFIED",
		1: "PROFILE_DATA_FORM_IDENTIFIER_TYPE_PAN",
		2: "PROFILE_DATA_FORM_IDENTIFIER_TYPE_DOB",
	}
	ProfileDataFormIdentifierType_value = map[string]int32{
		"PROFILE_DATA_FORM_IDENTIFIER_TYPE_UNSPECIFIED": 0,
		"PROFILE_DATA_FORM_IDENTIFIER_TYPE_PAN":         1,
		"PROFILE_DATA_FORM_IDENTIFIER_TYPE_DOB":         2,
	}
)

func (x ProfileDataFormIdentifierType) Enum() *ProfileDataFormIdentifierType {
	p := new(ProfileDataFormIdentifierType)
	*p = x
	return p
}

func (x ProfileDataFormIdentifierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProfileDataFormIdentifierType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_frontend_manual_form_identifiers_proto_enumTypes[0].Descriptor()
}

func (ProfileDataFormIdentifierType) Type() protoreflect.EnumType {
	return &file_api_insights_networth_frontend_manual_form_identifiers_proto_enumTypes[0]
}

func (x ProfileDataFormIdentifierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProfileDataFormIdentifierType.Descriptor instead.
func (ProfileDataFormIdentifierType) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescGZIP(), []int{0}
}

// ManualFormIdentifier id used to identify the form being filed
// This field is supposed to be sent as a marshalled json string to client for ease of forward compatability
type ManualFormIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*ManualFormIdentifier_InsightsUserDeclarationFormIdentifier
	//	*ManualFormIdentifier_ProfileDataFormIdentifier
	//	*ManualFormIdentifier_MagicImportFormIdentifier
	Identifier isManualFormIdentifier_Identifier `protobuf_oneof:"identifier"`
}

func (x *ManualFormIdentifier) Reset() {
	*x = ManualFormIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ManualFormIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ManualFormIdentifier) ProtoMessage() {}

func (x *ManualFormIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ManualFormIdentifier.ProtoReflect.Descriptor instead.
func (*ManualFormIdentifier) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescGZIP(), []int{0}
}

func (m *ManualFormIdentifier) GetIdentifier() isManualFormIdentifier_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *ManualFormIdentifier) GetInsightsUserDeclarationFormIdentifier() *form.UserDeclarationFormIdentifier {
	if x, ok := x.GetIdentifier().(*ManualFormIdentifier_InsightsUserDeclarationFormIdentifier); ok {
		return x.InsightsUserDeclarationFormIdentifier
	}
	return nil
}

func (x *ManualFormIdentifier) GetProfileDataFormIdentifier() *ProfileDataFormIdentifier {
	if x, ok := x.GetIdentifier().(*ManualFormIdentifier_ProfileDataFormIdentifier); ok {
		return x.ProfileDataFormIdentifier
	}
	return nil
}

func (x *ManualFormIdentifier) GetMagicImportFormIdentifier() *MagicImportFormIdentifier {
	if x, ok := x.GetIdentifier().(*ManualFormIdentifier_MagicImportFormIdentifier); ok {
		return x.MagicImportFormIdentifier
	}
	return nil
}

type isManualFormIdentifier_Identifier interface {
	isManualFormIdentifier_Identifier()
}

type ManualFormIdentifier_InsightsUserDeclarationFormIdentifier struct {
	InsightsUserDeclarationFormIdentifier *form.UserDeclarationFormIdentifier `protobuf:"bytes,1,opt,name=insights_user_declaration_form_identifier,json=insightsUserDeclarationFormIdentifier,proto3,oneof"`
}

type ManualFormIdentifier_ProfileDataFormIdentifier struct {
	ProfileDataFormIdentifier *ProfileDataFormIdentifier `protobuf:"bytes,2,opt,name=profile_data_form_identifier,json=profileDataFormIdentifier,proto3,oneof"`
}

type ManualFormIdentifier_MagicImportFormIdentifier struct {
	MagicImportFormIdentifier *MagicImportFormIdentifier `protobuf:"bytes,3,opt,name=magic_import_form_identifier,json=magicImportFormIdentifier,proto3,oneof"`
}

func (*ManualFormIdentifier_InsightsUserDeclarationFormIdentifier) isManualFormIdentifier_Identifier() {
}

func (*ManualFormIdentifier_ProfileDataFormIdentifier) isManualFormIdentifier_Identifier() {}

func (*ManualFormIdentifier_MagicImportFormIdentifier) isManualFormIdentifier_Identifier() {}

type MagicImportFormIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InstrumentType typesv2.InvestmentInstrumentType `protobuf:"varint,1,opt,name=instrument_type,json=instrumentType,proto3,enum=api.typesv2.InvestmentInstrumentType" json:"instrument_type,omitempty"`
}

func (x *MagicImportFormIdentifier) Reset() {
	*x = MagicImportFormIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MagicImportFormIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagicImportFormIdentifier) ProtoMessage() {}

func (x *MagicImportFormIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagicImportFormIdentifier.ProtoReflect.Descriptor instead.
func (*MagicImportFormIdentifier) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescGZIP(), []int{1}
}

func (x *MagicImportFormIdentifier) GetInstrumentType() typesv2.InvestmentInstrumentType {
	if x != nil {
		return x.InstrumentType
	}
	return typesv2.InvestmentInstrumentType(0)
}

type ProfileDataFormIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type ProfileDataFormIdentifierType `protobuf:"varint,1,opt,name=type,proto3,enum=insights.networth.frontend.ProfileDataFormIdentifierType" json:"type,omitempty"`
}

func (x *ProfileDataFormIdentifier) Reset() {
	*x = ProfileDataFormIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProfileDataFormIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfileDataFormIdentifier) ProtoMessage() {}

func (x *ProfileDataFormIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfileDataFormIdentifier.ProtoReflect.Descriptor instead.
func (*ProfileDataFormIdentifier) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescGZIP(), []int{2}
}

func (x *ProfileDataFormIdentifier) GetType() ProfileDataFormIdentifierType {
	if x != nil {
		return x.Type
	}
	return ProfileDataFormIdentifierType_PROFILE_DATA_FORM_IDENTIFIER_TYPE_UNSPECIFIED
}

var File_api_insights_networth_frontend_manual_form_identifiers_proto protoreflect.FileDescriptor

var file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2f, 0x6d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1a,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x66,
	0x6f, 0x72, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xb8, 0x03, 0x0a, 0x14, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x46, 0x6f, 0x72,
	0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x9d, 0x01, 0x0a, 0x29,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65,
	0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x41, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x25, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72,
	0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x78, 0x0a, 0x1c, 0x70,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x66, 0x6f, 0x72, 0x6d,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x35, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x19, 0x70, 0x72, 0x6f, 0x66,
	0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x78, 0x0a, 0x1c, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x5f, 0x69,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d,
	0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x48, 0x00, 0x52, 0x19, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x42,
	0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x6b, 0x0a,
	0x19, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x46, 0x6f, 0x72, 0x6d,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x0f, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x49, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x69, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22, 0x6a, 0x0a, 0x19, 0x50, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f,
	0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x2a, 0xa8, 0x01, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x31, 0x0a, 0x2d, 0x50, 0x52, 0x4f, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x50,
	0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x4f, 0x52, 0x4d,
	0x5f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x41, 0x4e, 0x10, 0x01, 0x12, 0x29, 0x0a, 0x25, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c,
	0x45, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x49, 0x44, 0x45, 0x4e,
	0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x42, 0x10,
	0x02, 0x42, 0x6e, 0x0a, 0x35, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescOnce sync.Once
	file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescData = file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDesc
)

func file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescGZIP() []byte {
	file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescOnce.Do(func() {
		file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescData)
	})
	return file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDescData
}

var file_api_insights_networth_frontend_manual_form_identifiers_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_insights_networth_frontend_manual_form_identifiers_proto_goTypes = []interface{}{
	(ProfileDataFormIdentifierType)(0),         // 0: insights.networth.frontend.ProfileDataFormIdentifierType
	(*ManualFormIdentifier)(nil),               // 1: insights.networth.frontend.ManualFormIdentifier
	(*MagicImportFormIdentifier)(nil),          // 2: insights.networth.frontend.MagicImportFormIdentifier
	(*ProfileDataFormIdentifier)(nil),          // 3: insights.networth.frontend.ProfileDataFormIdentifier
	(*form.UserDeclarationFormIdentifier)(nil), // 4: api.insights.user_declaration.form.UserDeclarationFormIdentifier
	(typesv2.InvestmentInstrumentType)(0),      // 5: api.typesv2.InvestmentInstrumentType
}
var file_api_insights_networth_frontend_manual_form_identifiers_proto_depIdxs = []int32{
	4, // 0: insights.networth.frontend.ManualFormIdentifier.insights_user_declaration_form_identifier:type_name -> api.insights.user_declaration.form.UserDeclarationFormIdentifier
	3, // 1: insights.networth.frontend.ManualFormIdentifier.profile_data_form_identifier:type_name -> insights.networth.frontend.ProfileDataFormIdentifier
	2, // 2: insights.networth.frontend.ManualFormIdentifier.magic_import_form_identifier:type_name -> insights.networth.frontend.MagicImportFormIdentifier
	5, // 3: insights.networth.frontend.MagicImportFormIdentifier.instrument_type:type_name -> api.typesv2.InvestmentInstrumentType
	0, // 4: insights.networth.frontend.ProfileDataFormIdentifier.type:type_name -> insights.networth.frontend.ProfileDataFormIdentifierType
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_insights_networth_frontend_manual_form_identifiers_proto_init() }
func file_api_insights_networth_frontend_manual_form_identifiers_proto_init() {
	if File_api_insights_networth_frontend_manual_form_identifiers_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ManualFormIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MagicImportFormIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProfileDataFormIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*ManualFormIdentifier_InsightsUserDeclarationFormIdentifier)(nil),
		(*ManualFormIdentifier_ProfileDataFormIdentifier)(nil),
		(*ManualFormIdentifier_MagicImportFormIdentifier)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_insights_networth_frontend_manual_form_identifiers_proto_goTypes,
		DependencyIndexes: file_api_insights_networth_frontend_manual_form_identifiers_proto_depIdxs,
		EnumInfos:         file_api_insights_networth_frontend_manual_form_identifiers_proto_enumTypes,
		MessageInfos:      file_api_insights_networth_frontend_manual_form_identifiers_proto_msgTypes,
	}.Build()
	File_api_insights_networth_frontend_manual_form_identifiers_proto = out.File
	file_api_insights_networth_frontend_manual_form_identifiers_proto_rawDesc = nil
	file_api_insights_networth_frontend_manual_form_identifiers_proto_goTypes = nil
	file_api_insights_networth_frontend_manual_form_identifiers_proto_depIdxs = nil
}
