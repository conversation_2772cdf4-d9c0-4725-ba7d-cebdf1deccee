// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/networth/frontend/manual_form_identifiers.proto

package frontend

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.InvestmentInstrumentType(0)
)

// Validate checks the field values on ManualFormIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ManualFormIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ManualFormIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ManualFormIdentifierMultiError, or nil if none found.
func (m *ManualFormIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *ManualFormIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *ManualFormIdentifier_InsightsUserDeclarationFormIdentifier:
		if v == nil {
			err := ManualFormIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetInsightsUserDeclarationFormIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ManualFormIdentifierValidationError{
						field:  "InsightsUserDeclarationFormIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ManualFormIdentifierValidationError{
						field:  "InsightsUserDeclarationFormIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetInsightsUserDeclarationFormIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ManualFormIdentifierValidationError{
					field:  "InsightsUserDeclarationFormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ManualFormIdentifier_ProfileDataFormIdentifier:
		if v == nil {
			err := ManualFormIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProfileDataFormIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ManualFormIdentifierValidationError{
						field:  "ProfileDataFormIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ManualFormIdentifierValidationError{
						field:  "ProfileDataFormIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProfileDataFormIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ManualFormIdentifierValidationError{
					field:  "ProfileDataFormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ManualFormIdentifier_MagicImportFormIdentifier:
		if v == nil {
			err := ManualFormIdentifierValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMagicImportFormIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ManualFormIdentifierValidationError{
						field:  "MagicImportFormIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ManualFormIdentifierValidationError{
						field:  "MagicImportFormIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMagicImportFormIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ManualFormIdentifierValidationError{
					field:  "MagicImportFormIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ManualFormIdentifierMultiError(errors)
	}

	return nil
}

// ManualFormIdentifierMultiError is an error wrapping multiple validation
// errors returned by ManualFormIdentifier.ValidateAll() if the designated
// constraints aren't met.
type ManualFormIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ManualFormIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ManualFormIdentifierMultiError) AllErrors() []error { return m }

// ManualFormIdentifierValidationError is the validation error returned by
// ManualFormIdentifier.Validate if the designated constraints aren't met.
type ManualFormIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ManualFormIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ManualFormIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ManualFormIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ManualFormIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ManualFormIdentifierValidationError) ErrorName() string {
	return "ManualFormIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e ManualFormIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sManualFormIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ManualFormIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ManualFormIdentifierValidationError{}

// Validate checks the field values on MagicImportFormIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MagicImportFormIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MagicImportFormIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MagicImportFormIdentifierMultiError, or nil if none found.
func (m *MagicImportFormIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *MagicImportFormIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InstrumentType

	if len(errors) > 0 {
		return MagicImportFormIdentifierMultiError(errors)
	}

	return nil
}

// MagicImportFormIdentifierMultiError is an error wrapping multiple validation
// errors returned by MagicImportFormIdentifier.ValidateAll() if the
// designated constraints aren't met.
type MagicImportFormIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MagicImportFormIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MagicImportFormIdentifierMultiError) AllErrors() []error { return m }

// MagicImportFormIdentifierValidationError is the validation error returned by
// MagicImportFormIdentifier.Validate if the designated constraints aren't met.
type MagicImportFormIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MagicImportFormIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MagicImportFormIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MagicImportFormIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MagicImportFormIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MagicImportFormIdentifierValidationError) ErrorName() string {
	return "MagicImportFormIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e MagicImportFormIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMagicImportFormIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MagicImportFormIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MagicImportFormIdentifierValidationError{}

// Validate checks the field values on ProfileDataFormIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProfileDataFormIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProfileDataFormIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProfileDataFormIdentifierMultiError, or nil if none found.
func (m *ProfileDataFormIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *ProfileDataFormIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if len(errors) > 0 {
		return ProfileDataFormIdentifierMultiError(errors)
	}

	return nil
}

// ProfileDataFormIdentifierMultiError is an error wrapping multiple validation
// errors returned by ProfileDataFormIdentifier.ValidateAll() if the
// designated constraints aren't met.
type ProfileDataFormIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProfileDataFormIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProfileDataFormIdentifierMultiError) AllErrors() []error { return m }

// ProfileDataFormIdentifierValidationError is the validation error returned by
// ProfileDataFormIdentifier.Validate if the designated constraints aren't met.
type ProfileDataFormIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProfileDataFormIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProfileDataFormIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProfileDataFormIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProfileDataFormIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProfileDataFormIdentifierValidationError) ErrorName() string {
	return "ProfileDataFormIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e ProfileDataFormIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProfileDataFormIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProfileDataFormIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProfileDataFormIdentifierValidationError{}
