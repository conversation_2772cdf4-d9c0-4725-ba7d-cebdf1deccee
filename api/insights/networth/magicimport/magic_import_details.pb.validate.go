// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/networth/magicimport/magic_import_details.proto

package magicimport

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on MagicImportDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MagicImportDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MagicImportDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MagicImportDetailsMultiError, or nil if none found.
func (m *MagicImportDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MagicImportDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAssetDetailsList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MagicImportDetailsValidationError{
						field:  fmt.Sprintf("AssetDetailsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MagicImportDetailsValidationError{
						field:  fmt.Sprintf("AssetDetailsList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MagicImportDetailsValidationError{
					field:  fmt.Sprintf("AssetDetailsList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return MagicImportDetailsMultiError(errors)
	}

	return nil
}

// MagicImportDetailsMultiError is an error wrapping multiple validation errors
// returned by MagicImportDetails.ValidateAll() if the designated constraints
// aren't met.
type MagicImportDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MagicImportDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MagicImportDetailsMultiError) AllErrors() []error { return m }

// MagicImportDetailsValidationError is the validation error returned by
// MagicImportDetails.Validate if the designated constraints aren't met.
type MagicImportDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MagicImportDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MagicImportDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MagicImportDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MagicImportDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MagicImportDetailsValidationError) ErrorName() string {
	return "MagicImportDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e MagicImportDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMagicImportDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MagicImportDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MagicImportDetailsValidationError{}

// Validate checks the field values on MagicImportAssetDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MagicImportAssetDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MagicImportAssetDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MagicImportAssetDetailsMultiError, or nil if none found.
func (m *MagicImportAssetDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *MagicImportAssetDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssetType

	switch v := m.Details.(type) {
	case *MagicImportAssetDetails_ConventionalAssetDetails:
		if v == nil {
			err := MagicImportAssetDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetConventionalAssetDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MagicImportAssetDetailsValidationError{
						field:  "ConventionalAssetDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MagicImportAssetDetailsValidationError{
						field:  "ConventionalAssetDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetConventionalAssetDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MagicImportAssetDetailsValidationError{
					field:  "ConventionalAssetDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *MagicImportAssetDetails_UnconventionalAssetDetails:
		if v == nil {
			err := MagicImportAssetDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUnconventionalAssetDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, MagicImportAssetDetailsValidationError{
						field:  "UnconventionalAssetDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, MagicImportAssetDetailsValidationError{
						field:  "UnconventionalAssetDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUnconventionalAssetDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return MagicImportAssetDetailsValidationError{
					field:  "UnconventionalAssetDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return MagicImportAssetDetailsMultiError(errors)
	}

	return nil
}

// MagicImportAssetDetailsMultiError is an error wrapping multiple validation
// errors returned by MagicImportAssetDetails.ValidateAll() if the designated
// constraints aren't met.
type MagicImportAssetDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MagicImportAssetDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MagicImportAssetDetailsMultiError) AllErrors() []error { return m }

// MagicImportAssetDetailsValidationError is the validation error returned by
// MagicImportAssetDetails.Validate if the designated constraints aren't met.
type MagicImportAssetDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MagicImportAssetDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MagicImportAssetDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MagicImportAssetDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MagicImportAssetDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MagicImportAssetDetailsValidationError) ErrorName() string {
	return "MagicImportAssetDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e MagicImportAssetDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMagicImportAssetDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MagicImportAssetDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MagicImportAssetDetailsValidationError{}

// Validate checks the field values on ConventionalAssetDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ConventionalAssetDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConventionalAssetDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ConventionalAssetDetailsMultiError, or nil if none found.
func (m *ConventionalAssetDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ConventionalAssetDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInvestmentDeclaration()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConventionalAssetDetailsValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConventionalAssetDetailsValidationError{
					field:  "InvestmentDeclaration",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDeclaration()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConventionalAssetDetailsValidationError{
				field:  "InvestmentDeclaration",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEstimatedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConventionalAssetDetailsValidationError{
					field:  "EstimatedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConventionalAssetDetailsValidationError{
					field:  "EstimatedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEstimatedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConventionalAssetDetailsValidationError{
				field:  "EstimatedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InvestmentName

	// no validation rules for FileName

	if len(errors) > 0 {
		return ConventionalAssetDetailsMultiError(errors)
	}

	return nil
}

// ConventionalAssetDetailsMultiError is an error wrapping multiple validation
// errors returned by ConventionalAssetDetails.ValidateAll() if the designated
// constraints aren't met.
type ConventionalAssetDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConventionalAssetDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConventionalAssetDetailsMultiError) AllErrors() []error { return m }

// ConventionalAssetDetailsValidationError is the validation error returned by
// ConventionalAssetDetails.Validate if the designated constraints aren't met.
type ConventionalAssetDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConventionalAssetDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConventionalAssetDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConventionalAssetDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConventionalAssetDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConventionalAssetDetailsValidationError) ErrorName() string {
	return "ConventionalAssetDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ConventionalAssetDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConventionalAssetDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConventionalAssetDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConventionalAssetDetailsValidationError{}

// Validate checks the field values on UnconventionalAssetDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnconventionalAssetDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnconventionalAssetDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnconventionalAssetDetailsMultiError, or nil if none found.
func (m *UnconventionalAssetDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *UnconventionalAssetDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AssetName

	// no validation rules for AssetType

	if len(errors) > 0 {
		return UnconventionalAssetDetailsMultiError(errors)
	}

	return nil
}

// UnconventionalAssetDetailsMultiError is an error wrapping multiple
// validation errors returned by UnconventionalAssetDetails.ValidateAll() if
// the designated constraints aren't met.
type UnconventionalAssetDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnconventionalAssetDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnconventionalAssetDetailsMultiError) AllErrors() []error { return m }

// UnconventionalAssetDetailsValidationError is the validation error returned
// by UnconventionalAssetDetails.Validate if the designated constraints aren't met.
type UnconventionalAssetDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnconventionalAssetDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnconventionalAssetDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnconventionalAssetDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnconventionalAssetDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnconventionalAssetDetailsValidationError) ErrorName() string {
	return "UnconventionalAssetDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e UnconventionalAssetDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnconventionalAssetDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnconventionalAssetDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnconventionalAssetDetailsValidationError{}
