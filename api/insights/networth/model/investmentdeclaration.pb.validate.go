// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/insights/networth/model/investmentdeclaration.proto

package model

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.InvestmentInstrumentType(0)
)

// Validate checks the field values on InvestmentDeclaration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InvestmentDeclaration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvestmentDeclaration with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InvestmentDeclarationMultiError, or nil if none found.
func (m *InvestmentDeclaration) ValidateAll() error {
	return m.validate(true)
}

func (m *InvestmentDeclaration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for InstrumentType

	if all {
		switch v := interface{}(m.GetInvestedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "InvestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "InvestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDeclarationValidationError{
				field:  "InvestedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "InvestedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "InvestedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDeclarationValidationError{
				field:  "InvestedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaturityDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "MaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "MaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDeclarationValidationError{
				field:  "MaturityDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InterestRate

	if all {
		switch v := interface{}(m.GetDeclarationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "DeclarationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "DeclarationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeclarationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDeclarationValidationError{
				field:  "DeclarationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDeclarationValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDeclarationValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvestmentDeclarationValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvestmentDeclarationValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ConsentId

	// no validation rules for ExternalId

	// no validation rules for Source

	if len(errors) > 0 {
		return InvestmentDeclarationMultiError(errors)
	}

	return nil
}

// InvestmentDeclarationMultiError is an error wrapping multiple validation
// errors returned by InvestmentDeclaration.ValidateAll() if the designated
// constraints aren't met.
type InvestmentDeclarationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvestmentDeclarationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvestmentDeclarationMultiError) AllErrors() []error { return m }

// InvestmentDeclarationValidationError is the validation error returned by
// InvestmentDeclaration.Validate if the designated constraints aren't met.
type InvestmentDeclarationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvestmentDeclarationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvestmentDeclarationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvestmentDeclarationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvestmentDeclarationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvestmentDeclarationValidationError) ErrorName() string {
	return "InvestmentDeclarationValidationError"
}

// Error satisfies the builtin error interface
func (e InvestmentDeclarationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvestmentDeclaration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvestmentDeclarationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvestmentDeclarationValidationError{}

// Validate checks the field values on OtherDeclarationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OtherDeclarationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OtherDeclarationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OtherDeclarationDetailsMultiError, or nil if none found.
func (m *OtherDeclarationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OtherDeclarationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Details.(type) {
	case *OtherDeclarationDetails_FixedDepositDeclarationDetails:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFixedDepositDeclarationDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "FixedDepositDeclarationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "FixedDepositDeclarationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFixedDepositDeclarationDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "FixedDepositDeclarationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_RecurringDepositDeclarationDetails:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRecurringDepositDeclarationDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "RecurringDepositDeclarationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "RecurringDepositDeclarationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRecurringDepositDeclarationDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "RecurringDepositDeclarationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_RealEstate:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRealEstate()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "RealEstate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "RealEstate",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRealEstate()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "RealEstate",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_Aif:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAif()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "Aif",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "Aif",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAif()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "Aif",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_PrivateEquity:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPrivateEquity()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "PrivateEquity",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "PrivateEquity",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPrivateEquity()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "PrivateEquity",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_DigitalGold:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDigitalGold()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "DigitalGold",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "DigitalGold",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDigitalGold()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "DigitalGold",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_Cash:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCash()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "Cash",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "Cash",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCash()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "Cash",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_DigitalSilver:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDigitalSilver()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "DigitalSilver",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "DigitalSilver",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDigitalSilver()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "DigitalSilver",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_Bond:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBond()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "Bond",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "Bond",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBond()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "Bond",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_ArtAndArtefacts:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetArtAndArtefacts()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "ArtAndArtefacts",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "ArtAndArtefacts",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetArtAndArtefacts()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "ArtAndArtefacts",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_PortfolioManagementService:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPortfolioManagementService()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "PortfolioManagementService",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "PortfolioManagementService",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPortfolioManagementService()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "PortfolioManagementService",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_PublicProvidentFund:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPublicProvidentFund()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "PublicProvidentFund",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "PublicProvidentFund",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPublicProvidentFund()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "PublicProvidentFund",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_EmployeeStockOption:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetEmployeeStockOption()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "EmployeeStockOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "EmployeeStockOption",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetEmployeeStockOption()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "EmployeeStockOption",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *OtherDeclarationDetails_Gadgets:
		if v == nil {
			err := OtherDeclarationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetGadgets()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "Gadgets",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OtherDeclarationDetailsValidationError{
						field:  "Gadgets",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetGadgets()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OtherDeclarationDetailsValidationError{
					field:  "Gadgets",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return OtherDeclarationDetailsMultiError(errors)
	}

	return nil
}

// OtherDeclarationDetailsMultiError is an error wrapping multiple validation
// errors returned by OtherDeclarationDetails.ValidateAll() if the designated
// constraints aren't met.
type OtherDeclarationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtherDeclarationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtherDeclarationDetailsMultiError) AllErrors() []error { return m }

// OtherDeclarationDetailsValidationError is the validation error returned by
// OtherDeclarationDetails.Validate if the designated constraints aren't met.
type OtherDeclarationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtherDeclarationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtherDeclarationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtherDeclarationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtherDeclarationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtherDeclarationDetailsValidationError) ErrorName() string {
	return "OtherDeclarationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OtherDeclarationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOtherDeclarationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtherDeclarationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtherDeclarationDetailsValidationError{}

// Validate checks the field values on RealEstate with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RealEstate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RealEstate with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RealEstateMultiError, or
// nil if none found.
func (m *RealEstate) ValidateAll() error {
	return m.validate(true)
}

func (m *RealEstate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetInvestmentName()); l < 1 || l > 100 {
		err := RealEstateValidationError{
			field:  "InvestmentName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInvestedValue() == nil {
		err := RealEstateValidationError{
			field:  "InvestedValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RealEstateValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RealEstateValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RealEstateValidationError{
				field:  "InvestedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetCurrentValue() == nil {
		err := RealEstateValidationError{
			field:  "CurrentValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RealEstateValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RealEstateValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RealEstateValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RealEstateValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RealEstateValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RealEstateValidationError{
				field:  "InvestmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RealEstateMultiError(errors)
	}

	return nil
}

// RealEstateMultiError is an error wrapping multiple validation errors
// returned by RealEstate.ValidateAll() if the designated constraints aren't met.
type RealEstateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RealEstateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RealEstateMultiError) AllErrors() []error { return m }

// RealEstateValidationError is the validation error returned by
// RealEstate.Validate if the designated constraints aren't met.
type RealEstateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RealEstateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RealEstateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RealEstateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RealEstateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RealEstateValidationError) ErrorName() string { return "RealEstateValidationError" }

// Error satisfies the builtin error interface
func (e RealEstateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRealEstate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RealEstateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RealEstateValidationError{}

// Validate checks the field values on AIF with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *AIF) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AIF with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AIFMultiError, or nil if none found.
func (m *AIF) ValidateAll() error {
	return m.validate(true)
}

func (m *AIF) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AifName

	if all {
		switch v := interface{}(m.GetInvestmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AIFValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AIFValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AIFValidationError{
				field:  "InvestmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEvaluationDate() == nil {
		err := AIFValidationError{
			field:  "EvaluationDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEvaluationDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AIFValidationError{
					field:  "EvaluationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AIFValidationError{
					field:  "EvaluationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvaluationDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AIFValidationError{
				field:  "EvaluationDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AmcName

	if _, ok := _AIF_Category_NotInLookup[m.GetCategory()]; ok {
		err := AIFValidationError{
			field:  "Category",
			reason: "value must not be in list [CATEGORY_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInvestedValue() == nil {
		err := AIFValidationError{
			field:  "InvestedValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AIFValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AIFValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AIFValidationError{
				field:  "InvestedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetCurrentValue() == nil {
		err := AIFValidationError{
			field:  "CurrentValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AIFValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AIFValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AIFValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetFolioId()) > 100 {
		err := AIFValidationError{
			field:  "FolioId",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetBrokerCode()) > 100 {
		err := AIFValidationError{
			field:  "BrokerCode",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRemarks()) > 100 {
		err := AIFValidationError{
			field:  "Remarks",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Identifier.(type) {
	case *AIF_AifId:
		if v == nil {
			err := AIFValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if l := utf8.RuneCountInString(m.GetAifId()); l < 1 || l > 100 {
			err := AIFValidationError{
				field:  "AifId",
				reason: "value length must be between 1 and 100 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *AIF_AifNameV2:
		if v == nil {
			err := AIFValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if l := utf8.RuneCountInString(m.GetAifNameV2()); l < 1 || l > 100 {
			err := AIFValidationError{
				field:  "AifNameV2",
				reason: "value length must be between 1 and 100 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AIFMultiError(errors)
	}

	return nil
}

// AIFMultiError is an error wrapping multiple validation errors returned by
// AIF.ValidateAll() if the designated constraints aren't met.
type AIFMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AIFMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AIFMultiError) AllErrors() []error { return m }

// AIFValidationError is the validation error returned by AIF.Validate if the
// designated constraints aren't met.
type AIFValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AIFValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AIFValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AIFValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AIFValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AIFValidationError) ErrorName() string { return "AIFValidationError" }

// Error satisfies the builtin error interface
func (e AIFValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAIF.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AIFValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AIFValidationError{}

var _AIF_Category_NotInLookup = map[AIF_Category]struct{}{
	0: {},
}

// Validate checks the field values on PrivateEquity with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PrivateEquity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrivateEquity with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PrivateEquityMultiError, or
// nil if none found.
func (m *PrivateEquity) ValidateAll() error {
	return m.validate(true)
}

func (m *PrivateEquity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetInvestmentName()); l < 1 || l > 100 {
		err := PrivateEquityValidationError{
			field:  "InvestmentName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInvestedValue() == nil {
		err := PrivateEquityValidationError{
			field:  "InvestedValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrivateEquityValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrivateEquityValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrivateEquityValidationError{
				field:  "InvestedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetCurrentValue() == nil {
		err := PrivateEquityValidationError{
			field:  "CurrentValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrivateEquityValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrivateEquityValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrivateEquityValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PrivateEquityValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PrivateEquityValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PrivateEquityValidationError{
				field:  "InvestmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PrivateEquityMultiError(errors)
	}

	return nil
}

// PrivateEquityMultiError is an error wrapping multiple validation errors
// returned by PrivateEquity.ValidateAll() if the designated constraints
// aren't met.
type PrivateEquityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrivateEquityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrivateEquityMultiError) AllErrors() []error { return m }

// PrivateEquityValidationError is the validation error returned by
// PrivateEquity.Validate if the designated constraints aren't met.
type PrivateEquityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrivateEquityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrivateEquityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrivateEquityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrivateEquityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrivateEquityValidationError) ErrorName() string { return "PrivateEquityValidationError" }

// Error satisfies the builtin error interface
func (e PrivateEquityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrivateEquity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrivateEquityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrivateEquityValidationError{}

// Validate checks the field values on DigitalGold with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DigitalGold) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DigitalGold with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DigitalGoldMultiError, or
// nil if none found.
func (m *DigitalGold) ValidateAll() error {
	return m.validate(true)
}

func (m *DigitalGold) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetInvestmentName()); l < 1 || l > 100 {
		err := DigitalGoldValidationError{
			field:  "InvestmentName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInvestedValue() == nil {
		err := DigitalGoldValidationError{
			field:  "InvestedValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigitalGoldValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigitalGoldValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigitalGoldValidationError{
				field:  "InvestedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetQuantityInGrams() <= 0 {
		err := DigitalGoldValidationError{
			field:  "QuantityInGrams",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigitalGoldValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigitalGoldValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigitalGoldValidationError{
				field:  "InvestmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _DigitalGold_GoldCaratValue_NotInLookup[m.GetGoldCaratValue()]; ok {
		err := DigitalGoldValidationError{
			field:  "GoldCaratValue",
			reason: "value must not be in list [GOLD_CARAT_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DigitalGoldMultiError(errors)
	}

	return nil
}

// DigitalGoldMultiError is an error wrapping multiple validation errors
// returned by DigitalGold.ValidateAll() if the designated constraints aren't met.
type DigitalGoldMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DigitalGoldMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DigitalGoldMultiError) AllErrors() []error { return m }

// DigitalGoldValidationError is the validation error returned by
// DigitalGold.Validate if the designated constraints aren't met.
type DigitalGoldValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DigitalGoldValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DigitalGoldValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DigitalGoldValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DigitalGoldValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DigitalGoldValidationError) ErrorName() string { return "DigitalGoldValidationError" }

// Error satisfies the builtin error interface
func (e DigitalGoldValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDigitalGold.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DigitalGoldValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DigitalGoldValidationError{}

var _DigitalGold_GoldCaratValue_NotInLookup = map[GoldCarat]struct{}{
	0: {},
}

// Validate checks the field values on ArtAndArtefacts with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ArtAndArtefacts) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ArtAndArtefacts with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ArtAndArtefactsMultiError, or nil if none found.
func (m *ArtAndArtefacts) ValidateAll() error {
	return m.validate(true)
}

func (m *ArtAndArtefacts) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetInvestmentName()); l < 1 || l > 100 {
		err := ArtAndArtefactsValidationError{
			field:  "InvestmentName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInvestedValue() == nil {
		err := ArtAndArtefactsValidationError{
			field:  "InvestedValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArtAndArtefactsValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArtAndArtefactsValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArtAndArtefactsValidationError{
				field:  "InvestedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetCurrentValue() == nil {
		err := ArtAndArtefactsValidationError{
			field:  "CurrentValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArtAndArtefactsValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArtAndArtefactsValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArtAndArtefactsValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPurchaseDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ArtAndArtefactsValidationError{
					field:  "PurchaseDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ArtAndArtefactsValidationError{
					field:  "PurchaseDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPurchaseDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ArtAndArtefactsValidationError{
				field:  "PurchaseDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ArtAndArtefactsMultiError(errors)
	}

	return nil
}

// ArtAndArtefactsMultiError is an error wrapping multiple validation errors
// returned by ArtAndArtefacts.ValidateAll() if the designated constraints
// aren't met.
type ArtAndArtefactsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ArtAndArtefactsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ArtAndArtefactsMultiError) AllErrors() []error { return m }

// ArtAndArtefactsValidationError is the validation error returned by
// ArtAndArtefacts.Validate if the designated constraints aren't met.
type ArtAndArtefactsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ArtAndArtefactsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ArtAndArtefactsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ArtAndArtefactsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ArtAndArtefactsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ArtAndArtefactsValidationError) ErrorName() string { return "ArtAndArtefactsValidationError" }

// Error satisfies the builtin error interface
func (e ArtAndArtefactsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sArtAndArtefacts.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ArtAndArtefactsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ArtAndArtefactsValidationError{}

// Validate checks the field values on Bond with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Bond) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Bond with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BondMultiError, or nil if none found.
func (m *Bond) ValidateAll() error {
	return m.validate(true)
}

func (m *Bond) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetInvestmentName()); l < 1 || l > 100 {
		err := BondValidationError{
			field:  "InvestmentName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetNumberOfUnits() <= 0 {
		err := BondValidationError{
			field:  "NumberOfUnits",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInvestedValuePerUnit() == nil {
		err := BondValidationError{
			field:  "InvestedValuePerUnit",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestedValuePerUnit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BondValidationError{
					field:  "InvestedValuePerUnit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BondValidationError{
					field:  "InvestedValuePerUnit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedValuePerUnit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BondValidationError{
				field:  "InvestedValuePerUnit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetCurrentValuePerUnit() == nil {
		err := BondValidationError{
			field:  "CurrentValuePerUnit",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCurrentValuePerUnit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BondValidationError{
					field:  "CurrentValuePerUnit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BondValidationError{
					field:  "CurrentValuePerUnit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValuePerUnit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BondValidationError{
				field:  "CurrentValuePerUnit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetInvestmentDate() == nil {
		err := BondValidationError{
			field:  "InvestmentDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BondValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BondValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BondValidationError{
				field:  "InvestmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetMaturityDate() == nil {
		err := BondValidationError{
			field:  "MaturityDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetMaturityDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BondValidationError{
					field:  "MaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BondValidationError{
					field:  "MaturityDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaturityDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BondValidationError{
				field:  "MaturityDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BondMultiError(errors)
	}

	return nil
}

// BondMultiError is an error wrapping multiple validation errors returned by
// Bond.ValidateAll() if the designated constraints aren't met.
type BondMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BondMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BondMultiError) AllErrors() []error { return m }

// BondValidationError is the validation error returned by Bond.Validate if the
// designated constraints aren't met.
type BondValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BondValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BondValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BondValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BondValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BondValidationError) ErrorName() string { return "BondValidationError" }

// Error satisfies the builtin error interface
func (e BondValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBond.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BondValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BondValidationError{}

// Validate checks the field values on DigitalSilver with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DigitalSilver) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DigitalSilver with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DigitalSilverMultiError, or
// nil if none found.
func (m *DigitalSilver) ValidateAll() error {
	return m.validate(true)
}

func (m *DigitalSilver) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetInvestmentName()); l < 1 || l > 100 {
		err := DigitalSilverValidationError{
			field:  "InvestmentName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInvestedValue() == nil {
		err := DigitalSilverValidationError{
			field:  "InvestedValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigitalSilverValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigitalSilverValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigitalSilverValidationError{
				field:  "InvestedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetQuantityInGrams() <= 0 {
		err := DigitalSilverValidationError{
			field:  "QuantityInGrams",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DigitalSilverValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DigitalSilverValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DigitalSilverValidationError{
				field:  "InvestmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DigitalSilverMultiError(errors)
	}

	return nil
}

// DigitalSilverMultiError is an error wrapping multiple validation errors
// returned by DigitalSilver.ValidateAll() if the designated constraints
// aren't met.
type DigitalSilverMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DigitalSilverMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DigitalSilverMultiError) AllErrors() []error { return m }

// DigitalSilverValidationError is the validation error returned by
// DigitalSilver.Validate if the designated constraints aren't met.
type DigitalSilverValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DigitalSilverValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DigitalSilverValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DigitalSilverValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DigitalSilverValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DigitalSilverValidationError) ErrorName() string { return "DigitalSilverValidationError" }

// Error satisfies the builtin error interface
func (e DigitalSilverValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDigitalSilver.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DigitalSilverValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DigitalSilverValidationError{}

// Validate checks the field values on Cash with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Cash) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Cash with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CashMultiError, or nil if none found.
func (m *Cash) ValidateAll() error {
	return m.validate(true)
}

func (m *Cash) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetCurrentAmount() == nil {
		err := CashValidationError{
			field:  "CurrentAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCurrentAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CashValidationError{
					field:  "CurrentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CashValidationError{
					field:  "CurrentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CashValidationError{
				field:  "CurrentAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastViewedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CashValidationError{
					field:  "LastViewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CashValidationError{
					field:  "LastViewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastViewedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CashValidationError{
				field:  "LastViewedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetName()); l < 0 || l > 100 {
		err := CashValidationError{
			field:  "Name",
			reason: "value length must be between 0 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CashMultiError(errors)
	}

	return nil
}

// CashMultiError is an error wrapping multiple validation errors returned by
// Cash.ValidateAll() if the designated constraints aren't met.
type CashMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CashMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CashMultiError) AllErrors() []error { return m }

// CashValidationError is the validation error returned by Cash.Validate if the
// designated constraints aren't met.
type CashValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CashValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CashValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CashValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CashValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CashValidationError) ErrorName() string { return "CashValidationError" }

// Error satisfies the builtin error interface
func (e CashValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCash.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CashValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CashValidationError{}

// Validate checks the field values on FixedDepositDeclarationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FixedDepositDeclarationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FixedDepositDeclarationDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FixedDepositDeclarationDetailsMultiError, or nil if none found.
func (m *FixedDepositDeclarationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FixedDepositDeclarationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompoundingFrequency

	// no validation rules for DepositName

	if len(errors) > 0 {
		return FixedDepositDeclarationDetailsMultiError(errors)
	}

	return nil
}

// FixedDepositDeclarationDetailsMultiError is an error wrapping multiple
// validation errors returned by FixedDepositDeclarationDetails.ValidateAll()
// if the designated constraints aren't met.
type FixedDepositDeclarationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FixedDepositDeclarationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FixedDepositDeclarationDetailsMultiError) AllErrors() []error { return m }

// FixedDepositDeclarationDetailsValidationError is the validation error
// returned by FixedDepositDeclarationDetails.Validate if the designated
// constraints aren't met.
type FixedDepositDeclarationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FixedDepositDeclarationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FixedDepositDeclarationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FixedDepositDeclarationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FixedDepositDeclarationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FixedDepositDeclarationDetailsValidationError) ErrorName() string {
	return "FixedDepositDeclarationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FixedDepositDeclarationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFixedDepositDeclarationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FixedDepositDeclarationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FixedDepositDeclarationDetailsValidationError{}

// Validate checks the field values on RecurringDepositDeclarationDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecurringDepositDeclarationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecurringDepositDeclarationDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecurringDepositDeclarationDetailsMultiError, or nil if none found.
func (m *RecurringDepositDeclarationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RecurringDepositDeclarationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CompoundingFrequency

	// no validation rules for DepositName

	if len(errors) > 0 {
		return RecurringDepositDeclarationDetailsMultiError(errors)
	}

	return nil
}

// RecurringDepositDeclarationDetailsMultiError is an error wrapping multiple
// validation errors returned by
// RecurringDepositDeclarationDetails.ValidateAll() if the designated
// constraints aren't met.
type RecurringDepositDeclarationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecurringDepositDeclarationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecurringDepositDeclarationDetailsMultiError) AllErrors() []error { return m }

// RecurringDepositDeclarationDetailsValidationError is the validation error
// returned by RecurringDepositDeclarationDetails.Validate if the designated
// constraints aren't met.
type RecurringDepositDeclarationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecurringDepositDeclarationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecurringDepositDeclarationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecurringDepositDeclarationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecurringDepositDeclarationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecurringDepositDeclarationDetailsValidationError) ErrorName() string {
	return "RecurringDepositDeclarationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RecurringDepositDeclarationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecurringDepositDeclarationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecurringDepositDeclarationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecurringDepositDeclarationDetailsValidationError{}

// Validate checks the field values on PortfolioManagementService with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PortfolioManagementService) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortfolioManagementService with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PortfolioManagementServiceMultiError, or nil if none found.
func (m *PortfolioManagementService) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioManagementService) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetPmsName()); l < 1 || l > 100 {
		err := PortfolioManagementServiceValidationError{
			field:  "PmsName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInvestmentDate() == nil {
		err := PortfolioManagementServiceValidationError{
			field:  "InvestmentDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioManagementServiceValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioManagementServiceValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioManagementServiceValidationError{
				field:  "InvestmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetEvaluationDate() == nil {
		err := PortfolioManagementServiceValidationError{
			field:  "EvaluationDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEvaluationDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioManagementServiceValidationError{
					field:  "EvaluationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioManagementServiceValidationError{
					field:  "EvaluationDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEvaluationDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioManagementServiceValidationError{
				field:  "EvaluationDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AmcName

	if m.GetInvestedValue() == nil {
		err := PortfolioManagementServiceValidationError{
			field:  "InvestedValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestedValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioManagementServiceValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioManagementServiceValidationError{
					field:  "InvestedValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestedValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioManagementServiceValidationError{
				field:  "InvestedValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetCurrentValue() == nil {
		err := PortfolioManagementServiceValidationError{
			field:  "CurrentValue",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PortfolioManagementServiceValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PortfolioManagementServiceValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PortfolioManagementServiceValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetFolioId()) > 100 {
		err := PortfolioManagementServiceValidationError{
			field:  "FolioId",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetBrokerCode()) > 100 {
		err := PortfolioManagementServiceValidationError{
			field:  "BrokerCode",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetRemarks()) > 100 {
		err := PortfolioManagementServiceValidationError{
			field:  "Remarks",
			reason: "value length must be at most 100 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Identifier.(type) {
	case *PortfolioManagementService_AmcId:
		if v == nil {
			err := PortfolioManagementServiceValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if l := utf8.RuneCountInString(m.GetAmcId()); l < 1 || l > 100 {
			err := PortfolioManagementServiceValidationError{
				field:  "AmcId",
				reason: "value length must be between 1 and 100 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	case *PortfolioManagementService_AmcNameV2:
		if v == nil {
			err := PortfolioManagementServiceValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if l := utf8.RuneCountInString(m.GetAmcNameV2()); l < 1 || l > 100 {
			err := PortfolioManagementServiceValidationError{
				field:  "AmcNameV2",
				reason: "value length must be between 1 and 100 runes, inclusive",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PortfolioManagementServiceMultiError(errors)
	}

	return nil
}

// PortfolioManagementServiceMultiError is an error wrapping multiple
// validation errors returned by PortfolioManagementService.ValidateAll() if
// the designated constraints aren't met.
type PortfolioManagementServiceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioManagementServiceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioManagementServiceMultiError) AllErrors() []error { return m }

// PortfolioManagementServiceValidationError is the validation error returned
// by PortfolioManagementService.Validate if the designated constraints aren't met.
type PortfolioManagementServiceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioManagementServiceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioManagementServiceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioManagementServiceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioManagementServiceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioManagementServiceValidationError) ErrorName() string {
	return "PortfolioManagementServiceValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioManagementServiceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioManagementService.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioManagementServiceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioManagementServiceValidationError{}

// Validate checks the field values on PublicProvidentFund with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PublicProvidentFund) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PublicProvidentFund with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PublicProvidentFundMultiError, or nil if none found.
func (m *PublicProvidentFund) ValidateAll() error {
	return m.validate(true)
}

func (m *PublicProvidentFund) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _PublicProvidentFund_InvestmentFrequency_NotInLookup[m.GetInvestmentFrequency()]; ok {
		err := PublicProvidentFundValidationError{
			field:  "InvestmentFrequency",
			reason: "value must not be in list [INVESTMENT_FREQUENCY_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDepositAmount() == nil {
		err := PublicProvidentFundValidationError{
			field:  "DepositAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDepositAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublicProvidentFundValidationError{
					field:  "DepositAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublicProvidentFundValidationError{
					field:  "DepositAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDepositAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublicProvidentFundValidationError{
				field:  "DepositAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetInvestmentDate() == nil {
		err := PublicProvidentFundValidationError{
			field:  "InvestmentDate",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInvestmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PublicProvidentFundValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PublicProvidentFundValidationError{
					field:  "InvestmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PublicProvidentFundValidationError{
				field:  "InvestmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PublicProvidentFundMultiError(errors)
	}

	return nil
}

// PublicProvidentFundMultiError is an error wrapping multiple validation
// errors returned by PublicProvidentFund.ValidateAll() if the designated
// constraints aren't met.
type PublicProvidentFundMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PublicProvidentFundMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PublicProvidentFundMultiError) AllErrors() []error { return m }

// PublicProvidentFundValidationError is the validation error returned by
// PublicProvidentFund.Validate if the designated constraints aren't met.
type PublicProvidentFundValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PublicProvidentFundValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PublicProvidentFundValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PublicProvidentFundValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PublicProvidentFundValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PublicProvidentFundValidationError) ErrorName() string {
	return "PublicProvidentFundValidationError"
}

// Error satisfies the builtin error interface
func (e PublicProvidentFundValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPublicProvidentFund.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PublicProvidentFundValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PublicProvidentFundValidationError{}

var _PublicProvidentFund_InvestmentFrequency_NotInLookup = map[InvestmentFrequency]struct{}{
	0: {},
}

// Validate checks the field values on EmployeeStockOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EmployeeStockOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmployeeStockOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmployeeStockOptionMultiError, or nil if none found.
func (m *EmployeeStockOption) ValidateAll() error {
	return m.validate(true)
}

func (m *EmployeeStockOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrencyType

	// no validation rules for CompanyName

	// no validation rules for EsopInGrant

	if all {
		switch v := interface{}(m.GetFirstIssueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmployeeStockOptionValidationError{
					field:  "FirstIssueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmployeeStockOptionValidationError{
					field:  "FirstIssueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFirstIssueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmployeeStockOptionValidationError{
				field:  "FirstIssueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVestingSchedule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmployeeStockOptionValidationError{
					field:  "VestingSchedule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmployeeStockOptionValidationError{
					field:  "VestingSchedule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVestingSchedule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmployeeStockOptionValidationError{
				field:  "VestingSchedule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentValuePerShare()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EmployeeStockOptionValidationError{
					field:  "CurrentValuePerShare",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EmployeeStockOptionValidationError{
					field:  "CurrentValuePerShare",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValuePerShare()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EmployeeStockOptionValidationError{
				field:  "CurrentValuePerShare",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EmployeeStockOptionMultiError(errors)
	}

	return nil
}

// EmployeeStockOptionMultiError is an error wrapping multiple validation
// errors returned by EmployeeStockOption.ValidateAll() if the designated
// constraints aren't met.
type EmployeeStockOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmployeeStockOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmployeeStockOptionMultiError) AllErrors() []error { return m }

// EmployeeStockOptionValidationError is the validation error returned by
// EmployeeStockOption.Validate if the designated constraints aren't met.
type EmployeeStockOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmployeeStockOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmployeeStockOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmployeeStockOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmployeeStockOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmployeeStockOptionValidationError) ErrorName() string {
	return "EmployeeStockOptionValidationError"
}

// Error satisfies the builtin error interface
func (e EmployeeStockOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmployeeStockOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmployeeStockOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmployeeStockOptionValidationError{}

// Validate checks the field values on VestingSchedule with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VestingSchedule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VestingSchedule with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VestingScheduleMultiError, or nil if none found.
func (m *VestingSchedule) ValidateAll() error {
	return m.validate(true)
}

func (m *VestingSchedule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return VestingScheduleMultiError(errors)
	}

	return nil
}

// VestingScheduleMultiError is an error wrapping multiple validation errors
// returned by VestingSchedule.ValidateAll() if the designated constraints
// aren't met.
type VestingScheduleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VestingScheduleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VestingScheduleMultiError) AllErrors() []error { return m }

// VestingScheduleValidationError is the validation error returned by
// VestingSchedule.Validate if the designated constraints aren't met.
type VestingScheduleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VestingScheduleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VestingScheduleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VestingScheduleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VestingScheduleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VestingScheduleValidationError) ErrorName() string { return "VestingScheduleValidationError" }

// Error satisfies the builtin error interface
func (e VestingScheduleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVestingSchedule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VestingScheduleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VestingScheduleValidationError{}

// Validate checks the field values on Gadgets with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Gadgets) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Gadgets with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in GadgetsMultiError, or nil if none found.
func (m *Gadgets) ValidateAll() error {
	return m.validate(true)
}

func (m *Gadgets) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if l := utf8.RuneCountInString(m.GetDeviceType()); l < 1 || l > 100 {
		err := GadgetsValidationError{
			field:  "DeviceType",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if l := utf8.RuneCountInString(m.GetInvestmentName()); l < 1 || l > 100 {
		err := GadgetsValidationError{
			field:  "InvestmentName",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetYearOfPurchase()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GadgetsValidationError{
					field:  "YearOfPurchase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GadgetsValidationError{
					field:  "YearOfPurchase",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetYearOfPurchase()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GadgetsValidationError{
				field:  "YearOfPurchase",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := utf8.RuneCountInString(m.GetCondition()); l < 1 || l > 100 {
		err := GadgetsValidationError{
			field:  "Condition",
			reason: "value length must be between 1 and 100 runes, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetCurrentValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GadgetsValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GadgetsValidationError{
					field:  "CurrentValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GadgetsValidationError{
				field:  "CurrentValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GadgetsMultiError(errors)
	}

	return nil
}

// GadgetsMultiError is an error wrapping multiple validation errors returned
// by Gadgets.ValidateAll() if the designated constraints aren't met.
type GadgetsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GadgetsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GadgetsMultiError) AllErrors() []error { return m }

// GadgetsValidationError is the validation error returned by Gadgets.Validate
// if the designated constraints aren't met.
type GadgetsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GadgetsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GadgetsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GadgetsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GadgetsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GadgetsValidationError) ErrorName() string { return "GadgetsValidationError" }

// Error satisfies the builtin error interface
func (e GadgetsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGadgets.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GadgetsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GadgetsValidationError{}
