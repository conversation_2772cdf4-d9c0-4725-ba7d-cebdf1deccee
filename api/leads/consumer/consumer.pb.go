// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/leads/consumer/consumer.proto

package consumer

import (
	queue "github.com/epifi/be-common/api/queue"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProcessDeleteUserEventRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer request
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// actor id of the user to be deleted
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *ProcessDeleteUserEventRequest) Reset() {
	*x = ProcessDeleteUserEventRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_consumer_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessDeleteUserEventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessDeleteUserEventRequest) ProtoMessage() {}

func (x *ProcessDeleteUserEventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_consumer_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessDeleteUserEventRequest.ProtoReflect.Descriptor instead.
func (*ProcessDeleteUserEventRequest) Descriptor() ([]byte, []int) {
	return file_api_leads_consumer_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *ProcessDeleteUserEventRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *ProcessDeleteUserEventRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type ProcessDeleteUserEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessDeleteUserEventResponse) Reset() {
	*x = ProcessDeleteUserEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_leads_consumer_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessDeleteUserEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessDeleteUserEventResponse) ProtoMessage() {}

func (x *ProcessDeleteUserEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_leads_consumer_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessDeleteUserEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessDeleteUserEventResponse) Descriptor() ([]byte, []int) {
	return file_api_leads_consumer_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessDeleteUserEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

var File_api_leads_consumer_consumer_proto protoreflect.FileDescriptor

var file_api_leads_consumer_consumer_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2f, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7f, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c,
	0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x68, 0x0a, 0x1e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x32, 0x83, 0x01, 0x0a, 0x08, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x12, 0x77, 0x0a,
	0x16, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2d, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x56, 0x0a, 0x29, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6c, 0x65, 0x61, 0x64, 0x73, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x6c, 0x65, 0x61, 0x64, 0x73, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_leads_consumer_consumer_proto_rawDescOnce sync.Once
	file_api_leads_consumer_consumer_proto_rawDescData = file_api_leads_consumer_consumer_proto_rawDesc
)

func file_api_leads_consumer_consumer_proto_rawDescGZIP() []byte {
	file_api_leads_consumer_consumer_proto_rawDescOnce.Do(func() {
		file_api_leads_consumer_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_leads_consumer_consumer_proto_rawDescData)
	})
	return file_api_leads_consumer_consumer_proto_rawDescData
}

var file_api_leads_consumer_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_leads_consumer_consumer_proto_goTypes = []interface{}{
	(*ProcessDeleteUserEventRequest)(nil),  // 0: leads.consumer.ProcessDeleteUserEventRequest
	(*ProcessDeleteUserEventResponse)(nil), // 1: leads.consumer.ProcessDeleteUserEventResponse
	(*queue.ConsumerRequestHeader)(nil),    // 2: queue.ConsumerRequestHeader
	(*queue.ConsumerResponseHeader)(nil),   // 3: queue.ConsumerResponseHeader
}
var file_api_leads_consumer_consumer_proto_depIdxs = []int32{
	2, // 0: leads.consumer.ProcessDeleteUserEventRequest.request_header:type_name -> queue.ConsumerRequestHeader
	3, // 1: leads.consumer.ProcessDeleteUserEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	0, // 2: leads.consumer.Consumer.ProcessDeleteUserEvent:input_type -> leads.consumer.ProcessDeleteUserEventRequest
	1, // 3: leads.consumer.Consumer.ProcessDeleteUserEvent:output_type -> leads.consumer.ProcessDeleteUserEventResponse
	3, // [3:4] is the sub-list for method output_type
	2, // [2:3] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_api_leads_consumer_consumer_proto_init() }
func file_api_leads_consumer_consumer_proto_init() {
	if File_api_leads_consumer_consumer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_leads_consumer_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessDeleteUserEventRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_leads_consumer_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessDeleteUserEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_leads_consumer_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_leads_consumer_consumer_proto_goTypes,
		DependencyIndexes: file_api_leads_consumer_consumer_proto_depIdxs,
		MessageInfos:      file_api_leads_consumer_consumer_proto_msgTypes,
	}.Build()
	File_api_leads_consumer_consumer_proto = out.File
	file_api_leads_consumer_consumer_proto_rawDesc = nil
	file_api_leads_consumer_consumer_proto_goTypes = nil
	file_api_leads_consumer_consumer_proto_depIdxs = nil
}
