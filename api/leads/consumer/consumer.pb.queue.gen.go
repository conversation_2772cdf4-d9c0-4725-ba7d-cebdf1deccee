// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/leads/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	ProcessDeleteUserEventMethod = "ProcessDeleteUserEvent"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &ProcessDeleteUserEventRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessDeleteUserEventRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterProcessDeleteUserEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessDeleteUserEventMethodToSubscriber(subscriber queue.Subscriber, srv ConsumerServer) {
	subscriber.RegisterService(&Consumer_ServiceDesc, srv, ProcessDeleteUserEventMethod)
}
