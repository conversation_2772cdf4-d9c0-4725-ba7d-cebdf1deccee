// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/leads/consumer/consumer.proto

package consumer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProcessDeleteUserEventRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessDeleteUserEventRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessDeleteUserEventRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessDeleteUserEventRequestMultiError, or nil if none found.
func (m *ProcessDeleteUserEventRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessDeleteUserEventRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDeleteUserEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDeleteUserEventRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDeleteUserEventRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return ProcessDeleteUserEventRequestMultiError(errors)
	}

	return nil
}

// ProcessDeleteUserEventRequestMultiError is an error wrapping multiple
// validation errors returned by ProcessDeleteUserEventRequest.ValidateAll()
// if the designated constraints aren't met.
type ProcessDeleteUserEventRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessDeleteUserEventRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessDeleteUserEventRequestMultiError) AllErrors() []error { return m }

// ProcessDeleteUserEventRequestValidationError is the validation error
// returned by ProcessDeleteUserEventRequest.Validate if the designated
// constraints aren't met.
type ProcessDeleteUserEventRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessDeleteUserEventRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessDeleteUserEventRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessDeleteUserEventRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessDeleteUserEventRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessDeleteUserEventRequestValidationError) ErrorName() string {
	return "ProcessDeleteUserEventRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessDeleteUserEventRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessDeleteUserEventRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessDeleteUserEventRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessDeleteUserEventRequestValidationError{}

// Validate checks the field values on ProcessDeleteUserEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessDeleteUserEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessDeleteUserEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessDeleteUserEventResponseMultiError, or nil if none found.
func (m *ProcessDeleteUserEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessDeleteUserEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessDeleteUserEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessDeleteUserEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessDeleteUserEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessDeleteUserEventResponseMultiError(errors)
	}

	return nil
}

// ProcessDeleteUserEventResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessDeleteUserEventResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessDeleteUserEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessDeleteUserEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessDeleteUserEventResponseMultiError) AllErrors() []error { return m }

// ProcessDeleteUserEventResponseValidationError is the validation error
// returned by ProcessDeleteUserEventResponse.Validate if the designated
// constraints aren't met.
type ProcessDeleteUserEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessDeleteUserEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessDeleteUserEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessDeleteUserEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessDeleteUserEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessDeleteUserEventResponseValidationError) ErrorName() string {
	return "ProcessDeleteUserEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessDeleteUserEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessDeleteUserEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessDeleteUserEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessDeleteUserEventResponseValidationError{}
