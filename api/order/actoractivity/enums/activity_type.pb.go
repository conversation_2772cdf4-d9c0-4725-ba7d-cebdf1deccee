// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/order/actoractivity/enums/activity_type.proto

package enums

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// type of activity like NEFT,IMPS transaction or
type ActivityType int32

const (
	ActivityType_ACTIVITY_TYPE_UNSPECIFIED       ActivityType = 0
	ActivityType_IMPS_TRANSACTION_SUCCESS        ActivityType = 1
	ActivityType_IMPS_TRANSACTION_FAILED         ActivityType = 2
	ActivityType_NEFT_TRANSACTION_SUCCESS        ActivityType = 3
	ActivityType_NEFT_TRANSACTION_FAILED         ActivityType = 4
	ActivityType_RTGS_TRANSACTION_SUCCESS        ActivityType = 5
	ActivityType_RTGS_TRANSACTION_FAILED         ActivityType = 6
	ActivityType_UPI_TRANSACTION_SUCCESS         ActivityType = 7
	ActivityType_UPI_TRANSACTION_FAILED          ActivityType = 8
	ActivityType_INTRABANK_TRANSACTION_SUCCESS   ActivityType = 9
	ActivityType_INTRABANK_TRANSACTION_FAILED    ActivityType = 10
	ActivityType_ATM_TRANSACTION_SUCCESS         ActivityType = 11
	ActivityType_ATM_TRANSACTION_FAILED          ActivityType = 12
	ActivityType_SMART_DEPOSIT_CREATED           ActivityType = 13
	ActivityType_SMART_DEPOSIT_MATURED           ActivityType = 14
	ActivityType_SMART_DEPOSIT_PRECLOSED         ActivityType = 15
	ActivityType_SMART_DEPOSIT_AMT_ADDED         ActivityType = 16
	ActivityType_FIXED_DEPOSIT_CREATED           ActivityType = 17
	ActivityType_FIXED_DEPOSIT_MATURED           ActivityType = 18
	ActivityType_FIXED_DEPOSIT_PRECLOSED         ActivityType = 19
	ActivityType_IMPS_TRANSACTION_REVERSED       ActivityType = 20
	ActivityType_NEFT_TRANSACTION_REVERSED       ActivityType = 21
	ActivityType_RTGS_TRANSACTION_REVERSED       ActivityType = 22
	ActivityType_UPI_TRANSACTION_REVERSED        ActivityType = 23
	ActivityType_INTRABANK_TRANSACTION_REVERSED  ActivityType = 24
	ActivityType_ATM_TRANSACTION_REVERSED        ActivityType = 25
	ActivityType_DEBIT_CARD_TRANSACTION_SUCCESS  ActivityType = 26
	ActivityType_DEBIT_CARD_TRANSACTION_FAILED   ActivityType = 27
	ActivityType_DEBIT_CARD_TRANSACTION_REVERSED ActivityType = 28
	ActivityType_SMART_DEPOSIT_INTEREST_CREDIT   ActivityType = 29
	ActivityType_FIXED_DEPOSIT_INTEREST_CREDIT   ActivityType = 30
	ActivityType_NEFT_TRANSACTION_PENDING        ActivityType = 31
	ActivityType_RTGS_TRANSACTION_PENDING        ActivityType = 32
	ActivityType_UPI_TRANSACTION_PENDING         ActivityType = 33
	ActivityType_INTRABANK_TRANSACTION_PENDING   ActivityType = 34
	ActivityType_IMPS_TRANSACTION_PENDING        ActivityType = 35
	ActivityType_ENACH_TRANSACTION_FAILED        ActivityType = 36
	ActivityType_ENACH_TRANSACTION_SUCCESS       ActivityType = 37
	ActivityType_ENACH_TRANSACTION_PENDING       ActivityType = 38
	ActivityType_ENACH_TRANSACTION_REVERSED      ActivityType = 39
)

// Enum value maps for ActivityType.
var (
	ActivityType_name = map[int32]string{
		0:  "ACTIVITY_TYPE_UNSPECIFIED",
		1:  "IMPS_TRANSACTION_SUCCESS",
		2:  "IMPS_TRANSACTION_FAILED",
		3:  "NEFT_TRANSACTION_SUCCESS",
		4:  "NEFT_TRANSACTION_FAILED",
		5:  "RTGS_TRANSACTION_SUCCESS",
		6:  "RTGS_TRANSACTION_FAILED",
		7:  "UPI_TRANSACTION_SUCCESS",
		8:  "UPI_TRANSACTION_FAILED",
		9:  "INTRABANK_TRANSACTION_SUCCESS",
		10: "INTRABANK_TRANSACTION_FAILED",
		11: "ATM_TRANSACTION_SUCCESS",
		12: "ATM_TRANSACTION_FAILED",
		13: "SMART_DEPOSIT_CREATED",
		14: "SMART_DEPOSIT_MATURED",
		15: "SMART_DEPOSIT_PRECLOSED",
		16: "SMART_DEPOSIT_AMT_ADDED",
		17: "FIXED_DEPOSIT_CREATED",
		18: "FIXED_DEPOSIT_MATURED",
		19: "FIXED_DEPOSIT_PRECLOSED",
		20: "IMPS_TRANSACTION_REVERSED",
		21: "NEFT_TRANSACTION_REVERSED",
		22: "RTGS_TRANSACTION_REVERSED",
		23: "UPI_TRANSACTION_REVERSED",
		24: "INTRABANK_TRANSACTION_REVERSED",
		25: "ATM_TRANSACTION_REVERSED",
		26: "DEBIT_CARD_TRANSACTION_SUCCESS",
		27: "DEBIT_CARD_TRANSACTION_FAILED",
		28: "DEBIT_CARD_TRANSACTION_REVERSED",
		29: "SMART_DEPOSIT_INTEREST_CREDIT",
		30: "FIXED_DEPOSIT_INTEREST_CREDIT",
		31: "NEFT_TRANSACTION_PENDING",
		32: "RTGS_TRANSACTION_PENDING",
		33: "UPI_TRANSACTION_PENDING",
		34: "INTRABANK_TRANSACTION_PENDING",
		35: "IMPS_TRANSACTION_PENDING",
		36: "ENACH_TRANSACTION_FAILED",
		37: "ENACH_TRANSACTION_SUCCESS",
		38: "ENACH_TRANSACTION_PENDING",
		39: "ENACH_TRANSACTION_REVERSED",
	}
	ActivityType_value = map[string]int32{
		"ACTIVITY_TYPE_UNSPECIFIED":       0,
		"IMPS_TRANSACTION_SUCCESS":        1,
		"IMPS_TRANSACTION_FAILED":         2,
		"NEFT_TRANSACTION_SUCCESS":        3,
		"NEFT_TRANSACTION_FAILED":         4,
		"RTGS_TRANSACTION_SUCCESS":        5,
		"RTGS_TRANSACTION_FAILED":         6,
		"UPI_TRANSACTION_SUCCESS":         7,
		"UPI_TRANSACTION_FAILED":          8,
		"INTRABANK_TRANSACTION_SUCCESS":   9,
		"INTRABANK_TRANSACTION_FAILED":    10,
		"ATM_TRANSACTION_SUCCESS":         11,
		"ATM_TRANSACTION_FAILED":          12,
		"SMART_DEPOSIT_CREATED":           13,
		"SMART_DEPOSIT_MATURED":           14,
		"SMART_DEPOSIT_PRECLOSED":         15,
		"SMART_DEPOSIT_AMT_ADDED":         16,
		"FIXED_DEPOSIT_CREATED":           17,
		"FIXED_DEPOSIT_MATURED":           18,
		"FIXED_DEPOSIT_PRECLOSED":         19,
		"IMPS_TRANSACTION_REVERSED":       20,
		"NEFT_TRANSACTION_REVERSED":       21,
		"RTGS_TRANSACTION_REVERSED":       22,
		"UPI_TRANSACTION_REVERSED":        23,
		"INTRABANK_TRANSACTION_REVERSED":  24,
		"ATM_TRANSACTION_REVERSED":        25,
		"DEBIT_CARD_TRANSACTION_SUCCESS":  26,
		"DEBIT_CARD_TRANSACTION_FAILED":   27,
		"DEBIT_CARD_TRANSACTION_REVERSED": 28,
		"SMART_DEPOSIT_INTEREST_CREDIT":   29,
		"FIXED_DEPOSIT_INTEREST_CREDIT":   30,
		"NEFT_TRANSACTION_PENDING":        31,
		"RTGS_TRANSACTION_PENDING":        32,
		"UPI_TRANSACTION_PENDING":         33,
		"INTRABANK_TRANSACTION_PENDING":   34,
		"IMPS_TRANSACTION_PENDING":        35,
		"ENACH_TRANSACTION_FAILED":        36,
		"ENACH_TRANSACTION_SUCCESS":       37,
		"ENACH_TRANSACTION_PENDING":       38,
		"ENACH_TRANSACTION_REVERSED":      39,
	}
)

func (x ActivityType) Enum() *ActivityType {
	p := new(ActivityType)
	*p = x
	return p
}

func (x ActivityType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_actoractivity_enums_activity_type_proto_enumTypes[0].Descriptor()
}

func (ActivityType) Type() protoreflect.EnumType {
	return &file_api_order_actoractivity_enums_activity_type_proto_enumTypes[0]
}

func (x ActivityType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityType.Descriptor instead.
func (ActivityType) EnumDescriptor() ([]byte, []int) {
	return file_api_order_actoractivity_enums_activity_type_proto_rawDescGZIP(), []int{0}
}

var File_api_order_actoractivity_enums_activity_type_proto protoreflect.FileDescriptor

var file_api_order_actoractivity_enums_activity_type_proto_rawDesc = []byte{
	0x0a, 0x31, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x19, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2a, 0xdd,
	0x09, 0x0a, 0x0c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x1d, 0x0a, 0x19, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1c,
	0x0a, 0x18, 0x49, 0x4d, 0x50, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1b, 0x0a, 0x17,
	0x49, 0x4d, 0x50, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1c, 0x0a, 0x18, 0x4e, 0x45, 0x46,
	0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55,
	0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x4e, 0x45, 0x46, 0x54, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x54, 0x47, 0x53, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53,
	0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x54, 0x47, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x12,
	0x1b, 0x0a, 0x17, 0x55, 0x50, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x07, 0x12, 0x1a, 0x0a, 0x16,
	0x55, 0x50, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x08, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x54, 0x52,
	0x41, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x09, 0x12, 0x20, 0x0a, 0x1c, 0x49,
	0x4e, 0x54, 0x52, 0x41, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x1b, 0x0a,
	0x17, 0x41, 0x54, 0x4d, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x0b, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x54,
	0x4d, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f,
	0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x0d, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x45, 0x44, 0x10, 0x0e, 0x12, 0x1b, 0x0a, 0x17,
	0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x50, 0x52,
	0x45, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x4d, 0x41,
	0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x41, 0x4d, 0x54, 0x5f, 0x41,
	0x44, 0x44, 0x45, 0x44, 0x10, 0x10, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f,
	0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x11, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x45, 0x44, 0x10, 0x12, 0x12, 0x1b, 0x0a, 0x17,
	0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x50, 0x52,
	0x45, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x13, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4d, 0x50,
	0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x14, 0x12, 0x1d, 0x0a, 0x19, 0x4e, 0x45, 0x46, 0x54,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56,
	0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x15, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x54, 0x47, 0x53, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45,
	0x52, 0x53, 0x45, 0x44, 0x10, 0x16, 0x12, 0x1c, 0x0a, 0x18, 0x55, 0x50, 0x49, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53,
	0x45, 0x44, 0x10, 0x17, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x42, 0x41, 0x4e,
	0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x18, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x54, 0x4d, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45,
	0x52, 0x53, 0x45, 0x44, 0x10, 0x19, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x1a, 0x12, 0x21, 0x0a, 0x1d, 0x44, 0x45,
	0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x1b, 0x12, 0x23, 0x0a,
	0x1f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44,
	0x10, 0x1c, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x10, 0x1d, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44,
	0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f,
	0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x1e, 0x12, 0x1c, 0x0a, 0x18, 0x4e, 0x45, 0x46, 0x54,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x1f, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x54, 0x47, 0x53, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x20, 0x12, 0x1b, 0x0a, 0x17, 0x55, 0x50, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x21, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x22, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4d, 0x50, 0x53, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x23, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x24,
	0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x25, 0x12,
	0x1d, 0x0a, 0x19, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x26, 0x12, 0x1e,
	0x0a, 0x1a, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x27, 0x42, 0x6c,
	0x0a, 0x34, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_order_actoractivity_enums_activity_type_proto_rawDescOnce sync.Once
	file_api_order_actoractivity_enums_activity_type_proto_rawDescData = file_api_order_actoractivity_enums_activity_type_proto_rawDesc
)

func file_api_order_actoractivity_enums_activity_type_proto_rawDescGZIP() []byte {
	file_api_order_actoractivity_enums_activity_type_proto_rawDescOnce.Do(func() {
		file_api_order_actoractivity_enums_activity_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_order_actoractivity_enums_activity_type_proto_rawDescData)
	})
	return file_api_order_actoractivity_enums_activity_type_proto_rawDescData
}

var file_api_order_actoractivity_enums_activity_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_order_actoractivity_enums_activity_type_proto_goTypes = []interface{}{
	(ActivityType)(0), // 0: order.actoractivity.enums.ActivityType
}
var file_api_order_actoractivity_enums_activity_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_order_actoractivity_enums_activity_type_proto_init() }
func file_api_order_actoractivity_enums_activity_type_proto_init() {
	if File_api_order_actoractivity_enums_activity_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_order_actoractivity_enums_activity_type_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_order_actoractivity_enums_activity_type_proto_goTypes,
		DependencyIndexes: file_api_order_actoractivity_enums_activity_type_proto_depIdxs,
		EnumInfos:         file_api_order_actoractivity_enums_activity_type_proto_enumTypes,
	}.Build()
	File_api_order_actoractivity_enums_activity_type_proto = out.File
	file_api_order_actoractivity_enums_activity_type_proto_rawDesc = nil
	file_api_order_actoractivity_enums_activity_type_proto_goTypes = nil
	file_api_order_actoractivity_enums_activity_type_proto_depIdxs = nil
}
