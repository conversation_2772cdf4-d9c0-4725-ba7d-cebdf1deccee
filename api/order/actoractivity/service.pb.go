// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/order/actoractivity/service.proto

package actoractivity

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	accounts "github.com/epifi/gamma/api/accounts"
	activity "github.com/epifi/gamma/api/order/actoractivity/activity"
	enums "github.com/epifi/gamma/api/order/actoractivity/enums"
	payment "github.com/epifi/gamma/api/order/payment"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// entry point for the activity in the actor activity service
type ActivityEntryPoint int32

const (
	// unspecified
	ActivityEntryPoint_ACTIVITY_ENTRY_POINT_UNSPECIFIED ActivityEntryPoint = 0
	// activity is fetched from order service
	ActivityEntryPoint_ORDER ActivityEntryPoint = 1
	// activity is fetched from aa service
	ActivityEntryPoint_AA ActivityEntryPoint = 2
	// activity is fetched from cc service
	ActivityEntryPoint_CREDIT_CARD ActivityEntryPoint = 3
)

// Enum value maps for ActivityEntryPoint.
var (
	ActivityEntryPoint_name = map[int32]string{
		0: "ACTIVITY_ENTRY_POINT_UNSPECIFIED",
		1: "ORDER",
		2: "AA",
		3: "CREDIT_CARD",
	}
	ActivityEntryPoint_value = map[string]int32{
		"ACTIVITY_ENTRY_POINT_UNSPECIFIED": 0,
		"ORDER":                            1,
		"AA":                               2,
		"CREDIT_CARD":                      3,
	}
)

func (x ActivityEntryPoint) Enum() *ActivityEntryPoint {
	p := new(ActivityEntryPoint)
	*p = x
	return p
}

func (x ActivityEntryPoint) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ActivityEntryPoint) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_actoractivity_service_proto_enumTypes[0].Descriptor()
}

func (ActivityEntryPoint) Type() protoreflect.EnumType {
	return &file_api_order_actoractivity_service_proto_enumTypes[0]
}

func (x ActivityEntryPoint) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ActivityEntryPoint.Descriptor instead.
func (ActivityEntryPoint) EnumDescriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{0}
}

// based on entry point we decide which backend order RPC to be called, due to evolving different needs of internal teams and UI
// we have decided to keep different RPC one for internal services and one for UI / app
type GetActivitiesRequest_EntryPointType int32

const (
	GetActivitiesRequest_ENTRY_POINT_TYPE_UNSPECIFIED      GetActivitiesRequest_EntryPointType = 0
	GetActivitiesRequest_ENTRY_POINT_TYPE_UI               GetActivitiesRequest_EntryPointType = 1
	GetActivitiesRequest_ENTRY_POINT_TYPE_INTERNAL_SERVICE GetActivitiesRequest_EntryPointType = 2
)

// Enum value maps for GetActivitiesRequest_EntryPointType.
var (
	GetActivitiesRequest_EntryPointType_name = map[int32]string{
		0: "ENTRY_POINT_TYPE_UNSPECIFIED",
		1: "ENTRY_POINT_TYPE_UI",
		2: "ENTRY_POINT_TYPE_INTERNAL_SERVICE",
	}
	GetActivitiesRequest_EntryPointType_value = map[string]int32{
		"ENTRY_POINT_TYPE_UNSPECIFIED":      0,
		"ENTRY_POINT_TYPE_UI":               1,
		"ENTRY_POINT_TYPE_INTERNAL_SERVICE": 2,
	}
)

func (x GetActivitiesRequest_EntryPointType) Enum() *GetActivitiesRequest_EntryPointType {
	p := new(GetActivitiesRequest_EntryPointType)
	*p = x
	return p
}

func (x GetActivitiesRequest_EntryPointType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetActivitiesRequest_EntryPointType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_actoractivity_service_proto_enumTypes[1].Descriptor()
}

func (GetActivitiesRequest_EntryPointType) Type() protoreflect.EnumType {
	return &file_api_order_actoractivity_service_proto_enumTypes[1]
}

func (x GetActivitiesRequest_EntryPointType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetActivitiesRequest_EntryPointType.Descriptor instead.
func (GetActivitiesRequest_EntryPointType) EnumDescriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{0, 0}
}

type GetActivitiesResponse_Status int32

const (
	// rpc successful
	GetActivitiesResponse_OK GetActivitiesResponse_Status = 0
	// no activity found for the actor
	GetActivitiesResponse_RECORD_NOT_FOUND GetActivitiesResponse_Status = 5
	// Internal error while processing the request
	GetActivitiesResponse_INTERNAL GetActivitiesResponse_Status = 13
)

// Enum value maps for GetActivitiesResponse_Status.
var (
	GetActivitiesResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetActivitiesResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetActivitiesResponse_Status) Enum() *GetActivitiesResponse_Status {
	p := new(GetActivitiesResponse_Status)
	*p = x
	return p
}

func (x GetActivitiesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetActivitiesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_actoractivity_service_proto_enumTypes[2].Descriptor()
}

func (GetActivitiesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_order_actoractivity_service_proto_enumTypes[2]
}

func (x GetActivitiesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetActivitiesResponse_Status.Descriptor instead.
func (GetActivitiesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{1, 0}
}

type GetActivitiesResponse_Activity_AmountBadge int32

const (
	// unspecified
	GetActivitiesResponse_Activity_AMOUNT_BADGE_UNSPECIFIED GetActivitiesResponse_Activity_AmountBadge = 0
	GetActivitiesResponse_Activity_DEBIT                    GetActivitiesResponse_Activity_AmountBadge = 1
	GetActivitiesResponse_Activity_CREDIT                   GetActivitiesResponse_Activity_AmountBadge = 2
	GetActivitiesResponse_Activity_SAVINGS                  GetActivitiesResponse_Activity_AmountBadge = 3
	GetActivitiesResponse_Activity_FAILED                   GetActivitiesResponse_Activity_AmountBadge = 4
	GetActivitiesResponse_Activity_REVERSED                 GetActivitiesResponse_Activity_AmountBadge = 5
	GetActivitiesResponse_Activity_IN_PAYMENT               GetActivitiesResponse_Activity_AmountBadge = 6
)

// Enum value maps for GetActivitiesResponse_Activity_AmountBadge.
var (
	GetActivitiesResponse_Activity_AmountBadge_name = map[int32]string{
		0: "AMOUNT_BADGE_UNSPECIFIED",
		1: "DEBIT",
		2: "CREDIT",
		3: "SAVINGS",
		4: "FAILED",
		5: "REVERSED",
		6: "IN_PAYMENT",
	}
	GetActivitiesResponse_Activity_AmountBadge_value = map[string]int32{
		"AMOUNT_BADGE_UNSPECIFIED": 0,
		"DEBIT":                    1,
		"CREDIT":                   2,
		"SAVINGS":                  3,
		"FAILED":                   4,
		"REVERSED":                 5,
		"IN_PAYMENT":               6,
	}
)

func (x GetActivitiesResponse_Activity_AmountBadge) Enum() *GetActivitiesResponse_Activity_AmountBadge {
	p := new(GetActivitiesResponse_Activity_AmountBadge)
	*p = x
	return p
}

func (x GetActivitiesResponse_Activity_AmountBadge) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetActivitiesResponse_Activity_AmountBadge) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_actoractivity_service_proto_enumTypes[3].Descriptor()
}

func (GetActivitiesResponse_Activity_AmountBadge) Type() protoreflect.EnumType {
	return &file_api_order_actoractivity_service_proto_enumTypes[3]
}

func (x GetActivitiesResponse_Activity_AmountBadge) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetActivitiesResponse_Activity_AmountBadge.Descriptor instead.
func (GetActivitiesResponse_Activity_AmountBadge) EnumDescriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{1, 0, 0}
}

type GetActivitiesResponse_Activity_Type int32

const (
	GetActivitiesResponse_Activity_ACTIVITY_TYPE_UNSPECIFIED       GetActivitiesResponse_Activity_Type = 0
	GetActivitiesResponse_Activity_IMPS_TRANSACTION_SUCCESS        GetActivitiesResponse_Activity_Type = 1
	GetActivitiesResponse_Activity_IMPS_TRANSACTION_FAILED         GetActivitiesResponse_Activity_Type = 2
	GetActivitiesResponse_Activity_NEFT_TRANSACTION_SUCCESS        GetActivitiesResponse_Activity_Type = 3
	GetActivitiesResponse_Activity_NEFT_TRANSACTION_FAILED         GetActivitiesResponse_Activity_Type = 4
	GetActivitiesResponse_Activity_RTGS_TRANSACTION_SUCCESS        GetActivitiesResponse_Activity_Type = 5
	GetActivitiesResponse_Activity_RTGS_TRANSACTION_FAILED         GetActivitiesResponse_Activity_Type = 6
	GetActivitiesResponse_Activity_UPI_TRANSACTION_SUCCESS         GetActivitiesResponse_Activity_Type = 7
	GetActivitiesResponse_Activity_UPI_TRANSACTION_FAILED          GetActivitiesResponse_Activity_Type = 8
	GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_SUCCESS   GetActivitiesResponse_Activity_Type = 9
	GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_FAILED    GetActivitiesResponse_Activity_Type = 10
	GetActivitiesResponse_Activity_ATM_TRANSACTION_SUCCESS         GetActivitiesResponse_Activity_Type = 11
	GetActivitiesResponse_Activity_ATM_TRANSACTION_FAILED          GetActivitiesResponse_Activity_Type = 12
	GetActivitiesResponse_Activity_SMART_DEPOSIT_CREATED           GetActivitiesResponse_Activity_Type = 13
	GetActivitiesResponse_Activity_SMART_DEPOSIT_MATURED           GetActivitiesResponse_Activity_Type = 14
	GetActivitiesResponse_Activity_SMART_DEPOSIT_PRECLOSED         GetActivitiesResponse_Activity_Type = 15
	GetActivitiesResponse_Activity_SMART_DEPOSIT_AMT_ADDED         GetActivitiesResponse_Activity_Type = 16
	GetActivitiesResponse_Activity_FIXED_DEPOSIT_CREATED           GetActivitiesResponse_Activity_Type = 17
	GetActivitiesResponse_Activity_FIXED_DEPOSIT_MATURED           GetActivitiesResponse_Activity_Type = 18
	GetActivitiesResponse_Activity_FIXED_DEPOSIT_PRECLOSED         GetActivitiesResponse_Activity_Type = 19
	GetActivitiesResponse_Activity_IMPS_TRANSACTION_REVERSED       GetActivitiesResponse_Activity_Type = 20
	GetActivitiesResponse_Activity_NEFT_TRANSACTION_REVERSED       GetActivitiesResponse_Activity_Type = 21
	GetActivitiesResponse_Activity_RTGS_TRANSACTION_REVERSED       GetActivitiesResponse_Activity_Type = 22
	GetActivitiesResponse_Activity_UPI_TRANSACTION_REVERSED        GetActivitiesResponse_Activity_Type = 23
	GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_REVERSED  GetActivitiesResponse_Activity_Type = 24
	GetActivitiesResponse_Activity_ATM_TRANSACTION_REVERSED        GetActivitiesResponse_Activity_Type = 25
	GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_SUCCESS  GetActivitiesResponse_Activity_Type = 26
	GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_FAILED   GetActivitiesResponse_Activity_Type = 27
	GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_REVERSED GetActivitiesResponse_Activity_Type = 28
	GetActivitiesResponse_Activity_SMART_DEPOSIT_INTEREST_CREDIT   GetActivitiesResponse_Activity_Type = 29
	GetActivitiesResponse_Activity_FIXED_DEPOSIT_INTEREST_CREDIT   GetActivitiesResponse_Activity_Type = 30
	GetActivitiesResponse_Activity_NEFT_TRANSACTION_PENDING        GetActivitiesResponse_Activity_Type = 31
	GetActivitiesResponse_Activity_RTGS_TRANSACTION_PENDING        GetActivitiesResponse_Activity_Type = 32
	GetActivitiesResponse_Activity_UPI_TRANSACTION_PENDING         GetActivitiesResponse_Activity_Type = 33
	GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_PENDING   GetActivitiesResponse_Activity_Type = 34
	GetActivitiesResponse_Activity_IMPS_TRANSACTION_PENDING        GetActivitiesResponse_Activity_Type = 35
	GetActivitiesResponse_Activity_ENACH_TRANSACTION_FAILED        GetActivitiesResponse_Activity_Type = 36
	GetActivitiesResponse_Activity_ENACH_TRANSACTION_SUCCESS       GetActivitiesResponse_Activity_Type = 37
	GetActivitiesResponse_Activity_ENACH_TRANSACTION_PENDING       GetActivitiesResponse_Activity_Type = 38
	GetActivitiesResponse_Activity_ENACH_TRANSACTION_REVERSED      GetActivitiesResponse_Activity_Type = 39
)

// Enum value maps for GetActivitiesResponse_Activity_Type.
var (
	GetActivitiesResponse_Activity_Type_name = map[int32]string{
		0:  "ACTIVITY_TYPE_UNSPECIFIED",
		1:  "IMPS_TRANSACTION_SUCCESS",
		2:  "IMPS_TRANSACTION_FAILED",
		3:  "NEFT_TRANSACTION_SUCCESS",
		4:  "NEFT_TRANSACTION_FAILED",
		5:  "RTGS_TRANSACTION_SUCCESS",
		6:  "RTGS_TRANSACTION_FAILED",
		7:  "UPI_TRANSACTION_SUCCESS",
		8:  "UPI_TRANSACTION_FAILED",
		9:  "INTRABANK_TRANSACTION_SUCCESS",
		10: "INTRABANK_TRANSACTION_FAILED",
		11: "ATM_TRANSACTION_SUCCESS",
		12: "ATM_TRANSACTION_FAILED",
		13: "SMART_DEPOSIT_CREATED",
		14: "SMART_DEPOSIT_MATURED",
		15: "SMART_DEPOSIT_PRECLOSED",
		16: "SMART_DEPOSIT_AMT_ADDED",
		17: "FIXED_DEPOSIT_CREATED",
		18: "FIXED_DEPOSIT_MATURED",
		19: "FIXED_DEPOSIT_PRECLOSED",
		20: "IMPS_TRANSACTION_REVERSED",
		21: "NEFT_TRANSACTION_REVERSED",
		22: "RTGS_TRANSACTION_REVERSED",
		23: "UPI_TRANSACTION_REVERSED",
		24: "INTRABANK_TRANSACTION_REVERSED",
		25: "ATM_TRANSACTION_REVERSED",
		26: "DEBIT_CARD_TRANSACTION_SUCCESS",
		27: "DEBIT_CARD_TRANSACTION_FAILED",
		28: "DEBIT_CARD_TRANSACTION_REVERSED",
		29: "SMART_DEPOSIT_INTEREST_CREDIT",
		30: "FIXED_DEPOSIT_INTEREST_CREDIT",
		31: "NEFT_TRANSACTION_PENDING",
		32: "RTGS_TRANSACTION_PENDING",
		33: "UPI_TRANSACTION_PENDING",
		34: "INTRABANK_TRANSACTION_PENDING",
		35: "IMPS_TRANSACTION_PENDING",
		36: "ENACH_TRANSACTION_FAILED",
		37: "ENACH_TRANSACTION_SUCCESS",
		38: "ENACH_TRANSACTION_PENDING",
		39: "ENACH_TRANSACTION_REVERSED",
	}
	GetActivitiesResponse_Activity_Type_value = map[string]int32{
		"ACTIVITY_TYPE_UNSPECIFIED":       0,
		"IMPS_TRANSACTION_SUCCESS":        1,
		"IMPS_TRANSACTION_FAILED":         2,
		"NEFT_TRANSACTION_SUCCESS":        3,
		"NEFT_TRANSACTION_FAILED":         4,
		"RTGS_TRANSACTION_SUCCESS":        5,
		"RTGS_TRANSACTION_FAILED":         6,
		"UPI_TRANSACTION_SUCCESS":         7,
		"UPI_TRANSACTION_FAILED":          8,
		"INTRABANK_TRANSACTION_SUCCESS":   9,
		"INTRABANK_TRANSACTION_FAILED":    10,
		"ATM_TRANSACTION_SUCCESS":         11,
		"ATM_TRANSACTION_FAILED":          12,
		"SMART_DEPOSIT_CREATED":           13,
		"SMART_DEPOSIT_MATURED":           14,
		"SMART_DEPOSIT_PRECLOSED":         15,
		"SMART_DEPOSIT_AMT_ADDED":         16,
		"FIXED_DEPOSIT_CREATED":           17,
		"FIXED_DEPOSIT_MATURED":           18,
		"FIXED_DEPOSIT_PRECLOSED":         19,
		"IMPS_TRANSACTION_REVERSED":       20,
		"NEFT_TRANSACTION_REVERSED":       21,
		"RTGS_TRANSACTION_REVERSED":       22,
		"UPI_TRANSACTION_REVERSED":        23,
		"INTRABANK_TRANSACTION_REVERSED":  24,
		"ATM_TRANSACTION_REVERSED":        25,
		"DEBIT_CARD_TRANSACTION_SUCCESS":  26,
		"DEBIT_CARD_TRANSACTION_FAILED":   27,
		"DEBIT_CARD_TRANSACTION_REVERSED": 28,
		"SMART_DEPOSIT_INTEREST_CREDIT":   29,
		"FIXED_DEPOSIT_INTEREST_CREDIT":   30,
		"NEFT_TRANSACTION_PENDING":        31,
		"RTGS_TRANSACTION_PENDING":        32,
		"UPI_TRANSACTION_PENDING":         33,
		"INTRABANK_TRANSACTION_PENDING":   34,
		"IMPS_TRANSACTION_PENDING":        35,
		"ENACH_TRANSACTION_FAILED":        36,
		"ENACH_TRANSACTION_SUCCESS":       37,
		"ENACH_TRANSACTION_PENDING":       38,
		"ENACH_TRANSACTION_REVERSED":      39,
	}
)

func (x GetActivitiesResponse_Activity_Type) Enum() *GetActivitiesResponse_Activity_Type {
	p := new(GetActivitiesResponse_Activity_Type)
	*p = x
	return p
}

func (x GetActivitiesResponse_Activity_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetActivitiesResponse_Activity_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_actoractivity_service_proto_enumTypes[4].Descriptor()
}

func (GetActivitiesResponse_Activity_Type) Type() protoreflect.EnumType {
	return &file_api_order_actoractivity_service_proto_enumTypes[4]
}

func (x GetActivitiesResponse_Activity_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetActivitiesResponse_Activity_Type.Descriptor instead.
func (GetActivitiesResponse_Activity_Type) EnumDescriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{1, 0, 1}
}

type GetFinancialActivitiesResponse_Status int32

const (
	// rpc successful
	GetFinancialActivitiesResponse_OK GetFinancialActivitiesResponse_Status = 0
	// no activity found for the actor
	GetFinancialActivitiesResponse_RECORD_NOT_FOUND GetFinancialActivitiesResponse_Status = 5
	// Internal error while processing the request
	GetFinancialActivitiesResponse_INTERNAL GetFinancialActivitiesResponse_Status = 13
)

// Enum value maps for GetFinancialActivitiesResponse_Status.
var (
	GetFinancialActivitiesResponse_Status_name = map[int32]string{
		0:  "OK",
		5:  "RECORD_NOT_FOUND",
		13: "INTERNAL",
	}
	GetFinancialActivitiesResponse_Status_value = map[string]int32{
		"OK":               0,
		"RECORD_NOT_FOUND": 5,
		"INTERNAL":         13,
	}
)

func (x GetFinancialActivitiesResponse_Status) Enum() *GetFinancialActivitiesResponse_Status {
	p := new(GetFinancialActivitiesResponse_Status)
	*p = x
	return p
}

func (x GetFinancialActivitiesResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetFinancialActivitiesResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_actoractivity_service_proto_enumTypes[5].Descriptor()
}

func (GetFinancialActivitiesResponse_Status) Type() protoreflect.EnumType {
	return &file_api_order_actoractivity_service_proto_enumTypes[5]
}

func (x GetFinancialActivitiesResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetFinancialActivitiesResponse_Status.Descriptor instead.
func (GetFinancialActivitiesResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{3, 0}
}

type GetActivitiesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// current actor id for which the activities needs to be fetched
	CurrentActorId string `protobuf:"bytes,1,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	// list of accounts to filter the results
	// only activities associated with the account id in these filters will be returned
	AccountFilter []*GetActivitiesRequest_AccountFilter `protobuf:"bytes,2,rep,name=account_filter,json=accountFilter,proto3" json:"account_filter,omitempty"`
	// timestamp starting from which records are scanned.
	// NOTE- the records are returned INCLUSIVE of the timestamp.
	ActivitiesStartTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=activities_start_timestamp,json=activitiesStartTimestamp,proto3" json:"activities_start_timestamp,omitempty"`
	// Page size determines the upper bound on the number of records
	// returned in a particular response.
	// Page size must be in the range [10, 40]
	// minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// An offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp.
	// e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
	// set to 5 then first 5 records from the qualifying set are removed.
	// deprecated. use order offset and aaTxnOffset
	//
	// Deprecated: Marked as deprecated in api/order/actoractivity/service.proto.
	ActivityOffset int32 `protobuf:"varint,5,opt,name=activity_offset,json=activityOffset,proto3" json:"activity_offset,omitempty"`
	// The sequence of the result returned be based on the boolean flag descending.
	// i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
	// If marked false ASCENDING ordered results are returned from the given start timestamp.
	Descending bool `protobuf:"varint,6,opt,name=descending,proto3" json:"descending,omitempty"`
	// list of PiIds to filter the result.
	// Activities associated with these piIds will be returned
	// deprecated in favour of use of payment_filter options
	//
	// Deprecated: Marked as deprecated in api/order/actoractivity/service.proto.
	PiFilter []string `protobuf:"bytes,7,rep,name=pi_filter,json=piFilter,proto3" json:"pi_filter,omitempty"`
	// An offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp.
	// e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
	// set to 5 then first 5 records from the qualifying set are removed.
	OrderOffset int32 `protobuf:"varint,8,opt,name=order_offset,json=orderOffset,proto3" json:"order_offset,omitempty"`
	// An offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp.
	// e.g. we can have 10 aa txns starting with the timestamp start_timestamp. If offset is
	// set to 5 then first 5 records from the qualifying set are removed.
	AaTxnOffset int32 `protobuf:"varint,9,opt,name=aa_txn_offset,json=aaTxnOffset,proto3" json:"aa_txn_offset,omitempty"`
	// timestamp till which records are scanned.
	// NOTE- the records are returned INCLUSIVE of the timestamp.
	// If kept empty, by default all the timestamps related to the actor are returned
	ActivitiesEndTimestamp *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=activities_end_timestamp,json=activitiesEndTimestamp,proto3" json:"activities_end_timestamp,omitempty"`
	// Optional: filter based on payment specific fields such as protocol, transaction type, amount, etc.
	PaymentFilter *GetActivitiesRequest_PaymentFilter `protobuf:"bytes,11,opt,name=payment_filter,json=paymentFilter,proto3" json:"payment_filter,omitempty"`
	// page landing timestamp is the time when user opens the all transactions page.
	// since this activity is called in paginated fashion to render all transactions, this timestamp is used to fetch the data in their updated state till this time using time travel query. This is because order status can change while user is scrolling the all transactions page which can break the pagination logic.
	// If user want to get latest he needs to exit and come back to this page
	//
	//	NOTE: this timestamp is only used when the rpc is called to render all transactions page
	PageLandingTimestamp *timestamppb.Timestamp              `protobuf:"bytes,12,opt,name=page_landing_timestamp,json=pageLandingTimestamp,proto3" json:"page_landing_timestamp,omitempty"`
	EntryPointType       GetActivitiesRequest_EntryPointType `protobuf:"varint,13,opt,name=entry_point_type,json=entryPointType,proto3,enum=actoractivity.GetActivitiesRequest_EntryPointType" json:"entry_point_type,omitempty"`
	// flag to determine if activities for soft-deleted users should be fetched or not
	// This is helpful when the activities are between users where one of them is soft-deleted (this can happen when the account is closed etc).
	// For e.g., using this for all-txns page will help when the non-deleted user is browsing all the txns. Otherwise, it would lead to missing txns or page breaking.
	FetchSoftDeletedUsers bool `protobuf:"varint,14,opt,name=fetch_soft_deleted_users,json=fetchSoftDeletedUsers,proto3" json:"fetch_soft_deleted_users,omitempty"`
}

func (x *GetActivitiesRequest) Reset() {
	*x = GetActivitiesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivitiesRequest) ProtoMessage() {}

func (x *GetActivitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivitiesRequest.ProtoReflect.Descriptor instead.
func (*GetActivitiesRequest) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetActivitiesRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *GetActivitiesRequest) GetAccountFilter() []*GetActivitiesRequest_AccountFilter {
	if x != nil {
		return x.AccountFilter
	}
	return nil
}

func (x *GetActivitiesRequest) GetActivitiesStartTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivitiesStartTimestamp
	}
	return nil
}

func (x *GetActivitiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Deprecated: Marked as deprecated in api/order/actoractivity/service.proto.
func (x *GetActivitiesRequest) GetActivityOffset() int32 {
	if x != nil {
		return x.ActivityOffset
	}
	return 0
}

func (x *GetActivitiesRequest) GetDescending() bool {
	if x != nil {
		return x.Descending
	}
	return false
}

// Deprecated: Marked as deprecated in api/order/actoractivity/service.proto.
func (x *GetActivitiesRequest) GetPiFilter() []string {
	if x != nil {
		return x.PiFilter
	}
	return nil
}

func (x *GetActivitiesRequest) GetOrderOffset() int32 {
	if x != nil {
		return x.OrderOffset
	}
	return 0
}

func (x *GetActivitiesRequest) GetAaTxnOffset() int32 {
	if x != nil {
		return x.AaTxnOffset
	}
	return 0
}

func (x *GetActivitiesRequest) GetActivitiesEndTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivitiesEndTimestamp
	}
	return nil
}

func (x *GetActivitiesRequest) GetPaymentFilter() *GetActivitiesRequest_PaymentFilter {
	if x != nil {
		return x.PaymentFilter
	}
	return nil
}

func (x *GetActivitiesRequest) GetPageLandingTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.PageLandingTimestamp
	}
	return nil
}

func (x *GetActivitiesRequest) GetEntryPointType() GetActivitiesRequest_EntryPointType {
	if x != nil {
		return x.EntryPointType
	}
	return GetActivitiesRequest_ENTRY_POINT_TYPE_UNSPECIFIED
}

func (x *GetActivitiesRequest) GetFetchSoftDeletedUsers() bool {
	if x != nil {
		return x.FetchSoftDeletedUsers
	}
	return false
}

type GetActivitiesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status for the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of activities for the actor
	Activities []*GetActivitiesResponse_Activity `protobuf:"bytes,2,rep,name=activities,proto3" json:"activities,omitempty"`
}

func (x *GetActivitiesResponse) Reset() {
	*x = GetActivitiesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivitiesResponse) ProtoMessage() {}

func (x *GetActivitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivitiesResponse.ProtoReflect.Descriptor instead.
func (*GetActivitiesResponse) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetActivitiesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActivitiesResponse) GetActivities() []*GetActivitiesResponse_Activity {
	if x != nil {
		return x.Activities
	}
	return nil
}

type GetFinancialActivitiesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// current actor id for which the activities needs to be fetched
	CurrentActorId string `protobuf:"bytes,1,opt,name=current_actor_id,json=currentActorId,proto3" json:"current_actor_id,omitempty"`
	// timestamp starting from which records are scanned.
	// NOTE- the records are returned INCLUSIVE of the timestamp.
	ActivitiesStartTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=activities_start_timestamp,json=activitiesStartTimestamp,proto3" json:"activities_start_timestamp,omitempty"`
	// timestamp till which records are scanned.
	// NOTE- the records are returned INCLUSIVE of the timestamp.
	// If kept empty, by default all the timestamps related to the actor are returned
	ActivitiesEndTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=activities_end_timestamp,json=activitiesEndTimestamp,proto3" json:"activities_end_timestamp,omitempty"`
	// Page size determines the upper bound on the number of records
	// returned in a particular response.
	// Page size must be in the range [10, 40]
	// minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The sequence of the result returned be based on the boolean flag descending.
	// i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
	// If marked false ASCENDING ordered results are returned from the given start timestamp.
	Descending bool `protobuf:"varint,5,opt,name=descending,proto3" json:"descending,omitempty"`
	// activity source specifies the source of financial activities that we want
	// we can specify whether we want fi transactions activities or connected account transactions activities
	// If kept empty, by default all the financial activities related to the actor are returned
	// If TransactionSource is ACTIVITY_SOURCE_UNSPECIFIED, all the financial activities related to the actor are returned.
	ActivitySource enums.ActivitySource `protobuf:"varint,6,opt,name=activity_source,json=activitySource,proto3,enum=order.actoractivity.enums.ActivitySource" json:"activity_source,omitempty"`
	// An offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp.
	// It contains offset for different type of txns such as fi and connected account separately
	ActivityOffset *GetFinancialActivitiesRequest_ActivityOffSet `protobuf:"bytes,7,opt,name=activity_offset,json=activityOffset,proto3" json:"activity_offset,omitempty"`
	// Optional: filter based on payment specific fields such as protocol, transaction type, amount, etc.
	PaymentFilter *GetFinancialActivitiesRequest_PaymentFilter `protobuf:"bytes,8,opt,name=payment_filter,json=paymentFilter,proto3" json:"payment_filter,omitempty"`
}

func (x *GetFinancialActivitiesRequest) Reset() {
	*x = GetFinancialActivitiesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFinancialActivitiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFinancialActivitiesRequest) ProtoMessage() {}

func (x *GetFinancialActivitiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFinancialActivitiesRequest.ProtoReflect.Descriptor instead.
func (*GetFinancialActivitiesRequest) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetFinancialActivitiesRequest) GetCurrentActorId() string {
	if x != nil {
		return x.CurrentActorId
	}
	return ""
}

func (x *GetFinancialActivitiesRequest) GetActivitiesStartTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivitiesStartTimestamp
	}
	return nil
}

func (x *GetFinancialActivitiesRequest) GetActivitiesEndTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivitiesEndTimestamp
	}
	return nil
}

func (x *GetFinancialActivitiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetFinancialActivitiesRequest) GetDescending() bool {
	if x != nil {
		return x.Descending
	}
	return false
}

func (x *GetFinancialActivitiesRequest) GetActivitySource() enums.ActivitySource {
	if x != nil {
		return x.ActivitySource
	}
	return enums.ActivitySource(0)
}

func (x *GetFinancialActivitiesRequest) GetActivityOffset() *GetFinancialActivitiesRequest_ActivityOffSet {
	if x != nil {
		return x.ActivityOffset
	}
	return nil
}

func (x *GetFinancialActivitiesRequest) GetPaymentFilter() *GetFinancialActivitiesRequest_PaymentFilter {
	if x != nil {
		return x.PaymentFilter
	}
	return nil
}

type GetFinancialActivitiesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc status for the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of activities for the actor
	Activities []*activity.Activity `protobuf:"bytes,2,rep,name=activities,proto3" json:"activities,omitempty"`
}

func (x *GetFinancialActivitiesResponse) Reset() {
	*x = GetFinancialActivitiesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFinancialActivitiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFinancialActivitiesResponse) ProtoMessage() {}

func (x *GetFinancialActivitiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFinancialActivitiesResponse.ProtoReflect.Descriptor instead.
func (*GetFinancialActivitiesResponse) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetFinancialActivitiesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetFinancialActivitiesResponse) GetActivities() []*activity.Activity {
	if x != nil {
		return x.Activities
	}
	return nil
}

type GetActivitiesRequest_AccountFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account Id to filter the results
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// type of the account for the account id
	AccountType accounts.Type `protobuf:"varint,2,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
}

func (x *GetActivitiesRequest_AccountFilter) Reset() {
	*x = GetActivitiesRequest_AccountFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivitiesRequest_AccountFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivitiesRequest_AccountFilter) ProtoMessage() {}

func (x *GetActivitiesRequest_AccountFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivitiesRequest_AccountFilter.ProtoReflect.Descriptor instead.
func (*GetActivitiesRequest_AccountFilter) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetActivitiesRequest_AccountFilter) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetActivitiesRequest_AccountFilter) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

type GetActivitiesRequest_PaymentFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// optional: order payment_protocol filter. orders with payment_protocol in this list are considered.
	// If kept empty, by default all the orders related to the actor are returned
	PaymentProtocol []payment.PaymentProtocol `protobuf:"varint,1,rep,packed,name=payment_protocol,json=paymentProtocol,proto3,enum=order.payment.PaymentProtocol" json:"payment_protocol,omitempty"`
	// Optional: The minimum amount or the lower limit of the amount to be searched on.
	FromAmount *money.Money `protobuf:"bytes,2,opt,name=from_amount,json=fromAmount,proto3" json:"from_amount,omitempty"`
	// Optional: The maximum amount or the upper limit of the amount to be searched on.
	ToAmount *money.Money `protobuf:"bytes,3,opt,name=to_amount,json=toAmount,proto3" json:"to_amount,omitempty"`
	// optional: transaction type wrt the given actor.
	// The caller can use this to filter out the results corresponding to a particular transactionType.
	// If kept empty, by default all the orders (credit or debit) related to the actor are returned
	// If TransactionType is TRANSACTION_TYPE_UNSPECIFIED, all the orders related to the actor are returned.
	TransactionType payment.AccountingEntryType `protobuf:"varint,5,opt,name=transaction_type,json=transactionType,proto3,enum=order.payment.AccountingEntryType" json:"transaction_type,omitempty"`
	// list of PiIds to filter the result.
	// Activities associated with these piIds will be returned
	PiFilter []string `protobuf:"bytes,6,rep,name=pi_filter,json=piFilter,proto3" json:"pi_filter,omitempty"`
}

func (x *GetActivitiesRequest_PaymentFilter) Reset() {
	*x = GetActivitiesRequest_PaymentFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivitiesRequest_PaymentFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivitiesRequest_PaymentFilter) ProtoMessage() {}

func (x *GetActivitiesRequest_PaymentFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivitiesRequest_PaymentFilter.ProtoReflect.Descriptor instead.
func (*GetActivitiesRequest_PaymentFilter) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{0, 1}
}

func (x *GetActivitiesRequest_PaymentFilter) GetPaymentProtocol() []payment.PaymentProtocol {
	if x != nil {
		return x.PaymentProtocol
	}
	return nil
}

func (x *GetActivitiesRequest_PaymentFilter) GetFromAmount() *money.Money {
	if x != nil {
		return x.FromAmount
	}
	return nil
}

func (x *GetActivitiesRequest_PaymentFilter) GetToAmount() *money.Money {
	if x != nil {
		return x.ToAmount
	}
	return nil
}

func (x *GetActivitiesRequest_PaymentFilter) GetTransactionType() payment.AccountingEntryType {
	if x != nil {
		return x.TransactionType
	}
	return payment.AccountingEntryType(0)
}

func (x *GetActivitiesRequest_PaymentFilter) GetPiFilter() []string {
	if x != nil {
		return x.PiFilter
	}
	return nil
}

type GetActivitiesResponse_Activity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// icon image url
	IconUrl string `protobuf:"bytes,1,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// bold desc / title for the activity
	Title string `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// short desc for the activity
	ShortDesc string `protobuf:"bytes,3,opt,name=short_desc,json=shortDesc,proto3" json:"short_desc,omitempty"`
	// short desc icon url
	ShortDescIconUrl string `protobuf:"bytes,4,opt,name=short_desc_icon_url,json=shortDescIconUrl,proto3" json:"short_desc_icon_url,omitempty"`
	// Amount involved in the transaction
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// badge to be applied on the amount
	AmountBadge GetActivitiesResponse_Activity_AmountBadge `protobuf:"varint,6,opt,name=amount_badge,json=amountBadge,proto3,enum=actoractivity.GetActivitiesResponse_Activity_AmountBadge" json:"amount_badge,omitempty"`
	// timestamp of the activity
	// given the activity will be in terminal state, it will be equal to last updated time
	ActivityTimestamp *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=activity_timestamp,json=activityTimestamp,proto3" json:"activity_timestamp,omitempty"`
	// unique identifier representing the activity
	// will change depending on the type of the activity
	// for pay and collect fund transfers will be equal to order id
	ActivityId string `protobuf:"bytes,8,opt,name=activity_id,json=activityId,proto3" json:"activity_id,omitempty"`
	// actor activity of the second actor involved in the transaction
	// we need the second actor id in the response to populate the actor colour
	SecondActorId string `protobuf:"bytes,9,opt,name=second_actor_id,json=secondActorId,proto3" json:"second_actor_id,omitempty"`
	// Types that are assignable to DeeplinkIdentifier:
	//
	//	*GetActivitiesResponse_Activity_TimelineId
	//	*GetActivitiesResponse_Activity_DepositAccountIdentifier_
	DeeplinkIdentifier isGetActivitiesResponse_Activity_DeeplinkIdentifier `protobuf_oneof:"deeplink_identifier"`
	ActivityType       GetActivitiesResponse_Activity_Type                 `protobuf:"varint,12,opt,name=activity_type,json=activityType,proto3,enum=actoractivity.GetActivitiesResponse_Activity_Type" json:"activity_type,omitempty"`
	ActivityEntryPoint ActivityEntryPoint                                  `protobuf:"varint,13,opt,name=activity_entry_point,json=activityEntryPoint,proto3,enum=actoractivity.ActivityEntryPoint" json:"activity_entry_point,omitempty"`
	// partner tag for the activity
	// e.g. AutoPay logo for the upi
	// mandate autoPay txn
	PartnerTag *common.VisualElement `protobuf:"bytes,14,opt,name=partner_tag,json=partnerTag,proto3" json:"partner_tag,omitempty"`
}

func (x *GetActivitiesResponse_Activity) Reset() {
	*x = GetActivitiesResponse_Activity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivitiesResponse_Activity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivitiesResponse_Activity) ProtoMessage() {}

func (x *GetActivitiesResponse_Activity) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivitiesResponse_Activity.ProtoReflect.Descriptor instead.
func (*GetActivitiesResponse_Activity) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetActivitiesResponse_Activity) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *GetActivitiesResponse_Activity) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *GetActivitiesResponse_Activity) GetShortDesc() string {
	if x != nil {
		return x.ShortDesc
	}
	return ""
}

func (x *GetActivitiesResponse_Activity) GetShortDescIconUrl() string {
	if x != nil {
		return x.ShortDescIconUrl
	}
	return ""
}

func (x *GetActivitiesResponse_Activity) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *GetActivitiesResponse_Activity) GetAmountBadge() GetActivitiesResponse_Activity_AmountBadge {
	if x != nil {
		return x.AmountBadge
	}
	return GetActivitiesResponse_Activity_AMOUNT_BADGE_UNSPECIFIED
}

func (x *GetActivitiesResponse_Activity) GetActivityTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.ActivityTimestamp
	}
	return nil
}

func (x *GetActivitiesResponse_Activity) GetActivityId() string {
	if x != nil {
		return x.ActivityId
	}
	return ""
}

func (x *GetActivitiesResponse_Activity) GetSecondActorId() string {
	if x != nil {
		return x.SecondActorId
	}
	return ""
}

func (m *GetActivitiesResponse_Activity) GetDeeplinkIdentifier() isGetActivitiesResponse_Activity_DeeplinkIdentifier {
	if m != nil {
		return m.DeeplinkIdentifier
	}
	return nil
}

func (x *GetActivitiesResponse_Activity) GetTimelineId() string {
	if x, ok := x.GetDeeplinkIdentifier().(*GetActivitiesResponse_Activity_TimelineId); ok {
		return x.TimelineId
	}
	return ""
}

func (x *GetActivitiesResponse_Activity) GetDepositAccountIdentifier() *GetActivitiesResponse_Activity_DepositAccountIdentifier {
	if x, ok := x.GetDeeplinkIdentifier().(*GetActivitiesResponse_Activity_DepositAccountIdentifier_); ok {
		return x.DepositAccountIdentifier
	}
	return nil
}

func (x *GetActivitiesResponse_Activity) GetActivityType() GetActivitiesResponse_Activity_Type {
	if x != nil {
		return x.ActivityType
	}
	return GetActivitiesResponse_Activity_ACTIVITY_TYPE_UNSPECIFIED
}

func (x *GetActivitiesResponse_Activity) GetActivityEntryPoint() ActivityEntryPoint {
	if x != nil {
		return x.ActivityEntryPoint
	}
	return ActivityEntryPoint_ACTIVITY_ENTRY_POINT_UNSPECIFIED
}

func (x *GetActivitiesResponse_Activity) GetPartnerTag() *common.VisualElement {
	if x != nil {
		return x.PartnerTag
	}
	return nil
}

type isGetActivitiesResponse_Activity_DeeplinkIdentifier interface {
	isGetActivitiesResponse_Activity_DeeplinkIdentifier()
}

type GetActivitiesResponse_Activity_TimelineId struct {
	// unique identifier for the timeline between
	// the two actors involved in the activity
	TimelineId string `protobuf:"bytes,10,opt,name=timeline_id,json=timelineId,proto3,oneof"`
}

type GetActivitiesResponse_Activity_DepositAccountIdentifier_ struct {
	DepositAccountIdentifier *GetActivitiesResponse_Activity_DepositAccountIdentifier `protobuf:"bytes,11,opt,name=deposit_account_identifier,json=depositAccountIdentifier,proto3,oneof"`
}

func (*GetActivitiesResponse_Activity_TimelineId) isGetActivitiesResponse_Activity_DeeplinkIdentifier() {
}

func (*GetActivitiesResponse_Activity_DepositAccountIdentifier_) isGetActivitiesResponse_Activity_DeeplinkIdentifier() {
}

type GetActivitiesResponse_Activity_DepositAccountIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// type of the account for the account id
	AccountType accounts.Type `protobuf:"varint,2,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
}

func (x *GetActivitiesResponse_Activity_DepositAccountIdentifier) Reset() {
	*x = GetActivitiesResponse_Activity_DepositAccountIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActivitiesResponse_Activity_DepositAccountIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActivitiesResponse_Activity_DepositAccountIdentifier) ProtoMessage() {}

func (x *GetActivitiesResponse_Activity_DepositAccountIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActivitiesResponse_Activity_DepositAccountIdentifier.ProtoReflect.Descriptor instead.
func (*GetActivitiesResponse_Activity_DepositAccountIdentifier) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *GetActivitiesResponse_Activity_DepositAccountIdentifier) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *GetActivitiesResponse_Activity_DepositAccountIdentifier) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

type GetFinancialActivitiesRequest_ActivityOffSet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// fi_txn_offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp for fi txns
	FiTxnOffset int32 `protobuf:"varint,1,opt,name=fi_txn_offset,json=fiTxnOffset,proto3" json:"fi_txn_offset,omitempty"`
	// connected_account_txn_offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp for connected account txns
	ConnectedAccountTxnOffset int32 `protobuf:"varint,2,opt,name=connected_account_txn_offset,json=connectedAccountTxnOffset,proto3" json:"connected_account_txn_offset,omitempty"`
}

func (x *GetFinancialActivitiesRequest_ActivityOffSet) Reset() {
	*x = GetFinancialActivitiesRequest_ActivityOffSet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFinancialActivitiesRequest_ActivityOffSet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFinancialActivitiesRequest_ActivityOffSet) ProtoMessage() {}

func (x *GetFinancialActivitiesRequest_ActivityOffSet) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFinancialActivitiesRequest_ActivityOffSet.ProtoReflect.Descriptor instead.
func (*GetFinancialActivitiesRequest_ActivityOffSet) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *GetFinancialActivitiesRequest_ActivityOffSet) GetFiTxnOffset() int32 {
	if x != nil {
		return x.FiTxnOffset
	}
	return 0
}

func (x *GetFinancialActivitiesRequest_ActivityOffSet) GetConnectedAccountTxnOffset() int32 {
	if x != nil {
		return x.ConnectedAccountTxnOffset
	}
	return 0
}

type GetFinancialActivitiesRequest_PaymentFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// optional: order payment_protocol filter. orders with payment_protocol in this list are considered.
	// If kept empty, by default all the orders related to the actor are returned
	PaymentProtocol []payment.PaymentProtocol `protobuf:"varint,1,rep,packed,name=payment_protocol,json=paymentProtocol,proto3,enum=order.payment.PaymentProtocol" json:"payment_protocol,omitempty"`
	// Optional: The minimum amount or the lower limit of the amount to be searched on.
	FromAmount *money.Money `protobuf:"bytes,2,opt,name=from_amount,json=fromAmount,proto3" json:"from_amount,omitempty"`
	// Optional: The maximum amount or the upper limit of the amount to be searched on.
	ToAmount *money.Money `protobuf:"bytes,3,opt,name=to_amount,json=toAmount,proto3" json:"to_amount,omitempty"`
	// optional: transaction type wrt the given actor.
	// The caller can use this to filter out the results corresponding to a particular transactionType.
	// If kept empty, by default all the orders (credit or debit) related to the actor are returned
	// If TransactionType is TRANSACTION_TYPE_UNSPECIFIED, all the orders related to the actor are returned.
	TransactionType payment.AccountingEntryType `protobuf:"varint,4,opt,name=transaction_type,json=transactionType,proto3,enum=order.payment.AccountingEntryType" json:"transaction_type,omitempty"`
	// list of internal and connected account piIds to filter the result
	// Activities associated with only these piIds will be returned
	PiFilter *GetFinancialActivitiesRequest_PaymentFilter_PiFilter `protobuf:"bytes,5,opt,name=pi_filter,json=piFilter,proto3" json:"pi_filter,omitempty"`
}

func (x *GetFinancialActivitiesRequest_PaymentFilter) Reset() {
	*x = GetFinancialActivitiesRequest_PaymentFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFinancialActivitiesRequest_PaymentFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFinancialActivitiesRequest_PaymentFilter) ProtoMessage() {}

func (x *GetFinancialActivitiesRequest_PaymentFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFinancialActivitiesRequest_PaymentFilter.ProtoReflect.Descriptor instead.
func (*GetFinancialActivitiesRequest_PaymentFilter) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{2, 1}
}

func (x *GetFinancialActivitiesRequest_PaymentFilter) GetPaymentProtocol() []payment.PaymentProtocol {
	if x != nil {
		return x.PaymentProtocol
	}
	return nil
}

func (x *GetFinancialActivitiesRequest_PaymentFilter) GetFromAmount() *money.Money {
	if x != nil {
		return x.FromAmount
	}
	return nil
}

func (x *GetFinancialActivitiesRequest_PaymentFilter) GetToAmount() *money.Money {
	if x != nil {
		return x.ToAmount
	}
	return nil
}

func (x *GetFinancialActivitiesRequest_PaymentFilter) GetTransactionType() payment.AccountingEntryType {
	if x != nil {
		return x.TransactionType
	}
	return payment.AccountingEntryType(0)
}

func (x *GetFinancialActivitiesRequest_PaymentFilter) GetPiFilter() *GetFinancialActivitiesRequest_PaymentFilter_PiFilter {
	if x != nil {
		return x.PiFilter
	}
	return nil
}

type GetFinancialActivitiesRequest_PaymentFilter_PiFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// optional: list of internal PiIds to filter the result.
	// Activities associated with only these piIds will be returned
	InternalPiFilter []string `protobuf:"bytes,1,rep,name=internal_pi_filter,json=internalPiFilter,proto3" json:"internal_pi_filter,omitempty"`
	// optional: list of connected account PiIds to filter the result.
	// Activities associated with only these piIds will be returned
	AaPiFilter []string `protobuf:"bytes,2,rep,name=aa_pi_filter,json=aaPiFilter,proto3" json:"aa_pi_filter,omitempty"`
	// optional: list of other internal PiIds to filter the result.
	// Activities associated with between piIds for the actor and other given piIds will be returned
	OtherInternalPiFilter []string `protobuf:"bytes,3,rep,name=other_internal_pi_filter,json=otherInternalPiFilter,proto3" json:"other_internal_pi_filter,omitempty"`
	// optional: list of other connected account PiIds to filter the result.
	// Activities associated with between connected account piIds for the actor and other given
	// connected account piIds will be returned
	OtherAaPiFilter []string `protobuf:"bytes,4,rep,name=other_aa_pi_filter,json=otherAaPiFilter,proto3" json:"other_aa_pi_filter,omitempty"`
}

func (x *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) Reset() {
	*x = GetFinancialActivitiesRequest_PaymentFilter_PiFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_actoractivity_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFinancialActivitiesRequest_PaymentFilter_PiFilter) ProtoMessage() {}

func (x *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_actoractivity_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFinancialActivitiesRequest_PaymentFilter_PiFilter.ProtoReflect.Descriptor instead.
func (*GetFinancialActivitiesRequest_PaymentFilter_PiFilter) Descriptor() ([]byte, []int) {
	return file_api_order_actoractivity_service_proto_rawDescGZIP(), []int{2, 1, 0}
}

func (x *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) GetInternalPiFilter() []string {
	if x != nil {
		return x.InternalPiFilter
	}
	return nil
}

func (x *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) GetAaPiFilter() []string {
	if x != nil {
		return x.AaPiFilter
	}
	return nil
}

func (x *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) GetOtherInternalPiFilter() []string {
	if x != nil {
		return x.OtherInternalPiFilter
	}
	return nil
}

func (x *GetFinancialActivitiesRequest_PaymentFilter_PiFilter) GetOtherAaPiFilter() []string {
	if x != nil {
		return x.OtherAaPiFilter
	}
	return nil
}

var File_api_order_actoractivity_service_proto protoreflect.FileDescriptor

var file_api_order_actoractivity_service_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61,
	0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70,
	0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf0, 0x0a, 0x0a, 0x14, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x0e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x58, 0x0a, 0x1a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x18, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65,
	0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x26, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x28, 0x28, 0x0a, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x2b, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4f, 0x66,
	0x66, 0x73, 0x65, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x1f, 0x0a, 0x09, 0x70, 0x69, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x08, 0x70, 0x69, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x6f,
	0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x61, 0x5f, 0x74,
	0x78, 0x6e, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0b, 0x61, 0x61, 0x54, 0x78, 0x6e, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x54, 0x0a, 0x18,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x16, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x12, 0x58, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x0d, 0x70,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x50, 0x0a, 0x16,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x70, 0x61, 0x67, 0x65, 0x4c, 0x61,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x5c,
	0x0a, 0x10, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6e,
	0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x37, 0x0a, 0x18,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x6f, 0x66, 0x74, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15,
	0x66, 0x65, 0x74, 0x63, 0x68, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x1a, 0x61, 0x0a, 0x0d, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0xac, 0x02, 0x0a, 0x0d, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x10, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x33, 0x0a, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a,
	0x66, 0x72, 0x6f, 0x6d, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x6f,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x08, 0x74, 0x6f, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x10, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x69,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70,
	0x69, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0x72, 0x0a, 0x0e, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4e, 0x54,
	0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x45,
	0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x49, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f,
	0x49, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x02, 0x22, 0xe1, 0x13, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x0a, 0x0a, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x52, 0x0a, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x1a, 0x9d, 0x12, 0x0a, 0x08, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72,
	0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74,
	0x5f, 0x64, 0x65, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x68, 0x6f,
	0x72, 0x74, 0x44, 0x65, 0x73, 0x63, 0x12, 0x2d, 0x0a, 0x13, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x44, 0x65, 0x73, 0x63, 0x49, 0x63,
	0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x5c, 0x0a, 0x0c, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x64, 0x67,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x64,
	0x67, 0x65, 0x52, 0x0b, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x64, 0x67, 0x65, 0x12,
	0x49, 0x0a, 0x12, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x11, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x73,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x74, 0x69, 0x6d, 0x65,
	0x6c, 0x69, 0x6e, 0x65, 0x49, 0x64, 0x12, 0x86, 0x01, 0x0a, 0x1a, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x48, 0x00, 0x52, 0x18, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12,
	0x57, 0x0a, 0x0d, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x53, 0x0a, 0x14, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x69, 0x74, 0x79, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x12, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x42, 0x0a,
	0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x54, 0x61,
	0x67, 0x1a, 0x6c, 0x0a, 0x18, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x22,
	0x79, 0x0a, 0x0b, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x64, 0x67, 0x65, 0x12, 0x1c,
	0x0a, 0x18, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x42, 0x41, 0x44, 0x47, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x03,
	0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08,
	0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x06, 0x22, 0xd5, 0x09, 0x0a, 0x04, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4d, 0x50, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01,
	0x12, 0x1b, 0x0a, 0x17, 0x49, 0x4d, 0x50, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x1c, 0x0a,
	0x18, 0x4e, 0x45, 0x46, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x4e,
	0x45, 0x46, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x54, 0x47, 0x53,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x10, 0x05, 0x12, 0x1b, 0x0a, 0x17, 0x52, 0x54, 0x47, 0x53, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x06, 0x12, 0x1b, 0x0a, 0x17, 0x55, 0x50, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x07,
	0x12, 0x1a, 0x0a, 0x16, 0x55, 0x50, 0x49, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x08, 0x12, 0x21, 0x0a, 0x1d,
	0x49, 0x4e, 0x54, 0x52, 0x41, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x09, 0x12,
	0x20, 0x0a, 0x1c, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x0a, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x54, 0x4d, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x0b, 0x12, 0x1a,
	0x0a, 0x16, 0x41, 0x54, 0x4d, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x0c, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x4d,
	0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x0d, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x44,
	0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x45, 0x44, 0x10, 0x0e,
	0x12, 0x1b, 0x0a, 0x17, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x1b, 0x0a,
	0x17, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x41,
	0x4d, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x45, 0x44, 0x10, 0x10, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49,
	0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x10, 0x11, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44,
	0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x45, 0x44, 0x10, 0x12,
	0x12, 0x1b, 0x0a, 0x17, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x13, 0x12, 0x1d, 0x0a,
	0x19, 0x49, 0x4d, 0x50, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x14, 0x12, 0x1d, 0x0a, 0x19,
	0x4e, 0x45, 0x46, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x15, 0x12, 0x1d, 0x0a, 0x19, 0x52,
	0x54, 0x47, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x16, 0x12, 0x1c, 0x0a, 0x18, 0x55, 0x50,
	0x49, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45,
	0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x17, 0x12, 0x22, 0x0a, 0x1e, 0x49, 0x4e, 0x54, 0x52,
	0x41, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x18, 0x12, 0x1c, 0x0a, 0x18,
	0x41, 0x54, 0x4d, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44, 0x10, 0x19, 0x12, 0x22, 0x0a, 0x1e, 0x44, 0x45,
	0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x1a, 0x12, 0x21,
	0x0a, 0x1d, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x1b, 0x12, 0x23, 0x0a, 0x1f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45,
	0x52, 0x53, 0x45, 0x44, 0x10, 0x1c, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f,
	0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x1d, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x49, 0x58,
	0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x45, 0x53, 0x54, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x1e, 0x12, 0x1c, 0x0a, 0x18,
	0x4e, 0x45, 0x46, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x1f, 0x12, 0x1c, 0x0a, 0x18, 0x52, 0x54,
	0x47, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x20, 0x12, 0x1b, 0x0a, 0x17, 0x55, 0x50, 0x49, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x21, 0x12, 0x21, 0x0a, 0x1d, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x42, 0x41,
	0x4e, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x22, 0x12, 0x1c, 0x0a, 0x18, 0x49, 0x4d, 0x50, 0x53,
	0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x23, 0x12, 0x1c, 0x0a, 0x18, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x10, 0x24, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x10, 0x25, 0x12, 0x1d, 0x0a, 0x19, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x26, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44,
	0x10, 0x27, 0x42, 0x15, 0x0a, 0x13, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x34, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x52,
	0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10,
	0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x22,
	0x8c, 0x0a, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x1a, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x18, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x54, 0x0a, 0x18, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x16, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x45,
	0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x26, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09,
	0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x28, 0x28, 0x0a, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x12, 0x52, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x79, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x64, 0x0a, 0x0f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4f, 0x66, 0x66, 0x53, 0x65, 0x74, 0x52, 0x0e, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x61, 0x0a,
	0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69,
	0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x0d, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x1a, 0x75, 0x0a, 0x0e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x4f, 0x66, 0x66, 0x53,
	0x65, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x69, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x66, 0x69, 0x54, 0x78, 0x6e,
	0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x3f, 0x0a, 0x1c, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x78, 0x6e, 0x5f,
	0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x19, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x78,
	0x6e, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x1a, 0xb4, 0x04, 0x0a, 0x0d, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x10, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x33, 0x0a, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x66,
	0x72, 0x6f, 0x6d, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x09, 0x74, 0x6f, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x08, 0x74, 0x6f, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4d, 0x0a, 0x10, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x60, 0x0a, 0x09, 0x70, 0x69, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74,
	0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2e, 0x50, 0x69, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x52, 0x08, 0x70, 0x69, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x1a, 0xc0, 0x01, 0x0a, 0x08,
	0x50, 0x69, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x50, 0x69,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x0c, 0x61, 0x61, 0x5f, 0x70, 0x69, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x61,
	0x50, 0x69, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x18, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x50, 0x69, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x2b, 0x0a, 0x12, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x61, 0x61, 0x5f, 0x70, 0x69,
	0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x6f,
	0x74, 0x68, 0x65, 0x72, 0x41, 0x61, 0x50, 0x69, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xc3,
	0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x41,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x46, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x2e, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x22, 0x34,
	0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00,
	0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x4f, 0x52, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46,
	0x4f, 0x55, 0x4e, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x4c, 0x10, 0x0d, 0x2a, 0x5e, 0x0a, 0x12, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x09, 0x0a, 0x05, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x41,
	0x41, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x10, 0x03, 0x32, 0xe6, 0x01, 0x0a, 0x0d, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x63,
	0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x5c, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74,
	0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x23, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61,
	0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x77, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x69, 0x65, 0x73, 0x12, 0x2c,
	0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47,
	0x65, 0x74, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x2e, 0x47, 0x65, 0x74,
	0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x60, 0x0a,
	0x2e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x5a,
	0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_order_actoractivity_service_proto_rawDescOnce sync.Once
	file_api_order_actoractivity_service_proto_rawDescData = file_api_order_actoractivity_service_proto_rawDesc
)

func file_api_order_actoractivity_service_proto_rawDescGZIP() []byte {
	file_api_order_actoractivity_service_proto_rawDescOnce.Do(func() {
		file_api_order_actoractivity_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_order_actoractivity_service_proto_rawDescData)
	})
	return file_api_order_actoractivity_service_proto_rawDescData
}

var file_api_order_actoractivity_service_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_order_actoractivity_service_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_order_actoractivity_service_proto_goTypes = []interface{}{
	(ActivityEntryPoint)(0),                                         // 0: actoractivity.ActivityEntryPoint
	(GetActivitiesRequest_EntryPointType)(0),                        // 1: actoractivity.GetActivitiesRequest.EntryPointType
	(GetActivitiesResponse_Status)(0),                               // 2: actoractivity.GetActivitiesResponse.Status
	(GetActivitiesResponse_Activity_AmountBadge)(0),                 // 3: actoractivity.GetActivitiesResponse.Activity.AmountBadge
	(GetActivitiesResponse_Activity_Type)(0),                        // 4: actoractivity.GetActivitiesResponse.Activity.Type
	(GetFinancialActivitiesResponse_Status)(0),                      // 5: actoractivity.GetFinancialActivitiesResponse.Status
	(*GetActivitiesRequest)(nil),                                    // 6: actoractivity.GetActivitiesRequest
	(*GetActivitiesResponse)(nil),                                   // 7: actoractivity.GetActivitiesResponse
	(*GetFinancialActivitiesRequest)(nil),                           // 8: actoractivity.GetFinancialActivitiesRequest
	(*GetFinancialActivitiesResponse)(nil),                          // 9: actoractivity.GetFinancialActivitiesResponse
	(*GetActivitiesRequest_AccountFilter)(nil),                      // 10: actoractivity.GetActivitiesRequest.AccountFilter
	(*GetActivitiesRequest_PaymentFilter)(nil),                      // 11: actoractivity.GetActivitiesRequest.PaymentFilter
	(*GetActivitiesResponse_Activity)(nil),                          // 12: actoractivity.GetActivitiesResponse.Activity
	(*GetActivitiesResponse_Activity_DepositAccountIdentifier)(nil), // 13: actoractivity.GetActivitiesResponse.Activity.DepositAccountIdentifier
	(*GetFinancialActivitiesRequest_ActivityOffSet)(nil),            // 14: actoractivity.GetFinancialActivitiesRequest.ActivityOffSet
	(*GetFinancialActivitiesRequest_PaymentFilter)(nil),             // 15: actoractivity.GetFinancialActivitiesRequest.PaymentFilter
	(*GetFinancialActivitiesRequest_PaymentFilter_PiFilter)(nil),    // 16: actoractivity.GetFinancialActivitiesRequest.PaymentFilter.PiFilter
	(*timestamppb.Timestamp)(nil),                                   // 17: google.protobuf.Timestamp
	(*rpc.Status)(nil),                                              // 18: rpc.Status
	(enums.ActivitySource)(0),                                       // 19: order.actoractivity.enums.ActivitySource
	(*activity.Activity)(nil),                                       // 20: order.actoractivity.activity.Activity
	(accounts.Type)(0),                                              // 21: accounts.Type
	(payment.PaymentProtocol)(0),                                    // 22: order.payment.PaymentProtocol
	(*money.Money)(nil),                                             // 23: google.type.Money
	(payment.AccountingEntryType)(0),                                // 24: order.payment.AccountingEntryType
	(*common.VisualElement)(nil),                                    // 25: api.typesv2.common.VisualElement
}
var file_api_order_actoractivity_service_proto_depIdxs = []int32{
	10, // 0: actoractivity.GetActivitiesRequest.account_filter:type_name -> actoractivity.GetActivitiesRequest.AccountFilter
	17, // 1: actoractivity.GetActivitiesRequest.activities_start_timestamp:type_name -> google.protobuf.Timestamp
	17, // 2: actoractivity.GetActivitiesRequest.activities_end_timestamp:type_name -> google.protobuf.Timestamp
	11, // 3: actoractivity.GetActivitiesRequest.payment_filter:type_name -> actoractivity.GetActivitiesRequest.PaymentFilter
	17, // 4: actoractivity.GetActivitiesRequest.page_landing_timestamp:type_name -> google.protobuf.Timestamp
	1,  // 5: actoractivity.GetActivitiesRequest.entry_point_type:type_name -> actoractivity.GetActivitiesRequest.EntryPointType
	18, // 6: actoractivity.GetActivitiesResponse.status:type_name -> rpc.Status
	12, // 7: actoractivity.GetActivitiesResponse.activities:type_name -> actoractivity.GetActivitiesResponse.Activity
	17, // 8: actoractivity.GetFinancialActivitiesRequest.activities_start_timestamp:type_name -> google.protobuf.Timestamp
	17, // 9: actoractivity.GetFinancialActivitiesRequest.activities_end_timestamp:type_name -> google.protobuf.Timestamp
	19, // 10: actoractivity.GetFinancialActivitiesRequest.activity_source:type_name -> order.actoractivity.enums.ActivitySource
	14, // 11: actoractivity.GetFinancialActivitiesRequest.activity_offset:type_name -> actoractivity.GetFinancialActivitiesRequest.ActivityOffSet
	15, // 12: actoractivity.GetFinancialActivitiesRequest.payment_filter:type_name -> actoractivity.GetFinancialActivitiesRequest.PaymentFilter
	18, // 13: actoractivity.GetFinancialActivitiesResponse.status:type_name -> rpc.Status
	20, // 14: actoractivity.GetFinancialActivitiesResponse.activities:type_name -> order.actoractivity.activity.Activity
	21, // 15: actoractivity.GetActivitiesRequest.AccountFilter.account_type:type_name -> accounts.Type
	22, // 16: actoractivity.GetActivitiesRequest.PaymentFilter.payment_protocol:type_name -> order.payment.PaymentProtocol
	23, // 17: actoractivity.GetActivitiesRequest.PaymentFilter.from_amount:type_name -> google.type.Money
	23, // 18: actoractivity.GetActivitiesRequest.PaymentFilter.to_amount:type_name -> google.type.Money
	24, // 19: actoractivity.GetActivitiesRequest.PaymentFilter.transaction_type:type_name -> order.payment.AccountingEntryType
	23, // 20: actoractivity.GetActivitiesResponse.Activity.amount:type_name -> google.type.Money
	3,  // 21: actoractivity.GetActivitiesResponse.Activity.amount_badge:type_name -> actoractivity.GetActivitiesResponse.Activity.AmountBadge
	17, // 22: actoractivity.GetActivitiesResponse.Activity.activity_timestamp:type_name -> google.protobuf.Timestamp
	13, // 23: actoractivity.GetActivitiesResponse.Activity.deposit_account_identifier:type_name -> actoractivity.GetActivitiesResponse.Activity.DepositAccountIdentifier
	4,  // 24: actoractivity.GetActivitiesResponse.Activity.activity_type:type_name -> actoractivity.GetActivitiesResponse.Activity.Type
	0,  // 25: actoractivity.GetActivitiesResponse.Activity.activity_entry_point:type_name -> actoractivity.ActivityEntryPoint
	25, // 26: actoractivity.GetActivitiesResponse.Activity.partner_tag:type_name -> api.typesv2.common.VisualElement
	21, // 27: actoractivity.GetActivitiesResponse.Activity.DepositAccountIdentifier.account_type:type_name -> accounts.Type
	22, // 28: actoractivity.GetFinancialActivitiesRequest.PaymentFilter.payment_protocol:type_name -> order.payment.PaymentProtocol
	23, // 29: actoractivity.GetFinancialActivitiesRequest.PaymentFilter.from_amount:type_name -> google.type.Money
	23, // 30: actoractivity.GetFinancialActivitiesRequest.PaymentFilter.to_amount:type_name -> google.type.Money
	24, // 31: actoractivity.GetFinancialActivitiesRequest.PaymentFilter.transaction_type:type_name -> order.payment.AccountingEntryType
	16, // 32: actoractivity.GetFinancialActivitiesRequest.PaymentFilter.pi_filter:type_name -> actoractivity.GetFinancialActivitiesRequest.PaymentFilter.PiFilter
	6,  // 33: actoractivity.ActorActivity.GetActivities:input_type -> actoractivity.GetActivitiesRequest
	8,  // 34: actoractivity.ActorActivity.GetFinancialActivities:input_type -> actoractivity.GetFinancialActivitiesRequest
	7,  // 35: actoractivity.ActorActivity.GetActivities:output_type -> actoractivity.GetActivitiesResponse
	9,  // 36: actoractivity.ActorActivity.GetFinancialActivities:output_type -> actoractivity.GetFinancialActivitiesResponse
	35, // [35:37] is the sub-list for method output_type
	33, // [33:35] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	33, // [33:33] is the sub-list for extension extendee
	0,  // [0:33] is the sub-list for field type_name
}

func init() { file_api_order_actoractivity_service_proto_init() }
func file_api_order_actoractivity_service_proto_init() {
	if File_api_order_actoractivity_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_order_actoractivity_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivitiesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivitiesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFinancialActivitiesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFinancialActivitiesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivitiesRequest_AccountFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivitiesRequest_PaymentFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivitiesResponse_Activity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActivitiesResponse_Activity_DepositAccountIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFinancialActivitiesRequest_ActivityOffSet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFinancialActivitiesRequest_PaymentFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_actoractivity_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetFinancialActivitiesRequest_PaymentFilter_PiFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_order_actoractivity_service_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*GetActivitiesResponse_Activity_TimelineId)(nil),
		(*GetActivitiesResponse_Activity_DepositAccountIdentifier_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_order_actoractivity_service_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_order_actoractivity_service_proto_goTypes,
		DependencyIndexes: file_api_order_actoractivity_service_proto_depIdxs,
		EnumInfos:         file_api_order_actoractivity_service_proto_enumTypes,
		MessageInfos:      file_api_order_actoractivity_service_proto_msgTypes,
	}.Build()
	File_api_order_actoractivity_service_proto = out.File
	file_api_order_actoractivity_service_proto_rawDesc = nil
	file_api_order_actoractivity_service_proto_goTypes = nil
	file_api_order_actoractivity_service_proto_depIdxs = nil
}
