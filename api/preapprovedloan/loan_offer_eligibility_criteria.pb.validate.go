// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/preapprovedloan/loan_offer_eligibility_criteria.proto

package preapprovedloan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/preapprovedloan/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.LoanPurpose(0)
)

// Validate checks the field values on LoanOfferEligibilityCriteria with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanOfferEligibilityCriteria) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanOfferEligibilityCriteria with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanOfferEligibilityCriteriaMultiError, or nil if none found.
func (m *LoanOfferEligibilityCriteria) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanOfferEligibilityCriteria) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for Vendor

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetVendorResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "VendorResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "VendorResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferEligibilityCriteriaValidationError{
				field:  "VendorResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferEligibilityCriteriaValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferEligibilityCriteriaValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferEligibilityCriteriaValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferId

	// no validation rules for BatchId

	// no validation rules for SubStatus

	// no validation rules for LoanScheme

	// no validation rules for LoanProgram

	// no validation rules for VendorRequestId

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferEligibilityCriteriaValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpiredAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "ExpiredAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "ExpiredAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiredAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferEligibilityCriteriaValidationError{
				field:  "ExpiredAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDataRequirementDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "DataRequirementDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanOfferEligibilityCriteriaValidationError{
					field:  "DataRequirementDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataRequirementDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanOfferEligibilityCriteriaValidationError{
				field:  "DataRequirementDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanOfferEligibilityCriteriaMultiError(errors)
	}

	return nil
}

// LoanOfferEligibilityCriteriaMultiError is an error wrapping multiple
// validation errors returned by LoanOfferEligibilityCriteria.ValidateAll() if
// the designated constraints aren't met.
type LoanOfferEligibilityCriteriaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanOfferEligibilityCriteriaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanOfferEligibilityCriteriaMultiError) AllErrors() []error { return m }

// LoanOfferEligibilityCriteriaValidationError is the validation error returned
// by LoanOfferEligibilityCriteria.Validate if the designated constraints
// aren't met.
type LoanOfferEligibilityCriteriaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanOfferEligibilityCriteriaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanOfferEligibilityCriteriaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanOfferEligibilityCriteriaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanOfferEligibilityCriteriaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanOfferEligibilityCriteriaValidationError) ErrorName() string {
	return "LoanOfferEligibilityCriteriaValidationError"
}

// Error satisfies the builtin error interface
func (e LoanOfferEligibilityCriteriaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanOfferEligibilityCriteria.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanOfferEligibilityCriteriaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanOfferEligibilityCriteriaValidationError{}

// Validate checks the field values on VendorResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VendorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VendorResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VendorResponseMultiError,
// or nil if none found.
func (m *VendorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VendorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetVendorResponseTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VendorResponseValidationError{
					field:  "VendorResponseTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VendorResponseValidationError{
					field:  "VendorResponseTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVendorResponseTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VendorResponseValidationError{
				field:  "VendorResponseTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawPreBreResponse

	// no validation rules for RawFinalBreResponse

	// no validation rules for LoanId

	// no validation rules for ReferenceId

	if len(errors) > 0 {
		return VendorResponseMultiError(errors)
	}

	return nil
}

// VendorResponseMultiError is an error wrapping multiple validation errors
// returned by VendorResponse.ValidateAll() if the designated constraints
// aren't met.
type VendorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VendorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VendorResponseMultiError) AllErrors() []error { return m }

// VendorResponseValidationError is the validation error returned by
// VendorResponse.Validate if the designated constraints aren't met.
type VendorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VendorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VendorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VendorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VendorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VendorResponseValidationError) ErrorName() string { return "VendorResponseValidationError" }

// Error satisfies the builtin error interface
func (e VendorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVendorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VendorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VendorResponseValidationError{}

// Validate checks the field values on PolicyParams with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PolicyParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PolicyParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PolicyParamsMultiError, or
// nil if none found.
func (m *PolicyParams) ValidateAll() error {
	return m.validate(true)
}

func (m *PolicyParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PricingScheme

	// no validation rules for EverVkycAttempted

	// no validation rules for PdScore

	// no validation rules for PdScoreVersion

	// no validation rules for SchemeId

	// no validation rules for PricingSchemeBre

	// no validation rules for BatchId

	if all {
		switch v := interface{}(m.GetPre()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "Pre",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "Pre",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPre()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PolicyParamsValidationError{
				field:  "Pre",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinal()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "Final",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "Final",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinal()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PolicyParamsValidationError{
				field:  "Final",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExecutionInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "ExecutionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "ExecutionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutionInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PolicyParamsValidationError{
				field:  "ExecutionInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDataInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "DataInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "DataInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PolicyParamsValidationError{
				field:  "DataInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PolicyParamsMultiError(errors)
	}

	return nil
}

// PolicyParamsMultiError is an error wrapping multiple validation errors
// returned by PolicyParams.ValidateAll() if the designated constraints aren't met.
type PolicyParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PolicyParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PolicyParamsMultiError) AllErrors() []error { return m }

// PolicyParamsValidationError is the validation error returned by
// PolicyParams.Validate if the designated constraints aren't met.
type PolicyParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PolicyParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PolicyParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PolicyParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PolicyParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PolicyParamsValidationError) ErrorName() string { return "PolicyParamsValidationError" }

// Error satisfies the builtin error interface
func (e PolicyParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPolicyParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PolicyParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PolicyParamsValidationError{}

// Validate checks the field values on ExecutionInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExecutionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecutionInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExecutionInfoMultiError, or
// nil if none found.
func (m *ExecutionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecutionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPre() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecutionInfoValidationError{
						field:  fmt.Sprintf("Pre[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecutionInfoValidationError{
						field:  fmt.Sprintf("Pre[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecutionInfoValidationError{
					field:  fmt.Sprintf("Pre[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFinal() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecutionInfoValidationError{
						field:  fmt.Sprintf("Final[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecutionInfoValidationError{
						field:  fmt.Sprintf("Final[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecutionInfoValidationError{
					field:  fmt.Sprintf("Final[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExecutionInfoMultiError(errors)
	}

	return nil
}

// ExecutionInfoMultiError is an error wrapping multiple validation errors
// returned by ExecutionInfo.ValidateAll() if the designated constraints
// aren't met.
type ExecutionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecutionInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecutionInfoMultiError) AllErrors() []error { return m }

// ExecutionInfoValidationError is the validation error returned by
// ExecutionInfo.Validate if the designated constraints aren't met.
type ExecutionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecutionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecutionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecutionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecutionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecutionInfoValidationError) ErrorName() string { return "ExecutionInfoValidationError" }

// Error satisfies the builtin error interface
func (e ExecutionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecutionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecutionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecutionInfoValidationError{}

// Validate checks the field values on PolicyParamsDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PolicyParamsDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PolicyParamsDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PolicyParamsDetailsMultiError, or nil if none found.
func (m *PolicyParamsDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PolicyParamsDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BatchId

	// no validation rules for SchemeId

	// no validation rules for LendingProgram

	// no validation rules for PreBreRequestId

	// no validation rules for FinalBreRequestId

	if len(errors) > 0 {
		return PolicyParamsDetailsMultiError(errors)
	}

	return nil
}

// PolicyParamsDetailsMultiError is an error wrapping multiple validation
// errors returned by PolicyParamsDetails.ValidateAll() if the designated
// constraints aren't met.
type PolicyParamsDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PolicyParamsDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PolicyParamsDetailsMultiError) AllErrors() []error { return m }

// PolicyParamsDetailsValidationError is the validation error returned by
// PolicyParamsDetails.Validate if the designated constraints aren't met.
type PolicyParamsDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PolicyParamsDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PolicyParamsDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PolicyParamsDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PolicyParamsDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PolicyParamsDetailsValidationError) ErrorName() string {
	return "PolicyParamsDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PolicyParamsDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPolicyParamsDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PolicyParamsDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PolicyParamsDetailsValidationError{}

// Validate checks the field values on DataInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DataInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DataInfoMultiError, or nil
// if none found.
func (m *DataInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DataInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataInfoValidationError{
					field:  "AaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataInfoValidationError{
					field:  "AaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataInfoValidationError{
				field:  "AaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEtbUser

	// no validation rules for IsB2BSalaryUser

	// no validation rules for MonthlyIncome

	// no validation rules for MonthsSinceSalaryActive

	// no validation rules for SalaryCreditDay

	if len(errors) > 0 {
		return DataInfoMultiError(errors)
	}

	return nil
}

// DataInfoMultiError is an error wrapping multiple validation errors returned
// by DataInfo.ValidateAll() if the designated constraints aren't met.
type DataInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataInfoMultiError) AllErrors() []error { return m }

// DataInfoValidationError is the validation error returned by
// DataInfo.Validate if the designated constraints aren't met.
type DataInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataInfoValidationError) ErrorName() string { return "DataInfoValidationError" }

// Error satisfies the builtin error interface
func (e DataInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataInfoValidationError{}

// Validate checks the field values on DataRequirementDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataRequirementDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataRequirementDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataRequirementDetailsMultiError, or nil if none found.
func (m *DataRequirementDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DataRequirementDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDataRequirements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataRequirementDetailsValidationError{
						field:  fmt.Sprintf("DataRequirements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataRequirementDetailsValidationError{
						field:  fmt.Sprintf("DataRequirements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataRequirementDetailsValidationError{
					field:  fmt.Sprintf("DataRequirements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for PreScreeningDecisionLrId

	// no validation rules for FinalBreDecisionLrId

	// no validation rules for VendorBreDecisionLrId

	// no validation rules for PreBreRequestId

	// no validation rules for FinalBreRequestId

	// no validation rules for VendorBreReferenceId

	if all {
		switch v := interface{}(m.GetDesiredLoanAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataRequirementDetailsValidationError{
					field:  "DesiredLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataRequirementDetailsValidationError{
					field:  "DesiredLoanAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDesiredLoanAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataRequirementDetailsValidationError{
				field:  "DesiredLoanAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanPurpose

	if len(errors) > 0 {
		return DataRequirementDetailsMultiError(errors)
	}

	return nil
}

// DataRequirementDetailsMultiError is an error wrapping multiple validation
// errors returned by DataRequirementDetails.ValidateAll() if the designated
// constraints aren't met.
type DataRequirementDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataRequirementDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataRequirementDetailsMultiError) AllErrors() []error { return m }

// DataRequirementDetailsValidationError is the validation error returned by
// DataRequirementDetails.Validate if the designated constraints aren't met.
type DataRequirementDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataRequirementDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataRequirementDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataRequirementDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataRequirementDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataRequirementDetailsValidationError) ErrorName() string {
	return "DataRequirementDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e DataRequirementDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataRequirementDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataRequirementDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataRequirementDetailsValidationError{}

// Validate checks the field values on DataRequirement with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DataRequirement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataRequirement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataRequirementMultiError, or nil if none found.
func (m *DataRequirement) ValidateAll() error {
	return m.validate(true)
}

func (m *DataRequirement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DataRequirementType

	// no validation rules for IsRequired

	// no validation rules for IsCollected

	switch v := m.Data.(type) {
	case *DataRequirement_PreBreDataLoanPreferences_:
		if v == nil {
			err := DataRequirementValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPreBreDataLoanPreferences()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataRequirementValidationError{
						field:  "PreBreDataLoanPreferences",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataRequirementValidationError{
						field:  "PreBreDataLoanPreferences",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPreBreDataLoanPreferences()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataRequirementValidationError{
					field:  "PreBreDataLoanPreferences",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DataRequirement_PreBreConsent_:
		if v == nil {
			err := DataRequirementValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPreBreConsent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataRequirementValidationError{
						field:  "PreBreConsent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataRequirementValidationError{
						field:  "PreBreConsent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPreBreConsent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataRequirementValidationError{
					field:  "PreBreConsent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *DataRequirement_AaAnalysisBankDetails_:
		if v == nil {
			err := DataRequirementValidationError{
				field:  "Data",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAaAnalysisBankDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DataRequirementValidationError{
						field:  "AaAnalysisBankDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DataRequirementValidationError{
						field:  "AaAnalysisBankDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAaAnalysisBankDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DataRequirementValidationError{
					field:  "AaAnalysisBankDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return DataRequirementMultiError(errors)
	}

	return nil
}

// DataRequirementMultiError is an error wrapping multiple validation errors
// returned by DataRequirement.ValidateAll() if the designated constraints
// aren't met.
type DataRequirementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataRequirementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataRequirementMultiError) AllErrors() []error { return m }

// DataRequirementValidationError is the validation error returned by
// DataRequirement.Validate if the designated constraints aren't met.
type DataRequirementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataRequirementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataRequirementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataRequirementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataRequirementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataRequirementValidationError) ErrorName() string { return "DataRequirementValidationError" }

// Error satisfies the builtin error interface
func (e DataRequirementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataRequirement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataRequirementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataRequirementValidationError{}

// Validate checks the field values on PolicyParams_Pre with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PolicyParams_Pre) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PolicyParams_Pre with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PolicyParams_PreMultiError, or nil if none found.
func (m *PolicyParams_Pre) ValidateAll() error {
	return m.validate(true)
}

func (m *PolicyParams_Pre) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PdScore

	// no validation rules for PdScoreVersion

	// no validation rules for SchemeId

	// no validation rules for BatchId

	// no validation rules for EverVkycAttempted

	// no validation rules for PricingScheme

	if len(errors) > 0 {
		return PolicyParams_PreMultiError(errors)
	}

	return nil
}

// PolicyParams_PreMultiError is an error wrapping multiple validation errors
// returned by PolicyParams_Pre.ValidateAll() if the designated constraints
// aren't met.
type PolicyParams_PreMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PolicyParams_PreMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PolicyParams_PreMultiError) AllErrors() []error { return m }

// PolicyParams_PreValidationError is the validation error returned by
// PolicyParams_Pre.Validate if the designated constraints aren't met.
type PolicyParams_PreValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PolicyParams_PreValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PolicyParams_PreValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PolicyParams_PreValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PolicyParams_PreValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PolicyParams_PreValidationError) ErrorName() string { return "PolicyParams_PreValidationError" }

// Error satisfies the builtin error interface
func (e PolicyParams_PreValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPolicyParams_Pre.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PolicyParams_PreValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PolicyParams_PreValidationError{}

// Validate checks the field values on PolicyParams_Final with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PolicyParams_Final) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PolicyParams_Final with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PolicyParams_FinalMultiError, or nil if none found.
func (m *PolicyParams_Final) ValidateAll() error {
	return m.validate(true)
}

func (m *PolicyParams_Final) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SchemeId

	// no validation rules for BatchId

	// no validation rules for PricingSchemeBre

	if len(errors) > 0 {
		return PolicyParams_FinalMultiError(errors)
	}

	return nil
}

// PolicyParams_FinalMultiError is an error wrapping multiple validation errors
// returned by PolicyParams_Final.ValidateAll() if the designated constraints
// aren't met.
type PolicyParams_FinalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PolicyParams_FinalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PolicyParams_FinalMultiError) AllErrors() []error { return m }

// PolicyParams_FinalValidationError is the validation error returned by
// PolicyParams_Final.Validate if the designated constraints aren't met.
type PolicyParams_FinalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PolicyParams_FinalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PolicyParams_FinalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PolicyParams_FinalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PolicyParams_FinalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PolicyParams_FinalValidationError) ErrorName() string {
	return "PolicyParams_FinalValidationError"
}

// Error satisfies the builtin error interface
func (e PolicyParams_FinalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPolicyParams_Final.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PolicyParams_FinalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PolicyParams_FinalValidationError{}

// Validate checks the field values on DataInfo_AaData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DataInfo_AaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataInfo_AaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataInfo_AaDataMultiError, or nil if none found.
func (m *DataInfo_AaData) ValidateAll() error {
	return m.validate(true)
}

func (m *DataInfo_AaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MedianAmountSalaryLast_180Days

	if len(errors) > 0 {
		return DataInfo_AaDataMultiError(errors)
	}

	return nil
}

// DataInfo_AaDataMultiError is an error wrapping multiple validation errors
// returned by DataInfo_AaData.ValidateAll() if the designated constraints
// aren't met.
type DataInfo_AaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataInfo_AaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataInfo_AaDataMultiError) AllErrors() []error { return m }

// DataInfo_AaDataValidationError is the validation error returned by
// DataInfo_AaData.Validate if the designated constraints aren't met.
type DataInfo_AaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataInfo_AaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataInfo_AaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataInfo_AaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataInfo_AaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataInfo_AaDataValidationError) ErrorName() string { return "DataInfo_AaDataValidationError" }

// Error satisfies the builtin error interface
func (e DataInfo_AaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataInfo_AaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataInfo_AaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataInfo_AaDataValidationError{}

// Validate checks the field values on DataRequirement_AaAnalysisBankDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *DataRequirement_AaAnalysisBankDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataRequirement_AaAnalysisBankDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// DataRequirement_AaAnalysisBankDetailsMultiError, or nil if none found.
func (m *DataRequirement_AaAnalysisBankDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *DataRequirement_AaAnalysisBankDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountHolderName

	// no validation rules for AccountNumber

	// no validation rules for Ifsc

	// no validation rules for Type

	// no validation rules for BankName

	if len(errors) > 0 {
		return DataRequirement_AaAnalysisBankDetailsMultiError(errors)
	}

	return nil
}

// DataRequirement_AaAnalysisBankDetailsMultiError is an error wrapping
// multiple validation errors returned by
// DataRequirement_AaAnalysisBankDetails.ValidateAll() if the designated
// constraints aren't met.
type DataRequirement_AaAnalysisBankDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataRequirement_AaAnalysisBankDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataRequirement_AaAnalysisBankDetailsMultiError) AllErrors() []error { return m }

// DataRequirement_AaAnalysisBankDetailsValidationError is the validation error
// returned by DataRequirement_AaAnalysisBankDetails.Validate if the
// designated constraints aren't met.
type DataRequirement_AaAnalysisBankDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataRequirement_AaAnalysisBankDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataRequirement_AaAnalysisBankDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataRequirement_AaAnalysisBankDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataRequirement_AaAnalysisBankDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataRequirement_AaAnalysisBankDetailsValidationError) ErrorName() string {
	return "DataRequirement_AaAnalysisBankDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e DataRequirement_AaAnalysisBankDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataRequirement_AaAnalysisBankDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataRequirement_AaAnalysisBankDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataRequirement_AaAnalysisBankDetailsValidationError{}

// Validate checks the field values on
// DataRequirement_PreBreDataLoanPreferences with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DataRequirement_PreBreDataLoanPreferences) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// DataRequirement_PreBreDataLoanPreferences with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// DataRequirement_PreBreDataLoanPreferencesMultiError, or nil if none found.
func (m *DataRequirement_PreBreDataLoanPreferences) ValidateAll() error {
	return m.validate(true)
}

func (m *DataRequirement_PreBreDataLoanPreferences) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanAmount

	// no validation rules for Interest

	if len(errors) > 0 {
		return DataRequirement_PreBreDataLoanPreferencesMultiError(errors)
	}

	return nil
}

// DataRequirement_PreBreDataLoanPreferencesMultiError is an error wrapping
// multiple validation errors returned by
// DataRequirement_PreBreDataLoanPreferences.ValidateAll() if the designated
// constraints aren't met.
type DataRequirement_PreBreDataLoanPreferencesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataRequirement_PreBreDataLoanPreferencesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataRequirement_PreBreDataLoanPreferencesMultiError) AllErrors() []error { return m }

// DataRequirement_PreBreDataLoanPreferencesValidationError is the validation
// error returned by DataRequirement_PreBreDataLoanPreferences.Validate if the
// designated constraints aren't met.
type DataRequirement_PreBreDataLoanPreferencesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataRequirement_PreBreDataLoanPreferencesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataRequirement_PreBreDataLoanPreferencesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataRequirement_PreBreDataLoanPreferencesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataRequirement_PreBreDataLoanPreferencesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataRequirement_PreBreDataLoanPreferencesValidationError) ErrorName() string {
	return "DataRequirement_PreBreDataLoanPreferencesValidationError"
}

// Error satisfies the builtin error interface
func (e DataRequirement_PreBreDataLoanPreferencesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataRequirement_PreBreDataLoanPreferences.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataRequirement_PreBreDataLoanPreferencesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataRequirement_PreBreDataLoanPreferencesValidationError{}

// Validate checks the field values on DataRequirement_PreBreConsent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DataRequirement_PreBreConsent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataRequirement_PreBreConsent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// DataRequirement_PreBreConsentMultiError, or nil if none found.
func (m *DataRequirement_PreBreConsent) ValidateAll() error {
	return m.validate(true)
}

func (m *DataRequirement_PreBreConsent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if len(errors) > 0 {
		return DataRequirement_PreBreConsentMultiError(errors)
	}

	return nil
}

// DataRequirement_PreBreConsentMultiError is an error wrapping multiple
// validation errors returned by DataRequirement_PreBreConsent.ValidateAll()
// if the designated constraints aren't met.
type DataRequirement_PreBreConsentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataRequirement_PreBreConsentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataRequirement_PreBreConsentMultiError) AllErrors() []error { return m }

// DataRequirement_PreBreConsentValidationError is the validation error
// returned by DataRequirement_PreBreConsent.Validate if the designated
// constraints aren't met.
type DataRequirement_PreBreConsentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataRequirement_PreBreConsentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataRequirement_PreBreConsentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataRequirement_PreBreConsentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataRequirement_PreBreConsentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataRequirement_PreBreConsentValidationError) ErrorName() string {
	return "DataRequirement_PreBreConsentValidationError"
}

// Error satisfies the builtin error interface
func (e DataRequirement_PreBreConsentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataRequirement_PreBreConsent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataRequirement_PreBreConsentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataRequirement_PreBreConsentValidationError{}
