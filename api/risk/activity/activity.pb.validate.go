// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/activity/activity.proto

package activity

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/risk/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.Action(0)
)

// Validate checks the field values on AddToBankActionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddToBankActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddToBankActionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddToBankActionRequestMultiError, or nil if none found.
func (m *AddToBankActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddToBankActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddToBankActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddToBankActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddToBankActionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetWorkflowId()) < 1 {
		err := AddToBankActionRequestValidationError{
			field:  "WorkflowId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _AddToBankActionRequest_Action_NotInLookup[m.GetAction()]; ok {
		err := AddToBankActionRequestValidationError{
			field:  "Action",
			reason: "value must not be in list [ACTION_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddToBankActionRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddToBankActionRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddToBankActionRequestValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := AddToBankActionRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AddToBankActionRequestMultiError(errors)
	}

	return nil
}

// AddToBankActionRequestMultiError is an error wrapping multiple validation
// errors returned by AddToBankActionRequest.ValidateAll() if the designated
// constraints aren't met.
type AddToBankActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddToBankActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddToBankActionRequestMultiError) AllErrors() []error { return m }

// AddToBankActionRequestValidationError is the validation error returned by
// AddToBankActionRequest.Validate if the designated constraints aren't met.
type AddToBankActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddToBankActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddToBankActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddToBankActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddToBankActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddToBankActionRequestValidationError) ErrorName() string {
	return "AddToBankActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddToBankActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddToBankActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddToBankActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddToBankActionRequestValidationError{}

var _AddToBankActionRequest_Action_NotInLookup = map[enums.Action]struct{}{
	0: {},
}

// Validate checks the field values on AddToBankActionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddToBankActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddToBankActionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddToBankActionResponseMultiError, or nil if none found.
func (m *AddToBankActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddToBankActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddToBankActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddToBankActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddToBankActionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BankActionId

	if len(errors) > 0 {
		return AddToBankActionResponseMultiError(errors)
	}

	return nil
}

// AddToBankActionResponseMultiError is an error wrapping multiple validation
// errors returned by AddToBankActionResponse.ValidateAll() if the designated
// constraints aren't met.
type AddToBankActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddToBankActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddToBankActionResponseMultiError) AllErrors() []error { return m }

// AddToBankActionResponseValidationError is the validation error returned by
// AddToBankActionResponse.Validate if the designated constraints aren't met.
type AddToBankActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddToBankActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddToBankActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddToBankActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddToBankActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddToBankActionResponseValidationError) ErrorName() string {
	return "AddToBankActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddToBankActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddToBankActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddToBankActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddToBankActionResponseValidationError{}

// Validate checks the field values on GetReminderPointRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReminderPointRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReminderPointRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReminderPointRequestMultiError, or nil if none found.
func (m *GetReminderPointRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReminderPointRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReminderPointRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReminderPointRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReminderPointRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWorkflowType()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReminderPointRequestValidationError{
					field:  "WorkflowType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReminderPointRequestValidationError{
					field:  "WorkflowType",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWorkflowType()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReminderPointRequestValidationError{
				field:  "WorkflowType",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReminderStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReminderPointRequestValidationError{
					field:  "ReminderStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReminderPointRequestValidationError{
					field:  "ReminderStartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReminderStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReminderPointRequestValidationError{
				field:  "ReminderStartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetReminderPointRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for FormId

	switch v := m.RequestIdentifier.(type) {
	case *GetReminderPointRequest_ClientRequestId:
		if v == nil {
			err := GetReminderPointRequestValidationError{
				field:  "RequestIdentifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ClientRequestId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetReminderPointRequestMultiError(errors)
	}

	return nil
}

// GetReminderPointRequestMultiError is an error wrapping multiple validation
// errors returned by GetReminderPointRequest.ValidateAll() if the designated
// constraints aren't met.
type GetReminderPointRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReminderPointRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReminderPointRequestMultiError) AllErrors() []error { return m }

// GetReminderPointRequestValidationError is the validation error returned by
// GetReminderPointRequest.Validate if the designated constraints aren't met.
type GetReminderPointRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReminderPointRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReminderPointRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReminderPointRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReminderPointRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReminderPointRequestValidationError) ErrorName() string {
	return "GetReminderPointRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetReminderPointRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReminderPointRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReminderPointRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReminderPointRequestValidationError{}

// Validate checks the field values on GetReminderPointResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReminderPointResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReminderPointResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetReminderPointResponseMultiError, or nil if none found.
func (m *GetReminderPointResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReminderPointResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReminderPointResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReminderPointResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReminderPointResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextReminderPoint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReminderPointResponseValidationError{
					field:  "NextReminderPoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReminderPointResponseValidationError{
					field:  "NextReminderPoint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextReminderPoint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReminderPointResponseValidationError{
				field:  "NextReminderPoint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetReminderPointResponseMultiError(errors)
	}

	return nil
}

// GetReminderPointResponseMultiError is an error wrapping multiple validation
// errors returned by GetReminderPointResponse.ValidateAll() if the designated
// constraints aren't met.
type GetReminderPointResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReminderPointResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReminderPointResponseMultiError) AllErrors() []error { return m }

// GetReminderPointResponseValidationError is the validation error returned by
// GetReminderPointResponse.Validate if the designated constraints aren't met.
type GetReminderPointResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReminderPointResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReminderPointResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReminderPointResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReminderPointResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReminderPointResponseValidationError) ErrorName() string {
	return "GetReminderPointResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetReminderPointResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReminderPointResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReminderPointResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReminderPointResponseValidationError{}

// Validate checks the field values on ValidateAndGetLEAHandlerRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateAndGetLEAHandlerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateAndGetLEAHandlerRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ValidateAndGetLEAHandlerRequestMultiError, or nil if none found.
func (m *ValidateAndGetLEAHandlerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateAndGetLEAHandlerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAndGetLEAHandlerRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAndGetLEAHandlerRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAndGetLEAHandlerRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for WorkflowId

	if all {
		switch v := interface{}(m.GetUnifiedLeaComplaint()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAndGetLEAHandlerRequestValidationError{
					field:  "UnifiedLeaComplaint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAndGetLEAHandlerRequestValidationError{
					field:  "UnifiedLeaComplaint",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnifiedLeaComplaint()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAndGetLEAHandlerRequestValidationError{
				field:  "UnifiedLeaComplaint",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateAndGetLEAHandlerRequestMultiError(errors)
	}

	return nil
}

// ValidateAndGetLEAHandlerRequestMultiError is an error wrapping multiple
// validation errors returned by ValidateAndGetLEAHandlerRequest.ValidateAll()
// if the designated constraints aren't met.
type ValidateAndGetLEAHandlerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateAndGetLEAHandlerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateAndGetLEAHandlerRequestMultiError) AllErrors() []error { return m }

// ValidateAndGetLEAHandlerRequestValidationError is the validation error
// returned by ValidateAndGetLEAHandlerRequest.Validate if the designated
// constraints aren't met.
type ValidateAndGetLEAHandlerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateAndGetLEAHandlerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateAndGetLEAHandlerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateAndGetLEAHandlerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateAndGetLEAHandlerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateAndGetLEAHandlerRequestValidationError) ErrorName() string {
	return "ValidateAndGetLEAHandlerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateAndGetLEAHandlerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateAndGetLEAHandlerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateAndGetLEAHandlerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateAndGetLEAHandlerRequestValidationError{}

// Validate checks the field values on ValidateAndGetLEAHandlerResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ValidateAndGetLEAHandlerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateAndGetLEAHandlerResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ValidateAndGetLEAHandlerResponseMultiError, or nil if none found.
func (m *ValidateAndGetLEAHandlerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateAndGetLEAHandlerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAndGetLEAHandlerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAndGetLEAHandlerResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAndGetLEAHandlerResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHandlingParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateAndGetLEAHandlerResponseValidationError{
					field:  "HandlingParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateAndGetLEAHandlerResponseValidationError{
					field:  "HandlingParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHandlingParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateAndGetLEAHandlerResponseValidationError{
				field:  "HandlingParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateAndGetLEAHandlerResponseMultiError(errors)
	}

	return nil
}

// ValidateAndGetLEAHandlerResponseMultiError is an error wrapping multiple
// validation errors returned by
// ValidateAndGetLEAHandlerResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateAndGetLEAHandlerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateAndGetLEAHandlerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateAndGetLEAHandlerResponseMultiError) AllErrors() []error { return m }

// ValidateAndGetLEAHandlerResponseValidationError is the validation error
// returned by ValidateAndGetLEAHandlerResponse.Validate if the designated
// constraints aren't met.
type ValidateAndGetLEAHandlerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateAndGetLEAHandlerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateAndGetLEAHandlerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateAndGetLEAHandlerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateAndGetLEAHandlerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateAndGetLEAHandlerResponseValidationError) ErrorName() string {
	return "ValidateAndGetLEAHandlerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateAndGetLEAHandlerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateAndGetLEAHandlerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateAndGetLEAHandlerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateAndGetLEAHandlerResponseValidationError{}

// Validate checks the field values on
// GetNotificationTemplateForBankActionsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetNotificationTemplateForBankActionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNotificationTemplateForBankActionsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetNotificationTemplateForBankActionsRequestMultiError, or nil if none found.
func (m *GetNotificationTemplateForBankActionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNotificationTemplateForBankActionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNotificationTemplateForBankActionsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNotificationTemplateForBankActionsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNotificationTemplateForBankActionsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FormId

	// no validation rules for BankActionClientReqId

	// no validation rules for IsReminder

	if len(errors) > 0 {
		return GetNotificationTemplateForBankActionsRequestMultiError(errors)
	}

	return nil
}

// GetNotificationTemplateForBankActionsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetNotificationTemplateForBankActionsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetNotificationTemplateForBankActionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNotificationTemplateForBankActionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNotificationTemplateForBankActionsRequestMultiError) AllErrors() []error { return m }

// GetNotificationTemplateForBankActionsRequestValidationError is the
// validation error returned by
// GetNotificationTemplateForBankActionsRequest.Validate if the designated
// constraints aren't met.
type GetNotificationTemplateForBankActionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNotificationTemplateForBankActionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNotificationTemplateForBankActionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNotificationTemplateForBankActionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNotificationTemplateForBankActionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNotificationTemplateForBankActionsRequestValidationError) ErrorName() string {
	return "GetNotificationTemplateForBankActionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNotificationTemplateForBankActionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNotificationTemplateForBankActionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNotificationTemplateForBankActionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNotificationTemplateForBankActionsRequestValidationError{}

// Validate checks the field values on
// GetNotificationTemplateForBankActionsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetNotificationTemplateForBankActionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetNotificationTemplateForBankActionsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetNotificationTemplateForBankActionsResponseMultiError, or nil if none found.
func (m *GetNotificationTemplateForBankActionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNotificationTemplateForBankActionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNotificationTemplateForBankActionsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNotificationTemplateForBankActionsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNotificationTemplateForBankActionsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNotifications()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNotificationTemplateForBankActionsResponseValidationError{
					field:  "Notifications",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNotificationTemplateForBankActionsResponseValidationError{
					field:  "Notifications",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNotifications()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNotificationTemplateForBankActionsResponseValidationError{
				field:  "Notifications",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNotificationTemplateForBankActionsResponseMultiError(errors)
	}

	return nil
}

// GetNotificationTemplateForBankActionsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetNotificationTemplateForBankActionsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetNotificationTemplateForBankActionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNotificationTemplateForBankActionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNotificationTemplateForBankActionsResponseMultiError) AllErrors() []error { return m }

// GetNotificationTemplateForBankActionsResponseValidationError is the
// validation error returned by
// GetNotificationTemplateForBankActionsResponse.Validate if the designated
// constraints aren't met.
type GetNotificationTemplateForBankActionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNotificationTemplateForBankActionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNotificationTemplateForBankActionsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetNotificationTemplateForBankActionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNotificationTemplateForBankActionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNotificationTemplateForBankActionsResponseValidationError) ErrorName() string {
	return "GetNotificationTemplateForBankActionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNotificationTemplateForBankActionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNotificationTemplateForBankActionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNotificationTemplateForBankActionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNotificationTemplateForBankActionsResponseValidationError{}

// Validate checks the field values on AppAccessUpdateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AppAccessUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppAccessUpdateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AppAccessUpdateRequestMultiError, or nil if none found.
func (m *AppAccessUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AppAccessUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AppAccessUpdateRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AppAccessUpdateRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AppAccessUpdateRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := AppAccessUpdateRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _AppAccessUpdateRequest_Action_NotInLookup[m.GetAction()]; ok {
		err := AppAccessUpdateRequestValidationError{
			field:  "Action",
			reason: "value must not be in list [ACTION_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AppAccessUpdateRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AppAccessUpdateRequestValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AppAccessUpdateRequestValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InitiatedBy

	if len(errors) > 0 {
		return AppAccessUpdateRequestMultiError(errors)
	}

	return nil
}

// AppAccessUpdateRequestMultiError is an error wrapping multiple validation
// errors returned by AppAccessUpdateRequest.ValidateAll() if the designated
// constraints aren't met.
type AppAccessUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppAccessUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppAccessUpdateRequestMultiError) AllErrors() []error { return m }

// AppAccessUpdateRequestValidationError is the validation error returned by
// AppAccessUpdateRequest.Validate if the designated constraints aren't met.
type AppAccessUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppAccessUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppAccessUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppAccessUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppAccessUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppAccessUpdateRequestValidationError) ErrorName() string {
	return "AppAccessUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AppAccessUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppAccessUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppAccessUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppAccessUpdateRequestValidationError{}

var _AppAccessUpdateRequest_Action_NotInLookup = map[enums.Action]struct{}{
	0: {},
}

// Validate checks the field values on AppAccessUpdateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AppAccessUpdateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppAccessUpdateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AppAccessUpdateResponseMultiError, or nil if none found.
func (m *AppAccessUpdateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AppAccessUpdateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AppAccessUpdateResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AppAccessUpdateResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AppAccessUpdateResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AppAccessUpdateResponseMultiError(errors)
	}

	return nil
}

// AppAccessUpdateResponseMultiError is an error wrapping multiple validation
// errors returned by AppAccessUpdateResponse.ValidateAll() if the designated
// constraints aren't met.
type AppAccessUpdateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppAccessUpdateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppAccessUpdateResponseMultiError) AllErrors() []error { return m }

// AppAccessUpdateResponseValidationError is the validation error returned by
// AppAccessUpdateResponse.Validate if the designated constraints aren't met.
type AppAccessUpdateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppAccessUpdateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppAccessUpdateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppAccessUpdateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppAccessUpdateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppAccessUpdateResponseValidationError) ErrorName() string {
	return "AppAccessUpdateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AppAccessUpdateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppAccessUpdateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppAccessUpdateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppAccessUpdateResponseValidationError{}

// Validate checks the field values on CreateAsyncAlertsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAsyncAlertsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAsyncAlertsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAsyncAlertsRequestMultiError, or nil if none found.
func (m *CreateAsyncAlertsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAsyncAlertsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAsyncAlertsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAsyncAlertsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAsyncAlertsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRawAlerts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateAsyncAlertsRequestValidationError{
						field:  fmt.Sprintf("RawAlerts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateAsyncAlertsRequestValidationError{
						field:  fmt.Sprintf("RawAlerts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateAsyncAlertsRequestValidationError{
					field:  fmt.Sprintf("RawAlerts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateAsyncAlertsRequestMultiError(errors)
	}

	return nil
}

// CreateAsyncAlertsRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAsyncAlertsRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAsyncAlertsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAsyncAlertsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAsyncAlertsRequestMultiError) AllErrors() []error { return m }

// CreateAsyncAlertsRequestValidationError is the validation error returned by
// CreateAsyncAlertsRequest.Validate if the designated constraints aren't met.
type CreateAsyncAlertsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAsyncAlertsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAsyncAlertsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAsyncAlertsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAsyncAlertsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAsyncAlertsRequestValidationError) ErrorName() string {
	return "CreateAsyncAlertsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAsyncAlertsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAsyncAlertsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAsyncAlertsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAsyncAlertsRequestValidationError{}

// Validate checks the field values on CreateAsyncAlertsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAsyncAlertsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAsyncAlertsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAsyncAlertsResponseMultiError, or nil if none found.
func (m *CreateAsyncAlertsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAsyncAlertsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAsyncAlertsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAsyncAlertsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAsyncAlertsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureCount

	for idx, item := range m.GetFailures() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreateAsyncAlertsResponseValidationError{
						field:  fmt.Sprintf("Failures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreateAsyncAlertsResponseValidationError{
						field:  fmt.Sprintf("Failures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreateAsyncAlertsResponseValidationError{
					field:  fmt.Sprintf("Failures[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CreateAsyncAlertsResponseMultiError(errors)
	}

	return nil
}

// CreateAsyncAlertsResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAsyncAlertsResponse.ValidateAll() if the
// designated constraints aren't met.
type CreateAsyncAlertsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAsyncAlertsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAsyncAlertsResponseMultiError) AllErrors() []error { return m }

// CreateAsyncAlertsResponseValidationError is the validation error returned by
// CreateAsyncAlertsResponse.Validate if the designated constraints aren't met.
type CreateAsyncAlertsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAsyncAlertsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAsyncAlertsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAsyncAlertsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAsyncAlertsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAsyncAlertsResponseValidationError) ErrorName() string {
	return "CreateAsyncAlertsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAsyncAlertsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAsyncAlertsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAsyncAlertsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAsyncAlertsResponseValidationError{}

// Validate checks the field values on GetUnifiedLEACommsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUnifiedLEACommsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUnifiedLEACommsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUnifiedLEACommsRequestMultiError, or nil if none found.
func (m *GetUnifiedLEACommsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUnifiedLEACommsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUnifiedLEACommsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUnifiedLEACommsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUnifiedLEACommsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHandlingParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUnifiedLEACommsRequestValidationError{
					field:  "HandlingParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUnifiedLEACommsRequestValidationError{
					field:  "HandlingParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHandlingParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUnifiedLEACommsRequestValidationError{
				field:  "HandlingParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsReminder

	if len(errors) > 0 {
		return GetUnifiedLEACommsRequestMultiError(errors)
	}

	return nil
}

// GetUnifiedLEACommsRequestMultiError is an error wrapping multiple validation
// errors returned by GetUnifiedLEACommsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetUnifiedLEACommsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUnifiedLEACommsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUnifiedLEACommsRequestMultiError) AllErrors() []error { return m }

// GetUnifiedLEACommsRequestValidationError is the validation error returned by
// GetUnifiedLEACommsRequest.Validate if the designated constraints aren't met.
type GetUnifiedLEACommsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUnifiedLEACommsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUnifiedLEACommsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUnifiedLEACommsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUnifiedLEACommsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUnifiedLEACommsRequestValidationError) ErrorName() string {
	return "GetUnifiedLEACommsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUnifiedLEACommsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUnifiedLEACommsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUnifiedLEACommsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUnifiedLEACommsRequestValidationError{}

// Validate checks the field values on GetUnifiedLEACommsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUnifiedLEACommsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUnifiedLEACommsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUnifiedLEACommsResponseMultiError, or nil if none found.
func (m *GetUnifiedLEACommsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUnifiedLEACommsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUnifiedLEACommsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUnifiedLEACommsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUnifiedLEACommsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCommunications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetUnifiedLEACommsResponseValidationError{
						field:  fmt.Sprintf("Communications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetUnifiedLEACommsResponseValidationError{
						field:  fmt.Sprintf("Communications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetUnifiedLEACommsResponseValidationError{
					field:  fmt.Sprintf("Communications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetUnifiedLEACommsResponseMultiError(errors)
	}

	return nil
}

// GetUnifiedLEACommsResponseMultiError is an error wrapping multiple
// validation errors returned by GetUnifiedLEACommsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetUnifiedLEACommsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUnifiedLEACommsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUnifiedLEACommsResponseMultiError) AllErrors() []error { return m }

// GetUnifiedLEACommsResponseValidationError is the validation error returned
// by GetUnifiedLEACommsResponse.Validate if the designated constraints aren't met.
type GetUnifiedLEACommsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUnifiedLEACommsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUnifiedLEACommsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUnifiedLEACommsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUnifiedLEACommsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUnifiedLEACommsResponseValidationError) ErrorName() string {
	return "GetUnifiedLEACommsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUnifiedLEACommsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUnifiedLEACommsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUnifiedLEACommsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUnifiedLEACommsResponseValidationError{}

// Validate checks the field values on ApplyLienRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ApplyLienRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplyLienRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplyLienRequestMultiError, or nil if none found.
func (m *ApplyLienRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplyLienRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyLienRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	// no validation rules for Amount

	// no validation rules for CurrencyCode

	// no validation rules for ReasonCode

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyLienRequestValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyLienRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyLienRequestValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplyLienRequestMultiError(errors)
	}

	return nil
}

// ApplyLienRequestMultiError is an error wrapping multiple validation errors
// returned by ApplyLienRequest.ValidateAll() if the designated constraints
// aren't met.
type ApplyLienRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplyLienRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplyLienRequestMultiError) AllErrors() []error { return m }

// ApplyLienRequestValidationError is the validation error returned by
// ApplyLienRequest.Validate if the designated constraints aren't met.
type ApplyLienRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplyLienRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplyLienRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplyLienRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplyLienRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplyLienRequestValidationError) ErrorName() string { return "ApplyLienRequestValidationError" }

// Error satisfies the builtin error interface
func (e ApplyLienRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplyLienRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplyLienRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplyLienRequestValidationError{}

// Validate checks the field values on ApplyLienResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ApplyLienResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplyLienResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplyLienResponseMultiError, or nil if none found.
func (m *ApplyLienResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplyLienResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyLienResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplyLienResponseMultiError(errors)
	}

	return nil
}

// ApplyLienResponseMultiError is an error wrapping multiple validation errors
// returned by ApplyLienResponse.ValidateAll() if the designated constraints
// aren't met.
type ApplyLienResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplyLienResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplyLienResponseMultiError) AllErrors() []error { return m }

// ApplyLienResponseValidationError is the validation error returned by
// ApplyLienResponse.Validate if the designated constraints aren't met.
type ApplyLienResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplyLienResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplyLienResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplyLienResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplyLienResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplyLienResponseValidationError) ErrorName() string {
	return "ApplyLienResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ApplyLienResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplyLienResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplyLienResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplyLienResponseValidationError{}

// Validate checks the field values on AddLienRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddLienRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddLienRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddLienRequestMultiError,
// or nil if none found.
func (m *AddLienRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddLienRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddLienRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	// no validation rules for Amount

	// no validation rules for CurrencyCode

	// no validation rules for ReasonCode

	// no validation rules for Remarks

	// no validation rules for LienDurationInHours

	// no validation rules for ChannelRequestId

	// no validation rules for BankActionId

	if len(errors) > 0 {
		return AddLienRequestMultiError(errors)
	}

	return nil
}

// AddLienRequestMultiError is an error wrapping multiple validation errors
// returned by AddLienRequest.ValidateAll() if the designated constraints
// aren't met.
type AddLienRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddLienRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddLienRequestMultiError) AllErrors() []error { return m }

// AddLienRequestValidationError is the validation error returned by
// AddLienRequest.Validate if the designated constraints aren't met.
type AddLienRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddLienRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddLienRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddLienRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddLienRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddLienRequestValidationError) ErrorName() string { return "AddLienRequestValidationError" }

// Error satisfies the builtin error interface
func (e AddLienRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddLienRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddLienRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddLienRequestValidationError{}

// Validate checks the field values on AddLienResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddLienResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddLienResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddLienResponseMultiError, or nil if none found.
func (m *AddLienResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddLienResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddLienResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddLienResponseMultiError(errors)
	}

	return nil
}

// AddLienResponseMultiError is an error wrapping multiple validation errors
// returned by AddLienResponse.ValidateAll() if the designated constraints
// aren't met.
type AddLienResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddLienResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddLienResponseMultiError) AllErrors() []error { return m }

// AddLienResponseValidationError is the validation error returned by
// AddLienResponse.Validate if the designated constraints aren't met.
type AddLienResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddLienResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddLienResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddLienResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddLienResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddLienResponseValidationError) ErrorName() string { return "AddLienResponseValidationError" }

// Error satisfies the builtin error interface
func (e AddLienResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddLienResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddLienResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddLienResponseValidationError{}

// Validate checks the field values on VerifyLienRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyLienRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyLienRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyLienRequestMultiError, or nil if none found.
func (m *VerifyLienRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyLienRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyLienRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	// no validation rules for RequestId

	if len(errors) > 0 {
		return VerifyLienRequestMultiError(errors)
	}

	return nil
}

// VerifyLienRequestMultiError is an error wrapping multiple validation errors
// returned by VerifyLienRequest.ValidateAll() if the designated constraints
// aren't met.
type VerifyLienRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyLienRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyLienRequestMultiError) AllErrors() []error { return m }

// VerifyLienRequestValidationError is the validation error returned by
// VerifyLienRequest.Validate if the designated constraints aren't met.
type VerifyLienRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyLienRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyLienRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyLienRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyLienRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyLienRequestValidationError) ErrorName() string {
	return "VerifyLienRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyLienRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyLienRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyLienRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyLienRequestValidationError{}

// Validate checks the field values on VerifyLienResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyLienResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyLienResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyLienResponseMultiError, or nil if none found.
func (m *VerifyLienResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyLienResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyLienResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyLienResponseMultiError(errors)
	}

	return nil
}

// VerifyLienResponseMultiError is an error wrapping multiple validation errors
// returned by VerifyLienResponse.ValidateAll() if the designated constraints
// aren't met.
type VerifyLienResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyLienResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyLienResponseMultiError) AllErrors() []error { return m }

// VerifyLienResponseValidationError is the validation error returned by
// VerifyLienResponse.Validate if the designated constraints aren't met.
type VerifyLienResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyLienResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyLienResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyLienResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyLienResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyLienResponseValidationError) ErrorName() string {
	return "VerifyLienResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyLienResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyLienResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyLienResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyLienResponseValidationError{}

// Validate checks the field values on CheckForExistingLienRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckForExistingLienRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckForExistingLienRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckForExistingLienRequestMultiError, or nil if none found.
func (m *CheckForExistingLienRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckForExistingLienRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckForExistingLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckForExistingLienRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckForExistingLienRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	if len(errors) > 0 {
		return CheckForExistingLienRequestMultiError(errors)
	}

	return nil
}

// CheckForExistingLienRequestMultiError is an error wrapping multiple
// validation errors returned by CheckForExistingLienRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckForExistingLienRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckForExistingLienRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckForExistingLienRequestMultiError) AllErrors() []error { return m }

// CheckForExistingLienRequestValidationError is the validation error returned
// by CheckForExistingLienRequest.Validate if the designated constraints
// aren't met.
type CheckForExistingLienRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckForExistingLienRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckForExistingLienRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckForExistingLienRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckForExistingLienRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckForExistingLienRequestValidationError) ErrorName() string {
	return "CheckForExistingLienRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckForExistingLienRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckForExistingLienRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckForExistingLienRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckForExistingLienRequestValidationError{}

// Validate checks the field values on CheckForExistingLienResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckForExistingLienResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckForExistingLienResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckForExistingLienResponseMultiError, or nil if none found.
func (m *CheckForExistingLienResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckForExistingLienResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckForExistingLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckForExistingLienResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckForExistingLienResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HasExistingLien

	if len(errors) > 0 {
		return CheckForExistingLienResponseMultiError(errors)
	}

	return nil
}

// CheckForExistingLienResponseMultiError is an error wrapping multiple
// validation errors returned by CheckForExistingLienResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckForExistingLienResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckForExistingLienResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckForExistingLienResponseMultiError) AllErrors() []error { return m }

// CheckForExistingLienResponseValidationError is the validation error returned
// by CheckForExistingLienResponse.Validate if the designated constraints
// aren't met.
type CheckForExistingLienResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckForExistingLienResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckForExistingLienResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckForExistingLienResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckForExistingLienResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckForExistingLienResponseValidationError) ErrorName() string {
	return "CheckForExistingLienResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckForExistingLienResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckForExistingLienResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckForExistingLienResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckForExistingLienResponseValidationError{}

// Validate checks the field values on GetCommsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCommsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommsRequestMultiError, or nil if none found.
func (m *GetCommsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommsRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountNumber

	if all {
		switch v := interface{}(m.GetStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "StartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommsRequestValidationError{
				field:  "StartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "EndDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommsRequestValidationError{
				field:  "EndDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommsRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCommsRequestMultiError(errors)
	}

	return nil
}

// GetCommsRequestMultiError is an error wrapping multiple validation errors
// returned by GetCommsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetCommsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommsRequestMultiError) AllErrors() []error { return m }

// GetCommsRequestValidationError is the validation error returned by
// GetCommsRequest.Validate if the designated constraints aren't met.
type GetCommsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommsRequestValidationError) ErrorName() string { return "GetCommsRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetCommsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommsRequestValidationError{}

// Validate checks the field values on GetCommsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCommsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCommsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCommsResponseMultiError, or nil if none found.
func (m *GetCommsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCommsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCommsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCommsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCommsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCommunications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCommsResponseValidationError{
						field:  fmt.Sprintf("Communications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCommsResponseValidationError{
						field:  fmt.Sprintf("Communications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCommsResponseValidationError{
					field:  fmt.Sprintf("Communications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for EntityId

	if len(errors) > 0 {
		return GetCommsResponseMultiError(errors)
	}

	return nil
}

// GetCommsResponseMultiError is an error wrapping multiple validation errors
// returned by GetCommsResponse.ValidateAll() if the designated constraints
// aren't met.
type GetCommsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCommsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCommsResponseMultiError) AllErrors() []error { return m }

// GetCommsResponseValidationError is the validation error returned by
// GetCommsResponse.Validate if the designated constraints aren't met.
type GetCommsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCommsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCommsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCommsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCommsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCommsResponseValidationError) ErrorName() string { return "GetCommsResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetCommsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCommsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCommsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCommsResponseValidationError{}

// Validate checks the field values on UpdateBankActionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBankActionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBankActionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBankActionRequestMultiError, or nil if none found.
func (m *UpdateBankActionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBankActionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBankActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBankActionRequestValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBankActionRequestValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	// no validation rules for State

	if len(errors) > 0 {
		return UpdateBankActionRequestMultiError(errors)
	}

	return nil
}

// UpdateBankActionRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateBankActionRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateBankActionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBankActionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBankActionRequestMultiError) AllErrors() []error { return m }

// UpdateBankActionRequestValidationError is the validation error returned by
// UpdateBankActionRequest.Validate if the designated constraints aren't met.
type UpdateBankActionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBankActionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBankActionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBankActionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBankActionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBankActionRequestValidationError) ErrorName() string {
	return "UpdateBankActionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBankActionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBankActionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBankActionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBankActionRequestValidationError{}

// Validate checks the field values on UpdateBankActionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBankActionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBankActionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBankActionResponseMultiError, or nil if none found.
func (m *UpdateBankActionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBankActionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBankActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBankActionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBankActionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateBankActionResponseMultiError(errors)
	}

	return nil
}

// UpdateBankActionResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateBankActionResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateBankActionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBankActionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBankActionResponseMultiError) AllErrors() []error { return m }

// UpdateBankActionResponseValidationError is the validation error returned by
// UpdateBankActionResponse.Validate if the designated constraints aren't met.
type UpdateBankActionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBankActionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBankActionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBankActionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBankActionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBankActionResponseValidationError) ErrorName() string {
	return "UpdateBankActionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBankActionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBankActionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBankActionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBankActionResponseValidationError{}

// Validate checks the field values on CreateAsyncAlertsResponse_Failure with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateAsyncAlertsResponse_Failure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAsyncAlertsResponse_Failure
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateAsyncAlertsResponse_FailureMultiError, or nil if none found.
func (m *CreateAsyncAlertsResponse_Failure) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAsyncAlertsResponse_Failure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAlert()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAsyncAlertsResponse_FailureValidationError{
					field:  "Alert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAsyncAlertsResponse_FailureValidationError{
					field:  "Alert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAlert()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAsyncAlertsResponse_FailureValidationError{
				field:  "Alert",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Reason

	if len(errors) > 0 {
		return CreateAsyncAlertsResponse_FailureMultiError(errors)
	}

	return nil
}

// CreateAsyncAlertsResponse_FailureMultiError is an error wrapping multiple
// validation errors returned by
// CreateAsyncAlertsResponse_Failure.ValidateAll() if the designated
// constraints aren't met.
type CreateAsyncAlertsResponse_FailureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAsyncAlertsResponse_FailureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAsyncAlertsResponse_FailureMultiError) AllErrors() []error { return m }

// CreateAsyncAlertsResponse_FailureValidationError is the validation error
// returned by CreateAsyncAlertsResponse_Failure.Validate if the designated
// constraints aren't met.
type CreateAsyncAlertsResponse_FailureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAsyncAlertsResponse_FailureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAsyncAlertsResponse_FailureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAsyncAlertsResponse_FailureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAsyncAlertsResponse_FailureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAsyncAlertsResponse_FailureValidationError) ErrorName() string {
	return "CreateAsyncAlertsResponse_FailureValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAsyncAlertsResponse_FailureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAsyncAlertsResponse_Failure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAsyncAlertsResponse_FailureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAsyncAlertsResponse_FailureValidationError{}
