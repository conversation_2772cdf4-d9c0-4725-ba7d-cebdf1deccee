// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/bankactions/risk_bank_actions.proto

package risk

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	accounts "github.com/epifi/gamma/api/accounts"
	enums "github.com/epifi/gamma/api/risk/enums"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RiskBankActionsFieldMask is the enum representation of all the RiskBankActions fields.
// Meant to be used as field mask to help with database updates
type RiskBankActionsFieldMask int32

const (
	RiskBankActionsFieldMask_RISK_BANK_ACTIONS_UNSPECIFIED RiskBankActionsFieldMask = 0
	RiskBankActionsFieldMask_ID                            RiskBankActionsFieldMask = 1
	RiskBankActionsFieldMask_CLIENT_REQ_ID                 RiskBankActionsFieldMask = 2
	RiskBankActionsFieldMask_ACTOR_ID                      RiskBankActionsFieldMask = 3
	RiskBankActionsFieldMask_ACCOUNT_ID                    RiskBankActionsFieldMask = 4
	RiskBankActionsFieldMask_ACCOUNT_TYPE                  RiskBankActionsFieldMask = 5
	RiskBankActionsFieldMask_VENDOR                        RiskBankActionsFieldMask = 6
	RiskBankActionsFieldMask_ACTION                        RiskBankActionsFieldMask = 7
	RiskBankActionsFieldMask_STATE                         RiskBankActionsFieldMask = 8
	RiskBankActionsFieldMask_REQUEST_REASON                RiskBankActionsFieldMask = 9
	RiskBankActionsFieldMask_FAILURE_REASON                RiskBankActionsFieldMask = 10
	RiskBankActionsFieldMask_BANK_ACTION_REASON            RiskBankActionsFieldMask = 11
	RiskBankActionsFieldMask_PROVENANCE                    RiskBankActionsFieldMask = 12
	RiskBankActionsFieldMask_CREATED_AT                    RiskBankActionsFieldMask = 13
	RiskBankActionsFieldMask_UPDATED_AT                    RiskBankActionsFieldMask = 14
	RiskBankActionsFieldMask_DELETED_AT                    RiskBankActionsFieldMask = 15
	RiskBankActionsFieldMask_ALL                           RiskBankActionsFieldMask = 16
	RiskBankActionsFieldMask_COMMS_TEMPLATE                RiskBankActionsFieldMask = 17
	RiskBankActionsFieldMask_IS_RECON                      RiskBankActionsFieldMask = 18
	RiskBankActionsFieldMask_BANK_ACTION_DATE              RiskBankActionsFieldMask = 19
	RiskBankActionsFieldMask_LIEN_REQUEST_ID               RiskBankActionsFieldMask = 20
)

// Enum value maps for RiskBankActionsFieldMask.
var (
	RiskBankActionsFieldMask_name = map[int32]string{
		0:  "RISK_BANK_ACTIONS_UNSPECIFIED",
		1:  "ID",
		2:  "CLIENT_REQ_ID",
		3:  "ACTOR_ID",
		4:  "ACCOUNT_ID",
		5:  "ACCOUNT_TYPE",
		6:  "VENDOR",
		7:  "ACTION",
		8:  "STATE",
		9:  "REQUEST_REASON",
		10: "FAILURE_REASON",
		11: "BANK_ACTION_REASON",
		12: "PROVENANCE",
		13: "CREATED_AT",
		14: "UPDATED_AT",
		15: "DELETED_AT",
		16: "ALL",
		17: "COMMS_TEMPLATE",
		18: "IS_RECON",
		19: "BANK_ACTION_DATE",
		20: "LIEN_REQUEST_ID",
	}
	RiskBankActionsFieldMask_value = map[string]int32{
		"RISK_BANK_ACTIONS_UNSPECIFIED": 0,
		"ID":                            1,
		"CLIENT_REQ_ID":                 2,
		"ACTOR_ID":                      3,
		"ACCOUNT_ID":                    4,
		"ACCOUNT_TYPE":                  5,
		"VENDOR":                        6,
		"ACTION":                        7,
		"STATE":                         8,
		"REQUEST_REASON":                9,
		"FAILURE_REASON":                10,
		"BANK_ACTION_REASON":            11,
		"PROVENANCE":                    12,
		"CREATED_AT":                    13,
		"UPDATED_AT":                    14,
		"DELETED_AT":                    15,
		"ALL":                           16,
		"COMMS_TEMPLATE":                17,
		"IS_RECON":                      18,
		"BANK_ACTION_DATE":              19,
		"LIEN_REQUEST_ID":               20,
	}
)

func (x RiskBankActionsFieldMask) Enum() *RiskBankActionsFieldMask {
	p := new(RiskBankActionsFieldMask)
	*p = x
	return p
}

func (x RiskBankActionsFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RiskBankActionsFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_risk_bankactions_risk_bank_actions_proto_enumTypes[0].Descriptor()
}

func (RiskBankActionsFieldMask) Type() protoreflect.EnumType {
	return &file_api_risk_bankactions_risk_bank_actions_proto_enumTypes[0]
}

func (x RiskBankActionsFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RiskBankActionsFieldMask.Descriptor instead.
func (RiskBankActionsFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_risk_bankactions_risk_bank_actions_proto_rawDescGZIP(), []int{0}
}

// Reason for which request was entered onto our system
type RequestReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies reason for which action is being taken
	Reason enums.RequestReason `protobuf:"varint,1,opt,name=reason,proto3,enum=enums.RequestReason" json:"reason,omitempty"`
	// remarks should be non empty if reason is other
	Remarks string `protobuf:"bytes,2,opt,name=remarks,proto3" json:"remarks,omitempty"`
}

func (x *RequestReason) Reset() {
	*x = RequestReason{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bankactions_risk_bank_actions_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RequestReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestReason) ProtoMessage() {}

func (x *RequestReason) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bankactions_risk_bank_actions_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestReason.ProtoReflect.Descriptor instead.
func (*RequestReason) Descriptor() ([]byte, []int) {
	return file_api_risk_bankactions_risk_bank_actions_proto_rawDescGZIP(), []int{0}
}

func (x *RequestReason) GetReason() enums.RequestReason {
	if x != nil {
		return x.Reason
	}
	return enums.RequestReason(0)
}

func (x *RequestReason) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

// Account status with reason received from bank api
// Federal API Doc : https://docs.google.com/document/d/1QCFihSNkV2hwpjewV-4sXLRzY2MCE42r/edit#bookmark=id.im8dvgfgq2uv
type BankActionReason struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies reason for which action is being taken eg: 52:
	Status string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// reason for above status eg: NEO BANKING- LIVENESS CHECK FAILURE
	Reason string `protobuf:"bytes,2,opt,name=reason,proto3" json:"reason,omitempty"`
}

func (x *BankActionReason) Reset() {
	*x = BankActionReason{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bankactions_risk_bank_actions_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankActionReason) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankActionReason) ProtoMessage() {}

func (x *BankActionReason) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bankactions_risk_bank_actions_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankActionReason.ProtoReflect.Descriptor instead.
func (*BankActionReason) Descriptor() ([]byte, []int) {
	return file_api_risk_bankactions_risk_bank_actions_proto_rawDescGZIP(), []int{1}
}

func (x *BankActionReason) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *BankActionReason) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type RiskBankActions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Client request id corresponding to the celestial workflow
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	ActorId     string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// account onto consideration
	AccountId string `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// type of account on which action is taken eg: Savings, credit-card
	AccountType accounts.Type `protobuf:"varint,5,opt,name=account_type,json=accountType,proto3,enum=accounts.Type" json:"account_type,omitempty"`
	// Vendor - bank identifier/ action executing authority eg: Federal for account block/unblock
	Vendor vendorgateway.Vendor `protobuf:"varint,6,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	// Action is the flow current activity leads to
	Action enums.Action `protobuf:"varint,7,opt,name=action,proto3,enum=enums.Action" json:"action,omitempty"`
	// State will determine what's the current user position is in the workflow for that Action
	State         enums.State    `protobuf:"varint,8,opt,name=state,proto3,enum=enums.State" json:"state,omitempty"`
	RequestReason *RequestReason `protobuf:"bytes,9,opt,name=request_reason,json=requestReason,proto3" json:"request_reason,omitempty"`
	// exceptions that can occur in workflow
	// states reason why overall state machine failed
	FailureReason enums.FailureReason `protobuf:"varint,10,opt,name=failure_reason,json=failureReason,proto3,enum=enums.FailureReason" json:"failure_reason,omitempty"`
	// Account status with reason received from bank api
	BankActionReason *BankActionReason `protobuf:"bytes,11,opt,name=bank_action_reason,json=bankActionReason,proto3" json:"bank_action_reason,omitempty"`
	// Provenance: the beginning of something's existence; something's origin.
	// e.g. FI (FI initiated), BANK (BANK initiated), LEA (Law enforcement agency initiated)
	// This enum represents different entry provenance of request in the system
	Provenance enums.Provenance       `protobuf:"varint,12,opt,name=provenance,proto3,enum=enums.Provenance" json:"provenance,omitempty"`
	CreatedAt  *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt  *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// Array of strings for comms trigger
	// Will support multiple types of comms
	CommsTemplate []enums.CommsTemplate `protobuf:"varint,16,rep,packed,name=comms_template,json=commsTemplate,proto3,enum=enums.CommsTemplate" json:"comms_template,omitempty"`
	// based on if an entry is recon we might have separate handling like
	// not de-duping the entry, not sending to bank etc.
	IsRecon common.BooleanEnum `protobuf:"varint,17,opt,name=is_recon,json=isRecon,proto3,enum=api.typesv2.common.BooleanEnum" json:"is_recon,omitempty"`
	// date at which action vendor takes action on this case
	// data can be populated manually, by API response
	// or can be null if no info is present
	BankActionDate *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=bank_action_date,json=bankActionDate,proto3" json:"bank_action_date,omitempty"`
	// id of the lien request which was used to add lien for this lien action, will only be present in case of lien action
	LienRequestId string `protobuf:"bytes,19,opt,name=lien_request_id,json=lienRequestId,proto3" json:"lien_request_id,omitempty"`
}

func (x *RiskBankActions) Reset() {
	*x = RiskBankActions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_bankactions_risk_bank_actions_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskBankActions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskBankActions) ProtoMessage() {}

func (x *RiskBankActions) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_bankactions_risk_bank_actions_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskBankActions.ProtoReflect.Descriptor instead.
func (*RiskBankActions) Descriptor() ([]byte, []int) {
	return file_api_risk_bankactions_risk_bank_actions_proto_rawDescGZIP(), []int{2}
}

func (x *RiskBankActions) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *RiskBankActions) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *RiskBankActions) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *RiskBankActions) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *RiskBankActions) GetAccountType() accounts.Type {
	if x != nil {
		return x.AccountType
	}
	return accounts.Type(0)
}

func (x *RiskBankActions) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *RiskBankActions) GetAction() enums.Action {
	if x != nil {
		return x.Action
	}
	return enums.Action(0)
}

func (x *RiskBankActions) GetState() enums.State {
	if x != nil {
		return x.State
	}
	return enums.State(0)
}

func (x *RiskBankActions) GetRequestReason() *RequestReason {
	if x != nil {
		return x.RequestReason
	}
	return nil
}

func (x *RiskBankActions) GetFailureReason() enums.FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return enums.FailureReason(0)
}

func (x *RiskBankActions) GetBankActionReason() *BankActionReason {
	if x != nil {
		return x.BankActionReason
	}
	return nil
}

func (x *RiskBankActions) GetProvenance() enums.Provenance {
	if x != nil {
		return x.Provenance
	}
	return enums.Provenance(0)
}

func (x *RiskBankActions) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *RiskBankActions) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *RiskBankActions) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *RiskBankActions) GetCommsTemplate() []enums.CommsTemplate {
	if x != nil {
		return x.CommsTemplate
	}
	return nil
}

func (x *RiskBankActions) GetIsRecon() common.BooleanEnum {
	if x != nil {
		return x.IsRecon
	}
	return common.BooleanEnum(0)
}

func (x *RiskBankActions) GetBankActionDate() *timestamppb.Timestamp {
	if x != nil {
		return x.BankActionDate
	}
	return nil
}

func (x *RiskBankActions) GetLienRequestId() string {
	if x != nil {
		return x.LienRequestId
	}
	return ""
}

var File_api_risk_bankactions_risk_bank_actions_proto protoreflect.FileDescriptor

var file_api_risk_bankactions_risk_bank_actions_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x62, 0x61, 0x6e, 0x6b,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x04,
	0x72, 0x69, 0x73, 0x6b, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x57, 0x0a, 0x0d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x2c, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x22, 0x42, 0x0a,
	0x10, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x22, 0xb6, 0x07, 0x0a, 0x0f, 0x52, 0x69, 0x73, 0x6b, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0e, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x25, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0d, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0c, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x3a, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0e,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x46, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x12, 0x62, 0x61, 0x6e,
	0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x42, 0x61, 0x6e,
	0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x10, 0x62,
	0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x31, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x76,
	0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x5f, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x6d, 0x73, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x12, 0x3a, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6e, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45,
	0x6e, 0x75, 0x6d, 0x52, 0x07, 0x69, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x6e, 0x12, 0x44, 0x0a, 0x10,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0e, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x69, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x2a, 0x81, 0x03, 0x0a, 0x18, 0x52,
	0x69, 0x73, 0x6b, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x21, 0x0a, 0x1d, 0x52, 0x49, 0x53, 0x4b, 0x5f,
	0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x44,
	0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51,
	0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49,
	0x44, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x49,
	0x44, 0x10, 0x04, 0x12, 0x10, 0x0a, 0x0c, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10,
	0x06, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x07, 0x12, 0x09, 0x0a,
	0x05, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x51, 0x55,
	0x45, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x09, 0x12, 0x12, 0x0a, 0x0e,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x0a,
	0x12, 0x16, 0x0a, 0x12, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x0c, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0d, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0e, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x45, 0x4c, 0x45,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x0f, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c, 0x4c, 0x10,
	0x10, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c,
	0x41, 0x54, 0x45, 0x10, 0x11, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x53, 0x5f, 0x52, 0x45, 0x43, 0x4f,
	0x4e, 0x10, 0x12, 0x12, 0x14, 0x0a, 0x10, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x13, 0x12, 0x13, 0x0a, 0x0f, 0x4c, 0x49, 0x45,
	0x4e, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x49, 0x44, 0x10, 0x14, 0x42, 0x42,
	0x0a, 0x1f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x5a, 0x1f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_bankactions_risk_bank_actions_proto_rawDescOnce sync.Once
	file_api_risk_bankactions_risk_bank_actions_proto_rawDescData = file_api_risk_bankactions_risk_bank_actions_proto_rawDesc
)

func file_api_risk_bankactions_risk_bank_actions_proto_rawDescGZIP() []byte {
	file_api_risk_bankactions_risk_bank_actions_proto_rawDescOnce.Do(func() {
		file_api_risk_bankactions_risk_bank_actions_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_bankactions_risk_bank_actions_proto_rawDescData)
	})
	return file_api_risk_bankactions_risk_bank_actions_proto_rawDescData
}

var file_api_risk_bankactions_risk_bank_actions_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_risk_bankactions_risk_bank_actions_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_risk_bankactions_risk_bank_actions_proto_goTypes = []interface{}{
	(RiskBankActionsFieldMask)(0), // 0: risk.RiskBankActionsFieldMask
	(*RequestReason)(nil),         // 1: risk.RequestReason
	(*BankActionReason)(nil),      // 2: risk.BankActionReason
	(*RiskBankActions)(nil),       // 3: risk.RiskBankActions
	(enums.RequestReason)(0),      // 4: enums.RequestReason
	(accounts.Type)(0),            // 5: accounts.Type
	(vendorgateway.Vendor)(0),     // 6: vendorgateway.Vendor
	(enums.Action)(0),             // 7: enums.Action
	(enums.State)(0),              // 8: enums.State
	(enums.FailureReason)(0),      // 9: enums.FailureReason
	(enums.Provenance)(0),         // 10: enums.Provenance
	(*timestamppb.Timestamp)(nil), // 11: google.protobuf.Timestamp
	(enums.CommsTemplate)(0),      // 12: enums.CommsTemplate
	(common.BooleanEnum)(0),       // 13: api.typesv2.common.BooleanEnum
}
var file_api_risk_bankactions_risk_bank_actions_proto_depIdxs = []int32{
	4,  // 0: risk.RequestReason.reason:type_name -> enums.RequestReason
	5,  // 1: risk.RiskBankActions.account_type:type_name -> accounts.Type
	6,  // 2: risk.RiskBankActions.vendor:type_name -> vendorgateway.Vendor
	7,  // 3: risk.RiskBankActions.action:type_name -> enums.Action
	8,  // 4: risk.RiskBankActions.state:type_name -> enums.State
	1,  // 5: risk.RiskBankActions.request_reason:type_name -> risk.RequestReason
	9,  // 6: risk.RiskBankActions.failure_reason:type_name -> enums.FailureReason
	2,  // 7: risk.RiskBankActions.bank_action_reason:type_name -> risk.BankActionReason
	10, // 8: risk.RiskBankActions.provenance:type_name -> enums.Provenance
	11, // 9: risk.RiskBankActions.created_at:type_name -> google.protobuf.Timestamp
	11, // 10: risk.RiskBankActions.updated_at:type_name -> google.protobuf.Timestamp
	11, // 11: risk.RiskBankActions.deleted_at:type_name -> google.protobuf.Timestamp
	12, // 12: risk.RiskBankActions.comms_template:type_name -> enums.CommsTemplate
	13, // 13: risk.RiskBankActions.is_recon:type_name -> api.typesv2.common.BooleanEnum
	11, // 14: risk.RiskBankActions.bank_action_date:type_name -> google.protobuf.Timestamp
	15, // [15:15] is the sub-list for method output_type
	15, // [15:15] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_api_risk_bankactions_risk_bank_actions_proto_init() }
func file_api_risk_bankactions_risk_bank_actions_proto_init() {
	if File_api_risk_bankactions_risk_bank_actions_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_risk_bankactions_risk_bank_actions_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RequestReason); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bankactions_risk_bank_actions_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankActionReason); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_bankactions_risk_bank_actions_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskBankActions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_bankactions_risk_bank_actions_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_risk_bankactions_risk_bank_actions_proto_goTypes,
		DependencyIndexes: file_api_risk_bankactions_risk_bank_actions_proto_depIdxs,
		EnumInfos:         file_api_risk_bankactions_risk_bank_actions_proto_enumTypes,
		MessageInfos:      file_api_risk_bankactions_risk_bank_actions_proto_msgTypes,
	}.Build()
	File_api_risk_bankactions_risk_bank_actions_proto = out.File
	file_api_risk_bankactions_risk_bank_actions_proto_rawDesc = nil
	file_api_risk_bankactions_risk_bank_actions_proto_goTypes = nil
	file_api_risk_bankactions_risk_bank_actions_proto_depIdxs = nil
}
