// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/bankactions/risk_bank_actions.proto

package risk

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	accounts "github.com/epifi/gamma/api/accounts"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/risk/enums"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = accounts.Type(0)

	_ = common.BooleanEnum(0)

	_ = enums.RequestReason(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on RequestReason with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RequestReason) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RequestReason with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RequestReasonMultiError, or
// nil if none found.
func (m *RequestReason) ValidateAll() error {
	return m.validate(true)
}

func (m *RequestReason) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Reason

	// no validation rules for Remarks

	if len(errors) > 0 {
		return RequestReasonMultiError(errors)
	}

	return nil
}

// RequestReasonMultiError is an error wrapping multiple validation errors
// returned by RequestReason.ValidateAll() if the designated constraints
// aren't met.
type RequestReasonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RequestReasonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RequestReasonMultiError) AllErrors() []error { return m }

// RequestReasonValidationError is the validation error returned by
// RequestReason.Validate if the designated constraints aren't met.
type RequestReasonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RequestReasonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RequestReasonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RequestReasonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RequestReasonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RequestReasonValidationError) ErrorName() string { return "RequestReasonValidationError" }

// Error satisfies the builtin error interface
func (e RequestReasonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRequestReason.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RequestReasonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RequestReasonValidationError{}

// Validate checks the field values on BankActionReason with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BankActionReason) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankActionReason with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankActionReasonMultiError, or nil if none found.
func (m *BankActionReason) ValidateAll() error {
	return m.validate(true)
}

func (m *BankActionReason) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	// no validation rules for Reason

	if len(errors) > 0 {
		return BankActionReasonMultiError(errors)
	}

	return nil
}

// BankActionReasonMultiError is an error wrapping multiple validation errors
// returned by BankActionReason.ValidateAll() if the designated constraints
// aren't met.
type BankActionReasonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankActionReasonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankActionReasonMultiError) AllErrors() []error { return m }

// BankActionReasonValidationError is the validation error returned by
// BankActionReason.Validate if the designated constraints aren't met.
type BankActionReasonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankActionReasonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankActionReasonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankActionReasonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankActionReasonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankActionReasonValidationError) ErrorName() string { return "BankActionReasonValidationError" }

// Error satisfies the builtin error interface
func (e BankActionReasonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankActionReason.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankActionReasonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankActionReasonValidationError{}

// Validate checks the field values on RiskBankActions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *RiskBankActions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskBankActions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskBankActionsMultiError, or nil if none found.
func (m *RiskBankActions) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskBankActions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ClientReqId

	// no validation rules for ActorId

	// no validation rules for AccountId

	// no validation rules for AccountType

	// no validation rules for Vendor

	// no validation rules for Action

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetRequestReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "RequestReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskBankActionsValidationError{
				field:  "RequestReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetBankActionReason()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "BankActionReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "BankActionReason",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankActionReason()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskBankActionsValidationError{
				field:  "BankActionReason",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provenance

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskBankActionsValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskBankActionsValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskBankActionsValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsRecon

	if all {
		switch v := interface{}(m.GetBankActionDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "BankActionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskBankActionsValidationError{
					field:  "BankActionDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankActionDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskBankActionsValidationError{
				field:  "BankActionDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LienRequestId

	if len(errors) > 0 {
		return RiskBankActionsMultiError(errors)
	}

	return nil
}

// RiskBankActionsMultiError is an error wrapping multiple validation errors
// returned by RiskBankActions.ValidateAll() if the designated constraints
// aren't met.
type RiskBankActionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskBankActionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskBankActionsMultiError) AllErrors() []error { return m }

// RiskBankActionsValidationError is the validation error returned by
// RiskBankActions.Validate if the designated constraints aren't met.
type RiskBankActionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskBankActionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskBankActionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskBankActionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskBankActionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskBankActionsValidationError) ErrorName() string { return "RiskBankActionsValidationError" }

// Error satisfies the builtin error interface
func (e RiskBankActionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskBankActions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskBankActionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskBankActionsValidationError{}
