// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/savings/closed_accounts_balance_transfer.proto

package savings

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ClosedAccountBalanceTransfer with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClosedAccountBalanceTransfer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClosedAccountBalanceTransfer with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClosedAccountBalanceTransferMultiError, or nil if none found.
func (m *ClosedAccountBalanceTransfer) ValidateAll() error {
	return m.validate(true)
}

func (m *ClosedAccountBalanceTransfer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for SavingsAccountId

	if all {
		switch v := interface{}(m.GetLastKnownBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "LastKnownBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "LastKnownBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastKnownBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClosedAccountBalanceTransferValidationError{
				field:  "LastKnownBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastKnownBalanceCapturedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "LastKnownBalanceCapturedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "LastKnownBalanceCapturedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastKnownBalanceCapturedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClosedAccountBalanceTransferValidationError{
				field:  "LastKnownBalanceCapturedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReportedClosureBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "ReportedClosureBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "ReportedClosureBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportedClosureBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClosedAccountBalanceTransferValidationError{
				field:  "ReportedClosureBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBalanceCapturedFromStatement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "BalanceCapturedFromStatement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "BalanceCapturedFromStatement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceCapturedFromStatement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClosedAccountBalanceTransferValidationError{
				field:  "BalanceCapturedFromStatement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BavId

	// no validation rules for Utr

	if all {
		switch v := interface{}(m.GetDateOfTransfer()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "DateOfTransfer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "DateOfTransfer",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateOfTransfer()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClosedAccountBalanceTransferValidationError{
				field:  "DateOfTransfer",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmountTransferred()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "AmountTransferred",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "AmountTransferred",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountTransferred()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClosedAccountBalanceTransferValidationError{
				field:  "AmountTransferred",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionStatus

	// no validation rules for TransactionFailureReason

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClosedAccountBalanceTransferValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ClosedAccountBalanceTransferValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ClosedAccountBalanceTransferValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeletedAtUnix

	if len(errors) > 0 {
		return ClosedAccountBalanceTransferMultiError(errors)
	}

	return nil
}

// ClosedAccountBalanceTransferMultiError is an error wrapping multiple
// validation errors returned by ClosedAccountBalanceTransfer.ValidateAll() if
// the designated constraints aren't met.
type ClosedAccountBalanceTransferMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClosedAccountBalanceTransferMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClosedAccountBalanceTransferMultiError) AllErrors() []error { return m }

// ClosedAccountBalanceTransferValidationError is the validation error returned
// by ClosedAccountBalanceTransfer.Validate if the designated constraints
// aren't met.
type ClosedAccountBalanceTransferValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClosedAccountBalanceTransferValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClosedAccountBalanceTransferValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClosedAccountBalanceTransferValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClosedAccountBalanceTransferValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClosedAccountBalanceTransferValidationError) ErrorName() string {
	return "ClosedAccountBalanceTransferValidationError"
}

// Error satisfies the builtin error interface
func (e ClosedAccountBalanceTransferValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClosedAccountBalanceTransfer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClosedAccountBalanceTransferValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClosedAccountBalanceTransferValidationError{}
