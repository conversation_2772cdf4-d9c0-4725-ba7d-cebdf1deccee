// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/savings/developer/developer.proto

package developer

import (
	savings "github.com/epifi/gamma/api/savings"
	extacct "github.com/epifi/gamma/api/savings/extacct"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SavingsEntity int32

const (
	SavingsEntity_SAVINGS_ENTITY_UNSPECIFIED SavingsEntity = 0
	// To fetch savings account by user Id(entity id)
	SavingsEntity_SAVINGS_ACCOUNT SavingsEntity = 1
	// to fetch user initiated savings account closure requests
	SavingsEntity_SAVINGS_ACCOUNT_CLOSURE_REQUESTS SavingsEntity = 2
	// to fetch closed account balance transfer records
	SavingsEntity_CLOSED_ACCOUNT_BALANCE_TRANSFER SavingsEntity = 3
	// to fetch bank account verification records
	SavingsEntity_BANK_ACCOUNT_VERIFICATIONS SavingsEntity = 4
)

// Enum value maps for SavingsEntity.
var (
	SavingsEntity_name = map[int32]string{
		0: "SAVINGS_ENTITY_UNSPECIFIED",
		1: "SAVINGS_ACCOUNT",
		2: "SAVINGS_ACCOUNT_CLOSURE_REQUESTS",
		3: "CLOSED_ACCOUNT_BALANCE_TRANSFER",
		4: "BANK_ACCOUNT_VERIFICATIONS",
	}
	SavingsEntity_value = map[string]int32{
		"SAVINGS_ENTITY_UNSPECIFIED":       0,
		"SAVINGS_ACCOUNT":                  1,
		"SAVINGS_ACCOUNT_CLOSURE_REQUESTS": 2,
		"CLOSED_ACCOUNT_BALANCE_TRANSFER":  3,
		"BANK_ACCOUNT_VERIFICATIONS":       4,
	}
)

func (x SavingsEntity) Enum() *SavingsEntity {
	p := new(SavingsEntity)
	*p = x
	return p
}

func (x SavingsEntity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SavingsEntity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_developer_developer_proto_enumTypes[0].Descriptor()
}

func (SavingsEntity) Type() protoreflect.EnumType {
	return &file_api_savings_developer_developer_proto_enumTypes[0]
}

func (x SavingsEntity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SavingsEntity.Descriptor instead.
func (SavingsEntity) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_developer_developer_proto_rawDescGZIP(), []int{0}
}

type SavingsAccountClosureRequestsEntityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SavingsAccountClosureRequests []*savings.SavingsAccountClosureRequest `protobuf:"bytes,1,rep,name=savings_account_closure_requests,json=savingsAccountClosureRequests,proto3" json:"savings_account_closure_requests,omitempty"`
}

func (x *SavingsAccountClosureRequestsEntityResponse) Reset() {
	*x = SavingsAccountClosureRequestsEntityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_developer_developer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SavingsAccountClosureRequestsEntityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SavingsAccountClosureRequestsEntityResponse) ProtoMessage() {}

func (x *SavingsAccountClosureRequestsEntityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_developer_developer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SavingsAccountClosureRequestsEntityResponse.ProtoReflect.Descriptor instead.
func (*SavingsAccountClosureRequestsEntityResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_developer_developer_proto_rawDescGZIP(), []int{0}
}

func (x *SavingsAccountClosureRequestsEntityResponse) GetSavingsAccountClosureRequests() []*savings.SavingsAccountClosureRequest {
	if x != nil {
		return x.SavingsAccountClosureRequests
	}
	return nil
}

type ClosedAccountBalanceTransferEntityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClosedAccountBalanceTransfers []*savings.ClosedAccountBalanceTransfer `protobuf:"bytes,1,rep,name=closed_account_balance_transfers,json=closedAccountBalanceTransfers,proto3" json:"closed_account_balance_transfers,omitempty"`
}

func (x *ClosedAccountBalanceTransferEntityResponse) Reset() {
	*x = ClosedAccountBalanceTransferEntityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_developer_developer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClosedAccountBalanceTransferEntityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClosedAccountBalanceTransferEntityResponse) ProtoMessage() {}

func (x *ClosedAccountBalanceTransferEntityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_developer_developer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClosedAccountBalanceTransferEntityResponse.ProtoReflect.Descriptor instead.
func (*ClosedAccountBalanceTransferEntityResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_developer_developer_proto_rawDescGZIP(), []int{1}
}

func (x *ClosedAccountBalanceTransferEntityResponse) GetClosedAccountBalanceTransfers() []*savings.ClosedAccountBalanceTransfer {
	if x != nil {
		return x.ClosedAccountBalanceTransfers
	}
	return nil
}

type BankAccountVerificationsEntityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BankAccountVerifications []*extacct.BankAccountVerification `protobuf:"bytes,1,rep,name=bank_account_verifications,json=bankAccountVerifications,proto3" json:"bank_account_verifications,omitempty"`
}

func (x *BankAccountVerificationsEntityResponse) Reset() {
	*x = BankAccountVerificationsEntityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_developer_developer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankAccountVerificationsEntityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccountVerificationsEntityResponse) ProtoMessage() {}

func (x *BankAccountVerificationsEntityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_developer_developer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccountVerificationsEntityResponse.ProtoReflect.Descriptor instead.
func (*BankAccountVerificationsEntityResponse) Descriptor() ([]byte, []int) {
	return file_api_savings_developer_developer_proto_rawDescGZIP(), []int{2}
}

func (x *BankAccountVerificationsEntityResponse) GetBankAccountVerifications() []*extacct.BankAccountVerification {
	if x != nil {
		return x.BankAccountVerifications
	}
	return nil
}

var File_api_savings_developer_developer_proto protoreflect.FileDescriptor

var file_api_savings_developer_developer_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x1a, 0x31, 0x61, 0x70, 0x69, 0x2f,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x32, 0x61,
	0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x73, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x65,
	0x78, 0x74, 0x61, 0x63, 0x63, 0x74, 0x2f, 0x65, 0x78, 0x74, 0x61, 0x63, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9d, 0x01, 0x0a, 0x2b, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x20, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x1d, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x73, 0x22, 0x9c, 0x01, 0x0a, 0x2a, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x66, 0x65, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x6e, 0x0a, 0x20, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x66, 0x65, 0x72, 0x52, 0x1d, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66,
	0x65, 0x72, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x26, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x66,
	0x0a, 0x1a, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x76,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x65, 0x78, 0x74,
	0x61, 0x63, 0x63, 0x74, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x18, 0x62, 0x61,
	0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2a, 0xaf, 0x01, 0x0a, 0x0d, 0x53, 0x61, 0x76, 0x69, 0x6e,
	0x67, 0x73, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x41, 0x56, 0x49,
	0x4e, 0x47, 0x53, 0x5f, 0x45, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x41, 0x56, 0x49,
	0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x01, 0x12, 0x24, 0x0a,
	0x20, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x53, 0x10, 0x02, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x42, 0x41, 0x4e, 0x4b,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x04, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x64, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_savings_developer_developer_proto_rawDescOnce sync.Once
	file_api_savings_developer_developer_proto_rawDescData = file_api_savings_developer_developer_proto_rawDesc
)

func file_api_savings_developer_developer_proto_rawDescGZIP() []byte {
	file_api_savings_developer_developer_proto_rawDescOnce.Do(func() {
		file_api_savings_developer_developer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_savings_developer_developer_proto_rawDescData)
	})
	return file_api_savings_developer_developer_proto_rawDescData
}

var file_api_savings_developer_developer_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_savings_developer_developer_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_savings_developer_developer_proto_goTypes = []interface{}{
	(SavingsEntity)(0), // 0: savings.developer.SavingsEntity
	(*SavingsAccountClosureRequestsEntityResponse)(nil), // 1: savings.developer.SavingsAccountClosureRequestsEntityResponse
	(*ClosedAccountBalanceTransferEntityResponse)(nil),  // 2: savings.developer.ClosedAccountBalanceTransferEntityResponse
	(*BankAccountVerificationsEntityResponse)(nil),      // 3: savings.developer.BankAccountVerificationsEntityResponse
	(*savings.SavingsAccountClosureRequest)(nil),        // 4: savings.SavingsAccountClosureRequest
	(*savings.ClosedAccountBalanceTransfer)(nil),        // 5: savings.ClosedAccountBalanceTransfer
	(*extacct.BankAccountVerification)(nil),             // 6: savings.extacct.BankAccountVerification
}
var file_api_savings_developer_developer_proto_depIdxs = []int32{
	4, // 0: savings.developer.SavingsAccountClosureRequestsEntityResponse.savings_account_closure_requests:type_name -> savings.SavingsAccountClosureRequest
	5, // 1: savings.developer.ClosedAccountBalanceTransferEntityResponse.closed_account_balance_transfers:type_name -> savings.ClosedAccountBalanceTransfer
	6, // 2: savings.developer.BankAccountVerificationsEntityResponse.bank_account_verifications:type_name -> savings.extacct.BankAccountVerification
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_savings_developer_developer_proto_init() }
func file_api_savings_developer_developer_proto_init() {
	if File_api_savings_developer_developer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_savings_developer_developer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SavingsAccountClosureRequestsEntityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_developer_developer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClosedAccountBalanceTransferEntityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_developer_developer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankAccountVerificationsEntityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_savings_developer_developer_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_savings_developer_developer_proto_goTypes,
		DependencyIndexes: file_api_savings_developer_developer_proto_depIdxs,
		EnumInfos:         file_api_savings_developer_developer_proto_enumTypes,
		MessageInfos:      file_api_savings_developer_developer_proto_msgTypes,
	}.Build()
	File_api_savings_developer_developer_proto = out.File
	file_api_savings_developer_developer_proto_rawDesc = nil
	file_api_savings_developer_developer_proto_goTypes = nil
	file_api_savings_developer_developer_proto_depIdxs = nil
}
