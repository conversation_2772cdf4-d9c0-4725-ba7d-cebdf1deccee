// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/savings/developer/developer.proto

package developer

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on
// SavingsAccountClosureRequestsEntityResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SavingsAccountClosureRequestsEntityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SavingsAccountClosureRequestsEntityResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SavingsAccountClosureRequestsEntityResponseMultiError, or nil if none found.
func (m *SavingsAccountClosureRequestsEntityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SavingsAccountClosureRequestsEntityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSavingsAccountClosureRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SavingsAccountClosureRequestsEntityResponseValidationError{
						field:  fmt.Sprintf("SavingsAccountClosureRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SavingsAccountClosureRequestsEntityResponseValidationError{
						field:  fmt.Sprintf("SavingsAccountClosureRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SavingsAccountClosureRequestsEntityResponseValidationError{
					field:  fmt.Sprintf("SavingsAccountClosureRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SavingsAccountClosureRequestsEntityResponseMultiError(errors)
	}

	return nil
}

// SavingsAccountClosureRequestsEntityResponseMultiError is an error wrapping
// multiple validation errors returned by
// SavingsAccountClosureRequestsEntityResponse.ValidateAll() if the designated
// constraints aren't met.
type SavingsAccountClosureRequestsEntityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SavingsAccountClosureRequestsEntityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SavingsAccountClosureRequestsEntityResponseMultiError) AllErrors() []error { return m }

// SavingsAccountClosureRequestsEntityResponseValidationError is the validation
// error returned by SavingsAccountClosureRequestsEntityResponse.Validate if
// the designated constraints aren't met.
type SavingsAccountClosureRequestsEntityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SavingsAccountClosureRequestsEntityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SavingsAccountClosureRequestsEntityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SavingsAccountClosureRequestsEntityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SavingsAccountClosureRequestsEntityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SavingsAccountClosureRequestsEntityResponseValidationError) ErrorName() string {
	return "SavingsAccountClosureRequestsEntityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SavingsAccountClosureRequestsEntityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSavingsAccountClosureRequestsEntityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SavingsAccountClosureRequestsEntityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SavingsAccountClosureRequestsEntityResponseValidationError{}

// Validate checks the field values on
// ClosedAccountBalanceTransferEntityResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClosedAccountBalanceTransferEntityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ClosedAccountBalanceTransferEntityResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// ClosedAccountBalanceTransferEntityResponseMultiError, or nil if none found.
func (m *ClosedAccountBalanceTransferEntityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ClosedAccountBalanceTransferEntityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClosedAccountBalanceTransfers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ClosedAccountBalanceTransferEntityResponseValidationError{
						field:  fmt.Sprintf("ClosedAccountBalanceTransfers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ClosedAccountBalanceTransferEntityResponseValidationError{
						field:  fmt.Sprintf("ClosedAccountBalanceTransfers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ClosedAccountBalanceTransferEntityResponseValidationError{
					field:  fmt.Sprintf("ClosedAccountBalanceTransfers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ClosedAccountBalanceTransferEntityResponseMultiError(errors)
	}

	return nil
}

// ClosedAccountBalanceTransferEntityResponseMultiError is an error wrapping
// multiple validation errors returned by
// ClosedAccountBalanceTransferEntityResponse.ValidateAll() if the designated
// constraints aren't met.
type ClosedAccountBalanceTransferEntityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClosedAccountBalanceTransferEntityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClosedAccountBalanceTransferEntityResponseMultiError) AllErrors() []error { return m }

// ClosedAccountBalanceTransferEntityResponseValidationError is the validation
// error returned by ClosedAccountBalanceTransferEntityResponse.Validate if
// the designated constraints aren't met.
type ClosedAccountBalanceTransferEntityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClosedAccountBalanceTransferEntityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClosedAccountBalanceTransferEntityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClosedAccountBalanceTransferEntityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClosedAccountBalanceTransferEntityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClosedAccountBalanceTransferEntityResponseValidationError) ErrorName() string {
	return "ClosedAccountBalanceTransferEntityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ClosedAccountBalanceTransferEntityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClosedAccountBalanceTransferEntityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClosedAccountBalanceTransferEntityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClosedAccountBalanceTransferEntityResponseValidationError{}

// Validate checks the field values on BankAccountVerificationsEntityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *BankAccountVerificationsEntityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// BankAccountVerificationsEntityResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// BankAccountVerificationsEntityResponseMultiError, or nil if none found.
func (m *BankAccountVerificationsEntityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *BankAccountVerificationsEntityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetBankAccountVerifications() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BankAccountVerificationsEntityResponseValidationError{
						field:  fmt.Sprintf("BankAccountVerifications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BankAccountVerificationsEntityResponseValidationError{
						field:  fmt.Sprintf("BankAccountVerifications[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BankAccountVerificationsEntityResponseValidationError{
					field:  fmt.Sprintf("BankAccountVerifications[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BankAccountVerificationsEntityResponseMultiError(errors)
	}

	return nil
}

// BankAccountVerificationsEntityResponseMultiError is an error wrapping
// multiple validation errors returned by
// BankAccountVerificationsEntityResponse.ValidateAll() if the designated
// constraints aren't met.
type BankAccountVerificationsEntityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankAccountVerificationsEntityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankAccountVerificationsEntityResponseMultiError) AllErrors() []error { return m }

// BankAccountVerificationsEntityResponseValidationError is the validation
// error returned by BankAccountVerificationsEntityResponse.Validate if the
// designated constraints aren't met.
type BankAccountVerificationsEntityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankAccountVerificationsEntityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankAccountVerificationsEntityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankAccountVerificationsEntityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankAccountVerificationsEntityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankAccountVerificationsEntityResponseValidationError) ErrorName() string {
	return "BankAccountVerificationsEntityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e BankAccountVerificationsEntityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankAccountVerificationsEntityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankAccountVerificationsEntityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankAccountVerificationsEntityResponseValidationError{}
