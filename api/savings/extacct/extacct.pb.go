//go:generate gen_sql -types=OverallStatus,FailureReason,BankAccountVerificationFieldMask,Source,Vendor,NameMatchData,Caller
// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/savings/extacct/extacct.proto

package extacct

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	kyc "github.com/epifi/gamma/api/kyc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OverallStatus int32

const (
	OverallStatus_OVERALL_STATUS_UNSPECIFIED OverallStatus = 0
	OverallStatus_OVERALL_STATUS_IN_PROGRESS OverallStatus = 1
	OverallStatus_OVERALL_STATUS_FAILURE     OverallStatus = 2
	OverallStatus_OVERALL_STATUS_SUCCESS     OverallStatus = 3
)

// Enum value maps for OverallStatus.
var (
	OverallStatus_name = map[int32]string{
		0: "OVERALL_STATUS_UNSPECIFIED",
		1: "OVERALL_STATUS_IN_PROGRESS",
		2: "OVERALL_STATUS_FAILURE",
		3: "OVERALL_STATUS_SUCCESS",
	}
	OverallStatus_value = map[string]int32{
		"OVERALL_STATUS_UNSPECIFIED": 0,
		"OVERALL_STATUS_IN_PROGRESS": 1,
		"OVERALL_STATUS_FAILURE":     2,
		"OVERALL_STATUS_SUCCESS":     3,
	}
)

func (x OverallStatus) Enum() *OverallStatus {
	p := new(OverallStatus)
	*p = x
	return p
}

func (x OverallStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OverallStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_extacct_extacct_proto_enumTypes[0].Descriptor()
}

func (OverallStatus) Type() protoreflect.EnumType {
	return &file_api_savings_extacct_extacct_proto_enumTypes[0]
}

func (x OverallStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OverallStatus.Descriptor instead.
func (OverallStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_extacct_extacct_proto_rawDescGZIP(), []int{0}
}

// FailureReason enum describes why the bank account verification failed.
// It's derived from vendor API response & other checks.
type FailureReason int32

const (
	FailureReason_FAILURE_REASON_UNSPECIFIED               FailureReason = 0
	FailureReason_FAILURE_REASON_API_TIMEOUT               FailureReason = 1
	FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH  FailureReason = 2
	FailureReason_FAILURE_REASON_NAME_AT_BANK_MISMATCH     FailureReason = 3
	FailureReason_FAILURE_REASON_INVALID_IFSC              FailureReason = 4
	FailureReason_FAILURE_REASON_INVALID_ACCOUNT_NUMBER    FailureReason = 5
	FailureReason_FAILURE_REASON_ACCOUNT_CLOSED            FailureReason = 6
	FailureReason_FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK FailureReason = 7
	// Unknown vendor response, usually Invalid ID Number or Combination of Inputs from Karza
	FailureReason_FAILURE_REASON_UNKNOWN FailureReason = 8
	// user entered account number is same as of fi account number
	FailureReason_FAILURE_REASON_SAME_ACCOUNT_NUMBER FailureReason = 9
	// used when vendor is federal. this means penny drop payment to input account Number + Ifsc failed
	FailureReason_FAILURE_REASON_PAYMENT_FAILED FailureReason = 10
)

// Enum value maps for FailureReason.
var (
	FailureReason_name = map[int32]string{
		0:  "FAILURE_REASON_UNSPECIFIED",
		1:  "FAILURE_REASON_API_TIMEOUT",
		2:  "FAILURE_REASON_USER_GIVEN_NAME_MISMATCH",
		3:  "FAILURE_REASON_NAME_AT_BANK_MISMATCH",
		4:  "FAILURE_REASON_INVALID_IFSC",
		5:  "FAILURE_REASON_INVALID_ACCOUNT_NUMBER",
		6:  "FAILURE_REASON_ACCOUNT_CLOSED",
		7:  "FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK",
		8:  "FAILURE_REASON_UNKNOWN",
		9:  "FAILURE_REASON_SAME_ACCOUNT_NUMBER",
		10: "FAILURE_REASON_PAYMENT_FAILED",
	}
	FailureReason_value = map[string]int32{
		"FAILURE_REASON_UNSPECIFIED":               0,
		"FAILURE_REASON_API_TIMEOUT":               1,
		"FAILURE_REASON_USER_GIVEN_NAME_MISMATCH":  2,
		"FAILURE_REASON_NAME_AT_BANK_MISMATCH":     3,
		"FAILURE_REASON_INVALID_IFSC":              4,
		"FAILURE_REASON_INVALID_ACCOUNT_NUMBER":    5,
		"FAILURE_REASON_ACCOUNT_CLOSED":            6,
		"FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK": 7,
		"FAILURE_REASON_UNKNOWN":                   8,
		"FAILURE_REASON_SAME_ACCOUNT_NUMBER":       9,
		"FAILURE_REASON_PAYMENT_FAILED":            10,
	}
)

func (x FailureReason) Enum() *FailureReason {
	p := new(FailureReason)
	*p = x
	return p
}

func (x FailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_extacct_extacct_proto_enumTypes[1].Descriptor()
}

func (FailureReason) Type() protoreflect.EnumType {
	return &file_api_savings_extacct_extacct_proto_enumTypes[1]
}

func (x FailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FailureReason.Descriptor instead.
func (FailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_extacct_extacct_proto_rawDescGZIP(), []int{1}
}

// Field mask specifies which fields need to be considered for a particular operation.
// For example, when updating name match data.
type BankAccountVerificationFieldMask int32

const (
	BankAccountVerificationFieldMask_FIELD_MASK_UNSPECIFIED                 BankAccountVerificationFieldMask = 0
	BankAccountVerificationFieldMask_FIELD_MASK_ACCOUNT_NUMBER              BankAccountVerificationFieldMask = 1
	BankAccountVerificationFieldMask_FIELD_MASK_IFSC                        BankAccountVerificationFieldMask = 2
	BankAccountVerificationFieldMask_FIELD_MASK_NAME_AT_BANK                BankAccountVerificationFieldMask = 3
	BankAccountVerificationFieldMask_FIELD_MASK_OVERALL_STATUS              BankAccountVerificationFieldMask = 4
	BankAccountVerificationFieldMask_FIELD_MASK_VENDOR_STATUS               BankAccountVerificationFieldMask = 5
	BankAccountVerificationFieldMask_FIELD_MASK_VENDOR_REQ_ID               BankAccountVerificationFieldMask = 6
	BankAccountVerificationFieldMask_FIELD_MASK_VENDOR                      BankAccountVerificationFieldMask = 7
	BankAccountVerificationFieldMask_FIELD_MASK_FAILURE_REASON              BankAccountVerificationFieldMask = 8
	BankAccountVerificationFieldMask_FIELD_MASK_USER_GIVEN_NAME_MATCH_SCORE BankAccountVerificationFieldMask = 9
	BankAccountVerificationFieldMask_FIELD_MASK_NAME_AT_BANK_MATCH_SCORE    BankAccountVerificationFieldMask = 10
	BankAccountVerificationFieldMask_FIELD_MASK_KYC_NAME                    BankAccountVerificationFieldMask = 11
	BankAccountVerificationFieldMask_FIELD_MASK_DELETED_AT_UNIX             BankAccountVerificationFieldMask = 12
)

// Enum value maps for BankAccountVerificationFieldMask.
var (
	BankAccountVerificationFieldMask_name = map[int32]string{
		0:  "FIELD_MASK_UNSPECIFIED",
		1:  "FIELD_MASK_ACCOUNT_NUMBER",
		2:  "FIELD_MASK_IFSC",
		3:  "FIELD_MASK_NAME_AT_BANK",
		4:  "FIELD_MASK_OVERALL_STATUS",
		5:  "FIELD_MASK_VENDOR_STATUS",
		6:  "FIELD_MASK_VENDOR_REQ_ID",
		7:  "FIELD_MASK_VENDOR",
		8:  "FIELD_MASK_FAILURE_REASON",
		9:  "FIELD_MASK_USER_GIVEN_NAME_MATCH_SCORE",
		10: "FIELD_MASK_NAME_AT_BANK_MATCH_SCORE",
		11: "FIELD_MASK_KYC_NAME",
		12: "FIELD_MASK_DELETED_AT_UNIX",
	}
	BankAccountVerificationFieldMask_value = map[string]int32{
		"FIELD_MASK_UNSPECIFIED":                 0,
		"FIELD_MASK_ACCOUNT_NUMBER":              1,
		"FIELD_MASK_IFSC":                        2,
		"FIELD_MASK_NAME_AT_BANK":                3,
		"FIELD_MASK_OVERALL_STATUS":              4,
		"FIELD_MASK_VENDOR_STATUS":               5,
		"FIELD_MASK_VENDOR_REQ_ID":               6,
		"FIELD_MASK_VENDOR":                      7,
		"FIELD_MASK_FAILURE_REASON":              8,
		"FIELD_MASK_USER_GIVEN_NAME_MATCH_SCORE": 9,
		"FIELD_MASK_NAME_AT_BANK_MATCH_SCORE":    10,
		"FIELD_MASK_KYC_NAME":                    11,
		"FIELD_MASK_DELETED_AT_UNIX":             12,
	}
)

func (x BankAccountVerificationFieldMask) Enum() *BankAccountVerificationFieldMask {
	p := new(BankAccountVerificationFieldMask)
	*p = x
	return p
}

func (x BankAccountVerificationFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BankAccountVerificationFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_extacct_extacct_proto_enumTypes[2].Descriptor()
}

func (BankAccountVerificationFieldMask) Type() protoreflect.EnumType {
	return &file_api_savings_extacct_extacct_proto_enumTypes[2]
}

func (x BankAccountVerificationFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BankAccountVerificationFieldMask.Descriptor instead.
func (BankAccountVerificationFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_extacct_extacct_proto_rawDescGZIP(), []int{2}
}

// Source defines from where the bank account verification was initiated
type Source int32

const (
	Source_SOURCE_UNSPECIFIED Source = 0
	// penny drop verification triggered by user in-app
	Source_SOURCE_USER Source = 1
	// penny drop verification triggered from sherlock
	Source_SOURCE_SHERLOCK Source = 2
	// manually verified accounts that we share to federal (and they send back UTR after successful transaction)
	// are stored when dataops agent uploads csv containing successful balance transfer UTRs in sherlock
	Source_SOURCE_FEDERAL_UTR_SHEET Source = 3
	// penny drop initiated by user via web form
	Source_SOURCE_WEB Source = 4
)

// Enum value maps for Source.
var (
	Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "SOURCE_USER",
		2: "SOURCE_SHERLOCK",
		3: "SOURCE_FEDERAL_UTR_SHEET",
		4: "SOURCE_WEB",
	}
	Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED":       0,
		"SOURCE_USER":              1,
		"SOURCE_SHERLOCK":          2,
		"SOURCE_FEDERAL_UTR_SHEET": 3,
		"SOURCE_WEB":               4,
	}
)

func (x Source) Enum() *Source {
	p := new(Source)
	*p = x
	return p
}

func (x Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_extacct_extacct_proto_enumTypes[3].Descriptor()
}

func (Source) Type() protoreflect.EnumType {
	return &file_api_savings_extacct_extacct_proto_enumTypes[3]
}

func (x Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source.Descriptor instead.
func (Source) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_extacct_extacct_proto_rawDescGZIP(), []int{3}
}

// Vendor used to perform account verification
type Vendor int32

const (
	Vendor_VENDOR_UNSPECIFIED Vendor = 0
	Vendor_KARZA              Vendor = 9
	Vendor_FEDERAL_BANK       Vendor = 10
)

// Enum value maps for Vendor.
var (
	Vendor_name = map[int32]string{
		0:  "VENDOR_UNSPECIFIED",
		9:  "KARZA",
		10: "FEDERAL_BANK",
	}
	Vendor_value = map[string]int32{
		"VENDOR_UNSPECIFIED": 0,
		"KARZA":              9,
		"FEDERAL_BANK":       10,
	}
)

func (x Vendor) Enum() *Vendor {
	p := new(Vendor)
	*p = x
	return p
}

func (x Vendor) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Vendor) Descriptor() protoreflect.EnumDescriptor {
	return file_api_savings_extacct_extacct_proto_enumTypes[4].Descriptor()
}

func (Vendor) Type() protoreflect.EnumType {
	return &file_api_savings_extacct_extacct_proto_enumTypes[4]
}

func (x Vendor) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Vendor.Descriptor instead.
func (Vendor) EnumDescriptor() ([]byte, []int) {
	return file_api_savings_extacct_extacct_proto_rawDescGZIP(), []int{4}
}

// BankAccountVerification contains all the information pertaining to
// verifying a users bank account
type BankAccountVerification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string        `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId       string        `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AccountNumber string        `protobuf:"bytes,3,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Ifsc          string        `protobuf:"bytes,4,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	NameAtBank    string        `protobuf:"bytes,5,opt,name=name_at_bank,json=nameAtBank,proto3" json:"name_at_bank,omitempty"`
	OverallStatus OverallStatus `protobuf:"varint,6,opt,name=overall_status,json=overallStatus,proto3,enum=savings.extacct.OverallStatus" json:"overall_status,omitempty"`
	// contains vendor rpc status and response description
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,7,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
	VendorReqId  string                      `protobuf:"bytes,8,opt,name=vendor_req_id,json=vendorReqId,proto3" json:"vendor_req_id,omitempty"`
	// name of vendor used to validate bank account
	Vendor        Vendor                 `protobuf:"varint,9,opt,name=vendor,proto3,enum=savings.extacct.Vendor" json:"vendor,omitempty"`
	FailureReason FailureReason          `protobuf:"varint,10,opt,name=failure_reason,json=failureReason,proto3,enum=savings.extacct.FailureReason" json:"failure_reason,omitempty"`
	NameMatchData *NameMatchData         `protobuf:"bytes,11,opt,name=name_match_data,json=nameMatchData,proto3" json:"name_match_data,omitempty"`
	Caller        *Caller                `protobuf:"bytes,12,opt,name=caller,proto3" json:"caller,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAtUnix int64                  `protobuf:"varint,15,opt,name=deleted_at_unix,json=deletedAtUnix,proto3" json:"deleted_at_unix,omitempty"`
}

func (x *BankAccountVerification) Reset() {
	*x = BankAccountVerification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_extacct_extacct_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankAccountVerification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccountVerification) ProtoMessage() {}

func (x *BankAccountVerification) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_extacct_extacct_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccountVerification.ProtoReflect.Descriptor instead.
func (*BankAccountVerification) Descriptor() ([]byte, []int) {
	return file_api_savings_extacct_extacct_proto_rawDescGZIP(), []int{0}
}

func (x *BankAccountVerification) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BankAccountVerification) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *BankAccountVerification) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *BankAccountVerification) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *BankAccountVerification) GetNameAtBank() string {
	if x != nil {
		return x.NameAtBank
	}
	return ""
}

func (x *BankAccountVerification) GetOverallStatus() OverallStatus {
	if x != nil {
		return x.OverallStatus
	}
	return OverallStatus_OVERALL_STATUS_UNSPECIFIED
}

func (x *BankAccountVerification) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

func (x *BankAccountVerification) GetVendorReqId() string {
	if x != nil {
		return x.VendorReqId
	}
	return ""
}

func (x *BankAccountVerification) GetVendor() Vendor {
	if x != nil {
		return x.Vendor
	}
	return Vendor_VENDOR_UNSPECIFIED
}

func (x *BankAccountVerification) GetFailureReason() FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return FailureReason_FAILURE_REASON_UNSPECIFIED
}

func (x *BankAccountVerification) GetNameMatchData() *NameMatchData {
	if x != nil {
		return x.NameMatchData
	}
	return nil
}

func (x *BankAccountVerification) GetCaller() *Caller {
	if x != nil {
		return x.Caller
	}
	return nil
}

func (x *BankAccountVerification) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *BankAccountVerification) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *BankAccountVerification) GetDeletedAtUnix() int64 {
	if x != nil {
		return x.DeletedAtUnix
	}
	return 0
}

// NameMatchData contains the names and scores of
// name matches performed to verify a users authenticity
// with the kyc name of the actor at Fi
type NameMatchData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KycName                 *common.Name        `protobuf:"bytes,1,opt,name=kyc_name,json=kycName,proto3" json:"kyc_name,omitempty"`
	UserGivenName           string              `protobuf:"bytes,2,opt,name=user_given_name,json=userGivenName,proto3" json:"user_given_name,omitempty"`
	UserGivenNameMatchScore *kyc.NameMatchScore `protobuf:"bytes,3,opt,name=user_given_name_match_score,json=userGivenNameMatchScore,proto3" json:"user_given_name_match_score,omitempty"`
	NameAtBank              string              `protobuf:"bytes,4,opt,name=name_at_bank,json=nameAtBank,proto3" json:"name_at_bank,omitempty"`
	NameAtBankMatchScore    *kyc.NameMatchScore `protobuf:"bytes,5,opt,name=name_at_bank_match_score,json=nameAtBankMatchScore,proto3" json:"name_at_bank_match_score,omitempty"`
}

func (x *NameMatchData) Reset() {
	*x = NameMatchData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_extacct_extacct_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NameMatchData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NameMatchData) ProtoMessage() {}

func (x *NameMatchData) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_extacct_extacct_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NameMatchData.ProtoReflect.Descriptor instead.
func (*NameMatchData) Descriptor() ([]byte, []int) {
	return file_api_savings_extacct_extacct_proto_rawDescGZIP(), []int{1}
}

func (x *NameMatchData) GetKycName() *common.Name {
	if x != nil {
		return x.KycName
	}
	return nil
}

func (x *NameMatchData) GetUserGivenName() string {
	if x != nil {
		return x.UserGivenName
	}
	return ""
}

func (x *NameMatchData) GetUserGivenNameMatchScore() *kyc.NameMatchScore {
	if x != nil {
		return x.UserGivenNameMatchScore
	}
	return nil
}

func (x *NameMatchData) GetNameAtBank() string {
	if x != nil {
		return x.NameAtBank
	}
	return ""
}

func (x *NameMatchData) GetNameAtBankMatchScore() *kyc.NameMatchScore {
	if x != nil {
		return x.NameAtBankMatchScore
	}
	return nil
}

// Caller contains details of the person
// initiating the bank account verification
type Caller struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// source enum tells the person initiating the bank account validation
	Source Source `protobuf:"varint,1,opt,name=source,proto3,enum=savings.extacct.Source" json:"source,omitempty"`
	// email is needed if sherlock agent is the source
	Email string `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *Caller) Reset() {
	*x = Caller{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_savings_extacct_extacct_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Caller) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Caller) ProtoMessage() {}

func (x *Caller) ProtoReflect() protoreflect.Message {
	mi := &file_api_savings_extacct_extacct_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Caller.ProtoReflect.Descriptor instead.
func (*Caller) Descriptor() ([]byte, []int) {
	return file_api_savings_extacct_extacct_proto_rawDescGZIP(), []int{2}
}

func (x *Caller) GetSource() Source {
	if x != nil {
		return x.Source
	}
	return Source_SOURCE_UNSPECIFIED
}

func (x *Caller) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

var File_api_savings_extacct_extacct_proto protoreflect.FileDescriptor

var file_api_savings_extacct_extacct_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x65, 0x78,
	0x74, 0x61, 0x63, 0x63, 0x74, 0x2f, 0x65, 0x78, 0x74, 0x61, 0x63, 0x63, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x65, 0x78, 0x74,
	0x61, 0x63, 0x63, 0x74, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x6b, 0x79, 0x63, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xdd, 0x05, 0x0a, 0x17, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a,
	0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x66, 0x73,
	0x63, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x74, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x42,
	0x61, 0x6e, 0x6b, 0x12, 0x45, 0x0a, 0x0e, 0x6f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x73, 0x61,
	0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x65, 0x78, 0x74, 0x61, 0x63, 0x63, 0x74, 0x2e, 0x4f, 0x76,
	0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x6f, 0x76, 0x65,
	0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0d,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x65, 0x78, 0x74, 0x61, 0x63,
	0x63, 0x74, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x12, 0x45, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69,
	0x6e, 0x67, 0x73, 0x2e, 0x65, 0x78, 0x74, 0x61, 0x63, 0x63, 0x74, 0x2e, 0x46, 0x61, 0x69, 0x6c,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x0f, 0x6e, 0x61, 0x6d, 0x65,
	0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x65, 0x78, 0x74, 0x61,
	0x63, 0x63, 0x74, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x0d, 0x6e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x2f, 0x0a, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x65, 0x78, 0x74, 0x61, 0x63,
	0x63, 0x74, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x52, 0x06, 0x63, 0x61, 0x6c, 0x6c, 0x65,
	0x72, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x26, 0x0a, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x5f, 0x75, 0x6e, 0x69, 0x78, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0d, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x55, 0x6e, 0x69, 0x78, 0x22,
	0xae, 0x02, 0x0a, 0x0d, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x33, 0x0a, 0x08, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x07, 0x6b,
	0x79, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67,
	0x69, 0x76, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x75, 0x73, 0x65, 0x72, 0x47, 0x69, 0x76, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x51,
	0x0a, 0x1b, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x67, 0x69, 0x76, 0x65, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61,
	0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x17, 0x75, 0x73, 0x65, 0x72, 0x47, 0x69,
	0x76, 0x65, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72,
	0x65, 0x12, 0x20, 0x0a, 0x0c, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x74, 0x5f, 0x62, 0x61, 0x6e,
	0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x41, 0x74, 0x42,
	0x61, 0x6e, 0x6b, 0x12, 0x4b, 0x0a, 0x18, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x61, 0x74, 0x5f, 0x62,
	0x61, 0x6e, 0x6b, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x52, 0x14, 0x6e, 0x61, 0x6d, 0x65,
	0x41, 0x74, 0x42, 0x61, 0x6e, 0x6b, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x22, 0x4f, 0x0a, 0x06, 0x43, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x65, 0x78, 0x74, 0x61, 0x63, 0x63, 0x74, 0x2e, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x2a, 0x87, 0x01, 0x0a, 0x0d, 0x4f, 0x76, 0x65, 0x72, 0x61, 0x6c, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53,
	0x53, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c, 0x4c, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12,
	0x1a, 0x0a, 0x16, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x03, 0x2a, 0xaa, 0x03, 0x0a, 0x0d,
	0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1e, 0x0a,
	0x1a, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a,
	0x1a, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x41, 0x50, 0x49, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x4f, 0x55, 0x54, 0x10, 0x01, 0x12, 0x2b, 0x0a,
	0x27, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x47, 0x49, 0x56, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x41,
	0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x41, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54,
	0x43, 0x48, 0x10, 0x03, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x49,
	0x46, 0x53, 0x43, 0x10, 0x04, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x05,
	0x12, 0x21, 0x0a, 0x1d, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45,
	0x44, 0x10, 0x06, 0x12, 0x2c, 0x0a, 0x28, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x49, 0x4d, 0x50, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41,
	0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10,
	0x07, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x08, 0x12, 0x26, 0x0a,
	0x22, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x53, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x55, 0x4d,
	0x42, 0x45, 0x52, 0x10, 0x09, 0x12, 0x21, 0x0a, 0x1d, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x0a, 0x2a, 0xae, 0x03, 0x0a, 0x20, 0x42, 0x61, 0x6e,
	0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x1a, 0x0a,
	0x16, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x46, 0x53, 0x43, 0x10, 0x02, 0x12, 0x1b, 0x0a,
	0x17, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x5f, 0x41, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x41, 0x4c, 0x4c,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x51,
	0x5f, 0x49, 0x44, 0x10, 0x06, 0x12, 0x15, 0x0a, 0x11, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x07, 0x12, 0x1d, 0x0a, 0x19,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x10, 0x08, 0x12, 0x2a, 0x0a, 0x26, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x47,
	0x49, 0x56, 0x45, 0x4e, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f,
	0x53, 0x43, 0x4f, 0x52, 0x45, 0x10, 0x09, 0x12, 0x27, 0x0a, 0x23, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x41, 0x54, 0x5f, 0x42, 0x41,
	0x4e, 0x4b, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x10, 0x0a,
	0x12, 0x17, 0x0a, 0x13, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4b,
	0x59, 0x43, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x0b, 0x12, 0x1e, 0x0a, 0x1a, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x58, 0x10, 0x0c, 0x2a, 0x74, 0x0a, 0x06, 0x53, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x53,
	0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f,
	0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x53, 0x48, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x10,
	0x02, 0x12, 0x1c, 0x0a, 0x18, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x46, 0x45, 0x44, 0x45,
	0x52, 0x41, 0x4c, 0x5f, 0x55, 0x54, 0x52, 0x5f, 0x53, 0x48, 0x45, 0x45, 0x54, 0x10, 0x03, 0x12,
	0x0e, 0x0a, 0x0a, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x57, 0x45, 0x42, 0x10, 0x04, 0x2a,
	0x3d, 0x0a, 0x06, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x16, 0x0a, 0x12, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x09, 0x0a, 0x05, 0x4b, 0x41, 0x52, 0x5a, 0x41, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c,
	0x46, 0x45, 0x44, 0x45, 0x52, 0x41, 0x4c, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x0a, 0x42, 0x58,
	0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x76,
	0x69, 0x6e, 0x67, 0x73, 0x2e, 0x65, 0x78, 0x74, 0x61, 0x63, 0x63, 0x74, 0x5a, 0x2a, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73,
	0x2f, 0x65, 0x78, 0x74, 0x61, 0x63, 0x63, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_savings_extacct_extacct_proto_rawDescOnce sync.Once
	file_api_savings_extacct_extacct_proto_rawDescData = file_api_savings_extacct_extacct_proto_rawDesc
)

func file_api_savings_extacct_extacct_proto_rawDescGZIP() []byte {
	file_api_savings_extacct_extacct_proto_rawDescOnce.Do(func() {
		file_api_savings_extacct_extacct_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_savings_extacct_extacct_proto_rawDescData)
	})
	return file_api_savings_extacct_extacct_proto_rawDescData
}

var file_api_savings_extacct_extacct_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_savings_extacct_extacct_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_savings_extacct_extacct_proto_goTypes = []interface{}{
	(OverallStatus)(0),                    // 0: savings.extacct.OverallStatus
	(FailureReason)(0),                    // 1: savings.extacct.FailureReason
	(BankAccountVerificationFieldMask)(0), // 2: savings.extacct.BankAccountVerificationFieldMask
	(Source)(0),                           // 3: savings.extacct.Source
	(Vendor)(0),                           // 4: savings.extacct.Vendor
	(*BankAccountVerification)(nil),       // 5: savings.extacct.BankAccountVerification
	(*NameMatchData)(nil),                 // 6: savings.extacct.NameMatchData
	(*Caller)(nil),                        // 7: savings.extacct.Caller
	(*vendorgateway.VendorStatus)(nil),    // 8: vendorgateway.VendorStatus
	(*timestamppb.Timestamp)(nil),         // 9: google.protobuf.Timestamp
	(*common.Name)(nil),                   // 10: api.typesv2.common.Name
	(*kyc.NameMatchScore)(nil),            // 11: kyc.NameMatchScore
}
var file_api_savings_extacct_extacct_proto_depIdxs = []int32{
	0,  // 0: savings.extacct.BankAccountVerification.overall_status:type_name -> savings.extacct.OverallStatus
	8,  // 1: savings.extacct.BankAccountVerification.vendor_status:type_name -> vendorgateway.VendorStatus
	4,  // 2: savings.extacct.BankAccountVerification.vendor:type_name -> savings.extacct.Vendor
	1,  // 3: savings.extacct.BankAccountVerification.failure_reason:type_name -> savings.extacct.FailureReason
	6,  // 4: savings.extacct.BankAccountVerification.name_match_data:type_name -> savings.extacct.NameMatchData
	7,  // 5: savings.extacct.BankAccountVerification.caller:type_name -> savings.extacct.Caller
	9,  // 6: savings.extacct.BankAccountVerification.created_at:type_name -> google.protobuf.Timestamp
	9,  // 7: savings.extacct.BankAccountVerification.updated_at:type_name -> google.protobuf.Timestamp
	10, // 8: savings.extacct.NameMatchData.kyc_name:type_name -> api.typesv2.common.Name
	11, // 9: savings.extacct.NameMatchData.user_given_name_match_score:type_name -> kyc.NameMatchScore
	11, // 10: savings.extacct.NameMatchData.name_at_bank_match_score:type_name -> kyc.NameMatchScore
	3,  // 11: savings.extacct.Caller.source:type_name -> savings.extacct.Source
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_savings_extacct_extacct_proto_init() }
func file_api_savings_extacct_extacct_proto_init() {
	if File_api_savings_extacct_extacct_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_savings_extacct_extacct_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankAccountVerification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_extacct_extacct_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NameMatchData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_savings_extacct_extacct_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Caller); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_savings_extacct_extacct_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_savings_extacct_extacct_proto_goTypes,
		DependencyIndexes: file_api_savings_extacct_extacct_proto_depIdxs,
		EnumInfos:         file_api_savings_extacct_extacct_proto_enumTypes,
		MessageInfos:      file_api_savings_extacct_extacct_proto_msgTypes,
	}.Build()
	File_api_savings_extacct_extacct_proto = out.File
	file_api_savings_extacct_extacct_proto_rawDesc = nil
	file_api_savings_extacct_extacct_proto_goTypes = nil
	file_api_savings_extacct_extacct_proto_depIdxs = nil
}
