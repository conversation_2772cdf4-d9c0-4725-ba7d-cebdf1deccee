// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/savings/service.proto

package savings

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	account "github.com/epifi/gamma/api/typesv2/account"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = account.AccountProductOffering(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on UpdateSavingsAccountNomineesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateSavingsAccountNomineesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSavingsAccountNomineesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateSavingsAccountNomineesRequestMultiError, or nil if none found.
func (m *UpdateSavingsAccountNomineesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSavingsAccountNomineesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetNomineeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSavingsAccountNomineesRequestValidationError{
					field:  "NomineeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSavingsAccountNomineesRequestValidationError{
					field:  "NomineeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNomineeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSavingsAccountNomineesRequestValidationError{
				field:  "NomineeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSavingsAccountNomineesRequestMultiError(errors)
	}

	return nil
}

// UpdateSavingsAccountNomineesRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateSavingsAccountNomineesRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateSavingsAccountNomineesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSavingsAccountNomineesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSavingsAccountNomineesRequestMultiError) AllErrors() []error { return m }

// UpdateSavingsAccountNomineesRequestValidationError is the validation error
// returned by UpdateSavingsAccountNomineesRequest.Validate if the designated
// constraints aren't met.
type UpdateSavingsAccountNomineesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSavingsAccountNomineesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSavingsAccountNomineesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSavingsAccountNomineesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSavingsAccountNomineesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSavingsAccountNomineesRequestValidationError) ErrorName() string {
	return "UpdateSavingsAccountNomineesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSavingsAccountNomineesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSavingsAccountNomineesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSavingsAccountNomineesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSavingsAccountNomineesRequestValidationError{}

// Validate checks the field values on UpdateSavingsAccountNomineesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateSavingsAccountNomineesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSavingsAccountNomineesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateSavingsAccountNomineesResponseMultiError, or nil if none found.
func (m *UpdateSavingsAccountNomineesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSavingsAccountNomineesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSavingsAccountNomineesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSavingsAccountNomineesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSavingsAccountNomineesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSavingsAccountNomineesResponseMultiError(errors)
	}

	return nil
}

// UpdateSavingsAccountNomineesResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateSavingsAccountNomineesResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateSavingsAccountNomineesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSavingsAccountNomineesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSavingsAccountNomineesResponseMultiError) AllErrors() []error { return m }

// UpdateSavingsAccountNomineesResponseValidationError is the validation error
// returned by UpdateSavingsAccountNomineesResponse.Validate if the designated
// constraints aren't met.
type UpdateSavingsAccountNomineesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSavingsAccountNomineesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSavingsAccountNomineesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSavingsAccountNomineesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSavingsAccountNomineesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSavingsAccountNomineesResponseValidationError) ErrorName() string {
	return "UpdateSavingsAccountNomineesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSavingsAccountNomineesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSavingsAccountNomineesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSavingsAccountNomineesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSavingsAccountNomineesResponseValidationError{}

// Validate checks the field values on GetSavingsAccountNomineesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSavingsAccountNomineesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSavingsAccountNomineesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSavingsAccountNomineesRequestMultiError, or nil if none found.
func (m *GetSavingsAccountNomineesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSavingsAccountNomineesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetSavingsAccountNomineesRequestMultiError(errors)
	}

	return nil
}

// GetSavingsAccountNomineesRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSavingsAccountNomineesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSavingsAccountNomineesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSavingsAccountNomineesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSavingsAccountNomineesRequestMultiError) AllErrors() []error { return m }

// GetSavingsAccountNomineesRequestValidationError is the validation error
// returned by GetSavingsAccountNomineesRequest.Validate if the designated
// constraints aren't met.
type GetSavingsAccountNomineesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSavingsAccountNomineesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSavingsAccountNomineesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSavingsAccountNomineesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSavingsAccountNomineesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSavingsAccountNomineesRequestValidationError) ErrorName() string {
	return "GetSavingsAccountNomineesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSavingsAccountNomineesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSavingsAccountNomineesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSavingsAccountNomineesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSavingsAccountNomineesRequestValidationError{}

// Validate checks the field values on GetSavingsAccountNomineesResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSavingsAccountNomineesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSavingsAccountNomineesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSavingsAccountNomineesResponseMultiError, or nil if none found.
func (m *GetSavingsAccountNomineesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSavingsAccountNomineesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSavingsAccountNomineesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSavingsAccountNomineesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSavingsAccountNomineesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetNominee() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSavingsAccountNomineesResponseValidationError{
						field:  fmt.Sprintf("Nominee[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSavingsAccountNomineesResponseValidationError{
						field:  fmt.Sprintf("Nominee[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSavingsAccountNomineesResponseValidationError{
					field:  fmt.Sprintf("Nominee[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSavingsAccountNomineesResponseMultiError(errors)
	}

	return nil
}

// GetSavingsAccountNomineesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetSavingsAccountNomineesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSavingsAccountNomineesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSavingsAccountNomineesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSavingsAccountNomineesResponseMultiError) AllErrors() []error { return m }

// GetSavingsAccountNomineesResponseValidationError is the validation error
// returned by GetSavingsAccountNomineesResponse.Validate if the designated
// constraints aren't met.
type GetSavingsAccountNomineesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSavingsAccountNomineesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSavingsAccountNomineesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSavingsAccountNomineesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSavingsAccountNomineesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSavingsAccountNomineesResponseValidationError) ErrorName() string {
	return "GetSavingsAccountNomineesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSavingsAccountNomineesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSavingsAccountNomineesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSavingsAccountNomineesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSavingsAccountNomineesResponseValidationError{}

// Validate checks the field values on UpdateBalanceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBalanceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBalanceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBalanceRequestMultiError, or nil if none found.
func (m *UpdateBalanceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBalanceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetPrevUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBalanceRequestValidationError{
					field:  "PrevUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBalanceRequestValidationError{
					field:  "PrevUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrevUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBalanceRequestValidationError{
				field:  "PrevUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateBalanceRequestMultiError(errors)
	}

	return nil
}

// UpdateBalanceRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateBalanceRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateBalanceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBalanceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBalanceRequestMultiError) AllErrors() []error { return m }

// UpdateBalanceRequestValidationError is the validation error returned by
// UpdateBalanceRequest.Validate if the designated constraints aren't met.
type UpdateBalanceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBalanceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBalanceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBalanceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBalanceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBalanceRequestValidationError) ErrorName() string {
	return "UpdateBalanceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBalanceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBalanceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBalanceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBalanceRequestValidationError{}

// Validate checks the field values on UpdateBalanceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBalanceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBalanceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateBalanceResponseMultiError, or nil if none found.
func (m *UpdateBalanceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBalanceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBalanceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateBalanceResponseMultiError(errors)
	}

	return nil
}

// UpdateBalanceResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateBalanceResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateBalanceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBalanceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBalanceResponseMultiError) AllErrors() []error { return m }

// UpdateBalanceResponseValidationError is the validation error returned by
// UpdateBalanceResponse.Validate if the designated constraints aren't met.
type UpdateBalanceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBalanceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBalanceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBalanceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBalanceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBalanceResponseValidationError) ErrorName() string {
	return "UpdateBalanceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBalanceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBalanceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBalanceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBalanceResponseValidationError{}

// Validate checks the field values on GetAccountBalanceV1Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountBalanceV1Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountBalanceV1Request with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountBalanceV1RequestMultiError, or nil if none found.
func (m *GetAccountBalanceV1Request) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountBalanceV1Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DataFreshness

	// no validation rules for VendorApiOption

	switch v := m.Identifier.(type) {
	case *GetAccountBalanceV1Request_Id:
		if v == nil {
			err := GetAccountBalanceV1RequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *GetAccountBalanceV1Request_ExternalId:
		if v == nil {
			err := GetAccountBalanceV1RequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountBalanceV1RequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountBalanceV1RequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountBalanceV1RequestValidationError{
					field:  "ExternalId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAccountBalanceV1RequestMultiError(errors)
	}

	return nil
}

// GetAccountBalanceV1RequestMultiError is an error wrapping multiple
// validation errors returned by GetAccountBalanceV1Request.ValidateAll() if
// the designated constraints aren't met.
type GetAccountBalanceV1RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountBalanceV1RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountBalanceV1RequestMultiError) AllErrors() []error { return m }

// GetAccountBalanceV1RequestValidationError is the validation error returned
// by GetAccountBalanceV1Request.Validate if the designated constraints aren't met.
type GetAccountBalanceV1RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountBalanceV1RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountBalanceV1RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountBalanceV1RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountBalanceV1RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountBalanceV1RequestValidationError) ErrorName() string {
	return "GetAccountBalanceV1RequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountBalanceV1RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountBalanceV1Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountBalanceV1RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountBalanceV1RequestValidationError{}

// Validate checks the field values on GetAccountBalanceV1Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountBalanceV1Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountBalanceV1Response with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountBalanceV1ResponseMultiError, or nil if none found.
func (m *GetAccountBalanceV1Response) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountBalanceV1Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceV1ResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLedgerBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLedgerBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceV1ResponseValidationError{
				field:  "LedgerBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceV1ResponseValidationError{
				field:  "AvailableBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClearanceBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "ClearanceBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "ClearanceBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClearanceBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceV1ResponseValidationError{
				field:  "ClearanceBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLienBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "LienBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "LienBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLienBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceV1ResponseValidationError{
				field:  "LienBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBalanceAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceV1ResponseValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceV1ResponseValidationError{
				field:  "BalanceAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FreezeRawCode

	// no validation rules for FreezeReason

	if len(errors) > 0 {
		return GetAccountBalanceV1ResponseMultiError(errors)
	}

	return nil
}

// GetAccountBalanceV1ResponseMultiError is an error wrapping multiple
// validation errors returned by GetAccountBalanceV1Response.ValidateAll() if
// the designated constraints aren't met.
type GetAccountBalanceV1ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountBalanceV1ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountBalanceV1ResponseMultiError) AllErrors() []error { return m }

// GetAccountBalanceV1ResponseValidationError is the validation error returned
// by GetAccountBalanceV1Response.Validate if the designated constraints
// aren't met.
type GetAccountBalanceV1ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountBalanceV1ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountBalanceV1ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountBalanceV1ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountBalanceV1ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountBalanceV1ResponseValidationError) ErrorName() string {
	return "GetAccountBalanceV1ResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountBalanceV1ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountBalanceV1Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountBalanceV1ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountBalanceV1ResponseValidationError{}

// Validate checks the field values on CreateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAccountRequestMultiError, or nil if none found.
func (m *CreateAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PrimaryAccountHolderId

	// no validation rules for ReqId

	// no validation rules for ForceRetry

	// no validation rules for Sku

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetOptions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "Options",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOptions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountRequestValidationError{
				field:  "Options",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAccountRequestMultiError(errors)
	}

	return nil
}

// CreateAccountRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAccountRequestMultiError) AllErrors() []error { return m }

// CreateAccountRequestValidationError is the validation error returned by
// CreateAccountRequest.Validate if the designated constraints aren't met.
type CreateAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAccountRequestValidationError) ErrorName() string {
	return "CreateAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAccountRequestValidationError{}

// Validate checks the field values on CreateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAccountResponseMultiError, or nil if none found.
func (m *CreateAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAccountResponseMultiError(errors)
	}

	return nil
}

// CreateAccountResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAccountResponseMultiError) AllErrors() []error { return m }

// CreateAccountResponseValidationError is the validation error returned by
// CreateAccountResponse.Validate if the designated constraints aren't met.
type CreateAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAccountResponseValidationError) ErrorName() string {
	return "CreateAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAccountResponseValidationError{}

// Validate checks the field values on BankAccountIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BankAccountIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankAccountIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BankAccountIdentifierMultiError, or nil if none found.
func (m *BankAccountIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *BankAccountIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNo

	// no validation rules for IfscCode

	if len(errors) > 0 {
		return BankAccountIdentifierMultiError(errors)
	}

	return nil
}

// BankAccountIdentifierMultiError is an error wrapping multiple validation
// errors returned by BankAccountIdentifier.ValidateAll() if the designated
// constraints aren't met.
type BankAccountIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankAccountIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankAccountIdentifierMultiError) AllErrors() []error { return m }

// BankAccountIdentifierValidationError is the validation error returned by
// BankAccountIdentifier.Validate if the designated constraints aren't met.
type BankAccountIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankAccountIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankAccountIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankAccountIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankAccountIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankAccountIdentifierValidationError) ErrorName() string {
	return "BankAccountIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e BankAccountIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankAccountIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankAccountIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankAccountIdentifierValidationError{}

// Validate checks the field values on GetAccountRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountRequestMultiError, or nil if none found.
func (m *GetAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetAccountRequest_Id:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *GetAccountRequest_ExternalId:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountRequestValidationError{
					field:  "ExternalId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetAccountRequest_PrimaryUserId:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PrimaryUserId
	case *GetAccountRequest_ActorId:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	case *GetAccountRequest_AccountNumBankFilter:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAccountNumBankFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountRequestValidationError{
						field:  "AccountNumBankFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountRequestValidationError{
						field:  "AccountNumBankFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAccountNumBankFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountRequestValidationError{
					field:  "AccountNumBankFilter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetAccountRequest_ActorUniqueAccountIdentifier:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActorUniqueAccountIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountRequestValidationError{
						field:  "ActorUniqueAccountIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountRequestValidationError{
						field:  "ActorUniqueAccountIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActorUniqueAccountIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountRequestValidationError{
					field:  "ActorUniqueAccountIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAccountRequestMultiError(errors)
	}

	return nil
}

// GetAccountRequestMultiError is an error wrapping multiple validation errors
// returned by GetAccountRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountRequestMultiError) AllErrors() []error { return m }

// GetAccountRequestValidationError is the validation error returned by
// GetAccountRequest.Validate if the designated constraints aren't met.
type GetAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountRequestValidationError) ErrorName() string {
	return "GetAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountRequestValidationError{}

// Validate checks the field values on GetAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountResponseMultiError, or nil if none found.
func (m *GetAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccountResponseMultiError(errors)
	}

	return nil
}

// GetAccountResponseMultiError is an error wrapping multiple validation errors
// returned by GetAccountResponse.ValidateAll() if the designated constraints
// aren't met.
type GetAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountResponseMultiError) AllErrors() []error { return m }

// GetAccountResponseValidationError is the validation error returned by
// GetAccountResponse.Validate if the designated constraints aren't met.
type GetAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountResponseValidationError) ErrorName() string {
	return "GetAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountResponseValidationError{}

// Validate checks the field values on GetAccountsListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountsListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountsListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountsListRequestMultiError, or nil if none found.
func (m *GetAccountsListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountsListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *GetAccountsListRequest_UserIds:
		if v == nil {
			err := GetAccountsListRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUserIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountsListRequestValidationError{
						field:  "UserIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountsListRequestValidationError{
						field:  "UserIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUserIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountsListRequestValidationError{
					field:  "UserIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetAccountsListRequest_BulkActorIdentifier:
		if v == nil {
			err := GetAccountsListRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBulkActorIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountsListRequestValidationError{
						field:  "BulkActorIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountsListRequestValidationError{
						field:  "BulkActorIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBulkActorIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountsListRequestValidationError{
					field:  "BulkActorIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAccountsListRequestMultiError(errors)
	}

	return nil
}

// GetAccountsListRequestMultiError is an error wrapping multiple validation
// errors returned by GetAccountsListRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAccountsListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountsListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountsListRequestMultiError) AllErrors() []error { return m }

// GetAccountsListRequestValidationError is the validation error returned by
// GetAccountsListRequest.Validate if the designated constraints aren't met.
type GetAccountsListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountsListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountsListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountsListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountsListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountsListRequestValidationError) ErrorName() string {
	return "GetAccountsListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountsListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountsListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountsListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountsListRequestValidationError{}

// Validate checks the field values on GetAccountsListResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountsListResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountsListResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountsListResponseMultiError, or nil if none found.
func (m *GetAccountsListResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountsListResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountsListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountsListResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountsListResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountsListResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountsListResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountsListResponseValidationError{
					field:  fmt.Sprintf("Accounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAccountsListResponseMultiError(errors)
	}

	return nil
}

// GetAccountsListResponseMultiError is an error wrapping multiple validation
// errors returned by GetAccountsListResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAccountsListResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountsListResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountsListResponseMultiError) AllErrors() []error { return m }

// GetAccountsListResponseValidationError is the validation error returned by
// GetAccountsListResponse.Validate if the designated constraints aren't met.
type GetAccountsListResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountsListResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountsListResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountsListResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountsListResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountsListResponseValidationError) ErrorName() string {
	return "GetAccountsListResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountsListResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountsListResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountsListResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountsListResponseValidationError{}

// Validate checks the field values on PrimaryUserIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PrimaryUserIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PrimaryUserIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PrimaryUserIdentifierMultiError, or nil if none found.
func (m *PrimaryUserIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *PrimaryUserIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return PrimaryUserIdentifierMultiError(errors)
	}

	return nil
}

// PrimaryUserIdentifierMultiError is an error wrapping multiple validation
// errors returned by PrimaryUserIdentifier.ValidateAll() if the designated
// constraints aren't met.
type PrimaryUserIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PrimaryUserIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PrimaryUserIdentifierMultiError) AllErrors() []error { return m }

// PrimaryUserIdentifierValidationError is the validation error returned by
// PrimaryUserIdentifier.Validate if the designated constraints aren't met.
type PrimaryUserIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PrimaryUserIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PrimaryUserIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PrimaryUserIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PrimaryUserIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PrimaryUserIdentifierValidationError) ErrorName() string {
	return "PrimaryUserIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e PrimaryUserIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPrimaryUserIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PrimaryUserIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PrimaryUserIdentifierValidationError{}

// Validate checks the field values on BulkActorIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BulkActorIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BulkActorIdentifier with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BulkActorIdentifierMultiError, or nil if none found.
func (m *BulkActorIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *BulkActorIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BulkActorIdentifierMultiError(errors)
	}

	return nil
}

// BulkActorIdentifierMultiError is an error wrapping multiple validation
// errors returned by BulkActorIdentifier.ValidateAll() if the designated
// constraints aren't met.
type BulkActorIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BulkActorIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BulkActorIdentifierMultiError) AllErrors() []error { return m }

// BulkActorIdentifierValidationError is the validation error returned by
// BulkActorIdentifier.Validate if the designated constraints aren't met.
type BulkActorIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BulkActorIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BulkActorIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BulkActorIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BulkActorIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BulkActorIdentifierValidationError) ErrorName() string {
	return "BulkActorIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e BulkActorIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBulkActorIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BulkActorIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BulkActorIdentifierValidationError{}

// Validate checks the field values on ActorUniqueAccountIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActorUniqueAccountIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActorUniqueAccountIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActorUniqueAccountIdentifierMultiError, or nil if none found.
func (m *ActorUniqueAccountIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *ActorUniqueAccountIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for AccountProductOffering

	// no validation rules for PartnerBank

	if len(errors) > 0 {
		return ActorUniqueAccountIdentifierMultiError(errors)
	}

	return nil
}

// ActorUniqueAccountIdentifierMultiError is an error wrapping multiple
// validation errors returned by ActorUniqueAccountIdentifier.ValidateAll() if
// the designated constraints aren't met.
type ActorUniqueAccountIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActorUniqueAccountIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActorUniqueAccountIdentifierMultiError) AllErrors() []error { return m }

// ActorUniqueAccountIdentifierValidationError is the validation error returned
// by ActorUniqueAccountIdentifier.Validate if the designated constraints
// aren't met.
type ActorUniqueAccountIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActorUniqueAccountIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActorUniqueAccountIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActorUniqueAccountIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActorUniqueAccountIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActorUniqueAccountIdentifierValidationError) ErrorName() string {
	return "ActorUniqueAccountIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e ActorUniqueAccountIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActorUniqueAccountIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActorUniqueAccountIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActorUniqueAccountIdentifierValidationError{}

// Validate checks the field values on GetAccountBalanceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountBalanceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountBalanceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountBalanceRequestMultiError, or nil if none found.
func (m *GetAccountBalanceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountBalanceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for DataFreshness

	switch v := m.Identifier.(type) {
	case *GetAccountBalanceRequest_Id:
		if v == nil {
			err := GetAccountBalanceRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *GetAccountBalanceRequest_ExternalId:
		if v == nil {
			err := GetAccountBalanceRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountBalanceRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountBalanceRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountBalanceRequestValidationError{
					field:  "ExternalId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAccountBalanceRequestMultiError(errors)
	}

	return nil
}

// GetAccountBalanceRequestMultiError is an error wrapping multiple validation
// errors returned by GetAccountBalanceRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAccountBalanceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountBalanceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountBalanceRequestMultiError) AllErrors() []error { return m }

// GetAccountBalanceRequestValidationError is the validation error returned by
// GetAccountBalanceRequest.Validate if the designated constraints aren't met.
type GetAccountBalanceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountBalanceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountBalanceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountBalanceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountBalanceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountBalanceRequestValidationError) ErrorName() string {
	return "GetAccountBalanceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountBalanceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountBalanceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountBalanceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountBalanceRequestValidationError{}

// Validate checks the field values on GetAccountBalanceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountBalanceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountBalanceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountBalanceResponseMultiError, or nil if none found.
func (m *GetAccountBalanceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountBalanceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceResponseValidationError{
				field:  "AvailableBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLedgerBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLedgerBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceResponseValidationError{
				field:  "LedgerBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLastUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceResponseValidationError{
					field:  "LastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceResponseValidationError{
					field:  "LastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceResponseValidationError{
				field:  "LastUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccountBalanceResponseMultiError(errors)
	}

	return nil
}

// GetAccountBalanceResponseMultiError is an error wrapping multiple validation
// errors returned by GetAccountBalanceResponse.ValidateAll() if the
// designated constraints aren't met.
type GetAccountBalanceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountBalanceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountBalanceResponseMultiError) AllErrors() []error { return m }

// GetAccountBalanceResponseValidationError is the validation error returned by
// GetAccountBalanceResponse.Validate if the designated constraints aren't met.
type GetAccountBalanceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountBalanceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountBalanceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountBalanceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountBalanceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountBalanceResponseValidationError) ErrorName() string {
	return "GetAccountBalanceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountBalanceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountBalanceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountBalanceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountBalanceResponseValidationError{}

// Validate checks the field values on UpdateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAccountRequestMultiError, or nil if none found.
func (m *UpdateAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAccountRequestValidationError{
					field:  "Balance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAccountRequestValidationError{
					field:  "Balance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAccountRequestValidationError{
				field:  "Balance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConstraints()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAccountRequestValidationError{
					field:  "Constraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAccountRequestValidationError{
					field:  "Constraints",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConstraints()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAccountRequestValidationError{
				field:  "Constraints",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAccountRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAccountRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAccountRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmailId

	// no validation rules for Sku

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetSkuInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAccountRequestValidationError{
					field:  "SkuInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAccountRequestValidationError{
					field:  "SkuInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkuInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAccountRequestValidationError{
				field:  "SkuInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Identifier.(type) {
	case *UpdateAccountRequest_Id:
		if v == nil {
			err := UpdateAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *UpdateAccountRequest_ExternalId:
		if v == nil {
			err := UpdateAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateAccountRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateAccountRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateAccountRequestValidationError{
					field:  "ExternalId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *UpdateAccountRequest_PrimaryAccountHolder:
		if v == nil {
			err := UpdateAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PrimaryAccountHolder
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UpdateAccountRequestMultiError(errors)
	}

	return nil
}

// UpdateAccountRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAccountRequestMultiError) AllErrors() []error { return m }

// UpdateAccountRequestValidationError is the validation error returned by
// UpdateAccountRequest.Validate if the designated constraints aren't met.
type UpdateAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAccountRequestValidationError) ErrorName() string {
	return "UpdateAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAccountRequestValidationError{}

// Validate checks the field values on UpdateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateAccountResponseMultiError, or nil if none found.
func (m *UpdateAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateAccountResponseMultiError(errors)
	}

	return nil
}

// UpdateAccountResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateAccountResponseMultiError) AllErrors() []error { return m }

// UpdateAccountResponseValidationError is the validation error returned by
// UpdateAccountResponse.Validate if the designated constraints aren't met.
type UpdateAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateAccountResponseValidationError) ErrorName() string {
	return "UpdateAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateAccountResponseValidationError{}

// Validate checks the field values on GetOpeningBalanceRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOpeningBalanceRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOpeningBalanceRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOpeningBalanceRequestMultiError, or nil if none found.
func (m *GetOpeningBalanceRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOpeningBalanceRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDate() == nil {
		err := GetOpeningBalanceRequestValidationError{
			field:  "Date",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceRequestValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceRequestValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetOpeningBalanceRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	switch v := m.Identifier.(type) {
	case *GetOpeningBalanceRequest_Id:
		if v == nil {
			err := GetOpeningBalanceRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *GetOpeningBalanceRequest_ExternalId:
		if v == nil {
			err := GetOpeningBalanceRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetOpeningBalanceRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetOpeningBalanceRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetOpeningBalanceRequestValidationError{
					field:  "ExternalId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetOpeningBalanceRequest_PrimaryAccountHolderActor:
		if v == nil {
			err := GetOpeningBalanceRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PrimaryAccountHolderActor
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetOpeningBalanceRequestMultiError(errors)
	}

	return nil
}

// GetOpeningBalanceRequestMultiError is an error wrapping multiple validation
// errors returned by GetOpeningBalanceRequest.ValidateAll() if the designated
// constraints aren't met.
type GetOpeningBalanceRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOpeningBalanceRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOpeningBalanceRequestMultiError) AllErrors() []error { return m }

// GetOpeningBalanceRequestValidationError is the validation error returned by
// GetOpeningBalanceRequest.Validate if the designated constraints aren't met.
type GetOpeningBalanceRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOpeningBalanceRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOpeningBalanceRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOpeningBalanceRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOpeningBalanceRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOpeningBalanceRequestValidationError) ErrorName() string {
	return "GetOpeningBalanceRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetOpeningBalanceRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOpeningBalanceRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOpeningBalanceRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOpeningBalanceRequestValidationError{}

// Validate checks the field values on GetOpeningBalanceResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetOpeningBalanceResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetOpeningBalanceResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetOpeningBalanceResponseMultiError, or nil if none found.
func (m *GetOpeningBalanceResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetOpeningBalanceResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOpeningBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "OpeningBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetOpeningBalanceResponseValidationError{
					field:  "OpeningBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOpeningBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetOpeningBalanceResponseValidationError{
				field:  "OpeningBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetOpeningBalanceResponseMultiError(errors)
	}

	return nil
}

// GetOpeningBalanceResponseMultiError is an error wrapping multiple validation
// errors returned by GetOpeningBalanceResponse.ValidateAll() if the
// designated constraints aren't met.
type GetOpeningBalanceResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetOpeningBalanceResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetOpeningBalanceResponseMultiError) AllErrors() []error { return m }

// GetOpeningBalanceResponseValidationError is the validation error returned by
// GetOpeningBalanceResponse.Validate if the designated constraints aren't met.
type GetOpeningBalanceResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetOpeningBalanceResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetOpeningBalanceResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetOpeningBalanceResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetOpeningBalanceResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetOpeningBalanceResponseValidationError) ErrorName() string {
	return "GetOpeningBalanceResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetOpeningBalanceResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetOpeningBalanceResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetOpeningBalanceResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetOpeningBalanceResponseValidationError{}

// Validate checks the field values on GetAccountBalanceWithSummaryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAccountBalanceWithSummaryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountBalanceWithSummaryRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAccountBalanceWithSummaryRequestMultiError, or nil if none found.
func (m *GetAccountBalanceWithSummaryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountBalanceWithSummaryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if _, ok := _GetAccountBalanceWithSummaryRequest_TimeRange_NotInLookup[m.GetTimeRange()]; ok {
		err := GetAccountBalanceWithSummaryRequestValidationError{
			field:  "TimeRange",
			reason: "value must not be in list [RANGE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ForceBalanceUpdate

	switch v := m.Identifier.(type) {
	case *GetAccountBalanceWithSummaryRequest_Id:
		if v == nil {
			err := GetAccountBalanceWithSummaryRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *GetAccountBalanceWithSummaryRequest_ExternalId:
		if v == nil {
			err := GetAccountBalanceWithSummaryRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountBalanceWithSummaryRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountBalanceWithSummaryRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountBalanceWithSummaryRequestValidationError{
					field:  "ExternalId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetAccountBalanceWithSummaryRequest_PrimaryAccountHolderActor:
		if v == nil {
			err := GetAccountBalanceWithSummaryRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PrimaryAccountHolderActor
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAccountBalanceWithSummaryRequestMultiError(errors)
	}

	return nil
}

// GetAccountBalanceWithSummaryRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAccountBalanceWithSummaryRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAccountBalanceWithSummaryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountBalanceWithSummaryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountBalanceWithSummaryRequestMultiError) AllErrors() []error { return m }

// GetAccountBalanceWithSummaryRequestValidationError is the validation error
// returned by GetAccountBalanceWithSummaryRequest.Validate if the designated
// constraints aren't met.
type GetAccountBalanceWithSummaryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountBalanceWithSummaryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountBalanceWithSummaryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountBalanceWithSummaryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountBalanceWithSummaryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountBalanceWithSummaryRequestValidationError) ErrorName() string {
	return "GetAccountBalanceWithSummaryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountBalanceWithSummaryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountBalanceWithSummaryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountBalanceWithSummaryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountBalanceWithSummaryRequestValidationError{}

var _GetAccountBalanceWithSummaryRequest_TimeRange_NotInLookup = map[GetAccountBalanceWithSummaryRequest_Range]struct{}{
	0: {},
}

// Validate checks the field values on GetAccountBalanceWithSummaryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetAccountBalanceWithSummaryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountBalanceWithSummaryResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAccountBalanceWithSummaryResponseMultiError, or nil if none found.
func (m *GetAccountBalanceWithSummaryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountBalanceWithSummaryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceWithSummaryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOpeningBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "OpeningBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "OpeningBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOpeningBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceWithSummaryResponseValidationError{
				field:  "OpeningBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAvailableBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "AvailableBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAvailableBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceWithSummaryResponseValidationError{
				field:  "AvailableBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLedgerBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "LedgerBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLedgerBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceWithSummaryResponseValidationError{
				field:  "LedgerBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalSpent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "TotalSpent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "TotalSpent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalSpent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceWithSummaryResponseValidationError{
				field:  "TotalSpent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalCredit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "TotalCredit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "TotalCredit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalCredit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceWithSummaryResponseValidationError{
				field:  "TotalCredit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalDebit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "TotalDebit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "TotalDebit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalDebit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceWithSummaryResponseValidationError{
				field:  "TotalDebit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBucketDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
						field:  fmt.Sprintf("BucketDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
						field:  fmt.Sprintf("BucketDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountBalanceWithSummaryResponseValidationError{
					field:  fmt.Sprintf("BucketDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsComputedBalanceStale

	if all {
		switch v := interface{}(m.GetBalanceAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountBalanceWithSummaryResponseValidationError{
					field:  "BalanceAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBalanceAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountBalanceWithSummaryResponseValidationError{
				field:  "BalanceAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsBalanceStale

	if len(errors) > 0 {
		return GetAccountBalanceWithSummaryResponseMultiError(errors)
	}

	return nil
}

// GetAccountBalanceWithSummaryResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAccountBalanceWithSummaryResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAccountBalanceWithSummaryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountBalanceWithSummaryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountBalanceWithSummaryResponseMultiError) AllErrors() []error { return m }

// GetAccountBalanceWithSummaryResponseValidationError is the validation error
// returned by GetAccountBalanceWithSummaryResponse.Validate if the designated
// constraints aren't met.
type GetAccountBalanceWithSummaryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountBalanceWithSummaryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountBalanceWithSummaryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountBalanceWithSummaryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountBalanceWithSummaryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountBalanceWithSummaryResponseValidationError) ErrorName() string {
	return "GetAccountBalanceWithSummaryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountBalanceWithSummaryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountBalanceWithSummaryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountBalanceWithSummaryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountBalanceWithSummaryResponseValidationError{}

// Validate checks the field values on AccountSummaryBucketDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountSummaryBucketDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountSummaryBucketDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountSummaryBucketDetailsMultiError, or nil if none found.
func (m *AccountSummaryBucketDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountSummaryBucketDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Bucket

	if all {
		switch v := interface{}(m.GetValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AccountSummaryBucketDetailsValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AccountSummaryBucketDetailsValidationError{
					field:  "Value",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AccountSummaryBucketDetailsValidationError{
				field:  "Value",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PercentChange

	for idx, item := range m.GetSections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AccountSummaryBucketDetailsValidationError{
						field:  fmt.Sprintf("Sections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AccountSummaryBucketDetailsValidationError{
						field:  fmt.Sprintf("Sections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AccountSummaryBucketDetailsValidationError{
					field:  fmt.Sprintf("Sections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AccountSummaryBucketDetailsMultiError(errors)
	}

	return nil
}

// AccountSummaryBucketDetailsMultiError is an error wrapping multiple
// validation errors returned by AccountSummaryBucketDetails.ValidateAll() if
// the designated constraints aren't met.
type AccountSummaryBucketDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountSummaryBucketDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountSummaryBucketDetailsMultiError) AllErrors() []error { return m }

// AccountSummaryBucketDetailsValidationError is the validation error returned
// by AccountSummaryBucketDetails.Validate if the designated constraints
// aren't met.
type AccountSummaryBucketDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountSummaryBucketDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountSummaryBucketDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountSummaryBucketDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountSummaryBucketDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountSummaryBucketDetailsValidationError) ErrorName() string {
	return "AccountSummaryBucketDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AccountSummaryBucketDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountSummaryBucketDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountSummaryBucketDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountSummaryBucketDetailsValidationError{}

// Validate checks the field values on GetListOfActiveAccountsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetListOfActiveAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetListOfActiveAccountsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetListOfActiveAccountsRequestMultiError, or nil if none found.
func (m *GetListOfActiveAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetListOfActiveAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCreatedBefore()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetListOfActiveAccountsRequestValidationError{
					field:  "CreatedBefore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetListOfActiveAccountsRequestValidationError{
					field:  "CreatedBefore",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedBefore()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetListOfActiveAccountsRequestValidationError{
				field:  "CreatedBefore",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAfter()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetListOfActiveAccountsRequestValidationError{
					field:  "CreatedAfter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetListOfActiveAccountsRequestValidationError{
					field:  "CreatedAfter",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAfter()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetListOfActiveAccountsRequestValidationError{
				field:  "CreatedAfter",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PageSize

	// no validation rules for Offset

	if len(errors) > 0 {
		return GetListOfActiveAccountsRequestMultiError(errors)
	}

	return nil
}

// GetListOfActiveAccountsRequestMultiError is an error wrapping multiple
// validation errors returned by GetListOfActiveAccountsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetListOfActiveAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetListOfActiveAccountsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetListOfActiveAccountsRequestMultiError) AllErrors() []error { return m }

// GetListOfActiveAccountsRequestValidationError is the validation error
// returned by GetListOfActiveAccountsRequest.Validate if the designated
// constraints aren't met.
type GetListOfActiveAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetListOfActiveAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetListOfActiveAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetListOfActiveAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetListOfActiveAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetListOfActiveAccountsRequestValidationError) ErrorName() string {
	return "GetListOfActiveAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetListOfActiveAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetListOfActiveAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetListOfActiveAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetListOfActiveAccountsRequestValidationError{}

// Validate checks the field values on GetListOfActiveAccountsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetListOfActiveAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetListOfActiveAccountsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetListOfActiveAccountsResponseMultiError, or nil if none found.
func (m *GetListOfActiveAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetListOfActiveAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAccount() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetListOfActiveAccountsResponseValidationError{
						field:  fmt.Sprintf("Account[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetListOfActiveAccountsResponseValidationError{
						field:  fmt.Sprintf("Account[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetListOfActiveAccountsResponseValidationError{
					field:  fmt.Sprintf("Account[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetListOfActiveAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetListOfActiveAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetListOfActiveAccountsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccountList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetListOfActiveAccountsResponseValidationError{
						field:  fmt.Sprintf("AccountList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetListOfActiveAccountsResponseValidationError{
						field:  fmt.Sprintf("AccountList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetListOfActiveAccountsResponseValidationError{
					field:  fmt.Sprintf("AccountList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextOffset

	if len(errors) > 0 {
		return GetListOfActiveAccountsResponseMultiError(errors)
	}

	return nil
}

// GetListOfActiveAccountsResponseMultiError is an error wrapping multiple
// validation errors returned by GetListOfActiveAccountsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetListOfActiveAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetListOfActiveAccountsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetListOfActiveAccountsResponseMultiError) AllErrors() []error { return m }

// GetListOfActiveAccountsResponseValidationError is the validation error
// returned by GetListOfActiveAccountsResponse.Validate if the designated
// constraints aren't met.
type GetListOfActiveAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetListOfActiveAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetListOfActiveAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetListOfActiveAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetListOfActiveAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetListOfActiveAccountsResponseValidationError) ErrorName() string {
	return "GetListOfActiveAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetListOfActiveAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetListOfActiveAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetListOfActiveAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetListOfActiveAccountsResponseValidationError{}

// Validate checks the field values on CloseAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CloseAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloseAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CloseAccountRequestMultiError, or nil if none found.
func (m *CloseAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CloseAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *CloseAccountRequest_Id:
		if v == nil {
			err := CloseAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *CloseAccountRequest_ExternalId:
		if v == nil {
			err := CloseAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CloseAccountRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CloseAccountRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CloseAccountRequestValidationError{
					field:  "ExternalId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CloseAccountRequest_PrimaryUserId:
		if v == nil {
			err := CloseAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PrimaryUserId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CloseAccountRequestMultiError(errors)
	}

	return nil
}

// CloseAccountRequestMultiError is an error wrapping multiple validation
// errors returned by CloseAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type CloseAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloseAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloseAccountRequestMultiError) AllErrors() []error { return m }

// CloseAccountRequestValidationError is the validation error returned by
// CloseAccountRequest.Validate if the designated constraints aren't met.
type CloseAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloseAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloseAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloseAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloseAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloseAccountRequestValidationError) ErrorName() string {
	return "CloseAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CloseAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloseAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloseAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloseAccountRequestValidationError{}

// Validate checks the field values on CloseAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CloseAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CloseAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CloseAccountResponseMultiError, or nil if none found.
func (m *CloseAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CloseAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CloseAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CloseAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CloseAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CloseAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CloseAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CloseAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CloseAccountResponseMultiError(errors)
	}

	return nil
}

// CloseAccountResponseMultiError is an error wrapping multiple validation
// errors returned by CloseAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type CloseAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CloseAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CloseAccountResponseMultiError) AllErrors() []error { return m }

// CloseAccountResponseValidationError is the validation error returned by
// CloseAccountResponse.Validate if the designated constraints aren't met.
type CloseAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CloseAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CloseAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CloseAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CloseAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CloseAccountResponseValidationError) ErrorName() string {
	return "CloseAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CloseAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCloseAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CloseAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CloseAccountResponseValidationError{}

// Validate checks the field values on ReopenAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReopenAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReopenAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReopenAccountRequestMultiError, or nil if none found.
func (m *ReopenAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReopenAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Identifier.(type) {
	case *ReopenAccountRequest_Id:
		if v == nil {
			err := ReopenAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *ReopenAccountRequest_ExternalId:
		if v == nil {
			err := ReopenAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReopenAccountRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReopenAccountRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReopenAccountRequestValidationError{
					field:  "ExternalId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *ReopenAccountRequest_PrimaryUserId:
		if v == nil {
			err := ReopenAccountRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PrimaryUserId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ReopenAccountRequestMultiError(errors)
	}

	return nil
}

// ReopenAccountRequestMultiError is an error wrapping multiple validation
// errors returned by ReopenAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type ReopenAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReopenAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReopenAccountRequestMultiError) AllErrors() []error { return m }

// ReopenAccountRequestValidationError is the validation error returned by
// ReopenAccountRequest.Validate if the designated constraints aren't met.
type ReopenAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReopenAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReopenAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReopenAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReopenAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReopenAccountRequestValidationError) ErrorName() string {
	return "ReopenAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReopenAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReopenAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReopenAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReopenAccountRequestValidationError{}

// Validate checks the field values on ReopenAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReopenAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReopenAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReopenAccountResponseMultiError, or nil if none found.
func (m *ReopenAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ReopenAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReopenAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReopenAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReopenAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ReopenAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ReopenAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ReopenAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ReopenAccountResponseMultiError(errors)
	}

	return nil
}

// ReopenAccountResponseMultiError is an error wrapping multiple validation
// errors returned by ReopenAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type ReopenAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReopenAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReopenAccountResponseMultiError) AllErrors() []error { return m }

// ReopenAccountResponseValidationError is the validation error returned by
// ReopenAccountResponse.Validate if the designated constraints aren't met.
type ReopenAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReopenAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReopenAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReopenAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReopenAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReopenAccountResponseValidationError) ErrorName() string {
	return "ReopenAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ReopenAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReopenAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReopenAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReopenAccountResponseValidationError{}

// Validate checks the field values on IsTxnAllowedRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsTxnAllowedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsTxnAllowedRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsTxnAllowedRequestMultiError, or nil if none found.
func (m *IsTxnAllowedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsTxnAllowedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTxnAllowedRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTxnAllowedRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTxnAllowedRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TxnType

	switch v := m.Identifier.(type) {
	case *IsTxnAllowedRequest_Id:
		if v == nil {
			err := IsTxnAllowedRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Id
	case *IsTxnAllowedRequest_ExternalId:
		if v == nil {
			err := IsTxnAllowedRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetExternalId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IsTxnAllowedRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IsTxnAllowedRequestValidationError{
						field:  "ExternalId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetExternalId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IsTxnAllowedRequestValidationError{
					field:  "ExternalId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *IsTxnAllowedRequest_PrimaryAccountHolderActor:
		if v == nil {
			err := IsTxnAllowedRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for PrimaryAccountHolderActor
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return IsTxnAllowedRequestMultiError(errors)
	}

	return nil
}

// IsTxnAllowedRequestMultiError is an error wrapping multiple validation
// errors returned by IsTxnAllowedRequest.ValidateAll() if the designated
// constraints aren't met.
type IsTxnAllowedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsTxnAllowedRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsTxnAllowedRequestMultiError) AllErrors() []error { return m }

// IsTxnAllowedRequestValidationError is the validation error returned by
// IsTxnAllowedRequest.Validate if the designated constraints aren't met.
type IsTxnAllowedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsTxnAllowedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsTxnAllowedRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsTxnAllowedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsTxnAllowedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsTxnAllowedRequestValidationError) ErrorName() string {
	return "IsTxnAllowedRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsTxnAllowedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsTxnAllowedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsTxnAllowedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsTxnAllowedRequestValidationError{}

// Validate checks the field values on IsTxnAllowedResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsTxnAllowedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsTxnAllowedResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsTxnAllowedResponseMultiError, or nil if none found.
func (m *IsTxnAllowedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsTxnAllowedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTxnAllowedResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTxnAllowedResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTxnAllowedResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreditAmountAllowed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTxnAllowedResponseValidationError{
					field:  "CreditAmountAllowed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTxnAllowedResponseValidationError{
					field:  "CreditAmountAllowed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditAmountAllowed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTxnAllowedResponseValidationError{
				field:  "CreditAmountAllowed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountFreezeDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTxnAllowedResponseValidationError{
					field:  "AccountFreezeDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTxnAllowedResponseValidationError{
					field:  "AccountFreezeDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountFreezeDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTxnAllowedResponseValidationError{
				field:  "AccountFreezeDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NotAllowedReason

	if all {
		switch v := interface{}(m.GetSavingsTxnLimits()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTxnAllowedResponseValidationError{
					field:  "SavingsTxnLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTxnAllowedResponseValidationError{
					field:  "SavingsTxnLimits",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSavingsTxnLimits()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTxnAllowedResponseValidationError{
				field:  "SavingsTxnLimits",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IsTxnAllowedResponseMultiError(errors)
	}

	return nil
}

// IsTxnAllowedResponseMultiError is an error wrapping multiple validation
// errors returned by IsTxnAllowedResponse.ValidateAll() if the designated
// constraints aren't met.
type IsTxnAllowedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsTxnAllowedResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsTxnAllowedResponseMultiError) AllErrors() []error { return m }

// IsTxnAllowedResponseValidationError is the validation error returned by
// IsTxnAllowedResponse.Validate if the designated constraints aren't met.
type IsTxnAllowedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsTxnAllowedResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsTxnAllowedResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsTxnAllowedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsTxnAllowedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsTxnAllowedResponseValidationError) ErrorName() string {
	return "IsTxnAllowedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsTxnAllowedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsTxnAllowedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsTxnAllowedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsTxnAllowedResponseValidationError{}

// Validate checks the field values on StoreClosedAccountBalTransferDataRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *StoreClosedAccountBalTransferDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoreClosedAccountBalTransferDataRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// StoreClosedAccountBalTransferDataRequestMultiError, or nil if none found.
func (m *StoreClosedAccountBalTransferDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreClosedAccountBalTransferDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreClosedAccountBalTransferDataRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreClosedAccountBalTransferDataRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreClosedAccountBalTransferDataRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoreClosedAccountBalTransferDataRequestMultiError(errors)
	}

	return nil
}

// StoreClosedAccountBalTransferDataRequestMultiError is an error wrapping
// multiple validation errors returned by
// StoreClosedAccountBalTransferDataRequest.ValidateAll() if the designated
// constraints aren't met.
type StoreClosedAccountBalTransferDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreClosedAccountBalTransferDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreClosedAccountBalTransferDataRequestMultiError) AllErrors() []error { return m }

// StoreClosedAccountBalTransferDataRequestValidationError is the validation
// error returned by StoreClosedAccountBalTransferDataRequest.Validate if the
// designated constraints aren't met.
type StoreClosedAccountBalTransferDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreClosedAccountBalTransferDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoreClosedAccountBalTransferDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoreClosedAccountBalTransferDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoreClosedAccountBalTransferDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoreClosedAccountBalTransferDataRequestValidationError) ErrorName() string {
	return "StoreClosedAccountBalTransferDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StoreClosedAccountBalTransferDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreClosedAccountBalTransferDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreClosedAccountBalTransferDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreClosedAccountBalTransferDataRequestValidationError{}

// Validate checks the field values on
// StoreClosedAccountBalTransferDataResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StoreClosedAccountBalTransferDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoreClosedAccountBalTransferDataResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// StoreClosedAccountBalTransferDataResponseMultiError, or nil if none found.
func (m *StoreClosedAccountBalTransferDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreClosedAccountBalTransferDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreClosedAccountBalTransferDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreClosedAccountBalTransferDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreClosedAccountBalTransferDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoreClosedAccountBalTransferDataResponseMultiError(errors)
	}

	return nil
}

// StoreClosedAccountBalTransferDataResponseMultiError is an error wrapping
// multiple validation errors returned by
// StoreClosedAccountBalTransferDataResponse.ValidateAll() if the designated
// constraints aren't met.
type StoreClosedAccountBalTransferDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreClosedAccountBalTransferDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreClosedAccountBalTransferDataResponseMultiError) AllErrors() []error { return m }

// StoreClosedAccountBalTransferDataResponseValidationError is the validation
// error returned by StoreClosedAccountBalTransferDataResponse.Validate if the
// designated constraints aren't met.
type StoreClosedAccountBalTransferDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreClosedAccountBalTransferDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StoreClosedAccountBalTransferDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StoreClosedAccountBalTransferDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StoreClosedAccountBalTransferDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StoreClosedAccountBalTransferDataResponseValidationError) ErrorName() string {
	return "StoreClosedAccountBalTransferDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StoreClosedAccountBalTransferDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreClosedAccountBalTransferDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreClosedAccountBalTransferDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreClosedAccountBalTransferDataResponseValidationError{}

// Validate checks the field values on
// StoreClosedAccountBalTransferDataFromStatementRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoreClosedAccountBalTransferDataFromStatementRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoreClosedAccountBalTransferDataFromStatementRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoreClosedAccountBalTransferDataFromStatementRequestMultiError, or nil if
// none found.
func (m *StoreClosedAccountBalTransferDataFromStatementRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreClosedAccountBalTransferDataFromStatementRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SavingsAccountId

	if len(errors) > 0 {
		return StoreClosedAccountBalTransferDataFromStatementRequestMultiError(errors)
	}

	return nil
}

// StoreClosedAccountBalTransferDataFromStatementRequestMultiError is an error
// wrapping multiple validation errors returned by
// StoreClosedAccountBalTransferDataFromStatementRequest.ValidateAll() if the
// designated constraints aren't met.
type StoreClosedAccountBalTransferDataFromStatementRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreClosedAccountBalTransferDataFromStatementRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreClosedAccountBalTransferDataFromStatementRequestMultiError) AllErrors() []error {
	return m
}

// StoreClosedAccountBalTransferDataFromStatementRequestValidationError is the
// validation error returned by
// StoreClosedAccountBalTransferDataFromStatementRequest.Validate if the
// designated constraints aren't met.
type StoreClosedAccountBalTransferDataFromStatementRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreClosedAccountBalTransferDataFromStatementRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e StoreClosedAccountBalTransferDataFromStatementRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e StoreClosedAccountBalTransferDataFromStatementRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e StoreClosedAccountBalTransferDataFromStatementRequestValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e StoreClosedAccountBalTransferDataFromStatementRequestValidationError) ErrorName() string {
	return "StoreClosedAccountBalTransferDataFromStatementRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StoreClosedAccountBalTransferDataFromStatementRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreClosedAccountBalTransferDataFromStatementRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreClosedAccountBalTransferDataFromStatementRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreClosedAccountBalTransferDataFromStatementRequestValidationError{}

// Validate checks the field values on
// StoreClosedAccountBalTransferDataFromStatementResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StoreClosedAccountBalTransferDataFromStatementResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// StoreClosedAccountBalTransferDataFromStatementResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StoreClosedAccountBalTransferDataFromStatementResponseMultiError, or nil if
// none found.
func (m *StoreClosedAccountBalTransferDataFromStatementResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StoreClosedAccountBalTransferDataFromStatementResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StoreClosedAccountBalTransferDataFromStatementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StoreClosedAccountBalTransferDataFromStatementResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StoreClosedAccountBalTransferDataFromStatementResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StoreClosedAccountBalTransferDataFromStatementResponseMultiError(errors)
	}

	return nil
}

// StoreClosedAccountBalTransferDataFromStatementResponseMultiError is an error
// wrapping multiple validation errors returned by
// StoreClosedAccountBalTransferDataFromStatementResponse.ValidateAll() if the
// designated constraints aren't met.
type StoreClosedAccountBalTransferDataFromStatementResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StoreClosedAccountBalTransferDataFromStatementResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StoreClosedAccountBalTransferDataFromStatementResponseMultiError) AllErrors() []error {
	return m
}

// StoreClosedAccountBalTransferDataFromStatementResponseValidationError is the
// validation error returned by
// StoreClosedAccountBalTransferDataFromStatementResponse.Validate if the
// designated constraints aren't met.
type StoreClosedAccountBalTransferDataFromStatementResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StoreClosedAccountBalTransferDataFromStatementResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e StoreClosedAccountBalTransferDataFromStatementResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e StoreClosedAccountBalTransferDataFromStatementResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e StoreClosedAccountBalTransferDataFromStatementResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e StoreClosedAccountBalTransferDataFromStatementResponseValidationError) ErrorName() string {
	return "StoreClosedAccountBalTransferDataFromStatementResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StoreClosedAccountBalTransferDataFromStatementResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStoreClosedAccountBalTransferDataFromStatementResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StoreClosedAccountBalTransferDataFromStatementResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StoreClosedAccountBalTransferDataFromStatementResponseValidationError{}

// Validate checks the field values on GetClosedAccountBalTransferDataRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetClosedAccountBalTransferDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetClosedAccountBalTransferDataRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetClosedAccountBalTransferDataRequestMultiError, or nil if none found.
func (m *GetClosedAccountBalTransferDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClosedAccountBalTransferDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SavingsAccountId

	if len(errors) > 0 {
		return GetClosedAccountBalTransferDataRequestMultiError(errors)
	}

	return nil
}

// GetClosedAccountBalTransferDataRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetClosedAccountBalTransferDataRequest.ValidateAll() if the designated
// constraints aren't met.
type GetClosedAccountBalTransferDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClosedAccountBalTransferDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClosedAccountBalTransferDataRequestMultiError) AllErrors() []error { return m }

// GetClosedAccountBalTransferDataRequestValidationError is the validation
// error returned by GetClosedAccountBalTransferDataRequest.Validate if the
// designated constraints aren't met.
type GetClosedAccountBalTransferDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClosedAccountBalTransferDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClosedAccountBalTransferDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClosedAccountBalTransferDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClosedAccountBalTransferDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClosedAccountBalTransferDataRequestValidationError) ErrorName() string {
	return "GetClosedAccountBalTransferDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetClosedAccountBalTransferDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClosedAccountBalTransferDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClosedAccountBalTransferDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClosedAccountBalTransferDataRequestValidationError{}

// Validate checks the field values on GetClosedAccountBalTransferDataResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetClosedAccountBalTransferDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetClosedAccountBalTransferDataResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetClosedAccountBalTransferDataResponseMultiError, or nil if none found.
func (m *GetClosedAccountBalTransferDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetClosedAccountBalTransferDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetClosedAccountBalTransferDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetClosedAccountBalTransferDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetClosedAccountBalTransferDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetEntries() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetClosedAccountBalTransferDataResponseValidationError{
						field:  fmt.Sprintf("Entries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetClosedAccountBalTransferDataResponseValidationError{
						field:  fmt.Sprintf("Entries[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetClosedAccountBalTransferDataResponseValidationError{
					field:  fmt.Sprintf("Entries[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetClosedAccountBalTransferDataResponseMultiError(errors)
	}

	return nil
}

// GetClosedAccountBalTransferDataResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetClosedAccountBalTransferDataResponse.ValidateAll() if the designated
// constraints aren't met.
type GetClosedAccountBalTransferDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetClosedAccountBalTransferDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetClosedAccountBalTransferDataResponseMultiError) AllErrors() []error { return m }

// GetClosedAccountBalTransferDataResponseValidationError is the validation
// error returned by GetClosedAccountBalTransferDataResponse.Validate if the
// designated constraints aren't met.
type GetClosedAccountBalTransferDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetClosedAccountBalTransferDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetClosedAccountBalTransferDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetClosedAccountBalTransferDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetClosedAccountBalTransferDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetClosedAccountBalTransferDataResponseValidationError) ErrorName() string {
	return "GetClosedAccountBalTransferDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetClosedAccountBalTransferDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetClosedAccountBalTransferDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetClosedAccountBalTransferDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetClosedAccountBalTransferDataResponseValidationError{}

// Validate checks the field values on GetEODSavBalanceHistoryRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEODSavBalanceHistoryRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEODSavBalanceHistoryRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEODSavBalanceHistoryRequestMultiError, or nil if none found.
func (m *GetEODSavBalanceHistoryRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEODSavBalanceHistoryRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetStartTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEODSavBalanceHistoryRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEODSavBalanceHistoryRequestValidationError{
					field:  "StartTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEODSavBalanceHistoryRequestValidationError{
				field:  "StartTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEndTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEODSavBalanceHistoryRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEODSavBalanceHistoryRequestValidationError{
					field:  "EndTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEndTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEODSavBalanceHistoryRequestValidationError{
				field:  "EndTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetEODSavBalanceHistoryRequestMultiError(errors)
	}

	return nil
}

// GetEODSavBalanceHistoryRequestMultiError is an error wrapping multiple
// validation errors returned by GetEODSavBalanceHistoryRequest.ValidateAll()
// if the designated constraints aren't met.
type GetEODSavBalanceHistoryRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEODSavBalanceHistoryRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEODSavBalanceHistoryRequestMultiError) AllErrors() []error { return m }

// GetEODSavBalanceHistoryRequestValidationError is the validation error
// returned by GetEODSavBalanceHistoryRequest.Validate if the designated
// constraints aren't met.
type GetEODSavBalanceHistoryRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEODSavBalanceHistoryRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEODSavBalanceHistoryRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEODSavBalanceHistoryRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEODSavBalanceHistoryRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEODSavBalanceHistoryRequestValidationError) ErrorName() string {
	return "GetEODSavBalanceHistoryRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEODSavBalanceHistoryRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEODSavBalanceHistoryRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEODSavBalanceHistoryRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEODSavBalanceHistoryRequestValidationError{}

// Validate checks the field values on GetEODSavBalanceHistoryResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEODSavBalanceHistoryResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEODSavBalanceHistoryResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetEODSavBalanceHistoryResponseMultiError, or nil if none found.
func (m *GetEODSavBalanceHistoryResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEODSavBalanceHistoryResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetEODSavBalanceHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetEODSavBalanceHistoryResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetEODSavBalanceHistoryResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBalanceByDates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetEODSavBalanceHistoryResponseValidationError{
						field:  fmt.Sprintf("BalanceByDates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetEODSavBalanceHistoryResponseValidationError{
						field:  fmt.Sprintf("BalanceByDates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetEODSavBalanceHistoryResponseValidationError{
					field:  fmt.Sprintf("BalanceByDates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetEODSavBalanceHistoryResponseMultiError(errors)
	}

	return nil
}

// GetEODSavBalanceHistoryResponseMultiError is an error wrapping multiple
// validation errors returned by GetEODSavBalanceHistoryResponse.ValidateAll()
// if the designated constraints aren't met.
type GetEODSavBalanceHistoryResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEODSavBalanceHistoryResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEODSavBalanceHistoryResponseMultiError) AllErrors() []error { return m }

// GetEODSavBalanceHistoryResponseValidationError is the validation error
// returned by GetEODSavBalanceHistoryResponse.Validate if the designated
// constraints aren't met.
type GetEODSavBalanceHistoryResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEODSavBalanceHistoryResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEODSavBalanceHistoryResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEODSavBalanceHistoryResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEODSavBalanceHistoryResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEODSavBalanceHistoryResponseValidationError) ErrorName() string {
	return "GetEODSavBalanceHistoryResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetEODSavBalanceHistoryResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEODSavBalanceHistoryResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEODSavBalanceHistoryResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEODSavBalanceHistoryResponseValidationError{}

// Validate checks the field values on BalanceByDate with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BalanceByDate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BalanceByDate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BalanceByDateMultiError, or
// nil if none found.
func (m *BalanceByDate) ValidateAll() error {
	return m.validate(true)
}

func (m *BalanceByDate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceByDateValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceByDateValidationError{
					field:  "Date",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceByDateValidationError{
				field:  "Date",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEodBalance()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceByDateValidationError{
					field:  "EodBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceByDateValidationError{
					field:  "EodBalance",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEodBalance()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceByDateValidationError{
				field:  "EodBalance",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BalanceByDateMultiError(errors)
	}

	return nil
}

// BalanceByDateMultiError is an error wrapping multiple validation errors
// returned by BalanceByDate.ValidateAll() if the designated constraints
// aren't met.
type BalanceByDateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BalanceByDateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BalanceByDateMultiError) AllErrors() []error { return m }

// BalanceByDateValidationError is the validation error returned by
// BalanceByDate.Validate if the designated constraints aren't met.
type BalanceByDateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BalanceByDateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BalanceByDateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BalanceByDateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BalanceByDateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BalanceByDateValidationError) ErrorName() string { return "BalanceByDateValidationError" }

// Error satisfies the builtin error interface
func (e BalanceByDateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBalanceByDate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BalanceByDateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BalanceByDateValidationError{}

// Validate checks the field values on
// UpdateClosedAccountBalTransferDataRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateClosedAccountBalTransferDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateClosedAccountBalTransferDataRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateClosedAccountBalTransferDataRequestMultiError, or nil if none found.
func (m *UpdateClosedAccountBalTransferDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateClosedAccountBalTransferDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateClosedAccountBalTransferDataRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateClosedAccountBalTransferDataRequestValidationError{
					field:  "Data",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateClosedAccountBalTransferDataRequestValidationError{
				field:  "Data",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateClosedAccountBalTransferDataRequestMultiError(errors)
	}

	return nil
}

// UpdateClosedAccountBalTransferDataRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateClosedAccountBalTransferDataRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateClosedAccountBalTransferDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateClosedAccountBalTransferDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateClosedAccountBalTransferDataRequestMultiError) AllErrors() []error { return m }

// UpdateClosedAccountBalTransferDataRequestValidationError is the validation
// error returned by UpdateClosedAccountBalTransferDataRequest.Validate if the
// designated constraints aren't met.
type UpdateClosedAccountBalTransferDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateClosedAccountBalTransferDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateClosedAccountBalTransferDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateClosedAccountBalTransferDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateClosedAccountBalTransferDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateClosedAccountBalTransferDataRequestValidationError) ErrorName() string {
	return "UpdateClosedAccountBalTransferDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateClosedAccountBalTransferDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateClosedAccountBalTransferDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateClosedAccountBalTransferDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateClosedAccountBalTransferDataRequestValidationError{}

// Validate checks the field values on
// UpdateClosedAccountBalTransferDataResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *UpdateClosedAccountBalTransferDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateClosedAccountBalTransferDataResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// UpdateClosedAccountBalTransferDataResponseMultiError, or nil if none found.
func (m *UpdateClosedAccountBalTransferDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateClosedAccountBalTransferDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateClosedAccountBalTransferDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateClosedAccountBalTransferDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateClosedAccountBalTransferDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateClosedAccountBalTransferDataResponseMultiError(errors)
	}

	return nil
}

// UpdateClosedAccountBalTransferDataResponseMultiError is an error wrapping
// multiple validation errors returned by
// UpdateClosedAccountBalTransferDataResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateClosedAccountBalTransferDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateClosedAccountBalTransferDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateClosedAccountBalTransferDataResponseMultiError) AllErrors() []error { return m }

// UpdateClosedAccountBalTransferDataResponseValidationError is the validation
// error returned by UpdateClosedAccountBalTransferDataResponse.Validate if
// the designated constraints aren't met.
type UpdateClosedAccountBalTransferDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateClosedAccountBalTransferDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateClosedAccountBalTransferDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateClosedAccountBalTransferDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateClosedAccountBalTransferDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateClosedAccountBalTransferDataResponseValidationError) ErrorName() string {
	return "UpdateClosedAccountBalTransferDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateClosedAccountBalTransferDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateClosedAccountBalTransferDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateClosedAccountBalTransferDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateClosedAccountBalTransferDataResponseValidationError{}

// Validate checks the field values on FetchOrCreateSignAttemptRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchOrCreateSignAttemptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchOrCreateSignAttemptRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchOrCreateSignAttemptRequestMultiError, or nil if none found.
func (m *FetchOrCreateSignAttemptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchOrCreateSignAttemptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return FetchOrCreateSignAttemptRequestMultiError(errors)
	}

	return nil
}

// FetchOrCreateSignAttemptRequestMultiError is an error wrapping multiple
// validation errors returned by FetchOrCreateSignAttemptRequest.ValidateAll()
// if the designated constraints aren't met.
type FetchOrCreateSignAttemptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchOrCreateSignAttemptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchOrCreateSignAttemptRequestMultiError) AllErrors() []error { return m }

// FetchOrCreateSignAttemptRequestValidationError is the validation error
// returned by FetchOrCreateSignAttemptRequest.Validate if the designated
// constraints aren't met.
type FetchOrCreateSignAttemptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchOrCreateSignAttemptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchOrCreateSignAttemptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchOrCreateSignAttemptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchOrCreateSignAttemptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchOrCreateSignAttemptRequestValidationError) ErrorName() string {
	return "FetchOrCreateSignAttemptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchOrCreateSignAttemptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchOrCreateSignAttemptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchOrCreateSignAttemptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchOrCreateSignAttemptRequestValidationError{}

// Validate checks the field values on FetchOrCreateSignAttemptResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FetchOrCreateSignAttemptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchOrCreateSignAttemptResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchOrCreateSignAttemptResponseMultiError, or nil if none found.
func (m *FetchOrCreateSignAttemptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchOrCreateSignAttemptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchOrCreateSignAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchOrCreateSignAttemptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchOrCreateSignAttemptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SignStatus

	// no validation rules for SignUrl

	// no validation rules for ExitUrl

	if len(errors) > 0 {
		return FetchOrCreateSignAttemptResponseMultiError(errors)
	}

	return nil
}

// FetchOrCreateSignAttemptResponseMultiError is an error wrapping multiple
// validation errors returned by
// FetchOrCreateSignAttemptResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchOrCreateSignAttemptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchOrCreateSignAttemptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchOrCreateSignAttemptResponseMultiError) AllErrors() []error { return m }

// FetchOrCreateSignAttemptResponseValidationError is the validation error
// returned by FetchOrCreateSignAttemptResponse.Validate if the designated
// constraints aren't met.
type FetchOrCreateSignAttemptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchOrCreateSignAttemptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchOrCreateSignAttemptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchOrCreateSignAttemptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchOrCreateSignAttemptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchOrCreateSignAttemptResponseValidationError) ErrorName() string {
	return "FetchOrCreateSignAttemptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchOrCreateSignAttemptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchOrCreateSignAttemptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchOrCreateSignAttemptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchOrCreateSignAttemptResponseValidationError{}

// Validate checks the field values on ActorIdBankFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ActorIdBankFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActorIdBankFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActorIdBankFilterMultiError, or nil if none found.
func (m *ActorIdBankFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *ActorIdBankFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for PartnerBank

	if len(errors) > 0 {
		return ActorIdBankFilterMultiError(errors)
	}

	return nil
}

// ActorIdBankFilterMultiError is an error wrapping multiple validation errors
// returned by ActorIdBankFilter.ValidateAll() if the designated constraints
// aren't met.
type ActorIdBankFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActorIdBankFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActorIdBankFilterMultiError) AllErrors() []error { return m }

// ActorIdBankFilterValidationError is the validation error returned by
// ActorIdBankFilter.Validate if the designated constraints aren't met.
type ActorIdBankFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActorIdBankFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActorIdBankFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActorIdBankFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActorIdBankFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActorIdBankFilterValidationError) ErrorName() string {
	return "ActorIdBankFilterValidationError"
}

// Error satisfies the builtin error interface
func (e ActorIdBankFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActorIdBankFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActorIdBankFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActorIdBankFilterValidationError{}

// Validate checks the field values on AccountNumberBankFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountNumberBankFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountNumberBankFilter with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountNumberBankFilterMultiError, or nil if none found.
func (m *AccountNumberBankFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountNumberBankFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for PartnerBank

	if len(errors) > 0 {
		return AccountNumberBankFilterMultiError(errors)
	}

	return nil
}

// AccountNumberBankFilterMultiError is an error wrapping multiple validation
// errors returned by AccountNumberBankFilter.ValidateAll() if the designated
// constraints aren't met.
type AccountNumberBankFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountNumberBankFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountNumberBankFilterMultiError) AllErrors() []error { return m }

// AccountNumberBankFilterValidationError is the validation error returned by
// AccountNumberBankFilter.Validate if the designated constraints aren't met.
type AccountNumberBankFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountNumberBankFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountNumberBankFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountNumberBankFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountNumberBankFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountNumberBankFilterValidationError) ErrorName() string {
	return "AccountNumberBankFilterValidationError"
}

// Error satisfies the builtin error interface
func (e AccountNumberBankFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountNumberBankFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountNumberBankFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountNumberBankFilterValidationError{}

// Validate checks the field values on GetSavingsAccountEssentialsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSavingsAccountEssentialsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSavingsAccountEssentialsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSavingsAccountEssentialsRequestMultiError, or nil if none found.
func (m *GetSavingsAccountEssentialsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSavingsAccountEssentialsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Filter.(type) {
	case *GetSavingsAccountEssentialsRequest_ActorIdBankFilter:
		if v == nil {
			err := GetSavingsAccountEssentialsRequestValidationError{
				field:  "Filter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActorIdBankFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSavingsAccountEssentialsRequestValidationError{
						field:  "ActorIdBankFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSavingsAccountEssentialsRequestValidationError{
						field:  "ActorIdBankFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActorIdBankFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSavingsAccountEssentialsRequestValidationError{
					field:  "ActorIdBankFilter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetSavingsAccountEssentialsRequest_AccountNumBankFilter:
		if v == nil {
			err := GetSavingsAccountEssentialsRequestValidationError{
				field:  "Filter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAccountNumBankFilter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSavingsAccountEssentialsRequestValidationError{
						field:  "AccountNumBankFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSavingsAccountEssentialsRequestValidationError{
						field:  "AccountNumBankFilter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAccountNumBankFilter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSavingsAccountEssentialsRequestValidationError{
					field:  "AccountNumBankFilter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier:
		if v == nil {
			err := GetSavingsAccountEssentialsRequestValidationError{
				field:  "Filter",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetActorUniqueAccountIdentifier()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSavingsAccountEssentialsRequestValidationError{
						field:  "ActorUniqueAccountIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSavingsAccountEssentialsRequestValidationError{
						field:  "ActorUniqueAccountIdentifier",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetActorUniqueAccountIdentifier()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSavingsAccountEssentialsRequestValidationError{
					field:  "ActorUniqueAccountIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetSavingsAccountEssentialsRequestMultiError(errors)
	}

	return nil
}

// GetSavingsAccountEssentialsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSavingsAccountEssentialsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSavingsAccountEssentialsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSavingsAccountEssentialsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSavingsAccountEssentialsRequestMultiError) AllErrors() []error { return m }

// GetSavingsAccountEssentialsRequestValidationError is the validation error
// returned by GetSavingsAccountEssentialsRequest.Validate if the designated
// constraints aren't met.
type GetSavingsAccountEssentialsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSavingsAccountEssentialsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSavingsAccountEssentialsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSavingsAccountEssentialsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSavingsAccountEssentialsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSavingsAccountEssentialsRequestValidationError) ErrorName() string {
	return "GetSavingsAccountEssentialsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSavingsAccountEssentialsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSavingsAccountEssentialsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSavingsAccountEssentialsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSavingsAccountEssentialsRequestValidationError{}

// Validate checks the field values on SavingsAccountEssentials with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SavingsAccountEssentials) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SavingsAccountEssentials with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SavingsAccountEssentialsMultiError, or nil if none found.
func (m *SavingsAccountEssentials) ValidateAll() error {
	return m.validate(true)
}

func (m *SavingsAccountEssentials) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for AccountNo

	// no validation rules for ActorId

	// no validation rules for IfscCode

	// no validation rules for State

	// no validation rules for PartnerBank

	if all {
		switch v := interface{}(m.GetSkuInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SavingsAccountEssentialsValidationError{
					field:  "SkuInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SavingsAccountEssentialsValidationError{
					field:  "SkuInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSkuInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SavingsAccountEssentialsValidationError{
				field:  "SkuInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CacheVersion

	if len(errors) > 0 {
		return SavingsAccountEssentialsMultiError(errors)
	}

	return nil
}

// SavingsAccountEssentialsMultiError is an error wrapping multiple validation
// errors returned by SavingsAccountEssentials.ValidateAll() if the designated
// constraints aren't met.
type SavingsAccountEssentialsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SavingsAccountEssentialsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SavingsAccountEssentialsMultiError) AllErrors() []error { return m }

// SavingsAccountEssentialsValidationError is the validation error returned by
// SavingsAccountEssentials.Validate if the designated constraints aren't met.
type SavingsAccountEssentialsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SavingsAccountEssentialsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SavingsAccountEssentialsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SavingsAccountEssentialsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SavingsAccountEssentialsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SavingsAccountEssentialsValidationError) ErrorName() string {
	return "SavingsAccountEssentialsValidationError"
}

// Error satisfies the builtin error interface
func (e SavingsAccountEssentialsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSavingsAccountEssentials.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SavingsAccountEssentialsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SavingsAccountEssentialsValidationError{}

// Validate checks the field values on GetSavingsAccountEssentialsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSavingsAccountEssentialsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSavingsAccountEssentialsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSavingsAccountEssentialsResponseMultiError, or nil if none found.
func (m *GetSavingsAccountEssentialsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSavingsAccountEssentialsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSavingsAccountEssentialsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSavingsAccountEssentialsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSavingsAccountEssentialsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSavingsAccountEssentialsResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSavingsAccountEssentialsResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSavingsAccountEssentialsResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSavingsAccountEssentialsResponseMultiError(errors)
	}

	return nil
}

// GetSavingsAccountEssentialsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetSavingsAccountEssentialsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSavingsAccountEssentialsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSavingsAccountEssentialsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSavingsAccountEssentialsResponseMultiError) AllErrors() []error { return m }

// GetSavingsAccountEssentialsResponseValidationError is the validation error
// returned by GetSavingsAccountEssentialsResponse.Validate if the designated
// constraints aren't met.
type GetSavingsAccountEssentialsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSavingsAccountEssentialsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSavingsAccountEssentialsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSavingsAccountEssentialsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSavingsAccountEssentialsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSavingsAccountEssentialsResponseValidationError) ErrorName() string {
	return "GetSavingsAccountEssentialsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSavingsAccountEssentialsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSavingsAccountEssentialsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSavingsAccountEssentialsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSavingsAccountEssentialsResponseValidationError{}

// Validate checks the field values on CreateOrGetSaClosureRequestRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateOrGetSaClosureRequestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrGetSaClosureRequestRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateOrGetSaClosureRequestRequestMultiError, or nil if none found.
func (m *CreateOrGetSaClosureRequestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrGetSaClosureRequestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := CreateOrGetSaClosureRequestRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EntryPoint

	if len(errors) > 0 {
		return CreateOrGetSaClosureRequestRequestMultiError(errors)
	}

	return nil
}

// CreateOrGetSaClosureRequestRequestMultiError is an error wrapping multiple
// validation errors returned by
// CreateOrGetSaClosureRequestRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateOrGetSaClosureRequestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrGetSaClosureRequestRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrGetSaClosureRequestRequestMultiError) AllErrors() []error { return m }

// CreateOrGetSaClosureRequestRequestValidationError is the validation error
// returned by CreateOrGetSaClosureRequestRequest.Validate if the designated
// constraints aren't met.
type CreateOrGetSaClosureRequestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrGetSaClosureRequestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrGetSaClosureRequestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrGetSaClosureRequestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrGetSaClosureRequestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrGetSaClosureRequestRequestValidationError) ErrorName() string {
	return "CreateOrGetSaClosureRequestRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrGetSaClosureRequestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrGetSaClosureRequestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrGetSaClosureRequestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrGetSaClosureRequestRequestValidationError{}

// Validate checks the field values on CreateOrGetSaClosureRequestResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CreateOrGetSaClosureRequestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateOrGetSaClosureRequestResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreateOrGetSaClosureRequestResponseMultiError, or nil if none found.
func (m *CreateOrGetSaClosureRequestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateOrGetSaClosureRequestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrGetSaClosureRequestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrGetSaClosureRequestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrGetSaClosureRequestResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClosureRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateOrGetSaClosureRequestResponseValidationError{
					field:  "ClosureRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateOrGetSaClosureRequestResponseValidationError{
					field:  "ClosureRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClosureRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateOrGetSaClosureRequestResponseValidationError{
				field:  "ClosureRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateOrGetSaClosureRequestResponseMultiError(errors)
	}

	return nil
}

// CreateOrGetSaClosureRequestResponseMultiError is an error wrapping multiple
// validation errors returned by
// CreateOrGetSaClosureRequestResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateOrGetSaClosureRequestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateOrGetSaClosureRequestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateOrGetSaClosureRequestResponseMultiError) AllErrors() []error { return m }

// CreateOrGetSaClosureRequestResponseValidationError is the validation error
// returned by CreateOrGetSaClosureRequestResponse.Validate if the designated
// constraints aren't met.
type CreateOrGetSaClosureRequestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateOrGetSaClosureRequestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateOrGetSaClosureRequestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateOrGetSaClosureRequestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateOrGetSaClosureRequestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateOrGetSaClosureRequestResponseValidationError) ErrorName() string {
	return "CreateOrGetSaClosureRequestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateOrGetSaClosureRequestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateOrGetSaClosureRequestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateOrGetSaClosureRequestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateOrGetSaClosureRequestResponseValidationError{}

// Validate checks the field values on GetActiveSaClosureRequestForUserRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetActiveSaClosureRequestForUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetActiveSaClosureRequestForUserRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetActiveSaClosureRequestForUserRequestMultiError, or nil if none found.
func (m *GetActiveSaClosureRequestForUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActiveSaClosureRequestForUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetActiveSaClosureRequestForUserRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetActiveSaClosureRequestForUserRequestMultiError(errors)
	}

	return nil
}

// GetActiveSaClosureRequestForUserRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetActiveSaClosureRequestForUserRequest.ValidateAll() if the designated
// constraints aren't met.
type GetActiveSaClosureRequestForUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActiveSaClosureRequestForUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActiveSaClosureRequestForUserRequestMultiError) AllErrors() []error { return m }

// GetActiveSaClosureRequestForUserRequestValidationError is the validation
// error returned by GetActiveSaClosureRequestForUserRequest.Validate if the
// designated constraints aren't met.
type GetActiveSaClosureRequestForUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActiveSaClosureRequestForUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActiveSaClosureRequestForUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActiveSaClosureRequestForUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActiveSaClosureRequestForUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActiveSaClosureRequestForUserRequestValidationError) ErrorName() string {
	return "GetActiveSaClosureRequestForUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActiveSaClosureRequestForUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActiveSaClosureRequestForUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActiveSaClosureRequestForUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActiveSaClosureRequestForUserRequestValidationError{}

// Validate checks the field values on GetActiveSaClosureRequestForUserResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetActiveSaClosureRequestForUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetActiveSaClosureRequestForUserResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetActiveSaClosureRequestForUserResponseMultiError, or nil if none found.
func (m *GetActiveSaClosureRequestForUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActiveSaClosureRequestForUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActiveSaClosureRequestForUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActiveSaClosureRequestForUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActiveSaClosureRequestForUserResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClosureRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActiveSaClosureRequestForUserResponseValidationError{
					field:  "ClosureRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActiveSaClosureRequestForUserResponseValidationError{
					field:  "ClosureRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClosureRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActiveSaClosureRequestForUserResponseValidationError{
				field:  "ClosureRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActiveSaClosureRequestForUserResponseMultiError(errors)
	}

	return nil
}

// GetActiveSaClosureRequestForUserResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetActiveSaClosureRequestForUserResponse.ValidateAll() if the designated
// constraints aren't met.
type GetActiveSaClosureRequestForUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActiveSaClosureRequestForUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActiveSaClosureRequestForUserResponseMultiError) AllErrors() []error { return m }

// GetActiveSaClosureRequestForUserResponseValidationError is the validation
// error returned by GetActiveSaClosureRequestForUserResponse.Validate if the
// designated constraints aren't met.
type GetActiveSaClosureRequestForUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActiveSaClosureRequestForUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetActiveSaClosureRequestForUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetActiveSaClosureRequestForUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActiveSaClosureRequestForUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActiveSaClosureRequestForUserResponseValidationError) ErrorName() string {
	return "GetActiveSaClosureRequestForUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActiveSaClosureRequestForUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActiveSaClosureRequestForUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActiveSaClosureRequestForUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActiveSaClosureRequestForUserResponseValidationError{}

// Validate checks the field values on UpdateSaClosureRequestStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateSaClosureRequestStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSaClosureRequestStatusRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateSaClosureRequestStatusRequestMultiError, or nil if none found.
func (m *UpdateSaClosureRequestStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSaClosureRequestStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetClosureRequestId()) < 1 {
		err := UpdateSaClosureRequestStatusRequestValidationError{
			field:  "ClosureRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Status

	// no validation rules for StatusReason

	if len(errors) > 0 {
		return UpdateSaClosureRequestStatusRequestMultiError(errors)
	}

	return nil
}

// UpdateSaClosureRequestStatusRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateSaClosureRequestStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateSaClosureRequestStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSaClosureRequestStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSaClosureRequestStatusRequestMultiError) AllErrors() []error { return m }

// UpdateSaClosureRequestStatusRequestValidationError is the validation error
// returned by UpdateSaClosureRequestStatusRequest.Validate if the designated
// constraints aren't met.
type UpdateSaClosureRequestStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSaClosureRequestStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSaClosureRequestStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSaClosureRequestStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSaClosureRequestStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSaClosureRequestStatusRequestValidationError) ErrorName() string {
	return "UpdateSaClosureRequestStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSaClosureRequestStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSaClosureRequestStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSaClosureRequestStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSaClosureRequestStatusRequestValidationError{}

// Validate checks the field values on UpdateSaClosureRequestStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateSaClosureRequestStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSaClosureRequestStatusResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateSaClosureRequestStatusResponseMultiError, or nil if none found.
func (m *UpdateSaClosureRequestStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSaClosureRequestStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSaClosureRequestStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSaClosureRequestStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSaClosureRequestStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSaClosureRequestStatusResponseMultiError(errors)
	}

	return nil
}

// UpdateSaClosureRequestStatusResponseMultiError is an error wrapping multiple
// validation errors returned by
// UpdateSaClosureRequestStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateSaClosureRequestStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSaClosureRequestStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSaClosureRequestStatusResponseMultiError) AllErrors() []error { return m }

// UpdateSaClosureRequestStatusResponseValidationError is the validation error
// returned by UpdateSaClosureRequestStatusResponse.Validate if the designated
// constraints aren't met.
type UpdateSaClosureRequestStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSaClosureRequestStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSaClosureRequestStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSaClosureRequestStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSaClosureRequestStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSaClosureRequestStatusResponseValidationError) ErrorName() string {
	return "UpdateSaClosureRequestStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSaClosureRequestStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSaClosureRequestStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSaClosureRequestStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSaClosureRequestStatusResponseValidationError{}

// Validate checks the field values on RecordSaClosureUserFeedbackRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordSaClosureUserFeedbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordSaClosureUserFeedbackRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordSaClosureUserFeedbackRequestMultiError, or nil if none found.
func (m *RecordSaClosureUserFeedbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordSaClosureUserFeedbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetClosureRequestId()) < 1 {
		err := RecordSaClosureUserFeedbackRequestValidationError{
			field:  "ClosureRequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetUserFeedback()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordSaClosureUserFeedbackRequestValidationError{
					field:  "UserFeedback",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordSaClosureUserFeedbackRequestValidationError{
					field:  "UserFeedback",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUserFeedback()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordSaClosureUserFeedbackRequestValidationError{
				field:  "UserFeedback",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordSaClosureUserFeedbackRequestMultiError(errors)
	}

	return nil
}

// RecordSaClosureUserFeedbackRequestMultiError is an error wrapping multiple
// validation errors returned by
// RecordSaClosureUserFeedbackRequest.ValidateAll() if the designated
// constraints aren't met.
type RecordSaClosureUserFeedbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordSaClosureUserFeedbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordSaClosureUserFeedbackRequestMultiError) AllErrors() []error { return m }

// RecordSaClosureUserFeedbackRequestValidationError is the validation error
// returned by RecordSaClosureUserFeedbackRequest.Validate if the designated
// constraints aren't met.
type RecordSaClosureUserFeedbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordSaClosureUserFeedbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordSaClosureUserFeedbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordSaClosureUserFeedbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordSaClosureUserFeedbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordSaClosureUserFeedbackRequestValidationError) ErrorName() string {
	return "RecordSaClosureUserFeedbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RecordSaClosureUserFeedbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordSaClosureUserFeedbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordSaClosureUserFeedbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordSaClosureUserFeedbackRequestValidationError{}

// Validate checks the field values on RecordSaClosureUserFeedbackResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *RecordSaClosureUserFeedbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RecordSaClosureUserFeedbackResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// RecordSaClosureUserFeedbackResponseMultiError, or nil if none found.
func (m *RecordSaClosureUserFeedbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RecordSaClosureUserFeedbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RecordSaClosureUserFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RecordSaClosureUserFeedbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RecordSaClosureUserFeedbackResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RecordSaClosureUserFeedbackResponseMultiError(errors)
	}

	return nil
}

// RecordSaClosureUserFeedbackResponseMultiError is an error wrapping multiple
// validation errors returned by
// RecordSaClosureUserFeedbackResponse.ValidateAll() if the designated
// constraints aren't met.
type RecordSaClosureUserFeedbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RecordSaClosureUserFeedbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RecordSaClosureUserFeedbackResponseMultiError) AllErrors() []error { return m }

// RecordSaClosureUserFeedbackResponseValidationError is the validation error
// returned by RecordSaClosureUserFeedbackResponse.Validate if the designated
// constraints aren't met.
type RecordSaClosureUserFeedbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RecordSaClosureUserFeedbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RecordSaClosureUserFeedbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RecordSaClosureUserFeedbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RecordSaClosureUserFeedbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RecordSaClosureUserFeedbackResponseValidationError) ErrorName() string {
	return "RecordSaClosureUserFeedbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RecordSaClosureUserFeedbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRecordSaClosureUserFeedbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RecordSaClosureUserFeedbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RecordSaClosureUserFeedbackResponseValidationError{}

// Validate checks the field values on GetSubmittedSaClosureRequestsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetSubmittedSaClosureRequestsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSubmittedSaClosureRequestsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSubmittedSaClosureRequestsRequestMultiError, or nil if none found.
func (m *GetSubmittedSaClosureRequestsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSubmittedSaClosureRequestsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubmittedSaClosureRequestsRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFromTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsRequestValidationError{
					field:  "FromTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsRequestValidationError{
					field:  "FromTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubmittedSaClosureRequestsRequestValidationError{
				field:  "FromTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsRequestValidationError{
					field:  "ToTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsRequestValidationError{
					field:  "ToTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubmittedSaClosureRequestsRequestValidationError{
				field:  "ToTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSubmittedSaClosureRequestsRequestMultiError(errors)
	}

	return nil
}

// GetSubmittedSaClosureRequestsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSubmittedSaClosureRequestsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSubmittedSaClosureRequestsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSubmittedSaClosureRequestsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSubmittedSaClosureRequestsRequestMultiError) AllErrors() []error { return m }

// GetSubmittedSaClosureRequestsRequestValidationError is the validation error
// returned by GetSubmittedSaClosureRequestsRequest.Validate if the designated
// constraints aren't met.
type GetSubmittedSaClosureRequestsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSubmittedSaClosureRequestsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSubmittedSaClosureRequestsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSubmittedSaClosureRequestsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSubmittedSaClosureRequestsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSubmittedSaClosureRequestsRequestValidationError) ErrorName() string {
	return "GetSubmittedSaClosureRequestsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSubmittedSaClosureRequestsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSubmittedSaClosureRequestsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSubmittedSaClosureRequestsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSubmittedSaClosureRequestsRequestValidationError{}

// Validate checks the field values on GetSubmittedSaClosureRequestsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetSubmittedSaClosureRequestsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSubmittedSaClosureRequestsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSubmittedSaClosureRequestsResponseMultiError, or nil if none found.
func (m *GetSubmittedSaClosureRequestsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSubmittedSaClosureRequestsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubmittedSaClosureRequestsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSubmittedSaClosureRequestsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSubmittedSaClosureRequestsResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSavingsAccountClosureRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSubmittedSaClosureRequestsResponseValidationError{
						field:  fmt.Sprintf("SavingsAccountClosureRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSubmittedSaClosureRequestsResponseValidationError{
						field:  fmt.Sprintf("SavingsAccountClosureRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSubmittedSaClosureRequestsResponseValidationError{
					field:  fmt.Sprintf("SavingsAccountClosureRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSubmittedSaClosureRequestsResponseMultiError(errors)
	}

	return nil
}

// GetSubmittedSaClosureRequestsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetSubmittedSaClosureRequestsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSubmittedSaClosureRequestsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSubmittedSaClosureRequestsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSubmittedSaClosureRequestsResponseMultiError) AllErrors() []error { return m }

// GetSubmittedSaClosureRequestsResponseValidationError is the validation error
// returned by GetSubmittedSaClosureRequestsResponse.Validate if the
// designated constraints aren't met.
type GetSubmittedSaClosureRequestsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSubmittedSaClosureRequestsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSubmittedSaClosureRequestsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSubmittedSaClosureRequestsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSubmittedSaClosureRequestsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSubmittedSaClosureRequestsResponseValidationError) ErrorName() string {
	return "GetSubmittedSaClosureRequestsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSubmittedSaClosureRequestsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSubmittedSaClosureRequestsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSubmittedSaClosureRequestsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSubmittedSaClosureRequestsResponseValidationError{}

// Validate checks the field values on GetSaClosureRequestsByFilterRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSaClosureRequestsByFilterRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSaClosureRequestsByFilterRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSaClosureRequestsByFilterRequestMultiError, or nil if none found.
func (m *GetSaClosureRequestsByFilterRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSaClosureRequestsByFilterRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureRequestsByFilterRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureRequestsByFilterRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureRequestsByFilterRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSaClosureRequestsByFilterRequestMultiError(errors)
	}

	return nil
}

// GetSaClosureRequestsByFilterRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSaClosureRequestsByFilterRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSaClosureRequestsByFilterRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSaClosureRequestsByFilterRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSaClosureRequestsByFilterRequestMultiError) AllErrors() []error { return m }

// GetSaClosureRequestsByFilterRequestValidationError is the validation error
// returned by GetSaClosureRequestsByFilterRequest.Validate if the designated
// constraints aren't met.
type GetSaClosureRequestsByFilterRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSaClosureRequestsByFilterRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSaClosureRequestsByFilterRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSaClosureRequestsByFilterRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSaClosureRequestsByFilterRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSaClosureRequestsByFilterRequestValidationError) ErrorName() string {
	return "GetSaClosureRequestsByFilterRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSaClosureRequestsByFilterRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSaClosureRequestsByFilterRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSaClosureRequestsByFilterRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSaClosureRequestsByFilterRequestValidationError{}

// Validate checks the field values on GetSaClosureRequestsByFilterResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetSaClosureRequestsByFilterResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSaClosureRequestsByFilterResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSaClosureRequestsByFilterResponseMultiError, or nil if none found.
func (m *GetSaClosureRequestsByFilterResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSaClosureRequestsByFilterResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureRequestsByFilterResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureRequestsByFilterResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureRequestsByFilterResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureRequestsByFilterResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureRequestsByFilterResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureRequestsByFilterResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSavingsAccountClosureRequests() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSaClosureRequestsByFilterResponseValidationError{
						field:  fmt.Sprintf("SavingsAccountClosureRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSaClosureRequestsByFilterResponseValidationError{
						field:  fmt.Sprintf("SavingsAccountClosureRequests[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSaClosureRequestsByFilterResponseValidationError{
					field:  fmt.Sprintf("SavingsAccountClosureRequests[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetSaClosureRequestsByFilterResponseMultiError(errors)
	}

	return nil
}

// GetSaClosureRequestsByFilterResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetSaClosureRequestsByFilterResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSaClosureRequestsByFilterResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSaClosureRequestsByFilterResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSaClosureRequestsByFilterResponseMultiError) AllErrors() []error { return m }

// GetSaClosureRequestsByFilterResponseValidationError is the validation error
// returned by GetSaClosureRequestsByFilterResponse.Validate if the designated
// constraints aren't met.
type GetSaClosureRequestsByFilterResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSaClosureRequestsByFilterResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSaClosureRequestsByFilterResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSaClosureRequestsByFilterResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSaClosureRequestsByFilterResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSaClosureRequestsByFilterResponseValidationError) ErrorName() string {
	return "GetSaClosureRequestsByFilterResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSaClosureRequestsByFilterResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSaClosureRequestsByFilterResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSaClosureRequestsByFilterResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSaClosureRequestsByFilterResponseValidationError{}

// Validate checks the field values on VerifyPanForAccountClosureRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VerifyPanForAccountClosureRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyPanForAccountClosureRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VerifyPanForAccountClosureRequestMultiError, or nil if none found.
func (m *VerifyPanForAccountClosureRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyPanForAccountClosureRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for Pan

	if len(errors) > 0 {
		return VerifyPanForAccountClosureRequestMultiError(errors)
	}

	return nil
}

// VerifyPanForAccountClosureRequestMultiError is an error wrapping multiple
// validation errors returned by
// VerifyPanForAccountClosureRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyPanForAccountClosureRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyPanForAccountClosureRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyPanForAccountClosureRequestMultiError) AllErrors() []error { return m }

// VerifyPanForAccountClosureRequestValidationError is the validation error
// returned by VerifyPanForAccountClosureRequest.Validate if the designated
// constraints aren't met.
type VerifyPanForAccountClosureRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyPanForAccountClosureRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyPanForAccountClosureRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyPanForAccountClosureRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyPanForAccountClosureRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyPanForAccountClosureRequestValidationError) ErrorName() string {
	return "VerifyPanForAccountClosureRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyPanForAccountClosureRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyPanForAccountClosureRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyPanForAccountClosureRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyPanForAccountClosureRequestValidationError{}

// Validate checks the field values on VerifyPanForAccountClosureResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *VerifyPanForAccountClosureResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyPanForAccountClosureResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// VerifyPanForAccountClosureResponseMultiError, or nil if none found.
func (m *VerifyPanForAccountClosureResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyPanForAccountClosureResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPanForAccountClosureResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPanForAccountClosureResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPanForAccountClosureResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AttemptsLeft

	if len(errors) > 0 {
		return VerifyPanForAccountClosureResponseMultiError(errors)
	}

	return nil
}

// VerifyPanForAccountClosureResponseMultiError is an error wrapping multiple
// validation errors returned by
// VerifyPanForAccountClosureResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyPanForAccountClosureResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyPanForAccountClosureResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyPanForAccountClosureResponseMultiError) AllErrors() []error { return m }

// VerifyPanForAccountClosureResponseValidationError is the validation error
// returned by VerifyPanForAccountClosureResponse.Validate if the designated
// constraints aren't met.
type VerifyPanForAccountClosureResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyPanForAccountClosureResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyPanForAccountClosureResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyPanForAccountClosureResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyPanForAccountClosureResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyPanForAccountClosureResponseValidationError) ErrorName() string {
	return "VerifyPanForAccountClosureResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyPanForAccountClosureResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyPanForAccountClosureResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyPanForAccountClosureResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyPanForAccountClosureResponseValidationError{}

// Validate checks the field values on CreateAccountRequest_Options with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAccountRequest_Options) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAccountRequest_Options with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAccountRequest_OptionsMultiError, or nil if none found.
func (m *CreateAccountRequest_Options) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAccountRequest_Options) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ShouldOpenAmbAccount

	if len(errors) > 0 {
		return CreateAccountRequest_OptionsMultiError(errors)
	}

	return nil
}

// CreateAccountRequest_OptionsMultiError is an error wrapping multiple
// validation errors returned by CreateAccountRequest_Options.ValidateAll() if
// the designated constraints aren't met.
type CreateAccountRequest_OptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAccountRequest_OptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAccountRequest_OptionsMultiError) AllErrors() []error { return m }

// CreateAccountRequest_OptionsValidationError is the validation error returned
// by CreateAccountRequest_Options.Validate if the designated constraints
// aren't met.
type CreateAccountRequest_OptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAccountRequest_OptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAccountRequest_OptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAccountRequest_OptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAccountRequest_OptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAccountRequest_OptionsValidationError) ErrorName() string {
	return "CreateAccountRequest_OptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAccountRequest_OptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAccountRequest_Options.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAccountRequest_OptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAccountRequest_OptionsValidationError{}

// Validate checks the field values on GetListOfActiveAccountsResponse_Account
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetListOfActiveAccountsResponse_Account) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetListOfActiveAccountsResponse_Account with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetListOfActiveAccountsResponse_AccountMultiError, or nil if none found.
func (m *GetListOfActiveAccountsResponse_Account) ValidateAll() error {
	return m.validate(true)
}

func (m *GetListOfActiveAccountsResponse_Account) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for PrimaryAccountHolderUserId

	if len(errors) > 0 {
		return GetListOfActiveAccountsResponse_AccountMultiError(errors)
	}

	return nil
}

// GetListOfActiveAccountsResponse_AccountMultiError is an error wrapping
// multiple validation errors returned by
// GetListOfActiveAccountsResponse_Account.ValidateAll() if the designated
// constraints aren't met.
type GetListOfActiveAccountsResponse_AccountMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetListOfActiveAccountsResponse_AccountMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetListOfActiveAccountsResponse_AccountMultiError) AllErrors() []error { return m }

// GetListOfActiveAccountsResponse_AccountValidationError is the validation
// error returned by GetListOfActiveAccountsResponse_Account.Validate if the
// designated constraints aren't met.
type GetListOfActiveAccountsResponse_AccountValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetListOfActiveAccountsResponse_AccountValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetListOfActiveAccountsResponse_AccountValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetListOfActiveAccountsResponse_AccountValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetListOfActiveAccountsResponse_AccountValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetListOfActiveAccountsResponse_AccountValidationError) ErrorName() string {
	return "GetListOfActiveAccountsResponse_AccountValidationError"
}

// Error satisfies the builtin error interface
func (e GetListOfActiveAccountsResponse_AccountValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetListOfActiveAccountsResponse_Account.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetListOfActiveAccountsResponse_AccountValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetListOfActiveAccountsResponse_AccountValidationError{}

// Validate checks the field values on IsTxnAllowedResponse_SavingsTxnLimits
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *IsTxnAllowedResponse_SavingsTxnLimits) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsTxnAllowedResponse_SavingsTxnLimits
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// IsTxnAllowedResponse_SavingsTxnLimitsMultiError, or nil if none found.
func (m *IsTxnAllowedResponse_SavingsTxnLimits) ValidateAll() error {
	return m.validate(true)
}

func (m *IsTxnAllowedResponse_SavingsTxnLimits) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AllowedCreditLimitPercent

	if all {
		switch v := interface{}(m.GetAllowedCreditLimitAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
					field:  "AllowedCreditLimitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
					field:  "AllowedCreditLimitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAllowedCreditLimitAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
				field:  "AllowedCreditLimitAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AllowedSavingsLimitPercent

	if all {
		switch v := interface{}(m.GetAllowedSavingsLimitAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
					field:  "AllowedSavingsLimitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
					field:  "AllowedSavingsLimitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAllowedSavingsLimitAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
				field:  "AllowedSavingsLimitAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAllowedSavingsLimitAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
					field:  "MaxAllowedSavingsLimitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
					field:  "MaxAllowedSavingsLimitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAllowedSavingsLimitAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
				field:  "MaxAllowedSavingsLimitAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalAllowedCreditLimitAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
					field:  "TotalAllowedCreditLimitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
					field:  "TotalAllowedCreditLimitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAllowedCreditLimitAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsTxnAllowedResponse_SavingsTxnLimitsValidationError{
				field:  "TotalAllowedCreditLimitAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AllowedCreditLimitPercentFloat

	// no validation rules for AllowedSavingsLimitPercentFloat

	if len(errors) > 0 {
		return IsTxnAllowedResponse_SavingsTxnLimitsMultiError(errors)
	}

	return nil
}

// IsTxnAllowedResponse_SavingsTxnLimitsMultiError is an error wrapping
// multiple validation errors returned by
// IsTxnAllowedResponse_SavingsTxnLimits.ValidateAll() if the designated
// constraints aren't met.
type IsTxnAllowedResponse_SavingsTxnLimitsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsTxnAllowedResponse_SavingsTxnLimitsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsTxnAllowedResponse_SavingsTxnLimitsMultiError) AllErrors() []error { return m }

// IsTxnAllowedResponse_SavingsTxnLimitsValidationError is the validation error
// returned by IsTxnAllowedResponse_SavingsTxnLimits.Validate if the
// designated constraints aren't met.
type IsTxnAllowedResponse_SavingsTxnLimitsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsTxnAllowedResponse_SavingsTxnLimitsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsTxnAllowedResponse_SavingsTxnLimitsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsTxnAllowedResponse_SavingsTxnLimitsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsTxnAllowedResponse_SavingsTxnLimitsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsTxnAllowedResponse_SavingsTxnLimitsValidationError) ErrorName() string {
	return "IsTxnAllowedResponse_SavingsTxnLimitsValidationError"
}

// Error satisfies the builtin error interface
func (e IsTxnAllowedResponse_SavingsTxnLimitsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsTxnAllowedResponse_SavingsTxnLimits.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsTxnAllowedResponse_SavingsTxnLimitsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsTxnAllowedResponse_SavingsTxnLimitsValidationError{}
