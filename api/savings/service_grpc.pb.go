// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/savings/service.proto

package savings

import (
	context "context"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Savings_CreateAccount_FullMethodName                                  = "/savings.Savings/CreateAccount"
	Savings_GetAccount_FullMethodName                                     = "/savings.Savings/GetAccount"
	Savings_GetAccountsList_FullMethodName                                = "/savings.Savings/GetAccountsList"
	Savings_GetAccountBalance_FullMethodName                              = "/savings.Savings/GetAccountBalance"
	Savings_UpdateAccount_FullMethodName                                  = "/savings.Savings/UpdateAccount"
	Savings_GetOpeningBalance_FullMethodName                              = "/savings.Savings/GetOpeningBalance"
	Savings_GetAccountBalanceWithSummary_FullMethodName                   = "/savings.Savings/GetAccountBalanceWithSummary"
	Savings_GetListOfActiveAccounts_FullMethodName                        = "/savings.Savings/GetListOfActiveAccounts"
	Savings_CloseAccount_FullMethodName                                   = "/savings.Savings/CloseAccount"
	Savings_ReopenAccount_FullMethodName                                  = "/savings.Savings/ReopenAccount"
	Savings_IsTxnAllowed_FullMethodName                                   = "/savings.Savings/IsTxnAllowed"
	Savings_GetAccountBalanceV1_FullMethodName                            = "/savings.Savings/GetAccountBalanceV1"
	Savings_UpdateBalance_FullMethodName                                  = "/savings.Savings/UpdateBalance"
	Savings_StoreClosedAccountBalTransferData_FullMethodName              = "/savings.Savings/StoreClosedAccountBalTransferData"
	Savings_StoreClosedAccountBalTransferDataFromStatement_FullMethodName = "/savings.Savings/StoreClosedAccountBalTransferDataFromStatement"
	Savings_GetClosedAccountBalTransferData_FullMethodName                = "/savings.Savings/GetClosedAccountBalTransferData"
	Savings_FetchDynamicElements_FullMethodName                           = "/savings.Savings/FetchDynamicElements"
	Savings_DynamicElementCallback_FullMethodName                         = "/savings.Savings/DynamicElementCallback"
	Savings_GetEODSavBalanceHistory_FullMethodName                        = "/savings.Savings/GetEODSavBalanceHistory"
	Savings_UpdateClosedAccountBalTransferData_FullMethodName             = "/savings.Savings/UpdateClosedAccountBalTransferData"
	Savings_FetchOrCreateSignAttempt_FullMethodName                       = "/savings.Savings/FetchOrCreateSignAttempt"
	Savings_GetSavingsAccountEssentials_FullMethodName                    = "/savings.Savings/GetSavingsAccountEssentials"
	Savings_CreateOrGetSaClosureRequest_FullMethodName                    = "/savings.Savings/CreateOrGetSaClosureRequest"
	Savings_GetActiveSaClosureRequestForUser_FullMethodName               = "/savings.Savings/GetActiveSaClosureRequestForUser"
	Savings_UpdateSaClosureRequestStatus_FullMethodName                   = "/savings.Savings/UpdateSaClosureRequestStatus"
	Savings_RecordSaClosureUserFeedback_FullMethodName                    = "/savings.Savings/RecordSaClosureUserFeedback"
	Savings_GetSubmittedSaClosureRequests_FullMethodName                  = "/savings.Savings/GetSubmittedSaClosureRequests"
	Savings_GetSaClosureRequestsByFilter_FullMethodName                   = "/savings.Savings/GetSaClosureRequestsByFilter"
	Savings_VerifyPanForAccountClosure_FullMethodName                     = "/savings.Savings/VerifyPanForAccountClosure"
	Savings_GetSavingsAccountNominees_FullMethodName                      = "/savings.Savings/GetSavingsAccountNominees"
	Savings_UpdateSavingsAccountNominees_FullMethodName                   = "/savings.Savings/UpdateSavingsAccountNominees"
)

// SavingsClient is the client API for Savings service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SavingsClient interface {
	// Create and Read Operations on savings account entity
	CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc.CallOption) (*CreateAccountResponse, error)
	// GetAccount rpc can be used to fetch the complete savings account entity.
	// Use GetAccount only in the case GetSavingsAccountEssentials does not return relevant data.
	GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*GetAccountResponse, error)
	// RPC to get list of accounts for a given list of identifiers like primary user id.
	// returns : NOT_FOUND status if for given list of identifiers, not a single account is not found, else INTERNAL in other error cases
	// For a given list of identifiers, if some records are not found, the list of records that are found is returned.
	GetAccountsList(ctx context.Context, in *GetAccountsListRequest, opts ...grpc.CallOption) (*GetAccountsListResponse, error)
	// Deprecated: Do not use.
	// RPC to enquire balance corresponding to an account
	// Deprecated in favour of accounts.balance.GetAccountBalance
	GetAccountBalance(ctx context.Context, in *GetAccountBalanceRequest, opts ...grpc.CallOption) (*GetAccountBalanceResponse, error)
	UpdateAccount(ctx context.Context, in *UpdateAccountRequest, opts ...grpc.CallOption) (*UpdateAccountResponse, error)
	// RPC to enquire opening balance corresponding to account(s) over a specified time time range
	//
	// Opening balance is the balance that an account has at the beginning of an accounting time period
	GetOpeningBalance(ctx context.Context, in *GetOpeningBalanceRequest, opts ...grpc.CallOption) (*GetOpeningBalanceResponse, error)
	// GetAccountBalanceWithSummary returns account balance with detailed summary and buckets
	GetAccountBalanceWithSummary(ctx context.Context, in *GetAccountBalanceWithSummaryRequest, opts ...grpc.CallOption) (*GetAccountBalanceWithSummaryResponse, error)
	// GetListOfActiveAccounts returns a list of active savings accounts.
	// The RPC returns the result in following sequence,
	// If only created_before is specified then response are returned in descending order based on creation time.
	// If only created_after is specified then response are returned in ascending order based on creation time.
	// If both created_before and created_after is specified then response are returned in ascending order based on creation time.
	// If non of created_before and created_after is specified then all the accounts returned in descending order based on creation time.
	GetListOfActiveAccounts(ctx context.Context, in *GetListOfActiveAccountsRequest, opts ...grpc.CallOption) (*GetListOfActiveAccountsResponse, error)
	CloseAccount(ctx context.Context, in *CloseAccountRequest, opts ...grpc.CallOption) (*CloseAccountResponse, error)
	ReopenAccount(ctx context.Context, in *ReopenAccountRequest, opts ...grpc.CallOption) (*ReopenAccountResponse, error)
	// RPC to check if a credit transaction is allowed for an account.
	// The RPC can be used to check if the credit transaction for the given amount in the RPC request will breach any of
	// min-kyc limits or not.
	IsTxnAllowed(ctx context.Context, in *IsTxnAllowedRequest, opts ...grpc.CallOption) (*IsTxnAllowedResponse, error)
	// Deprecated: Do not use.
	// RPC to return account balance with details such as available, ledger and lien balance. It also returns freeze status
	// of the account. Balance details in this api can be stale.
	// To maintain backward compatibility, this API currently invokes both version of balance API (new and old).
	// Old API is given preference until the time we gain decent confidence in the new API.
	// Hence, client should use vendor_api_option to explicitly invoke newer version of balance api call.
	// Deprecated in favour of accounts.balance.GetAccountBalance
	GetAccountBalanceV1(ctx context.Context, in *GetAccountBalanceV1Request, opts ...grpc.CallOption) (*GetAccountBalanceV1Response, error)
	// RPC to update savings balance account if last updated timestamp received in input is after updatedAt
	// timestamp for the account mapped with input actorId, we will sync the balance by calling vendor api
	UpdateBalance(ctx context.Context, in *UpdateBalanceRequest, opts ...grpc.CallOption) (*UpdateBalanceResponse, error)
	// StoreClosedAccountBalTransferData rpc creates entry in closed accounts balance transfer table.
	// It will not create an entry if a row already exists with the same information
	// Returns:
	// Success - data creation was successful
	// InvalidArgument - savings account id is not mentioned
	// AlreadyExists - row with same data already exists
	// Internal - unforeseen error
	StoreClosedAccountBalTransferData(ctx context.Context, in *StoreClosedAccountBalTransferDataRequest, opts ...grpc.CallOption) (*StoreClosedAccountBalTransferDataResponse, error)
	// StoreClosedAccountBalTransferDataFromStatement
	// checks if account is closed, fetches statement for the account during the closure period
	// will create an entry with the balance captured from the statement
	// FailedPrecondition - if savings account mentioned is not closed already
	//
	//	Internal - unforeseen error
	StoreClosedAccountBalTransferDataFromStatement(ctx context.Context, in *StoreClosedAccountBalTransferDataFromStatementRequest, opts ...grpc.CallOption) (*StoreClosedAccountBalTransferDataFromStatementResponse, error)
	// GetClosedAccountBalTransferData rpc fetches closed accounts balance transfer data from db.
	// Returns:
	// Success: record(s) found
	// InvalidArgument - savings account id is not mentioned
	// NotFound: no entries found
	// Internal: unforeseen error
	GetClosedAccountBalTransferData(ctx context.Context, in *GetClosedAccountBalTransferDataRequest, opts ...grpc.CallOption) (*GetClosedAccountBalTransferDataResponse, error)
	// FetchDynamicElements rpc returns the dynamic elements response based on screen type and additional inputs
	FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
	DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error)
	// GetEODSavBalanceHistory RPC to get eod balance history of user from a given start to end date
	// Rpc will support history upto a maximum of 30 days
	// Returns:
	// Success: dates are within expected range and records are found
	// If dates are out of range, max history (last 30 days) is returned
	// Internal: unforeseen error
	GetEODSavBalanceHistory(ctx context.Context, in *GetEODSavBalanceHistoryRequest, opts ...grpc.CallOption) (*GetEODSavBalanceHistoryResponse, error)
	// UpdateClosedAccountBalTransferData rpc updates closed accounts balance transfer data in db.
	// Returns:
	// Success: record was updated
	// InvalidArgument - cbt id is not mentioned or if field mask is empty
	// Internal: unforeseen error
	UpdateClosedAccountBalTransferData(ctx context.Context, in *UpdateClosedAccountBalTransferDataRequest, opts ...grpc.CallOption) (*UpdateClosedAccountBalTransferDataResponse, error)
	// FetchOrCreateSignAttempt checks if bank has signature available or not
	// if not it creates a sign attempt and return sign url
	// it also ensure only one attempt is in progress at a account level
	FetchOrCreateSignAttempt(ctx context.Context, in *FetchOrCreateSignAttemptRequest, opts ...grpc.CallOption) (*FetchOrCreateSignAttemptResponse, error)
	// GetSavingsAccountEssentials rpc returns frequently accessed fields from savings account entity. The data would be
	// cached to facilitate higher QPS and performance. For fetching data not returned by this RPC, use GetAccount instead.
	GetSavingsAccountEssentials(ctx context.Context, in *GetSavingsAccountEssentialsRequest, opts ...grpc.CallOption) (*GetSavingsAccountEssentialsResponse, error)
	// CreateOrGetSaClosureRequest rpc
	// - returns closure request if one is already created for the user
	// - creates new Closure Request if not created and returns the new request
	// - invalidates non-submitted stale request, creates new request and returns the new request
	// there should be only a single closure request in non-terminal state
	CreateOrGetSaClosureRequest(ctx context.Context, in *CreateOrGetSaClosureRequestRequest, opts ...grpc.CallOption) (*CreateOrGetSaClosureRequestResponse, error)
	// returns the closure request for the actor
	// returns RecordNotFound if no closure request is found
	// returns Internal if user has multiple closure requests or other server errors
	GetActiveSaClosureRequestForUser(ctx context.Context, in *GetActiveSaClosureRequestForUserRequest, opts ...grpc.CallOption) (*GetActiveSaClosureRequestForUserResponse, error)
	// UpdateSaClosureRequestStatus rpc updates Status of closure request on some validation
	UpdateSaClosureRequestStatus(ctx context.Context, in *UpdateSaClosureRequestStatusRequest, opts ...grpc.CallOption) (*UpdateSaClosureRequestStatusResponse, error)
	// records feedback entered by user
	// overrides feedback if there is any feedback stored for the closure request already
	RecordSaClosureUserFeedback(ctx context.Context, in *RecordSaClosureUserFeedbackRequest, opts ...grpc.CallOption) (*RecordSaClosureUserFeedbackResponse, error)
	// returns sa closure requests that become eligible to be sent for closure to federal between given timestamps
	// requests that are submitted T-10 days (config driven) are eligible to be processed for closure
	// data is fetched in paginated manner
	GetSubmittedSaClosureRequests(ctx context.Context, in *GetSubmittedSaClosureRequestsRequest, opts ...grpc.CallOption) (*GetSubmittedSaClosureRequestsResponse, error)
	// paginated rpc to return all sa closure requests filtered by the conditions
	// empty list for a filter applies no filter on the field
	GetSaClosureRequestsByFilter(ctx context.Context, in *GetSaClosureRequestsByFilterRequest, opts ...grpc.CallOption) (*GetSaClosureRequestsByFilterResponse, error)
	// VerifyPanForAccountClosure rpc checks if given pan matches with the user's pan to validate user during account closure process.
	VerifyPanForAccountClosure(ctx context.Context, in *VerifyPanForAccountClosureRequest, opts ...grpc.CallOption) (*VerifyPanForAccountClosureResponse, error)
	// GetSavingsAccountNominees rpc returns nominees for an actor's savings account.
	GetSavingsAccountNominees(ctx context.Context, in *GetSavingsAccountNomineesRequest, opts ...grpc.CallOption) (*GetSavingsAccountNomineesResponse, error)
	UpdateSavingsAccountNominees(ctx context.Context, in *UpdateSavingsAccountNomineesRequest, opts ...grpc.CallOption) (*UpdateSavingsAccountNomineesResponse, error)
}

type savingsClient struct {
	cc grpc.ClientConnInterface
}

func NewSavingsClient(cc grpc.ClientConnInterface) SavingsClient {
	return &savingsClient{cc}
}

func (c *savingsClient) CreateAccount(ctx context.Context, in *CreateAccountRequest, opts ...grpc.CallOption) (*CreateAccountResponse, error) {
	out := new(CreateAccountResponse)
	err := c.cc.Invoke(ctx, Savings_CreateAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetAccount(ctx context.Context, in *GetAccountRequest, opts ...grpc.CallOption) (*GetAccountResponse, error) {
	out := new(GetAccountResponse)
	err := c.cc.Invoke(ctx, Savings_GetAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetAccountsList(ctx context.Context, in *GetAccountsListRequest, opts ...grpc.CallOption) (*GetAccountsListResponse, error) {
	out := new(GetAccountsListResponse)
	err := c.cc.Invoke(ctx, Savings_GetAccountsList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *savingsClient) GetAccountBalance(ctx context.Context, in *GetAccountBalanceRequest, opts ...grpc.CallOption) (*GetAccountBalanceResponse, error) {
	out := new(GetAccountBalanceResponse)
	err := c.cc.Invoke(ctx, Savings_GetAccountBalance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) UpdateAccount(ctx context.Context, in *UpdateAccountRequest, opts ...grpc.CallOption) (*UpdateAccountResponse, error) {
	out := new(UpdateAccountResponse)
	err := c.cc.Invoke(ctx, Savings_UpdateAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetOpeningBalance(ctx context.Context, in *GetOpeningBalanceRequest, opts ...grpc.CallOption) (*GetOpeningBalanceResponse, error) {
	out := new(GetOpeningBalanceResponse)
	err := c.cc.Invoke(ctx, Savings_GetOpeningBalance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetAccountBalanceWithSummary(ctx context.Context, in *GetAccountBalanceWithSummaryRequest, opts ...grpc.CallOption) (*GetAccountBalanceWithSummaryResponse, error) {
	out := new(GetAccountBalanceWithSummaryResponse)
	err := c.cc.Invoke(ctx, Savings_GetAccountBalanceWithSummary_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetListOfActiveAccounts(ctx context.Context, in *GetListOfActiveAccountsRequest, opts ...grpc.CallOption) (*GetListOfActiveAccountsResponse, error) {
	out := new(GetListOfActiveAccountsResponse)
	err := c.cc.Invoke(ctx, Savings_GetListOfActiveAccounts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) CloseAccount(ctx context.Context, in *CloseAccountRequest, opts ...grpc.CallOption) (*CloseAccountResponse, error) {
	out := new(CloseAccountResponse)
	err := c.cc.Invoke(ctx, Savings_CloseAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) ReopenAccount(ctx context.Context, in *ReopenAccountRequest, opts ...grpc.CallOption) (*ReopenAccountResponse, error) {
	out := new(ReopenAccountResponse)
	err := c.cc.Invoke(ctx, Savings_ReopenAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) IsTxnAllowed(ctx context.Context, in *IsTxnAllowedRequest, opts ...grpc.CallOption) (*IsTxnAllowedResponse, error) {
	out := new(IsTxnAllowedResponse)
	err := c.cc.Invoke(ctx, Savings_IsTxnAllowed_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *savingsClient) GetAccountBalanceV1(ctx context.Context, in *GetAccountBalanceV1Request, opts ...grpc.CallOption) (*GetAccountBalanceV1Response, error) {
	out := new(GetAccountBalanceV1Response)
	err := c.cc.Invoke(ctx, Savings_GetAccountBalanceV1_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) UpdateBalance(ctx context.Context, in *UpdateBalanceRequest, opts ...grpc.CallOption) (*UpdateBalanceResponse, error) {
	out := new(UpdateBalanceResponse)
	err := c.cc.Invoke(ctx, Savings_UpdateBalance_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) StoreClosedAccountBalTransferData(ctx context.Context, in *StoreClosedAccountBalTransferDataRequest, opts ...grpc.CallOption) (*StoreClosedAccountBalTransferDataResponse, error) {
	out := new(StoreClosedAccountBalTransferDataResponse)
	err := c.cc.Invoke(ctx, Savings_StoreClosedAccountBalTransferData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) StoreClosedAccountBalTransferDataFromStatement(ctx context.Context, in *StoreClosedAccountBalTransferDataFromStatementRequest, opts ...grpc.CallOption) (*StoreClosedAccountBalTransferDataFromStatementResponse, error) {
	out := new(StoreClosedAccountBalTransferDataFromStatementResponse)
	err := c.cc.Invoke(ctx, Savings_StoreClosedAccountBalTransferDataFromStatement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetClosedAccountBalTransferData(ctx context.Context, in *GetClosedAccountBalTransferDataRequest, opts ...grpc.CallOption) (*GetClosedAccountBalTransferDataResponse, error) {
	out := new(GetClosedAccountBalTransferDataResponse)
	err := c.cc.Invoke(ctx, Savings_GetClosedAccountBalTransferData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	out := new(dynamic_elements.FetchDynamicElementsResponse)
	err := c.cc.Invoke(ctx, Savings_FetchDynamicElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	out := new(dynamic_elements.DynamicElementCallbackResponse)
	err := c.cc.Invoke(ctx, Savings_DynamicElementCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetEODSavBalanceHistory(ctx context.Context, in *GetEODSavBalanceHistoryRequest, opts ...grpc.CallOption) (*GetEODSavBalanceHistoryResponse, error) {
	out := new(GetEODSavBalanceHistoryResponse)
	err := c.cc.Invoke(ctx, Savings_GetEODSavBalanceHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) UpdateClosedAccountBalTransferData(ctx context.Context, in *UpdateClosedAccountBalTransferDataRequest, opts ...grpc.CallOption) (*UpdateClosedAccountBalTransferDataResponse, error) {
	out := new(UpdateClosedAccountBalTransferDataResponse)
	err := c.cc.Invoke(ctx, Savings_UpdateClosedAccountBalTransferData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) FetchOrCreateSignAttempt(ctx context.Context, in *FetchOrCreateSignAttemptRequest, opts ...grpc.CallOption) (*FetchOrCreateSignAttemptResponse, error) {
	out := new(FetchOrCreateSignAttemptResponse)
	err := c.cc.Invoke(ctx, Savings_FetchOrCreateSignAttempt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetSavingsAccountEssentials(ctx context.Context, in *GetSavingsAccountEssentialsRequest, opts ...grpc.CallOption) (*GetSavingsAccountEssentialsResponse, error) {
	out := new(GetSavingsAccountEssentialsResponse)
	err := c.cc.Invoke(ctx, Savings_GetSavingsAccountEssentials_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) CreateOrGetSaClosureRequest(ctx context.Context, in *CreateOrGetSaClosureRequestRequest, opts ...grpc.CallOption) (*CreateOrGetSaClosureRequestResponse, error) {
	out := new(CreateOrGetSaClosureRequestResponse)
	err := c.cc.Invoke(ctx, Savings_CreateOrGetSaClosureRequest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetActiveSaClosureRequestForUser(ctx context.Context, in *GetActiveSaClosureRequestForUserRequest, opts ...grpc.CallOption) (*GetActiveSaClosureRequestForUserResponse, error) {
	out := new(GetActiveSaClosureRequestForUserResponse)
	err := c.cc.Invoke(ctx, Savings_GetActiveSaClosureRequestForUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) UpdateSaClosureRequestStatus(ctx context.Context, in *UpdateSaClosureRequestStatusRequest, opts ...grpc.CallOption) (*UpdateSaClosureRequestStatusResponse, error) {
	out := new(UpdateSaClosureRequestStatusResponse)
	err := c.cc.Invoke(ctx, Savings_UpdateSaClosureRequestStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) RecordSaClosureUserFeedback(ctx context.Context, in *RecordSaClosureUserFeedbackRequest, opts ...grpc.CallOption) (*RecordSaClosureUserFeedbackResponse, error) {
	out := new(RecordSaClosureUserFeedbackResponse)
	err := c.cc.Invoke(ctx, Savings_RecordSaClosureUserFeedback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetSubmittedSaClosureRequests(ctx context.Context, in *GetSubmittedSaClosureRequestsRequest, opts ...grpc.CallOption) (*GetSubmittedSaClosureRequestsResponse, error) {
	out := new(GetSubmittedSaClosureRequestsResponse)
	err := c.cc.Invoke(ctx, Savings_GetSubmittedSaClosureRequests_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetSaClosureRequestsByFilter(ctx context.Context, in *GetSaClosureRequestsByFilterRequest, opts ...grpc.CallOption) (*GetSaClosureRequestsByFilterResponse, error) {
	out := new(GetSaClosureRequestsByFilterResponse)
	err := c.cc.Invoke(ctx, Savings_GetSaClosureRequestsByFilter_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) VerifyPanForAccountClosure(ctx context.Context, in *VerifyPanForAccountClosureRequest, opts ...grpc.CallOption) (*VerifyPanForAccountClosureResponse, error) {
	out := new(VerifyPanForAccountClosureResponse)
	err := c.cc.Invoke(ctx, Savings_VerifyPanForAccountClosure_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) GetSavingsAccountNominees(ctx context.Context, in *GetSavingsAccountNomineesRequest, opts ...grpc.CallOption) (*GetSavingsAccountNomineesResponse, error) {
	out := new(GetSavingsAccountNomineesResponse)
	err := c.cc.Invoke(ctx, Savings_GetSavingsAccountNominees_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *savingsClient) UpdateSavingsAccountNominees(ctx context.Context, in *UpdateSavingsAccountNomineesRequest, opts ...grpc.CallOption) (*UpdateSavingsAccountNomineesResponse, error) {
	out := new(UpdateSavingsAccountNomineesResponse)
	err := c.cc.Invoke(ctx, Savings_UpdateSavingsAccountNominees_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SavingsServer is the server API for Savings service.
// All implementations should embed UnimplementedSavingsServer
// for forward compatibility
type SavingsServer interface {
	// Create and Read Operations on savings account entity
	CreateAccount(context.Context, *CreateAccountRequest) (*CreateAccountResponse, error)
	// GetAccount rpc can be used to fetch the complete savings account entity.
	// Use GetAccount only in the case GetSavingsAccountEssentials does not return relevant data.
	GetAccount(context.Context, *GetAccountRequest) (*GetAccountResponse, error)
	// RPC to get list of accounts for a given list of identifiers like primary user id.
	// returns : NOT_FOUND status if for given list of identifiers, not a single account is not found, else INTERNAL in other error cases
	// For a given list of identifiers, if some records are not found, the list of records that are found is returned.
	GetAccountsList(context.Context, *GetAccountsListRequest) (*GetAccountsListResponse, error)
	// Deprecated: Do not use.
	// RPC to enquire balance corresponding to an account
	// Deprecated in favour of accounts.balance.GetAccountBalance
	GetAccountBalance(context.Context, *GetAccountBalanceRequest) (*GetAccountBalanceResponse, error)
	UpdateAccount(context.Context, *UpdateAccountRequest) (*UpdateAccountResponse, error)
	// RPC to enquire opening balance corresponding to account(s) over a specified time time range
	//
	// Opening balance is the balance that an account has at the beginning of an accounting time period
	GetOpeningBalance(context.Context, *GetOpeningBalanceRequest) (*GetOpeningBalanceResponse, error)
	// GetAccountBalanceWithSummary returns account balance with detailed summary and buckets
	GetAccountBalanceWithSummary(context.Context, *GetAccountBalanceWithSummaryRequest) (*GetAccountBalanceWithSummaryResponse, error)
	// GetListOfActiveAccounts returns a list of active savings accounts.
	// The RPC returns the result in following sequence,
	// If only created_before is specified then response are returned in descending order based on creation time.
	// If only created_after is specified then response are returned in ascending order based on creation time.
	// If both created_before and created_after is specified then response are returned in ascending order based on creation time.
	// If non of created_before and created_after is specified then all the accounts returned in descending order based on creation time.
	GetListOfActiveAccounts(context.Context, *GetListOfActiveAccountsRequest) (*GetListOfActiveAccountsResponse, error)
	CloseAccount(context.Context, *CloseAccountRequest) (*CloseAccountResponse, error)
	ReopenAccount(context.Context, *ReopenAccountRequest) (*ReopenAccountResponse, error)
	// RPC to check if a credit transaction is allowed for an account.
	// The RPC can be used to check if the credit transaction for the given amount in the RPC request will breach any of
	// min-kyc limits or not.
	IsTxnAllowed(context.Context, *IsTxnAllowedRequest) (*IsTxnAllowedResponse, error)
	// Deprecated: Do not use.
	// RPC to return account balance with details such as available, ledger and lien balance. It also returns freeze status
	// of the account. Balance details in this api can be stale.
	// To maintain backward compatibility, this API currently invokes both version of balance API (new and old).
	// Old API is given preference until the time we gain decent confidence in the new API.
	// Hence, client should use vendor_api_option to explicitly invoke newer version of balance api call.
	// Deprecated in favour of accounts.balance.GetAccountBalance
	GetAccountBalanceV1(context.Context, *GetAccountBalanceV1Request) (*GetAccountBalanceV1Response, error)
	// RPC to update savings balance account if last updated timestamp received in input is after updatedAt
	// timestamp for the account mapped with input actorId, we will sync the balance by calling vendor api
	UpdateBalance(context.Context, *UpdateBalanceRequest) (*UpdateBalanceResponse, error)
	// StoreClosedAccountBalTransferData rpc creates entry in closed accounts balance transfer table.
	// It will not create an entry if a row already exists with the same information
	// Returns:
	// Success - data creation was successful
	// InvalidArgument - savings account id is not mentioned
	// AlreadyExists - row with same data already exists
	// Internal - unforeseen error
	StoreClosedAccountBalTransferData(context.Context, *StoreClosedAccountBalTransferDataRequest) (*StoreClosedAccountBalTransferDataResponse, error)
	// StoreClosedAccountBalTransferDataFromStatement
	// checks if account is closed, fetches statement for the account during the closure period
	// will create an entry with the balance captured from the statement
	// FailedPrecondition - if savings account mentioned is not closed already
	//
	//	Internal - unforeseen error
	StoreClosedAccountBalTransferDataFromStatement(context.Context, *StoreClosedAccountBalTransferDataFromStatementRequest) (*StoreClosedAccountBalTransferDataFromStatementResponse, error)
	// GetClosedAccountBalTransferData rpc fetches closed accounts balance transfer data from db.
	// Returns:
	// Success: record(s) found
	// InvalidArgument - savings account id is not mentioned
	// NotFound: no entries found
	// Internal: unforeseen error
	GetClosedAccountBalTransferData(context.Context, *GetClosedAccountBalTransferDataRequest) (*GetClosedAccountBalTransferDataResponse, error)
	// FetchDynamicElements rpc returns the dynamic elements response based on screen type and additional inputs
	FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
	DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error)
	// GetEODSavBalanceHistory RPC to get eod balance history of user from a given start to end date
	// Rpc will support history upto a maximum of 30 days
	// Returns:
	// Success: dates are within expected range and records are found
	// If dates are out of range, max history (last 30 days) is returned
	// Internal: unforeseen error
	GetEODSavBalanceHistory(context.Context, *GetEODSavBalanceHistoryRequest) (*GetEODSavBalanceHistoryResponse, error)
	// UpdateClosedAccountBalTransferData rpc updates closed accounts balance transfer data in db.
	// Returns:
	// Success: record was updated
	// InvalidArgument - cbt id is not mentioned or if field mask is empty
	// Internal: unforeseen error
	UpdateClosedAccountBalTransferData(context.Context, *UpdateClosedAccountBalTransferDataRequest) (*UpdateClosedAccountBalTransferDataResponse, error)
	// FetchOrCreateSignAttempt checks if bank has signature available or not
	// if not it creates a sign attempt and return sign url
	// it also ensure only one attempt is in progress at a account level
	FetchOrCreateSignAttempt(context.Context, *FetchOrCreateSignAttemptRequest) (*FetchOrCreateSignAttemptResponse, error)
	// GetSavingsAccountEssentials rpc returns frequently accessed fields from savings account entity. The data would be
	// cached to facilitate higher QPS and performance. For fetching data not returned by this RPC, use GetAccount instead.
	GetSavingsAccountEssentials(context.Context, *GetSavingsAccountEssentialsRequest) (*GetSavingsAccountEssentialsResponse, error)
	// CreateOrGetSaClosureRequest rpc
	// - returns closure request if one is already created for the user
	// - creates new Closure Request if not created and returns the new request
	// - invalidates non-submitted stale request, creates new request and returns the new request
	// there should be only a single closure request in non-terminal state
	CreateOrGetSaClosureRequest(context.Context, *CreateOrGetSaClosureRequestRequest) (*CreateOrGetSaClosureRequestResponse, error)
	// returns the closure request for the actor
	// returns RecordNotFound if no closure request is found
	// returns Internal if user has multiple closure requests or other server errors
	GetActiveSaClosureRequestForUser(context.Context, *GetActiveSaClosureRequestForUserRequest) (*GetActiveSaClosureRequestForUserResponse, error)
	// UpdateSaClosureRequestStatus rpc updates Status of closure request on some validation
	UpdateSaClosureRequestStatus(context.Context, *UpdateSaClosureRequestStatusRequest) (*UpdateSaClosureRequestStatusResponse, error)
	// records feedback entered by user
	// overrides feedback if there is any feedback stored for the closure request already
	RecordSaClosureUserFeedback(context.Context, *RecordSaClosureUserFeedbackRequest) (*RecordSaClosureUserFeedbackResponse, error)
	// returns sa closure requests that become eligible to be sent for closure to federal between given timestamps
	// requests that are submitted T-10 days (config driven) are eligible to be processed for closure
	// data is fetched in paginated manner
	GetSubmittedSaClosureRequests(context.Context, *GetSubmittedSaClosureRequestsRequest) (*GetSubmittedSaClosureRequestsResponse, error)
	// paginated rpc to return all sa closure requests filtered by the conditions
	// empty list for a filter applies no filter on the field
	GetSaClosureRequestsByFilter(context.Context, *GetSaClosureRequestsByFilterRequest) (*GetSaClosureRequestsByFilterResponse, error)
	// VerifyPanForAccountClosure rpc checks if given pan matches with the user's pan to validate user during account closure process.
	VerifyPanForAccountClosure(context.Context, *VerifyPanForAccountClosureRequest) (*VerifyPanForAccountClosureResponse, error)
	// GetSavingsAccountNominees rpc returns nominees for an actor's savings account.
	GetSavingsAccountNominees(context.Context, *GetSavingsAccountNomineesRequest) (*GetSavingsAccountNomineesResponse, error)
	UpdateSavingsAccountNominees(context.Context, *UpdateSavingsAccountNomineesRequest) (*UpdateSavingsAccountNomineesResponse, error)
}

// UnimplementedSavingsServer should be embedded to have forward compatible implementations.
type UnimplementedSavingsServer struct {
}

func (UnimplementedSavingsServer) CreateAccount(context.Context, *CreateAccountRequest) (*CreateAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccount not implemented")
}
func (UnimplementedSavingsServer) GetAccount(context.Context, *GetAccountRequest) (*GetAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedSavingsServer) GetAccountsList(context.Context, *GetAccountsListRequest) (*GetAccountsListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountsList not implemented")
}
func (UnimplementedSavingsServer) GetAccountBalance(context.Context, *GetAccountBalanceRequest) (*GetAccountBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountBalance not implemented")
}
func (UnimplementedSavingsServer) UpdateAccount(context.Context, *UpdateAccountRequest) (*UpdateAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAccount not implemented")
}
func (UnimplementedSavingsServer) GetOpeningBalance(context.Context, *GetOpeningBalanceRequest) (*GetOpeningBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOpeningBalance not implemented")
}
func (UnimplementedSavingsServer) GetAccountBalanceWithSummary(context.Context, *GetAccountBalanceWithSummaryRequest) (*GetAccountBalanceWithSummaryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountBalanceWithSummary not implemented")
}
func (UnimplementedSavingsServer) GetListOfActiveAccounts(context.Context, *GetListOfActiveAccountsRequest) (*GetListOfActiveAccountsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetListOfActiveAccounts not implemented")
}
func (UnimplementedSavingsServer) CloseAccount(context.Context, *CloseAccountRequest) (*CloseAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseAccount not implemented")
}
func (UnimplementedSavingsServer) ReopenAccount(context.Context, *ReopenAccountRequest) (*ReopenAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReopenAccount not implemented")
}
func (UnimplementedSavingsServer) IsTxnAllowed(context.Context, *IsTxnAllowedRequest) (*IsTxnAllowedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsTxnAllowed not implemented")
}
func (UnimplementedSavingsServer) GetAccountBalanceV1(context.Context, *GetAccountBalanceV1Request) (*GetAccountBalanceV1Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountBalanceV1 not implemented")
}
func (UnimplementedSavingsServer) UpdateBalance(context.Context, *UpdateBalanceRequest) (*UpdateBalanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBalance not implemented")
}
func (UnimplementedSavingsServer) StoreClosedAccountBalTransferData(context.Context, *StoreClosedAccountBalTransferDataRequest) (*StoreClosedAccountBalTransferDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreClosedAccountBalTransferData not implemented")
}
func (UnimplementedSavingsServer) StoreClosedAccountBalTransferDataFromStatement(context.Context, *StoreClosedAccountBalTransferDataFromStatementRequest) (*StoreClosedAccountBalTransferDataFromStatementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StoreClosedAccountBalTransferDataFromStatement not implemented")
}
func (UnimplementedSavingsServer) GetClosedAccountBalTransferData(context.Context, *GetClosedAccountBalTransferDataRequest) (*GetClosedAccountBalTransferDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClosedAccountBalTransferData not implemented")
}
func (UnimplementedSavingsServer) FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchDynamicElements not implemented")
}
func (UnimplementedSavingsServer) DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DynamicElementCallback not implemented")
}
func (UnimplementedSavingsServer) GetEODSavBalanceHistory(context.Context, *GetEODSavBalanceHistoryRequest) (*GetEODSavBalanceHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEODSavBalanceHistory not implemented")
}
func (UnimplementedSavingsServer) UpdateClosedAccountBalTransferData(context.Context, *UpdateClosedAccountBalTransferDataRequest) (*UpdateClosedAccountBalTransferDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateClosedAccountBalTransferData not implemented")
}
func (UnimplementedSavingsServer) FetchOrCreateSignAttempt(context.Context, *FetchOrCreateSignAttemptRequest) (*FetchOrCreateSignAttemptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchOrCreateSignAttempt not implemented")
}
func (UnimplementedSavingsServer) GetSavingsAccountEssentials(context.Context, *GetSavingsAccountEssentialsRequest) (*GetSavingsAccountEssentialsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSavingsAccountEssentials not implemented")
}
func (UnimplementedSavingsServer) CreateOrGetSaClosureRequest(context.Context, *CreateOrGetSaClosureRequestRequest) (*CreateOrGetSaClosureRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrGetSaClosureRequest not implemented")
}
func (UnimplementedSavingsServer) GetActiveSaClosureRequestForUser(context.Context, *GetActiveSaClosureRequestForUserRequest) (*GetActiveSaClosureRequestForUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveSaClosureRequestForUser not implemented")
}
func (UnimplementedSavingsServer) UpdateSaClosureRequestStatus(context.Context, *UpdateSaClosureRequestStatusRequest) (*UpdateSaClosureRequestStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSaClosureRequestStatus not implemented")
}
func (UnimplementedSavingsServer) RecordSaClosureUserFeedback(context.Context, *RecordSaClosureUserFeedbackRequest) (*RecordSaClosureUserFeedbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordSaClosureUserFeedback not implemented")
}
func (UnimplementedSavingsServer) GetSubmittedSaClosureRequests(context.Context, *GetSubmittedSaClosureRequestsRequest) (*GetSubmittedSaClosureRequestsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubmittedSaClosureRequests not implemented")
}
func (UnimplementedSavingsServer) GetSaClosureRequestsByFilter(context.Context, *GetSaClosureRequestsByFilterRequest) (*GetSaClosureRequestsByFilterResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSaClosureRequestsByFilter not implemented")
}
func (UnimplementedSavingsServer) VerifyPanForAccountClosure(context.Context, *VerifyPanForAccountClosureRequest) (*VerifyPanForAccountClosureResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyPanForAccountClosure not implemented")
}
func (UnimplementedSavingsServer) GetSavingsAccountNominees(context.Context, *GetSavingsAccountNomineesRequest) (*GetSavingsAccountNomineesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSavingsAccountNominees not implemented")
}
func (UnimplementedSavingsServer) UpdateSavingsAccountNominees(context.Context, *UpdateSavingsAccountNomineesRequest) (*UpdateSavingsAccountNomineesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSavingsAccountNominees not implemented")
}

// UnsafeSavingsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SavingsServer will
// result in compilation errors.
type UnsafeSavingsServer interface {
	mustEmbedUnimplementedSavingsServer()
}

func RegisterSavingsServer(s grpc.ServiceRegistrar, srv SavingsServer) {
	s.RegisterService(&Savings_ServiceDesc, srv)
}

func _Savings_CreateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).CreateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_CreateAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).CreateAccount(ctx, req.(*CreateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetAccount(ctx, req.(*GetAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetAccountsList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountsListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetAccountsList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetAccountsList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetAccountsList(ctx, req.(*GetAccountsListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetAccountBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetAccountBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetAccountBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetAccountBalance(ctx, req.(*GetAccountBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_UpdateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).UpdateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_UpdateAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).UpdateAccount(ctx, req.(*UpdateAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetOpeningBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOpeningBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetOpeningBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetOpeningBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetOpeningBalance(ctx, req.(*GetOpeningBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetAccountBalanceWithSummary_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountBalanceWithSummaryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetAccountBalanceWithSummary(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetAccountBalanceWithSummary_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetAccountBalanceWithSummary(ctx, req.(*GetAccountBalanceWithSummaryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetListOfActiveAccounts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetListOfActiveAccountsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetListOfActiveAccounts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetListOfActiveAccounts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetListOfActiveAccounts(ctx, req.(*GetListOfActiveAccountsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_CloseAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).CloseAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_CloseAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).CloseAccount(ctx, req.(*CloseAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_ReopenAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReopenAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).ReopenAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_ReopenAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).ReopenAccount(ctx, req.(*ReopenAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_IsTxnAllowed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsTxnAllowedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).IsTxnAllowed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_IsTxnAllowed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).IsTxnAllowed(ctx, req.(*IsTxnAllowedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetAccountBalanceV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountBalanceV1Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetAccountBalanceV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetAccountBalanceV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetAccountBalanceV1(ctx, req.(*GetAccountBalanceV1Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_UpdateBalance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBalanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).UpdateBalance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_UpdateBalance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).UpdateBalance(ctx, req.(*UpdateBalanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_StoreClosedAccountBalTransferData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreClosedAccountBalTransferDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).StoreClosedAccountBalTransferData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_StoreClosedAccountBalTransferData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).StoreClosedAccountBalTransferData(ctx, req.(*StoreClosedAccountBalTransferDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_StoreClosedAccountBalTransferDataFromStatement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StoreClosedAccountBalTransferDataFromStatementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).StoreClosedAccountBalTransferDataFromStatement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_StoreClosedAccountBalTransferDataFromStatement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).StoreClosedAccountBalTransferDataFromStatement(ctx, req.(*StoreClosedAccountBalTransferDataFromStatementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetClosedAccountBalTransferData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClosedAccountBalTransferDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetClosedAccountBalTransferData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetClosedAccountBalTransferData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetClosedAccountBalTransferData(ctx, req.(*GetClosedAccountBalTransferDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_FetchDynamicElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.FetchDynamicElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).FetchDynamicElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_FetchDynamicElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).FetchDynamicElements(ctx, req.(*dynamic_elements.FetchDynamicElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_DynamicElementCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.DynamicElementCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).DynamicElementCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_DynamicElementCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).DynamicElementCallback(ctx, req.(*dynamic_elements.DynamicElementCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetEODSavBalanceHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEODSavBalanceHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetEODSavBalanceHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetEODSavBalanceHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetEODSavBalanceHistory(ctx, req.(*GetEODSavBalanceHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_UpdateClosedAccountBalTransferData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateClosedAccountBalTransferDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).UpdateClosedAccountBalTransferData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_UpdateClosedAccountBalTransferData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).UpdateClosedAccountBalTransferData(ctx, req.(*UpdateClosedAccountBalTransferDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_FetchOrCreateSignAttempt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchOrCreateSignAttemptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).FetchOrCreateSignAttempt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_FetchOrCreateSignAttempt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).FetchOrCreateSignAttempt(ctx, req.(*FetchOrCreateSignAttemptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetSavingsAccountEssentials_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSavingsAccountEssentialsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetSavingsAccountEssentials(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetSavingsAccountEssentials_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetSavingsAccountEssentials(ctx, req.(*GetSavingsAccountEssentialsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_CreateOrGetSaClosureRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrGetSaClosureRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).CreateOrGetSaClosureRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_CreateOrGetSaClosureRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).CreateOrGetSaClosureRequest(ctx, req.(*CreateOrGetSaClosureRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetActiveSaClosureRequestForUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveSaClosureRequestForUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetActiveSaClosureRequestForUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetActiveSaClosureRequestForUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetActiveSaClosureRequestForUser(ctx, req.(*GetActiveSaClosureRequestForUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_UpdateSaClosureRequestStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSaClosureRequestStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).UpdateSaClosureRequestStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_UpdateSaClosureRequestStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).UpdateSaClosureRequestStatus(ctx, req.(*UpdateSaClosureRequestStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_RecordSaClosureUserFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordSaClosureUserFeedbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).RecordSaClosureUserFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_RecordSaClosureUserFeedback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).RecordSaClosureUserFeedback(ctx, req.(*RecordSaClosureUserFeedbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetSubmittedSaClosureRequests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubmittedSaClosureRequestsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetSubmittedSaClosureRequests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetSubmittedSaClosureRequests_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetSubmittedSaClosureRequests(ctx, req.(*GetSubmittedSaClosureRequestsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetSaClosureRequestsByFilter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSaClosureRequestsByFilterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetSaClosureRequestsByFilter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetSaClosureRequestsByFilter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetSaClosureRequestsByFilter(ctx, req.(*GetSaClosureRequestsByFilterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_VerifyPanForAccountClosure_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyPanForAccountClosureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).VerifyPanForAccountClosure(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_VerifyPanForAccountClosure_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).VerifyPanForAccountClosure(ctx, req.(*VerifyPanForAccountClosureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_GetSavingsAccountNominees_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSavingsAccountNomineesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).GetSavingsAccountNominees(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_GetSavingsAccountNominees_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).GetSavingsAccountNominees(ctx, req.(*GetSavingsAccountNomineesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Savings_UpdateSavingsAccountNominees_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSavingsAccountNomineesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SavingsServer).UpdateSavingsAccountNominees(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Savings_UpdateSavingsAccountNominees_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SavingsServer).UpdateSavingsAccountNominees(ctx, req.(*UpdateSavingsAccountNomineesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Savings_ServiceDesc is the grpc.ServiceDesc for Savings service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Savings_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "savings.Savings",
	HandlerType: (*SavingsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAccount",
			Handler:    _Savings_CreateAccount_Handler,
		},
		{
			MethodName: "GetAccount",
			Handler:    _Savings_GetAccount_Handler,
		},
		{
			MethodName: "GetAccountsList",
			Handler:    _Savings_GetAccountsList_Handler,
		},
		{
			MethodName: "GetAccountBalance",
			Handler:    _Savings_GetAccountBalance_Handler,
		},
		{
			MethodName: "UpdateAccount",
			Handler:    _Savings_UpdateAccount_Handler,
		},
		{
			MethodName: "GetOpeningBalance",
			Handler:    _Savings_GetOpeningBalance_Handler,
		},
		{
			MethodName: "GetAccountBalanceWithSummary",
			Handler:    _Savings_GetAccountBalanceWithSummary_Handler,
		},
		{
			MethodName: "GetListOfActiveAccounts",
			Handler:    _Savings_GetListOfActiveAccounts_Handler,
		},
		{
			MethodName: "CloseAccount",
			Handler:    _Savings_CloseAccount_Handler,
		},
		{
			MethodName: "ReopenAccount",
			Handler:    _Savings_ReopenAccount_Handler,
		},
		{
			MethodName: "IsTxnAllowed",
			Handler:    _Savings_IsTxnAllowed_Handler,
		},
		{
			MethodName: "GetAccountBalanceV1",
			Handler:    _Savings_GetAccountBalanceV1_Handler,
		},
		{
			MethodName: "UpdateBalance",
			Handler:    _Savings_UpdateBalance_Handler,
		},
		{
			MethodName: "StoreClosedAccountBalTransferData",
			Handler:    _Savings_StoreClosedAccountBalTransferData_Handler,
		},
		{
			MethodName: "StoreClosedAccountBalTransferDataFromStatement",
			Handler:    _Savings_StoreClosedAccountBalTransferDataFromStatement_Handler,
		},
		{
			MethodName: "GetClosedAccountBalTransferData",
			Handler:    _Savings_GetClosedAccountBalTransferData_Handler,
		},
		{
			MethodName: "FetchDynamicElements",
			Handler:    _Savings_FetchDynamicElements_Handler,
		},
		{
			MethodName: "DynamicElementCallback",
			Handler:    _Savings_DynamicElementCallback_Handler,
		},
		{
			MethodName: "GetEODSavBalanceHistory",
			Handler:    _Savings_GetEODSavBalanceHistory_Handler,
		},
		{
			MethodName: "UpdateClosedAccountBalTransferData",
			Handler:    _Savings_UpdateClosedAccountBalTransferData_Handler,
		},
		{
			MethodName: "FetchOrCreateSignAttempt",
			Handler:    _Savings_FetchOrCreateSignAttempt_Handler,
		},
		{
			MethodName: "GetSavingsAccountEssentials",
			Handler:    _Savings_GetSavingsAccountEssentials_Handler,
		},
		{
			MethodName: "CreateOrGetSaClosureRequest",
			Handler:    _Savings_CreateOrGetSaClosureRequest_Handler,
		},
		{
			MethodName: "GetActiveSaClosureRequestForUser",
			Handler:    _Savings_GetActiveSaClosureRequestForUser_Handler,
		},
		{
			MethodName: "UpdateSaClosureRequestStatus",
			Handler:    _Savings_UpdateSaClosureRequestStatus_Handler,
		},
		{
			MethodName: "RecordSaClosureUserFeedback",
			Handler:    _Savings_RecordSaClosureUserFeedback_Handler,
		},
		{
			MethodName: "GetSubmittedSaClosureRequests",
			Handler:    _Savings_GetSubmittedSaClosureRequests_Handler,
		},
		{
			MethodName: "GetSaClosureRequestsByFilter",
			Handler:    _Savings_GetSaClosureRequestsByFilter_Handler,
		},
		{
			MethodName: "VerifyPanForAccountClosure",
			Handler:    _Savings_VerifyPanForAccountClosure_Handler,
		},
		{
			MethodName: "GetSavingsAccountNominees",
			Handler:    _Savings_GetSavingsAccountNominees_Handler,
		},
		{
			MethodName: "UpdateSavingsAccountNominees",
			Handler:    _Savings_UpdateSavingsAccountNominees_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/savings/service.proto",
}
