// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/securities/catalog/consumer
package consumer

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	RefreshSecurityDetailsMethod                 = "RefreshSecurityDetails"
	AddNewSecuritiesMethod                       = "AddNewSecurities"
	ProcessSecurityListingHistoricalPricesMethod = "ProcessSecurityListingHistoricalPrices"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &RefreshSecurityDetailsRequest{}
var _ queue.ConsumerRequest = &AddNewSecuritiesRequest{}
var _ queue.ConsumerRequest = &ProcessSecurityListingHistoricalPricesRequest{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *RefreshSecurityDetailsRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *AddNewSecuritiesRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *ProcessSecurityListingHistoricalPricesRequest) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterRefreshSecurityDetailsMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterRefreshSecurityDetailsMethodToSubscriber(subscriber queue.Subscriber, srv SecuritiesCatalogConsumerServer) {
	subscriber.RegisterService(&SecuritiesCatalogConsumer_ServiceDesc, srv, RefreshSecurityDetailsMethod)
}

// RegisterAddNewSecuritiesMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterAddNewSecuritiesMethodToSubscriber(subscriber queue.Subscriber, srv SecuritiesCatalogConsumerServer) {
	subscriber.RegisterService(&SecuritiesCatalogConsumer_ServiceDesc, srv, AddNewSecuritiesMethod)
}

// RegisterProcessSecurityListingHistoricalPricesMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessSecurityListingHistoricalPricesMethodToSubscriber(subscriber queue.Subscriber, srv SecuritiesCatalogConsumerServer) {
	subscriber.RegisterService(&SecuritiesCatalogConsumer_ServiceDesc, srv, ProcessSecurityListingHistoricalPricesMethod)
}
