// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/vendors/inhouse/bre.proto

package bre

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetLoanDecisioningRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values *GetLoanDecisioningRequest_Values `protobuf:"bytes,1,opt,name=values,proto3" json:"values,omitempty"`
}

func (x *GetLoanDecisioningRequest) Reset() {
	*x = GetLoanDecisioningRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningRequest) ProtoMessage() {}

func (x *GetLoanDecisioningRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningRequest.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{0}
}

func (x *GetLoanDecisioningRequest) GetValues() *GetLoanDecisioningRequest_Values {
	if x != nil {
		return x.Values
	}
	return nil
}

type GetLoanDecisioningResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Decision []*GetLoanDecisioningResponse_Decision `protobuf:"bytes,1,rep,name=decision,proto3" json:"decision,omitempty"`
}

func (x *GetLoanDecisioningResponse) Reset() {
	*x = GetLoanDecisioningResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningResponse) ProtoMessage() {}

func (x *GetLoanDecisioningResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningResponse.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{1}
}

func (x *GetLoanDecisioningResponse) GetDecision() []*GetLoanDecisioningResponse_Decision {
	if x != nil {
		return x.Decision
	}
	return nil
}

type PolicyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pre                     *PolicyParams_Pre        `protobuf:"bytes,1,opt,name=pre,proto3" json:"pre,omitempty"`
	Final                   *PolicyParams_Final      `protobuf:"bytes,2,opt,name=final,proto3" json:"final,omitempty"`
	ExecutionInfo           *ExecutionInfo           `protobuf:"bytes,3,opt,name=execution_info,json=executionInfo,proto3" json:"execution_info,omitempty"`
	DataInfo                *DataInfo                `protobuf:"bytes,4,opt,name=data_info,json=dataInfo,proto3" json:"data_info,omitempty"`
	ProductSpecificDataInfo *ProductSpecificDataInfo `protobuf:"bytes,5,opt,name=product_specific_data_info,json=productSpecificDataInfo,proto3" json:"product_specific_data_info,omitempty"`
}

func (x *PolicyParams) Reset() {
	*x = PolicyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyParams) ProtoMessage() {}

func (x *PolicyParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyParams.ProtoReflect.Descriptor instead.
func (*PolicyParams) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{2}
}

func (x *PolicyParams) GetPre() *PolicyParams_Pre {
	if x != nil {
		return x.Pre
	}
	return nil
}

func (x *PolicyParams) GetFinal() *PolicyParams_Final {
	if x != nil {
		return x.Final
	}
	return nil
}

func (x *PolicyParams) GetExecutionInfo() *ExecutionInfo {
	if x != nil {
		return x.ExecutionInfo
	}
	return nil
}

func (x *PolicyParams) GetDataInfo() *DataInfo {
	if x != nil {
		return x.DataInfo
	}
	return nil
}

func (x *PolicyParams) GetProductSpecificDataInfo() *ProductSpecificDataInfo {
	if x != nil {
		return x.ProductSpecificDataInfo
	}
	return nil
}

type ExecutionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pre   []*ExecutionInfoDetails `protobuf:"bytes,1,rep,name=pre,proto3" json:"pre,omitempty"`
	Final []*ExecutionInfoDetails `protobuf:"bytes,2,rep,name=final,proto3" json:"final,omitempty"`
}

func (x *ExecutionInfo) Reset() {
	*x = ExecutionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionInfo) ProtoMessage() {}

func (x *ExecutionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionInfo.ProtoReflect.Descriptor instead.
func (*ExecutionInfo) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{3}
}

func (x *ExecutionInfo) GetPre() []*ExecutionInfoDetails {
	if x != nil {
		return x.Pre
	}
	return nil
}

func (x *ExecutionInfo) GetFinal() []*ExecutionInfoDetails {
	if x != nil {
		return x.Final
	}
	return nil
}

type ExecutionInfoDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BatchId       string `protobuf:"bytes,1,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
	PricingScheme string `protobuf:"bytes,2,opt,name=pricing_scheme,json=pricingSchemeBRE,proto3" json:"pricing_scheme,omitempty"`
}

func (x *ExecutionInfoDetails) Reset() {
	*x = ExecutionInfoDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExecutionInfoDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExecutionInfoDetails) ProtoMessage() {}

func (x *ExecutionInfoDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExecutionInfoDetails.ProtoReflect.Descriptor instead.
func (*ExecutionInfoDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{4}
}

func (x *ExecutionInfoDetails) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *ExecutionInfoDetails) GetPricingScheme() string {
	if x != nil {
		return x.PricingScheme
	}
	return ""
}

type DataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AaData                  *DataInfo_AaData `protobuf:"bytes,1,opt,name=aa_data,json=aaData,proto3" json:"aa_data,omitempty"`
	IsEtbUser               bool             `protobuf:"varint,2,opt,name=is_etb_user,json=isEtbUser,proto3" json:"is_etb_user,omitempty"`
	CurrentEmiObligation    float64          `protobuf:"fixed64,3,opt,name=current_emi_obligation,json=currentEmiObligation,proto3" json:"current_emi_obligation,omitempty"`
	IsB2BSalaryUser         bool             `protobuf:"varint,4,opt,name=is_b2b_salary_user,json=isB2bSalaryUser,proto3" json:"is_b2b_salary_user,omitempty"`
	MonthlyIncome           float64          `protobuf:"fixed64,5,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	MonthsSinceSalaryActive int32            `protobuf:"varint,6,opt,name=months_since_salary_active,json=monthSinceSalaryActive,proto3" json:"months_since_salary_active,omitempty"`
	SalaryCreditDay         int32            `protobuf:"varint,7,opt,name=salary_credit_day,json=salaryCreditDay,proto3" json:"salary_credit_day,omitempty"`
}

func (x *DataInfo) Reset() {
	*x = DataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataInfo) ProtoMessage() {}

func (x *DataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataInfo.ProtoReflect.Descriptor instead.
func (*DataInfo) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{5}
}

func (x *DataInfo) GetAaData() *DataInfo_AaData {
	if x != nil {
		return x.AaData
	}
	return nil
}

func (x *DataInfo) GetIsEtbUser() bool {
	if x != nil {
		return x.IsEtbUser
	}
	return false
}

func (x *DataInfo) GetCurrentEmiObligation() float64 {
	if x != nil {
		return x.CurrentEmiObligation
	}
	return 0
}

func (x *DataInfo) GetIsB2BSalaryUser() bool {
	if x != nil {
		return x.IsB2BSalaryUser
	}
	return false
}

func (x *DataInfo) GetMonthlyIncome() float64 {
	if x != nil {
		return x.MonthlyIncome
	}
	return 0
}

func (x *DataInfo) GetMonthsSinceSalaryActive() int32 {
	if x != nil {
		return x.MonthsSinceSalaryActive
	}
	return 0
}

func (x *DataInfo) GetSalaryCreditDay() int32 {
	if x != nil {
		return x.SalaryCreditDay
	}
	return 0
}

// ProductSpecificDataInfo provides loan product specific data inputs to the BRE
type ProductSpecificDataInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Details specific to insurance policy that user intends to purchase with the loan
	InsurancePolicyDetails *ProductSpecificDataInfo_InsurancePolicyDetails `protobuf:"bytes,1,opt,name=insurance_policy_details,json=insurancePolicyDetails,proto3" json:"insurance_policy_details,omitempty"`
}

func (x *ProductSpecificDataInfo) Reset() {
	*x = ProductSpecificDataInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductSpecificDataInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductSpecificDataInfo) ProtoMessage() {}

func (x *ProductSpecificDataInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductSpecificDataInfo.ProtoReflect.Descriptor instead.
func (*ProductSpecificDataInfo) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{6}
}

func (x *ProductSpecificDataInfo) GetInsurancePolicyDetails() *ProductSpecificDataInfo_InsurancePolicyDetails {
	if x != nil {
		return x.InsurancePolicyDetails
	}
	return nil
}

type PersonalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dob         string `protobuf:"bytes,1,opt,name=dob,proto3" json:"dob,omitempty"`
	Gender      string `protobuf:"bytes,2,opt,name=gender,proto3" json:"gender,omitempty"`
	Name        string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Pan         string `protobuf:"bytes,4,opt,name=pan,proto3" json:"pan,omitempty"`
	PhoneNumber string `protobuf:"bytes,5,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email       string `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *PersonalDetails) Reset() {
	*x = PersonalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonalDetails) ProtoMessage() {}

func (x *PersonalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonalDetails.ProtoReflect.Descriptor instead.
func (*PersonalDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{7}
}

func (x *PersonalDetails) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *PersonalDetails) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *PersonalDetails) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PersonalDetails) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *PersonalDetails) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *PersonalDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type EmploymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmployerName          string  `protobuf:"bytes,1,opt,name=employer_name,json=employerName,proto3" json:"employer_name,omitempty"`
	EmploymentType        string  `protobuf:"bytes,2,opt,name=employment_type,json=employmentType,proto3" json:"employment_type,omitempty"`
	DeclaredMonthlyIncome float64 `protobuf:"fixed64,3,opt,name=declared_monthly_income,json=declaredMonthlyIncome,proto3" json:"declared_monthly_income,omitempty"`
	WorkEmail             string  `protobuf:"bytes,4,opt,name=work_email,json=workEmail,proto3" json:"work_email,omitempty"`
	WorkAddress           string  `protobuf:"bytes,5,opt,name=work_address,json=workAddress,proto3" json:"work_address,omitempty"`
	WorkPincode           int32   `protobuf:"varint,6,opt,name=work_pincode,json=workPincode,proto3" json:"work_pincode,omitempty"`
}

func (x *EmploymentDetails) Reset() {
	*x = EmploymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentDetails) ProtoMessage() {}

func (x *EmploymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentDetails.ProtoReflect.Descriptor instead.
func (*EmploymentDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{8}
}

func (x *EmploymentDetails) GetEmployerName() string {
	if x != nil {
		return x.EmployerName
	}
	return ""
}

func (x *EmploymentDetails) GetEmploymentType() string {
	if x != nil {
		return x.EmploymentType
	}
	return ""
}

func (x *EmploymentDetails) GetDeclaredMonthlyIncome() float64 {
	if x != nil {
		return x.DeclaredMonthlyIncome
	}
	return 0
}

func (x *EmploymentDetails) GetWorkEmail() string {
	if x != nil {
		return x.WorkEmail
	}
	return ""
}

func (x *EmploymentDetails) GetWorkAddress() string {
	if x != nil {
		return x.WorkAddress
	}
	return ""
}

func (x *EmploymentDetails) GetWorkPincode() int32 {
	if x != nil {
		return x.WorkPincode
	}
	return 0
}

type AddressDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Address     string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	Pincode     int32  `protobuf:"varint,2,opt,name=pincode,proto3" json:"pincode,omitempty"`
	AddressType string `protobuf:"bytes,3,opt,name=address_type,json=addressType,proto3" json:"address_type,omitempty"`
}

func (x *AddressDetails) Reset() {
	*x = AddressDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressDetails) ProtoMessage() {}

func (x *AddressDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressDetails.ProtoReflect.Descriptor instead.
func (*AddressDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{9}
}

func (x *AddressDetails) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AddressDetails) GetPincode() int32 {
	if x != nil {
		return x.Pincode
	}
	return 0
}

func (x *AddressDetails) GetAddressType() string {
	if x != nil {
		return x.AddressType
	}
	return ""
}

type CustomerDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PersonalDetails        *PersonalDetails                      `protobuf:"bytes,1,opt,name=personal_details,json=personalDetails,proto3" json:"personal_details,omitempty"`
	EmploymentDetails      *EmploymentDetails                    `protobuf:"bytes,2,opt,name=employment_details,json=employmentDetails,proto3" json:"employment_details,omitempty"`
	AddressDetails         *AddressDetails                       `protobuf:"bytes,3,opt,name=address_details,json=addressDetails,proto3" json:"address_details,omitempty"`
	RequestedLoanDetails   *CustomerDetails_RequestedLoanDetails `protobuf:"bytes,4,opt,name=requested_loan_details,json=requestedLoanDetails,proto3" json:"requested_loan_details,omitempty"`
	KycDetails             *CustomerDetails_KycDetails           `protobuf:"bytes,5,opt,name=kyc_details,json=kycDetails,proto3" json:"kyc_details,omitempty"`
	CurrentLocationDetails *AddressDetails                       `protobuf:"bytes,6,opt,name=current_location_details,json=currentLocationDetails,proto3" json:"current_location_details,omitempty"`
}

func (x *CustomerDetails) Reset() {
	*x = CustomerDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDetails) ProtoMessage() {}

func (x *CustomerDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDetails.ProtoReflect.Descriptor instead.
func (*CustomerDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{10}
}

func (x *CustomerDetails) GetPersonalDetails() *PersonalDetails {
	if x != nil {
		return x.PersonalDetails
	}
	return nil
}

func (x *CustomerDetails) GetEmploymentDetails() *EmploymentDetails {
	if x != nil {
		return x.EmploymentDetails
	}
	return nil
}

func (x *CustomerDetails) GetAddressDetails() *AddressDetails {
	if x != nil {
		return x.AddressDetails
	}
	return nil
}

func (x *CustomerDetails) GetRequestedLoanDetails() *CustomerDetails_RequestedLoanDetails {
	if x != nil {
		return x.RequestedLoanDetails
	}
	return nil
}

func (x *CustomerDetails) GetKycDetails() *CustomerDetails_KycDetails {
	if x != nil {
		return x.KycDetails
	}
	return nil
}

func (x *CustomerDetails) GetCurrentLocationDetails() *AddressDetails {
	if x != nil {
		return x.CurrentLocationDetails
	}
	return nil
}

type GetLoanDecisioningRequestV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values *GetLoanDecisioningRequestV2_Values `protobuf:"bytes,1,opt,name=values,proto3" json:"values,omitempty"`
}

func (x *GetLoanDecisioningRequestV2) Reset() {
	*x = GetLoanDecisioningRequestV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningRequestV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningRequestV2) ProtoMessage() {}

func (x *GetLoanDecisioningRequestV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningRequestV2.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningRequestV2) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{11}
}

func (x *GetLoanDecisioningRequestV2) GetValues() *GetLoanDecisioningRequestV2_Values {
	if x != nil {
		return x.Values
	}
	return nil
}

type OfferDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmiDueDate              string  `protobuf:"bytes,1,opt,name=emiDueDate,proto3" json:"emiDueDate,omitempty"`
	GstPercentage           float64 `protobuf:"fixed64,2,opt,name=gstPercentage,proto3" json:"gstPercentage,omitempty"`
	InterestPercentage      float64 `protobuf:"fixed64,3,opt,name=interestPercentage,proto3" json:"interestPercentage,omitempty"`
	MaxAmount               float64 `protobuf:"fixed64,4,opt,name=maxAmount,proto3" json:"maxAmount,omitempty"`
	MaxEmiAmount            float64 `protobuf:"fixed64,5,opt,name=maxEmiAmount,proto3" json:"maxEmiAmount,omitempty"`
	MaxTenureInMonths       int32   `protobuf:"varint,6,opt,name=maxTenureInMonths,proto3" json:"maxTenureInMonths,omitempty"`
	MinAmount               float64 `protobuf:"fixed64,7,opt,name=minAmount,proto3" json:"minAmount,omitempty"`
	MinTenureInMonths       int32   `protobuf:"varint,8,opt,name=minTenureInMonths,proto3" json:"minTenureInMonths,omitempty"`
	ProcessingFeePercentage float64 `protobuf:"fixed64,9,opt,name=processingFeePercentage,proto3" json:"processingFeePercentage,omitempty"`
	PricingScheme           string  `protobuf:"bytes,10,opt,name=pricingScheme,proto3" json:"pricingScheme,omitempty"`
}

func (x *OfferDetails) Reset() {
	*x = OfferDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferDetails) ProtoMessage() {}

func (x *OfferDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferDetails.ProtoReflect.Descriptor instead.
func (*OfferDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{12}
}

func (x *OfferDetails) GetEmiDueDate() string {
	if x != nil {
		return x.EmiDueDate
	}
	return ""
}

func (x *OfferDetails) GetGstPercentage() float64 {
	if x != nil {
		return x.GstPercentage
	}
	return 0
}

func (x *OfferDetails) GetInterestPercentage() float64 {
	if x != nil {
		return x.InterestPercentage
	}
	return 0
}

func (x *OfferDetails) GetMaxAmount() float64 {
	if x != nil {
		return x.MaxAmount
	}
	return 0
}

func (x *OfferDetails) GetMaxEmiAmount() float64 {
	if x != nil {
		return x.MaxEmiAmount
	}
	return 0
}

func (x *OfferDetails) GetMaxTenureInMonths() int32 {
	if x != nil {
		return x.MaxTenureInMonths
	}
	return 0
}

func (x *OfferDetails) GetMinAmount() float64 {
	if x != nil {
		return x.MinAmount
	}
	return 0
}

func (x *OfferDetails) GetMinTenureInMonths() int32 {
	if x != nil {
		return x.MinTenureInMonths
	}
	return 0
}

func (x *OfferDetails) GetProcessingFeePercentage() float64 {
	if x != nil {
		return x.ProcessingFeePercentage
	}
	return 0
}

func (x *OfferDetails) GetPricingScheme() string {
	if x != nil {
		return x.PricingScheme
	}
	return ""
}

type Decision struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LendingProgram   string        `protobuf:"bytes,1,opt,name=lending_program,json=lendingProgram,proto3" json:"lending_program,omitempty"`
	Action           string        `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	ValidTill        string        `protobuf:"bytes,3,opt,name=valid_till,json=validTill,proto3" json:"valid_till,omitempty"`
	Bureau           string        `protobuf:"bytes,4,opt,name=bureau,proto3" json:"bureau,omitempty"`
	Conditions       []string      `protobuf:"bytes,5,rep,name=conditions,proto3" json:"conditions,omitempty"`
	RejectionReasons []string      `protobuf:"bytes,6,rep,name=rejection_reasons,json=rejectionReasons,proto3" json:"rejection_reasons,omitempty"`
	ExternalReasons  []string      `protobuf:"bytes,7,rep,name=external_reasons,json=externalReasons,proto3" json:"external_reasons,omitempty"`
	ReportDate       string        `protobuf:"bytes,8,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
	ReportId         string        `protobuf:"bytes,9,opt,name=report_id,json=reportId,proto3" json:"report_id,omitempty"`
	SchemeId         string        `protobuf:"bytes,10,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	Strategy         string        `protobuf:"bytes,11,opt,name=strategy,proto3" json:"strategy,omitempty"`
	OfferDetails     *OfferDetails `protobuf:"bytes,12,opt,name=offer_details,json=offerDetails,proto3" json:"offer_details,omitempty"`
}

func (x *Decision) Reset() {
	*x = Decision{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Decision) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Decision) ProtoMessage() {}

func (x *Decision) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Decision.ProtoReflect.Descriptor instead.
func (*Decision) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{13}
}

func (x *Decision) GetLendingProgram() string {
	if x != nil {
		return x.LendingProgram
	}
	return ""
}

func (x *Decision) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *Decision) GetValidTill() string {
	if x != nil {
		return x.ValidTill
	}
	return ""
}

func (x *Decision) GetBureau() string {
	if x != nil {
		return x.Bureau
	}
	return ""
}

func (x *Decision) GetConditions() []string {
	if x != nil {
		return x.Conditions
	}
	return nil
}

func (x *Decision) GetRejectionReasons() []string {
	if x != nil {
		return x.RejectionReasons
	}
	return nil
}

func (x *Decision) GetExternalReasons() []string {
	if x != nil {
		return x.ExternalReasons
	}
	return nil
}

func (x *Decision) GetReportDate() string {
	if x != nil {
		return x.ReportDate
	}
	return ""
}

func (x *Decision) GetReportId() string {
	if x != nil {
		return x.ReportId
	}
	return ""
}

func (x *Decision) GetSchemeId() string {
	if x != nil {
		return x.SchemeId
	}
	return ""
}

func (x *Decision) GetStrategy() string {
	if x != nil {
		return x.Strategy
	}
	return ""
}

func (x *Decision) GetOfferDetails() *OfferDetails {
	if x != nil {
		return x.OfferDetails
	}
	return nil
}

type GetLoanDecisioningResponseV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId            string           `protobuf:"bytes,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	ApplicationId         string           `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	EvaluationRequestTime string           `protobuf:"bytes,3,opt,name=evaluation_request_time,json=evaluationRequestTime,proto3" json:"evaluation_request_time,omitempty"`
	RequestId             string           `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	Product               string           `protobuf:"bytes,5,opt,name=product,proto3" json:"product,omitempty"`
	PolicyParams          *PolicyParams    `protobuf:"bytes,6,opt,name=policy_params,json=policyParams,proto3" json:"policy_params,omitempty"`
	PrioritizedDecision   *Decision        `protobuf:"bytes,7,opt,name=prioritized_decision,json=prioritizedDecision,proto3" json:"prioritized_decision,omitempty"`
	Decision              []*Decision      `protobuf:"bytes,8,rep,name=decision,proto3" json:"decision,omitempty"`
	Details               *structpb.Struct `protobuf:"bytes,10,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *GetLoanDecisioningResponseV2) Reset() {
	*x = GetLoanDecisioningResponseV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningResponseV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningResponseV2) ProtoMessage() {}

func (x *GetLoanDecisioningResponseV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningResponseV2.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningResponseV2) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{14}
}

func (x *GetLoanDecisioningResponseV2) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *GetLoanDecisioningResponseV2) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetLoanDecisioningResponseV2) GetEvaluationRequestTime() string {
	if x != nil {
		return x.EvaluationRequestTime
	}
	return ""
}

func (x *GetLoanDecisioningResponseV2) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetLoanDecisioningResponseV2) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *GetLoanDecisioningResponseV2) GetPolicyParams() *PolicyParams {
	if x != nil {
		return x.PolicyParams
	}
	return nil
}

func (x *GetLoanDecisioningResponseV2) GetPrioritizedDecision() *Decision {
	if x != nil {
		return x.PrioritizedDecision
	}
	return nil
}

func (x *GetLoanDecisioningResponseV2) GetDecision() []*Decision {
	if x != nil {
		return x.Decision
	}
	return nil
}

func (x *GetLoanDecisioningResponseV2) GetDetails() *structpb.Struct {
	if x != nil {
		return x.Details
	}
	return nil
}

type Duration struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Duration     uint32 `protobuf:"varint,1,opt,name=duration,proto3" json:"duration,omitempty"`
	DurationUnit string `protobuf:"bytes,2,opt,name=duration_unit,json=durationUnit,proto3" json:"duration_unit,omitempty"`
}

func (x *Duration) Reset() {
	*x = Duration{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Duration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Duration) ProtoMessage() {}

func (x *Duration) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Duration.ProtoReflect.Descriptor instead.
func (*Duration) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{15}
}

func (x *Duration) GetDuration() uint32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *Duration) GetDurationUnit() string {
	if x != nil {
		return x.DurationUnit
	}
	return ""
}

type GetLoanDecisioningRequest_Values struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Input *GetLoanDecisioningRequest_Values_Input `protobuf:"bytes,1,opt,name=input,proto3" json:"input,omitempty"`
}

func (x *GetLoanDecisioningRequest_Values) Reset() {
	*x = GetLoanDecisioningRequest_Values{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningRequest_Values) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningRequest_Values) ProtoMessage() {}

func (x *GetLoanDecisioningRequest_Values) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningRequest_Values.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningRequest_Values) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{0, 0}
}

func (x *GetLoanDecisioningRequest_Values) GetInput() *GetLoanDecisioningRequest_Values_Input {
	if x != nil {
		return x.Input
	}
	return nil
}

type GetLoanDecisioningRequest_Values_Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId     string                                               `protobuf:"bytes,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	Name           string                                               `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Gender         string                                               `protobuf:"bytes,3,opt,name=gender,proto3" json:"gender,omitempty"`
	Pan            string                                               `protobuf:"bytes,4,opt,name=pan,proto3" json:"pan,omitempty"`
	Dob            string                                               `protobuf:"bytes,5,opt,name=dob,proto3" json:"dob,omitempty"`
	Address        string                                               `protobuf:"bytes,6,opt,name=address,proto3" json:"address,omitempty"`
	Pincode        int32                                                `protobuf:"varint,7,opt,name=pincode,proto3" json:"pincode,omitempty"`
	EmploymentType string                                               `protobuf:"bytes,8,opt,name=employment_type,json=employmentType,proto3" json:"employment_type,omitempty"`
	EmployerName   string                                               `protobuf:"bytes,9,opt,name=employer_name,json=employerName,proto3" json:"employer_name,omitempty"`
	WorkEmail      string                                               `protobuf:"bytes,10,opt,name=work_email,json=workEmail,proto3" json:"work_email,omitempty"`
	DeclaredIncome int32                                                `protobuf:"varint,11,opt,name=declared_income,json=declaredIncome,proto3" json:"declared_income,omitempty"`
	SchemeId       string                                               `protobuf:"bytes,12,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	BatchId        string                                               `protobuf:"bytes,13,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
	ClientId       string                                               `protobuf:"bytes,15,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	PolicyParams   *GetLoanDecisioningRequest_Values_Input_PolicyParams `protobuf:"bytes,16,opt,name=policy_params,json=policyParams,proto3" json:"policy_params,omitempty"`
	ApplicationId  string                                               `protobuf:"bytes,17,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	// amount selected bu user against which we want to make the request
	LoanAmount int64 `protobuf:"varint,18,opt,name=loan_amount,json=loanAmount,proto3" json:"loan_amount,omitempty"`
}

func (x *GetLoanDecisioningRequest_Values_Input) Reset() {
	*x = GetLoanDecisioningRequest_Values_Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningRequest_Values_Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningRequest_Values_Input) ProtoMessage() {}

func (x *GetLoanDecisioningRequest_Values_Input) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningRequest_Values_Input.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningRequest_Values_Input) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{0, 0, 0}
}

func (x *GetLoanDecisioningRequest_Values_Input) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetPincode() int32 {
	if x != nil {
		return x.Pincode
	}
	return 0
}

func (x *GetLoanDecisioningRequest_Values_Input) GetEmploymentType() string {
	if x != nil {
		return x.EmploymentType
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetEmployerName() string {
	if x != nil {
		return x.EmployerName
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetWorkEmail() string {
	if x != nil {
		return x.WorkEmail
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetDeclaredIncome() int32 {
	if x != nil {
		return x.DeclaredIncome
	}
	return 0
}

func (x *GetLoanDecisioningRequest_Values_Input) GetSchemeId() string {
	if x != nil {
		return x.SchemeId
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetPolicyParams() *GetLoanDecisioningRequest_Values_Input_PolicyParams {
	if x != nil {
		return x.PolicyParams
	}
	return nil
}

func (x *GetLoanDecisioningRequest_Values_Input) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input) GetLoanAmount() int64 {
	if x != nil {
		return x.LoanAmount
	}
	return 0
}

type GetLoanDecisioningRequest_Values_Input_PolicyParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PdScore           float64 `protobuf:"fixed64,3,opt,name=pd_score,json=pdScore,proto3" json:"pd_score,omitempty"`
	PricingScheme     string  `protobuf:"bytes,4,opt,name=pricing_scheme,json=pricingScheme,proto3" json:"pricing_scheme,omitempty"`
	EverVkycAttempted int32   `protobuf:"varint,5,opt,name=ever_vkyc_attempted,json=everVkycAttempted,proto3" json:"ever_vkyc_attempted,omitempty"`
	PdScoreVersion    string  `protobuf:"bytes,6,opt,name=pd_score_version,json=pdScoreVersion,proto3" json:"pd_score_version,omitempty"`
	SchemeId          string  `protobuf:"bytes,7,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	PricingSchemeBre  string  `protobuf:"bytes,8,opt,name=pricing_scheme_bre,json=pricingSchemeBRE,proto3" json:"pricing_scheme_bre,omitempty"`
	BatchId           string  `protobuf:"bytes,9,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) Reset() {
	*x = GetLoanDecisioningRequest_Values_Input_PolicyParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningRequest_Values_Input_PolicyParams) ProtoMessage() {}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningRequest_Values_Input_PolicyParams.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningRequest_Values_Input_PolicyParams) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{0, 0, 0, 0}
}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) GetPdScore() float64 {
	if x != nil {
		return x.PdScore
	}
	return 0
}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) GetPricingScheme() string {
	if x != nil {
		return x.PricingScheme
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) GetEverVkycAttempted() int32 {
	if x != nil {
		return x.EverVkycAttempted
	}
	return 0
}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) GetPdScoreVersion() string {
	if x != nil {
		return x.PdScoreVersion
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) GetSchemeId() string {
	if x != nil {
		return x.SchemeId
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) GetPricingSchemeBre() string {
	if x != nil {
		return x.PricingSchemeBre
	}
	return ""
}

func (x *GetLoanDecisioningRequest_Values_Input_PolicyParams) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

type GetLoanDecisioningResponse_Decision struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Action          string                                            `protobuf:"bytes,1,opt,name=action,proto3" json:"action,omitempty"`
	OfferDetails    *GetLoanDecisioningResponse_Decision_OfferDetails `protobuf:"bytes,2,opt,name=offer_details,json=offerDetails,proto3" json:"offer_details,omitempty"`
	LoanProgram     string                                            `protobuf:"bytes,3,opt,name=loan_program,json=loanProgram,proto3" json:"loan_program,omitempty"`
	SchemeId        string                                            `protobuf:"bytes,4,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	CustId          string                                            `protobuf:"bytes,5,opt,name=cust_id,json=custId,proto3" json:"cust_id,omitempty"`
	ClientId        string                                            `protobuf:"bytes,6,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	BatchId         string                                            `protobuf:"bytes,7,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
	Reasons         []string                                          `protobuf:"bytes,8,rep,name=reasons,proto3" json:"reasons,omitempty"`
	ExternalReasons []string                                          `protobuf:"bytes,9,rep,name=external_reasons,json=externalReasons,proto3" json:"external_reasons,omitempty"`
	PolicyParams    *PolicyParams                                     `protobuf:"bytes,10,opt,name=policy_params,json=policyParams,proto3" json:"policy_params,omitempty"`
}

func (x *GetLoanDecisioningResponse_Decision) Reset() {
	*x = GetLoanDecisioningResponse_Decision{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningResponse_Decision) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningResponse_Decision) ProtoMessage() {}

func (x *GetLoanDecisioningResponse_Decision) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningResponse_Decision.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningResponse_Decision) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetLoanDecisioningResponse_Decision) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *GetLoanDecisioningResponse_Decision) GetOfferDetails() *GetLoanDecisioningResponse_Decision_OfferDetails {
	if x != nil {
		return x.OfferDetails
	}
	return nil
}

func (x *GetLoanDecisioningResponse_Decision) GetLoanProgram() string {
	if x != nil {
		return x.LoanProgram
	}
	return ""
}

func (x *GetLoanDecisioningResponse_Decision) GetSchemeId() string {
	if x != nil {
		return x.SchemeId
	}
	return ""
}

func (x *GetLoanDecisioningResponse_Decision) GetCustId() string {
	if x != nil {
		return x.CustId
	}
	return ""
}

func (x *GetLoanDecisioningResponse_Decision) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *GetLoanDecisioningResponse_Decision) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *GetLoanDecisioningResponse_Decision) GetReasons() []string {
	if x != nil {
		return x.Reasons
	}
	return nil
}

func (x *GetLoanDecisioningResponse_Decision) GetExternalReasons() []string {
	if x != nil {
		return x.ExternalReasons
	}
	return nil
}

func (x *GetLoanDecisioningResponse_Decision) GetPolicyParams() *PolicyParams {
	if x != nil {
		return x.PolicyParams
	}
	return nil
}

type GetLoanDecisioningResponse_Decision_OfferDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinAmount               float64 `protobuf:"fixed64,1,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	MaxAmount               float64 `protobuf:"fixed64,2,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	MaxEmiAmount            float64 `protobuf:"fixed64,3,opt,name=max_emi_amount,json=maxEmiAmount,proto3" json:"max_emi_amount,omitempty"`
	InterestPercentage      float64 `protobuf:"fixed64,4,opt,name=interest_percentage,json=interestPercentage,proto3" json:"interest_percentage,omitempty"`
	ProcessingFeePercentage float64 `protobuf:"fixed64,5,opt,name=processing_fee_percentage,json=processingFeePercentage,proto3" json:"processing_fee_percentage,omitempty"`
	MinTenureInMonths       int32   `protobuf:"varint,6,opt,name=min_tenure_in_months,json=minTenureInMonths,proto3" json:"min_tenure_in_months,omitempty"`
	MaxTenureInMonths       int32   `protobuf:"varint,7,opt,name=max_tenure_in_months,json=maxTenureInMonths,proto3" json:"max_tenure_in_months,omitempty"`
	EmiDueDate              string  `protobuf:"bytes,8,opt,name=emi_due_date,json=emiDueDate,proto3" json:"emi_due_date,omitempty"`
	GstPercentage           float64 `protobuf:"fixed64,9,opt,name=gst_percentage,json=gstPercentage,proto3" json:"gst_percentage,omitempty"`
	ValidTillTimestamp      string  `protobuf:"bytes,10,opt,name=valid_till_timestamp,json=validTill,proto3" json:"valid_till_timestamp,omitempty"`
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) Reset() {
	*x = GetLoanDecisioningResponse_Decision_OfferDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningResponse_Decision_OfferDetails) ProtoMessage() {}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningResponse_Decision_OfferDetails.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningResponse_Decision_OfferDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetMinAmount() float64 {
	if x != nil {
		return x.MinAmount
	}
	return 0
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetMaxAmount() float64 {
	if x != nil {
		return x.MaxAmount
	}
	return 0
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetMaxEmiAmount() float64 {
	if x != nil {
		return x.MaxEmiAmount
	}
	return 0
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetInterestPercentage() float64 {
	if x != nil {
		return x.InterestPercentage
	}
	return 0
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetProcessingFeePercentage() float64 {
	if x != nil {
		return x.ProcessingFeePercentage
	}
	return 0
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetMinTenureInMonths() int32 {
	if x != nil {
		return x.MinTenureInMonths
	}
	return 0
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetMaxTenureInMonths() int32 {
	if x != nil {
		return x.MaxTenureInMonths
	}
	return 0
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetEmiDueDate() string {
	if x != nil {
		return x.EmiDueDate
	}
	return ""
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetGstPercentage() float64 {
	if x != nil {
		return x.GstPercentage
	}
	return 0
}

func (x *GetLoanDecisioningResponse_Decision_OfferDetails) GetValidTillTimestamp() string {
	if x != nil {
		return x.ValidTillTimestamp
	}
	return ""
}

type PolicyParams_Pre struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PdScore           float64 `protobuf:"fixed64,1,opt,name=pd_score,json=pdScore,proto3" json:"pd_score,omitempty"`
	PdScoreVersion    string  `protobuf:"bytes,2,opt,name=pd_score_version,json=pdScoreVersion,proto3" json:"pd_score_version,omitempty"`
	SchemeId          string  `protobuf:"bytes,3,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	BatchId           string  `protobuf:"bytes,4,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
	EverVkycAttempted int32   `protobuf:"varint,5,opt,name=ever_vkyc_attempted,json=everVkycAttempted,proto3" json:"ever_vkyc_attempted,omitempty"`
	PricingScheme     string  `protobuf:"bytes,6,opt,name=pricing_scheme,json=pricingScheme,proto3" json:"pricing_scheme,omitempty"`
}

func (x *PolicyParams_Pre) Reset() {
	*x = PolicyParams_Pre{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyParams_Pre) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyParams_Pre) ProtoMessage() {}

func (x *PolicyParams_Pre) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyParams_Pre.ProtoReflect.Descriptor instead.
func (*PolicyParams_Pre) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{2, 0}
}

func (x *PolicyParams_Pre) GetPdScore() float64 {
	if x != nil {
		return x.PdScore
	}
	return 0
}

func (x *PolicyParams_Pre) GetPdScoreVersion() string {
	if x != nil {
		return x.PdScoreVersion
	}
	return ""
}

func (x *PolicyParams_Pre) GetSchemeId() string {
	if x != nil {
		return x.SchemeId
	}
	return ""
}

func (x *PolicyParams_Pre) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *PolicyParams_Pre) GetEverVkycAttempted() int32 {
	if x != nil {
		return x.EverVkycAttempted
	}
	return 0
}

func (x *PolicyParams_Pre) GetPricingScheme() string {
	if x != nil {
		return x.PricingScheme
	}
	return ""
}

type PolicyParams_Final struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SchemeId         string `protobuf:"bytes,1,opt,name=scheme_id,json=schemeId,proto3" json:"scheme_id,omitempty"`
	BatchId          string `protobuf:"bytes,2,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
	PricingSchemeBre string `protobuf:"bytes,3,opt,name=pricing_scheme_bre,json=pricingSchemeBRE,proto3" json:"pricing_scheme_bre,omitempty"`
}

func (x *PolicyParams_Final) Reset() {
	*x = PolicyParams_Final{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyParams_Final) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyParams_Final) ProtoMessage() {}

func (x *PolicyParams_Final) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyParams_Final.ProtoReflect.Descriptor instead.
func (*PolicyParams_Final) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{2, 1}
}

func (x *PolicyParams_Final) GetSchemeId() string {
	if x != nil {
		return x.SchemeId
	}
	return ""
}

func (x *PolicyParams_Final) GetBatchId() string {
	if x != nil {
		return x.BatchId
	}
	return ""
}

func (x *PolicyParams_Final) GetPricingSchemeBre() string {
	if x != nil {
		return x.PricingSchemeBre
	}
	return ""
}

type DataInfo_AaData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MedianAmountSalaryLast_180Days float64 `protobuf:"fixed64,1,opt,name=median_amount_salary_last_180_days,json=medianAmountSalaryLast180Days,proto3" json:"median_amount_salary_last_180_days,omitempty"`
}

func (x *DataInfo_AaData) Reset() {
	*x = DataInfo_AaData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DataInfo_AaData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DataInfo_AaData) ProtoMessage() {}

func (x *DataInfo_AaData) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DataInfo_AaData.ProtoReflect.Descriptor instead.
func (*DataInfo_AaData) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{5, 0}
}

func (x *DataInfo_AaData) GetMedianAmountSalaryLast_180Days() float64 {
	if x != nil {
		return x.MedianAmountSalaryLast_180Days
	}
	return 0
}

type ProductSpecificDataInfo_InsurancePolicyDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PolicyType           string  `protobuf:"bytes,1,opt,name=policy_type,json=policyType,proto3" json:"policy_type,omitempty"`
	PolicyTenureInMonths int32   `protobuf:"varint,2,opt,name=policy_tenure_in_months,json=policyTenureInMonths,proto3" json:"policy_tenure_in_months,omitempty"`
	PolicyReferenceId    string  `protobuf:"bytes,3,opt,name=policy_reference_id,json=policyReferenceId,proto3" json:"policy_reference_id,omitempty"`
	PolicyPremiumAmount  float64 `protobuf:"fixed64,4,opt,name=policy_premium_amount,json=policyPremiumAmount,proto3" json:"policy_premium_amount,omitempty"`
}

func (x *ProductSpecificDataInfo_InsurancePolicyDetails) Reset() {
	*x = ProductSpecificDataInfo_InsurancePolicyDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProductSpecificDataInfo_InsurancePolicyDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProductSpecificDataInfo_InsurancePolicyDetails) ProtoMessage() {}

func (x *ProductSpecificDataInfo_InsurancePolicyDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProductSpecificDataInfo_InsurancePolicyDetails.ProtoReflect.Descriptor instead.
func (*ProductSpecificDataInfo_InsurancePolicyDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{6, 0}
}

func (x *ProductSpecificDataInfo_InsurancePolicyDetails) GetPolicyType() string {
	if x != nil {
		return x.PolicyType
	}
	return ""
}

func (x *ProductSpecificDataInfo_InsurancePolicyDetails) GetPolicyTenureInMonths() int32 {
	if x != nil {
		return x.PolicyTenureInMonths
	}
	return 0
}

func (x *ProductSpecificDataInfo_InsurancePolicyDetails) GetPolicyReferenceId() string {
	if x != nil {
		return x.PolicyReferenceId
	}
	return ""
}

func (x *ProductSpecificDataInfo_InsurancePolicyDetails) GetPolicyPremiumAmount() float64 {
	if x != nil {
		return x.PolicyPremiumAmount
	}
	return 0
}

type CustomerDetails_RequestedLoanDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DesiredLoanAmount    float64   `protobuf:"fixed64,1,opt,name=desired_loan_amount,json=desiredLoanAmount,proto3" json:"desired_loan_amount,omitempty"`
	Tenure               *Duration `protobuf:"bytes,2,opt,name=tenure,proto3" json:"tenure,omitempty"`
	Roi                  string    `protobuf:"bytes,3,opt,name=roi,proto3" json:"roi,omitempty"`
	PfPlusGst            float64   `protobuf:"fixed64,4,opt,name=pf_plus_gst,json=pfAmountWithGst,proto3" json:"pf_plus_gst,omitempty"`
	UpfrontPaymentAmount float64   `protobuf:"fixed64,5,opt,name=upfront_payment_amount,json=upfrontPaymentAmount,proto3" json:"upfront_payment_amount,omitempty"`
}

func (x *CustomerDetails_RequestedLoanDetails) Reset() {
	*x = CustomerDetails_RequestedLoanDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDetails_RequestedLoanDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDetails_RequestedLoanDetails) ProtoMessage() {}

func (x *CustomerDetails_RequestedLoanDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDetails_RequestedLoanDetails.ProtoReflect.Descriptor instead.
func (*CustomerDetails_RequestedLoanDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{10, 0}
}

func (x *CustomerDetails_RequestedLoanDetails) GetDesiredLoanAmount() float64 {
	if x != nil {
		return x.DesiredLoanAmount
	}
	return 0
}

func (x *CustomerDetails_RequestedLoanDetails) GetTenure() *Duration {
	if x != nil {
		return x.Tenure
	}
	return nil
}

func (x *CustomerDetails_RequestedLoanDetails) GetRoi() string {
	if x != nil {
		return x.Roi
	}
	return ""
}

func (x *CustomerDetails_RequestedLoanDetails) GetPfPlusGst() float64 {
	if x != nil {
		return x.PfPlusGst
	}
	return 0
}

func (x *CustomerDetails_RequestedLoanDetails) GetUpfrontPaymentAmount() float64 {
	if x != nil {
		return x.UpfrontPaymentAmount
	}
	return 0
}

type CustomerDetails_KycDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AddressPincode int32 `protobuf:"varint,1,opt,name=address_pincode,json=addressPincode,proto3" json:"address_pincode,omitempty"`
}

func (x *CustomerDetails_KycDetails) Reset() {
	*x = CustomerDetails_KycDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerDetails_KycDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerDetails_KycDetails) ProtoMessage() {}

func (x *CustomerDetails_KycDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerDetails_KycDetails.ProtoReflect.Descriptor instead.
func (*CustomerDetails_KycDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{10, 1}
}

func (x *CustomerDetails_KycDetails) GetAddressPincode() int32 {
	if x != nil {
		return x.AddressPincode
	}
	return 0
}

type GetLoanDecisioningRequestV2_Values struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Input *GetLoanDecisioningRequestV2_Values_Input `protobuf:"bytes,1,opt,name=input,proto3" json:"input,omitempty"`
}

func (x *GetLoanDecisioningRequestV2_Values) Reset() {
	*x = GetLoanDecisioningRequestV2_Values{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningRequestV2_Values) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningRequestV2_Values) ProtoMessage() {}

func (x *GetLoanDecisioningRequestV2_Values) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningRequestV2_Values.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningRequestV2_Values) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{11, 0}
}

func (x *GetLoanDecisioningRequestV2_Values) GetInput() *GetLoanDecisioningRequestV2_Values_Input {
	if x != nil {
		return x.Input
	}
	return nil
}

type GetLoanDecisioningRequestV2_Values_Input struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CustomerId            string           `protobuf:"bytes,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	ApplicationId         string           `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	LoanAmount            float64          `protobuf:"fixed64,3,opt,name=loan_amount,json=loanAmount,proto3" json:"loan_amount,omitempty"`
	EvaluationRequestTime string           `protobuf:"bytes,4,opt,name=evaluation_request_time,json=evaluationRequestTime,proto3" json:"evaluation_request_time,omitempty"`
	RequestId             string           `protobuf:"bytes,5,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	CustomerDetails       *CustomerDetails `protobuf:"bytes,6,opt,name=customer_details,json=customerDetails,proto3" json:"customer_details,omitempty"`
	Product               string           `protobuf:"bytes,7,opt,name=product,proto3" json:"product,omitempty"`
	PolicyParams          *PolicyParams    `protobuf:"bytes,8,opt,name=policy_params,json=policyParams,proto3" json:"policy_params,omitempty"`
	Bureau                string           `protobuf:"bytes,9,opt,name=bureau,proto3" json:"bureau,omitempty"`
	ClientId              string           `protobuf:"bytes,10,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *GetLoanDecisioningRequestV2_Values_Input) Reset() {
	*x = GetLoanDecisioningRequestV2_Values_Input{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDecisioningRequestV2_Values_Input) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDecisioningRequestV2_Values_Input) ProtoMessage() {}

func (x *GetLoanDecisioningRequestV2_Values_Input) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDecisioningRequestV2_Values_Input.ProtoReflect.Descriptor instead.
func (*GetLoanDecisioningRequestV2_Values_Input) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP(), []int{11, 0, 0}
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetLoanAmount() float64 {
	if x != nil {
		return x.LoanAmount
	}
	return 0
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetEvaluationRequestTime() string {
	if x != nil {
		return x.EvaluationRequestTime
	}
	return ""
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetCustomerDetails() *CustomerDetails {
	if x != nil {
		return x.CustomerDetails
	}
	return nil
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetPolicyParams() *PolicyParams {
	if x != nil {
		return x.PolicyParams
	}
	return nil
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetBureau() string {
	if x != nil {
		return x.Bureau
	}
	return ""
}

func (x *GetLoanDecisioningRequestV2_Values_Input) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

var File_api_stockguardian_vendors_inhouse_bre_proto protoreflect.FileDescriptor

var file_api_stockguardian_vendors_inhouse_bre_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x2f, 0x62, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd6,
	0x08, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x06,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x73, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0xdb, 0x07, 0x0a, 0x06, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x12, 0x5f, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x2e, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x05,
	0x69, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0xef, 0x06, 0x0a, 0x05, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12,
	0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x70, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x6f, 0x62,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x69,
	0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70, 0x69, 0x6e,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x5f, 0x69, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x64, 0x65, 0x63, 0x6c,
	0x61, 0x72, 0x65, 0x64, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x7b, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x56, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x2e, 0x49, 0x6e, 0x70, 0x75,
	0x74, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x90, 0x02, 0x0a, 0x0c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x76, 0x65, 0x72, 0x5f,
	0x76, 0x6b, 0x79, 0x63, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x65, 0x76, 0x65, 0x72, 0x56, 0x6b, 0x79, 0x63, 0x41, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x64, 0x5f, 0x73, 0x63,
	0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x70, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x5f, 0x62, 0x72, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x52, 0x45, 0x12, 0x19, 0x0a, 0x08,
	0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x62, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x22, 0x81, 0x08, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x62, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x08, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0xfe, 0x06, 0x0a, 0x08, 0x44,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x78, 0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x53, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6c, 0x6f, 0x61,
	0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x75, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12,
	0x54, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a, 0xb3, 0x03, 0x0a, 0x0c, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x65, 0x6d, 0x69, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x6d, 0x61,
	0x78, 0x45, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2f, 0x0a, 0x13, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x19, 0x70,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17,
	0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x50, 0x65, 0x72,
	0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x6d, 0x69, 0x6e, 0x5f, 0x74,
	0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x69, 0x6e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65,
	0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f,
	0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x61, 0x78, 0x54, 0x65, 0x6e, 0x75, 0x72,
	0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x65, 0x6d, 0x69,
	0x5f, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x65, 0x6d, 0x69, 0x44, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x67,
	0x73, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x0d, 0x67, 0x73, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x27, 0x0a, 0x14, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x54, 0x69, 0x6c, 0x6c, 0x22, 0x89, 0x06, 0x0a, 0x0c,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x45, 0x0a, 0x03,
	0x70, 0x72, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x52, 0x03,
	0x70, 0x72, 0x65, 0x12, 0x4b, 0x0a, 0x05, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75,
	0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6c, 0x52, 0x05, 0x66, 0x69, 0x6e, 0x61, 0x6c,
	0x12, 0x57, 0x0a, 0x0e, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x65, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x48, 0x0a, 0x09, 0x64, 0x61, 0x74,
	0x61, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x77, 0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67,
	0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x61, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xd9, 0x01, 0x0a,
	0x03, 0x50, 0x72, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x64, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x70, 0x64, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x64, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x49,
	0x64, 0x12, 0x2e, 0x0a, 0x13, 0x65, 0x76, 0x65, 0x72, 0x5f, 0x76, 0x6b, 0x79, 0x63, 0x5f, 0x61,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11,
	0x65, 0x76, 0x65, 0x72, 0x56, 0x6b, 0x79, 0x63, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x65,
	0x64, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x1a, 0x6d, 0x0a, 0x05, 0x46, 0x69, 0x6e, 0x61,
	0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f, 0x62, 0x72, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x65, 0x42, 0x52, 0x45, 0x22, 0xa9, 0x01, 0x0a, 0x0d, 0x45, 0x78, 0x65, 0x63,
	0x75, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x03, 0x70, 0x72, 0x65,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x03, 0x70, 0x72, 0x65, 0x12, 0x4d, 0x0a, 0x05, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x05, 0x66, 0x69,
	0x6e, 0x61, 0x6c, 0x22, 0x5b, 0x0a, 0x14, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x62,
	0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62,
	0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x0e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x42, 0x52, 0x45,
	0x22, 0xbe, 0x03, 0x0a, 0x08, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4b, 0x0a,
	0x07, 0x61, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62,
	0x72, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x41, 0x61, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x06, 0x61, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1e, 0x0a, 0x0b, 0x69, 0x73,
	0x5f, 0x65, 0x74, 0x62, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x73, 0x45, 0x74, 0x62, 0x55, 0x73, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6d, 0x69, 0x5f, 0x6f, 0x62, 0x6c, 0x69, 0x67, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x63, 0x75, 0x72, 0x72,
	0x65, 0x6e, 0x74, 0x45, 0x6d, 0x69, 0x4f, 0x62, 0x6c, 0x69, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2b, 0x0a, 0x12, 0x69, 0x73, 0x5f, 0x62, 0x32, 0x62, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x69, 0x73,
	0x42, 0x32, 0x62, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x55, 0x73, 0x65, 0x72, 0x12, 0x25, 0x0a,
	0x0e, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x1a, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x5f, 0x73,
	0x69, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x76, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x53,
	0x69, 0x6e, 0x63, 0x65, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65,
	0x12, 0x2a, 0x0a, 0x11, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x64, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x73, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x44, 0x61, 0x79, 0x1a, 0x53, 0x0a, 0x06,
	0x41, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x22, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x6e,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x6c,
	0x61, 0x73, 0x74, 0x5f, 0x31, 0x38, 0x30, 0x5f, 0x64, 0x61, 0x79, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x1d, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x4c, 0x61, 0x73, 0x74, 0x31, 0x38, 0x30, 0x44, 0x61, 0x79,
	0x73, 0x22, 0xfe, 0x02, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x8b, 0x01,
	0x0a, 0x18, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x51, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65,
	0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x44, 0x61, 0x74, 0x61, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x49, 0x6e, 0x73,
	0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x16, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xd4, 0x01, 0x0a, 0x16,
	0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x35, 0x0a, 0x17, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74,
	0x68, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x14, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x2e,
	0x0a, 0x13, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x32,
	0x0a, 0x15, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x70, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x13, 0x70,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x72, 0x65, 0x6d, 0x69, 0x75, 0x6d, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x9a, 0x01, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64,
	0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f,
	0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22,
	0xfe, 0x01, 0x0a, 0x11, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d,
	0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x5f,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x15, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x65, 0x64, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77,
	0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f,
	0x72, 0x6b, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a,
	0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65,
	0x22, 0x67, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a, 0x07,
	0x70, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x70,
	0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb0, 0x07, 0x0a, 0x0f, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5d, 0x0a,
	0x10, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67,
	0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e,
	0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x70, 0x65, 0x72,
	0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x63, 0x0a, 0x12,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x45, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x11,
	0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x5a, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x7d, 0x0a,
	0x16, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x47, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72,
	0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x61, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5e, 0x0a, 0x0b,
	0x6b, 0x79, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x3d, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73,
	0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4b, 0x79, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0a, 0x6b, 0x79, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6b, 0x0a, 0x18,
	0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62,
	0x72, 0x65, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x16, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xf9, 0x01, 0x0a, 0x14, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x11, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75,
	0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x69, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x72, 0x6f, 0x69, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x66, 0x5f,
	0x70, 0x6c, 0x75, 0x73, 0x5f, 0x67, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0f,
	0x70, 0x66, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x57, 0x69, 0x74, 0x68, 0x47, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x16, 0x75, 0x70, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x14, 0x75, 0x70, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x1a, 0x35, 0x0a, 0x0a, 0x4b, 0x79, 0x63, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x70,
	0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x50, 0x69, 0x6e, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xb8, 0x05, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x12, 0x5d, 0x0a, 0x06,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0xb9, 0x04, 0x0a, 0x06,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x12, 0x61, 0x0a, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61,
	0x6e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x56, 0x32, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x2e, 0x49, 0x6e, 0x70,
	0x75, 0x74, 0x52, 0x05, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x1a, 0xcb, 0x03, 0x0a, 0x05, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x36, 0x0a, 0x17,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x65,
	0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x10, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72,
	0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x0f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x54, 0x0a, 0x0d,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xa0, 0x03, 0x0a, 0x0c, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x65, 0x6d, 0x69, 0x44,
	0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6d,
	0x69, 0x44, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x67, 0x73, 0x74, 0x50,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0d, 0x67, 0x73, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2e,
	0x0a, 0x12, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x6d, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0c,
	0x6d, 0x61, 0x78, 0x45, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x0c, 0x6d, 0x61, 0x78, 0x45, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x2c, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x61, 0x78,
	0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x1c,
	0x0a, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x11,
	0x6d, 0x69, 0x6e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68,
	0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x69, 0x6e, 0x54, 0x65, 0x6e, 0x75,
	0x72, 0x65, 0x49, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x38, 0x0a, 0x17, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e,
	0x74, 0x61, 0x67, 0x65, 0x12, 0x24, 0x0a, 0x0d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x22, 0xc7, 0x03, 0x0a, 0x08, 0x44,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x27, 0x0a, 0x0f, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x5f, 0x74, 0x69, 0x6c, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x54, 0x69, 0x6c, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x72, 0x65, 0x61,
	0x75, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x72, 0x65, 0x61, 0x75, 0x12,
	0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x2b, 0x0a, 0x11, 0x72, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x29, 0x0a, 0x10,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x65,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x0b,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x54,
	0x0a, 0x0d, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0x89, 0x04, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x56, 0x32, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x36, 0x0a,
	0x17, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15,
	0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x54,
	0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e,
	0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x5e, 0x0a, 0x14, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69,
	0x7a, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75,
	0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x13, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x69, 0x7a, 0x65, 0x64, 0x44, 0x65, 0x63, 0x69,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x47, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69,
	0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x08, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a,
	0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0x4b, 0x0a, 0x08, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08,
	0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x6e, 0x69, 0x74, 0x42, 0x7c, 0x0a,
	0x3c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x5a, 0x3c, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67,
	0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f,
	0x69, 0x6e, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x2f, 0x62, 0x72, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_vendors_inhouse_bre_proto_rawDescOnce sync.Once
	file_api_stockguardian_vendors_inhouse_bre_proto_rawDescData = file_api_stockguardian_vendors_inhouse_bre_proto_rawDesc
)

func file_api_stockguardian_vendors_inhouse_bre_proto_rawDescGZIP() []byte {
	file_api_stockguardian_vendors_inhouse_bre_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_vendors_inhouse_bre_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_vendors_inhouse_bre_proto_rawDescData)
	})
	return file_api_stockguardian_vendors_inhouse_bre_proto_rawDescData
}

var file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_api_stockguardian_vendors_inhouse_bre_proto_goTypes = []interface{}{
	(*GetLoanDecisioningRequest)(nil),                           // 0: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest
	(*GetLoanDecisioningResponse)(nil),                          // 1: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponse
	(*PolicyParams)(nil),                                        // 2: stockguardian.vendors.inhouse.bre.PolicyParams
	(*ExecutionInfo)(nil),                                       // 3: stockguardian.vendors.inhouse.bre.ExecutionInfo
	(*ExecutionInfoDetails)(nil),                                // 4: stockguardian.vendors.inhouse.bre.ExecutionInfoDetails
	(*DataInfo)(nil),                                            // 5: stockguardian.vendors.inhouse.bre.DataInfo
	(*ProductSpecificDataInfo)(nil),                             // 6: stockguardian.vendors.inhouse.bre.ProductSpecificDataInfo
	(*PersonalDetails)(nil),                                     // 7: stockguardian.vendors.inhouse.bre.PersonalDetails
	(*EmploymentDetails)(nil),                                   // 8: stockguardian.vendors.inhouse.bre.EmploymentDetails
	(*AddressDetails)(nil),                                      // 9: stockguardian.vendors.inhouse.bre.AddressDetails
	(*CustomerDetails)(nil),                                     // 10: stockguardian.vendors.inhouse.bre.CustomerDetails
	(*GetLoanDecisioningRequestV2)(nil),                         // 11: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequestV2
	(*OfferDetails)(nil),                                        // 12: stockguardian.vendors.inhouse.bre.OfferDetails
	(*Decision)(nil),                                            // 13: stockguardian.vendors.inhouse.bre.Decision
	(*GetLoanDecisioningResponseV2)(nil),                        // 14: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponseV2
	(*Duration)(nil),                                            // 15: stockguardian.vendors.inhouse.bre.Duration
	(*GetLoanDecisioningRequest_Values)(nil),                    // 16: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest.Values
	(*GetLoanDecisioningRequest_Values_Input)(nil),              // 17: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest.Values.Input
	(*GetLoanDecisioningRequest_Values_Input_PolicyParams)(nil), // 18: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest.Values.Input.PolicyParams
	(*GetLoanDecisioningResponse_Decision)(nil),                 // 19: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponse.Decision
	(*GetLoanDecisioningResponse_Decision_OfferDetails)(nil),    // 20: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponse.Decision.OfferDetails
	(*PolicyParams_Pre)(nil),                                    // 21: stockguardian.vendors.inhouse.bre.PolicyParams.Pre
	(*PolicyParams_Final)(nil),                                  // 22: stockguardian.vendors.inhouse.bre.PolicyParams.Final
	(*DataInfo_AaData)(nil),                                     // 23: stockguardian.vendors.inhouse.bre.DataInfo.AaData
	(*ProductSpecificDataInfo_InsurancePolicyDetails)(nil),      // 24: stockguardian.vendors.inhouse.bre.ProductSpecificDataInfo.InsurancePolicyDetails
	(*CustomerDetails_RequestedLoanDetails)(nil),                // 25: stockguardian.vendors.inhouse.bre.CustomerDetails.RequestedLoanDetails
	(*CustomerDetails_KycDetails)(nil),                          // 26: stockguardian.vendors.inhouse.bre.CustomerDetails.KycDetails
	(*GetLoanDecisioningRequestV2_Values)(nil),                  // 27: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequestV2.Values
	(*GetLoanDecisioningRequestV2_Values_Input)(nil),            // 28: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequestV2.Values.Input
	(*structpb.Struct)(nil),                                     // 29: google.protobuf.Struct
}
var file_api_stockguardian_vendors_inhouse_bre_proto_depIdxs = []int32{
	16, // 0: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest.values:type_name -> stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest.Values
	19, // 1: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponse.decision:type_name -> stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponse.Decision
	21, // 2: stockguardian.vendors.inhouse.bre.PolicyParams.pre:type_name -> stockguardian.vendors.inhouse.bre.PolicyParams.Pre
	22, // 3: stockguardian.vendors.inhouse.bre.PolicyParams.final:type_name -> stockguardian.vendors.inhouse.bre.PolicyParams.Final
	3,  // 4: stockguardian.vendors.inhouse.bre.PolicyParams.execution_info:type_name -> stockguardian.vendors.inhouse.bre.ExecutionInfo
	5,  // 5: stockguardian.vendors.inhouse.bre.PolicyParams.data_info:type_name -> stockguardian.vendors.inhouse.bre.DataInfo
	6,  // 6: stockguardian.vendors.inhouse.bre.PolicyParams.product_specific_data_info:type_name -> stockguardian.vendors.inhouse.bre.ProductSpecificDataInfo
	4,  // 7: stockguardian.vendors.inhouse.bre.ExecutionInfo.pre:type_name -> stockguardian.vendors.inhouse.bre.ExecutionInfoDetails
	4,  // 8: stockguardian.vendors.inhouse.bre.ExecutionInfo.final:type_name -> stockguardian.vendors.inhouse.bre.ExecutionInfoDetails
	23, // 9: stockguardian.vendors.inhouse.bre.DataInfo.aa_data:type_name -> stockguardian.vendors.inhouse.bre.DataInfo.AaData
	24, // 10: stockguardian.vendors.inhouse.bre.ProductSpecificDataInfo.insurance_policy_details:type_name -> stockguardian.vendors.inhouse.bre.ProductSpecificDataInfo.InsurancePolicyDetails
	7,  // 11: stockguardian.vendors.inhouse.bre.CustomerDetails.personal_details:type_name -> stockguardian.vendors.inhouse.bre.PersonalDetails
	8,  // 12: stockguardian.vendors.inhouse.bre.CustomerDetails.employment_details:type_name -> stockguardian.vendors.inhouse.bre.EmploymentDetails
	9,  // 13: stockguardian.vendors.inhouse.bre.CustomerDetails.address_details:type_name -> stockguardian.vendors.inhouse.bre.AddressDetails
	25, // 14: stockguardian.vendors.inhouse.bre.CustomerDetails.requested_loan_details:type_name -> stockguardian.vendors.inhouse.bre.CustomerDetails.RequestedLoanDetails
	26, // 15: stockguardian.vendors.inhouse.bre.CustomerDetails.kyc_details:type_name -> stockguardian.vendors.inhouse.bre.CustomerDetails.KycDetails
	9,  // 16: stockguardian.vendors.inhouse.bre.CustomerDetails.current_location_details:type_name -> stockguardian.vendors.inhouse.bre.AddressDetails
	27, // 17: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequestV2.values:type_name -> stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequestV2.Values
	12, // 18: stockguardian.vendors.inhouse.bre.Decision.offer_details:type_name -> stockguardian.vendors.inhouse.bre.OfferDetails
	2,  // 19: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponseV2.policy_params:type_name -> stockguardian.vendors.inhouse.bre.PolicyParams
	13, // 20: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponseV2.prioritized_decision:type_name -> stockguardian.vendors.inhouse.bre.Decision
	13, // 21: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponseV2.decision:type_name -> stockguardian.vendors.inhouse.bre.Decision
	29, // 22: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponseV2.details:type_name -> google.protobuf.Struct
	17, // 23: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest.Values.input:type_name -> stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest.Values.Input
	18, // 24: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest.Values.Input.policy_params:type_name -> stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequest.Values.Input.PolicyParams
	20, // 25: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponse.Decision.offer_details:type_name -> stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponse.Decision.OfferDetails
	2,  // 26: stockguardian.vendors.inhouse.bre.GetLoanDecisioningResponse.Decision.policy_params:type_name -> stockguardian.vendors.inhouse.bre.PolicyParams
	15, // 27: stockguardian.vendors.inhouse.bre.CustomerDetails.RequestedLoanDetails.tenure:type_name -> stockguardian.vendors.inhouse.bre.Duration
	28, // 28: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequestV2.Values.input:type_name -> stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequestV2.Values.Input
	10, // 29: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequestV2.Values.Input.customer_details:type_name -> stockguardian.vendors.inhouse.bre.CustomerDetails
	2,  // 30: stockguardian.vendors.inhouse.bre.GetLoanDecisioningRequestV2.Values.Input.policy_params:type_name -> stockguardian.vendors.inhouse.bre.PolicyParams
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_api_stockguardian_vendors_inhouse_bre_proto_init() }
func file_api_stockguardian_vendors_inhouse_bre_proto_init() {
	if File_api_stockguardian_vendors_inhouse_bre_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExecutionInfoDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductSpecificDataInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningRequestV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Decision); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningResponseV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Duration); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningRequest_Values); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningRequest_Values_Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningRequest_Values_Input_PolicyParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningResponse_Decision); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningResponse_Decision_OfferDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyParams_Pre); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyParams_Final); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DataInfo_AaData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProductSpecificDataInfo_InsurancePolicyDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDetails_RequestedLoanDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerDetails_KycDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningRequestV2_Values); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDecisioningRequestV2_Values_Input); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_vendors_inhouse_bre_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_stockguardian_vendors_inhouse_bre_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_vendors_inhouse_bre_proto_depIdxs,
		MessageInfos:      file_api_stockguardian_vendors_inhouse_bre_proto_msgTypes,
	}.Build()
	File_api_stockguardian_vendors_inhouse_bre_proto = out.File
	file_api_stockguardian_vendors_inhouse_bre_proto_rawDesc = nil
	file_api_stockguardian_vendors_inhouse_bre_proto_goTypes = nil
	file_api_stockguardian_vendors_inhouse_bre_proto_depIdxs = nil
}
