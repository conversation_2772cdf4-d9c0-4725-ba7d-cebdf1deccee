// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/stockguardian/vendors/inhouse/bre.proto

package bre

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetLoanDecisioningRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningRequestMultiError, or nil if none found.
func (m *GetLoanDecisioningRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValues()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValues()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestValidationError{
				field:  "Values",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDecisioningRequestMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequestMultiError is an error wrapping multiple validation
// errors returned by GetLoanDecisioningRequest.ValidateAll() if the
// designated constraints aren't met.
type GetLoanDecisioningRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequestMultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequestValidationError is the validation error returned by
// GetLoanDecisioningRequest.Validate if the designated constraints aren't met.
type GetLoanDecisioningRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequestValidationError) ErrorName() string {
	return "GetLoanDecisioningRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequestValidationError{}

// Validate checks the field values on GetLoanDecisioningResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningResponseMultiError, or nil if none found.
func (m *GetLoanDecisioningResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDecision() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanDecisioningResponseValidationError{
						field:  fmt.Sprintf("Decision[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanDecisioningResponseValidationError{
						field:  fmt.Sprintf("Decision[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanDecisioningResponseValidationError{
					field:  fmt.Sprintf("Decision[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetLoanDecisioningResponseMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningResponseMultiError is an error wrapping multiple
// validation errors returned by GetLoanDecisioningResponse.ValidateAll() if
// the designated constraints aren't met.
type GetLoanDecisioningResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningResponseMultiError) AllErrors() []error { return m }

// GetLoanDecisioningResponseValidationError is the validation error returned
// by GetLoanDecisioningResponse.Validate if the designated constraints aren't met.
type GetLoanDecisioningResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningResponseValidationError) ErrorName() string {
	return "GetLoanDecisioningResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningResponseValidationError{}

// Validate checks the field values on PolicyParams with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PolicyParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PolicyParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PolicyParamsMultiError, or
// nil if none found.
func (m *PolicyParams) ValidateAll() error {
	return m.validate(true)
}

func (m *PolicyParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPre()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "Pre",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "Pre",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPre()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PolicyParamsValidationError{
				field:  "Pre",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinal()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "Final",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "Final",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinal()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PolicyParamsValidationError{
				field:  "Final",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExecutionInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "ExecutionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "ExecutionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExecutionInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PolicyParamsValidationError{
				field:  "ExecutionInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDataInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "DataInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "DataInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PolicyParamsValidationError{
				field:  "DataInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProductSpecificDataInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "ProductSpecificDataInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PolicyParamsValidationError{
					field:  "ProductSpecificDataInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProductSpecificDataInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PolicyParamsValidationError{
				field:  "ProductSpecificDataInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PolicyParamsMultiError(errors)
	}

	return nil
}

// PolicyParamsMultiError is an error wrapping multiple validation errors
// returned by PolicyParams.ValidateAll() if the designated constraints aren't met.
type PolicyParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PolicyParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PolicyParamsMultiError) AllErrors() []error { return m }

// PolicyParamsValidationError is the validation error returned by
// PolicyParams.Validate if the designated constraints aren't met.
type PolicyParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PolicyParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PolicyParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PolicyParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PolicyParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PolicyParamsValidationError) ErrorName() string { return "PolicyParamsValidationError" }

// Error satisfies the builtin error interface
func (e PolicyParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPolicyParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PolicyParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PolicyParamsValidationError{}

// Validate checks the field values on ExecutionInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ExecutionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecutionInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ExecutionInfoMultiError, or
// nil if none found.
func (m *ExecutionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecutionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetPre() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecutionInfoValidationError{
						field:  fmt.Sprintf("Pre[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecutionInfoValidationError{
						field:  fmt.Sprintf("Pre[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecutionInfoValidationError{
					field:  fmt.Sprintf("Pre[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetFinal() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ExecutionInfoValidationError{
						field:  fmt.Sprintf("Final[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ExecutionInfoValidationError{
						field:  fmt.Sprintf("Final[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ExecutionInfoValidationError{
					field:  fmt.Sprintf("Final[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ExecutionInfoMultiError(errors)
	}

	return nil
}

// ExecutionInfoMultiError is an error wrapping multiple validation errors
// returned by ExecutionInfo.ValidateAll() if the designated constraints
// aren't met.
type ExecutionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecutionInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecutionInfoMultiError) AllErrors() []error { return m }

// ExecutionInfoValidationError is the validation error returned by
// ExecutionInfo.Validate if the designated constraints aren't met.
type ExecutionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecutionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecutionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecutionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecutionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecutionInfoValidationError) ErrorName() string { return "ExecutionInfoValidationError" }

// Error satisfies the builtin error interface
func (e ExecutionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecutionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecutionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecutionInfoValidationError{}

// Validate checks the field values on ExecutionInfoDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExecutionInfoDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExecutionInfoDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExecutionInfoDetailsMultiError, or nil if none found.
func (m *ExecutionInfoDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ExecutionInfoDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BatchId

	// no validation rules for PricingScheme

	if len(errors) > 0 {
		return ExecutionInfoDetailsMultiError(errors)
	}

	return nil
}

// ExecutionInfoDetailsMultiError is an error wrapping multiple validation
// errors returned by ExecutionInfoDetails.ValidateAll() if the designated
// constraints aren't met.
type ExecutionInfoDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExecutionInfoDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExecutionInfoDetailsMultiError) AllErrors() []error { return m }

// ExecutionInfoDetailsValidationError is the validation error returned by
// ExecutionInfoDetails.Validate if the designated constraints aren't met.
type ExecutionInfoDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExecutionInfoDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExecutionInfoDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExecutionInfoDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExecutionInfoDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExecutionInfoDetailsValidationError) ErrorName() string {
	return "ExecutionInfoDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ExecutionInfoDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExecutionInfoDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExecutionInfoDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExecutionInfoDetailsValidationError{}

// Validate checks the field values on DataInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DataInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DataInfoMultiError, or nil
// if none found.
func (m *DataInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DataInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DataInfoValidationError{
					field:  "AaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DataInfoValidationError{
					field:  "AaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DataInfoValidationError{
				field:  "AaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEtbUser

	// no validation rules for CurrentEmiObligation

	// no validation rules for IsB2BSalaryUser

	// no validation rules for MonthlyIncome

	// no validation rules for MonthsSinceSalaryActive

	// no validation rules for SalaryCreditDay

	if len(errors) > 0 {
		return DataInfoMultiError(errors)
	}

	return nil
}

// DataInfoMultiError is an error wrapping multiple validation errors returned
// by DataInfo.ValidateAll() if the designated constraints aren't met.
type DataInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataInfoMultiError) AllErrors() []error { return m }

// DataInfoValidationError is the validation error returned by
// DataInfo.Validate if the designated constraints aren't met.
type DataInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataInfoValidationError) ErrorName() string { return "DataInfoValidationError" }

// Error satisfies the builtin error interface
func (e DataInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataInfoValidationError{}

// Validate checks the field values on ProductSpecificDataInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProductSpecificDataInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProductSpecificDataInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProductSpecificDataInfoMultiError, or nil if none found.
func (m *ProductSpecificDataInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ProductSpecificDataInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInsurancePolicyDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProductSpecificDataInfoValidationError{
					field:  "InsurancePolicyDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProductSpecificDataInfoValidationError{
					field:  "InsurancePolicyDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInsurancePolicyDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProductSpecificDataInfoValidationError{
				field:  "InsurancePolicyDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProductSpecificDataInfoMultiError(errors)
	}

	return nil
}

// ProductSpecificDataInfoMultiError is an error wrapping multiple validation
// errors returned by ProductSpecificDataInfo.ValidateAll() if the designated
// constraints aren't met.
type ProductSpecificDataInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProductSpecificDataInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProductSpecificDataInfoMultiError) AllErrors() []error { return m }

// ProductSpecificDataInfoValidationError is the validation error returned by
// ProductSpecificDataInfo.Validate if the designated constraints aren't met.
type ProductSpecificDataInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProductSpecificDataInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProductSpecificDataInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProductSpecificDataInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProductSpecificDataInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProductSpecificDataInfoValidationError) ErrorName() string {
	return "ProductSpecificDataInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ProductSpecificDataInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProductSpecificDataInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProductSpecificDataInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProductSpecificDataInfoValidationError{}

// Validate checks the field values on PersonalDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PersonalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PersonalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PersonalDetailsMultiError, or nil if none found.
func (m *PersonalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PersonalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Dob

	// no validation rules for Gender

	// no validation rules for Name

	// no validation rules for Pan

	// no validation rules for PhoneNumber

	// no validation rules for Email

	if len(errors) > 0 {
		return PersonalDetailsMultiError(errors)
	}

	return nil
}

// PersonalDetailsMultiError is an error wrapping multiple validation errors
// returned by PersonalDetails.ValidateAll() if the designated constraints
// aren't met.
type PersonalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PersonalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PersonalDetailsMultiError) AllErrors() []error { return m }

// PersonalDetailsValidationError is the validation error returned by
// PersonalDetails.Validate if the designated constraints aren't met.
type PersonalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PersonalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PersonalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PersonalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PersonalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PersonalDetailsValidationError) ErrorName() string { return "PersonalDetailsValidationError" }

// Error satisfies the builtin error interface
func (e PersonalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPersonalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PersonalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PersonalDetailsValidationError{}

// Validate checks the field values on EmploymentDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EmploymentDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EmploymentDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EmploymentDetailsMultiError, or nil if none found.
func (m *EmploymentDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *EmploymentDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmployerName

	// no validation rules for EmploymentType

	// no validation rules for DeclaredMonthlyIncome

	// no validation rules for WorkEmail

	// no validation rules for WorkAddress

	// no validation rules for WorkPincode

	if len(errors) > 0 {
		return EmploymentDetailsMultiError(errors)
	}

	return nil
}

// EmploymentDetailsMultiError is an error wrapping multiple validation errors
// returned by EmploymentDetails.ValidateAll() if the designated constraints
// aren't met.
type EmploymentDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EmploymentDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EmploymentDetailsMultiError) AllErrors() []error { return m }

// EmploymentDetailsValidationError is the validation error returned by
// EmploymentDetails.Validate if the designated constraints aren't met.
type EmploymentDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EmploymentDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EmploymentDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EmploymentDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EmploymentDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EmploymentDetailsValidationError) ErrorName() string {
	return "EmploymentDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e EmploymentDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEmploymentDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EmploymentDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EmploymentDetailsValidationError{}

// Validate checks the field values on AddressDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AddressDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddressDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AddressDetailsMultiError,
// or nil if none found.
func (m *AddressDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AddressDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Address

	// no validation rules for Pincode

	// no validation rules for AddressType

	if len(errors) > 0 {
		return AddressDetailsMultiError(errors)
	}

	return nil
}

// AddressDetailsMultiError is an error wrapping multiple validation errors
// returned by AddressDetails.ValidateAll() if the designated constraints
// aren't met.
type AddressDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddressDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddressDetailsMultiError) AllErrors() []error { return m }

// AddressDetailsValidationError is the validation error returned by
// AddressDetails.Validate if the designated constraints aren't met.
type AddressDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddressDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddressDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddressDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddressDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddressDetailsValidationError) ErrorName() string { return "AddressDetailsValidationError" }

// Error satisfies the builtin error interface
func (e AddressDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddressDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddressDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddressDetailsValidationError{}

// Validate checks the field values on CustomerDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CustomerDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerDetailsMultiError, or nil if none found.
func (m *CustomerDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPersonalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "PersonalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "PersonalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmploymentDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "EmploymentDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmploymentDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "EmploymentDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddressDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "AddressDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddressDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "AddressDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRequestedLoanDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "RequestedLoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "RequestedLoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestedLoanDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "RequestedLoanDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKycDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "KycDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "KycDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "KycDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCurrentLocationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "CurrentLocationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetailsValidationError{
					field:  "CurrentLocationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCurrentLocationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetailsValidationError{
				field:  "CurrentLocationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CustomerDetailsMultiError(errors)
	}

	return nil
}

// CustomerDetailsMultiError is an error wrapping multiple validation errors
// returned by CustomerDetails.ValidateAll() if the designated constraints
// aren't met.
type CustomerDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerDetailsMultiError) AllErrors() []error { return m }

// CustomerDetailsValidationError is the validation error returned by
// CustomerDetails.Validate if the designated constraints aren't met.
type CustomerDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerDetailsValidationError) ErrorName() string { return "CustomerDetailsValidationError" }

// Error satisfies the builtin error interface
func (e CustomerDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerDetailsValidationError{}

// Validate checks the field values on GetLoanDecisioningRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningRequestV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningRequestV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningRequestV2MultiError, or nil if none found.
func (m *GetLoanDecisioningRequestV2) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequestV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetValues()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2ValidationError{
					field:  "Values",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValues()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestV2ValidationError{
				field:  "Values",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDecisioningRequestV2MultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequestV2MultiError is an error wrapping multiple
// validation errors returned by GetLoanDecisioningRequestV2.ValidateAll() if
// the designated constraints aren't met.
type GetLoanDecisioningRequestV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequestV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequestV2MultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequestV2ValidationError is the validation error returned
// by GetLoanDecisioningRequestV2.Validate if the designated constraints
// aren't met.
type GetLoanDecisioningRequestV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequestV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequestV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequestV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequestV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequestV2ValidationError) ErrorName() string {
	return "GetLoanDecisioningRequestV2ValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequestV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequestV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequestV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequestV2ValidationError{}

// Validate checks the field values on OfferDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferDetailsMultiError, or
// nil if none found.
func (m *OfferDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EmiDueDate

	// no validation rules for GstPercentage

	// no validation rules for InterestPercentage

	// no validation rules for MaxAmount

	// no validation rules for MaxEmiAmount

	// no validation rules for MaxTenureInMonths

	// no validation rules for MinAmount

	// no validation rules for MinTenureInMonths

	// no validation rules for ProcessingFeePercentage

	// no validation rules for PricingScheme

	if len(errors) > 0 {
		return OfferDetailsMultiError(errors)
	}

	return nil
}

// OfferDetailsMultiError is an error wrapping multiple validation errors
// returned by OfferDetails.ValidateAll() if the designated constraints aren't met.
type OfferDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferDetailsMultiError) AllErrors() []error { return m }

// OfferDetailsValidationError is the validation error returned by
// OfferDetails.Validate if the designated constraints aren't met.
type OfferDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferDetailsValidationError) ErrorName() string { return "OfferDetailsValidationError" }

// Error satisfies the builtin error interface
func (e OfferDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferDetailsValidationError{}

// Validate checks the field values on Decision with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Decision) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Decision with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DecisionMultiError, or nil
// if none found.
func (m *Decision) ValidateAll() error {
	return m.validate(true)
}

func (m *Decision) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LendingProgram

	// no validation rules for Action

	// no validation rules for ValidTill

	// no validation rules for Bureau

	// no validation rules for ReportDate

	// no validation rules for ReportId

	// no validation rules for SchemeId

	// no validation rules for Strategy

	if all {
		switch v := interface{}(m.GetOfferDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DecisionValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DecisionValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DecisionValidationError{
				field:  "OfferDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DecisionMultiError(errors)
	}

	return nil
}

// DecisionMultiError is an error wrapping multiple validation errors returned
// by Decision.ValidateAll() if the designated constraints aren't met.
type DecisionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DecisionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DecisionMultiError) AllErrors() []error { return m }

// DecisionValidationError is the validation error returned by
// Decision.Validate if the designated constraints aren't met.
type DecisionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DecisionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DecisionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DecisionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DecisionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DecisionValidationError) ErrorName() string { return "DecisionValidationError" }

// Error satisfies the builtin error interface
func (e DecisionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDecision.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DecisionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DecisionValidationError{}

// Validate checks the field values on GetLoanDecisioningResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningResponseV2) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningResponseV2 with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningResponseV2MultiError, or nil if none found.
func (m *GetLoanDecisioningResponseV2) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningResponseV2) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for ApplicationId

	// no validation rules for EvaluationRequestTime

	// no validation rules for RequestId

	// no validation rules for Product

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseV2ValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrioritizedDecision()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "PrioritizedDecision",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "PrioritizedDecision",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrioritizedDecision()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseV2ValidationError{
				field:  "PrioritizedDecision",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDecision() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
						field:  fmt.Sprintf("Decision[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
						field:  fmt.Sprintf("Decision[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetLoanDecisioningResponseV2ValidationError{
					field:  fmt.Sprintf("Decision[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponseV2ValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponseV2ValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDecisioningResponseV2MultiError(errors)
	}

	return nil
}

// GetLoanDecisioningResponseV2MultiError is an error wrapping multiple
// validation errors returned by GetLoanDecisioningResponseV2.ValidateAll() if
// the designated constraints aren't met.
type GetLoanDecisioningResponseV2MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningResponseV2MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningResponseV2MultiError) AllErrors() []error { return m }

// GetLoanDecisioningResponseV2ValidationError is the validation error returned
// by GetLoanDecisioningResponseV2.Validate if the designated constraints
// aren't met.
type GetLoanDecisioningResponseV2ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningResponseV2ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningResponseV2ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningResponseV2ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningResponseV2ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningResponseV2ValidationError) ErrorName() string {
	return "GetLoanDecisioningResponseV2ValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningResponseV2ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningResponseV2.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningResponseV2ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningResponseV2ValidationError{}

// Validate checks the field values on Duration with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Duration) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Duration with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DurationMultiError, or nil
// if none found.
func (m *Duration) ValidateAll() error {
	return m.validate(true)
}

func (m *Duration) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Duration

	// no validation rules for DurationUnit

	if len(errors) > 0 {
		return DurationMultiError(errors)
	}

	return nil
}

// DurationMultiError is an error wrapping multiple validation errors returned
// by Duration.ValidateAll() if the designated constraints aren't met.
type DurationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DurationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DurationMultiError) AllErrors() []error { return m }

// DurationValidationError is the validation error returned by
// Duration.Validate if the designated constraints aren't met.
type DurationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DurationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DurationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DurationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DurationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DurationValidationError) ErrorName() string { return "DurationValidationError" }

// Error satisfies the builtin error interface
func (e DurationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDuration.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DurationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DurationValidationError{}

// Validate checks the field values on GetLoanDecisioningRequest_Values with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLoanDecisioningRequest_Values) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningRequest_Values with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningRequest_ValuesMultiError, or nil if none found.
func (m *GetLoanDecisioningRequest_Values) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequest_Values) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInput()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_ValuesValidationError{
					field:  "Input",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_ValuesValidationError{
					field:  "Input",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInput()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequest_ValuesValidationError{
				field:  "Input",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDecisioningRequest_ValuesMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequest_ValuesMultiError is an error wrapping multiple
// validation errors returned by
// GetLoanDecisioningRequest_Values.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDecisioningRequest_ValuesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequest_ValuesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequest_ValuesMultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequest_ValuesValidationError is the validation error
// returned by GetLoanDecisioningRequest_Values.Validate if the designated
// constraints aren't met.
type GetLoanDecisioningRequest_ValuesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequest_ValuesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequest_ValuesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequest_ValuesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequest_ValuesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequest_ValuesValidationError) ErrorName() string {
	return "GetLoanDecisioningRequest_ValuesValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequest_ValuesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequest_Values.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequest_ValuesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequest_ValuesValidationError{}

// Validate checks the field values on GetLoanDecisioningRequest_Values_Input
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLoanDecisioningRequest_Values_Input) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDecisioningRequest_Values_Input with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetLoanDecisioningRequest_Values_InputMultiError, or nil if none found.
func (m *GetLoanDecisioningRequest_Values_Input) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequest_Values_Input) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for Name

	// no validation rules for Gender

	// no validation rules for Pan

	// no validation rules for Dob

	// no validation rules for Address

	// no validation rules for Pincode

	// no validation rules for EmploymentType

	// no validation rules for EmployerName

	// no validation rules for WorkEmail

	// no validation rules for DeclaredIncome

	// no validation rules for SchemeId

	// no validation rules for BatchId

	// no validation rules for ClientId

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_Values_InputValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequest_Values_InputValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequest_Values_InputValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ApplicationId

	// no validation rules for LoanAmount

	if len(errors) > 0 {
		return GetLoanDecisioningRequest_Values_InputMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequest_Values_InputMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanDecisioningRequest_Values_Input.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDecisioningRequest_Values_InputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequest_Values_InputMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequest_Values_InputMultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequest_Values_InputValidationError is the validation
// error returned by GetLoanDecisioningRequest_Values_Input.Validate if the
// designated constraints aren't met.
type GetLoanDecisioningRequest_Values_InputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequest_Values_InputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequest_Values_InputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequest_Values_InputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequest_Values_InputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequest_Values_InputValidationError) ErrorName() string {
	return "GetLoanDecisioningRequest_Values_InputValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequest_Values_InputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequest_Values_Input.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequest_Values_InputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequest_Values_InputValidationError{}

// Validate checks the field values on
// GetLoanDecisioningRequest_Values_Input_PolicyParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningRequest_Values_Input_PolicyParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDecisioningRequest_Values_Input_PolicyParams with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetLoanDecisioningRequest_Values_Input_PolicyParamsMultiError, or nil if
// none found.
func (m *GetLoanDecisioningRequest_Values_Input_PolicyParams) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequest_Values_Input_PolicyParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PdScore

	// no validation rules for PricingScheme

	// no validation rules for EverVkycAttempted

	// no validation rules for PdScoreVersion

	// no validation rules for SchemeId

	// no validation rules for PricingSchemeBre

	// no validation rules for BatchId

	if len(errors) > 0 {
		return GetLoanDecisioningRequest_Values_Input_PolicyParamsMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequest_Values_Input_PolicyParamsMultiError is an error
// wrapping multiple validation errors returned by
// GetLoanDecisioningRequest_Values_Input_PolicyParams.ValidateAll() if the
// designated constraints aren't met.
type GetLoanDecisioningRequest_Values_Input_PolicyParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequest_Values_Input_PolicyParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequest_Values_Input_PolicyParamsMultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError is the
// validation error returned by
// GetLoanDecisioningRequest_Values_Input_PolicyParams.Validate if the
// designated constraints aren't met.
type GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError) ErrorName() string {
	return "GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequest_Values_Input_PolicyParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequest_Values_Input_PolicyParamsValidationError{}

// Validate checks the field values on GetLoanDecisioningResponse_Decision with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLoanDecisioningResponse_Decision) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningResponse_Decision
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningResponse_DecisionMultiError, or nil if none found.
func (m *GetLoanDecisioningResponse_Decision) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningResponse_Decision) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Action

	if all {
		switch v := interface{}(m.GetOfferDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_DecisionValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_DecisionValidationError{
					field:  "OfferDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponse_DecisionValidationError{
				field:  "OfferDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanProgram

	// no validation rules for SchemeId

	// no validation rules for CustId

	// no validation rules for ClientId

	// no validation rules for BatchId

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_DecisionValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningResponse_DecisionValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningResponse_DecisionValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDecisioningResponse_DecisionMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningResponse_DecisionMultiError is an error wrapping multiple
// validation errors returned by
// GetLoanDecisioningResponse_Decision.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDecisioningResponse_DecisionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningResponse_DecisionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningResponse_DecisionMultiError) AllErrors() []error { return m }

// GetLoanDecisioningResponse_DecisionValidationError is the validation error
// returned by GetLoanDecisioningResponse_Decision.Validate if the designated
// constraints aren't met.
type GetLoanDecisioningResponse_DecisionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningResponse_DecisionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningResponse_DecisionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningResponse_DecisionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningResponse_DecisionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningResponse_DecisionValidationError) ErrorName() string {
	return "GetLoanDecisioningResponse_DecisionValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningResponse_DecisionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningResponse_Decision.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningResponse_DecisionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningResponse_DecisionValidationError{}

// Validate checks the field values on
// GetLoanDecisioningResponse_Decision_OfferDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetLoanDecisioningResponse_Decision_OfferDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDecisioningResponse_Decision_OfferDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetLoanDecisioningResponse_Decision_OfferDetailsMultiError, or nil if none found.
func (m *GetLoanDecisioningResponse_Decision_OfferDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningResponse_Decision_OfferDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MinAmount

	// no validation rules for MaxAmount

	// no validation rules for MaxEmiAmount

	// no validation rules for InterestPercentage

	// no validation rules for ProcessingFeePercentage

	// no validation rules for MinTenureInMonths

	// no validation rules for MaxTenureInMonths

	// no validation rules for EmiDueDate

	// no validation rules for GstPercentage

	// no validation rules for ValidTillTimestamp

	if len(errors) > 0 {
		return GetLoanDecisioningResponse_Decision_OfferDetailsMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningResponse_Decision_OfferDetailsMultiError is an error
// wrapping multiple validation errors returned by
// GetLoanDecisioningResponse_Decision_OfferDetails.ValidateAll() if the
// designated constraints aren't met.
type GetLoanDecisioningResponse_Decision_OfferDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningResponse_Decision_OfferDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningResponse_Decision_OfferDetailsMultiError) AllErrors() []error { return m }

// GetLoanDecisioningResponse_Decision_OfferDetailsValidationError is the
// validation error returned by
// GetLoanDecisioningResponse_Decision_OfferDetails.Validate if the designated
// constraints aren't met.
type GetLoanDecisioningResponse_Decision_OfferDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningResponse_Decision_OfferDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetLoanDecisioningResponse_Decision_OfferDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetLoanDecisioningResponse_Decision_OfferDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetLoanDecisioningResponse_Decision_OfferDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningResponse_Decision_OfferDetailsValidationError) ErrorName() string {
	return "GetLoanDecisioningResponse_Decision_OfferDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningResponse_Decision_OfferDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningResponse_Decision_OfferDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningResponse_Decision_OfferDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningResponse_Decision_OfferDetailsValidationError{}

// Validate checks the field values on PolicyParams_Pre with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PolicyParams_Pre) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PolicyParams_Pre with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PolicyParams_PreMultiError, or nil if none found.
func (m *PolicyParams_Pre) ValidateAll() error {
	return m.validate(true)
}

func (m *PolicyParams_Pre) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PdScore

	// no validation rules for PdScoreVersion

	// no validation rules for SchemeId

	// no validation rules for BatchId

	// no validation rules for EverVkycAttempted

	// no validation rules for PricingScheme

	if len(errors) > 0 {
		return PolicyParams_PreMultiError(errors)
	}

	return nil
}

// PolicyParams_PreMultiError is an error wrapping multiple validation errors
// returned by PolicyParams_Pre.ValidateAll() if the designated constraints
// aren't met.
type PolicyParams_PreMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PolicyParams_PreMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PolicyParams_PreMultiError) AllErrors() []error { return m }

// PolicyParams_PreValidationError is the validation error returned by
// PolicyParams_Pre.Validate if the designated constraints aren't met.
type PolicyParams_PreValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PolicyParams_PreValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PolicyParams_PreValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PolicyParams_PreValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PolicyParams_PreValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PolicyParams_PreValidationError) ErrorName() string { return "PolicyParams_PreValidationError" }

// Error satisfies the builtin error interface
func (e PolicyParams_PreValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPolicyParams_Pre.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PolicyParams_PreValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PolicyParams_PreValidationError{}

// Validate checks the field values on PolicyParams_Final with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PolicyParams_Final) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PolicyParams_Final with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PolicyParams_FinalMultiError, or nil if none found.
func (m *PolicyParams_Final) ValidateAll() error {
	return m.validate(true)
}

func (m *PolicyParams_Final) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SchemeId

	// no validation rules for BatchId

	// no validation rules for PricingSchemeBre

	if len(errors) > 0 {
		return PolicyParams_FinalMultiError(errors)
	}

	return nil
}

// PolicyParams_FinalMultiError is an error wrapping multiple validation errors
// returned by PolicyParams_Final.ValidateAll() if the designated constraints
// aren't met.
type PolicyParams_FinalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PolicyParams_FinalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PolicyParams_FinalMultiError) AllErrors() []error { return m }

// PolicyParams_FinalValidationError is the validation error returned by
// PolicyParams_Final.Validate if the designated constraints aren't met.
type PolicyParams_FinalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PolicyParams_FinalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PolicyParams_FinalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PolicyParams_FinalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PolicyParams_FinalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PolicyParams_FinalValidationError) ErrorName() string {
	return "PolicyParams_FinalValidationError"
}

// Error satisfies the builtin error interface
func (e PolicyParams_FinalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPolicyParams_Final.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PolicyParams_FinalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PolicyParams_FinalValidationError{}

// Validate checks the field values on DataInfo_AaData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DataInfo_AaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DataInfo_AaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DataInfo_AaDataMultiError, or nil if none found.
func (m *DataInfo_AaData) ValidateAll() error {
	return m.validate(true)
}

func (m *DataInfo_AaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MedianAmountSalaryLast_180Days

	if len(errors) > 0 {
		return DataInfo_AaDataMultiError(errors)
	}

	return nil
}

// DataInfo_AaDataMultiError is an error wrapping multiple validation errors
// returned by DataInfo_AaData.ValidateAll() if the designated constraints
// aren't met.
type DataInfo_AaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DataInfo_AaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DataInfo_AaDataMultiError) AllErrors() []error { return m }

// DataInfo_AaDataValidationError is the validation error returned by
// DataInfo_AaData.Validate if the designated constraints aren't met.
type DataInfo_AaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DataInfo_AaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DataInfo_AaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DataInfo_AaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DataInfo_AaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DataInfo_AaDataValidationError) ErrorName() string { return "DataInfo_AaDataValidationError" }

// Error satisfies the builtin error interface
func (e DataInfo_AaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDataInfo_AaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DataInfo_AaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DataInfo_AaDataValidationError{}

// Validate checks the field values on
// ProductSpecificDataInfo_InsurancePolicyDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ProductSpecificDataInfo_InsurancePolicyDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ProductSpecificDataInfo_InsurancePolicyDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ProductSpecificDataInfo_InsurancePolicyDetailsMultiError, or nil if none found.
func (m *ProductSpecificDataInfo_InsurancePolicyDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ProductSpecificDataInfo_InsurancePolicyDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PolicyType

	// no validation rules for PolicyTenureInMonths

	// no validation rules for PolicyReferenceId

	// no validation rules for PolicyPremiumAmount

	if len(errors) > 0 {
		return ProductSpecificDataInfo_InsurancePolicyDetailsMultiError(errors)
	}

	return nil
}

// ProductSpecificDataInfo_InsurancePolicyDetailsMultiError is an error
// wrapping multiple validation errors returned by
// ProductSpecificDataInfo_InsurancePolicyDetails.ValidateAll() if the
// designated constraints aren't met.
type ProductSpecificDataInfo_InsurancePolicyDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProductSpecificDataInfo_InsurancePolicyDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProductSpecificDataInfo_InsurancePolicyDetailsMultiError) AllErrors() []error { return m }

// ProductSpecificDataInfo_InsurancePolicyDetailsValidationError is the
// validation error returned by
// ProductSpecificDataInfo_InsurancePolicyDetails.Validate if the designated
// constraints aren't met.
type ProductSpecificDataInfo_InsurancePolicyDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProductSpecificDataInfo_InsurancePolicyDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProductSpecificDataInfo_InsurancePolicyDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ProductSpecificDataInfo_InsurancePolicyDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProductSpecificDataInfo_InsurancePolicyDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProductSpecificDataInfo_InsurancePolicyDetailsValidationError) ErrorName() string {
	return "ProductSpecificDataInfo_InsurancePolicyDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ProductSpecificDataInfo_InsurancePolicyDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProductSpecificDataInfo_InsurancePolicyDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProductSpecificDataInfo_InsurancePolicyDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProductSpecificDataInfo_InsurancePolicyDetailsValidationError{}

// Validate checks the field values on CustomerDetails_RequestedLoanDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CustomerDetails_RequestedLoanDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerDetails_RequestedLoanDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CustomerDetails_RequestedLoanDetailsMultiError, or nil if none found.
func (m *CustomerDetails_RequestedLoanDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerDetails_RequestedLoanDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DesiredLoanAmount

	if all {
		switch v := interface{}(m.GetTenure()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CustomerDetails_RequestedLoanDetailsValidationError{
					field:  "Tenure",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTenure()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CustomerDetails_RequestedLoanDetailsValidationError{
				field:  "Tenure",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Roi

	// no validation rules for PfPlusGst

	// no validation rules for UpfrontPaymentAmount

	if len(errors) > 0 {
		return CustomerDetails_RequestedLoanDetailsMultiError(errors)
	}

	return nil
}

// CustomerDetails_RequestedLoanDetailsMultiError is an error wrapping multiple
// validation errors returned by
// CustomerDetails_RequestedLoanDetails.ValidateAll() if the designated
// constraints aren't met.
type CustomerDetails_RequestedLoanDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerDetails_RequestedLoanDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerDetails_RequestedLoanDetailsMultiError) AllErrors() []error { return m }

// CustomerDetails_RequestedLoanDetailsValidationError is the validation error
// returned by CustomerDetails_RequestedLoanDetails.Validate if the designated
// constraints aren't met.
type CustomerDetails_RequestedLoanDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerDetails_RequestedLoanDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerDetails_RequestedLoanDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerDetails_RequestedLoanDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerDetails_RequestedLoanDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerDetails_RequestedLoanDetailsValidationError) ErrorName() string {
	return "CustomerDetails_RequestedLoanDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerDetails_RequestedLoanDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerDetails_RequestedLoanDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerDetails_RequestedLoanDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerDetails_RequestedLoanDetailsValidationError{}

// Validate checks the field values on CustomerDetails_KycDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CustomerDetails_KycDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CustomerDetails_KycDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CustomerDetails_KycDetailsMultiError, or nil if none found.
func (m *CustomerDetails_KycDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CustomerDetails_KycDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AddressPincode

	if len(errors) > 0 {
		return CustomerDetails_KycDetailsMultiError(errors)
	}

	return nil
}

// CustomerDetails_KycDetailsMultiError is an error wrapping multiple
// validation errors returned by CustomerDetails_KycDetails.ValidateAll() if
// the designated constraints aren't met.
type CustomerDetails_KycDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomerDetails_KycDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomerDetails_KycDetailsMultiError) AllErrors() []error { return m }

// CustomerDetails_KycDetailsValidationError is the validation error returned
// by CustomerDetails_KycDetails.Validate if the designated constraints aren't met.
type CustomerDetails_KycDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomerDetails_KycDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomerDetails_KycDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomerDetails_KycDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomerDetails_KycDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomerDetails_KycDetailsValidationError) ErrorName() string {
	return "CustomerDetails_KycDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CustomerDetails_KycDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomerDetails_KycDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomerDetails_KycDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomerDetails_KycDetailsValidationError{}

// Validate checks the field values on GetLoanDecisioningRequestV2_Values with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetLoanDecisioningRequestV2_Values) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDecisioningRequestV2_Values
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetLoanDecisioningRequestV2_ValuesMultiError, or nil if none found.
func (m *GetLoanDecisioningRequestV2_Values) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequestV2_Values) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetInput()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2_ValuesValidationError{
					field:  "Input",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2_ValuesValidationError{
					field:  "Input",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInput()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestV2_ValuesValidationError{
				field:  "Input",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDecisioningRequestV2_ValuesMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequestV2_ValuesMultiError is an error wrapping multiple
// validation errors returned by
// GetLoanDecisioningRequestV2_Values.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDecisioningRequestV2_ValuesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequestV2_ValuesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequestV2_ValuesMultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequestV2_ValuesValidationError is the validation error
// returned by GetLoanDecisioningRequestV2_Values.Validate if the designated
// constraints aren't met.
type GetLoanDecisioningRequestV2_ValuesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequestV2_ValuesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequestV2_ValuesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequestV2_ValuesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequestV2_ValuesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequestV2_ValuesValidationError) ErrorName() string {
	return "GetLoanDecisioningRequestV2_ValuesValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequestV2_ValuesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequestV2_Values.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequestV2_ValuesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequestV2_ValuesValidationError{}

// Validate checks the field values on GetLoanDecisioningRequestV2_Values_Input
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetLoanDecisioningRequestV2_Values_Input) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetLoanDecisioningRequestV2_Values_Input with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetLoanDecisioningRequestV2_Values_InputMultiError, or nil if none found.
func (m *GetLoanDecisioningRequestV2_Values_Input) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDecisioningRequestV2_Values_Input) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for ApplicationId

	// no validation rules for LoanAmount

	// no validation rules for EvaluationRequestTime

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetCustomerDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2_Values_InputValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2_Values_InputValidationError{
					field:  "CustomerDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCustomerDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestV2_Values_InputValidationError{
				field:  "CustomerDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Product

	if all {
		switch v := interface{}(m.GetPolicyParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2_Values_InputValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDecisioningRequestV2_Values_InputValidationError{
					field:  "PolicyParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPolicyParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDecisioningRequestV2_Values_InputValidationError{
				field:  "PolicyParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Bureau

	// no validation rules for ClientId

	if len(errors) > 0 {
		return GetLoanDecisioningRequestV2_Values_InputMultiError(errors)
	}

	return nil
}

// GetLoanDecisioningRequestV2_Values_InputMultiError is an error wrapping
// multiple validation errors returned by
// GetLoanDecisioningRequestV2_Values_Input.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDecisioningRequestV2_Values_InputMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDecisioningRequestV2_Values_InputMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDecisioningRequestV2_Values_InputMultiError) AllErrors() []error { return m }

// GetLoanDecisioningRequestV2_Values_InputValidationError is the validation
// error returned by GetLoanDecisioningRequestV2_Values_Input.Validate if the
// designated constraints aren't met.
type GetLoanDecisioningRequestV2_Values_InputValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDecisioningRequestV2_Values_InputValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDecisioningRequestV2_Values_InputValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDecisioningRequestV2_Values_InputValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDecisioningRequestV2_Values_InputValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDecisioningRequestV2_Values_InputValidationError) ErrorName() string {
	return "GetLoanDecisioningRequestV2_Values_InputValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDecisioningRequestV2_Values_InputValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDecisioningRequestV2_Values_Input.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDecisioningRequestV2_Values_InputValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDecisioningRequestV2_Values_InputValidationError{}
