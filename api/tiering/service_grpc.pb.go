// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/tiering/service.proto

package tiering

import (
	context "context"
	sherlock_banners "github.com/epifi/gamma/api/cx/sherlock_banners"
	dynamic_elements "github.com/epifi/gamma/api/dynamic_elements"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Tiering_GetTierAtTime_FullMethodName                           = "/tiering.Tiering/GetTierAtTime"
	Tiering_Upgrade_FullMethodName                                 = "/tiering.Tiering/Upgrade"
	Tiering_ShowComponentToActor_FullMethodName                    = "/tiering.Tiering/ShowComponentToActor"
	Tiering_RecordComponentShownToActor_FullMethodName             = "/tiering.Tiering/RecordComponentShownToActor"
	Tiering_GetTieringPitchV2_FullMethodName                       = "/tiering.Tiering/GetTieringPitchV2"
	Tiering_GetDetailsForCx_FullMethodName                         = "/tiering.Tiering/GetDetailsForCx"
	Tiering_OverrideGracePeriod_FullMethodName                     = "/tiering.Tiering/OverrideGracePeriod"
	Tiering_OverrideCoolOffPeriod_FullMethodName                   = "/tiering.Tiering/OverrideCoolOffPeriod"
	Tiering_IsTieringEnabledForActor_FullMethodName                = "/tiering.Tiering/IsTieringEnabledForActor"
	Tiering_GetTieringPitch_FullMethodName                         = "/tiering.Tiering/GetTieringPitch"
	Tiering_GetCriteriaForActor_FullMethodName                     = "/tiering.Tiering/GetCriteriaForActor"
	Tiering_IsActorEligibleForMovement_FullMethodName              = "/tiering.Tiering/IsActorEligibleForMovement"
	Tiering_IsUserInGracePeriod_FullMethodName                     = "/tiering.Tiering/IsUserInGracePeriod"
	Tiering_FetchDynamicElements_FullMethodName                    = "/tiering.Tiering/FetchDynamicElements"
	Tiering_DynamicElementCallback_FullMethodName                  = "/tiering.Tiering/DynamicElementCallback"
	Tiering_GetConfigParams_FullMethodName                         = "/tiering.Tiering/GetConfigParams"
	Tiering_EvaluateTierForActor_FullMethodName                    = "/tiering.Tiering/EvaluateTierForActor"
	Tiering_CheckIfActorIsEligibleForCashbackReward_FullMethodName = "/tiering.Tiering/CheckIfActorIsEligibleForCashbackReward"
	Tiering_GetTierTimeRangesForActor_FullMethodName               = "/tiering.Tiering/GetTierTimeRangesForActor"
	Tiering_GetActorScreenInteractionDetails_FullMethodName        = "/tiering.Tiering/GetActorScreenInteractionDetails"
	Tiering_GetActorDistinctTiers_FullMethodName                   = "/tiering.Tiering/GetActorDistinctTiers"
	Tiering_IsUserEligibleForRewards_FullMethodName                = "/tiering.Tiering/IsUserEligibleForRewards"
	Tiering_GetAMBInfo_FullMethodName                              = "/tiering.Tiering/GetAMBInfo"
	Tiering_FetchSherlockBanners_FullMethodName                    = "/tiering.Tiering/FetchSherlockBanners"
)

// TieringClient is the client API for Tiering service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TieringClient interface {
	// Get tier of a user or before a particular time
	// In case tiering is disabled for a user the rpc returns status code as DISABLED
	GetTierAtTime(ctx context.Context, in *GetTierAtTimeRequest, opts ...grpc.CallOption) (*GetTierAtTimeResponse, error)
	// RPC to upgrade manually to a tier in case user was eligible
	Upgrade(ctx context.Context, in *UpgradeRequest, opts ...grpc.CallOption) (*UpgradeResponse, error)
	// RPC to determine whether a component should be shown for a user or not
	// If required to show the component, it is recorded first that the component is being shown to them
	ShowComponentToActor(ctx context.Context, in *ShowComponentToActorRequest, opts ...grpc.CallOption) (*ShowComponentToActorResponse, error)
	// RPC to record component shown for a user
	// INTERNAL in case of any server error
	RecordComponentShownToActor(ctx context.Context, in *RecordComponentShownToActorRequest, opts ...grpc.CallOption) (*RecordComponentShownToActorResponse, error)
	// RPC to get all probable movements and their details from the actors current tier, to higher tiers
	// If actor is cooloff, it sends details required for retaining the actor in that tier
	// Each movement detail consists of a set of options and satisfying one of them will lead to movement to that particular tier
	// Each option consists of a set of actions and satisfying all of them will lead to an option being met
	GetTieringPitchV2(ctx context.Context, in *GetTieringPitchV2Request, opts ...grpc.CallOption) (*GetTieringPitchV2Response, error)
	// GetDetailsForCx fetches details required for Cx service to power sherlock agent view
	// INTERNAL in case of any server error
	GetDetailsForCx(ctx context.Context, in *GetDetailsForCxRequest, opts ...grpc.CallOption) (*GetDetailsForCxResponse, error)
	// OverrideGracePeriod overrides users grace period with the given timestamp
	// INTERNAL in case of any server error
	OverrideGracePeriod(ctx context.Context, in *OverrideGracePeriodRequest, opts ...grpc.CallOption) (*OverrideGracePeriodResponse, error)
	// OverrideCoolOffPeriod overrides users cool off period with the given timestamp
	// INTERNAL in case of any server error
	OverrideCoolOffPeriod(ctx context.Context, in *OverrideCoolOffPeriodRequest, opts ...grpc.CallOption) (*OverrideCoolOffPeriodResponse, error)
	// Deprecated: Do not use.
	// Returns whether tiering is enabled for an actor or not
	// INTERNAL in case of any server error
	IsTieringEnabledForActor(ctx context.Context, in *IsTieringEnabledForActorRequest, opts ...grpc.CallOption) (*IsTieringEnabledForActorResponse, error)
	// Deprecated: Do not use.
	// Return whether tiering should be pitched to the actor or not and all the associated details with regards to it
	// Pitch is with respect to a particular flow
	// INTERNAL in case of any server error
	// Deprecated in favour of GetTieringPitchV2
	GetTieringPitch(ctx context.Context, in *GetTieringPitchRequest, opts ...grpc.CallOption) (*GetTieringPitchResponse, error)
	// Deprecated: Do not use.
	// Get criteria for actor that got referenced in eligible tier movement
	// If no eligible tier movement for actor is present then it will return current active criteria
	// INTERNAL in case of any server error
	// Deprecated in favour of GetTieringPitchV2
	GetCriteriaForActor(ctx context.Context, in *GetCriteriaForActorRequest, opts ...grpc.CallOption) (*GetCriteriaForActorResponse, error)
	// Deprecated: Do not use.
	// IsActorEligibleForMovement checks whether for an actor if given movement type is eligible or not
	// INVALID_ARGUMENT if movement type is not handled
	// INTERNAL in case of any server error
	// Deprecated in favour of GetTieringPitchV2
	IsActorEligibleForMovement(ctx context.Context, in *IsActorEligibleForMovementRequest, opts ...grpc.CallOption) (*IsActorEligibleForMovementResponse, error)
	// Deprecated: Do not use.
	// IsUserInGracePeriod checks whether user in grace period or not
	// INTERNAL in case of any server error
	// Deprecated in favour of GetTieringPitchV2
	IsUserInGracePeriod(ctx context.Context, in *IsUserInGracePeriodRequest, opts ...grpc.CallOption) (*IsUserInGracePeriodResponse, error)
	// RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
	// ActorId is a mandatory parameter in the Request
	// Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no elements found for the given user on this screen
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the list of targeted comms elements is fetched successfully
	FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
	DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error)
	// RPC to fetch tiering backend config
	GetConfigParams(ctx context.Context, in *GetConfigParamsRequest, opts ...grpc.CallOption) (*GetConfigParamsResponse, error)
	// RPC to evaluate tier with the current active criteria for actor
	// INTERNAL in case of any server error
	EvaluateTierForActor(ctx context.Context, in *EvaluateTierForActorRequest, opts ...grpc.CallOption) (*EvaluateTierForActorResponse, error)
	// RPC to check if the actor is eligible for X(2%) cashback reward
	// checks are on various parameters like for example ABWII(Average balance while in infinite) is above certain threshold etc
	// Some internal details:
	//  If tier itself is not eligible for cashback reward this RPC will return false
	//  If any tier is always eligible for cashback reward(eg: SALARY) this RPC will return true without any other checks
	// INTERNAL in case of any server error
	CheckIfActorIsEligibleForCashbackReward(ctx context.Context, in *CheckIfActorIsEligibleForCashbackRewardRequest, opts ...grpc.CallOption) (*CheckIfActorIsEligibleForCashbackRewardResponse, error)
	// GetTierTimeRangesForActor returns the list of Time Ranges when the user was in a particular tier
	GetTierTimeRangesForActor(ctx context.Context, in *GetTierTimeRangesForActorRequest, opts ...grpc.CallOption) (*GetTierTimeRangesForActorResponse, error)
	// GetActorScreenInteractionDetails return the details of the actor's interaction with the screen
	GetActorScreenInteractionDetails(ctx context.Context, in *GetActorScreenInteractionDetailsRequest, opts ...grpc.CallOption) (*GetActorScreenInteractionDetailsResponse, error)
	// GetActorDistintTiers returns the list of tiers user has been in
	GetActorDistinctTiers(ctx context.Context, in *GetActorDistinctTiersRequest, opts ...grpc.CallOption) (*GetActorDistinctTiersResponse, error)
	// IsUserIneligibleForRewards checks if a user is ineligible for rewards
	// based on abuser segments on a given date
	// To be used only for CX agent purposes
	IsUserEligibleForRewards(ctx context.Context, in *IsUserEligibleForRewardsRequest, opts ...grpc.CallOption) (*IsUserEligibleForRewardsResponse, error)
	// GetAMBInfo returns the current (projected) AMB and target AMB for a user
	// INTERNAL in case of any server error
	GetAMBInfo(ctx context.Context, in *GetAMBInfoRequest, opts ...grpc.CallOption) (*GetAMBInfoResponse, error)
	// FetchSherlockBanners returns banners to show for AMB charges communication
	// INTERNAL in case of any server error
	FetchSherlockBanners(ctx context.Context, in *sherlock_banners.FetchSherlockBannersRequest, opts ...grpc.CallOption) (*sherlock_banners.FetchSherlockBannersResponse, error)
}

type tieringClient struct {
	cc grpc.ClientConnInterface
}

func NewTieringClient(cc grpc.ClientConnInterface) TieringClient {
	return &tieringClient{cc}
}

func (c *tieringClient) GetTierAtTime(ctx context.Context, in *GetTierAtTimeRequest, opts ...grpc.CallOption) (*GetTierAtTimeResponse, error) {
	out := new(GetTierAtTimeResponse)
	err := c.cc.Invoke(ctx, Tiering_GetTierAtTime_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) Upgrade(ctx context.Context, in *UpgradeRequest, opts ...grpc.CallOption) (*UpgradeResponse, error) {
	out := new(UpgradeResponse)
	err := c.cc.Invoke(ctx, Tiering_Upgrade_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) ShowComponentToActor(ctx context.Context, in *ShowComponentToActorRequest, opts ...grpc.CallOption) (*ShowComponentToActorResponse, error) {
	out := new(ShowComponentToActorResponse)
	err := c.cc.Invoke(ctx, Tiering_ShowComponentToActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) RecordComponentShownToActor(ctx context.Context, in *RecordComponentShownToActorRequest, opts ...grpc.CallOption) (*RecordComponentShownToActorResponse, error) {
	out := new(RecordComponentShownToActorResponse)
	err := c.cc.Invoke(ctx, Tiering_RecordComponentShownToActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) GetTieringPitchV2(ctx context.Context, in *GetTieringPitchV2Request, opts ...grpc.CallOption) (*GetTieringPitchV2Response, error) {
	out := new(GetTieringPitchV2Response)
	err := c.cc.Invoke(ctx, Tiering_GetTieringPitchV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) GetDetailsForCx(ctx context.Context, in *GetDetailsForCxRequest, opts ...grpc.CallOption) (*GetDetailsForCxResponse, error) {
	out := new(GetDetailsForCxResponse)
	err := c.cc.Invoke(ctx, Tiering_GetDetailsForCx_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) OverrideGracePeriod(ctx context.Context, in *OverrideGracePeriodRequest, opts ...grpc.CallOption) (*OverrideGracePeriodResponse, error) {
	out := new(OverrideGracePeriodResponse)
	err := c.cc.Invoke(ctx, Tiering_OverrideGracePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) OverrideCoolOffPeriod(ctx context.Context, in *OverrideCoolOffPeriodRequest, opts ...grpc.CallOption) (*OverrideCoolOffPeriodResponse, error) {
	out := new(OverrideCoolOffPeriodResponse)
	err := c.cc.Invoke(ctx, Tiering_OverrideCoolOffPeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *tieringClient) IsTieringEnabledForActor(ctx context.Context, in *IsTieringEnabledForActorRequest, opts ...grpc.CallOption) (*IsTieringEnabledForActorResponse, error) {
	out := new(IsTieringEnabledForActorResponse)
	err := c.cc.Invoke(ctx, Tiering_IsTieringEnabledForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *tieringClient) GetTieringPitch(ctx context.Context, in *GetTieringPitchRequest, opts ...grpc.CallOption) (*GetTieringPitchResponse, error) {
	out := new(GetTieringPitchResponse)
	err := c.cc.Invoke(ctx, Tiering_GetTieringPitch_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *tieringClient) GetCriteriaForActor(ctx context.Context, in *GetCriteriaForActorRequest, opts ...grpc.CallOption) (*GetCriteriaForActorResponse, error) {
	out := new(GetCriteriaForActorResponse)
	err := c.cc.Invoke(ctx, Tiering_GetCriteriaForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *tieringClient) IsActorEligibleForMovement(ctx context.Context, in *IsActorEligibleForMovementRequest, opts ...grpc.CallOption) (*IsActorEligibleForMovementResponse, error) {
	out := new(IsActorEligibleForMovementResponse)
	err := c.cc.Invoke(ctx, Tiering_IsActorEligibleForMovement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *tieringClient) IsUserInGracePeriod(ctx context.Context, in *IsUserInGracePeriodRequest, opts ...grpc.CallOption) (*IsUserInGracePeriodResponse, error) {
	out := new(IsUserInGracePeriodResponse)
	err := c.cc.Invoke(ctx, Tiering_IsUserInGracePeriod_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) FetchDynamicElements(ctx context.Context, in *dynamic_elements.FetchDynamicElementsRequest, opts ...grpc.CallOption) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	out := new(dynamic_elements.FetchDynamicElementsResponse)
	err := c.cc.Invoke(ctx, Tiering_FetchDynamicElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) DynamicElementCallback(ctx context.Context, in *dynamic_elements.DynamicElementCallbackRequest, opts ...grpc.CallOption) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	out := new(dynamic_elements.DynamicElementCallbackResponse)
	err := c.cc.Invoke(ctx, Tiering_DynamicElementCallback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) GetConfigParams(ctx context.Context, in *GetConfigParamsRequest, opts ...grpc.CallOption) (*GetConfigParamsResponse, error) {
	out := new(GetConfigParamsResponse)
	err := c.cc.Invoke(ctx, Tiering_GetConfigParams_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) EvaluateTierForActor(ctx context.Context, in *EvaluateTierForActorRequest, opts ...grpc.CallOption) (*EvaluateTierForActorResponse, error) {
	out := new(EvaluateTierForActorResponse)
	err := c.cc.Invoke(ctx, Tiering_EvaluateTierForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) CheckIfActorIsEligibleForCashbackReward(ctx context.Context, in *CheckIfActorIsEligibleForCashbackRewardRequest, opts ...grpc.CallOption) (*CheckIfActorIsEligibleForCashbackRewardResponse, error) {
	out := new(CheckIfActorIsEligibleForCashbackRewardResponse)
	err := c.cc.Invoke(ctx, Tiering_CheckIfActorIsEligibleForCashbackReward_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) GetTierTimeRangesForActor(ctx context.Context, in *GetTierTimeRangesForActorRequest, opts ...grpc.CallOption) (*GetTierTimeRangesForActorResponse, error) {
	out := new(GetTierTimeRangesForActorResponse)
	err := c.cc.Invoke(ctx, Tiering_GetTierTimeRangesForActor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) GetActorScreenInteractionDetails(ctx context.Context, in *GetActorScreenInteractionDetailsRequest, opts ...grpc.CallOption) (*GetActorScreenInteractionDetailsResponse, error) {
	out := new(GetActorScreenInteractionDetailsResponse)
	err := c.cc.Invoke(ctx, Tiering_GetActorScreenInteractionDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) GetActorDistinctTiers(ctx context.Context, in *GetActorDistinctTiersRequest, opts ...grpc.CallOption) (*GetActorDistinctTiersResponse, error) {
	out := new(GetActorDistinctTiersResponse)
	err := c.cc.Invoke(ctx, Tiering_GetActorDistinctTiers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) IsUserEligibleForRewards(ctx context.Context, in *IsUserEligibleForRewardsRequest, opts ...grpc.CallOption) (*IsUserEligibleForRewardsResponse, error) {
	out := new(IsUserEligibleForRewardsResponse)
	err := c.cc.Invoke(ctx, Tiering_IsUserEligibleForRewards_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) GetAMBInfo(ctx context.Context, in *GetAMBInfoRequest, opts ...grpc.CallOption) (*GetAMBInfoResponse, error) {
	out := new(GetAMBInfoResponse)
	err := c.cc.Invoke(ctx, Tiering_GetAMBInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tieringClient) FetchSherlockBanners(ctx context.Context, in *sherlock_banners.FetchSherlockBannersRequest, opts ...grpc.CallOption) (*sherlock_banners.FetchSherlockBannersResponse, error) {
	out := new(sherlock_banners.FetchSherlockBannersResponse)
	err := c.cc.Invoke(ctx, Tiering_FetchSherlockBanners_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TieringServer is the server API for Tiering service.
// All implementations should embed UnimplementedTieringServer
// for forward compatibility
type TieringServer interface {
	// Get tier of a user or before a particular time
	// In case tiering is disabled for a user the rpc returns status code as DISABLED
	GetTierAtTime(context.Context, *GetTierAtTimeRequest) (*GetTierAtTimeResponse, error)
	// RPC to upgrade manually to a tier in case user was eligible
	Upgrade(context.Context, *UpgradeRequest) (*UpgradeResponse, error)
	// RPC to determine whether a component should be shown for a user or not
	// If required to show the component, it is recorded first that the component is being shown to them
	ShowComponentToActor(context.Context, *ShowComponentToActorRequest) (*ShowComponentToActorResponse, error)
	// RPC to record component shown for a user
	// INTERNAL in case of any server error
	RecordComponentShownToActor(context.Context, *RecordComponentShownToActorRequest) (*RecordComponentShownToActorResponse, error)
	// RPC to get all probable movements and their details from the actors current tier, to higher tiers
	// If actor is cooloff, it sends details required for retaining the actor in that tier
	// Each movement detail consists of a set of options and satisfying one of them will lead to movement to that particular tier
	// Each option consists of a set of actions and satisfying all of them will lead to an option being met
	GetTieringPitchV2(context.Context, *GetTieringPitchV2Request) (*GetTieringPitchV2Response, error)
	// GetDetailsForCx fetches details required for Cx service to power sherlock agent view
	// INTERNAL in case of any server error
	GetDetailsForCx(context.Context, *GetDetailsForCxRequest) (*GetDetailsForCxResponse, error)
	// OverrideGracePeriod overrides users grace period with the given timestamp
	// INTERNAL in case of any server error
	OverrideGracePeriod(context.Context, *OverrideGracePeriodRequest) (*OverrideGracePeriodResponse, error)
	// OverrideCoolOffPeriod overrides users cool off period with the given timestamp
	// INTERNAL in case of any server error
	OverrideCoolOffPeriod(context.Context, *OverrideCoolOffPeriodRequest) (*OverrideCoolOffPeriodResponse, error)
	// Deprecated: Do not use.
	// Returns whether tiering is enabled for an actor or not
	// INTERNAL in case of any server error
	IsTieringEnabledForActor(context.Context, *IsTieringEnabledForActorRequest) (*IsTieringEnabledForActorResponse, error)
	// Deprecated: Do not use.
	// Return whether tiering should be pitched to the actor or not and all the associated details with regards to it
	// Pitch is with respect to a particular flow
	// INTERNAL in case of any server error
	// Deprecated in favour of GetTieringPitchV2
	GetTieringPitch(context.Context, *GetTieringPitchRequest) (*GetTieringPitchResponse, error)
	// Deprecated: Do not use.
	// Get criteria for actor that got referenced in eligible tier movement
	// If no eligible tier movement for actor is present then it will return current active criteria
	// INTERNAL in case of any server error
	// Deprecated in favour of GetTieringPitchV2
	GetCriteriaForActor(context.Context, *GetCriteriaForActorRequest) (*GetCriteriaForActorResponse, error)
	// Deprecated: Do not use.
	// IsActorEligibleForMovement checks whether for an actor if given movement type is eligible or not
	// INVALID_ARGUMENT if movement type is not handled
	// INTERNAL in case of any server error
	// Deprecated in favour of GetTieringPitchV2
	IsActorEligibleForMovement(context.Context, *IsActorEligibleForMovementRequest) (*IsActorEligibleForMovementResponse, error)
	// Deprecated: Do not use.
	// IsUserInGracePeriod checks whether user in grace period or not
	// INTERNAL in case of any server error
	// Deprecated in favour of GetTieringPitchV2
	IsUserInGracePeriod(context.Context, *IsUserInGracePeriodRequest) (*IsUserInGracePeriodResponse, error)
	// RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
	// ActorId is a mandatory parameter in the Request
	// Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
	// INVALID ARGUMENT if any mandatory param is missing
	// RECORD NOT FOUND if no elements found for the given user on this screen
	// INTERNAL SERVER ERROR if any error in processing
	// OK if the list of targeted comms elements is fetched successfully
	FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error)
	// DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
	DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error)
	// RPC to fetch tiering backend config
	GetConfigParams(context.Context, *GetConfigParamsRequest) (*GetConfigParamsResponse, error)
	// RPC to evaluate tier with the current active criteria for actor
	// INTERNAL in case of any server error
	EvaluateTierForActor(context.Context, *EvaluateTierForActorRequest) (*EvaluateTierForActorResponse, error)
	// RPC to check if the actor is eligible for X(2%) cashback reward
	// checks are on various parameters like for example ABWII(Average balance while in infinite) is above certain threshold etc
	// Some internal details:
	//  If tier itself is not eligible for cashback reward this RPC will return false
	//  If any tier is always eligible for cashback reward(eg: SALARY) this RPC will return true without any other checks
	// INTERNAL in case of any server error
	CheckIfActorIsEligibleForCashbackReward(context.Context, *CheckIfActorIsEligibleForCashbackRewardRequest) (*CheckIfActorIsEligibleForCashbackRewardResponse, error)
	// GetTierTimeRangesForActor returns the list of Time Ranges when the user was in a particular tier
	GetTierTimeRangesForActor(context.Context, *GetTierTimeRangesForActorRequest) (*GetTierTimeRangesForActorResponse, error)
	// GetActorScreenInteractionDetails return the details of the actor's interaction with the screen
	GetActorScreenInteractionDetails(context.Context, *GetActorScreenInteractionDetailsRequest) (*GetActorScreenInteractionDetailsResponse, error)
	// GetActorDistintTiers returns the list of tiers user has been in
	GetActorDistinctTiers(context.Context, *GetActorDistinctTiersRequest) (*GetActorDistinctTiersResponse, error)
	// IsUserIneligibleForRewards checks if a user is ineligible for rewards
	// based on abuser segments on a given date
	// To be used only for CX agent purposes
	IsUserEligibleForRewards(context.Context, *IsUserEligibleForRewardsRequest) (*IsUserEligibleForRewardsResponse, error)
	// GetAMBInfo returns the current (projected) AMB and target AMB for a user
	// INTERNAL in case of any server error
	GetAMBInfo(context.Context, *GetAMBInfoRequest) (*GetAMBInfoResponse, error)
	// FetchSherlockBanners returns banners to show for AMB charges communication
	// INTERNAL in case of any server error
	FetchSherlockBanners(context.Context, *sherlock_banners.FetchSherlockBannersRequest) (*sherlock_banners.FetchSherlockBannersResponse, error)
}

// UnimplementedTieringServer should be embedded to have forward compatible implementations.
type UnimplementedTieringServer struct {
}

func (UnimplementedTieringServer) GetTierAtTime(context.Context, *GetTierAtTimeRequest) (*GetTierAtTimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTierAtTime not implemented")
}
func (UnimplementedTieringServer) Upgrade(context.Context, *UpgradeRequest) (*UpgradeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Upgrade not implemented")
}
func (UnimplementedTieringServer) ShowComponentToActor(context.Context, *ShowComponentToActorRequest) (*ShowComponentToActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ShowComponentToActor not implemented")
}
func (UnimplementedTieringServer) RecordComponentShownToActor(context.Context, *RecordComponentShownToActorRequest) (*RecordComponentShownToActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecordComponentShownToActor not implemented")
}
func (UnimplementedTieringServer) GetTieringPitchV2(context.Context, *GetTieringPitchV2Request) (*GetTieringPitchV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTieringPitchV2 not implemented")
}
func (UnimplementedTieringServer) GetDetailsForCx(context.Context, *GetDetailsForCxRequest) (*GetDetailsForCxResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDetailsForCx not implemented")
}
func (UnimplementedTieringServer) OverrideGracePeriod(context.Context, *OverrideGracePeriodRequest) (*OverrideGracePeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OverrideGracePeriod not implemented")
}
func (UnimplementedTieringServer) OverrideCoolOffPeriod(context.Context, *OverrideCoolOffPeriodRequest) (*OverrideCoolOffPeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OverrideCoolOffPeriod not implemented")
}
func (UnimplementedTieringServer) IsTieringEnabledForActor(context.Context, *IsTieringEnabledForActorRequest) (*IsTieringEnabledForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsTieringEnabledForActor not implemented")
}
func (UnimplementedTieringServer) GetTieringPitch(context.Context, *GetTieringPitchRequest) (*GetTieringPitchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTieringPitch not implemented")
}
func (UnimplementedTieringServer) GetCriteriaForActor(context.Context, *GetCriteriaForActorRequest) (*GetCriteriaForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCriteriaForActor not implemented")
}
func (UnimplementedTieringServer) IsActorEligibleForMovement(context.Context, *IsActorEligibleForMovementRequest) (*IsActorEligibleForMovementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsActorEligibleForMovement not implemented")
}
func (UnimplementedTieringServer) IsUserInGracePeriod(context.Context, *IsUserInGracePeriodRequest) (*IsUserInGracePeriodResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsUserInGracePeriod not implemented")
}
func (UnimplementedTieringServer) FetchDynamicElements(context.Context, *dynamic_elements.FetchDynamicElementsRequest) (*dynamic_elements.FetchDynamicElementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchDynamicElements not implemented")
}
func (UnimplementedTieringServer) DynamicElementCallback(context.Context, *dynamic_elements.DynamicElementCallbackRequest) (*dynamic_elements.DynamicElementCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DynamicElementCallback not implemented")
}
func (UnimplementedTieringServer) GetConfigParams(context.Context, *GetConfigParamsRequest) (*GetConfigParamsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfigParams not implemented")
}
func (UnimplementedTieringServer) EvaluateTierForActor(context.Context, *EvaluateTierForActorRequest) (*EvaluateTierForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EvaluateTierForActor not implemented")
}
func (UnimplementedTieringServer) CheckIfActorIsEligibleForCashbackReward(context.Context, *CheckIfActorIsEligibleForCashbackRewardRequest) (*CheckIfActorIsEligibleForCashbackRewardResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckIfActorIsEligibleForCashbackReward not implemented")
}
func (UnimplementedTieringServer) GetTierTimeRangesForActor(context.Context, *GetTierTimeRangesForActorRequest) (*GetTierTimeRangesForActorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTierTimeRangesForActor not implemented")
}
func (UnimplementedTieringServer) GetActorScreenInteractionDetails(context.Context, *GetActorScreenInteractionDetailsRequest) (*GetActorScreenInteractionDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActorScreenInteractionDetails not implemented")
}
func (UnimplementedTieringServer) GetActorDistinctTiers(context.Context, *GetActorDistinctTiersRequest) (*GetActorDistinctTiersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActorDistinctTiers not implemented")
}
func (UnimplementedTieringServer) IsUserEligibleForRewards(context.Context, *IsUserEligibleForRewardsRequest) (*IsUserEligibleForRewardsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsUserEligibleForRewards not implemented")
}
func (UnimplementedTieringServer) GetAMBInfo(context.Context, *GetAMBInfoRequest) (*GetAMBInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAMBInfo not implemented")
}
func (UnimplementedTieringServer) FetchSherlockBanners(context.Context, *sherlock_banners.FetchSherlockBannersRequest) (*sherlock_banners.FetchSherlockBannersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchSherlockBanners not implemented")
}

// UnsafeTieringServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TieringServer will
// result in compilation errors.
type UnsafeTieringServer interface {
	mustEmbedUnimplementedTieringServer()
}

func RegisterTieringServer(s grpc.ServiceRegistrar, srv TieringServer) {
	s.RegisterService(&Tiering_ServiceDesc, srv)
}

func _Tiering_GetTierAtTime_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTierAtTimeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetTierAtTime(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetTierAtTime_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetTierAtTime(ctx, req.(*GetTierAtTimeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_Upgrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpgradeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).Upgrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_Upgrade_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).Upgrade(ctx, req.(*UpgradeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_ShowComponentToActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ShowComponentToActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).ShowComponentToActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_ShowComponentToActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).ShowComponentToActor(ctx, req.(*ShowComponentToActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_RecordComponentShownToActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecordComponentShownToActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).RecordComponentShownToActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_RecordComponentShownToActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).RecordComponentShownToActor(ctx, req.(*RecordComponentShownToActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_GetTieringPitchV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTieringPitchV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetTieringPitchV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetTieringPitchV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetTieringPitchV2(ctx, req.(*GetTieringPitchV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_GetDetailsForCx_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDetailsForCxRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetDetailsForCx(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetDetailsForCx_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetDetailsForCx(ctx, req.(*GetDetailsForCxRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_OverrideGracePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverrideGracePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).OverrideGracePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_OverrideGracePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).OverrideGracePeriod(ctx, req.(*OverrideGracePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_OverrideCoolOffPeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverrideCoolOffPeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).OverrideCoolOffPeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_OverrideCoolOffPeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).OverrideCoolOffPeriod(ctx, req.(*OverrideCoolOffPeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_IsTieringEnabledForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsTieringEnabledForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).IsTieringEnabledForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_IsTieringEnabledForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).IsTieringEnabledForActor(ctx, req.(*IsTieringEnabledForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_GetTieringPitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTieringPitchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetTieringPitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetTieringPitch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetTieringPitch(ctx, req.(*GetTieringPitchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_GetCriteriaForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCriteriaForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetCriteriaForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetCriteriaForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetCriteriaForActor(ctx, req.(*GetCriteriaForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_IsActorEligibleForMovement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsActorEligibleForMovementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).IsActorEligibleForMovement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_IsActorEligibleForMovement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).IsActorEligibleForMovement(ctx, req.(*IsActorEligibleForMovementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_IsUserInGracePeriod_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUserInGracePeriodRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).IsUserInGracePeriod(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_IsUserInGracePeriod_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).IsUserInGracePeriod(ctx, req.(*IsUserInGracePeriodRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_FetchDynamicElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.FetchDynamicElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).FetchDynamicElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_FetchDynamicElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).FetchDynamicElements(ctx, req.(*dynamic_elements.FetchDynamicElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_DynamicElementCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dynamic_elements.DynamicElementCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).DynamicElementCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_DynamicElementCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).DynamicElementCallback(ctx, req.(*dynamic_elements.DynamicElementCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_GetConfigParams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigParamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetConfigParams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetConfigParams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetConfigParams(ctx, req.(*GetConfigParamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_EvaluateTierForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EvaluateTierForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).EvaluateTierForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_EvaluateTierForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).EvaluateTierForActor(ctx, req.(*EvaluateTierForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_CheckIfActorIsEligibleForCashbackReward_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIfActorIsEligibleForCashbackRewardRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).CheckIfActorIsEligibleForCashbackReward(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_CheckIfActorIsEligibleForCashbackReward_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).CheckIfActorIsEligibleForCashbackReward(ctx, req.(*CheckIfActorIsEligibleForCashbackRewardRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_GetTierTimeRangesForActor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTierTimeRangesForActorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetTierTimeRangesForActor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetTierTimeRangesForActor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetTierTimeRangesForActor(ctx, req.(*GetTierTimeRangesForActorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_GetActorScreenInteractionDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActorScreenInteractionDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetActorScreenInteractionDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetActorScreenInteractionDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetActorScreenInteractionDetails(ctx, req.(*GetActorScreenInteractionDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_GetActorDistinctTiers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActorDistinctTiersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetActorDistinctTiers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetActorDistinctTiers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetActorDistinctTiers(ctx, req.(*GetActorDistinctTiersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_IsUserEligibleForRewards_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsUserEligibleForRewardsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).IsUserEligibleForRewards(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_IsUserEligibleForRewards_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).IsUserEligibleForRewards(ctx, req.(*IsUserEligibleForRewardsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_GetAMBInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAMBInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).GetAMBInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_GetAMBInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).GetAMBInfo(ctx, req.(*GetAMBInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Tiering_FetchSherlockBanners_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(sherlock_banners.FetchSherlockBannersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TieringServer).FetchSherlockBanners(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Tiering_FetchSherlockBanners_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TieringServer).FetchSherlockBanners(ctx, req.(*sherlock_banners.FetchSherlockBannersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Tiering_ServiceDesc is the grpc.ServiceDesc for Tiering service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Tiering_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "tiering.Tiering",
	HandlerType: (*TieringServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetTierAtTime",
			Handler:    _Tiering_GetTierAtTime_Handler,
		},
		{
			MethodName: "Upgrade",
			Handler:    _Tiering_Upgrade_Handler,
		},
		{
			MethodName: "ShowComponentToActor",
			Handler:    _Tiering_ShowComponentToActor_Handler,
		},
		{
			MethodName: "RecordComponentShownToActor",
			Handler:    _Tiering_RecordComponentShownToActor_Handler,
		},
		{
			MethodName: "GetTieringPitchV2",
			Handler:    _Tiering_GetTieringPitchV2_Handler,
		},
		{
			MethodName: "GetDetailsForCx",
			Handler:    _Tiering_GetDetailsForCx_Handler,
		},
		{
			MethodName: "OverrideGracePeriod",
			Handler:    _Tiering_OverrideGracePeriod_Handler,
		},
		{
			MethodName: "OverrideCoolOffPeriod",
			Handler:    _Tiering_OverrideCoolOffPeriod_Handler,
		},
		{
			MethodName: "IsTieringEnabledForActor",
			Handler:    _Tiering_IsTieringEnabledForActor_Handler,
		},
		{
			MethodName: "GetTieringPitch",
			Handler:    _Tiering_GetTieringPitch_Handler,
		},
		{
			MethodName: "GetCriteriaForActor",
			Handler:    _Tiering_GetCriteriaForActor_Handler,
		},
		{
			MethodName: "IsActorEligibleForMovement",
			Handler:    _Tiering_IsActorEligibleForMovement_Handler,
		},
		{
			MethodName: "IsUserInGracePeriod",
			Handler:    _Tiering_IsUserInGracePeriod_Handler,
		},
		{
			MethodName: "FetchDynamicElements",
			Handler:    _Tiering_FetchDynamicElements_Handler,
		},
		{
			MethodName: "DynamicElementCallback",
			Handler:    _Tiering_DynamicElementCallback_Handler,
		},
		{
			MethodName: "GetConfigParams",
			Handler:    _Tiering_GetConfigParams_Handler,
		},
		{
			MethodName: "EvaluateTierForActor",
			Handler:    _Tiering_EvaluateTierForActor_Handler,
		},
		{
			MethodName: "CheckIfActorIsEligibleForCashbackReward",
			Handler:    _Tiering_CheckIfActorIsEligibleForCashbackReward_Handler,
		},
		{
			MethodName: "GetTierTimeRangesForActor",
			Handler:    _Tiering_GetTierTimeRangesForActor_Handler,
		},
		{
			MethodName: "GetActorScreenInteractionDetails",
			Handler:    _Tiering_GetActorScreenInteractionDetails_Handler,
		},
		{
			MethodName: "GetActorDistinctTiers",
			Handler:    _Tiering_GetActorDistinctTiers_Handler,
		},
		{
			MethodName: "IsUserEligibleForRewards",
			Handler:    _Tiering_IsUserEligibleForRewards_Handler,
		},
		{
			MethodName: "GetAMBInfo",
			Handler:    _Tiering_GetAMBInfo_Handler,
		},
		{
			MethodName: "FetchSherlockBanners",
			Handler:    _Tiering_FetchSherlockBanners_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/tiering/service.proto",
}
