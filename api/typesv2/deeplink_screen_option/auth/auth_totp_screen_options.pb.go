// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/auth/auth_totp_screen_options.proto

package auth

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthTotpScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Purpose to pass in generate totp rpc
	// auth.totp.enums.Purpose.String()
	Purpose  string                   `protobuf:"bytes,2,opt,name=purpose,proto3" json:"purpose,omitempty"`
	BgColour *widget.BackgroundColour `protobuf:"bytes,3,opt,name=bg_colour,json=bgColour,proto3" json:"bg_colour,omitempty"`
	// eg: Fi MCP
	TitleBar *ui.HeaderBar `protobuf:"bytes,4,opt,name=title_bar,json=titleBar,proto3" json:"title_bar,omitempty"`
	// eg: Key logo for net worth mcp
	MainLogo *common.VisualElement `protobuf:"bytes,5,opt,name=main_logo,json=mainLogo,proto3" json:"main_logo,omitempty"`
	// eg: Your Fi MCP code
	CodeTitleText *common.Text `protobuf:"bytes,6,opt,name=code_title_text,json=codeTitleText,proto3" json:"code_title_text,omitempty"`
	// eg: 123456
	// Text will initially be dummy and client will overwrite when rpc call is successful
	CodeText *common.Text `protobuf:"bytes,7,opt,name=code_text,json=codeText,proto3" json:"code_text,omitempty"`
	// eg: Expires in 24 sec
	// Text will initially be dummy and client will overwrite when rpc call is successful based on expiry timestamp
	ExpiryText *common.Text `protobuf:"bytes,8,opt,name=expiry_text,json=expiryText,proto3" json:"expiry_text,omitempty"`
}

func (x *AuthTotpScreenOptions) Reset() {
	*x = AuthTotpScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuthTotpScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthTotpScreenOptions) ProtoMessage() {}

func (x *AuthTotpScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthTotpScreenOptions.ProtoReflect.Descriptor instead.
func (*AuthTotpScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *AuthTotpScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AuthTotpScreenOptions) GetPurpose() string {
	if x != nil {
		return x.Purpose
	}
	return ""
}

func (x *AuthTotpScreenOptions) GetBgColour() *widget.BackgroundColour {
	if x != nil {
		return x.BgColour
	}
	return nil
}

func (x *AuthTotpScreenOptions) GetTitleBar() *ui.HeaderBar {
	if x != nil {
		return x.TitleBar
	}
	return nil
}

func (x *AuthTotpScreenOptions) GetMainLogo() *common.VisualElement {
	if x != nil {
		return x.MainLogo
	}
	return nil
}

func (x *AuthTotpScreenOptions) GetCodeTitleText() *common.Text {
	if x != nil {
		return x.CodeTitleText
	}
	return nil
}

func (x *AuthTotpScreenOptions) GetCodeText() *common.Text {
	if x != nil {
		return x.CodeText
	}
	return nil
}

func (x *AuthTotpScreenOptions) GetExpiryText() *common.Text {
	if x != nil {
		return x.ExpiryText
	}
	return nil
}

var File_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDesc = []byte{
	0x0a, 0x46, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x74,
	0x6f, 0x74, 0x70, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x27, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x75, 0x74,
	0x68, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf7, 0x03,
	0x0a, 0x15, 0x41, 0x75, 0x74, 0x68, 0x54, 0x6f, 0x74, 0x70, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f, 0x73,
	0x65, 0x12, 0x4b, 0x0a, 0x09, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x08, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x33,
	0x0a, 0x09, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x52, 0x08, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x42, 0x61, 0x72, 0x12, 0x3e, 0x0a, 0x09, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6c, 0x6f, 0x67, 0x6f,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x6d, 0x61, 0x69, 0x6e, 0x4c,
	0x6f, 0x67, 0x6f, 0x12, 0x40, 0x0a, 0x0f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0d, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x69, 0x74, 0x6c,
	0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x35, 0x0a, 0x09, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x64, 0x65, 0x54, 0x65, 0x78, 0x74, 0x12, 0x39, 0x0a, 0x0b,
	0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0a, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x79, 0x54, 0x65, 0x78, 0x74, 0x42, 0x82, 0x01, 0x0a, 0x3e, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x61, 0x75, 0x74, 0x68, 0x50, 0x01, 0x5a, 0x3e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_goTypes = []interface{}{
	(*AuthTotpScreenOptions)(nil),                     // 0: api.typesv2.deeplink_screen_option.auth.AuthTotpScreenOptions
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 1: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*widget.BackgroundColour)(nil),                   // 2: api.typesv2.common.ui.widget.BackgroundColour
	(*ui.HeaderBar)(nil),                              // 3: api.typesv2.HeaderBar
	(*common.VisualElement)(nil),                      // 4: api.typesv2.common.VisualElement
	(*common.Text)(nil),                               // 5: api.typesv2.common.Text
}
var file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_depIdxs = []int32{
	1, // 0: api.typesv2.deeplink_screen_option.auth.AuthTotpScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	2, // 1: api.typesv2.deeplink_screen_option.auth.AuthTotpScreenOptions.bg_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	3, // 2: api.typesv2.deeplink_screen_option.auth.AuthTotpScreenOptions.title_bar:type_name -> api.typesv2.HeaderBar
	4, // 3: api.typesv2.deeplink_screen_option.auth.AuthTotpScreenOptions.main_logo:type_name -> api.typesv2.common.VisualElement
	5, // 4: api.typesv2.deeplink_screen_option.auth.AuthTotpScreenOptions.code_title_text:type_name -> api.typesv2.common.Text
	5, // 5: api.typesv2.deeplink_screen_option.auth.AuthTotpScreenOptions.code_text:type_name -> api.typesv2.common.Text
	5, // 6: api.typesv2.deeplink_screen_option.auth.AuthTotpScreenOptions.expiry_text:type_name -> api.typesv2.common.Text
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_init() }
func file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuthTotpScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_depIdxs,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_auth_auth_totp_screen_options_proto_depIdxs = nil
}
