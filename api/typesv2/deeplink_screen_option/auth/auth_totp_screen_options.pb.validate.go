// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/auth/auth_totp_screen_options.proto

package auth

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AuthTotpScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AuthTotpScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AuthTotpScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AuthTotpScreenOptionsMultiError, or nil if none found.
func (m *AuthTotpScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *AuthTotpScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthTotpScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Purpose

	if all {
		switch v := interface{}(m.GetBgColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "BgColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthTotpScreenOptionsValidationError{
				field:  "BgColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitleBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "TitleBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "TitleBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitleBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthTotpScreenOptionsValidationError{
				field:  "TitleBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMainLogo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "MainLogo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "MainLogo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMainLogo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthTotpScreenOptionsValidationError{
				field:  "MainLogo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCodeTitleText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "CodeTitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "CodeTitleText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCodeTitleText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthTotpScreenOptionsValidationError{
				field:  "CodeTitleText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCodeText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "CodeText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "CodeText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCodeText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthTotpScreenOptionsValidationError{
				field:  "CodeText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpiryText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "ExpiryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AuthTotpScreenOptionsValidationError{
					field:  "ExpiryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AuthTotpScreenOptionsValidationError{
				field:  "ExpiryText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AuthTotpScreenOptionsMultiError(errors)
	}

	return nil
}

// AuthTotpScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by AuthTotpScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type AuthTotpScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AuthTotpScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AuthTotpScreenOptionsMultiError) AllErrors() []error { return m }

// AuthTotpScreenOptionsValidationError is the validation error returned by
// AuthTotpScreenOptions.Validate if the designated constraints aren't met.
type AuthTotpScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AuthTotpScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AuthTotpScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AuthTotpScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AuthTotpScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AuthTotpScreenOptionsValidationError) ErrorName() string {
	return "AuthTotpScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e AuthTotpScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAuthTotpScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AuthTotpScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AuthTotpScreenOptionsValidationError{}
