// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/consent/sa_essential_declarations.proto

package consent

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	form "github.com/epifi/gamma/api/typesv2/form"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3107-12167&t=Z7Ea1FAM7u6v5HyW-0
type SaEssentialDeclarationsScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header    *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	HeaderBar *deeplink.HeaderBar                        `protobuf:"bytes,2,opt,name=header_bar,json=headerBar,proto3" json:"header_bar,omitempty"`
	Title     *common.Text                               `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle  *common.Text                               `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	Cards     []*SAConsentCard                           `protobuf:"bytes,5,rep,name=cards,proto3" json:"cards,omitempty"`
	Ctas      []*deeplink.Cta                            `protobuf:"bytes,7,rep,name=ctas,proto3" json:"ctas,omitempty"`
	CtaHeader *ui.IconTextComponent                      `protobuf:"bytes,8,opt,name=cta_header,json=ctaHeader,proto3" json:"cta_header,omitempty"`
}

func (x *SaEssentialDeclarationsScreenOptions) Reset() {
	*x = SaEssentialDeclarationsScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaEssentialDeclarationsScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaEssentialDeclarationsScreenOptions) ProtoMessage() {}

func (x *SaEssentialDeclarationsScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaEssentialDeclarationsScreenOptions.ProtoReflect.Descriptor instead.
func (*SaEssentialDeclarationsScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescGZIP(), []int{0}
}

func (x *SaEssentialDeclarationsScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SaEssentialDeclarationsScreenOptions) GetHeaderBar() *deeplink.HeaderBar {
	if x != nil {
		return x.HeaderBar
	}
	return nil
}

func (x *SaEssentialDeclarationsScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SaEssentialDeclarationsScreenOptions) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *SaEssentialDeclarationsScreenOptions) GetCards() []*SAConsentCard {
	if x != nil {
		return x.Cards
	}
	return nil
}

func (x *SaEssentialDeclarationsScreenOptions) GetCtas() []*deeplink.Cta {
	if x != nil {
		return x.Ctas
	}
	return nil
}

func (x *SaEssentialDeclarationsScreenOptions) GetCtaHeader() *ui.IconTextComponent {
	if x != nil {
		return x.CtaHeader
	}
	return nil
}

type SAConsentCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Card:
	//
	//	*SAConsentCard_CheckBoxCard_
	//	*SAConsentCard_RadioButtonCard_
	Card isSAConsentCard_Card `protobuf_oneof:"card"`
}

func (x *SAConsentCard) Reset() {
	*x = SAConsentCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SAConsentCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SAConsentCard) ProtoMessage() {}

func (x *SAConsentCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SAConsentCard.ProtoReflect.Descriptor instead.
func (*SAConsentCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescGZIP(), []int{1}
}

func (m *SAConsentCard) GetCard() isSAConsentCard_Card {
	if m != nil {
		return m.Card
	}
	return nil
}

func (x *SAConsentCard) GetCheckBoxCard() *SAConsentCard_CheckBoxCard {
	if x, ok := x.GetCard().(*SAConsentCard_CheckBoxCard_); ok {
		return x.CheckBoxCard
	}
	return nil
}

func (x *SAConsentCard) GetRadioButtonCard() *SAConsentCard_RadioButtonCard {
	if x, ok := x.GetCard().(*SAConsentCard_RadioButtonCard_); ok {
		return x.RadioButtonCard
	}
	return nil
}

type isSAConsentCard_Card interface {
	isSAConsentCard_Card()
}

type SAConsentCard_CheckBoxCard_ struct {
	CheckBoxCard *SAConsentCard_CheckBoxCard `protobuf:"bytes,1,opt,name=check_box_card,json=checkBoxCard,proto3,oneof"`
}

type SAConsentCard_RadioButtonCard_ struct {
	RadioButtonCard *SAConsentCard_RadioButtonCard `protobuf:"bytes,2,opt,name=radio_button_card,json=radioButtonCard,proto3,oneof"`
}

func (*SAConsentCard_CheckBoxCard_) isSAConsentCard_Card() {}

func (*SAConsentCard_RadioButtonCard_) isSAConsentCard_Card() {}

type SAConsentCard_InfoButton struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Visual element for the info icon (typically an "i" icon)
	Icon *common.VisualElement `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	// Deeplink to navigate to help/info screen
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// Whether the info button should be visible (some declarations may not need info)
	IsVisible bool `protobuf:"varint,5,opt,name=is_visible,json=isVisible,proto3" json:"is_visible,omitempty"`
}

func (x *SAConsentCard_InfoButton) Reset() {
	*x = SAConsentCard_InfoButton{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SAConsentCard_InfoButton) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SAConsentCard_InfoButton) ProtoMessage() {}

func (x *SAConsentCard_InfoButton) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SAConsentCard_InfoButton.ProtoReflect.Descriptor instead.
func (*SAConsentCard_InfoButton) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescGZIP(), []int{1, 0}
}

func (x *SAConsentCard_InfoButton) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *SAConsentCard_InfoButton) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *SAConsentCard_InfoButton) GetIsVisible() bool {
	if x != nil {
		return x.IsVisible
	}
	return false
}

type SAConsentCard_CheckBoxCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Consents    *form.Consent            `protobuf:"bytes,1,opt,name=consents,proto3" json:"consents,omitempty"`
	BgColor     *widget.BackgroundColour `protobuf:"bytes,2,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	BorderColor *widget.BackgroundColour `protobuf:"bytes,3,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	Title       *common.Text             `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Description *common.Text             `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// Optional explanatory text shown below the checkbox (e.g., for PEP declaration)
	ExplanatoryText *common.Text              `protobuf:"bytes,6,opt,name=explanatory_text,json=explanatoryText,proto3" json:"explanatory_text,omitempty"`
	InfoButton      *SAConsentCard_InfoButton `protobuf:"bytes,7,opt,name=info_button,json=infoButton,proto3" json:"info_button,omitempty"`
}

func (x *SAConsentCard_CheckBoxCard) Reset() {
	*x = SAConsentCard_CheckBoxCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SAConsentCard_CheckBoxCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SAConsentCard_CheckBoxCard) ProtoMessage() {}

func (x *SAConsentCard_CheckBoxCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SAConsentCard_CheckBoxCard.ProtoReflect.Descriptor instead.
func (*SAConsentCard_CheckBoxCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescGZIP(), []int{1, 1}
}

func (x *SAConsentCard_CheckBoxCard) GetConsents() *form.Consent {
	if x != nil {
		return x.Consents
	}
	return nil
}

func (x *SAConsentCard_CheckBoxCard) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *SAConsentCard_CheckBoxCard) GetBorderColor() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColor
	}
	return nil
}

func (x *SAConsentCard_CheckBoxCard) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SAConsentCard_CheckBoxCard) GetDescription() *common.Text {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *SAConsentCard_CheckBoxCard) GetExplanatoryText() *common.Text {
	if x != nil {
		return x.ExplanatoryText
	}
	return nil
}

func (x *SAConsentCard_CheckBoxCard) GetInfoButton() *SAConsentCard_InfoButton {
	if x != nil {
		return x.InfoButton
	}
	return nil
}

// Radio button options for declarations requiring Yes/No choice (e.g., accessibility declaration)
type SAConsentCard_RadioButtonCard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The question or statement text (e.g., "I am a differently abled person")
	Title          *common.Text                               `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	Description    *common.Text                               `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	RadioOptionYes *SAConsentCard_RadioButtonCard_RadioOption `protobuf:"bytes,3,opt,name=radio_option_yes,json=radioOptionYes,proto3" json:"radio_option_yes,omitempty"`
	RadioOptionNo  *SAConsentCard_RadioButtonCard_RadioOption `protobuf:"bytes,4,opt,name=radio_option_no,json=radioOptionNo,proto3" json:"radio_option_no,omitempty"`
	BgColor        *widget.BackgroundColour                   `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	BorderColor    *widget.BackgroundColour                   `protobuf:"bytes,6,opt,name=border_color,json=borderColor,proto3" json:"border_color,omitempty"`
	// List of consent ids that need to be sent back to backend
	ConsentIds []string `protobuf:"bytes,7,rep,name=consent_ids,json=consentIds,proto3" json:"consent_ids,omitempty"`
	// Whether this is a mandatory field
	IsMandatory bool `protobuf:"varint,8,opt,name=is_mandatory,json=isMandatory,proto3" json:"is_mandatory,omitempty"`
	// Info button for providing additional context about the radio button question
	InfoButton *SAConsentCard_InfoButton `protobuf:"bytes,9,opt,name=info_button,json=infoButton,proto3" json:"info_button,omitempty"`
}

func (x *SAConsentCard_RadioButtonCard) Reset() {
	*x = SAConsentCard_RadioButtonCard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SAConsentCard_RadioButtonCard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SAConsentCard_RadioButtonCard) ProtoMessage() {}

func (x *SAConsentCard_RadioButtonCard) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SAConsentCard_RadioButtonCard.ProtoReflect.Descriptor instead.
func (*SAConsentCard_RadioButtonCard) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescGZIP(), []int{1, 2}
}

func (x *SAConsentCard_RadioButtonCard) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SAConsentCard_RadioButtonCard) GetDescription() *common.Text {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *SAConsentCard_RadioButtonCard) GetRadioOptionYes() *SAConsentCard_RadioButtonCard_RadioOption {
	if x != nil {
		return x.RadioOptionYes
	}
	return nil
}

func (x *SAConsentCard_RadioButtonCard) GetRadioOptionNo() *SAConsentCard_RadioButtonCard_RadioOption {
	if x != nil {
		return x.RadioOptionNo
	}
	return nil
}

func (x *SAConsentCard_RadioButtonCard) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *SAConsentCard_RadioButtonCard) GetBorderColor() *widget.BackgroundColour {
	if x != nil {
		return x.BorderColor
	}
	return nil
}

func (x *SAConsentCard_RadioButtonCard) GetConsentIds() []string {
	if x != nil {
		return x.ConsentIds
	}
	return nil
}

func (x *SAConsentCard_RadioButtonCard) GetIsMandatory() bool {
	if x != nil {
		return x.IsMandatory
	}
	return false
}

func (x *SAConsentCard_RadioButtonCard) GetInfoButton() *SAConsentCard_InfoButton {
	if x != nil {
		return x.InfoButton
	}
	return nil
}

type SAConsentCard_RadioButtonCard_RadioOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Option text (e.g., "Yes", "No")
	Text *common.Text `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	// Deeplink will be passed for yes with a bottom screen
	RadioOptionDeeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=radio_option_deeplink,json=radioOptionDeeplink,proto3" json:"radio_option_deeplink,omitempty"`
}

func (x *SAConsentCard_RadioButtonCard_RadioOption) Reset() {
	*x = SAConsentCard_RadioButtonCard_RadioOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SAConsentCard_RadioButtonCard_RadioOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SAConsentCard_RadioButtonCard_RadioOption) ProtoMessage() {}

func (x *SAConsentCard_RadioButtonCard_RadioOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SAConsentCard_RadioButtonCard_RadioOption.ProtoReflect.Descriptor instead.
func (*SAConsentCard_RadioButtonCard_RadioOption) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescGZIP(), []int{1, 2, 0}
}

func (x *SAConsentCard_RadioButtonCard_RadioOption) GetText() *common.Text {
	if x != nil {
		return x.Text
	}
	return nil
}

func (x *SAConsentCard_RadioButtonCard_RadioOption) GetRadioOptionDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.RadioOptionDeeplink
	}
	return nil
}

var File_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDesc = []byte{
	0x0a, 0x4a, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2f, 0x73, 0x61, 0x5f,
	0x65, 0x73, 0x73, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x2a, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xd9, 0x03, 0x0a, 0x24, 0x53, 0x61, 0x45, 0x73, 0x73, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x0a,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x52, 0x09,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x61, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x75, 0x62,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x4f, 0x0a, 0x05, 0x63, 0x61, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x39, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x41, 0x43,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x05, 0x63, 0x61, 0x72, 0x64,
	0x73, 0x12, 0x2a, 0x0a, 0x04, 0x63, 0x74, 0x61, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x04, 0x63, 0x74, 0x61, 0x73, 0x12, 0x40, 0x0a,
	0x0a, 0x63, 0x74, 0x61, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x63, 0x74, 0x61, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22,
	0xf4, 0x0d, 0x0a, 0x0d, 0x53, 0x41, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x12, 0x6e, 0x0a, 0x0e, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x78, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x41, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x6f, 0x78, 0x43, 0x61, 0x72,
	0x64, 0x48, 0x00, 0x52, 0x0c, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x6f, 0x78, 0x43, 0x61, 0x72,
	0x64, 0x12, 0x77, 0x0a, 0x11, 0x72, 0x61, 0x64, 0x69, 0x6f, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f,
	0x6e, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x49, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x41, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x52, 0x61, 0x64, 0x69, 0x6f, 0x42, 0x75, 0x74,
	0x74, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x48, 0x00, 0x52, 0x0f, 0x72, 0x61, 0x64, 0x69, 0x6f,
	0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x1a, 0x9b, 0x01, 0x0a, 0x0a, 0x49,
	0x6e, 0x66, 0x6f, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52,
	0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f,
	0x76, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x73, 0x56, 0x69, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x1a, 0xfb, 0x03, 0x0a, 0x0c, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x42, 0x6f, 0x78, 0x43, 0x61, 0x72, 0x64, 0x12, 0x35, 0x0a, 0x08, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73,
	0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x51, 0x0a, 0x0c, 0x62,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75,
	0x72, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x2e,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3a,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x43, 0x0a, 0x10, 0x65, 0x78,
	0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0f,
	0x65, 0x78, 0x70, 0x6c, 0x61, 0x6e, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x54, 0x65, 0x78, 0x74, 0x12,
	0x65, 0x0a, 0x0b, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x2e, 0x53, 0x41, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x2e,
	0x49, 0x6e, 0x66, 0x6f, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x52, 0x0a, 0x69, 0x6e, 0x66, 0x6f,
	0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x1a, 0xd5, 0x06, 0x0a, 0x0f, 0x52, 0x61, 0x64, 0x69, 0x6f,
	0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x7f, 0x0a, 0x10, 0x72, 0x61, 0x64, 0x69, 0x6f, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x79, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x55, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x41,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x52, 0x61, 0x64, 0x69,
	0x6f, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x52, 0x61, 0x64, 0x69,
	0x6f, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x72, 0x61, 0x64, 0x69, 0x6f, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x59, 0x65, 0x73, 0x12, 0x7d, 0x0a, 0x0f, 0x72, 0x61, 0x64, 0x69, 0x6f,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x55, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x41,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x52, 0x61, 0x64, 0x69,
	0x6f, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x52, 0x61, 0x64, 0x69,
	0x6f, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x72, 0x61, 0x64, 0x69, 0x6f, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c,
	0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69,
	0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75,
	0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f,
	0x72, 0x12, 0x51, 0x0a, 0x0c, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0b, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x6d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x65, 0x0a, 0x0b, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2e, 0x53, 0x41, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x64, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x75, 0x74,
	0x74, 0x6f, 0x6e, 0x52, 0x0a, 0x69, 0x6e, 0x66, 0x6f, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x1a,
	0x8c, 0x01, 0x0a, 0x0b, 0x52, 0x61, 0x64, 0x69, 0x6f, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2c, 0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x4f, 0x0a,
	0x15, 0x72, 0x61, 0x64, 0x69, 0x6f, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x13, 0x72, 0x61, 0x64, 0x69, 0x6f,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x42, 0x06,
	0x0a, 0x04, 0x63, 0x61, 0x72, 0x64, 0x42, 0x88, 0x01, 0x0a, 0x41, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x50, 0x01, 0x5a, 0x41,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescData = file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_goTypes = []interface{}{
	(*SaEssentialDeclarationsScreenOptions)(nil),      // 0: api.typesv2.deeplink_screen_option.consent.SaEssentialDeclarationsScreenOptions
	(*SAConsentCard)(nil),                             // 1: api.typesv2.deeplink_screen_option.consent.SAConsentCard
	(*SAConsentCard_InfoButton)(nil),                  // 2: api.typesv2.deeplink_screen_option.consent.SAConsentCard.InfoButton
	(*SAConsentCard_CheckBoxCard)(nil),                // 3: api.typesv2.deeplink_screen_option.consent.SAConsentCard.CheckBoxCard
	(*SAConsentCard_RadioButtonCard)(nil),             // 4: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard
	(*SAConsentCard_RadioButtonCard_RadioOption)(nil), // 5: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.RadioOption
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 6: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*deeplink.HeaderBar)(nil),                        // 7: frontend.deeplink.HeaderBar
	(*common.Text)(nil),                               // 8: api.typesv2.common.Text
	(*deeplink.Cta)(nil),                              // 9: frontend.deeplink.Cta
	(*ui.IconTextComponent)(nil),                      // 10: api.typesv2.ui.IconTextComponent
	(*common.VisualElement)(nil),                      // 11: api.typesv2.common.VisualElement
	(*deeplink.Deeplink)(nil),                         // 12: frontend.deeplink.Deeplink
	(*form.Consent)(nil),                              // 13: api.typesv2.form.Consent
	(*widget.BackgroundColour)(nil),                   // 14: api.typesv2.common.ui.widget.BackgroundColour
}
var file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_depIdxs = []int32{
	6,  // 0: api.typesv2.deeplink_screen_option.consent.SaEssentialDeclarationsScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	7,  // 1: api.typesv2.deeplink_screen_option.consent.SaEssentialDeclarationsScreenOptions.header_bar:type_name -> frontend.deeplink.HeaderBar
	8,  // 2: api.typesv2.deeplink_screen_option.consent.SaEssentialDeclarationsScreenOptions.title:type_name -> api.typesv2.common.Text
	8,  // 3: api.typesv2.deeplink_screen_option.consent.SaEssentialDeclarationsScreenOptions.sub_title:type_name -> api.typesv2.common.Text
	1,  // 4: api.typesv2.deeplink_screen_option.consent.SaEssentialDeclarationsScreenOptions.cards:type_name -> api.typesv2.deeplink_screen_option.consent.SAConsentCard
	9,  // 5: api.typesv2.deeplink_screen_option.consent.SaEssentialDeclarationsScreenOptions.ctas:type_name -> frontend.deeplink.Cta
	10, // 6: api.typesv2.deeplink_screen_option.consent.SaEssentialDeclarationsScreenOptions.cta_header:type_name -> api.typesv2.ui.IconTextComponent
	3,  // 7: api.typesv2.deeplink_screen_option.consent.SAConsentCard.check_box_card:type_name -> api.typesv2.deeplink_screen_option.consent.SAConsentCard.CheckBoxCard
	4,  // 8: api.typesv2.deeplink_screen_option.consent.SAConsentCard.radio_button_card:type_name -> api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard
	11, // 9: api.typesv2.deeplink_screen_option.consent.SAConsentCard.InfoButton.icon:type_name -> api.typesv2.common.VisualElement
	12, // 10: api.typesv2.deeplink_screen_option.consent.SAConsentCard.InfoButton.deeplink:type_name -> frontend.deeplink.Deeplink
	13, // 11: api.typesv2.deeplink_screen_option.consent.SAConsentCard.CheckBoxCard.consents:type_name -> api.typesv2.form.Consent
	14, // 12: api.typesv2.deeplink_screen_option.consent.SAConsentCard.CheckBoxCard.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	14, // 13: api.typesv2.deeplink_screen_option.consent.SAConsentCard.CheckBoxCard.border_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	8,  // 14: api.typesv2.deeplink_screen_option.consent.SAConsentCard.CheckBoxCard.title:type_name -> api.typesv2.common.Text
	8,  // 15: api.typesv2.deeplink_screen_option.consent.SAConsentCard.CheckBoxCard.description:type_name -> api.typesv2.common.Text
	8,  // 16: api.typesv2.deeplink_screen_option.consent.SAConsentCard.CheckBoxCard.explanatory_text:type_name -> api.typesv2.common.Text
	2,  // 17: api.typesv2.deeplink_screen_option.consent.SAConsentCard.CheckBoxCard.info_button:type_name -> api.typesv2.deeplink_screen_option.consent.SAConsentCard.InfoButton
	8,  // 18: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.title:type_name -> api.typesv2.common.Text
	8,  // 19: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.description:type_name -> api.typesv2.common.Text
	5,  // 20: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.radio_option_yes:type_name -> api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.RadioOption
	5,  // 21: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.radio_option_no:type_name -> api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.RadioOption
	14, // 22: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	14, // 23: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.border_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	2,  // 24: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.info_button:type_name -> api.typesv2.deeplink_screen_option.consent.SAConsentCard.InfoButton
	8,  // 25: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.RadioOption.text:type_name -> api.typesv2.common.Text
	12, // 26: api.typesv2.deeplink_screen_option.consent.SAConsentCard.RadioButtonCard.RadioOption.radio_option_deeplink:type_name -> frontend.deeplink.Deeplink
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_init() }
func file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_init() {
	if File_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaEssentialDeclarationsScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SAConsentCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SAConsentCard_InfoButton); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SAConsentCard_CheckBoxCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SAConsentCard_RadioButtonCard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SAConsentCard_RadioButtonCard_RadioOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*SAConsentCard_CheckBoxCard_)(nil),
		(*SAConsentCard_RadioButtonCard_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_depIdxs,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto = out.File
	file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_consent_sa_essential_declarations_proto_depIdxs = nil
}
