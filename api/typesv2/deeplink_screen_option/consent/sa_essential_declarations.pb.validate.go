// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/consent/sa_essential_declarations.proto

package consent

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SaEssentialDeclarationsScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SaEssentialDeclarationsScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaEssentialDeclarationsScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SaEssentialDeclarationsScreenOptionsMultiError, or nil if none found.
func (m *SaEssentialDeclarationsScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SaEssentialDeclarationsScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaEssentialDeclarationsScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "HeaderBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaEssentialDeclarationsScreenOptionsValidationError{
				field:  "HeaderBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaEssentialDeclarationsScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaEssentialDeclarationsScreenOptionsValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCards() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
						field:  fmt.Sprintf("Cards[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SaEssentialDeclarationsScreenOptionsValidationError{
					field:  fmt.Sprintf("Cards[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCtas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
						field:  fmt.Sprintf("Ctas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SaEssentialDeclarationsScreenOptionsValidationError{
					field:  fmt.Sprintf("Ctas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCtaHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "CtaHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SaEssentialDeclarationsScreenOptionsValidationError{
					field:  "CtaHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCtaHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SaEssentialDeclarationsScreenOptionsValidationError{
				field:  "CtaHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SaEssentialDeclarationsScreenOptionsMultiError(errors)
	}

	return nil
}

// SaEssentialDeclarationsScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// SaEssentialDeclarationsScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type SaEssentialDeclarationsScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaEssentialDeclarationsScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaEssentialDeclarationsScreenOptionsMultiError) AllErrors() []error { return m }

// SaEssentialDeclarationsScreenOptionsValidationError is the validation error
// returned by SaEssentialDeclarationsScreenOptions.Validate if the designated
// constraints aren't met.
type SaEssentialDeclarationsScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaEssentialDeclarationsScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaEssentialDeclarationsScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaEssentialDeclarationsScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaEssentialDeclarationsScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaEssentialDeclarationsScreenOptionsValidationError) ErrorName() string {
	return "SaEssentialDeclarationsScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SaEssentialDeclarationsScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaEssentialDeclarationsScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaEssentialDeclarationsScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaEssentialDeclarationsScreenOptionsValidationError{}

// Validate checks the field values on SAConsentCard with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SAConsentCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SAConsentCard with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SAConsentCardMultiError, or
// nil if none found.
func (m *SAConsentCard) ValidateAll() error {
	return m.validate(true)
}

func (m *SAConsentCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Card.(type) {
	case *SAConsentCard_CheckBoxCard_:
		if v == nil {
			err := SAConsentCardValidationError{
				field:  "Card",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCheckBoxCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SAConsentCardValidationError{
						field:  "CheckBoxCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SAConsentCardValidationError{
						field:  "CheckBoxCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCheckBoxCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SAConsentCardValidationError{
					field:  "CheckBoxCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SAConsentCard_RadioButtonCard_:
		if v == nil {
			err := SAConsentCardValidationError{
				field:  "Card",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRadioButtonCard()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SAConsentCardValidationError{
						field:  "RadioButtonCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SAConsentCardValidationError{
						field:  "RadioButtonCard",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRadioButtonCard()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SAConsentCardValidationError{
					field:  "RadioButtonCard",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SAConsentCardMultiError(errors)
	}

	return nil
}

// SAConsentCardMultiError is an error wrapping multiple validation errors
// returned by SAConsentCard.ValidateAll() if the designated constraints
// aren't met.
type SAConsentCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SAConsentCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SAConsentCardMultiError) AllErrors() []error { return m }

// SAConsentCardValidationError is the validation error returned by
// SAConsentCard.Validate if the designated constraints aren't met.
type SAConsentCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SAConsentCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SAConsentCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SAConsentCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SAConsentCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SAConsentCardValidationError) ErrorName() string { return "SAConsentCardValidationError" }

// Error satisfies the builtin error interface
func (e SAConsentCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSAConsentCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SAConsentCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SAConsentCardValidationError{}

// Validate checks the field values on SAConsentCard_InfoButton with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SAConsentCard_InfoButton) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SAConsentCard_InfoButton with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SAConsentCard_InfoButtonMultiError, or nil if none found.
func (m *SAConsentCard_InfoButton) ValidateAll() error {
	return m.validate(true)
}

func (m *SAConsentCard_InfoButton) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_InfoButtonValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_InfoButtonValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_InfoButtonValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_InfoButtonValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_InfoButtonValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_InfoButtonValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsVisible

	if len(errors) > 0 {
		return SAConsentCard_InfoButtonMultiError(errors)
	}

	return nil
}

// SAConsentCard_InfoButtonMultiError is an error wrapping multiple validation
// errors returned by SAConsentCard_InfoButton.ValidateAll() if the designated
// constraints aren't met.
type SAConsentCard_InfoButtonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SAConsentCard_InfoButtonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SAConsentCard_InfoButtonMultiError) AllErrors() []error { return m }

// SAConsentCard_InfoButtonValidationError is the validation error returned by
// SAConsentCard_InfoButton.Validate if the designated constraints aren't met.
type SAConsentCard_InfoButtonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SAConsentCard_InfoButtonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SAConsentCard_InfoButtonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SAConsentCard_InfoButtonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SAConsentCard_InfoButtonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SAConsentCard_InfoButtonValidationError) ErrorName() string {
	return "SAConsentCard_InfoButtonValidationError"
}

// Error satisfies the builtin error interface
func (e SAConsentCard_InfoButtonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSAConsentCard_InfoButton.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SAConsentCard_InfoButtonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SAConsentCard_InfoButtonValidationError{}

// Validate checks the field values on SAConsentCard_CheckBoxCard with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SAConsentCard_CheckBoxCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SAConsentCard_CheckBoxCard with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SAConsentCard_CheckBoxCardMultiError, or nil if none found.
func (m *SAConsentCard_CheckBoxCard) ValidateAll() error {
	return m.validate(true)
}

func (m *SAConsentCard_CheckBoxCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetConsents()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "Consents",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "Consents",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsents()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_CheckBoxCardValidationError{
				field:  "Consents",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_CheckBoxCardValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "BorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "BorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_CheckBoxCardValidationError{
				field:  "BorderColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_CheckBoxCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_CheckBoxCardValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExplanatoryText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "ExplanatoryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "ExplanatoryText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExplanatoryText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_CheckBoxCardValidationError{
				field:  "ExplanatoryText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "InfoButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_CheckBoxCardValidationError{
					field:  "InfoButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_CheckBoxCardValidationError{
				field:  "InfoButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SAConsentCard_CheckBoxCardMultiError(errors)
	}

	return nil
}

// SAConsentCard_CheckBoxCardMultiError is an error wrapping multiple
// validation errors returned by SAConsentCard_CheckBoxCard.ValidateAll() if
// the designated constraints aren't met.
type SAConsentCard_CheckBoxCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SAConsentCard_CheckBoxCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SAConsentCard_CheckBoxCardMultiError) AllErrors() []error { return m }

// SAConsentCard_CheckBoxCardValidationError is the validation error returned
// by SAConsentCard_CheckBoxCard.Validate if the designated constraints aren't met.
type SAConsentCard_CheckBoxCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SAConsentCard_CheckBoxCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SAConsentCard_CheckBoxCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SAConsentCard_CheckBoxCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SAConsentCard_CheckBoxCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SAConsentCard_CheckBoxCardValidationError) ErrorName() string {
	return "SAConsentCard_CheckBoxCardValidationError"
}

// Error satisfies the builtin error interface
func (e SAConsentCard_CheckBoxCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSAConsentCard_CheckBoxCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SAConsentCard_CheckBoxCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SAConsentCard_CheckBoxCardValidationError{}

// Validate checks the field values on SAConsentCard_RadioButtonCard with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SAConsentCard_RadioButtonCard) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SAConsentCard_RadioButtonCard with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SAConsentCard_RadioButtonCardMultiError, or nil if none found.
func (m *SAConsentCard_RadioButtonCard) ValidateAll() error {
	return m.validate(true)
}

func (m *SAConsentCard_RadioButtonCard) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_RadioButtonCardValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_RadioButtonCardValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRadioOptionYes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "RadioOptionYes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "RadioOptionYes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRadioOptionYes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_RadioButtonCardValidationError{
				field:  "RadioOptionYes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRadioOptionNo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "RadioOptionNo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "RadioOptionNo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRadioOptionNo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_RadioButtonCardValidationError{
				field:  "RadioOptionNo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBgColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "BgColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_RadioButtonCardValidationError{
				field:  "BgColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBorderColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "BorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "BorderColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBorderColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_RadioButtonCardValidationError{
				field:  "BorderColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsMandatory

	if all {
		switch v := interface{}(m.GetInfoButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "InfoButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCardValidationError{
					field:  "InfoButton",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_RadioButtonCardValidationError{
				field:  "InfoButton",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SAConsentCard_RadioButtonCardMultiError(errors)
	}

	return nil
}

// SAConsentCard_RadioButtonCardMultiError is an error wrapping multiple
// validation errors returned by SAConsentCard_RadioButtonCard.ValidateAll()
// if the designated constraints aren't met.
type SAConsentCard_RadioButtonCardMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SAConsentCard_RadioButtonCardMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SAConsentCard_RadioButtonCardMultiError) AllErrors() []error { return m }

// SAConsentCard_RadioButtonCardValidationError is the validation error
// returned by SAConsentCard_RadioButtonCard.Validate if the designated
// constraints aren't met.
type SAConsentCard_RadioButtonCardValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SAConsentCard_RadioButtonCardValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SAConsentCard_RadioButtonCardValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SAConsentCard_RadioButtonCardValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SAConsentCard_RadioButtonCardValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SAConsentCard_RadioButtonCardValidationError) ErrorName() string {
	return "SAConsentCard_RadioButtonCardValidationError"
}

// Error satisfies the builtin error interface
func (e SAConsentCard_RadioButtonCardValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSAConsentCard_RadioButtonCard.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SAConsentCard_RadioButtonCardValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SAConsentCard_RadioButtonCardValidationError{}

// Validate checks the field values on
// SAConsentCard_RadioButtonCard_RadioOption with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SAConsentCard_RadioButtonCard_RadioOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SAConsentCard_RadioButtonCard_RadioOption with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SAConsentCard_RadioButtonCard_RadioOptionMultiError, or nil if none found.
func (m *SAConsentCard_RadioButtonCard_RadioOption) ValidateAll() error {
	return m.validate(true)
}

func (m *SAConsentCard_RadioButtonCard_RadioOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCard_RadioOptionValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCard_RadioOptionValidationError{
					field:  "Text",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_RadioButtonCard_RadioOptionValidationError{
				field:  "Text",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRadioOptionDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCard_RadioOptionValidationError{
					field:  "RadioOptionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SAConsentCard_RadioButtonCard_RadioOptionValidationError{
					field:  "RadioOptionDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRadioOptionDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SAConsentCard_RadioButtonCard_RadioOptionValidationError{
				field:  "RadioOptionDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SAConsentCard_RadioButtonCard_RadioOptionMultiError(errors)
	}

	return nil
}

// SAConsentCard_RadioButtonCard_RadioOptionMultiError is an error wrapping
// multiple validation errors returned by
// SAConsentCard_RadioButtonCard_RadioOption.ValidateAll() if the designated
// constraints aren't met.
type SAConsentCard_RadioButtonCard_RadioOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SAConsentCard_RadioButtonCard_RadioOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SAConsentCard_RadioButtonCard_RadioOptionMultiError) AllErrors() []error { return m }

// SAConsentCard_RadioButtonCard_RadioOptionValidationError is the validation
// error returned by SAConsentCard_RadioButtonCard_RadioOption.Validate if the
// designated constraints aren't met.
type SAConsentCard_RadioButtonCard_RadioOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SAConsentCard_RadioButtonCard_RadioOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SAConsentCard_RadioButtonCard_RadioOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SAConsentCard_RadioButtonCard_RadioOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SAConsentCard_RadioButtonCard_RadioOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SAConsentCard_RadioButtonCard_RadioOptionValidationError) ErrorName() string {
	return "SAConsentCard_RadioButtonCard_RadioOptionValidationError"
}

// Error satisfies the builtin error interface
func (e SAConsentCard_RadioButtonCard_RadioOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSAConsentCard_RadioButtonCard_RadioOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SAConsentCard_RadioButtonCard_RadioOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SAConsentCard_RadioButtonCard_RadioOptionValidationError{}
