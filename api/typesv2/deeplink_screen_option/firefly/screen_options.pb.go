// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/deeplink_screen_option/firefly/screen_options.proto

package firefly

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	firefly "github.com/epifi/gamma/api/frontend/firefly"
	enums "github.com/epifi/gamma/api/frontend/firefly/enums"
	transaction "github.com/epifi/gamma/api/frontend/pay/transaction"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CardTabScreenOptions_SelectedTab int32

const (
	CardTabScreenOptions_CREDIT_CARD CardTabScreenOptions_SelectedTab = 0
	CardTabScreenOptions_DEBIT_CARD  CardTabScreenOptions_SelectedTab = 1
)

// Enum value maps for CardTabScreenOptions_SelectedTab.
var (
	CardTabScreenOptions_SelectedTab_name = map[int32]string{
		0: "CREDIT_CARD",
		1: "DEBIT_CARD",
	}
	CardTabScreenOptions_SelectedTab_value = map[string]int32{
		"CREDIT_CARD": 0,
		"DEBIT_CARD":  1,
	}
)

func (x CardTabScreenOptions_SelectedTab) Enum() *CardTabScreenOptions_SelectedTab {
	p := new(CardTabScreenOptions_SelectedTab)
	*p = x
	return p
}

func (x CardTabScreenOptions_SelectedTab) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CardTabScreenOptions_SelectedTab) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_enumTypes[0].Descriptor()
}

func (CardTabScreenOptions_SelectedTab) Type() protoreflect.EnumType {
	return &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_enumTypes[0]
}

func (x CardTabScreenOptions_SelectedTab) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CardTabScreenOptions_SelectedTab.Descriptor instead.
func (CardTabScreenOptions_SelectedTab) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{18, 0}
}

type CreditCardDetailsAndBenefitsScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory field needed for screenOptionV2 compatibility
	Header              *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ScreenTitle         string                                     `protobuf:"bytes,2,opt,name=screen_title,json=screenTitle,proto3" json:"screen_title,omitempty"`
	CardImageUrl        string                                     `protobuf:"bytes,3,opt,name=card_image_url,json=cardImageUrl,proto3" json:"card_image_url,omitempty"`
	CreditLimitStr      string                                     `protobuf:"bytes,4,opt,name=credit_limit_str,json=creditLimitStr,proto3" json:"credit_limit_str,omitempty"`
	Brands              *deeplink.InfoItemWithCta                  `protobuf:"bytes,5,opt,name=brands,proto3" json:"brands,omitempty"`
	ValuebackCheatsheet *deeplink.InfoItemWithCta                  `protobuf:"bytes,6,opt,name=valueback_cheatsheet,json=valuebackCheatsheet,proto3" json:"valueback_cheatsheet,omitempty"`
	WelcomeVouchers     *deeplink.InfoItemWithCta                  `protobuf:"bytes,7,opt,name=welcome_vouchers,json=welcomeVouchers,proto3" json:"welcome_vouchers,omitempty"`
	StaticImages        []*deeplink.InfoItem                       `protobuf:"bytes,8,rep,name=static_images,json=staticImages,proto3" json:"static_images,omitempty"`
	BottomInfoItems     []*deeplink.InfoItemWithCtaV2              `protobuf:"bytes,9,rep,name=bottom_info_items,json=bottomInfoItems,proto3" json:"bottom_info_items,omitempty"`
	Share               *deeplink.Cta                              `protobuf:"bytes,10,opt,name=share,proto3" json:"share,omitempty"`
	// text to be used while sharing
	TextForShare         string       `protobuf:"bytes,11,opt,name=text_for_share,json=textForShare,proto3" json:"text_for_share,omitempty"`
	ShareCreditLimitText *common.Text `protobuf:"bytes,12,opt,name=share_credit_limit_text,json=shareCreditLimitText,proto3" json:"share_credit_limit_text,omitempty"`
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) Reset() {
	*x = CreditCardDetailsAndBenefitsScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardDetailsAndBenefitsScreenOptions) ProtoMessage() {}

func (x *CreditCardDetailsAndBenefitsScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardDetailsAndBenefitsScreenOptions.ProtoReflect.Descriptor instead.
func (*CreditCardDetailsAndBenefitsScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetScreenTitle() string {
	if x != nil {
		return x.ScreenTitle
	}
	return ""
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetCardImageUrl() string {
	if x != nil {
		return x.CardImageUrl
	}
	return ""
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetCreditLimitStr() string {
	if x != nil {
		return x.CreditLimitStr
	}
	return ""
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetBrands() *deeplink.InfoItemWithCta {
	if x != nil {
		return x.Brands
	}
	return nil
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetValuebackCheatsheet() *deeplink.InfoItemWithCta {
	if x != nil {
		return x.ValuebackCheatsheet
	}
	return nil
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetWelcomeVouchers() *deeplink.InfoItemWithCta {
	if x != nil {
		return x.WelcomeVouchers
	}
	return nil
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetStaticImages() []*deeplink.InfoItem {
	if x != nil {
		return x.StaticImages
	}
	return nil
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetBottomInfoItems() []*deeplink.InfoItemWithCtaV2 {
	if x != nil {
		return x.BottomInfoItems
	}
	return nil
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetShare() *deeplink.Cta {
	if x != nil {
		return x.Share
	}
	return nil
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetTextForShare() string {
	if x != nil {
		return x.TextForShare
	}
	return ""
}

func (x *CreditCardDetailsAndBenefitsScreenOptions) GetShareCreditLimitText() *common.Text {
	if x != nil {
		return x.ShareCreditLimitText
	}
	return nil
}

type CreditCardAddressSelectionV2ScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header          *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	StepInfo        *deeplink.StepInfo                         `protobuf:"bytes,4,opt,name=step_info,json=stepInfo,proto3" json:"step_info,omitempty"`
	ContinueCta     *deeplink.Cta                              `protobuf:"bytes,6,opt,name=continue_cta,json=continueCta,proto3" json:"continue_cta,omitempty"`
	CardRequestId   string                                     `protobuf:"bytes,7,opt,name=card_request_id,json=cardRequestId,proto3" json:"card_request_id,omitempty"`
	KycLevel        typesv2.KYCLevel                           `protobuf:"varint,9,opt,name=kyc_level,json=kycLevel,proto3,enum=api.typesv2.KYCLevel" json:"kyc_level,omitempty"`
	HeaderDetails   *deeplink.InfoItemV3                       `protobuf:"bytes,10,opt,name=header_details,json=headerDetails,proto3" json:"header_details,omitempty"`
	AddAddressCtaV2 *deeplink.InfoItemWithCtaV3                `protobuf:"bytes,11,opt,name=add_address_cta_v2,json=addAddressCtaV2,proto3" json:"add_address_cta_v2,omitempty"`
	// deprecated in favour of header_details
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
	Text string `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`
	// deprecated in favour of header_details
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
	SubText string `protobuf:"bytes,3,opt,name=sub_text,json=subText,proto3" json:"sub_text,omitempty"`
	// deprecated in favour of header_details
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
	DeliveryAddressIconUrl string `protobuf:"bytes,5,opt,name=delivery_address_icon_url,json=deliveryAddressIconUrl,proto3" json:"delivery_address_icon_url,omitempty"`
	// deprecated in favour of add_address_cta_v2
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
	AddAddressCta           *deeplink.InfoItemWithCtaV2 `protobuf:"bytes,8,opt,name=add_address_cta,json=addAddressCta,proto3" json:"add_address_cta,omitempty"`
	HeaderIconTextComponent *ui.IconTextComponent       `protobuf:"bytes,12,opt,name=header_icon_text_component,json=headerIconTextComponent,proto3" json:"header_icon_text_component,omitempty"`
}

func (x *CreditCardAddressSelectionV2ScreenOptions) Reset() {
	*x = CreditCardAddressSelectionV2ScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardAddressSelectionV2ScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardAddressSelectionV2ScreenOptions) ProtoMessage() {}

func (x *CreditCardAddressSelectionV2ScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardAddressSelectionV2ScreenOptions.ProtoReflect.Descriptor instead.
func (*CreditCardAddressSelectionV2ScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *CreditCardAddressSelectionV2ScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreditCardAddressSelectionV2ScreenOptions) GetStepInfo() *deeplink.StepInfo {
	if x != nil {
		return x.StepInfo
	}
	return nil
}

func (x *CreditCardAddressSelectionV2ScreenOptions) GetContinueCta() *deeplink.Cta {
	if x != nil {
		return x.ContinueCta
	}
	return nil
}

func (x *CreditCardAddressSelectionV2ScreenOptions) GetCardRequestId() string {
	if x != nil {
		return x.CardRequestId
	}
	return ""
}

func (x *CreditCardAddressSelectionV2ScreenOptions) GetKycLevel() typesv2.KYCLevel {
	if x != nil {
		return x.KycLevel
	}
	return typesv2.KYCLevel(0)
}

func (x *CreditCardAddressSelectionV2ScreenOptions) GetHeaderDetails() *deeplink.InfoItemV3 {
	if x != nil {
		return x.HeaderDetails
	}
	return nil
}

func (x *CreditCardAddressSelectionV2ScreenOptions) GetAddAddressCtaV2() *deeplink.InfoItemWithCtaV3 {
	if x != nil {
		return x.AddAddressCtaV2
	}
	return nil
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
func (x *CreditCardAddressSelectionV2ScreenOptions) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
func (x *CreditCardAddressSelectionV2ScreenOptions) GetSubText() string {
	if x != nil {
		return x.SubText
	}
	return ""
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
func (x *CreditCardAddressSelectionV2ScreenOptions) GetDeliveryAddressIconUrl() string {
	if x != nil {
		return x.DeliveryAddressIconUrl
	}
	return ""
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
func (x *CreditCardAddressSelectionV2ScreenOptions) GetAddAddressCta() *deeplink.InfoItemWithCtaV2 {
	if x != nil {
		return x.AddAddressCta
	}
	return nil
}

func (x *CreditCardAddressSelectionV2ScreenOptions) GetHeaderIconTextComponent() *ui.IconTextComponent {
	if x != nil {
		return x.HeaderIconTextComponent
	}
	return nil
}

type CreditCardRealTimeEligibilityCheckIntroScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header              *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ImageTop            *common.VisualElement                      `protobuf:"bytes,2,opt,name=image_top,json=imageTop,proto3" json:"image_top,omitempty"`
	Title               *common.Text                               `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle            *common.Text                               `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	CheckCreditScoreCta *deeplink.Cta                              `protobuf:"bytes,5,opt,name=check_credit_score_cta,json=checkCreditScoreCta,proto3" json:"check_credit_score_cta,omitempty"`
	ImageBottom         *common.VisualElement                      `protobuf:"bytes,6,opt,name=image_bottom,json=imageBottom,proto3" json:"image_bottom,omitempty"`
}

func (x *CreditCardRealTimeEligibilityCheckIntroScreenOptions) Reset() {
	*x = CreditCardRealTimeEligibilityCheckIntroScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardRealTimeEligibilityCheckIntroScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardRealTimeEligibilityCheckIntroScreenOptions) ProtoMessage() {}

func (x *CreditCardRealTimeEligibilityCheckIntroScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardRealTimeEligibilityCheckIntroScreenOptions.ProtoReflect.Descriptor instead.
func (*CreditCardRealTimeEligibilityCheckIntroScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *CreditCardRealTimeEligibilityCheckIntroScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreditCardRealTimeEligibilityCheckIntroScreenOptions) GetImageTop() *common.VisualElement {
	if x != nil {
		return x.ImageTop
	}
	return nil
}

func (x *CreditCardRealTimeEligibilityCheckIntroScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CreditCardRealTimeEligibilityCheckIntroScreenOptions) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *CreditCardRealTimeEligibilityCheckIntroScreenOptions) GetCheckCreditScoreCta() *deeplink.Cta {
	if x != nil {
		return x.CheckCreditScoreCta
	}
	return nil
}

func (x *CreditCardRealTimeEligibilityCheckIntroScreenOptions) GetImageBottom() *common.VisualElement {
	if x != nil {
		return x.ImageBottom
	}
	return nil
}

type SecuredCreditCardDepositScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory field needed for screenOptionV2 compatibility
	Header                   *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	StepInfo                 *deeplink.StepInfo                         `protobuf:"bytes,2,opt,name=step_info,json=stepInfo,proto3" json:"step_info,omitempty"`
	TopSectionDetails        *deeplink.InfoItemWithCtaV2                `protobuf:"bytes,3,opt,name=top_section_details,json=topSectionDetails,proto3" json:"top_section_details,omitempty"`
	MinDepositAmount         *typesv2.Money                             `protobuf:"bytes,4,opt,name=min_deposit_amount,json=minDepositAmount,proto3" json:"min_deposit_amount,omitempty"`
	BottomText               *deeplink.InfoItemV2                       `protobuf:"bytes,5,opt,name=bottom_text,json=bottomText,proto3" json:"bottom_text,omitempty"`
	InsufficientBalanceCta   *deeplink.Cta                              `protobuf:"bytes,6,opt,name=insufficient_balance_cta,json=insufficientBalanceCta,proto3" json:"insufficient_balance_cta,omitempty"`
	OpenDepositCta           *deeplink.Cta                              `protobuf:"bytes,7,opt,name=open_deposit_cta,json=openDepositCta,proto3" json:"open_deposit_cta,omitempty"`
	IneligibleActionText     *common.Text                               `protobuf:"bytes,8,opt,name=ineligible_action_text,json=ineligibleActionText,proto3" json:"ineligible_action_text,omitempty"`
	AddNomineeCta            *deeplink.Cta                              `protobuf:"bytes,9,opt,name=add_nominee_cta,json=addNomineeCta,proto3" json:"add_nominee_cta,omitempty"`
	AddNomineeVisualElement  *common.VisualElement                      `protobuf:"bytes,10,opt,name=add_nominee_visual_element,json=addNomineeVisualElement,proto3" json:"add_nominee_visual_element,omitempty"`
	TermsAndConditionsText   *common.Text                               `protobuf:"bytes,11,opt,name=terms_and_conditions_text,json=termsAndConditionsText,proto3" json:"terms_and_conditions_text,omitempty"`
	DefaultCreditLimit       *typesv2.Money                             `protobuf:"bytes,12,opt,name=default_credit_limit,json=defaultCreditLimit,proto3" json:"default_credit_limit,omitempty"`
	SuggestedCreditLimits    []*typesv2.Money                           `protobuf:"bytes,13,rep,name=suggested_credit_limits,json=suggestedCreditLimits,proto3" json:"suggested_credit_limits,omitempty"`
	CreditLimitHeading       *common.Text                               `protobuf:"bytes,14,opt,name=credit_limit_heading,json=creditLimitHeading,proto3" json:"credit_limit_heading,omitempty"`
	DepositLimitHeading      *common.Text                               `protobuf:"bytes,15,opt,name=deposit_limit_heading,json=depositLimitHeading,proto3" json:"deposit_limit_heading,omitempty"`
	MaxDepositAmount         *typesv2.Money                             `protobuf:"bytes,16,opt,name=max_deposit_amount,json=maxDepositAmount,proto3" json:"max_deposit_amount,omitempty"`
	MinDepositText           *common.Text                               `protobuf:"bytes,17,opt,name=min_deposit_text,json=minDepositText,proto3" json:"min_deposit_text,omitempty"`
	MaxDepositText           *common.Text                               `protobuf:"bytes,18,opt,name=max_deposit_text,json=maxDepositText,proto3" json:"max_deposit_text,omitempty"`
	InfoTooltipCreditLimit   *deeplink.InfoToolTipV2                    `protobuf:"bytes,19,opt,name=info_tooltip_credit_limit,json=infoTooltipCreditLimit,proto3" json:"info_tooltip_credit_limit,omitempty"`
	InfoTooltipFixedDeposit  *deeplink.InfoToolTipV2                    `protobuf:"bytes,20,opt,name=info_tooltip_fixed_deposit,json=infoTooltipFixedDeposit,proto3" json:"info_tooltip_fixed_deposit,omitempty"`
	BenefitsVisualElementCta *deeplink.VisualElementCta                 `protobuf:"bytes,21,opt,name=benefits_visual_element_cta,json=benefitsVisualElementCta,proto3" json:"benefits_visual_element_cta,omitempty"`
}

func (x *SecuredCreditCardDepositScreenOptions) Reset() {
	*x = SecuredCreditCardDepositScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecuredCreditCardDepositScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecuredCreditCardDepositScreenOptions) ProtoMessage() {}

func (x *SecuredCreditCardDepositScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecuredCreditCardDepositScreenOptions.ProtoReflect.Descriptor instead.
func (*SecuredCreditCardDepositScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{3}
}

func (x *SecuredCreditCardDepositScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetStepInfo() *deeplink.StepInfo {
	if x != nil {
		return x.StepInfo
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetTopSectionDetails() *deeplink.InfoItemWithCtaV2 {
	if x != nil {
		return x.TopSectionDetails
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetMinDepositAmount() *typesv2.Money {
	if x != nil {
		return x.MinDepositAmount
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetBottomText() *deeplink.InfoItemV2 {
	if x != nil {
		return x.BottomText
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetInsufficientBalanceCta() *deeplink.Cta {
	if x != nil {
		return x.InsufficientBalanceCta
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetOpenDepositCta() *deeplink.Cta {
	if x != nil {
		return x.OpenDepositCta
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetIneligibleActionText() *common.Text {
	if x != nil {
		return x.IneligibleActionText
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetAddNomineeCta() *deeplink.Cta {
	if x != nil {
		return x.AddNomineeCta
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetAddNomineeVisualElement() *common.VisualElement {
	if x != nil {
		return x.AddNomineeVisualElement
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetTermsAndConditionsText() *common.Text {
	if x != nil {
		return x.TermsAndConditionsText
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetDefaultCreditLimit() *typesv2.Money {
	if x != nil {
		return x.DefaultCreditLimit
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetSuggestedCreditLimits() []*typesv2.Money {
	if x != nil {
		return x.SuggestedCreditLimits
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetCreditLimitHeading() *common.Text {
	if x != nil {
		return x.CreditLimitHeading
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetDepositLimitHeading() *common.Text {
	if x != nil {
		return x.DepositLimitHeading
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetMaxDepositAmount() *typesv2.Money {
	if x != nil {
		return x.MaxDepositAmount
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetMinDepositText() *common.Text {
	if x != nil {
		return x.MinDepositText
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetMaxDepositText() *common.Text {
	if x != nil {
		return x.MaxDepositText
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetInfoTooltipCreditLimit() *deeplink.InfoToolTipV2 {
	if x != nil {
		return x.InfoTooltipCreditLimit
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetInfoTooltipFixedDeposit() *deeplink.InfoToolTipV2 {
	if x != nil {
		return x.InfoTooltipFixedDeposit
	}
	return nil
}

func (x *SecuredCreditCardDepositScreenOptions) GetBenefitsVisualElementCta() *deeplink.VisualElementCta {
	if x != nil {
		return x.BenefitsVisualElementCta
	}
	return nil
}

type SecuredCreditCardFdDetailsScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory field needed for screenOptionV2 compatibility
	Header           *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	StepInfo         *deeplink.StepInfo                         `protobuf:"bytes,2,opt,name=step_info,json=stepInfo,proto3" json:"step_info,omitempty"`
	HeaderDetails    *deeplink.InfoItemV3                       `protobuf:"bytes,3,opt,name=header_details,json=headerDetails,proto3" json:"header_details,omitempty"`
	FdDetails        []*deeplink.InfoItemV3                     `protobuf:"bytes,4,rep,name=fd_details,json=fdDetails,proto3" json:"fd_details,omitempty"`
	NextCta          *deeplink.Cta                              `protobuf:"bytes,5,opt,name=next_cta,json=nextCta,proto3" json:"next_cta,omitempty"`
	FdCreationStatus typesv2.DepositStatus                      `protobuf:"varint,6,opt,name=fd_creation_status,json=fdCreationStatus,proto3,enum=api.typesv2.DepositStatus" json:"fd_creation_status,omitempty"`
	CardRequestId    string                                     `protobuf:"bytes,7,opt,name=card_request_id,json=cardRequestId,proto3" json:"card_request_id,omitempty"`
	VeBankLogo       *common.VisualElement                      `protobuf:"bytes,8,opt,name=ve_bank_logo,json=veBankLogo,proto3" json:"ve_bank_logo,omitempty"`
}

func (x *SecuredCreditCardFdDetailsScreenOptions) Reset() {
	*x = SecuredCreditCardFdDetailsScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecuredCreditCardFdDetailsScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecuredCreditCardFdDetailsScreenOptions) ProtoMessage() {}

func (x *SecuredCreditCardFdDetailsScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecuredCreditCardFdDetailsScreenOptions.ProtoReflect.Descriptor instead.
func (*SecuredCreditCardFdDetailsScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{4}
}

func (x *SecuredCreditCardFdDetailsScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SecuredCreditCardFdDetailsScreenOptions) GetStepInfo() *deeplink.StepInfo {
	if x != nil {
		return x.StepInfo
	}
	return nil
}

func (x *SecuredCreditCardFdDetailsScreenOptions) GetHeaderDetails() *deeplink.InfoItemV3 {
	if x != nil {
		return x.HeaderDetails
	}
	return nil
}

func (x *SecuredCreditCardFdDetailsScreenOptions) GetFdDetails() []*deeplink.InfoItemV3 {
	if x != nil {
		return x.FdDetails
	}
	return nil
}

func (x *SecuredCreditCardFdDetailsScreenOptions) GetNextCta() *deeplink.Cta {
	if x != nil {
		return x.NextCta
	}
	return nil
}

func (x *SecuredCreditCardFdDetailsScreenOptions) GetFdCreationStatus() typesv2.DepositStatus {
	if x != nil {
		return x.FdCreationStatus
	}
	return typesv2.DepositStatus(0)
}

func (x *SecuredCreditCardFdDetailsScreenOptions) GetCardRequestId() string {
	if x != nil {
		return x.CardRequestId
	}
	return ""
}

func (x *SecuredCreditCardFdDetailsScreenOptions) GetVeBankLogo() *common.VisualElement {
	if x != nil {
		return x.VeBankLogo
	}
	return nil
}

type SecuredCcDepositTenureSelectionBottomSheetScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header          *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	HeadingText     *common.Text                               `protobuf:"bytes,2,opt,name=heading_text,json=headingText,proto3" json:"heading_text,omitempty"`
	TenureDaysText  *common.Text                               `protobuf:"bytes,3,opt,name=tenure_days_text,json=tenureDaysText,proto3" json:"tenure_days_text,omitempty"`
	TenureMonthText *common.Text                               `protobuf:"bytes,4,opt,name=tenure_month_text,json=tenureMonthText,proto3" json:"tenure_month_text,omitempty"`
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
	SliderTermDetails       []*typesv2.DepositInterestDetails `protobuf:"bytes,5,rep,name=slider_term_details,json=sliderTermDetails,proto3" json:"slider_term_details,omitempty"`
	SelectedDepositTerm     *typesv2.DepositTerm              `protobuf:"bytes,6,opt,name=selected_deposit_term,json=selectedDepositTerm,proto3" json:"selected_deposit_term,omitempty"`
	ConfirmationCta         *deeplink.Cta                     `protobuf:"bytes,7,opt,name=confirmation_cta,json=confirmationCta,proto3" json:"confirmation_cta,omitempty"`
	MinDepositTermErrorText *common.Text                      `protobuf:"bytes,8,opt,name=min_deposit_term_error_text,json=minDepositTermErrorText,proto3" json:"min_deposit_term_error_text,omitempty"`
	MaxDepositTermErrorText *common.Text                      `protobuf:"bytes,9,opt,name=max_deposit_term_error_text,json=maxDepositTermErrorText,proto3" json:"max_deposit_term_error_text,omitempty"`
	// Needed for ios component reuse, android will also start using this
	DurationSliderValues []*typesv2.DurationSliderValue `protobuf:"bytes,10,rep,name=duration_slider_values,json=durationSliderValues,proto3" json:"duration_slider_values,omitempty"`
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) Reset() {
	*x = SecuredCcDepositTenureSelectionBottomSheetScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecuredCcDepositTenureSelectionBottomSheetScreenOptions) ProtoMessage() {}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecuredCcDepositTenureSelectionBottomSheetScreenOptions.ProtoReflect.Descriptor instead.
func (*SecuredCcDepositTenureSelectionBottomSheetScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{5}
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetHeadingText() *common.Text {
	if x != nil {
		return x.HeadingText
	}
	return nil
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetTenureDaysText() *common.Text {
	if x != nil {
		return x.TenureDaysText
	}
	return nil
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetTenureMonthText() *common.Text {
	if x != nil {
		return x.TenureMonthText
	}
	return nil
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetSliderTermDetails() []*typesv2.DepositInterestDetails {
	if x != nil {
		return x.SliderTermDetails
	}
	return nil
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetSelectedDepositTerm() *typesv2.DepositTerm {
	if x != nil {
		return x.SelectedDepositTerm
	}
	return nil
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetConfirmationCta() *deeplink.Cta {
	if x != nil {
		return x.ConfirmationCta
	}
	return nil
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetMinDepositTermErrorText() *common.Text {
	if x != nil {
		return x.MinDepositTermErrorText
	}
	return nil
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetMaxDepositTermErrorText() *common.Text {
	if x != nil {
		return x.MaxDepositTermErrorText
	}
	return nil
}

func (x *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) GetDurationSliderValues() []*typesv2.DurationSliderValue {
	if x != nil {
		return x.DurationSliderValues
	}
	return nil
}

type SecuredCcDetailsBottomSheetScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header         *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	HeaderDetails  *deeplink.InfoItemV3                       `protobuf:"bytes,2,opt,name=header_details,json=headerDetails,proto3" json:"header_details,omitempty"`
	FeatureDetails []*common.TextWithIcon                     `protobuf:"bytes,3,rep,name=feature_details,json=featureDetails,proto3" json:"feature_details,omitempty"`
	FeatureBgColor string                                     `protobuf:"bytes,4,opt,name=feature_bg_color,json=featureBgColor,proto3" json:"feature_bg_color,omitempty"`
}

func (x *SecuredCcDetailsBottomSheetScreenOptions) Reset() {
	*x = SecuredCcDetailsBottomSheetScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecuredCcDetailsBottomSheetScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecuredCcDetailsBottomSheetScreenOptions) ProtoMessage() {}

func (x *SecuredCcDetailsBottomSheetScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecuredCcDetailsBottomSheetScreenOptions.ProtoReflect.Descriptor instead.
func (*SecuredCcDetailsBottomSheetScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{6}
}

func (x *SecuredCcDetailsBottomSheetScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SecuredCcDetailsBottomSheetScreenOptions) GetHeaderDetails() *deeplink.InfoItemV3 {
	if x != nil {
		return x.HeaderDetails
	}
	return nil
}

func (x *SecuredCcDetailsBottomSheetScreenOptions) GetFeatureDetails() []*common.TextWithIcon {
	if x != nil {
		return x.FeatureDetails
	}
	return nil
}

func (x *SecuredCcDetailsBottomSheetScreenOptions) GetFeatureBgColor() string {
	if x != nil {
		return x.FeatureBgColor
	}
	return ""
}

// Figma link : https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-19690&mode=dev,
// Figma link: https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-19763&mode=dev
type CcIntroBottomSheetScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header           *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Title            *common.Text                               `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle         *common.Text                               `protobuf:"bytes,3,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	BackgroundColour *widget.BackgroundColour                   `protobuf:"bytes,4,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	Promotion        *firefly.PromotionInfo                     `protobuf:"bytes,5,opt,name=promotion,proto3" json:"promotion,omitempty"`
	Button           *firefly.WrappedButtonInfo                 `protobuf:"bytes,6,opt,name=button,proto3" json:"button,omitempty"`
	VisualElement    *common.VisualElement                      `protobuf:"bytes,7,opt,name=visual_element,json=visualElement,proto3" json:"visual_element,omitempty"`
}

func (x *CcIntroBottomSheetScreenOptions) Reset() {
	*x = CcIntroBottomSheetScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcIntroBottomSheetScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcIntroBottomSheetScreenOptions) ProtoMessage() {}

func (x *CcIntroBottomSheetScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcIntroBottomSheetScreenOptions.ProtoReflect.Descriptor instead.
func (*CcIntroBottomSheetScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{7}
}

func (x *CcIntroBottomSheetScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcIntroBottomSheetScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CcIntroBottomSheetScreenOptions) GetSubTitle() *common.Text {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *CcIntroBottomSheetScreenOptions) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *CcIntroBottomSheetScreenOptions) GetPromotion() *firefly.PromotionInfo {
	if x != nil {
		return x.Promotion
	}
	return nil
}

func (x *CcIntroBottomSheetScreenOptions) GetButton() *firefly.WrappedButtonInfo {
	if x != nil {
		return x.Button
	}
	return nil
}

func (x *CcIntroBottomSheetScreenOptions) GetVisualElement() *common.VisualElement {
	if x != nil {
		return x.VisualElement
	}
	return nil
}

// Figma link : https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-20302&mode=dev
type CcIntroScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header           *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ScreenIdentifier int32                                      `protobuf:"varint,2,opt,name=screen_identifier,json=screenIdentifier,proto3" json:"screen_identifier,omitempty"`
	// Deprecated: in favour of typesv2.CreditCardRequestHeader
	CardProgramType enums.CardProgramType `protobuf:"varint,3,opt,name=card_program_type,json=cardProgramType,proto3,enum=frontend.firefly.enums.CardProgramType" json:"card_program_type,omitempty"`
	// Deprecated: in favour of typesv2.CreditCardRequestHeader
	CardProgram             *typesv2.CardProgram             `protobuf:"bytes,4,opt,name=card_program,json=cardProgram,proto3" json:"card_program,omitempty"`
	CreditCardRequestHeader *typesv2.CreditCardRequestHeader `protobuf:"bytes,5,opt,name=credit_card_request_header,json=creditCardRequestHeader,proto3" json:"credit_card_request_header,omitempty"`
}

func (x *CcIntroScreenOptions) Reset() {
	*x = CcIntroScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcIntroScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcIntroScreenOptions) ProtoMessage() {}

func (x *CcIntroScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcIntroScreenOptions.ProtoReflect.Descriptor instead.
func (*CcIntroScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{8}
}

func (x *CcIntroScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcIntroScreenOptions) GetScreenIdentifier() int32 {
	if x != nil {
		return x.ScreenIdentifier
	}
	return 0
}

func (x *CcIntroScreenOptions) GetCardProgramType() enums.CardProgramType {
	if x != nil {
		return x.CardProgramType
	}
	return enums.CardProgramType(0)
}

func (x *CcIntroScreenOptions) GetCardProgram() *typesv2.CardProgram {
	if x != nil {
		return x.CardProgram
	}
	return nil
}

func (x *CcIntroScreenOptions) GetCreditCardRequestHeader() *typesv2.CreditCardRequestHeader {
	if x != nil {
		return x.CreditCardRequestHeader
	}
	return nil
}

type FireflySyncPollStatusScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	CardRequestId string                                     `protobuf:"bytes,2,opt,name=card_request_id,json=cardRequestId,proto3" json:"card_request_id,omitempty"`
	// deprecated in favour of display_message_object
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
	DisplayMessage string `protobuf:"bytes,3,opt,name=display_message,json=displayMessage,proto3" json:"display_message,omitempty"`
	// delay for the next attempt in milliseconds
	RetryDelay int32  `protobuf:"varint,4,opt,name=retry_delay,json=retryDelay,proto3" json:"retry_delay,omitempty"`
	WorkflowId string `protobuf:"bytes,5,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// url of the image that is displayed at the top half of the screen .
	// If this is passed as non-empty, the rest of the screen contents are pushed
	// down a bit
	ScreenImage *common.VisualElement `protobuf:"bytes,6,opt,name=screen_image,json=screenImage,proto3" json:"screen_image,omitempty"`
	// text to be displayed representing the process happening in the backend .
	// eg. Hold on! Your Credit Card is getting created
	DisplayMessageObject *common.Text `protobuf:"bytes,7,opt,name=display_message_object,json=displayMessageObject,proto3" json:"display_message_object,omitempty"`
	// text to be sent conditionally that is displayed below the display message.
	// condition can be anything, eg. unexpected delay while processing
	SubText *common.Text `protobuf:"bytes,8,opt,name=sub_text,json=subText,proto3" json:"sub_text,omitempty"`
	// This will be passed in the deeplink so that the client is able to provide that
	// in the next API call . This is being done to keep a track of the number of
	// polling attempts that have been done
	AttemptNumber int32 `protobuf:"varint,9,opt,name=attempt_number,json=attemptNumber,proto3" json:"attempt_number,omitempty"`
	// background color for the entire screen
	BgColor string `protobuf:"bytes,10,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *FireflySyncPollStatusScreenOptions) Reset() {
	*x = FireflySyncPollStatusScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FireflySyncPollStatusScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FireflySyncPollStatusScreenOptions) ProtoMessage() {}

func (x *FireflySyncPollStatusScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FireflySyncPollStatusScreenOptions.ProtoReflect.Descriptor instead.
func (*FireflySyncPollStatusScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{9}
}

func (x *FireflySyncPollStatusScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *FireflySyncPollStatusScreenOptions) GetCardRequestId() string {
	if x != nil {
		return x.CardRequestId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
func (x *FireflySyncPollStatusScreenOptions) GetDisplayMessage() string {
	if x != nil {
		return x.DisplayMessage
	}
	return ""
}

func (x *FireflySyncPollStatusScreenOptions) GetRetryDelay() int32 {
	if x != nil {
		return x.RetryDelay
	}
	return 0
}

func (x *FireflySyncPollStatusScreenOptions) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *FireflySyncPollStatusScreenOptions) GetScreenImage() *common.VisualElement {
	if x != nil {
		return x.ScreenImage
	}
	return nil
}

func (x *FireflySyncPollStatusScreenOptions) GetDisplayMessageObject() *common.Text {
	if x != nil {
		return x.DisplayMessageObject
	}
	return nil
}

func (x *FireflySyncPollStatusScreenOptions) GetSubText() *common.Text {
	if x != nil {
		return x.SubText
	}
	return nil
}

func (x *FireflySyncPollStatusScreenOptions) GetAttemptNumber() int32 {
	if x != nil {
		return x.AttemptNumber
	}
	return 0
}

func (x *FireflySyncPollStatusScreenOptions) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

type CreditCardSyncPollStatusScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	CardRequestId string                                     `protobuf:"bytes,2,opt,name=card_request_id,json=cardRequestId,proto3" json:"card_request_id,omitempty"`
	WorkflowId    string                                     `protobuf:"bytes,3,opt,name=workflow_id,json=workflowId,proto3" json:"workflow_id,omitempty"`
	// url of the image that is displayed at the top half of the screen .
	// If this is passed as non empty, the rest of the screen contents are pushed
	// down a bit
	ScreenImage *common.VisualElement `protobuf:"bytes,4,opt,name=screen_image,json=screenImage,proto3" json:"screen_image,omitempty"`
	// text to be displayed representing the process happening in the backend .
	// eg. Hold on! Your Credit Card is getting created
	DisplayMessage *common.Text `protobuf:"bytes,5,opt,name=display_message,json=displayMessage,proto3" json:"display_message,omitempty"`
	// text to be sent conditionally that is displayed below the display message.
	// condition can be anything, eg. unexpected delay while processing
	SubText *common.Text `protobuf:"bytes,6,opt,name=sub_text,json=subText,proto3" json:"sub_text,omitempty"`
	// This will be passed in the deeplink so that the client is able to provide that
	// in the next API call . This is being done to keep a track of the number of
	// polling attempts that have been done
	AttemptNumber int32 `protobuf:"varint,7,opt,name=attempt_number,json=attemptNumber,proto3" json:"attempt_number,omitempty"`
	// delay for the next attempt in milliseconds
	RetryDelay int64 `protobuf:"varint,8,opt,name=retry_delay,json=retryDelay,proto3" json:"retry_delay,omitempty"`
}

func (x *CreditCardSyncPollStatusScreenOptions) Reset() {
	*x = CreditCardSyncPollStatusScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardSyncPollStatusScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardSyncPollStatusScreenOptions) ProtoMessage() {}

func (x *CreditCardSyncPollStatusScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardSyncPollStatusScreenOptions.ProtoReflect.Descriptor instead.
func (*CreditCardSyncPollStatusScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{10}
}

func (x *CreditCardSyncPollStatusScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreditCardSyncPollStatusScreenOptions) GetCardRequestId() string {
	if x != nil {
		return x.CardRequestId
	}
	return ""
}

func (x *CreditCardSyncPollStatusScreenOptions) GetWorkflowId() string {
	if x != nil {
		return x.WorkflowId
	}
	return ""
}

func (x *CreditCardSyncPollStatusScreenOptions) GetScreenImage() *common.VisualElement {
	if x != nil {
		return x.ScreenImage
	}
	return nil
}

func (x *CreditCardSyncPollStatusScreenOptions) GetDisplayMessage() *common.Text {
	if x != nil {
		return x.DisplayMessage
	}
	return nil
}

func (x *CreditCardSyncPollStatusScreenOptions) GetSubText() *common.Text {
	if x != nil {
		return x.SubText
	}
	return nil
}

func (x *CreditCardSyncPollStatusScreenOptions) GetAttemptNumber() int32 {
	if x != nil {
		return x.AttemptNumber
	}
	return 0
}

func (x *CreditCardSyncPollStatusScreenOptions) GetRetryDelay() int64 {
	if x != nil {
		return x.RetryDelay
	}
	return 0
}

// Figma: https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=9701-42332&mode=design&t=2EHzNmCp0NDQRrgc-4
type CcAmpliFiScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ToolBar                *firefly.WrappedIconTextToolBar            `protobuf:"bytes,2,opt,name=tool_bar,json=toolBar,proto3" json:"tool_bar,omitempty"`
	Title                  *firefly.WrappedTextInfo                   `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	SubTitle               *firefly.WrappedTextInfo                   `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	AmpliFiCard            *firefly.WrappedAmpliFiCard                `protobuf:"bytes,5,opt,name=ampli_fi_card,json=ampliFiCard,proto3" json:"ampli_fi_card,omitempty"`
	DividerColour          *widget.BackgroundColour                   `protobuf:"bytes,6,opt,name=divider_colour,json=dividerColour,proto3" json:"divider_colour,omitempty"`
	AmpliFiBenefit         *firefly.AmpliFiBenefit                    `protobuf:"bytes,7,opt,name=ampli_fi_benefit,json=ampliFiBenefit,proto3" json:"ampli_fi_benefit,omitempty"`
	TextWithHyperlinks     *firefly.WrappedHyperLinksWidget           `protobuf:"bytes,8,opt,name=text_with_hyperlinks,json=textWithHyperlinks,proto3" json:"text_with_hyperlinks,omitempty"`
	SliderProperties       *firefly.SliderProperties                  `protobuf:"bytes,9,opt,name=slider_properties,json=sliderProperties,proto3" json:"slider_properties,omitempty"`
	FooterIcon             *firefly.WrappedVisualElement              `protobuf:"bytes,10,opt,name=footer_icon,json=footerIcon,proto3" json:"footer_icon,omitempty"`
	ScreenBackgroundColour *firefly.DrawableProperties                `protobuf:"bytes,11,opt,name=screen_background_colour,json=screenBackgroundColour,proto3" json:"screen_background_colour,omitempty"`
	AddressType            typesv2.AddressType                        `protobuf:"varint,12,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.AddressType" json:"address_type,omitempty"`
	KycLevel               typesv2.KYCLevel                           `protobuf:"varint,13,opt,name=kyc_level,json=kycLevel,proto3,enum=api.typesv2.KYCLevel" json:"kyc_level,omitempty"`
	CardRequestId          string                                     `protobuf:"bytes,14,opt,name=card_request_id,json=cardRequestId,proto3" json:"card_request_id,omitempty"`
}

func (x *CcAmpliFiScreenOptions) Reset() {
	*x = CcAmpliFiScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcAmpliFiScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcAmpliFiScreenOptions) ProtoMessage() {}

func (x *CcAmpliFiScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcAmpliFiScreenOptions.ProtoReflect.Descriptor instead.
func (*CcAmpliFiScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{11}
}

func (x *CcAmpliFiScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetToolBar() *firefly.WrappedIconTextToolBar {
	if x != nil {
		return x.ToolBar
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetTitle() *firefly.WrappedTextInfo {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetSubTitle() *firefly.WrappedTextInfo {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetAmpliFiCard() *firefly.WrappedAmpliFiCard {
	if x != nil {
		return x.AmpliFiCard
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetDividerColour() *widget.BackgroundColour {
	if x != nil {
		return x.DividerColour
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetAmpliFiBenefit() *firefly.AmpliFiBenefit {
	if x != nil {
		return x.AmpliFiBenefit
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetTextWithHyperlinks() *firefly.WrappedHyperLinksWidget {
	if x != nil {
		return x.TextWithHyperlinks
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetSliderProperties() *firefly.SliderProperties {
	if x != nil {
		return x.SliderProperties
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetFooterIcon() *firefly.WrappedVisualElement {
	if x != nil {
		return x.FooterIcon
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetScreenBackgroundColour() *firefly.DrawableProperties {
	if x != nil {
		return x.ScreenBackgroundColour
	}
	return nil
}

func (x *CcAmpliFiScreenOptions) GetAddressType() typesv2.AddressType {
	if x != nil {
		return x.AddressType
	}
	return typesv2.AddressType(0)
}

func (x *CcAmpliFiScreenOptions) GetKycLevel() typesv2.KYCLevel {
	if x != nil {
		return x.KycLevel
	}
	return typesv2.KYCLevel(0)
}

func (x *CcAmpliFiScreenOptions) GetCardRequestId() string {
	if x != nil {
		return x.CardRequestId
	}
	return ""
}

// figma: https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=2539-20531&mode=design&t=G1MP0tw5tHn8d1n0-0
type CcIneligibleUserScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                 *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	TopSectionDetails      *deeplink.InfoItemV3                       `protobuf:"bytes,2,opt,name=top_section_details,json=topSectionDetails,proto3" json:"top_section_details,omitempty"`
	SeparatorVisualElement *common.VisualElement                      `protobuf:"bytes,3,opt,name=separator_visual_element,json=separatorVisualElement,proto3" json:"separator_visual_element,omitempty"`
	BottomSectionHeading   *common.Text                               `protobuf:"bytes,4,opt,name=bottom_section_heading,json=bottomSectionHeading,proto3" json:"bottom_section_heading,omitempty"`
	IneligibleUserDetails  []*IneligibleUserDetails                   `protobuf:"bytes,5,rep,name=ineligible_user_details,json=ineligibleUserDetails,proto3" json:"ineligible_user_details,omitempty"`
	FooterText             *common.Text                               `protobuf:"bytes,6,opt,name=footer_text,json=footerText,proto3" json:"footer_text,omitempty"`
	MainCta                *deeplink.Cta                              `protobuf:"bytes,7,opt,name=main_cta,json=mainCta,proto3" json:"main_cta,omitempty"`
}

func (x *CcIneligibleUserScreenOptions) Reset() {
	*x = CcIneligibleUserScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcIneligibleUserScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcIneligibleUserScreenOptions) ProtoMessage() {}

func (x *CcIneligibleUserScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcIneligibleUserScreenOptions.ProtoReflect.Descriptor instead.
func (*CcIneligibleUserScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{12}
}

func (x *CcIneligibleUserScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcIneligibleUserScreenOptions) GetTopSectionDetails() *deeplink.InfoItemV3 {
	if x != nil {
		return x.TopSectionDetails
	}
	return nil
}

func (x *CcIneligibleUserScreenOptions) GetSeparatorVisualElement() *common.VisualElement {
	if x != nil {
		return x.SeparatorVisualElement
	}
	return nil
}

func (x *CcIneligibleUserScreenOptions) GetBottomSectionHeading() *common.Text {
	if x != nil {
		return x.BottomSectionHeading
	}
	return nil
}

func (x *CcIneligibleUserScreenOptions) GetIneligibleUserDetails() []*IneligibleUserDetails {
	if x != nil {
		return x.IneligibleUserDetails
	}
	return nil
}

func (x *CcIneligibleUserScreenOptions) GetFooterText() *common.Text {
	if x != nil {
		return x.FooterText
	}
	return nil
}

func (x *CcIneligibleUserScreenOptions) GetMainCta() *deeplink.Cta {
	if x != nil {
		return x.MainCta
	}
	return nil
}

type IneligibleUserDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BottomSectionDetails *deeplink.InfoItemWithCtaV3 `protobuf:"bytes,1,opt,name=bottom_section_details,json=bottomSectionDetails,proto3" json:"bottom_section_details,omitempty"`
	IsCollapsed          bool                        `protobuf:"varint,2,opt,name=is_collapsed,json=isCollapsed,proto3" json:"is_collapsed,omitempty"`
	Chevron              *common.VisualElement       `protobuf:"bytes,3,opt,name=chevron,proto3" json:"chevron,omitempty"`
}

func (x *IneligibleUserDetails) Reset() {
	*x = IneligibleUserDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IneligibleUserDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IneligibleUserDetails) ProtoMessage() {}

func (x *IneligibleUserDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IneligibleUserDetails.ProtoReflect.Descriptor instead.
func (*IneligibleUserDetails) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{13}
}

func (x *IneligibleUserDetails) GetBottomSectionDetails() *deeplink.InfoItemWithCtaV3 {
	if x != nil {
		return x.BottomSectionDetails
	}
	return nil
}

func (x *IneligibleUserDetails) GetIsCollapsed() bool {
	if x != nil {
		return x.IsCollapsed
	}
	return false
}

func (x *IneligibleUserDetails) GetChevron() *common.VisualElement {
	if x != nil {
		return x.Chevron
	}
	return nil
}

// screen options for CREDIT_CARD_BILLING_DETAILS_BOTTOM_VIEW_SCREEN
type CreditCardBillingDetailsBottomViewScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header        *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ScreenHeading *common.Text                               `protobuf:"bytes,2,opt,name=screen_heading,json=screenHeading,proto3" json:"screen_heading,omitempty"`
	BillingInfos  []*deeplink.InfoItemV2                     `protobuf:"bytes,3,rep,name=billing_infos,json=billingInfos,proto3" json:"billing_infos,omitempty"`
	BgColor       string                                     `protobuf:"bytes,4,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *CreditCardBillingDetailsBottomViewScreenOptions) Reset() {
	*x = CreditCardBillingDetailsBottomViewScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardBillingDetailsBottomViewScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardBillingDetailsBottomViewScreenOptions) ProtoMessage() {}

func (x *CreditCardBillingDetailsBottomViewScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardBillingDetailsBottomViewScreenOptions.ProtoReflect.Descriptor instead.
func (*CreditCardBillingDetailsBottomViewScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{14}
}

func (x *CreditCardBillingDetailsBottomViewScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreditCardBillingDetailsBottomViewScreenOptions) GetScreenHeading() *common.Text {
	if x != nil {
		return x.ScreenHeading
	}
	return nil
}

func (x *CreditCardBillingDetailsBottomViewScreenOptions) GetBillingInfos() []*deeplink.InfoItemV2 {
	if x != nil {
		return x.BillingInfos
	}
	return nil
}

func (x *CreditCardBillingDetailsBottomViewScreenOptions) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

// screen options for CC_BILL_GENERATION_DATE_SELECTION_SCREEN
type CcBillGenerationDateSelectionScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// [OPTIONAL] Card id will be required for cases where the billing info has to be
	// displayed for update after card creation
	CardId string `protobuf:"bytes,2,opt,name=card_id,json=cardId,proto3" json:"card_id,omitempty"`
	// bool to specify if the billing dates selection is for a newly onboarding user or
	// is it an update for an already onboarded user
	IsUpdate bool `protobuf:"varint,3,opt,name=is_update,json=isUpdate,proto3" json:"is_update,omitempty"`
}

func (x *CcBillGenerationDateSelectionScreenOptions) Reset() {
	*x = CcBillGenerationDateSelectionScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcBillGenerationDateSelectionScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcBillGenerationDateSelectionScreenOptions) ProtoMessage() {}

func (x *CcBillGenerationDateSelectionScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcBillGenerationDateSelectionScreenOptions.ProtoReflect.Descriptor instead.
func (*CcBillGenerationDateSelectionScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{15}
}

func (x *CcBillGenerationDateSelectionScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcBillGenerationDateSelectionScreenOptions) GetCardId() string {
	if x != nil {
		return x.CardId
	}
	return ""
}

func (x *CcBillGenerationDateSelectionScreenOptions) GetIsUpdate() bool {
	if x != nil {
		return x.IsUpdate
	}
	return false
}

// screen options for CREDIT_CARD_TPAP_PAYMENT_INIT_SCREEN
type CreditCardTpapPaymentInitScreenOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// attributes containing parameters to initiate a third party application payment flow
	TransactionAttributes *transaction.TransactionAttribute `protobuf:"bytes,2,opt,name=transaction_attributes,json=transactionAttributes,proto3" json:"transaction_attributes,omitempty"`
	// order id for the order which has been created to initiate payment
	OrderId string `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	// type of pin authorization that needs to be done for the payment
	PinRequiredType transaction.PinRequiredType `protobuf:"varint,4,opt,name=pin_required_type,json=pinRequiredType,proto3,enum=frontend.pay.transaction.PinRequiredType" json:"pin_required_type,omitempty"`
}

func (x *CreditCardTpapPaymentInitScreenOption) Reset() {
	*x = CreditCardTpapPaymentInitScreenOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardTpapPaymentInitScreenOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardTpapPaymentInitScreenOption) ProtoMessage() {}

func (x *CreditCardTpapPaymentInitScreenOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardTpapPaymentInitScreenOption.ProtoReflect.Descriptor instead.
func (*CreditCardTpapPaymentInitScreenOption) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{16}
}

func (x *CreditCardTpapPaymentInitScreenOption) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreditCardTpapPaymentInitScreenOption) GetTransactionAttributes() *transaction.TransactionAttribute {
	if x != nil {
		return x.TransactionAttributes
	}
	return nil
}

func (x *CreditCardTpapPaymentInitScreenOption) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *CreditCardTpapPaymentInitScreenOption) GetPinRequiredType() transaction.PinRequiredType {
	if x != nil {
		return x.PinRequiredType
	}
	return transaction.PinRequiredType(0)
}

// screen options for CC_NETWORK_SELECTION_SCREEN
// figma - https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4751-30530&mode=design&t=J4NxEIKvEI7Qj1v3-4
type CcNetworkSelectionScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                    *deeplink_screen_option.ScreenOptionHeader                   `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	HeaderText                *common.Text                                                 `protobuf:"bytes,2,opt,name=header_text,json=headerText,proto3" json:"header_text,omitempty"`
	Description               *common.Text                                                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	NetworkSelectionComponent []*CcNetworkSelectionScreenOptions_NetworkSelectionComponent `protobuf:"bytes,4,rep,name=network_selection_component,json=networkSelectionComponent,proto3" json:"network_selection_component,omitempty"`
	PrimaryCta                *deeplink.Cta                                                `protobuf:"bytes,5,opt,name=primary_cta,json=primaryCta,proto3" json:"primary_cta,omitempty"`
}

func (x *CcNetworkSelectionScreenOptions) Reset() {
	*x = CcNetworkSelectionScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcNetworkSelectionScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcNetworkSelectionScreenOptions) ProtoMessage() {}

func (x *CcNetworkSelectionScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcNetworkSelectionScreenOptions.ProtoReflect.Descriptor instead.
func (*CcNetworkSelectionScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{17}
}

func (x *CcNetworkSelectionScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcNetworkSelectionScreenOptions) GetHeaderText() *common.Text {
	if x != nil {
		return x.HeaderText
	}
	return nil
}

func (x *CcNetworkSelectionScreenOptions) GetDescription() *common.Text {
	if x != nil {
		return x.Description
	}
	return nil
}

func (x *CcNetworkSelectionScreenOptions) GetNetworkSelectionComponent() []*CcNetworkSelectionScreenOptions_NetworkSelectionComponent {
	if x != nil {
		return x.NetworkSelectionComponent
	}
	return nil
}

func (x *CcNetworkSelectionScreenOptions) GetPrimaryCta() *deeplink.Cta {
	if x != nil {
		return x.PrimaryCta
	}
	return nil
}

type CardTabScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// The selected tab
	SelectedTab CardTabScreenOptions_SelectedTab `protobuf:"varint,2,opt,name=selected_tab,json=selectedTab,proto3,enum=api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions_SelectedTab" json:"selected_tab,omitempty"`
	// Create map of SelectedTab and Deeplink
	TabToDeeplink map[string]*deeplink.Deeplink `protobuf:"bytes,3,rep,name=tab_to_deeplink,json=tabToDeeplink,proto3" json:"tab_to_deeplink,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *CardTabScreenOptions) Reset() {
	*x = CardTabScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardTabScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardTabScreenOptions) ProtoMessage() {}

func (x *CardTabScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardTabScreenOptions.ProtoReflect.Descriptor instead.
func (*CardTabScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{18}
}

func (x *CardTabScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CardTabScreenOptions) GetSelectedTab() CardTabScreenOptions_SelectedTab {
	if x != nil {
		return x.SelectedTab
	}
	return CardTabScreenOptions_CREDIT_CARD
}

func (x *CardTabScreenOptions) GetTabToDeeplink() map[string]*deeplink.Deeplink {
	if x != nil {
		return x.TabToDeeplink
	}
	return nil
}

// screen options for CC_CONSENT_BOTTOM_SHEET_SCREEN
// figma : https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-AmpliFi-Credit-Card-%E2%80%A2-FFF?type=design&node-id=6226-23210&mode=design&t=qRSwyCrbsNCWRuU4-4
type CcConsentBottomSheetScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// title for the bottom sheet
	Title *common.Text `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	// list of consent items to be shown in the Consent Bottom Sheet
	ConsentItems []*ConsentItem `protobuf:"bytes,3,rep,name=consent_items,json=consentItems,proto3" json:"consent_items,omitempty"`
	// visual element for the card program at the bottom of the consent items
	ProgramVisualElement *common.VisualElement `protobuf:"bytes,4,opt,name=program_visual_element,json=programVisualElement,proto3" json:"program_visual_element,omitempty"`
	// cta for the consent
	ConsentCta *deeplink.Cta `protobuf:"bytes,5,opt,name=consent_cta,json=consentCta,proto3" json:"consent_cta,omitempty"`
	// container properties for the consent list items
	ConsentContainerProperties *firefly.DrawableProperties `protobuf:"bytes,6,opt,name=consent_container_properties,json=consentContainerProperties,proto3" json:"consent_container_properties,omitempty"`
	// Deprecated in favour of hyper text field : consent_text
	// consent checkbox
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
	ConsentCheckboxText *widget.CheckboxItem `protobuf:"bytes,7,opt,name=consent_checkbox_text,json=consentCheckboxText,proto3" json:"consent_checkbox_text,omitempty"`
	// Deprecated in favour of consent_check_box having CreditCardOnboardingConsent enum
	// consent for credit card onboarding given by user
	//
	// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
	CreditCardOnboardingConsent enums.CreditCardOnboardingConsent `protobuf:"varint,8,opt,name=credit_card_onboarding_consent,json=creditCardOnboardingConsent,proto3,enum=frontend.firefly.enums.CreditCardOnboardingConsent" json:"credit_card_onboarding_consent,omitempty"`
	// checkable consent with consent type
	ConsentCheckBox *firefly.ConsentBox `protobuf:"bytes,9,opt,name=consent_check_box,json=consentCheckBox,proto3" json:"consent_check_box,omitempty"`
	// hyper text consent text
	ConsentText *ui.TextWithHyperlinks `protobuf:"bytes,10,opt,name=consent_text,json=consentText,proto3" json:"consent_text,omitempty"`
}

func (x *CcConsentBottomSheetScreenOptions) Reset() {
	*x = CcConsentBottomSheetScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcConsentBottomSheetScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcConsentBottomSheetScreenOptions) ProtoMessage() {}

func (x *CcConsentBottomSheetScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcConsentBottomSheetScreenOptions.ProtoReflect.Descriptor instead.
func (*CcConsentBottomSheetScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{19}
}

func (x *CcConsentBottomSheetScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcConsentBottomSheetScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CcConsentBottomSheetScreenOptions) GetConsentItems() []*ConsentItem {
	if x != nil {
		return x.ConsentItems
	}
	return nil
}

func (x *CcConsentBottomSheetScreenOptions) GetProgramVisualElement() *common.VisualElement {
	if x != nil {
		return x.ProgramVisualElement
	}
	return nil
}

func (x *CcConsentBottomSheetScreenOptions) GetConsentCta() *deeplink.Cta {
	if x != nil {
		return x.ConsentCta
	}
	return nil
}

func (x *CcConsentBottomSheetScreenOptions) GetConsentContainerProperties() *firefly.DrawableProperties {
	if x != nil {
		return x.ConsentContainerProperties
	}
	return nil
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
func (x *CcConsentBottomSheetScreenOptions) GetConsentCheckboxText() *widget.CheckboxItem {
	if x != nil {
		return x.ConsentCheckboxText
	}
	return nil
}

// Deprecated: Marked as deprecated in api/typesv2/deeplink_screen_option/firefly/screen_options.proto.
func (x *CcConsentBottomSheetScreenOptions) GetCreditCardOnboardingConsent() enums.CreditCardOnboardingConsent {
	if x != nil {
		return x.CreditCardOnboardingConsent
	}
	return enums.CreditCardOnboardingConsent(0)
}

func (x *CcConsentBottomSheetScreenOptions) GetConsentCheckBox() *firefly.ConsentBox {
	if x != nil {
		return x.ConsentCheckBox
	}
	return nil
}

func (x *CcConsentBottomSheetScreenOptions) GetConsentText() *ui.TextWithHyperlinks {
	if x != nil {
		return x.ConsentText
	}
	return nil
}

// message to contain the data for the consent items being rendered on the consent bottom sheet
type ConsentItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title for the consent item
	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// sub title text for the bottom sheet, hyperlinks to support showing links in the sub title
	SubTitle *ui.TextWithHyperlinks `protobuf:"bytes,2,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// left icon for the consent item
	LeftVisualElement *common.VisualElement `protobuf:"bytes,3,opt,name=left_visual_element,json=leftVisualElement,proto3" json:"left_visual_element,omitempty"`
	// bottom icon to show the welcome offer banner for the items
	BottomVisualElement *common.VisualElement `protobuf:"bytes,4,opt,name=bottom_visual_element,json=bottomVisualElement,proto3" json:"bottom_visual_element,omitempty"`
}

func (x *ConsentItem) Reset() {
	*x = ConsentItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConsentItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConsentItem) ProtoMessage() {}

func (x *ConsentItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConsentItem.ProtoReflect.Descriptor instead.
func (*ConsentItem) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{20}
}

func (x *ConsentItem) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *ConsentItem) GetSubTitle() *ui.TextWithHyperlinks {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *ConsentItem) GetLeftVisualElement() *common.VisualElement {
	if x != nil {
		return x.LeftVisualElement
	}
	return nil
}

func (x *ConsentItem) GetBottomVisualElement() *common.VisualElement {
	if x != nil {
		return x.BottomVisualElement
	}
	return nil
}

type CcCreditReportAddressScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header                       *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	ToolBar                      *firefly.WrappedIconTextToolBar            `protobuf:"bytes,2,opt,name=tool_bar,json=toolBar,proto3" json:"tool_bar,omitempty"`
	TopSection                   *deeplink.InfoItemV3                       `protobuf:"bytes,3,opt,name=top_section,json=topSection,proto3" json:"top_section,omitempty"`
	AddressSectionProperties     *firefly.DrawableProperties                `protobuf:"bytes,4,opt,name=address_section_properties,json=addressSectionProperties,proto3" json:"address_section_properties,omitempty"`
	ContinueCta                  *deeplink.Cta                              `protobuf:"bytes,5,opt,name=continue_cta,json=continueCta,proto3" json:"continue_cta,omitempty"`
	BackgroundColor              *widget.BackgroundColour                   `protobuf:"bytes,6,opt,name=background_color,json=backgroundColor,proto3" json:"background_color,omitempty"`
	AddAddressCta                *ui.IconTextComponent                      `protobuf:"bytes,7,opt,name=add_address_cta,json=addAddressCta,proto3" json:"add_address_cta,omitempty"`
	CardRequestId                string                                     `protobuf:"bytes,8,opt,name=card_request_id,json=cardRequestId,proto3" json:"card_request_id,omitempty"`
	IsLocationPermissionRequired bool                                       `protobuf:"varint,9,opt,name=is_location_permission_required,json=isLocationPermissionRequired,proto3" json:"is_location_permission_required,omitempty"`
}

func (x *CcCreditReportAddressScreenOptions) Reset() {
	*x = CcCreditReportAddressScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcCreditReportAddressScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcCreditReportAddressScreenOptions) ProtoMessage() {}

func (x *CcCreditReportAddressScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcCreditReportAddressScreenOptions.ProtoReflect.Descriptor instead.
func (*CcCreditReportAddressScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{21}
}

func (x *CcCreditReportAddressScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcCreditReportAddressScreenOptions) GetToolBar() *firefly.WrappedIconTextToolBar {
	if x != nil {
		return x.ToolBar
	}
	return nil
}

func (x *CcCreditReportAddressScreenOptions) GetTopSection() *deeplink.InfoItemV3 {
	if x != nil {
		return x.TopSection
	}
	return nil
}

func (x *CcCreditReportAddressScreenOptions) GetAddressSectionProperties() *firefly.DrawableProperties {
	if x != nil {
		return x.AddressSectionProperties
	}
	return nil
}

func (x *CcCreditReportAddressScreenOptions) GetContinueCta() *deeplink.Cta {
	if x != nil {
		return x.ContinueCta
	}
	return nil
}

func (x *CcCreditReportAddressScreenOptions) GetBackgroundColor() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColor
	}
	return nil
}

func (x *CcCreditReportAddressScreenOptions) GetAddAddressCta() *ui.IconTextComponent {
	if x != nil {
		return x.AddAddressCta
	}
	return nil
}

func (x *CcCreditReportAddressScreenOptions) GetCardRequestId() string {
	if x != nil {
		return x.CardRequestId
	}
	return ""
}

func (x *CcCreditReportAddressScreenOptions) GetIsLocationPermissionRequired() bool {
	if x != nil {
		return x.IsLocationPermissionRequired
	}
	return false
}

// screen options for CC_USER_INELIGIBLE_TRANSITION_SCREEN
// figma - https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4810-54748&mode=design&t=s16D6IUkCTgMlDIc-4
type CcUserIneligibleTransitionScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// top visual element for the screen
	ScreenHeaderVisualElement *common.VisualElement `protobuf:"bytes,2,opt,name=screen_header_visual_element,json=screenHeaderVisualElement,proto3" json:"screen_header_visual_element,omitempty"`
	// screen title
	Title *common.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// screen subtitle
	SubTitle *ui.TextWithHyperlinks `protobuf:"bytes,4,opt,name=sub_title,json=subTitle,proto3" json:"sub_title,omitempty"`
	// info text to be shown as title before card image
	InfoText *common.Text `protobuf:"bytes,5,opt,name=info_text,json=infoText,proto3" json:"info_text,omitempty"`
	// visual element to show the eligible card image
	InfoVisualElement *common.VisualElement `protobuf:"bytes,6,opt,name=info_visual_element,json=infoVisualElement,proto3" json:"info_visual_element,omitempty"`
	// cta to trigger share feedback Cta
	ShareFeedbackCta *deeplink.Cta `protobuf:"bytes,7,opt,name=share_feedback_cta,json=shareFeedbackCta,proto3" json:"share_feedback_cta,omitempty"`
	// cta to navigate to the eligible card intro screen
	GetCardCta *deeplink.Cta `protobuf:"bytes,8,opt,name=get_card_cta,json=getCardCta,proto3" json:"get_card_cta,omitempty"`
	// toolbar to be rendered on the top of the screen
	ToolBar *firefly.WrappedIconTextToolBar `protobuf:"bytes,9,opt,name=tool_bar,json=toolBar,proto3" json:"tool_bar,omitempty"`
}

func (x *CcUserIneligibleTransitionScreenOptions) Reset() {
	*x = CcUserIneligibleTransitionScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcUserIneligibleTransitionScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcUserIneligibleTransitionScreenOptions) ProtoMessage() {}

func (x *CcUserIneligibleTransitionScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcUserIneligibleTransitionScreenOptions.ProtoReflect.Descriptor instead.
func (*CcUserIneligibleTransitionScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{22}
}

func (x *CcUserIneligibleTransitionScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcUserIneligibleTransitionScreenOptions) GetScreenHeaderVisualElement() *common.VisualElement {
	if x != nil {
		return x.ScreenHeaderVisualElement
	}
	return nil
}

func (x *CcUserIneligibleTransitionScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CcUserIneligibleTransitionScreenOptions) GetSubTitle() *ui.TextWithHyperlinks {
	if x != nil {
		return x.SubTitle
	}
	return nil
}

func (x *CcUserIneligibleTransitionScreenOptions) GetInfoText() *common.Text {
	if x != nil {
		return x.InfoText
	}
	return nil
}

func (x *CcUserIneligibleTransitionScreenOptions) GetInfoVisualElement() *common.VisualElement {
	if x != nil {
		return x.InfoVisualElement
	}
	return nil
}

func (x *CcUserIneligibleTransitionScreenOptions) GetShareFeedbackCta() *deeplink.Cta {
	if x != nil {
		return x.ShareFeedbackCta
	}
	return nil
}

func (x *CcUserIneligibleTransitionScreenOptions) GetGetCardCta() *deeplink.Cta {
	if x != nil {
		return x.GetCardCta
	}
	return nil
}

func (x *CcUserIneligibleTransitionScreenOptions) GetToolBar() *firefly.WrappedIconTextToolBar {
	if x != nil {
		return x.ToolBar
	}
	return nil
}

// screen options for CC_CREDIT_LIMIT_UPDATE_SCREEN
// figma - https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=41507-40335&mode=design&t=8IAddUkbmT8aV2k8-4
type CCCreditLimitUpdateScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// background colour of the screen
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,2,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// visual element of top section
	TopVisualElement *common.VisualElement `protobuf:"bytes,3,opt,name=top_visual_element,json=topVisualElement,proto3" json:"top_visual_element,omitempty"`
	// screen title
	Title *common.Text `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	// screen subtitle
	Subtitle *common.Text `protobuf:"bytes,5,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// credit limit related info
	CreditLimitInfo *ui.VerticalIconTextComponent `protobuf:"bytes,6,opt,name=credit_limit_info,json=creditLimitInfo,proto3" json:"credit_limit_info,omitempty"`
	// primary cta to continue onboarding flow
	PrimaryCta *deeplink.Cta `protobuf:"bytes,7,opt,name=primary_cta,json=primaryCta,proto3" json:"primary_cta,omitempty"`
	// secondary cta
	SecondaryCta *ui.IconTextComponent `protobuf:"bytes,8,opt,name=secondary_cta,json=secondaryCta,proto3" json:"secondary_cta,omitempty"`
	// card request id for corresponding cc onboarding workflow for which this screen being used
	CardRequestId string `protobuf:"bytes,9,opt,name=card_request_id,json=cardRequestId,proto3" json:"card_request_id,omitempty"`
}

func (x *CCCreditLimitUpdateScreenOptions) Reset() {
	*x = CCCreditLimitUpdateScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CCCreditLimitUpdateScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CCCreditLimitUpdateScreenOptions) ProtoMessage() {}

func (x *CCCreditLimitUpdateScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CCCreditLimitUpdateScreenOptions.ProtoReflect.Descriptor instead.
func (*CCCreditLimitUpdateScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{23}
}

func (x *CCCreditLimitUpdateScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CCCreditLimitUpdateScreenOptions) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *CCCreditLimitUpdateScreenOptions) GetTopVisualElement() *common.VisualElement {
	if x != nil {
		return x.TopVisualElement
	}
	return nil
}

func (x *CCCreditLimitUpdateScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CCCreditLimitUpdateScreenOptions) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *CCCreditLimitUpdateScreenOptions) GetCreditLimitInfo() *ui.VerticalIconTextComponent {
	if x != nil {
		return x.CreditLimitInfo
	}
	return nil
}

func (x *CCCreditLimitUpdateScreenOptions) GetPrimaryCta() *deeplink.Cta {
	if x != nil {
		return x.PrimaryCta
	}
	return nil
}

func (x *CCCreditLimitUpdateScreenOptions) GetSecondaryCta() *ui.IconTextComponent {
	if x != nil {
		return x.SecondaryCta
	}
	return nil
}

func (x *CCCreditLimitUpdateScreenOptions) GetCardRequestId() string {
	if x != nil {
		return x.CardRequestId
	}
	return ""
}

// screen options for CC_CARD_TNC_CONSENT_SCREEN
// figma - https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=40754-7114&mode=design&t=8IAddUkbmT8aV2k8-4
type CCCardTnCConsentScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// background colour of the screen
	BackgroundColour *widget.BackgroundColour `protobuf:"bytes,2,opt,name=background_colour,json=backgroundColour,proto3" json:"background_colour,omitempty"`
	// screen title
	Title *common.Text `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// screen subtitle
	Subtitle *common.Text `protobuf:"bytes,4,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// property like background colour and corner radius
	CardBenefitsContainerProperty *firefly.DrawableProperties `protobuf:"bytes,5,opt,name=card_benefits_container_property,json=cardBenefitsContainerProperty,proto3" json:"card_benefits_container_property,omitempty"`
	// array containing card benefits
	CardBenefits []*deeplink.InfoItemV2 `protobuf:"bytes,6,rep,name=card_benefits,json=cardBenefits,proto3" json:"card_benefits,omitempty"`
	// cta to continue onboarding flow
	Cta *deeplink.Cta `protobuf:"bytes,7,opt,name=cta,proto3" json:"cta,omitempty"`
	// consent check box status
	ConsentCheckBox *firefly.ConsentBox `protobuf:"bytes,8,opt,name=consent_check_box,json=consentCheckBox,proto3" json:"consent_check_box,omitempty"`
	// consent text with tnc links
	ConsentText *ui.TextWithHyperlinks `protobuf:"bytes,9,opt,name=consent_text,json=consentText,proto3" json:"consent_text,omitempty"`
	// partner icon to be displayed in the footer area
	BottomVisualElement *common.VisualElement `protobuf:"bytes,10,opt,name=bottom_visual_element,json=bottomVisualElement,proto3" json:"bottom_visual_element,omitempty"`
	// card request id for corresponding cc onboarding workflow for which this screen being used
	CardRequestId string `protobuf:"bytes,11,opt,name=card_request_id,json=cardRequestId,proto3" json:"card_request_id,omitempty"`
}

func (x *CCCardTnCConsentScreenOptions) Reset() {
	*x = CCCardTnCConsentScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CCCardTnCConsentScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CCCardTnCConsentScreenOptions) ProtoMessage() {}

func (x *CCCardTnCConsentScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CCCardTnCConsentScreenOptions.ProtoReflect.Descriptor instead.
func (*CCCardTnCConsentScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{24}
}

func (x *CCCardTnCConsentScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetBackgroundColour() *widget.BackgroundColour {
	if x != nil {
		return x.BackgroundColour
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetSubtitle() *common.Text {
	if x != nil {
		return x.Subtitle
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetCardBenefitsContainerProperty() *firefly.DrawableProperties {
	if x != nil {
		return x.CardBenefitsContainerProperty
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetCardBenefits() []*deeplink.InfoItemV2 {
	if x != nil {
		return x.CardBenefits
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetCta() *deeplink.Cta {
	if x != nil {
		return x.Cta
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetConsentCheckBox() *firefly.ConsentBox {
	if x != nil {
		return x.ConsentCheckBox
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetConsentText() *ui.TextWithHyperlinks {
	if x != nil {
		return x.ConsentText
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetBottomVisualElement() *common.VisualElement {
	if x != nil {
		return x.BottomVisualElement
	}
	return nil
}

func (x *CCCardTnCConsentScreenOptions) GetCardRequestId() string {
	if x != nil {
		return x.CardRequestId
	}
	return ""
}

// Provide the deeplink pay load for generic screen
type GenericScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Unique screen identifier, mapped with deeplink screen ordinal
	ScreenId int32 `protobuf:"varint,2,opt,name=screen_id,json=screenId,proto3" json:"screen_id,omitempty"`
	// Use this field to pass any additional data required for the screen
	// For example, we can pass the offer id to display the offer details
	// In client side will send this back to server in "Frontend_Firefly_GetGenericScreenRequest"
	Metadata []byte `protobuf:"bytes,4,opt,name=metadata,proto3" json:"metadata,omitempty"`
}

func (x *GenericScreenOptions) Reset() {
	*x = GenericScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenericScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenericScreenOptions) ProtoMessage() {}

func (x *GenericScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenericScreenOptions.ProtoReflect.Descriptor instead.
func (*GenericScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{25}
}

func (x *GenericScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GenericScreenOptions) GetScreenId() int32 {
	if x != nil {
		return x.ScreenId
	}
	return 0
}

func (x *GenericScreenOptions) GetMetadata() []byte {
	if x != nil {
		return x.Metadata
	}
	return nil
}

type CcIntroScreenV2ScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header          *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoaderAnimation *common.VisualElement                      `protobuf:"bytes,2,opt,name=loader_animation,json=loaderAnimation,proto3" json:"loader_animation,omitempty"`
	// marshalled string of api/typesv2/deeplink_screen_options/firefly/screen_options.proto -> CreditCardMetadata
	Metadata                string                           `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	BgImage                 *common.VisualElement            `protobuf:"bytes,4,opt,name=bg_image,json=bgImage,proto3" json:"bg_image,omitempty"`
	CreditCardRequestHeader *typesv2.CreditCardRequestHeader `protobuf:"bytes,5,opt,name=credit_card_request_header,json=creditCardRequestHeader,proto3" json:"credit_card_request_header,omitempty"`
}

func (x *CcIntroScreenV2ScreenOptions) Reset() {
	*x = CcIntroScreenV2ScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcIntroScreenV2ScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcIntroScreenV2ScreenOptions) ProtoMessage() {}

func (x *CcIntroScreenV2ScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcIntroScreenV2ScreenOptions.ProtoReflect.Descriptor instead.
func (*CcIntroScreenV2ScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{26}
}

func (x *CcIntroScreenV2ScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CcIntroScreenV2ScreenOptions) GetLoaderAnimation() *common.VisualElement {
	if x != nil {
		return x.LoaderAnimation
	}
	return nil
}

func (x *CcIntroScreenV2ScreenOptions) GetMetadata() string {
	if x != nil {
		return x.Metadata
	}
	return ""
}

func (x *CcIntroScreenV2ScreenOptions) GetBgImage() *common.VisualElement {
	if x != nil {
		return x.BgImage
	}
	return nil
}

func (x *CcIntroScreenV2ScreenOptions) GetCreditCardRequestHeader() *typesv2.CreditCardRequestHeader {
	if x != nil {
		return x.CreditCardRequestHeader
	}
	return nil
}

type CreditCardMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Metadata:
	//
	//	*CreditCardMetadata_IntroScreenV2Metadata
	Metadata isCreditCardMetadata_Metadata `protobuf_oneof:"metadata"`
}

func (x *CreditCardMetadata) Reset() {
	*x = CreditCardMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardMetadata) ProtoMessage() {}

func (x *CreditCardMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardMetadata.ProtoReflect.Descriptor instead.
func (*CreditCardMetadata) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{27}
}

func (m *CreditCardMetadata) GetMetadata() isCreditCardMetadata_Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (x *CreditCardMetadata) GetIntroScreenV2Metadata() *IntroScreenV2Metadata {
	if x, ok := x.GetMetadata().(*CreditCardMetadata_IntroScreenV2Metadata); ok {
		return x.IntroScreenV2Metadata
	}
	return nil
}

type isCreditCardMetadata_Metadata interface {
	isCreditCardMetadata_Metadata()
}

type CreditCardMetadata_IntroScreenV2Metadata struct {
	IntroScreenV2Metadata *IntroScreenV2Metadata `protobuf:"bytes,1,opt,name=intro_screen_v2_metadata,json=introScreenV2Metadata,proto3,oneof"`
}

func (*CreditCardMetadata_IntroScreenV2Metadata) isCreditCardMetadata_Metadata() {}

type IntroScreenV2Metadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// card state if card is already issued, will be unspecified if card is not issued
	// firefly.v2.enums.CardState.String()
	CardState string `protobuf:"bytes,1,opt,name=card_state,json=cardState,proto3" json:"card_state,omitempty"`
	// card onboarding status
	// firefly.v2.enums.CardRequestStatus.String()
	OnboardingRequestStatus string `protobuf:"bytes,2,opt,name=onboarding_request_status,json=onboardingRequestStatus,proto3" json:"onboarding_request_status,omitempty"`
	// result from CC eligibility check,
	// this will be true when the user is not already onboarded and is eligible for it.
	IsUserCcEligible bool `protobuf:"varint,3,opt,name=is_user_cc_eligible,json=isUserCcEligible,proto3" json:"is_user_cc_eligible,omitempty"`
}

func (x *IntroScreenV2Metadata) Reset() {
	*x = IntroScreenV2Metadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IntroScreenV2Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntroScreenV2Metadata) ProtoMessage() {}

func (x *IntroScreenV2Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntroScreenV2Metadata.ProtoReflect.Descriptor instead.
func (*IntroScreenV2Metadata) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{28}
}

func (x *IntroScreenV2Metadata) GetCardState() string {
	if x != nil {
		return x.CardState
	}
	return ""
}

func (x *IntroScreenV2Metadata) GetOnboardingRequestStatus() string {
	if x != nil {
		return x.OnboardingRequestStatus
	}
	return ""
}

func (x *IntroScreenV2Metadata) GetIsUserCcEligible() bool {
	if x != nil {
		return x.IsUserCcEligible
	}
	return false
}

type CcNetworkSelectionScreenOptions_NetworkSelectionComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	VerticalIconTextComponent *ui.VerticalIconTextComponent `protobuf:"bytes,1,opt,name=vertical_icon_text_component,json=verticalIconTextComponent,proto3" json:"vertical_icon_text_component,omitempty"`
	CardNetworkType           enums.CardNetworkType         `protobuf:"varint,2,opt,name=card_network_type,json=cardNetworkType,proto3,enum=frontend.firefly.enums.CardNetworkType" json:"card_network_type,omitempty"`
}

func (x *CcNetworkSelectionScreenOptions_NetworkSelectionComponent) Reset() {
	*x = CcNetworkSelectionScreenOptions_NetworkSelectionComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CcNetworkSelectionScreenOptions_NetworkSelectionComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CcNetworkSelectionScreenOptions_NetworkSelectionComponent) ProtoMessage() {}

func (x *CcNetworkSelectionScreenOptions_NetworkSelectionComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CcNetworkSelectionScreenOptions_NetworkSelectionComponent.ProtoReflect.Descriptor instead.
func (*CcNetworkSelectionScreenOptions_NetworkSelectionComponent) Descriptor() ([]byte, []int) {
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP(), []int{17, 0}
}

func (x *CcNetworkSelectionScreenOptions_NetworkSelectionComponent) GetVerticalIconTextComponent() *ui.VerticalIconTextComponent {
	if x != nil {
		return x.VerticalIconTextComponent
	}
	return nil
}

func (x *CcNetworkSelectionScreenOptions_NetworkSelectionComponent) GetCardNetworkType() enums.CardNetworkType {
	if x != nil {
		return x.CardNetworkType
	}
	return enums.CardNetworkType(0)
}

var File_api_typesv2_deeplink_screen_option_firefly_screen_options_proto protoreflect.FileDescriptor

var file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x2a, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x1a, 0x24, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x64, 0x72, 0x61, 0x77,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x70, 0x61, 0x79, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x6e,
	0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75,
	0x69, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x68, 0x79, 0x70, 0x65,
	0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65,
	0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x06, 0x0a, 0x29, 0x43,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x41, 0x6e, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x28, 0x0a, 0x10, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x63, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x74, 0x72, 0x12, 0x3a, 0x0a, 0x06, 0x62,
	0x72, 0x61, 0x6e, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e,
	0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x43, 0x74, 0x61, 0x52,
	0x06, 0x62, 0x72, 0x61, 0x6e, 0x64, 0x73, 0x12, 0x55, 0x0a, 0x14, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x62, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x68, 0x65, 0x61, 0x74, 0x73, 0x68, 0x65, 0x65, 0x74, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74,
	0x65, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x43, 0x74, 0x61, 0x52, 0x13, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x62, 0x61, 0x63, 0x6b, 0x43, 0x68, 0x65, 0x61, 0x74, 0x73, 0x68, 0x65, 0x65, 0x74, 0x12, 0x4d,
	0x0a, 0x10, 0x77, 0x65, 0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x76, 0x6f, 0x75, 0x63, 0x68, 0x65,
	0x72, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66,
	0x6f, 0x49, 0x74, 0x65, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x43, 0x74, 0x61, 0x52, 0x0f, 0x77, 0x65,
	0x6c, 0x63, 0x6f, 0x6d, 0x65, 0x56, 0x6f, 0x75, 0x63, 0x68, 0x65, 0x72, 0x73, 0x12, 0x40, 0x0a,
	0x0d, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x08,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12,
	0x50, 0x0a, 0x11, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49,
	0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x43, 0x74, 0x61, 0x56, 0x32,
	0x52, 0x0f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x2c, 0x0a, 0x05, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x05, 0x73, 0x68, 0x61, 0x72, 0x65, 0x12,
	0x24, 0x0a, 0x0e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x73, 0x68, 0x61, 0x72,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x74, 0x65, 0x78, 0x74, 0x46, 0x6f, 0x72,
	0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x4f, 0x0a, 0x17, 0x73, 0x68, 0x61, 0x72, 0x65, 0x5f, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x14, 0x73, 0x68, 0x61, 0x72, 0x65, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x54, 0x65, 0x78, 0x74, 0x22, 0x8d, 0x06, 0x0a, 0x29, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x32, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x53, 0x74, 0x65, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0b, 0x63, 0x6f,
	0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x43, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49,
	0x64, 0x12, 0x32, 0x0a, 0x09, 0x6b, 0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x4b, 0x59, 0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x08, 0x6b, 0x79, 0x63,
	0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x44, 0x0a, 0x0e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x33, 0x52, 0x0d, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x51, 0x0a, 0x12, 0x61,
	0x64, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x74, 0x61, 0x5f, 0x76,
	0x32, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f,
	0x49, 0x74, 0x65, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x43, 0x74, 0x61, 0x56, 0x33, 0x52, 0x0f, 0x61,
	0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x74, 0x61, 0x56, 0x32, 0x12, 0x16,
	0x0a, 0x04, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x04, 0x74, 0x65, 0x78, 0x74, 0x12, 0x1d, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x73, 0x75,
	0x62, 0x54, 0x65, 0x78, 0x74, 0x12, 0x3d, 0x0a, 0x19, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75,
	0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x16, 0x64, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x49, 0x63, 0x6f,
	0x6e, 0x55, 0x72, 0x6c, 0x12, 0x50, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x43, 0x74,
	0x61, 0x56, 0x32, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x43, 0x74, 0x61, 0x12, 0x5e, 0x0a, 0x1a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x17, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0xc0, 0x03, 0x0a, 0x34, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x49, 0x6e, 0x74,
	0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x3e, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x70, 0x12,
	0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x35, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75,
	0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x4b, 0x0a, 0x16, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x73, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x74, 0x61,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x13,
	0x63, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x53, 0x63, 0x6f, 0x72, 0x65,
	0x43, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x0c, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x62, 0x6f, 0x74,
	0x74, 0x6f, 0x6d, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x22, 0xf8, 0x0c, 0x0a, 0x25, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x54, 0x0a,
	0x13, 0x74, 0x6f, 0x70, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49,
	0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x43, 0x74, 0x61, 0x56, 0x32,
	0x52, 0x11, 0x74, 0x6f, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x40, 0x0a, 0x12, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e, 0x0a, 0x0b, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49,
	0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x32, 0x52, 0x0a, 0x62, 0x6f, 0x74, 0x74, 0x6f,
	0x6d, 0x54, 0x65, 0x78, 0x74, 0x12, 0x50, 0x0a, 0x18, 0x69, 0x6e, 0x73, 0x75, 0x66, 0x66, 0x69,
	0x63, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x74,
	0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52,
	0x16, 0x69, 0x6e, 0x73, 0x75, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x74, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x43, 0x74, 0x61, 0x12, 0x40, 0x0a, 0x10, 0x6f, 0x70, 0x65, 0x6e, 0x5f,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0e, 0x6f, 0x70, 0x65, 0x6e, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x43, 0x74, 0x61, 0x12, 0x4e, 0x0a, 0x16, 0x69, 0x6e, 0x65,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x14, 0x69, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x12, 0x3e, 0x0a, 0x0f, 0x61, 0x64, 0x64,
	0x5f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x4e,
	0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x43, 0x74, 0x61, 0x12, 0x5e, 0x0a, 0x1a, 0x61, 0x64, 0x64,
	0x5f, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x52, 0x17, 0x61, 0x64, 0x64, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x53, 0x0a, 0x19, 0x74, 0x65, 0x72,
	0x6d, 0x73, 0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x16, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x41, 0x6e, 0x64,
	0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x54, 0x65, 0x78, 0x74, 0x12, 0x44,
	0x0a, 0x14, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x12, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x4a, 0x0a, 0x17, 0x73, 0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x73, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x73, 0x75, 0x67, 0x67, 0x65,
	0x73, 0x74, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x73,
	0x12, 0x4a, 0x0a, 0x14, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x12, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x4c, 0x69, 0x6d, 0x69, 0x74, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x4c, 0x0a, 0x15,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x13, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x40, 0x0a, 0x12, 0x6d, 0x61,
	0x78, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x6d, 0x61, 0x78, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x10,
	0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x0e, 0x6d, 0x69, 0x6e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x42, 0x0a, 0x10, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x6d, 0x61, 0x78, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x54, 0x65, 0x78, 0x74, 0x12, 0x5b, 0x0a, 0x19, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x74, 0x6f, 0x6f,
	0x6c, 0x74, 0x69, 0x70, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x13, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f,
	0x54, 0x6f, 0x6f, 0x6c, 0x54, 0x69, 0x70, 0x56, 0x32, 0x52, 0x16, 0x69, 0x6e, 0x66, 0x6f, 0x54,
	0x6f, 0x6f, 0x6c, 0x74, 0x69, 0x70, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6d, 0x69,
	0x74, 0x12, 0x5d, 0x0a, 0x1a, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x74, 0x6f, 0x6f, 0x6c, 0x74, 0x69,
	0x70, 0x5f, 0x66, 0x69, 0x78, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x54, 0x6f,
	0x6f, 0x6c, 0x54, 0x69, 0x70, 0x56, 0x32, 0x52, 0x17, 0x69, 0x6e, 0x66, 0x6f, 0x54, 0x6f, 0x6f,
	0x6c, 0x74, 0x69, 0x70, 0x46, 0x69, 0x78, 0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x12, 0x62, 0x0a, 0x1b, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x76, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x74, 0x61, 0x18,
	0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x74, 0x61, 0x52, 0x18, 0x62, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x43, 0x74, 0x61, 0x22, 0xa1, 0x04, 0x0a, 0x27, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x64,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x46, 0x64, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x38, 0x0a, 0x09, 0x73, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x53, 0x74, 0x65, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x73, 0x74, 0x65, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x44, 0x0a, 0x0e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x56,
	0x33, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x3c, 0x0a, 0x0a, 0x66, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65,
	0x6d, 0x56, 0x33, 0x52, 0x09, 0x66, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x31,
	0x0a, 0x08, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x07, 0x6e, 0x65, 0x78, 0x74, 0x43, 0x74,
	0x61, 0x12, 0x48, 0x0a, 0x12, 0x66, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x66, 0x64, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0c, 0x76, 0x65, 0x5f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6c,
	0x6f, 0x67, 0x6f, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0a, 0x76, 0x65,
	0x42, 0x61, 0x6e, 0x6b, 0x4c, 0x6f, 0x67, 0x6f, 0x22, 0xc2, 0x06, 0x0a, 0x37, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x65, 0x64, 0x43, 0x63, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x65, 0x6e,
	0x75, 0x72, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x6f, 0x74, 0x74,
	0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x0c, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x78,
	0x74, 0x12, 0x42, 0x0a, 0x10, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x79, 0x73,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x44, 0x61, 0x79,
	0x73, 0x54, 0x65, 0x78, 0x74, 0x12, 0x44, 0x0a, 0x11, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f,
	0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0f, 0x74, 0x65, 0x6e, 0x75,
	0x72, 0x65, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x54, 0x65, 0x78, 0x74, 0x12, 0x57, 0x0a, 0x13, 0x73,
	0x6c, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x11, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x54, 0x65, 0x72, 0x6d, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x15, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x52, 0x13, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x65,
	0x72, 0x6d, 0x12, 0x41, 0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x43, 0x74, 0x61, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x74, 0x61, 0x12, 0x56, 0x0a, 0x1b, 0x6d, 0x69, 0x6e, 0x5f, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x17, 0x6d, 0x69, 0x6e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x54, 0x65, 0x72, 0x6d, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x54, 0x65, 0x78, 0x74, 0x12, 0x56, 0x0a,
	0x1b, 0x6d, 0x61, 0x78, 0x5f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x5f, 0x74, 0x65, 0x72,
	0x6d, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x17, 0x6d, 0x61,
	0x78, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x54, 0x65, 0x72, 0x6d, 0x45, 0x72, 0x72, 0x6f,
	0x72, 0x54, 0x65, 0x78, 0x74, 0x12, 0x56, 0x0a, 0x16, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18,
	0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x6c, 0x69, 0x64,
	0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x14, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xb5, 0x02,
	0x0a, 0x28, 0x53, 0x65, 0x63, 0x75, 0x72, 0x65, 0x64, 0x43, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x44, 0x0a, 0x0e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x56,
	0x33, 0x52, 0x0d, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x49, 0x0a, 0x0f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x57, 0x69, 0x74, 0x68, 0x49, 0x63, 0x6f, 0x6e, 0x52, 0x0e, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xfb, 0x03, 0x0a, 0x1f, 0x43, 0x63, 0x49, 0x6e, 0x74, 0x72,
	0x6f, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x35, 0x0a, 0x09, 0x73, 0x75, 0x62,
	0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x3d, 0x0a,
	0x09, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x50, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x6d, 0x6f, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x06,
	0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x42, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x06, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x0e, 0x76, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x22, 0x88, 0x03, 0x0a, 0x14, 0x43, 0x63, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2b, 0x0a, 0x11,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x10, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x53, 0x0a, 0x11, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61,
	0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b,
	0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b,
	0x63, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x61, 0x0a, 0x1a, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x17, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x98,
	0x04, 0x0a, 0x22, 0x46, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x6f,
	0x6c, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x63, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x2b, 0x0a,
	0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x74, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0a, 0x72, 0x65, 0x74, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0c,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x4e, 0x0a, 0x16, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x14, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x4f, 0x62, 0x6a, 0x65,
	0x63, 0x74, 0x12, 0x33, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x54, 0x65, 0x78, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x19,
	0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xc6, 0x03, 0x0a, 0x25, 0x43, 0x72,
	0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x53, 0x79, 0x6e, 0x63, 0x50, 0x6f, 0x6c, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x49, 0x64, 0x12, 0x44, 0x0a, 0x0c,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x12, 0x41, 0x0a, 0x0f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x07, 0x73, 0x75, 0x62, 0x54, 0x65, 0x78, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x74,
	0x74, 0x65, 0x6d, 0x70, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x72, 0x79, 0x44, 0x65, 0x6c,
	0x61, 0x79, 0x22, 0x83, 0x08, 0x0a, 0x16, 0x43, 0x63, 0x41, 0x6d, 0x70, 0x6c, 0x69, 0x46, 0x69,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x43, 0x0a,
	0x08, 0x74, 0x6f, 0x6f, 0x6c, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x42, 0x61, 0x72, 0x52, 0x07, 0x74, 0x6f, 0x6f, 0x6c, 0x42,
	0x61, 0x72, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3e, 0x0a, 0x09, 0x73,
	0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x54, 0x65, 0x78, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x48, 0x0a, 0x0d, 0x61,
	0x6d, 0x70, 0x6c, 0x69, 0x5f, 0x66, 0x69, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x41, 0x6d, 0x70,
	0x6c, 0x69, 0x46, 0x69, 0x43, 0x61, 0x72, 0x64, 0x52, 0x0b, 0x61, 0x6d, 0x70, 0x6c, 0x69, 0x46,
	0x69, 0x43, 0x61, 0x72, 0x64, 0x12, 0x55, 0x0a, 0x0e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x0d, 0x64,
	0x69, 0x76, 0x69, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x4a, 0x0a, 0x10,
	0x61, 0x6d, 0x70, 0x6c, 0x69, 0x5f, 0x66, 0x69, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x41, 0x6d, 0x70, 0x6c, 0x69, 0x46,
	0x69, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x52, 0x0e, 0x61, 0x6d, 0x70, 0x6c, 0x69, 0x46,
	0x69, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x12, 0x5b, 0x0a, 0x14, 0x74, 0x65, 0x78, 0x74,
	0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x68, 0x79, 0x70, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x57, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x64, 0x48, 0x79, 0x70, 0x65, 0x72, 0x4c, 0x69, 0x6e, 0x6b, 0x73, 0x57, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x52, 0x12, 0x74, 0x65, 0x78, 0x74, 0x57, 0x69, 0x74, 0x68, 0x48, 0x79, 0x70, 0x65, 0x72,
	0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x12, 0x4f, 0x0a, 0x11, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x53, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x52, 0x10, 0x73, 0x6c, 0x69, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x47, 0x0a, 0x0b, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x0a, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x49, 0x63, 0x6f, 0x6e, 0x12,
	0x5e, 0x0a, 0x18, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72,
	0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x44, 0x72, 0x61, 0x77, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x16, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x42,
	0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12,
	0x3b, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32, 0x0a, 0x09,
	0x6b, 0x79, 0x63, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x15, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4b, 0x59,
	0x43, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x52, 0x08, 0x6b, 0x79, 0x63, 0x4c, 0x65, 0x76, 0x65, 0x6c,
	0x12, 0x26, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xd4, 0x04, 0x0a, 0x1d, 0x43, 0x63, 0x49,
	0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4d, 0x0a, 0x13, 0x74, 0x6f,
	0x70, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f,
	0x49, 0x74, 0x65, 0x6d, 0x56, 0x33, 0x52, 0x11, 0x74, 0x6f, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5b, 0x0a, 0x18, 0x73, 0x65, 0x70,
	0x61, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x16,
	0x73, 0x65, 0x70, 0x61, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x4e, 0x0a, 0x16, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x14, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x79, 0x0a, 0x17, 0x69, 0x6e, 0x65, 0x6c, 0x69, 0x67,
	0x69, 0x62, 0x6c, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x49, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x15, 0x69, 0x6e, 0x65, 0x6c,
	0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x39, 0x0a, 0x0b, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x0a, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x54, 0x65, 0x78, 0x74, 0x12, 0x31, 0x0a, 0x08,
	0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x07, 0x6d, 0x61, 0x69, 0x6e, 0x43, 0x74, 0x61, 0x22,
	0xd3, 0x01, 0x0a, 0x15, 0x49, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5a, 0x0a, 0x16, 0x62, 0x6f, 0x74,
	0x74, 0x6f, 0x6d, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e,
	0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x57, 0x69, 0x74, 0x68, 0x43, 0x74, 0x61, 0x56, 0x33, 0x52,
	0x14, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c,
	0x61, 0x70, 0x73, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x73, 0x43,
	0x6f, 0x6c, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x07, 0x63, 0x68, 0x65, 0x76,
	0x72, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x63, 0x68,
	0x65, 0x76, 0x72, 0x6f, 0x6e, 0x22, 0xa1, 0x02, 0x0a, 0x2f, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x56, 0x69, 0x65, 0x77, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0e, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0d, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x42, 0x0a, 0x0d, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x32,
	0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xb2, 0x01, 0x0a, 0x2a, 0x43, 0x63,
	0x42, 0x69, 0x6c, 0x6c, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0xd0,
	0x02, 0x0a, 0x25, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x70, 0x61,
	0x70, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x65, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74,
	0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x70, 0x61, 0x79, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41,
	0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x52, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x11, 0x70, 0x69,
	0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x70, 0x61, 0x79, 0x2e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x50, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0f, 0x70, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xa8, 0x05, 0x0a, 0x1f, 0x43, 0x63, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x39, 0x0a, 0x0b, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x54, 0x65, 0x78, 0x74, 0x52, 0x0a, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x54, 0x65, 0x78, 0x74,
	0x12, 0x3a, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0xa5, 0x01, 0x0a,
	0x1b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x43, 0x63, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x19, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0b, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f,
	0x63, 0x74, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74,
	0x61, 0x52, 0x0a, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x43, 0x74, 0x61, 0x1a, 0xdc, 0x01,
	0x0a, 0x19, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x6a, 0x0a, 0x1c, 0x76,
	0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x19, 0x76, 0x65,
	0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x53, 0x0a, 0x11, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63, 0x61, 0x72,
	0x64, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x22, 0xe3, 0x03, 0x0a,
	0x14, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x6f, 0x0a, 0x0c, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x74, 0x61, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4c, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x61, 0x62,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x61, 0x62, 0x52, 0x0b, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x54, 0x61, 0x62, 0x12, 0x7b, 0x0a, 0x0f, 0x74, 0x61, 0x62, 0x5f, 0x74, 0x6f,
	0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x53, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x61, 0x62, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x54, 0x61, 0x62, 0x54, 0x6f, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0d, 0x74, 0x61, 0x62, 0x54, 0x6f, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x1a, 0x5d, 0x0a, 0x12, 0x54, 0x61, 0x62, 0x54, 0x6f, 0x44, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x2e, 0x0a, 0x0b, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x61,
	0x62, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x10, 0x01, 0x22, 0xee, 0x06, 0x0a, 0x21, 0x43, 0x63, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x42, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x53, 0x68, 0x65, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78,
	0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x5c, 0x0a, 0x0d, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x37, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x57, 0x0a, 0x16, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x14, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x37, 0x0a, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0a, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x74, 0x61, 0x12, 0x66, 0x0a, 0x1c, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x44, 0x72, 0x61, 0x77, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x52, 0x1a, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x62, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63,
	0x6b, 0x62, 0x6f, 0x78, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x62, 0x6f, 0x78, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x13, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x62, 0x6f, 0x78,
	0x54, 0x65, 0x78, 0x74, 0x12, 0x7c, 0x0a, 0x1e, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64,
	0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x1b, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x12, 0x48, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x78, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x42, 0x6f, 0x78, 0x52, 0x0f, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x42, 0x6f, 0x78, 0x12, 0x45, 0x0a, 0x0c,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x57, 0x69, 0x74, 0x68, 0x48, 0x79, 0x70, 0x65,
	0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54,
	0x65, 0x78, 0x74, 0x22, 0xa8, 0x02, 0x0a, 0x0b, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x49,
	0x74, 0x65, 0x6d, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x3f, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x57, 0x69, 0x74, 0x68,
	0x48, 0x79, 0x70, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x51, 0x0a, 0x13, 0x6c, 0x65, 0x66, 0x74, 0x5f, 0x76, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x6c, 0x65, 0x66, 0x74, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c,
	0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x55, 0x0a, 0x15, 0x62, 0x6f, 0x74, 0x74, 0x6f,
	0x6d, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x62, 0x6f, 0x74, 0x74, 0x6f,
	0x6d, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0xad,
	0x05, 0x0a, 0x22, 0x43, 0x63, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x08, 0x74, 0x6f, 0x6f, 0x6c, 0x5f, 0x62, 0x61,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x57, 0x72, 0x61, 0x70, 0x70,
	0x65, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x54, 0x6f, 0x6f, 0x6c, 0x42, 0x61,
	0x72, 0x52, 0x07, 0x74, 0x6f, 0x6f, 0x6c, 0x42, 0x61, 0x72, 0x12, 0x3e, 0x0a, 0x0b, 0x74, 0x6f,
	0x70, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x56, 0x33, 0x52, 0x0a,
	0x74, 0x6f, 0x70, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x62, 0x0a, 0x1a, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x44, 0x72, 0x61, 0x77, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x52, 0x18, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x39,
	0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0b, 0x63, 0x6f,
	0x6e, 0x74, 0x69, 0x6e, 0x75, 0x65, 0x43, 0x74, 0x61, 0x12, 0x59, 0x0a, 0x10, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67,
	0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c,
	0x6f, 0x75, 0x72, 0x52, 0x0f, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x49, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x5f, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x0d, 0x61, 0x64, 0x64, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x43, 0x74, 0x61, 0x12,
	0x26, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x1f, 0x69, 0x73, 0x5f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x1c, 0x69, 0x73, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x65, 0x72, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x22, 0x9d,
	0x05, 0x0a, 0x27, 0x43, 0x63, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x65, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x6c, 0x65, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x62, 0x0a, 0x1c, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x76, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x19, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x2e,
	0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3f,
	0x0a, 0x09, 0x73, 0x75, 0x62, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x75, 0x69, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x57, 0x69, 0x74, 0x68, 0x48, 0x79, 0x70, 0x65, 0x72,
	0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x08, 0x73, 0x75, 0x62, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x35, 0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x69, 0x6e,
	0x66, 0x6f, 0x54, 0x65, 0x78, 0x74, 0x12, 0x51, 0x0a, 0x13, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x76,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x11, 0x69, 0x6e, 0x66, 0x6f, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x44, 0x0a, 0x12, 0x73, 0x68, 0x61,
	0x72, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x63, 0x74, 0x61, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x10, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x43, 0x74, 0x61, 0x12,
	0x38, 0x0a, 0x0c, 0x67, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x74, 0x61, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0a, 0x67,
	0x65, 0x74, 0x43, 0x61, 0x72, 0x64, 0x43, 0x74, 0x61, 0x12, 0x43, 0x0a, 0x08, 0x74, 0x6f, 0x6f,
	0x6c, 0x5f, 0x62, 0x61, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x57,
	0x72, 0x61, 0x70, 0x70, 0x65, 0x64, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x54, 0x6f,
	0x6f, 0x6c, 0x42, 0x61, 0x72, 0x52, 0x07, 0x74, 0x6f, 0x6f, 0x6c, 0x42, 0x61, 0x72, 0x22, 0x86,
	0x05, 0x0a, 0x20, 0x43, 0x43, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10,
	0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x12, 0x4f, 0x0a, 0x12, 0x74, 0x6f, 0x70, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x10, 0x74, 0x6f, 0x70, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c,
	0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x08, 0x73,
	0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x55, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x56, 0x65, 0x72, 0x74, 0x69, 0x63, 0x61, 0x6c, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x63,
	0x72, 0x65, 0x64, 0x69, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x37,
	0x0a, 0x0b, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0a, 0x70, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x43, 0x74, 0x61, 0x12, 0x46, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x61, 0x72, 0x79, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e,
	0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x43, 0x74, 0x61, 0x12,
	0x26, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x9f, 0x06, 0x0a, 0x1d, 0x43, 0x43, 0x43, 0x61,
	0x72, 0x64, 0x54, 0x6e, 0x43, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x5b, 0x0a, 0x11, 0x62, 0x61, 0x63,
	0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64,
	0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6f, 0x75, 0x72, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64,
	0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x34, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65,
	0x78, 0x74, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x6d, 0x0a, 0x20,
	0x63, 0x61, 0x72, 0x64, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x44, 0x72, 0x61, 0x77, 0x61, 0x62,
	0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x1d, 0x63, 0x61,
	0x72, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x42, 0x0a, 0x0d, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x74, 0x65, 0x6d, 0x56,
	0x32, 0x52, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12,
	0x28, 0x0a, 0x03, 0x63, 0x74, 0x61, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x43, 0x74, 0x61, 0x52, 0x03, 0x63, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x11, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x62, 0x6f, 0x78, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x42,
	0x6f, 0x78, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x42, 0x6f, 0x78, 0x12, 0x45, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x65, 0x78, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x57,
	0x69, 0x74, 0x68, 0x48, 0x79, 0x70, 0x65, 0x72, 0x6c, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x0b, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x65, 0x78, 0x74, 0x12, 0x55, 0x0a, 0x15, 0x62, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x13, 0x62, 0x6f,
	0x74, 0x74, 0x6f, 0x6d, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x61, 0x72, 0x64,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x9f, 0x01, 0x0a, 0x14, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x69, 0x63, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0xf9, 0x02, 0x0a, 0x1c,
	0x43, 0x63, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x56, 0x32, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x10,
	0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x61, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x6c, 0x6f, 0x61, 0x64, 0x65,
	0x72, 0x41, 0x6e, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3c, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69,
	0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x07, 0x62, 0x67, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x12, 0x61, 0x0a, 0x1a, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x63,
	0x61, 0x72, 0x64, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x17,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x9e, 0x01, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x7c,
	0x0a, 0x18, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x76,
	0x32, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x41, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x49, 0x6e,
	0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x56, 0x32, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x15, 0x69, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x56, 0x32, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42, 0x0a, 0x0a, 0x08,
	0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa1, 0x01, 0x0a, 0x15, 0x49, 0x6e, 0x74,
	0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x56, 0x32, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x3a, 0x0a, 0x19, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x5f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x17, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2d, 0x0a,
	0x13, 0x69, 0x73, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x63, 0x63, 0x5f, 0x65, 0x6c, 0x69, 0x67,
	0x69, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x63, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x42, 0x88, 0x01, 0x0a,
	0x41, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x50, 0x01, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescOnce sync.Once
	file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescData = file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDesc
)

func file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescGZIP() []byte {
	file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescOnce.Do(func() {
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescData)
	})
	return file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDescData
}

var file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_goTypes = []interface{}{
	(CardTabScreenOptions_SelectedTab)(0),                             // 0: api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions.SelectedTab
	(*CreditCardDetailsAndBenefitsScreenOptions)(nil),                 // 1: api.typesv2.deeplink_screen_option.firefly.CreditCardDetailsAndBenefitsScreenOptions
	(*CreditCardAddressSelectionV2ScreenOptions)(nil),                 // 2: api.typesv2.deeplink_screen_option.firefly.CreditCardAddressSelectionV2ScreenOptions
	(*CreditCardRealTimeEligibilityCheckIntroScreenOptions)(nil),      // 3: api.typesv2.deeplink_screen_option.firefly.CreditCardRealTimeEligibilityCheckIntroScreenOptions
	(*SecuredCreditCardDepositScreenOptions)(nil),                     // 4: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions
	(*SecuredCreditCardFdDetailsScreenOptions)(nil),                   // 5: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardFdDetailsScreenOptions
	(*SecuredCcDepositTenureSelectionBottomSheetScreenOptions)(nil),   // 6: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions
	(*SecuredCcDetailsBottomSheetScreenOptions)(nil),                  // 7: api.typesv2.deeplink_screen_option.firefly.SecuredCcDetailsBottomSheetScreenOptions
	(*CcIntroBottomSheetScreenOptions)(nil),                           // 8: api.typesv2.deeplink_screen_option.firefly.CcIntroBottomSheetScreenOptions
	(*CcIntroScreenOptions)(nil),                                      // 9: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenOptions
	(*FireflySyncPollStatusScreenOptions)(nil),                        // 10: api.typesv2.deeplink_screen_option.firefly.FireflySyncPollStatusScreenOptions
	(*CreditCardSyncPollStatusScreenOptions)(nil),                     // 11: api.typesv2.deeplink_screen_option.firefly.CreditCardSyncPollStatusScreenOptions
	(*CcAmpliFiScreenOptions)(nil),                                    // 12: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions
	(*CcIneligibleUserScreenOptions)(nil),                             // 13: api.typesv2.deeplink_screen_option.firefly.CcIneligibleUserScreenOptions
	(*IneligibleUserDetails)(nil),                                     // 14: api.typesv2.deeplink_screen_option.firefly.IneligibleUserDetails
	(*CreditCardBillingDetailsBottomViewScreenOptions)(nil),           // 15: api.typesv2.deeplink_screen_option.firefly.CreditCardBillingDetailsBottomViewScreenOptions
	(*CcBillGenerationDateSelectionScreenOptions)(nil),                // 16: api.typesv2.deeplink_screen_option.firefly.CcBillGenerationDateSelectionScreenOptions
	(*CreditCardTpapPaymentInitScreenOption)(nil),                     // 17: api.typesv2.deeplink_screen_option.firefly.CreditCardTpapPaymentInitScreenOption
	(*CcNetworkSelectionScreenOptions)(nil),                           // 18: api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions
	(*CardTabScreenOptions)(nil),                                      // 19: api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions
	(*CcConsentBottomSheetScreenOptions)(nil),                         // 20: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions
	(*ConsentItem)(nil),                                               // 21: api.typesv2.deeplink_screen_option.firefly.ConsentItem
	(*CcCreditReportAddressScreenOptions)(nil),                        // 22: api.typesv2.deeplink_screen_option.firefly.CcCreditReportAddressScreenOptions
	(*CcUserIneligibleTransitionScreenOptions)(nil),                   // 23: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions
	(*CCCreditLimitUpdateScreenOptions)(nil),                          // 24: api.typesv2.deeplink_screen_option.firefly.CCCreditLimitUpdateScreenOptions
	(*CCCardTnCConsentScreenOptions)(nil),                             // 25: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions
	(*GenericScreenOptions)(nil),                                      // 26: api.typesv2.deeplink_screen_option.firefly.GenericScreenOptions
	(*CcIntroScreenV2ScreenOptions)(nil),                              // 27: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenV2ScreenOptions
	(*CreditCardMetadata)(nil),                                        // 28: api.typesv2.deeplink_screen_option.firefly.CreditCardMetadata
	(*IntroScreenV2Metadata)(nil),                                     // 29: api.typesv2.deeplink_screen_option.firefly.IntroScreenV2Metadata
	(*CcNetworkSelectionScreenOptions_NetworkSelectionComponent)(nil), // 30: api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions.NetworkSelectionComponent
	nil, // 31: api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions.TabToDeeplinkEntry
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 32: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*deeplink.InfoItemWithCta)(nil),                  // 33: frontend.deeplink.InfoItemWithCta
	(*deeplink.InfoItem)(nil),                         // 34: frontend.deeplink.InfoItem
	(*deeplink.InfoItemWithCtaV2)(nil),                // 35: frontend.deeplink.InfoItemWithCtaV2
	(*deeplink.Cta)(nil),                              // 36: frontend.deeplink.Cta
	(*common.Text)(nil),                               // 37: api.typesv2.common.Text
	(*deeplink.StepInfo)(nil),                         // 38: frontend.deeplink.StepInfo
	(typesv2.KYCLevel)(0),                             // 39: api.typesv2.KYCLevel
	(*deeplink.InfoItemV3)(nil),                       // 40: frontend.deeplink.InfoItemV3
	(*deeplink.InfoItemWithCtaV3)(nil),                // 41: frontend.deeplink.InfoItemWithCtaV3
	(*ui.IconTextComponent)(nil),                      // 42: api.typesv2.ui.IconTextComponent
	(*common.VisualElement)(nil),                      // 43: api.typesv2.common.VisualElement
	(*typesv2.Money)(nil),                             // 44: api.typesv2.Money
	(*deeplink.InfoItemV2)(nil),                       // 45: frontend.deeplink.InfoItemV2
	(*deeplink.InfoToolTipV2)(nil),                    // 46: frontend.deeplink.InfoToolTipV2
	(*deeplink.VisualElementCta)(nil),                 // 47: frontend.deeplink.VisualElementCta
	(typesv2.DepositStatus)(0),                        // 48: api.typesv2.DepositStatus
	(*typesv2.DepositInterestDetails)(nil),            // 49: api.typesv2.DepositInterestDetails
	(*typesv2.DepositTerm)(nil),                       // 50: api.typesv2.DepositTerm
	(*typesv2.DurationSliderValue)(nil),               // 51: api.typesv2.DurationSliderValue
	(*common.TextWithIcon)(nil),                       // 52: api.typesv2.common.TextWithIcon
	(*widget.BackgroundColour)(nil),                   // 53: api.typesv2.common.ui.widget.BackgroundColour
	(*firefly.PromotionInfo)(nil),                     // 54: frontend.firefly.PromotionInfo
	(*firefly.WrappedButtonInfo)(nil),                 // 55: frontend.firefly.WrappedButtonInfo
	(enums.CardProgramType)(0),                        // 56: frontend.firefly.enums.CardProgramType
	(*typesv2.CardProgram)(nil),                       // 57: api.typesv2.CardProgram
	(*typesv2.CreditCardRequestHeader)(nil),           // 58: api.typesv2.CreditCardRequestHeader
	(*firefly.WrappedIconTextToolBar)(nil),            // 59: frontend.firefly.WrappedIconTextToolBar
	(*firefly.WrappedTextInfo)(nil),                   // 60: frontend.firefly.WrappedTextInfo
	(*firefly.WrappedAmpliFiCard)(nil),                // 61: frontend.firefly.WrappedAmpliFiCard
	(*firefly.AmpliFiBenefit)(nil),                    // 62: frontend.firefly.AmpliFiBenefit
	(*firefly.WrappedHyperLinksWidget)(nil),           // 63: frontend.firefly.WrappedHyperLinksWidget
	(*firefly.SliderProperties)(nil),                  // 64: frontend.firefly.SliderProperties
	(*firefly.WrappedVisualElement)(nil),              // 65: frontend.firefly.WrappedVisualElement
	(*firefly.DrawableProperties)(nil),                // 66: frontend.firefly.DrawableProperties
	(typesv2.AddressType)(0),                          // 67: api.typesv2.AddressType
	(*transaction.TransactionAttribute)(nil),          // 68: frontend.pay.transaction.TransactionAttribute
	(transaction.PinRequiredType)(0),                  // 69: frontend.pay.transaction.PinRequiredType
	(*widget.CheckboxItem)(nil),                       // 70: api.typesv2.common.ui.widget.CheckboxItem
	(enums.CreditCardOnboardingConsent)(0),            // 71: frontend.firefly.enums.CreditCardOnboardingConsent
	(*firefly.ConsentBox)(nil),                        // 72: frontend.firefly.ConsentBox
	(*ui.TextWithHyperlinks)(nil),                     // 73: api.typesv2.ui.TextWithHyperlinks
	(*ui.VerticalIconTextComponent)(nil),              // 74: api.typesv2.ui.VerticalIconTextComponent
	(enums.CardNetworkType)(0),                        // 75: frontend.firefly.enums.CardNetworkType
	(*deeplink.Deeplink)(nil),                         // 76: frontend.deeplink.Deeplink
}
var file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_depIdxs = []int32{
	32,  // 0: api.typesv2.deeplink_screen_option.firefly.CreditCardDetailsAndBenefitsScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	33,  // 1: api.typesv2.deeplink_screen_option.firefly.CreditCardDetailsAndBenefitsScreenOptions.brands:type_name -> frontend.deeplink.InfoItemWithCta
	33,  // 2: api.typesv2.deeplink_screen_option.firefly.CreditCardDetailsAndBenefitsScreenOptions.valueback_cheatsheet:type_name -> frontend.deeplink.InfoItemWithCta
	33,  // 3: api.typesv2.deeplink_screen_option.firefly.CreditCardDetailsAndBenefitsScreenOptions.welcome_vouchers:type_name -> frontend.deeplink.InfoItemWithCta
	34,  // 4: api.typesv2.deeplink_screen_option.firefly.CreditCardDetailsAndBenefitsScreenOptions.static_images:type_name -> frontend.deeplink.InfoItem
	35,  // 5: api.typesv2.deeplink_screen_option.firefly.CreditCardDetailsAndBenefitsScreenOptions.bottom_info_items:type_name -> frontend.deeplink.InfoItemWithCtaV2
	36,  // 6: api.typesv2.deeplink_screen_option.firefly.CreditCardDetailsAndBenefitsScreenOptions.share:type_name -> frontend.deeplink.Cta
	37,  // 7: api.typesv2.deeplink_screen_option.firefly.CreditCardDetailsAndBenefitsScreenOptions.share_credit_limit_text:type_name -> api.typesv2.common.Text
	32,  // 8: api.typesv2.deeplink_screen_option.firefly.CreditCardAddressSelectionV2ScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	38,  // 9: api.typesv2.deeplink_screen_option.firefly.CreditCardAddressSelectionV2ScreenOptions.step_info:type_name -> frontend.deeplink.StepInfo
	36,  // 10: api.typesv2.deeplink_screen_option.firefly.CreditCardAddressSelectionV2ScreenOptions.continue_cta:type_name -> frontend.deeplink.Cta
	39,  // 11: api.typesv2.deeplink_screen_option.firefly.CreditCardAddressSelectionV2ScreenOptions.kyc_level:type_name -> api.typesv2.KYCLevel
	40,  // 12: api.typesv2.deeplink_screen_option.firefly.CreditCardAddressSelectionV2ScreenOptions.header_details:type_name -> frontend.deeplink.InfoItemV3
	41,  // 13: api.typesv2.deeplink_screen_option.firefly.CreditCardAddressSelectionV2ScreenOptions.add_address_cta_v2:type_name -> frontend.deeplink.InfoItemWithCtaV3
	35,  // 14: api.typesv2.deeplink_screen_option.firefly.CreditCardAddressSelectionV2ScreenOptions.add_address_cta:type_name -> frontend.deeplink.InfoItemWithCtaV2
	42,  // 15: api.typesv2.deeplink_screen_option.firefly.CreditCardAddressSelectionV2ScreenOptions.header_icon_text_component:type_name -> api.typesv2.ui.IconTextComponent
	32,  // 16: api.typesv2.deeplink_screen_option.firefly.CreditCardRealTimeEligibilityCheckIntroScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	43,  // 17: api.typesv2.deeplink_screen_option.firefly.CreditCardRealTimeEligibilityCheckIntroScreenOptions.image_top:type_name -> api.typesv2.common.VisualElement
	37,  // 18: api.typesv2.deeplink_screen_option.firefly.CreditCardRealTimeEligibilityCheckIntroScreenOptions.title:type_name -> api.typesv2.common.Text
	37,  // 19: api.typesv2.deeplink_screen_option.firefly.CreditCardRealTimeEligibilityCheckIntroScreenOptions.sub_title:type_name -> api.typesv2.common.Text
	36,  // 20: api.typesv2.deeplink_screen_option.firefly.CreditCardRealTimeEligibilityCheckIntroScreenOptions.check_credit_score_cta:type_name -> frontend.deeplink.Cta
	43,  // 21: api.typesv2.deeplink_screen_option.firefly.CreditCardRealTimeEligibilityCheckIntroScreenOptions.image_bottom:type_name -> api.typesv2.common.VisualElement
	32,  // 22: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	38,  // 23: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.step_info:type_name -> frontend.deeplink.StepInfo
	35,  // 24: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.top_section_details:type_name -> frontend.deeplink.InfoItemWithCtaV2
	44,  // 25: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.min_deposit_amount:type_name -> api.typesv2.Money
	45,  // 26: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.bottom_text:type_name -> frontend.deeplink.InfoItemV2
	36,  // 27: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.insufficient_balance_cta:type_name -> frontend.deeplink.Cta
	36,  // 28: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.open_deposit_cta:type_name -> frontend.deeplink.Cta
	37,  // 29: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.ineligible_action_text:type_name -> api.typesv2.common.Text
	36,  // 30: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.add_nominee_cta:type_name -> frontend.deeplink.Cta
	43,  // 31: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.add_nominee_visual_element:type_name -> api.typesv2.common.VisualElement
	37,  // 32: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.terms_and_conditions_text:type_name -> api.typesv2.common.Text
	44,  // 33: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.default_credit_limit:type_name -> api.typesv2.Money
	44,  // 34: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.suggested_credit_limits:type_name -> api.typesv2.Money
	37,  // 35: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.credit_limit_heading:type_name -> api.typesv2.common.Text
	37,  // 36: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.deposit_limit_heading:type_name -> api.typesv2.common.Text
	44,  // 37: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.max_deposit_amount:type_name -> api.typesv2.Money
	37,  // 38: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.min_deposit_text:type_name -> api.typesv2.common.Text
	37,  // 39: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.max_deposit_text:type_name -> api.typesv2.common.Text
	46,  // 40: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.info_tooltip_credit_limit:type_name -> frontend.deeplink.InfoToolTipV2
	46,  // 41: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.info_tooltip_fixed_deposit:type_name -> frontend.deeplink.InfoToolTipV2
	47,  // 42: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardDepositScreenOptions.benefits_visual_element_cta:type_name -> frontend.deeplink.VisualElementCta
	32,  // 43: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardFdDetailsScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	38,  // 44: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardFdDetailsScreenOptions.step_info:type_name -> frontend.deeplink.StepInfo
	40,  // 45: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardFdDetailsScreenOptions.header_details:type_name -> frontend.deeplink.InfoItemV3
	40,  // 46: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardFdDetailsScreenOptions.fd_details:type_name -> frontend.deeplink.InfoItemV3
	36,  // 47: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardFdDetailsScreenOptions.next_cta:type_name -> frontend.deeplink.Cta
	48,  // 48: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardFdDetailsScreenOptions.fd_creation_status:type_name -> api.typesv2.DepositStatus
	43,  // 49: api.typesv2.deeplink_screen_option.firefly.SecuredCreditCardFdDetailsScreenOptions.ve_bank_logo:type_name -> api.typesv2.common.VisualElement
	32,  // 50: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	37,  // 51: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.heading_text:type_name -> api.typesv2.common.Text
	37,  // 52: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.tenure_days_text:type_name -> api.typesv2.common.Text
	37,  // 53: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.tenure_month_text:type_name -> api.typesv2.common.Text
	49,  // 54: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.slider_term_details:type_name -> api.typesv2.DepositInterestDetails
	50,  // 55: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.selected_deposit_term:type_name -> api.typesv2.DepositTerm
	36,  // 56: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.confirmation_cta:type_name -> frontend.deeplink.Cta
	37,  // 57: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.min_deposit_term_error_text:type_name -> api.typesv2.common.Text
	37,  // 58: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.max_deposit_term_error_text:type_name -> api.typesv2.common.Text
	51,  // 59: api.typesv2.deeplink_screen_option.firefly.SecuredCcDepositTenureSelectionBottomSheetScreenOptions.duration_slider_values:type_name -> api.typesv2.DurationSliderValue
	32,  // 60: api.typesv2.deeplink_screen_option.firefly.SecuredCcDetailsBottomSheetScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	40,  // 61: api.typesv2.deeplink_screen_option.firefly.SecuredCcDetailsBottomSheetScreenOptions.header_details:type_name -> frontend.deeplink.InfoItemV3
	52,  // 62: api.typesv2.deeplink_screen_option.firefly.SecuredCcDetailsBottomSheetScreenOptions.feature_details:type_name -> api.typesv2.common.TextWithIcon
	32,  // 63: api.typesv2.deeplink_screen_option.firefly.CcIntroBottomSheetScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	37,  // 64: api.typesv2.deeplink_screen_option.firefly.CcIntroBottomSheetScreenOptions.title:type_name -> api.typesv2.common.Text
	37,  // 65: api.typesv2.deeplink_screen_option.firefly.CcIntroBottomSheetScreenOptions.sub_title:type_name -> api.typesv2.common.Text
	53,  // 66: api.typesv2.deeplink_screen_option.firefly.CcIntroBottomSheetScreenOptions.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	54,  // 67: api.typesv2.deeplink_screen_option.firefly.CcIntroBottomSheetScreenOptions.promotion:type_name -> frontend.firefly.PromotionInfo
	55,  // 68: api.typesv2.deeplink_screen_option.firefly.CcIntroBottomSheetScreenOptions.button:type_name -> frontend.firefly.WrappedButtonInfo
	43,  // 69: api.typesv2.deeplink_screen_option.firefly.CcIntroBottomSheetScreenOptions.visual_element:type_name -> api.typesv2.common.VisualElement
	32,  // 70: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	56,  // 71: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenOptions.card_program_type:type_name -> frontend.firefly.enums.CardProgramType
	57,  // 72: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenOptions.card_program:type_name -> api.typesv2.CardProgram
	58,  // 73: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenOptions.credit_card_request_header:type_name -> api.typesv2.CreditCardRequestHeader
	32,  // 74: api.typesv2.deeplink_screen_option.firefly.FireflySyncPollStatusScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	43,  // 75: api.typesv2.deeplink_screen_option.firefly.FireflySyncPollStatusScreenOptions.screen_image:type_name -> api.typesv2.common.VisualElement
	37,  // 76: api.typesv2.deeplink_screen_option.firefly.FireflySyncPollStatusScreenOptions.display_message_object:type_name -> api.typesv2.common.Text
	37,  // 77: api.typesv2.deeplink_screen_option.firefly.FireflySyncPollStatusScreenOptions.sub_text:type_name -> api.typesv2.common.Text
	32,  // 78: api.typesv2.deeplink_screen_option.firefly.CreditCardSyncPollStatusScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	43,  // 79: api.typesv2.deeplink_screen_option.firefly.CreditCardSyncPollStatusScreenOptions.screen_image:type_name -> api.typesv2.common.VisualElement
	37,  // 80: api.typesv2.deeplink_screen_option.firefly.CreditCardSyncPollStatusScreenOptions.display_message:type_name -> api.typesv2.common.Text
	37,  // 81: api.typesv2.deeplink_screen_option.firefly.CreditCardSyncPollStatusScreenOptions.sub_text:type_name -> api.typesv2.common.Text
	32,  // 82: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	59,  // 83: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.tool_bar:type_name -> frontend.firefly.WrappedIconTextToolBar
	60,  // 84: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.title:type_name -> frontend.firefly.WrappedTextInfo
	60,  // 85: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.sub_title:type_name -> frontend.firefly.WrappedTextInfo
	61,  // 86: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.ampli_fi_card:type_name -> frontend.firefly.WrappedAmpliFiCard
	53,  // 87: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.divider_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	62,  // 88: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.ampli_fi_benefit:type_name -> frontend.firefly.AmpliFiBenefit
	63,  // 89: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.text_with_hyperlinks:type_name -> frontend.firefly.WrappedHyperLinksWidget
	64,  // 90: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.slider_properties:type_name -> frontend.firefly.SliderProperties
	65,  // 91: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.footer_icon:type_name -> frontend.firefly.WrappedVisualElement
	66,  // 92: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.screen_background_colour:type_name -> frontend.firefly.DrawableProperties
	67,  // 93: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.address_type:type_name -> api.typesv2.AddressType
	39,  // 94: api.typesv2.deeplink_screen_option.firefly.CcAmpliFiScreenOptions.kyc_level:type_name -> api.typesv2.KYCLevel
	32,  // 95: api.typesv2.deeplink_screen_option.firefly.CcIneligibleUserScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	40,  // 96: api.typesv2.deeplink_screen_option.firefly.CcIneligibleUserScreenOptions.top_section_details:type_name -> frontend.deeplink.InfoItemV3
	43,  // 97: api.typesv2.deeplink_screen_option.firefly.CcIneligibleUserScreenOptions.separator_visual_element:type_name -> api.typesv2.common.VisualElement
	37,  // 98: api.typesv2.deeplink_screen_option.firefly.CcIneligibleUserScreenOptions.bottom_section_heading:type_name -> api.typesv2.common.Text
	14,  // 99: api.typesv2.deeplink_screen_option.firefly.CcIneligibleUserScreenOptions.ineligible_user_details:type_name -> api.typesv2.deeplink_screen_option.firefly.IneligibleUserDetails
	37,  // 100: api.typesv2.deeplink_screen_option.firefly.CcIneligibleUserScreenOptions.footer_text:type_name -> api.typesv2.common.Text
	36,  // 101: api.typesv2.deeplink_screen_option.firefly.CcIneligibleUserScreenOptions.main_cta:type_name -> frontend.deeplink.Cta
	41,  // 102: api.typesv2.deeplink_screen_option.firefly.IneligibleUserDetails.bottom_section_details:type_name -> frontend.deeplink.InfoItemWithCtaV3
	43,  // 103: api.typesv2.deeplink_screen_option.firefly.IneligibleUserDetails.chevron:type_name -> api.typesv2.common.VisualElement
	32,  // 104: api.typesv2.deeplink_screen_option.firefly.CreditCardBillingDetailsBottomViewScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	37,  // 105: api.typesv2.deeplink_screen_option.firefly.CreditCardBillingDetailsBottomViewScreenOptions.screen_heading:type_name -> api.typesv2.common.Text
	45,  // 106: api.typesv2.deeplink_screen_option.firefly.CreditCardBillingDetailsBottomViewScreenOptions.billing_infos:type_name -> frontend.deeplink.InfoItemV2
	32,  // 107: api.typesv2.deeplink_screen_option.firefly.CcBillGenerationDateSelectionScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	32,  // 108: api.typesv2.deeplink_screen_option.firefly.CreditCardTpapPaymentInitScreenOption.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	68,  // 109: api.typesv2.deeplink_screen_option.firefly.CreditCardTpapPaymentInitScreenOption.transaction_attributes:type_name -> frontend.pay.transaction.TransactionAttribute
	69,  // 110: api.typesv2.deeplink_screen_option.firefly.CreditCardTpapPaymentInitScreenOption.pin_required_type:type_name -> frontend.pay.transaction.PinRequiredType
	32,  // 111: api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	37,  // 112: api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions.header_text:type_name -> api.typesv2.common.Text
	37,  // 113: api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions.description:type_name -> api.typesv2.common.Text
	30,  // 114: api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions.network_selection_component:type_name -> api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions.NetworkSelectionComponent
	36,  // 115: api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions.primary_cta:type_name -> frontend.deeplink.Cta
	32,  // 116: api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	0,   // 117: api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions.selected_tab:type_name -> api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions.SelectedTab
	31,  // 118: api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions.tab_to_deeplink:type_name -> api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions.TabToDeeplinkEntry
	32,  // 119: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	37,  // 120: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.title:type_name -> api.typesv2.common.Text
	21,  // 121: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.consent_items:type_name -> api.typesv2.deeplink_screen_option.firefly.ConsentItem
	43,  // 122: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.program_visual_element:type_name -> api.typesv2.common.VisualElement
	36,  // 123: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.consent_cta:type_name -> frontend.deeplink.Cta
	66,  // 124: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.consent_container_properties:type_name -> frontend.firefly.DrawableProperties
	70,  // 125: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.consent_checkbox_text:type_name -> api.typesv2.common.ui.widget.CheckboxItem
	71,  // 126: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.credit_card_onboarding_consent:type_name -> frontend.firefly.enums.CreditCardOnboardingConsent
	72,  // 127: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.consent_check_box:type_name -> frontend.firefly.ConsentBox
	73,  // 128: api.typesv2.deeplink_screen_option.firefly.CcConsentBottomSheetScreenOptions.consent_text:type_name -> api.typesv2.ui.TextWithHyperlinks
	37,  // 129: api.typesv2.deeplink_screen_option.firefly.ConsentItem.title:type_name -> api.typesv2.common.Text
	73,  // 130: api.typesv2.deeplink_screen_option.firefly.ConsentItem.sub_title:type_name -> api.typesv2.ui.TextWithHyperlinks
	43,  // 131: api.typesv2.deeplink_screen_option.firefly.ConsentItem.left_visual_element:type_name -> api.typesv2.common.VisualElement
	43,  // 132: api.typesv2.deeplink_screen_option.firefly.ConsentItem.bottom_visual_element:type_name -> api.typesv2.common.VisualElement
	32,  // 133: api.typesv2.deeplink_screen_option.firefly.CcCreditReportAddressScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	59,  // 134: api.typesv2.deeplink_screen_option.firefly.CcCreditReportAddressScreenOptions.tool_bar:type_name -> frontend.firefly.WrappedIconTextToolBar
	40,  // 135: api.typesv2.deeplink_screen_option.firefly.CcCreditReportAddressScreenOptions.top_section:type_name -> frontend.deeplink.InfoItemV3
	66,  // 136: api.typesv2.deeplink_screen_option.firefly.CcCreditReportAddressScreenOptions.address_section_properties:type_name -> frontend.firefly.DrawableProperties
	36,  // 137: api.typesv2.deeplink_screen_option.firefly.CcCreditReportAddressScreenOptions.continue_cta:type_name -> frontend.deeplink.Cta
	53,  // 138: api.typesv2.deeplink_screen_option.firefly.CcCreditReportAddressScreenOptions.background_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	42,  // 139: api.typesv2.deeplink_screen_option.firefly.CcCreditReportAddressScreenOptions.add_address_cta:type_name -> api.typesv2.ui.IconTextComponent
	32,  // 140: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	43,  // 141: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions.screen_header_visual_element:type_name -> api.typesv2.common.VisualElement
	37,  // 142: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions.title:type_name -> api.typesv2.common.Text
	73,  // 143: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions.sub_title:type_name -> api.typesv2.ui.TextWithHyperlinks
	37,  // 144: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions.info_text:type_name -> api.typesv2.common.Text
	43,  // 145: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions.info_visual_element:type_name -> api.typesv2.common.VisualElement
	36,  // 146: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions.share_feedback_cta:type_name -> frontend.deeplink.Cta
	36,  // 147: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions.get_card_cta:type_name -> frontend.deeplink.Cta
	59,  // 148: api.typesv2.deeplink_screen_option.firefly.CcUserIneligibleTransitionScreenOptions.tool_bar:type_name -> frontend.firefly.WrappedIconTextToolBar
	32,  // 149: api.typesv2.deeplink_screen_option.firefly.CCCreditLimitUpdateScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	53,  // 150: api.typesv2.deeplink_screen_option.firefly.CCCreditLimitUpdateScreenOptions.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	43,  // 151: api.typesv2.deeplink_screen_option.firefly.CCCreditLimitUpdateScreenOptions.top_visual_element:type_name -> api.typesv2.common.VisualElement
	37,  // 152: api.typesv2.deeplink_screen_option.firefly.CCCreditLimitUpdateScreenOptions.title:type_name -> api.typesv2.common.Text
	37,  // 153: api.typesv2.deeplink_screen_option.firefly.CCCreditLimitUpdateScreenOptions.subtitle:type_name -> api.typesv2.common.Text
	74,  // 154: api.typesv2.deeplink_screen_option.firefly.CCCreditLimitUpdateScreenOptions.credit_limit_info:type_name -> api.typesv2.ui.VerticalIconTextComponent
	36,  // 155: api.typesv2.deeplink_screen_option.firefly.CCCreditLimitUpdateScreenOptions.primary_cta:type_name -> frontend.deeplink.Cta
	42,  // 156: api.typesv2.deeplink_screen_option.firefly.CCCreditLimitUpdateScreenOptions.secondary_cta:type_name -> api.typesv2.ui.IconTextComponent
	32,  // 157: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	53,  // 158: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.background_colour:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	37,  // 159: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.title:type_name -> api.typesv2.common.Text
	37,  // 160: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.subtitle:type_name -> api.typesv2.common.Text
	66,  // 161: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.card_benefits_container_property:type_name -> frontend.firefly.DrawableProperties
	45,  // 162: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.card_benefits:type_name -> frontend.deeplink.InfoItemV2
	36,  // 163: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.cta:type_name -> frontend.deeplink.Cta
	72,  // 164: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.consent_check_box:type_name -> frontend.firefly.ConsentBox
	73,  // 165: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.consent_text:type_name -> api.typesv2.ui.TextWithHyperlinks
	43,  // 166: api.typesv2.deeplink_screen_option.firefly.CCCardTnCConsentScreenOptions.bottom_visual_element:type_name -> api.typesv2.common.VisualElement
	32,  // 167: api.typesv2.deeplink_screen_option.firefly.GenericScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	32,  // 168: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenV2ScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	43,  // 169: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenV2ScreenOptions.loader_animation:type_name -> api.typesv2.common.VisualElement
	43,  // 170: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenV2ScreenOptions.bg_image:type_name -> api.typesv2.common.VisualElement
	58,  // 171: api.typesv2.deeplink_screen_option.firefly.CcIntroScreenV2ScreenOptions.credit_card_request_header:type_name -> api.typesv2.CreditCardRequestHeader
	29,  // 172: api.typesv2.deeplink_screen_option.firefly.CreditCardMetadata.intro_screen_v2_metadata:type_name -> api.typesv2.deeplink_screen_option.firefly.IntroScreenV2Metadata
	74,  // 173: api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions.NetworkSelectionComponent.vertical_icon_text_component:type_name -> api.typesv2.ui.VerticalIconTextComponent
	75,  // 174: api.typesv2.deeplink_screen_option.firefly.CcNetworkSelectionScreenOptions.NetworkSelectionComponent.card_network_type:type_name -> frontend.firefly.enums.CardNetworkType
	76,  // 175: api.typesv2.deeplink_screen_option.firefly.CardTabScreenOptions.TabToDeeplinkEntry.value:type_name -> frontend.deeplink.Deeplink
	176, // [176:176] is the sub-list for method output_type
	176, // [176:176] is the sub-list for method input_type
	176, // [176:176] is the sub-list for extension type_name
	176, // [176:176] is the sub-list for extension extendee
	0,   // [0:176] is the sub-list for field type_name
}

func init() { file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_init() }
func file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_init() {
	if File_api_typesv2_deeplink_screen_option_firefly_screen_options_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardDetailsAndBenefitsScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardAddressSelectionV2ScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardRealTimeEligibilityCheckIntroScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecuredCreditCardDepositScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecuredCreditCardFdDetailsScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecuredCcDepositTenureSelectionBottomSheetScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecuredCcDetailsBottomSheetScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcIntroBottomSheetScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcIntroScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FireflySyncPollStatusScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardSyncPollStatusScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcAmpliFiScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcIneligibleUserScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IneligibleUserDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardBillingDetailsBottomViewScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcBillGenerationDateSelectionScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardTpapPaymentInitScreenOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcNetworkSelectionScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardTabScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcConsentBottomSheetScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConsentItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcCreditReportAddressScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcUserIneligibleTransitionScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CCCreditLimitUpdateScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CCCardTnCConsentScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenericScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcIntroScreenV2ScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IntroScreenV2Metadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CcNetworkSelectionScreenOptions_NetworkSelectionComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes[27].OneofWrappers = []interface{}{
		(*CreditCardMetadata_IntroScreenV2Metadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_goTypes,
		DependencyIndexes: file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_depIdxs,
		EnumInfos:         file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_enumTypes,
		MessageInfos:      file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_msgTypes,
	}.Build()
	File_api_typesv2_deeplink_screen_option_firefly_screen_options_proto = out.File
	file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_rawDesc = nil
	file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_goTypes = nil
	file_api_typesv2_deeplink_screen_option_firefly_screen_options_proto_depIdxs = nil
}
