// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/typesv2/deeplink_screen_option/firefly/screen_options.proto

package firefly

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/frontend/firefly/enums"

	transaction "github.com/epifi/gamma/api/frontend/pay/transaction"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.CardProgramType(0)

	_ = transaction.PinRequiredType(0)

	_ = typesv2.KYCLevel(0)
)

// Validate checks the field values on
// CreditCardDetailsAndBenefitsScreenOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreditCardDetailsAndBenefitsScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreditCardDetailsAndBenefitsScreenOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreditCardDetailsAndBenefitsScreenOptionsMultiError, or nil if none found.
func (m *CreditCardDetailsAndBenefitsScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardDetailsAndBenefitsScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardDetailsAndBenefitsScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScreenTitle

	// no validation rules for CardImageUrl

	// no validation rules for CreditLimitStr

	if all {
		switch v := interface{}(m.GetBrands()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "Brands",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "Brands",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrands()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardDetailsAndBenefitsScreenOptionsValidationError{
				field:  "Brands",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetValuebackCheatsheet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "ValuebackCheatsheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "ValuebackCheatsheet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetValuebackCheatsheet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardDetailsAndBenefitsScreenOptionsValidationError{
				field:  "ValuebackCheatsheet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetWelcomeVouchers()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "WelcomeVouchers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "WelcomeVouchers",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetWelcomeVouchers()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardDetailsAndBenefitsScreenOptionsValidationError{
				field:  "WelcomeVouchers",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetStaticImages() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
						field:  fmt.Sprintf("StaticImages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
						field:  fmt.Sprintf("StaticImages[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  fmt.Sprintf("StaticImages[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetBottomInfoItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
						field:  fmt.Sprintf("BottomInfoItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
						field:  fmt.Sprintf("BottomInfoItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  fmt.Sprintf("BottomInfoItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetShare()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "Share",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "Share",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShare()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardDetailsAndBenefitsScreenOptionsValidationError{
				field:  "Share",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TextForShare

	if all {
		switch v := interface{}(m.GetShareCreditLimitText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "ShareCreditLimitText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardDetailsAndBenefitsScreenOptionsValidationError{
					field:  "ShareCreditLimitText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShareCreditLimitText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardDetailsAndBenefitsScreenOptionsValidationError{
				field:  "ShareCreditLimitText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreditCardDetailsAndBenefitsScreenOptionsMultiError(errors)
	}

	return nil
}

// CreditCardDetailsAndBenefitsScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// CreditCardDetailsAndBenefitsScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CreditCardDetailsAndBenefitsScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardDetailsAndBenefitsScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardDetailsAndBenefitsScreenOptionsMultiError) AllErrors() []error { return m }

// CreditCardDetailsAndBenefitsScreenOptionsValidationError is the validation
// error returned by CreditCardDetailsAndBenefitsScreenOptions.Validate if the
// designated constraints aren't met.
type CreditCardDetailsAndBenefitsScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardDetailsAndBenefitsScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardDetailsAndBenefitsScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardDetailsAndBenefitsScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardDetailsAndBenefitsScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardDetailsAndBenefitsScreenOptionsValidationError) ErrorName() string {
	return "CreditCardDetailsAndBenefitsScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardDetailsAndBenefitsScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardDetailsAndBenefitsScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardDetailsAndBenefitsScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardDetailsAndBenefitsScreenOptionsValidationError{}

// Validate checks the field values on
// CreditCardAddressSelectionV2ScreenOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreditCardAddressSelectionV2ScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreditCardAddressSelectionV2ScreenOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CreditCardAddressSelectionV2ScreenOptionsMultiError, or nil if none found.
func (m *CreditCardAddressSelectionV2ScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardAddressSelectionV2ScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardAddressSelectionV2ScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStepInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "StepInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "StepInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStepInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardAddressSelectionV2ScreenOptionsValidationError{
				field:  "StepInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContinueCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "ContinueCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "ContinueCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContinueCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardAddressSelectionV2ScreenOptionsValidationError{
				field:  "ContinueCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardRequestId

	// no validation rules for KycLevel

	if all {
		switch v := interface{}(m.GetHeaderDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "HeaderDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "HeaderDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardAddressSelectionV2ScreenOptionsValidationError{
				field:  "HeaderDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddAddressCtaV2()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "AddAddressCtaV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "AddAddressCtaV2",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddAddressCtaV2()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardAddressSelectionV2ScreenOptionsValidationError{
				field:  "AddAddressCtaV2",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Text

	// no validation rules for SubText

	// no validation rules for DeliveryAddressIconUrl

	if all {
		switch v := interface{}(m.GetAddAddressCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "AddAddressCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "AddAddressCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddAddressCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardAddressSelectionV2ScreenOptionsValidationError{
				field:  "AddAddressCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderIconTextComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "HeaderIconTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardAddressSelectionV2ScreenOptionsValidationError{
					field:  "HeaderIconTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderIconTextComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardAddressSelectionV2ScreenOptionsValidationError{
				field:  "HeaderIconTextComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreditCardAddressSelectionV2ScreenOptionsMultiError(errors)
	}

	return nil
}

// CreditCardAddressSelectionV2ScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// CreditCardAddressSelectionV2ScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CreditCardAddressSelectionV2ScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardAddressSelectionV2ScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardAddressSelectionV2ScreenOptionsMultiError) AllErrors() []error { return m }

// CreditCardAddressSelectionV2ScreenOptionsValidationError is the validation
// error returned by CreditCardAddressSelectionV2ScreenOptions.Validate if the
// designated constraints aren't met.
type CreditCardAddressSelectionV2ScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardAddressSelectionV2ScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardAddressSelectionV2ScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardAddressSelectionV2ScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardAddressSelectionV2ScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardAddressSelectionV2ScreenOptionsValidationError) ErrorName() string {
	return "CreditCardAddressSelectionV2ScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardAddressSelectionV2ScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardAddressSelectionV2ScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardAddressSelectionV2ScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardAddressSelectionV2ScreenOptionsValidationError{}

// Validate checks the field values on
// CreditCardRealTimeEligibilityCheckIntroScreenOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreditCardRealTimeEligibilityCheckIntroScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreditCardRealTimeEligibilityCheckIntroScreenOptions with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CreditCardRealTimeEligibilityCheckIntroScreenOptionsMultiError, or nil if
// none found.
func (m *CreditCardRealTimeEligibilityCheckIntroScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardRealTimeEligibilityCheckIntroScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImageTop()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "ImageTop",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "ImageTop",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImageTop()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
				field:  "ImageTop",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCheckCreditScoreCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "CheckCreditScoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "CheckCreditScoreCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCheckCreditScoreCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
				field:  "CheckCreditScoreCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImageBottom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "ImageBottom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
					field:  "ImageBottom",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImageBottom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{
				field:  "ImageBottom",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreditCardRealTimeEligibilityCheckIntroScreenOptionsMultiError(errors)
	}

	return nil
}

// CreditCardRealTimeEligibilityCheckIntroScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// CreditCardRealTimeEligibilityCheckIntroScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type CreditCardRealTimeEligibilityCheckIntroScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardRealTimeEligibilityCheckIntroScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardRealTimeEligibilityCheckIntroScreenOptionsMultiError) AllErrors() []error { return m }

// CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError is the
// validation error returned by
// CreditCardRealTimeEligibilityCheckIntroScreenOptions.Validate if the
// designated constraints aren't met.
type CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError) ErrorName() string {
	return "CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardRealTimeEligibilityCheckIntroScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardRealTimeEligibilityCheckIntroScreenOptionsValidationError{}

// Validate checks the field values on SecuredCreditCardDepositScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SecuredCreditCardDepositScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecuredCreditCardDepositScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SecuredCreditCardDepositScreenOptionsMultiError, or nil if none found.
func (m *SecuredCreditCardDepositScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SecuredCreditCardDepositScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStepInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "StepInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "StepInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStepInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "StepInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopSectionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "TopSectionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "TopSectionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopSectionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "TopSectionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinDepositAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "MinDepositAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "MinDepositAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinDepositAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "MinDepositAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "BottomText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "BottomText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "BottomText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInsufficientBalanceCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "InsufficientBalanceCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "InsufficientBalanceCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInsufficientBalanceCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "InsufficientBalanceCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOpenDepositCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "OpenDepositCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "OpenDepositCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOpenDepositCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "OpenDepositCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIneligibleActionText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "IneligibleActionText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "IneligibleActionText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIneligibleActionText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "IneligibleActionText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddNomineeCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "AddNomineeCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "AddNomineeCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddNomineeCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "AddNomineeCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddNomineeVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "AddNomineeVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "AddNomineeVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddNomineeVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "AddNomineeVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTermsAndConditionsText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "TermsAndConditionsText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "TermsAndConditionsText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTermsAndConditionsText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "TermsAndConditionsText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDefaultCreditLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "DefaultCreditLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "DefaultCreditLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultCreditLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "DefaultCreditLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSuggestedCreditLimits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
						field:  fmt.Sprintf("SuggestedCreditLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
						field:  fmt.Sprintf("SuggestedCreditLimits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecuredCreditCardDepositScreenOptionsValidationError{
					field:  fmt.Sprintf("SuggestedCreditLimits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCreditLimitHeading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "CreditLimitHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "CreditLimitHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditLimitHeading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "CreditLimitHeading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDepositLimitHeading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "DepositLimitHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "DepositLimitHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDepositLimitHeading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "DepositLimitHeading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxDepositAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "MaxDepositAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "MaxDepositAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxDepositAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "MaxDepositAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinDepositText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "MinDepositText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "MinDepositText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinDepositText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "MinDepositText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxDepositText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "MaxDepositText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "MaxDepositText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxDepositText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "MaxDepositText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoTooltipCreditLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "InfoTooltipCreditLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "InfoTooltipCreditLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoTooltipCreditLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "InfoTooltipCreditLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoTooltipFixedDeposit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "InfoTooltipFixedDeposit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "InfoTooltipFixedDeposit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoTooltipFixedDeposit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "InfoTooltipFixedDeposit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBenefitsVisualElementCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "BenefitsVisualElementCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardDepositScreenOptionsValidationError{
					field:  "BenefitsVisualElementCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBenefitsVisualElementCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardDepositScreenOptionsValidationError{
				field:  "BenefitsVisualElementCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SecuredCreditCardDepositScreenOptionsMultiError(errors)
	}

	return nil
}

// SecuredCreditCardDepositScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// SecuredCreditCardDepositScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type SecuredCreditCardDepositScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecuredCreditCardDepositScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecuredCreditCardDepositScreenOptionsMultiError) AllErrors() []error { return m }

// SecuredCreditCardDepositScreenOptionsValidationError is the validation error
// returned by SecuredCreditCardDepositScreenOptions.Validate if the
// designated constraints aren't met.
type SecuredCreditCardDepositScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecuredCreditCardDepositScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecuredCreditCardDepositScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecuredCreditCardDepositScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecuredCreditCardDepositScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecuredCreditCardDepositScreenOptionsValidationError) ErrorName() string {
	return "SecuredCreditCardDepositScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SecuredCreditCardDepositScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecuredCreditCardDepositScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecuredCreditCardDepositScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecuredCreditCardDepositScreenOptionsValidationError{}

// Validate checks the field values on SecuredCreditCardFdDetailsScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SecuredCreditCardFdDetailsScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SecuredCreditCardFdDetailsScreenOptions with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// SecuredCreditCardFdDetailsScreenOptionsMultiError, or nil if none found.
func (m *SecuredCreditCardFdDetailsScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SecuredCreditCardFdDetailsScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardFdDetailsScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStepInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "StepInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "StepInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStepInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardFdDetailsScreenOptionsValidationError{
				field:  "StepInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "HeaderDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "HeaderDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardFdDetailsScreenOptionsValidationError{
				field:  "HeaderDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFdDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
						field:  fmt.Sprintf("FdDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
						field:  fmt.Sprintf("FdDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  fmt.Sprintf("FdDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetNextCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "NextCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "NextCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardFdDetailsScreenOptionsValidationError{
				field:  "NextCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FdCreationStatus

	// no validation rules for CardRequestId

	if all {
		switch v := interface{}(m.GetVeBankLogo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "VeBankLogo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCreditCardFdDetailsScreenOptionsValidationError{
					field:  "VeBankLogo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVeBankLogo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCreditCardFdDetailsScreenOptionsValidationError{
				field:  "VeBankLogo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SecuredCreditCardFdDetailsScreenOptionsMultiError(errors)
	}

	return nil
}

// SecuredCreditCardFdDetailsScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// SecuredCreditCardFdDetailsScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type SecuredCreditCardFdDetailsScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecuredCreditCardFdDetailsScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecuredCreditCardFdDetailsScreenOptionsMultiError) AllErrors() []error { return m }

// SecuredCreditCardFdDetailsScreenOptionsValidationError is the validation
// error returned by SecuredCreditCardFdDetailsScreenOptions.Validate if the
// designated constraints aren't met.
type SecuredCreditCardFdDetailsScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecuredCreditCardFdDetailsScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecuredCreditCardFdDetailsScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecuredCreditCardFdDetailsScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecuredCreditCardFdDetailsScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecuredCreditCardFdDetailsScreenOptionsValidationError) ErrorName() string {
	return "SecuredCreditCardFdDetailsScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SecuredCreditCardFdDetailsScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecuredCreditCardFdDetailsScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecuredCreditCardFdDetailsScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecuredCreditCardFdDetailsScreenOptionsValidationError{}

// Validate checks the field values on
// SecuredCcDepositTenureSelectionBottomSheetScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SecuredCcDepositTenureSelectionBottomSheetScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecuredCcDepositTenureSelectionBottomSheetScreenOptionsMultiError, or nil
// if none found.
func (m *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SecuredCcDepositTenureSelectionBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeadingText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "HeadingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "HeadingText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeadingText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
				field:  "HeadingText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTenureDaysText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "TenureDaysText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "TenureDaysText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTenureDaysText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
				field:  "TenureDaysText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTenureMonthText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "TenureMonthText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "TenureMonthText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTenureMonthText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
				field:  "TenureMonthText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetSliderTermDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("SliderTermDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("SliderTermDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  fmt.Sprintf("SliderTermDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetSelectedDepositTerm()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "SelectedDepositTerm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "SelectedDepositTerm",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedDepositTerm()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
				field:  "SelectedDepositTerm",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConfirmationCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "ConfirmationCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "ConfirmationCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConfirmationCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
				field:  "ConfirmationCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinDepositTermErrorText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "MinDepositTermErrorText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "MinDepositTermErrorText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinDepositTermErrorText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
				field:  "MinDepositTermErrorText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxDepositTermErrorText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "MaxDepositTermErrorText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  "MaxDepositTermErrorText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxDepositTermErrorText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
				field:  "MaxDepositTermErrorText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDurationSliderValues() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("DurationSliderValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("DurationSliderValues[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{
					field:  fmt.Sprintf("DurationSliderValues[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SecuredCcDepositTenureSelectionBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// SecuredCcDepositTenureSelectionBottomSheetScreenOptionsMultiError is an
// error wrapping multiple validation errors returned by
// SecuredCcDepositTenureSelectionBottomSheetScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type SecuredCcDepositTenureSelectionBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecuredCcDepositTenureSelectionBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecuredCcDepositTenureSelectionBottomSheetScreenOptionsMultiError) AllErrors() []error {
	return m
}

// SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError is
// the validation error returned by
// SecuredCcDepositTenureSelectionBottomSheetScreenOptions.Validate if the
// designated constraints aren't met.
type SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecuredCcDepositTenureSelectionBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecuredCcDepositTenureSelectionBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on SecuredCcDetailsBottomSheetScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *SecuredCcDetailsBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// SecuredCcDetailsBottomSheetScreenOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// SecuredCcDetailsBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *SecuredCcDetailsBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *SecuredCcDetailsBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDetailsBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDetailsBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDetailsBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecuredCcDetailsBottomSheetScreenOptionsValidationError{
					field:  "HeaderDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecuredCcDetailsBottomSheetScreenOptionsValidationError{
					field:  "HeaderDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecuredCcDetailsBottomSheetScreenOptionsValidationError{
				field:  "HeaderDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFeatureDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecuredCcDetailsBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("FeatureDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecuredCcDetailsBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("FeatureDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecuredCcDetailsBottomSheetScreenOptionsValidationError{
					field:  fmt.Sprintf("FeatureDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for FeatureBgColor

	if len(errors) > 0 {
		return SecuredCcDetailsBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// SecuredCcDetailsBottomSheetScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// SecuredCcDetailsBottomSheetScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type SecuredCcDetailsBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecuredCcDetailsBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecuredCcDetailsBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// SecuredCcDetailsBottomSheetScreenOptionsValidationError is the validation
// error returned by SecuredCcDetailsBottomSheetScreenOptions.Validate if the
// designated constraints aren't met.
type SecuredCcDetailsBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecuredCcDetailsBottomSheetScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecuredCcDetailsBottomSheetScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecuredCcDetailsBottomSheetScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecuredCcDetailsBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecuredCcDetailsBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "SecuredCcDetailsBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e SecuredCcDetailsBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecuredCcDetailsBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecuredCcDetailsBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecuredCcDetailsBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on CcIntroBottomSheetScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CcIntroBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CcIntroBottomSheetScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CcIntroBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *CcIntroBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcIntroBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroBottomSheetScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroBottomSheetScreenOptionsValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroBottomSheetScreenOptionsValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPromotion()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "Promotion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "Promotion",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPromotion()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroBottomSheetScreenOptionsValidationError{
				field:  "Promotion",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetButton()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "Button",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "Button",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetButton()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroBottomSheetScreenOptionsValidationError{
				field:  "Button",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroBottomSheetScreenOptionsValidationError{
					field:  "VisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroBottomSheetScreenOptionsValidationError{
				field:  "VisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CcIntroBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// CcIntroBottomSheetScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by CcIntroBottomSheetScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type CcIntroBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcIntroBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcIntroBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// CcIntroBottomSheetScreenOptionsValidationError is the validation error
// returned by CcIntroBottomSheetScreenOptions.Validate if the designated
// constraints aren't met.
type CcIntroBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcIntroBottomSheetScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcIntroBottomSheetScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcIntroBottomSheetScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcIntroBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcIntroBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "CcIntroBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcIntroBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcIntroBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcIntroBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcIntroBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on CcIntroScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CcIntroScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CcIntroScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CcIntroScreenOptionsMultiError, or nil if none found.
func (m *CcIntroScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcIntroScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScreenIdentifier

	// no validation rules for CardProgramType

	if all {
		switch v := interface{}(m.GetCardProgram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroScreenOptionsValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroScreenOptionsValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardProgram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroScreenOptionsValidationError{
				field:  "CardProgram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreditCardRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroScreenOptionsValidationError{
					field:  "CreditCardRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroScreenOptionsValidationError{
					field:  "CreditCardRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditCardRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroScreenOptionsValidationError{
				field:  "CreditCardRequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CcIntroScreenOptionsMultiError(errors)
	}

	return nil
}

// CcIntroScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by CcIntroScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CcIntroScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcIntroScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcIntroScreenOptionsMultiError) AllErrors() []error { return m }

// CcIntroScreenOptionsValidationError is the validation error returned by
// CcIntroScreenOptions.Validate if the designated constraints aren't met.
type CcIntroScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcIntroScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcIntroScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcIntroScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcIntroScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcIntroScreenOptionsValidationError) ErrorName() string {
	return "CcIntroScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcIntroScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcIntroScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcIntroScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcIntroScreenOptionsValidationError{}

// Validate checks the field values on FireflySyncPollStatusScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FireflySyncPollStatusScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FireflySyncPollStatusScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FireflySyncPollStatusScreenOptionsMultiError, or nil if none found.
func (m *FireflySyncPollStatusScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *FireflySyncPollStatusScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FireflySyncPollStatusScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FireflySyncPollStatusScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FireflySyncPollStatusScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardRequestId

	// no validation rules for DisplayMessage

	// no validation rules for RetryDelay

	// no validation rules for WorkflowId

	if all {
		switch v := interface{}(m.GetScreenImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FireflySyncPollStatusScreenOptionsValidationError{
					field:  "ScreenImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FireflySyncPollStatusScreenOptionsValidationError{
					field:  "ScreenImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FireflySyncPollStatusScreenOptionsValidationError{
				field:  "ScreenImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisplayMessageObject()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FireflySyncPollStatusScreenOptionsValidationError{
					field:  "DisplayMessageObject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FireflySyncPollStatusScreenOptionsValidationError{
					field:  "DisplayMessageObject",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayMessageObject()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FireflySyncPollStatusScreenOptionsValidationError{
				field:  "DisplayMessageObject",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FireflySyncPollStatusScreenOptionsValidationError{
					field:  "SubText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FireflySyncPollStatusScreenOptionsValidationError{
					field:  "SubText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FireflySyncPollStatusScreenOptionsValidationError{
				field:  "SubText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AttemptNumber

	// no validation rules for BgColor

	if len(errors) > 0 {
		return FireflySyncPollStatusScreenOptionsMultiError(errors)
	}

	return nil
}

// FireflySyncPollStatusScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// FireflySyncPollStatusScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type FireflySyncPollStatusScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FireflySyncPollStatusScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FireflySyncPollStatusScreenOptionsMultiError) AllErrors() []error { return m }

// FireflySyncPollStatusScreenOptionsValidationError is the validation error
// returned by FireflySyncPollStatusScreenOptions.Validate if the designated
// constraints aren't met.
type FireflySyncPollStatusScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FireflySyncPollStatusScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FireflySyncPollStatusScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FireflySyncPollStatusScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FireflySyncPollStatusScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FireflySyncPollStatusScreenOptionsValidationError) ErrorName() string {
	return "FireflySyncPollStatusScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e FireflySyncPollStatusScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFireflySyncPollStatusScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FireflySyncPollStatusScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FireflySyncPollStatusScreenOptionsValidationError{}

// Validate checks the field values on CreditCardSyncPollStatusScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreditCardSyncPollStatusScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditCardSyncPollStatusScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreditCardSyncPollStatusScreenOptionsMultiError, or nil if none found.
func (m *CreditCardSyncPollStatusScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardSyncPollStatusScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardSyncPollStatusScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardSyncPollStatusScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardSyncPollStatusScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardRequestId

	// no validation rules for WorkflowId

	if all {
		switch v := interface{}(m.GetScreenImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardSyncPollStatusScreenOptionsValidationError{
					field:  "ScreenImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardSyncPollStatusScreenOptionsValidationError{
					field:  "ScreenImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardSyncPollStatusScreenOptionsValidationError{
				field:  "ScreenImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisplayMessage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardSyncPollStatusScreenOptionsValidationError{
					field:  "DisplayMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardSyncPollStatusScreenOptionsValidationError{
					field:  "DisplayMessage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayMessage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardSyncPollStatusScreenOptionsValidationError{
				field:  "DisplayMessage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardSyncPollStatusScreenOptionsValidationError{
					field:  "SubText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardSyncPollStatusScreenOptionsValidationError{
					field:  "SubText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardSyncPollStatusScreenOptionsValidationError{
				field:  "SubText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AttemptNumber

	// no validation rules for RetryDelay

	if len(errors) > 0 {
		return CreditCardSyncPollStatusScreenOptionsMultiError(errors)
	}

	return nil
}

// CreditCardSyncPollStatusScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// CreditCardSyncPollStatusScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CreditCardSyncPollStatusScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardSyncPollStatusScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardSyncPollStatusScreenOptionsMultiError) AllErrors() []error { return m }

// CreditCardSyncPollStatusScreenOptionsValidationError is the validation error
// returned by CreditCardSyncPollStatusScreenOptions.Validate if the
// designated constraints aren't met.
type CreditCardSyncPollStatusScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardSyncPollStatusScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardSyncPollStatusScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardSyncPollStatusScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardSyncPollStatusScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardSyncPollStatusScreenOptionsValidationError) ErrorName() string {
	return "CreditCardSyncPollStatusScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardSyncPollStatusScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardSyncPollStatusScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardSyncPollStatusScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardSyncPollStatusScreenOptionsValidationError{}

// Validate checks the field values on CcAmpliFiScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CcAmpliFiScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CcAmpliFiScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CcAmpliFiScreenOptionsMultiError, or nil if none found.
func (m *CcAmpliFiScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcAmpliFiScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToolBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "ToolBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "ToolBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToolBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "ToolBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmpliFiCard()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "AmpliFiCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "AmpliFiCard",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmpliFiCard()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "AmpliFiCard",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDividerColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "DividerColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "DividerColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDividerColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "DividerColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmpliFiBenefit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "AmpliFiBenefit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "AmpliFiBenefit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmpliFiBenefit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "AmpliFiBenefit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTextWithHyperlinks()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "TextWithHyperlinks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "TextWithHyperlinks",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTextWithHyperlinks()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "TextWithHyperlinks",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSliderProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "SliderProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "SliderProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSliderProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "SliderProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFooterIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "FooterIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "FooterIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooterIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "FooterIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "ScreenBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcAmpliFiScreenOptionsValidationError{
					field:  "ScreenBackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcAmpliFiScreenOptionsValidationError{
				field:  "ScreenBackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AddressType

	// no validation rules for KycLevel

	// no validation rules for CardRequestId

	if len(errors) > 0 {
		return CcAmpliFiScreenOptionsMultiError(errors)
	}

	return nil
}

// CcAmpliFiScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by CcAmpliFiScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CcAmpliFiScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcAmpliFiScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcAmpliFiScreenOptionsMultiError) AllErrors() []error { return m }

// CcAmpliFiScreenOptionsValidationError is the validation error returned by
// CcAmpliFiScreenOptions.Validate if the designated constraints aren't met.
type CcAmpliFiScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcAmpliFiScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcAmpliFiScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcAmpliFiScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcAmpliFiScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcAmpliFiScreenOptionsValidationError) ErrorName() string {
	return "CcAmpliFiScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcAmpliFiScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcAmpliFiScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcAmpliFiScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcAmpliFiScreenOptionsValidationError{}

// Validate checks the field values on CcIneligibleUserScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CcIneligibleUserScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CcIneligibleUserScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CcIneligibleUserScreenOptionsMultiError, or nil if none found.
func (m *CcIneligibleUserScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcIneligibleUserScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIneligibleUserScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopSectionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "TopSectionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "TopSectionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopSectionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIneligibleUserScreenOptionsValidationError{
				field:  "TopSectionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSeparatorVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "SeparatorVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "SeparatorVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSeparatorVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIneligibleUserScreenOptionsValidationError{
				field:  "SeparatorVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomSectionHeading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "BottomSectionHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "BottomSectionHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomSectionHeading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIneligibleUserScreenOptionsValidationError{
				field:  "BottomSectionHeading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetIneligibleUserDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
						field:  fmt.Sprintf("IneligibleUserDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
						field:  fmt.Sprintf("IneligibleUserDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CcIneligibleUserScreenOptionsValidationError{
					field:  fmt.Sprintf("IneligibleUserDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetFooterText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "FooterText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "FooterText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFooterText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIneligibleUserScreenOptionsValidationError{
				field:  "FooterText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMainCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "MainCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIneligibleUserScreenOptionsValidationError{
					field:  "MainCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMainCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIneligibleUserScreenOptionsValidationError{
				field:  "MainCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CcIneligibleUserScreenOptionsMultiError(errors)
	}

	return nil
}

// CcIneligibleUserScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by CcIneligibleUserScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type CcIneligibleUserScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcIneligibleUserScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcIneligibleUserScreenOptionsMultiError) AllErrors() []error { return m }

// CcIneligibleUserScreenOptionsValidationError is the validation error
// returned by CcIneligibleUserScreenOptions.Validate if the designated
// constraints aren't met.
type CcIneligibleUserScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcIneligibleUserScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcIneligibleUserScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcIneligibleUserScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcIneligibleUserScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcIneligibleUserScreenOptionsValidationError) ErrorName() string {
	return "CcIneligibleUserScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcIneligibleUserScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcIneligibleUserScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcIneligibleUserScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcIneligibleUserScreenOptionsValidationError{}

// Validate checks the field values on IneligibleUserDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IneligibleUserDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IneligibleUserDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IneligibleUserDetailsMultiError, or nil if none found.
func (m *IneligibleUserDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *IneligibleUserDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBottomSectionDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IneligibleUserDetailsValidationError{
					field:  "BottomSectionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IneligibleUserDetailsValidationError{
					field:  "BottomSectionDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomSectionDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IneligibleUserDetailsValidationError{
				field:  "BottomSectionDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsCollapsed

	if all {
		switch v := interface{}(m.GetChevron()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IneligibleUserDetailsValidationError{
					field:  "Chevron",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IneligibleUserDetailsValidationError{
					field:  "Chevron",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChevron()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IneligibleUserDetailsValidationError{
				field:  "Chevron",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IneligibleUserDetailsMultiError(errors)
	}

	return nil
}

// IneligibleUserDetailsMultiError is an error wrapping multiple validation
// errors returned by IneligibleUserDetails.ValidateAll() if the designated
// constraints aren't met.
type IneligibleUserDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IneligibleUserDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IneligibleUserDetailsMultiError) AllErrors() []error { return m }

// IneligibleUserDetailsValidationError is the validation error returned by
// IneligibleUserDetails.Validate if the designated constraints aren't met.
type IneligibleUserDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IneligibleUserDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IneligibleUserDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IneligibleUserDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IneligibleUserDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IneligibleUserDetailsValidationError) ErrorName() string {
	return "IneligibleUserDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e IneligibleUserDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIneligibleUserDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IneligibleUserDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IneligibleUserDetailsValidationError{}

// Validate checks the field values on
// CreditCardBillingDetailsBottomViewScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CreditCardBillingDetailsBottomViewScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CreditCardBillingDetailsBottomViewScreenOptions with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// CreditCardBillingDetailsBottomViewScreenOptionsMultiError, or nil if none found.
func (m *CreditCardBillingDetailsBottomViewScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardBillingDetailsBottomViewScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillingDetailsBottomViewScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillingDetailsBottomViewScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillingDetailsBottomViewScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenHeading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardBillingDetailsBottomViewScreenOptionsValidationError{
					field:  "ScreenHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardBillingDetailsBottomViewScreenOptionsValidationError{
					field:  "ScreenHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenHeading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardBillingDetailsBottomViewScreenOptionsValidationError{
				field:  "ScreenHeading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBillingInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreditCardBillingDetailsBottomViewScreenOptionsValidationError{
						field:  fmt.Sprintf("BillingInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreditCardBillingDetailsBottomViewScreenOptionsValidationError{
						field:  fmt.Sprintf("BillingInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreditCardBillingDetailsBottomViewScreenOptionsValidationError{
					field:  fmt.Sprintf("BillingInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for BgColor

	if len(errors) > 0 {
		return CreditCardBillingDetailsBottomViewScreenOptionsMultiError(errors)
	}

	return nil
}

// CreditCardBillingDetailsBottomViewScreenOptionsMultiError is an error
// wrapping multiple validation errors returned by
// CreditCardBillingDetailsBottomViewScreenOptions.ValidateAll() if the
// designated constraints aren't met.
type CreditCardBillingDetailsBottomViewScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardBillingDetailsBottomViewScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardBillingDetailsBottomViewScreenOptionsMultiError) AllErrors() []error { return m }

// CreditCardBillingDetailsBottomViewScreenOptionsValidationError is the
// validation error returned by
// CreditCardBillingDetailsBottomViewScreenOptions.Validate if the designated
// constraints aren't met.
type CreditCardBillingDetailsBottomViewScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardBillingDetailsBottomViewScreenOptionsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CreditCardBillingDetailsBottomViewScreenOptionsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CreditCardBillingDetailsBottomViewScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardBillingDetailsBottomViewScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardBillingDetailsBottomViewScreenOptionsValidationError) ErrorName() string {
	return "CreditCardBillingDetailsBottomViewScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardBillingDetailsBottomViewScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardBillingDetailsBottomViewScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardBillingDetailsBottomViewScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardBillingDetailsBottomViewScreenOptionsValidationError{}

// Validate checks the field values on
// CcBillGenerationDateSelectionScreenOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CcBillGenerationDateSelectionScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CcBillGenerationDateSelectionScreenOptions with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CcBillGenerationDateSelectionScreenOptionsMultiError, or nil if none found.
func (m *CcBillGenerationDateSelectionScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcBillGenerationDateSelectionScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcBillGenerationDateSelectionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcBillGenerationDateSelectionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcBillGenerationDateSelectionScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardId

	// no validation rules for IsUpdate

	if len(errors) > 0 {
		return CcBillGenerationDateSelectionScreenOptionsMultiError(errors)
	}

	return nil
}

// CcBillGenerationDateSelectionScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// CcBillGenerationDateSelectionScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CcBillGenerationDateSelectionScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcBillGenerationDateSelectionScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcBillGenerationDateSelectionScreenOptionsMultiError) AllErrors() []error { return m }

// CcBillGenerationDateSelectionScreenOptionsValidationError is the validation
// error returned by CcBillGenerationDateSelectionScreenOptions.Validate if
// the designated constraints aren't met.
type CcBillGenerationDateSelectionScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcBillGenerationDateSelectionScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcBillGenerationDateSelectionScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcBillGenerationDateSelectionScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcBillGenerationDateSelectionScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcBillGenerationDateSelectionScreenOptionsValidationError) ErrorName() string {
	return "CcBillGenerationDateSelectionScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcBillGenerationDateSelectionScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcBillGenerationDateSelectionScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcBillGenerationDateSelectionScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcBillGenerationDateSelectionScreenOptionsValidationError{}

// Validate checks the field values on CreditCardTpapPaymentInitScreenOption
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CreditCardTpapPaymentInitScreenOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditCardTpapPaymentInitScreenOption
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CreditCardTpapPaymentInitScreenOptionMultiError, or nil if none found.
func (m *CreditCardTpapPaymentInitScreenOption) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardTpapPaymentInitScreenOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardTpapPaymentInitScreenOptionValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardTpapPaymentInitScreenOptionValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardTpapPaymentInitScreenOptionValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTransactionAttributes()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardTpapPaymentInitScreenOptionValidationError{
					field:  "TransactionAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardTpapPaymentInitScreenOptionValidationError{
					field:  "TransactionAttributes",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionAttributes()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardTpapPaymentInitScreenOptionValidationError{
				field:  "TransactionAttributes",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderId

	// no validation rules for PinRequiredType

	if len(errors) > 0 {
		return CreditCardTpapPaymentInitScreenOptionMultiError(errors)
	}

	return nil
}

// CreditCardTpapPaymentInitScreenOptionMultiError is an error wrapping
// multiple validation errors returned by
// CreditCardTpapPaymentInitScreenOption.ValidateAll() if the designated
// constraints aren't met.
type CreditCardTpapPaymentInitScreenOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardTpapPaymentInitScreenOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardTpapPaymentInitScreenOptionMultiError) AllErrors() []error { return m }

// CreditCardTpapPaymentInitScreenOptionValidationError is the validation error
// returned by CreditCardTpapPaymentInitScreenOption.Validate if the
// designated constraints aren't met.
type CreditCardTpapPaymentInitScreenOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardTpapPaymentInitScreenOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardTpapPaymentInitScreenOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardTpapPaymentInitScreenOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardTpapPaymentInitScreenOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardTpapPaymentInitScreenOptionValidationError) ErrorName() string {
	return "CreditCardTpapPaymentInitScreenOptionValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardTpapPaymentInitScreenOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardTpapPaymentInitScreenOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardTpapPaymentInitScreenOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardTpapPaymentInitScreenOptionValidationError{}

// Validate checks the field values on CcNetworkSelectionScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CcNetworkSelectionScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CcNetworkSelectionScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CcNetworkSelectionScreenOptionsMultiError, or nil if none found.
func (m *CcNetworkSelectionScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcNetworkSelectionScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcNetworkSelectionScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHeaderText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
					field:  "HeaderText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
					field:  "HeaderText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeaderText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcNetworkSelectionScreenOptionsValidationError{
				field:  "HeaderText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDescription()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
					field:  "Description",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDescription()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcNetworkSelectionScreenOptionsValidationError{
				field:  "Description",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetNetworkSelectionComponent() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
						field:  fmt.Sprintf("NetworkSelectionComponent[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
						field:  fmt.Sprintf("NetworkSelectionComponent[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CcNetworkSelectionScreenOptionsValidationError{
					field:  fmt.Sprintf("NetworkSelectionComponent[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPrimaryCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
					field:  "PrimaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptionsValidationError{
					field:  "PrimaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrimaryCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcNetworkSelectionScreenOptionsValidationError{
				field:  "PrimaryCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CcNetworkSelectionScreenOptionsMultiError(errors)
	}

	return nil
}

// CcNetworkSelectionScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by CcNetworkSelectionScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type CcNetworkSelectionScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcNetworkSelectionScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcNetworkSelectionScreenOptionsMultiError) AllErrors() []error { return m }

// CcNetworkSelectionScreenOptionsValidationError is the validation error
// returned by CcNetworkSelectionScreenOptions.Validate if the designated
// constraints aren't met.
type CcNetworkSelectionScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcNetworkSelectionScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcNetworkSelectionScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcNetworkSelectionScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcNetworkSelectionScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcNetworkSelectionScreenOptionsValidationError) ErrorName() string {
	return "CcNetworkSelectionScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcNetworkSelectionScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcNetworkSelectionScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcNetworkSelectionScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcNetworkSelectionScreenOptionsValidationError{}

// Validate checks the field values on CardTabScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardTabScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardTabScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardTabScreenOptionsMultiError, or nil if none found.
func (m *CardTabScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CardTabScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTabScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTabScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTabScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SelectedTab

	{
		sorted_keys := make([]string, len(m.GetTabToDeeplink()))
		i := 0
		for key := range m.GetTabToDeeplink() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetTabToDeeplink()[key]
			_ = val

			// no validation rules for TabToDeeplink[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, CardTabScreenOptionsValidationError{
							field:  fmt.Sprintf("TabToDeeplink[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, CardTabScreenOptionsValidationError{
							field:  fmt.Sprintf("TabToDeeplink[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return CardTabScreenOptionsValidationError{
						field:  fmt.Sprintf("TabToDeeplink[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return CardTabScreenOptionsMultiError(errors)
	}

	return nil
}

// CardTabScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by CardTabScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CardTabScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardTabScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardTabScreenOptionsMultiError) AllErrors() []error { return m }

// CardTabScreenOptionsValidationError is the validation error returned by
// CardTabScreenOptions.Validate if the designated constraints aren't met.
type CardTabScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardTabScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardTabScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardTabScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardTabScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardTabScreenOptionsValidationError) ErrorName() string {
	return "CardTabScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CardTabScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardTabScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardTabScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardTabScreenOptionsValidationError{}

// Validate checks the field values on CcConsentBottomSheetScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CcConsentBottomSheetScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CcConsentBottomSheetScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CcConsentBottomSheetScreenOptionsMultiError, or nil if none found.
func (m *CcConsentBottomSheetScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcConsentBottomSheetScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcConsentBottomSheetScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcConsentBottomSheetScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetConsentItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("ConsentItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
						field:  fmt.Sprintf("ConsentItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CcConsentBottomSheetScreenOptionsValidationError{
					field:  fmt.Sprintf("ConsentItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetProgramVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ProgramVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ProgramVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProgramVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcConsentBottomSheetScreenOptionsValidationError{
				field:  "ProgramVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcConsentBottomSheetScreenOptionsValidationError{
				field:  "ConsentCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentContainerProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentContainerProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentContainerProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcConsentBottomSheetScreenOptionsValidationError{
				field:  "ConsentContainerProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentCheckboxText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentCheckboxText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentCheckboxText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentCheckboxText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcConsentBottomSheetScreenOptionsValidationError{
				field:  "ConsentCheckboxText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CreditCardOnboardingConsent

	if all {
		switch v := interface{}(m.GetConsentCheckBox()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentCheckBox",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentCheckBox",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentCheckBox()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcConsentBottomSheetScreenOptionsValidationError{
				field:  "ConsentCheckBox",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcConsentBottomSheetScreenOptionsValidationError{
					field:  "ConsentText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcConsentBottomSheetScreenOptionsValidationError{
				field:  "ConsentText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CcConsentBottomSheetScreenOptionsMultiError(errors)
	}

	return nil
}

// CcConsentBottomSheetScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// CcConsentBottomSheetScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CcConsentBottomSheetScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcConsentBottomSheetScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcConsentBottomSheetScreenOptionsMultiError) AllErrors() []error { return m }

// CcConsentBottomSheetScreenOptionsValidationError is the validation error
// returned by CcConsentBottomSheetScreenOptions.Validate if the designated
// constraints aren't met.
type CcConsentBottomSheetScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcConsentBottomSheetScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcConsentBottomSheetScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcConsentBottomSheetScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcConsentBottomSheetScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcConsentBottomSheetScreenOptionsValidationError) ErrorName() string {
	return "CcConsentBottomSheetScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcConsentBottomSheetScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcConsentBottomSheetScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcConsentBottomSheetScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcConsentBottomSheetScreenOptionsValidationError{}

// Validate checks the field values on ConsentItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConsentItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsentItem with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConsentItemMultiError, or
// nil if none found.
func (m *ConsentItem) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsentItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsentItemValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsentItemValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsentItemValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsentItemValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsentItemValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsentItemValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLeftVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsentItemValidationError{
					field:  "LeftVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsentItemValidationError{
					field:  "LeftVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLeftVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsentItemValidationError{
				field:  "LeftVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ConsentItemValidationError{
					field:  "BottomVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ConsentItemValidationError{
					field:  "BottomVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ConsentItemValidationError{
				field:  "BottomVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ConsentItemMultiError(errors)
	}

	return nil
}

// ConsentItemMultiError is an error wrapping multiple validation errors
// returned by ConsentItem.ValidateAll() if the designated constraints aren't met.
type ConsentItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsentItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsentItemMultiError) AllErrors() []error { return m }

// ConsentItemValidationError is the validation error returned by
// ConsentItem.Validate if the designated constraints aren't met.
type ConsentItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsentItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsentItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsentItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsentItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsentItemValidationError) ErrorName() string { return "ConsentItemValidationError" }

// Error satisfies the builtin error interface
func (e ConsentItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsentItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsentItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsentItemValidationError{}

// Validate checks the field values on CcCreditReportAddressScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CcCreditReportAddressScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CcCreditReportAddressScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CcCreditReportAddressScreenOptionsMultiError, or nil if none found.
func (m *CcCreditReportAddressScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcCreditReportAddressScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcCreditReportAddressScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToolBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "ToolBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "ToolBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToolBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcCreditReportAddressScreenOptionsValidationError{
				field:  "ToolBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopSection()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "TopSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "TopSection",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopSection()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcCreditReportAddressScreenOptionsValidationError{
				field:  "TopSection",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddressSectionProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "AddressSectionProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "AddressSectionProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddressSectionProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcCreditReportAddressScreenOptionsValidationError{
				field:  "AddressSectionProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContinueCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "ContinueCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "ContinueCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContinueCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcCreditReportAddressScreenOptionsValidationError{
				field:  "ContinueCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColor()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "BackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "BackgroundColor",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColor()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcCreditReportAddressScreenOptionsValidationError{
				field:  "BackgroundColor",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddAddressCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "AddAddressCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcCreditReportAddressScreenOptionsValidationError{
					field:  "AddAddressCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddAddressCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcCreditReportAddressScreenOptionsValidationError{
				field:  "AddAddressCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardRequestId

	// no validation rules for IsLocationPermissionRequired

	if len(errors) > 0 {
		return CcCreditReportAddressScreenOptionsMultiError(errors)
	}

	return nil
}

// CcCreditReportAddressScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// CcCreditReportAddressScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CcCreditReportAddressScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcCreditReportAddressScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcCreditReportAddressScreenOptionsMultiError) AllErrors() []error { return m }

// CcCreditReportAddressScreenOptionsValidationError is the validation error
// returned by CcCreditReportAddressScreenOptions.Validate if the designated
// constraints aren't met.
type CcCreditReportAddressScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcCreditReportAddressScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcCreditReportAddressScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcCreditReportAddressScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcCreditReportAddressScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcCreditReportAddressScreenOptionsValidationError) ErrorName() string {
	return "CcCreditReportAddressScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcCreditReportAddressScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcCreditReportAddressScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcCreditReportAddressScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcCreditReportAddressScreenOptionsValidationError{}

// Validate checks the field values on CcUserIneligibleTransitionScreenOptions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *CcUserIneligibleTransitionScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CcUserIneligibleTransitionScreenOptions with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// CcUserIneligibleTransitionScreenOptionsMultiError, or nil if none found.
func (m *CcUserIneligibleTransitionScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcUserIneligibleTransitionScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcUserIneligibleTransitionScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetScreenHeaderVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "ScreenHeaderVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "ScreenHeaderVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetScreenHeaderVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcUserIneligibleTransitionScreenOptionsValidationError{
				field:  "ScreenHeaderVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcUserIneligibleTransitionScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "SubTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcUserIneligibleTransitionScreenOptionsValidationError{
				field:  "SubTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "InfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "InfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcUserIneligibleTransitionScreenOptionsValidationError{
				field:  "InfoText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInfoVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "InfoVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "InfoVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcUserIneligibleTransitionScreenOptionsValidationError{
				field:  "InfoVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetShareFeedbackCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "ShareFeedbackCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "ShareFeedbackCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetShareFeedbackCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcUserIneligibleTransitionScreenOptionsValidationError{
				field:  "ShareFeedbackCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGetCardCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "GetCardCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "GetCardCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGetCardCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcUserIneligibleTransitionScreenOptionsValidationError{
				field:  "GetCardCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToolBar()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "ToolBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcUserIneligibleTransitionScreenOptionsValidationError{
					field:  "ToolBar",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToolBar()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcUserIneligibleTransitionScreenOptionsValidationError{
				field:  "ToolBar",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CcUserIneligibleTransitionScreenOptionsMultiError(errors)
	}

	return nil
}

// CcUserIneligibleTransitionScreenOptionsMultiError is an error wrapping
// multiple validation errors returned by
// CcUserIneligibleTransitionScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CcUserIneligibleTransitionScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcUserIneligibleTransitionScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcUserIneligibleTransitionScreenOptionsMultiError) AllErrors() []error { return m }

// CcUserIneligibleTransitionScreenOptionsValidationError is the validation
// error returned by CcUserIneligibleTransitionScreenOptions.Validate if the
// designated constraints aren't met.
type CcUserIneligibleTransitionScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcUserIneligibleTransitionScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcUserIneligibleTransitionScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcUserIneligibleTransitionScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcUserIneligibleTransitionScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcUserIneligibleTransitionScreenOptionsValidationError) ErrorName() string {
	return "CcUserIneligibleTransitionScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcUserIneligibleTransitionScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcUserIneligibleTransitionScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcUserIneligibleTransitionScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcUserIneligibleTransitionScreenOptionsValidationError{}

// Validate checks the field values on CCCreditLimitUpdateScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CCCreditLimitUpdateScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CCCreditLimitUpdateScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CCCreditLimitUpdateScreenOptionsMultiError, or nil if none found.
func (m *CCCreditLimitUpdateScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CCCreditLimitUpdateScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCreditLimitUpdateScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCreditLimitUpdateScreenOptionsValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTopVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "TopVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "TopVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTopVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCreditLimitUpdateScreenOptionsValidationError{
				field:  "TopVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCreditLimitUpdateScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCreditLimitUpdateScreenOptionsValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreditLimitInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "CreditLimitInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "CreditLimitInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditLimitInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCreditLimitUpdateScreenOptionsValidationError{
				field:  "CreditLimitInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPrimaryCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "PrimaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "PrimaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrimaryCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCreditLimitUpdateScreenOptionsValidationError{
				field:  "PrimaryCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSecondaryCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "SecondaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCreditLimitUpdateScreenOptionsValidationError{
					field:  "SecondaryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecondaryCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCreditLimitUpdateScreenOptionsValidationError{
				field:  "SecondaryCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardRequestId

	if len(errors) > 0 {
		return CCCreditLimitUpdateScreenOptionsMultiError(errors)
	}

	return nil
}

// CCCreditLimitUpdateScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by
// CCCreditLimitUpdateScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type CCCreditLimitUpdateScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CCCreditLimitUpdateScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CCCreditLimitUpdateScreenOptionsMultiError) AllErrors() []error { return m }

// CCCreditLimitUpdateScreenOptionsValidationError is the validation error
// returned by CCCreditLimitUpdateScreenOptions.Validate if the designated
// constraints aren't met.
type CCCreditLimitUpdateScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CCCreditLimitUpdateScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CCCreditLimitUpdateScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CCCreditLimitUpdateScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CCCreditLimitUpdateScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CCCreditLimitUpdateScreenOptionsValidationError) ErrorName() string {
	return "CCCreditLimitUpdateScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CCCreditLimitUpdateScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCCCreditLimitUpdateScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CCCreditLimitUpdateScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CCCreditLimitUpdateScreenOptionsValidationError{}

// Validate checks the field values on CCCardTnCConsentScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CCCardTnCConsentScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CCCardTnCConsentScreenOptions with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CCCardTnCConsentScreenOptionsMultiError, or nil if none found.
func (m *CCCardTnCConsentScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CCCardTnCConsentScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCardTnCConsentScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBackgroundColour()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "BackgroundColour",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBackgroundColour()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCardTnCConsentScreenOptionsValidationError{
				field:  "BackgroundColour",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "Title",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCardTnCConsentScreenOptionsValidationError{
				field:  "Title",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "Subtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCardTnCConsentScreenOptionsValidationError{
				field:  "Subtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardBenefitsContainerProperty()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "CardBenefitsContainerProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "CardBenefitsContainerProperty",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardBenefitsContainerProperty()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCardTnCConsentScreenOptionsValidationError{
				field:  "CardBenefitsContainerProperty",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCardBenefits() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
						field:  fmt.Sprintf("CardBenefits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
						field:  fmt.Sprintf("CardBenefits[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CCCardTnCConsentScreenOptionsValidationError{
					field:  fmt.Sprintf("CardBenefits[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "Cta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCardTnCConsentScreenOptionsValidationError{
				field:  "Cta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentCheckBox()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "ConsentCheckBox",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "ConsentCheckBox",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentCheckBox()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCardTnCConsentScreenOptionsValidationError{
				field:  "ConsentCheckBox",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "ConsentText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "ConsentText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCardTnCConsentScreenOptionsValidationError{
				field:  "ConsentText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBottomVisualElement()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "BottomVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CCCardTnCConsentScreenOptionsValidationError{
					field:  "BottomVisualElement",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBottomVisualElement()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CCCardTnCConsentScreenOptionsValidationError{
				field:  "BottomVisualElement",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardRequestId

	if len(errors) > 0 {
		return CCCardTnCConsentScreenOptionsMultiError(errors)
	}

	return nil
}

// CCCardTnCConsentScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by CCCardTnCConsentScreenOptions.ValidateAll()
// if the designated constraints aren't met.
type CCCardTnCConsentScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CCCardTnCConsentScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CCCardTnCConsentScreenOptionsMultiError) AllErrors() []error { return m }

// CCCardTnCConsentScreenOptionsValidationError is the validation error
// returned by CCCardTnCConsentScreenOptions.Validate if the designated
// constraints aren't met.
type CCCardTnCConsentScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CCCardTnCConsentScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CCCardTnCConsentScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CCCardTnCConsentScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CCCardTnCConsentScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CCCardTnCConsentScreenOptionsValidationError) ErrorName() string {
	return "CCCardTnCConsentScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CCCardTnCConsentScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCCCardTnCConsentScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CCCardTnCConsentScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CCCardTnCConsentScreenOptionsValidationError{}

// Validate checks the field values on GenericScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenericScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenericScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenericScreenOptionsMultiError, or nil if none found.
func (m *GenericScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *GenericScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenericScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenericScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenericScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ScreenId

	// no validation rules for Metadata

	if len(errors) > 0 {
		return GenericScreenOptionsMultiError(errors)
	}

	return nil
}

// GenericScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by GenericScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type GenericScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenericScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenericScreenOptionsMultiError) AllErrors() []error { return m }

// GenericScreenOptionsValidationError is the validation error returned by
// GenericScreenOptions.Validate if the designated constraints aren't met.
type GenericScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenericScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenericScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenericScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenericScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenericScreenOptionsValidationError) ErrorName() string {
	return "GenericScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e GenericScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenericScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenericScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenericScreenOptionsValidationError{}

// Validate checks the field values on CcIntroScreenV2ScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CcIntroScreenV2ScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CcIntroScreenV2ScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CcIntroScreenV2ScreenOptionsMultiError, or nil if none found.
func (m *CcIntroScreenV2ScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *CcIntroScreenV2ScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroScreenV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroScreenV2ScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroScreenV2ScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLoaderAnimation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroScreenV2ScreenOptionsValidationError{
					field:  "LoaderAnimation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroScreenV2ScreenOptionsValidationError{
					field:  "LoaderAnimation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoaderAnimation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroScreenV2ScreenOptionsValidationError{
				field:  "LoaderAnimation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Metadata

	if all {
		switch v := interface{}(m.GetBgImage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroScreenV2ScreenOptionsValidationError{
					field:  "BgImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroScreenV2ScreenOptionsValidationError{
					field:  "BgImage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBgImage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroScreenV2ScreenOptionsValidationError{
				field:  "BgImage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreditCardRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcIntroScreenV2ScreenOptionsValidationError{
					field:  "CreditCardRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcIntroScreenV2ScreenOptionsValidationError{
					field:  "CreditCardRequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditCardRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcIntroScreenV2ScreenOptionsValidationError{
				field:  "CreditCardRequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CcIntroScreenV2ScreenOptionsMultiError(errors)
	}

	return nil
}

// CcIntroScreenV2ScreenOptionsMultiError is an error wrapping multiple
// validation errors returned by CcIntroScreenV2ScreenOptions.ValidateAll() if
// the designated constraints aren't met.
type CcIntroScreenV2ScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcIntroScreenV2ScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcIntroScreenV2ScreenOptionsMultiError) AllErrors() []error { return m }

// CcIntroScreenV2ScreenOptionsValidationError is the validation error returned
// by CcIntroScreenV2ScreenOptions.Validate if the designated constraints
// aren't met.
type CcIntroScreenV2ScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcIntroScreenV2ScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CcIntroScreenV2ScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CcIntroScreenV2ScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CcIntroScreenV2ScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CcIntroScreenV2ScreenOptionsValidationError) ErrorName() string {
	return "CcIntroScreenV2ScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e CcIntroScreenV2ScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcIntroScreenV2ScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcIntroScreenV2ScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcIntroScreenV2ScreenOptionsValidationError{}

// Validate checks the field values on CreditCardMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditCardMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditCardMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreditCardMetadataMultiError, or nil if none found.
func (m *CreditCardMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Metadata.(type) {
	case *CreditCardMetadata_IntroScreenV2Metadata:
		if v == nil {
			err := CreditCardMetadataValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetIntroScreenV2Metadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CreditCardMetadataValidationError{
						field:  "IntroScreenV2Metadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CreditCardMetadataValidationError{
						field:  "IntroScreenV2Metadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetIntroScreenV2Metadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CreditCardMetadataValidationError{
					field:  "IntroScreenV2Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CreditCardMetadataMultiError(errors)
	}

	return nil
}

// CreditCardMetadataMultiError is an error wrapping multiple validation errors
// returned by CreditCardMetadata.ValidateAll() if the designated constraints
// aren't met.
type CreditCardMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardMetadataMultiError) AllErrors() []error { return m }

// CreditCardMetadataValidationError is the validation error returned by
// CreditCardMetadata.Validate if the designated constraints aren't met.
type CreditCardMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardMetadataValidationError) ErrorName() string {
	return "CreditCardMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardMetadataValidationError{}

// Validate checks the field values on IntroScreenV2Metadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IntroScreenV2Metadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IntroScreenV2Metadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IntroScreenV2MetadataMultiError, or nil if none found.
func (m *IntroScreenV2Metadata) ValidateAll() error {
	return m.validate(true)
}

func (m *IntroScreenV2Metadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardState

	// no validation rules for OnboardingRequestStatus

	// no validation rules for IsUserCcEligible

	if len(errors) > 0 {
		return IntroScreenV2MetadataMultiError(errors)
	}

	return nil
}

// IntroScreenV2MetadataMultiError is an error wrapping multiple validation
// errors returned by IntroScreenV2Metadata.ValidateAll() if the designated
// constraints aren't met.
type IntroScreenV2MetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IntroScreenV2MetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IntroScreenV2MetadataMultiError) AllErrors() []error { return m }

// IntroScreenV2MetadataValidationError is the validation error returned by
// IntroScreenV2Metadata.Validate if the designated constraints aren't met.
type IntroScreenV2MetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IntroScreenV2MetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IntroScreenV2MetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IntroScreenV2MetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IntroScreenV2MetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IntroScreenV2MetadataValidationError) ErrorName() string {
	return "IntroScreenV2MetadataValidationError"
}

// Error satisfies the builtin error interface
func (e IntroScreenV2MetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIntroScreenV2Metadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IntroScreenV2MetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IntroScreenV2MetadataValidationError{}

// Validate checks the field values on
// CcNetworkSelectionScreenOptions_NetworkSelectionComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CcNetworkSelectionScreenOptions_NetworkSelectionComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CcNetworkSelectionScreenOptions_NetworkSelectionComponent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CcNetworkSelectionScreenOptions_NetworkSelectionComponentMultiError, or nil
// if none found.
func (m *CcNetworkSelectionScreenOptions_NetworkSelectionComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *CcNetworkSelectionScreenOptions_NetworkSelectionComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetVerticalIconTextComponent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError{
					field:  "VerticalIconTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError{
					field:  "VerticalIconTextComponent",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVerticalIconTextComponent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError{
				field:  "VerticalIconTextComponent",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CardNetworkType

	if len(errors) > 0 {
		return CcNetworkSelectionScreenOptions_NetworkSelectionComponentMultiError(errors)
	}

	return nil
}

// CcNetworkSelectionScreenOptions_NetworkSelectionComponentMultiError is an
// error wrapping multiple validation errors returned by
// CcNetworkSelectionScreenOptions_NetworkSelectionComponent.ValidateAll() if
// the designated constraints aren't met.
type CcNetworkSelectionScreenOptions_NetworkSelectionComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CcNetworkSelectionScreenOptions_NetworkSelectionComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CcNetworkSelectionScreenOptions_NetworkSelectionComponentMultiError) AllErrors() []error {
	return m
}

// CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError is
// the validation error returned by
// CcNetworkSelectionScreenOptions_NetworkSelectionComponent.Validate if the
// designated constraints aren't met.
type CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError) ErrorName() string {
	return "CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError"
}

// Error satisfies the builtin error interface
func (e CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCcNetworkSelectionScreenOptions_NetworkSelectionComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CcNetworkSelectionScreenOptions_NetworkSelectionComponentValidationError{}
