// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/feature.proto

package typesv2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// possible features available to the epiFi users
type Feature int32

const (
	Feature_FEATURE_UNSPECIFIED Feature = 0
	// INVESTMENT_MF_UI feature controls visibility of UI elements to invest in mutual fund
	Feature_INVESTMENT_MF_UI Feature = 1
	// PAY_VIA_PHONE_NUMBER feature controls if user is allowed to search VPAs by phone number
	Feature_PAY_VIA_PHONE_NUMBER Feature = 2
	// MF_ADVANCE_FILTER feature controls if user can use Advance filter for exploring Mutual funds or not.
	Feature_MF_ADVANCE_FILTER Feature = 3
	// MF_TEXT_SEARCH feature controls if user can use text search for exploring Mutual funds or not.
	Feature_MF_TEXT_SEARCH Feature = 4
	// DISPUTE_FOR_OFF_APP_TXN feature controls if users/support agents raise dispute for off app txns
	Feature_DISPUTE_FOR_OFF_APP_TXN Feature = 5
	// MF_SIP feature controls if we would be allowing amc specific SIPs for certain funds
	Feature_MF_SIP Feature = 6
	// MF_NEW_OTI_PAYMENT_FLOW controls which payment flow to use for one time payments
	// We currently call Pay's frontend rpc for executing payments and need to migrate to backend rpc for payment
	// This flag would be governing this switch
	Feature_MF_NEW_OTI_PAYMENT_FLOW Feature = 7
	// SHOW_SUPPORT_TICKETS_IN_APP controls if users can see the support tickets in the app
	Feature_SHOW_SUPPORT_TICKETS_IN_APP Feature = 8
	// HOME_PAGE_LAYOUT_V2 feature indicates that the new home page layout api needs to be used for the actor
	Feature_HOME_PAGE_LAYOUT_V2 Feature = 9
	// MERCHANT_ANALYSER controls if user can see merchant analyser banner and screen
	Feature_MERCHANT_ANALYSER Feature = 10
	// TIME_ANALYSER controls if user can see time analyser, banner and time filter v2 implementation
	Feature_TIME_ANALYSER Feature = 11
	// SENSEFORTH_CHATBOT feature controls if senseforth chabot webview has to be loaded for the users on a mobile client
	Feature_SENSEFORTH_CHATBOT Feature = 12
	// AA_FINVU_TOKEN_AUTHENTICATION feature controls if token authentication is to be used in Finvu flows
	Feature_AA_FINVU_TOKEN_AUTHENTICATION Feature = 13
	// Controls whether to show JumpV2 to user or not
	Feature_JUMP_V2 Feature = 14
	// CATEGORY_ANALYSER_ADD_FUNDS_BANNER flag controls if add funds banner can be shown to the user
	Feature_CATEGORY_ANALYSER_ADD_FUNDS_BANNER Feature = 15
	// Controls if upi tpap if enabled for user or not
	Feature_UPI_TPAP Feature = 16
	// Controls entry to credit score analyser
	Feature_CREDIT_SCORE_ANALYSER Feature = 17
	// controls which users are eligible for feedback
	Feature_ANALYSER_FEEDBACK Feature = 18
	// controls the users eligible for fi minute hub banner
	Feature_FI_MINUTE_HUB_BANNER Feature = 19
	// US_STOCK_UI feature controls visibility of UI elements to invest in us stocks
	Feature_US_STOCK_UI Feature = 20
	// US_STOCK_LANDING_PAGE_UI feature controls visibility of landing page to the user to invest in us stocks
	// Pre-launch page is shown instead of landing for users not allowed for this feature
	Feature_US_STOCK_LANDING_PAGE_UI Feature = 21
	// controls which users are eligible to see/claim the salaryprogram health insurance benefit.
	Feature_SALARY_PROGRAM_HEALTH_INSURANCE_BENEFIT Feature = 22
	Feature_ASK_FI_HOME_SEARCH_BAR                  Feature = 23
	// Referrals V1 landing page flag
	Feature_REFERRALS_V1_LANDING_PAGE Feature = 24
	// controls whether to enable jump renewal flow
	Feature_JUMP_RENEWAL_FLOW Feature = 25
	// Controls if upi mapper if enabled for user or not
	Feature_UPI_MAPPER               Feature = 26
	Feature_CREDIT_SCORE_ANALYSER_V2 Feature = 27
	// Controls if the new QR SDK is enable for user or not
	Feature_ML_KIT_QR Feature = 28
	// control flag on client side to enable vkyc next action approch
	Feature_ENABLE_GET_VKYC_NEXT_ACTION Feature = 29
	// Controls flag on client side to enable or disable 2FA for mutual fund one time purchase flow
	Feature_ENABLE_2FA_MF_ONE_TIME_BUY Feature = 30
	// Controls flag on client side to enable or disable 2FA for sip registration flow
	Feature_ENABLE_2FA_MF_REGISTER_SIP Feature = 31
	// Controls flag to convert autoInvest orders to SIP orders.
	Feature_ENABLE_MF_FIT_SIP_CONVERSION Feature = 32
	// TIME_ANALYSER_INSIGHTS flag controls if we should generate new insights or continue showing the older insights.
	Feature_TIME_ANALYSER_INSIGHTS Feature = 33
	// CATEGORY_ANALYSER_ACCOUNT_FILTER flags controls release of account filter in category analyser.
	// old bank account filter will get disabled for users who are eligible for the account filter.
	Feature_CATEGORY_ANALYSER_ACCOUNT_FILTER_V2 Feature = 34
	// Feature used to perform AB testing on the ordering of investment landing components
	Feature_INVESTMENT_LANDING_COMPONENTS_ORDERING Feature = 35
	// feature used to perform controlled rollout and AB for referral link generation at client side
	Feature_REFERRAL_LINK_GENERATION_AT_CLIENT_SIDE Feature = 36
	// feature for controlled rollout of connected accounts as a screener check in onboarding
	Feature_FEATURE_CONNECTED_ACCOUNTS_IN_SCREENER Feature = 37
	// feature to enable jump dashboard with zero values to non invested users
	Feature_JUMP_DASHBOARD_FOR_NON_INVESTED_USERS Feature = 38
	// feature to perform A/B testing on interest rate for fixed deposit (all templates)
	Feature_FIXED_DEPOSIT_INTEREST_RATES Feature = 39
	// feature to perform A/B testing on default term for fixed deposit custom template
	Feature_FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM Feature = 40
	// feature to show new txn receipt screen v1 to the user group
	Feature_TXN_RECEIPT_SCREEN_V1 Feature = 41
	// feature to perform A/B testing on home search bar chips
	Feature_HOME_SEARCH_BAR_CHIPS Feature = 42
	// feature to check if qr should be validated for
	// the user by client at their end or they should
	// use ResolveQrData FE rpc
	Feature_VERIFY_QR_V1 Feature = 43
	// feature used to perform controlled rollout and AB for new referral page during onboarding
	Feature_REFERRAL_SCREEN_DURING_ONBOARDING_V1 Feature = 44
	// feature used to perform controlled rollout and AB for new affluent user bonus transition screen during onboarding
	// this screen is currently being shown only for users who belong to particular affluence classes and have onboarded via regular referral
	Feature_AFFLUENT_USER_BONUS_TRANSITION_SCREEN Feature = 45
	// feature to enable health engine for payments
	Feature_HEALTH_ENGINE_FOR_PAYMENTS Feature = 46
	// feature to perform A/B testing for different eligibility of user and scheme in JUMP
	Feature_JUMP_USER_SCHEME_ELIGIBILITY Feature = 47
	// feature used to control the view of upcoming transactions line item in time analyser
	Feature_TIME_ANALYSER_UPCOMING_TRANSACTIONS Feature = 48
	// feature used to perform controlled rollout and testing of aadhaar number flow for upi pin set
	Feature_UPI_PIN_SET_USING_AADHAAR Feature = 49
	// feature perform vkyc then add funds
	Feature_PRIORITIES_VKYC_OVER_ADD_FUNDS Feature = 50
	// feature controls configurable zero states to be shown in analysers
	Feature_ANALYSER_ZERO_STATES Feature = 51
	// Feature used to perform AB testing on the deeplink for Mutual Funds in investment instruments section of investment landing
	Feature_INVESTMENT_LANDING_MUTUAL_FUNDS_DEEPLINK Feature = 52
	// feature used to perform controlled rollout and testing of international payments for upi accounts
	Feature_UPI_INTERNATIONAL_PAYMENT Feature = 53
	// whether to show Fi credit card txn data in askfi or not
	Feature_CC_TXN_DATA_IN_ASKFI Feature = 54
	// Feature used to perform AB testing on the home banner for investment products eg. MF, usstocks
	Feature_INVESTMENT_HOME_COMPONENT Feature = 55
	// Feature for Connected Account AA Consent Renewal
	Feature_AA_CONSENT_RENEWAL Feature = 56
	// Feature used to perform AB testing on the Invest landing banner products eg. MF, usstocks, P2P, deposits etc.
	Feature_INVEST_LANDING_BANNER Feature = 57
	// Feature for Jump Maturity Consent flow
	Feature_JUMP_MATURITY_CONSENT Feature = 58
	// Feature used to perform controlled rollout and testing of UPI Pin Flow Error Ticket Creation
	Feature_PAY_INCIDENT_MANAGER_UPI_PIN_FLOW_ERROR Feature = 59
	// Feature for fund transfer via Celestial rollout
	Feature_FUND_TRANSFER_V1 Feature = 60
	// US_STOCK_ETF_SUPPORT feature controls visibility of ETFs to the user to invest in ETFs
	Feature_US_STOCK_ETF_SUPPORT Feature = 61
	// ONBOARDING_ADD_FUNDS_V2 feature to perform AB testing and enabling/disabling of ONBOARDING_ADD_FUNDS_V2 screen
	Feature_ONBOARDING_ADD_FUNDS_V2 Feature = 62
	// FRESHDESK_MONORAIL_INTEGRATION feature to control creation of Monorail tickets when a user's issue is escalated from Freshdesk
	Feature_FRESHDESK_MONORAIL_INTEGRATION Feature = 63
	// feature used to perform a controlled rollout and testing of credit card linking for upi payments
	Feature_CC_UPI_LINKING Feature = 64
	// ConnectedAccount: Connect Fi To Fi feature for connecting Fi Federal saving bank account
	Feature_CA_CONNECT_FI_TO_FI Feature = 65
	// Feature used to perform controlled rollout and testing of P2P deemed transaction Ticket Creation/Resolution
	Feature_PAY_INCIDENT_MANAGER_P2P_DEEMED_TRANSACTION Feature = 66
	// Feature used to perform controlled rollout and testing of P2M deemed transaction Ticket Creation/Resolution
	Feature_PAY_INCIDENT_MANAGER_P2M_DEEMED_TRANSACTION Feature = 67
	// Jump all activity deeplink support
	Feature_JUMP_ALL_ACTIVITY_DEEPLINK Feature = 68
	// NEW_VPA_HANDLE controls feature to assign new vpa handles to users i.e @fifederal
	Feature_NEW_VPA_HANDLE Feature = 69
	// Feature used to perform controlled rollout and testing of updated benefits on adding funds for affluent users
	Feature_UPDATED_BENEFITS_FOR_AFFLUENT_USER_ONB_ADD_FUNDS Feature = 70
	// Feature used to perform controlled rollout and testing for realtime credit card eligibility for a user
	Feature_FEATURE_CC_REALTIME_ELIGIBILITY Feature = 71
	// show new profile details bottom sheet in credit score analyser
	Feature_CREDIT_SCORE_ANALYSER_PROFILE_DETAILS_V2 Feature = 72
	// Feature used to show backend configurable feedback surveys to users, as part of inapphelp service
	Feature_INAPPHELP_FEEDBACK_ENGINE Feature = 73
	// Feature to fetch order with transactions using in memory join instead of db-join (db-optimisation)
	Feature_FETCH_ORDER_WITH_TRANSACTIONS_USING_IN_MEMORY_JOIN Feature = 74
	// feature used to perform controlled rollout and AB for new affluent user bonus transition screen for non referees during onboarding
	// this screen is currently being shown only for users who belong to particular affluence classes and are non refrees
	Feature_AFFLUENT_USER_BONUS_TRANSITION_SCREEN_NON_REFEREE Feature = 75
	// feature to perform A/B testing for stacked referral rewards in referrals landing v1 page
	Feature_REFERRALS_V1_LANDING_PAGE_STACKED_REWARDS_COMPONENT Feature = 76
	// Feature used to perform controlled rollout and testing of CC EMI flows on AmpliFi
	Feature_FEATURE_CC_EMI Feature = 77
	// feature used to perform controlled rollout of new onboarding API for credit cards
	Feature_CC_REGISTER_CUSTOMER_V2 Feature = 78
	// Feature used to perform controlled rollout and testing of Jump invest v2 page
	Feature_FEATURE_JUMP_INVEST_PAGE_V2 Feature = 79
	// Feature used to perform AB for component on usstocks landing page
	Feature_USS_LANDING_PAGE_AB_SUPPORT Feature = 80
	// Feature used to perform AB for collection on usstocks collection screen
	Feature_USS_COLLECTION_SCREEN_AB_SUPPORT Feature = 81
	// feature to display phone number screen in mf holdings import flow
	Feature_MF_HOLDINGS_IMPORT_PHONE_NUMBER_SCREEN Feature = 82
	// CREDIT_SCORE_ANALYSER_AUTO_REFRESH is used to control auto refresh of credit report while loading credit score analyser
	Feature_CREDIT_SCORE_ANALYSER_AUTO_REFRESH Feature = 83
	// ATT permission prompt for iOS
	Feature_ATT_IOS_PERMISSION_PROMPT Feature = 84
	// CONSENT_RENEWAL_WIDGET_IN_ASKFI feature is used to display a warning widget in askfi transactions search
	Feature_CONSENT_RENEWAL_WIDGET_IN_ASKFI Feature = 85
	// Feature used to perform controlled rollout for new landing page
	Feature_FEATURE_JUMP_NEW_LANDING_PAGE Feature = 86
	// AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET feature is used to display a warning widget in all transactions page when consent is expired
	Feature_AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET Feature = 87
	// feature on order receipt page for quick re-categorization
	Feature_QUICK_RECAT Feature = 88
	// POST_PAYMENT_SCREEN is used to display post payment screen.
	Feature_POST_PAYMENT_SCREEN Feature = 89
	// app-update hard nudge on referrals screen
	Feature_APP_UPDATE_HARD_NUDGE_REFERRALS Feature = 90
	// app-update soft nudge on referrals screen
	Feature_APP_UPDATE_SOFT_NUDGE_REFERRALS Feature = 91
	// REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO is used to display earning summary info in referral history of referrals v1 page
	// Figma - https://www.figma.com/file/5TZH6I0awKw8DMtj3aMzrC/Referrals-%E2%80%A2-FFF?type=design&node-id=9465-13384&mode=dev
	Feature_REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO Feature = 92
	// CC feature to update card details at bank
	Feature_FEATURE_UPDATE_CARD_DETAILS_AT_BANK Feature = 93
	// feature flag for upi lite
	Feature_UPI_LITE Feature = 94
	// CC_ONBOARDING_WITHOUT_BILL_GEN_DATE_CAPTURE_STAGE is for showing bill gen date selection on card activation screen instead of separate stage screen
	Feature_FEATURE_CC_ONBOARDING_WITHOUT_BILL_GEN_DATE_CAPTURE_STAGE Feature = 95
	// feature flag for investment retention
	Feature_INVESTMENT_RETENTION_UI Feature = 96
	// feature flag for Early Salary, used to control the user groups and app version release evaluator
	Feature_LOANS_EARLY_SALARY Feature = 97
	// feature flag for Loans Fi-lite, used to control the user groups and app version release evaluator
	Feature_LOANS_FI_LITE Feature = 98
	// NETWORTH_DASHBOARD is for enabling and disabling the networth dashboard entry points on home
	Feature_NETWORTH_DASHBOARD Feature = 99
	// feature for biometric revalidation
	Feature_FEATURE_BIOMETRIC_REVALIDATION Feature = 100
	// Feature flag for releasing the deposits widget in networth
	Feature_NETWORTH_DEPOSITS_WIDGET Feature = 101
	// feature for remitter info backfill workflow
	Feature_FEATURE_REMITTER_INFO_BACKFILL Feature = 102
	// feature for new endpoint on inbound notification
	Feature_FEATURE_NEW_ENDPOINT_INBOUND_NOTIFICATION Feature = 103
	// feature flag for IMPS deemed txns handling
	Feature_IMPS_DEEMED_HANDLING Feature = 104
	// Feature used to perform controlled rollout and testing of
	// debited-transaction-threshold-breach ticket Creation/Resolution
	Feature_PAY_INCIDENT_MANAGER_DEBITED_TRANSACTION_THRESHOLD_BREACH Feature = 105
	// feature to enable health engine for INTRA payments
	Feature_HEALTH_ENGINE_FOR_INTRA_PAYMENTS Feature = 106
	// feature to enable health engine for NEFT payments
	Feature_HEALTH_ENGINE_FOR_NEFT_PAYMENTS Feature = 107
	// feature to enable health engine for IMPS payments
	Feature_HEALTH_ENGINE_FOR_IMPS_PAYMENTS Feature = 108
	// feature to enable health engine for RTGS payments
	Feature_HEALTH_ENGINE_FOR_RTGS_PAYMENTS Feature = 109
	// Feature used to perform controlled rollout and testing of
	// transaction detailed status update ticket Creation/Resolution
	Feature_PAY_INCIDENT_MANAGER_TRANSACTION_DETAILED_STATUS_UPDATE Feature = 110
	// deposit create v2 flow which is BE driven UI flow
	Feature_IS_CREATE_DEPOSIT_V2_ENABLED Feature = 111
	// deposit close payout v2 flow which is BE  driven UI flow
	Feature_IS_CLOSE_DEPOSIT_V2_ENABLED Feature = 112
	// Feature used to display new cc intro horizontal layout screen
	Feature_FEATURE_CC_INTRO_SCREEN_HORIZONTAL_LAYOUT Feature = 113
	// Feature used to perform controlled rollout and testing of new add funds vpa
	Feature_NEW_ADD_FUNDS_VPA Feature = 114
	// Feature used to perform controlled rollout of CX feedback engine integrated
	// screen to take user feedback if they are ineligible to onboard.
	Feature_FEATURE_CC_USER_INELIGIBLE_FEEDBACK Feature = 115
	// first time payment prompt
	Feature_FIRST_TIME_PAYMENT_PROMPT Feature = 116
	// flag to control if disabled cards are to be shown in analyser landing page
	Feature_ANALYSER_LANDING_PAGE_DISABLED_CARD Feature = 117
	// Feature used to perform controlled rollout of phone number as referral code
	Feature_PHONE_NUMBER_AS_REFERRAL_CODE Feature = 118
	// feature used to control rollout for CardSwitchNotifications
	Feature_CARD_SWITCH_NOTIFICATION Feature = 119
	// Feature to enable recent_activities be sorted on created_at as index
	Feature_SORT_RECENT_ACTIVITIES_ON_CREATED_AT Feature = 120
	// Feature used to trigger a notification through which in app csat survey is served
	Feature_IN_APP_CSAT_SURVEY Feature = 121
	// mf investment calculator feature flag
	Feature_MF_INVESTMENT_CALCULATOR_UI Feature = 122
	// mf investment nav chart feature flag
	Feature_MF_NAV_GRAPH_UI Feature = 123
	// Feature is used to control rollout for analyser error view v2
	Feature_ANALYSER_ERROR_VIEW_V2 Feature = 124
	// Feature is used to control rollout for Self transfer feature
	Feature_SELF_TRANSFER Feature = 125
	// AA data fetch feature to wait until we receive all FI notification and then proceed with data fetch
	Feature_AA_DATA_FETCH_WAIT_FOR_ALL_NOTIFICATION Feature = 126
	// feature for non-pre-approved unsecured card onboarding api
	Feature_FEATURE_CC_REGISTER_CUSTOMER_V3 Feature = 127
	// Feature is used to control rollout for New liveness screen for credit card onboarding
	Feature_FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN Feature = 128
	// Salary lite program feature
	Feature_SALARY_LITE_PROGRAM Feature = 129
	// flag to use upcoming transaction service to show user's upcoming txns (on home) instead of Fit service
	Feature_UPCOMING_TRANSACTIONS_V2 Feature = 130
	// flag to enable connected accounts benefits screen v2
	Feature_CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2 Feature = 131
	// flag to enable Ind securities feature on net worth
	Feature_NETWORTH_IND_SECURITIES_WIDGET Feature = 132
	// flag to allow display of Equity Accounts on home summmary
	Feature_AA_EQUITY_ACC_HOME_SUMMARY Feature = 133
	// Feature is used to control rollout of secured credit card
	Feature_FEATURE_CC_SECURED_CARDS Feature = 134
	// Feature is used to control rollout of vpa migration intro screen
	Feature_VPA_MIGRATION_INTRO_SCREEN Feature = 135
	// control if mf holdings import v2 flow is enabled
	Feature_MF_HOLDINGS_IMPORT_V2_FLOW Feature = 136
	// feature for in-house bre checks in RTBRE flows
	Feature_FEATURE_CC_INHOUSE_BRE Feature = 137
	// feature for forced balance refresh
	//
	// Deprecated: Marked as deprecated in api/typesv2/feature.proto.
	Feature_FEATURE_FORCED_BALANCE_REFRESH Feature = 138
	// Feature is used to enable secured loans for users
	Feature_FEATURE_SECURED_LOANS Feature = 139
	// feature for user activity to show the list of activities done by the user in cx chatbot (senseforth)
	Feature_CX_CHATBOT_USER_ACTIVITY Feature = 140
	// feature flag for beneficiary cool down period
	Feature_BENEFICIARY_COOL_DOWN_RULE Feature = 141
	// flag to allow and control AA txn to pipe to client for tpap
	Feature_ALLOW_AND_CONTROL_AA_TXN_FOR_TPAP Feature = 142
	// Feature flag for getting credit report consent on PAN DOB stage for savings account
	Feature_CREDIT_REPORT_CONSENT_FOR_SA_ON_PAN_DOB_STAGE Feature = 143
	// Feature enum to enable mass unsecured card
	Feature_MASS_UNSECURED_CARD Feature = 144
	// feature flags to control manual assets form release
	Feature_MANUAL_ASSET_FORM_AIF             Feature = 145
	Feature_MANUAL_ASSET_FORM_ART_ARTEFACTS   Feature = 146
	Feature_MANUAL_ASSET_FORM_BONDS           Feature = 147
	Feature_MANUAL_ASSET_FORM_CASH            Feature = 148
	Feature_MANUAL_ASSET_FORM_DIGITAL_GOLD    Feature = 149
	Feature_MANUAL_ASSET_FORM_DIGITAL_SILVER  Feature = 150
	Feature_MANUAL_ASSET_FORM_PRIVATE_EQUITY  Feature = 151
	Feature_MANUAL_ASSET_FORM_REAL_ESTATE     Feature = 152
	Feature_ACTIVATE_BENEFICIARY_VIA_LIVENESS Feature = 153
	// Feature enum to check if AutoPay / RecurringPayment
	// is enabled for the user.
	Feature_AUTOPAY_HUB Feature = 154
	// Feature for options page in screener
	Feature_SCREENER_CHOICE_PAGE Feature = 155
	// Feature flags used to rollout contextual insights on spend analysers
	Feature_SPEND_ANALYSER_INVEST_MORE_INSIGHT            Feature = 156
	Feature_SPEND_ANALYSER_INVEST_SURPLUS_BALANCE_INSIGHT Feature = 157
	Feature_SPEND_ANALYSER_EARLY_SALARY_INSIGHT           Feature = 158
	Feature_SPEND_ANALYSER_SET_REMINDER_INSIGHT           Feature = 159
	Feature_PAY_ASK_FI_SEARCH_SCREEN                      Feature = 160
	// Feature flag to control beneficiary cool down for pay via phone number
	Feature_SUPPORT_FOR_BENEFICIARY_COOL_DOWN_IN_PAY_BY_PHONE_NUMBER Feature = 161
	// Feature flag for onb add funds screen and to enable opt in upgrades
	Feature_ONB_ADD_FUNDS_TIERING_SUCCESS Feature = 162
	// feature flag for savings account closure flow - profile settings entry point
	Feature_SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT Feature = 163
	// feature flag for enabling tpap for credit card payments
	Feature_FEATURE_CREDIT_CARD_TPAP_PAYMENTS Feature = 164
	// Feature flag for getting permitted FIP config V2
	Feature_AA_PERMITTED_FIP_CONFIG_V2 Feature = 165
	// feature flag for enabling credit card recommendation framework
	Feature_FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK Feature = 166
	// Feature flag for add funds v3
	// Collect flow/Intent flow switch based on add funds amount
	// figma: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-62110&mode=design&t=x4uoemlNigT4wMXl-0
	Feature_ADD_FUNDS_V3 Feature = 167
	// Feature flag for onboarding add funds v2.2
	// Collect flow/Intent flow switch based on add funds amount
	// https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-87925&mode=design&t=WxERMQgI6gAmfn9N-0
	Feature_ONBOARDING_ADD_FUNDS_V2_2 Feature = 168
	// Feature flag for getting Auto Renew CTA for Deposit Accounts
	Feature_DEPOSIT_AUTO_RENEW_CTA Feature = 169
	// Feature flag for enabling simplifi atm withdrawals
	Feature_FEATURE_ENABLE_SIMPLIFI_CREDIT_CARD_ATM Feature = 170
	Feature_LOANS_FEDERAL_V2                        Feature = 171
	// Feature flag for add funds v4
	// Flow with payment options integration
	// figma: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=2174%3A128107&mode=dev
	Feature_ADD_FUNDS_V4 Feature = 172
	// Feature flag to enable savings account sign update flow
	Feature_ALFRED_SAVINGS_ACC_SIGN_UPDATE Feature = 173
	// feature for EMI on SimpliFi
	Feature_FEATURE_CC_EMI_SIMPLIFI Feature = 174
	// feature for EMI on MagniFi
	Feature_FEATURE_CC_EMI_MAGNIFI Feature = 175
	// Feature flag to enable new vkyc review screens
	Feature_VKYC_NEW_REVIEW_SCREEN Feature = 176
	// Feature flag for Credit fetch stage (CIBIL)
	Feature_LOANS_CIBIL_REPORT_FETCH Feature = 177
	// Feature flag to enable/disable etf in indian stocks
	Feature_INDIAN_SECURITIES_ETF Feature = 178
	// feature flag for one click tpap enablement flow
	Feature_ONE_CLICK_TPAP_ENABLEMENT_FLOW Feature = 179
	// feature flag for credit report address selection
	Feature_CREDIT_REPORT_ADDRESS_SELECTION Feature = 180
	// feature flag for New VKYC PAN CAPTURE and parents name update flow
	Feature_VKYC_PAN_IMAGE_CAPTURE Feature = 181
	// Feature flag for manual uan flow
	Feature_MANUAL_UAN_EPF_FLOW Feature = 182
	// feature flag for CIBIL integration in CC Fi Lite flow
	Feature_FEATURE_CC_FILITE_CIBIL_INTEGRATION Feature = 183
	// feature flag for tiering earned benefits screen - profile notch entry point
	Feature_TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH Feature = 184
	// feature for debit card offer widget on home screen
	Feature_DEBIT_CARD_OFFER_WIDGET_HOME Feature = 187
	// feature flag to enable loan pre-payment via tpap for fi-core users
	Feature_LOANS_TPAP_PRE_PAYMENT Feature = 188
	// feature flag to enable new nsdl pan api
	Feature_NSDL_PAN_API_V2_FOR_CA Feature = 189
	// feature for CC preapproved unsecured card program
	Feature_FEATURE_CC_PREAPPROVED_AMPLIFI Feature = 185
	// feature for CC BRE unsecured card program
	Feature_FEATURE_CC_BRE_AMPLIFI Feature = 186
	// feature for CC preapproved secured card program
	Feature_FEATURE_CC_PREAPPROVED_SIMPLIFI Feature = 190
	// feature for CC BRE secured card program
	Feature_FEATURE_CC_BRE_SIMPLIFI Feature = 191
	// feature for CC preapproved mass_unsecured card program
	Feature_FEATURE_CC_PREAPPROVED_MAGNIFI Feature = 192
	// feature for CC BRE mass_unsecured card program
	Feature_FEATURE_CC_BRE_MAGNIFI Feature = 193
	// feature flag to enable/disable report fraud in order receipt page
	Feature_REPORT_FRAUD_ORDER_RECEIPT Feature = 194
	// feature flag to enable/disable reward details in order receipt page
	Feature_FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT Feature = 195
	// feature flag to enable phase 3.1 of lamf
	Feature_FEATURE_LAMF_PHASE3_1 Feature = 196
	// feature flag for loans estimate income via itr flow
	Feature_FEATURE_LOANS_INCOME_ESTIMATE_VIA_ITR Feature = 197
	// feature flag to enable networth refresh v2
	Feature_NETWORTH_REFRESH_V2 Feature = 198
	// feature flag to control calling new pan validation api in aadhaar link status check
	Feature_FEATURE_PAN_VALIDATE_V2_AADHAAR_LINK_STATUS Feature = 199
	// Feature flag to control rollout of MS Clarity SDk on clients
	Feature_FEATURE_MS_CLARITY_SDK_ENABLED Feature = 200
	// feature flag for new post disbursal screens
	Feature_FEATURE_POST_LOAN_DISBURSAL_SCREENS Feature = 201
	// feature flag to enable/disable reward details in all transactions page
	Feature_FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE Feature = 202
	// feature flag to control pms manual asset form release
	Feature_MANUAL_ASSET_FORM_PORTFOLIO_MANAGEMENT_SERVICE Feature = 203
	// feature flag for adding alternate phone number
	Feature_FEATURE_LOAN_ADD_ALTERNATE_PHONE_NUMBER Feature = 204
	// feature flag for IDFC vkyc workapps integration
	Feature_LOANS_IDFC_VKYC_V2 Feature = 205
	// feature flag to perform controlled rollout for US Stocks RTSP (real time stock prices)
	Feature_FEATURE_USS_TABBED_CARD_RTSP Feature = 206
	// feature flag for nsdl pan flow v2 enabled for mf analyser or not
	Feature_NSDL_PAN_FLOW_V2_MF_ANALYSER Feature = 207
	// feature flag for nsdl pan flow v2 enabled for credit report analyser or not
	Feature_NSDL_PAN_FLOW_V2_CREDIT_REPORT_ANALYSER Feature = 208
	// feature flag to control rollout for off app eNACH cancellation.
	// figma for off app eNACH cancellation workflow: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=23674-47816&t=UVECot1zQoI1wswn-0
	Feature_FEATURE_OFF_APP_ENACH_CANCELLATION Feature = 209
	// feature flag to control ppf manual asset form release
	Feature_MANUAL_ASSET_FORM_PUBLIC_PROVIDENT_FUND Feature = 210
	// feature flag to control esop asset form release
	Feature_MANUAL_ASSET_FORM_EMPLOYEE_STOCK_OPTION Feature = 211
	// feature flag for Liquiloans Early Salary Program version V2
	Feature_LOANS_LIQUILOANS_EARLY_SALARY_V2 Feature = 212
	// feature flag for call IVR use case in CX
	Feature_FEATURE_CX_CALL_IVR Feature = 213
	// feature flag for rollout of automatic fee waiver
	Feature_FEATURE_CC_AUTOMATIC_FEE_WAIVER Feature = 214
	// feature flag for USS taxation document request option via alfred
	Feature_FEATURE_ALFRED_USS_TAX_DOCUMENT_REQUEST Feature = 215
	// loans feature flag for repeat loan rollout for LL stpl
	Feature_REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL Feature = 216
	// feature flag for rollout of call quality screen
	Feature_VKYC_CALL_QUALITY_SCREEN Feature = 217
	// feature flag to control whether to redirect user to all payment options screen for physical debit card charges payments
	Feature_PHYSICAL_DEBIT_CARD_CHARGES_PAYMENT_OPTIONS_SCREEN Feature = 218
	// feature flag to enable revamped debit card dashboard
	Feature_FEATURE_DC_DASHBOARD_V2_SCREEN Feature = 219
	// asset landing page for manual asset feature
	Feature_ASSET_LANDING_PAGE_FOR_MANUAL_ASSET Feature = 220
	// feature flag for Amplifi CC minutes
	Feature_FEATURE_AMPLIFI_CC_MINUTES Feature = 221
	// feature flag to determine if analyser hub widget overlay config is enabled for fi-to-fi integration
	Feature_ANALYSER_HUB_FI_TO_FI_INTEGRATION Feature = 222
	// Feature flag to enable the credit card reward dashboard for unsecured cards
	Feature_FEATURE_CC_REWARD_DASHBOARD_UNSECURED_CARD Feature = 223
	// Feature flag to enable the credit card reward dashboard for mass unsecured cards
	Feature_FEATURE_CC_REWARD_DASHBOARD_MASS_UNSECURED_CARD Feature = 224
	// Feature flag to enable the credit card reward dashboard for secured cards
	Feature_FEATURE_CC_REWARD_DASHBOARD_SECURED_CARD Feature = 225
	// feature for off-app-upi txns managed via temporal workflow
	Feature_FEATURE_OFF_APP_UPI_VIA_TEMPORAL Feature = 226
	// feature flag for enabling REIT
	Feature_FEATURE_INDIAN_SECURITIES_REIT Feature = 227
	// feature flag for enabling INVIT
	Feature_FEATURE_INDIAN_SECURITIES_INVIT Feature = 228
	// feature flag to enable double pin flow in uss add fund
	Feature_FEATURE_ADD_FUNDS_USS_DOUBLE_PIN_FLOW Feature = 229
	// feature flag for enabling NPS on net worth
	Feature_FEATURE_NET_WORTH_NPS Feature = 230
	// figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-50168&t=Kr2bD7QECk0A5v6B-4
	// feature flag for enabling new cc dashboard offers widget
	Feature_FEATURE_CC_OFFERS_WIDGET Feature = 231
	// feature flag for sending amount screen in collect PNs
	Feature_FEATURE_AMOUNT_SCREEN_IN_COLLECT_PN Feature = 232
	// feature flag for money secret on home screen
	Feature_FEATURE_MONEY_SECRET_HOME_SCREEN Feature = 233
	// feature flag for enabling and disabling dashboard sections for milestone and benefits widget
	Feature_FEATURE_CC_DASHBOARD_BENEFITS_WIDGET_UNSECURED_CARD Feature = 234
	// feature flag for exposing issue reporting flow on App
	Feature_FEATURE_IN_APP_ISSUE_REPORTING_FLOW Feature = 235
	// feature flag for enabling and disabling loans pre-payment via payment gateway
	Feature_FEATURE_LOAN_PRE_PAY_VIA_PG Feature = 236
	// Feature flag for enabling bank selection screen in connected account flow
	Feature_FEATURE_CA_BANK_SELECTION Feature = 237
	// Feature flag for enabling Single RPC driven Home-screen on the app
	Feature_FEATURE_HOME_SINGLE_RPC Feature = 238
	// feature flag for disabling dc delivery address update
	Feature_FEATURE_DISABLE_DC_DELIVERY_ADDRESS_UPDATE Feature = 239
	// feature flag for enabling consent screen V2 in onboarding
	Feature_FEATURE_ENABLE_CONSENT_SCREEN_V2 Feature = 240
	// feature flag for help recent activities flow
	Feature_FEATURE_CX_HELP_RECENT_ACTIVITY Feature = 241
	// feature flag to show new pay receipt error UI
	//
	//	figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=26564-28903&t=KbLwg48sh5O5tqmh-4
	Feature_FEATURE_PAY_RECEIPT_NEW_ERROR_VIEW Feature = 242
	// For enabling searching of PMS providers and AIFs in manual asset forms
	Feature_FEATURE_PMS_PROVIDER_AND_AIF_SEARCH Feature = 243
	// feature flag to enable multi lender and program eligibility evaluation for non fi core users
	Feature_FEATURE_LOANS_NON_FI_CORE_ELIGIBILITY_V2 Feature = 244
	// feature flag for DC new international ATM withdrawal layout
	// figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=22261-47439&t=MCh9gXKttXBPPOUr-4
	Feature_FEATURE_DC_INTERNATIONAL_ATM_WITHDRAWAL_LAYOUT Feature = 245
	Feature_FEATURE_US_STOCKS_SIP                          Feature = 246
	// feature flag for restricting users to initiate payments in case of Account Frozen or Not Active.
	Feature_FEATURE_ENABLE_ACCOUNT_OPERATIONAL_STATUS_CHECK Feature = 247
	// flag to control access to payment options v1 implementation
	Feature_FEATURE_ENABLE_PAYMENT_OPTIONS_V1           Feature = 248
	Feature_FEATURE_IN_APP_ISSUE_REPORTING_NON_FCR_FLOW Feature = 249
	// flag to enable/disable `ORDER_PHYSICAL_CARD` stage in SA onboarding flow
	// figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=9250-19535&t=L2zcBY9YvAOmYqDs-4
	Feature_FEATURE_ENABLE_ORDER_PHYSICAL_CARD_DURING_ONBOARDING Feature = 250
	// feature flag to show DC Travel mode
	Feature_FEATURE_DC_TRAVEL_MODE Feature = 251
	// feature flag to control secret summaries in networth dashboard
	Feature_FEATURE_NETWORTH_DASHBOARD_SECRET_SUMMARIES Feature = 252
	// feature flag to for associated transaction component in order receipt
	// Deprecated: In favour of FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT
	Feature_FEATURE_DC_FOREX_ORDER_RECEIPT Feature = 253
	// feature flag to control release of networth section in home bottom nav bar
	Feature_FEATURE_HOME_BOTTOM_NAV_BAR_NETWORTH_SECTION Feature = 254
	// feature flag to control release of pay search v2
	Feature_FEATURE_PAY_SEARCH_V2 Feature = 255
	// feature flag for call blocking functionality
	Feature_FEATURE_CX_CALL_BLOCKER Feature = 256
	// feature flag to control release of regular tier
	Feature_FEATURE_REGULAR_TIER Feature = 257
	// feature flag to control generic error screen in EPF
	Feature_FEATURE_EPF_GENERIC_ERROR_SCREEN Feature = 258
	// feature flag for USS document request option via alfred
	Feature_FEATURE_ALFRED_USS_DOCUMENT_REQUEST Feature = 259
	// feature flag to control release of DC toggle travel mode and related changes
	Feature_FEATURE_DC_TOGGLE_TRAVEL_MODE Feature = 260
	// feature flag to for associated transaction component in order receipt
	// https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=23330-40867&t=y4PxrDvsgrciSv6t-4
	Feature_FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT Feature = 263
	// feature flag to avoid user's with older app directly get connected to agents using chatbot
	Feature_FEATURE_REQUEST_APP_UPGRADE_FOR_CHATBOT_LOADING Feature = 261
	// feature flag to enable uss limit order
	Feature_FEATURE_US_STOCKS_LIMIT_ORDER Feature = 262
	// feature flag to enable sms parser client sdk
	Feature_FEATURE_SMS_PARSER_PARTNER_SDK Feature = 264
	// feature flag to roll out upi mandates
	Feature_FEATURE_UPI_MANDATES    Feature = 265
	Feature_FEATURE_MONEY_SECRET_V2 Feature = 266
	// FEATURE_REPORT_FRAUD_IN_DISPUTE_BOTTOM_SHEET feature controls if users should see "report fraud" option in dispute bottom sheet
	Feature_FEATURE_REPORT_FRAUD_IN_DISPUTE_BOTTOM_SHEET Feature = 267
	// FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME feature controls if users should see the wealth analyser widget instead of the money secrets widget
	Feature_FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME Feature = 268
	// feature flag to show ask fi widget in issue reporting flow
	Feature_FEATURE_ISSUE_REPORTING_ASK_FI_INTEGRATION Feature = 269
	// feature flag to roll out alternate app icon
	Feature_FEATURE_ALTERNATE_APP_ICON Feature = 270
	// feature flag to control if pan dob check should be skipped before initialising connected accounts sdk
	Feature_FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB Feature = 271
	// feature flag to enable risk ivr flow for user
	Feature_FEATURE_CX_CALL_RISK_IVR_FLOW Feature = 272
	// feature flag for SMS parser flow in prime
	Feature_PRIME_SMS_PARSER Feature = 273
	// feature flag to enable wealth builder pan collection form
	Feature_FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM Feature = 274
	// feature flag to enable tpap option in payment options screen for lending pre pay flow
	Feature_FEATURE_LENDING_TPAP_IN_PAYMENT_OPTIONS_SCREEN Feature = 275
	// feature flag to control release of share post payment screen
	Feature_FEATURE_SHARE_POST_PAYMENT_SCREEN Feature = 276
	// Connected Accounts CASdkV3FlowParams consist of params that decides version Flow V3 should be enabled or not depending on either absolute flag or app version
	Feature_FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW Feature = 277
	// feature flag to control release of self transfer screen
	Feature_FEATURE_SELF_TRANSFER_SCREEN Feature = 278
	// feature flag for enabling peer comparsion in secrets
	Feature_FEATURE_MONEY_SECRET_PEER_COMPARISON Feature = 279
	// feature flag to control release of vpa preference for list account, either User's VPA or default VPA.
	Feature_FEATURE_VPA_PREFERENCE_FOR_LIST_ACCOUNT Feature = 280
	// feature flag to enable auto id for debited and failed transactions catch all for in-app transaction
	Feature_FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP Feature = 281
	// feature flag to enable auto id for debited and in progress transactions catch all for in-app transaction
	Feature_FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP Feature = 282
	// feature flag to enable auto id for pay incident manager in progress transactions catch all for in-app transaction
	Feature_FEATURE_PAY_INCIDENT_MANAGER_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP Feature = 283
	// Feature used to perform controlled rollout and testing of new add funds prefunding flow
	Feature_FEATURE_PAY_ADD_FUNDS_PREFUNDING Feature = 284
	// feature flag to enable salary report money secret
	Feature_FEATURE_SALARY_REPORT_MONEY_SECRET Feature = 285
	// feature flag to pre account creation add funds (aka prefunding flow)
	Feature_FEATURE_ONBOARDING_PRE_ACCOUNT_CREATION_ADD_FUNDS Feature = 286
	// feature flag to enable lottie animation in DC travel mode
	Feature_FEATURE_DC_TRAVEL_MODE_LOTTIE Feature = 287
	// feature flag to control ListAccount Vendor Call.
	// Note - We are using this flag currently to restrict Vendor Call in GetPinStatus flow (Temporary solution)
	Feature_FEATURE_RESTRICT_LIST_ACCOUNT Feature = 288
	// Feature to enable/disable order and txns data enrichment from debit card switch notification.
	Feature_FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH Feature = 289
	// Feature to enable multiple ways to enter tiering
	Feature_FEATURE_TIERING_MULTIPLE_WAYS Feature = 290
	// Feature to enable/disable order and dc mandates view on autoPay screen.
	Feature_FEATURE_DC_MANDATES Feature = 291
	// Feature flag to control release of OFF_APP_UPI preempt flow (i.e. via non-temporal)
	Feature_FEATURE_OFF_APP_UPI_PREEMPT Feature = 292
	// Feature flag to enable money secret footer v2
	Feature_FEATURE_MONEY_SECRET_FOOTER_V2 Feature = 293
	// feature flag to enable auto id for pay incident manager cheque credit transaction
	Feature_FEATURE_PAY_INCIDENT_MANAGER_CHEQUE_CREDIT_TRANSACTION Feature = 294
	// feature flag to enable generic description tile in order receipt
	Feature_FEATURE_ORDER_RECEIPT_GENERIC_DESCRIPTION_DETAILS_TILE Feature = 295
	// Feature flag-to-control ticket resolution CSAT comms
	Feature_FEATURE_CX_TICKET_RESOLUTION_CSAT_COMMS Feature = 296
	// Feature to enable/disable address update flow from debit card order screen.
	Feature_FEATURE_DC_ORDER_SCREEN_WA_ADDRESS_UPDATE_FLOW Feature = 297
	// Feature to enable revamped vkyc v2 flow
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
	Feature_FEATURE_VKYC_FLOW_V2 Feature = 298
	// Feature to enable VKYC benefit screen with comparison module
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
	Feature_FEATURE_VKYC_BENEFIT_SCREEN_V2 Feature = 299
	// Feature flag to enable displaying of Stale Computed balance icon text component on balance card in home
	Feature_FEATURE_SHOW_STALE_COMPUTED_BALANCE_WARNING Feature = 300
	// Feature to enable asset import animation flow post completion of asset import
	Feature_FEATURE_ASSET_IMPORT_FLOW Feature = 301
	// Feature to enable PAN+MF import flow for the wealth builder 2.0 flow
	Feature_FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW Feature = 302
	// Feature to enable mf import during wealth builder onboarding
	Feature_FEATURE_MF_IMPORT_WEALTH_BUILDER_ONBOARDING Feature = 303
	// feature flag to control release of wealth builder section in home nav bar
	Feature_FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION Feature = 304
	// feature flag to control asset dashboard deeplink
	Feature_FEATURE_ASSET_DASHBOARD_FLOW Feature = 305
	// feature flag to control roll out of beneficiary name lookup integration
	Feature_FEATURE_BENEFICIARY_NAME_LOOKUP Feature = 306
	// Feature to enable/disable charges API for posting debit card charges in user's savings account
	Feature_FEATURE_DC_CHARGES_API Feature = 307
	// feature flag to enable savings account nominee update
	Feature_FEATURE_UPDATE_SAVINGS_ACCOUNT_NOMINEE Feature = 308
	// feature flag to enable new designs of min balance account screen (With SDUI section)
	// figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=34528-4662&t=DTx18ljrDCqwzsCF-0
	Feature_FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI Feature = 309
	// Feature to enable themes in mf holdings import
	Feature_FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME Feature = 310
	// feature flag for showing delta balance required in profile and plans v2
	Feature_FEATURE_SHOW_REQUIRED_BALANCE_FOR_TIER_UPGRADE Feature = 311
	// Feature for Chatheads optimised query
	Feature_FEATURE_CHATHEADS_OPTIMISED_QUERY Feature = 312
	// Feature to enable Money Plants in Earned Benefits Screen
	Feature_FEATURE_MONEY_PLANT_EARNED_BENEFITS Feature = 313
	// feature flag to enable auto id for add funds transactions with second leg failure
	Feature_FEATURE_PAY_INCIDENT_MANAGER_ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE Feature = 314
	// Feature flag to enable UI refinements and enhancements for the new home design.
	// Figma: https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28047-37963&t=6vx7apY5tjgUMWZh-0
	Feature_FEATURE_HOME_DESIGN_ENHANCEMENTS Feature = 315
	// Feature flag for the wealth builder landing page
	// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=2546-19484&t=357SbDCR0jr1aCXT-4
	Feature_FEATURE_WEALTH_BUILDER_NETWORTH_PAGE Feature = 316
	// Feature flag to enable QR scan enhancements
	Feature_FEATURE_QR_SCAN_ENHANCEMENTS Feature = 317
	// Feature to enable fetch sms data during wealth builder onboarding
	Feature_FEATURE_SEND_SMS_DATA_WEALTH_BUILDER Feature = 318
	// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-63839&t=D0LXOO8JlqA5WNII-0
	// Feature flag to enable new data collection screens in loans flow
	Feature_FEATURE_LOANS_NEW_DATA_COLLECTION_SCREENS Feature = 319
	// Feature to enable quick link banner in UPI pay landing screen
	Feature_FEATURE_UPI_QUICK_LINK_BANNER_PAY_LANDING_SCREEN Feature = 320
	// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1569&t=dbNwbHXlR83a2GfC-4
	// Feature flag to enable daily portfolio tracker
	Feature_FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN Feature = 321
	// Feature flag to enable new design for the second look flow
	// Figma :https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=63454-23835&t=J79RcvDgRU2Y5zUf-0
	Feature_FEATURE_LOANS_SECOND_LOOK_V1 Feature = 322
	// feature flag to enable prequal offer flows in Loans
	Feature_FEATURE_LOANS_PREQUAL_OFFER_FLOW Feature = 323
	// feature flag to decide if pin screen v2 should be shown while onboarding.
	Feature_FEATURE_SHOULD_SHOW_PIN_SCREEN_V2 Feature = 324
	// Feature flag to enable UI refinements and enhancements for the new pay landing screen design.
	// Figma: https://www.figma.com/design/FWW6omNbhzvzwBqlMT03am/D-fixit---workfile?node-id=816-100466&t=XUiu62Z60Gog7svF-0
	Feature_FEATURE_PAY_DESIGN_ENHANCEMENTS Feature = 325
	// feature flag to control roll out of failed ENACH transaction creation flow
	Feature_FEATURE_FAILED_ENACH_TRANSACTIONS Feature = 326
	// Feature flag to control whether AMB Entrypoint Banner should be displayed to user
	// This banner will appear on the user's account summary page when enabled
	// Figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay---Workfile?node-id=36832-49342&t=CS7H6Hd6TMoC6gwJ-0
	Feature_FEATURE_AMB_ENTRYPOINT_BANNER Feature = 327
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=96205
	Feature_FEATURE_CA_FLOW_WEALTH_BUILDER_ONBOARDING Feature = 328
	// Feature to enable daily total transaction limit on app
	Feature_FEATURE_DAILY_TOTAL_TRANSACTION_AMOUNT_LIMIT_ON_APP Feature = 329
	// Feature to enable freshchat chatbot SDK experiment
	Feature_FEATURE_FRESHCHAT_CHATBOT_SDK_ENABLED Feature = 330
	// Feature flag to enable/disable new cx landing page
	// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-34926&t=WFUh2hRcaHUvoUg6-4
	Feature_FEATURE_CX_NEW_LANDING_PAGE Feature = 331
	// Feature flag to enable/disable new rewards catalog merged page
	// Figma: https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=30402-16385&t=L5cs9CRphCT9WXJS-4
	Feature_FEATURE_REWARDS_CATALOG_MERGED_PAGE Feature = 332
	// feature flag to control roll out of indian stocks in net worth daily report page
	Feature_FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION Feature = 333
	// Feature flag to enable/disable liabilities section on WB dashboard
	Feature_FEATURE_WB_DASHBOARD_LIABILITIES Feature = 334
	Feature_FEATURE_WB_MAGIC_IMPORT          Feature = 335
	// Feature flag to enable/disable Weekly Portfolio Tracker
	// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=11417-3443&t=KVw50P7F6VMVXEEq-4
	Feature_FEATURE_WEEKLY_PORTFOLIO_TRACKER Feature = 336
	// Feature flag to enable/disable US Funds in Invest Landing Page
	Feature_FEATURE_US_FUNDS_IN_INVEST_LANDING_PAGE Feature = 337
	// Feature flag to enable redis based counter on Upi payments vendor Enquiry (ReqCheckTxnStatus)
	Feature_FEATURE_UPI_ENQUIRY_REDIS_BASED_COUNTER Feature = 338
	// Feature flag to enable/disable ruPay CC GTM banner on Pay Landing Screen.
	// Currently we are doing this from BE only as We need some client fixes to make it Sherlock driven
	Feature_FEATURE_PAY_LANDING_BANNER_RUPAY_CC_BE_DRIVEN Feature = 339
	// Feature flag to enable fetching pay landing screen banner via Dynamic Elements RPC.
	Feature_FEATURE_PAY_LANDING_SCREEN_BANNER_DYNAMIC_ELEMENTS Feature = 340
	// Feature flag to enable FI mcp TOTP code
	Feature_FEATURE_FI_MCP_TOTP_CODE Feature = 341
	// feature flag to control roll-out of credit card v2 (sdk based) flow
	Feature_FEATURE_CREDIT_CARD_V2_FLOW Feature = 342
)

// Enum value maps for Feature.
var (
	Feature_name = map[int32]string{
		0:   "FEATURE_UNSPECIFIED",
		1:   "INVESTMENT_MF_UI",
		2:   "PAY_VIA_PHONE_NUMBER",
		3:   "MF_ADVANCE_FILTER",
		4:   "MF_TEXT_SEARCH",
		5:   "DISPUTE_FOR_OFF_APP_TXN",
		6:   "MF_SIP",
		7:   "MF_NEW_OTI_PAYMENT_FLOW",
		8:   "SHOW_SUPPORT_TICKETS_IN_APP",
		9:   "HOME_PAGE_LAYOUT_V2",
		10:  "MERCHANT_ANALYSER",
		11:  "TIME_ANALYSER",
		12:  "SENSEFORTH_CHATBOT",
		13:  "AA_FINVU_TOKEN_AUTHENTICATION",
		14:  "JUMP_V2",
		15:  "CATEGORY_ANALYSER_ADD_FUNDS_BANNER",
		16:  "UPI_TPAP",
		17:  "CREDIT_SCORE_ANALYSER",
		18:  "ANALYSER_FEEDBACK",
		19:  "FI_MINUTE_HUB_BANNER",
		20:  "US_STOCK_UI",
		21:  "US_STOCK_LANDING_PAGE_UI",
		22:  "SALARY_PROGRAM_HEALTH_INSURANCE_BENEFIT",
		23:  "ASK_FI_HOME_SEARCH_BAR",
		24:  "REFERRALS_V1_LANDING_PAGE",
		25:  "JUMP_RENEWAL_FLOW",
		26:  "UPI_MAPPER",
		27:  "CREDIT_SCORE_ANALYSER_V2",
		28:  "ML_KIT_QR",
		29:  "ENABLE_GET_VKYC_NEXT_ACTION",
		30:  "ENABLE_2FA_MF_ONE_TIME_BUY",
		31:  "ENABLE_2FA_MF_REGISTER_SIP",
		32:  "ENABLE_MF_FIT_SIP_CONVERSION",
		33:  "TIME_ANALYSER_INSIGHTS",
		34:  "CATEGORY_ANALYSER_ACCOUNT_FILTER_V2",
		35:  "INVESTMENT_LANDING_COMPONENTS_ORDERING",
		36:  "REFERRAL_LINK_GENERATION_AT_CLIENT_SIDE",
		37:  "FEATURE_CONNECTED_ACCOUNTS_IN_SCREENER",
		38:  "JUMP_DASHBOARD_FOR_NON_INVESTED_USERS",
		39:  "FIXED_DEPOSIT_INTEREST_RATES",
		40:  "FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM",
		41:  "TXN_RECEIPT_SCREEN_V1",
		42:  "HOME_SEARCH_BAR_CHIPS",
		43:  "VERIFY_QR_V1",
		44:  "REFERRAL_SCREEN_DURING_ONBOARDING_V1",
		45:  "AFFLUENT_USER_BONUS_TRANSITION_SCREEN",
		46:  "HEALTH_ENGINE_FOR_PAYMENTS",
		47:  "JUMP_USER_SCHEME_ELIGIBILITY",
		48:  "TIME_ANALYSER_UPCOMING_TRANSACTIONS",
		49:  "UPI_PIN_SET_USING_AADHAAR",
		50:  "PRIORITIES_VKYC_OVER_ADD_FUNDS",
		51:  "ANALYSER_ZERO_STATES",
		52:  "INVESTMENT_LANDING_MUTUAL_FUNDS_DEEPLINK",
		53:  "UPI_INTERNATIONAL_PAYMENT",
		54:  "CC_TXN_DATA_IN_ASKFI",
		55:  "INVESTMENT_HOME_COMPONENT",
		56:  "AA_CONSENT_RENEWAL",
		57:  "INVEST_LANDING_BANNER",
		58:  "JUMP_MATURITY_CONSENT",
		59:  "PAY_INCIDENT_MANAGER_UPI_PIN_FLOW_ERROR",
		60:  "FUND_TRANSFER_V1",
		61:  "US_STOCK_ETF_SUPPORT",
		62:  "ONBOARDING_ADD_FUNDS_V2",
		63:  "FRESHDESK_MONORAIL_INTEGRATION",
		64:  "CC_UPI_LINKING",
		65:  "CA_CONNECT_FI_TO_FI",
		66:  "PAY_INCIDENT_MANAGER_P2P_DEEMED_TRANSACTION",
		67:  "PAY_INCIDENT_MANAGER_P2M_DEEMED_TRANSACTION",
		68:  "JUMP_ALL_ACTIVITY_DEEPLINK",
		69:  "NEW_VPA_HANDLE",
		70:  "UPDATED_BENEFITS_FOR_AFFLUENT_USER_ONB_ADD_FUNDS",
		71:  "FEATURE_CC_REALTIME_ELIGIBILITY",
		72:  "CREDIT_SCORE_ANALYSER_PROFILE_DETAILS_V2",
		73:  "INAPPHELP_FEEDBACK_ENGINE",
		74:  "FETCH_ORDER_WITH_TRANSACTIONS_USING_IN_MEMORY_JOIN",
		75:  "AFFLUENT_USER_BONUS_TRANSITION_SCREEN_NON_REFEREE",
		76:  "REFERRALS_V1_LANDING_PAGE_STACKED_REWARDS_COMPONENT",
		77:  "FEATURE_CC_EMI",
		78:  "CC_REGISTER_CUSTOMER_V2",
		79:  "FEATURE_JUMP_INVEST_PAGE_V2",
		80:  "USS_LANDING_PAGE_AB_SUPPORT",
		81:  "USS_COLLECTION_SCREEN_AB_SUPPORT",
		82:  "MF_HOLDINGS_IMPORT_PHONE_NUMBER_SCREEN",
		83:  "CREDIT_SCORE_ANALYSER_AUTO_REFRESH",
		84:  "ATT_IOS_PERMISSION_PROMPT",
		85:  "CONSENT_RENEWAL_WIDGET_IN_ASKFI",
		86:  "FEATURE_JUMP_NEW_LANDING_PAGE",
		87:  "AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET",
		88:  "QUICK_RECAT",
		89:  "POST_PAYMENT_SCREEN",
		90:  "APP_UPDATE_HARD_NUDGE_REFERRALS",
		91:  "APP_UPDATE_SOFT_NUDGE_REFERRALS",
		92:  "REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO",
		93:  "FEATURE_UPDATE_CARD_DETAILS_AT_BANK",
		94:  "UPI_LITE",
		95:  "FEATURE_CC_ONBOARDING_WITHOUT_BILL_GEN_DATE_CAPTURE_STAGE",
		96:  "INVESTMENT_RETENTION_UI",
		97:  "LOANS_EARLY_SALARY",
		98:  "LOANS_FI_LITE",
		99:  "NETWORTH_DASHBOARD",
		100: "FEATURE_BIOMETRIC_REVALIDATION",
		101: "NETWORTH_DEPOSITS_WIDGET",
		102: "FEATURE_REMITTER_INFO_BACKFILL",
		103: "FEATURE_NEW_ENDPOINT_INBOUND_NOTIFICATION",
		104: "IMPS_DEEMED_HANDLING",
		105: "PAY_INCIDENT_MANAGER_DEBITED_TRANSACTION_THRESHOLD_BREACH",
		106: "HEALTH_ENGINE_FOR_INTRA_PAYMENTS",
		107: "HEALTH_ENGINE_FOR_NEFT_PAYMENTS",
		108: "HEALTH_ENGINE_FOR_IMPS_PAYMENTS",
		109: "HEALTH_ENGINE_FOR_RTGS_PAYMENTS",
		110: "PAY_INCIDENT_MANAGER_TRANSACTION_DETAILED_STATUS_UPDATE",
		111: "IS_CREATE_DEPOSIT_V2_ENABLED",
		112: "IS_CLOSE_DEPOSIT_V2_ENABLED",
		113: "FEATURE_CC_INTRO_SCREEN_HORIZONTAL_LAYOUT",
		114: "NEW_ADD_FUNDS_VPA",
		115: "FEATURE_CC_USER_INELIGIBLE_FEEDBACK",
		116: "FIRST_TIME_PAYMENT_PROMPT",
		117: "ANALYSER_LANDING_PAGE_DISABLED_CARD",
		118: "PHONE_NUMBER_AS_REFERRAL_CODE",
		119: "CARD_SWITCH_NOTIFICATION",
		120: "SORT_RECENT_ACTIVITIES_ON_CREATED_AT",
		121: "IN_APP_CSAT_SURVEY",
		122: "MF_INVESTMENT_CALCULATOR_UI",
		123: "MF_NAV_GRAPH_UI",
		124: "ANALYSER_ERROR_VIEW_V2",
		125: "SELF_TRANSFER",
		126: "AA_DATA_FETCH_WAIT_FOR_ALL_NOTIFICATION",
		127: "FEATURE_CC_REGISTER_CUSTOMER_V3",
		128: "FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN",
		129: "SALARY_LITE_PROGRAM",
		130: "UPCOMING_TRANSACTIONS_V2",
		131: "CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2",
		132: "NETWORTH_IND_SECURITIES_WIDGET",
		133: "AA_EQUITY_ACC_HOME_SUMMARY",
		134: "FEATURE_CC_SECURED_CARDS",
		135: "VPA_MIGRATION_INTRO_SCREEN",
		136: "MF_HOLDINGS_IMPORT_V2_FLOW",
		137: "FEATURE_CC_INHOUSE_BRE",
		138: "FEATURE_FORCED_BALANCE_REFRESH",
		139: "FEATURE_SECURED_LOANS",
		140: "CX_CHATBOT_USER_ACTIVITY",
		141: "BENEFICIARY_COOL_DOWN_RULE",
		142: "ALLOW_AND_CONTROL_AA_TXN_FOR_TPAP",
		143: "CREDIT_REPORT_CONSENT_FOR_SA_ON_PAN_DOB_STAGE",
		144: "MASS_UNSECURED_CARD",
		145: "MANUAL_ASSET_FORM_AIF",
		146: "MANUAL_ASSET_FORM_ART_ARTEFACTS",
		147: "MANUAL_ASSET_FORM_BONDS",
		148: "MANUAL_ASSET_FORM_CASH",
		149: "MANUAL_ASSET_FORM_DIGITAL_GOLD",
		150: "MANUAL_ASSET_FORM_DIGITAL_SILVER",
		151: "MANUAL_ASSET_FORM_PRIVATE_EQUITY",
		152: "MANUAL_ASSET_FORM_REAL_ESTATE",
		153: "ACTIVATE_BENEFICIARY_VIA_LIVENESS",
		154: "AUTOPAY_HUB",
		155: "SCREENER_CHOICE_PAGE",
		156: "SPEND_ANALYSER_INVEST_MORE_INSIGHT",
		157: "SPEND_ANALYSER_INVEST_SURPLUS_BALANCE_INSIGHT",
		158: "SPEND_ANALYSER_EARLY_SALARY_INSIGHT",
		159: "SPEND_ANALYSER_SET_REMINDER_INSIGHT",
		160: "PAY_ASK_FI_SEARCH_SCREEN",
		161: "SUPPORT_FOR_BENEFICIARY_COOL_DOWN_IN_PAY_BY_PHONE_NUMBER",
		162: "ONB_ADD_FUNDS_TIERING_SUCCESS",
		163: "SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT",
		164: "FEATURE_CREDIT_CARD_TPAP_PAYMENTS",
		165: "AA_PERMITTED_FIP_CONFIG_V2",
		166: "FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK",
		167: "ADD_FUNDS_V3",
		168: "ONBOARDING_ADD_FUNDS_V2_2",
		169: "DEPOSIT_AUTO_RENEW_CTA",
		170: "FEATURE_ENABLE_SIMPLIFI_CREDIT_CARD_ATM",
		171: "LOANS_FEDERAL_V2",
		172: "ADD_FUNDS_V4",
		173: "ALFRED_SAVINGS_ACC_SIGN_UPDATE",
		174: "FEATURE_CC_EMI_SIMPLIFI",
		175: "FEATURE_CC_EMI_MAGNIFI",
		176: "VKYC_NEW_REVIEW_SCREEN",
		177: "LOANS_CIBIL_REPORT_FETCH",
		178: "INDIAN_SECURITIES_ETF",
		179: "ONE_CLICK_TPAP_ENABLEMENT_FLOW",
		180: "CREDIT_REPORT_ADDRESS_SELECTION",
		181: "VKYC_PAN_IMAGE_CAPTURE",
		182: "MANUAL_UAN_EPF_FLOW",
		183: "FEATURE_CC_FILITE_CIBIL_INTEGRATION",
		184: "TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH",
		187: "DEBIT_CARD_OFFER_WIDGET_HOME",
		188: "LOANS_TPAP_PRE_PAYMENT",
		189: "NSDL_PAN_API_V2_FOR_CA",
		185: "FEATURE_CC_PREAPPROVED_AMPLIFI",
		186: "FEATURE_CC_BRE_AMPLIFI",
		190: "FEATURE_CC_PREAPPROVED_SIMPLIFI",
		191: "FEATURE_CC_BRE_SIMPLIFI",
		192: "FEATURE_CC_PREAPPROVED_MAGNIFI",
		193: "FEATURE_CC_BRE_MAGNIFI",
		194: "REPORT_FRAUD_ORDER_RECEIPT",
		195: "FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT",
		196: "FEATURE_LAMF_PHASE3_1",
		197: "FEATURE_LOANS_INCOME_ESTIMATE_VIA_ITR",
		198: "NETWORTH_REFRESH_V2",
		199: "FEATURE_PAN_VALIDATE_V2_AADHAAR_LINK_STATUS",
		200: "FEATURE_MS_CLARITY_SDK_ENABLED",
		201: "FEATURE_POST_LOAN_DISBURSAL_SCREENS",
		202: "FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE",
		203: "MANUAL_ASSET_FORM_PORTFOLIO_MANAGEMENT_SERVICE",
		204: "FEATURE_LOAN_ADD_ALTERNATE_PHONE_NUMBER",
		205: "LOANS_IDFC_VKYC_V2",
		206: "FEATURE_USS_TABBED_CARD_RTSP",
		207: "NSDL_PAN_FLOW_V2_MF_ANALYSER",
		208: "NSDL_PAN_FLOW_V2_CREDIT_REPORT_ANALYSER",
		209: "FEATURE_OFF_APP_ENACH_CANCELLATION",
		210: "MANUAL_ASSET_FORM_PUBLIC_PROVIDENT_FUND",
		211: "MANUAL_ASSET_FORM_EMPLOYEE_STOCK_OPTION",
		212: "LOANS_LIQUILOANS_EARLY_SALARY_V2",
		213: "FEATURE_CX_CALL_IVR",
		214: "FEATURE_CC_AUTOMATIC_FEE_WAIVER",
		215: "FEATURE_ALFRED_USS_TAX_DOCUMENT_REQUEST",
		216: "REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL",
		217: "VKYC_CALL_QUALITY_SCREEN",
		218: "PHYSICAL_DEBIT_CARD_CHARGES_PAYMENT_OPTIONS_SCREEN",
		219: "FEATURE_DC_DASHBOARD_V2_SCREEN",
		220: "ASSET_LANDING_PAGE_FOR_MANUAL_ASSET",
		221: "FEATURE_AMPLIFI_CC_MINUTES",
		222: "ANALYSER_HUB_FI_TO_FI_INTEGRATION",
		223: "FEATURE_CC_REWARD_DASHBOARD_UNSECURED_CARD",
		224: "FEATURE_CC_REWARD_DASHBOARD_MASS_UNSECURED_CARD",
		225: "FEATURE_CC_REWARD_DASHBOARD_SECURED_CARD",
		226: "FEATURE_OFF_APP_UPI_VIA_TEMPORAL",
		227: "FEATURE_INDIAN_SECURITIES_REIT",
		228: "FEATURE_INDIAN_SECURITIES_INVIT",
		229: "FEATURE_ADD_FUNDS_USS_DOUBLE_PIN_FLOW",
		230: "FEATURE_NET_WORTH_NPS",
		231: "FEATURE_CC_OFFERS_WIDGET",
		232: "FEATURE_AMOUNT_SCREEN_IN_COLLECT_PN",
		233: "FEATURE_MONEY_SECRET_HOME_SCREEN",
		234: "FEATURE_CC_DASHBOARD_BENEFITS_WIDGET_UNSECURED_CARD",
		235: "FEATURE_IN_APP_ISSUE_REPORTING_FLOW",
		236: "FEATURE_LOAN_PRE_PAY_VIA_PG",
		237: "FEATURE_CA_BANK_SELECTION",
		238: "FEATURE_HOME_SINGLE_RPC",
		239: "FEATURE_DISABLE_DC_DELIVERY_ADDRESS_UPDATE",
		240: "FEATURE_ENABLE_CONSENT_SCREEN_V2",
		241: "FEATURE_CX_HELP_RECENT_ACTIVITY",
		242: "FEATURE_PAY_RECEIPT_NEW_ERROR_VIEW",
		243: "FEATURE_PMS_PROVIDER_AND_AIF_SEARCH",
		244: "FEATURE_LOANS_NON_FI_CORE_ELIGIBILITY_V2",
		245: "FEATURE_DC_INTERNATIONAL_ATM_WITHDRAWAL_LAYOUT",
		246: "FEATURE_US_STOCKS_SIP",
		247: "FEATURE_ENABLE_ACCOUNT_OPERATIONAL_STATUS_CHECK",
		248: "FEATURE_ENABLE_PAYMENT_OPTIONS_V1",
		249: "FEATURE_IN_APP_ISSUE_REPORTING_NON_FCR_FLOW",
		250: "FEATURE_ENABLE_ORDER_PHYSICAL_CARD_DURING_ONBOARDING",
		251: "FEATURE_DC_TRAVEL_MODE",
		252: "FEATURE_NETWORTH_DASHBOARD_SECRET_SUMMARIES",
		253: "FEATURE_DC_FOREX_ORDER_RECEIPT",
		254: "FEATURE_HOME_BOTTOM_NAV_BAR_NETWORTH_SECTION",
		255: "FEATURE_PAY_SEARCH_V2",
		256: "FEATURE_CX_CALL_BLOCKER",
		257: "FEATURE_REGULAR_TIER",
		258: "FEATURE_EPF_GENERIC_ERROR_SCREEN",
		259: "FEATURE_ALFRED_USS_DOCUMENT_REQUEST",
		260: "FEATURE_DC_TOGGLE_TRAVEL_MODE",
		263: "FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT",
		261: "FEATURE_REQUEST_APP_UPGRADE_FOR_CHATBOT_LOADING",
		262: "FEATURE_US_STOCKS_LIMIT_ORDER",
		264: "FEATURE_SMS_PARSER_PARTNER_SDK",
		265: "FEATURE_UPI_MANDATES",
		266: "FEATURE_MONEY_SECRET_V2",
		267: "FEATURE_REPORT_FRAUD_IN_DISPUTE_BOTTOM_SHEET",
		268: "FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME",
		269: "FEATURE_ISSUE_REPORTING_ASK_FI_INTEGRATION",
		270: "FEATURE_ALTERNATE_APP_ICON",
		271: "FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB",
		272: "FEATURE_CX_CALL_RISK_IVR_FLOW",
		273: "PRIME_SMS_PARSER",
		274: "FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM",
		275: "FEATURE_LENDING_TPAP_IN_PAYMENT_OPTIONS_SCREEN",
		276: "FEATURE_SHARE_POST_PAYMENT_SCREEN",
		277: "FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW",
		278: "FEATURE_SELF_TRANSFER_SCREEN",
		279: "FEATURE_MONEY_SECRET_PEER_COMPARISON",
		280: "FEATURE_VPA_PREFERENCE_FOR_LIST_ACCOUNT",
		281: "FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP",
		282: "FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP",
		283: "FEATURE_PAY_INCIDENT_MANAGER_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP",
		284: "FEATURE_PAY_ADD_FUNDS_PREFUNDING",
		285: "FEATURE_SALARY_REPORT_MONEY_SECRET",
		286: "FEATURE_ONBOARDING_PRE_ACCOUNT_CREATION_ADD_FUNDS",
		287: "FEATURE_DC_TRAVEL_MODE_LOTTIE",
		288: "FEATURE_RESTRICT_LIST_ACCOUNT",
		289: "FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH",
		290: "FEATURE_TIERING_MULTIPLE_WAYS",
		291: "FEATURE_DC_MANDATES",
		292: "FEATURE_OFF_APP_UPI_PREEMPT",
		293: "FEATURE_MONEY_SECRET_FOOTER_V2",
		294: "FEATURE_PAY_INCIDENT_MANAGER_CHEQUE_CREDIT_TRANSACTION",
		295: "FEATURE_ORDER_RECEIPT_GENERIC_DESCRIPTION_DETAILS_TILE",
		296: "FEATURE_CX_TICKET_RESOLUTION_CSAT_COMMS",
		297: "FEATURE_DC_ORDER_SCREEN_WA_ADDRESS_UPDATE_FLOW",
		298: "FEATURE_VKYC_FLOW_V2",
		299: "FEATURE_VKYC_BENEFIT_SCREEN_V2",
		300: "FEATURE_SHOW_STALE_COMPUTED_BALANCE_WARNING",
		301: "FEATURE_ASSET_IMPORT_FLOW",
		302: "FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW",
		303: "FEATURE_MF_IMPORT_WEALTH_BUILDER_ONBOARDING",
		304: "FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION",
		305: "FEATURE_ASSET_DASHBOARD_FLOW",
		306: "FEATURE_BENEFICIARY_NAME_LOOKUP",
		307: "FEATURE_DC_CHARGES_API",
		308: "FEATURE_UPDATE_SAVINGS_ACCOUNT_NOMINEE",
		309: "FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI",
		310: "FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME",
		311: "FEATURE_SHOW_REQUIRED_BALANCE_FOR_TIER_UPGRADE",
		312: "FEATURE_CHATHEADS_OPTIMISED_QUERY",
		313: "FEATURE_MONEY_PLANT_EARNED_BENEFITS",
		314: "FEATURE_PAY_INCIDENT_MANAGER_ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE",
		315: "FEATURE_HOME_DESIGN_ENHANCEMENTS",
		316: "FEATURE_WEALTH_BUILDER_NETWORTH_PAGE",
		317: "FEATURE_QR_SCAN_ENHANCEMENTS",
		318: "FEATURE_SEND_SMS_DATA_WEALTH_BUILDER",
		319: "FEATURE_LOANS_NEW_DATA_COLLECTION_SCREENS",
		320: "FEATURE_UPI_QUICK_LINK_BANNER_PAY_LANDING_SCREEN",
		321: "FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN",
		322: "FEATURE_LOANS_SECOND_LOOK_V1",
		323: "FEATURE_LOANS_PREQUAL_OFFER_FLOW",
		324: "FEATURE_SHOULD_SHOW_PIN_SCREEN_V2",
		325: "FEATURE_PAY_DESIGN_ENHANCEMENTS",
		326: "FEATURE_FAILED_ENACH_TRANSACTIONS",
		327: "FEATURE_AMB_ENTRYPOINT_BANNER",
		328: "FEATURE_CA_FLOW_WEALTH_BUILDER_ONBOARDING",
		329: "FEATURE_DAILY_TOTAL_TRANSACTION_AMOUNT_LIMIT_ON_APP",
		330: "FEATURE_FRESHCHAT_CHATBOT_SDK_ENABLED",
		331: "FEATURE_CX_NEW_LANDING_PAGE",
		332: "FEATURE_REWARDS_CATALOG_MERGED_PAGE",
		333: "FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION",
		334: "FEATURE_WB_DASHBOARD_LIABILITIES",
		335: "FEATURE_WB_MAGIC_IMPORT",
		336: "FEATURE_WEEKLY_PORTFOLIO_TRACKER",
		337: "FEATURE_US_FUNDS_IN_INVEST_LANDING_PAGE",
		338: "FEATURE_UPI_ENQUIRY_REDIS_BASED_COUNTER",
		339: "FEATURE_PAY_LANDING_BANNER_RUPAY_CC_BE_DRIVEN",
		340: "FEATURE_PAY_LANDING_SCREEN_BANNER_DYNAMIC_ELEMENTS",
		341: "FEATURE_FI_MCP_TOTP_CODE",
		342: "FEATURE_CREDIT_CARD_V2_FLOW",
	}
	Feature_value = map[string]int32{
		"FEATURE_UNSPECIFIED":                                       0,
		"INVESTMENT_MF_UI":                                          1,
		"PAY_VIA_PHONE_NUMBER":                                      2,
		"MF_ADVANCE_FILTER":                                         3,
		"MF_TEXT_SEARCH":                                            4,
		"DISPUTE_FOR_OFF_APP_TXN":                                   5,
		"MF_SIP":                                                    6,
		"MF_NEW_OTI_PAYMENT_FLOW":                                   7,
		"SHOW_SUPPORT_TICKETS_IN_APP":                               8,
		"HOME_PAGE_LAYOUT_V2":                                       9,
		"MERCHANT_ANALYSER":                                         10,
		"TIME_ANALYSER":                                             11,
		"SENSEFORTH_CHATBOT":                                        12,
		"AA_FINVU_TOKEN_AUTHENTICATION":                             13,
		"JUMP_V2":                                                   14,
		"CATEGORY_ANALYSER_ADD_FUNDS_BANNER":                        15,
		"UPI_TPAP":                                                  16,
		"CREDIT_SCORE_ANALYSER":                                     17,
		"ANALYSER_FEEDBACK":                                         18,
		"FI_MINUTE_HUB_BANNER":                                      19,
		"US_STOCK_UI":                                               20,
		"US_STOCK_LANDING_PAGE_UI":                                  21,
		"SALARY_PROGRAM_HEALTH_INSURANCE_BENEFIT":                   22,
		"ASK_FI_HOME_SEARCH_BAR":                                    23,
		"REFERRALS_V1_LANDING_PAGE":                                 24,
		"JUMP_RENEWAL_FLOW":                                         25,
		"UPI_MAPPER":                                                26,
		"CREDIT_SCORE_ANALYSER_V2":                                  27,
		"ML_KIT_QR":                                                 28,
		"ENABLE_GET_VKYC_NEXT_ACTION":                               29,
		"ENABLE_2FA_MF_ONE_TIME_BUY":                                30,
		"ENABLE_2FA_MF_REGISTER_SIP":                                31,
		"ENABLE_MF_FIT_SIP_CONVERSION":                              32,
		"TIME_ANALYSER_INSIGHTS":                                    33,
		"CATEGORY_ANALYSER_ACCOUNT_FILTER_V2":                       34,
		"INVESTMENT_LANDING_COMPONENTS_ORDERING":                    35,
		"REFERRAL_LINK_GENERATION_AT_CLIENT_SIDE":                   36,
		"FEATURE_CONNECTED_ACCOUNTS_IN_SCREENER":                    37,
		"JUMP_DASHBOARD_FOR_NON_INVESTED_USERS":                     38,
		"FIXED_DEPOSIT_INTEREST_RATES":                              39,
		"FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM":                40,
		"TXN_RECEIPT_SCREEN_V1":                                     41,
		"HOME_SEARCH_BAR_CHIPS":                                     42,
		"VERIFY_QR_V1":                                              43,
		"REFERRAL_SCREEN_DURING_ONBOARDING_V1":                      44,
		"AFFLUENT_USER_BONUS_TRANSITION_SCREEN":                     45,
		"HEALTH_ENGINE_FOR_PAYMENTS":                                46,
		"JUMP_USER_SCHEME_ELIGIBILITY":                              47,
		"TIME_ANALYSER_UPCOMING_TRANSACTIONS":                       48,
		"UPI_PIN_SET_USING_AADHAAR":                                 49,
		"PRIORITIES_VKYC_OVER_ADD_FUNDS":                            50,
		"ANALYSER_ZERO_STATES":                                      51,
		"INVESTMENT_LANDING_MUTUAL_FUNDS_DEEPLINK":                  52,
		"UPI_INTERNATIONAL_PAYMENT":                                 53,
		"CC_TXN_DATA_IN_ASKFI":                                      54,
		"INVESTMENT_HOME_COMPONENT":                                 55,
		"AA_CONSENT_RENEWAL":                                        56,
		"INVEST_LANDING_BANNER":                                     57,
		"JUMP_MATURITY_CONSENT":                                     58,
		"PAY_INCIDENT_MANAGER_UPI_PIN_FLOW_ERROR":                   59,
		"FUND_TRANSFER_V1":                                          60,
		"US_STOCK_ETF_SUPPORT":                                      61,
		"ONBOARDING_ADD_FUNDS_V2":                                   62,
		"FRESHDESK_MONORAIL_INTEGRATION":                            63,
		"CC_UPI_LINKING":                                            64,
		"CA_CONNECT_FI_TO_FI":                                       65,
		"PAY_INCIDENT_MANAGER_P2P_DEEMED_TRANSACTION":               66,
		"PAY_INCIDENT_MANAGER_P2M_DEEMED_TRANSACTION":               67,
		"JUMP_ALL_ACTIVITY_DEEPLINK":                                68,
		"NEW_VPA_HANDLE":                                            69,
		"UPDATED_BENEFITS_FOR_AFFLUENT_USER_ONB_ADD_FUNDS":          70,
		"FEATURE_CC_REALTIME_ELIGIBILITY":                           71,
		"CREDIT_SCORE_ANALYSER_PROFILE_DETAILS_V2":                  72,
		"INAPPHELP_FEEDBACK_ENGINE":                                 73,
		"FETCH_ORDER_WITH_TRANSACTIONS_USING_IN_MEMORY_JOIN":        74,
		"AFFLUENT_USER_BONUS_TRANSITION_SCREEN_NON_REFEREE":         75,
		"REFERRALS_V1_LANDING_PAGE_STACKED_REWARDS_COMPONENT":       76,
		"FEATURE_CC_EMI":                                            77,
		"CC_REGISTER_CUSTOMER_V2":                                   78,
		"FEATURE_JUMP_INVEST_PAGE_V2":                               79,
		"USS_LANDING_PAGE_AB_SUPPORT":                               80,
		"USS_COLLECTION_SCREEN_AB_SUPPORT":                          81,
		"MF_HOLDINGS_IMPORT_PHONE_NUMBER_SCREEN":                    82,
		"CREDIT_SCORE_ANALYSER_AUTO_REFRESH":                        83,
		"ATT_IOS_PERMISSION_PROMPT":                                 84,
		"CONSENT_RENEWAL_WIDGET_IN_ASKFI":                           85,
		"FEATURE_JUMP_NEW_LANDING_PAGE":                             86,
		"AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET":                   87,
		"QUICK_RECAT":                                               88,
		"POST_PAYMENT_SCREEN":                                       89,
		"APP_UPDATE_HARD_NUDGE_REFERRALS":                           90,
		"APP_UPDATE_SOFT_NUDGE_REFERRALS":                           91,
		"REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO":        92,
		"FEATURE_UPDATE_CARD_DETAILS_AT_BANK":                       93,
		"UPI_LITE":                                                  94,
		"FEATURE_CC_ONBOARDING_WITHOUT_BILL_GEN_DATE_CAPTURE_STAGE": 95,
		"INVESTMENT_RETENTION_UI":                                   96,
		"LOANS_EARLY_SALARY":                                        97,
		"LOANS_FI_LITE":                                             98,
		"NETWORTH_DASHBOARD":                                        99,
		"FEATURE_BIOMETRIC_REVALIDATION":                            100,
		"NETWORTH_DEPOSITS_WIDGET":                                  101,
		"FEATURE_REMITTER_INFO_BACKFILL":                            102,
		"FEATURE_NEW_ENDPOINT_INBOUND_NOTIFICATION":                 103,
		"IMPS_DEEMED_HANDLING":                                      104,
		"PAY_INCIDENT_MANAGER_DEBITED_TRANSACTION_THRESHOLD_BREACH": 105,
		"HEALTH_ENGINE_FOR_INTRA_PAYMENTS":                          106,
		"HEALTH_ENGINE_FOR_NEFT_PAYMENTS":                           107,
		"HEALTH_ENGINE_FOR_IMPS_PAYMENTS":                           108,
		"HEALTH_ENGINE_FOR_RTGS_PAYMENTS":                           109,
		"PAY_INCIDENT_MANAGER_TRANSACTION_DETAILED_STATUS_UPDATE":   110,
		"IS_CREATE_DEPOSIT_V2_ENABLED":                              111,
		"IS_CLOSE_DEPOSIT_V2_ENABLED":                               112,
		"FEATURE_CC_INTRO_SCREEN_HORIZONTAL_LAYOUT":                 113,
		"NEW_ADD_FUNDS_VPA":                                         114,
		"FEATURE_CC_USER_INELIGIBLE_FEEDBACK":                       115,
		"FIRST_TIME_PAYMENT_PROMPT":                                 116,
		"ANALYSER_LANDING_PAGE_DISABLED_CARD":                       117,
		"PHONE_NUMBER_AS_REFERRAL_CODE":                             118,
		"CARD_SWITCH_NOTIFICATION":                                  119,
		"SORT_RECENT_ACTIVITIES_ON_CREATED_AT":                      120,
		"IN_APP_CSAT_SURVEY":                                        121,
		"MF_INVESTMENT_CALCULATOR_UI":                               122,
		"MF_NAV_GRAPH_UI":                                           123,
		"ANALYSER_ERROR_VIEW_V2":                                    124,
		"SELF_TRANSFER":                                             125,
		"AA_DATA_FETCH_WAIT_FOR_ALL_NOTIFICATION":                   126,
		"FEATURE_CC_REGISTER_CUSTOMER_V3":                           127,
		"FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN":                      128,
		"SALARY_LITE_PROGRAM":                                       129,
		"UPCOMING_TRANSACTIONS_V2":                                  130,
		"CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2":                      131,
		"NETWORTH_IND_SECURITIES_WIDGET":                            132,
		"AA_EQUITY_ACC_HOME_SUMMARY":                                133,
		"FEATURE_CC_SECURED_CARDS":                                  134,
		"VPA_MIGRATION_INTRO_SCREEN":                                135,
		"MF_HOLDINGS_IMPORT_V2_FLOW":                                136,
		"FEATURE_CC_INHOUSE_BRE":                                    137,
		"FEATURE_FORCED_BALANCE_REFRESH":                            138,
		"FEATURE_SECURED_LOANS":                                     139,
		"CX_CHATBOT_USER_ACTIVITY":                                  140,
		"BENEFICIARY_COOL_DOWN_RULE":                                141,
		"ALLOW_AND_CONTROL_AA_TXN_FOR_TPAP":                         142,
		"CREDIT_REPORT_CONSENT_FOR_SA_ON_PAN_DOB_STAGE":             143,
		"MASS_UNSECURED_CARD":                                       144,
		"MANUAL_ASSET_FORM_AIF":                                     145,
		"MANUAL_ASSET_FORM_ART_ARTEFACTS":                           146,
		"MANUAL_ASSET_FORM_BONDS":                                   147,
		"MANUAL_ASSET_FORM_CASH":                                    148,
		"MANUAL_ASSET_FORM_DIGITAL_GOLD":                            149,
		"MANUAL_ASSET_FORM_DIGITAL_SILVER":                          150,
		"MANUAL_ASSET_FORM_PRIVATE_EQUITY":                          151,
		"MANUAL_ASSET_FORM_REAL_ESTATE":                             152,
		"ACTIVATE_BENEFICIARY_VIA_LIVENESS":                         153,
		"AUTOPAY_HUB":                                               154,
		"SCREENER_CHOICE_PAGE":                                      155,
		"SPEND_ANALYSER_INVEST_MORE_INSIGHT":                        156,
		"SPEND_ANALYSER_INVEST_SURPLUS_BALANCE_INSIGHT":             157,
		"SPEND_ANALYSER_EARLY_SALARY_INSIGHT":                       158,
		"SPEND_ANALYSER_SET_REMINDER_INSIGHT":                       159,
		"PAY_ASK_FI_SEARCH_SCREEN":                                  160,
		"SUPPORT_FOR_BENEFICIARY_COOL_DOWN_IN_PAY_BY_PHONE_NUMBER":  161,
		"ONB_ADD_FUNDS_TIERING_SUCCESS":                             162,
		"SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT":      163,
		"FEATURE_CREDIT_CARD_TPAP_PAYMENTS":                         164,
		"AA_PERMITTED_FIP_CONFIG_V2":                                165,
		"FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK":              166,
		"ADD_FUNDS_V3":                                              167,
		"ONBOARDING_ADD_FUNDS_V2_2":                                 168,
		"DEPOSIT_AUTO_RENEW_CTA":                                    169,
		"FEATURE_ENABLE_SIMPLIFI_CREDIT_CARD_ATM":                   170,
		"LOANS_FEDERAL_V2":                                          171,
		"ADD_FUNDS_V4":                                              172,
		"ALFRED_SAVINGS_ACC_SIGN_UPDATE":                            173,
		"FEATURE_CC_EMI_SIMPLIFI":                                   174,
		"FEATURE_CC_EMI_MAGNIFI":                                    175,
		"VKYC_NEW_REVIEW_SCREEN":                                    176,
		"LOANS_CIBIL_REPORT_FETCH":                                  177,
		"INDIAN_SECURITIES_ETF":                                     178,
		"ONE_CLICK_TPAP_ENABLEMENT_FLOW":                            179,
		"CREDIT_REPORT_ADDRESS_SELECTION":                           180,
		"VKYC_PAN_IMAGE_CAPTURE":                                    181,
		"MANUAL_UAN_EPF_FLOW":                                       182,
		"FEATURE_CC_FILITE_CIBIL_INTEGRATION":                       183,
		"TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH":                184,
		"DEBIT_CARD_OFFER_WIDGET_HOME":                              187,
		"LOANS_TPAP_PRE_PAYMENT":                                    188,
		"NSDL_PAN_API_V2_FOR_CA":                                    189,
		"FEATURE_CC_PREAPPROVED_AMPLIFI":                            185,
		"FEATURE_CC_BRE_AMPLIFI":                                    186,
		"FEATURE_CC_PREAPPROVED_SIMPLIFI":                           190,
		"FEATURE_CC_BRE_SIMPLIFI":                                   191,
		"FEATURE_CC_PREAPPROVED_MAGNIFI":                            192,
		"FEATURE_CC_BRE_MAGNIFI":                                    193,
		"REPORT_FRAUD_ORDER_RECEIPT":                                194,
		"FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT":                   195,
		"FEATURE_LAMF_PHASE3_1":                                     196,
		"FEATURE_LOANS_INCOME_ESTIMATE_VIA_ITR":                     197,
		"NETWORTH_REFRESH_V2":                                       198,
		"FEATURE_PAN_VALIDATE_V2_AADHAAR_LINK_STATUS":               199,
		"FEATURE_MS_CLARITY_SDK_ENABLED":                            200,
		"FEATURE_POST_LOAN_DISBURSAL_SCREENS":                       201,
		"FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE":           202,
		"MANUAL_ASSET_FORM_PORTFOLIO_MANAGEMENT_SERVICE":            203,
		"FEATURE_LOAN_ADD_ALTERNATE_PHONE_NUMBER":                   204,
		"LOANS_IDFC_VKYC_V2":                                        205,
		"FEATURE_USS_TABBED_CARD_RTSP":                              206,
		"NSDL_PAN_FLOW_V2_MF_ANALYSER":                              207,
		"NSDL_PAN_FLOW_V2_CREDIT_REPORT_ANALYSER":                   208,
		"FEATURE_OFF_APP_ENACH_CANCELLATION":                        209,
		"MANUAL_ASSET_FORM_PUBLIC_PROVIDENT_FUND":                   210,
		"MANUAL_ASSET_FORM_EMPLOYEE_STOCK_OPTION":                   211,
		"LOANS_LIQUILOANS_EARLY_SALARY_V2":                          212,
		"FEATURE_CX_CALL_IVR":                                       213,
		"FEATURE_CC_AUTOMATIC_FEE_WAIVER":                           214,
		"FEATURE_ALFRED_USS_TAX_DOCUMENT_REQUEST":                   215,
		"REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL":                 216,
		"VKYC_CALL_QUALITY_SCREEN":                                  217,
		"PHYSICAL_DEBIT_CARD_CHARGES_PAYMENT_OPTIONS_SCREEN":        218,
		"FEATURE_DC_DASHBOARD_V2_SCREEN":                            219,
		"ASSET_LANDING_PAGE_FOR_MANUAL_ASSET":                       220,
		"FEATURE_AMPLIFI_CC_MINUTES":                                221,
		"ANALYSER_HUB_FI_TO_FI_INTEGRATION":                         222,
		"FEATURE_CC_REWARD_DASHBOARD_UNSECURED_CARD":                223,
		"FEATURE_CC_REWARD_DASHBOARD_MASS_UNSECURED_CARD":           224,
		"FEATURE_CC_REWARD_DASHBOARD_SECURED_CARD":                  225,
		"FEATURE_OFF_APP_UPI_VIA_TEMPORAL":                          226,
		"FEATURE_INDIAN_SECURITIES_REIT":                            227,
		"FEATURE_INDIAN_SECURITIES_INVIT":                           228,
		"FEATURE_ADD_FUNDS_USS_DOUBLE_PIN_FLOW":                     229,
		"FEATURE_NET_WORTH_NPS":                                     230,
		"FEATURE_CC_OFFERS_WIDGET":                                  231,
		"FEATURE_AMOUNT_SCREEN_IN_COLLECT_PN":                       232,
		"FEATURE_MONEY_SECRET_HOME_SCREEN":                          233,
		"FEATURE_CC_DASHBOARD_BENEFITS_WIDGET_UNSECURED_CARD":       234,
		"FEATURE_IN_APP_ISSUE_REPORTING_FLOW":                       235,
		"FEATURE_LOAN_PRE_PAY_VIA_PG":                               236,
		"FEATURE_CA_BANK_SELECTION":                                 237,
		"FEATURE_HOME_SINGLE_RPC":                                   238,
		"FEATURE_DISABLE_DC_DELIVERY_ADDRESS_UPDATE":                239,
		"FEATURE_ENABLE_CONSENT_SCREEN_V2":                          240,
		"FEATURE_CX_HELP_RECENT_ACTIVITY":                           241,
		"FEATURE_PAY_RECEIPT_NEW_ERROR_VIEW":                        242,
		"FEATURE_PMS_PROVIDER_AND_AIF_SEARCH":                       243,
		"FEATURE_LOANS_NON_FI_CORE_ELIGIBILITY_V2":                  244,
		"FEATURE_DC_INTERNATIONAL_ATM_WITHDRAWAL_LAYOUT":            245,
		"FEATURE_US_STOCKS_SIP":                                     246,
		"FEATURE_ENABLE_ACCOUNT_OPERATIONAL_STATUS_CHECK":           247,
		"FEATURE_ENABLE_PAYMENT_OPTIONS_V1":                         248,
		"FEATURE_IN_APP_ISSUE_REPORTING_NON_FCR_FLOW":               249,
		"FEATURE_ENABLE_ORDER_PHYSICAL_CARD_DURING_ONBOARDING":      250,
		"FEATURE_DC_TRAVEL_MODE":                                    251,
		"FEATURE_NETWORTH_DASHBOARD_SECRET_SUMMARIES":               252,
		"FEATURE_DC_FOREX_ORDER_RECEIPT":                            253,
		"FEATURE_HOME_BOTTOM_NAV_BAR_NETWORTH_SECTION":              254,
		"FEATURE_PAY_SEARCH_V2":                                     255,
		"FEATURE_CX_CALL_BLOCKER":                                   256,
		"FEATURE_REGULAR_TIER":                                      257,
		"FEATURE_EPF_GENERIC_ERROR_SCREEN":                          258,
		"FEATURE_ALFRED_USS_DOCUMENT_REQUEST":                       259,
		"FEATURE_DC_TOGGLE_TRAVEL_MODE":                             260,
		"FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT":              263,
		"FEATURE_REQUEST_APP_UPGRADE_FOR_CHATBOT_LOADING":           261,
		"FEATURE_US_STOCKS_LIMIT_ORDER":                             262,
		"FEATURE_SMS_PARSER_PARTNER_SDK":                            264,
		"FEATURE_UPI_MANDATES":                                      265,
		"FEATURE_MONEY_SECRET_V2":                                   266,
		"FEATURE_REPORT_FRAUD_IN_DISPUTE_BOTTOM_SHEET":              267,
		"FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME":                  268,
		"FEATURE_ISSUE_REPORTING_ASK_FI_INTEGRATION":                269,
		"FEATURE_ALTERNATE_APP_ICON":                                270,
		"FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB":                   271,
		"FEATURE_CX_CALL_RISK_IVR_FLOW":                             272,
		"PRIME_SMS_PARSER":                                          273,
		"FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM":                274,
		"FEATURE_LENDING_TPAP_IN_PAYMENT_OPTIONS_SCREEN":            275,
		"FEATURE_SHARE_POST_PAYMENT_SCREEN":                         276,
		"FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW":                    277,
		"FEATURE_SELF_TRANSFER_SCREEN":                              278,
		"FEATURE_MONEY_SECRET_PEER_COMPARISON":                      279,
		"FEATURE_VPA_PREFERENCE_FOR_LIST_ACCOUNT":                   280,
		"FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP":      281,
		"FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP": 282,
		"FEATURE_PAY_INCIDENT_MANAGER_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP":             283,
		"FEATURE_PAY_ADD_FUNDS_PREFUNDING":                                                  284,
		"FEATURE_SALARY_REPORT_MONEY_SECRET":                                                285,
		"FEATURE_ONBOARDING_PRE_ACCOUNT_CREATION_ADD_FUNDS":                                 286,
		"FEATURE_DC_TRAVEL_MODE_LOTTIE":                                                     287,
		"FEATURE_RESTRICT_LIST_ACCOUNT":                                                     288,
		"FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH":                                       289,
		"FEATURE_TIERING_MULTIPLE_WAYS":                                                     290,
		"FEATURE_DC_MANDATES":                                                               291,
		"FEATURE_OFF_APP_UPI_PREEMPT":                                                       292,
		"FEATURE_MONEY_SECRET_FOOTER_V2":                                                    293,
		"FEATURE_PAY_INCIDENT_MANAGER_CHEQUE_CREDIT_TRANSACTION":                            294,
		"FEATURE_ORDER_RECEIPT_GENERIC_DESCRIPTION_DETAILS_TILE":                            295,
		"FEATURE_CX_TICKET_RESOLUTION_CSAT_COMMS":                                           296,
		"FEATURE_DC_ORDER_SCREEN_WA_ADDRESS_UPDATE_FLOW":                                    297,
		"FEATURE_VKYC_FLOW_V2":                                                              298,
		"FEATURE_VKYC_BENEFIT_SCREEN_V2":                                                    299,
		"FEATURE_SHOW_STALE_COMPUTED_BALANCE_WARNING":                                       300,
		"FEATURE_ASSET_IMPORT_FLOW":                                                         301,
		"FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW":                                       302,
		"FEATURE_MF_IMPORT_WEALTH_BUILDER_ONBOARDING":                                       303,
		"FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION":                                       304,
		"FEATURE_ASSET_DASHBOARD_FLOW":                                                      305,
		"FEATURE_BENEFICIARY_NAME_LOOKUP":                                                   306,
		"FEATURE_DC_CHARGES_API":                                                            307,
		"FEATURE_UPDATE_SAVINGS_ACCOUNT_NOMINEE":                                            308,
		"FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI":                                           309,
		"FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME":                                             310,
		"FEATURE_SHOW_REQUIRED_BALANCE_FOR_TIER_UPGRADE":                                    311,
		"FEATURE_CHATHEADS_OPTIMISED_QUERY":                                                 312,
		"FEATURE_MONEY_PLANT_EARNED_BENEFITS":                                               313,
		"FEATURE_PAY_INCIDENT_MANAGER_ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE":                 314,
		"FEATURE_HOME_DESIGN_ENHANCEMENTS":                                                  315,
		"FEATURE_WEALTH_BUILDER_NETWORTH_PAGE":                                              316,
		"FEATURE_QR_SCAN_ENHANCEMENTS":                                                      317,
		"FEATURE_SEND_SMS_DATA_WEALTH_BUILDER":                                              318,
		"FEATURE_LOANS_NEW_DATA_COLLECTION_SCREENS":                                         319,
		"FEATURE_UPI_QUICK_LINK_BANNER_PAY_LANDING_SCREEN":                                  320,
		"FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN":                                          321,
		"FEATURE_LOANS_SECOND_LOOK_V1":                                                      322,
		"FEATURE_LOANS_PREQUAL_OFFER_FLOW":                                                  323,
		"FEATURE_SHOULD_SHOW_PIN_SCREEN_V2":                                                 324,
		"FEATURE_PAY_DESIGN_ENHANCEMENTS":                                                   325,
		"FEATURE_FAILED_ENACH_TRANSACTIONS":                                                 326,
		"FEATURE_AMB_ENTRYPOINT_BANNER":                                                     327,
		"FEATURE_CA_FLOW_WEALTH_BUILDER_ONBOARDING":                                         328,
		"FEATURE_DAILY_TOTAL_TRANSACTION_AMOUNT_LIMIT_ON_APP":                               329,
		"FEATURE_FRESHCHAT_CHATBOT_SDK_ENABLED":                                             330,
		"FEATURE_CX_NEW_LANDING_PAGE":                                                       331,
		"FEATURE_REWARDS_CATALOG_MERGED_PAGE":                                               332,
		"FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION":                                      333,
		"FEATURE_WB_DASHBOARD_LIABILITIES":                                                  334,
		"FEATURE_WB_MAGIC_IMPORT":                                                           335,
		"FEATURE_WEEKLY_PORTFOLIO_TRACKER":                                                  336,
		"FEATURE_US_FUNDS_IN_INVEST_LANDING_PAGE":                                           337,
		"FEATURE_UPI_ENQUIRY_REDIS_BASED_COUNTER":                                           338,
		"FEATURE_PAY_LANDING_BANNER_RUPAY_CC_BE_DRIVEN":                                     339,
		"FEATURE_PAY_LANDING_SCREEN_BANNER_DYNAMIC_ELEMENTS":                                340,
		"FEATURE_FI_MCP_TOTP_CODE":                                                          341,
		"FEATURE_CREDIT_CARD_V2_FLOW":                                                       342,
	}
)

func (x Feature) Enum() *Feature {
	p := new(Feature)
	*p = x
	return p
}

func (x Feature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Feature) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_feature_proto_enumTypes[0].Descriptor()
}

func (Feature) Type() protoreflect.EnumType {
	return &file_api_typesv2_feature_proto_enumTypes[0]
}

func (x Feature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Feature.Descriptor instead.
func (Feature) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_feature_proto_rawDescGZIP(), []int{0}
}

var File_api_typesv2_feature_proto protoreflect.FileDescriptor

var file_api_typesv2_feature_proto_rawDesc = []byte{
	0x0a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2a, 0xd0, 0x64, 0x0a, 0x07, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x46, 0x5f, 0x55,
	0x49, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x41, 0x59, 0x5f, 0x56, 0x49, 0x41, 0x5f, 0x50,
	0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x02, 0x12, 0x15, 0x0a,
	0x11, 0x4d, 0x46, 0x5f, 0x41, 0x44, 0x56, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x49, 0x4c, 0x54,
	0x45, 0x52, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x46, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x5f,
	0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x10, 0x04, 0x12, 0x1b, 0x0a, 0x17, 0x44, 0x49, 0x53, 0x50,
	0x55, 0x54, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x41, 0x50, 0x50, 0x5f,
	0x54, 0x58, 0x4e, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x4d, 0x46, 0x5f, 0x53, 0x49, 0x50, 0x10,
	0x06, 0x12, 0x1b, 0x0a, 0x17, 0x4d, 0x46, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x4f, 0x54, 0x49, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x07, 0x12, 0x1f,
	0x0a, 0x1b, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x54,
	0x49, 0x43, 0x4b, 0x45, 0x54, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x08, 0x12,
	0x17, 0x0a, 0x13, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x4c, 0x41, 0x59,
	0x4f, 0x55, 0x54, 0x5f, 0x56, 0x32, 0x10, 0x09, 0x12, 0x15, 0x0a, 0x11, 0x4d, 0x45, 0x52, 0x43,
	0x48, 0x41, 0x4e, 0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x0a, 0x12,
	0x11, 0x0a, 0x0d, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52,
	0x10, 0x0b, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x45, 0x4e, 0x53, 0x45, 0x46, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x10, 0x0c, 0x12, 0x21, 0x0a, 0x1d, 0x41, 0x41,
	0x5f, 0x46, 0x49, 0x4e, 0x56, 0x55, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x41, 0x55, 0x54,
	0x48, 0x45, 0x4e, 0x54, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x0b, 0x0a,
	0x07, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x56, 0x32, 0x10, 0x0e, 0x12, 0x26, 0x0a, 0x22, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f,
	0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52,
	0x10, 0x0f, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x49, 0x5f, 0x54, 0x50, 0x41, 0x50, 0x10, 0x10,
	0x12, 0x19, 0x0a, 0x15, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x11, 0x12, 0x15, 0x0a, 0x11, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b,
	0x10, 0x12, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x49, 0x5f, 0x4d, 0x49, 0x4e, 0x55, 0x54, 0x45, 0x5f,
	0x48, 0x55, 0x42, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0x13, 0x12, 0x0f, 0x0a, 0x0b,
	0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x55, 0x49, 0x10, 0x14, 0x12, 0x1c, 0x0a,
	0x18, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x49, 0x10, 0x15, 0x12, 0x2b, 0x0a, 0x27, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f, 0x48, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x42,
	0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x10, 0x16, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x53, 0x4b, 0x5f,
	0x46, 0x49, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x42,
	0x41, 0x52, 0x10, 0x17, 0x12, 0x1d, 0x0a, 0x19, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c,
	0x53, 0x5f, 0x56, 0x31, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47,
	0x45, 0x10, 0x18, 0x12, 0x15, 0x0a, 0x11, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x52, 0x45, 0x4e, 0x45,
	0x57, 0x41, 0x4c, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x19, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x50,
	0x49, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x45, 0x52, 0x10, 0x1a, 0x12, 0x1c, 0x0a, 0x18, 0x43, 0x52,
	0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59,
	0x53, 0x45, 0x52, 0x5f, 0x56, 0x32, 0x10, 0x1b, 0x12, 0x0d, 0x0a, 0x09, 0x4d, 0x4c, 0x5f, 0x4b,
	0x49, 0x54, 0x5f, 0x51, 0x52, 0x10, 0x1c, 0x12, 0x1f, 0x0a, 0x1b, 0x45, 0x4e, 0x41, 0x42, 0x4c,
	0x45, 0x5f, 0x47, 0x45, 0x54, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x45, 0x58, 0x54, 0x5f,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x1d, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x32, 0x46, 0x41, 0x5f, 0x4d, 0x46, 0x5f, 0x4f, 0x4e, 0x45, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x42, 0x55, 0x59, 0x10, 0x1e, 0x12, 0x1e, 0x0a, 0x1a, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x32, 0x46, 0x41, 0x5f, 0x4d, 0x46, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54,
	0x45, 0x52, 0x5f, 0x53, 0x49, 0x50, 0x10, 0x1f, 0x12, 0x20, 0x0a, 0x1c, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x46, 0x49, 0x54, 0x5f, 0x53, 0x49, 0x50, 0x5f, 0x43, 0x4f,
	0x4e, 0x56, 0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x10, 0x20, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x53, 0x49,
	0x47, 0x48, 0x54, 0x53, 0x10, 0x21, 0x12, 0x27, 0x0a, 0x23, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f,
	0x52, 0x59, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x46, 0x49, 0x4c, 0x54, 0x45, 0x52, 0x5f, 0x56, 0x32, 0x10, 0x22, 0x12,
	0x2a, 0x0a, 0x26, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x41,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x53,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x23, 0x12, 0x2b, 0x0a, 0x27, 0x52,
	0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x47, 0x45, 0x4e,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x5f, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x53, 0x49, 0x44, 0x45, 0x10, 0x24, 0x12, 0x2a, 0x0a, 0x26, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e,
	0x45, 0x52, 0x10, 0x25, 0x12, 0x29, 0x0a, 0x25, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x44, 0x41, 0x53,
	0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x49,
	0x4e, 0x56, 0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x53, 0x10, 0x26, 0x12,
	0x20, 0x0a, 0x1c, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x53, 0x10,
	0x27, 0x12, 0x2e, 0x0a, 0x2a, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53,
	0x49, 0x54, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4c, 0x41,
	0x54, 0x45, 0x5f, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x5f, 0x54, 0x45, 0x52, 0x4d, 0x10,
	0x28, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x58, 0x4e, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x31, 0x10, 0x29, 0x12, 0x19, 0x0a, 0x15,
	0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f, 0x42, 0x41, 0x52, 0x5f,
	0x43, 0x48, 0x49, 0x50, 0x53, 0x10, 0x2a, 0x12, 0x10, 0x0a, 0x0c, 0x56, 0x45, 0x52, 0x49, 0x46,
	0x59, 0x5f, 0x51, 0x52, 0x5f, 0x56, 0x31, 0x10, 0x2b, 0x12, 0x28, 0x0a, 0x24, 0x52, 0x45, 0x46,
	0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x44, 0x55, 0x52,
	0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x56,
	0x31, 0x10, 0x2c, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x46, 0x46, 0x4c, 0x55, 0x45, 0x4e, 0x54, 0x5f,
	0x55, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x4f, 0x4e, 0x55, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x2d, 0x12, 0x1e,
	0x0a, 0x1a, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x2e, 0x12, 0x20,
	0x0a, 0x1c, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x48, 0x45,
	0x4d, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x2f,
	0x12, 0x27, 0x0a, 0x23, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45,
	0x52, 0x5f, 0x55, 0x50, 0x43, 0x4f, 0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x30, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x50, 0x49,
	0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x55, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x41,
	0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x10, 0x31, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x52, 0x49, 0x4f,
	0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x4f, 0x56, 0x45, 0x52,
	0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x32, 0x12, 0x18, 0x0a, 0x14,
	0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x5a, 0x45, 0x52, 0x4f, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x53, 0x10, 0x33, 0x12, 0x2c, 0x0a, 0x28, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x55, 0x54,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x44, 0x45, 0x45, 0x50, 0x4c, 0x49,
	0x4e, 0x4b, 0x10, 0x34, 0x12, 0x1d, 0x0a, 0x19, 0x55, 0x50, 0x49, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x35, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x43, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x53, 0x4b, 0x46, 0x49, 0x10, 0x36, 0x12, 0x1d, 0x0a,
	0x19, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x48, 0x4f, 0x4d, 0x45,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x37, 0x12, 0x16, 0x0a, 0x12,
	0x41, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57,
	0x41, 0x4c, 0x10, 0x38, 0x12, 0x19, 0x0a, 0x15, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x5f, 0x4c,
	0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0x39, 0x12,
	0x19, 0x0a, 0x15, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x4d, 0x41, 0x54, 0x55, 0x52, 0x49, 0x54, 0x59,
	0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x3a, 0x12, 0x2b, 0x0a, 0x27, 0x50, 0x41,
	0x59, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47,
	0x45, 0x52, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x49, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x3b, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x55, 0x4e, 0x44, 0x5f,
	0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x56, 0x31, 0x10, 0x3c, 0x12, 0x18, 0x0a,
	0x14, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x45, 0x54, 0x46, 0x5f, 0x53, 0x55,
	0x50, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x3d, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f,
	0x56, 0x32, 0x10, 0x3e, 0x12, 0x22, 0x0a, 0x1e, 0x46, 0x52, 0x45, 0x53, 0x48, 0x44, 0x45, 0x53,
	0x4b, 0x5f, 0x4d, 0x4f, 0x4e, 0x4f, 0x52, 0x41, 0x49, 0x4c, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x3f, 0x12, 0x12, 0x0a, 0x0e, 0x43, 0x43, 0x5f, 0x55,
	0x50, 0x49, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x40, 0x12, 0x17, 0x0a, 0x13,
	0x43, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x5f, 0x46, 0x49, 0x5f, 0x54, 0x4f,
	0x5f, 0x46, 0x49, 0x10, 0x41, 0x12, 0x2f, 0x0a, 0x2b, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x43,
	0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x52, 0x5f, 0x50, 0x32,
	0x50, 0x5f, 0x44, 0x45, 0x45, 0x4d, 0x45, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x42, 0x12, 0x2f, 0x0a, 0x2b, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e,
	0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x52, 0x5f, 0x50,
	0x32, 0x4d, 0x5f, 0x44, 0x45, 0x45, 0x4d, 0x45, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x43, 0x12, 0x1e, 0x0a, 0x1a, 0x4a, 0x55, 0x4d, 0x50, 0x5f,
	0x41, 0x4c, 0x4c, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x45, 0x45,
	0x50, 0x4c, 0x49, 0x4e, 0x4b, 0x10, 0x44, 0x12, 0x12, 0x0a, 0x0e, 0x4e, 0x45, 0x57, 0x5f, 0x56,
	0x50, 0x41, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x45, 0x10, 0x45, 0x12, 0x34, 0x0a, 0x30, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x41, 0x46, 0x46, 0x4c, 0x55, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x53, 0x45,
	0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10,
	0x46, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f,
	0x52, 0x45, 0x41, 0x4c, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49,
	0x4c, 0x49, 0x54, 0x59, 0x10, 0x47, 0x12, 0x2c, 0x0a, 0x28, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54,
	0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f,
	0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f,
	0x56, 0x32, 0x10, 0x48, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x41, 0x50, 0x50, 0x48, 0x45, 0x4c,
	0x50, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e,
	0x45, 0x10, 0x49, 0x12, 0x36, 0x0a, 0x32, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x55, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x5f, 0x4d, 0x45,
	0x4d, 0x4f, 0x52, 0x59, 0x5f, 0x4a, 0x4f, 0x49, 0x4e, 0x10, 0x4a, 0x12, 0x35, 0x0a, 0x31, 0x41,
	0x46, 0x46, 0x4c, 0x55, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x42, 0x4f, 0x4e,
	0x55, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45, 0x45,
	0x10, 0x4b, 0x12, 0x37, 0x0a, 0x33, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f,
	0x56, 0x31, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f,
	0x53, 0x54, 0x41, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x4c, 0x12, 0x12, 0x0a, 0x0e, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x45, 0x4d, 0x49, 0x10, 0x4d, 0x12,
	0x1b, 0x0a, 0x17, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x5f, 0x43,
	0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x56, 0x32, 0x10, 0x4e, 0x12, 0x1f, 0x0a, 0x1b,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x49, 0x4e, 0x56,
	0x45, 0x53, 0x54, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x56, 0x32, 0x10, 0x4f, 0x12, 0x1f, 0x0a,
	0x1b, 0x55, 0x53, 0x53, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47,
	0x45, 0x5f, 0x41, 0x42, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x50, 0x12, 0x24,
	0x0a, 0x20, 0x55, 0x53, 0x53, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x41, 0x42, 0x5f, 0x53, 0x55, 0x50, 0x50, 0x4f,
	0x52, 0x54, 0x10, 0x51, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49,
	0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45,
	0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x52,
	0x12, 0x26, 0x0a, 0x22, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x4f, 0x52, 0x45,
	0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x52,
	0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x10, 0x53, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x54, 0x54, 0x5f,
	0x49, 0x4f, 0x53, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x50,
	0x52, 0x4f, 0x4d, 0x50, 0x54, 0x10, 0x54, 0x12, 0x23, 0x0a, 0x1f, 0x43, 0x4f, 0x4e, 0x53, 0x45,
	0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x41, 0x4c, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45,
	0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x53, 0x4b, 0x46, 0x49, 0x10, 0x55, 0x12, 0x21, 0x0a, 0x1d,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4a, 0x55, 0x4d, 0x50, 0x5f, 0x4e, 0x45, 0x57,
	0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0x56, 0x12,
	0x2b, 0x0a, 0x27, 0x41, 0x41, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45,
	0x4e, 0x45, 0x57, 0x41, 0x4c, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x58, 0x4e, 0x53, 0x5f, 0x50,
	0x41, 0x47, 0x45, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x10, 0x57, 0x12, 0x0f, 0x0a, 0x0b,
	0x51, 0x55, 0x49, 0x43, 0x4b, 0x5f, 0x52, 0x45, 0x43, 0x41, 0x54, 0x10, 0x58, 0x12, 0x17, 0x0a,
	0x13, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x59, 0x12, 0x23, 0x0a, 0x1f, 0x41, 0x50, 0x50, 0x5f, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x45, 0x5f, 0x48, 0x41, 0x52, 0x44, 0x5f, 0x4e, 0x55, 0x44, 0x47, 0x45, 0x5f,
	0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x10, 0x5a, 0x12, 0x23, 0x0a, 0x1f, 0x41,
	0x50, 0x50, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x4f, 0x46, 0x54, 0x5f, 0x4e,
	0x55, 0x44, 0x47, 0x45, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x10, 0x5b,
	0x12, 0x36, 0x0a, 0x32, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x5f, 0x56, 0x31,
	0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x5f, 0x48, 0x49, 0x53, 0x54, 0x4f, 0x52,
	0x59, 0x5f, 0x45, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52,
	0x59, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x5c, 0x12, 0x27, 0x0a, 0x23, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x41, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10,
	0x5d, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x5e, 0x12,
	0x3d, 0x0a, 0x39, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x4f, 0x4e,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x4f, 0x55, 0x54,
	0x5f, 0x42, 0x49, 0x4c, 0x4c, 0x5f, 0x47, 0x45, 0x4e, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x43,
	0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x10, 0x5f, 0x12, 0x1b,
	0x0a, 0x17, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x54,
	0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x49, 0x10, 0x60, 0x12, 0x16, 0x0a, 0x12, 0x4c,
	0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x10, 0x61, 0x12, 0x11, 0x0a, 0x0d, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x46, 0x49, 0x5f,
	0x4c, 0x49, 0x54, 0x45, 0x10, 0x62, 0x12, 0x16, 0x0a, 0x12, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52,
	0x54, 0x48, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x10, 0x63, 0x12, 0x22,
	0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x42, 0x49, 0x4f, 0x4d, 0x45, 0x54,
	0x52, 0x49, 0x43, 0x5f, 0x52, 0x45, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x64, 0x12, 0x1c, 0x0a, 0x18, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44,
	0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x53, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x10, 0x65,
	0x12, 0x22, 0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x4d, 0x49,
	0x54, 0x54, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x42, 0x41, 0x43, 0x4b, 0x46, 0x49,
	0x4c, 0x4c, 0x10, 0x66, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x4e, 0x45, 0x57, 0x5f, 0x45, 0x4e, 0x44, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x42,
	0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x10, 0x67, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4d, 0x50, 0x53, 0x5f, 0x44, 0x45, 0x45, 0x4d,
	0x45, 0x44, 0x5f, 0x48, 0x41, 0x4e, 0x44, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x68, 0x12, 0x3d, 0x0a,
	0x39, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41,
	0x4e, 0x41, 0x47, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x45, 0x44, 0x5f, 0x54, 0x52,
	0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x48, 0x52, 0x45, 0x53, 0x48,
	0x4f, 0x4c, 0x44, 0x5f, 0x42, 0x52, 0x45, 0x41, 0x43, 0x48, 0x10, 0x69, 0x12, 0x24, 0x0a, 0x20,
	0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53,
	0x10, 0x6a, 0x12, 0x23, 0x0a, 0x1f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x45, 0x4e, 0x47,
	0x49, 0x4e, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4e, 0x45, 0x46, 0x54, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x6b, 0x12, 0x23, 0x0a, 0x1f, 0x48, 0x45, 0x41, 0x4c, 0x54,
	0x48, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x49, 0x4d, 0x50,
	0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0x6c, 0x12, 0x23, 0x0a, 0x1f,
	0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x45, 0x4e, 0x47, 0x49, 0x4e, 0x45, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x52, 0x54, 0x47, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10,
	0x6d, 0x12, 0x3b, 0x0a, 0x37, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e,
	0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x6e, 0x12, 0x20,
	0x0a, 0x1c, 0x49, 0x53, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x5f, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x5f, 0x56, 0x32, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0x6f,
	0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x53, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x5f, 0x44, 0x45, 0x50,
	0x4f, 0x53, 0x49, 0x54, 0x5f, 0x56, 0x32, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10,
	0x70, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f,
	0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x48, 0x4f, 0x52,
	0x49, 0x5a, 0x4f, 0x4e, 0x54, 0x41, 0x4c, 0x5f, 0x4c, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x10, 0x71,
	0x12, 0x15, 0x0a, 0x11, 0x4e, 0x45, 0x57, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x53, 0x5f, 0x56, 0x50, 0x41, 0x10, 0x72, 0x12, 0x27, 0x0a, 0x23, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x45, 0x4c, 0x49,
	0x47, 0x49, 0x42, 0x4c, 0x45, 0x5f, 0x46, 0x45, 0x45, 0x44, 0x42, 0x41, 0x43, 0x4b, 0x10, 0x73,
	0x12, 0x1d, 0x0a, 0x19, 0x46, 0x49, 0x52, 0x53, 0x54, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x5f, 0x50,
	0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x52, 0x4f, 0x4d, 0x50, 0x54, 0x10, 0x74, 0x12,
	0x27, 0x0a, 0x23, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45,
	0x44, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x75, 0x12, 0x21, 0x0a, 0x1d, 0x50, 0x48, 0x4f, 0x4e,
	0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x41, 0x53, 0x5f, 0x52, 0x45, 0x46, 0x45,
	0x52, 0x52, 0x41, 0x4c, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x76, 0x12, 0x1c, 0x0a, 0x18, 0x43,
	0x41, 0x52, 0x44, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x5f, 0x4e, 0x4f, 0x54, 0x49, 0x46,
	0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x77, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x4f, 0x52,
	0x54, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x49, 0x54,
	0x49, 0x45, 0x53, 0x5f, 0x4f, 0x4e, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41,
	0x54, 0x10, 0x78, 0x12, 0x16, 0x0a, 0x12, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x43, 0x53,
	0x41, 0x54, 0x5f, 0x53, 0x55, 0x52, 0x56, 0x45, 0x59, 0x10, 0x79, 0x12, 0x1f, 0x0a, 0x1b, 0x4d,
	0x46, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x41, 0x4c,
	0x43, 0x55, 0x4c, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x49, 0x10, 0x7a, 0x12, 0x13, 0x0a, 0x0f,
	0x4d, 0x46, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x47, 0x52, 0x41, 0x50, 0x48, 0x5f, 0x55, 0x49, 0x10,
	0x7b, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x45, 0x52,
	0x52, 0x4f, 0x52, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x56, 0x32, 0x10, 0x7c, 0x12, 0x11, 0x0a,
	0x0d, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x10, 0x7d,
	0x12, 0x2b, 0x0a, 0x27, 0x41, 0x41, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x46, 0x45, 0x54, 0x43,
	0x48, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4e,
	0x4f, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x7e, 0x12, 0x23, 0x0a,
	0x1f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x47, 0x49,
	0x53, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x5f, 0x56, 0x33,
	0x10, 0x7f, 0x12, 0x29, 0x0a, 0x24, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43,
	0x5f, 0x42, 0x45, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x4e, 0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e,
	0x45, 0x53, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x80, 0x01, 0x12, 0x18, 0x0a,
	0x13, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x41, 0x4d, 0x10, 0x81, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x55, 0x50, 0x43, 0x4f, 0x4d,
	0x49, 0x4e, 0x47, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53,
	0x5f, 0x56, 0x32, 0x10, 0x82, 0x01, 0x12, 0x29, 0x0a, 0x24, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x42, 0x45, 0x4e, 0x45,
	0x46, 0x49, 0x54, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0x83,
	0x01, 0x12, 0x23, 0x0a, 0x1e, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x49, 0x4e,
	0x44, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x5f, 0x57, 0x49, 0x44,
	0x47, 0x45, 0x54, 0x10, 0x84, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x41, 0x41, 0x5f, 0x45, 0x51, 0x55,
	0x49, 0x54, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x55, 0x4d,
	0x4d, 0x41, 0x52, 0x59, 0x10, 0x85, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x53, 0x10, 0x86, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x56, 0x50, 0x41, 0x5f, 0x4d, 0x49,
	0x47, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x54, 0x52, 0x4f, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0x87, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x4d, 0x46, 0x5f, 0x48, 0x4f,
	0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x56, 0x32,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x88, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x49, 0x4e, 0x48, 0x4f, 0x55, 0x53, 0x45, 0x5f, 0x42,
	0x52, 0x45, 0x10, 0x89, 0x01, 0x12, 0x27, 0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x44, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f,
	0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48, 0x10, 0x8a, 0x01, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x1a,
	0x0a, 0x15, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45,
	0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x8b, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x43, 0x58,
	0x5f, 0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x10, 0x8c, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x42, 0x45, 0x4e,
	0x45, 0x46, 0x49, 0x43, 0x49, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4f, 0x4c, 0x5f, 0x44, 0x4f,
	0x57, 0x4e, 0x5f, 0x52, 0x55, 0x4c, 0x45, 0x10, 0x8d, 0x01, 0x12, 0x26, 0x0a, 0x21, 0x41, 0x4c,
	0x4c, 0x4f, 0x57, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f, 0x4c, 0x5f,
	0x41, 0x41, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x50, 0x41, 0x50, 0x10,
	0x8e, 0x01, 0x12, 0x32, 0x0a, 0x2d, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50,
	0x4f, 0x52, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f,
	0x53, 0x41, 0x5f, 0x4f, 0x4e, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x44, 0x4f, 0x42, 0x5f, 0x53, 0x54,
	0x41, 0x47, 0x45, 0x10, 0x8f, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x4d, 0x41, 0x53, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x90, 0x01,
	0x12, 0x1a, 0x0a, 0x15, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x41, 0x49, 0x46, 0x10, 0x91, 0x01, 0x12, 0x24, 0x0a, 0x1f,
	0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52,
	0x4d, 0x5f, 0x41, 0x52, 0x54, 0x5f, 0x41, 0x52, 0x54, 0x45, 0x46, 0x41, 0x43, 0x54, 0x53, 0x10,
	0x92, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x42, 0x4f, 0x4e, 0x44, 0x53, 0x10, 0x93, 0x01,
	0x12, 0x1b, 0x0a, 0x16, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x43, 0x41, 0x53, 0x48, 0x10, 0x94, 0x01, 0x12, 0x23, 0x0a,
	0x1e, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x4f,
	0x52, 0x4d, 0x5f, 0x44, 0x49, 0x47, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x47, 0x4f, 0x4c, 0x44, 0x10,
	0x95, 0x01, 0x12, 0x25, 0x0a, 0x20, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x44, 0x49, 0x47, 0x49, 0x54, 0x41, 0x4c, 0x5f,
	0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0x96, 0x01, 0x12, 0x25, 0x0a, 0x20, 0x4d, 0x41, 0x4e,
	0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x50,
	0x52, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59, 0x10, 0x97, 0x01,
	0x12, 0x22, 0x0a, 0x1d, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54,
	0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x45, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x10, 0x98, 0x01, 0x12, 0x26, 0x0a, 0x21, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45,
	0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x43, 0x49, 0x41, 0x52, 0x59, 0x5f, 0x56, 0x49, 0x41,
	0x5f, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x99, 0x01, 0x12, 0x10, 0x0a, 0x0b,
	0x41, 0x55, 0x54, 0x4f, 0x50, 0x41, 0x59, 0x5f, 0x48, 0x55, 0x42, 0x10, 0x9a, 0x01, 0x12, 0x19,
	0x0a, 0x14, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x4f, 0x49, 0x43,
	0x45, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0x9b, 0x01, 0x12, 0x27, 0x0a, 0x22, 0x53, 0x50, 0x45,
	0x4e, 0x44, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45,
	0x53, 0x54, 0x5f, 0x4d, 0x4f, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x10,
	0x9c, 0x01, 0x12, 0x32, 0x0a, 0x2d, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x5f, 0x41, 0x4e, 0x41, 0x4c,
	0x59, 0x53, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x55, 0x52, 0x50,
	0x4c, 0x55, 0x53, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x49, 0x4e, 0x53, 0x49,
	0x47, 0x48, 0x54, 0x10, 0x9d, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x5f,
	0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x10, 0x9e, 0x01,
	0x12, 0x28, 0x0a, 0x23, 0x53, 0x50, 0x45, 0x4e, 0x44, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x45, 0x52, 0x5f, 0x53, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x4e, 0x44, 0x45, 0x52, 0x5f,
	0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x10, 0x9f, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x50, 0x41,
	0x59, 0x5f, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x49, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x5f,
	0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xa0, 0x01, 0x12, 0x3d, 0x0a, 0x38, 0x53, 0x55, 0x50,
	0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x43,
	0x49, 0x41, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4f, 0x4c, 0x5f, 0x44, 0x4f, 0x57, 0x4e, 0x5f, 0x49,
	0x4e, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e,
	0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0xa1, 0x01, 0x12, 0x22, 0x0a, 0x1d, 0x4f, 0x4e, 0x42, 0x5f,
	0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e,
	0x47, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0xa2, 0x01, 0x12, 0x39, 0x0a, 0x34,
	0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f,
	0x53, 0x45, 0x54, 0x54, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50,
	0x4f, 0x49, 0x4e, 0x54, 0x10, 0xa3, 0x01, 0x12, 0x26, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x54,
	0x50, 0x41, 0x50, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0xa4, 0x01, 0x12,
	0x1f, 0x0a, 0x1a, 0x41, 0x41, 0x5f, 0x50, 0x45, 0x52, 0x4d, 0x49, 0x54, 0x54, 0x45, 0x44, 0x5f,
	0x46, 0x49, 0x50, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x5f, 0x56, 0x32, 0x10, 0xa5, 0x01,
	0x12, 0x31, 0x0a, 0x2c, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x45, 0x4e,
	0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x41, 0x4d, 0x45, 0x57, 0x4f, 0x52, 0x4b,
	0x10, 0xa6, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53,
	0x5f, 0x56, 0x33, 0x10, 0xa7, 0x01, 0x12, 0x1e, 0x0a, 0x19, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x56,
	0x32, 0x5f, 0x32, 0x10, 0xa8, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49,
	0x54, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x5f, 0x43, 0x54, 0x41,
	0x10, 0xa9, 0x01, 0x12, 0x2c, 0x0a, 0x27, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45,
	0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x49, 0x4d, 0x50, 0x4c, 0x49, 0x46, 0x49, 0x5f, 0x43,
	0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x54, 0x4d, 0x10, 0xaa,
	0x01, 0x12, 0x15, 0x0a, 0x10, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x46, 0x45, 0x44, 0x45, 0x52,
	0x41, 0x4c, 0x5f, 0x56, 0x32, 0x10, 0xab, 0x01, 0x12, 0x11, 0x0a, 0x0c, 0x41, 0x44, 0x44, 0x5f,
	0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x56, 0x34, 0x10, 0xac, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x41,
	0x4c, 0x46, 0x52, 0x45, 0x44, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43,
	0x43, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0xad, 0x01,
	0x12, 0x1c, 0x0a, 0x17, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x45,
	0x4d, 0x49, 0x5f, 0x53, 0x49, 0x4d, 0x50, 0x4c, 0x49, 0x46, 0x49, 0x10, 0xae, 0x01, 0x12, 0x1b,
	0x0a, 0x16, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x45, 0x4d, 0x49,
	0x5f, 0x4d, 0x41, 0x47, 0x4e, 0x49, 0x46, 0x49, 0x10, 0xaf, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x56,
	0x4b, 0x59, 0x43, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x52, 0x45, 0x56, 0x49, 0x45, 0x57, 0x5f, 0x53,
	0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xb0, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x4c, 0x4f, 0x41, 0x4e,
	0x53, 0x5f, 0x43, 0x49, 0x42, 0x49, 0x4c, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46,
	0x45, 0x54, 0x43, 0x48, 0x10, 0xb1, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x49, 0x4e, 0x44, 0x49, 0x41,
	0x4e, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x5f, 0x45, 0x54, 0x46,
	0x10, 0xb2, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x4f, 0x4e, 0x45, 0x5f, 0x43, 0x4c, 0x49, 0x43, 0x4b,
	0x5f, 0x54, 0x50, 0x41, 0x50, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0xb3, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45, 0x53,
	0x53, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xb4, 0x01, 0x12, 0x1b,
	0x0a, 0x16, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x49, 0x4d, 0x41, 0x47, 0x45,
	0x5f, 0x43, 0x41, 0x50, 0x54, 0x55, 0x52, 0x45, 0x10, 0xb5, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x55, 0x41, 0x4e, 0x5f, 0x45, 0x50, 0x46, 0x5f, 0x46, 0x4c,
	0x4f, 0x57, 0x10, 0xb6, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x43, 0x43, 0x5f, 0x46, 0x49, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x43, 0x49, 0x42, 0x49, 0x4c,
	0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xb7, 0x01, 0x12,
	0x2f, 0x0a, 0x2a, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x45, 0x41, 0x52, 0x4e, 0x45,
	0x44, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x46, 0x52, 0x4f, 0x4d, 0x5f,
	0x50, 0x52, 0x4f, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x43, 0x48, 0x10, 0xb8, 0x01,
	0x12, 0x21, 0x0a, 0x1c, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x48, 0x4f, 0x4d, 0x45,
	0x10, 0xbb, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x54, 0x50, 0x41,
	0x50, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0xbc, 0x01,
	0x12, 0x1b, 0x0a, 0x16, 0x4e, 0x53, 0x44, 0x4c, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x49,
	0x5f, 0x56, 0x32, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43, 0x41, 0x10, 0xbd, 0x01, 0x12, 0x23, 0x0a,
	0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x50, 0x52, 0x45, 0x41,
	0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x41, 0x4d, 0x50, 0x4c, 0x49, 0x46, 0x49, 0x10,
	0xb9, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43,
	0x5f, 0x42, 0x52, 0x45, 0x5f, 0x41, 0x4d, 0x50, 0x4c, 0x49, 0x46, 0x49, 0x10, 0xba, 0x01, 0x12,
	0x24, 0x0a, 0x1f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x50, 0x52,
	0x45, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x4d, 0x50, 0x4c, 0x49,
	0x46, 0x49, 0x10, 0xbe, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x43, 0x43, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x53, 0x49, 0x4d, 0x50, 0x4c, 0x49, 0x46, 0x49,
	0x10, 0xbf, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43,
	0x43, 0x5f, 0x50, 0x52, 0x45, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4d, 0x41,
	0x47, 0x4e, 0x49, 0x46, 0x49, 0x10, 0xc0, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x42, 0x52, 0x45, 0x5f, 0x4d, 0x41, 0x47, 0x4e, 0x49,
	0x46, 0x49, 0x10, 0xc1, 0x01, 0x12, 0x1f, 0x0a, 0x1a, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f,
	0x46, 0x52, 0x41, 0x55, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43, 0x45,
	0x49, 0x50, 0x54, 0x10, 0xc2, 0x01, 0x12, 0x2c, 0x0a, 0x27, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53,
	0x5f, 0x49, 0x4e, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50,
	0x54, 0x10, 0xc3, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x4c, 0x41, 0x4d, 0x46, 0x5f, 0x50, 0x48, 0x41, 0x53, 0x45, 0x33, 0x5f, 0x31, 0x10, 0xc4, 0x01,
	0x12, 0x2a, 0x0a, 0x25, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e,
	0x53, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d, 0x45, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54,
	0x45, 0x5f, 0x56, 0x49, 0x41, 0x5f, 0x49, 0x54, 0x52, 0x10, 0xc5, 0x01, 0x12, 0x18, 0x0a, 0x13,
	0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x52, 0x45, 0x46, 0x52, 0x45, 0x53, 0x48,
	0x5f, 0x56, 0x32, 0x10, 0xc6, 0x01, 0x12, 0x30, 0x0a, 0x2b, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x56,
	0x32, 0x5f, 0x41, 0x41, 0x44, 0x48, 0x41, 0x41, 0x52, 0x5f, 0x4c, 0x49, 0x4e, 0x4b, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0xc7, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x4d, 0x53, 0x5f, 0x43, 0x4c, 0x41, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x53,
	0x44, 0x4b, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x44, 0x10, 0xc8, 0x01, 0x12, 0x28, 0x0a,
	0x23, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x44, 0x49, 0x53, 0x42, 0x55, 0x52, 0x53, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x53, 0x10, 0xc9, 0x01, 0x12, 0x34, 0x0a, 0x2f, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c,
	0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0xca, 0x01, 0x12, 0x33, 0x0a,
	0x2e, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x4f,
	0x52, 0x4d, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x4d, 0x41, 0x4e,
	0x41, 0x47, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10,
	0xcb, 0x01, 0x12, 0x2c, 0x0a, 0x27, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4c, 0x4f,
	0x41, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x41, 0x4c, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x45,
	0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0xcc, 0x01,
	0x12, 0x17, 0x0a, 0x12, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x49, 0x44, 0x46, 0x43, 0x5f, 0x56,
	0x4b, 0x59, 0x43, 0x5f, 0x56, 0x32, 0x10, 0xcd, 0x01, 0x12, 0x21, 0x0a, 0x1c, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x53, 0x53, 0x5f, 0x54, 0x41, 0x42, 0x42, 0x45, 0x44, 0x5f,
	0x43, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x54, 0x53, 0x50, 0x10, 0xce, 0x01, 0x12, 0x21, 0x0a, 0x1c,
	0x4e, 0x53, 0x44, 0x4c, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x56, 0x32,
	0x5f, 0x4d, 0x46, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0xcf, 0x01, 0x12,
	0x2c, 0x0a, 0x27, 0x4e, 0x53, 0x44, 0x4c, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x56, 0x32, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52,
	0x54, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0xd0, 0x01, 0x12, 0x27, 0x0a,
	0x22, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x41, 0x50, 0x50,
	0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0xd1, 0x01, 0x12, 0x2c, 0x0a, 0x27, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c,
	0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x50, 0x55, 0x42, 0x4c,
	0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e,
	0x44, 0x10, 0xd2, 0x01, 0x12, 0x2c, 0x0a, 0x27, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x5f, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59,
	0x45, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0xd3, 0x01, 0x12, 0x25, 0x0a, 0x20, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4c, 0x49, 0x51, 0x55,
	0x49, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x53, 0x41, 0x4c,
	0x41, 0x52, 0x59, 0x5f, 0x56, 0x32, 0x10, 0xd4, 0x01, 0x12, 0x18, 0x0a, 0x13, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x58, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x49, 0x56, 0x52,
	0x10, 0xd5, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43,
	0x43, 0x5f, 0x41, 0x55, 0x54, 0x4f, 0x4d, 0x41, 0x54, 0x49, 0x43, 0x5f, 0x46, 0x45, 0x45, 0x5f,
	0x57, 0x41, 0x49, 0x56, 0x45, 0x52, 0x10, 0xd6, 0x01, 0x12, 0x2c, 0x0a, 0x27, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x4c, 0x46, 0x52, 0x45, 0x44, 0x5f, 0x55, 0x53, 0x53, 0x5f,
	0x54, 0x41, 0x58, 0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51,
	0x55, 0x45, 0x53, 0x54, 0x10, 0xd7, 0x01, 0x12, 0x2e, 0x0a, 0x29, 0x52, 0x45, 0x50, 0x45, 0x41,
	0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4c, 0x49, 0x51, 0x55, 0x49, 0x4c, 0x4f, 0x41,
	0x4e, 0x53, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x5f,
	0x53, 0x54, 0x50, 0x4c, 0x10, 0xd8, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x56, 0x4b, 0x59, 0x43, 0x5f,
	0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x51, 0x55, 0x41, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0xd9, 0x01, 0x12, 0x37, 0x0a, 0x32, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43,
	0x41, 0x4c, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x53, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x50,
	0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xda, 0x01, 0x12,
	0x23, 0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x44, 0x43, 0x5f, 0x44, 0x41,
	0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x56, 0x32, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45,
	0x4e, 0x10, 0xdb, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x4c, 0x41,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4d,
	0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x10, 0xdc, 0x01, 0x12, 0x1f,
	0x0a, 0x1a, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x4d, 0x50, 0x4c, 0x49, 0x46,
	0x49, 0x5f, 0x43, 0x43, 0x5f, 0x4d, 0x49, 0x4e, 0x55, 0x54, 0x45, 0x53, 0x10, 0xdd, 0x01, 0x12,
	0x26, 0x0a, 0x21, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x48, 0x55, 0x42, 0x5f,
	0x46, 0x49, 0x5f, 0x54, 0x4f, 0x5f, 0x46, 0x49, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0xde, 0x01, 0x12, 0x2f, 0x0a, 0x2a, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x41, 0x53,
	0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0xdf, 0x01, 0x12, 0x34, 0x0a, 0x2f, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x44, 0x41,
	0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x53, 0x5f, 0x55, 0x4e, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0xe0, 0x01, 0x12, 0x2d,
	0x0a, 0x28, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0xe1, 0x01, 0x12, 0x25, 0x0a,
	0x20, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x41, 0x50, 0x50,
	0x5f, 0x55, 0x50, 0x49, 0x5f, 0x56, 0x49, 0x41, 0x5f, 0x54, 0x45, 0x4d, 0x50, 0x4f, 0x52, 0x41,
	0x4c, 0x10, 0xe2, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x49, 0x45,
	0x53, 0x5f, 0x52, 0x45, 0x49, 0x54, 0x10, 0xe3, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53, 0x45, 0x43, 0x55,
	0x52, 0x49, 0x54, 0x49, 0x45, 0x53, 0x5f, 0x49, 0x4e, 0x56, 0x49, 0x54, 0x10, 0xe4, 0x01, 0x12,
	0x2a, 0x0a, 0x25, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46,
	0x55, 0x4e, 0x44, 0x53, 0x5f, 0x55, 0x53, 0x53, 0x5f, 0x44, 0x4f, 0x55, 0x42, 0x4c, 0x45, 0x5f,
	0x50, 0x49, 0x4e, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0xe5, 0x01, 0x12, 0x1a, 0x0a, 0x15, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x4e, 0x50, 0x53, 0x10, 0xe6, 0x01, 0x12, 0x1d, 0x0a, 0x18, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x53, 0x5f, 0x57, 0x49, 0x44,
	0x47, 0x45, 0x54, 0x10, 0xe7, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f,
	0x49, 0x4e, 0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x4e, 0x10, 0xe8, 0x01,
	0x12, 0x25, 0x0a, 0x20, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x45,
	0x59, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x43,
	0x52, 0x45, 0x45, 0x4e, 0x10, 0xe9, 0x01, 0x12, 0x38, 0x0a, 0x33, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f,
	0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f,
	0x55, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0xea,
	0x01, 0x12, 0x28, 0x0a, 0x23, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x5f,
	0x41, 0x50, 0x50, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0xeb, 0x01, 0x12, 0x20, 0x0a, 0x1b, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50, 0x52, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x5f, 0x56, 0x49, 0x41, 0x5f, 0x50, 0x47, 0x10, 0xec, 0x01, 0x12, 0x1e, 0x0a,
	0x19, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x41, 0x5f, 0x42, 0x41, 0x4e, 0x4b,
	0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xed, 0x01, 0x12, 0x1c, 0x0a,
	0x17, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x53, 0x49,
	0x4e, 0x47, 0x4c, 0x45, 0x5f, 0x52, 0x50, 0x43, 0x10, 0xee, 0x01, 0x12, 0x2f, 0x0a, 0x2a, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x44,
	0x43, 0x5f, 0x44, 0x45, 0x4c, 0x49, 0x56, 0x45, 0x52, 0x59, 0x5f, 0x41, 0x44, 0x44, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0xef, 0x01, 0x12, 0x25, 0x0a, 0x20,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x43,
	0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32,
	0x10, 0xf0, 0x01, 0x12, 0x24, 0x0a, 0x1f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43,
	0x58, 0x5f, 0x48, 0x45, 0x4c, 0x50, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x4e, 0x54, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x49, 0x54, 0x59, 0x10, 0xf1, 0x01, 0x12, 0x27, 0x0a, 0x22, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54,
	0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10,
	0xf2, 0x01, 0x12, 0x28, 0x0a, 0x23, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x4d,
	0x53, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41,
	0x49, 0x46, 0x5f, 0x53, 0x45, 0x41, 0x52, 0x43, 0x48, 0x10, 0xf3, 0x01, 0x12, 0x2d, 0x0a, 0x28,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4e, 0x4f,
	0x4e, 0x5f, 0x46, 0x49, 0x5f, 0x43, 0x4f, 0x52, 0x45, 0x5f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42,
	0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x56, 0x32, 0x10, 0xf4, 0x01, 0x12, 0x33, 0x0a, 0x2e, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x44, 0x43, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x44, 0x52, 0x41, 0x57, 0x41, 0x4c, 0x5f, 0x4c, 0x41, 0x59, 0x4f, 0x55, 0x54, 0x10, 0xf5, 0x01,
	0x12, 0x1a, 0x0a, 0x15, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x53, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x53, 0x49, 0x50, 0x10, 0xf6, 0x01, 0x12, 0x34, 0x0a, 0x2f,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x10,
	0xf7, 0x01, 0x12, 0x26, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4e,
	0x41, 0x42, 0x4c, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x56, 0x31, 0x10, 0xf8, 0x01, 0x12, 0x30, 0x0a, 0x2b, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x5f, 0x49, 0x53, 0x53,
	0x55, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x4e, 0x4f, 0x4e,
	0x5f, 0x46, 0x43, 0x52, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0xf9, 0x01, 0x12, 0x39, 0x0a, 0x34,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c, 0x5f, 0x43, 0x41,
	0x52, 0x44, 0x5f, 0x44, 0x55, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0xfa, 0x01, 0x12, 0x1b, 0x0a, 0x16, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x44, 0x43, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x4d, 0x4f, 0x44,
	0x45, 0x10, 0xfb, 0x01, 0x12, 0x30, 0x0a, 0x2b, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52,
	0x49, 0x45, 0x53, 0x10, 0xfc, 0x01, 0x12, 0x23, 0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54, 0x10, 0xfd, 0x01, 0x12, 0x31, 0x0a, 0x2c, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x54,
	0x4f, 0x4d, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x42, 0x41, 0x52, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f,
	0x52, 0x54, 0x48, 0x5f, 0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xfe, 0x01, 0x12, 0x1a,
	0x0a, 0x15, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x53, 0x45,
	0x41, 0x52, 0x43, 0x48, 0x5f, 0x56, 0x32, 0x10, 0xff, 0x01, 0x12, 0x1c, 0x0a, 0x17, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x58, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x42, 0x4c,
	0x4f, 0x43, 0x4b, 0x45, 0x52, 0x10, 0x80, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x55, 0x4c, 0x41, 0x52, 0x5f, 0x54, 0x49, 0x45, 0x52,
	0x10, 0x81, 0x02, 0x12, 0x25, 0x0a, 0x20, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45,
	0x50, 0x46, 0x5f, 0x47, 0x45, 0x4e, 0x45, 0x52, 0x49, 0x43, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x82, 0x02, 0x12, 0x28, 0x0a, 0x23, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x4c, 0x46, 0x52, 0x45, 0x44, 0x5f, 0x55, 0x53, 0x53,
	0x5f, 0x44, 0x4f, 0x43, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x10, 0x83, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x44, 0x43, 0x5f, 0x54, 0x4f, 0x47, 0x47, 0x4c, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c,
	0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x10, 0x84, 0x02, 0x12, 0x31, 0x0a, 0x2c, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x4f, 0x43, 0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54, 0x10, 0x87, 0x02, 0x12, 0x34, 0x0a, 0x2f, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x41,
	0x50, 0x50, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x43,
	0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x85,
	0x02, 0x12, 0x22, 0x0a, 0x1d, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x53, 0x5f,
	0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x10, 0x86, 0x02, 0x12, 0x23, 0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x53, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x52, 0x54,
	0x4e, 0x45, 0x52, 0x5f, 0x53, 0x44, 0x4b, 0x10, 0x88, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x53, 0x10, 0x89, 0x02, 0x12, 0x1c, 0x0a, 0x17, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x56, 0x32,
	0x10, 0x8a, 0x02, 0x12, 0x31, 0x0a, 0x2c, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x52, 0x41, 0x55, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x44,
	0x49, 0x53, 0x50, 0x55, 0x54, 0x45, 0x5f, 0x42, 0x4f, 0x54, 0x54, 0x4f, 0x4d, 0x5f, 0x53, 0x48,
	0x45, 0x45, 0x54, 0x10, 0x8b, 0x02, 0x12, 0x2d, 0x0a, 0x28, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x4e,
	0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x5f, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x48, 0x4f,
	0x4d, 0x45, 0x10, 0x8c, 0x02, 0x12, 0x2f, 0x0a, 0x2a, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x49, 0x53, 0x53, 0x55, 0x45, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x49, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x8d, 0x02, 0x12, 0x1f, 0x0a, 0x1a, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x41, 0x4c, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x5f,
	0x49, 0x43, 0x4f, 0x4e, 0x10, 0x8e, 0x02, 0x12, 0x2c, 0x0a, 0x27, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x53, 0x5f, 0x53, 0x4b, 0x49, 0x50, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x44,
	0x4f, 0x42, 0x10, 0x8f, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x43, 0x58, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x5f, 0x49, 0x56,
	0x52, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x90, 0x02, 0x12, 0x15, 0x0a, 0x10, 0x50, 0x52, 0x49,
	0x4d, 0x45, 0x5f, 0x53, 0x4d, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x53, 0x45, 0x52, 0x10, 0x91, 0x02,
	0x12, 0x2f, 0x0a, 0x2a, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x57, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x43,
	0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x10, 0x92,
	0x02, 0x12, 0x33, 0x0a, 0x2e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4c, 0x45, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x50, 0x41, 0x50, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x10, 0x93, 0x02, 0x12, 0x26, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x50, 0x4f, 0x53, 0x54, 0x5f, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x94, 0x02, 0x12, 0x2b,
	0x0a, 0x26, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43,
	0x54, 0x45, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x95, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x5f, 0x54, 0x52, 0x41, 0x4e,
	0x53, 0x46, 0x45, 0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0x96, 0x02, 0x12, 0x29,
	0x0a, 0x24, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f,
	0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x50, 0x45, 0x45, 0x52, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x41, 0x52, 0x49, 0x53, 0x4f, 0x4e, 0x10, 0x97, 0x02, 0x12, 0x2c, 0x0a, 0x27, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x56, 0x50, 0x41, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52, 0x45,
	0x4e, 0x43, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x98, 0x02, 0x12, 0x51, 0x0a, 0x4c, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f,
	0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x4c, 0x4c,
	0x5f, 0x4f, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x99, 0x02, 0x12, 0x56, 0x0a, 0x51, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45,
	0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x52, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54,
	0x45, 0x44, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43,
	0x41, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10,
	0x9a, 0x02, 0x12, 0x4a, 0x0a, 0x45, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41,
	0x59, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47,
	0x45, 0x52, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x41, 0x54, 0x43, 0x48,
	0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x4f, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0x9b, 0x02, 0x12, 0x25,
	0x0a, 0x20, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x41, 0x44,
	0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x9c, 0x02, 0x12, 0x27, 0x0a, 0x22, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x4d,
	0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x10, 0x9d, 0x02, 0x12, 0x36,
	0x0a, 0x31, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55,
	0x4e, 0x44, 0x53, 0x10, 0x9e, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x44, 0x43, 0x5f, 0x54, 0x52, 0x41, 0x56, 0x45, 0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x45,
	0x5f, 0x4c, 0x4f, 0x54, 0x54, 0x49, 0x45, 0x10, 0x9f, 0x02, 0x12, 0x22, 0x0a, 0x1d, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x53, 0x54, 0x52, 0x49, 0x43, 0x54, 0x5f, 0x4c,
	0x49, 0x53, 0x54, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0xa0, 0x02, 0x12, 0x30,
	0x0a, 0x2b, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x4e, 0x52, 0x49, 0x43, 0x48,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x46,
	0x52, 0x4f, 0x4d, 0x5f, 0x44, 0x43, 0x5f, 0x53, 0x57, 0x49, 0x54, 0x43, 0x48, 0x10, 0xa1, 0x02,
	0x12, 0x22, 0x0a, 0x1d, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x54, 0x49, 0x45, 0x52,
	0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x50, 0x4c, 0x45, 0x5f, 0x57, 0x41, 0x59,
	0x53, 0x10, 0xa2, 0x02, 0x12, 0x18, 0x0a, 0x13, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x44, 0x43, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x53, 0x10, 0xa3, 0x02, 0x12, 0x20,
	0x0a, 0x1b, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x46, 0x46, 0x5f, 0x41, 0x50,
	0x50, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x50, 0x52, 0x45, 0x45, 0x4d, 0x50, 0x54, 0x10, 0xa4, 0x02,
	0x12, 0x23, 0x0a, 0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x4f, 0x4e, 0x45,
	0x59, 0x5f, 0x53, 0x45, 0x43, 0x52, 0x45, 0x54, 0x5f, 0x46, 0x4f, 0x4f, 0x54, 0x45, 0x52, 0x5f,
	0x56, 0x32, 0x10, 0xa5, 0x02, 0x12, 0x3b, 0x0a, 0x36, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e, 0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41,
	0x4e, 0x41, 0x47, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x5f, 0x43, 0x52, 0x45,
	0x44, 0x49, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0xa6, 0x02, 0x12, 0x3b, 0x0a, 0x36, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x50, 0x54, 0x5f, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x49, 0x43, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52, 0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x54, 0x49, 0x4c, 0x45, 0x10, 0xa7, 0x02, 0x12,
	0x2c, 0x0a, 0x27, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x58, 0x5f, 0x54, 0x49,
	0x43, 0x4b, 0x45, 0x54, 0x5f, 0x52, 0x45, 0x53, 0x4f, 0x4c, 0x55, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x43, 0x53, 0x41, 0x54, 0x5f, 0x43, 0x4f, 0x4d, 0x4d, 0x53, 0x10, 0xa8, 0x02, 0x12, 0x33, 0x0a,
	0x2e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x44, 0x43, 0x5f, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x57, 0x41, 0x5f, 0x41, 0x44, 0x44, 0x52,
	0x45, 0x53, 0x53, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10,
	0xa9, 0x02, 0x12, 0x19, 0x0a, 0x14, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x56, 0x4b,
	0x59, 0x43, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x56, 0x32, 0x10, 0xaa, 0x02, 0x12, 0x23, 0x0a,
	0x1e, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x42, 0x45,
	0x4e, 0x45, 0x46, 0x49, 0x54, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10,
	0xab, 0x02, 0x12, 0x30, 0x0a, 0x2b, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x48,
	0x4f, 0x57, 0x5f, 0x53, 0x54, 0x41, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x55, 0x54, 0x45,
	0x44, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e,
	0x47, 0x10, 0xac, 0x02, 0x12, 0x1e, 0x0a, 0x19, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x46, 0x4c, 0x4f,
	0x57, 0x10, 0xad, 0x02, 0x12, 0x30, 0x0a, 0x2b, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f,
	0x52, 0x54, 0x5f, 0x50, 0x41, 0x4e, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x10, 0xae, 0x02, 0x12, 0x30, 0x0a, 0x2b, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x57, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xaf, 0x02, 0x12, 0x30, 0x0a, 0x2b, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x4e, 0x41, 0x56, 0x5f, 0x42, 0x41, 0x52,
	0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x45, 0x52, 0x5f,
	0x53, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0xb0, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x44, 0x41, 0x53, 0x48,
	0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0xb1, 0x02, 0x12, 0x24, 0x0a,
	0x1f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x43,
	0x49, 0x41, 0x52, 0x59, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x4f, 0x4b, 0x55, 0x50,
	0x10, 0xb2, 0x02, 0x12, 0x1b, 0x0a, 0x16, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x44,
	0x43, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x53, 0x5f, 0x41, 0x50, 0x49, 0x10, 0xb3, 0x02,
	0x12, 0x2b, 0x0a, 0x26, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x4d, 0x49, 0x4e, 0x45, 0x45, 0x10, 0xb4, 0x02, 0x12, 0x2c, 0x0a,
	0x27, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x49, 0x4e, 0x5f, 0x42, 0x41, 0x4c,
	0x41, 0x4e, 0x43, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x5f, 0x53, 0x44, 0x55, 0x49, 0x10, 0xb5, 0x02, 0x12, 0x2a, 0x0a, 0x25, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4d, 0x46, 0x5f, 0x48, 0x4f, 0x4c, 0x44, 0x49, 0x4e,
	0x47, 0x53, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x44, 0x41, 0x52, 0x4b, 0x5f, 0x54,
	0x48, 0x45, 0x4d, 0x45, 0x10, 0xb6, 0x02, 0x12, 0x33, 0x0a, 0x2e, 0x46, 0x45, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44,
	0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x49, 0x45,
	0x52, 0x5f, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0xb7, 0x02, 0x12, 0x26, 0x0a, 0x21,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x54, 0x48, 0x45, 0x41, 0x44,
	0x53, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4d, 0x49, 0x53, 0x45, 0x44, 0x5f, 0x51, 0x55, 0x45, 0x52,
	0x59, 0x10, 0xb8, 0x02, 0x12, 0x28, 0x0a, 0x23, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x54, 0x5f, 0x45, 0x41, 0x52, 0x4e,
	0x45, 0x44, 0x5f, 0x42, 0x45, 0x4e, 0x45, 0x46, 0x49, 0x54, 0x53, 0x10, 0xb9, 0x02, 0x12, 0x46,
	0x0a, 0x41, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x49, 0x4e,
	0x43, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47, 0x45, 0x52, 0x5f, 0x4f,
	0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53,
	0x5f, 0x53, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x5f, 0x4c, 0x45, 0x47, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x10, 0xba, 0x02, 0x12, 0x25, 0x0a, 0x20, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x48, 0x4f, 0x4d, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x45, 0x4e,
	0x48, 0x41, 0x4e, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0xbb, 0x02, 0x12, 0x29, 0x0a,
	0x24, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f,
	0x42, 0x55, 0x49, 0x4c, 0x44, 0x45, 0x52, 0x5f, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x54, 0x48,
	0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0xbc, 0x02, 0x12, 0x21, 0x0a, 0x1c, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x51, 0x52, 0x5f, 0x53, 0x43, 0x41, 0x4e, 0x5f, 0x45, 0x4e, 0x48, 0x41,
	0x4e, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0xbd, 0x02, 0x12, 0x29, 0x0a, 0x24, 0x46,
	0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x53, 0x4d, 0x53, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x42, 0x55, 0x49, 0x4c,
	0x44, 0x45, 0x52, 0x10, 0xbe, 0x02, 0x12, 0x2e, 0x0a, 0x29, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x53, 0x10, 0xbf, 0x02, 0x12, 0x35, 0x0a, 0x30, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x51, 0x55, 0x49, 0x43, 0x4b, 0x5f, 0x4c, 0x49, 0x4e, 0x4b,
	0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x4c, 0x41, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc0, 0x02, 0x12, 0x2d, 0x0a,
	0x28, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c,
	0x49, 0x4f, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49,
	0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x10, 0xc1, 0x02, 0x12, 0x21, 0x0a, 0x1c,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x53, 0x45,
	0x43, 0x4f, 0x4e, 0x44, 0x5f, 0x4c, 0x4f, 0x4f, 0x4b, 0x5f, 0x56, 0x31, 0x10, 0xc2, 0x02, 0x12,
	0x25, 0x0a, 0x20, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53,
	0x5f, 0x50, 0x52, 0x45, 0x51, 0x55, 0x41, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x10, 0xc3, 0x02, 0x12, 0x26, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x53, 0x48, 0x4f, 0x55, 0x4c, 0x44, 0x5f, 0x53, 0x48, 0x4f, 0x57, 0x5f, 0x50, 0x49,
	0x4e, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f, 0x56, 0x32, 0x10, 0xc4, 0x02, 0x12, 0x24,
	0x0a, 0x1f, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x44, 0x45,
	0x53, 0x49, 0x47, 0x4e, 0x5f, 0x45, 0x4e, 0x48, 0x41, 0x4e, 0x43, 0x45, 0x4d, 0x45, 0x4e, 0x54,
	0x53, 0x10, 0xc5, 0x02, 0x12, 0x26, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x54, 0x52, 0x41,
	0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0xc6, 0x02, 0x12, 0x22, 0x0a, 0x1d,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x41, 0x4d, 0x42, 0x5f, 0x45, 0x4e, 0x54, 0x52,
	0x59, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x10, 0xc7, 0x02,
	0x12, 0x2e, 0x0a, 0x29, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x41, 0x5f, 0x46,
	0x4c, 0x4f, 0x57, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44,
	0x45, 0x52, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x10, 0xc8, 0x02,
	0x12, 0x38, 0x0a, 0x33, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x44, 0x41, 0x49, 0x4c,
	0x59, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x5f, 0x4f, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x10, 0xc9, 0x02, 0x12, 0x2a, 0x0a, 0x25, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x46, 0x52, 0x45, 0x53, 0x48, 0x43, 0x48, 0x41, 0x54, 0x5f,
	0x43, 0x48, 0x41, 0x54, 0x42, 0x4f, 0x54, 0x5f, 0x53, 0x44, 0x4b, 0x5f, 0x45, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x44, 0x10, 0xca, 0x02, 0x12, 0x20, 0x0a, 0x1b, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52,
	0x45, 0x5f, 0x43, 0x58, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0xcb, 0x02, 0x12, 0x28, 0x0a, 0x23, 0x46, 0x45, 0x41, 0x54,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x53, 0x5f, 0x43, 0x41, 0x54, 0x41,
	0x4c, 0x4f, 0x47, 0x5f, 0x4d, 0x45, 0x52, 0x47, 0x45, 0x44, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10,
	0xcc, 0x02, 0x12, 0x31, 0x0a, 0x2c, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x4e, 0x45,
	0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53,
	0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x44, 0x49, 0x53, 0x54, 0x52, 0x49, 0x42, 0x55, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0xcd, 0x02, 0x12, 0x25, 0x0a, 0x20, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45,
	0x5f, 0x57, 0x42, 0x5f, 0x44, 0x41, 0x53, 0x48, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x4c, 0x49,
	0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0xce, 0x02, 0x12, 0x1c, 0x0a, 0x17,
	0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x57, 0x42, 0x5f, 0x4d, 0x41, 0x47, 0x49, 0x43,
	0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x10, 0xcf, 0x02, 0x12, 0x25, 0x0a, 0x20, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x57, 0x45, 0x45, 0x4b, 0x4c, 0x59, 0x5f, 0x50, 0x4f, 0x52,
	0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x45, 0x52, 0x10, 0xd0,
	0x02, 0x12, 0x2c, 0x0a, 0x27, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x53, 0x5f,
	0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x5f,
	0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x41, 0x47, 0x45, 0x10, 0xd1, 0x02, 0x12,
	0x2c, 0x0a, 0x27, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x45,
	0x4e, 0x51, 0x55, 0x49, 0x52, 0x59, 0x5f, 0x52, 0x45, 0x44, 0x49, 0x53, 0x5f, 0x42, 0x41, 0x53,
	0x45, 0x44, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x45, 0x52, 0x10, 0xd2, 0x02, 0x12, 0x32, 0x0a,
	0x2d, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x4c, 0x41, 0x4e,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x52, 0x55, 0x50, 0x41,
	0x59, 0x5f, 0x43, 0x43, 0x5f, 0x42, 0x45, 0x5f, 0x44, 0x52, 0x49, 0x56, 0x45, 0x4e, 0x10, 0xd3,
	0x02, 0x12, 0x37, 0x0a, 0x32, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x50, 0x41, 0x59,
	0x5f, 0x4c, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x5f,
	0x42, 0x41, 0x4e, 0x4e, 0x45, 0x52, 0x5f, 0x44, 0x59, 0x4e, 0x41, 0x4d, 0x49, 0x43, 0x5f, 0x45,
	0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x53, 0x10, 0xd4, 0x02, 0x12, 0x1d, 0x0a, 0x18, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x46, 0x49, 0x5f, 0x4d, 0x43, 0x50, 0x5f, 0x54, 0x4f, 0x54,
	0x50, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0xd5, 0x02, 0x12, 0x20, 0x0a, 0x1b, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44,
	0x5f, 0x56, 0x32, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0xd6, 0x02, 0x42, 0x48, 0x0a, 0x22, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_feature_proto_rawDescOnce sync.Once
	file_api_typesv2_feature_proto_rawDescData = file_api_typesv2_feature_proto_rawDesc
)

func file_api_typesv2_feature_proto_rawDescGZIP() []byte {
	file_api_typesv2_feature_proto_rawDescOnce.Do(func() {
		file_api_typesv2_feature_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_feature_proto_rawDescData)
	})
	return file_api_typesv2_feature_proto_rawDescData
}

var file_api_typesv2_feature_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_feature_proto_goTypes = []interface{}{
	(Feature)(0), // 0: api.typesv2.Feature
}
var file_api_typesv2_feature_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_typesv2_feature_proto_init() }
func file_api_typesv2_feature_proto_init() {
	if File_api_typesv2_feature_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_feature_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_feature_proto_goTypes,
		DependencyIndexes: file_api_typesv2_feature_proto_depIdxs,
		EnumInfos:         file_api_typesv2_feature_proto_enumTypes,
	}.Build()
	File_api_typesv2_feature_proto = out.File
	file_api_typesv2_feature_proto_rawDesc = nil
	file_api_typesv2_feature_proto_goTypes = nil
	file_api_typesv2_feature_proto_depIdxs = nil
}
