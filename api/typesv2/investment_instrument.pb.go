//go:generate gen_sql -typesv2=InvestmentInstrumentType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/typesv2/investment_instrument.proto

package typesv2

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// An investment instrument is an asset/tool wherein money can be put in to achieve the financial goal.
type InvestmentInstrumentType int32

const (
	InvestmentInstrumentType_INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED InvestmentInstrumentType = 0
	InvestmentInstrumentType_MUTUAL_FUNDS                           InvestmentInstrumentType = 1
	InvestmentInstrumentType_P2P_JUMP                               InvestmentInstrumentType = 2
	InvestmentInstrumentType_SMART_DEPOSIT                          InvestmentInstrumentType = 3
	InvestmentInstrumentType_FIXED_DEPOSIT                          InvestmentInstrumentType = 4
	InvestmentInstrumentType_US_STOCKS                              InvestmentInstrumentType = 5
	// Recurring Deposit (RD) is a term deposit that allows to make regular deposits & earn returns on the investment.
	InvestmentInstrumentType_RECURRING_DEPOSIT InvestmentInstrumentType = 6
	InvestmentInstrumentType_INDIAN_STOCKS     InvestmentInstrumentType = 7
	InvestmentInstrumentType_REAL_ESTATE       InvestmentInstrumentType = 8
	// alternate investment fund
	InvestmentInstrumentType_AIF                          InvestmentInstrumentType = 9
	InvestmentInstrumentType_PRIVATE_EQUITY               InvestmentInstrumentType = 10
	InvestmentInstrumentType_DIGITAL_GOLD                 InvestmentInstrumentType = 11
	InvestmentInstrumentType_CASH                         InvestmentInstrumentType = 12
	InvestmentInstrumentType_DIGITAL_SILVER               InvestmentInstrumentType = 13
	InvestmentInstrumentType_BOND                         InvestmentInstrumentType = 14
	InvestmentInstrumentType_ART_AND_ARTEFACTS            InvestmentInstrumentType = 15
	InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE InvestmentInstrumentType = 16
	InvestmentInstrumentType_PUBLIC_PROVIDENT_FUND        InvestmentInstrumentType = 17
	InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION        InvestmentInstrumentType = 18
	// National Pension Scheme
	InvestmentInstrumentType_NPS     InvestmentInstrumentType = 19
	InvestmentInstrumentType_GADGETS InvestmentInstrumentType = 20
)

// Enum value maps for InvestmentInstrumentType.
var (
	InvestmentInstrumentType_name = map[int32]string{
		0:  "INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED",
		1:  "MUTUAL_FUNDS",
		2:  "P2P_JUMP",
		3:  "SMART_DEPOSIT",
		4:  "FIXED_DEPOSIT",
		5:  "US_STOCKS",
		6:  "RECURRING_DEPOSIT",
		7:  "INDIAN_STOCKS",
		8:  "REAL_ESTATE",
		9:  "AIF",
		10: "PRIVATE_EQUITY",
		11: "DIGITAL_GOLD",
		12: "CASH",
		13: "DIGITAL_SILVER",
		14: "BOND",
		15: "ART_AND_ARTEFACTS",
		16: "PORTFOLIO_MANAGEMENT_SERVICE",
		17: "PUBLIC_PROVIDENT_FUND",
		18: "EMPLOYEE_STOCK_OPTION",
		19: "NPS",
		20: "GADGETS",
	}
	InvestmentInstrumentType_value = map[string]int32{
		"INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED": 0,
		"MUTUAL_FUNDS":                           1,
		"P2P_JUMP":                               2,
		"SMART_DEPOSIT":                          3,
		"FIXED_DEPOSIT":                          4,
		"US_STOCKS":                              5,
		"RECURRING_DEPOSIT":                      6,
		"INDIAN_STOCKS":                          7,
		"REAL_ESTATE":                            8,
		"AIF":                                    9,
		"PRIVATE_EQUITY":                         10,
		"DIGITAL_GOLD":                           11,
		"CASH":                                   12,
		"DIGITAL_SILVER":                         13,
		"BOND":                                   14,
		"ART_AND_ARTEFACTS":                      15,
		"PORTFOLIO_MANAGEMENT_SERVICE":           16,
		"PUBLIC_PROVIDENT_FUND":                  17,
		"EMPLOYEE_STOCK_OPTION":                  18,
		"NPS":                                    19,
		"GADGETS":                                20,
	}
)

func (x InvestmentInstrumentType) Enum() *InvestmentInstrumentType {
	p := new(InvestmentInstrumentType)
	*p = x
	return p
}

func (x InvestmentInstrumentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InvestmentInstrumentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_typesv2_investment_instrument_proto_enumTypes[0].Descriptor()
}

func (InvestmentInstrumentType) Type() protoreflect.EnumType {
	return &file_api_typesv2_investment_instrument_proto_enumTypes[0]
}

func (x InvestmentInstrumentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InvestmentInstrumentType.Descriptor instead.
func (InvestmentInstrumentType) EnumDescriptor() ([]byte, []int) {
	return file_api_typesv2_investment_instrument_proto_rawDescGZIP(), []int{0}
}

var File_api_typesv2_investment_instrument_proto protoreflect.FileDescriptor

var file_api_typesv2_investment_instrument_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x69, 0x6e,
	0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2a, 0xb2, 0x03, 0x0a, 0x18, 0x49, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x26, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x10, 0x0a, 0x0c, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x10,
	0x01, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x32, 0x50, 0x5f, 0x4a, 0x55, 0x4d, 0x50, 0x10, 0x02, 0x12,
	0x11, 0x0a, 0x0d, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54,
	0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x10, 0x04, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x53, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e,
	0x47, 0x5f, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x49,
	0x4e, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x07, 0x12, 0x0f,
	0x0a, 0x0b, 0x52, 0x45, 0x41, 0x4c, 0x5f, 0x45, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x08, 0x12,
	0x07, 0x0a, 0x03, 0x41, 0x49, 0x46, 0x10, 0x09, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x52, 0x49, 0x56,
	0x41, 0x54, 0x45, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59, 0x10, 0x0a, 0x12, 0x10, 0x0a, 0x0c,
	0x44, 0x49, 0x47, 0x49, 0x54, 0x41, 0x4c, 0x5f, 0x47, 0x4f, 0x4c, 0x44, 0x10, 0x0b, 0x12, 0x08,
	0x0a, 0x04, 0x43, 0x41, 0x53, 0x48, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x49, 0x47, 0x49,
	0x54, 0x41, 0x4c, 0x5f, 0x53, 0x49, 0x4c, 0x56, 0x45, 0x52, 0x10, 0x0d, 0x12, 0x08, 0x0a, 0x04,
	0x42, 0x4f, 0x4e, 0x44, 0x10, 0x0e, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x52, 0x54, 0x5f, 0x41, 0x4e,
	0x44, 0x5f, 0x41, 0x52, 0x54, 0x45, 0x46, 0x41, 0x43, 0x54, 0x53, 0x10, 0x0f, 0x12, 0x20, 0x0a,
	0x1c, 0x50, 0x4f, 0x52, 0x54, 0x46, 0x4f, 0x4c, 0x49, 0x4f, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47,
	0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x10, 0x12,
	0x19, 0x0a, 0x15, 0x50, 0x55, 0x42, 0x4c, 0x49, 0x43, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x44,
	0x45, 0x4e, 0x54, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x11, 0x12, 0x19, 0x0a, 0x15, 0x45, 0x4d,
	0x50, 0x4c, 0x4f, 0x59, 0x45, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x5f, 0x4f, 0x50, 0x54,
	0x49, 0x4f, 0x4e, 0x10, 0x12, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x50, 0x53, 0x10, 0x13, 0x12, 0x0b,
	0x0a, 0x07, 0x47, 0x41, 0x44, 0x47, 0x45, 0x54, 0x53, 0x10, 0x14, 0x42, 0x48, 0x0a, 0x22, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_typesv2_investment_instrument_proto_rawDescOnce sync.Once
	file_api_typesv2_investment_instrument_proto_rawDescData = file_api_typesv2_investment_instrument_proto_rawDesc
)

func file_api_typesv2_investment_instrument_proto_rawDescGZIP() []byte {
	file_api_typesv2_investment_instrument_proto_rawDescOnce.Do(func() {
		file_api_typesv2_investment_instrument_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_typesv2_investment_instrument_proto_rawDescData)
	})
	return file_api_typesv2_investment_instrument_proto_rawDescData
}

var file_api_typesv2_investment_instrument_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_typesv2_investment_instrument_proto_goTypes = []interface{}{
	(InvestmentInstrumentType)(0), // 0: api.typesv2.InvestmentInstrumentType
}
var file_api_typesv2_investment_instrument_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_typesv2_investment_instrument_proto_init() }
func file_api_typesv2_investment_instrument_proto_init() {
	if File_api_typesv2_investment_instrument_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_typesv2_investment_instrument_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_typesv2_investment_instrument_proto_goTypes,
		DependencyIndexes: file_api_typesv2_investment_instrument_proto_depIdxs,
		EnumInfos:         file_api_typesv2_investment_instrument_proto_enumTypes,
	}.Build()
	File_api_typesv2_investment_instrument_proto = out.File
	file_api_typesv2_investment_instrument_proto_rawDesc = nil
	file_api_typesv2_investment_instrument_proto_goTypes = nil
	file_api_typesv2_investment_instrument_proto_depIdxs = nil
}
