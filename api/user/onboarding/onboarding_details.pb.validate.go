// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/user/onboarding/internal/onboarding_details.proto

package onboarding

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	customer "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"

	enums "github.com/epifi/gamma/api/inappreferral/enums"

	kyc "github.com/epifi/gamma/api/kyc"

	onboarding "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"

	typesv2 "github.com/epifi/gamma/api/typesv2"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = customer.DedupeStatus(0)

	_ = enums.FiniteCodeChannel(0)

	_ = kyc.KYCLevel(0)

	_ = onboarding.FeatureOnboardingEntryPoint(0)

	_ = typesv2.Verdict(0)

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on OnboardingDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *OnboardingDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OnboardingDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OnboardingDetailsMultiError, or nil if none found.
func (m *OnboardingDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OnboardingDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetStageDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "StageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "StageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStageDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "StageDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccountInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "AccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "AccountInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccountInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "AccountInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "CardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "CardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vendor

	// no validation rules for OnboardingId

	// no validation rules for UserId

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStageMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "StageMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "StageMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStageMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "StageMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentOnboardingStage

	// no validation rules for Feature

	if all {
		switch v := interface{}(m.GetFiLiteDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "FiLiteDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "FiLiteDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFiLiteDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "FiLiteDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFeatureDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "FeatureDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "FeatureDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFeatureDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "FeatureDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPanAadharLinkageDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "PanAadharLinkageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "PanAadharLinkageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanAadharLinkageDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "PanAadharLinkageDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStageProcLastResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "StageProcLastResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OnboardingDetailsValidationError{
					field:  "StageProcLastResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStageProcLastResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OnboardingDetailsValidationError{
				field:  "StageProcLastResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OnboardingDetailsMultiError(errors)
	}

	return nil
}

// OnboardingDetailsMultiError is an error wrapping multiple validation errors
// returned by OnboardingDetails.ValidateAll() if the designated constraints
// aren't met.
type OnboardingDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OnboardingDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OnboardingDetailsMultiError) AllErrors() []error { return m }

// OnboardingDetailsValidationError is the validation error returned by
// OnboardingDetails.Validate if the designated constraints aren't met.
type OnboardingDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OnboardingDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OnboardingDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OnboardingDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OnboardingDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OnboardingDetailsValidationError) ErrorName() string {
	return "OnboardingDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OnboardingDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOnboardingDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OnboardingDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OnboardingDetailsValidationError{}

// Validate checks the field values on AccountInformationInternal with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountInformationInternal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountInformationInternal with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountInformationInternalMultiError, or nil if none found.
func (m *AccountInformationInternal) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountInformationInternal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountNumber

	// no validation rules for AccountId

	// no validation rules for IsPrimary

	if len(errors) > 0 {
		return AccountInformationInternalMultiError(errors)
	}

	return nil
}

// AccountInformationInternalMultiError is an error wrapping multiple
// validation errors returned by AccountInformationInternal.ValidateAll() if
// the designated constraints aren't met.
type AccountInformationInternalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountInformationInternalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountInformationInternalMultiError) AllErrors() []error { return m }

// AccountInformationInternalValidationError is the validation error returned
// by AccountInformationInternal.Validate if the designated constraints aren't met.
type AccountInformationInternalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountInformationInternalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountInformationInternalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountInformationInternalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountInformationInternalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountInformationInternalValidationError) ErrorName() string {
	return "AccountInformationInternalValidationError"
}

// Error satisfies the builtin error interface
func (e AccountInformationInternalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountInformationInternal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountInformationInternalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountInformationInternalValidationError{}

// Validate checks the field values on CardInformationInternal with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CardInformationInternal) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardInformationInternal with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CardInformationInternalMultiError, or nil if none found.
func (m *CardInformationInternal) ValidateAll() error {
	return m.validate(true)
}

func (m *CardInformationInternal) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCardDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CardInformationInternalValidationError{
						field:  fmt.Sprintf("CardDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CardInformationInternalValidationError{
						field:  fmt.Sprintf("CardDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CardInformationInternalValidationError{
					field:  fmt.Sprintf("CardDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CardInformationInternalMultiError(errors)
	}

	return nil
}

// CardInformationInternalMultiError is an error wrapping multiple validation
// errors returned by CardInformationInternal.ValidateAll() if the designated
// constraints aren't met.
type CardInformationInternalMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardInformationInternalMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardInformationInternalMultiError) AllErrors() []error { return m }

// CardInformationInternalValidationError is the validation error returned by
// CardInformationInternal.Validate if the designated constraints aren't met.
type CardInformationInternalValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardInformationInternalValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardInformationInternalValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardInformationInternalValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardInformationInternalValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardInformationInternalValidationError) ErrorName() string {
	return "CardInformationInternalValidationError"
}

// Error satisfies the builtin error interface
func (e CardInformationInternalValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardInformationInternal.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardInformationInternalValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardInformationInternalValidationError{}

// Validate checks the field values on SingleCardInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SingleCardInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SingleCardInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SingleCardInfoMultiError,
// or nil if none found.
func (m *SingleCardInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *SingleCardInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardId

	if all {
		switch v := interface{}(m.GetBasicCardInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SingleCardInfoValidationError{
					field:  "BasicCardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SingleCardInfoValidationError{
					field:  "BasicCardInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBasicCardInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SingleCardInfoValidationError{
				field:  "BasicCardInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SingleCardInfoMultiError(errors)
	}

	return nil
}

// SingleCardInfoMultiError is an error wrapping multiple validation errors
// returned by SingleCardInfo.ValidateAll() if the designated constraints
// aren't met.
type SingleCardInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SingleCardInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SingleCardInfoMultiError) AllErrors() []error { return m }

// SingleCardInfoValidationError is the validation error returned by
// SingleCardInfo.Validate if the designated constraints aren't met.
type SingleCardInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SingleCardInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SingleCardInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SingleCardInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SingleCardInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SingleCardInfoValidationError) ErrorName() string { return "SingleCardInfoValidationError" }

// Error satisfies the builtin error interface
func (e SingleCardInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSingleCardInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SingleCardInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SingleCardInfoValidationError{}

// Validate checks the field values on StageDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageDetailsMultiError, or
// nil if none found.
func (m *StageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *StageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetStageMapping()))
		i := 0
		for key := range m.GetStageMapping() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetStageMapping()[key]
			_ = val

			// no validation rules for StageMapping[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, StageDetailsValidationError{
							field:  fmt.Sprintf("StageMapping[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, StageDetailsValidationError{
							field:  fmt.Sprintf("StageMapping[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return StageDetailsValidationError{
						field:  fmt.Sprintf("StageMapping[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return StageDetailsMultiError(errors)
	}

	return nil
}

// StageDetailsMultiError is an error wrapping multiple validation errors
// returned by StageDetails.ValidateAll() if the designated constraints aren't met.
type StageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageDetailsMultiError) AllErrors() []error { return m }

// StageDetailsValidationError is the validation error returned by
// StageDetails.Validate if the designated constraints aren't met.
type StageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageDetailsValidationError) ErrorName() string { return "StageDetailsValidationError" }

// Error satisfies the builtin error interface
func (e StageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageDetailsValidationError{}

// Validate checks the field values on KYCMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KYCMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KYCMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KYCMetadataMultiError, or
// nil if none found.
func (m *KYCMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *KYCMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetExpiryAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KYCMetadataValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KYCMetadataValidationError{
					field:  "ExpiryAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpiryAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KYCMetadataValidationError{
				field:  "ExpiryAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LastUpdatedKycLevel

	// no validation rules for FailureType

	// no validation rules for CkycFailureType

	if len(errors) > 0 {
		return KYCMetadataMultiError(errors)
	}

	return nil
}

// KYCMetadataMultiError is an error wrapping multiple validation errors
// returned by KYCMetadata.ValidateAll() if the designated constraints aren't met.
type KYCMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KYCMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KYCMetadataMultiError) AllErrors() []error { return m }

// KYCMetadataValidationError is the validation error returned by
// KYCMetadata.Validate if the designated constraints aren't met.
type KYCMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KYCMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KYCMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KYCMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KYCMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KYCMetadataValidationError) ErrorName() string { return "KYCMetadataValidationError" }

// Error satisfies the builtin error interface
func (e KYCMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKYCMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KYCMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KYCMetadataValidationError{}

// Validate checks the field values on StageInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageInfoMultiError, or nil
// if none found.
func (m *StageInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StageInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetLastUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "LastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "LastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageInfoValidationError{
				field:  "LastUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStartedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageInfoValidationError{
					field:  "StartedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageInfoValidationError{
				field:  "StartedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LocationToken

	if len(errors) > 0 {
		return StageInfoMultiError(errors)
	}

	return nil
}

// StageInfoMultiError is an error wrapping multiple validation errors returned
// by StageInfo.ValidateAll() if the designated constraints aren't met.
type StageInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageInfoMultiError) AllErrors() []error { return m }

// StageInfoValidationError is the validation error returned by
// StageInfo.Validate if the designated constraints aren't met.
type StageInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageInfoValidationError) ErrorName() string { return "StageInfoValidationError" }

// Error satisfies the builtin error interface
func (e StageInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageInfoValidationError{}

// Validate checks the field values on StageProcLastResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StageProcLastResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageProcLastResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StageProcLastResponseMultiError, or nil if none found.
func (m *StageProcLastResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *StageProcLastResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ScreenName

	// no validation rules for Stage

	// no validation rules for AnalyticsEventProperties

	// no validation rules for Error

	if len(errors) > 0 {
		return StageProcLastResponseMultiError(errors)
	}

	return nil
}

// StageProcLastResponseMultiError is an error wrapping multiple validation
// errors returned by StageProcLastResponse.ValidateAll() if the designated
// constraints aren't met.
type StageProcLastResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageProcLastResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageProcLastResponseMultiError) AllErrors() []error { return m }

// StageProcLastResponseValidationError is the validation error returned by
// StageProcLastResponse.Validate if the designated constraints aren't met.
type StageProcLastResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageProcLastResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageProcLastResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageProcLastResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageProcLastResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageProcLastResponseValidationError) ErrorName() string {
	return "StageProcLastResponseValidationError"
}

// Error satisfies the builtin error interface
func (e StageProcLastResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageProcLastResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageProcLastResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageProcLastResponseValidationError{}

// Validate checks the field values on PANNameCheck with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PANNameCheck) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PANNameCheck with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PANNameCheckMultiError, or
// nil if none found.
func (m *PANNameCheck) ValidateAll() error {
	return m.validate(true)
}

func (m *PANNameCheck) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InhouseNameMatchPassed

	// no validation rules for InhouseNameMatchScore

	// no validation rules for OldNameMatchPassed

	// no validation rules for OldNameMatchScore

	// no validation rules for InhouseNameMatchFeatureDict

	// no validation rules for InhouseNameMatchRiskMatch

	if len(errors) > 0 {
		return PANNameCheckMultiError(errors)
	}

	return nil
}

// PANNameCheckMultiError is an error wrapping multiple validation errors
// returned by PANNameCheck.ValidateAll() if the designated constraints aren't met.
type PANNameCheckMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PANNameCheckMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PANNameCheckMultiError) AllErrors() []error { return m }

// PANNameCheckValidationError is the validation error returned by
// PANNameCheck.Validate if the designated constraints aren't met.
type PANNameCheckValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PANNameCheckValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PANNameCheckValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PANNameCheckValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PANNameCheckValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PANNameCheckValidationError) ErrorName() string { return "PANNameCheckValidationError" }

// Error satisfies the builtin error interface
func (e PANNameCheckValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPANNameCheck.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PANNameCheckValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PANNameCheckValidationError{}

// Validate checks the field values on StageMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StageMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StageMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StageMetadataMultiError, or
// nil if none found.
func (m *StageMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *StageMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetKycMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "KycMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "KycMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "KycMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DedupeStatus

	// no validation rules for KycDedupeStatus

	// no validation rules for KycDedupeRetryCount

	if all {
		switch v := interface{}(m.GetPanNameCheck()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PanNameCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PanNameCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanNameCheck()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "PanNameCheck",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAppScreeningData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "AppScreeningData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "AppScreeningData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAppScreeningData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "AppScreeningData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDebitCardNameCheck()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "DebitCardNameCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "DebitCardNameCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDebitCardNameCheck()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "DebitCardNameCheck",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEkycNameDobValidation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "EkycNameDobValidation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "EkycNameDobValidation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEkycNameDobValidation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "EkycNameDobValidation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPanValidation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PanValidation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PanValidation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanValidation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "PanValidation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UNNameCheckStatus

	// no validation rules for PreCustomerCreationDedupeStatus

	if all {
		switch v := interface{}(m.GetPanManualReviewAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PanManualReviewAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PanManualReviewAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPanManualReviewAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "PanManualReviewAnnotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetVkycMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "VkycMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "VkycMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVkycMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "VkycMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GmailPanNameMatchScore

	if all {
		switch v := interface{}(m.GetRiskScreeningMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "RiskScreeningMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "RiskScreeningMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRiskScreeningMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "RiskScreeningMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DedupeDobRetryCount

	// no validation rules for LatestDedupeStatus

	if all {
		switch v := interface{}(m.GetLivenessMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "LivenessMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "LivenessMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLivenessMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "LivenessMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UNNameCheckStatusV2

	if all {
		switch v := interface{}(m.GetIntentSelectionMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "IntentSelectionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "IntentSelectionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntentSelectionMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "IntentSelectionMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreCustomerCreationMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PreCustomerCreationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PreCustomerCreationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreCustomerCreationMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "PreCustomerCreationMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateProfileDetailsMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "UpdateProfileDetailsMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "UpdateProfileDetailsMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateProfileDetailsMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "UpdateProfileDetailsMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSoftIntentSelectionMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "SoftIntentSelectionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "SoftIntentSelectionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSoftIntentSelectionMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "SoftIntentSelectionMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPassportVerificationMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PassportVerificationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PassportVerificationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPassportVerificationMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "PassportVerificationMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCountryIdVerificationMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "CountryIdVerificationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "CountryIdVerificationMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCountryIdVerificationMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "CountryIdVerificationMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNonResidentCrossValidationResult()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "NonResidentCrossValidationResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "NonResidentCrossValidationResult",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNonResidentCrossValidationResult()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "NonResidentCrossValidationResult",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserAllowedToOpenAmbAccount

	if all {
		switch v := interface{}(m.GetCreditReportFetchMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "CreditReportFetchMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "CreditReportFetchMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreditReportFetchMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "CreditReportFetchMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreAccountCreationAddMoneyMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PreAccountCreationAddMoneyMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PreAccountCreationAddMoneyMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreAccountCreationAddMoneyMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "PreAccountCreationAddMoneyMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermissionMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PermissionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "PermissionMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermissionMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "PermissionMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAddMoneyMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "AddMoneyMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StageMetadataValidationError{
					field:  "AddMoneyMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddMoneyMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StageMetadataValidationError{
				field:  "AddMoneyMetadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StageMetadataMultiError(errors)
	}

	return nil
}

// StageMetadataMultiError is an error wrapping multiple validation errors
// returned by StageMetadata.ValidateAll() if the designated constraints
// aren't met.
type StageMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StageMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StageMetadataMultiError) AllErrors() []error { return m }

// StageMetadataValidationError is the validation error returned by
// StageMetadata.Validate if the designated constraints aren't met.
type StageMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StageMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StageMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StageMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StageMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StageMetadataValidationError) ErrorName() string { return "StageMetadataValidationError" }

// Error satisfies the builtin error interface
func (e StageMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStageMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StageMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StageMetadataValidationError{}

// Validate checks the field values on PermissionMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PermissionMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PermissionMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PermissionMetaDataMultiError, or nil if none found.
func (m *PermissionMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *PermissionMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PermissionAckReceived

	if len(errors) > 0 {
		return PermissionMetaDataMultiError(errors)
	}

	return nil
}

// PermissionMetaDataMultiError is an error wrapping multiple validation errors
// returned by PermissionMetaData.ValidateAll() if the designated constraints
// aren't met.
type PermissionMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PermissionMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PermissionMetaDataMultiError) AllErrors() []error { return m }

// PermissionMetaDataValidationError is the validation error returned by
// PermissionMetaData.Validate if the designated constraints aren't met.
type PermissionMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PermissionMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PermissionMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PermissionMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PermissionMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PermissionMetaDataValidationError) ErrorName() string {
	return "PermissionMetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e PermissionMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPermissionMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PermissionMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PermissionMetaDataValidationError{}

// Validate checks the field values on AddMoneyMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddMoneyMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddMoneyMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddMoneyMetadataMultiError, or nil if none found.
func (m *AddMoneyMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *AddMoneyMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SkipReason

	// no validation rules for SuccessReason

	if len(errors) > 0 {
		return AddMoneyMetadataMultiError(errors)
	}

	return nil
}

// AddMoneyMetadataMultiError is an error wrapping multiple validation errors
// returned by AddMoneyMetadata.ValidateAll() if the designated constraints
// aren't met.
type AddMoneyMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddMoneyMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddMoneyMetadataMultiError) AllErrors() []error { return m }

// AddMoneyMetadataValidationError is the validation error returned by
// AddMoneyMetadata.Validate if the designated constraints aren't met.
type AddMoneyMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddMoneyMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddMoneyMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddMoneyMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddMoneyMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddMoneyMetadataValidationError) ErrorName() string { return "AddMoneyMetadataValidationError" }

// Error satisfies the builtin error interface
func (e AddMoneyMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddMoneyMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddMoneyMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddMoneyMetadataValidationError{}

// Validate checks the field values on PreAccountCreationAddMoneyMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *PreAccountCreationAddMoneyMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreAccountCreationAddMoneyMetadata
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// PreAccountCreationAddMoneyMetadataMultiError, or nil if none found.
func (m *PreAccountCreationAddMoneyMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *PreAccountCreationAddMoneyMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SkippedAfterSavingsAccCreation

	// no validation rules for SkippedAfterSuccessfulAddMoney

	// no validation rules for SkipReason

	// no validation rules for SuccessReason

	if len(errors) > 0 {
		return PreAccountCreationAddMoneyMetadataMultiError(errors)
	}

	return nil
}

// PreAccountCreationAddMoneyMetadataMultiError is an error wrapping multiple
// validation errors returned by
// PreAccountCreationAddMoneyMetadata.ValidateAll() if the designated
// constraints aren't met.
type PreAccountCreationAddMoneyMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreAccountCreationAddMoneyMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreAccountCreationAddMoneyMetadataMultiError) AllErrors() []error { return m }

// PreAccountCreationAddMoneyMetadataValidationError is the validation error
// returned by PreAccountCreationAddMoneyMetadata.Validate if the designated
// constraints aren't met.
type PreAccountCreationAddMoneyMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreAccountCreationAddMoneyMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreAccountCreationAddMoneyMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreAccountCreationAddMoneyMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreAccountCreationAddMoneyMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreAccountCreationAddMoneyMetadataValidationError) ErrorName() string {
	return "PreAccountCreationAddMoneyMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e PreAccountCreationAddMoneyMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreAccountCreationAddMoneyMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreAccountCreationAddMoneyMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreAccountCreationAddMoneyMetadataValidationError{}

// Validate checks the field values on CreditReportFetchMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditReportFetchMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditReportFetchMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreditReportFetchMetadataMultiError, or nil if none found.
func (m *CreditReportFetchMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditReportFetchMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ClientReqIdFetchWithoutPan

	// no validation rules for ClientReqIdFetchWithPan

	if len(errors) > 0 {
		return CreditReportFetchMetadataMultiError(errors)
	}

	return nil
}

// CreditReportFetchMetadataMultiError is an error wrapping multiple validation
// errors returned by CreditReportFetchMetadata.ValidateAll() if the
// designated constraints aren't met.
type CreditReportFetchMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditReportFetchMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditReportFetchMetadataMultiError) AllErrors() []error { return m }

// CreditReportFetchMetadataValidationError is the validation error returned by
// CreditReportFetchMetadata.Validate if the designated constraints aren't met.
type CreditReportFetchMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditReportFetchMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditReportFetchMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditReportFetchMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditReportFetchMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditReportFetchMetadataValidationError) ErrorName() string {
	return "CreditReportFetchMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e CreditReportFetchMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditReportFetchMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditReportFetchMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditReportFetchMetadataValidationError{}

// Validate checks the field values on PreCustomerCreationMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PreCustomerCreationMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreCustomerCreationMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreCustomerCreationMetadataMultiError, or nil if none found.
func (m *PreCustomerCreationMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *PreCustomerCreationMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return PreCustomerCreationMetadataMultiError(errors)
	}

	return nil
}

// PreCustomerCreationMetadataMultiError is an error wrapping multiple
// validation errors returned by PreCustomerCreationMetadata.ValidateAll() if
// the designated constraints aren't met.
type PreCustomerCreationMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreCustomerCreationMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreCustomerCreationMetadataMultiError) AllErrors() []error { return m }

// PreCustomerCreationMetadataValidationError is the validation error returned
// by PreCustomerCreationMetadata.Validate if the designated constraints
// aren't met.
type PreCustomerCreationMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreCustomerCreationMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreCustomerCreationMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreCustomerCreationMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreCustomerCreationMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreCustomerCreationMetadataValidationError) ErrorName() string {
	return "PreCustomerCreationMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e PreCustomerCreationMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreCustomerCreationMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreCustomerCreationMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreCustomerCreationMetadataValidationError{}

// Validate checks the field values on UpdateProfileDetailsMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateProfileDetailsMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateProfileDetailsMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateProfileDetailsMetadataMultiError, or nil if none found.
func (m *UpdateProfileDetailsMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateProfileDetailsMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if len(errors) > 0 {
		return UpdateProfileDetailsMetadataMultiError(errors)
	}

	return nil
}

// UpdateProfileDetailsMetadataMultiError is an error wrapping multiple
// validation errors returned by UpdateProfileDetailsMetadata.ValidateAll() if
// the designated constraints aren't met.
type UpdateProfileDetailsMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateProfileDetailsMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateProfileDetailsMetadataMultiError) AllErrors() []error { return m }

// UpdateProfileDetailsMetadataValidationError is the validation error returned
// by UpdateProfileDetailsMetadata.Validate if the designated constraints
// aren't met.
type UpdateProfileDetailsMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateProfileDetailsMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateProfileDetailsMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateProfileDetailsMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateProfileDetailsMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateProfileDetailsMetadataValidationError) ErrorName() string {
	return "UpdateProfileDetailsMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateProfileDetailsMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateProfileDetailsMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateProfileDetailsMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateProfileDetailsMetadataValidationError{}

// Validate checks the field values on LivenessMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LivenessMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LivenessMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LivenessMetadataMultiError, or nil if none found.
func (m *LivenessMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *LivenessMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if len(errors) > 0 {
		return LivenessMetadataMultiError(errors)
	}

	return nil
}

// LivenessMetadataMultiError is an error wrapping multiple validation errors
// returned by LivenessMetadata.ValidateAll() if the designated constraints
// aren't met.
type LivenessMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LivenessMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LivenessMetadataMultiError) AllErrors() []error { return m }

// LivenessMetadataValidationError is the validation error returned by
// LivenessMetadata.Validate if the designated constraints aren't met.
type LivenessMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LivenessMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LivenessMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LivenessMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LivenessMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LivenessMetadataValidationError) ErrorName() string { return "LivenessMetadataValidationError" }

// Error satisfies the builtin error interface
func (e LivenessMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLivenessMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LivenessMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LivenessMetadataValidationError{}

// Validate checks the field values on RiskScreeningMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskScreeningMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskScreeningMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskScreeningMetadataMultiError, or nil if none found.
func (m *RiskScreeningMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskScreeningMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RiskBlockingReason

	// no validation rules for ForcedManualReviewReason

	if len(errors) > 0 {
		return RiskScreeningMetadataMultiError(errors)
	}

	return nil
}

// RiskScreeningMetadataMultiError is an error wrapping multiple validation
// errors returned by RiskScreeningMetadata.ValidateAll() if the designated
// constraints aren't met.
type RiskScreeningMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskScreeningMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskScreeningMetadataMultiError) AllErrors() []error { return m }

// RiskScreeningMetadataValidationError is the validation error returned by
// RiskScreeningMetadata.Validate if the designated constraints aren't met.
type RiskScreeningMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskScreeningMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskScreeningMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskScreeningMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskScreeningMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskScreeningMetadataValidationError) ErrorName() string {
	return "RiskScreeningMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e RiskScreeningMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskScreeningMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskScreeningMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskScreeningMetadataValidationError{}

// Validate checks the field values on PanManualReviewAnnotation with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PanManualReviewAnnotation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PanManualReviewAnnotation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PanManualReviewAnnotationMultiError, or nil if none found.
func (m *PanManualReviewAnnotation) ValidateAll() error {
	return m.validate(true)
}

func (m *PanManualReviewAnnotation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Verdict

	// no validation rules for ReviewedBy

	if all {
		switch v := interface{}(m.GetReviewedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanManualReviewAnnotationValidationError{
					field:  "ReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanManualReviewAnnotationValidationError{
					field:  "ReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanManualReviewAnnotationValidationError{
				field:  "ReviewedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	if len(errors) > 0 {
		return PanManualReviewAnnotationMultiError(errors)
	}

	return nil
}

// PanManualReviewAnnotationMultiError is an error wrapping multiple validation
// errors returned by PanManualReviewAnnotation.ValidateAll() if the
// designated constraints aren't met.
type PanManualReviewAnnotationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PanManualReviewAnnotationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PanManualReviewAnnotationMultiError) AllErrors() []error { return m }

// PanManualReviewAnnotationValidationError is the validation error returned by
// PanManualReviewAnnotation.Validate if the designated constraints aren't met.
type PanManualReviewAnnotationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PanManualReviewAnnotationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PanManualReviewAnnotationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PanManualReviewAnnotationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PanManualReviewAnnotationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PanManualReviewAnnotationValidationError) ErrorName() string {
	return "PanManualReviewAnnotationValidationError"
}

// Error satisfies the builtin error interface
func (e PanManualReviewAnnotationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPanManualReviewAnnotation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PanManualReviewAnnotationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PanManualReviewAnnotationValidationError{}

// Validate checks the field values on DebitCardNameCheck with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DebitCardNameCheck) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DebitCardNameCheck with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DebitCardNameCheckMultiError, or nil if none found.
func (m *DebitCardNameCheck) ValidateAll() error {
	return m.validate(true)
}

func (m *DebitCardNameCheck) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for InhouseNameMatchPassed

	// no validation rules for InhouseNameMatchScore

	// no validation rules for OldNameMatchPassed

	// no validation rules for OldNameMatchScore

	// no validation rules for NameCheckRetryCount

	if len(errors) > 0 {
		return DebitCardNameCheckMultiError(errors)
	}

	return nil
}

// DebitCardNameCheckMultiError is an error wrapping multiple validation errors
// returned by DebitCardNameCheck.ValidateAll() if the designated constraints
// aren't met.
type DebitCardNameCheckMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DebitCardNameCheckMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DebitCardNameCheckMultiError) AllErrors() []error { return m }

// DebitCardNameCheckValidationError is the validation error returned by
// DebitCardNameCheck.Validate if the designated constraints aren't met.
type DebitCardNameCheckValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DebitCardNameCheckValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DebitCardNameCheckValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DebitCardNameCheckValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DebitCardNameCheckValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DebitCardNameCheckValidationError) ErrorName() string {
	return "DebitCardNameCheckValidationError"
}

// Error satisfies the builtin error interface
func (e DebitCardNameCheckValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDebitCardNameCheck.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DebitCardNameCheckValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DebitCardNameCheckValidationError{}

// Validate checks the field values on AppScreeningMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AppScreeningMetaData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AppScreeningMetaData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AppScreeningMetaDataMultiError, or nil if none found.
func (m *AppScreeningMetaData) ValidateAll() error {
	return m.validate(true)
}

func (m *AppScreeningMetaData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CreditReportFound

	// no validation rules for PanCreditReportCheckAttempted

	// no validation rules for ConsentCreditReportDownload

	// no validation rules for CreditReportVerificationPassed

	// no validation rules for EmploymentVerificationPassed

	// no validation rules for ManualScreeningReason

	// no validation rules for SkipReason

	// no validation rules for CreditReportFlowSkipReason

	// no validation rules for EmploymentVerificationSuccessReason

	// no validation rules for CreditReportVerificationSuccessReason

	// no validation rules for FiniteCodeChannel

	// no validation rules for FiniteCodeType

	// no validation rules for EmploymentDeclarationOnly

	// no validation rules for GmailVerificationPassed

	// no validation rules for WorkEmailVerificationPassed

	// no validation rules for WorkEmailVerificationFailureReason

	// no validation rules for LinkedinVerificationPassed

	// no validation rules for LinkedinValidationType

	// no validation rules for HeuristicScreeningPassed

	// no validation rules for GstinPresenceCheckPassed

	// no validation rules for GstinPresenceCheckSuccessReason

	// no validation rules for ExperimentalScreeningBypass

	// no validation rules for ScreeningPassed

	// no validation rules for HeuristicScreeningPassReason

	// no validation rules for Form16CheckPassed

	// no validation rules for UanPresenceCheckPassed

	// no validation rules for AffluenceCheckPassed

	// no validation rules for AffluenceScore

	// no validation rules for CreditReportPanMatched

	if len(errors) > 0 {
		return AppScreeningMetaDataMultiError(errors)
	}

	return nil
}

// AppScreeningMetaDataMultiError is an error wrapping multiple validation
// errors returned by AppScreeningMetaData.ValidateAll() if the designated
// constraints aren't met.
type AppScreeningMetaDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AppScreeningMetaDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AppScreeningMetaDataMultiError) AllErrors() []error { return m }

// AppScreeningMetaDataValidationError is the validation error returned by
// AppScreeningMetaData.Validate if the designated constraints aren't met.
type AppScreeningMetaDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AppScreeningMetaDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AppScreeningMetaDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AppScreeningMetaDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AppScreeningMetaDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AppScreeningMetaDataValidationError) ErrorName() string {
	return "AppScreeningMetaDataValidationError"
}

// Error satisfies the builtin error interface
func (e AppScreeningMetaDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAppScreeningMetaData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AppScreeningMetaDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AppScreeningMetaDataValidationError{}

// Validate checks the field values on EKYCNameDOBValidationData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EKYCNameDOBValidationData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EKYCNameDOBValidationData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EKYCNameDOBValidationDataMultiError, or nil if none found.
func (m *EKYCNameDOBValidationData) ValidateAll() error {
	return m.validate(true)
}

func (m *EKYCNameDOBValidationData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Retries

	// no validation rules for FailureDesc

	// no validation rules for RawResponse

	if len(errors) > 0 {
		return EKYCNameDOBValidationDataMultiError(errors)
	}

	return nil
}

// EKYCNameDOBValidationDataMultiError is an error wrapping multiple validation
// errors returned by EKYCNameDOBValidationData.ValidateAll() if the
// designated constraints aren't met.
type EKYCNameDOBValidationDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EKYCNameDOBValidationDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EKYCNameDOBValidationDataMultiError) AllErrors() []error { return m }

// EKYCNameDOBValidationDataValidationError is the validation error returned by
// EKYCNameDOBValidationData.Validate if the designated constraints aren't met.
type EKYCNameDOBValidationDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EKYCNameDOBValidationDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EKYCNameDOBValidationDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EKYCNameDOBValidationDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EKYCNameDOBValidationDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EKYCNameDOBValidationDataValidationError) ErrorName() string {
	return "EKYCNameDOBValidationDataValidationError"
}

// Error satisfies the builtin error interface
func (e EKYCNameDOBValidationDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEKYCNameDOBValidationData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EKYCNameDOBValidationDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EKYCNameDOBValidationDataValidationError{}

// Validate checks the field values on PanValidation with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PanValidation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PanValidation with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PanValidationMultiError, or
// nil if none found.
func (m *PanValidation) ValidateAll() error {
	return m.validate(true)
}

func (m *PanValidation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBlockTill()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PanValidationValidationError{
					field:  "BlockTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PanValidationValidationError{
					field:  "BlockTill",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBlockTill()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PanValidationValidationError{
				field:  "BlockTill",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PanValidationSuspendReason

	if len(errors) > 0 {
		return PanValidationMultiError(errors)
	}

	return nil
}

// PanValidationMultiError is an error wrapping multiple validation errors
// returned by PanValidation.ValidateAll() if the designated constraints
// aren't met.
type PanValidationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PanValidationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PanValidationMultiError) AllErrors() []error { return m }

// PanValidationValidationError is the validation error returned by
// PanValidation.Validate if the designated constraints aren't met.
type PanValidationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PanValidationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PanValidationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PanValidationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PanValidationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PanValidationValidationError) ErrorName() string { return "PanValidationValidationError" }

// Error satisfies the builtin error interface
func (e PanValidationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPanValidation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PanValidationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PanValidationValidationError{}

// Validate checks the field values on VKYCMetadata with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VKYCMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VKYCMetadata with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VKYCMetadataMultiError, or
// nil if none found.
func (m *VKYCMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *VKYCMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PerformVkycCheck

	// no validation rules for VkycOption

	if all {
		switch v := interface{}(m.GetInhouseVKYC()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VKYCMetadataValidationError{
					field:  "InhouseVKYC",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VKYCMetadataValidationError{
					field:  "InhouseVKYC",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInhouseVKYC()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VKYCMetadataValidationError{
				field:  "InhouseVKYC",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VKYCMetadataMultiError(errors)
	}

	return nil
}

// VKYCMetadataMultiError is an error wrapping multiple validation errors
// returned by VKYCMetadata.ValidateAll() if the designated constraints aren't met.
type VKYCMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VKYCMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VKYCMetadataMultiError) AllErrors() []error { return m }

// VKYCMetadataValidationError is the validation error returned by
// VKYCMetadata.Validate if the designated constraints aren't met.
type VKYCMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VKYCMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VKYCMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VKYCMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VKYCMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VKYCMetadataValidationError) ErrorName() string { return "VKYCMetadataValidationError" }

// Error satisfies the builtin error interface
func (e VKYCMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVKYCMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VKYCMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VKYCMetadataValidationError{}

// Validate checks the field values on InhouseVKYC with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InhouseVKYC) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InhouseVKYC with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InhouseVKYCMultiError, or
// nil if none found.
func (m *InhouseVKYC) ValidateAll() error {
	return m.validate(true)
}

func (m *InhouseVKYC) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ApplicationId

	// no validation rules for CallId

	if len(errors) > 0 {
		return InhouseVKYCMultiError(errors)
	}

	return nil
}

// InhouseVKYCMultiError is an error wrapping multiple validation errors
// returned by InhouseVKYC.ValidateAll() if the designated constraints aren't met.
type InhouseVKYCMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InhouseVKYCMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InhouseVKYCMultiError) AllErrors() []error { return m }

// InhouseVKYCValidationError is the validation error returned by
// InhouseVKYC.Validate if the designated constraints aren't met.
type InhouseVKYCValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InhouseVKYCValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InhouseVKYCValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InhouseVKYCValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InhouseVKYCValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InhouseVKYCValidationError) ErrorName() string { return "InhouseVKYCValidationError" }

// Error satisfies the builtin error interface
func (e InhouseVKYCValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInhouseVKYC.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InhouseVKYCValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InhouseVKYCValidationError{}

// Validate checks the field values on IntentSelectionMetadata with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IntentSelectionMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IntentSelectionMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IntentSelectionMetadataMultiError, or nil if none found.
func (m *IntentSelectionMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *IntentSelectionMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Selection

	// no validation rules for AutoIntent

	if len(errors) > 0 {
		return IntentSelectionMetadataMultiError(errors)
	}

	return nil
}

// IntentSelectionMetadataMultiError is an error wrapping multiple validation
// errors returned by IntentSelectionMetadata.ValidateAll() if the designated
// constraints aren't met.
type IntentSelectionMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IntentSelectionMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IntentSelectionMetadataMultiError) AllErrors() []error { return m }

// IntentSelectionMetadataValidationError is the validation error returned by
// IntentSelectionMetadata.Validate if the designated constraints aren't met.
type IntentSelectionMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IntentSelectionMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IntentSelectionMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IntentSelectionMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IntentSelectionMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IntentSelectionMetadataValidationError) ErrorName() string {
	return "IntentSelectionMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e IntentSelectionMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIntentSelectionMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IntentSelectionMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IntentSelectionMetadataValidationError{}

// Validate checks the field values on SoftIntentSelectionMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SoftIntentSelectionMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SoftIntentSelectionMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SoftIntentSelectionMetadataMultiError, or nil if none found.
func (m *SoftIntentSelectionMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *SoftIntentSelectionMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CreditReportReqId

	if len(errors) > 0 {
		return SoftIntentSelectionMetadataMultiError(errors)
	}

	return nil
}

// SoftIntentSelectionMetadataMultiError is an error wrapping multiple
// validation errors returned by SoftIntentSelectionMetadata.ValidateAll() if
// the designated constraints aren't met.
type SoftIntentSelectionMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SoftIntentSelectionMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SoftIntentSelectionMetadataMultiError) AllErrors() []error { return m }

// SoftIntentSelectionMetadataValidationError is the validation error returned
// by SoftIntentSelectionMetadata.Validate if the designated constraints
// aren't met.
type SoftIntentSelectionMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SoftIntentSelectionMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SoftIntentSelectionMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SoftIntentSelectionMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SoftIntentSelectionMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SoftIntentSelectionMetadataValidationError) ErrorName() string {
	return "SoftIntentSelectionMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e SoftIntentSelectionMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSoftIntentSelectionMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SoftIntentSelectionMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SoftIntentSelectionMetadataValidationError{}

// Validate checks the field values on FiLiteDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FiLiteDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiLiteDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FiLiteDetailsMultiError, or
// nil if none found.
func (m *FiLiteDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FiLiteDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsEnabled

	// no validation rules for FiLiteSource

	if all {
		switch v := interface{}(m.GetAccessibilityEnabledAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiLiteDetailsValidationError{
					field:  "AccessibilityEnabledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiLiteDetailsValidationError{
					field:  "AccessibilityEnabledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccessibilityEnabledAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiLiteDetailsValidationError{
				field:  "AccessibilityEnabledAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FiLiteDetailsMultiError(errors)
	}

	return nil
}

// FiLiteDetailsMultiError is an error wrapping multiple validation errors
// returned by FiLiteDetails.ValidateAll() if the designated constraints
// aren't met.
type FiLiteDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiLiteDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiLiteDetailsMultiError) AllErrors() []error { return m }

// FiLiteDetailsValidationError is the validation error returned by
// FiLiteDetails.Validate if the designated constraints aren't met.
type FiLiteDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiLiteDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiLiteDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiLiteDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiLiteDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiLiteDetailsValidationError) ErrorName() string { return "FiLiteDetailsValidationError" }

// Error satisfies the builtin error interface
func (e FiLiteDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiLiteDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiLiteDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiLiteDetailsValidationError{}

// Validate checks the field values on FeatureInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FeatureInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeatureInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FeatureInfoMultiError, or
// nil if none found.
func (m *FeatureInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FeatureInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CurrentStage

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureInfoValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureInfoValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureInfoValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FeatureStatus

	// no validation rules for FeatureOnboardingEntryPoint

	if len(errors) > 0 {
		return FeatureInfoMultiError(errors)
	}

	return nil
}

// FeatureInfoMultiError is an error wrapping multiple validation errors
// returned by FeatureInfo.ValidateAll() if the designated constraints aren't met.
type FeatureInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeatureInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeatureInfoMultiError) AllErrors() []error { return m }

// FeatureInfoValidationError is the validation error returned by
// FeatureInfo.Validate if the designated constraints aren't met.
type FeatureInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeatureInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeatureInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeatureInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeatureInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeatureInfoValidationError) ErrorName() string { return "FeatureInfoValidationError" }

// Error satisfies the builtin error interface
func (e FeatureInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeatureInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeatureInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeatureInfoValidationError{}

// Validate checks the field values on FeatureDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FeatureDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeatureDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FeatureDetailsMultiError,
// or nil if none found.
func (m *FeatureDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FeatureDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetFeatureInfo()))
		i := 0
		for key := range m.GetFeatureInfo() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetFeatureInfo()[key]
			_ = val

			// no validation rules for FeatureInfo[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, FeatureDetailsValidationError{
							field:  fmt.Sprintf("FeatureInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, FeatureDetailsValidationError{
							field:  fmt.Sprintf("FeatureInfo[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return FeatureDetailsValidationError{
						field:  fmt.Sprintf("FeatureInfo[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	if len(errors) > 0 {
		return FeatureDetailsMultiError(errors)
	}

	return nil
}

// FeatureDetailsMultiError is an error wrapping multiple validation errors
// returned by FeatureDetails.ValidateAll() if the designated constraints
// aren't met.
type FeatureDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeatureDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeatureDetailsMultiError) AllErrors() []error { return m }

// FeatureDetailsValidationError is the validation error returned by
// FeatureDetails.Validate if the designated constraints aren't met.
type FeatureDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeatureDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeatureDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeatureDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeatureDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeatureDetailsValidationError) ErrorName() string { return "FeatureDetailsValidationError" }

// Error satisfies the builtin error interface
func (e FeatureDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeatureDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeatureDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeatureDetailsValidationError{}

// Validate checks the field values on PanAadharLinkageDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PanAadharLinkageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PanAadharLinkageDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PanAadharLinkageDetailsMultiError, or nil if none found.
func (m *PanAadharLinkageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PanAadharLinkageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EkycNameDobValidationAadharDigitsHash

	// no validation rules for PanLinkedAadharDigitsHash

	// no validation rules for IsAadharDigitsHashMismatch

	// no validation rules for PanAadhaarLinked

	// no validation rules for EkycNameDobValidationAadharLast2DigitsHash

	// no validation rules for PanLinkedAadhaarLast2DigitsHash

	if len(errors) > 0 {
		return PanAadharLinkageDetailsMultiError(errors)
	}

	return nil
}

// PanAadharLinkageDetailsMultiError is an error wrapping multiple validation
// errors returned by PanAadharLinkageDetails.ValidateAll() if the designated
// constraints aren't met.
type PanAadharLinkageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PanAadharLinkageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PanAadharLinkageDetailsMultiError) AllErrors() []error { return m }

// PanAadharLinkageDetailsValidationError is the validation error returned by
// PanAadharLinkageDetails.Validate if the designated constraints aren't met.
type PanAadharLinkageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PanAadharLinkageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PanAadharLinkageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PanAadharLinkageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PanAadharLinkageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PanAadharLinkageDetailsValidationError) ErrorName() string {
	return "PanAadharLinkageDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PanAadharLinkageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPanAadharLinkageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PanAadharLinkageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PanAadharLinkageDetailsValidationError{}

// Validate checks the field values on PassportVerificationMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PassportVerificationMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PassportVerificationMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PassportVerificationMetadataMultiError, or nil if none found.
func (m *PassportVerificationMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *PassportVerificationMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocExtractClientReqId

	// no validation rules for PassportFrontUrl

	// no validation rules for PassportBackUrl

	// no validation rules for Status

	// no validation rules for PassportFrontDocExtractClientReqId

	// no validation rules for PassportBackDocExtractClientReqId

	if all {
		switch v := interface{}(m.GetAttemptedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportVerificationMetadataValidationError{
					field:  "AttemptedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportVerificationMetadataValidationError{
					field:  "AttemptedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAttemptedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportVerificationMetadataValidationError{
				field:  "AttemptedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentAttemptsInCooldownWindow

	// no validation rules for FaceImageUrl

	if all {
		switch v := interface{}(m.GetVerificationVendorApiResults()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportVerificationMetadataValidationError{
					field:  "VerificationVendorApiResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportVerificationMetadataValidationError{
					field:  "VerificationVendorApiResults",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVerificationVendorApiResults()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportVerificationMetadataValidationError{
				field:  "VerificationVendorApiResults",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OldPassportFileNumber

	// no validation rules for FailureReason

	// no validation rules for PassportArn

	if all {
		switch v := interface{}(m.GetManualReviewAnnotation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportVerificationMetadataValidationError{
					field:  "ManualReviewAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportVerificationMetadataValidationError{
					field:  "ManualReviewAnnotation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetManualReviewAnnotation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportVerificationMetadataValidationError{
				field:  "ManualReviewAnnotation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PassportVerificationMetadataMultiError(errors)
	}

	return nil
}

// PassportVerificationMetadataMultiError is an error wrapping multiple
// validation errors returned by PassportVerificationMetadata.ValidateAll() if
// the designated constraints aren't met.
type PassportVerificationMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PassportVerificationMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PassportVerificationMetadataMultiError) AllErrors() []error { return m }

// PassportVerificationMetadataValidationError is the validation error returned
// by PassportVerificationMetadata.Validate if the designated constraints
// aren't met.
type PassportVerificationMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PassportVerificationMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PassportVerificationMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PassportVerificationMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PassportVerificationMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PassportVerificationMetadataValidationError) ErrorName() string {
	return "PassportVerificationMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e PassportVerificationMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPassportVerificationMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PassportVerificationMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PassportVerificationMetadataValidationError{}

// Validate checks the field values on PassportManualReviewDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PassportManualReviewDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PassportManualReviewDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PassportManualReviewDetailsMultiError, or nil if none found.
func (m *PassportManualReviewDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PassportManualReviewDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Verdict

	// no validation rules for ReviewedBy

	if all {
		switch v := interface{}(m.GetReviewedOn()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PassportManualReviewDetailsValidationError{
					field:  "ReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PassportManualReviewDetailsValidationError{
					field:  "ReviewedOn",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewedOn()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PassportManualReviewDetailsValidationError{
				field:  "ReviewedOn",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Remarks

	// no validation rules for ReviewProofImageUrl

	if len(errors) > 0 {
		return PassportManualReviewDetailsMultiError(errors)
	}

	return nil
}

// PassportManualReviewDetailsMultiError is an error wrapping multiple
// validation errors returned by PassportManualReviewDetails.ValidateAll() if
// the designated constraints aren't met.
type PassportManualReviewDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PassportManualReviewDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PassportManualReviewDetailsMultiError) AllErrors() []error { return m }

// PassportManualReviewDetailsValidationError is the validation error returned
// by PassportManualReviewDetails.Validate if the designated constraints
// aren't met.
type PassportManualReviewDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PassportManualReviewDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PassportManualReviewDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PassportManualReviewDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PassportManualReviewDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PassportManualReviewDetailsValidationError) ErrorName() string {
	return "PassportManualReviewDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PassportManualReviewDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPassportManualReviewDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PassportManualReviewDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PassportManualReviewDetailsValidationError{}

// Validate checks the field values on CrossValidationResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CrossValidationResult) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CrossValidationResult with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CrossValidationResultMultiError, or nil if none found.
func (m *CrossValidationResult) ValidateAll() error {
	return m.validate(true)
}

func (m *CrossValidationResult) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Verdict

	// no validation rules for Check

	// no validation rules for FailureReason

	// no validation rules for DataSource_1

	// no validation rules for DataSource_2

	if all {
		switch v := interface{}(m.GetCrossValidationManualReviewInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CrossValidationResultValidationError{
					field:  "CrossValidationManualReviewInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CrossValidationResultValidationError{
					field:  "CrossValidationManualReviewInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCrossValidationManualReviewInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CrossValidationResultValidationError{
				field:  "CrossValidationManualReviewInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CrossValidationResultMultiError(errors)
	}

	return nil
}

// CrossValidationResultMultiError is an error wrapping multiple validation
// errors returned by CrossValidationResult.ValidateAll() if the designated
// constraints aren't met.
type CrossValidationResultMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CrossValidationResultMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CrossValidationResultMultiError) AllErrors() []error { return m }

// CrossValidationResultValidationError is the validation error returned by
// CrossValidationResult.Validate if the designated constraints aren't met.
type CrossValidationResultValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CrossValidationResultValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CrossValidationResultValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CrossValidationResultValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CrossValidationResultValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CrossValidationResultValidationError) ErrorName() string {
	return "CrossValidationResultValidationError"
}

// Error satisfies the builtin error interface
func (e CrossValidationResultValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCrossValidationResult.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CrossValidationResultValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CrossValidationResultValidationError{}

// Validate checks the field values on CrossValidationManualReviewInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CrossValidationManualReviewInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CrossValidationManualReviewInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CrossValidationManualReviewInfoMultiError, or nil if none found.
func (m *CrossValidationManualReviewInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CrossValidationManualReviewInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AgentEmail

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetReviewedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CrossValidationManualReviewInfoValidationError{
					field:  "ReviewedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CrossValidationManualReviewInfoValidationError{
					field:  "ReviewedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReviewedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CrossValidationManualReviewInfoValidationError{
				field:  "ReviewedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CrossValidationManualReviewInfoMultiError(errors)
	}

	return nil
}

// CrossValidationManualReviewInfoMultiError is an error wrapping multiple
// validation errors returned by CrossValidationManualReviewInfo.ValidateAll()
// if the designated constraints aren't met.
type CrossValidationManualReviewInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CrossValidationManualReviewInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CrossValidationManualReviewInfoMultiError) AllErrors() []error { return m }

// CrossValidationManualReviewInfoValidationError is the validation error
// returned by CrossValidationManualReviewInfo.Validate if the designated
// constraints aren't met.
type CrossValidationManualReviewInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CrossValidationManualReviewInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CrossValidationManualReviewInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CrossValidationManualReviewInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CrossValidationManualReviewInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CrossValidationManualReviewInfoValidationError) ErrorName() string {
	return "CrossValidationManualReviewInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CrossValidationManualReviewInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCrossValidationManualReviewInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CrossValidationManualReviewInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CrossValidationManualReviewInfoValidationError{}

// Validate checks the field values on CountryIdVerificationMetadata with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CountryIdVerificationMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CountryIdVerificationMetadata with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CountryIdVerificationMetadataMultiError, or nil if none found.
func (m *CountryIdVerificationMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *CountryIdVerificationMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DocExtractClientReqId

	if len(errors) > 0 {
		return CountryIdVerificationMetadataMultiError(errors)
	}

	return nil
}

// CountryIdVerificationMetadataMultiError is an error wrapping multiple
// validation errors returned by CountryIdVerificationMetadata.ValidateAll()
// if the designated constraints aren't met.
type CountryIdVerificationMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CountryIdVerificationMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CountryIdVerificationMetadataMultiError) AllErrors() []error { return m }

// CountryIdVerificationMetadataValidationError is the validation error
// returned by CountryIdVerificationMetadata.Validate if the designated
// constraints aren't met.
type CountryIdVerificationMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CountryIdVerificationMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CountryIdVerificationMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CountryIdVerificationMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CountryIdVerificationMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CountryIdVerificationMetadataValidationError) ErrorName() string {
	return "CountryIdVerificationMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e CountryIdVerificationMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCountryIdVerificationMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CountryIdVerificationMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CountryIdVerificationMetadataValidationError{}

// Validate checks the field values on FeatureIntentSelectionInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FeatureIntentSelectionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeatureIntentSelectionInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeatureIntentSelectionInfoMultiError, or nil if none found.
func (m *FeatureIntentSelectionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FeatureIntentSelectionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SelectedAsHardIntent

	if len(errors) > 0 {
		return FeatureIntentSelectionInfoMultiError(errors)
	}

	return nil
}

// FeatureIntentSelectionInfoMultiError is an error wrapping multiple
// validation errors returned by FeatureIntentSelectionInfo.ValidateAll() if
// the designated constraints aren't met.
type FeatureIntentSelectionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeatureIntentSelectionInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeatureIntentSelectionInfoMultiError) AllErrors() []error { return m }

// FeatureIntentSelectionInfoValidationError is the validation error returned
// by FeatureIntentSelectionInfo.Validate if the designated constraints aren't met.
type FeatureIntentSelectionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeatureIntentSelectionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeatureIntentSelectionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeatureIntentSelectionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeatureIntentSelectionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeatureIntentSelectionInfoValidationError) ErrorName() string {
	return "FeatureIntentSelectionInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FeatureIntentSelectionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeatureIntentSelectionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeatureIntentSelectionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeatureIntentSelectionInfoValidationError{}

// Validate checks the field values on FeatureEligibility with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FeatureEligibility) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeatureEligibility with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeatureEligibilityMultiError, or nil if none found.
func (m *FeatureEligibility) ValidateAll() error {
	return m.validate(true)
}

func (m *FeatureEligibility) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetAdditionalDataRequired()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureEligibilityValidationError{
					field:  "AdditionalDataRequired",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureEligibilityValidationError{
					field:  "AdditionalDataRequired",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDataRequired()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureEligibilityValidationError{
				field:  "AdditionalDataRequired",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FeatureEligibilityMultiError(errors)
	}

	return nil
}

// FeatureEligibilityMultiError is an error wrapping multiple validation errors
// returned by FeatureEligibility.ValidateAll() if the designated constraints
// aren't met.
type FeatureEligibilityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeatureEligibilityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeatureEligibilityMultiError) AllErrors() []error { return m }

// FeatureEligibilityValidationError is the validation error returned by
// FeatureEligibility.Validate if the designated constraints aren't met.
type FeatureEligibilityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeatureEligibilityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeatureEligibilityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeatureEligibilityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeatureEligibilityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeatureEligibilityValidationError) ErrorName() string {
	return "FeatureEligibilityValidationError"
}

// Error satisfies the builtin error interface
func (e FeatureEligibilityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeatureEligibility.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeatureEligibilityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeatureEligibilityValidationError{}

// Validate checks the field values on FeatureLifecycle with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FeatureLifecycle) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeatureLifecycle with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeatureLifecycleMultiError, or nil if none found.
func (m *FeatureLifecycle) ValidateAll() error {
	return m.validate(true)
}

func (m *FeatureLifecycle) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Feature

	if all {
		switch v := interface{}(m.GetIntentSelectionInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureLifecycleValidationError{
					field:  "IntentSelectionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureLifecycleValidationError{
					field:  "IntentSelectionInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIntentSelectionInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureLifecycleValidationError{
				field:  "IntentSelectionInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEligibilityStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureLifecycleValidationError{
					field:  "EligibilityStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureLifecycleValidationError{
					field:  "EligibilityStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEligibilityStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureLifecycleValidationError{
				field:  "EligibilityStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActivationStatus

	if len(errors) > 0 {
		return FeatureLifecycleMultiError(errors)
	}

	return nil
}

// FeatureLifecycleMultiError is an error wrapping multiple validation errors
// returned by FeatureLifecycle.ValidateAll() if the designated constraints
// aren't met.
type FeatureLifecycleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeatureLifecycleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeatureLifecycleMultiError) AllErrors() []error { return m }

// FeatureLifecycleValidationError is the validation error returned by
// FeatureLifecycle.Validate if the designated constraints aren't met.
type FeatureLifecycleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeatureLifecycleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeatureLifecycleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeatureLifecycleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeatureLifecycleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeatureLifecycleValidationError) ErrorName() string { return "FeatureLifecycleValidationError" }

// Error satisfies the builtin error interface
func (e FeatureLifecycleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeatureLifecycle.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeatureLifecycleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeatureLifecycleValidationError{}

// Validate checks the field values on
// PassportVerificationMetadata_VerificationVendorAPIResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PassportVerificationMetadata_VerificationVendorAPIResults) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// PassportVerificationMetadata_VerificationVendorAPIResults with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PassportVerificationMetadata_VerificationVendorAPIResultsMultiError, or nil
// if none found.
func (m *PassportVerificationMetadata_VerificationVendorAPIResults) ValidateAll() error {
	return m.validate(true)
}

func (m *PassportVerificationMetadata_VerificationVendorAPIResults) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PassportNumberMatch

	// no validation rules for UserNameMatch

	// no validation rules for DateOfIssueMatch

	if len(errors) > 0 {
		return PassportVerificationMetadata_VerificationVendorAPIResultsMultiError(errors)
	}

	return nil
}

// PassportVerificationMetadata_VerificationVendorAPIResultsMultiError is an
// error wrapping multiple validation errors returned by
// PassportVerificationMetadata_VerificationVendorAPIResults.ValidateAll() if
// the designated constraints aren't met.
type PassportVerificationMetadata_VerificationVendorAPIResultsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PassportVerificationMetadata_VerificationVendorAPIResultsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PassportVerificationMetadata_VerificationVendorAPIResultsMultiError) AllErrors() []error {
	return m
}

// PassportVerificationMetadata_VerificationVendorAPIResultsValidationError is
// the validation error returned by
// PassportVerificationMetadata_VerificationVendorAPIResults.Validate if the
// designated constraints aren't met.
type PassportVerificationMetadata_VerificationVendorAPIResultsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PassportVerificationMetadata_VerificationVendorAPIResultsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e PassportVerificationMetadata_VerificationVendorAPIResultsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e PassportVerificationMetadata_VerificationVendorAPIResultsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e PassportVerificationMetadata_VerificationVendorAPIResultsValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e PassportVerificationMetadata_VerificationVendorAPIResultsValidationError) ErrorName() string {
	return "PassportVerificationMetadata_VerificationVendorAPIResultsValidationError"
}

// Error satisfies the builtin error interface
func (e PassportVerificationMetadata_VerificationVendorAPIResultsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPassportVerificationMetadata_VerificationVendorAPIResults.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PassportVerificationMetadata_VerificationVendorAPIResultsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PassportVerificationMetadata_VerificationVendorAPIResultsValidationError{}

// Validate checks the field values on
// FeatureEligibility_AdditionalDataRequired with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FeatureEligibility_AdditionalDataRequired) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FeatureEligibility_AdditionalDataRequired with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// FeatureEligibility_AdditionalDataRequiredMultiError, or nil if none found.
func (m *FeatureEligibility_AdditionalDataRequired) ValidateAll() error {
	return m.validate(true)
}

func (m *FeatureEligibility_AdditionalDataRequired) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FeatureEligibility_AdditionalDataRequiredValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FeatureEligibility_AdditionalDataRequiredValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FeatureEligibility_AdditionalDataRequiredValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FeatureEligibility_AdditionalDataRequiredMultiError(errors)
	}

	return nil
}

// FeatureEligibility_AdditionalDataRequiredMultiError is an error wrapping
// multiple validation errors returned by
// FeatureEligibility_AdditionalDataRequired.ValidateAll() if the designated
// constraints aren't met.
type FeatureEligibility_AdditionalDataRequiredMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeatureEligibility_AdditionalDataRequiredMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeatureEligibility_AdditionalDataRequiredMultiError) AllErrors() []error { return m }

// FeatureEligibility_AdditionalDataRequiredValidationError is the validation
// error returned by FeatureEligibility_AdditionalDataRequired.Validate if the
// designated constraints aren't met.
type FeatureEligibility_AdditionalDataRequiredValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeatureEligibility_AdditionalDataRequiredValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeatureEligibility_AdditionalDataRequiredValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeatureEligibility_AdditionalDataRequiredValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeatureEligibility_AdditionalDataRequiredValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeatureEligibility_AdditionalDataRequiredValidationError) ErrorName() string {
	return "FeatureEligibility_AdditionalDataRequiredValidationError"
}

// Error satisfies the builtin error interface
func (e FeatureEligibility_AdditionalDataRequiredValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeatureEligibility_AdditionalDataRequired.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeatureEligibility_AdditionalDataRequiredValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeatureEligibility_AdditionalDataRequiredValidationError{}
