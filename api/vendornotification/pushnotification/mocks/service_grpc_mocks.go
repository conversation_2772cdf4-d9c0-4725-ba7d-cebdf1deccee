// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendornotification/pushnotification/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	pushnotification "github.com/epifi/gamma/api/vendors/pushnotification"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockPushNotificationClient is a mock of PushNotificationClient interface.
type MockPushNotificationClient struct {
	ctrl     *gomock.Controller
	recorder *MockPushNotificationClientMockRecorder
}

// MockPushNotificationClientMockRecorder is the mock recorder for MockPushNotificationClient.
type MockPushNotificationClientMockRecorder struct {
	mock *MockPushNotificationClient
}

// NewMockPushNotificationClient creates a new mock instance.
func NewMockPushNotificationClient(ctrl *gomock.Controller) *MockPushNotificationClient {
	mock := &MockPushNotificationClient{ctrl: ctrl}
	mock.recorder = &MockPushNotificationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPushNotificationClient) EXPECT() *MockPushNotificationClientMockRecorder {
	return m.recorder
}

// SendPushNotification mocks base method.
func (m *MockPushNotificationClient) SendPushNotification(ctx context.Context, in *pushnotification.SendPushNotificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SendPushNotification", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPushNotification indicates an expected call of SendPushNotification.
func (mr *MockPushNotificationClientMockRecorder) SendPushNotification(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPushNotification", reflect.TypeOf((*MockPushNotificationClient)(nil).SendPushNotification), varargs...)
}

// MockPushNotificationServer is a mock of PushNotificationServer interface.
type MockPushNotificationServer struct {
	ctrl     *gomock.Controller
	recorder *MockPushNotificationServerMockRecorder
}

// MockPushNotificationServerMockRecorder is the mock recorder for MockPushNotificationServer.
type MockPushNotificationServerMockRecorder struct {
	mock *MockPushNotificationServer
}

// NewMockPushNotificationServer creates a new mock instance.
func NewMockPushNotificationServer(ctrl *gomock.Controller) *MockPushNotificationServer {
	mock := &MockPushNotificationServer{ctrl: ctrl}
	mock.recorder = &MockPushNotificationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPushNotificationServer) EXPECT() *MockPushNotificationServerMockRecorder {
	return m.recorder
}

// SendPushNotification mocks base method.
func (m *MockPushNotificationServer) SendPushNotification(arg0 context.Context, arg1 *pushnotification.SendPushNotificationRequest) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendPushNotification", arg0, arg1)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendPushNotification indicates an expected call of SendPushNotification.
func (mr *MockPushNotificationServerMockRecorder) SendPushNotification(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendPushNotification", reflect.TypeOf((*MockPushNotificationServer)(nil).SendPushNotification), arg0, arg1)
}

// MockUnsafePushNotificationServer is a mock of UnsafePushNotificationServer interface.
type MockUnsafePushNotificationServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafePushNotificationServerMockRecorder
}

// MockUnsafePushNotificationServerMockRecorder is the mock recorder for MockUnsafePushNotificationServer.
type MockUnsafePushNotificationServerMockRecorder struct {
	mock *MockUnsafePushNotificationServer
}

// NewMockUnsafePushNotificationServer creates a new mock instance.
func NewMockUnsafePushNotificationServer(ctrl *gomock.Controller) *MockUnsafePushNotificationServer {
	mock := &MockUnsafePushNotificationServer{ctrl: ctrl}
	mock.recorder = &MockUnsafePushNotificationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafePushNotificationServer) EXPECT() *MockUnsafePushNotificationServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedPushNotificationServer mocks base method.
func (m *MockUnsafePushNotificationServer) mustEmbedUnimplementedPushNotificationServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPushNotificationServer")
}

// mustEmbedUnimplementedPushNotificationServer indicates an expected call of mustEmbedUnimplementedPushNotificationServer.
func (mr *MockUnsafePushNotificationServerMockRecorder) mustEmbedUnimplementedPushNotificationServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPushNotificationServer", reflect.TypeOf((*MockUnsafePushNotificationServer)(nil).mustEmbedUnimplementedPushNotificationServer))
}
