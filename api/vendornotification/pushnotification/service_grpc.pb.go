// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendornotification/pushnotification/service.proto

package pushnotification

import (
	context "context"
	pushnotification "github.com/epifi/gamma/api/vendors/pushnotification"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	PushNotification_SendPushNotification_FullMethodName = "/pushnotification.PushNotification/SendPushNotification"
)

// PushNotificationClient is the client API for PushNotification service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PushNotificationClient interface {
	// SendPushNotification is used to receive notifications from vendors to send app PNs,
	// particularly where we do not directly control the user flow e.g. flow is controlled by Saven SDK
	SendPushNotification(ctx context.Context, in *pushnotification.SendPushNotificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type pushNotificationClient struct {
	cc grpc.ClientConnInterface
}

func NewPushNotificationClient(cc grpc.ClientConnInterface) PushNotificationClient {
	return &pushNotificationClient{cc}
}

func (c *pushNotificationClient) SendPushNotification(ctx context.Context, in *pushnotification.SendPushNotificationRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PushNotification_SendPushNotification_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushNotificationServer is the server API for PushNotification service.
// All implementations should embed UnimplementedPushNotificationServer
// for forward compatibility
type PushNotificationServer interface {
	// SendPushNotification is used to receive notifications from vendors to send app PNs,
	// particularly where we do not directly control the user flow e.g. flow is controlled by Saven SDK
	SendPushNotification(context.Context, *pushnotification.SendPushNotificationRequest) (*emptypb.Empty, error)
}

// UnimplementedPushNotificationServer should be embedded to have forward compatible implementations.
type UnimplementedPushNotificationServer struct {
}

func (UnimplementedPushNotificationServer) SendPushNotification(context.Context, *pushnotification.SendPushNotificationRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendPushNotification not implemented")
}

// UnsafePushNotificationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PushNotificationServer will
// result in compilation errors.
type UnsafePushNotificationServer interface {
	mustEmbedUnimplementedPushNotificationServer()
}

func RegisterPushNotificationServer(s grpc.ServiceRegistrar, srv PushNotificationServer) {
	s.RegisterService(&PushNotification_ServiceDesc, srv)
}

func _PushNotification_SendPushNotification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushnotification.SendPushNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).SendPushNotification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PushNotification_SendPushNotification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).SendPushNotification(ctx, req.(*pushnotification.SendPushNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PushNotification_ServiceDesc is the grpc.ServiceDesc for PushNotification service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PushNotification_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pushnotification.PushNotification",
	HandlerType: (*PushNotificationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendPushNotification",
			Handler:    _PushNotification_SendPushNotification_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendornotification/pushnotification/service.proto",
}
