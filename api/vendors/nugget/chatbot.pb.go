// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/nugget/chatbot.proto

package nugget

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// FetchChatbotAccessTokenRequest represents the request payload for Nugget's fetch access token API
type FetchChatbotAccessTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Uid         string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	ClientId    int32  `protobuf:"varint,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	Platform    string `protobuf:"bytes,3,opt,name=platform,proto3" json:"platform,omitempty"`
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Email       string `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	PhoneNumber string `protobuf:"bytes,6,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	PhotoUrl    string `protobuf:"bytes,7,opt,name=photo_url,json=photoUrl,proto3" json:"photo_url,omitempty"`
}

func (x *FetchChatbotAccessTokenRequest) Reset() {
	*x = FetchChatbotAccessTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_nugget_chatbot_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchChatbotAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchChatbotAccessTokenRequest) ProtoMessage() {}

func (x *FetchChatbotAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_nugget_chatbot_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchChatbotAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*FetchChatbotAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_nugget_chatbot_proto_rawDescGZIP(), []int{0}
}

func (x *FetchChatbotAccessTokenRequest) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *FetchChatbotAccessTokenRequest) GetClientId() int32 {
	if x != nil {
		return x.ClientId
	}
	return 0
}

func (x *FetchChatbotAccessTokenRequest) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *FetchChatbotAccessTokenRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *FetchChatbotAccessTokenRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *FetchChatbotAccessTokenRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *FetchChatbotAccessTokenRequest) GetPhotoUrl() string {
	if x != nil {
		return x.PhotoUrl
	}
	return ""
}

// FetchChatbotAccessTokenResponse represents the response from Nugget's fetch access token API
type FetchChatbotAccessTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Success     bool   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	AccessToken string `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
}

func (x *FetchChatbotAccessTokenResponse) Reset() {
	*x = FetchChatbotAccessTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_nugget_chatbot_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchChatbotAccessTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchChatbotAccessTokenResponse) ProtoMessage() {}

func (x *FetchChatbotAccessTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_nugget_chatbot_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchChatbotAccessTokenResponse.ProtoReflect.Descriptor instead.
func (*FetchChatbotAccessTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_nugget_chatbot_proto_rawDescGZIP(), []int{1}
}

func (x *FetchChatbotAccessTokenResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *FetchChatbotAccessTokenResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

var File_api_vendors_nugget_chatbot_proto protoreflect.FileDescriptor

var file_api_vendors_nugget_chatbot_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6e, 0x75,
	0x67, 0x67, 0x65, 0x74, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x6e, 0x75, 0x67, 0x67,
	0x65, 0x74, 0x22, 0xe4, 0x01, 0x0a, 0x1e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x43, 0x68, 0x61, 0x74,
	0x62, 0x6f, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x68, 0x6f, 0x74, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x55, 0x72, 0x6c, 0x22, 0x5e, 0x0a, 0x1f, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x2b, 0x5a, 0x29, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f,
	0x6e, 0x75, 0x67, 0x67, 0x65, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_nugget_chatbot_proto_rawDescOnce sync.Once
	file_api_vendors_nugget_chatbot_proto_rawDescData = file_api_vendors_nugget_chatbot_proto_rawDesc
)

func file_api_vendors_nugget_chatbot_proto_rawDescGZIP() []byte {
	file_api_vendors_nugget_chatbot_proto_rawDescOnce.Do(func() {
		file_api_vendors_nugget_chatbot_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_nugget_chatbot_proto_rawDescData)
	})
	return file_api_vendors_nugget_chatbot_proto_rawDescData
}

var file_api_vendors_nugget_chatbot_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_vendors_nugget_chatbot_proto_goTypes = []interface{}{
	(*FetchChatbotAccessTokenRequest)(nil),  // 0: vendors.nugget.FetchChatbotAccessTokenRequest
	(*FetchChatbotAccessTokenResponse)(nil), // 1: vendors.nugget.FetchChatbotAccessTokenResponse
}
var file_api_vendors_nugget_chatbot_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendors_nugget_chatbot_proto_init() }
func file_api_vendors_nugget_chatbot_proto_init() {
	if File_api_vendors_nugget_chatbot_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_nugget_chatbot_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchChatbotAccessTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_nugget_chatbot_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchChatbotAccessTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_nugget_chatbot_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_nugget_chatbot_proto_goTypes,
		DependencyIndexes: file_api_vendors_nugget_chatbot_proto_depIdxs,
		MessageInfos:      file_api_vendors_nugget_chatbot_proto_msgTypes,
	}.Build()
	File_api_vendors_nugget_chatbot_proto = out.File
	file_api_vendors_nugget_chatbot_proto_rawDesc = nil
	file_api_vendors_nugget_chatbot_proto_goTypes = nil
	file_api_vendors_nugget_chatbot_proto_depIdxs = nil
}
