// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/nugget/chatbot.proto

package nugget

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on FetchChatbotAccessTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchChatbotAccessTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchChatbotAccessTokenRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchChatbotAccessTokenRequestMultiError, or nil if none found.
func (m *FetchChatbotAccessTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchChatbotAccessTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for ClientId

	// no validation rules for Platform

	// no validation rules for DisplayName

	// no validation rules for Email

	// no validation rules for PhoneNumber

	// no validation rules for PhotoUrl

	if len(errors) > 0 {
		return FetchChatbotAccessTokenRequestMultiError(errors)
	}

	return nil
}

// FetchChatbotAccessTokenRequestMultiError is an error wrapping multiple
// validation errors returned by FetchChatbotAccessTokenRequest.ValidateAll()
// if the designated constraints aren't met.
type FetchChatbotAccessTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchChatbotAccessTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchChatbotAccessTokenRequestMultiError) AllErrors() []error { return m }

// FetchChatbotAccessTokenRequestValidationError is the validation error
// returned by FetchChatbotAccessTokenRequest.Validate if the designated
// constraints aren't met.
type FetchChatbotAccessTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchChatbotAccessTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchChatbotAccessTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchChatbotAccessTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchChatbotAccessTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchChatbotAccessTokenRequestValidationError) ErrorName() string {
	return "FetchChatbotAccessTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchChatbotAccessTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchChatbotAccessTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchChatbotAccessTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchChatbotAccessTokenRequestValidationError{}

// Validate checks the field values on FetchChatbotAccessTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchChatbotAccessTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchChatbotAccessTokenResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FetchChatbotAccessTokenResponseMultiError, or nil if none found.
func (m *FetchChatbotAccessTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchChatbotAccessTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Success

	// no validation rules for AccessToken

	if len(errors) > 0 {
		return FetchChatbotAccessTokenResponseMultiError(errors)
	}

	return nil
}

// FetchChatbotAccessTokenResponseMultiError is an error wrapping multiple
// validation errors returned by FetchChatbotAccessTokenResponse.ValidateAll()
// if the designated constraints aren't met.
type FetchChatbotAccessTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchChatbotAccessTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchChatbotAccessTokenResponseMultiError) AllErrors() []error { return m }

// FetchChatbotAccessTokenResponseValidationError is the validation error
// returned by FetchChatbotAccessTokenResponse.Validate if the designated
// constraints aren't met.
type FetchChatbotAccessTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchChatbotAccessTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchChatbotAccessTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchChatbotAccessTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchChatbotAccessTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchChatbotAccessTokenResponseValidationError) ErrorName() string {
	return "FetchChatbotAccessTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchChatbotAccessTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchChatbotAccessTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchChatbotAccessTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchChatbotAccessTokenResponseValidationError{}
