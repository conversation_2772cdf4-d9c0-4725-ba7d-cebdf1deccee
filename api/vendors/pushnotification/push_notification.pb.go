// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/pushnotification/push_notification.proto

package pushnotification

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SendPushNotificationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// JWT token containing a serialized SendPushNotificationRequestPayload as the 'request_payload' claim.
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *SendPushNotificationRequest) Reset() {
	*x = SendPushNotificationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_pushnotification_push_notification_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendPushNotificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushNotificationRequest) ProtoMessage() {}

func (x *SendPushNotificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_pushnotification_push_notification_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushNotificationRequest.ProtoReflect.Descriptor instead.
func (*SendPushNotificationRequest) Descriptor() ([]byte, []int) {
	return file_api_vendors_pushnotification_push_notification_proto_rawDescGZIP(), []int{0}
}

func (x *SendPushNotificationRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type SendPushNotificationRequestPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique user identifier at vendor's end
	VendorInternalUserId string `protobuf:"bytes,1,opt,name=vendor_internal_user_id,json=internalUserId,proto3" json:"vendor_internal_user_id,omitempty"`
	// Name of the vendor (e.g., SAVEN, M2P, etc.)
	Vendor string `protobuf:"bytes,2,opt,name=vendor,proto3" json:"vendor,omitempty"`
	// Push notification content
	Content *PushNotificationContent `protobuf:"bytes,3,opt,name=content,proto3" json:"content,omitempty"`
	// Specific notification ID for tracking/deduplication
	NotificationId string `protobuf:"bytes,4,opt,name=notification_id,json=notificationId,proto3" json:"notification_id,omitempty"`
	// Timestamp when the notification was created/originated at vendor's end in RFC3339 format
	CreatedAt string `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Optional: Expiry time for the notification in RFC3339 format
	ExpiresAt string `protobuf:"bytes,6,opt,name=expires_at,json=expiresAt,proto3" json:"expires_at,omitempty"`
}

func (x *SendPushNotificationRequestPayload) Reset() {
	*x = SendPushNotificationRequestPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_pushnotification_push_notification_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendPushNotificationRequestPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPushNotificationRequestPayload) ProtoMessage() {}

func (x *SendPushNotificationRequestPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_pushnotification_push_notification_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPushNotificationRequestPayload.ProtoReflect.Descriptor instead.
func (*SendPushNotificationRequestPayload) Descriptor() ([]byte, []int) {
	return file_api_vendors_pushnotification_push_notification_proto_rawDescGZIP(), []int{1}
}

func (x *SendPushNotificationRequestPayload) GetVendorInternalUserId() string {
	if x != nil {
		return x.VendorInternalUserId
	}
	return ""
}

func (x *SendPushNotificationRequestPayload) GetVendor() string {
	if x != nil {
		return x.Vendor
	}
	return ""
}

func (x *SendPushNotificationRequestPayload) GetContent() *PushNotificationContent {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *SendPushNotificationRequestPayload) GetNotificationId() string {
	if x != nil {
		return x.NotificationId
	}
	return ""
}

func (x *SendPushNotificationRequestPayload) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *SendPushNotificationRequestPayload) GetExpiresAt() string {
	if x != nil {
		return x.ExpiresAt
	}
	return ""
}

type PushNotificationContent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Notification title (required)
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// Notification subtitle/body (required)
	Subtitle string `protobuf:"bytes,2,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// Optional: Icon URL for the notification
	// Defaults to Fi icon url
	IconUrl string `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// Optional: Image URL for rich notifications
	// Defaults to no image
	ImageUrl string `protobuf:"bytes,4,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	// Optional: Deep link for navigation
	// Defaults to deeplink mapped to the vendor if exists, else Home deeplink
	Deeplink string `protobuf:"bytes,5,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *PushNotificationContent) Reset() {
	*x = PushNotificationContent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_pushnotification_push_notification_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushNotificationContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushNotificationContent) ProtoMessage() {}

func (x *PushNotificationContent) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_pushnotification_push_notification_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushNotificationContent.ProtoReflect.Descriptor instead.
func (*PushNotificationContent) Descriptor() ([]byte, []int) {
	return file_api_vendors_pushnotification_push_notification_proto_rawDescGZIP(), []int{2}
}

func (x *PushNotificationContent) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *PushNotificationContent) GetSubtitle() string {
	if x != nil {
		return x.Subtitle
	}
	return ""
}

func (x *PushNotificationContent) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *PushNotificationContent) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *PushNotificationContent) GetDeeplink() string {
	if x != nil {
		return x.Deeplink
	}
	return ""
}

var File_api_vendors_pushnotification_push_notification_proto protoreflect.FileDescriptor

var file_api_vendors_pushnotification_push_notification_proto_rawDesc = []byte{
	0x0a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x70, 0x75,
	0x73, 0x68, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x70,
	0x75, 0x73, 0x68, 0x5f, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x33, 0x0a, 0x1b, 0x53, 0x65, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa5, 0x02, 0x0a, 0x22, 0x53, 0x65,
	0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x12, 0x2f, 0x0a, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x12, 0x4f, 0x0a, 0x07, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x6e, 0x6f, 0x74,
	0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0e, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x5f, 0x61, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41,
	0x74, 0x22, 0x9f, 0x01, 0x0a, 0x17, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x69, 0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x42, 0x6a, 0x0a, 0x33, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x6e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x70,
	0x75, 0x73, 0x68, 0x6e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendors_pushnotification_push_notification_proto_rawDescOnce sync.Once
	file_api_vendors_pushnotification_push_notification_proto_rawDescData = file_api_vendors_pushnotification_push_notification_proto_rawDesc
)

func file_api_vendors_pushnotification_push_notification_proto_rawDescGZIP() []byte {
	file_api_vendors_pushnotification_push_notification_proto_rawDescOnce.Do(func() {
		file_api_vendors_pushnotification_push_notification_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_pushnotification_push_notification_proto_rawDescData)
	})
	return file_api_vendors_pushnotification_push_notification_proto_rawDescData
}

var file_api_vendors_pushnotification_push_notification_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_vendors_pushnotification_push_notification_proto_goTypes = []interface{}{
	(*SendPushNotificationRequest)(nil),        // 0: api.vendors.pushnotification.SendPushNotificationRequest
	(*SendPushNotificationRequestPayload)(nil), // 1: api.vendors.pushnotification.SendPushNotificationRequestPayload
	(*PushNotificationContent)(nil),            // 2: api.vendors.pushnotification.PushNotificationContent
}
var file_api_vendors_pushnotification_push_notification_proto_depIdxs = []int32{
	2, // 0: api.vendors.pushnotification.SendPushNotificationRequestPayload.content:type_name -> api.vendors.pushnotification.PushNotificationContent
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_vendors_pushnotification_push_notification_proto_init() }
func file_api_vendors_pushnotification_push_notification_proto_init() {
	if File_api_vendors_pushnotification_push_notification_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_pushnotification_push_notification_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendPushNotificationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_pushnotification_push_notification_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendPushNotificationRequestPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_pushnotification_push_notification_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushNotificationContent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_pushnotification_push_notification_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_pushnotification_push_notification_proto_goTypes,
		DependencyIndexes: file_api_vendors_pushnotification_push_notification_proto_depIdxs,
		MessageInfos:      file_api_vendors_pushnotification_push_notification_proto_msgTypes,
	}.Build()
	File_api_vendors_pushnotification_push_notification_proto = out.File
	file_api_vendors_pushnotification_push_notification_proto_rawDesc = nil
	file_api_vendors_pushnotification_push_notification_proto_goTypes = nil
	file_api_vendors_pushnotification_push_notification_proto_depIdxs = nil
}
