// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/pushnotification/push_notification.proto

package pushnotification

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SendPushNotificationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendPushNotificationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendPushNotificationRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendPushNotificationRequestMultiError, or nil if none found.
func (m *SendPushNotificationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendPushNotificationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	if len(errors) > 0 {
		return SendPushNotificationRequestMultiError(errors)
	}

	return nil
}

// SendPushNotificationRequestMultiError is an error wrapping multiple
// validation errors returned by SendPushNotificationRequest.ValidateAll() if
// the designated constraints aren't met.
type SendPushNotificationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendPushNotificationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendPushNotificationRequestMultiError) AllErrors() []error { return m }

// SendPushNotificationRequestValidationError is the validation error returned
// by SendPushNotificationRequest.Validate if the designated constraints
// aren't met.
type SendPushNotificationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendPushNotificationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendPushNotificationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendPushNotificationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendPushNotificationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendPushNotificationRequestValidationError) ErrorName() string {
	return "SendPushNotificationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendPushNotificationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendPushNotificationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendPushNotificationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendPushNotificationRequestValidationError{}

// Validate checks the field values on SendPushNotificationRequestPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SendPushNotificationRequestPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendPushNotificationRequestPayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SendPushNotificationRequestPayloadMultiError, or nil if none found.
func (m *SendPushNotificationRequestPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *SendPushNotificationRequestPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorInternalUserId

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendPushNotificationRequestPayloadValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendPushNotificationRequestPayloadValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendPushNotificationRequestPayloadValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for NotificationId

	// no validation rules for CreatedAt

	// no validation rules for ExpiresAt

	if len(errors) > 0 {
		return SendPushNotificationRequestPayloadMultiError(errors)
	}

	return nil
}

// SendPushNotificationRequestPayloadMultiError is an error wrapping multiple
// validation errors returned by
// SendPushNotificationRequestPayload.ValidateAll() if the designated
// constraints aren't met.
type SendPushNotificationRequestPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendPushNotificationRequestPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendPushNotificationRequestPayloadMultiError) AllErrors() []error { return m }

// SendPushNotificationRequestPayloadValidationError is the validation error
// returned by SendPushNotificationRequestPayload.Validate if the designated
// constraints aren't met.
type SendPushNotificationRequestPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendPushNotificationRequestPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendPushNotificationRequestPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendPushNotificationRequestPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendPushNotificationRequestPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendPushNotificationRequestPayloadValidationError) ErrorName() string {
	return "SendPushNotificationRequestPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e SendPushNotificationRequestPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendPushNotificationRequestPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendPushNotificationRequestPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendPushNotificationRequestPayloadValidationError{}

// Validate checks the field values on PushNotificationContent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PushNotificationContent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PushNotificationContent with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PushNotificationContentMultiError, or nil if none found.
func (m *PushNotificationContent) ValidateAll() error {
	return m.validate(true)
}

func (m *PushNotificationContent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Title

	// no validation rules for Subtitle

	// no validation rules for IconUrl

	// no validation rules for ImageUrl

	// no validation rules for Deeplink

	if len(errors) > 0 {
		return PushNotificationContentMultiError(errors)
	}

	return nil
}

// PushNotificationContentMultiError is an error wrapping multiple validation
// errors returned by PushNotificationContent.ValidateAll() if the designated
// constraints aren't met.
type PushNotificationContentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PushNotificationContentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PushNotificationContentMultiError) AllErrors() []error { return m }

// PushNotificationContentValidationError is the validation error returned by
// PushNotificationContent.Validate if the designated constraints aren't met.
type PushNotificationContentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PushNotificationContentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PushNotificationContentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PushNotificationContentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PushNotificationContentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PushNotificationContentValidationError) ErrorName() string {
	return "PushNotificationContentValidationError"
}

// Error satisfies the builtin error interface
func (e PushNotificationContentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPushNotificationContent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PushNotificationContentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PushNotificationContentValidationError{}
