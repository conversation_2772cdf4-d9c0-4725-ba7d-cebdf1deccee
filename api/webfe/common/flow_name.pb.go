// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/webfe/common/flow_name.proto

package common

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FlowName int32

const (
	FlowName_FLOW_NAME_UNSPECIFIED           FlowName = 0
	FlowName_FLOW_NAME_LOANS_ELIGIBILITY     FlowName = 1
	FlowName_FLOW_NAME_CREDIT_SCORE_ANALYSER FlowName = 2
	FlowName_FLOW_NAME_CC_ELIGIBILITY_CHECK  FlowName = 3
	FlowName_FLOW_NAME_NET_WORTH_MCP_AUTH    FlowName = 4
)

// Enum value maps for FlowName.
var (
	FlowName_name = map[int32]string{
		0: "FLOW_NAME_UNSPECIFIED",
		1: "FLOW_NAME_LOANS_ELIGIBILITY",
		2: "FLOW_NAME_CREDIT_SCORE_ANALYSER",
		3: "FLOW_NAME_CC_ELIGIBILITY_CHECK",
		4: "FLOW_NAME_NET_WORTH_MCP_AUTH",
	}
	FlowName_value = map[string]int32{
		"FLOW_NAME_UNSPECIFIED":           0,
		"FLOW_NAME_LOANS_ELIGIBILITY":     1,
		"FLOW_NAME_CREDIT_SCORE_ANALYSER": 2,
		"FLOW_NAME_CC_ELIGIBILITY_CHECK":  3,
		"FLOW_NAME_NET_WORTH_MCP_AUTH":    4,
	}
)

func (x FlowName) Enum() *FlowName {
	p := new(FlowName)
	*p = x
	return p
}

func (x FlowName) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FlowName) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_common_flow_name_proto_enumTypes[0].Descriptor()
}

func (FlowName) Type() protoreflect.EnumType {
	return &file_api_webfe_common_flow_name_proto_enumTypes[0]
}

func (x FlowName) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FlowName.Descriptor instead.
func (FlowName) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_common_flow_name_proto_rawDescGZIP(), []int{0}
}

var File_api_webfe_common_flow_name_proto protoreflect.FileDescriptor

var file_api_webfe_common_flow_name_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0c, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2a, 0xb1, 0x01, 0x0a, 0x08, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x15, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x4c, 0x4f, 0x57,
	0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x5f, 0x45, 0x4c, 0x49, 0x47,
	0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x4c, 0x4f,
	0x57, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x53, 0x43,
	0x4f, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x02, 0x12, 0x22,
	0x0a, 0x1e, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f, 0x43, 0x43, 0x5f, 0x45,
	0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b,
	0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x5f,
	0x4e, 0x45, 0x54, 0x5f, 0x57, 0x4f, 0x52, 0x54, 0x48, 0x5f, 0x4d, 0x43, 0x50, 0x5f, 0x41, 0x55,
	0x54, 0x48, 0x10, 0x04, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x5a,
	0x27, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66,
	0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_webfe_common_flow_name_proto_rawDescOnce sync.Once
	file_api_webfe_common_flow_name_proto_rawDescData = file_api_webfe_common_flow_name_proto_rawDesc
)

func file_api_webfe_common_flow_name_proto_rawDescGZIP() []byte {
	file_api_webfe_common_flow_name_proto_rawDescOnce.Do(func() {
		file_api_webfe_common_flow_name_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_webfe_common_flow_name_proto_rawDescData)
	})
	return file_api_webfe_common_flow_name_proto_rawDescData
}

var file_api_webfe_common_flow_name_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_webfe_common_flow_name_proto_goTypes = []interface{}{
	(FlowName)(0), // 0: webfe.common.FlowName
}
var file_api_webfe_common_flow_name_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_webfe_common_flow_name_proto_init() }
func file_api_webfe_common_flow_name_proto_init() {
	if File_api_webfe_common_flow_name_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_webfe_common_flow_name_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_webfe_common_flow_name_proto_goTypes,
		DependencyIndexes: file_api_webfe_common_flow_name_proto_depIdxs,
		EnumInfos:         file_api_webfe_common_flow_name_proto_enumTypes,
	}.Build()
	File_api_webfe_common_flow_name_proto = out.File
	file_api_webfe_common_flow_name_proto_rawDesc = nil
	file_api_webfe_common_flow_name_proto_goTypes = nil
	file_api_webfe_common_flow_name_proto_depIdxs = nil
}
