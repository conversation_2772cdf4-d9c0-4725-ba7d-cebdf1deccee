// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/webfe/deeplink_screen_option/screen_options.proto

package deeplink_screen_option

import (
	deeplink_screen_option "github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	common "github.com/epifi/gamma/api/webfe/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WebErrorScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// image url to be displayed
	ImageUrl string `protobuf:"bytes,2,opt,name=image_url,json=imageUrl,proto3" json:"image_url,omitempty"`
	// title to be displayed
	Title string `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	// subtitle to be displayed
	Subtitle string `protobuf:"bytes,4,opt,name=subtitle,proto3" json:"subtitle,omitempty"`
	// button text to be displayed
	ShowRefreshButton bool `protobuf:"varint,5,opt,name=show_refresh_button,json=showRefreshButton,proto3" json:"show_refresh_button,omitempty"`
}

func (x *WebErrorScreenOptions) Reset() {
	*x = WebErrorScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebErrorScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebErrorScreenOptions) ProtoMessage() {}

func (x *WebErrorScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebErrorScreenOptions.ProtoReflect.Descriptor instead.
func (*WebErrorScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescGZIP(), []int{0}
}

func (x *WebErrorScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *WebErrorScreenOptions) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *WebErrorScreenOptions) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *WebErrorScreenOptions) GetSubtitle() string {
	if x != nil {
		return x.Subtitle
	}
	return ""
}

func (x *WebErrorScreenOptions) GetShowRefreshButton() bool {
	if x != nil {
		return x.ShowRefreshButton
	}
	return false
}

type WebPollingScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// common content for all screen options
	Content *CommonOptions `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// flag to show the loader which is intended to be shown for a longer duration
	ShowLongerDurationLoader bool `protobuf:"varint,3,opt,name=show_longer_duration_loader,json=showLongerDurationLoader,proto3" json:"show_longer_duration_loader,omitempty"`
	// flow name to be used for the flow for deciding the rpc to be called
	FlowName common.FlowName `protobuf:"varint,4,opt,name=flow_name,json=flowName,proto3,enum=webfe.common.FlowName" json:"flow_name,omitempty"`
	// request properties to be sent in the request to the rpc
	RequestProperties map[string]string `protobuf:"bytes,5,rep,name=request_properties,json=requestProperties,proto3" json:"request_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *WebPollingScreenOptions) Reset() {
	*x = WebPollingScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebPollingScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebPollingScreenOptions) ProtoMessage() {}

func (x *WebPollingScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebPollingScreenOptions.ProtoReflect.Descriptor instead.
func (*WebPollingScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescGZIP(), []int{1}
}

func (x *WebPollingScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *WebPollingScreenOptions) GetContent() *CommonOptions {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *WebPollingScreenOptions) GetShowLongerDurationLoader() bool {
	if x != nil {
		return x.ShowLongerDurationLoader
	}
	return false
}

func (x *WebPollingScreenOptions) GetFlowName() common.FlowName {
	if x != nil {
		return x.FlowName
	}
	return common.FlowName(0)
}

func (x *WebPollingScreenOptions) GetRequestProperties() map[string]string {
	if x != nil {
		return x.RequestProperties
	}
	return nil
}

type WebPanFormScreenOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// common header for all screen options
	Header *deeplink_screen_option.ScreenOptionHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// common content for all screen options
	Content *CommonOptions `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`
	// extra request properties to be sent in the request to the rpc
	ExtraRequestProperties map[string]string `protobuf:"bytes,3,rep,name=extra_request_properties,json=extraRequestProperties,proto3" json:"extra_request_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *WebPanFormScreenOptions) Reset() {
	*x = WebPanFormScreenOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WebPanFormScreenOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WebPanFormScreenOptions) ProtoMessage() {}

func (x *WebPanFormScreenOptions) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WebPanFormScreenOptions.ProtoReflect.Descriptor instead.
func (*WebPanFormScreenOptions) Descriptor() ([]byte, []int) {
	return file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescGZIP(), []int{2}
}

func (x *WebPanFormScreenOptions) GetHeader() *deeplink_screen_option.ScreenOptionHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *WebPanFormScreenOptions) GetContent() *CommonOptions {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *WebPanFormScreenOptions) GetExtraRequestProperties() map[string]string {
	if x != nil {
		return x.ExtraRequestProperties
	}
	return nil
}

var File_api_webfe_deeplink_screen_option_screen_options_proto protoreflect.FileDescriptor

var file_api_webfe_deeplink_screen_option_screen_options_proto_rawDesc = []byte{
	0x0a, 0x35, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62,
	0x66, 0x65, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f,
	0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2f,
	0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f,
	0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x66, 0x6c, 0x6f,
	0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe6, 0x01, 0x0a,
	0x15, 0x57, 0x65, 0x62, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x75, 0x62,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x72, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x5f, 0x62, 0x75, 0x74, 0x74, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x11, 0x73, 0x68, 0x6f, 0x77, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x42,
	0x75, 0x74, 0x74, 0x6f, 0x6e, 0x22, 0xef, 0x03, 0x0a, 0x17, 0x57, 0x65, 0x62, 0x50, 0x6f, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x49, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3d, 0x0a, 0x1b,
	0x73, 0x68, 0x6f, 0x77, 0x5f, 0x6c, 0x6f, 0x6e, 0x67, 0x65, 0x72, 0x5f, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x18, 0x73, 0x68, 0x6f, 0x77, 0x4c, 0x6f, 0x6e, 0x67, 0x65, 0x72, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x61, 0x64, 0x65, 0x72, 0x12, 0x33, 0x0a, 0x09, 0x66,
	0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x6c,
	0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x7f, 0x0a, 0x12, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x57, 0x65, 0x62, 0x50, 0x6f, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x1a, 0x44, 0x0a, 0x16, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x91, 0x03, 0x0a, 0x17, 0x57, 0x65, 0x62, 0x50,
	0x61, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x4e, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65,
	0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x8f,
	0x01, 0x0a, 0x18, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x55, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x57, 0x65, 0x62, 0x50, 0x61, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x53,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x45, 0x78, 0x74,
	0x72, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x16, 0x65, 0x78, 0x74, 0x72, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x1a, 0x49, 0x0a, 0x1b, 0x45, 0x78, 0x74, 0x72, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x42, 0x74, 0x0a, 0x37, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x01, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescOnce sync.Once
	file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescData = file_api_webfe_deeplink_screen_option_screen_options_proto_rawDesc
)

func file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescGZIP() []byte {
	file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescOnce.Do(func() {
		file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescData)
	})
	return file_api_webfe_deeplink_screen_option_screen_options_proto_rawDescData
}

var file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_webfe_deeplink_screen_option_screen_options_proto_goTypes = []interface{}{
	(*WebErrorScreenOptions)(nil),   // 0: api.webfe.deeplink_screen_option.WebErrorScreenOptions
	(*WebPollingScreenOptions)(nil), // 1: api.webfe.deeplink_screen_option.WebPollingScreenOptions
	(*WebPanFormScreenOptions)(nil), // 2: api.webfe.deeplink_screen_option.WebPanFormScreenOptions
	nil,                             // 3: api.webfe.deeplink_screen_option.WebPollingScreenOptions.RequestPropertiesEntry
	nil,                             // 4: api.webfe.deeplink_screen_option.WebPanFormScreenOptions.ExtraRequestPropertiesEntry
	(*deeplink_screen_option.ScreenOptionHeader)(nil), // 5: api.typesv2.deeplink_screen_option.ScreenOptionHeader
	(*CommonOptions)(nil),                             // 6: api.webfe.deeplink_screen_option.CommonOptions
	(common.FlowName)(0),                              // 7: webfe.common.FlowName
}
var file_api_webfe_deeplink_screen_option_screen_options_proto_depIdxs = []int32{
	5, // 0: api.webfe.deeplink_screen_option.WebErrorScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	5, // 1: api.webfe.deeplink_screen_option.WebPollingScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	6, // 2: api.webfe.deeplink_screen_option.WebPollingScreenOptions.content:type_name -> api.webfe.deeplink_screen_option.CommonOptions
	7, // 3: api.webfe.deeplink_screen_option.WebPollingScreenOptions.flow_name:type_name -> webfe.common.FlowName
	3, // 4: api.webfe.deeplink_screen_option.WebPollingScreenOptions.request_properties:type_name -> api.webfe.deeplink_screen_option.WebPollingScreenOptions.RequestPropertiesEntry
	5, // 5: api.webfe.deeplink_screen_option.WebPanFormScreenOptions.header:type_name -> api.typesv2.deeplink_screen_option.ScreenOptionHeader
	6, // 6: api.webfe.deeplink_screen_option.WebPanFormScreenOptions.content:type_name -> api.webfe.deeplink_screen_option.CommonOptions
	4, // 7: api.webfe.deeplink_screen_option.WebPanFormScreenOptions.extra_request_properties:type_name -> api.webfe.deeplink_screen_option.WebPanFormScreenOptions.ExtraRequestPropertiesEntry
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_webfe_deeplink_screen_option_screen_options_proto_init() }
func file_api_webfe_deeplink_screen_option_screen_options_proto_init() {
	if File_api_webfe_deeplink_screen_option_screen_options_proto != nil {
		return
	}
	file_api_webfe_deeplink_screen_option_message_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebErrorScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebPollingScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WebPanFormScreenOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_webfe_deeplink_screen_option_screen_options_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_webfe_deeplink_screen_option_screen_options_proto_goTypes,
		DependencyIndexes: file_api_webfe_deeplink_screen_option_screen_options_proto_depIdxs,
		MessageInfos:      file_api_webfe_deeplink_screen_option_screen_options_proto_msgTypes,
	}.Build()
	File_api_webfe_deeplink_screen_option_screen_options_proto = out.File
	file_api_webfe_deeplink_screen_option_screen_options_proto_rawDesc = nil
	file_api_webfe_deeplink_screen_option_screen_options_proto_goTypes = nil
	file_api_webfe_deeplink_screen_option_screen_options_proto_depIdxs = nil
}
