// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/webfe/deeplink_screen_option/screen_options.proto

package deeplink_screen_option

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/gamma/api/webfe/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.FlowName(0)
)

// Validate checks the field values on WebErrorScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WebErrorScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebErrorScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WebErrorScreenOptionsMultiError, or nil if none found.
func (m *WebErrorScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *WebErrorScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WebErrorScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WebErrorScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebErrorScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ImageUrl

	// no validation rules for Title

	// no validation rules for Subtitle

	// no validation rules for ShowRefreshButton

	if len(errors) > 0 {
		return WebErrorScreenOptionsMultiError(errors)
	}

	return nil
}

// WebErrorScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by WebErrorScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type WebErrorScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebErrorScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebErrorScreenOptionsMultiError) AllErrors() []error { return m }

// WebErrorScreenOptionsValidationError is the validation error returned by
// WebErrorScreenOptions.Validate if the designated constraints aren't met.
type WebErrorScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebErrorScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebErrorScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebErrorScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebErrorScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebErrorScreenOptionsValidationError) ErrorName() string {
	return "WebErrorScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e WebErrorScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebErrorScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebErrorScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebErrorScreenOptionsValidationError{}

// Validate checks the field values on WebPollingScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WebPollingScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebPollingScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WebPollingScreenOptionsMultiError, or nil if none found.
func (m *WebPollingScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *WebPollingScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WebPollingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WebPollingScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebPollingScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WebPollingScreenOptionsValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WebPollingScreenOptionsValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebPollingScreenOptionsValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShowLongerDurationLoader

	// no validation rules for FlowName

	// no validation rules for RequestProperties

	if len(errors) > 0 {
		return WebPollingScreenOptionsMultiError(errors)
	}

	return nil
}

// WebPollingScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by WebPollingScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type WebPollingScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebPollingScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebPollingScreenOptionsMultiError) AllErrors() []error { return m }

// WebPollingScreenOptionsValidationError is the validation error returned by
// WebPollingScreenOptions.Validate if the designated constraints aren't met.
type WebPollingScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebPollingScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebPollingScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebPollingScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebPollingScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebPollingScreenOptionsValidationError) ErrorName() string {
	return "WebPollingScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e WebPollingScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebPollingScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebPollingScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebPollingScreenOptionsValidationError{}

// Validate checks the field values on WebPanFormScreenOptions with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *WebPanFormScreenOptions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on WebPanFormScreenOptions with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// WebPanFormScreenOptionsMultiError, or nil if none found.
func (m *WebPanFormScreenOptions) ValidateAll() error {
	return m.validate(true)
}

func (m *WebPanFormScreenOptions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WebPanFormScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WebPanFormScreenOptionsValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebPanFormScreenOptionsValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetContent()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, WebPanFormScreenOptionsValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, WebPanFormScreenOptionsValidationError{
					field:  "Content",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetContent()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return WebPanFormScreenOptionsValidationError{
				field:  "Content",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExtraRequestProperties

	if len(errors) > 0 {
		return WebPanFormScreenOptionsMultiError(errors)
	}

	return nil
}

// WebPanFormScreenOptionsMultiError is an error wrapping multiple validation
// errors returned by WebPanFormScreenOptions.ValidateAll() if the designated
// constraints aren't met.
type WebPanFormScreenOptionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m WebPanFormScreenOptionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m WebPanFormScreenOptionsMultiError) AllErrors() []error { return m }

// WebPanFormScreenOptionsValidationError is the validation error returned by
// WebPanFormScreenOptions.Validate if the designated constraints aren't met.
type WebPanFormScreenOptionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e WebPanFormScreenOptionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e WebPanFormScreenOptionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e WebPanFormScreenOptionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e WebPanFormScreenOptionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e WebPanFormScreenOptionsValidationError) ErrorName() string {
	return "WebPanFormScreenOptionsValidationError"
}

// Error satisfies the builtin error interface
func (e WebPanFormScreenOptionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sWebPanFormScreenOptions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = WebPanFormScreenOptionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = WebPanFormScreenOptionsValidationError{}
