// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/webfe/service.proto

package webfe

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/gamma/api/webfe/common"

	common1 "github.com/epifi/be-common/api/typesv2/common"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.FlowName(0)

	_ = common1.BooleanEnum(0)

	_ = typesv2.CardProgramType(0)
)

// define the regex for a UUID once up-front
var _service_uuidPattern = regexp.MustCompile("^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$")

// Validate checks the field values on CheckCreditCardEligibilityRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckCreditCardEligibilityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckCreditCardEligibilityRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckCreditCardEligibilityRequestMultiError, or nil if none found.
func (m *CheckCreditCardEligibilityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCreditCardEligibilityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditCardEligibilityRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditCardEligibilityRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditCardEligibilityRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditCardEligibilityRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditCardEligibilityRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditCardEligibilityRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditCardEligibilityRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditCardEligibilityRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditCardEligibilityRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmailId

	// no validation rules for Token

	// no validation rules for CardProgramType

	// no validation rules for CardProgramVendor

	if len(errors) > 0 {
		return CheckCreditCardEligibilityRequestMultiError(errors)
	}

	return nil
}

// CheckCreditCardEligibilityRequestMultiError is an error wrapping multiple
// validation errors returned by
// CheckCreditCardEligibilityRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckCreditCardEligibilityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCreditCardEligibilityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCreditCardEligibilityRequestMultiError) AllErrors() []error { return m }

// CheckCreditCardEligibilityRequestValidationError is the validation error
// returned by CheckCreditCardEligibilityRequest.Validate if the designated
// constraints aren't met.
type CheckCreditCardEligibilityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCreditCardEligibilityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCreditCardEligibilityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCreditCardEligibilityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCreditCardEligibilityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCreditCardEligibilityRequestValidationError) ErrorName() string {
	return "CheckCreditCardEligibilityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCreditCardEligibilityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCreditCardEligibilityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCreditCardEligibilityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCreditCardEligibilityRequestValidationError{}

// Validate checks the field values on CheckCreditCardEligibilityResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CheckCreditCardEligibilityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckCreditCardEligibilityResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CheckCreditCardEligibilityResponseMultiError, or nil if none found.
func (m *CheckCreditCardEligibilityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckCreditCardEligibilityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckCreditCardEligibilityResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckCreditCardEligibilityResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckCreditCardEligibilityResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return CheckCreditCardEligibilityResponseMultiError(errors)
	}

	return nil
}

// CheckCreditCardEligibilityResponseMultiError is an error wrapping multiple
// validation errors returned by
// CheckCreditCardEligibilityResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckCreditCardEligibilityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckCreditCardEligibilityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckCreditCardEligibilityResponseMultiError) AllErrors() []error { return m }

// CheckCreditCardEligibilityResponseValidationError is the validation error
// returned by CheckCreditCardEligibilityResponse.Validate if the designated
// constraints aren't met.
type CheckCreditCardEligibilityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckCreditCardEligibilityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckCreditCardEligibilityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckCreditCardEligibilityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckCreditCardEligibilityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckCreditCardEligibilityResponseValidationError) ErrorName() string {
	return "CheckCreditCardEligibilityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckCreditCardEligibilityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckCreditCardEligibilityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckCreditCardEligibilityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckCreditCardEligibilityResponseValidationError{}

// Validate checks the field values on ValidateLoginRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateLoginRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateLoginRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateLoginRequestMultiError, or nil if none found.
func (m *ValidateLoginRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateLoginRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateLoginRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateLoginRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateLoginRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if err := m._validateUuid(m.GetToken()); err != nil {
		err = ValidateLoginRequestValidationError{
			field:  "Token",
			reason: "value must be a valid UUID",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetOtp()) != 6 {
		err := ValidateLoginRequestValidationError{
			field:  "Otp",
			reason: "value length must be 6 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if !_ValidateLoginRequest_Otp_Pattern.MatchString(m.GetOtp()) {
		err := ValidateLoginRequestValidationError{
			field:  "Otp",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for WebFlow

	// no validation rules for FlowName

	// no validation rules for EmailId

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateLoginRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateLoginRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateLoginRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateLoginRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateLoginRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateLoginRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateLoginRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateLoginRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateLoginRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidateLoginRequestMultiError(errors)
	}

	return nil
}

func (m *ValidateLoginRequest) _validateUuid(uuid string) error {
	if matched := _service_uuidPattern.MatchString(uuid); !matched {
		return errors.New("invalid uuid format")
	}

	return nil
}

// ValidateLoginRequestMultiError is an error wrapping multiple validation
// errors returned by ValidateLoginRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidateLoginRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateLoginRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateLoginRequestMultiError) AllErrors() []error { return m }

// ValidateLoginRequestValidationError is the validation error returned by
// ValidateLoginRequest.Validate if the designated constraints aren't met.
type ValidateLoginRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateLoginRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateLoginRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateLoginRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateLoginRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateLoginRequestValidationError) ErrorName() string {
	return "ValidateLoginRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateLoginRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateLoginRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateLoginRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateLoginRequestValidationError{}

var _ValidateLoginRequest_Otp_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on ValidateLoginResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ValidateLoginResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidateLoginResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ValidateLoginResponseMultiError, or nil if none found.
func (m *ValidateLoginResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidateLoginResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidateLoginResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidateLoginResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidateLoginResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	// no validation rules for AccessToken

	// no validation rules for ActorId

	if len(errors) > 0 {
		return ValidateLoginResponseMultiError(errors)
	}

	return nil
}

// ValidateLoginResponseMultiError is an error wrapping multiple validation
// errors returned by ValidateLoginResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidateLoginResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidateLoginResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidateLoginResponseMultiError) AllErrors() []error { return m }

// ValidateLoginResponseValidationError is the validation error returned by
// ValidateLoginResponse.Validate if the designated constraints aren't met.
type ValidateLoginResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidateLoginResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidateLoginResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidateLoginResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidateLoginResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidateLoginResponseValidationError) ErrorName() string {
	return "ValidateLoginResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidateLoginResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidateLoginResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidateLoginResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidateLoginResponseValidationError{}

// Validate checks the field values on GetRequestStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRequestStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRequestStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRequestStatusRequestMultiError, or nil if none found.
func (m *GetRequestStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRequestStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRequestStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRequestStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRequestStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	// no validation rules for WebFlow

	// no validation rules for FlowName

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetRequestStatusRequestMultiError(errors)
	}

	return nil
}

// GetRequestStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetRequestStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetRequestStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRequestStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRequestStatusRequestMultiError) AllErrors() []error { return m }

// GetRequestStatusRequestValidationError is the validation error returned by
// GetRequestStatusRequest.Validate if the designated constraints aren't met.
type GetRequestStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRequestStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRequestStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRequestStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRequestStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRequestStatusRequestValidationError) ErrorName() string {
	return "GetRequestStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRequestStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRequestStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRequestStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRequestStatusRequestValidationError{}

// Validate checks the field values on GetRequestStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRequestStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRequestStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRequestStatusResponseMultiError, or nil if none found.
func (m *GetRequestStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRequestStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRequestStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRequestStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRequestStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestStatus

	if all {
		switch v := interface{}(m.GetDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRequestStatusResponseValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRequestStatusResponseValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRequestStatusResponseValidationError{
				field:  "DisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRequestStatusResponseMultiError(errors)
	}

	return nil
}

// GetRequestStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetRequestStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetRequestStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRequestStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRequestStatusResponseMultiError) AllErrors() []error { return m }

// GetRequestStatusResponseValidationError is the validation error returned by
// GetRequestStatusResponse.Validate if the designated constraints aren't met.
type GetRequestStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRequestStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRequestStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRequestStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRequestStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRequestStatusResponseValidationError) ErrorName() string {
	return "GetRequestStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRequestStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRequestStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRequestStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRequestStatusResponseValidationError{}

// Validate checks the field values on DisplayInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DisplayInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DisplayInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DisplayInfoMultiError, or
// nil if none found.
func (m *DisplayInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *DisplayInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HeaderIcon

	// no validation rules for ScreenTitle

	// no validation rules for ScreenMessage

	// no validation rules for ScreenImage

	// no validation rules for CtaText

	// no validation rules for AdditionalText

	// no validation rules for BottomText

	if len(errors) > 0 {
		return DisplayInfoMultiError(errors)
	}

	return nil
}

// DisplayInfoMultiError is an error wrapping multiple validation errors
// returned by DisplayInfo.ValidateAll() if the designated constraints aren't met.
type DisplayInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DisplayInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DisplayInfoMultiError) AllErrors() []error { return m }

// DisplayInfoValidationError is the validation error returned by
// DisplayInfo.Validate if the designated constraints aren't met.
type DisplayInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DisplayInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DisplayInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DisplayInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DisplayInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DisplayInfoValidationError) ErrorName() string { return "DisplayInfoValidationError" }

// Error satisfies the builtin error interface
func (e DisplayInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDisplayInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DisplayInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DisplayInfoValidationError{}

// Validate checks the field values on GeneratePhoneOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GeneratePhoneOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GeneratePhoneOtpRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GeneratePhoneOtpRequestMultiError, or nil if none found.
func (m *GeneratePhoneOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GeneratePhoneOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GeneratePhoneOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GeneratePhoneOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GeneratePhoneOtpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GeneratePhoneOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GeneratePhoneOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GeneratePhoneOtpRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for GenerateOtpFlow

	// no validation rules for FlowName

	// no validation rules for Token

	if len(errors) > 0 {
		return GeneratePhoneOtpRequestMultiError(errors)
	}

	return nil
}

// GeneratePhoneOtpRequestMultiError is an error wrapping multiple validation
// errors returned by GeneratePhoneOtpRequest.ValidateAll() if the designated
// constraints aren't met.
type GeneratePhoneOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeneratePhoneOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeneratePhoneOtpRequestMultiError) AllErrors() []error { return m }

// GeneratePhoneOtpRequestValidationError is the validation error returned by
// GeneratePhoneOtpRequest.Validate if the designated constraints aren't met.
type GeneratePhoneOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeneratePhoneOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeneratePhoneOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeneratePhoneOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeneratePhoneOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeneratePhoneOtpRequestValidationError) ErrorName() string {
	return "GeneratePhoneOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GeneratePhoneOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeneratePhoneOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeneratePhoneOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeneratePhoneOtpRequestValidationError{}

// Validate checks the field values on GeneratePhoneOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GeneratePhoneOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GeneratePhoneOtpResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GeneratePhoneOtpResponseMultiError, or nil if none found.
func (m *GeneratePhoneOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GeneratePhoneOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GeneratePhoneOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GeneratePhoneOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GeneratePhoneOtpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for RetryTimerSeconds

	if len(errors) > 0 {
		return GeneratePhoneOtpResponseMultiError(errors)
	}

	return nil
}

// GeneratePhoneOtpResponseMultiError is an error wrapping multiple validation
// errors returned by GeneratePhoneOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type GeneratePhoneOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeneratePhoneOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeneratePhoneOtpResponseMultiError) AllErrors() []error { return m }

// GeneratePhoneOtpResponseValidationError is the validation error returned by
// GeneratePhoneOtpResponse.Validate if the designated constraints aren't met.
type GeneratePhoneOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeneratePhoneOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeneratePhoneOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeneratePhoneOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeneratePhoneOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeneratePhoneOtpResponseValidationError) ErrorName() string {
	return "GeneratePhoneOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GeneratePhoneOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeneratePhoneOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeneratePhoneOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeneratePhoneOtpResponseValidationError{}

// Validate checks the field values on VerifyPhoneOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyPhoneOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyPhoneOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyPhoneOtpRequestMultiError, or nil if none found.
func (m *VerifyPhoneOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyPhoneOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPhoneOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPhoneOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPhoneOtpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPhoneOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPhoneOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPhoneOtpRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for Otp

	// no validation rules for HasWhatsappConsent

	// no validation rules for WebUrl

	// no validation rules for WebFlow

	// no validation rules for FlowName

	if all {
		switch v := interface{}(m.GetAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPhoneOtpRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPhoneOtpRequestValidationError{
					field:  "AdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPhoneOtpRequestValidationError{
				field:  "AdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyPhoneOtpRequestMultiError(errors)
	}

	return nil
}

// VerifyPhoneOtpRequestMultiError is an error wrapping multiple validation
// errors returned by VerifyPhoneOtpRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyPhoneOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyPhoneOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyPhoneOtpRequestMultiError) AllErrors() []error { return m }

// VerifyPhoneOtpRequestValidationError is the validation error returned by
// VerifyPhoneOtpRequest.Validate if the designated constraints aren't met.
type VerifyPhoneOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyPhoneOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyPhoneOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyPhoneOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyPhoneOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyPhoneOtpRequestValidationError) ErrorName() string {
	return "VerifyPhoneOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyPhoneOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyPhoneOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyPhoneOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyPhoneOtpRequestValidationError{}

// Validate checks the field values on AdditionalDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AdditionalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AdditionalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AdditionalDetailsMultiError, or nil if none found.
func (m *AdditionalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AdditionalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.WebFlowData.(type) {
	case *AdditionalDetails_CreditCardEligibilityCheckData:
		if v == nil {
			err := AdditionalDetailsValidationError{
				field:  "WebFlowData",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCreditCardEligibilityCheckData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AdditionalDetailsValidationError{
						field:  "CreditCardEligibilityCheckData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AdditionalDetailsValidationError{
						field:  "CreditCardEligibilityCheckData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCreditCardEligibilityCheckData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AdditionalDetailsValidationError{
					field:  "CreditCardEligibilityCheckData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AdditionalDetailsMultiError(errors)
	}

	return nil
}

// AdditionalDetailsMultiError is an error wrapping multiple validation errors
// returned by AdditionalDetails.ValidateAll() if the designated constraints
// aren't met.
type AdditionalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AdditionalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AdditionalDetailsMultiError) AllErrors() []error { return m }

// AdditionalDetailsValidationError is the validation error returned by
// AdditionalDetails.Validate if the designated constraints aren't met.
type AdditionalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AdditionalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AdditionalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AdditionalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AdditionalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AdditionalDetailsValidationError) ErrorName() string {
	return "AdditionalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AdditionalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAdditionalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AdditionalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AdditionalDetailsValidationError{}

// Validate checks the field values on CreditCardEligibilityCheckData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditCardEligibilityCheckData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditCardEligibilityCheckData with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreditCardEligibilityCheckDataMultiError, or nil if none found.
func (m *CreditCardEligibilityCheckData) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardEligibilityCheckData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CardProgramType

	// no validation rules for CardProgramVendor

	if len(errors) > 0 {
		return CreditCardEligibilityCheckDataMultiError(errors)
	}

	return nil
}

// CreditCardEligibilityCheckDataMultiError is an error wrapping multiple
// validation errors returned by CreditCardEligibilityCheckData.ValidateAll()
// if the designated constraints aren't met.
type CreditCardEligibilityCheckDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardEligibilityCheckDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardEligibilityCheckDataMultiError) AllErrors() []error { return m }

// CreditCardEligibilityCheckDataValidationError is the validation error
// returned by CreditCardEligibilityCheckData.Validate if the designated
// constraints aren't met.
type CreditCardEligibilityCheckDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardEligibilityCheckDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardEligibilityCheckDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardEligibilityCheckDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardEligibilityCheckDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardEligibilityCheckDataValidationError) ErrorName() string {
	return "CreditCardEligibilityCheckDataValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardEligibilityCheckDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardEligibilityCheckData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardEligibilityCheckDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardEligibilityCheckDataValidationError{}

// Validate checks the field values on VerifyPhoneOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyPhoneOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyPhoneOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyPhoneOtpResponseMultiError, or nil if none found.
func (m *VerifyPhoneOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyPhoneOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPhoneOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPhoneOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPhoneOtpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessToken

	if all {
		switch v := interface{}(m.GetNextScreen()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyPhoneOtpResponseValidationError{
					field:  "NextScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyPhoneOtpResponseValidationError{
					field:  "NextScreen",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextScreen()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyPhoneOtpResponseValidationError{
				field:  "NextScreen",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return VerifyPhoneOtpResponseMultiError(errors)
	}

	return nil
}

// VerifyPhoneOtpResponseMultiError is an error wrapping multiple validation
// errors returned by VerifyPhoneOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyPhoneOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyPhoneOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyPhoneOtpResponseMultiError) AllErrors() []error { return m }

// VerifyPhoneOtpResponseValidationError is the validation error returned by
// VerifyPhoneOtpResponse.Validate if the designated constraints aren't met.
type VerifyPhoneOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyPhoneOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyPhoneOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyPhoneOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyPhoneOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyPhoneOtpResponseValidationError) ErrorName() string {
	return "VerifyPhoneOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyPhoneOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyPhoneOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyPhoneOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyPhoneOtpResponseValidationError{}

// Validate checks the field values on GenerateEmailOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateEmailOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateEmailOtpRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateEmailOtpRequestMultiError, or nil if none found.
func (m *GenerateEmailOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateEmailOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateEmailOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateEmailOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateEmailOtpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for GenerateOtpFlow

	// no validation rules for Token

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return GenerateEmailOtpRequestMultiError(errors)
	}

	return nil
}

// GenerateEmailOtpRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateEmailOtpRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateEmailOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateEmailOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateEmailOtpRequestMultiError) AllErrors() []error { return m }

// GenerateEmailOtpRequestValidationError is the validation error returned by
// GenerateEmailOtpRequest.Validate if the designated constraints aren't met.
type GenerateEmailOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateEmailOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateEmailOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateEmailOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateEmailOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateEmailOtpRequestValidationError) ErrorName() string {
	return "GenerateEmailOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateEmailOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateEmailOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateEmailOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateEmailOtpRequestValidationError{}

// Validate checks the field values on GenerateEmailOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateEmailOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateEmailOtpResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateEmailOtpResponseMultiError, or nil if none found.
func (m *GenerateEmailOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateEmailOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateEmailOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateEmailOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateEmailOtpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for RetryTimerSeconds

	if len(errors) > 0 {
		return GenerateEmailOtpResponseMultiError(errors)
	}

	return nil
}

// GenerateEmailOtpResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateEmailOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateEmailOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateEmailOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateEmailOtpResponseMultiError) AllErrors() []error { return m }

// GenerateEmailOtpResponseValidationError is the validation error returned by
// GenerateEmailOtpResponse.Validate if the designated constraints aren't met.
type GenerateEmailOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateEmailOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateEmailOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateEmailOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateEmailOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateEmailOtpResponseValidationError) ErrorName() string {
	return "GenerateEmailOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateEmailOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateEmailOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateEmailOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateEmailOtpResponseValidationError{}

// Validate checks the field values on VerifyEmailOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyEmailOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyEmailOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyEmailOtpRequestMultiError, or nil if none found.
func (m *VerifyEmailOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyEmailOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyEmailOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyEmailOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyEmailOtpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Email

	// no validation rules for Otp

	// no validation rules for Token

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return VerifyEmailOtpRequestMultiError(errors)
	}

	return nil
}

// VerifyEmailOtpRequestMultiError is an error wrapping multiple validation
// errors returned by VerifyEmailOtpRequest.ValidateAll() if the designated
// constraints aren't met.
type VerifyEmailOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyEmailOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyEmailOtpRequestMultiError) AllErrors() []error { return m }

// VerifyEmailOtpRequestValidationError is the validation error returned by
// VerifyEmailOtpRequest.Validate if the designated constraints aren't met.
type VerifyEmailOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyEmailOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyEmailOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyEmailOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyEmailOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyEmailOtpRequestValidationError) ErrorName() string {
	return "VerifyEmailOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyEmailOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyEmailOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyEmailOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyEmailOtpRequestValidationError{}

// Validate checks the field values on VerifyEmailOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyEmailOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyEmailOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyEmailOtpResponseMultiError, or nil if none found.
func (m *VerifyEmailOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyEmailOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyEmailOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyEmailOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyEmailOtpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyEmailOtpResponseMultiError(errors)
	}

	return nil
}

// VerifyEmailOtpResponseMultiError is an error wrapping multiple validation
// errors returned by VerifyEmailOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type VerifyEmailOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyEmailOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyEmailOtpResponseMultiError) AllErrors() []error { return m }

// VerifyEmailOtpResponseValidationError is the validation error returned by
// VerifyEmailOtpResponse.Validate if the designated constraints aren't met.
type VerifyEmailOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyEmailOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyEmailOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyEmailOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyEmailOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyEmailOtpResponseValidationError) ErrorName() string {
	return "VerifyEmailOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyEmailOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyEmailOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyEmailOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyEmailOtpResponseValidationError{}

// Validate checks the field values on SendAppLinkToUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendAppLinkToUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendAppLinkToUserRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendAppLinkToUserRequestMultiError, or nil if none found.
func (m *SendAppLinkToUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SendAppLinkToUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendAppLinkToUserRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendAppLinkToUserRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendAppLinkToUserRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendAppLinkToUserRequestMultiError(errors)
	}

	return nil
}

// SendAppLinkToUserRequestMultiError is an error wrapping multiple validation
// errors returned by SendAppLinkToUserRequest.ValidateAll() if the designated
// constraints aren't met.
type SendAppLinkToUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendAppLinkToUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendAppLinkToUserRequestMultiError) AllErrors() []error { return m }

// SendAppLinkToUserRequestValidationError is the validation error returned by
// SendAppLinkToUserRequest.Validate if the designated constraints aren't met.
type SendAppLinkToUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendAppLinkToUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendAppLinkToUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendAppLinkToUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendAppLinkToUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendAppLinkToUserRequestValidationError) ErrorName() string {
	return "SendAppLinkToUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SendAppLinkToUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendAppLinkToUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendAppLinkToUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendAppLinkToUserRequestValidationError{}

// Validate checks the field values on SendAppLinkToUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SendAppLinkToUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SendAppLinkToUserResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SendAppLinkToUserResponseMultiError, or nil if none found.
func (m *SendAppLinkToUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SendAppLinkToUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SendAppLinkToUserResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SendAppLinkToUserResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SendAppLinkToUserResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SendAppLinkToUserResponseMultiError(errors)
	}

	return nil
}

// SendAppLinkToUserResponseMultiError is an error wrapping multiple validation
// errors returned by SendAppLinkToUserResponse.ValidateAll() if the
// designated constraints aren't met.
type SendAppLinkToUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SendAppLinkToUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SendAppLinkToUserResponseMultiError) AllErrors() []error { return m }

// SendAppLinkToUserResponseValidationError is the validation error returned by
// SendAppLinkToUserResponse.Validate if the designated constraints aren't met.
type SendAppLinkToUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SendAppLinkToUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SendAppLinkToUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SendAppLinkToUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SendAppLinkToUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SendAppLinkToUserResponseValidationError) ErrorName() string {
	return "SendAppLinkToUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SendAppLinkToUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSendAppLinkToUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SendAppLinkToUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SendAppLinkToUserResponseValidationError{}
