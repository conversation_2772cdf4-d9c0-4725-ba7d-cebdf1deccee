// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/webfe/signup/service.proto

package signup

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	header "github.com/epifi/gamma/api/frontend/header"
	common1 "github.com/epifi/gamma/api/webfe/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// List of status codes returned
type GenerateOtpResponse_Status int32

const (
	// Otp Sent
	GenerateOtpResponse_OK GenerateOtpResponse_Status = 0
	// Expired token. Generate a new token
	GenerateOtpResponse_OTP_TOKEN_EXPIRED GenerateOtpResponse_Status = 100
	// Input token is not active. Cannot reuse it
	GenerateOtpResponse_OTP_INACTIVE GenerateOtpResponse_Status = 101
	// Resend failed; Reached the limit to resend OTP
	GenerateOtpResponse_OTP_RESEND_LIMIT GenerateOtpResponse_Status = 102
	// Request too soon; Please retry after a while
	GenerateOtpResponse_OTP_RESEND_INTERVAL GenerateOtpResponse_Status = 103
)

// Enum value maps for GenerateOtpResponse_Status.
var (
	GenerateOtpResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "OTP_TOKEN_EXPIRED",
		101: "OTP_INACTIVE",
		102: "OTP_RESEND_LIMIT",
		103: "OTP_RESEND_INTERVAL",
	}
	GenerateOtpResponse_Status_value = map[string]int32{
		"OK":                  0,
		"OTP_TOKEN_EXPIRED":   100,
		"OTP_INACTIVE":        101,
		"OTP_RESEND_LIMIT":    102,
		"OTP_RESEND_INTERVAL": 103,
	}
)

func (x GenerateOtpResponse_Status) Enum() *GenerateOtpResponse_Status {
	p := new(GenerateOtpResponse_Status)
	*p = x
	return p
}

func (x GenerateOtpResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GenerateOtpResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_signup_service_proto_enumTypes[0].Descriptor()
}

func (GenerateOtpResponse_Status) Type() protoreflect.EnumType {
	return &file_api_webfe_signup_service_proto_enumTypes[0]
}

func (x GenerateOtpResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GenerateOtpResponse_Status.Descriptor instead.
func (GenerateOtpResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_signup_service_proto_rawDescGZIP(), []int{1, 0}
}

// List of status codes returned
type VerifyOtpResponse_Status int32

const (
	// OTP verified
	VerifyOtpResponse_OK VerifyOtpResponse_Status = 0
	// Expired token. Generate a new token
	VerifyOtpResponse_OTP_TOKEN_EXPIRED VerifyOtpResponse_Status = 100
	// Input token is not active. Cannot reuse it
	VerifyOtpResponse_OTP_INACTIVE VerifyOtpResponse_Status = 101
	// Incorrect Otp
	VerifyOtpResponse_OTP_INCORRECT VerifyOtpResponse_Status = 102
	// Incorrect Otp. Last attempt remaining
	VerifyOtpResponse_OTP_INCORRECT_LAST_WARNING VerifyOtpResponse_Status = 103
	// Too many verification attempts on the token. Generate new token
	VerifyOtpResponse_OTP_VERIFY_LIMIT VerifyOtpResponse_Status = 104
)

// Enum value maps for VerifyOtpResponse_Status.
var (
	VerifyOtpResponse_Status_name = map[int32]string{
		0:   "OK",
		100: "OTP_TOKEN_EXPIRED",
		101: "OTP_INACTIVE",
		102: "OTP_INCORRECT",
		103: "OTP_INCORRECT_LAST_WARNING",
		104: "OTP_VERIFY_LIMIT",
	}
	VerifyOtpResponse_Status_value = map[string]int32{
		"OK":                         0,
		"OTP_TOKEN_EXPIRED":          100,
		"OTP_INACTIVE":               101,
		"OTP_INCORRECT":              102,
		"OTP_INCORRECT_LAST_WARNING": 103,
		"OTP_VERIFY_LIMIT":           104,
	}
)

func (x VerifyOtpResponse_Status) Enum() *VerifyOtpResponse_Status {
	p := new(VerifyOtpResponse_Status)
	*p = x
	return p
}

func (x VerifyOtpResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyOtpResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_webfe_signup_service_proto_enumTypes[1].Descriptor()
}

func (VerifyOtpResponse_Status) Type() protoreflect.EnumType {
	return &file_api_webfe_signup_service_proto_enumTypes[1]
}

func (x VerifyOtpResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyOtpResponse_Status.Descriptor instead.
func (VerifyOtpResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_webfe_signup_service_proto_rawDescGZIP(), []int{4, 0}
}

type GenerateOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req         *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	PhoneNumber *common.PhoneNumber   `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Unique identifier of GenerateOtp request
	// If token(+phone_number) is not sent, a new OTP is generated and sent to the user via SMS
	// If token(+phone_number) is sent, request is treated as ResendOtp and existing OTP is sent to the user phone via SMS
	// For FLOW_NAME_NET_WORTH_MCP_AUTH, token is mandatory; it contains signed session id
	Token    string           `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	FlowName common1.FlowName `protobuf:"varint,4,opt,name=flow_name,json=flowName,proto3,enum=webfe.common.FlowName" json:"flow_name,omitempty"`
}

func (x *GenerateOtpRequest) Reset() {
	*x = GenerateOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_signup_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpRequest) ProtoMessage() {}

func (x *GenerateOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_signup_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpRequest.ProtoReflect.Descriptor instead.
func (*GenerateOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_signup_service_proto_rawDescGZIP(), []int{0}
}

func (x *GenerateOtpRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GenerateOtpRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *GenerateOtpRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GenerateOtpRequest) GetFlowName() common1.FlowName {
	if x != nil {
		return x.FlowName
	}
	return common1.FlowName(0)
}

// Represents response of GenerateOtp method
type GenerateOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Represents if the OTP has been generated and sent without any errors
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Unique identifier of GenerateOtp request
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// A timer(in seconds) for the client post which it should raise a new request for Otp
	// Any attempt prior to this timer will not be honored & will result in error
	RetryTimerSeconds uint32 `protobuf:"varint,3,opt,name=retry_timer_seconds,json=retryTimerSeconds,proto3" json:"retry_timer_seconds,omitempty"`
}

func (x *GenerateOtpResponse) Reset() {
	*x = GenerateOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_signup_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateOtpResponse) ProtoMessage() {}

func (x *GenerateOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_signup_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateOtpResponse.ProtoReflect.Descriptor instead.
func (*GenerateOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_signup_service_proto_rawDescGZIP(), []int{1}
}

func (x *GenerateOtpResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GenerateOtpResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateOtpResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GenerateOtpResponse) GetRetryTimerSeconds() uint32 {
	if x != nil {
		return x.RetryTimerSeconds
	}
	return 0
}

// Represents a request of VerifyOtp method
type VerifyOtpRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// Phone number that is being verified
	PhoneNumber *common.PhoneNumber `protobuf:"bytes,1,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	// Unique identifier of GenerateOtp request
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// 6-digit OTP that is shared to the phone number via SMS
	Otp string `protobuf:"bytes,3,opt,name=otp,proto3" json:"otp,omitempty"`
	// Acquisition Info for the user
	// For example - URL from where verify otp was triggered
	AcqInfo  *AcquisitionInfo `protobuf:"bytes,4,opt,name=acq_info,json=acqInfo,proto3" json:"acq_info,omitempty"`
	FlowName common1.FlowName `protobuf:"varint,5,opt,name=flow_name,json=flowName,proto3,enum=webfe.common.FlowName" json:"flow_name,omitempty"`
}

func (x *VerifyOtpRequest) Reset() {
	*x = VerifyOtpRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_signup_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyOtpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyOtpRequest) ProtoMessage() {}

func (x *VerifyOtpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_signup_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyOtpRequest.ProtoReflect.Descriptor instead.
func (*VerifyOtpRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_signup_service_proto_rawDescGZIP(), []int{2}
}

func (x *VerifyOtpRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *VerifyOtpRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *VerifyOtpRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *VerifyOtpRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *VerifyOtpRequest) GetAcqInfo() *AcquisitionInfo {
	if x != nil {
		return x.AcqInfo
	}
	return nil
}

func (x *VerifyOtpRequest) GetFlowName() common1.FlowName {
	if x != nil {
		return x.FlowName
	}
	return common1.FlowName(0)
}

type AcquisitionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *AcquisitionInfo) Reset() {
	*x = AcquisitionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_signup_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcquisitionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcquisitionInfo) ProtoMessage() {}

func (x *AcquisitionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_signup_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcquisitionInfo.ProtoReflect.Descriptor instead.
func (*AcquisitionInfo) Descriptor() ([]byte, []int) {
	return file_api_webfe_signup_service_proto_rawDescGZIP(), []int{3}
}

func (x *AcquisitionInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

// Represents a response of VerifyOtp method
type VerifyOtpResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// contains `device_integrity_nonce` required to generate safetynet attestaion.
	Metadata *header.Metadata `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// Status of the request
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Represents a User's access token.
	// This token will serve as authentication parameter in the
	// further requests until an access token is generated
	AccessToken string `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	ActorId     string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *VerifyOtpResponse) Reset() {
	*x = VerifyOtpResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_signup_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyOtpResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyOtpResponse) ProtoMessage() {}

func (x *VerifyOtpResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_signup_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyOtpResponse.ProtoReflect.Descriptor instead.
func (*VerifyOtpResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_signup_service_proto_rawDescGZIP(), []int{4}
}

func (x *VerifyOtpResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *VerifyOtpResponse) GetMetadata() *header.Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *VerifyOtpResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *VerifyOtpResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *VerifyOtpResponse) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

var File_api_webfe_signup_service_proto protoreflect.FileDescriptor

var file_api_webfe_signup_service_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x73, 0x69, 0x67, 0x6e,
	0x75, 0x70, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0c, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x1a, 0x21,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xd5, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x42, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52,
	0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x66,
	0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xac, 0x02, 0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x2e, 0x0a, 0x13,
	0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x72, 0x5f, 0x73, 0x65, 0x63, 0x6f,
	0x6e, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x72, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x73, 0x22, 0x68, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x15,
	0x0a, 0x11, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49,
	0x52, 0x45, 0x44, 0x10, 0x64, 0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x54, 0x50, 0x5f, 0x49, 0x4e, 0x41,
	0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x54, 0x50, 0x5f, 0x52,
	0x45, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x66, 0x12, 0x17, 0x0a,
	0x13, 0x4f, 0x54, 0x50, 0x5f, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x49, 0x4e, 0x54, 0x45,
	0x52, 0x56, 0x41, 0x4c, 0x10, 0x67, 0x22, 0xb3, 0x02, 0x0a, 0x10, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x42, 0x0a,
	0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x24, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x12, 0xfa, 0x42, 0x0f, 0x72, 0x0d, 0x32, 0x08, 0x5e, 0x5b, 0x30,
	0x2d, 0x39, 0x5d, 0x2b, 0x24, 0x98, 0x01, 0x06, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x38, 0x0a,
	0x08, 0x61, 0x63, 0x71, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x2e, 0x41,
	0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07,
	0x61, 0x63, 0x71, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x6c, 0x6f, 0x77, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x77, 0x65, 0x62,
	0x66, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x6c, 0x6f, 0x77, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x23, 0x0a, 0x0f,
	0x41, 0x63, 0x71, 0x75, 0x69, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72,
	0x6c, 0x22, 0xf4, 0x02, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x08, 0x6d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x22, 0x82, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x54, 0x50, 0x5f, 0x54, 0x4f,
	0x4b, 0x45, 0x4e, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x64, 0x12, 0x10, 0x0a,
	0x0c, 0x4f, 0x54, 0x50, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x65, 0x12,
	0x11, 0x0a, 0x0d, 0x4f, 0x54, 0x50, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45, 0x43, 0x54,
	0x10, 0x66, 0x12, 0x1e, 0x0a, 0x1a, 0x4f, 0x54, 0x50, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52,
	0x45, 0x43, 0x54, 0x5f, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x57, 0x41, 0x52, 0x4e, 0x49, 0x4e, 0x47,
	0x10, 0x67, 0x12, 0x14, 0x0a, 0x10, 0x4f, 0x54, 0x50, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59,
	0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x10, 0x68, 0x32, 0xd6, 0x01, 0x0a, 0x06, 0x53, 0x69, 0x67,
	0x6e, 0x75, 0x70, 0x12, 0x68, 0x0a, 0x0b, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f,
	0x74, 0x70, 0x12, 0x20, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x73, 0x69, 0x67, 0x6e, 0x75,
	0x70, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x73, 0x69, 0x67,
	0x6e, 0x75, 0x70, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4f, 0x74, 0x70, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x00, 0x88, 0x9e,
	0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x62, 0x0a,
	0x09, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x74, 0x70, 0x12, 0x1e, 0x2e, 0x77, 0x65, 0x62,
	0x66, 0x65, 0x2e, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x4f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x77, 0x65, 0x62,
	0x66, 0x65, 0x2e, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x4f, 0x74, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7,
	0x0a, 0x00, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a,
	0x00, 0x42, 0x52, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x5a, 0x27, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x73,
	0x69, 0x67, 0x6e, 0x75, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_webfe_signup_service_proto_rawDescOnce sync.Once
	file_api_webfe_signup_service_proto_rawDescData = file_api_webfe_signup_service_proto_rawDesc
)

func file_api_webfe_signup_service_proto_rawDescGZIP() []byte {
	file_api_webfe_signup_service_proto_rawDescOnce.Do(func() {
		file_api_webfe_signup_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_webfe_signup_service_proto_rawDescData)
	})
	return file_api_webfe_signup_service_proto_rawDescData
}

var file_api_webfe_signup_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_webfe_signup_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_webfe_signup_service_proto_goTypes = []interface{}{
	(GenerateOtpResponse_Status)(0), // 0: webfe.signup.GenerateOtpResponse.Status
	(VerifyOtpResponse_Status)(0),   // 1: webfe.signup.VerifyOtpResponse.Status
	(*GenerateOtpRequest)(nil),      // 2: webfe.signup.GenerateOtpRequest
	(*GenerateOtpResponse)(nil),     // 3: webfe.signup.GenerateOtpResponse
	(*VerifyOtpRequest)(nil),        // 4: webfe.signup.VerifyOtpRequest
	(*AcquisitionInfo)(nil),         // 5: webfe.signup.AcquisitionInfo
	(*VerifyOtpResponse)(nil),       // 6: webfe.signup.VerifyOtpResponse
	(*header.RequestHeader)(nil),    // 7: frontend.header.RequestHeader
	(*common.PhoneNumber)(nil),      // 8: api.typesv2.common.PhoneNumber
	(common1.FlowName)(0),           // 9: webfe.common.FlowName
	(*header.ResponseHeader)(nil),   // 10: frontend.header.ResponseHeader
	(*rpc.Status)(nil),              // 11: rpc.Status
	(*header.Metadata)(nil),         // 12: frontend.header.Metadata
}
var file_api_webfe_signup_service_proto_depIdxs = []int32{
	7,  // 0: webfe.signup.GenerateOtpRequest.req:type_name -> frontend.header.RequestHeader
	8,  // 1: webfe.signup.GenerateOtpRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	9,  // 2: webfe.signup.GenerateOtpRequest.flow_name:type_name -> webfe.common.FlowName
	10, // 3: webfe.signup.GenerateOtpResponse.resp_header:type_name -> frontend.header.ResponseHeader
	11, // 4: webfe.signup.GenerateOtpResponse.status:type_name -> rpc.Status
	7,  // 5: webfe.signup.VerifyOtpRequest.req:type_name -> frontend.header.RequestHeader
	8,  // 6: webfe.signup.VerifyOtpRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	5,  // 7: webfe.signup.VerifyOtpRequest.acq_info:type_name -> webfe.signup.AcquisitionInfo
	9,  // 8: webfe.signup.VerifyOtpRequest.flow_name:type_name -> webfe.common.FlowName
	10, // 9: webfe.signup.VerifyOtpResponse.resp_header:type_name -> frontend.header.ResponseHeader
	12, // 10: webfe.signup.VerifyOtpResponse.metadata:type_name -> frontend.header.Metadata
	11, // 11: webfe.signup.VerifyOtpResponse.status:type_name -> rpc.Status
	2,  // 12: webfe.signup.Signup.GenerateOtp:input_type -> webfe.signup.GenerateOtpRequest
	4,  // 13: webfe.signup.Signup.VerifyOtp:input_type -> webfe.signup.VerifyOtpRequest
	3,  // 14: webfe.signup.Signup.GenerateOtp:output_type -> webfe.signup.GenerateOtpResponse
	6,  // 15: webfe.signup.Signup.VerifyOtp:output_type -> webfe.signup.VerifyOtpResponse
	14, // [14:16] is the sub-list for method output_type
	12, // [12:14] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_webfe_signup_service_proto_init() }
func file_api_webfe_signup_service_proto_init() {
	if File_api_webfe_signup_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_webfe_signup_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_signup_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_signup_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyOtpRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_signup_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcquisitionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_signup_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyOtpResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_webfe_signup_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_webfe_signup_service_proto_goTypes,
		DependencyIndexes: file_api_webfe_signup_service_proto_depIdxs,
		EnumInfos:         file_api_webfe_signup_service_proto_enumTypes,
		MessageInfos:      file_api_webfe_signup_service_proto_msgTypes,
	}.Build()
	File_api_webfe_signup_service_proto = out.File
	file_api_webfe_signup_service_proto_rawDesc = nil
	file_api_webfe_signup_service_proto_goTypes = nil
	file_api_webfe_signup_service_proto_depIdxs = nil
}
