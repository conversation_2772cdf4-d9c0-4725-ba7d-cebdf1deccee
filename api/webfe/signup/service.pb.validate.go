// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/webfe/signup/service.proto

package signup

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/gamma/api/webfe/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.FlowName(0)
)

// Validate checks the field values on GenerateOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpRequestMultiError, or nil if none found.
func (m *GenerateOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for FlowName

	if len(errors) > 0 {
		return GenerateOtpRequestMultiError(errors)
	}

	return nil
}

// GenerateOtpRequestMultiError is an error wrapping multiple validation errors
// returned by GenerateOtpRequest.ValidateAll() if the designated constraints
// aren't met.
type GenerateOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpRequestMultiError) AllErrors() []error { return m }

// GenerateOtpRequestValidationError is the validation error returned by
// GenerateOtpRequest.Validate if the designated constraints aren't met.
type GenerateOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpRequestValidationError) ErrorName() string {
	return "GenerateOtpRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpRequestValidationError{}

// Validate checks the field values on GenerateOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateOtpResponseMultiError, or nil if none found.
func (m *GenerateOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	// no validation rules for RetryTimerSeconds

	if len(errors) > 0 {
		return GenerateOtpResponseMultiError(errors)
	}

	return nil
}

// GenerateOtpResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateOtpResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateOtpResponseMultiError) AllErrors() []error { return m }

// GenerateOtpResponseValidationError is the validation error returned by
// GenerateOtpResponse.Validate if the designated constraints aren't met.
type GenerateOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateOtpResponseValidationError) ErrorName() string {
	return "GenerateOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateOtpResponseValidationError{}

// Validate checks the field values on VerifyOtpRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOtpRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOtpRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOtpRequestMultiError, or nil if none found.
func (m *VerifyOtpRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOtpRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Token

	if utf8.RuneCountInString(m.GetOtp()) != 6 {
		err := VerifyOtpRequestValidationError{
			field:  "Otp",
			reason: "value length must be 6 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)

	}

	if !_VerifyOtpRequest_Otp_Pattern.MatchString(m.GetOtp()) {
		err := VerifyOtpRequestValidationError{
			field:  "Otp",
			reason: "value does not match regex pattern \"^[0-9]+$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAcqInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "AcqInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpRequestValidationError{
					field:  "AcqInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAcqInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpRequestValidationError{
				field:  "AcqInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FlowName

	if len(errors) > 0 {
		return VerifyOtpRequestMultiError(errors)
	}

	return nil
}

// VerifyOtpRequestMultiError is an error wrapping multiple validation errors
// returned by VerifyOtpRequest.ValidateAll() if the designated constraints
// aren't met.
type VerifyOtpRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOtpRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOtpRequestMultiError) AllErrors() []error { return m }

// VerifyOtpRequestValidationError is the validation error returned by
// VerifyOtpRequest.Validate if the designated constraints aren't met.
type VerifyOtpRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOtpRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOtpRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOtpRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOtpRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOtpRequestValidationError) ErrorName() string { return "VerifyOtpRequestValidationError" }

// Error satisfies the builtin error interface
func (e VerifyOtpRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOtpRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOtpRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOtpRequestValidationError{}

var _VerifyOtpRequest_Otp_Pattern = regexp.MustCompile("^[0-9]+$")

// Validate checks the field values on AcquisitionInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AcquisitionInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AcquisitionInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AcquisitionInfoMultiError, or nil if none found.
func (m *AcquisitionInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *AcquisitionInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Url

	if len(errors) > 0 {
		return AcquisitionInfoMultiError(errors)
	}

	return nil
}

// AcquisitionInfoMultiError is an error wrapping multiple validation errors
// returned by AcquisitionInfo.ValidateAll() if the designated constraints
// aren't met.
type AcquisitionInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AcquisitionInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AcquisitionInfoMultiError) AllErrors() []error { return m }

// AcquisitionInfoValidationError is the validation error returned by
// AcquisitionInfo.Validate if the designated constraints aren't met.
type AcquisitionInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AcquisitionInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AcquisitionInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AcquisitionInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AcquisitionInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AcquisitionInfoValidationError) ErrorName() string { return "AcquisitionInfoValidationError" }

// Error satisfies the builtin error interface
func (e AcquisitionInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAcquisitionInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AcquisitionInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AcquisitionInfoValidationError{}

// Validate checks the field values on VerifyOtpResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOtpResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOtpResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOtpResponseMultiError, or nil if none found.
func (m *VerifyOtpResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOtpResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMetadata()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "Metadata",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMetadata()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpResponseValidationError{
				field:  "Metadata",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOtpResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOtpResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessToken

	// no validation rules for ActorId

	if len(errors) > 0 {
		return VerifyOtpResponseMultiError(errors)
	}

	return nil
}

// VerifyOtpResponseMultiError is an error wrapping multiple validation errors
// returned by VerifyOtpResponse.ValidateAll() if the designated constraints
// aren't met.
type VerifyOtpResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOtpResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOtpResponseMultiError) AllErrors() []error { return m }

// VerifyOtpResponseValidationError is the validation error returned by
// VerifyOtpResponse.Validate if the designated constraints aren't met.
type VerifyOtpResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOtpResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOtpResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOtpResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOtpResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOtpResponseValidationError) ErrorName() string {
	return "VerifyOtpResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyOtpResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOtpResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOtpResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOtpResponseValidationError{}
