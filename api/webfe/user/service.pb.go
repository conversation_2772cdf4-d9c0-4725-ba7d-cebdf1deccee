// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/webfe/user/service.proto

package user

import (
	_ "github.com/epifi/be-common/api/rpc"
	common1 "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	common "github.com/epifi/gamma/api/webfe/common"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CollectUserDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Either basic user details or employment details must be provided
	//
	// Types that are assignable to Details:
	//
	//	*CollectUserDetailsRequest_BasicUserDetails_
	Details isCollectUserDetailsRequest_Details `protobuf_oneof:"details"`
	// Indicates the originating screen from which this RPC was invoked, for tracking purposes
	DeeplinkScreen deeplink.Screen `protobuf:"varint,3,opt,name=deeplink_screen,json=deeplinkScreen,proto3,enum=frontend.deeplink.Screen" json:"deeplink_screen,omitempty"`
	// The name of the flow from which this RPC was invoked, for tracking purposes
	FlowName common.FlowName `protobuf:"varint,4,opt,name=flow_name,json=flowName,proto3,enum=webfe.common.FlowName" json:"flow_name,omitempty"`
}

func (x *CollectUserDetailsRequest) Reset() {
	*x = CollectUserDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_user_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectUserDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectUserDetailsRequest) ProtoMessage() {}

func (x *CollectUserDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_user_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectUserDetailsRequest.ProtoReflect.Descriptor instead.
func (*CollectUserDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_user_service_proto_rawDescGZIP(), []int{0}
}

func (x *CollectUserDetailsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (m *CollectUserDetailsRequest) GetDetails() isCollectUserDetailsRequest_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *CollectUserDetailsRequest) GetBasicUserDetails() *CollectUserDetailsRequest_BasicUserDetails {
	if x, ok := x.GetDetails().(*CollectUserDetailsRequest_BasicUserDetails_); ok {
		return x.BasicUserDetails
	}
	return nil
}

func (x *CollectUserDetailsRequest) GetDeeplinkScreen() deeplink.Screen {
	if x != nil {
		return x.DeeplinkScreen
	}
	return deeplink.Screen(0)
}

func (x *CollectUserDetailsRequest) GetFlowName() common.FlowName {
	if x != nil {
		return x.FlowName
	}
	return common.FlowName(0)
}

type isCollectUserDetailsRequest_Details interface {
	isCollectUserDetailsRequest_Details()
}

type CollectUserDetailsRequest_BasicUserDetails_ struct {
	BasicUserDetails *CollectUserDetailsRequest_BasicUserDetails `protobuf:"bytes,2,opt,name=basic_user_details,json=basicUserDetails,proto3,oneof"`
}

func (*CollectUserDetailsRequest_BasicUserDetails_) isCollectUserDetailsRequest_Details() {}

type CollectUserDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Deeplink to navigate to the screen corresponding to the next step after collecting user details
	Deeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
}

func (x *CollectUserDetailsResponse) Reset() {
	*x = CollectUserDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_user_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectUserDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectUserDetailsResponse) ProtoMessage() {}

func (x *CollectUserDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_user_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectUserDetailsResponse.ProtoReflect.Descriptor instead.
func (*CollectUserDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_user_service_proto_rawDescGZIP(), []int{1}
}

func (x *CollectUserDetailsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *CollectUserDetailsResponse) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

type CollectUserDetailsRequest_BasicUserDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Name of the user as per PAN card
	Name *common1.Name `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Date of birth of the user in the format "YYYY-MM-DD"
	Dob *date.Date `protobuf:"bytes,2,opt,name=dob,proto3" json:"dob,omitempty"`
	// PAN number of the user
	Pan string `protobuf:"bytes,3,opt,name=pan,proto3" json:"pan,omitempty"`
}

func (x *CollectUserDetailsRequest_BasicUserDetails) Reset() {
	*x = CollectUserDetailsRequest_BasicUserDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_user_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectUserDetailsRequest_BasicUserDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectUserDetailsRequest_BasicUserDetails) ProtoMessage() {}

func (x *CollectUserDetailsRequest_BasicUserDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_user_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectUserDetailsRequest_BasicUserDetails.ProtoReflect.Descriptor instead.
func (*CollectUserDetailsRequest_BasicUserDetails) Descriptor() ([]byte, []int) {
	return file_api_webfe_user_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CollectUserDetailsRequest_BasicUserDetails) GetName() *common1.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CollectUserDetailsRequest_BasicUserDetails) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *CollectUserDetailsRequest_BasicUserDetails) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

var File_api_webfe_user_service_proto protoreflect.FileDescriptor

var file_api_webfe_user_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x75, 0x73, 0x65, 0x72,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb2,
	0x03, 0x0a, 0x19, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03,
	0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x66,
	0x0a, 0x12, 0x62, 0x61, 0x73, 0x69, 0x63, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x77, 0x65, 0x62,
	0x66, 0x65, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x42, 0x61, 0x73, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x48, 0x00, 0x52, 0x10, 0x62, 0x61, 0x73, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x42, 0x0a, 0x0f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x19, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x0e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x33, 0x0a, 0x09, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e,
	0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x6c, 0x6f,
	0x77, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x08, 0x66, 0x6c, 0x6f, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x1a,
	0x77, 0x0a, 0x10, 0x42, 0x61, 0x73, 0x69, 0x63, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x22, 0x97, 0x01, 0x0a, 0x1a, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x32, 0x7c, 0x0a,
	0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x74, 0x0a, 0x12, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x2e, 0x77, 0x65,
	0x62, 0x66, 0x65, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x26, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x4e, 0x0a, 0x25, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x5a, 0x25, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_api_webfe_user_service_proto_rawDescOnce sync.Once
	file_api_webfe_user_service_proto_rawDescData = file_api_webfe_user_service_proto_rawDesc
)

func file_api_webfe_user_service_proto_rawDescGZIP() []byte {
	file_api_webfe_user_service_proto_rawDescOnce.Do(func() {
		file_api_webfe_user_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_webfe_user_service_proto_rawDescData)
	})
	return file_api_webfe_user_service_proto_rawDescData
}

var file_api_webfe_user_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_webfe_user_service_proto_goTypes = []interface{}{
	(*CollectUserDetailsRequest)(nil),                  // 0: webfe.user.CollectUserDetailsRequest
	(*CollectUserDetailsResponse)(nil),                 // 1: webfe.user.CollectUserDetailsResponse
	(*CollectUserDetailsRequest_BasicUserDetails)(nil), // 2: webfe.user.CollectUserDetailsRequest.BasicUserDetails
	(*header.RequestHeader)(nil),                       // 3: frontend.header.RequestHeader
	(deeplink.Screen)(0),                               // 4: frontend.deeplink.Screen
	(common.FlowName)(0),                               // 5: webfe.common.FlowName
	(*header.ResponseHeader)(nil),                      // 6: frontend.header.ResponseHeader
	(*deeplink.Deeplink)(nil),                          // 7: frontend.deeplink.Deeplink
	(*common1.Name)(nil),                               // 8: api.typesv2.common.Name
	(*date.Date)(nil),                                  // 9: google.type.Date
}
var file_api_webfe_user_service_proto_depIdxs = []int32{
	3, // 0: webfe.user.CollectUserDetailsRequest.req:type_name -> frontend.header.RequestHeader
	2, // 1: webfe.user.CollectUserDetailsRequest.basic_user_details:type_name -> webfe.user.CollectUserDetailsRequest.BasicUserDetails
	4, // 2: webfe.user.CollectUserDetailsRequest.deeplink_screen:type_name -> frontend.deeplink.Screen
	5, // 3: webfe.user.CollectUserDetailsRequest.flow_name:type_name -> webfe.common.FlowName
	6, // 4: webfe.user.CollectUserDetailsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	7, // 5: webfe.user.CollectUserDetailsResponse.deeplink:type_name -> frontend.deeplink.Deeplink
	8, // 6: webfe.user.CollectUserDetailsRequest.BasicUserDetails.name:type_name -> api.typesv2.common.Name
	9, // 7: webfe.user.CollectUserDetailsRequest.BasicUserDetails.dob:type_name -> google.type.Date
	0, // 8: webfe.user.User.CollectUserDetails:input_type -> webfe.user.CollectUserDetailsRequest
	1, // 9: webfe.user.User.CollectUserDetails:output_type -> webfe.user.CollectUserDetailsResponse
	9, // [9:10] is the sub-list for method output_type
	8, // [8:9] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_api_webfe_user_service_proto_init() }
func file_api_webfe_user_service_proto_init() {
	if File_api_webfe_user_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_webfe_user_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectUserDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_user_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectUserDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_user_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectUserDetailsRequest_BasicUserDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_webfe_user_service_proto_msgTypes[0].OneofWrappers = []interface{}{
		(*CollectUserDetailsRequest_BasicUserDetails_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_webfe_user_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_webfe_user_service_proto_goTypes,
		DependencyIndexes: file_api_webfe_user_service_proto_depIdxs,
		MessageInfos:      file_api_webfe_user_service_proto_msgTypes,
	}.Build()
	File_api_webfe_user_service_proto = out.File
	file_api_webfe_user_service_proto_rawDesc = nil
	file_api_webfe_user_service_proto_goTypes = nil
	file_api_webfe_user_service_proto_depIdxs = nil
}
