// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/webfe/user/service.proto

package user

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/gamma/api/webfe/common"

	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.FlowName(0)

	_ = deeplink.Screen(0)
)

// Validate checks the field values on CollectUserDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectUserDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectUserDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectUserDetailsRequestMultiError, or nil if none found.
func (m *CollectUserDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectUserDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectUserDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectUserDetailsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectUserDetailsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DeeplinkScreen

	// no validation rules for FlowName

	switch v := m.Details.(type) {
	case *CollectUserDetailsRequest_BasicUserDetails_:
		if v == nil {
			err := CollectUserDetailsRequestValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBasicUserDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CollectUserDetailsRequestValidationError{
						field:  "BasicUserDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CollectUserDetailsRequestValidationError{
						field:  "BasicUserDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBasicUserDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CollectUserDetailsRequestValidationError{
					field:  "BasicUserDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CollectUserDetailsRequestMultiError(errors)
	}

	return nil
}

// CollectUserDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by CollectUserDetailsRequest.ValidateAll() if the
// designated constraints aren't met.
type CollectUserDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectUserDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectUserDetailsRequestMultiError) AllErrors() []error { return m }

// CollectUserDetailsRequestValidationError is the validation error returned by
// CollectUserDetailsRequest.Validate if the designated constraints aren't met.
type CollectUserDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectUserDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectUserDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectUserDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectUserDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectUserDetailsRequestValidationError) ErrorName() string {
	return "CollectUserDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CollectUserDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectUserDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectUserDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectUserDetailsRequestValidationError{}

// Validate checks the field values on CollectUserDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectUserDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectUserDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectUserDetailsResponseMultiError, or nil if none found.
func (m *CollectUserDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectUserDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectUserDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectUserDetailsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectUserDetailsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectUserDetailsResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectUserDetailsResponseValidationError{
					field:  "Deeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectUserDetailsResponseValidationError{
				field:  "Deeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollectUserDetailsResponseMultiError(errors)
	}

	return nil
}

// CollectUserDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by CollectUserDetailsResponse.ValidateAll() if
// the designated constraints aren't met.
type CollectUserDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectUserDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectUserDetailsResponseMultiError) AllErrors() []error { return m }

// CollectUserDetailsResponseValidationError is the validation error returned
// by CollectUserDetailsResponse.Validate if the designated constraints aren't met.
type CollectUserDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectUserDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectUserDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectUserDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectUserDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectUserDetailsResponseValidationError) ErrorName() string {
	return "CollectUserDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CollectUserDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectUserDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectUserDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectUserDetailsResponseValidationError{}

// Validate checks the field values on
// CollectUserDetailsRequest_BasicUserDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CollectUserDetailsRequest_BasicUserDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// CollectUserDetailsRequest_BasicUserDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// CollectUserDetailsRequest_BasicUserDetailsMultiError, or nil if none found.
func (m *CollectUserDetailsRequest_BasicUserDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectUserDetailsRequest_BasicUserDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectUserDetailsRequest_BasicUserDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectUserDetailsRequest_BasicUserDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectUserDetailsRequest_BasicUserDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectUserDetailsRequest_BasicUserDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectUserDetailsRequest_BasicUserDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectUserDetailsRequest_BasicUserDetailsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	if len(errors) > 0 {
		return CollectUserDetailsRequest_BasicUserDetailsMultiError(errors)
	}

	return nil
}

// CollectUserDetailsRequest_BasicUserDetailsMultiError is an error wrapping
// multiple validation errors returned by
// CollectUserDetailsRequest_BasicUserDetails.ValidateAll() if the designated
// constraints aren't met.
type CollectUserDetailsRequest_BasicUserDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectUserDetailsRequest_BasicUserDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectUserDetailsRequest_BasicUserDetailsMultiError) AllErrors() []error { return m }

// CollectUserDetailsRequest_BasicUserDetailsValidationError is the validation
// error returned by CollectUserDetailsRequest_BasicUserDetails.Validate if
// the designated constraints aren't met.
type CollectUserDetailsRequest_BasicUserDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectUserDetailsRequest_BasicUserDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectUserDetailsRequest_BasicUserDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectUserDetailsRequest_BasicUserDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectUserDetailsRequest_BasicUserDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectUserDetailsRequest_BasicUserDetailsValidationError) ErrorName() string {
	return "CollectUserDetailsRequest_BasicUserDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e CollectUserDetailsRequest_BasicUserDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectUserDetailsRequest_BasicUserDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectUserDetailsRequest_BasicUserDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectUserDetailsRequest_BasicUserDetailsValidationError{}
