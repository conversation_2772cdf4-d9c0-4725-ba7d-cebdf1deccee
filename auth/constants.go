package auth

const (
	maxAFUReRegInitRetries = 4
	pinLength              = 4

	// parameters for argon2 hashing
	minSaltLen  = 8
	BkycTime    = uint32(12)
	BkycMemory  = uint32(64)
	BkycThreads = uint8(1)
	BkycKeyLen  = uint32(32)

	// notification title and message
	livenssManualReviewPushNotificationTitle = "You’re 1 step away from logging back into Fi \U0001F90F"
	livenssManualReviewPushNotificationBody  = "Your video verification is done ✅ Tap to complete your login"

	// buffer Time for linking device_integrity with access_token in sec
	bufferTimeForLinkingIntegrityWithAccessToken = 60
	// percentage of token lifetime left, after which it is to be considered for regeneration
	tokenRemainingLifeThresholdForRegeneration = 30

	DeviceRegistrationRequestPrefix   = "DevReg"
	DeviceReRegistrationRequestPrefix = "DevReReg"
	EKYCRequestPrefix                 = "AFUEKYC"

	redListAFUManualReviewFailure = "AFU Manual Review Suspect"
)
