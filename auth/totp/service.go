package totp

import (
	"context"
	"encoding/base32"
	"fmt"
	"time"

	"github.com/pquerna/otp/totp"
	"go.uber.org/zap"
	"golang.org/x/crypto/argon2"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	totpPb "github.com/epifi/gamma/api/auth/totp"
	totpEnumsPb "github.com/epifi/gamma/api/auth/totp/enums"
	"github.com/epifi/gamma/auth"
)

// Service implements the TOTP service
type Service struct {
	// Embedded to satisfy the protobuf interface
	totpPb.UnimplementedTotpServer
}

// NewService creates a new TOTP service instance
func NewService() *Service {
	return &Service{}
}

const (
	// TODO(sainath): get this from secrets manager
	networthMcpSalt = "dummySalt"
	// TOTP time step in seconds (standard is 30 seconds)
	totpTimeStep = 30
)

// calculateTotpExpiryTime calculates when the current TOTP code will expire
func calculateTotpExpiryTime() time.Time {
	now := time.Now().UTC()
	// Calculate the current time step
	currentTimeStep := now.Unix() / totpTimeStep
	// Next expiry time is at the end of current time step
	expiryTime := time.Unix((currentTimeStep+1)*totpTimeStep, 0).UTC()
	return expiryTime
}

// GenerateTotp generates a new TOTP code for the given actor and purpose
func (s *Service) GenerateTotp(ctx context.Context, req *totpPb.GenerateTotpRequest) (*totpPb.GenerateTotpResponse, error) {
	actorId := req.GetActorId()
	purpose := req.GetPurpose()
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	switch purpose {
	case totpEnumsPb.Purpose_PURPOSE_NET_WORTH_MCP:
		key := getSecureKey(actorId, purpose)
		totpCode, genTotpErr := totp.GenerateCode(key, time.Now().UTC())
		if genTotpErr != nil {
			logger.Error(ctx, "error generating totp for net worth mcp", zap.Error(genTotpErr))
			return &totpPb.GenerateTotpResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error generating totp for net worth mcp"),
			}, nil
		}
		return &totpPb.GenerateTotpResponse{
			Status:          rpcPb.StatusOk(),
			Totp:            totpCode,
			ExpiryTimestamp: timestamppb.New(calculateTotpExpiryTime()),
		}, nil
	default:
		logger.Error(ctx, "invalid purpose provided", zap.String("purpose", purpose.String()))
		return &totpPb.GenerateTotpResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("invalid purpose provided: %s", purpose.String())),
		}, nil
	}
}

// VerifyTotp verifies the provided TOTP code
func (s *Service) VerifyTotp(ctx context.Context, req *totpPb.VerifyTotpRequest) (*totpPb.VerifyTotpResponse, error) {
	purpose := req.GetPurpose()
	actorId := req.GetActorId()
	passcode := req.GetTotp()
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	switch purpose {
	case totpEnumsPb.Purpose_PURPOSE_NET_WORTH_MCP:
		key := getSecureKey(actorId, purpose)
		isValid := totp.Validate(passcode, key)
		var status *rpcPb.Status
		if isValid {
			status = rpcPb.StatusOk()
		} else {
			status = rpcPb.NewStatusWithoutDebug(uint32(totpPb.VerifyTotpResponse_STATUS_INVALID_CODE), "invalid totp code")
		}
		return &totpPb.VerifyTotpResponse{
			Status: status,
		}, nil
	default:
		logger.Error(ctx, "invalid purpose provided", zap.String("purpose", purpose.String()))
		return &totpPb.VerifyTotpResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("invalid purpose provided: %s", purpose.String())),
		}, nil
	}
}

func getSecureKey(actorId string, purpose totpEnumsPb.Purpose) string {
	// Combine purpose with salt for domain separation
	saltWithPurpose := fmt.Sprintf("%s:%s", networthMcpSalt, purpose)
	key := argon2.IDKey([]byte(actorId), []byte(saltWithPurpose), auth.BkycTime, auth.BkycMemory, auth.BkycThreads, auth.BkycKeyLen)
	return base32.StdEncoding.EncodeToString([]byte(key))
}
