// nolint
package totp

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	totpPb "github.com/epifi/gamma/api/auth/totp"
	"github.com/epifi/gamma/api/auth/totp/enums"
)

func TestMain(m *testing.M) {
	// Initialize logger for tests
	logger.Init(cfg.TestEnv)
	exitCode := m.Run()
	_ = logger.Log.Sync()
	os.Exit(exitCode)
}

func TestService_GenerateTotp(t *testing.T) {
	type fields struct {
		UnimplementedTotpServer totpPb.UnimplementedTotpServer
	}
	type args struct {
		ctx context.Context
		req *totpPb.GenerateTotpRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *totpPb.GenerateTotpResponse
		wantErr bool
	}{
		{
			name:   "should generate TOTP for net worth MCP purpose",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &totpPb.GenerateTotpRequest{
					ActorId: "test-actor-123",
					Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
				},
			},
			want:    nil, // We'll validate the response in the test logic since TOTP is dynamic
			wantErr: false,
		},
		{
			name:   "should return error for invalid purpose",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &totpPb.GenerateTotpRequest{
					ActorId: "test-actor-123",
					Purpose: enums.Purpose_PURPOSE_UNSPECIFIED,
				},
			},
			want:    nil, // We'll validate the error response in the test logic
			wantErr: false,
		},
		{
			name:   "should handle empty actor ID",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &totpPb.GenerateTotpRequest{
					ActorId: "",
					Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
				},
			},
			want:    nil, // We'll validate the response in the test logic
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				UnimplementedTotpServer: tt.fields.UnimplementedTotpServer,
			}
			got, err := s.GenerateTotp(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateTotp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Custom validation based on test case
			switch tt.name {
			case "should generate TOTP for net worth MCP purpose", "should handle empty actor ID":
				assert.NotNil(t, got)
				assert.NotNil(t, got.Status)
				assert.Equal(t, uint32(0), got.Status.Code)
				assert.NotEmpty(t, got.Totp)
				assert.Len(t, got.Totp, 6)
				assert.NotNil(t, got.ExpiryTimestamp)

				// Verify the expiry timestamp is in the future
				expiryTime := got.ExpiryTimestamp.AsTime()
				assert.True(t, expiryTime.After(time.Now()))

				// Verify the expiry time is within the next 30 seconds
				assert.True(t, expiryTime.Before(time.Now().Add(31*time.Second)))

			case "should return error for invalid purpose":
				assert.NotNil(t, got)
				assert.NotNil(t, got.Status)
				assert.NotEqual(t, uint32(0), got.Status.Code)
				assert.Contains(t, got.Status.DebugMessage, "invalid purpose provided")
				assert.Empty(t, got.Totp)
				assert.Nil(t, got.ExpiryTimestamp)
			}
		})
	}
}

func TestService_VerifyTotp(t *testing.T) {
	// Generate a valid TOTP for testing
	service := NewService()
	ctx := context.Background()
	generateReq := &totpPb.GenerateTotpRequest{
		ActorId: "test-actor-123",
		Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
	}
	generateResp, err := service.GenerateTotp(ctx, generateReq)
	require.NoError(t, err)
	require.NotNil(t, generateResp)
	require.Equal(t, uint32(0), generateResp.Status.Code)
	validTotp := generateResp.Totp

	type fields struct {
		UnimplementedTotpServer totpPb.UnimplementedTotpServer
	}
	type args struct {
		ctx context.Context
		req *totpPb.VerifyTotpRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *totpPb.VerifyTotpResponse
		wantErr bool
	}{
		{
			name:   "should verify valid TOTP for net worth MCP purpose",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &totpPb.VerifyTotpRequest{
					ActorId: "test-actor-123",
					Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
					Totp:    validTotp,
				},
			},
			want:    nil, // We'll validate in test logic
			wantErr: false,
		},
		{
			name:   "should return error for invalid TOTP code",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &totpPb.VerifyTotpRequest{
					ActorId: "test-actor-123",
					Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
					Totp:    "123456", // Invalid TOTP
				},
			},
			want:    nil, // We'll validate in test logic
			wantErr: false,
		},
		{
			name:   "should return error for invalid purpose",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &totpPb.VerifyTotpRequest{
					ActorId: "test-actor-123",
					Purpose: enums.Purpose_PURPOSE_UNSPECIFIED,
					Totp:    validTotp,
				},
			},
			want:    nil, // We'll validate in test logic
			wantErr: false,
		},
		{
			name:   "should return error for mismatched actor ID",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &totpPb.VerifyTotpRequest{
					ActorId: "different-actor-456",
					Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
					Totp:    validTotp,
				},
			},
			want:    nil, // We'll validate in test logic
			wantErr: false,
		},
		{
			name:   "should handle empty TOTP code",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &totpPb.VerifyTotpRequest{
					ActorId: "test-actor-123",
					Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
					Totp:    "",
				},
			},
			want:    nil, // We'll validate in test logic
			wantErr: false,
		},
		{
			name:   "should handle malformed TOTP code",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &totpPb.VerifyTotpRequest{
					ActorId: "test-actor-123",
					Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
					Totp:    "abc123", // Non-numeric TOTP
				},
			},
			want:    nil, // We'll validate in test logic
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				UnimplementedTotpServer: tt.fields.UnimplementedTotpServer,
			}
			got, err := s.VerifyTotp(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("VerifyTotp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Custom validation based on test case
			switch tt.name {
			case "should verify valid TOTP for net worth MCP purpose":
				assert.NotNil(t, got)
				assert.NotNil(t, got.Status)
				assert.Equal(t, uint32(0), got.Status.Code)

			case "should return error for invalid TOTP code", "should return error for mismatched actor ID",
				"should handle empty TOTP code", "should handle malformed TOTP code":
				assert.NotNil(t, got)
				assert.NotNil(t, got.Status)
				assert.Equal(t, uint32(totpPb.VerifyTotpResponse_STATUS_INVALID_CODE), got.Status.Code)

			case "should return error for invalid purpose":
				assert.NotNil(t, got)
				assert.NotNil(t, got.Status)
				assert.NotEqual(t, uint32(0), got.Status.Code)
				assert.Contains(t, got.Status.DebugMessage, "invalid purpose provided")
			}
		})
	}
}

func Test_calculateTotpExpiryTime(t *testing.T) {
	tests := []struct {
		name string
		want time.Time
	}{
		{
			name: "should return expiry time in future and aligned to 30-second boundary",
			want: time.Time{}, // We'll validate in test logic since this is time-dependent
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := calculateTotpExpiryTime()

			// Should return expiry time in future
			now := time.Now().UTC()
			assert.True(t, got.After(now), "expiry time should be in the future")

			// Should return expiry time within next 30 seconds
			maxExpiry := now.Add(31 * time.Second)
			assert.True(t, got.Before(maxExpiry), "expiry time should be within next 30 seconds")

			// Should return time aligned to 30-second boundary
			assert.Equal(t, int64(0), got.Unix()%30, "expiry time should be aligned to 30-second boundary")

			// Should be consistent within same time step
			got2 := calculateTotpExpiryTime()
			assert.Equal(t, got.Unix(), got2.Unix(), "expiry time should be consistent within same time step")
		})
	}
}

func Test_getSecureKey(t *testing.T) {
	type args struct {
		actorId string
		purpose enums.Purpose
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "should generate key for net worth MCP purpose",
			args: args{
				actorId: "test-actor-123",
				purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
			},
			want: "", // We'll validate in test logic since key is deterministic but we don't know exact value
		},
		{
			name: "should generate key for empty actor ID",
			args: args{
				actorId: "",
				purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
			},
			want: "", // We'll validate in test logic
		},
		{
			name: "should generate key for different purpose",
			args: args{
				actorId: "test-actor-123",
				purpose: enums.Purpose_PURPOSE_UNSPECIFIED,
			},
			want: "", // We'll validate in test logic
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getSecureKey(tt.args.actorId, tt.args.purpose)

			// Should generate non-empty key
			assert.NotEmpty(t, got)
			assert.Greater(t, len(got), 10) // Should be a reasonable length for base32 encoded key

			// Should be deterministic - same inputs should generate same key
			got2 := getSecureKey(tt.args.actorId, tt.args.purpose)
			assert.Equal(t, got, got2, "keys should be same for same actor ID and purpose")

			// Different actor IDs should generate different keys (if actor ID is different)
			if tt.args.actorId != "" {
				differentKey := getSecureKey("different-actor", tt.args.purpose)
				assert.NotEqual(t, got, differentKey, "keys should be different for different actor IDs")
			}
		})
	}
}

func TestService_GenerateAndVerifyTotp_Integration(t *testing.T) {
	type fields struct {
		UnimplementedTotpServer totpPb.UnimplementedTotpServer
	}
	type args struct {
		ctx     context.Context
		actorId string
		purpose enums.Purpose
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:   "should generate and verify TOTP successfully",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "integration-test-actor",
				purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := &Service{
				UnimplementedTotpServer: tt.fields.UnimplementedTotpServer,
			}

			// Generate TOTP
			generateReq := &totpPb.GenerateTotpRequest{
				ActorId: tt.args.actorId,
				Purpose: tt.args.purpose,
			}
			generateResp, err := service.GenerateTotp(tt.args.ctx, generateReq)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.NotNil(t, generateResp)
			require.Equal(t, uint32(0), generateResp.Status.Code)
			require.NotEmpty(t, generateResp.Totp)

			// Verify the generated TOTP
			verifyReq := &totpPb.VerifyTotpRequest{
				ActorId: tt.args.actorId,
				Purpose: tt.args.purpose,
				Totp:    generateResp.Totp,
			}
			verifyResp, err := service.VerifyTotp(tt.args.ctx, verifyReq)
			require.NoError(t, err)
			require.NotNil(t, verifyResp)
			assert.Equal(t, uint32(0), verifyResp.Status.Code, "generated TOTP should be successfully verified")
		})
	}
}

func TestService_GenerateTotp_ConsistencyAcrossTime(t *testing.T) {
	// Test that TOTP generation is consistent within the same time window
	type args struct {
		ctx     context.Context
		actorId string
		purpose enums.Purpose
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "should generate consistent TOTP within same time window",
			args: args{
				ctx:     context.Background(),
				actorId: "consistency-test-actor",
				purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			service := NewService()

			req := &totpPb.GenerateTotpRequest{
				ActorId: tt.args.actorId,
				Purpose: tt.args.purpose,
			}

			// Generate multiple TOTPs quickly
			resp1, err1 := service.GenerateTotp(tt.args.ctx, req)
			resp2, err2 := service.GenerateTotp(tt.args.ctx, req)

			require.NoError(t, err1)
			require.NoError(t, err2)
			require.NotNil(t, resp1)
			require.NotNil(t, resp2)

			// Both should be successful
			assert.Equal(t, uint32(0), resp1.Status.Code)
			assert.Equal(t, uint32(0), resp2.Status.Code)

			// Both should generate the same TOTP (since they're in the same time window)
			assert.Equal(t, resp1.Totp, resp2.Totp, "TOTP should be consistent within same time window")

			// Expiry times should be the same
			assert.Equal(t, resp1.ExpiryTimestamp.AsTime().Unix(), resp2.ExpiryTimestamp.AsTime().Unix())
		})
	}
}
