//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"

	"github.com/epifi/gamma/auth/totp"

	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/bankcust"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/consent"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/health_engine"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/agent"
	payPb "github.com/epifi/gamma/api/pay"
	productPb "github.com/epifi/gamma/api/product"
	riskpb "github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/risk/redlist"
	"github.com/epifi/gamma/api/savings"
	simAuthFederal "github.com/epifi/gamma/api/simulator/openbanking/auth/federal"
	"github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	vgAuth "github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	vgPartnerSDKPb "github.com/epifi/gamma/api/vendorgateway/openbanking/auth/partnersdk"
	"github.com/epifi/gamma/auth"
	"github.com/epifi/gamma/auth/authorizer"
	tokenCacheCollector "github.com/epifi/gamma/auth/authorizer/token_cache/collector"
	biometricsDao "github.com/epifi/gamma/auth/biometrics/dao"
	"github.com/epifi/gamma/auth/config"
	"github.com/epifi/gamma/auth/config/genconf"
	"github.com/epifi/gamma/auth/dao"
	"github.com/epifi/gamma/auth/developer"
	"github.com/epifi/gamma/auth/developer/processor"
	v2 "github.com/epifi/gamma/auth/id_token_verifier/v2"
	"github.com/epifi/gamma/auth/keycloak"
	"github.com/epifi/gamma/auth/location"
	locationDao "github.com/epifi/gamma/auth/location/dao"
	"github.com/epifi/gamma/auth/partnersdk"
	"github.com/epifi/gamma/auth/session"
	tokenMgr "github.com/epifi/gamma/auth/token_manager"
	tokenProcessor "github.com/epifi/gamma/auth/token_manager/processor"
	wireTypes "github.com/epifi/gamma/auth/wire/types"
	types2 "github.com/epifi/gamma/comms/wire/types"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/pkg/vendorstore"

	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
)

func RedisOptionProvider(conf *genconf.Config) *cfg.RedisOptions { return conf.RedisOptions() }
func TokenStoresCacheConfigProvider(gconf *genconf.Config) *genconf.TokenStoresCacheConfig {
	return gconf.TokenStoresCacheConfig()
}

func DeviceIntegrityNonceCacheConfigProvider(gconf *genconf.Config) *genconf.DeviceIntegrityNonceCacheConfig {
	return gconf.DeviceIntegrityNonceCacheConfig()
}

func EnvironmentProvider(conf *genconf.Config) string { return conf.Application().Environment }

func OAuthAndroidClientIDProvider(conf *genconf.Config) auth.OAuthAndroidClientIDType {
	return auth.OAuthAndroidClientIDType(conf.Application().OAuthAndroidClientID)
}

func OAuthIOSClientIDTypeProvider(conf *genconf.Config) auth.OAuthIOSClientIDType {
	return auth.OAuthIOSClientIDType(conf.Application().OAuthIOSClientID)
}
func AndroidClientSignatureTypeProvider(conf *genconf.Config) auth.AndroidClientSignatureType {
	return auth.AndroidClientSignatureType(conf.Application().AndroidClientSignature)
}

func OAuthProviderProvider(conf *genconf.Config) authPb.OAuthProvider {
	return authPb.OAuthProvider_GOOGLE
}

func AuthFactorUpdateCacheConfigProvider(conf *genconf.Config) *genconf.AuthFactorUpdateCacheConfig {
	return conf.AFU().AuthFactorUpdateCacheConfig()
}

func AuthTokenCacheProvider(conf *genconf.Config, authTokenStore types.AuthTokenRedisStore) wireTypes.AuthTokenCacheStorage {
	redisStore := cache.NewRedisCacheStorage(authTokenStore)
	return cache.NewRedisCacheStorageWithHystrix(redisStore, conf.RedisOptions().HystrixCommand)
}

func DeviceRegistrationCacheConfigProvider(conf *genconf.Config) *genconf.DeviceRegistrationCacheConfig {
	return conf.DeviceRegistrationCacheConfig()
}

func AFUCooldownProvider(conf *genconf.Config) *genconf.AFUCooldown {
	return conf.AFU().AFUCooldown()
}

func provideGetSessionParamsLockRedisClient(client types.AuthRedisStore) *redis.Client {
	return client
}

func provideKeycloakConfig(conf *genconf.Config) *keycloak.AdminClientConfig {
	return conf.Keycloak()
}

func InitializeService(dbV2 types.EpifiCRDB, pgdb types.AuthPGDB, commsClient wireTypes.AuthCommsClientWithInterceptors,
	vgClient vgAuth.VendorAuthClient, conf *config.Config, dynConf *genconf.Config,
	userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient,
	broker events.Broker, publisher wireTypes.AFUVendorUpdatePublisher,
	afuPublisher wireTypes.AuthFactorUpdatePublisher, authTokenCreationPublisher wireTypes.AuthTokenCreationPublisher, cardProvisioningClient cardPb.CardProvisioningClient,
	redisClient types.AuthRedisStore, authTokensRedisClient types.AuthTokenRedisStore, deviceIntegrityNonceCacheStorage wireTypes.AuthDeviceIntegrityNonceCacheStorage,
	authDevRegCacheStorage wireTypes.AuthDeviceRegistrationsCacheStorage, livenessClient liveness.LivenessClient,
	consentClient consent.ConsentClient, bcClient bankcust.BankCustomerServiceClient, rlClient redlist.RedListClient,
	riskClient riskpb.RiskClient, simAuthClient simAuthFederal.AuthClient,
	heClient health_engine.HealthEngineServiceClient,
	caseManagementClient case_management.CaseManagementClient, kycAgentClient agent.KycAgentServiceClient,
	fcmDeviceTokenClient types2.FCMDeviceTokenClientWithInterceptors, opStatusClient operationalStatusPb.OperationalStatusServiceClient, productClient productPb.ProductClient,
	kycClient kycPb.KycClient, onbClient onbPb.OnboardingClient, payClient payPb.PayClient) *auth.Service {
	wire.Build(
		wireTypes.CommsClientProvider,
		types2.FCMDeviceTokenClientProvider,
		AuthTokenCacheProvider,
		EnvironmentProvider,
		OAuthAndroidClientIDProvider,
		OAuthIOSClientIDTypeProvider,
		AndroidClientSignatureTypeProvider,
		OAuthProviderProvider,
		AuthFactorUpdateCacheConfigProvider,
		datetime.WireDefaultTimeSet,
		RedisOptionProvider,
		TokenStoresCacheConfigProvider,
		DeviceIntegrityNonceCacheConfigProvider,
		dao.NewDeviceIntegrityNonceCache,
		types.AuthRedisStoreRedisClientProvider,
		types.EpifiCRDBGormDBProvider,
		lock.RedisV9LockManagerWireSet,
		auth.NewService,
		dao.NewAuthDao,
		dao.DeviceRegistrationDaoWireSet,
		DeviceRegistrationCacheConfigProvider,
		dao.AuthFactorUpdateDaoWireSet,
		dao.NewAuthFactorUpdateCache,
		AFUCooldownProvider,
		dao.OAuthSignupDataDaoWireSet,
		dao.WireSet,
		wire.NewSet(v2.NewTokenHelperImpl, wire.Bind(new(v2.TokenHelper), new(*v2.TokenHelperImpl))),
		auth.NewIDTokenVerifier,
		auth.NewAppleIDTokenVerifier,
		auth.NewGoogleIDTokenVerifier,
		dao.DeviceAttestationDaoWireSet,
		auth.NewDeviceIntegrityVerifier,
		vendorstore.NewVendorStore,
		vendorstore.VendorStoreDAOWireSet,
		dao.DeviceRegistrationAttemptGormDaoWireSet,
		dao.DeviceIntegrityResultDaoWireSet,
		auth.NewSmsOtpService,
		auth.NewEmailOtpService,
		auth.NewOTPFactory,
		dao.AuthAttemptsDaoWireSet,
		auth.NewLivenessProcessorImpl,
		auth.NewDeviceDeactivationProcImpl,
		auth.NewDeviceReRegistrationInitProcImpl,
		auth.NewDeviceReRegistrationProcImpl,
		auth.NewDeviceReactivationProcImpl,
		auth.NewFiDeviceUpdateProcImpl,
		auth.NewStageTroubleshooter,
		cache.RedisStorageWithHystrixWireSet,
		persistentqueue.NewPersistentQueue,
		idgen.WireSet,
		idgen.NewCryptoSeededSource,
		provideKeycloakConfig,
		keycloak.NewAdminClient,
		authorizer.WireSet,
		tokenCacheCollector.WireSet,
		dao.TokenStoresDaoV2WireSet,
		tokenProcessor.NewSubjectBasedTokenProcessor,
		tokenProcessor.NewIDBasedTokenProcessor,
		tokenMgr.TokenManagerWireSet,
	)
	return &auth.Service{}
}

func InitializePartnerSDKService(authClient authPb.AuthClient, actorClient actorPb.ActorClient,
	partnerSDKClient vgPartnerSDKPb.PartnerSDKClient, config *genconf.Config,
	userGroupClient userGroupPb.GroupClient, redisClient types.AuthRedisStore,
	ffAccountingClient ffAccPb.AccountingClient) *partnersdk.Service {
	wire.Build(partnersdk.NewService, provideGetSessionParamsLockRedisClient, lock.DefaultLockMangerWireSet)
	return &partnersdk.Service{}
}

func LocationCacheConfigProvider(gcong *genconf.Config) *genconf.DeviceLocationCacheConfig {
	return gcong.DeviceLocationCacheConfig()
}

func InitializeLocationService(db types.EpifiCRDB, pgdb types.AuthPGDB, gconf *genconf.Config, cacheStorage wireTypes.AuthDeviceLocationCacheStorage, broker events.Broker) *location.Service {
	wire.Build(
		LocationCacheConfigProvider,
		locationDao.WireSet,
		location.NewService,
	)
	return &location.Service{}
}

func InitializeDevAuthService(db types.EpifiCRDB, pgdb types.AuthPGDB, gconf *genconf.Config,
	authTokenStore types.AuthTokenRedisStore, broker events.Broker) *developer.DevAuthService {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		AuthTokenCacheProvider,
		TokenStoresCacheConfigProvider,
		dao.WireSet,
		biometricsDao.BiometricsDaoWireSet,
		dao.DeviceAttestationDaoWireSet,
		dao.DeviceIntegrityResultDaoWireSet,
		developer.NewDevAuthService,
		developer.NewDevFactory,
		processor.NewAuthFactorUpdateEntity,
		processor.NewTokenStore,
		processor.NewOtpEntity,
		processor.NewPIITokenEntity,
		processor.NewDeviceRegistration,
		processor.NewAuthAttemptsEntity,
		processor.NewBiometricsEntity,
		processor.NewOAuthSignupData,
		processor.NewIosDeviceAttestationData,
		processor.NewDeviceIntegrityResult,
		dao.NewAuthDao,
		dao.NewDeviceRegistrationDaoCrdb,
		dao.NewOAuthSignupDataDaoCRDB,
		dao.AuthAttemptsDaoWireSet,
		dao.AuthFactorUpdateDaoWireSet,
		dao.TokenStoresDaoV2WireSet,
	)
	return &developer.DevAuthService{}
}

func InitializeTotpService() *totp.Service {
	wire.Build(totp.NewService)
	return &totp.Service{}
}

func InitializeSessionManagerService() *session.SessionManager {
	wire.Build(
		session.NewSessionManager,
	)
	return &session.SessionManager{}
}
