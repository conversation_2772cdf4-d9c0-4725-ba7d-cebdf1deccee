// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	auth3 "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/health_engine"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/agent"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/risk/redlist"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/simulator/openbanking/auth/federal"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/auth/partnersdk"
	auth2 "github.com/epifi/gamma/auth"
	"github.com/epifi/gamma/auth/authorizer"
	"github.com/epifi/gamma/auth/authorizer/token_cache/collector"
	dao3 "github.com/epifi/gamma/auth/biometrics/dao"
	"github.com/epifi/gamma/auth/config"
	"github.com/epifi/gamma/auth/config/genconf"
	"github.com/epifi/gamma/auth/dao"
	"github.com/epifi/gamma/auth/developer"
	processor2 "github.com/epifi/gamma/auth/developer/processor"
	"github.com/epifi/gamma/auth/id_token_verifier/v2"
	"github.com/epifi/gamma/auth/keycloak"
	"github.com/epifi/gamma/auth/location"
	dao2 "github.com/epifi/gamma/auth/location/dao"
	partnersdk2 "github.com/epifi/gamma/auth/partnersdk"
	"github.com/epifi/gamma/auth/session"
	"github.com/epifi/gamma/auth/token_manager"
	"github.com/epifi/gamma/auth/token_manager/processor"
	"github.com/epifi/gamma/auth/totp"
	types2 "github.com/epifi/gamma/auth/wire/types"
	types3 "github.com/epifi/gamma/comms/wire/types"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/pkg/vendorstore"
	"github.com/redis/go-redis/v9"
)

// Injectors from wire.go:

func InitializeService(dbV2 types.EpifiCRDB, pgdb types.AuthPGDB, commsClient types2.AuthCommsClientWithInterceptors, vgClient auth.VendorAuthClient, conf *config.Config, dynConf *genconf.Config, userClient user.UsersClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, broker events.Broker, publisher types2.AFUVendorUpdatePublisher, afuPublisher types2.AuthFactorUpdatePublisher, authTokenCreationPublisher types2.AuthTokenCreationPublisher, cardProvisioningClient provisioning.CardProvisioningClient, redisClient types.AuthRedisStore, authTokensRedisClient types.AuthTokenRedisStore, deviceIntegrityNonceCacheStorage types2.AuthDeviceIntegrityNonceCacheStorage, authDevRegCacheStorage types2.AuthDeviceRegistrationsCacheStorage, livenessClient liveness.LivenessClient, consentClient consent.ConsentClient, bcClient bankcust.BankCustomerServiceClient, rlClient redlist.RedListClient, riskClient risk.RiskClient, simAuthClient federal.AuthClient, heClient health_engine.HealthEngineServiceClient, caseManagementClient case_management.CaseManagementClient, kycAgentClient agent.KycAgentServiceClient, fcmDeviceTokenClient types3.FCMDeviceTokenClientWithInterceptors, opStatusClient operstatus.OperationalStatusServiceClient, productClient product.ProductClient, kycClient kyc.KycClient, onbClient onboarding.OnboardingClient, payClient pay.PayClient) *auth2.Service {
	authDao := dao.NewAuthDao(dbV2)
	afuCrdbDao := dao.NewAfuCrdbDao(dbV2, broker)
	client := types.AuthRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	redisOptions := RedisOptionProvider(dynConf)
	hystrixCommand := redisOptions.HystrixCommand
	cacheStorageWithHystrix := cache.NewRedisCacheStorageWithHystrix(redisCacheStorage, hystrixCommand)
	authFactorUpdateCacheConfig := AuthFactorUpdateCacheConfigProvider(dynConf)
	defaultTime := datetime.NewDefaultTime()
	afuCooldown := AFUCooldownProvider(dynConf)
	afuCache := dao.NewAuthFactorUpdateCache(cacheStorageWithHystrix, authFactorUpdateCacheConfig, defaultTime, afuCooldown)
	vendorOtpDaoCrdb := dao.NewVendorOtpDao(dbV2)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	oAuthAndroidClientIDType := OAuthAndroidClientIDProvider(dynConf)
	oAuthIOSClientIDType := OAuthIOSClientIDTypeProvider(dynConf)
	oAuthProvider := OAuthProviderProvider(dynConf)
	idTokenVerifier := auth2.NewIDTokenVerifier(oAuthAndroidClientIDType, oAuthIOSClientIDType, conf, oAuthProvider)
	androidClientSignatureType := AndroidClientSignatureTypeProvider(dynConf)
	string2 := EnvironmentProvider(dynConf)
	deviceIntegrityNonceCacheConfig := DeviceIntegrityNonceCacheConfigProvider(dynConf)
	deviceIntegrityNonceCache := dao.NewDeviceIntegrityNonceCache(deviceIntegrityNonceCacheStorage, deviceIntegrityNonceCacheConfig)
	deviceIntegrityNonceCacheDao := dao.ProvideDeviceIntegrityNonceCacheDao(deviceIntegrityNonceCache)
	deviceIntegrityNonceDao := dao.ProvideDeviceIntegrityNonceDao(deviceIntegrityNonceCacheDao)
	iosDeviceAttestationDaoCrdb := dao.NewIosDeviceAttestationDaoCrdb(dbV2)
	deviceIntegrityVerifier := auth2.NewDeviceIntegrityVerifier(string2, dynConf, authDao, deviceIntegrityNonceDao, iosDeviceAttestationDaoCrdb, conf)
	db := types.EpifiCRDBGormDBProvider(dbV2)
	vendorResponseCRDB := vendorstore.NewVendorResponseDAO(db)
	vendorStore := vendorstore.NewVendorStore(vendorResponseCRDB)
	deviceRegistrationAttemptsCrdb := dao.NewDeviceRegistrationAttempts(dbV2)
	v2IDTokenVerifier := auth2.NewGoogleIDTokenVerifier(oAuthAndroidClientIDType, oAuthIOSClientIDType, conf, oAuthProvider)
	oAuthSignupDataDaoCRDB := dao.NewOAuthSignupDataDaoCRDB(dbV2)
	tokenHelperImpl := v2.NewTokenHelperImpl(conf)
	appleAuthVerifier := auth2.NewAppleIDTokenVerifier(oAuthSignupDataDaoCRDB, tokenHelperImpl, conf)
	deviceIntegrityResultDaoCrdb := dao.NewDeviceIntegrityResultDaoCrdb(dbV2)
	smsOtpService := auth2.NewSmsOtpService(authDao, commsCommsClient, conf, androidClientSignatureType, userClient, broker, dynConf)
	emailOtpService := auth2.NewEmailOtpService(authDao, commsCommsClient, conf, androidClientSignatureType, userClient)
	iotpFactory := auth2.NewOTPFactory(smsOtpService, emailOtpService)
	authAttemptsCrdb := dao.NewAuthAttemptsCrdb(dbV2)
	livenessProcessor := auth2.NewLivenessProcessorImpl()
	deviceDeactivationProc := auth2.NewDeviceDeactivationProcImpl()
	deviceReRegistrationInitProc := auth2.NewDeviceReRegistrationInitProcImpl()
	deviceReRegistrationProc := auth2.NewDeviceReRegistrationProcImpl()
	deviceReactivationProc := auth2.NewDeviceReactivationProcImpl()
	fiDeviceUpdateProc := auth2.NewFiDeviceUpdateProcImpl()
	stageTroubleshooter := auth2.NewStageTroubleshooter(livenessProcessor, deviceDeactivationProc, deviceReRegistrationInitProc, deviceReRegistrationProc, deviceReactivationProc, fiDeviceUpdateProc)
	tokenStoresPgdb := dao.NewTokenStoresPgdb(pgdb, dynConf)
	tokenStoresDBDao := dao.ProvideTokenStoresDBDao(tokenStoresPgdb)
	tokenStoresCacheConfig := TokenStoresCacheConfigProvider(dynConf)
	tokenStoresDBDaoV2 := dao.ProvideTokenStoresDBDaoV2(tokenStoresPgdb)
	authTokenCacheStorage := AuthTokenCacheProvider(dynConf, authTokensRedisClient)
	tokenStoresIdCache := dao.NewTokenStoresIdCache(authTokenCacheStorage, tokenStoresCacheConfig)
	tokenStoresSubjectCache := dao.NewTokenStoresSubjectCache(authTokenCacheStorage, tokenStoresCacheConfig)
	tokenStoresDaoV2Impl := dao.NewTokenStoresDaoV2Impl(tokenStoresCacheConfig, tokenStoresDBDaoV2, tokenStoresIdCache, tokenStoresSubjectCache)
	tokenStoresCache := dao.NewTokenStoresCache(tokenStoresDBDao, tokenStoresDaoV2Impl, authTokenCacheStorage, tokenStoresCacheConfig)
	tokenStoresDao := dao.ProvideTokenStoresDao(tokenStoresCache)
	deviceRegistrationDaoCrdb := dao.NewDeviceRegistrationDaoCrdb(dbV2)
	deviceRegistrationCacheConfig := DeviceRegistrationCacheConfigProvider(dynConf)
	deviceRegistrationCache := dao.NewDeviceRegistrationCache(deviceRegistrationDaoCrdb, authDevRegCacheStorage, deviceRegistrationCacheConfig)
	persistentQueue := persistentqueue.NewPersistentQueue(db)
	adminClientConfig := provideKeycloakConfig(dynConf)
	source := idgen.NewCryptoSeededSource()
	runeNumberIdGenerator := idgen.NewNumberIdGenerator(source)
	adminClient := keycloak.NewAdminClient(adminClientConfig, runeNumberIdGenerator)
	device_tokenFCMDeviceTokenClient := types3.FCMDeviceTokenClientProvider(fcmDeviceTokenClient)
	federalSavingsAccountProcessor := authorizer.NewFederalSavingsAccountProcessor()
	authorizerProcessor := authorizer.NewProcessor(federalSavingsAccountProcessor)
	deviceRegistrationStatusProcessor := collector.NewDeviceRegistrationStatusProcessor()
	collectorProcessor := collector.NewProcessor(deviceRegistrationStatusProcessor)
	idBasedTokenProcessor := processor.NewIDBasedTokenProcessor(dynConf, tokenStoresDaoV2Impl, deviceRegistrationCache, afuCrdbDao, defaultTime)
	subjectBasedTokenProcessor := processor.NewSubjectBasedTokenProcessor(dynConf, tokenStoresDaoV2Impl, deviceRegistrationCache, afuCrdbDao, defaultTime)
	tokenManagerService := tokenmgr.NewTokenManagerService(tokenStoresDaoV2Impl, idBasedTokenProcessor, subjectBasedTokenProcessor)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client, clock, uuidGenerator)
	service := auth2.NewService(authDao, afuCrdbDao, afuCache, vendorOtpDaoCrdb, actorClient, commsCommsClient, idTokenVerifier, androidClientSignatureType, vgClient, dynConf, userClient, deviceIntegrityVerifier, savingsClient, broker, publisher, afuPublisher, vendorStore, deviceRegistrationAttemptsCrdb, v2IDTokenVerifier, appleAuthVerifier, deviceIntegrityResultDaoCrdb, iotpFactory, cardProvisioningClient, authAttemptsCrdb, stageTroubleshooter, tokenStoresDao, livenessClient, consentClient, defaultTime, rlClient, deviceIntegrityNonceDao, deviceRegistrationCache, bcClient, riskClient, persistentQueue, simAuthClient, heClient, caseManagementClient, adminClient, kycAgentClient, runeNumberIdGenerator, device_tokenFCMDeviceTokenClient, opStatusClient, productClient, kycClient, onbClient, authorizerProcessor, collectorProcessor, tokenManagerService, redisV9LockManager, payClient, authTokenCreationPublisher)
	return service
}

func InitializePartnerSDKService(authClient auth3.AuthClient, actorClient actor.ActorClient, partnerSDKClient partnersdk.PartnerSDKClient, config2 *genconf.Config, userGroupClient group.GroupClient, redisClient types.AuthRedisStore, ffAccountingClient accounting.AccountingClient) *partnersdk2.Service {
	client := provideGetSessionParamsLockRedisClient(redisClient)
	clock := lock.NewRealClockProvider()
	redisRwLock := lock.NewRedisRwLock(client, clock)
	service := partnersdk2.NewService(authClient, actorClient, partnerSDKClient, userGroupClient, config2, redisRwLock, ffAccountingClient)
	return service
}

func InitializeLocationService(db types.EpifiCRDB, pgdb types.AuthPGDB, gconf *genconf.Config, cacheStorage types2.AuthDeviceLocationCacheStorage, broker events.Broker) *location.Service {
	locationDaoCrdb := dao2.NewLocationDao(db)
	deviceLocationCacheConfig := LocationCacheConfigProvider(gconf)
	deviceLocationCache := dao2.NewDeviceLocationCache(locationDaoCrdb, cacheStorage, deviceLocationCacheConfig)
	locationDao := dao2.ProvideDeviceLocationDao(deviceLocationCache, locationDaoCrdb, deviceLocationCacheConfig)
	service := location.NewService(locationDao, broker)
	return service
}

func InitializeDevAuthService(db types.EpifiCRDB, pgdb types.AuthPGDB, gconf *genconf.Config, authTokenStore types.AuthTokenRedisStore, broker events.Broker) *developer.DevAuthService {
	afuCrdbDao := dao.NewAfuCrdbDao(db, broker)
	authFactorUpdateEntity := processor2.NewAuthFactorUpdateEntity(afuCrdbDao)
	tokenStoresPgdb := dao.NewTokenStoresPgdb(pgdb, gconf)
	tokenStoresDBDao := dao.ProvideTokenStoresDBDao(tokenStoresPgdb)
	tokenStoresCacheConfig := TokenStoresCacheConfigProvider(gconf)
	tokenStoresDBDaoV2 := dao.ProvideTokenStoresDBDaoV2(tokenStoresPgdb)
	authTokenCacheStorage := AuthTokenCacheProvider(gconf, authTokenStore)
	tokenStoresIdCache := dao.NewTokenStoresIdCache(authTokenCacheStorage, tokenStoresCacheConfig)
	tokenStoresSubjectCache := dao.NewTokenStoresSubjectCache(authTokenCacheStorage, tokenStoresCacheConfig)
	tokenStoresDaoV2Impl := dao.NewTokenStoresDaoV2Impl(tokenStoresCacheConfig, tokenStoresDBDaoV2, tokenStoresIdCache, tokenStoresSubjectCache)
	tokenStoresCache := dao.NewTokenStoresCache(tokenStoresDBDao, tokenStoresDaoV2Impl, authTokenCacheStorage, tokenStoresCacheConfig)
	tokenStoresDao := dao.ProvideTokenStoresDao(tokenStoresCache)
	tokenStoreEntity := processor2.NewTokenStore(tokenStoresDao)
	authDao := dao.NewAuthDao(db)
	otpEntity := processor2.NewOtpEntity(authDao)
	piiTokenEntity := processor2.NewPIITokenEntity()
	deviceRegistrationDaoCrdb := dao.NewDeviceRegistrationDaoCrdb(db)
	deviceRegistration := processor2.NewDeviceRegistration(deviceRegistrationDaoCrdb)
	authAttemptsCrdb := dao.NewAuthAttemptsCrdb(db)
	authAttemptsEntity := processor2.NewAuthAttemptsEntity(authAttemptsCrdb)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	biometricsPgdb := dao3.NewBiometricsPgdb(pgdb, domainIdGenerator)
	biometricsEntity := processor2.NewBiometricsEntity(biometricsPgdb)
	oAuthSignupDataDaoCRDB := dao.NewOAuthSignupDataDaoCRDB(db)
	oAuthSignupData := processor2.NewOAuthSignupData(oAuthSignupDataDaoCRDB)
	iosDeviceAttestationDaoCrdb := dao.NewIosDeviceAttestationDaoCrdb(db)
	iosDeviceAttestationData := processor2.NewIosDeviceAttestationData(iosDeviceAttestationDaoCrdb)
	deviceIntegrityResultDaoCrdb := dao.NewDeviceIntegrityResultDaoCrdb(db)
	deviceIntegrityResult := processor2.NewDeviceIntegrityResult(deviceIntegrityResultDaoCrdb, tokenStoresDao)
	devFactory := developer.NewDevFactory(authFactorUpdateEntity, tokenStoreEntity, otpEntity, piiTokenEntity, deviceRegistration, authAttemptsEntity, biometricsEntity, oAuthSignupData, iosDeviceAttestationData, deviceIntegrityResult)
	devAuthService := developer.NewDevAuthService(devFactory)
	return devAuthService
}

func InitializeTotpService() *totp.Service {
	service := totp.NewService()
	return service
}

func InitializeSessionManagerService() *session.SessionManager {
	sessionManager := session.NewSessionManager()
	return sessionManager
}

// wire.go:

func RedisOptionProvider(conf *genconf.Config) *cfg.RedisOptions { return conf.RedisOptions() }

func TokenStoresCacheConfigProvider(gconf *genconf.Config) *genconf.TokenStoresCacheConfig {
	return gconf.TokenStoresCacheConfig()
}

func DeviceIntegrityNonceCacheConfigProvider(gconf *genconf.Config) *genconf.DeviceIntegrityNonceCacheConfig {
	return gconf.DeviceIntegrityNonceCacheConfig()
}

func EnvironmentProvider(conf *genconf.Config) string { return conf.Application().Environment }

func OAuthAndroidClientIDProvider(conf *genconf.Config) auth2.OAuthAndroidClientIDType {
	return auth2.OAuthAndroidClientIDType(conf.Application().OAuthAndroidClientID)
}

func OAuthIOSClientIDTypeProvider(conf *genconf.Config) auth2.OAuthIOSClientIDType {
	return auth2.OAuthIOSClientIDType(conf.Application().OAuthIOSClientID)
}

func AndroidClientSignatureTypeProvider(conf *genconf.Config) auth2.AndroidClientSignatureType {
	return auth2.AndroidClientSignatureType(conf.Application().AndroidClientSignature)
}

func OAuthProviderProvider(conf *genconf.Config) auth3.OAuthProvider {
	return auth3.OAuthProvider_GOOGLE
}

func AuthFactorUpdateCacheConfigProvider(conf *genconf.Config) *genconf.AuthFactorUpdateCacheConfig {
	return conf.AFU().AuthFactorUpdateCacheConfig()
}

func AuthTokenCacheProvider(conf *genconf.Config, authTokenStore types.AuthTokenRedisStore) types2.AuthTokenCacheStorage {
	redisStore := cache.NewRedisCacheStorage(authTokenStore)
	return cache.NewRedisCacheStorageWithHystrix(redisStore, conf.RedisOptions().HystrixCommand)
}

func DeviceRegistrationCacheConfigProvider(conf *genconf.Config) *genconf.DeviceRegistrationCacheConfig {
	return conf.DeviceRegistrationCacheConfig()
}

func AFUCooldownProvider(conf *genconf.Config) *genconf.AFUCooldown {
	return conf.AFU().AFUCooldown()
}

func provideGetSessionParamsLockRedisClient(client types.AuthRedisStore) *redis.Client {
	return client
}

func provideKeycloakConfig(conf *genconf.Config) *keycloak.AdminClientConfig {
	return conf.Keycloak()
}

func LocationCacheConfigProvider(gcong *genconf.Config) *genconf.DeviceLocationCacheConfig {
	return gcong.DeviceLocationCacheConfig()
}
