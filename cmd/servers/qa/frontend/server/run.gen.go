// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	pkgtypes "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire2 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	operationalstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	statementpb "github.com/epifi/gamma/api/accounts/statement"
	beaccrualpb "github.com/epifi/gamma/api/accrual"
	crossattachpb "github.com/epifi/gamma/api/acquisition/crossattach"
	actor "github.com/epifi/gamma/api/actor"
	cxaapb "github.com/epifi/gamma/api/actor_activity"
	alfredpb "github.com/epifi/gamma/api/alfred"
	investment "github.com/epifi/gamma/api/analyser/investment"
	txnaggregatespb "github.com/epifi/gamma/api/analyser/txnaggregates"
	analyservariablespb "github.com/epifi/gamma/api/analyser/variables"
	authpb "github.com/epifi/gamma/api/auth"
	biometrics "github.com/epifi/gamma/api/auth/biometrics"
	liveness "github.com/epifi/gamma/api/auth/liveness"
	locationpb "github.com/epifi/gamma/api/auth/location"
	authv2pb "github.com/epifi/gamma/api/auth/orchestrator"
	bepartnersdkpb "github.com/epifi/gamma/api/auth/partnersdk"
	session "github.com/epifi/gamma/api/auth/session"
	totppb "github.com/epifi/gamma/api/auth/totp"
	bankcustpb "github.com/epifi/gamma/api/bankcust"
	compliancepb "github.com/epifi/gamma/api/bankcust/compliance"
	reminder "github.com/epifi/gamma/api/budgeting/reminder"
	becardctrl "github.com/epifi/gamma/api/card/control"
	cardcipb "github.com/epifi/gamma/api/card/currencyinsights"
	provisioning "github.com/epifi/gamma/api/card/provisioning"
	becasperpb "github.com/epifi/gamma/api/casper"
	beexchangerpb "github.com/epifi/gamma/api/casper/exchanger"
	evrpb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	beredemptionpb "github.com/epifi/gamma/api/casper/redemption"
	categorizer "github.com/epifi/gamma/api/categorizer"
	comms "github.com/epifi/gamma/api/comms"
	devicetoken "github.com/epifi/gamma/api/comms/device_token"
	tcpb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	uppb "github.com/epifi/gamma/api/comms/user_preference"
	beconnectedaccpb "github.com/epifi/gamma/api/connected_account"
	securities "github.com/epifi/gamma/api/connected_account/securities"
	consentpb "github.com/epifi/gamma/api/consent"
	limitestimatorpb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditreportv2pb "github.com/epifi/gamma/api/creditreportv2"
	bealpb "github.com/epifi/gamma/api/cx/app_log"
	becallroutingpb "github.com/epifi/gamma/api/cx/call_routing"
	bechatpb "github.com/epifi/gamma/api/cx/chat"
	becxpb "github.com/epifi/gamma/api/cx/customer_auth"
	disputepb "github.com/epifi/gamma/api/cx/dispute"
	issueconfigpb "github.com/epifi/gamma/api/cx/issue_config"
	beticketpb "github.com/epifi/gamma/api/cx/ticket"
	datasharingpb "github.com/epifi/gamma/api/datasharing"
	bedepositpb "github.com/epifi/gamma/api/deposit"
	depb "github.com/epifi/gamma/api/dynamic_elements"
	employment "github.com/epifi/gamma/api/employment"
	ffbepb "github.com/epifi/gamma/api/firefly"
	ffaccountspb "github.com/epifi/gamma/api/firefly/accounting"
	ffbillpb "github.com/epifi/gamma/api/firefly/billing"
	ffrepb "github.com/epifi/gamma/api/firefly/card_recommendation"
	fflmspb "github.com/epifi/gamma/api/firefly/lms"
	ffpinotpb "github.com/epifi/gamma/api/firefly/pinot"
	ffbev2pb "github.com/epifi/gamma/api/firefly/v2"
	fitttpb "github.com/epifi/gamma/api/fittt"
	sports "github.com/epifi/gamma/api/fittt/sports"
	saclosurepb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	fescreeningpb "github.com/epifi/gamma/api/frontend/account/screening"
	fesignuppb "github.com/epifi/gamma/api/frontend/account/signup"
	festatementpb "github.com/epifi/gamma/api/frontend/account/statement"
	feaccupipb "github.com/epifi/gamma/api/frontend/account/upi"
	feacquisitionpb "github.com/epifi/gamma/api/frontend/acquisition"
	feactoractivitypb "github.com/epifi/gamma/api/frontend/actoractivity"
	alfred2 "github.com/epifi/gamma/api/frontend/alfred"
	analyserfepb "github.com/epifi/gamma/api/frontend/analyser"
	apikeys2 "github.com/epifi/gamma/api/frontend/apikeys"
	feauthpb "github.com/epifi/gamma/api/frontend/auth"
	felivpb "github.com/epifi/gamma/api/frontend/auth/liveness"
	authorchfe "github.com/epifi/gamma/api/frontend/auth/orchestrator"
	fepartnersdkpb "github.com/epifi/gamma/api/frontend/auth/partnersdk"
	totp2 "github.com/epifi/gamma/api/frontend/auth/totp"
	febankcustomerpb "github.com/epifi/gamma/api/frontend/bank_customer"
	febudgetingreminder "github.com/epifi/gamma/api/frontend/budgeting/reminder"
	fecardpb "github.com/epifi/gamma/api/frontend/card"
	fecategorizerpb "github.com/epifi/gamma/api/frontend/categorizer"
	clientlogger2 "github.com/epifi/gamma/api/frontend/clientlogger"
	fecfgpb "github.com/epifi/gamma/api/frontend/config"
	connectedaccountfepb "github.com/epifi/gamma/api/frontend/connected_account"
	feconsentpb "github.com/epifi/gamma/api/frontend/consent"
	ack2 "github.com/epifi/gamma/api/frontend/consent/ack"
	fecontactspb "github.com/epifi/gamma/api/frontend/contacts"
	creditreportfepb "github.com/epifi/gamma/api/frontend/credit_report"
	fealpb "github.com/epifi/gamma/api/frontend/cx/app_logs"
	fecallpb "github.com/epifi/gamma/api/frontend/cx/call"
	fechatpb "github.com/epifi/gamma/api/frontend/cx/chat"
	fecxpb "github.com/epifi/gamma/api/frontend/cx/customer_auth"
	fedisputepb "github.com/epifi/gamma/api/frontend/cx/dispute"
	cxhomefepb "github.com/epifi/gamma/api/frontend/cx/home"
	feticketpb "github.com/epifi/gamma/api/frontend/cx/ticket"
	datasharing2 "github.com/epifi/gamma/api/frontend/datasharing"
	fedepositpb "github.com/epifi/gamma/api/frontend/deposit"
	digilockerfepb "github.com/epifi/gamma/api/frontend/digilocker"
	docs2 "github.com/epifi/gamma/api/frontend/docs"
	docuploadfepb "github.com/epifi/gamma/api/frontend/document_upload"
	defepb "github.com/epifi/gamma/api/frontend/dynamic_elements"
	fedevicetokenpb "github.com/epifi/gamma/api/frontend/fcm"
	fireflyfepb "github.com/epifi/gamma/api/frontend/firefly"
	fefitttpb "github.com/epifi/gamma/api/frontend/fittt"
	clientmetrics2 "github.com/epifi/gamma/api/frontend/fittt/client_metrics"
	fegeniepb "github.com/epifi/gamma/api/frontend/genie"
	fegoalspb "github.com/epifi/gamma/api/frontend/goals"
	fehomepb "github.com/epifi/gamma/api/frontend/home"
	fehomeorchestratorpb "github.com/epifi/gamma/api/frontend/home/<USER>"
	feinapphelpactoractivitypb "github.com/epifi/gamma/api/frontend/inapphelp/actor_activity"
	feappfeedback2 "github.com/epifi/gamma/api/frontend/inapphelp/app_feedback"
	fecontactus "github.com/epifi/gamma/api/frontend/inapphelp/contact_us"
	fefaqpb "github.com/epifi/gamma/api/frontend/inapphelp/faq"
	fefeedbackengine "github.com/epifi/gamma/api/frontend/inapphelp/feedback_engine"
	femedia "github.com/epifi/gamma/api/frontend/inapphelp/media"
	feinsightspb "github.com/epifi/gamma/api/frontend/insights"
	feaccessinfopb "github.com/epifi/gamma/api/frontend/insights/accessinfo"
	feemailparserpb "github.com/epifi/gamma/api/frontend/insights/emailparser"
	feepfpb2 "github.com/epifi/gamma/api/frontend/insights/epf"
	networthfepb "github.com/epifi/gamma/api/frontend/insights/networth"
	secretsfepb "github.com/epifi/gamma/api/frontend/insights/secrets"
	storypb "github.com/epifi/gamma/api/frontend/insights/story"
	investmentfepb "github.com/epifi/gamma/api/frontend/investment/aggregator"
	indianstocks2 "github.com/epifi/gamma/api/frontend/investment/indianstocks"
	femf "github.com/epifi/gamma/api/frontend/investment/mutualfund"
	investmentprofile "github.com/epifi/gamma/api/frontend/investment/profile"
	fekubairpb "github.com/epifi/gamma/api/frontend/kubair"
	feuqudopb "github.com/epifi/gamma/api/frontend/kyc/uqudo"
	fevkycpb "github.com/epifi/gamma/api/frontend/kyc/vkyc"
	media2 "github.com/epifi/gamma/api/frontend/media"
	nudgefepb "github.com/epifi/gamma/api/frontend/nudge"
	otp2 "github.com/epifi/gamma/api/frontend/otp"
	fep2ppb "github.com/epifi/gamma/api/frontend/p2pinvestment"
	pan2 "github.com/epifi/gamma/api/frontend/pan"
	fepaypb "github.com/epifi/gamma/api/frontend/pay"
	fetransactionpb "github.com/epifi/gamma/api/frontend/pay/transaction"
	fepreapprovedloanpb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	prompt2 "github.com/epifi/gamma/api/frontend/prompt"
	feqrpb "github.com/epifi/gamma/api/frontend/qr"
	frontendpb "github.com/epifi/gamma/api/frontend/recurringpayment"
	fereferralpb "github.com/epifi/gamma/api/frontend/referral"
	rewardsfepb "github.com/epifi/gamma/api/frontend/rewards"
	salaryestimation2 "github.com/epifi/gamma/api/frontend/salaryestimation"
	fesalarypgpb2 "github.com/epifi/gamma/api/frontend/salaryprogram"
	fesavingpb "github.com/epifi/gamma/api/frontend/savings"
	fesearchpb "github.com/epifi/gamma/api/frontend/search"
	smsfetcherpb "github.com/epifi/gamma/api/frontend/smsfetcher"
	matrixfepb "github.com/epifi/gamma/api/frontend/stockguardian/matrix"
	fetieringpb "github.com/epifi/gamma/api/frontend/tiering"
	fetimelinepb "github.com/epifi/gamma/api/frontend/timeline"
	feupipb "github.com/epifi/gamma/api/frontend/upi"
	feupionbpb "github.com/epifi/gamma/api/frontend/upi/onboarding"
	feuserpb "github.com/epifi/gamma/api/frontend/user"
	usstocksfepb2 "github.com/epifi/gamma/api/frontend/usstocks"
	fevkyccallpb "github.com/epifi/gamma/api/frontend/vkyccall"
	waitlistfepb "github.com/epifi/gamma/api/frontend/waitlist"
	fewopb "github.com/epifi/gamma/api/frontend/wealthonboarding"
	goalspb "github.com/epifi/gamma/api/goals"
	healthengine "github.com/epifi/gamma/api/health_engine"
	appfeedbackpb "github.com/epifi/gamma/api/inapphelp/app_feedback"
	befaqpb "github.com/epifi/gamma/api/inapphelp/faq/serving"
	befepb "github.com/epifi/gamma/api/inapphelp/feedback_engine"
	irpb "github.com/epifi/gamma/api/inapphelp/issue_reporting"
	mediapb "github.com/epifi/gamma/api/inapphelp/media"
	berapb "github.com/epifi/gamma/api/inapphelp/recent_activity"
	beinappreferralpb "github.com/epifi/gamma/api/inappreferral"
	seasonpb "github.com/epifi/gamma/api/inappreferral/season"
	beinsightspb "github.com/epifi/gamma/api/insights"
	beaccessinfopb "github.com/epifi/gamma/api/insights/accessinfo"
	beemailparserpb "github.com/epifi/gamma/api/insights/emailparser"
	epfpb "github.com/epifi/gamma/api/insights/epf"
	insightkubairpb "github.com/epifi/gamma/api/insights/kubair"
	benetworthpb "github.com/epifi/gamma/api/insights/networth"
	bestorypb "github.com/epifi/gamma/api/insights/story"
	userdeclarationpb "github.com/epifi/gamma/api/insights/user_declaration"
	invaggrpb "github.com/epifi/gamma/api/investment/aggregator"
	auth "github.com/epifi/gamma/api/investment/auth"
	dynamicelementuipb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	investmenteventprocessorpb "github.com/epifi/gamma/api/investment/event_processor"
	catalogpb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	mfexternalpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	mfnotipb "github.com/epifi/gamma/api/investment/mutualfund/notifications"
	orderpb "github.com/epifi/gamma/api/investment/mutualfund/order"
	investpaypb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	investmentprofilepb "github.com/epifi/gamma/api/investment/profile"
	kyc "github.com/epifi/gamma/api/kyc"
	agentpb "github.com/epifi/gamma/api/kyc/agent"
	kycdocspb "github.com/epifi/gamma/api/kyc/docs"
	kycuqudopb "github.com/epifi/gamma/api/kyc/uqudo"
	bevkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	mpb "github.com/epifi/gamma/api/merchant"
	nudgebepb "github.com/epifi/gamma/api/nudge"
	journeypb "github.com/epifi/gamma/api/nudge/journey"
	order "github.com/epifi/gamma/api/order"
	aaorderpb "github.com/epifi/gamma/api/order/aa"
	actoractivitypb "github.com/epifi/gamma/api/order/actoractivity"
	paymentpb "github.com/epifi/gamma/api/order/payment"
	p2ppbbe "github.com/epifi/gamma/api/p2pinvestment"
	panpb "github.com/epifi/gamma/api/pan"
	paypb "github.com/epifi/gamma/api/pay"
	bmspb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	iftpb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	bepipb "github.com/epifi/gamma/api/paymentinstrument"
	beaccountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	palbepb "github.com/epifi/gamma/api/preapprovedloan"
	lendabilitypb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	preeligibility "github.com/epifi/gamma/api/preapprovedloan/pre_eligibility"
	securedloanspb "github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	product "github.com/epifi/gamma/api/product"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	berecurringpayment "github.com/epifi/gamma/api/recurringpayment"
	enachpb "github.com/epifi/gamma/api/recurringpayment/enach"
	paymentgateway "github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	berewardspb "github.com/epifi/gamma/api/rewards"
	luckydrawpb "github.com/epifi/gamma/api/rewards/luckydraw"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	rewardsprojectionpb "github.com/epifi/gamma/api/rewards/projector"
	berewardofferspb "github.com/epifi/gamma/api/rewards/rewardoffers"
	riskpb "github.com/epifi/gamma/api/risk"
	cmpb "github.com/epifi/gamma/api/risk/case_management"
	profilepb "github.com/epifi/gamma/api/risk/profile"
	manager "github.com/epifi/gamma/api/rms/manager"
	ui "github.com/epifi/gamma/api/rms/ui"
	salaryestimationpb "github.com/epifi/gamma/api/salaryestimation"
	besalarypb "github.com/epifi/gamma/api/salaryprogram"
	healthinsurancepb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	besalaryreferralspb "github.com/epifi/gamma/api/salaryprogram/referrals"
	savingspb "github.com/epifi/gamma/api/savings"
	extacct "github.com/epifi/gamma/api/savings/extacct"
	screener "github.com/epifi/gamma/api/screener"
	besearchpb "github.com/epifi/gamma/api/search"
	segmentpb "github.com/epifi/gamma/api/segment"
	tieringpb "github.com/epifi/gamma/api/tiering"
	tieringpinotpb "github.com/epifi/gamma/api/tiering/pinot"
	betimelinepb "github.com/epifi/gamma/api/timeline"
	upcomingtransactions "github.com/epifi/gamma/api/upcomingtransactions"
	beupipb "github.com/epifi/gamma/api/upi"
	mandatepb "github.com/epifi/gamma/api/upi/mandate"
	upionboardingpb "github.com/epifi/gamma/api/upi/onboarding"
	simulation "github.com/epifi/gamma/api/upi/simulation"
	user "github.com/epifi/gamma/api/user"
	usercontactpb "github.com/epifi/gamma/api/user/contact"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	location "github.com/epifi/gamma/api/user/location"
	obfuscatorpb "github.com/epifi/gamma/api/user/obfuscator"
	useronboardingpb "github.com/epifi/gamma/api/user/onboarding"
	useractions "github.com/epifi/gamma/api/useractions"
	userintelpb "github.com/epifi/gamma/api/userintel"
	usstocksaccountpb "github.com/epifi/gamma/api/usstocks/account"
	usstockscatalogpb "github.com/epifi/gamma/api/usstocks/catalog"
	usstocksorderpb "github.com/epifi/gamma/api/usstocks/order"
	usstocksportfoliopb "github.com/epifi/gamma/api/usstocks/portfolio"
	ussrewardspb "github.com/epifi/gamma/api/usstocks/rewards"
	ippb "github.com/epifi/gamma/api/vendordata/ip"
	currencyinsightsvgpb "github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	vgemploymentpb "github.com/epifi/gamma/api/vendorgateway/employment"
	fiftyfin "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	employernamematchvgpb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch"
	ocr "github.com/epifi/gamma/api/vendorgateway/ocr"
	vgpaymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgiftpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	savingsvgpb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	vendormapping "github.com/epifi/gamma/api/vendormapping"
	vkyccall2 "github.com/epifi/gamma/api/vkyccall"
	wopb "github.com/epifi/gamma/api/wealthonboarding"
	webfe2 "github.com/epifi/gamma/api/webfe"
	webaccountspb "github.com/epifi/gamma/api/webfe/accounts"
	webauth2 "github.com/epifi/gamma/api/webfe/auth"
	consentpb2 "github.com/epifi/gamma/api/webfe/consent"
	loanseligibility "github.com/epifi/gamma/api/webfe/loanseligibility"
	webriskpb "github.com/epifi/gamma/api/webfe/risk"
	secretanalyser2 "github.com/epifi/gamma/api/webfe/secretanalyser"
	signup3 "github.com/epifi/gamma/api/webfe/signup"
	travelpb "github.com/epifi/gamma/api/webfe/travel"
	user3 "github.com/epifi/gamma/api/webfe/user"
	frontend "github.com/epifi/gamma/cmd/service_groups/frontend"
	commstypes "github.com/epifi/gamma/comms/wire/types"
	dewiretypes "github.com/epifi/gamma/dynamicelements/wire/types"
	frontendconf "github.com/epifi/gamma/frontend/config"
	genconf2 "github.com/epifi/gamma/frontend/config/genconf"
	servergenhook "github.com/epifi/gamma/frontend/servergenhook"
	wire "github.com/epifi/gamma/frontend/wire"
	types "github.com/epifi/gamma/frontend/wire/types"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	verifipkg "github.com/epifi/gamma/verifi/pkg"
	webfeconf "github.com/epifi/gamma/webfe/config"
	wire2 "github.com/epifi/gamma/webfe/wire"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.FRONTEND_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.FRONTEND_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.FRONTEND_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.FRONTEND_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	authConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.AUTH_SERVER)
	defer epifigrpc.CloseConn(authConn)
	authClient := authpb.NewAuthClient(authConn)

	attributeRateLimiter := ratelimiter.NewAttributeRateLimiter(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcAttributeRatelimiterParams().Namespace())
	locationClient := locationpb.NewLocationClient(authConn)
	consentClient := consentpb.NewConsentClient(authConn)
	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	growthinfraConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	managerClient := managerpb.NewManagerClient(growthinfraConn)
	groupClient := usergrouppb.NewGroupClient(authConn)
	usersClient := user.NewUsersClient(authConn)
	actorConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.ACTOR_SERVER)
	defer epifigrpc.CloseConn(actorConn)
	actorClient := actor.NewActorClient(actorConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(growthinfraConn)
	onboardingConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	kycClient := kyc.NewKycClient(onboardingConn)
	livenessClient := liveness.NewLivenessClient(onboardingConn)
	onboardingClient := useronboardingpb.NewOnboardingClient(authConn)
	cardConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.CARD_SERVER)
	defer epifigrpc.CloseConn(cardConn)
	cardProvisioningClient := provisioning.NewCardProvisioningClient(cardConn)
	growthinfraConnVar3ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar3 := servergenwire.NewFrontendRequestClientInterceptor()
	if unaryClientInterceptorVar3 != nil {
		growthinfraConnVar3ClientInterceptors = append(growthinfraConnVar3ClientInterceptors, unaryClientInterceptorVar3)
	}
	growthinfraConnVar3 := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar3ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar3)
	fCMDeviceTokenClient := devicetoken.NewFCMDeviceTokenClient(growthinfraConnVar3)
	vendormappingConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vendormapping.NewVendorMappingServiceClient(vendormappingConn)
	vKYCClient := bevkycpb.NewVKYCClient(onboardingConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	externalAccountsClient := extacct.NewExternalAccountsClient(centralgrowthConn)
	userActionsClient := useractions.NewUserActionsClient(onboardingConn)
	employmentClient := employment.NewEmploymentClient(onboardingConn)
	obfuscatorClient := obfuscatorpb.NewObfuscatorClient(authConn)
	savingsClient := savingspb.NewSavingsClient(centralgrowthConn)
	bankCustomerServiceClient := bankcustpb.NewBankCustomerServiceClient(onboardingConn)
	operationalStatusServiceClient := operationalstatuspb.NewOperationalStatusServiceClient(actorConn)
	panClient := panpb.NewPanClient(onboardingConn)
	connectedAccountClient := beconnectedaccpb.NewConnectedAccountClient(centralgrowthConn)
	productClient := product.NewProductClient(onboardingConn)
	lendingConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	creditReportManagerClient := creditreportv2pb.NewCreditReportManagerClient(lendingConn)
	userriskConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.USER_RISK_SERVER)
	defer epifigrpc.CloseConn(userriskConn)
	riskClient := riskpb.NewRiskClient(userriskConn)
	ipServiceClient := ippb.NewIpServiceClient(onboardingConn)
	userPreferenceClient := uppb.NewUserPreferenceClient(growthinfraConn)
	inAppReferralClient := beinappreferralpb.NewInAppReferralClient(onboardingConn)
	seasonServiceClient := seasonpb.NewSeasonServiceClient(onboardingConn)
	rewardsGeneratorClient := berewardspb.NewRewardsGeneratorClient(growthinfraConn)
	contactClient := usercontactpb.NewContactClient(authConn)
	nudgeServiceClient := nudgebepb.NewNudgeServiceClient(growthinfraConn)
	piClient := bepipb.NewPiClient(actorConn)
	accountPIRelationClient := beaccountpipb.NewAccountPIRelationClient(actorConn)
	orderConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.ORDER_SERVER)
	defer epifigrpc.CloseConn(orderConn)
	uPIClient := beupipb.NewUPIClient(orderConn)
	upiOnboardingClient := upionboardingpb.NewUpiOnboardingClient(orderConn)
	balanceClient := accountbalancepb.NewBalanceClient(actorConn)
	accountStatementClient := statementpb.NewAccountStatementClient(actorConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	depositClient := bedepositpb.NewDepositClient(wealthdmfConn)
	preApprovedLoanClient := palbepb.NewPreApprovedLoanClient(lendingConn)
	catalogManagerClient := catalogpb.NewCatalogManagerClient(wealthdmfConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	fiftyFinClient := fiftyfin.NewFiftyFinClient(vendorgatewayConn)
	securedLoansClient := securedloanspb.NewSecuredLoansClient(lendingConn)
	actionBarClient := besearchpb.NewActionBarClient(wealthdmfConn)
	locationClientVar4 := location.NewLocationClient(authConn)
	frontendConn := epifigrpc.NewLocalServerConnForFrontend(cfg.FRONTEND_SERVER, cfg.FRONTEND_SERVER, gconf.ServerPorts().GrpcPort, gconf.ServerPorts().GrpcSecurePort)
	defer epifigrpc.CloseConn(frontendConn)
	recurringPaymentServiceClient := frontendpb.NewRecurringPaymentServiceClient(frontendConn)
	payClient := paypb.NewPayClient(orderConn)
	growthinfraConnVar8ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar4 := servergenwire.NewFrontendRequestClientInterceptor()
	if unaryClientInterceptorVar4 != nil {
		growthinfraConnVar8ClientInterceptors = append(growthinfraConnVar8ClientInterceptors, unaryClientInterceptorVar4)
	}
	growthinfraConnVar8 := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar8ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar8)
	userPreferenceClientVar2 := uppb.NewUserPreferenceClient(growthinfraConnVar8)
	vKYCFeClient := bevkycpb.NewVKYCFeClient(onboardingConn)
	complianceClient := compliancepb.NewComplianceClient(onboardingConn)
	orderServiceClient := order.NewOrderServiceClient(orderConn)
	paymentClient := paymentpb.NewPaymentClient(orderConn)
	txnCategorizerClient := categorizer.NewTxnCategorizerClient(wealthdmfConn)
	decisionEngineClient := paymentpb.NewDecisionEngineClient(orderConn)
	timelineServiceClient := betimelinepb.NewTimelineServiceClient(actorConn)
	rewardOffersClient := berewardofferspb.NewRewardOffersClient(growthinfraConn)
	tieringClient := tieringpb.NewTieringClient(centralgrowthConn)
	eODBalanceClient := tieringpinotpb.NewEODBalanceClient(centralgrowthConn)
	salaryProgramClient := besalarypb.NewSalaryProgramClient(centralgrowthConn)
	docsConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.DOCS_SERVER)
	defer epifigrpc.CloseConn(docsConn)
	healthEngineServiceClient := healthengine.NewHealthEngineServiceClient(docsConn)
	p2PInvestmentClient := p2ppbbe.NewP2PInvestmentClient(wealthdmfConn)
	userIntelServiceClient := userintelpb.NewUserIntelServiceClient(onboardingConn)
	alfredClient := alfredpb.NewAlfredClient(onboardingConn)
	projectorServiceClient := rewardsprojectionpb.NewProjectorServiceClient(growthinfraConn)
	txnAggregatesClient := txnaggregatespb.NewTxnAggregatesClient(wealthdmfConn)
	rewardsAggregatesClient := rewardspinotpb.NewRewardsAggregatesClient(growthinfraConn)
	recurringPaymentServiceClientVar2 := berecurringpayment.NewRecurringPaymentServiceClient(orderConn)
	paymentGatewayServiceClient := paymentgateway.NewPaymentGatewayServiceClient(orderConn)
	accountManagerClient := usstocksaccountpb.NewAccountManagerClient(wealthdmfConn)
	healthInsuranceClient := healthinsurancepb.NewHealthInsuranceClient(centralgrowthConn)
	consumerClient := beupipb.NewConsumerClient(orderConn)
	simulationClient := simulation.NewSimulationClient(orderConn)
	growthinfraConnVar15ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewFrontendRequestClientInterceptor()
	if unaryClientInterceptor != nil {
		growthinfraConnVar15ClientInterceptors = append(growthinfraConnVar15ClientInterceptors, unaryClientInterceptor)
	}
	growthinfraConnVar15 := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar15ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar15)
	commsClient := comms.NewCommsClient(growthinfraConnVar15)
	accessInfoClient := beaccessinfopb.NewAccessInfoClient(wealthdmfConn)
	connectedAccountClientVar2 := connectedaccountfepb.NewConnectedAccountClient(frontendConn)
	cardControlClient := becardctrl.NewCardControlClient(cardConn)
	offerListingServiceClient := becasperpb.NewOfferListingServiceClient(growthinfraConn)
	actorActivityClient := actoractivitypb.NewActorActivityClient(orderConn)
	actorActivityClientVar2 := feactoractivitypb.NewActorActivityClient(frontendConn)
	inAppTargetedCommsClient := tcpb.NewInAppTargetedCommsClient(growthinfraConn)
	currencyInsightsClient := cardcipb.NewCurrencyInsightsClient(cardConn)
	questRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["QuestRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = questRedisStore.Close() }()
	questCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(questRedisStore), gconf.RedisClusters()["QuestRedisStore"].HystrixCommand)
	netWorthClient := benetworthpb.NewNetWorthClient(wealthdmfConn)
	ruleManagerClient := manager.NewRuleManagerClient(wealthdmfConn)
	accountingClient := ffaccountspb.NewAccountingClient(cardConn)
	storyClient := bestorypb.NewStoryClient(wealthdmfConn)
	fireflyClient := ffbepb.NewFireflyClient(cardConn)
	homeRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["HomeRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = homeRedisStore.Close() }()
	upcomingTransactionsClient := upcomingtransactions.NewUpcomingTransactionsClient(wealthdmfConn)
	merchantServiceClient := mpb.NewMerchantServiceClient(actorConn)
	offerCatalogServiceClient := becasperpb.NewOfferCatalogServiceClient(growthinfraConn)
	crossAttachClient := crossattachpb.NewCrossAttachClient(onboardingConn)
	lendabilityClient := lendabilitypb.NewLendabilityClient(lendingConn)
	homeClient := fehomepb.NewHomeClient(frontendConn)
	fCMClient := fedevicetokenpb.NewFCMClient(frontendConn)
	investmentAggregatorClient := investmentfepb.NewInvestmentAggregatorClient(frontendConn)
	fireflyClientVar2 := fireflyfepb.NewFireflyClient(frontendConn)
	preApprovedLoanClientVar4 := fepreapprovedloanpb.NewPreApprovedLoanClient(frontendConn)
	netWorthClientVar3 := networthfepb.NewNetWorthClient(frontendConn)
	analyserServiceClient := analyserfepb.NewAnalyserServiceClient(frontendConn)
	searchClient := fesearchpb.NewSearchClient(frontendConn)
	referralClient := fereferralpb.NewReferralClient(frontendConn)
	dynamicElementsClient := defepb.NewDynamicElementsClient(frontendConn)
	nudgeServiceClientVar3 := nudgefepb.NewNudgeServiceClient(frontendConn)
	rewardsClient := rewardsfepb.NewRewardsClient(frontendConn)
	homeClientVar2 := cxhomefepb.NewHomeClient(frontendConn)
	secretsClient := secretsfepb.NewSecretsClient(frontendConn)
	journeyServiceClient := nudgefepb.NewJourneyServiceClient(frontendConn)
	cardClient := fecardpb.NewCardClient(frontendConn)
	employmentFeClient := employment.NewEmploymentFeClient(onboardingConn)
	paymentClientVar2 := vgpaymentpb.NewPaymentClient(vendorgatewayConn)
	wealthOnboardingClient := wopb.NewWealthOnboardingClient(wealthdmfConn)
	docExtractionClient := kycdocspb.NewDocExtractionClient(onboardingConn)
	savingsClientVar14 := savingsvgpb.NewSavingsClient(vendorgatewayConn)
	nebulaConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	cxConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.CX_SERVER)
	defer epifigrpc.CloseConn(cxConn)
	customerAuthCallbackClient := becxpb.NewCustomerAuthCallbackClient(cxConn)
	serveFAQClient := befaqpb.NewServeFAQClient(cxConn)
	recentActivityClient := berapb.NewRecentActivityClient(cxConn)
	serviceClient := irpb.NewServiceClient(cxConn)
	ticketClient := beticketpb.NewTicketClient(cxConn)
	issueConfigManagementClient := issueconfigpb.NewIssueConfigManagementClient(cxConn)
	customerAuthenticationClient := becxpb.NewCustomerAuthenticationClient(cxConn)
	fAQClient := fefaqpb.NewFAQClient(frontendConn)
	ticketClientVar2 := feticketpb.NewTicketClient(frontendConn)
	profileClient := profilepb.NewProfileClient(userriskConn)
	disputeClient := disputepb.NewDisputeClient(cxConn)
	appLogClient := bealpb.NewAppLogClient(cxConn)
	goalsClient := goalspb.NewGoalsClient(wealthdmfConn)
	chatsClient := bechatpb.NewChatsClient(cxConn)
	callRoutingClient := becallroutingpb.NewCallRoutingClient(cxConn)
	mandateServiceClient := mandatepb.NewMandateServiceClient(orderConn)
	referralsClient := besalaryreferralspb.NewReferralsClient(centralgrowthConn)
	employmentClientVar8 := vgemploymentpb.NewEmploymentClient(vendorgatewayConn)
	employerNameMatchClient := employernamematchvgpb.NewEmployerNameMatchClient(vendorgatewayConn)
	enachServiceClient := enachpb.NewEnachServiceClient(orderConn)
	offerRedemptionServiceClient := beredemptionpb.NewOfferRedemptionServiceClient(growthinfraConn)
	accrualClient := beaccrualpb.NewAccrualClient(growthinfraConn)
	luckyDrawServiceClient := luckydrawpb.NewLuckyDrawServiceClient(growthinfraConn)
	exchangerOfferServiceClient := beexchangerpb.NewExchangerOfferServiceClient(growthinfraConn)
	offerInventoryServiceClient := becasperpb.NewOfferInventoryServiceClient(growthinfraConn)
	externalVendorRedemptionServiceClient := evrpb.NewExternalVendorRedemptionServiceClient(growthinfraConn)
	biometricsServiceClient := biometrics.NewBiometricsServiceClient(authConn)
	orchestratorClient := authv2pb.NewOrchestratorClient(authConn)
	partnerSDKClient := bepartnersdkpb.NewPartnerSDKClient(authConn)
	emailParserClient := beemailparserpb.NewEmailParserClient(wealthdmfConn)
	insightsClient := beinsightspb.NewInsightsClient(wealthdmfConn)
	fitttClient := fitttpb.NewFitttClient(wealthdmfConn)
	wealthOnboardingClientVar2 := fewopb.NewWealthOnboardingClient(frontendConn)
	paymentHandlerClient := investpaypb.NewPaymentHandlerClient(wealthdmfConn)
	orderManagerClient := orderpb.NewOrderManagerClient(wealthdmfConn)
	sportsManagerClient := sports.NewSportsManagerClient(wealthdmfConn)
	ruleUIManagerClient := ui.NewRuleUIManagerClient(wealthdmfConn)
	timelineServiceClientVar6 := fetimelinepb.NewTimelineServiceClient(frontendConn)
	catalogManagerClientVar4 := usstockscatalogpb.NewCatalogManagerClient(wealthdmfConn)
	internationalFundTransferClient := vgiftpb.NewInternationalFundTransferClient(vendorgatewayConn)
	internationalFundTransferClientVar2 := iftpb.NewInternationalFundTransferClient(orderConn)
	screenerClient := screener.NewScreenerClient(onboardingConn)
	epfClient := epfpb.NewEpfClient(wealthdmfConn)
	investmentAnalyticsClient := investment.NewInvestmentAnalyticsClient(wealthdmfConn)
	investmentProfileServiceClient := investmentprofilepb.NewInvestmentProfileServiceClient(wealthdmfConn)
	appFeedbackClient := appfeedbackpb.NewAppFeedbackClient(cxConn)
	inAppHelpMediaClient := mediapb.NewInAppHelpMediaClient(cxConn)
	transactionClient := fetransactionpb.NewTransactionClient(frontendConn)
	authClientVar23 := auth.NewAuthClient(wealthdmfConn)
	notificationsClient := mfnotipb.NewNotificationsClient(wealthdmfConn)
	mFExternalOrdersClient := mfexternalpb.NewMFExternalOrdersClient(wealthdmfConn)
	investmentAggregatorClientVar2 := invaggrpb.NewInvestmentAggregatorClient(wealthdmfConn)
	eventProcessorClient := investmenteventprocessorpb.NewEventProcessorClient(wealthdmfConn)
	orderManagerClientVar3 := usstocksorderpb.NewOrderManagerClient(wealthdmfConn)
	dynamicUIElementServiceClient := dynamicelementuipb.NewDynamicUIElementServiceClient(wealthdmfConn)
	growthinfraConnVar61ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire.NewFrontendRequestClientInterceptor()
	if unaryClientInterceptorVar2 != nil {
		growthinfraConnVar61ClientInterceptors = append(growthinfraConnVar61ClientInterceptors, unaryClientInterceptorVar2)
	}
	growthinfraConnVar61 := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.GROWTH_INFRA_SERVER, growthinfraConnVar61ClientInterceptors...)
	defer epifigrpc.CloseConn(growthinfraConnVar61)
	dynamicElementsClientVar3 := depb.NewDynamicElementsClient(growthinfraConnVar61)
	accountAggregatorClient := aaorderpb.NewAccountAggregatorClient(orderConn)
	txnCategorizerClientVar5 := fecategorizerpb.NewTxnCategorizerClient(frontendConn)
	txnAggregatesClientVar6 := ffpinotpb.NewTxnAggregatesClient(cardConn)
	reminderServiceClient := reminder.NewReminderServiceClient(wealthdmfConn)
	portfolioManagerClient := usstocksportfoliopb.NewPortfolioManagerClient(wealthdmfConn)
	wealthdmfConnVar97 := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConnVar97)
	catalogManagerClientVar10 := usstockscatalogpb.NewCatalogManagerClient(wealthdmfConnVar97)
	ussRewardManagerClient := ussrewardspb.NewUssRewardManagerClient(wealthdmfConn)
	billingClient := ffbillpb.NewBillingClient(cardConn)
	loanManagementSystemClient := fflmspb.NewLoanManagementSystemClient(cardConn)
	creditLimitEstimatorClient := limitestimatorpb.NewCreditLimitEstimatorClient(lendingConn)
	cardRecommendationServiceClient := ffrepb.NewCardRecommendationServiceClient(cardConn)
	fireflyV2Client := ffbev2pb.NewFireflyV2Client(cardConn)
	journeyServiceClientVar2 := journeypb.NewJourneyServiceClient(growthinfraConn)
	feedbackEngineClient := befepb.NewFeedbackEngineClient(cxConn)
	kycAgentServiceClient := agentpb.NewKycAgentServiceClient(onboardingConn)
	serviceClientVar2 := userdeclarationpb.NewServiceClient(wealthdmfConn)
	variableGeneratorClient := analyservariablespb.NewVariableGeneratorClient(wealthdmfConn)
	beneficiaryManagementClient := bmspb.NewBeneficiaryManagementClient(orderConn)
	securitiesClient := securities.NewSecuritiesClient(centralgrowthConn)
	oCRClient := ocr.NewOCRClient(vendorgatewayConn)
	insightKubairClient := insightkubairpb.NewInsightKubairClient(wealthdmfConn)
	analyserRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AnalyserRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = analyserRedisStore.Close() }()
	vkycCallClient := vkyccall2.NewVkycCallClient(onboardingConn)
	sgapigatewayConn := epifigrpc.NewServerConn(cfg.FRONTEND_SERVER, cfg.SG_API_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(sgapigatewayConn)
	vkycCallClientVar2 := vkyccall2.NewVkycCallClient(sgapigatewayConn)
	uqudoClient := kycuqudopb.NewUqudoClient(onboardingConn)
	actorActivityClientVar5 := cxaapb.NewActorActivityClient(cxConn)
	dataSharingClient := datasharingpb.NewDataSharingClient(centralgrowthConn)
	salaryEstimationClient := salaryestimationpb.NewSalaryEstimationClient(centralgrowthConn)
	totpClient := totppb.NewTotpClient(authConn)
	commsClientVar4 := comms.NewCommsClient(growthinfraConn)
	caseManagementClient := cmpb.NewCaseManagementClient(userriskConn)
	serviceClientVar4 := currencyinsightsvgpb.NewServiceClient(vendorgatewayConn)
	sessionManagerClient := session.NewSessionManagerClient(authConn)
	preEligibilityClient := preeligibility.NewPreEligibilityClient(lendingConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.UnaryMockFEReqContextInterceptor(gconf)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire.ReqTimeoutUnaryInterceptor(gconf)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3 := servergenwire.RequestHeaderUnaryInterceptor()
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	unaryServerInterceptorVar4 := servergenwire.RPCFeatureControlInterceptor()
	if unaryServerInterceptorVar4 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar4)
	}

	unaryServerInterceptorVar5 := servergenwire.HandshakeInterceptor(authClient)
	if unaryServerInterceptorVar5 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar5)
	}

	unaryServerInterceptorVar6 := servergenwire.FrontendRateLimitServerInterceptor(gconf, rateLimiter)
	if unaryServerInterceptorVar6 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar6)
	}

	unaryServerInterceptorVar7, err := servergenwire.AddFERudderEventsUnaryInterceptor(broker)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar7 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar7)
	}

	unaryServerInterceptorVar8 := servergenwire.AuthUnaryInterceptor(authClient)
	if unaryServerInterceptorVar8 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar8)
	}

	unaryServerInterceptorVar9, err := servergenwire.DeviceIntegrityCheckerInterceptor(gconf, authClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar9 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar9)
	}

	unaryServerInterceptorVar10 := servergenwire2.RateLimitServerInterceptorV2WithDefaultKeyGen(gconf, rateLimiter, attributeRateLimiter)
	if unaryServerInterceptorVar10 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar10)
	}

	unaryServerInterceptorVar11 := servergenwire.LocationInterceptor(gconf, locationClient)
	if unaryServerInterceptorVar11 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar11)
	}

	unaryServerInterceptorVar12 := servergenwire.UnaryIPInterceptor(gconf)
	if unaryServerInterceptorVar12 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar12)
	}

	unaryServerInterceptorVar13, err := servergenwire.DeviceCheckInterceptor(gconf, authClient, consentClient, broker)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar13 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar13)
	}

	unaryServerInterceptorVar14 := servergenwire.AddNonceUnaryInterceptor(gconf, authClient)
	if unaryServerInterceptorVar14 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar14)
	}

	unaryServerInterceptorVar15 := servergenwire.PartnerLogUnaryInterceptor(gconf)
	if unaryServerInterceptorVar15 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar15)
	}

	unaryServerInterceptorVar16 := servergenwire.RequestHeaderInterceptor()
	if unaryServerInterceptorVar16 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar16)
	}

	unaryServerInterceptorVar17 := servergenwire.DeeplinkBackwardCompatibilityInterceptor()
	if unaryServerInterceptorVar17 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar17)
	}

	unaryServerInterceptorVar18 := servergenwire.ResponseHeaderInterceptor()
	if unaryServerInterceptorVar18 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar18)
	}

	unaryServerInterceptorVar19 := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptorVar19 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar19)
	}

	unaryServerInterceptorVar20 := servergenwire2.NewEdgeActorRequestTracingServerInterceptor(gconf)
	if unaryServerInterceptorVar20 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar20)
	}

	unaryServerInterceptorVar21, err := servergenwire2.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar21 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar21)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	streamServerInterceptor := servergenwire.StreamMockFEReqContextInterceptor(gconf)
	if streamServerInterceptor != nil {
		streamInterceptors = append(streamInterceptors, streamServerInterceptor)
	}

	streamServerInterceptorVar2 := servergenwire.RequestTimeoutStreamInterceptor(gconf)
	if streamServerInterceptorVar2 != nil {
		streamInterceptors = append(streamInterceptors, streamServerInterceptorVar2)
	}

	streamServerInterceptorVar3 := servergenwire.AuthStreamInterceptor(authClient)
	if streamServerInterceptorVar3 != nil {
		streamInterceptors = append(streamInterceptors, streamServerInterceptorVar3)
	}

	streamServerInterceptorVar4 := servergenwire.RequestHeaderStreamInterceptor()
	if streamServerInterceptorVar4 != nil {
		streamInterceptors = append(streamInterceptors, streamServerInterceptorVar4)
	}

	streamServerInterceptorVar5 := servergenwire.StreamIPInterceptor(gconf)
	if streamServerInterceptorVar5 != nil {
		streamInterceptors = append(streamInterceptors, streamServerInterceptorVar5)
	}

	streamServerInterceptorVar6 := servergenwire.FrontendRequestHeaderStreamInterceptor()
	if streamServerInterceptorVar6 != nil {
		streamInterceptors = append(streamInterceptors, streamServerInterceptorVar6)
	}

	streamServerInterceptorVar7 := servergenwire.CustomLoggingStreamInterceptor()
	if streamServerInterceptorVar7 != nil {
		streamInterceptors = append(streamInterceptors, streamServerInterceptorVar7)
	}

	streamServerInterceptorVar8 := servergenwire.LocationStreamInterceptor(gconf, locationClient)
	if streamServerInterceptorVar8 != nil {
		streamInterceptors = append(streamInterceptors, streamServerInterceptorVar8)
	}

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewServerWithConfig(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	httpMux := http.NewServeMux()
	_ = httpMux

	err = setupFrontend(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, authClient, kycClient, livenessClient, consentClient, onboardingClient, cardProvisioningClient, fCMDeviceTokenClient, vendorMappingServiceClient, vKYCClient, locationClient, externalAccountsClient, userActionsClient, employmentClient, obfuscatorClient, savingsClient, bankCustomerServiceClient, operationalStatusServiceClient, panClient, connectedAccountClient, productClient, creditReportManagerClient, riskClient, ipServiceClient, userPreferenceClient, inAppReferralClient, seasonServiceClient, rewardsGeneratorClient, contactClient, nudgeServiceClient, piClient, accountPIRelationClient, uPIClient, upiOnboardingClient, balanceClient, accountStatementClient, depositClient, preApprovedLoanClient, catalogManagerClient, fiftyFinClient, securedLoansClient, actionBarClient, locationClientVar4, recurringPaymentServiceClient, payClient, userPreferenceClientVar2, vKYCFeClient, complianceClient, orderServiceClient, paymentClient, txnCategorizerClient, decisionEngineClient, timelineServiceClient, rewardOffersClient, tieringClient, eODBalanceClient, salaryProgramClient, healthEngineServiceClient, p2PInvestmentClient, userIntelServiceClient, alfredClient, projectorServiceClient, txnAggregatesClient, rewardsAggregatesClient, recurringPaymentServiceClientVar2, paymentGatewayServiceClient, accountManagerClient, healthInsuranceClient, consumerClient, simulationClient, commsClient, accessInfoClient, connectedAccountClientVar2, cardControlClient, offerListingServiceClient, actorActivityClient, actorActivityClientVar2, inAppTargetedCommsClient, currencyInsightsClient, questCacheStorage, netWorthClient, ruleManagerClient, accountingClient, storyClient, fireflyClient, homeRedisStore, upcomingTransactionsClient, merchantServiceClient, offerCatalogServiceClient, crossAttachClient, lendabilityClient, homeClient, fCMClient, investmentAggregatorClient, fireflyClientVar2, preApprovedLoanClientVar4, netWorthClientVar3, analyserServiceClient, searchClient, referralClient, dynamicElementsClient, nudgeServiceClientVar3, rewardsClient, homeClientVar2, secretsClient, journeyServiceClient, cardClient, employmentFeClient, paymentClientVar2, wealthOnboardingClient, docExtractionClient, savingsClientVar14, celestialClient, customerAuthCallbackClient, serveFAQClient, recentActivityClient, serviceClient, ticketClient, issueConfigManagementClient, customerAuthenticationClient, fAQClient, ticketClientVar2, profileClient, disputeClient, appLogClient, goalsClient, chatsClient, callRoutingClient, mandateServiceClient, referralsClient, employmentClientVar8, employerNameMatchClient, enachServiceClient, offerRedemptionServiceClient, accrualClient, luckyDrawServiceClient, exchangerOfferServiceClient, offerInventoryServiceClient, externalVendorRedemptionServiceClient, biometricsServiceClient, orchestratorClient, partnerSDKClient, emailParserClient, insightsClient, fitttClient, wealthOnboardingClientVar2, paymentHandlerClient, orderManagerClient, sportsManagerClient, ruleUIManagerClient, timelineServiceClientVar6, catalogManagerClientVar4, internationalFundTransferClient, internationalFundTransferClientVar2, screenerClient, epfClient, investmentAnalyticsClient, investmentProfileServiceClient, appFeedbackClient, inAppHelpMediaClient, transactionClient, authClientVar23, notificationsClient, mFExternalOrdersClient, investmentAggregatorClientVar2, eventProcessorClient, orderManagerClientVar3, dynamicUIElementServiceClient, dynamicElementsClientVar3, accountAggregatorClient, txnCategorizerClientVar5, txnAggregatesClientVar6, reminderServiceClient, portfolioManagerClient, catalogManagerClientVar10, ussRewardManagerClient, billingClient, loanManagementSystemClient, creditLimitEstimatorClient, cardRecommendationServiceClient, fireflyV2Client, journeyServiceClientVar2, feedbackEngineClient, kycAgentServiceClient, serviceClientVar2, variableGeneratorClient, beneficiaryManagementClient, securitiesClient, oCRClient, insightKubairClient, analyserRedisStore, vkycCallClient, vkycCallClientVar2, uqudoClient, actorActivityClientVar5, dataSharingClient, salaryEstimationClient, totpClient)
	if err != nil {
		return err
	}
	err = setupWebfe(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, actorClient, authClient, employmentClient, commsClientVar4, usersClient, userPreferenceClient, consentClient, screenerClient, onboardingClient, fireflyClient, vendorMappingServiceClient, savingsClient, externalAccountsClient, operationalStatusServiceClient, caseManagementClient, cardProvisioningClient, offerListingServiceClient, cardControlClient, serviceClientVar4, totpClient, sessionManagerClient, preEligibilityClient)
	if err != nil {
		return err
	}
	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := servergenhook.FrontendBeforeServerStartHook() // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "FrontendBeforeServerStartHook"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := servergenwire.SetupWireInitDependenciesHook(s, gconf) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "SetupWireInitDependenciesHook"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	cleanupFnVar3, err := servergenwire2.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar3()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	serverStartCleanupFn, err := servergenhook.StartFrontendServer(s, gconf, initNotifier) // Hook configured to start the server
	if err != nil {
		logger.Error(ctx, "failed to start server", zap.Error(err))
		return err
	}
	defer serverStartCleanupFn()

	return nil
}

// nolint: funlen
func setupFrontend(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	authClient authpb.AuthClient,
	kycClient kyc.KycClient,
	livenessClient liveness.LivenessClient,
	consentClient consentpb.ConsentClient,
	onboardingClient useronboardingpb.OnboardingClient,
	cardProvisioningClient provisioning.CardProvisioningClient,
	fCMDeviceTokenClient commstypes.FCMDeviceTokenClientWithInterceptors,
	vendorMappingServiceClient vendormapping.VendorMappingServiceClient,
	vKYCClient bevkycpb.VKYCClient,
	locationClient locationpb.LocationClient,
	externalAccountsClient extacct.ExternalAccountsClient,
	userActionsClient useractions.UserActionsClient,
	employmentClient employment.EmploymentClient,
	obfuscatorClient obfuscatorpb.ObfuscatorClient,
	savingsClient savingspb.SavingsClient,
	bankCustomerServiceClient bankcustpb.BankCustomerServiceClient,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	panClient panpb.PanClient,
	connectedAccountClient beconnectedaccpb.ConnectedAccountClient,
	productClient product.ProductClient,
	creditReportManagerClient creditreportv2pb.CreditReportManagerClient,
	riskClient riskpb.RiskClient,
	ipServiceClient ippb.IpServiceClient,
	userPreferenceClient uppb.UserPreferenceClient,
	inAppReferralClient beinappreferralpb.InAppReferralClient,
	seasonServiceClient seasonpb.SeasonServiceClient,
	rewardsGeneratorClient berewardspb.RewardsGeneratorClient,
	contactClient usercontactpb.ContactClient,
	nudgeServiceClient nudgebepb.NudgeServiceClient,
	piClient bepipb.PiClient,
	accountPIRelationClient beaccountpipb.AccountPIRelationClient,
	uPIClient beupipb.UPIClient,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	balanceClient accountbalancepb.BalanceClient,
	accountStatementClient statementpb.AccountStatementClient,
	depositClient bedepositpb.DepositClient,
	preApprovedLoanClient palbepb.PreApprovedLoanClient,
	catalogManagerClient catalogpb.CatalogManagerClient,
	fiftyFinClient fiftyfin.FiftyFinClient,
	securedLoansClient securedloanspb.SecuredLoansClient,
	actionBarClient besearchpb.ActionBarClient,
	locationClientVar4 location.LocationClient,
	recurringPaymentServiceClient frontendpb.RecurringPaymentServiceClient,
	payClient paypb.PayClient,
	userPreferenceClientVar2 commstypes.UserPreferenceClientWithInterceptors,
	vKYCFeClient bevkycpb.VKYCFeClient,
	complianceClient compliancepb.ComplianceClient,
	orderServiceClient order.OrderServiceClient,
	paymentClient paymentpb.PaymentClient,
	txnCategorizerClient categorizer.TxnCategorizerClient,
	decisionEngineClient paymentpb.DecisionEngineClient,
	timelineServiceClient betimelinepb.TimelineServiceClient,
	rewardOffersClient berewardofferspb.RewardOffersClient,
	tieringClient tieringpb.TieringClient,
	eODBalanceClient tieringpinotpb.EODBalanceClient,
	salaryProgramClient besalarypb.SalaryProgramClient,
	healthEngineServiceClient healthengine.HealthEngineServiceClient,
	p2PInvestmentClient p2ppbbe.P2PInvestmentClient,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	alfredClient alfredpb.AlfredClient,
	projectorServiceClient rewardsprojectionpb.ProjectorServiceClient,
	txnAggregatesClient txnaggregatespb.TxnAggregatesClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	recurringPaymentServiceClientVar2 berecurringpayment.RecurringPaymentServiceClient,
	paymentGatewayServiceClient paymentgateway.PaymentGatewayServiceClient,
	accountManagerClient usstocksaccountpb.AccountManagerClient,
	healthInsuranceClient healthinsurancepb.HealthInsuranceClient,
	consumerClient beupipb.ConsumerClient,
	simulationClient simulation.SimulationClient,
	commsClient commstypes.CommsClientWithInterceptors,
	accessInfoClient beaccessinfopb.AccessInfoClient,
	connectedAccountClientVar2 connectedaccountfepb.ConnectedAccountClient,
	cardControlClient becardctrl.CardControlClient,
	offerListingServiceClient becasperpb.OfferListingServiceClient,
	actorActivityClient actoractivitypb.ActorActivityClient,
	actorActivityClientVar2 feactoractivitypb.ActorActivityClient,
	inAppTargetedCommsClient tcpb.InAppTargetedCommsClient,
	currencyInsightsClient cardcipb.CurrencyInsightsClient,
	questCacheStorage pkgtypes.QuestCacheStorage,
	netWorthClient benetworthpb.NetWorthClient,
	ruleManagerClient manager.RuleManagerClient,
	accountingClient ffaccountspb.AccountingClient,
	storyClient bestorypb.StoryClient,
	fireflyClient ffbepb.FireflyClient,
	homeRedisStore types.HomeRedisStore,
	upcomingTransactionsClient upcomingtransactions.UpcomingTransactionsClient,
	merchantServiceClient mpb.MerchantServiceClient,
	offerCatalogServiceClient becasperpb.OfferCatalogServiceClient,
	crossAttachClient crossattachpb.CrossAttachClient,
	lendabilityClient lendabilitypb.LendabilityClient,
	homeClient fehomepb.HomeClient,
	fCMClient fedevicetokenpb.FCMClient,
	investmentAggregatorClient investmentfepb.InvestmentAggregatorClient,
	fireflyClientVar2 fireflyfepb.FireflyClient,
	preApprovedLoanClientVar4 fepreapprovedloanpb.PreApprovedLoanClient,
	netWorthClientVar3 networthfepb.NetWorthClient,
	analyserServiceClient analyserfepb.AnalyserServiceClient,
	searchClient fesearchpb.SearchClient,
	referralClient fereferralpb.ReferralClient,
	dynamicElementsClient defepb.DynamicElementsClient,
	nudgeServiceClientVar3 nudgefepb.NudgeServiceClient,
	rewardsClient rewardsfepb.RewardsClient,
	homeClientVar2 cxhomefepb.HomeClient,
	secretsClient secretsfepb.SecretsClient,
	journeyServiceClient nudgefepb.JourneyServiceClient,
	cardClient fecardpb.CardClient,
	employmentFeClient employment.EmploymentFeClient,
	paymentClientVar2 vgpaymentpb.PaymentClient,
	wealthOnboardingClient wopb.WealthOnboardingClient,
	docExtractionClient kycdocspb.DocExtractionClient,
	savingsClientVar14 savingsvgpb.SavingsClient,
	celestialClient celestialpb.CelestialClient,
	customerAuthCallbackClient becxpb.CustomerAuthCallbackClient,
	serveFAQClient befaqpb.ServeFAQClient,
	recentActivityClient berapb.RecentActivityClient,
	serviceClient irpb.ServiceClient,
	ticketClient beticketpb.TicketClient,
	issueConfigManagementClient issueconfigpb.IssueConfigManagementClient,
	customerAuthenticationClient becxpb.CustomerAuthenticationClient,
	fAQClient fefaqpb.FAQClient,
	ticketClientVar2 feticketpb.TicketClient,
	profileClient profilepb.ProfileClient,
	disputeClient disputepb.DisputeClient,
	appLogClient bealpb.AppLogClient,
	goalsClient goalspb.GoalsClient,
	chatsClient bechatpb.ChatsClient,
	callRoutingClient becallroutingpb.CallRoutingClient,
	mandateServiceClient mandatepb.MandateServiceClient,
	referralsClient besalaryreferralspb.ReferralsClient,
	employmentClientVar8 vgemploymentpb.EmploymentClient,
	employerNameMatchClient employernamematchvgpb.EmployerNameMatchClient,
	enachServiceClient enachpb.EnachServiceClient,
	offerRedemptionServiceClient beredemptionpb.OfferRedemptionServiceClient,
	accrualClient beaccrualpb.AccrualClient,
	luckyDrawServiceClient luckydrawpb.LuckyDrawServiceClient,
	exchangerOfferServiceClient beexchangerpb.ExchangerOfferServiceClient,
	offerInventoryServiceClient becasperpb.OfferInventoryServiceClient,
	externalVendorRedemptionServiceClient evrpb.ExternalVendorRedemptionServiceClient,
	biometricsServiceClient biometrics.BiometricsServiceClient,
	orchestratorClient authv2pb.OrchestratorClient,
	partnerSDKClient bepartnersdkpb.PartnerSDKClient,
	emailParserClient beemailparserpb.EmailParserClient,
	insightsClient beinsightspb.InsightsClient,
	fitttClient fitttpb.FitttClient,
	wealthOnboardingClientVar2 fewopb.WealthOnboardingClient,
	paymentHandlerClient investpaypb.PaymentHandlerClient,
	orderManagerClient orderpb.OrderManagerClient,
	sportsManagerClient sports.SportsManagerClient,
	ruleUIManagerClient ui.RuleUIManagerClient,
	timelineServiceClientVar6 fetimelinepb.TimelineServiceClient,
	catalogManagerClientVar4 usstockscatalogpb.CatalogManagerClient,
	internationalFundTransferClient vgiftpb.InternationalFundTransferClient,
	internationalFundTransferClientVar2 iftpb.InternationalFundTransferClient,
	screenerClient screener.ScreenerClient,
	epfClient epfpb.EpfClient,
	investmentAnalyticsClient investment.InvestmentAnalyticsClient,
	investmentProfileServiceClient investmentprofilepb.InvestmentProfileServiceClient,
	appFeedbackClient appfeedbackpb.AppFeedbackClient,
	inAppHelpMediaClient mediapb.InAppHelpMediaClient,
	transactionClient fetransactionpb.TransactionClient,
	authClientVar23 auth.AuthClient,
	notificationsClient mfnotipb.NotificationsClient,
	mFExternalOrdersClient mfexternalpb.MFExternalOrdersClient,
	investmentAggregatorClientVar2 invaggrpb.InvestmentAggregatorClient,
	eventProcessorClient investmenteventprocessorpb.EventProcessorClient,
	orderManagerClientVar3 usstocksorderpb.OrderManagerClient,
	dynamicUIElementServiceClient dynamicelementuipb.DynamicUIElementServiceClient,
	dynamicElementsClientVar3 dewiretypes.DynamicElementsClientWithInterceptors,
	accountAggregatorClient aaorderpb.AccountAggregatorClient,
	txnCategorizerClientVar5 fecategorizerpb.TxnCategorizerClient,
	txnAggregatesClientVar6 ffpinotpb.TxnAggregatesClient,
	reminderServiceClient reminder.ReminderServiceClient,
	portfolioManagerClient usstocksportfoliopb.PortfolioManagerClient,
	catalogManagerClientVar10 types.USSCatalogMgrStreamClient,
	ussRewardManagerClient ussrewardspb.UssRewardManagerClient,
	billingClient ffbillpb.BillingClient,
	loanManagementSystemClient fflmspb.LoanManagementSystemClient,
	creditLimitEstimatorClient limitestimatorpb.CreditLimitEstimatorClient,
	cardRecommendationServiceClient ffrepb.CardRecommendationServiceClient,
	fireflyV2Client ffbev2pb.FireflyV2Client,
	journeyServiceClientVar2 journeypb.JourneyServiceClient,
	feedbackEngineClient befepb.FeedbackEngineClient,
	kycAgentServiceClient agentpb.KycAgentServiceClient,
	serviceClientVar2 userdeclarationpb.ServiceClient,
	variableGeneratorClient analyservariablespb.VariableGeneratorClient,
	beneficiaryManagementClient bmspb.BeneficiaryManagementClient,
	securitiesClient securities.SecuritiesClient,
	oCRClient ocr.OCRClient,
	insightKubairClient insightkubairpb.InsightKubairClient,
	analyserRedisStore types.AnalyserRedisStore,
	vkycCallClient verifipkg.VkycCallClientToOnboardingServer,
	vkycCallClientVar2 verifipkg.VkycCallClientToSGApiGatewayServer,
	uqudoClient kycuqudopb.UqudoClient,
	actorActivityClientVar5 cxaapb.ActorActivityClient,
	dataSharingClient datasharingpb.DataSharingClient,
	salaryEstimationClient salaryestimationpb.SalaryEstimationClient,
	totpClient totppb.TotpClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	frontendConf, err := frontendconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.FRONTEND_SERVICE))
		return err
	}
	_ = frontendConf

	frontendGenConf, err := dynconf.LoadConfigWithQuestConfig(frontendconf.Load, genconf2.NewConfigWithQuest, cfg.FRONTEND_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.FRONTEND_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		frontendGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: frontendGenConf, SdkConfig: frontendGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{frontendGenConfAppConfig}, string(cfg.FRONTEND_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = frontendGenConf

	epifiIconsS3Client := s3pkg.NewClient(awsConf, frontendGenConf.EpifiIconsBucketName())
	salaryProgramS3Client := s3pkg.NewClient(awsConf, frontendGenConf.SalaryProgramBucketName())

	service, err := wire.InitializeSignupService(authClient, usersClient, kycClient, livenessClient, actorClient, consentClient, onboardingClient, cardProvisioningClient, broker, fCMDeviceTokenClient, vendorMappingServiceClient, vKYCClient, locationClient, externalAccountsClient, frontendGenConf, userActionsClient, employmentClient, obfuscatorClient, savingsClient, bankCustomerServiceClient, operationalStatusServiceClient, panClient, connectedAccountClient, groupClient, productClient, creditReportManagerClient, riskClient, ipServiceClient, userPreferenceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	fesignuppb.RegisterSignupServer(s, service)

	serviceVar2 := wire.InitialiseAcquisitionService(broker, frontendGenConf, authClient)

	feacquisitionpb.RegisterAcquisitionServer(s, serviceVar2)

	serviceVar3 := wire.InitializeReferralService(actorClient, onboardingClient, inAppReferralClient, seasonServiceClient, rewardsGeneratorClient, usersClient, groupClient, contactClient, nudgeServiceClient, frontendConf, frontendGenConf, broker)

	fereferralpb.RegisterReferralServer(s, serviceVar3)

	serviceVar4 := wire.InitializeUPIService(actorClient, savingsClient, piClient, accountPIRelationClient, uPIClient, frontendConf, bankCustomerServiceClient, usersClient, upiOnboardingClient, onboardingClient)

	feaccupipb.RegisterUPIServer(s, serviceVar4)

	serviceVar5 := wire.InitializeSavingsService(usersClient, savingsClient, authClient, cardProvisioningClient, accountPIRelationClient, piClient, actorClient, frontendConf, onboardingClient, balanceClient, kycClient, bankCustomerServiceClient)

	fesavingpb.RegisterSavingsServer(s, serviceVar5)

	serviceVar6 := wire.InitializeStatementService(savingsClient, accountStatementClient, frontendConf, depositClient, actorClient)

	festatementpb.RegisterStatementServer(s, serviceVar6)

	serviceVar7 := wire.InitializePreApprovedLoanService(preApprovedLoanClient, consentClient, vKYCClient, savingsClient, actorClient, broker, frontendConf, usersClient, employmentClient, frontendGenConf, onboardingClient, segmentationServiceClient, groupClient, balanceClient, catalogManagerClient, authClient, fiftyFinClient, securedLoansClient, actionBarClient, locationClientVar4, recurringPaymentServiceClient, payClient)

	fepreapprovedloanpb.RegisterPreApprovedLoanServer(s, serviceVar7)

	serviceVar8 := wire.InitializeConsentService(authClient, consentClient, userPreferenceClientVar2, onboardingClient, actorClient, upiOnboardingClient, usersClient)

	feconsentpb.RegisterConsentServer(s, serviceVar8)

	ackService := wire.InitialiseAckService(vKYCFeClient, onboardingClient, complianceClient)

	ack2.RegisterAckServer(s, ackService)

	serviceVar9, err := wire.InitializePayTransactionService(frontendGenConf, orderServiceClient, paymentClient, actorClient, savingsClient, txnCategorizerClient, accountPIRelationClient, piClient, decisionEngineClient, authClient, uPIClient, timelineServiceClient, usersClient, onboardingClient, broker, rewardOffersClient, groupClient, payClient, upiOnboardingClient, bankCustomerServiceClient, tieringClient, eODBalanceClient, salaryProgramClient, healthEngineServiceClient, p2PInvestmentClient, frontendConf, inAppReferralClient, userIntelServiceClient, operationalStatusServiceClient, balanceClient, cardProvisioningClient, alfredClient, rewardsGeneratorClient, projectorServiceClient, txnAggregatesClient, segmentationServiceClient, rewardsAggregatesClient, recurringPaymentServiceClientVar2, paymentGatewayServiceClient, accountManagerClient, employmentClient, healthInsuranceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	fetransactionpb.RegisterTransactionServer(s, serviceVar9)

	serviceVar10 := wire.InitializeSimulationService(orderServiceClient, savingsClient, piClient, consumerClient, simulationClient)

	fepaypb.RegisterSimulationServer(s, serviceVar10)

	serviceVar11 := wire.InitializeContactsService()

	fecontactspb.RegisterContactsServer(s, serviceVar11)

	serviceVar12 := wire.InitializeDeviceTokenService(fCMDeviceTokenClient, commsClient, frontendConf)

	fedevicetokenpb.RegisterFCMServer(s, serviceVar12)

	serviceVar13 := wire.InitializeSearchService(actionBarClient, timelineServiceClient, actorClient, cardProvisioningClient, accessInfoClient, accountPIRelationClient, connectedAccountClientVar2, broker, frontendGenConf, usersClient, groupClient, preApprovedLoanClient, connectedAccountClient, savingsClient, onboardingClient)

	fesearchpb.RegisterSearchServer(s, serviceVar13)

	serviceVar14, err := wire.InitializeCardService(cardProvisioningClient, cardControlClient, uPIClient, frontendConf, usersClient, actorClient, groupClient, onboardingClient, livenessClient, savingsClient, kycClient, vKYCClient, frontendGenConf, tieringClient, salaryProgramClient, bankCustomerServiceClient, offerListingServiceClient, actorActivityClient, rewardsGeneratorClient, rewardsAggregatesClient, segmentationServiceClient, authClient, actorActivityClientVar2, balanceClient, inAppTargetedCommsClient, broker, panClient, currencyInsightsClient, payClient, questCacheStorage, managerClient, netWorthClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	fecardpb.RegisterCardServer(s, serviceVar14)

	serviceVar15 := wire.InitializeHomeService(frontendConf, frontendGenConf, savingsClient, actionBarClient, orderServiceClient, actorClient, timelineServiceClient, piClient, accountPIRelationClient, depositClient, groupClient, usersClient, txnAggregatesClient, ruleManagerClient, catalogManagerClient, recurringPaymentServiceClientVar2, onboardingClient, nudgeServiceClient, kycClient, salaryProgramClient, accountingClient, vKYCFeClient, tieringClient, bankCustomerServiceClient, storyClient, employmentClient, commsClient, connectedAccountClient, segmentationServiceClient, fireflyClient, balanceClient, homeRedisStore, upcomingTransactionsClient, merchantServiceClient, managerClient, authClient, vendorMappingServiceClient, questCacheStorage, broker, preApprovedLoanClient, consentClient, cardProvisioningClient, offerCatalogServiceClient, upiOnboardingClient, productClient, alfredClient, rewardsAggregatesClient, projectorServiceClient, rewardsGeneratorClient, accountManagerClient, crossAttachClient, healthInsuranceClient, rewardOffersClient, netWorthClient, lendabilityClient)

	fehomepb.RegisterHomeServer(s, serviceVar15)

	serviceVar16 := wire.InitializeHomeOrchestratorService(homeClient, fCMClient, investmentAggregatorClient, fireflyClientVar2, preApprovedLoanClientVar4, netWorthClientVar3, connectedAccountClientVar2, analyserServiceClient, searchClient, referralClient, dynamicElementsClient, nudgeServiceClientVar3, rewardsClient, homeClientVar2, secretsClient, journeyServiceClient, cardClient)

	fehomeorchestratorpb.RegisterHomeOrchestratorServer(s, serviceVar16)

	serviceVar17 := wire.InitializePromptService(frontendConf, frontendGenConf, consentClient, employmentClient, actorClient, usersClient, vKYCClient, groupClient, kycClient, savingsClient, bankCustomerServiceClient, employmentFeClient)

	prompt2.RegisterPromptServer(s, serviceVar17)

	serviceVar18 := wire.InitializeTimelineService(frontendConf, piClient, actorClient, timelineServiceClient, orderServiceClient, uPIClient, usersClient, savingsClient, merchantServiceClient, groupClient, frontendGenConf, accountingClient, upiOnboardingClient, actionBarClient, paymentClientVar2, onboardingClient, broker)

	fetimelinepb.RegisterTimelineServiceServer(s, serviceVar18)

	serviceVar19, err := wire.InitializeUserService(frontendConf, onboardingClient, usersClient, savingsClient, depositClient, commsClient, piClient, accountPIRelationClient, actorClient, uPIClient, contactClient, cardProvisioningClient, broker, kycClient, groupClient, salaryProgramClient, vendorMappingServiceClient, authClient, connectedAccountClient, employmentClient, externalAccountsClient, upiOnboardingClient, frontendGenConf, accountingClient, fireflyClient, tieringClient, vKYCFeClient, employmentFeClient, bankCustomerServiceClient, wealthOnboardingClient, connectedAccountClientVar2, complianceClient, panClient, consentClient, creditReportManagerClient, locationClientVar4, questCacheStorage, managerClient, segmentationServiceClient, docExtractionClient, productClient, savingsClientVar14, celestialClient, balanceClient, alfredClient, rewardsGeneratorClient, projectorServiceClient, txnAggregatesClient, rewardsAggregatesClient, orderServiceClient, accountManagerClient, healthInsuranceClient, rewardOffersClient, netWorthClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	feuserpb.RegisterUserServer(s, serviceVar19)

	serviceVar20 := wire.InitializeCustomerAuthCallBackService(customerAuthCallbackClient)

	fecxpb.RegisterCustomerAuthCallbackServer(s, serviceVar20)

	serviceVar21 := wire.InitializeFaqService(serveFAQClient, recentActivityClient, frontendGenConf, broker)

	fefaqpb.RegisterFAQServer(s, serviceVar21)

	serviceVar22 := wire.InitializeContactUsService(serviceClient, frontendGenConf, ticketClient, broker, usersClient, issueConfigManagementClient, groupClient, actorClient, customerAuthenticationClient, fAQClient, ticketClientVar2, profileClient)

	fecontactus.RegisterContactUsServer(s, serviceVar22)

	serviceVar23 := wire.InitializeDisputeService(disputeClient, paymentClient, frontendGenConf, orderServiceClient)

	fedisputepb.RegisterDisputeServer(s, serviceVar23)

	serviceVar24 := wire.InitializeAppLogService(appLogClient, authClient, frontendGenConf)

	fealpb.RegisterAppLogsServer(s, serviceVar24)

	serviceVar25 := wire.InitializeDepositService(depositClient, orderServiceClient, piClient, savingsClient, usersClient, uPIClient, accountPIRelationClient, groupClient, actorClient, goalsClient, ruleManagerClient, accountStatementClient, healthEngineServiceClient, frontendGenConf, accountingClient, balanceClient)

	fedepositpb.RegisterDepositServer(s, serviceVar25)

	serviceVar26 := wire.InitializeChatService(chatsClient, frontendGenConf)

	fechatpb.RegisterChatsServer(s, serviceVar26)

	serviceVar27 := wire.InitializeCallService(frontendGenConf, callRoutingClient, customerAuthenticationClient)

	fecallpb.RegisterCallServer(s, serviceVar27)

	serviceVar28 := wire.InitializeActorActivitiesService(actorActivityClient, savingsClient, cardProvisioningClient, piClient, frontendConf, depositClient, connectedAccountClient, txnCategorizerClient, actorClient, groupClient, usersClient, accountPIRelationClient, frontendGenConf, accountingClient, timelineServiceClient, upiOnboardingClient, onboardingClient, orderServiceClient, rewardsGeneratorClient, rewardsAggregatesClient, searchClient)

	feactoractivitypb.RegisterActorActivityServer(s, serviceVar28)

	serviceVar29 := wire.InitializeRecurrentPaymentsService(recurringPaymentServiceClientVar2, actorClient, piClient, accountPIRelationClient, orderServiceClient, savingsClient, paymentClient, frontendGenConf, uPIClient, mandateServiceClient, upiOnboardingClient, ticketClient, usersClient, groupClient, bankCustomerServiceClient, preApprovedLoanClient)

	frontendpb.RegisterRecurringPaymentServiceServer(s, serviceVar29)

	serviceVar30 := wire.InitializeSalaryProgramService(salaryProgramClient, referralsClient, rewardsGeneratorClient, healthInsuranceClient, rewardOffersClient, employmentClient, actorClient, savingsClient, usersClient, groupClient, balanceClient, alfredClient, cardProvisioningClient, projectorServiceClient, txnAggregatesClient, frontendConf, epifiIconsS3Client, salaryProgramS3Client, frontendGenConf, actorActivityClient, inAppReferralClient, bankCustomerServiceClient, employmentClientVar8, employerNameMatchClient, preApprovedLoanClient, recurringPaymentServiceClientVar2, enachServiceClient, orderServiceClient, txnCategorizerClient, segmentationServiceClient, upiOnboardingClient, accountPIRelationClient, connectedAccountClient, tieringClient, rewardsAggregatesClient, consentClient, accountManagerClient)

	fesalarypgpb2.RegisterSalaryProgramServer(s, serviceVar30)

	rewardService := wire.InitializeRewardsService(rewardsGeneratorClient, rewardOffersClient, offerRedemptionServiceClient, accrualClient, tieringClient, offerListingServiceClient, offerCatalogServiceClient, salaryProgramClient, savingsClient, luckyDrawServiceClient, usersClient, depositClient, exchangerOfferServiceClient, actorClient, frontendConf, frontendGenConf, offerInventoryServiceClient, bankCustomerServiceClient, fireflyClient, vendorMappingServiceClient, cardProvisioningClient, groupClient, externalVendorRedemptionServiceClient, segmentationServiceClient, onboardingClient, preApprovedLoanClient, questCacheStorage, managerClient, broker, netWorthClient)

	rewardsfepb.RegisterRewardsServer(s, rewardService)

	serviceVar31 := wire.InitializeAuthService(authClient, biometricsServiceClient, frontendGenConf, orchestratorClient, usersClient, livenessClient, actorClient)

	feauthpb.RegisterAuthServer(s, serviceVar31)

	serviceVar32 := wire.InitializePartnerSDKService(partnerSDKClient)

	fepartnersdkpb.RegisterPartnerSDKServer(s, serviceVar32)

	serviceVar33 := wire.InitializeAccessInfoService(accessInfoClient, authClient, actorClient, usersClient)

	feaccessinfopb.RegisterAccessInfoServer(s, serviceVar33)

	serviceVar34 := wire.InitializeEmailParserService(emailParserClient)

	feemailparserpb.RegisterEmailParserServer(s, serviceVar34)

	serviceVar35 := wire.InitializeStoryService(frontendGenConf, storyClient)

	storypb.RegisterStoryServer(s, serviceVar35)

	serviceVar36 := wire.InitializeInsightsService(frontendConf, insightsClient)

	feinsightspb.RegisterInsightsServer(s, serviceVar36)

	serviceVar37 := wire.InitializeVkycService(vKYCClient, onboardingClient, usersClient, actorClient, groupClient, frontendGenConf, kycClient, savingsClient, locationClientVar4, frontendConf, bankCustomerServiceClient, panClient, balanceClient)

	fevkycpb.RegisterVkycServer(s, serviceVar37)

	serviceVar38, err := wire.InitializeFitttService(frontendGenConf, ruleManagerClient, depositClient, fitttClient, usersClient, actorClient, groupClient, actionBarClient, catalogManagerClient, wealthOnboardingClientVar2, paymentHandlerClient, orderManagerClient, broker, sportsManagerClient, ruleUIManagerClient, rewardsGeneratorClient, luckyDrawServiceClient, savingsClient, recurringPaymentServiceClientVar2, timelineServiceClientVar6, onboardingClient, balanceClient, catalogManagerClientVar4, bankCustomerServiceClient, internationalFundTransferClient, internationalFundTransferClientVar2, payClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	fefitttpb.RegisterFitttServer(s, serviceVar38)

	clientMetricsService := wire.InitializeClientMetricsService(ruleManagerClient, fitttClient, ruleUIManagerClient, frontendConf)

	clientmetrics2.RegisterClientMetricsServiceServer(s, clientMetricsService)

	serviceVar39 := wire.InitializeScreeningService(employmentClient, onboardingClient, actorClient, consentClient, usersClient, screenerClient, broker, frontendGenConf, frontendConf, creditReportManagerClient)

	fescreeningpb.RegisterScreeningServer(s, serviceVar39)

	serviceVar40 := wire.InitializeConnectedAccService(connectedAccountClient, usersClient, actorClient, savingsClient, authClient, consentClient, groupClient, onboardingClient, screenerClient, frontendConf, frontendGenConf, celestialClient, accountManagerClient, segmentationServiceClient, depositClient, netWorthClient, balanceClient, preApprovedLoanClient, broker, epfClient, investmentAnalyticsClient, creditReportManagerClient)

	connectedaccountfepb.RegisterConnectedAccountServer(s, serviceVar40)

	wealthOnboardingService := wire.InitializeWealthOnboardingService(wealthOnboardingClient, consentClient, actorClient, usersClient, frontendGenConf, authClient, broker, investmentProfileServiceClient)

	fewopb.RegisterWealthOnboardingServer(s, wealthOnboardingService)

	serviceVar41 := wire.InitializeClientLoggerService(authClient)

	clientlogger2.RegisterClientLoggerServer(s, serviceVar41)

	serviceVar42 := wire.InitializeQRService(cardProvisioningClient, cardClient, timelineServiceClientVar6, frontendConf, recurringPaymentServiceClientVar2, uPIClient, piClient, actorClient, merchantServiceClient, orderServiceClient, mandateServiceClient, timelineServiceClient, accountPIRelationClient, fireflyClient, frontendGenConf, upiOnboardingClient, onboardingClient, savingsClient, broker)

	feqrpb.RegisterQRServer(s, serviceVar42)

	serviceVar43 := wire.InitializeAppFeedbackService(appFeedbackClient)

	feappfeedback2.RegisterAppFeedbackServer(s, serviceVar43)

	serviceVar44, err := wire.InitializeApiKeysService(frontendConf, frontendGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	apikeys2.RegisterApiKeysServer(s, serviceVar44)

	serviceVar45 := wire.InitializeInAppHelpMediaService(inAppHelpMediaClient)

	femedia.RegisterInAppHelpMediaServer(s, serviceVar45)

	serviceVar46 := wire.InitializeInvestmentService(catalogManagerClient, ruleManagerClient, paymentHandlerClient, orderManagerClient, wealthOnboardingClient, wealthOnboardingClientVar2, vKYCClient, transactionClient, payClient, savingsClient, orderServiceClient, timelineServiceClient, actorClient, authClient, authClientVar23, frontendGenConf, usersClient, groupClient, broker, notificationsClient, fitttClient, consentClient, mFExternalOrdersClient, onboardingClient, balanceClient, creditReportManagerClient, connectedAccountClient, netWorthClient)

	femf.RegisterInvestmentServer(s, serviceVar46)

	serviceVar47 := wire.InitializeInvestmentAggregatorService(investmentAggregatorClientVar2, vKYCClient, savingsClient, actorClient, paymentHandlerClient, frontendGenConf, usersClient, p2PInvestmentClient, dynamicElementsClient, groupClient, segmentationServiceClient, catalogManagerClient, catalogManagerClientVar4, broker, ruleManagerClient, eventProcessorClient, depositClient, orderManagerClientVar3, onboardingClient, accountManagerClient, dynamicUIElementServiceClient, balanceClient, connectedAccountClient)

	investmentfepb.RegisterInvestmentAggregatorServer(s, serviceVar47)

	serviceVar48 := wire.InitializeDynamicElementsService(dynamicElementsClientVar3, frontendGenConf)

	defepb.RegisterDynamicElementsServer(s, serviceVar48)

	serviceVar49 := wire.InitializeP2PInvestmentService(p2PInvestmentClient, usersClient, actorClient, orderServiceClient, profileClient, consentClient, groupClient, frontendGenConf, onboardingClient, tieringClient)

	fep2ppb.RegisterP2PInvestmentServer(s, serviceVar49)

	serviceVar50 := wire.InitializeCategorizerService(txnCategorizerClient, orderServiceClient, accountAggregatorClient, frontendConf)

	fecategorizerpb.RegisterTxnCategorizerServer(s, serviceVar50)

	serviceVar51, err := wire.InitializeAnalyserService(frontendConf, frontendGenConf, actorClient, groupClient, connectedAccountClient, savingsClient, txnAggregatesClient, txnCategorizerClientVar5, broker, merchantServiceClient, txnCategorizerClient, usersClient, creditReportManagerClient, appFeedbackClient, storyClient, investmentAnalyticsClient, catalogManagerClient, txnAggregatesClientVar6, fireflyClient, accountingClient, mFExternalOrdersClient, upcomingTransactionsClient, depositClient, piClient, onboardingClient, balanceClient, preApprovedLoanClient, tieringClient, reminderServiceClient, segmentationServiceClient, portfolioManagerClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	analyserfepb.RegisterAnalyserServiceServer(s, serviceVar51)

	configSvc, err := wire.InitializeConfigService(frontendGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	fecfgpb.RegisterConfigServer(s, configSvc)

	serviceVar52 := wire.InitializeGoalsService(goalsClient, usersClient, depositClient, actorClient, groupClient, frontendGenConf, bankCustomerServiceClient)

	fegoalspb.RegisterGoalsServer(s, serviceVar52)

	serviceVar53 := wire.InitializeTicketService(ticketClient, frontendGenConf)

	feticketpb.RegisterTicketServer(s, serviceVar53)

	serviceVar54, err := wire.InitializeUSStocksService(accountManagerClient, frontendGenConf, internationalFundTransferClientVar2, actorClient, catalogManagerClientVar4, catalogManagerClientVar10, orderManagerClientVar3, portfolioManagerClient, savingsClient, authClient, usersClient, groupClient, payClient, bankCustomerServiceClient, broker, dynamicUIElementServiceClient, onboardingClient, balanceClient, preApprovedLoanClient, ussRewardManagerClient, upiOnboardingClient, ruleManagerClient, fitttClient, salaryProgramClient, tieringClient, cardProvisioningClient, alfredClient, rewardsGeneratorClient, rewardsAggregatesClient, projectorServiceClient, txnAggregatesClient, orderServiceClient, employmentClient, healthInsuranceClient, rewardOffersClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	usstocksfepb2.RegisterUSStocksServer(s, serviceVar54)

	serviceVar55 := wire.InitializeDocumentUploadService(frontendGenConf, wealthOnboardingClient, accountManagerClient, actorClient, savingsClient, accountStatementClient, connectedAccountClient, internationalFundTransferClientVar2, celestialClient, salaryProgramClient)

	docuploadfepb.RegisterDocumentUploadServer(s, serviceVar55)

	serviceVar56 := wire.InitializeUpiOnboardingService(upiOnboardingClient, actorClient, consentClient, frontendConf, frontendGenConf, savingsClient, authClient, paymentClient, onboardingClient, connectedAccountClient, groupClient, usersClient, broker, accountPIRelationClient)

	feupionbpb.RegisterOnboardingServer(s, serviceVar56)

	serviceVar57 := wire.InitialiseFireflyService(fireflyClient, accountingClient, txnCategorizerClientVar5, billingClient, actorClient, rewardsGeneratorClient, savingsClient, txnCategorizerClient, usersClient, merchantServiceClient, loanManagementSystemClient, consentClient, frontendConf, frontendGenConf, txnAggregatesClientVar6, creditLimitEstimatorClient, onboardingClient, depositClient, balanceClient, projectorServiceClient, cardRecommendationServiceClient, transactionClient, payClient, groupClient, creditReportManagerClient, segmentationServiceClient, offerListingServiceClient, offerRedemptionServiceClient, fireflyV2Client, questCacheStorage, managerClient, broker, rewardsAggregatesClient, netWorthClient)

	fireflyfepb.RegisterFireflyServer(s, serviceVar57)

	serviceVar58 := wire.InitializeAuthOrchestratorService(orchestratorClient, frontendGenConf)

	authorchfe.RegisterOrchestratorServer(s, serviceVar58)

	serviceVar59 := wire.InitializeNudgeService(nudgeServiceClient, usersClient, frontendConf, frontendGenConf, actorClient, groupClient, onboardingClient, segmentationServiceClient, questCacheStorage, managerClient, broker, netWorthClient)

	nudgefepb.RegisterNudgeServiceServer(s, serviceVar59)

	serviceVar60 := wire.InitializeJourneyService(frontendGenConf, journeyServiceClientVar2)

	nudgefepb.RegisterJourneyServiceServer(s, serviceVar60)

	serviceVar61 := wire.InitializeCreditReportService(creditReportManagerClient, frontendGenConf)

	creditreportfepb.RegisterCreditReportServer(s, serviceVar61)

	serviceVar62 := wire.InitializeTieringService(frontendGenConf, tieringClient, savingsClient, actorClient, bankCustomerServiceClient, salaryProgramClient, rewardOffersClient, p2PInvestmentClient, balanceClient, usersClient, groupClient, segmentationServiceClient, cardProvisioningClient, alfredClient, rewardsGeneratorClient, projectorServiceClient, txnAggregatesClient, orderServiceClient, rewardsAggregatesClient, broker, accountManagerClient, rewardsClient, employmentClient, healthInsuranceClient, eODBalanceClient, dynamicElementsClient)

	fetieringpb.RegisterTieringServer(s, serviceVar62)

	serviceVar63 := wire.InitializeBankCustomerService(frontendGenConf, bankCustomerServiceClient, alfredClient, consentClient, complianceClient)

	febankcustomerpb.RegisterBankCustomerServer(s, serviceVar63)

	serviceVar64 := wire.InitialiseUpiService(actorClient, savingsClient, uPIClient)

	feupipb.RegisterUpiServer(s, serviceVar64)

	serviceVar65 := wire.InitializeAlfredService(frontendGenConf, alfredClient, actorClient, usersClient, groupClient)

	alfred2.RegisterAlfredServer(s, serviceVar65)

	serviceVar66 := wire.InitializeDocsService(alfredClient, panClient, userIntelServiceClient, onboardingClient, preApprovedLoanClient, celestialClient, broker)

	docs2.RegisterDocsServer(s, serviceVar66)

	serviceVar67 := wire.InitialiseEpfService(frontendGenConf, epfClient, consentClient, usersClient, actorClient, groupClient)

	feepfpb2.RegisterEpfServer(s, serviceVar67)

	serviceVar68 := wire.InitializeFeedbackEngineService(frontendGenConf, feedbackEngineClient, actorClient, usersClient, groupClient)

	fefeedbackengine.RegisterFeedbackEngineServer(s, serviceVar68)

	serviceVar69 := wire.InitialiseGenieService(frontendGenConf, authClient, usersClient, bankCustomerServiceClient, kycClient, actorClient, kycAgentServiceClient, groupClient, consentClient, panClient, employmentClient)

	fegeniepb.RegisterGenieServer(s, serviceVar69)

	serviceVar70 := wire.InitializeInvestmentProfileService(investmentProfileServiceClient)

	investmentprofile.RegisterInvestmentProfileServer(s, serviceVar70)

	serviceVar71 := wire.InitialiseNetWorthService(frontendGenConf, netWorthClient, usersClient, consentClient, epfClient, creditReportManagerClient, groupClient, actorClient, connectedAccountClient, mFExternalOrdersClient, onboardingClient, secretsClient, serviceClientVar2, employmentClient, investmentAnalyticsClient, catalogManagerClient, segmentationServiceClient, variableGeneratorClient)

	networthfepb.RegisterNetWorthServer(s, serviceVar71)

	serviceVar72 := wire.InitializeOTPService(catalogManagerClient, wealthOnboardingClient, ruleManagerClient, authClient, authClientVar23)

	otp2.RegisterOTPServiceServer(s, serviceVar72)

	serviceVar73 := wire.InitializePanService(frontendConf, panClient)

	pan2.RegisterPanServer(s, serviceVar73)

	serviceVar74 := wire.InitializeReminderService(reminderServiceClient)

	febudgetingreminder.RegisterReminderServiceServer(s, serviceVar74)

	serviceVar75 := wire.InitializeWaitlistService()

	waitlistfepb.RegisterWaitlistServer(s, serviceVar75)

	serviceVar76 := wire.InitialisePayService(beneficiaryManagementClient, upiOnboardingClient, frontendConf, actorClient, uPIClient, onboardingClient, accountPIRelationClient, savingsClient, frontendGenConf, connectedAccountClient, payClient, recurringPaymentServiceClientVar2, usersClient, groupClient, segmentationServiceClient, questCacheStorage, managerClient, broker, netWorthClient)

	fepaypb.RegisterPayServer(s, serviceVar76)

	savingsAccountClosureService := wire.InitialiseSavingsAccountClosureService(frontendGenConf, savingsClient, accrualClient, investmentAggregatorClientVar2, preApprovedLoanClient, ruleManagerClient, accountingClient, fireflyClient, balanceClient, ticketClient, usersClient, operationalStatusServiceClient, bankCustomerServiceClient)

	saclosurepb.RegisterSavingsAccountClosureServer(s, savingsAccountClosureService)

	serviceVar77 := wire.InitialiseIndianStocksService(frontendGenConf, connectedAccountClient, securitiesClient, actorClient, usersClient, groupClient)

	indianstocks2.RegisterServiceServer(s, serviceVar77)

	serviceVar78 := wire.InitializeMediaService(oCRClient, panClient, frontendGenConf, onboardingClient)

	media2.RegisterMediaServer(s, serviceVar78)

	serviceVar79 := wire.InitializeCXHomeService(ticketClient, frontendGenConf, actorClient, usersClient, groupClient)

	cxhomefepb.RegisterHomeServer(s, serviceVar79)

	serviceVar80 := wire.InitializeLivenessService(livenessClient)

	felivpb.RegisterLivenessServer(s, serviceVar80)

	serviceVar81 := wire.InitializeKubairService(frontendGenConf, insightKubairClient)

	fekubairpb.RegisterKubairServer(s, serviceVar81)

	serviceVar82 := wire.InitializerInsightSecretsService(frontendGenConf, usersClient, actorClient, groupClient, epfClient, creditReportManagerClient, segmentationServiceClient, txnCategorizerClientVar5, txnCategorizerClient, savingsClient, txnAggregatesClient, fireflyClient, accountingClient, connectedAccountClient, txnAggregatesClientVar6, investmentAnalyticsClient, catalogManagerClient, netWorthClient, mFExternalOrdersClient, analyserRedisStore, consentClient, serviceClientVar2, nudgeServiceClient, onboardingClient, preApprovedLoanClient, variableGeneratorClient)

	secretsfepb.RegisterSecretsServer(s, serviceVar82)

	serviceVar83, err := wire.InitializeVKYCCallService(frontendGenConf, vkycCallClient, vkycCallClientVar2, obfuscatorClient, locationClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	fevkyccallpb.RegisterVkycCallServer(s, serviceVar83)

	serviceVar84 := wire.InitializeUqudoService(frontendGenConf, uqudoClient, docExtractionClient)

	feuqudopb.RegisterUqudoServer(s, serviceVar84)

	serviceVar85 := wire.InitializeInAppHelpActorActivityService(frontendGenConf, actorActivityClientVar5, actorClient, usersClient, groupClient)

	feinapphelpactoractivitypb.RegisterActorActivityServer(s, serviceVar85)

	serviceVar86 := wire.InitialiseSmsFetcherService(analyserRedisStore, consentClient, epfClient)

	smsfetcherpb.RegisterSmsFetcherServer(s, serviceVar86)

	serviceVar87 := wire.InitialiseDataSharingService(dataSharingClient)

	datasharing2.RegisterDataSharingServer(s, serviceVar87)

	serviceVar88 := wire.InitialiseSalaryEstimationService(salaryEstimationClient, consentClient, broker)

	salaryestimation2.RegisterSalaryEstimationServer(s, serviceVar88)

	serviceVar89 := wire.InitializeDigilockerService(frontendConf)

	digilockerfepb.RegisterDigilockerServer(s, serviceVar89)

	serviceVar90 := wire.InitializeStockguardianMatrixService(frontendConf)

	matrixfepb.RegisterMatrixServer(s, serviceVar90)

	serviceVar91 := wire.InitializeTotpService(totpClient)

	totp2.RegisterTotpServer(s, serviceVar91)

	configNameToConfMap[cfg.ConfigName(cfg.FRONTEND_SERVICE)] = &commonexplorer.Config{StaticConf: &frontendconf.Config{}, QuestIntegratedConfig: frontendGenConf}
	err = frontend.AfterServiceGroupInit(configNameToConfMap)
	if err != nil {
		logger.Error(ctx, "failed to run AfterServicesInitHook", zap.Error(err))
		return err
	}

	return nil

}

// nolint: funlen
func setupWebfe(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	actorClient actor.ActorClient,
	authClient authpb.AuthClient,
	employmentClient employment.EmploymentClient,
	commsClientVar4 comms.CommsClient,
	usersClient user.UsersClient,
	userPreferenceClient uppb.UserPreferenceClient,
	consentClient consentpb.ConsentClient,
	screenerClient screener.ScreenerClient,
	onboardingClient useronboardingpb.OnboardingClient,
	fireflyClient ffbepb.FireflyClient,
	vendorMappingServiceClient vendormapping.VendorMappingServiceClient,
	savingsClient savingspb.SavingsClient,
	externalAccountsClient extacct.ExternalAccountsClient,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	caseManagementClient cmpb.CaseManagementClient,
	cardProvisioningClient provisioning.CardProvisioningClient,
	offerListingServiceClient becasperpb.OfferListingServiceClient,
	cardControlClient becardctrl.CardControlClient,
	serviceClientVar4 currencyinsightsvgpb.ServiceClient,
	totpClient totppb.TotpClient,
	sessionManagerClient session.SessionManagerClient,
	preEligibilityClient preeligibility.PreEligibilityClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	webfeConf, err := webfeconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.WEBFE_SERVICE))
		return err
	}
	_ = webfeConf

	s3Client := s3pkg.NewClient(awsConf, webfeConf.RiskS3Config.BucketName)
	dcDocS3Client := s3pkg.NewClient(awsConf, webfeConf.DebitCardS3Buckets.DcDocsBucketName)

	serviceVar92 := wire2.InitialiseService(actorClient, authClient, employmentClient, commsClientVar4, usersClient, userPreferenceClient, consentClient, screenerClient, onboardingClient, fireflyClient, vendorMappingServiceClient)

	webfe2.RegisterWebfeServer(s, serviceVar92)

	serviceVar93 := wire2.InitialiseWebAuthService(authClient, usersClient, actorClient, broker)

	webauth2.RegisterAuthServer(s, serviceVar93)

	serviceVar94 := wire2.InitialiseAccountsService(authClient, usersClient, webfeConf, savingsClient, actorClient, externalAccountsClient, broker, operationalStatusServiceClient)

	webaccountspb.RegisterAccountsServer(s, serviceVar94)

	serviceVar95 := wire2.InitialiseRiskService(usersClient, caseManagementClient, s3Client, webfeConf)

	webriskpb.RegisterRiskServer(s, serviceVar95)

	serviceVar96, err := wire2.InitialiseTravelService(actorClient, cardProvisioningClient, offerListingServiceClient, dcDocS3Client, webfeConf, cardControlClient, serviceClientVar4)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	travelpb.RegisterTravelBudgetServer(s, serviceVar96)

	serviceVar97 := wire2.InitialiseWebSignupService(authClient, usersClient, actorClient, broker, vendorMappingServiceClient, consentClient, totpClient, sessionManagerClient)

	signup3.RegisterSignupServer(s, serviceVar97)

	serviceVar98 := wire2.InitialiseConsentService(consentClient, userPreferenceClient)

	consentpb2.RegisterConsentServer(s, serviceVar98)

	serviceVar99 := wire2.InitialiseWebLoansEligibilityService(actorClient, webfeConf, preEligibilityClient, usersClient)

	loanseligibility.RegisterLoansEligibilityServer(s, serviceVar99)

	serviceVar100 := wire2.InitialiseSecretAnalyserService(actorClient, usersClient)

	secretanalyser2.RegisterSecretAnalyserServer(s, serviceVar100)

	serviceVar101 := wire2.InitialiseUserService(actorClient, usersClient)

	user3.RegisterUserServer(s, serviceVar101)

	configNameToConfMap[cfg.ConfigName(cfg.WEBFE_SERVICE)] = &commonexplorer.Config{StaticConf: &webfeconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.FRONTEND_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
