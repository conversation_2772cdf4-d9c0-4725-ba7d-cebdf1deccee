// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	"net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	temporalworkflowpb "go.temporal.io/api/workflowservice/v1"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	cmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire2 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	actoractivityconf "github.com/epifi/gamma/actor_activity/config"
	genconf2 "github.com/epifi/gamma/actor_activity/config/genconf"
	wire "github.com/epifi/gamma/actor_activity/wire"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	developer3 "github.com/epifi/gamma/api/accounts/developer"
	operstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	statement "github.com/epifi/gamma/api/accounts/statement"
	actorpb "github.com/epifi/gamma/api/actor"
	actordeveloper "github.com/epifi/gamma/api/actor/developer"
	aapb "github.com/epifi/gamma/api/actor_activity"
	alfredpb "github.com/epifi/gamma/api/alfred"
	alfreddeveloper "github.com/epifi/gamma/api/alfred/developer"
	amlpb "github.com/epifi/gamma/api/aml"
	amldeveloper "github.com/epifi/gamma/api/aml/developer"
	analyserdeveloper "github.com/epifi/gamma/api/analyser/developer"
	investmentanalyserpb "github.com/epifi/gamma/api/analyser/investment"
	txnaggregatespb "github.com/epifi/gamma/api/analyser/txnaggregates"
	authpb "github.com/epifi/gamma/api/auth"
	authdeveloper "github.com/epifi/gamma/api/auth/developer"
	livenesspb "github.com/epifi/gamma/api/auth/liveness"
	livenessdeveloper "github.com/epifi/gamma/api/auth/liveness/developer"
	location "github.com/epifi/gamma/api/auth/location"
	authorchdeveloper "github.com/epifi/gamma/api/auth/orchestrator/developer"
	bankcustpb "github.com/epifi/gamma/api/bankcust"
	compliancepb "github.com/epifi/gamma/api/bankcust/compliance"
	bankcustdeveloper "github.com/epifi/gamma/api/bankcust/developer"
	ccpb "github.com/epifi/gamma/api/card/control"
	cardcxpb "github.com/epifi/gamma/api/card/cx"
	carddeveloper "github.com/epifi/gamma/api/card/developer"
	cardpb "github.com/epifi/gamma/api/card/provisioning"
	casbin "github.com/epifi/gamma/api/casbin"
	developer2 "github.com/epifi/gamma/api/casbin/developer"
	casperpb "github.com/epifi/gamma/api/casper"
	casperdeveloper "github.com/epifi/gamma/api/casper/developer"
	discountspb "github.com/epifi/gamma/api/casper/discounts"
	exchangerpb "github.com/epifi/gamma/api/casper/exchanger"
	evrpb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	redemption "github.com/epifi/gamma/api/casper/redemption"
	categorizerpb "github.com/epifi/gamma/api/categorizer"
	categorizerdeveloper "github.com/epifi/gamma/api/categorizer/developer"
	celestialdeveloper "github.com/epifi/gamma/api/celestial/developer"
	cmspb "github.com/epifi/gamma/api/cms"
	cmsdeveloper "github.com/epifi/gamma/api/cms/developer"
	collectiondeveloperpb "github.com/epifi/gamma/api/collection/developer"
	commspb "github.com/epifi/gamma/api/comms"
	commsdeveloper "github.com/epifi/gamma/api/comms/developer"
	commsdeveloperactionspb "github.com/epifi/gamma/api/comms/developer/actions"
	tcpb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	uppb "github.com/epifi/gamma/api/comms/user_preference"
	capb "github.com/epifi/gamma/api/connected_account"
	cadeveloper "github.com/epifi/gamma/api/connected_account/developer"
	consentpb "github.com/epifi/gamma/api/consent"
	limitestimatorpb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditreport "github.com/epifi/gamma/api/creditreportv2"
	creditreportpb "github.com/epifi/gamma/api/creditreportv2/developer"
	adminactionspb "github.com/epifi/gamma/api/cx/admin_actions"
	applog2 "github.com/epifi/gamma/api/cx/app_log"
	alpb "github.com/epifi/gamma/api/cx/audit_log"
	callpb "github.com/epifi/gamma/api/cx/call"
	consumer "github.com/epifi/gamma/api/cx/call/consumer"
	callivrpb "github.com/epifi/gamma/api/cx/call_ivr"
	callroutingpb "github.com/epifi/gamma/api/cx/call_routing"
	chat "github.com/epifi/gamma/api/cx/chat"
	livechatfallback2 "github.com/epifi/gamma/api/cx/chat/bot/livechatfallback"
	chatbotworkflowpb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	consumer2 "github.com/epifi/gamma/api/cx/chat/consumer"
	cxconnectedaccountpb "github.com/epifi/gamma/api/cx/connected_account"
	consumer4 "github.com/epifi/gamma/api/cx/consumer"
	citpb "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration"
	issuetrackerconsumer "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer"
	cxcapb "github.com/epifi/gamma/api/cx/customer_auth"
	cxaccountpb "github.com/epifi/gamma/api/cx/data_collector/account"
	alfredcxpb "github.com/epifi/gamma/api/cx/data_collector/alfred"
	cxcardpb "github.com/epifi/gamma/api/cx/data_collector/card"
	communications "github.com/epifi/gamma/api/cx/data_collector/communications"
	cxccpb "github.com/epifi/gamma/api/cx/data_collector/firefly"
	cxfitttpb "github.com/epifi/gamma/api/cx/data_collector/fittt"
	mutualfund "github.com/epifi/gamma/api/cx/data_collector/investment/mutualfund"
	usstocks2 "github.com/epifi/gamma/api/cx/data_collector/investment/usstocks"
	kyc2 "github.com/epifi/gamma/api/cx/data_collector/kyc"
	obpb "github.com/epifi/gamma/api/cx/data_collector/onboarding"
	cxp2pinvpb "github.com/epifi/gamma/api/cx/data_collector/p2pinvestment"
	cxinternationalfiletransferdatacollectorpb "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer"
	paymentinstruments2 "github.com/epifi/gamma/api/cx/data_collector/payment_instruments"
	cxpreapprovedloanpb "github.com/epifi/gamma/api/cx/data_collector/preapprovedloan"
	profilepb "github.com/epifi/gamma/api/cx/data_collector/profile"
	referrals2 "github.com/epifi/gamma/api/cx/data_collector/referrals"
	repb "github.com/epifi/gamma/api/cx/data_collector/rewards"
	riskopswealthpb "github.com/epifi/gamma/api/cx/data_collector/risk_ops_wealth"
	salarydataops2 "github.com/epifi/gamma/api/cx/data_collector/salarydataops"
	salaryb2bpb "github.com/epifi/gamma/api/cx/data_collector/salaryprogram/salaryb2b"
	sherlockaapb "github.com/epifi/gamma/api/cx/data_collector/sherlock_actor_activity"
	tieringpb "github.com/epifi/gamma/api/cx/data_collector/tiering"
	transaction2 "github.com/epifi/gamma/api/cx/data_collector/transaction"
	userreqpb "github.com/epifi/gamma/api/cx/data_collector/user_requests"
	cxvkyccallpb "github.com/epifi/gamma/api/cx/data_collector/vkyccall"
	wealthonboarding2 "github.com/epifi/gamma/api/cx/data_collector/wealth_onboarding"
	cxdeveloper "github.com/epifi/gamma/api/cx/developer"
	actionpb "github.com/epifi/gamma/api/cx/developer/actions"
	consumer7 "github.com/epifi/gamma/api/cx/developer/actions/consumer"
	dbstatepb "github.com/epifi/gamma/api/cx/developer/db_state"
	ticketsummary2 "github.com/epifi/gamma/api/cx/developer/ticket_summary"
	dipb "github.com/epifi/gamma/api/cx/dispute"
	consumer9 "github.com/epifi/gamma/api/cx/dispute/consumer"
	job2 "github.com/epifi/gamma/api/cx/dispute/job"
	watsoninfoproviderpb "github.com/epifi/gamma/api/cx/error_activity/watson_info_provider"
	espb "github.com/epifi/gamma/api/cx/escalations"
	cxfederalpb "github.com/epifi/gamma/api/cx/federal"
	cxdevconsolepb "github.com/epifi/gamma/api/cx/fittt/devconsole"
	cxquestionresponsesubscriptionpb "github.com/epifi/gamma/api/cx/inapphelp_feedback_engine_clients/question_response_subscription"
	issuepb "github.com/epifi/gamma/api/cx/issue_category"
	icpb "github.com/epifi/gamma/api/cx/issue_config"
	issueresolutionfeedbackpb "github.com/epifi/gamma/api/cx/issue_resolution_feedback"
	lppb "github.com/epifi/gamma/api/cx/landing_page"
	cxlvpb "github.com/epifi/gamma/api/cx/liveness_video"
	stagewisecommspb "github.com/epifi/gamma/api/cx/manual_ticket_stage_wise_comms"
	payoutpb "github.com/epifi/gamma/api/cx/payout"
	consumer11 "github.com/epifi/gamma/api/cx/payout/consumer"
	riskopspb "github.com/epifi/gamma/api/cx/risk_ops"
	sherlockriskchartpb "github.com/epifi/gamma/api/cx/risk_ops/chart"
	sbpb "github.com/epifi/gamma/api/cx/sherlock_banners"
	sbpf "github.com/epifi/gamma/api/cx/sherlock_feedback"
	sherlockscriptspb "github.com/epifi/gamma/api/cx/sherlock_scripts"
	sherlocksop2 "github.com/epifi/gamma/api/cx/sherlock_sop"
	sherlockuserpb "github.com/epifi/gamma/api/cx/sherlock_user"
	sprinklrpb "github.com/epifi/gamma/api/cx/sprinklr"
	cxsgkycpb "github.com/epifi/gamma/api/cx/stockguardian/kyc"
	ticketpb "github.com/epifi/gamma/api/cx/ticket"
	ticketconsumerpb "github.com/epifi/gamma/api/cx/ticket/consumer"
	userissueinfopb "github.com/epifi/gamma/api/cx/user_issue_info"
	watsonpb "github.com/epifi/gamma/api/cx/watson"
	watsonconsumer2 "github.com/epifi/gamma/api/cx/watson/consumer"
	mockwatsonclient "github.com/epifi/gamma/api/cx/watson/mock_client"
	depositpb "github.com/epifi/gamma/api/deposit"
	depositdeveloper "github.com/epifi/gamma/api/deposit/developer"
	depositwatsonpb "github.com/epifi/gamma/api/deposit/watson"
	employment "github.com/epifi/gamma/api/employment"
	employmentdeveloper "github.com/epifi/gamma/api/employment/developer"
	firefly "github.com/epifi/gamma/api/firefly"
	ffaccountspb "github.com/epifi/gamma/api/firefly/accounting"
	ffbillpb "github.com/epifi/gamma/api/firefly/billing"
	cccxpb "github.com/epifi/gamma/api/firefly/cx"
	ffdeveloper "github.com/epifi/gamma/api/firefly/developer"
	fflmspb "github.com/epifi/gamma/api/firefly/lms"
	ffpinotpb "github.com/epifi/gamma/api/firefly/pinot"
	fitttpb "github.com/epifi/gamma/api/fittt"
	devconsolepb "github.com/epifi/gamma/api/fittt/devconsole"
	fitttdeveloper "github.com/epifi/gamma/api/fittt/developer"
	schedulerpb "github.com/epifi/gamma/api/fittt/scheduler"
	sportspb "github.com/epifi/gamma/api/fittt/sports"
	saclosurepb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	hedeveloper "github.com/epifi/gamma/api/health_engine/developer"
	appfeedback2 "github.com/epifi/gamma/api/inapphelp/app_feedback"
	inapphelpdeveloper "github.com/epifi/gamma/api/inapphelp/developer"
	processor2 "github.com/epifi/gamma/api/inapphelp/faq/processor"
	inapphelpservingpb "github.com/epifi/gamma/api/inapphelp/faq/serving"
	feedbackenginepb "github.com/epifi/gamma/api/inapphelp/feedback_engine"
	mockfeedbackengineclient "github.com/epifi/gamma/api/inapphelp/feedback_engine/mock_client"
	feedbackenginewebpb "github.com/epifi/gamma/api/inapphelp/feedback_engine/serving/jarvis"
	irpb "github.com/epifi/gamma/api/inapphelp/issue_reporting"
	mediapb "github.com/epifi/gamma/api/inapphelp/media"
	recentactivity2 "github.com/epifi/gamma/api/inapphelp/recent_activity"
	inappreferralpb "github.com/epifi/gamma/api/inappreferral"
	inappreferraldeveloper "github.com/epifi/gamma/api/inappreferral/developer"
	seasonspb "github.com/epifi/gamma/api/inappreferral/season"
	insights "github.com/epifi/gamma/api/insights"
	accessinfo "github.com/epifi/gamma/api/insights/accessinfo"
	insightsdeveloper "github.com/epifi/gamma/api/insights/developer"
	emailparserpb "github.com/epifi/gamma/api/insights/emailparser"
	epfpb "github.com/epifi/gamma/api/insights/epf"
	networthpb "github.com/epifi/gamma/api/insights/networth"
	invaggrpb "github.com/epifi/gamma/api/investment/aggregator"
	dynuielpb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	investmentcatalogpb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	investdeveloper "github.com/epifi/gamma/api/investment/mutualfund/developer"
	mfexternalpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	foliopb "github.com/epifi/gamma/api/investment/mutualfund/foliodetails"
	investmentorder "github.com/epifi/gamma/api/investment/mutualfund/order"
	fgpb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	mfops "github.com/epifi/gamma/api/investment/mutualfund/order/operations"
	prhpb "github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler"
	rfpb "github.com/epifi/gamma/api/investment/mutualfund/order/reverse_feed"
	phpb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	reconciliationpb "github.com/epifi/gamma/api/investment/mutualfund/reconciliation"
	investmentwatsonpb "github.com/epifi/gamma/api/investment/watson"
	kycpb "github.com/epifi/gamma/api/kyc"
	agentpb "github.com/epifi/gamma/api/kyc/agent"
	kycdeveloper "github.com/epifi/gamma/api/kyc/developer"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	devleadspb "github.com/epifi/gamma/api/leads/developer"
	merchantdeveloper "github.com/epifi/gamma/api/merchant/developer"
	nudge "github.com/epifi/gamma/api/nudge"
	nudgedeveloper "github.com/epifi/gamma/api/nudge/developer"
	journey "github.com/epifi/gamma/api/nudge/journey"
	devomeglepb "github.com/epifi/gamma/api/omegle/developer"
	orderpb "github.com/epifi/gamma/api/order"
	aaorderpb "github.com/epifi/gamma/api/order/aa"
	orderactoractivitypb "github.com/epifi/gamma/api/order/actoractivity"
	opb "github.com/epifi/gamma/api/order/cx"
	orderdeveloper "github.com/epifi/gamma/api/order/developer"
	paymentpb "github.com/epifi/gamma/api/order/payment"
	reconpb "github.com/epifi/gamma/api/order/recon"
	p2investmentpb "github.com/epifi/gamma/api/p2pinvestment"
	p2pcxpb "github.com/epifi/gamma/api/p2pinvestment/cx"
	p2pdeveloper "github.com/epifi/gamma/api/p2pinvestment/developer"
	p2pincidentmanagerpb "github.com/epifi/gamma/api/p2pinvestment/incidentmanager"
	pan "github.com/epifi/gamma/api/pan"
	pandeveloper "github.com/epifi/gamma/api/pan/developer"
	paypb "github.com/epifi/gamma/api/pay"
	paycxpb "github.com/epifi/gamma/api/pay/cx"
	paydeveloper "github.com/epifi/gamma/api/pay/developer"
	iftpb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	payfilegeneratorpb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	payincidentmanagerpb "github.com/epifi/gamma/api/pay/payincidentmanager"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	pideveloper "github.com/epifi/gamma/api/paymentinstrument/developer"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	preapprovedloanpb "github.com/epifi/gamma/api/preapprovedloan"
	preapprovedloancxpb "github.com/epifi/gamma/api/preapprovedloan/cx"
	pldeveloper "github.com/epifi/gamma/api/preapprovedloan/developer"
	palsherlockbannerspb "github.com/epifi/gamma/api/preapprovedloan/sherlock_banners"
	product "github.com/epifi/gamma/api/product"
	questdeveloper "github.com/epifi/gamma/api/quest/developer"
	questmanagerpb "github.com/epifi/gamma/api/quest/manager"
	recurringpaymentpb "github.com/epifi/gamma/api/recurringpayment"
	recurrdeveloper "github.com/epifi/gamma/api/recurringpayment/developer"
	enachdeveloper "github.com/epifi/gamma/api/recurringpayment/enach/developer"
	rewardspb "github.com/epifi/gamma/api/rewards"
	campaigncomm "github.com/epifi/gamma/api/rewards/campaigncomm"
	rewardsdeveloper "github.com/epifi/gamma/api/rewards/developer"
	rewardsimulatorpb "github.com/epifi/gamma/api/rewards/generator"
	luckydrawpb "github.com/epifi/gamma/api/rewards/luckydraw"
	rewardsprojectionpb "github.com/epifi/gamma/api/rewards/projector"
	rewardofferpb "github.com/epifi/gamma/api/rewards/rewardoffers"
	riskpb "github.com/epifi/gamma/api/risk"
	casemanagementpb "github.com/epifi/gamma/api/risk/case_management"
	riskdeveloper "github.com/epifi/gamma/api/risk/developer"
	leapb "github.com/epifi/gamma/api/risk/lea"
	profilepb2 "github.com/epifi/gamma/api/risk/profile"
	redlist "github.com/epifi/gamma/api/risk/redlist"
	whitelistpb "github.com/epifi/gamma/api/risk/whitelist"
	rmsdeveloper "github.com/epifi/gamma/api/rms/developer"
	manager "github.com/epifi/gamma/api/rms/manager"
	salaryprogram "github.com/epifi/gamma/api/salaryprogram"
	salarycxpb "github.com/epifi/gamma/api/salaryprogram/cx"
	spdeveloper "github.com/epifi/gamma/api/salaryprogram/developer"
	spdynamicelementpb "github.com/epifi/gamma/api/salaryprogram/dynamic_ui_element"
	healthinsurancepb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	salaryreferralspb "github.com/epifi/gamma/api/salaryprogram/referrals"
	savingspb "github.com/epifi/gamma/api/savings"
	savingsdeveloper "github.com/epifi/gamma/api/savings/developer"
	extacct "github.com/epifi/gamma/api/savings/extacct"
	savingswatsonclient "github.com/epifi/gamma/api/savings/watson"
	screener "github.com/epifi/gamma/api/screener"
	screenerdeveloper "github.com/epifi/gamma/api/screener/developer"
	searchpb "github.com/epifi/gamma/api/search"
	searchdevpb "github.com/epifi/gamma/api/search/developer"
	indexerpb "github.com/epifi/gamma/api/search/indexer"
	segmentpb "github.com/epifi/gamma/api/segment"
	segmentconsumerpb "github.com/epifi/gamma/api/segment/consumer"
	segmentdeveloper "github.com/epifi/gamma/api/segment/developer"
	simulatorwcpb "github.com/epifi/gamma/api/simulator/cx/watson_client"
	preapprovedloansimpb "github.com/epifi/gamma/api/simulator/lending/preapprovedloan"
	accountsdevpb "github.com/epifi/gamma/api/simulator/openbanking/accounts/developer"
	profileevaluatorsimpb "github.com/epifi/gamma/api/simulator/profileevaluator"
	sgapplicationpb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	sgapigwdbspb "github.com/epifi/gamma/api/stockguardian/sgapigateway/dbstate"
	sgkycapigatewaypb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	sglmspb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	betieringpb "github.com/epifi/gamma/api/tiering"
	tieringdeveloper "github.com/epifi/gamma/api/tiering/developer"
	tieringpinotpb "github.com/epifi/gamma/api/tiering/pinot"
	timelinepb "github.com/epifi/gamma/api/timeline"
	timelinedeveloper "github.com/epifi/gamma/api/timeline/developer"
	tspdbstate "github.com/epifi/gamma/api/tspuser"
	developerclient "github.com/epifi/gamma/api/tspuser/developer"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	upcomingtxndeveloper "github.com/epifi/gamma/api/upcomingtransactions/developer"
	upipb "github.com/epifi/gamma/api/upi"
	cx "github.com/epifi/gamma/api/upi/cx"
	upideveloper "github.com/epifi/gamma/api/upi/developer"
	upionboardingpb "github.com/epifi/gamma/api/upi/onboarding"
	userspb "github.com/epifi/gamma/api/user"
	userdeveloper "github.com/epifi/gamma/api/user/developer"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	location2 "github.com/epifi/gamma/api/user/location"
	onboarding "github.com/epifi/gamma/api/user/onboarding"
	onboardingwatsonclient "github.com/epifi/gamma/api/user/onboarding/watson"
	useractions "github.com/epifi/gamma/api/useractions"
	accountmanagerpb "github.com/epifi/gamma/api/usstocks/account"
	usstockscatalogpb "github.com/epifi/gamma/api/usstocks/catalog"
	usstockdeveloper "github.com/epifi/gamma/api/usstocks/developer"
	ussoperationspb "github.com/epifi/gamma/api/usstocks/operations"
	ordermanagerpb "github.com/epifi/gamma/api/usstocks/order"
	portfolio "github.com/epifi/gamma/api/usstocks/portfolio"
	vgaapb "github.com/epifi/gamma/api/vendorgateway/aa"
	vgleadsquaredpb "github.com/epifi/gamma/api/vendorgateway/crm/leadsquared"
	vgsenseforthpb "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/livechatfallback/senseforth"
	escalfederalvgpb "github.com/epifi/gamma/api/vendorgateway/cx/federal"
	vgfcpb "github.com/epifi/gamma/api/vendorgateway/cx/freshchat"
	freshdesk "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	inhouse "github.com/epifi/gamma/api/vendorgateway/cx/inhouse"
	ozonetel "github.com/epifi/gamma/api/vendorgateway/cx/ozonetel"
	solpb "github.com/epifi/gamma/api/vendorgateway/cx/solutions"
	ekycpb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	extvalidate "github.com/epifi/gamma/api/vendorgateway/extvalidate"
	fennelpb "github.com/epifi/gamma/api/vendorgateway/fennel"
	vgp2ppb "github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	ncpb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	employernamecategoriserpb "github.com/epifi/gamma/api/vendorgateway/namecheck/employernamecategoriser"
	vgaccountspb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgdepositpb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	dispute "github.com/epifi/gamma/api/vendorgateway/openbanking/dispute"
	vgpaymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgscienapticpb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	stockspb "github.com/epifi/gamma/api/vendorgateway/stocks"
	mutualfund2 "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	vendormapping "github.com/epifi/gamma/api/vendormapping"
	vmdeveloper "github.com/epifi/gamma/api/vendormapping/developer"
	rpfederalpb "github.com/epifi/gamma/api/vendornotification/openbanking/recurringpayment/federal"
	vkyccall "github.com/epifi/gamma/api/vkyccall"
	devvkyccallpb "github.com/epifi/gamma/api/vkyccall/developer"
	troubleshootpb "github.com/epifi/gamma/api/vkyccall/troubleshoot"
	wopb "github.com/epifi/gamma/api/wealthonboarding"
	wocxpb "github.com/epifi/gamma/api/wealthonboarding/cx"
	wonbdeveloper "github.com/epifi/gamma/api/wealthonboarding/developer"
	casbinconf "github.com/epifi/gamma/casbin/config"
	wire2 "github.com/epifi/gamma/casbin/wire"
	types "github.com/epifi/gamma/casbin/wire/types"
	hook "github.com/epifi/gamma/cmd/servers/staging/cx/hook"
	cx2 "github.com/epifi/gamma/cmd/service_groups/cx"
	cxconf "github.com/epifi/gamma/cx/config"
	cxgenconf "github.com/epifi/gamma/cx/config/genconf"
	servergenwire "github.com/epifi/gamma/cx/interceptor/servergen_wire"
	wire3 "github.com/epifi/gamma/cx/wire"
	cxtypes "github.com/epifi/gamma/cx/wire/types"
	inapphelpconf "github.com/epifi/gamma/inapphelp/config"
	genconf3 "github.com/epifi/gamma/inapphelp/config/genconf"
	wire4 "github.com/epifi/gamma/inapphelp/wire"
	types2 "github.com/epifi/gamma/inapphelp/wire/types"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire3 "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	verifipkg "github.com/epifi/gamma/verifi/pkg"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.CX_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.CX_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.CX_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.CX_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	casbinSherlockPGDBv1, err := storage.NewPostgresDBWithConfig(gconf.Databases()["CasbinSherlockPGDBv1"])
	if err != nil {
		logger.Error(ctx, "unable to connect to db", zap.Error(err))
		return err
	}
	defer func() { _ = casbinSherlockPGDBv1.Close() }()

	sherlockPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["SherlockPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "SherlockPGDB"))
		return err
	}
	sherlockPGDBSqlDb, err := sherlockPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "SherlockPGDB"))
		return err
	}
	defer func() { _ = sherlockPGDBSqlDb.Close() }()
	inapphelpPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["InapphelpPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "InapphelpPGDB"))
		return err
	}
	inapphelpPGDBSqlDb, err := inapphelpPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "InapphelpPGDB"))
		return err
	}
	defer func() { _ = inapphelpPGDBSqlDb.Close() }()

	cxConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.CX_SERVER)
	defer epifigrpc.CloseConn(cxConn)
	watsonClient := watsonpb.NewWatsonClient(cxConn)
	actorActivityClient := aapb.NewActorActivityClient(cxConn)
	ticketClient := ticketpb.NewTicketClient(cxConn)
	cxRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["CxRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = cxRedisStore.Close() }()
	casbinClient := casbin.NewCasbinClient(cxConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	freshdeskClient := freshdesk.NewFreshdeskClient(vendorgatewayConn)
	onboardingConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	usersClient := userspb.NewUsersClient(onboardingConn)
	payConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.PAY_SERVER)
	defer epifigrpc.CloseConn(payConn)
	actorClient := actorpb.NewActorClient(payConn)
	chatsClient := chat.NewChatsClient(cxConn)
	vendormappingConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vendormapping.NewVendorMappingServiceClient(vendormappingConn)
	collapserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["CollapserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for CollapserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { collapserRueidisRedisStore.Close() }()
	collapserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(collapserRueidisRedisStore), gconf.RueidisRedisClients()["CollapserRueidisRedisStore"].Hystrix)
	actorActivityClientVar2 := orderactoractivitypb.NewActorActivityClient(payConn)
	cardConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.CARD_SERVER)
	defer epifigrpc.CloseConn(cardConn)
	accountingClient := ffaccountspb.NewAccountingClient(cardConn)
	cXClient := opb.NewCXClient(payConn)
	orderServiceClient := orderpb.NewOrderServiceClient(payConn)
	centralgrowthConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.CENTRAL_GROWTH_SERVER)
	defer epifigrpc.CloseConn(centralgrowthConn)
	savingsClient := savingspb.NewSavingsClient(centralgrowthConn)
	piClient := pipb.NewPiClient(payConn)
	wealthdmfConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.WEALTH_DMF_SERVER)
	defer epifigrpc.CloseConn(wealthdmfConn)
	depositClient := depositpb.NewDepositClient(wealthdmfConn)
	catalogManagerClient := investmentcatalogpb.NewCatalogManagerClient(wealthdmfConn)
	p2PInvestmentClient := p2investmentpb.NewP2PInvestmentClient(wealthdmfConn)
	portfolioManagerClient := portfolio.NewPortfolioManagerClient(wealthdmfConn)
	orderManagerClient := ordermanagerpb.NewOrderManagerClient(wealthdmfConn)
	balanceClient := accountbalancepb.NewBalanceClient(payConn)
	cXClientVar2 := paycxpb.NewCXClient(payConn)
	payClient := paypb.NewPayClient(payConn)
	paymentClient := paymentpb.NewPaymentClient(payConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(payConn)
	upiOnboardingClient := upionboardingpb.NewUpiOnboardingClient(payConn)
	fireflyClient := firefly.NewFireflyClient(cardConn)
	externalAccountsClient := extacct.NewExternalAccountsClient(centralgrowthConn)
	tieringClient := betieringpb.NewTieringClient(centralgrowthConn)
	bankCustomerServiceClient := bankcustpb.NewBankCustomerServiceClient(onboardingConn)
	onboardingClient := onboarding.NewOnboardingClient(onboardingConn)
	growthinfraConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.GROWTH_INFRA_SERVER)
	defer epifigrpc.CloseConn(growthinfraConn)
	commsClient := commspb.NewCommsClient(growthinfraConn)
	operationalStatusServiceClient := operstatuspb.NewOperationalStatusServiceClient(payConn)
	ozonetelClient := ozonetel.NewOzonetelClient(vendorgatewayConn)
	authClient := authpb.NewAuthClient(onboardingConn)
	customerProfileClient := profilepb.NewCustomerProfileClient(cxConn)
	depositClientVar3 := vgdepositpb.NewDepositClient(vendorgatewayConn)
	frontendConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.FRONTEND_SERVER)
	defer epifigrpc.CloseConn(frontendConn)
	savingsAccountClosureClient := saclosurepb.NewSavingsAccountClosureClient(frontendConn)
	cardProvisioningClient := cardpb.NewCardProvisioningClient(cardConn)
	cardControlClient := ccpb.NewCardControlClient(cardConn)
	salaryProgramClient := salaryprogram.NewSalaryProgramClient(centralgrowthConn)
	groupClient := usergrouppb.NewGroupClient(onboardingConn)
	freshchatClient := vgfcpb.NewFreshchatClient(vendorgatewayConn)
	kycClient := kycpb.NewKycClient(onboardingConn)
	vKYCClient := vkycpb.NewVKYCClient(onboardingConn)
	livenessClient := livenesspb.NewLivenessClient(onboardingConn)
	panClient := pan.NewPanClient(onboardingConn)
	complianceClient := compliancepb.NewComplianceClient(onboardingConn)
	fitttClient := fitttpb.NewFitttClient(wealthdmfConn)
	ruleManagerClient := manager.NewRuleManagerClient(wealthdmfConn)
	catalogManagerClientVar4 := usstockscatalogpb.NewCatalogManagerClient(wealthdmfConn)
	txnCategorizerClient := categorizerpb.NewTxnCategorizerClient(wealthdmfConn)
	recurringPaymentServiceClient := recurringpaymentpb.NewRecurringPaymentServiceClient(payConn)
	uPIClient := upipb.NewUPIClient(payConn)
	upiCXClient := cx.NewUpiCXClient(payConn)
	rewardOffersClient := rewardofferpb.NewRewardOffersClient(growthinfraConn)
	offerListingServiceClient := casperpb.NewOfferListingServiceClient(growthinfraConn)
	rewardsGeneratorClient := rewardspb.NewRewardsGeneratorClient(growthinfraConn)
	offerRedemptionServiceClient := redemption.NewOfferRedemptionServiceClient(growthinfraConn)
	exchangerOfferServiceClient := exchangerpb.NewExchangerOfferServiceClient(growthinfraConn)
	projectorServiceClient := rewardsprojectionpb.NewProjectorServiceClient(growthinfraConn)
	externalVendorRedemptionServiceClient := evrpb.NewExternalVendorRedemptionServiceClient(growthinfraConn)
	disputeClient := dispute.NewDisputeClient(vendorgatewayConn)
	issueResolutionFeedbackServiceClient := issueresolutionfeedbackpb.NewIssueResolutionFeedbackServiceClient(cxConn)
	productClient := product.NewProductClient(onboardingConn)
	employmentClient := employment.NewEmploymentClient(onboardingConn)
	wealthOnboardingClient := wopb.NewWealthOnboardingClient(wealthdmfConn)
	wealthCxServiceClient := wocxpb.NewWealthCxServiceClient(wealthdmfConn)
	orderManagerClientVar5 := investmentorder.NewOrderManagerClient(wealthdmfConn)
	paymentHandlerClient := phpb.NewPaymentHandlerClient(wealthdmfConn)
	commsDbStatesClient := commsdeveloper.NewCommsDbStatesClient(growthinfraConn)
	devActorClient := actordeveloper.NewDevActorClient(payConn)
	devDepositClient := depositdeveloper.NewDevDepositClient(wealthdmfConn)
	devKYCClient := kycdeveloper.NewDevKYCClient(onboardingConn)
	devUserClient := userdeveloper.NewDevUserClient(onboardingConn)
	devClient := orderdeveloper.NewDevClient(payConn)
	devPaymentIntrumentClient := pideveloper.NewDevPaymentIntrumentClient(payConn)
	cardDbStatesClient := carddeveloper.NewCardDbStatesClient(cardConn)
	devLivenessClient := livenessdeveloper.NewDevLivenessClient(onboardingConn)
	savingsDbStatesClient := savingsdeveloper.NewSavingsDbStatesClient(centralgrowthConn)
	devClientVar2 := upideveloper.NewDevClient(payConn)
	devInapphelpClient := inapphelpdeveloper.NewDevInapphelpClient(cxConn)
	devCXClient := cxdeveloper.NewDevCXClient(cxConn)
	devCasbinClient := developer2.NewDevCasbinClient(cxConn)
	devInsightsClient := insightsdeveloper.NewDevInsightsClient(wealthdmfConn)
	devCategorizerClient := categorizerdeveloper.NewDevCategorizerClient(wealthdmfConn)
	rewardsDevClient := rewardsdeveloper.NewRewardsDevClient(growthinfraConn)
	devAuthClient := authdeveloper.NewDevAuthClient(onboardingConn)
	devVendorMappingClient := vmdeveloper.NewDevVendorMappingClient(vendormappingConn)
	devTimelineClient := timelinedeveloper.NewDevTimelineClient(payConn)
	casperDevClient := casperdeveloper.NewCasperDevClient(growthinfraConn)
	rMSDbStatesClient := rmsdeveloper.NewRMSDbStatesClient(wealthdmfConn)
	devMerchantClient := merchantdeveloper.NewDevMerchantClient(payConn)
	fITTTDbStatesClient := fitttdeveloper.NewFITTTDbStatesClient(wealthdmfConn)
	devConnectedAccClient := cadeveloper.NewDevConnectedAccClient(centralgrowthConn)
	devInAppReferralClient := inappreferraldeveloper.NewDevInAppReferralClient(onboardingConn)
	devWealthOnboardingClient := wonbdeveloper.NewDevWealthOnboardingClient(wealthdmfConn)
	mutualFundDbStatesClient := investdeveloper.NewMutualFundDbStatesClient(wealthdmfConn)
	recurringPaymentDevClient := recurrdeveloper.NewRecurringPaymentDevClient(payConn)
	enachDevClient := enachdeveloper.NewEnachDevClient(payConn)
	devP2PInvestmentClient := p2pdeveloper.NewDevP2PInvestmentClient(wealthdmfConn)
	segmentDbStatesClient := segmentdeveloper.NewSegmentDbStatesClient(growthinfraConn)
	nudgeDbStatesClient := nudgedeveloper.NewNudgeDbStatesClient(growthinfraConn)
	salaryProgramDevClient := spdeveloper.NewSalaryProgramDevClient(centralgrowthConn)
	devAnalyserClient := analyserdeveloper.NewDevAnalyserClient(wealthdmfConn)
	lendingConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	devPreApprovedLoanClient := pldeveloper.NewDevPreApprovedLoanClient(lendingConn)
	devFireflyClient := ffdeveloper.NewDevFireflyClient(cardConn)
	nebulaConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.NEBULA_SERVER)
	defer epifigrpc.CloseConn(nebulaConn)
	developerClient := celestialdeveloper.NewDeveloperClient(nebulaConn)
	userriskConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.USER_RISK_SERVER)
	defer epifigrpc.CloseConn(userriskConn)
	developerClientVar2 := riskdeveloper.NewDeveloperClient(userriskConn)
	developerClientVar3 := screenerdeveloper.NewDeveloperClient(onboardingConn)
	devOrchestratorClient := authorchdeveloper.NewDevOrchestratorClient(onboardingConn)
	devClientVar3 := paydeveloper.NewDevClient(payConn)
	dBStateClient := usstockdeveloper.NewDBStateClient(wealthdmfConn)
	tieringDevServiceClient := tieringdeveloper.NewTieringDevServiceClient(centralgrowthConn)
	amlDevServiceClient := amldeveloper.NewAmlDevServiceClient(wealthdmfConn)
	developerClientVar4 := alfreddeveloper.NewDeveloperClient(onboardingConn)
	developerClientVar5 := pandeveloper.NewDeveloperClient(onboardingConn)
	questDbStatesClient := questdeveloper.NewQuestDbStatesClient(growthinfraConn)
	devUpcomingTransactionsClient := upcomingtxndeveloper.NewDevUpcomingTransactionsClient(wealthdmfConn)
	healthEngineDevClient := hedeveloper.NewHealthEngineDevClient(payConn)
	cmsDevClient := cmsdeveloper.NewCmsDevClient(growthinfraConn)
	devBankCustClient := bankcustdeveloper.NewDevBankCustClient(onboardingConn)
	sgapigatewayConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.SG_API_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(sgapigatewayConn)
	devOmegleClient := devomeglepb.NewDevOmegleClient(sgapigatewayConn)
	devOmegleClientVar2 := devomeglepb.NewDevOmegleClient(onboardingConn)
	devVkycCallClient := devvkyccallpb.NewDevVkycCallClient(sgapigatewayConn)
	devVkycCallClientVar2 := devvkyccallpb.NewDevVkycCallClient(onboardingConn)
	devEmploymentClient := employmentdeveloper.NewDevEmploymentClient(onboardingConn)
	developerClientVar6 := collectiondeveloperpb.NewDeveloperClient(lendingConn)
	goblinConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.GOBLIN_SERVER)
	defer epifigrpc.CloseConn(goblinConn)
	dBStateServiceClient := sgapigwdbspb.NewDBStateServiceClient(goblinConn)
	tspUserServiceClient := tspdbstate.NewTspUserServiceClient(onboardingConn)
	developerClientVar7 := developerclient.NewDeveloperClient(onboardingConn)
	devLeadClient := devleadspb.NewDevLeadClient(lendingConn)
	devCreditReportClient := creditreportpb.NewDevCreditReportClient(lendingConn)
	accountsDbStatesClient := developer3.NewAccountsDbStatesClient(payConn)
	timelineServiceClient := timelinepb.NewTimelineServiceClient(payConn)
	luckyDrawServiceClient := luckydrawpb.NewLuckyDrawServiceClient(growthinfraConn)
	indexerClient := indexerpb.NewIndexerClient(wealthdmfConn)
	actionBarClient := searchpb.NewActionBarClient(wealthdmfConn)
	consumerServiceClient := emailparserpb.NewConsumerServiceClient(wealthdmfConn)
	offerCatalogServiceClient := casperpb.NewOfferCatalogServiceClient(growthinfraConn)
	offerInventoryServiceClient := casperpb.NewOfferInventoryServiceClient(growthinfraConn)
	ledgerReconciliationClient := reconpb.NewLedgerReconciliationClient(payConn)
	emailParserClient := emailparserpb.NewEmailParserClient(wealthdmfConn)
	accessInfoClient := accessinfo.NewAccessInfoClient(wealthdmfConn)
	sportsManagerClient := sportspb.NewSportsManagerClient(wealthdmfConn)
	schedulerServiceClient := schedulerpb.NewSchedulerServiceClient(wealthdmfConn)
	userPreferenceClient := uppb.NewUserPreferenceClient(growthinfraConn)
	rewardsCampaignCommClient := campaigncomm.NewRewardsCampaignCommClient(growthinfraConn)
	accountStatementClient := statement.NewAccountStatementClient(payConn)
	connectedAccountClient := capb.NewConnectedAccountClient(centralgrowthConn)
	inAppReferralClient := inappreferralpb.NewInAppReferralClient(onboardingConn)
	serveFAQClient := inapphelpservingpb.NewServeFAQClient(cxConn)
	accountAggregatorClient := vgaapb.NewAccountAggregatorClient(vendorgatewayConn)
	inAppHelpMediaClient := mediapb.NewInAppHelpMediaClient(cxConn)
	devSearchClient := searchdevpb.NewDevSearchClient(wealthdmfConn)
	fileGeneratorClient := fgpb.NewFileGeneratorClient(wealthdmfConn)
	inAppTargetedCommsClient := tcpb.NewInAppTargetedCommsClient(growthinfraConn)
	prerequisiteHandlerClient := prhpb.NewPrerequisiteHandlerClient(wealthdmfConn)
	orderOperationsClient := mfops.NewOrderOperationsClient(wealthdmfConn)
	insightsClient := insights.NewInsightsClient(wealthdmfConn)
	reverseFeedManagerClient := rfpb.NewReverseFeedManagerClient(wealthdmfConn)
	seasonServiceClient := seasonspb.NewSeasonServiceClient(onboardingConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(growthinfraConn)
	mutualFundClient := mutualfund2.NewMutualFundClient(vendorgatewayConn)
	consumerClient := segmentconsumerpb.NewConsumerClient(growthinfraConn)
	reconciliationServiceClient := reconciliationpb.NewReconciliationServiceClient(wealthdmfConn)
	cxClient := cccxpb.NewCxClient(cardConn)
	cxClientVar2 := preapprovedloancxpb.NewCxClient(lendingConn)
	simulatorConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.SIMULATOR_GRPC_SERVER)
	defer epifigrpc.CloseConn(simulatorConn)
	preApprovedLoanClient := preapprovedloansimpb.NewPreApprovedLoanClient(simulatorConn)
	preApprovedLoanClientVar2 := preapprovedloanpb.NewPreApprovedLoanClient(lendingConn)
	profileEvaluatorClient := profileevaluatorsimpb.NewProfileEvaluatorClient(simulatorConn)
	referralsClient := salaryreferralspb.NewReferralsClient(centralgrowthConn)
	userActionsClient := useractions.NewUserActionsClient(onboardingConn)
	riskClient := riskpb.NewRiskClient(userriskConn)
	leaClient := leapb.NewLeaClient(userriskConn)
	nudgeServiceClient := nudge.NewNudgeServiceClient(growthinfraConn)
	accountsDevServiceClient := accountsdevpb.NewAccountsDevServiceClient(simulatorConn)
	discountServiceClient := discountspb.NewDiscountServiceClient(growthinfraConn)
	userIssueInfoServiceClient := userissueinfopb.NewUserIssueInfoServiceClient(cxConn)
	fileGeneratorClientVar2 := payfilegeneratorpb.NewFileGeneratorClient(payConn)
	redListClient := redlist.NewRedListClient(userriskConn)
	amlClient := amlpb.NewAmlClient(wealthdmfConn)
	celestialClient := celestialpb.NewCelestialClient(nebulaConn)
	internationalFundTransferClient := iftpb.NewInternationalFundTransferClient(payConn)
	caseManagementClient := casemanagementpb.NewCaseManagementClient(userriskConn)
	temporalConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.TEMPORAL_SERVER)
	defer epifigrpc.CloseConn(temporalConn)
	workflowServiceClient := temporalworkflowpb.NewWorkflowServiceClient(temporalConn)
	employmentFeClient := employment.NewEmploymentFeClient(onboardingConn)
	accountsClient := vgaccountspb.NewAccountsClient(vendorgatewayConn)
	operationsClient := ussoperationspb.NewOperationsClient(wealthdmfConn)
	externalValidateClient := extvalidate.NewExternalValidateClient(vendorgatewayConn)
	uNNameCheckClient := ncpb.NewUNNameCheckClient(vendorgatewayConn)
	cmsServiceClient := cmspb.NewCmsServiceClient(growthinfraConn)
	dynamicUIElementServiceClient := dynuielpb.NewDynamicUIElementServiceClient(wealthdmfConn)
	cxClientVar3 := cardcxpb.NewCxClient(cardConn)
	commsDevActionClient := commsdeveloperactionspb.NewCommsDevActionClient(growthinfraConn)
	kycAgentServiceClient := agentpb.NewKycAgentServiceClient(onboardingConn)
	simulationClient := p2investmentpb.NewSimulationClient(wealthdmfConn)
	stocksClient := stockspb.NewStocksClient(vendorgatewayConn)
	managerClient := questmanagerpb.NewManagerClient(growthinfraConn)
	eKYCClient := ekycpb.NewEKYCClient(vendorgatewayConn)
	p2PClient := vgp2ppb.NewP2PClient(vendorgatewayConn)
	mfFolioServiceClient := foliopb.NewMfFolioServiceClient(wealthdmfConn)
	simulatorClient := rewardsimulatorpb.NewSimulatorClient(growthinfraConn)
	accountManagerClient := accountmanagerpb.NewAccountManagerClient(wealthdmfConn)
	whiteListClient := whitelistpb.NewWhiteListClient(userriskConn)
	serviceClient := irpb.NewServiceClient(cxConn)
	journeyServiceClient := journey.NewJourneyServiceClient(growthinfraConn)
	troubleshootClient := troubleshootpb.NewTroubleshootClient(onboardingConn)
	troubleshootClientVar2 := troubleshootpb.NewTroubleshootClient(sgapigatewayConn)
	alfredClient := alfredpb.NewAlfredClient(onboardingConn)
	investmentAggregatorClient := invaggrpb.NewInvestmentAggregatorClient(wealthdmfConn)
	accountAggregatorClientVar2 := aaorderpb.NewAccountAggregatorClient(payConn)
	kYCClient := sgkycapigatewaypb.NewKYCClient(goblinConn)
	salaryB2BClient := salaryb2bpb.NewSalaryB2BClient(cxConn)
	lmsClient := sglmspb.NewLmsClient(goblinConn)
	dynamicUIElementServiceClientVar2 := spdynamicelementpb.NewDynamicUIElementServiceClient(centralgrowthConn)
	applicationClient := sgapplicationpb.NewApplicationClient(goblinConn)
	vendornotificationConn := epifigrpc.NewServerConn(cfg.CX_SERVER, cfg.VENDOR_NOTIFI_SERVER)
	defer epifigrpc.CloseConn(vendornotificationConn)
	recurringPaymentClient := rpfederalpb.NewRecurringPaymentClient(vendornotificationConn)
	epfClient := epfpb.NewEpfClient(wealthdmfConn)
	mFExternalOrdersClient := mfexternalpb.NewMFExternalOrdersClient(wealthdmfConn)
	netWorthClient := networthpb.NewNetWorthClient(wealthdmfConn)
	consentClient := consentpb.NewConsentClient(onboardingConn)
	investmentAnalyticsClient := investmentanalyserpb.NewInvestmentAnalyticsClient(wealthdmfConn)
	developerConsoleServiceClient := devconsolepb.NewDeveloperConsoleServiceClient(wealthdmfConn)
	profileClient := profilepb2.NewProfileClient(userriskConn)
	locationClient := location.NewLocationClient(onboardingConn)
	locationClientVar2 := location2.NewLocationClient(onboardingConn)
	screenerClient := screener.NewScreenerClient(onboardingConn)
	fennelFeatureStoreClient := fennelpb.NewFennelFeatureStoreClient(vendorgatewayConn)
	scienapticClient := vgscienapticpb.NewScienapticClient(vendorgatewayConn)
	paymentClientVar10 := vgpaymentpb.NewPaymentClient(vendorgatewayConn)
	cxClientVar5 := salarycxpb.NewCxClient(centralgrowthConn)
	healthInsuranceClient := healthinsurancepb.NewHealthInsuranceClient(centralgrowthConn)
	employerNameCategoriserClient := employernamecategoriserpb.NewEmployerNameCategoriserClient(vendorgatewayConn)
	cxClientVar6 := p2pcxpb.NewCxClient(wealthdmfConn)
	billingClient := ffbillpb.NewBillingClient(cardConn)
	creditLimitEstimatorClient := limitestimatorpb.NewCreditLimitEstimatorClient(cardConn)
	loanManagementSystemClient := fflmspb.NewLoanManagementSystemClient(cardConn)
	txnAggregatesClient := ffpinotpb.NewTxnAggregatesClient(cardConn)
	preApprovedLoanSherlockBannersClient := palsherlockbannerspb.NewPreApprovedLoanSherlockBannersClient(lendingConn)
	vkycCallClient := vkyccall.NewVkycCallClient(onboardingConn)
	vkycCallClientVar2 := vkyccall.NewVkycCallClient(sgapigatewayConn)
	senseforthLiveChatFallbackClient := vgsenseforthpb.NewSenseforthLiveChatFallbackClient(vendorgatewayConn)
	federalEscalationServiceClient := escalfederalvgpb.NewFederalEscalationServiceClient(vendorgatewayConn)
	watsonClientClient := simulatorwcpb.NewWatsonClientClient(simulatorConn)
	mockWatsonClientServiceClient := mockwatsonclient.NewMockWatsonClientServiceClient(cxConn)
	watsonClientVar3 := savingswatsonclient.NewWatsonClient(centralgrowthConn)
	watsonClientVar4 := onboardingwatsonclient.NewWatsonClient(onboardingConn)
	payIncidentManagerClient := payincidentmanagerpb.NewPayIncidentManagerClient(payConn)
	watsonClientVar5 := investmentwatsonpb.NewWatsonClient(wealthdmfConn)
	watsonClientVar6 := depositwatsonpb.NewWatsonClient(wealthdmfConn)
	incidentManagerClient := p2pincidentmanagerpb.NewIncidentManagerClient(wealthdmfConn)
	watsonInfoProviderClient := watsoninfoproviderpb.NewWatsonInfoProviderClient(cxConn)
	manualTicketStageWiseCommsClient := stagewisecommspb.NewManualTicketStageWiseCommsClient(cxConn)
	crmIssueTrackerIntegrationClient := citpb.NewCrmIssueTrackerIntegrationClient(cxConn)
	leadManagementClient := vgleadsquaredpb.NewLeadManagementClient(vendorgatewayConn)
	txnAggregatesClientVar2 := txnaggregatespb.NewTxnAggregatesClient(wealthdmfConn)
	eODBalanceClient := tieringpinotpb.NewEODBalanceClient(centralgrowthConn)
	solutionsClient := solpb.NewSolutionsClient(vendorgatewayConn)
	cXInHouseClient := inhouse.NewCXInHouseClient(vendorgatewayConn)
	creditReportManagerClient := creditreport.NewCreditReportManagerClient(lendingConn)
	feedbackSubscriptionServiceClient := cxquestionresponsesubscriptionpb.NewFeedbackSubscriptionServiceClient(cxConn)
	mockCustomEligibilityEvaluationServiceClient := mockfeedbackengineclient.NewMockCustomEligibilityEvaluationServiceClient(cxConn)
	vKYCFeClient := vkycpb.NewVKYCFeClient(onboardingConn)
	issueConfigManagementClient := icpb.NewIssueConfigManagementClient(cxConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.NewRateLimitInterceptor(cxRedisStore, rateLimiterRedisStore)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire.NewUnaryAuthInterceptor(ctx, sherlockPGDB, casbinClient)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3 := servergenwire.NewTicketValidationInterceptor(ctx, sherlockPGDB, freshdeskClient, cxRedisStore, sqsClient)
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	unaryServerInterceptorVar4 := servergenwire.NewEnricherInterceptor(sherlockPGDB, usersClient, actorClient, chatsClient, vendorMappingServiceClient)
	if unaryServerInterceptorVar4 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar4)
	}

	unaryServerInterceptorVar5, err := servergenwire2.CollapserInterceptor(collapserRueidisRedisStore, collapserRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar5 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar5)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	streamServerInterceptor := servergenwire.AuthStreamInterceptor(ctx, sherlockPGDB, casbinClient)
	if streamServerInterceptor != nil {
		streamInterceptors = append(streamInterceptors, streamServerInterceptor)
	}

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	httpMux := http.NewServeMux()
	_ = httpMux

	var grpcWebUnaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptorVar6, err := servergenwire3.JarvisAuthInterceptor(ctx, gconf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc web unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptorVar6 != nil {
		grpcWebUnaryInterceptors = append(grpcWebUnaryInterceptors, unaryServerInterceptorVar6)
	}

	var grpcWebStreamInterceptors []grpc.StreamServerInterceptor

	grpcWebServer := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), prometheus.DefBuckets, grpcWebUnaryInterceptors, grpcWebStreamInterceptors, extractFeRespStatusWithErrMsgFunc)

	err = setupActoractivity(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, actorActivityClientVar2, accountingClient, actorClient, cXClient, orderServiceClient, savingsClient, piClient, usersClient, depositClient, catalogManagerClient, p2PInvestmentClient, portfolioManagerClient, orderManagerClient, balanceClient, cXClientVar2, payClient, paymentClient, accountPIRelationClient, upiOnboardingClient, sherlockPGDB, fireflyClient, cxRedisStore)
	if err != nil {
		return err
	}
	err = setupCasbin(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, casbinSherlockPGDBv1)
	if err != nil {
		return err
	}
	err = setupCx(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, sherlockPGDB, casbinClient, savingsClient, usersClient, actorClient, ticketClient, vendorMappingServiceClient, externalAccountsClient, tieringClient, bankCustomerServiceClient, onboardingClient, commsClient, operationalStatusServiceClient, chatsClient, piClient, paymentClient, orderServiceClient, depositClient, catalogManagerClient, p2PInvestmentClient, portfolioManagerClient, orderManagerClient, balanceClient, upiOnboardingClient, accountPIRelationClient, payClient, cXClientVar2, cXClient, fireflyClient, ozonetelClient, authClient, customerProfileClient, depositClientVar3, watsonClient, savingsAccountClosureClient, cardProvisioningClient, cardControlClient, cxRedisStore, freshdeskClient, salaryProgramClient, groupClient, freshchatClient, kycClient, vKYCClient, livenessClient, panClient, complianceClient, fitttClient, ruleManagerClient, catalogManagerClientVar4, txnCategorizerClient, recurringPaymentServiceClient, uPIClient, upiCXClient, rewardOffersClient, offerListingServiceClient, rewardsGeneratorClient, offerRedemptionServiceClient, exchangerOfferServiceClient, projectorServiceClient, externalVendorRedemptionServiceClient, disputeClient, issueResolutionFeedbackServiceClient, productClient, employmentClient, wealthOnboardingClient, wealthCxServiceClient, orderManagerClientVar5, paymentHandlerClient, commsDbStatesClient, devActorClient, devDepositClient, devKYCClient, devUserClient, devClient, devPaymentIntrumentClient, cardDbStatesClient, devLivenessClient, savingsDbStatesClient, devClientVar2, devInapphelpClient, devCXClient, devCasbinClient, devInsightsClient, devCategorizerClient, rewardsDevClient, devAuthClient, devVendorMappingClient, devTimelineClient, casperDevClient, rMSDbStatesClient, devMerchantClient, fITTTDbStatesClient, devConnectedAccClient, devInAppReferralClient, devWealthOnboardingClient, mutualFundDbStatesClient, recurringPaymentDevClient, enachDevClient, devP2PInvestmentClient, segmentDbStatesClient, nudgeDbStatesClient, salaryProgramDevClient, devAnalyserClient, devPreApprovedLoanClient, devFireflyClient, developerClient, developerClientVar2, developerClientVar3, devOrchestratorClient, devClientVar3, dBStateClient, tieringDevServiceClient, amlDevServiceClient, developerClientVar4, developerClientVar5, questDbStatesClient, devUpcomingTransactionsClient, healthEngineDevClient, cmsDevClient, devBankCustClient, devOmegleClient, devOmegleClientVar2, devVkycCallClient, devVkycCallClientVar2, devEmploymentClient, developerClientVar6, dBStateServiceClient, tspUserServiceClient, developerClientVar7, devLeadClient, devCreditReportClient, accountsDbStatesClient, timelineServiceClient, luckyDrawServiceClient, indexerClient, actionBarClient, consumerServiceClient, offerCatalogServiceClient, offerInventoryServiceClient, ledgerReconciliationClient, emailParserClient, accessInfoClient, sportsManagerClient, schedulerServiceClient, userPreferenceClient, rewardsCampaignCommClient, accountStatementClient, connectedAccountClient, inAppReferralClient, serveFAQClient, accountAggregatorClient, inAppHelpMediaClient, devSearchClient, fileGeneratorClient, inAppTargetedCommsClient, prerequisiteHandlerClient, orderOperationsClient, insightsClient, reverseFeedManagerClient, seasonServiceClient, segmentationServiceClient, mutualFundClient, consumerClient, reconciliationServiceClient, cxClient, cxClientVar2, preApprovedLoanClient, preApprovedLoanClientVar2, profileEvaluatorClient, referralsClient, userActionsClient, riskClient, leaClient, nudgeServiceClient, accountsDevServiceClient, discountServiceClient, userIssueInfoServiceClient, fileGeneratorClientVar2, redListClient, amlClient, celestialClient, internationalFundTransferClient, caseManagementClient, workflowServiceClient, employmentFeClient, accountsClient, operationsClient, externalValidateClient, uNNameCheckClient, cmsServiceClient, dynamicUIElementServiceClient, cxClientVar3, commsDevActionClient, kycAgentServiceClient, simulationClient, stocksClient, managerClient, eKYCClient, p2PClient, mfFolioServiceClient, simulatorClient, accountManagerClient, whiteListClient, serviceClient, journeyServiceClient, troubleshootClient, troubleshootClientVar2, alfredClient, investmentAggregatorClient, accountingClient, accountAggregatorClientVar2, kYCClient, salaryB2BClient, lmsClient, dynamicUIElementServiceClientVar2, applicationClient, recurringPaymentClient, epfClient, mFExternalOrdersClient, netWorthClient, consentClient, investmentAnalyticsClient, developerConsoleServiceClient, profileClient, locationClient, locationClientVar2, screenerClient, fennelFeatureStoreClient, scienapticClient, actorActivityClient, paymentClientVar10, cxClientVar5, healthInsuranceClient, employerNameCategoriserClient, cxClientVar6, billingClient, creditLimitEstimatorClient, loanManagementSystemClient, txnAggregatesClient, preApprovedLoanSherlockBannersClient, vkycCallClient, vkycCallClientVar2, senseforthLiveChatFallbackClient, federalEscalationServiceClient, rateLimiterRedisStore, watsonClientClient, mockWatsonClientServiceClient, watsonClientVar3, watsonClientVar4, payIncidentManagerClient, watsonClientVar5, watsonClientVar6, incidentManagerClient, watsonInfoProviderClient, manualTicketStageWiseCommsClient, crmIssueTrackerIntegrationClient, leadManagementClient, txnAggregatesClientVar2, eODBalanceClient)
	if err != nil {
		return err
	}
	err = setupInapphelp(ctx, s, grpcWebServer, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, inapphelpPGDB, solutionsClient, depositClient, cXInHouseClient, actorClient, usersClient, groupClient, onboardingClient, rewardsGeneratorClient, orderServiceClient, rateLimiterRedisStore, chatsClient, vendorMappingServiceClient, fitttClient, cardProvisioningClient, ticketClient, connectedAccountClient, netWorthClient, creditReportManagerClient, segmentationServiceClient, sherlockPGDB, feedbackSubscriptionServiceClient, payClient, mockCustomEligibilityEvaluationServiceClient, vKYCFeClient, actorActivityClient, watsonClient, serviceClient, cxRedisStore, actionBarClient, issueConfigManagementClient, accountingClient, tieringClient, profileClient)
	if err != nil {
		return err
	}
	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.InitCxServer(s, grpcWebServer, sherlockPGDB, watsonClient, actorActivityClient, ticketClient) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitCxServer"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := hook.InitCxPoliciesInCasbinServer(s, casbinSherlockPGDBv1) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitCxPoliciesInCasbinServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	cleanupFnVar3, err := servergenwire2.LoadCollapserWithGrpcServer(gconf, s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCollapserWithGrpcServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar3()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServerWithGrpcWeb(s, grpcWebServer, gconf.ServerPorts(), conf.HttpCorsOptions, string(gconf.Name()))
		return nil
	})

	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupActoractivity(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	actorActivityClientVar2 orderactoractivitypb.ActorActivityClient,
	accountingClient ffaccountspb.AccountingClient,
	actorClient actorpb.ActorClient,
	cXClient opb.CXClient,
	orderServiceClient orderpb.OrderServiceClient,
	savingsClient savingspb.SavingsClient,
	piClient pipb.PiClient,
	usersClient userspb.UsersClient,
	depositClient depositpb.DepositClient,
	catalogManagerClient investmentcatalogpb.CatalogManagerClient,
	p2PInvestmentClient p2investmentpb.P2PInvestmentClient,
	portfolioManagerClient portfolio.PortfolioManagerClient,
	orderManagerClient ordermanagerpb.OrderManagerClient,
	balanceClient accountbalancepb.BalanceClient,
	cXClientVar2 paycxpb.CXClient,
	payClient paypb.PayClient,
	paymentClient paymentpb.PaymentClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	sherlockPGDB cmdtypes.SherlockPGDB,
	fireflyClient firefly.FireflyClient,
	cxRedisStore cxtypes.CxRedisStore) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	actor_activityConf, err := actoractivityconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACTOR_ACTIVITY_SERVICE))
		return err
	}
	_ = actor_activityConf

	actor_activityGenConf, err := dynconf.LoadConfig(actoractivityconf.Load, genconf2.NewConfig, cfg.ACTOR_ACTIVITY_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACTOR_ACTIVITY_SERVICE))
		return err
	}

	_ = actor_activityGenConf

	service := wire.InitializeActorActivityService(actor_activityGenConf, actorActivityClientVar2, accountingClient, actorClient, cXClient, orderServiceClient, savingsClient, piClient, usersClient, depositClient, catalogManagerClient, p2PInvestmentClient, portfolioManagerClient, orderManagerClient, balanceClient, cXClientVar2, payClient, paymentClient, accountPIRelationClient, upiOnboardingClient, sherlockPGDB, fireflyClient, cxRedisStore)

	aapb.RegisterActorActivityServer(s, service)

	configNameToConfMap[cfg.ConfigName(cfg.ACTOR_ACTIVITY_SERVICE)] = &commonexplorer.Config{StaticConf: &actoractivityconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupCasbin(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	casbinSherlockPGDBv1 types.CasbinSherlockPGDBv1) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	casbinConf, err := casbinconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CASBIN_SERVICE))
		return err
	}
	_ = casbinConf

	devCasbin, err := wire2.InitCasbinDev(casbinSherlockPGDBv1, casbinConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	developer2.RegisterDevCasbinServer(s, devCasbin)

	configNameToConfMap[cfg.ConfigName(cfg.CASBIN_SERVICE)] = &commonexplorer.Config{StaticConf: &casbinconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupCx(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	sherlockPGDB cmdtypes.SherlockPGDB,
	casbinClient casbin.CasbinClient,
	savingsClient savingspb.SavingsClient,
	usersClient userspb.UsersClient,
	actorClient actorpb.ActorClient,
	ticketClient ticketpb.TicketClient,
	vendorMappingServiceClient vendormapping.VendorMappingServiceClient,
	externalAccountsClient extacct.ExternalAccountsClient,
	tieringClient betieringpb.TieringClient,
	bankCustomerServiceClient bankcustpb.BankCustomerServiceClient,
	onboardingClient onboarding.OnboardingClient,
	commsClient commspb.CommsClient,
	operationalStatusServiceClient operstatuspb.OperationalStatusServiceClient,
	chatsClient chat.ChatsClient,
	piClient pipb.PiClient,
	paymentClient paymentpb.PaymentClient,
	orderServiceClient orderpb.OrderServiceClient,
	depositClient depositpb.DepositClient,
	catalogManagerClient investmentcatalogpb.CatalogManagerClient,
	p2PInvestmentClient p2investmentpb.P2PInvestmentClient,
	portfolioManagerClient portfolio.PortfolioManagerClient,
	orderManagerClient ordermanagerpb.OrderManagerClient,
	balanceClient accountbalancepb.BalanceClient,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	payClient paypb.PayClient,
	cXClientVar2 paycxpb.CXClient,
	cXClient opb.CXClient,
	fireflyClient firefly.FireflyClient,
	ozonetelClient ozonetel.OzonetelClient,
	authClient authpb.AuthClient,
	customerProfileClient profilepb.CustomerProfileClient,
	depositClientVar3 vgdepositpb.DepositClient,
	watsonClient watsonpb.WatsonClient,
	savingsAccountClosureClient saclosurepb.SavingsAccountClosureClient,
	cardProvisioningClient cardpb.CardProvisioningClient,
	cardControlClient ccpb.CardControlClient,
	cxRedisStore cxtypes.CxRedisStore,
	freshdeskClient freshdesk.FreshdeskClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	groupClient usergrouppb.GroupClient,
	freshchatClient vgfcpb.FreshchatClient,
	kycClient kycpb.KycClient,
	vKYCClient vkycpb.VKYCClient,
	livenessClient livenesspb.LivenessClient,
	panClient pan.PanClient,
	complianceClient compliancepb.ComplianceClient,
	fitttClient fitttpb.FitttClient,
	ruleManagerClient manager.RuleManagerClient,
	catalogManagerClientVar4 usstockscatalogpb.CatalogManagerClient,
	txnCategorizerClient categorizerpb.TxnCategorizerClient,
	recurringPaymentServiceClient recurringpaymentpb.RecurringPaymentServiceClient,
	uPIClient upipb.UPIClient,
	upiCXClient cx.UpiCXClient,
	rewardOffersClient rewardofferpb.RewardOffersClient,
	offerListingServiceClient casperpb.OfferListingServiceClient,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient,
	offerRedemptionServiceClient redemption.OfferRedemptionServiceClient,
	exchangerOfferServiceClient exchangerpb.ExchangerOfferServiceClient,
	projectorServiceClient rewardsprojectionpb.ProjectorServiceClient,
	externalVendorRedemptionServiceClient evrpb.ExternalVendorRedemptionServiceClient,
	disputeClient dispute.DisputeClient,
	issueResolutionFeedbackServiceClient issueresolutionfeedbackpb.IssueResolutionFeedbackServiceClient,
	productClient product.ProductClient,
	employmentClient employment.EmploymentClient,
	wealthOnboardingClient wopb.WealthOnboardingClient,
	wealthCxServiceClient wocxpb.WealthCxServiceClient,
	orderManagerClientVar5 investmentorder.OrderManagerClient,
	paymentHandlerClient phpb.PaymentHandlerClient,
	commsDbStatesClient commsdeveloper.CommsDbStatesClient,
	devActorClient actordeveloper.DevActorClient,
	devDepositClient depositdeveloper.DevDepositClient,
	devKYCClient kycdeveloper.DevKYCClient,
	devUserClient userdeveloper.DevUserClient,
	devClient orderdeveloper.DevClient,
	devPaymentIntrumentClient pideveloper.DevPaymentIntrumentClient,
	cardDbStatesClient carddeveloper.CardDbStatesClient,
	devLivenessClient livenessdeveloper.DevLivenessClient,
	savingsDbStatesClient savingsdeveloper.SavingsDbStatesClient,
	devClientVar2 upideveloper.DevClient,
	devInapphelpClient inapphelpdeveloper.DevInapphelpClient,
	devCXClient cxdeveloper.DevCXClient,
	devCasbinClient developer2.DevCasbinClient,
	devInsightsClient insightsdeveloper.DevInsightsClient,
	devCategorizerClient categorizerdeveloper.DevCategorizerClient,
	rewardsDevClient rewardsdeveloper.RewardsDevClient,
	devAuthClient authdeveloper.DevAuthClient,
	devVendorMappingClient vmdeveloper.DevVendorMappingClient,
	devTimelineClient timelinedeveloper.DevTimelineClient,
	casperDevClient casperdeveloper.CasperDevClient,
	rMSDbStatesClient rmsdeveloper.RMSDbStatesClient,
	devMerchantClient merchantdeveloper.DevMerchantClient,
	fITTTDbStatesClient fitttdeveloper.FITTTDbStatesClient,
	devConnectedAccClient cadeveloper.DevConnectedAccClient,
	devInAppReferralClient inappreferraldeveloper.DevInAppReferralClient,
	devWealthOnboardingClient wonbdeveloper.DevWealthOnboardingClient,
	mutualFundDbStatesClient investdeveloper.MutualFundDbStatesClient,
	recurringPaymentDevClient recurrdeveloper.RecurringPaymentDevClient,
	enachDevClient enachdeveloper.EnachDevClient,
	devP2PInvestmentClient p2pdeveloper.DevP2PInvestmentClient,
	segmentDbStatesClient segmentdeveloper.SegmentDbStatesClient,
	nudgeDbStatesClient nudgedeveloper.NudgeDbStatesClient,
	salaryProgramDevClient spdeveloper.SalaryProgramDevClient,
	devAnalyserClient analyserdeveloper.DevAnalyserClient,
	devPreApprovedLoanClient pldeveloper.DevPreApprovedLoanClient,
	devFireflyClient ffdeveloper.DevFireflyClient,
	developerClient celestialdeveloper.DeveloperClient,
	developerClientVar2 riskdeveloper.DeveloperClient,
	developerClientVar3 screenerdeveloper.DeveloperClient,
	devOrchestratorClient authorchdeveloper.DevOrchestratorClient,
	devClientVar3 paydeveloper.DevClient,
	dBStateClient usstockdeveloper.DBStateClient,
	tieringDevServiceClient tieringdeveloper.TieringDevServiceClient,
	amlDevServiceClient amldeveloper.AmlDevServiceClient,
	developerClientVar4 alfreddeveloper.DeveloperClient,
	developerClientVar5 pandeveloper.DeveloperClient,
	questDbStatesClient questdeveloper.QuestDbStatesClient,
	devUpcomingTransactionsClient upcomingtxndeveloper.DevUpcomingTransactionsClient,
	healthEngineDevClient hedeveloper.HealthEngineDevClient,
	cmsDevClient cmsdeveloper.CmsDevClient,
	devBankCustClient bankcustdeveloper.DevBankCustClient,
	devOmegleClient verifipkg.DevOmegleClientToSGApiGatewayServer,
	devOmegleClientVar2 verifipkg.DevOmegleClientToOnboardingServer,
	devVkycCallClient verifipkg.DevVkycCallClientToSGApiGatewayServer,
	devVkycCallClientVar2 verifipkg.DevVkycCallClientToOnboardingServer,
	devEmploymentClient employmentdeveloper.DevEmploymentClient,
	developerClientVar6 collectiondeveloperpb.DeveloperClient,
	dBStateServiceClient sgapigwdbspb.DBStateServiceClient,
	tspUserServiceClient tspdbstate.TspUserServiceClient,
	developerClientVar7 developerclient.DeveloperClient,
	devLeadClient devleadspb.DevLeadClient,
	devCreditReportClient creditreportpb.DevCreditReportClient,
	accountsDbStatesClient developer3.AccountsDbStatesClient,
	timelineServiceClient timelinepb.TimelineServiceClient,
	luckyDrawServiceClient luckydrawpb.LuckyDrawServiceClient,
	indexerClient indexerpb.IndexerClient,
	actionBarClient searchpb.ActionBarClient,
	consumerServiceClient emailparserpb.ConsumerServiceClient,
	offerCatalogServiceClient casperpb.OfferCatalogServiceClient,
	offerInventoryServiceClient casperpb.OfferInventoryServiceClient,
	ledgerReconciliationClient reconpb.LedgerReconciliationClient,
	emailParserClient emailparserpb.EmailParserClient,
	accessInfoClient accessinfo.AccessInfoClient,
	sportsManagerClient sportspb.SportsManagerClient,
	schedulerServiceClient schedulerpb.SchedulerServiceClient,
	userPreferenceClient uppb.UserPreferenceClient,
	rewardsCampaignCommClient campaigncomm.RewardsCampaignCommClient,
	accountStatementClient statement.AccountStatementClient,
	connectedAccountClient capb.ConnectedAccountClient,
	inAppReferralClient inappreferralpb.InAppReferralClient,
	serveFAQClient inapphelpservingpb.ServeFAQClient,
	accountAggregatorClient vgaapb.AccountAggregatorClient,
	inAppHelpMediaClient mediapb.InAppHelpMediaClient,
	devSearchClient searchdevpb.DevSearchClient,
	fileGeneratorClient fgpb.FileGeneratorClient,
	inAppTargetedCommsClient tcpb.InAppTargetedCommsClient,
	prerequisiteHandlerClient prhpb.PrerequisiteHandlerClient,
	orderOperationsClient mfops.OrderOperationsClient,
	insightsClient insights.InsightsClient,
	reverseFeedManagerClient rfpb.ReverseFeedManagerClient,
	seasonServiceClient seasonspb.SeasonServiceClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	mutualFundClient mutualfund2.MutualFundClient,
	consumerClient segmentconsumerpb.ConsumerClient,
	reconciliationServiceClient reconciliationpb.ReconciliationServiceClient,
	cxClient cccxpb.CxClient,
	cxClientVar2 preapprovedloancxpb.CxClient,
	preApprovedLoanClient preapprovedloansimpb.PreApprovedLoanClient,
	preApprovedLoanClientVar2 preapprovedloanpb.PreApprovedLoanClient,
	profileEvaluatorClient profileevaluatorsimpb.ProfileEvaluatorClient,
	referralsClient salaryreferralspb.ReferralsClient,
	userActionsClient useractions.UserActionsClient,
	riskClient riskpb.RiskClient,
	leaClient leapb.LeaClient,
	nudgeServiceClient nudge.NudgeServiceClient,
	accountsDevServiceClient accountsdevpb.AccountsDevServiceClient,
	discountServiceClient discountspb.DiscountServiceClient,
	userIssueInfoServiceClient userissueinfopb.UserIssueInfoServiceClient,
	fileGeneratorClientVar2 payfilegeneratorpb.FileGeneratorClient,
	redListClient redlist.RedListClient,
	amlClient amlpb.AmlClient,
	celestialClient celestialpb.CelestialClient,
	internationalFundTransferClient iftpb.InternationalFundTransferClient,
	caseManagementClient casemanagementpb.CaseManagementClient,
	workflowServiceClient temporalworkflowpb.WorkflowServiceClient,
	employmentFeClient employment.EmploymentFeClient,
	accountsClient vgaccountspb.AccountsClient,
	operationsClient ussoperationspb.OperationsClient,
	externalValidateClient extvalidate.ExternalValidateClient,
	uNNameCheckClient ncpb.UNNameCheckClient,
	cmsServiceClient cmspb.CmsServiceClient,
	dynamicUIElementServiceClient dynuielpb.DynamicUIElementServiceClient,
	cxClientVar3 cardcxpb.CxClient,
	commsDevActionClient commsdeveloperactionspb.CommsDevActionClient,
	kycAgentServiceClient agentpb.KycAgentServiceClient,
	simulationClient p2investmentpb.SimulationClient,
	stocksClient stockspb.StocksClient,
	managerClient questmanagerpb.ManagerClient,
	eKYCClient ekycpb.EKYCClient,
	p2PClient vgp2ppb.P2PClient,
	mfFolioServiceClient foliopb.MfFolioServiceClient,
	simulatorClient rewardsimulatorpb.SimulatorClient,
	accountManagerClient accountmanagerpb.AccountManagerClient,
	whiteListClient whitelistpb.WhiteListClient,
	serviceClient irpb.ServiceClient,
	journeyServiceClient journey.JourneyServiceClient,
	troubleshootClient verifipkg.VkycCallTroubleshootClientToOnboardingServer,
	troubleshootClientVar2 verifipkg.VkycCallTroubleshootClientToSGApiGatewayServer,
	alfredClient alfredpb.AlfredClient,
	investmentAggregatorClient invaggrpb.InvestmentAggregatorClient,
	accountingClient ffaccountspb.AccountingClient,
	accountAggregatorClientVar2 aaorderpb.AccountAggregatorClient,
	kYCClient sgkycapigatewaypb.KYCClient,
	salaryB2BClient salaryb2bpb.SalaryB2BClient,
	lmsClient sglmspb.LmsClient,
	dynamicUIElementServiceClientVar2 spdynamicelementpb.DynamicUIElementServiceClient,
	applicationClient sgapplicationpb.ApplicationClient,
	recurringPaymentClient rpfederalpb.RecurringPaymentClient,
	epfClient epfpb.EpfClient,
	mFExternalOrdersClient mfexternalpb.MFExternalOrdersClient,
	netWorthClient networthpb.NetWorthClient,
	consentClient consentpb.ConsentClient,
	investmentAnalyticsClient investmentanalyserpb.InvestmentAnalyticsClient,
	developerConsoleServiceClient devconsolepb.DeveloperConsoleServiceClient,
	profileClient profilepb2.ProfileClient,
	locationClient location.LocationClient,
	locationClientVar2 location2.LocationClient,
	screenerClient screener.ScreenerClient,
	fennelFeatureStoreClient fennelpb.FennelFeatureStoreClient,
	scienapticClient vgscienapticpb.ScienapticClient,
	actorActivityClient aapb.ActorActivityClient,
	paymentClientVar10 vgpaymentpb.PaymentClient,
	cxClientVar5 salarycxpb.CxClient,
	healthInsuranceClient healthinsurancepb.HealthInsuranceClient,
	employerNameCategoriserClient employernamecategoriserpb.EmployerNameCategoriserClient,
	cxClientVar6 p2pcxpb.CxClient,
	billingClient ffbillpb.BillingClient,
	creditLimitEstimatorClient limitestimatorpb.CreditLimitEstimatorClient,
	loanManagementSystemClient fflmspb.LoanManagementSystemClient,
	txnAggregatesClient ffpinotpb.TxnAggregatesClient,
	preApprovedLoanSherlockBannersClient palsherlockbannerspb.PreApprovedLoanSherlockBannersClient,
	vkycCallClient verifipkg.VkycCallClientToOnboardingServer,
	vkycCallClientVar2 verifipkg.VkycCallClientToSGApiGatewayServer,
	senseforthLiveChatFallbackClient vgsenseforthpb.SenseforthLiveChatFallbackClient,
	federalEscalationServiceClient escalfederalvgpb.FederalEscalationServiceClient,
	rateLimiterRedisStore cmdtypes.RateLimiterRedisStore,
	watsonClientClient simulatorwcpb.WatsonClientClient,
	mockWatsonClientServiceClient mockwatsonclient.MockWatsonClientServiceClient,
	watsonClientVar3 savingswatsonclient.WatsonClient,
	watsonClientVar4 onboardingwatsonclient.WatsonClient,
	payIncidentManagerClient payincidentmanagerpb.PayIncidentManagerClient,
	watsonClientVar5 investmentwatsonpb.WatsonClient,
	watsonClientVar6 depositwatsonpb.WatsonClient,
	incidentManagerClient p2pincidentmanagerpb.IncidentManagerClient,
	watsonInfoProviderClient watsoninfoproviderpb.WatsonInfoProviderClient,
	manualTicketStageWiseCommsClient stagewisecommspb.ManualTicketStageWiseCommsClient,
	crmIssueTrackerIntegrationClient citpb.CrmIssueTrackerIntegrationClient,
	leadManagementClient vgleadsquaredpb.LeadManagementClient,
	txnAggregatesClientVar2 txnaggregatespb.TxnAggregatesClient,
	eODBalanceClient tieringpinotpb.EODBalanceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	cxConf, err := cxconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CX_SERVICE))
		return err
	}
	_ = cxConf

	cxGenConf, err := dynconf.LoadConfig(cxconf.Load, cxgenconf.NewConfig, cfg.CX_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CX_SERVICE))
		return err
	}

	_ = cxGenConf

	freshdeskTicketPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.FreshdeskTicketPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	disputePublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.DisputePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	disputeUpdateTicketPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.DisputeUpdateTicketPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	disputeCreateTicketPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.DisputeCreateTicketPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	createTicketPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.CreateTicketPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	updateTicketPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.UpdateTicketPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	disputeExternalPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.DisputeExternalPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	payoutStatusCheckPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.PayoutStatusCheckPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	rMSEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.RMSEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	devActionDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.DevActionPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	iFTFileProcessorEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.IFTFileProcessorEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	rewardsManualGiveawayEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.RewardsManualGiveawayEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	riskCasePublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.RiskCasePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	riskDisputePublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.RiskDisputePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	casperItcDownloadFileQueuePublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.CasperItcDownloadFileQueuePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderUpdateEventForTxnCategorizationPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.OrderUpdateEventForTxnCategorizationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	aATxnCategorizationPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.AATxnCategorizationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cCTxnCategorizationPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.CCTxnCategorizationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	federalEscalationCreateEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.FederalEscalationCreateEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	celestialSignalWorkflowPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.CelestialSignalWorkflowPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	rewardsCreditCardTxnEventQueuePublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.RewardsCreditCardTxnEventQueuePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	rewardsOrderUpdateEventQueuePublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.RewardsOrderUpdateEventQueuePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	freshdeskContactPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.FreshdeskContactPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	disputeAddNoteTicketPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.DisputeAddNoteTicketPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	crmIssueTrackerIntegrationPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.CrmIssueTrackerIntegrationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	watsonIncidentReportingPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.WatsonIncidentReportingPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	watsonIncidentResolutionPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.WatsonIncidentResolutionPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	watsonTicketEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cxGenConf.WatsonTicketEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	callRoutingEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, cxGenConf.CallRoutingEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	createTicketEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, cxGenConf.CreateTicketEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	ticketUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, cxGenConf.TicketUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	cxS3Client := s3pkg.NewClient(awsConf, cxGenConf.CxS3Config().BucketName)
	cxS3ClientVar2 := s3pkg.NewClient(awsConf, cxGenConf.CxS3Config().BucketName)
	disputes3Client := s3pkg.NewClient(awsConf, cxGenConf.Dispute().S3BucketName())
	cxS3ClientVar3 := s3pkg.NewClient(awsConf, cxGenConf.CxS3Config().BucketName)
	cxS3ClientVar4 := s3pkg.NewClient(awsConf, cxGenConf.CxS3Config().BucketName)
	dataS3Client := s3pkg.NewClient(awsConf, cxGenConf.DataS3Config().BucketName)
	epifiIconsS3Client := s3pkg.NewClient(awsConf, cxGenConf.EpifiIconS3Config().BucketName)
	federalEscalationAttachmentsS3Client := s3pkg.NewClient(awsConf, cxGenConf.FederalEscalationConfig().FederalEscalationAttachmentBucketName())
	livenessVideoConfigS3Client := s3pkg.NewClient(awsConf, cxGenConf.LivenessVideoConfig().S3BucketName)
	cxS3ClientVar5 := s3pkg.NewClient(awsConf, cxGenConf.CxS3Config().BucketName)
	livenessVideoConfigS3ClientVar2 := s3pkg.NewClient(awsConf, cxGenConf.LivenessVideoConfig().S3BucketName)
	riskS3Client := s3pkg.NewClient(awsConf, cxGenConf.RiskS3Config().BucketName)
	salaryProgramS3Client := s3pkg.NewClient(awsConf, cxGenConf.SalaryOpsConfig().SalaryProgramS3BucketName())
	callRecordingS3Client := s3pkg.NewClient(awsConf, cxGenConf.CallRecording().CallRecordingBucketName)
	payIFTDocumentsS3Client := s3pkg.NewClient(awsConf, cxGenConf.InternationalFundTransfer().DocumentsBucketName())
	s3Client := s3pkg.NewClient(awsConf, cxGenConf.S3EventConsumerConfig().BucketName())
	salaryProgramNonProdS3Client := s3pkg.NewClient(awsConf, cxGenConf.SalaryProgramLeadManagementConfig().SalaryProgramS3BucketName)
	salaryProgramB2BS3Client := s3pkg.NewClient(awsConf, cxGenConf.SalaryProgramLeadManagementConfig().SalaryProgramB2BS3BucketName)
	cxS3ClientVar6 := s3pkg.NewClient(awsConf, cxGenConf.CxS3Config().BucketName)

	serviceVar2 := wire3.InitializeSherlockUserService(sherlockPGDB, casbinClient)

	sherlockuserpb.RegisterSherlockUserServiceServer(s, serviceVar2)

	serviceVar3 := wire3.InitializeProfileService(cxConf, sherlockPGDB, savingsClient, usersClient, actorClient, cxS3Client, ticketClient, vendorMappingServiceClient, cxGenConf, externalAccountsClient, tieringClient, bankCustomerServiceClient, onboardingClient, commsClient, operationalStatusServiceClient, chatsClient, piClient, paymentClient, orderServiceClient, depositClient, catalogManagerClient, p2PInvestmentClient, portfolioManagerClient, orderManagerClient, balanceClient, upiOnboardingClient, accountPIRelationClient, payClient, cXClientVar2, cXClient, fireflyClient)

	profilepb.RegisterCustomerProfileServer(s, serviceVar3)

	serviceVar4 := wire3.InitializeLandingPageService(usersClient, actorClient, ozonetelClient, chatsClient, sherlockPGDB, authClient, vendorMappingServiceClient, cxConf, cxGenConf, customerProfileClient)

	lppb.RegisterLandingPageServer(s, serviceVar4)

	serviceVar5 := wire3.InitializeAccountService(sherlockPGDB, externalAccountsClient, commsClient, cxConf, cxGenConf, depositClientVar3, bankCustomerServiceClient, watsonClient, ticketClient, actorClient, piClient, usersClient, paymentClient, orderServiceClient, savingsClient, depositClient, catalogManagerClient, p2PInvestmentClient, portfolioManagerClient, orderManagerClient, balanceClient, authClient, fireflyClient, savingsAccountClosureClient)

	cxaccountpb.RegisterAccountServer(s, serviceVar5)

	serviceVar6 := wire3.IntializeCustomerCardService(cxConf, cxGenConf, sherlockPGDB, usersClient, cardProvisioningClient, cardControlClient, tieringClient, payClient)

	cxcardpb.RegisterCardsServer(s, serviceVar6)

	customerAuth := wire3.InitializeCustomerAuth(commsClient, sherlockPGDB, cxGenConf, cxConf, usersClient, chatsClient, freshdeskTicketPublisher, orderServiceClient, bankCustomerServiceClient, onboardingClient, cxRedisStore)

	cxcapb.RegisterCustomerAuthenticationServer(s, customerAuth)

	authCallback := wire3.InitializeAuthCallbackService(sherlockPGDB, cxConf, cxGenConf, freshdeskClient, cxRedisStore, usersClient)

	cxcapb.RegisterCustomerAuthCallbackServer(s, authCallback)

	serviceVar7 := wire3.InitializeChatInitInformationService(sherlockPGDB, freshdeskTicketPublisher, cxGenConf, cxConf, vendorMappingServiceClient, cxRedisStore, onboardingClient, actorClient, usersClient, salaryProgramClient, authClient, groupClient, freshchatClient, freshdeskClient, broker, savingsClient, operationalStatusServiceClient, balanceClient, chatsClient)

	chat.RegisterChatsServer(s, serviceVar7)

	serviceVar8 := wire3.InitializeAuditLogService(sherlockPGDB, cxConf)

	alpb.RegisterAuditLogsServer(s, serviceVar8)

	serviceVar9 := wire3.InitializeKycService(kycClient, vKYCClient, cxConf, cxGenConf, sherlockPGDB, livenessClient, actorClient, usersClient, onboardingClient, bankCustomerServiceClient, panClient, complianceClient)

	kyc2.RegisterCustomerKYCServer(s, serviceVar9)

	serviceVar10 := wire3.InitializeFitttService(cxConf, cxGenConf, sherlockPGDB, usersClient, fitttClient, ruleManagerClient, catalogManagerClientVar4)

	cxfitttpb.RegisterFitttServer(s, serviceVar10)

	serviceVar11 := wire3.InitializeTransactionService(cxConf, cXClient, cxGenConf, sherlockPGDB, txnCategorizerClient, cXClientVar2, payClient, accountPIRelationClient, upiOnboardingClient, actorClient, piClient, usersClient, paymentClient, orderServiceClient, savingsClient, depositClient, catalogManagerClient, p2PInvestmentClient, portfolioManagerClient, orderManagerClient, balanceClient, recurringPaymentServiceClient, fireflyClient)

	transaction2.RegisterCustomerTransactionsServer(s, serviceVar11)

	serviceVar12 := wire3.InitializePiService(piClient, accountPIRelationClient, uPIClient, upiCXClient, sherlockPGDB, cxConf, cxGenConf, usersClient)

	paymentinstruments2.RegisterCustomerPIServer(s, serviceVar12)

	serviceVar13 := wire3.InitializeRewardsService(sherlockPGDB, cxConf, cxGenConf, rewardOffersClient, offerListingServiceClient, rewardsGeneratorClient, offerRedemptionServiceClient, exchangerOfferServiceClient, actorClient, usersClient, projectorServiceClient, externalVendorRedemptionServiceClient)

	repb.RegisterRewardsServer(s, serviceVar13)

	serviceVar14 := wire3.InitializeDisputeService(sherlockPGDB, disputePublisher, actorClient, cxConf, cxGenConf, broker, uPIClient, usersClient, groupClient, disputeUpdateTicketPublisher, disputeCreateTicketPublisher, createTicketPublisher, chatsClient, vendorMappingServiceClient, disputeClient, cxS3ClientVar2, updateTicketPublisher, bankCustomerServiceClient, disputeExternalPublisher, paymentClient, piClient, orderServiceClient, savingsClient, commsClient, cxRedisStore)

	dipb.RegisterDisputeServer(s, serviceVar14)

	serviceVar15 := wire3.InitializeDisputeJobService(usersClient, actorClient, chatsClient, vendorMappingServiceClient, sherlockPGDB, freshdeskClient, disputeUpdateTicketPublisher, disputeClient, disputes3Client, cxGenConf, savingsClient, commsClient, ticketClient, issueResolutionFeedbackServiceClient, updateTicketPublisher, disputeExternalPublisher, paymentClient, piClient, orderServiceClient, groupClient, disputeCreateTicketPublisher)

	job2.RegisterDisputeProcessingJobServer(s, serviceVar15)

	serviceVar16 := wire3.InitializeOnboardingService(kycClient, sherlockPGDB, livenessClient, onboardingClient, cxConf, groupClient, authClient, actorClient, usersClient, cxGenConf, productClient, salaryProgramClient, employmentClient)

	obpb.RegisterOnboardingServer(s, serviceVar16)

	serviceVar17 := wire3.InitializeWealthOnboardingService(cxConf, cxGenConf, sherlockPGDB, usersClient, wealthOnboardingClient, wealthCxServiceClient)

	wealthonboarding2.RegisterWealthOnboardingServer(s, serviceVar17)

	serviceVar18 := wire3.InitializeInvestmentService(cxConf, cxGenConf, sherlockPGDB, usersClient, catalogManagerClient, ruleManagerClient, orderManagerClientVar5, paymentHandlerClient, wealthOnboardingClient)

	mutualfund.RegisterInvestmentServer(s, serviceVar18)

	serviceVar19 := wire3.InitializeCommsService(commsClient, cxConf, cxGenConf, sherlockPGDB, usersClient)

	communications.RegisterCustomerCommunicationServer(s, serviceVar19)

	serviceVar20 := wire3.InitializeAppLogService(actorClient, chatsClient, vendorMappingServiceClient, cxRedisStore, commsClient, cxConf, cxS3ClientVar3, cxGenConf, sherlockPGDB, usersClient)

	applog2.RegisterAppLogServer(s, serviceVar20)

	ticketSummaryService := wire3.InitializeTicketSummaryService(freshdeskClient, chatsClient, actorClient, usersClient, vendorMappingServiceClient)

	ticketsummary2.RegisterTicketSummaryServer(s, ticketSummaryService)

	serviceVar21 := wire3.InitializeDbStatesService(commsDbStatesClient, devActorClient, devDepositClient, devKYCClient, devUserClient, devClient, devPaymentIntrumentClient, cardDbStatesClient, devLivenessClient, savingsDbStatesClient, devClientVar2, devInapphelpClient, devCXClient, devCasbinClient, devInsightsClient, devCategorizerClient, rewardsDevClient, devAuthClient, devVendorMappingClient, devTimelineClient, casperDevClient, rMSDbStatesClient, devMerchantClient, fITTTDbStatesClient, devConnectedAccClient, devInAppReferralClient, devWealthOnboardingClient, mutualFundDbStatesClient, recurringPaymentDevClient, enachDevClient, devP2PInvestmentClient, segmentDbStatesClient, nudgeDbStatesClient, salaryProgramDevClient, devAnalyserClient, devPreApprovedLoanClient, devFireflyClient, developerClient, developerClientVar2, developerClientVar3, devOrchestratorClient, devClientVar3, dBStateClient, tieringDevServiceClient, amlDevServiceClient, developerClientVar4, developerClientVar5, questDbStatesClient, devUpcomingTransactionsClient, healthEngineDevClient, cmsDevClient, devBankCustClient, devOmegleClient, devOmegleClientVar2, devVkycCallClient, devVkycCallClientVar2, devEmploymentClient, developerClientVar6, dBStateServiceClient, tspUserServiceClient, developerClientVar7, cxGenConf, casbinClient, devLeadClient, devCreditReportClient, accountsDbStatesClient)

	dbstatepb.RegisterDBStateServer(s, serviceVar21)

	serviceVar22 := wire3.InitializePayoutService(sherlockPGDB, accountPIRelationClient, orderServiceClient, cxGenConf, actorClient, timelineServiceClient, piClient, payoutStatusCheckPublisher, payClient, rewardsGeneratorClient)

	payoutpb.RegisterPayoutServer(s, serviceVar22)

	devActions, err := wire3.InitializeDevActionsService(ctx, sqsClient, chatsClient, kycClient, livenessClient, usersClient, savingsClient, vendorMappingServiceClient, cardProvisioningClient, rewardOffersClient, luckyDrawServiceClient, groupClient, onboardingClient, authClient, indexerClient, actionBarClient, consumerServiceClient, accountPIRelationClient, offerCatalogServiceClient, offerInventoryServiceClient, offerListingServiceClient, actorClient, ledgerReconciliationClient, emailParserClient, accessInfoClient, ruleManagerClient, sportsManagerClient, schedulerServiceClient, vKYCClient, userPreferenceClient, orderServiceClient, rMSEventPublisher, commsClient, rewardsCampaignCommClient, rewardsGeneratorClient, accountStatementClient, offerRedemptionServiceClient, devActionDelayPublisher, casbinClient, connectedAccountClient, paymentClient, wealthOnboardingClient, inAppReferralClient, serveFAQClient, accountAggregatorClient, inAppHelpMediaClient, catalogManagerClient, orderManagerClientVar5, exchangerOfferServiceClient, devSearchClient, depositClient, wealthCxServiceClient, fileGeneratorClient, recurringPaymentServiceClient, cxS3ClientVar4, cxConf, inAppTargetedCommsClient, prerequisiteHandlerClient, orderOperationsClient, insightsClient, devP2PInvestmentClient, reverseFeedManagerClient, seasonServiceClient, segmentationServiceClient, mutualFundClient, uPIClient, sherlockPGDB, consumerClient, ticketClient, reconciliationServiceClient, cxClient, cxClientVar2, preApprovedLoanClient, preApprovedLoanClientVar2, devPreApprovedLoanClient, profileEvaluatorClient, employmentClient, referralsClient, userActionsClient, riskClient, leaClient, upiOnboardingClient, nudgeServiceClient, accountsDevServiceClient, payClient, discountServiceClient, userIssueInfoServiceClient, fileGeneratorClientVar2, iFTFileProcessorEventPublisher, rewardsManualGiveawayEventPublisher, riskCasePublisher, cxGenConf, redListClient, catalogManagerClientVar4, amlClient, orderManagerClient, celestialClient, fitttClient, internationalFundTransferClient, caseManagementClient, workflowServiceClient, employmentFeClient, bankCustomerServiceClient, accountsClient, operationsClient, externalValidateClient, uNNameCheckClient, cmsServiceClient, dynamicUIElementServiceClient, cxClientVar3, commsDevActionClient, kycAgentServiceClient, simulationClient, p2PInvestmentClient, stocksClient, managerClient, operationalStatusServiceClient, cxRedisStore, eKYCClient, p2PClient, mfFolioServiceClient, salaryProgramClient, simulatorClient, riskDisputePublisher, casperItcDownloadFileQueuePublisher, accountManagerClient, whiteListClient, orderUpdateEventForTxnCategorizationPublisher, dataS3Client, epifiIconsS3Client, fireflyClient, serviceClient, journeyServiceClient, troubleshootClient, troubleshootClientVar2, alfredClient, investmentAggregatorClient, accountingClient, accountAggregatorClientVar2, aATxnCategorizationPublisher, cCTxnCategorizationPublisher, kYCClient, salaryB2BClient, lmsClient, savingsAccountClosureClient, dynamicUIElementServiceClientVar2, applicationClient, recurringPaymentClient, federalEscalationCreateEventPublisher, federalEscalationAttachmentsS3Client, epfClient, mFExternalOrdersClient, netWorthClient, consentClient, investmentAnalyticsClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	actionpb.RegisterDevActionsServer(s, devActions)

	serviceVar23 := wire3.InitializeLivenessVideoService(ctx, livenessClient, casbinClient, livenessVideoConfigS3Client, cxConf, sherlockPGDB)

	cxlvpb.RegisterLivenessVideoServer(s, serviceVar23)

	serviceVar24, err := wire3.InitializeTicketService(ctx, freshdeskClient, casbinClient, sherlockPGDB, paymentClient, orderServiceClient, updateTicketPublisher, createTicketPublisher, cxGenConf, usersClient, actorClient, chatsClient, vendorMappingServiceClient, cxConf, cxRedisStore, disputeExternalPublisher, piClient, groupClient, disputeCreateTicketPublisher, savingsClient, commsClient, disputeClient, broker, ticketClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	ticketpb.RegisterTicketServer(s, serviceVar24)

	cXDev := wire3.InitializeCxDevService(sherlockPGDB, cxRedisStore, cxGenConf)

	cxdeveloper.RegisterDevCXServer(s, cXDev)

	serviceVar25 := wire3.InitializeFederalService(freshdeskClient)

	cxfederalpb.RegisterFederalServer(s, serviceVar25)

	adminActionsService := wire3.InitializeAdminActionService(chatsClient, vendorMappingServiceClient, sherlockPGDB, usersClient, authClient, actorClient, freshdeskTicketPublisher, onboardingClient, kycClient, casbinClient, cardProvisioningClient, freshdeskClient, cxRedisStore, cxGenConf, commsClient, cxS3ClientVar5, cxConf)

	adminactionspb.RegisterAdminActonsServer(s, adminActionsService)

	serviceVar26 := wire3.InitializeReferralService(actorClient, chatsClient, vendorMappingServiceClient, inAppReferralClient, rewardsGeneratorClient, onboardingClient, savingsClient, cXClient, cxConf, cxGenConf, sherlockPGDB, usersClient)

	referrals2.RegisterReferralsServer(s, serviceVar26)

	serviceVar27 := wire3.InitializeFITDevConsoleService(developerConsoleServiceClient)

	cxdevconsolepb.RegisterCxFitDeveloperConsoleServiceServer(s, serviceVar27)

	serviceVar28 := wire3.InitializeCallRoutingService(chatsClient, vendorMappingServiceClient, onboardingClient, groupClient, salaryProgramClient, externalAccountsClient, sherlockPGDB, cxGenConf, segmentationServiceClient, broker, fireflyClient, actorClient, piClient, usersClient, paymentClient, orderServiceClient, savingsClient, depositClient, catalogManagerClient, p2PInvestmentClient, portfolioManagerClient, orderManagerClient, balanceClient, profileClient, commsClient, productClient, callRoutingEventPublisher, cxRedisStore, tieringClient, ticketClient, preApprovedLoanClientVar2, complianceClient)

	callroutingpb.RegisterCallRoutingServer(s, serviceVar28)

	serviceVar29 := wire3.InitializeRiskOpsService(onboardingClient, usersClient, kycClient, vKYCClient, locationClient, actorClient, livenessClient, connectedAccountClient, authClient, riskClient, locationClientVar2, employmentClient, emailParserClient, cxClientVar2, screenerClient, caseManagementClient, bankCustomerServiceClient, livenessVideoConfigS3ClientVar2, cxGenConf, redListClient, payClient, txnCategorizerClient, savingsClient, profileClient, broker, preApprovedLoanClientVar2, uNNameCheckClient, fennelFeatureStoreClient, scienapticClient, riskS3Client, orderServiceClient, accountPIRelationClient, actorActivityClient, ticketClient, kYCClient, leaClient, cxConf)

	riskopspb.RegisterRiskOpsServer(s, serviceVar29)

	serviceVar30 := wire3.InitializeRiskOpsWealthService(wealthCxServiceClient)

	riskopswealthpb.RegisterRiskOpsWealthServer(s, serviceVar30)

	serviceVar31 := wire3.InitializeSalaryDataOpsService(cxGenConf, cxConf, sherlockPGDB, usersClient, actorClient, employmentClient, salaryProgramClient, orderServiceClient, piClient, vKYCClient, groupClient, paymentClientVar10, cxClientVar5, txnCategorizerClient, bankCustomerServiceClient, healthInsuranceClient, salaryProgramS3Client, employerNameCategoriserClient, recurringPaymentServiceClient)

	salarydataops2.RegisterSalaryDataOpsServer(s, serviceVar31)

	serviceVar32 := wire3.InitializeConnectedAccountService(cxConf, cxGenConf, sherlockPGDB, usersClient, connectedAccountClient)

	cxconnectedaccountpb.RegisterConnectedAccountServer(s, serviceVar32)

	serviceVar33 := wire3.InitializeCallService(sherlockPGDB, freshdeskClient, ticketClient, cxConf, callRecordingS3Client)

	callpb.RegisterCallServer(s, serviceVar33)

	serviceVar34 := wire3.InitializeP2PInvestmentService(cxConf, cxGenConf, sherlockPGDB, usersClient, cxClientVar6)

	cxp2pinvpb.RegisterP2PInvestmentServer(s, serviceVar34)

	serviceVar35 := wire3.InitializePreApprovedLoanService(cxConf, cxGenConf, sherlockPGDB, usersClient, cxClientVar2, catalogManagerClient, ticketClient, freshdeskClient)

	cxpreapprovedloanpb.RegisterPreApprovedLoanServer(s, serviceVar35)

	serviceVar36 := wire3.InitializeFireflyService(cxConf, cxGenConf, sherlockPGDB, usersClient, cxClient, fireflyClient, billingClient, accountingClient, creditLimitEstimatorClient, loanManagementSystemClient, actorClient, rewardsGeneratorClient, bankCustomerServiceClient, depositClient, projectorServiceClient, txnAggregatesClient, segmentationServiceClient)

	cxccpb.RegisterFireflyServer(s, serviceVar36)

	serviceVar37 := wire3.InitializeSherlockBannersService(sherlockPGDB, cxGenConf, preApprovedLoanSherlockBannersClient, tieringClient, profileClient)

	sbpb.RegisterSherlockBannersServer(s, serviceVar37)

	serviceVar38 := wire3.InitializeSherlockFeedbackDetailsService(sherlockPGDB, cxConf)

	sbpf.RegisterSherlockFeedbackServer(s, serviceVar38)

	serviceVar39 := wire3.InitializeIssueResolutionFeedbackService(sherlockPGDB, cxGenConf, celestialClient, celestialSignalWorkflowPublisher, ticketClient)

	issueresolutionfeedbackpb.RegisterIssueResolutionFeedbackServiceServer(s, serviceVar39)

	serviceVar40 := wire3.InitializeLiveChatFallbackService(sherlockPGDB, cxGenConf, freshchatClient, vendorMappingServiceClient, usersClient, actorClient, cxRedisStore, cxConf, onboardingClient, salaryProgramClient, commsClient)

	livechatfallback2.RegisterLiveChatFallbackServer(s, serviceVar40)

	serviceVar41 := wire3.InitializeChatbotWorkflowService(cxConf, cxGenConf, serveFAQClient, chatsClient, vendorMappingServiceClient, freshdeskClient, vKYCClient, bankCustomerServiceClient, cXClient, cardProvisioningClient, fireflyClient, accountingClient, payClient, cXClientVar2, employmentClient, onboardingClient, livenessClient, rewardOffersClient, salaryProgramClient, inAppReferralClient, rewardsCreditCardTxnEventQueuePublisher, rewardsOrderUpdateEventQueuePublisher, rewardsGeneratorClient, accountPIRelationClient, upiOnboardingClient, actorClient, piClient, usersClient, paymentClient, orderServiceClient, savingsClient, depositClient, catalogManagerClient, p2PInvestmentClient, portfolioManagerClient, orderManagerClient, balanceClient, sherlockPGDB, disputeExternalPublisher, groupClient, disputeCreateTicketPublisher, commsClient, disputeClient, profileClient)

	chatbotworkflowpb.RegisterWorkflowServer(s, serviceVar41)

	serviceVar42 := wire3.InitializeUserIssueInfoService(cxRedisStore)

	userissueinfopb.RegisterUserIssueInfoServiceServer(s, serviceVar42)

	serviceVar43 := wire3.NewInternationalFundTransferService(cxConf, cxGenConf, sherlockPGDB, usersClient, fileGeneratorClientVar2, payIFTDocumentsS3Client, internationalFundTransferClient)

	cxinternationalfiletransferdatacollectorpb.RegisterInternationalFundTransferServer(s, serviceVar43)

	serviceVar44 := wire3.InitializeVKYCCallService(vkycCallClient, vkycCallClientVar2, cxConf, cxGenConf, usersClient, sherlockPGDB, uNNameCheckClient)

	cxvkyccallpb.RegisterVkycCallServer(s, serviceVar44)

	serviceVar45 := wire3.InitialiseSGKycService(kYCClient)

	cxsgkycpb.RegisterKYCServer(s, serviceVar45)

	serviceVar46 := wire3.InitializeOzonetelConsumerService(sherlockPGDB, updateTicketPublisher, usersClient, actorClient, chatsClient, vendorMappingServiceClient, cxGenConf, commsClient, cxRedisStore)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.OzonetelCallEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer.RegisterAddOrUpdateOzonetelCallDetailsMethodToSubscriber(subscriber, serviceVar46)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar47 := wire3.InitializeFreshchatConsumerService(cxConf, senseforthLiveChatFallbackClient, freshchatClient, cxRedisStore)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.FreshchatEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer2.RegisterProcessFreshchatEventMethodToSubscriber(subscriber, serviceVar47)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar48 := wire3.InitializeConsumerService(sherlockPGDB, freshdeskClient, freshdeskContactPublisher, usersClient, actorClient, chatsClient, vendorMappingServiceClient, ticketClient, cxConf, cxGenConf, disputeAddNoteTicketPublisher, bankCustomerServiceClient, onboardingClient, updateTicketPublisher, disputeExternalPublisher, paymentClient, piClient, orderServiceClient, groupClient, disputeCreateTicketPublisher, savingsClient, commsClient, disputeClient, s3Client, createTicketEventPublisher, federalEscalationServiceClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.FreshdeskTicketSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessTicketEventMethodToSubscriber(subscriber, serviceVar48)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.FreshdeskContactSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessContactEventMethodToSubscriber(subscriber, serviceVar48)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.UpdateTicketSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterUpdateTicketEventMethodToSubscriber(subscriber, serviceVar48)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.CreateTicketSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterCreateTicketEventMethodToSubscriber(subscriber, serviceVar48)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.S3EventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessS3EventMethodToSubscriber(subscriber, serviceVar48)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.FederalEscalationUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessFederalEscalationEventMethodToSubscriber(subscriber, serviceVar48)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.FederalEscalationCreationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer4.RegisterProcessFederalEscalationCreationEventMethodToSubscriber(subscriber, serviceVar48)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	watsonConsumerService, err := wire3.InitializeWatsonConsumerService(sherlockPGDB, cxConf, rateLimiterRedisStore, celestialClient, celestialSignalWorkflowPublisher, broker, watsonClientClient, mockWatsonClientServiceClient, watsonClientVar3, watsonClientVar4, payIncidentManagerClient, watsonClientVar5, watsonClientVar6, incidentManagerClient, watsonClient, watsonInfoProviderClient, manualTicketStageWiseCommsClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.WatsonIncidentReportingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		watsonconsumer2.RegisterProcessReportIncidentEventMethodToSubscriber(subscriber, watsonConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.WatsonIncidentResolutionSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		watsonconsumer2.RegisterProcessResolveIncidentEventMethodToSubscriber(subscriber, watsonConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.WatsonTicketEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		watsonconsumer2.RegisterProcessTicketEventForWatsonMethodToSubscriber(subscriber, watsonConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.WatsonCreateTicketSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		watsonconsumer2.RegisterProcessCreateTicketEventMethodToSubscriber(subscriber, watsonConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar49 := wire3.InitializeCrmIssueTrackerIntegrationConsumerService(cxGenConf, crmIssueTrackerIntegrationClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.CrmIssueTrackerIntegrationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		issuetrackerconsumer.RegisterProcessCrmIssueTrackerIntegrationEventMethodToSubscriber(subscriber, serviceVar49)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar50 := wire3.InitializeTicketConsumerService(sherlockPGDB, cxConf, chatsClient, vendorMappingServiceClient, paymentClient, orderServiceClient, updateTicketPublisher, crmIssueTrackerIntegrationPublisher, actorClient, usersClient, groupClient, cxGenConf, watsonClient, commsClient, authClient, ticketUpdateEventPublisher, ticketClient, broker)

	if func(c *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.FreshdeskTicketDataEventSubscriberFifo(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			ticketconsumerpb.RegisterProcessFreshdeskTicketEventMethodToSubscriber(subscriber, serviceVar50)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(c *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.FreshdeskTicketDataEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			ticketconsumerpb.RegisterProcessFreshdeskTicketEventMethodToSubscriber(subscriber, serviceVar50)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	serviceVar51 := wire3.InitializeTicketConsumerService(sherlockPGDB, cxConf, chatsClient, vendorMappingServiceClient, paymentClient, orderServiceClient, updateTicketPublisher, crmIssueTrackerIntegrationPublisher, actorClient, usersClient, groupClient, cxGenConf, watsonClient, commsClient, authClient, ticketUpdateEventPublisher, ticketClient, broker)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.TicketReconciliationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		ticketconsumerpb.RegisterProcessTicketReconciliationEventMethodToSubscriber(subscriber, serviceVar51)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar52 := wire3.InitializeDevActionConsumerService(sherlockPGDB, freshdeskTicketPublisher, freshdeskClient)

	if func(config *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.DevActionSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			consumer7.RegisterProcessDelayedActionMethodToSubscriber(subscriber, serviceVar52)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	disputeConsumerSvc := wire3.InitializeDisputeConsumerService(sherlockPGDB, freshdeskClient, disputeUpdateTicketPublisher, disputeCreateTicketPublisher, usersClient, disputeAddNoteTicketPublisher, actorClient, chatsClient, uPIClient, vendorMappingServiceClient, cxGenConf, disputeClient, bankCustomerServiceClient, ticketClient, disputeExternalPublisher, paymentClient, piClient, orderServiceClient, groupClient, savingsClient, commsClient, cxRedisStore)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.DisputeCreateTicketSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer9.RegisterCreateDisputeTicketMethodToSubscriber(subscriber, disputeConsumerSvc)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.DisputeUpdateTicketSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer9.RegisterUpdateDisputeTicketMethodToSubscriber(subscriber, disputeConsumerSvc)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.DisputeAddNoteTicketSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer9.RegisterAddPrivateNoteTicketMethodToSubscriber(subscriber, disputeConsumerSvc)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.DisputeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer9.RegisterProcessDisputeMethodToSubscriber(subscriber, disputeConsumerSvc)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.UpiDisputeAutoUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer9.RegisterProcessUpiDisputeAutoUpdateEventMethodToSubscriber(subscriber, disputeConsumerSvc)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar53 := wire3.InitializePayoutConsumerService(sherlockPGDB, orderServiceClient, cxGenConf, payoutStatusCheckPublisher, payClient, rewardsGeneratorClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cxGenConf.PayoutStatusCheckSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer11.RegisterCheckPayoutStatusMethodToSubscriber(subscriber, serviceVar53)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar54 := wire3.InitializeAlfredService(cxConf, cxGenConf, sherlockPGDB, usersClient, alfredClient, savingsClient, operationalStatusServiceClient)

	alfredcxpb.RegisterAlfredServer(s, serviceVar54)

	serviceVar55, err := wire3.InitializeCrmIssueTrackerIntegrationService(ctx, sherlockPGDB, cxGenConf, cxConf, ticketClient, freshdeskClient, fireflyClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	citpb.RegisterCrmIssueTrackerIntegrationServer(s, serviceVar55)

	feedbackSubscriptionClient := wire3.InitializeQuestionResponseSubscriptionClient(sherlockPGDB, broker)

	cxquestionresponsesubscriptionpb.RegisterFeedbackSubscriptionServiceServer(s, feedbackSubscriptionClient)

	mockWatsonClientService := wire3.InitializeWatsonClientService()

	mockwatsonclient.RegisterMockWatsonClientServiceServer(s, mockWatsonClientService)

	salaryB2BService := wire3.InitializeSalaryB2BService(leadManagementClient, salaryProgramNonProdS3Client, salaryProgramB2BS3Client, authClient, commsClient, cxConf)

	salaryb2bpb.RegisterSalaryB2BServer(s, salaryB2BService)

	sherlockActorActivityHandler := wire3.InitSherlockActorActivityService(cxConf, cxGenConf, sherlockPGDB, authClient, actorActivityClient, actorClient, piClient, usersClient, paymentClient, orderServiceClient, savingsClient, depositClient, catalogManagerClient, p2PInvestmentClient, portfolioManagerClient, orderManagerClient, balanceClient, fireflyClient)

	sherlockaapb.RegisterSherlockActorActivityServer(s, sherlockActorActivityHandler)

	eventsService := wire3.InitializeSprinklrEventsService(updateTicketPublisher, createTicketPublisher, sherlockPGDB, usersClient, actorClient, chatsClient, vendorMappingServiceClient)

	sprinklrpb.RegisterSprinklrServer(s, eventsService)

	serviceVar56 := wire3.InitializeTieringService(cxConf, cxGenConf, sherlockPGDB, usersClient, tieringClient)

	tieringpb.RegisterTieringServer(s, serviceVar56)

	serviceVar57 := wire3.InitializeUsStockService(cxConf, cxGenConf, sherlockPGDB, usersClient, celestialClient, accountManagerClient, orderManagerClient, portfolioManagerClient)

	usstocks2.RegisterUsStocksInvestmentServer(s, serviceVar57)

	serviceVar58 := wire3.InitializeUserRequestsService(cxConf, cxGenConf, sherlockPGDB, usersClient, savingsClient, accountStatementClient, broker, commsClient)

	userreqpb.RegisterUserRequestsServer(s, serviceVar58)

	serviceVar59 := wire3.InitializeWatsonService(sherlockPGDB, watsonIncidentReportingPublisher, watsonIncidentResolutionPublisher, watsonClientClient, mockWatsonClientServiceClient, watsonClientVar3, watsonClientVar4, payIncidentManagerClient, watsonClientVar5, watsonClientVar6, incidentManagerClient, watsonClient, cxConf, watsonTicketEventPublisher, watsonInfoProviderClient, manualTicketStageWiseCommsClient)

	watsonpb.RegisterWatsonServer(s, serviceVar59)

	watsonInfoProvider := wire3.InitializeWatsonInfoProvider(sherlockPGDB, usersClient)

	watsoninfoproviderpb.RegisterWatsonInfoProviderServer(s, watsonInfoProvider)

	manualTicketStageWiseCommsService := wire3.InitializeManualTicketStageWiseCommsService(sherlockPGDB, cxGenConf)

	stagewisecommspb.RegisterManualTicketStageWiseCommsServer(s, manualTicketStageWiseCommsService)

	issueConfig := wire3.InitializeIssueConfigService(sherlockPGDB, cxGenConf, cxS3ClientVar6, cxRedisStore)

	icpb.RegisterIssueConfigManagementServer(s, issueConfig)

	sherlockScriptsService := wire3.InitializeSherlockScriptsService(actionBarClient, cxConf)

	sherlockscriptspb.RegisterSherlockScriptsServer(s, sherlockScriptsService)

	sherlockSopService := wire3.InitializeSherlockSopService(sherlockPGDB, cxConf, updateTicketPublisher, actionBarClient, ticketClient)

	sherlocksop2.RegisterSherlockSopServiceServer(s, sherlockSopService)

	serviceVar60 := wire3.InitializeRiskChartsService(txnAggregatesClientVar2, employmentClient, authClient, caseManagementClient, savingsClient, cxGenConf, eODBalanceClient)

	sherlockriskchartpb.RegisterChartServiceServer(s, serviceVar60)

	serviceVar61 := wire3.InitializeCallIvrService(sherlockPGDB, cxGenConf, onboardingClient, usersClient, savingsClient, commsClient, cxRedisStore, tieringClient, profileClient, ticketClient, broker, actorClient, groupClient, preApprovedLoanClientVar2, complianceClient, fireflyClient, cardProvisioningClient, cardControlClient)

	callivrpb.RegisterIvrServer(s, serviceVar61)

	issueCategory := wire3.InitializeIssueCategoryService(sherlockPGDB, actorActivityClient)

	issuepb.RegisterIssueCategoryServiceServer(s, issueCategory)

	serviceVar62 := wire3.InitialiseEscalationsService(sherlockPGDB, cxGenConf)

	espb.RegisterEscalationServiceServer(s, serviceVar62)

	configNameToConfMap[cfg.ConfigName(cfg.CX_SERVICE)] = &commonexplorer.Config{StaticConf: &cxconf.Config{}, QuestIntegratedConfig: nil}
	err = cx2.AfterServiceGroupInit(serviceVar14)
	if err != nil {
		logger.Error(ctx, "failed to run AfterServicesInitHook", zap.Error(err))
		return err
	}

	return nil

}

// nolint: funlen
func setupInapphelp(ctx context.Context, s, grpcWebServer *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	inapphelpPGDB cmdtypes.InapphelpPGDB,
	solutionsClient solpb.SolutionsClient,
	depositClient depositpb.DepositClient,
	cXInHouseClient inhouse.CXInHouseClient,
	actorClient actorpb.ActorClient,
	usersClient userspb.UsersClient,
	groupClient usergrouppb.GroupClient,
	onboardingClient onboarding.OnboardingClient,
	rewardsGeneratorClient rewardspb.RewardsGeneratorClient,
	orderServiceClient orderpb.OrderServiceClient,
	rateLimiterRedisStore types2.RateLimiterRedisStore,
	chatsClient chat.ChatsClient,
	vendorMappingServiceClient vendormapping.VendorMappingServiceClient,
	fitttClient fitttpb.FitttClient,
	cardProvisioningClient cardpb.CardProvisioningClient,
	ticketClient ticketpb.TicketClient,
	connectedAccountClient capb.ConnectedAccountClient,
	netWorthClient networthpb.NetWorthClient,
	creditReportManagerClient creditreport.CreditReportManagerClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	sherlockPGDB cmdtypes.SherlockPGDB,
	feedbackSubscriptionServiceClient cxquestionresponsesubscriptionpb.FeedbackSubscriptionServiceClient,
	payClient paypb.PayClient,
	mockCustomEligibilityEvaluationServiceClient mockfeedbackengineclient.MockCustomEligibilityEvaluationServiceClient,
	vKYCFeClient vkycpb.VKYCFeClient,
	actorActivityClient aapb.ActorActivityClient,
	watsonClient watsonpb.WatsonClient,
	serviceClient irpb.ServiceClient,
	cxRedisStore cxtypes.CxRedisStore,
	actionBarClient searchpb.ActionBarClient,
	issueConfigManagementClient icpb.IssueConfigManagementClient,
	accountingClient ffaccountspb.AccountingClient,
	tieringClient betieringpb.TieringClient,
	profileClient profilepb2.ProfileClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	inapphelpConf, err := inapphelpconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INAPP_HELP_SERVICE))
		return err
	}
	_ = inapphelpConf

	inapphelpGenConf, err := dynconf.LoadConfig(inapphelpconf.Load, genconf3.NewConfig, cfg.INAPP_HELP_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INAPP_HELP_SERVICE))
		return err
	}

	_ = inapphelpGenConf

	faqDocumentEventExternalQueuePublisher, err := sqs.NewPublisherWithConfig(ctx, inapphelpGenConf.FaqDocumentEventExternalQueuePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	feedbackInfoEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, inapphelpGenConf.FeedbackInfoEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	relatedFaqConfigS3Client := s3pkg.NewClient(awsConf, inapphelpGenConf.RelatedFaqConfig().BucketName)

	serviceVar63 := wire4.InitializeRecentActivityService(inapphelpPGDB, inapphelpConf)

	recentactivity2.RegisterRecentActivityServer(s, serviceVar63)

	refreshFAQService := wire4.InitializeStoreFAQService(inapphelpPGDB, solutionsClient, faqDocumentEventExternalQueuePublisher, relatedFaqConfigS3Client, inapphelpConf)

	processor2.RegisterFAQProcessorServer(s, refreshFAQService)

	fAQServingService := wire4.InitializeFAQServingService(inapphelpPGDB, broker, depositClient, cXInHouseClient, actorClient, usersClient, groupClient, inapphelpConf, inapphelpGenConf, onboardingClient)

	inapphelpservingpb.RegisterServeFAQServer(s, fAQServingService)

	inAppHelpDev := wire4.InitializeDevInapphelpService(inapphelpPGDB, inapphelpConf)

	inapphelpdeveloper.RegisterDevInapphelpServer(s, inAppHelpDev)

	serviceVar64 := wire4.InitializeAppFeedbackService(inapphelpPGDB, rewardsGeneratorClient, orderServiceClient, rateLimiterRedisStore, usersClient, actorClient, chatsClient, vendorMappingServiceClient, onboardingClient, fitttClient, cardProvisioningClient, ticketClient, connectedAccountClient, netWorthClient, creditReportManagerClient, inapphelpGenConf, inapphelpConf)

	appfeedback2.RegisterAppFeedbackServer(s, serviceVar64)

	serviceVar65 := wire4.InitializeInAppHelpMediaService(inapphelpPGDB, segmentationServiceClient, inapphelpGenConf, onboardingClient)

	mediapb.RegisterInAppHelpMediaServer(s, serviceVar65)

	feedbackEngineService := wire4.InitializeFeedbackEngineService(sherlockPGDB, inapphelpPGDB, inapphelpGenConf, feedbackSubscriptionServiceClient, feedbackInfoEventSnsPublisher, rewardsGeneratorClient, payClient, mockCustomEligibilityEvaluationServiceClient, vKYCFeClient)

	feedbackenginepb.RegisterFeedbackEngineServer(s, feedbackEngineService)

	feedbackEngineWeb := wire4.InitializeFeedbackEngineWebService(inapphelpPGDB, inapphelpGenConf)

	feedbackenginewebpb.RegisterFrontendServer(s, feedbackEngineWeb)

	feedbackenginewebpb.RegisterFrontendServer(grpcWebServer, feedbackEngineWeb)

	mockCustomEligibilityEvaluationClient := wire4.InitializeMockCustomEligibilityEvaluationClient()

	mockfeedbackengineclient.RegisterMockCustomEligibilityEvaluationServiceServer(s, mockCustomEligibilityEvaluationClient)

	serviceVar66 := wire4.InitializeIssueReportingService(sherlockPGDB, inapphelpGenConf, usersClient, actorActivityClient, ticketClient, watsonClient, serviceClient, cxRedisStore, actionBarClient, issueConfigManagementClient, broker, inapphelpConf, accountingClient, cardProvisioningClient, tieringClient, profileClient)

	irpb.RegisterServiceServer(s, serviceVar66)

	configNameToConfMap[cfg.ConfigName(cfg.INAPP_HELP_SERVICE)] = &commonexplorer.Config{StaticConf: &inapphelpconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.CX_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
