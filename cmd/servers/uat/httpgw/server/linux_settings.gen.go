//go:build linux

// code generated by tools/servergen
package server

import (
	"fmt"

	"golang.org/x/sys/unix"
)

func init() {
	// https://github.com/golang/go/issues/64332
	// https://go.dev/doc/gc-guide#Linux_transparent_huge_pages
	// Prctl util is available only in linux OS and this file is restricted to linux OS using build tag at the top of the file
	fmt.Println("Disabling Transparent Huge Pages in linux...")
	unix.Prctl(unix.PR_SET_THP_DISABLE, 1, 0, 0, 0)
}
