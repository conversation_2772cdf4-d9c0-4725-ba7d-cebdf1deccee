// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	http "net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	gorm "gorm.io/gorm"

	celestial "github.com/epifi/be-common/api/celestial"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	awswire "github.com/epifi/be-common/pkg/aws/v2/wire"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	servergenwire3 "github.com/epifi/be-common/pkg/epifigrpc/interceptors/servergen_wire"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	temporalcl "github.com/epifi/be-common/pkg/epifitemporal/client"
	namespace "github.com/epifi/be-common/pkg/epifitemporal/namespace"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	usecase "github.com/epifi/be-common/pkg/storage/v2/usecase"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	accountsconf "github.com/epifi/gamma/accounts/config"
	genconf2 "github.com/epifi/gamma/accounts/config/genconf"
	wire "github.com/epifi/gamma/accounts/wire"
	accountstypes "github.com/epifi/gamma/accounts/wire/types"
	actorconf "github.com/epifi/gamma/actor/config"
	genconf3 "github.com/epifi/gamma/actor/config/genconf"
	wire2 "github.com/epifi/gamma/actor/wire"
	actorwiretypes "github.com/epifi/gamma/actor/wire/types"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	developer2 "github.com/epifi/gamma/api/accounts/developer"
	operstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	accountstatementpb "github.com/epifi/gamma/api/accounts/statement"
	actor "github.com/epifi/gamma/api/actor"
	actorconsumerpb "github.com/epifi/gamma/api/actor/consumer"
	developer4 "github.com/epifi/gamma/api/actor/developer"
	txnaggregatespb "github.com/epifi/gamma/api/analyser/txnaggregates"
	authpb "github.com/epifi/gamma/api/auth"
	locationpb "github.com/epifi/gamma/api/auth/location"
	authorchestratorpb "github.com/epifi/gamma/api/auth/orchestrator"
	bcpb "github.com/epifi/gamma/api/bankcust"
	billpaypb "github.com/epifi/gamma/api/billpay"
	ccpb "github.com/epifi/gamma/api/card/control"
	cardcipb "github.com/epifi/gamma/api/card/currencyinsights"
	cardcxpb "github.com/epifi/gamma/api/card/cx"
	dcmandatepb "github.com/epifi/gamma/api/card/debitcardmandate"
	developer8 "github.com/epifi/gamma/api/card/developer"
	notificationpb "github.com/epifi/gamma/api/card/notification"
	provisioning2 "github.com/epifi/gamma/api/card/provisioning"
	tokenizerproxypb "github.com/epifi/gamma/api/card/tokenizer_proxy"
	categorizer "github.com/epifi/gamma/api/categorizer"
	comms "github.com/epifi/gamma/api/comms"
	tcpb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	connectedaccountpb "github.com/epifi/gamma/api/connected_account"
	consentpb "github.com/epifi/gamma/api/consent"
	disputepb "github.com/epifi/gamma/api/cx/dispute"
	watsonpb "github.com/epifi/gamma/api/cx/watson"
	depositpb "github.com/epifi/gamma/api/deposit"
	docspb "github.com/epifi/gamma/api/docs"
	esign2 "github.com/epifi/gamma/api/docs/esign"
	ffpb "github.com/epifi/gamma/api/firefly"
	healthenginepb "github.com/epifi/gamma/api/health_engine"
	consumer13 "github.com/epifi/gamma/api/health_engine/consumer"
	hedeveloperpb "github.com/epifi/gamma/api/health_engine/developer"
	catalogmanagerpb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	kyc "github.com/epifi/gamma/api/kyc"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	mpb "github.com/epifi/gamma/api/merchant"
	developer6 "github.com/epifi/gamma/api/merchant/developer"
	nudgepb "github.com/epifi/gamma/api/nudge"
	orderpb "github.com/epifi/gamma/api/order"
	aaorderpb "github.com/epifi/gamma/api/order/aa"
	actoractivitypb "github.com/epifi/gamma/api/order/actoractivity"
	campaignpb "github.com/epifi/gamma/api/order/campaign"
	cxpb "github.com/epifi/gamma/api/order/cx"
	developer14 "github.com/epifi/gamma/api/order/developer"
	paymentpb "github.com/epifi/gamma/api/order/payment"
	disputes "github.com/epifi/gamma/api/order/payment/disputes"
	reconpb "github.com/epifi/gamma/api/order/recon"
	p2ppb "github.com/epifi/gamma/api/p2pinvestment"
	parserpb "github.com/epifi/gamma/api/parser"
	consumer6 "github.com/epifi/gamma/api/parser/consumer"
	paypb "github.com/epifi/gamma/api/pay"
	beneficiarymanagementpb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	paycxpb "github.com/epifi/gamma/api/pay/cx"
	paydeveloper "github.com/epifi/gamma/api/pay/developer"
	internationalfundtransfer "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	consumer9 "github.com/epifi/gamma/api/pay/internationalfundtransfer/consumer"
	filegenpb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	consumer8 "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator/consumer"
	forex2 "github.com/epifi/gamma/api/pay/internationalfundtransfer/forex"
	payincidentmanagerconsumer "github.com/epifi/gamma/api/pay/payincidentmanager"
	payincidentmgrconsumerpb "github.com/epifi/gamma/api/pay/payincidentmanager/consumer"
	pgconsumerpb "github.com/epifi/gamma/api/pay/paymentgateway/consumer"
	paymentrecommendationsystem2 "github.com/epifi/gamma/api/pay/paymentrecommendationsystem"
	savingsconsumerpb "github.com/epifi/gamma/api/pay/savings_account/consumer"
	velocityengine2 "github.com/epifi/gamma/api/pay/velocity_engine"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	accountpipb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	piconsumerpb "github.com/epifi/gamma/api/paymentinstrument/consumer"
	developer10 "github.com/epifi/gamma/api/paymentinstrument/developer"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	rppb "github.com/epifi/gamma/api/recurringpayment"
	recurringpaymentconsumerpb "github.com/epifi/gamma/api/recurringpayment/consumer"
	recurringpaymentcxpb "github.com/epifi/gamma/api/recurringpayment/cx"
	recurringpaymentdeveloper "github.com/epifi/gamma/api/recurringpayment/developer"
	enachpb "github.com/epifi/gamma/api/recurringpayment/enach"
	enachconsumerpb "github.com/epifi/gamma/api/recurringpayment/enach/consumer"
	enachdevpb "github.com/epifi/gamma/api/recurringpayment/enach/developer"
	paymentgateway2 "github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	sipb "github.com/epifi/gamma/api/recurringpayment/standinginstruction"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	projectorpb "github.com/epifi/gamma/api/rewards/projector"
	salaryprogrampb "github.com/epifi/gamma/api/salaryprogram"
	savingspb "github.com/epifi/gamma/api/savings"
	segmentpb "github.com/epifi/gamma/api/segment"
	tieringpb "github.com/epifi/gamma/api/tiering"
	timeline "github.com/epifi/gamma/api/timeline"
	developer12 "github.com/epifi/gamma/api/timeline/developer"
	tspuserpb "github.com/epifi/gamma/api/tspuser"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	upcomingtxnspb "github.com/epifi/gamma/api/upcomingtransactions"
	upipb "github.com/epifi/gamma/api/upi"
	consumer11 "github.com/epifi/gamma/api/upi/consumer"
	upicxpb "github.com/epifi/gamma/api/upi/cx"
	upideveloper "github.com/epifi/gamma/api/upi/developer"
	upimandatepb "github.com/epifi/gamma/api/upi/mandate"
	consumer12 "github.com/epifi/gamma/api/upi/mandate/consumer"
	upionboardingpb "github.com/epifi/gamma/api/upi/onboarding"
	upionboardingconsumerpb "github.com/epifi/gamma/api/upi/onboarding/consumer"
	simulation2 "github.com/epifi/gamma/api/upi/simulation"
	user "github.com/epifi/gamma/api/user"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	userlocationpb "github.com/epifi/gamma/api/user/location"
	onboardingpb "github.com/epifi/gamma/api/user/onboarding"
	usstocksaccountpb "github.com/epifi/gamma/api/usstocks/account"
	usstockscatalogpb "github.com/epifi/gamma/api/usstocks/catalog"
	usstocksorderpb "github.com/epifi/gamma/api/usstocks/order"
	currencyinsightsvgpb "github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	esignvgclient "github.com/epifi/gamma/api/vendorgateway/esign"
	location "github.com/epifi/gamma/api/vendorgateway/location"
	merchantresolutionpb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	namecheck "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgaccountpb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgpb2 "github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	vgenachpb "github.com/epifi/gamma/api/vendorgateway/openbanking/enach"
	vgpaymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgb2cpaymentpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	vgiftpb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	vgsavingspb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	sivg "github.com/epifi/gamma/api/vendorgateway/openbanking/standinginstruction"
	vgupipb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	vgparserpb "github.com/epifi/gamma/api/vendorgateway/parser"
	vgpgpb "github.com/epifi/gamma/api/vendorgateway/pg"
	vgshipwaypb "github.com/epifi/gamma/api/vendorgateway/shipway"
	billpayconf "github.com/epifi/gamma/billpay/config"
	genconf8 "github.com/epifi/gamma/billpay/config/genconf"
	wire14 "github.com/epifi/gamma/billpay/wire"
	cardconf "github.com/epifi/gamma/card/config"
	genconf5 "github.com/epifi/gamma/card/config/genconf"
	dao "github.com/epifi/gamma/card/dao"
	provisioning "github.com/epifi/gamma/card/provisioning"
	wire4 "github.com/epifi/gamma/card/wire"
	types2 "github.com/epifi/gamma/card/wire/types"
	hook "github.com/epifi/gamma/cmd/servers/uat/pay/hook"
	docsconf "github.com/epifi/gamma/docs/config"
	wire5 "github.com/epifi/gamma/docs/wire"
	docstypes "github.com/epifi/gamma/docs/wire/types"
	healthengineconf "github.com/epifi/gamma/health_engine/config"
	wire13 "github.com/epifi/gamma/health_engine/wire"
	types7 "github.com/epifi/gamma/health_engine/wire/types"
	merchantconf "github.com/epifi/gamma/merchant/config"
	genconf4 "github.com/epifi/gamma/merchant/config/genconf"
	wire3 "github.com/epifi/gamma/merchant/wire"
	merchanttypes "github.com/epifi/gamma/merchant/wire/types"
	orderconf "github.com/epifi/gamma/order/config"
	config2 "github.com/epifi/gamma/order/config/genconf"
	wire9 "github.com/epifi/gamma/order/wire"
	types3 "github.com/epifi/gamma/order/wire/types"
	parserconf "github.com/epifi/gamma/parser/config"
	parsergenconf "github.com/epifi/gamma/parser/config/genconf"
	wire8 "github.com/epifi/gamma/parser/wire"
	payconf "github.com/epifi/gamma/pay/config/server"
	payservergenconfig "github.com/epifi/gamma/pay/config/server/genconf"
	wire10 "github.com/epifi/gamma/pay/wire"
	types4 "github.com/epifi/gamma/pay/wire/types"
	paymentinstrumentconf "github.com/epifi/gamma/paymentinstrument/config"
	genconf6 "github.com/epifi/gamma/paymentinstrument/config/genconf"
	wire6 "github.com/epifi/gamma/paymentinstrument/wire"
	piwiretypes "github.com/epifi/gamma/paymentinstrument/wire/types"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	recurringpaymentconf "github.com/epifi/gamma/recurringpayment/config/server"
	rpservergenconf "github.com/epifi/gamma/recurringpayment/config/server/genconf"
	wire11 "github.com/epifi/gamma/recurringpayment/wire"
	types6 "github.com/epifi/gamma/recurringpayment/wire/types"
	timelineconf "github.com/epifi/gamma/timeline/config"
	genconf7 "github.com/epifi/gamma/timeline/config/genconf"
	wire7 "github.com/epifi/gamma/timeline/wire"
	timelinetypes "github.com/epifi/gamma/timeline/wire/types"
	upiconf "github.com/epifi/gamma/upi/config"
	upidyanmicconf "github.com/epifi/gamma/upi/config/genconf"
	servergenwire2 "github.com/epifi/gamma/upi/interceptor/servergen_wire"
	wire12 "github.com/epifi/gamma/upi/wire"
	upitypes "github.com/epifi/gamma/upi/wire/types"
	vgtypes "github.com/epifi/gamma/vendorgateway/wire/types"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.PAY_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.PAY_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.PAY_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.PAY_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	var dbConnTeardown func()
	crdbResourceMap, crdbTxnResourceMap, dbConnTeardown, err = storage2.NewDBResourceProviderV2(conf.DBConfigMap.GetOwnershipToDbConfigMap(), conf.Tracing.Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to get db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		dbConnTeardown()
	}()

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()
	epifiWealthCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiWealthCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiWealthCRDB"))
		return err
	}
	epifiWealthCRDBSqlDb, err := epifiWealthCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiWealthCRDB"))
		return err
	}
	defer func() { _ = epifiWealthCRDBSqlDb.Close() }()

	payPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["PayPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "PayPGDB"))
		return err
	}
	payPGDBSqlDb, err := payPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "PayPGDB"))
		return err
	}
	defer func() { _ = payPGDBSqlDb.Close() }()
	actorPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["ActorPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "ActorPGDB"))
		return err
	}
	actorPGDBSqlDb, err := actorPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "ActorPGDB"))
		return err
	}
	defer func() { _ = actorPGDBSqlDb.Close() }()
	merchantPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["MerchantPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "MerchantPGDB"))
		return err
	}
	merchantPGDBSqlDb, err := merchantPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "MerchantPGDB"))
		return err
	}
	defer func() { _ = merchantPGDBSqlDb.Close() }()
	debitCardPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["DebitCardPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "DebitCardPGDB"))
		return err
	}
	debitCardPGDBSqlDb, err := debitCardPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "DebitCardPGDB"))
		return err
	}
	defer func() { _ = debitCardPGDBSqlDb.Close() }()
	paymentInstrumentPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["PaymentInstrumentPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "PaymentInstrumentPGDB"))
		return err
	}
	paymentInstrumentPGDBSqlDb, err := paymentInstrumentPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "PaymentInstrumentPGDB"))
		return err
	}
	defer func() { _ = paymentInstrumentPGDBSqlDb.Close() }()
	timelinePGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["TimelinePGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "TimelinePGDB"))
		return err
	}
	timelinePGDBSqlDb, err := timelinePGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "TimelinePGDB"))
		return err
	}
	defer func() { _ = timelinePGDBSqlDb.Close() }()
	recurringPaymentPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["RecurringPaymentPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "RecurringPaymentPGDB"))
		return err
	}
	recurringPaymentPGDBSqlDb, err := recurringPaymentPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "RecurringPaymentPGDB"))
		return err
	}
	defer func() { _ = recurringPaymentPGDBSqlDb.Close() }()

	onboardingConn := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	locationClient := locationpb.NewLocationClient(onboardingConn)

	attributeRateLimiter := ratelimiter.NewAttributeRateLimiter(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcAttributeRatelimiterParams().Namespace())
	universalConn := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.UNIVERSAL_SERVER)
	defer epifigrpc.CloseConn(universalConn)
	managerClient := managerpb.NewManagerClient(universalConn)
	groupClient := usergrouppb.NewGroupClient(onboardingConn)
	usersClient := user.NewUsersClient(onboardingConn)
	payConn := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.PAY_SERVER)
	defer epifigrpc.CloseConn(payConn)
	actorClient := actor.NewActorClient(payConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(universalConn)
	savingsClient := savingspb.NewSavingsClient(universalConn)
	authClient := authpb.NewAuthClient(onboardingConn)
	depositClient := depositpb.NewDepositClient(universalConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	accountsClient := vgaccountpb.NewAccountsClient(vendorgatewayConn)
	bankCustomerServiceClient := bcpb.NewBankCustomerServiceClient(onboardingConn)
	savingsClientVar4 := vgsavingspb.NewSavingsClient(vendorgatewayConn)
	payRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["PayRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = payRedisStore.Close() }()
	payCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(payRedisStore), gconf.RedisClusters()["PayRedisStore"].HystrixCommand)
	healthEngineServiceClient := healthenginepb.NewHealthEngineServiceClient(payConn)
	universalConnVar9ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewSavingsRequestClientInterceptor()
	if unaryClientInterceptor != nil {
		universalConnVar9ClientInterceptors = append(universalConnVar9ClientInterceptors, unaryClientInterceptor)
	}
	universalConnVar9 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.UNIVERSAL_SERVER, universalConnVar9ClientInterceptors...)
	defer epifigrpc.CloseConn(universalConnVar9)
	commsClient := comms.NewCommsClient(universalConnVar9)
	payConnVar6ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar3 := servergenwire.NewSavingsRequestClientInterceptor()
	if unaryClientInterceptorVar3 != nil {
		payConnVar6ClientInterceptors = append(payConnVar6ClientInterceptors, unaryClientInterceptorVar3)
	}
	payConnVar6 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.PAY_SERVER, payConnVar6ClientInterceptors...)
	defer epifigrpc.CloseConn(payConnVar6)
	docsClient := docspb.NewDocsClient(payConnVar6)
	accountStatementClient := accountstatementpb.NewAccountStatementClient(payConn)
	celestialClient := celestial.NewCelestialClient(universalConn)
	operationalStatusServiceClient := operstatuspb.NewOperationalStatusServiceClient(payConn)
	piClient := pipb.NewPiClient(payConn)
	accountPIRelationClient := accountpipb.NewAccountPIRelationClient(payConn)
	merchantServiceClient := mpb.NewMerchantServiceClient(payConn)
	uPIClient := upipb.NewUPIClient(payConn)
	vendormappingActorRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["VendormappingActorRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = vendormappingActorRedisStore.Close() }()
	timelineServiceClient := timeline.NewTimelineServiceClient(payConn)
	beneficiaryManagementClient := beneficiarymanagementpb.NewBeneficiaryManagementClient(payConn)
	useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec, useCaseDbResourceProviderTeardown, err := usecase.NewDBResourceProvider(gconf.UseCaseDBConfigMap(), gconf.Tracing().Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to initialize usecase db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		useCaseDbResourceProviderTeardown()
	}()
	_ = useCaseDbResourceProvider
	_ = useCaseDbResourceProviderTxnExec
	tspUserServiceClient := tspuserpb.NewTspUserServiceClient(onboardingConn)
	accountAggregatorClient := aaorderpb.NewAccountAggregatorClient(payConn)
	payMerchantRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["PayMerchantRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = payMerchantRedisStore.Close() }()
	merchantResolutionClient := merchantresolutionpb.NewMerchantResolutionClient(vendorgatewayConn)
	serviceClient := currencyinsightsvgpb.NewServiceClient(vendorgatewayConn)
	cardsRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["CardsRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = cardsRedisStore.Close() }()
	cardProvisioningClient := vgpb2.NewCardProvisioningClient(vendorgatewayConn)
	kycClient := kyc.NewKycClient(onboardingConn)
	cardControlClient := ccpb.NewCardControlClient(payConn)
	universalConnVar14ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire.NewCardRequestClientInterceptor()
	if unaryClientInterceptorVar2 != nil {
		universalConnVar14ClientInterceptors = append(universalConnVar14ClientInterceptors, unaryClientInterceptorVar2)
	}
	universalConnVar14 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.UNIVERSAL_SERVER, universalConnVar14ClientInterceptors...)
	defer epifigrpc.CloseConn(universalConnVar14)
	commsClientVar2 := comms.NewCommsClient(universalConnVar14)
	onboardingClient := onboardingpb.NewOnboardingClient(onboardingConn)
	vendorgatewaypciConn := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDORGATEWAY_PCI_SERVER)
	defer epifigrpc.CloseConn(vendorgatewaypciConn)
	cardProvisioningClientVar2 := vgpb2.NewCardProvisioningClient(vendorgatewaypciConn)
	salaryProgramClient := salaryprogrampb.NewSalaryProgramClient(universalConn)
	payClient := paypb.NewPayClient(payConn)
	tieringClient := tieringpb.NewTieringClient(universalConn)
	orderServiceClient := orderpb.NewOrderServiceClient(payConn)
	balanceClient := accountbalancepb.NewBalanceClient(payConn)
	vKYCClient := vkycpb.NewVKYCClient(onboardingConn)
	inAppTargetedCommsClient := tcpb.NewInAppTargetedCommsClient(universalConn)
	cardProvisioningClientVar7 := provisioning2.NewCardProvisioningClient(payConn)
	shipwayClient := vgshipwaypb.NewShipwayClient(vendorgatewayConn)
	nudgeServiceClient := nudgepb.NewNudgeServiceClient(universalConn)
	locationClientVar12 := userlocationpb.NewLocationClient(onboardingConn)
	eSignClient := esignvgclient.NewESignClient(vendorgatewayConn)
	vendormappingPiRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["VendormappingPiRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = vendormappingPiRedisStore.Close() }()
	userTimelineRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserTimelineRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userTimelineRedisStore.Close() }()
	parserClient := vgparserpb.NewParserClient(vendorgatewayConn)
	disputeClient := disputepb.NewDisputeClient(universalConn)
	connectedAccountClient := connectedaccountpb.NewConnectedAccountClient(universalConn)
	universalConnVar38ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar5 := servergenwire.NewOrderRequestClientInterceptor()
	if unaryClientInterceptorVar5 != nil {
		universalConnVar38ClientInterceptors = append(universalConnVar38ClientInterceptors, unaryClientInterceptorVar5)
	}
	universalConnVar38 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.UNIVERSAL_SERVER, universalConnVar38ClientInterceptors...)
	defer epifigrpc.CloseConn(universalConnVar38)
	commsClientVar7 := comms.NewCommsClient(universalConnVar38)
	vendorgatewayConnVar14ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar8 := servergenwire2.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar8 != nil {
		vendorgatewayConnVar14ClientInterceptors = append(vendorgatewayConnVar14ClientInterceptors, unaryClientInterceptorVar8)
	}
	vendorgatewayConnVar14 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar14ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar14)
	uNNameCheckClient := namecheck.NewUNNameCheckClient(vendorgatewayConnVar14)
	upiOnboardingClient := upionboardingpb.NewUpiOnboardingClient(payConn)
	p2PInvestmentClient := p2ppb.NewP2PInvestmentClient(universalConn)
	vendorgatewayConnVar15ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar14 := servergenwire2.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar14 != nil {
		vendorgatewayConnVar15ClientInterceptors = append(vendorgatewayConnVar15ClientInterceptors, unaryClientInterceptorVar14)
	}
	vendorgatewayConnVar15 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar15ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar15)
	paymentClient := vgpaymentpb.NewPaymentClient(vendorgatewayConnVar15)
	internationalFundTransferClient := internationalfundtransfer.NewInternationalFundTransferClient(payConn)
	vendorgatewayConnVar16ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar4 := servergenwire2.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar4 != nil {
		vendorgatewayConnVar16ClientInterceptors = append(vendorgatewayConnVar16ClientInterceptors, unaryClientInterceptorVar4)
	}
	vendorgatewayConnVar16 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar16ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar16)
	merchantResolutionClientVar2 := merchantresolutionpb.NewMerchantResolutionClient(vendorgatewayConnVar16)
	txnCategorizerClient := categorizer.NewTxnCategorizerClient(universalConn)
	parserClientVar4 := parserpb.NewParserClient(payConn)
	recurringPaymentServiceClient := rppb.NewRecurringPaymentServiceClient(payConn)
	enachServiceClient := enachpb.NewEnachServiceClient(payConn)
	orderRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["OrderRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = orderRedisStore.Close() }()
	uPIClientVar3 := vgupipb.NewUPIClient(vendorgatewayConn)
	vendorgatewayConnVar19ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar15 := servergenwire2.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar15 != nil {
		vendorgatewayConnVar19ClientInterceptors = append(vendorgatewayConnVar19ClientInterceptors, unaryClientInterceptorVar15)
	}
	vendorgatewayConnVar19 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar19ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar19)
	uPIClientVar4 := vgupipb.NewUPIClient(vendorgatewayConnVar19)
	delayQueue1RedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["DelayQueue1RedisStore"], gconf.Tracing().Enable)
	defer func() { _ = delayQueue1RedisStore.Close() }()
	consumerClient := paymentpb.NewConsumerClient(payConn)
	savingsLedgerRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["SavingsLedgerRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = savingsLedgerRedisStore.Close() }()
	paymentClientVar3 := paymentpb.NewPaymentClient(payConn)
	vendorgatewayConnVar21ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar11 := servergenwire2.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar11 != nil {
		vendorgatewayConnVar21ClientInterceptors = append(vendorgatewayConnVar21ClientInterceptors, unaryClientInterceptorVar11)
	}
	vendorgatewayConnVar21 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar21ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar21)
	paymentClientVar4 := vgb2cpaymentpb.NewPaymentClient(vendorgatewayConnVar21)
	b2CPaymentLockRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["B2CPaymentLockRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = b2CPaymentLockRedisStore.Close() }()
	payIncidentManagerClient := payincidentmanagerconsumer.NewPayIncidentManagerClient(payConn)
	vendorgatewayConnVar25ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar13 := servergenwire2.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar13 != nil {
		vendorgatewayConnVar25ClientInterceptors = append(vendorgatewayConnVar25ClientInterceptors, unaryClientInterceptorVar13)
	}
	vendorgatewayConnVar25 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar25ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar25)
	parserClientVar5 := vgparserpb.NewParserClient(vendorgatewayConnVar25)
	vendorgatewayConnVar36ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar10 := servergenwire2.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar10 != nil {
		vendorgatewayConnVar36ClientInterceptors = append(vendorgatewayConnVar36ClientInterceptors, unaryClientInterceptorVar10)
	}
	vendorgatewayConnVar36 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar36ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar36)
	accountsClientVar4 := vgaccountpb.NewAccountsClient(vendorgatewayConnVar36)
	decisionEngineClient := paymentpb.NewDecisionEngineClient(payConn)
	txnAggregatesClient := txnaggregatespb.NewTxnAggregatesClient(universalConn)
	paymentGatewayClient := vgpgpb.NewPaymentGatewayClient(vendorgatewayConn)
	lendingConn := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	fireflyClient := ffpb.NewFireflyClient(lendingConn)
	rewardsAggregatesClient := rewardspinotpb.NewRewardsAggregatesClient(universalConn)
	projectorServiceClient := projectorpb.NewProjectorServiceClient(universalConn)
	fileGeneratorClient := filegenpb.NewFileGeneratorClient(payConn)
	internationalFundTransferClientVar5 := vgiftpb.NewInternationalFundTransferClient(vendorgatewayConn)
	orderManagerClient := usstocksorderpb.NewOrderManagerClient(universalConn)
	locationClientVar16 := location.NewLocationClient(vendorgatewayConn)
	forexServiceClient := forex2.NewForexServiceClient(payConn)
	docsClientVar2 := docspb.NewDocsClient(payConn)
	accountManagerClient := usstocksaccountpb.NewAccountManagerClient(universalConn)
	watsonClient := watsonpb.NewWatsonClient(universalConn)
	orchestratorClient := authorchestratorpb.NewOrchestratorClient(onboardingConn)
	standingInstructionServiceClient := sipb.NewStandingInstructionServiceClient(payConn)
	mandateServiceClient := upimandatepb.NewMandateServiceClient(payConn)
	universalConnVar93ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar6 := servergenwire.NewOrderRequestClientInterceptor()
	if unaryClientInterceptorVar6 != nil {
		universalConnVar93ClientInterceptors = append(universalConnVar93ClientInterceptors, unaryClientInterceptorVar6)
	}
	universalConnVar93 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.UNIVERSAL_SERVER, universalConnVar93ClientInterceptors...)
	defer epifigrpc.CloseConn(universalConnVar93)
	commsClientVar11 := comms.NewCommsClient(universalConnVar93)
	upiRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UpiRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = upiRedisStore.Close() }()
	vendorgatewayConnVar46ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar12 := servergenwire2.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar12 != nil {
		vendorgatewayConnVar46ClientInterceptors = append(vendorgatewayConnVar46ClientInterceptors, unaryClientInterceptorVar12)
	}
	vendorgatewayConnVar46 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar46ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar46)
	enachClient := vgenachpb.NewEnachClient(vendorgatewayConnVar46)
	upcomingTransactionsClient := upcomingtxnspb.NewUpcomingTransactionsClient(universalConn)
	catalogManagerClient := catalogmanagerpb.NewCatalogManagerClient(universalConn)
	paymentGatewayServiceClient := paymentgateway2.NewPaymentGatewayServiceClient(payConn)
	catalogManagerClientVar2 := usstockscatalogpb.NewCatalogManagerClient(universalConn)
	vendorgatewayConnVar47ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar7 := servergenwire2.NewReverseLocationClientInterceptor(locationClient)
	if unaryClientInterceptorVar7 != nil {
		vendorgatewayConnVar47ClientInterceptors = append(vendorgatewayConnVar47ClientInterceptors, unaryClientInterceptorVar7)
	}
	vendorgatewayConnVar47 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.VENDOR_GATEWAY_SERVER, vendorgatewayConnVar47ClientInterceptors...)
	defer epifigrpc.CloseConn(vendorgatewayConnVar47)
	standingInstructionClient := sivg.NewStandingInstructionClient(vendorgatewayConnVar47)
	debitCardMandateServiceClient := dcmandatepb.NewDebitCardMandateServiceClient(payConn)
	universalConnVar104ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar9 := servergenwire.NewOrderRequestClientInterceptor()
	if unaryClientInterceptorVar9 != nil {
		universalConnVar104ClientInterceptors = append(universalConnVar104ClientInterceptors, unaryClientInterceptorVar9)
	}
	universalConnVar104 := epifigrpc.NewServerConn(cfg.PAY_SERVER, cfg.UNIVERSAL_SERVER, universalConnVar104ClientInterceptors...)
	defer epifigrpc.CloseConn(universalConnVar104)
	commsClientVar12 := comms.NewCommsClient(universalConnVar104)
	uPIOnboardingRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UPIOnboardingRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = uPIOnboardingRedisStore.Close() }()
	consentClient := consentpb.NewConsentClient(onboardingConn)
	commsClientVar15 := comms.NewCommsClient(universalConn)
	healthEngineRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["HealthEngineRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = healthEngineRedisStore.Close() }()

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire.OrderRateLimiterInterceptor(gconf)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	unaryServerInterceptorVar3 := servergenwire.PayRateLimiterInterceptor(gconf)
	if unaryServerInterceptorVar3 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar3)
	}

	unaryServerInterceptorVar4 := servergenwire3.RateLimitServerInterceptorV2WithDefaultKeyGen(gconf, rateLimiter, attributeRateLimiter)
	if unaryServerInterceptorVar4 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar4)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	httpMux := http.NewServeMux()
	_ = httpMux

	err = setupAccounts(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, savingsClient, usersClient, authClient, depositClient, actorClient, epifiCRDB, groupClient, accountsClient, bankCustomerServiceClient, payPGDB, savingsClientVar4, payCacheStorage, payRedisStore, healthEngineServiceClient, commsClient, docsClient, accountStatementClient, celestialClient, operationalStatusServiceClient)
	if err != nil {
		return err
	}
	err = setupActor(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, actorPGDB, piClient, accountPIRelationClient, usersClient, merchantServiceClient, uPIClient, vendormappingActorRedisStore, timelineServiceClient, authClient, beneficiaryManagementClient, useCaseDbResourceProvider, tspUserServiceClient, bankCustomerServiceClient, accountAggregatorClient)
	if err != nil {
		return err
	}
	err = setupMerchant(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, merchantPGDB, piClient, payMerchantRedisStore, merchantResolutionClient)
	if err != nil {
		return err
	}
	err = setupCard(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, serviceClient, locationClient, cardsRedisStore, cardProvisioningClient, epifiCRDB, debitCardPGDB, authClient, savingsClient, kycClient, cardControlClient, commsClientVar2, onboardingClient, rateLimiterRedisStore, piClient, cardProvisioningClientVar2, salaryProgramClient, payClient, celestialClient, tieringClient, orderServiceClient, bankCustomerServiceClient, balanceClient, vKYCClient, inAppTargetedCommsClient, accountPIRelationClient, cardProvisioningClientVar7, shipwayClient, healthEngineServiceClient, nudgeServiceClient, locationClientVar12)
	if err != nil {
		return err
	}
	err = setupDocs(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, eSignClient)
	if err != nil {
		return err
	}
	err = setupPaymentinstrument(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, paymentInstrumentPGDB, savingsClient, vendormappingPiRedisStore, accountPIRelationClient, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec, actorClient, usersClient, groupClient, piClient, accountAggregatorClient)
	if err != nil {
		return err
	}
	err = setupTimeline(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, timelinePGDB, merchantServiceClient, actorClient, groupClient, usersClient, userTimelineRedisStore)
	if err != nil {
		return err
	}
	err = setupParser(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, parserClient)
	if err != nil {
		return err
	}
	err = setupOrder(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, epifiCRDB, epifiWealthCRDB, piClient, authClient, timelineServiceClient, depositClient, disputeClient, accountPIRelationClient, connectedAccountClient, kycClient, savingsClient, commsClientVar7, uNNameCheckClient, merchantServiceClient, upiOnboardingClient, uPIClient, p2PInvestmentClient, paymentClient, celestialClient, internationalFundTransferClient, merchantResolutionClientVar2, bankCustomerServiceClient, cardProvisioningClientVar7, txnCategorizerClient, parserClientVar4, recurringPaymentServiceClient, enachServiceClient, orderRedisStore, uPIClientVar3, payClient, uPIClientVar4, delayQueue1RedisStore, consumerClient, healthEngineServiceClient, locationClient, savingsLedgerRedisStore, orderServiceClient, paymentClientVar3, beneficiaryManagementClient, operationalStatusServiceClient, accountAggregatorClient, paymentClientVar4, b2CPaymentLockRedisStore, payIncidentManagerClient, onboardingClient, parserClientVar5, balanceClient, tieringClient, accountsClientVar4, accountStatementClient)
	if err != nil {
		return err
	}
	err = setupPay(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, payPGDB, internationalFundTransferClient, payRedisStore, celestialClient, orderServiceClient, actorClient, savingsClient, decisionEngineClient, usersClient, authClient, piClient, paymentClient, paymentClientVar3, accountPIRelationClient, upiOnboardingClient, txnAggregatesClient, timelineServiceClient, groupClient, bankCustomerServiceClient, onboardingClient, uPIClient, balanceClient, paymentGatewayClient, fireflyClient, uNNameCheckClient, recurringPaymentServiceClient, cardProvisioningClientVar7, tieringClient, rewardsAggregatesClient, projectorServiceClient, fileGeneratorClient, internationalFundTransferClientVar5, connectedAccountClient, orderManagerClient, payClient, accountAggregatorClient, locationClientVar16, forexServiceClient, docsClientVar2, accountManagerClient, watsonClient, merchantServiceClient, payIncidentManagerClient, orchestratorClient, enachServiceClient)
	if err != nil {
		return err
	}
	err = setupRecurringpayment(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, paymentGatewayClient, payClient, orderServiceClient, epifiCRDB, usersClient, celestialClient, recurringPaymentServiceClient, piClient, useCaseDbResourceProvider, tspUserServiceClient, groupClient, actorClient, enachServiceClient, standingInstructionServiceClient, savingsClient, authClient, paymentClientVar3, mandateServiceClient, accountPIRelationClient, timelineServiceClient, uPIClient, commsClientVar11, upiRedisStore, balanceClient, enachClient, operationalStatusServiceClient, upcomingTransactionsClient, merchantServiceClient, catalogManagerClient, depositClient, paymentGatewayServiceClient, catalogManagerClientVar2, standingInstructionClient, paymentClient, bankCustomerServiceClient, recurringPaymentPGDB, cardProvisioningClientVar7, debitCardMandateServiceClient)
	if err != nil {
		return err
	}
	err = setupUpi(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, uPIClientVar4, piClient, accountPIRelationClient, epifiCRDB, orderServiceClient, paymentClientVar3, actorClient, timelineServiceClient, savingsClient, authClient, commsClientVar12, uPIClient, usersClient, groupClient, onboardingClient, upiRedisStore, upiOnboardingClient, connectedAccountClient, locationClient, recurringPaymentServiceClient, nudgeServiceClient, uPIOnboardingRedisStore, celestialClient, consentClient, commsClientVar15)
	if err != nil {
		return err
	}
	err = setupHealthengine(ctx, s, httpMux, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, healthEngineRedisStore)
	if err != nil {
		return err
	}
	err = setupBillpay(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker)
	if err != nil {
		return err
	}
	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.LoadCardTxnResponseStatusCodes() // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "LoadCardTxnResponseStatusCodes"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := hook.InitPayServer(httpMux, epifiCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitPayServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	cleanupFnVar3, err := hook.InitOrderServiceGroupWorkflowConfigMap() // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitOrderServiceGroupWorkflowConfigMap"), zap.Error(err))
		return err
	}
	defer cleanupFnVar3()

	cleanupFnVar4, err := hook.InitialisePgProgramToAuthParamsMap() // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitialisePgProgramToAuthParamsMap"), zap.Error(err))
		return err
	}
	defer cleanupFnVar4()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureHttpServer(gconf.HttpServerPorts(), httpMux, string(gconf.Name()))
		return nil
	})

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupAccounts(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	savingsClient savingspb.SavingsClient,
	usersClient user.UsersClient,
	authClient authpb.AuthClient,
	depositClient depositpb.DepositClient,
	actorClient actor.ActorClient,
	epifiCRDB types.EpifiCRDB,
	groupClient usergrouppb.GroupClient,
	accountsClient vgaccountpb.AccountsClient,
	bankCustomerServiceClient bcpb.BankCustomerServiceClient,
	payPGDB types.PayPGDB,
	savingsClientVar4 vgsavingspb.SavingsClient,
	payCacheStorage accountstypes.PayCacheStorage,
	payRedisStore accountstypes.PayRedisStore,
	healthEngineServiceClient healthenginepb.HealthEngineServiceClient,
	commsClient accountstypes.AccountCommsClientWithInterceptors,
	docsClient docstypes.DocsClientWithInterceptors,
	accountStatementClient accountstatementpb.AccountStatementClient,
	celestialClient celestial.CelestialClient,
	operationalStatusServiceClient operstatuspb.OperationalStatusServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	accountsConf, err := accountsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACCOUNTS_SERVICE))
		return err
	}
	_ = accountsConf

	accountsGenConf, err := dynconf.LoadConfig(accountsconf.Load, genconf2.NewConfig, cfg.ACCOUNTS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACCOUNTS_SERVICE))
		return err
	}

	_ = accountsGenConf

	accountStmtPublisher, err := sqs.NewPublisherWithConfig(ctx, accountsGenConf.AccountStmtPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	accountMonthlyStmtPublisher, err := sqs.NewPublisherWithConfig(ctx, accountsGenConf.AccountMonthlyStmtPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	accountOperationalStatusPublisher, err := sns.NewSnsPublisherWithConfig(ctx, accountsGenConf.AccountOperationalStatusPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	balanceChangeEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, accountsGenConf.BalanceChangeEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	sftpStatementS3Client := s3pkg.NewClient(awsConf, accountsGenConf.SftpStatementAwsBucket().AwsBucket)
	sftpStatementS3ClientVar2 := s3pkg.NewClient(awsConf, accountsGenConf.SftpStatementAwsBucket().AwsBucket)

	service, err := wire.InitialiseStatementService(savingsClient, accountStmtPublisher, usersClient, authClient, depositClient, actorClient, epifiCRDB, groupClient, accountsConf, sftpStatementS3Client, accountMonthlyStmtPublisher, accountsClient, accountsGenConf, bankCustomerServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	accountstatementpb.RegisterAccountStatementServer(s, service)

	serviceVar2 := wire.InitialiseOperationalStatusService(accountsGenConf, epifiCRDB, savingsClient, accountsClient, usersClient, accountOperationalStatusPublisher, broker)

	operstatuspb.RegisterOperationalStatusServiceServer(s, serviceVar2)

	serviceVar3 := wire.InitialiseBalanceService(payPGDB, savingsClient, savingsClientVar4, authClient, actorClient, bankCustomerServiceClient, payCacheStorage, payRedisStore, accountsConf, groupClient, usersClient, balanceChangeEventPublisher, accountsGenConf, broker, healthEngineServiceClient)

	accountbalancepb.RegisterBalanceServer(s, serviceVar3)

	statementConsumerService, err := wire.InitialiseStatementConsumerService(commsClient, accountsClient, docsClient, usersClient, accountsConf, epifiCRDB, groupClient, savingsClient, accountStatementClient, sftpStatementS3ClientVar2, actorClient, celestialClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, accountsGenConf.AccountStmtSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accountstatementpb.RegisterProcessAccountStatementMethodToSubscriber(subscriber, statementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, accountsGenConf.AccountMonthlyStmtSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accountstatementpb.RegisterProcessMonthlyAccountStatementMethodToSubscriber(subscriber, statementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, accountsGenConf.SftpStatementEventSubsriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accountstatementpb.RegisterProcessAccountStatementSftpEventMethodToSubscriber(subscriber, statementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	consumerService := wire.InitialiseOperStatusConsumerService(savingsClient, accountOperationalStatusPublisher, operationalStatusServiceClient, accountsGenConf, epifiCRDB, broker)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, accountsGenConf.AccountStatusCallBackSub(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		operstatuspb.RegisterProcessFederalAccountStatusCallBackMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	accountsDevService := wire.InitialiseAccountsDevService(payPGDB, payCacheStorage, accountsConf, accountsGenConf)

	developer2.RegisterAccountsDbStatesServer(s, accountsDevService)

	configNameToConfMap[cfg.ConfigName(cfg.ACCOUNTS_SERVICE)] = &commonexplorer.Config{StaticConf: &accountsconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupActor(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	actorPGDB types.ActorPGDB,
	piClient pipb.PiClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	usersClient user.UsersClient,
	merchantServiceClient mpb.MerchantServiceClient,
	uPIClient upipb.UPIClient,
	vendormappingActorRedisStore actorwiretypes.VendormappingActorRedisStore,
	timelineServiceClient timeline.TimelineServiceClient,
	authClient authpb.AuthClient,
	beneficiaryManagementClient beneficiarymanagementpb.BeneficiaryManagementClient,
	useCaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	tspUserServiceClient tspuserpb.TspUserServiceClient,
	bankCustomerServiceClient bcpb.BankCustomerServiceClient,
	accountAggregatorClient aaorderpb.AccountAggregatorClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	actorConf, err := actorconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACTOR_SERVICE))
		return err
	}
	_ = actorConf

	actorGenConf, err := dynconf.LoadConfig(actorconf.Load, genconf3.NewConfig, cfg.ACTOR_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACTOR_SERVICE))
		return err
	}

	_ = actorGenConf

	actorPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, actorGenConf.ActorPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	piPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, actorGenConf.PiPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	actorCreationEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, actorGenConf.ActorCreationEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	serviceVar4, err := wire2.InitializeService(actorConf, actorPGDB, piClient, accountPIRelationClient, usersClient, broker, merchantServiceClient, uPIClient, vendormappingActorRedisStore, actorCreationEventPublisher, timelineServiceClient, authClient, gconf.VendorApiConf(), actorGenConf, beneficiaryManagementClient, useCaseDbResourceProvider, tspUserServiceClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	actor.RegisterActorServer(s, serviceVar4)

	actorDevEntity, err := wire2.InitializeActorDevEntityService(actorConf, actorPGDB, usersClient, vendormappingActorRedisStore, bankCustomerServiceClient, actorGenConf, useCaseDbResourceProvider)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	developer4.RegisterDevActorServer(s, actorDevEntity)

	serviceVar5, err := wire2.InitializeActorConsumerService(actorConf, actorPGDB, actorPurgePublisher, accountAggregatorClient, piPurgePublisher, vendormappingActorRedisStore, actorGenConf, useCaseDbResourceProvider)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	actorconsumerpb.RegisterConsumerServer(s, serviceVar5)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, actorGenConf.ActorPiRelationPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		actorconsumerpb.RegisterPurgeActorPiResolutionMethodToSubscriber(subscriber, serviceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, actorGenConf.ActorPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		actorconsumerpb.RegisterPurgeWealthActorsMethodToSubscriber(subscriber, serviceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.ACTOR_SERVICE)] = &commonexplorer.Config{StaticConf: &actorconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupMerchant(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	merchantPGDB types.MerchantPGDB,
	piClient pipb.PiClient,
	payMerchantRedisStore merchanttypes.PayMerchantRedisStore,
	merchantResolutionClient merchantresolutionpb.MerchantResolutionClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	merchantConf, err := merchantconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.MERCHANT_SERVICE))
		return err
	}
	_ = merchantConf

	merchantGenConf, err := dynconf.LoadConfig(merchantconf.Load, genconf4.NewConfig, cfg.MERCHANT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.MERCHANT_SERVICE))
		return err
	}

	_ = merchantGenConf

	serviceVar6, err := wire3.InitializeMerchantService(merchantPGDB, piClient, merchantConf, payMerchantRedisStore, merchantGenConf, merchantResolutionClient, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	mpb.RegisterMerchantServiceServer(s, serviceVar6)

	merchantDevEntity, err := wire3.InitializeMerchantDevService(merchantPGDB, merchantConf, payMerchantRedisStore, merchantGenConf)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	developer6.RegisterDevMerchantServer(s, merchantDevEntity)

	configNameToConfMap[cfg.ConfigName(cfg.MERCHANT_SERVICE)] = &commonexplorer.Config{StaticConf: &merchantconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupCard(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	serviceClient currencyinsightsvgpb.ServiceClient,
	locationClient locationpb.LocationClient,
	cardsRedisStore types2.CardsRedisStore,
	cardProvisioningClient vgpb2.CardProvisioningClient,
	epifiCRDB types.EpifiCRDB,
	debitCardPGDB dao.DebitCardPGDB,
	authClient authpb.AuthClient,
	savingsClient savingspb.SavingsClient,
	kycClient kyc.KycClient,
	cardControlClient ccpb.CardControlClient,
	commsClientVar2 types2.CardCommsClientWithInterceptors,
	onboardingClient onboardingpb.OnboardingClient,
	rateLimiterRedisStore types.RateLimiterRedisStore,
	piClient pipb.PiClient,
	cardProvisioningClientVar2 provisioning.CardProvisioningClientToVendorGatewayPCIServer,
	salaryProgramClient salaryprogrampb.SalaryProgramClient,
	payClient paypb.PayClient,
	celestialClient celestial.CelestialClient,
	tieringClient tieringpb.TieringClient,
	orderServiceClient orderpb.OrderServiceClient,
	bankCustomerServiceClient bcpb.BankCustomerServiceClient,
	balanceClient accountbalancepb.BalanceClient,
	vKYCClient vkycpb.VKYCClient,
	inAppTargetedCommsClient tcpb.InAppTargetedCommsClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	cardProvisioningClientVar7 provisioning2.CardProvisioningClient,
	shipwayClient vgshipwaypb.ShipwayClient,
	healthEngineServiceClient healthenginepb.HealthEngineServiceClient,
	nudgeServiceClient nudgepb.NudgeServiceClient,
	locationClientVar12 userlocationpb.LocationClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	cardConf, err := cardconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CARD_SERVICE))
		return err
	}
	_ = cardConf

	cardGenConf, err := dynconf.LoadConfigWithQuestConfig(cardconf.Load, genconf5.NewConfigWithQuest, cfg.CARD_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.CARD_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		cardGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: cardGenConf, SdkConfig: cardGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{cardGenConfAppConfig}, string(cfg.PAY_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = cardGenConf

	creationPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.CreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	pinSetEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.PinSetEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	renewCardPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.RenewCardPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	shipmentRegisterEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.ShipmentRegisterEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cardDispatchRequestPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.CardDispatchRequestPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderPhysicalCardCriticalNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.OrderPhysicalCardCriticalNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	initDispatchWithAddressUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.InitDispatchWithAddressUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	creationDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.CreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	piCreationPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.PiCreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deliveryDelayEventDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.DeliveryDelayEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deliveryDelayEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.DeliveryDelayEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deliveredEventDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.DeliveredEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	cardDispatchRequestDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.CardDispatchRequestPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deliveredEventPublisher, err := sqs.NewPublisherWithConfig(ctx, cardGenConf.DeliveredEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	debitCardUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, cardGenConf.DebitCardUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	cardCreationEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, cardGenConf.CardCreationEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	cardSwitchNotificationRewardsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, cardGenConf.CardSwitchNotificationRewardsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	dcDocS3Client := s3pkg.NewClient(awsConf, cardGenConf.Buckets().DebitCardDocs)
	dcDocS3ClientVar2 := s3pkg.NewClient(awsConf, cardGenConf.Buckets().DebitCardDocs)
	rawDataS3Client := s3pkg.NewClient(awsConf, cardGenConf.CardSwitchNotificationsRawDataStore().BucketName)

	serviceVar7 := wire4.InitializeCurrencyInsightsService(serviceClient, locationClient, cardConf, cardsRedisStore)

	cardcipb.RegisterCurrencyInsightsServer(s, serviceVar7)

	serviceVar8 := wire4.InitializeService(cardProvisioningClient, epifiCRDB, debitCardPGDB, creationPublisher, pinSetEventPublisher, actorClient, authClient, usersClient, broker, savingsClient, renewCardPublisher, kycClient, cardControlClient, cardConf, commsClientVar2, groupClient, onboardingClient, shipmentRegisterEventPublisher, rateLimiterRedisStore, piClient, cardProvisioningClientVar2, cardGenConf, cardDispatchRequestPublisher, orderPhysicalCardCriticalNotificationPublisher, initDispatchWithAddressUpdatePublisher, salaryProgramClient, payClient, celestialClient, tieringClient, orderServiceClient, bankCustomerServiceClient, debitCardUpdateEventPublisher, balanceClient, segmentationServiceClient, cardsRedisStore, vKYCClient, serviceClient, inAppTargetedCommsClient)

	provisioning2.RegisterCardProvisioningServer(s, serviceVar8)

	cardDbStatesService := wire4.InitializeDevCardService(epifiCRDB, debitCardPGDB, cardsRedisStore, cardConf)

	developer8.RegisterCardDbStatesServer(s, cardDbStatesService)

	serviceVar9 := wire4.InitializeCardControlService(epifiCRDB, debitCardPGDB, cardProvisioningClient, authClient, actorClient, usersClient, savingsClient, broker, cardConf, piClient, commsClientVar2, cardGenConf, cardProvisioningClientVar2, groupClient, bankCustomerServiceClient, cardsRedisStore)

	ccpb.RegisterCardControlServer(s, serviceVar9)

	serviceVar10 := wire4.InitializeCxService(debitCardPGDB, epifiCRDB, cardsRedisStore, cardConf)

	cardcxpb.RegisterCxServer(s, serviceVar10)

	serviceVar11 := wire4.InitializeTokenizerProxyService(cardProvisioningClientVar2)

	tokenizerproxypb.RegisterTokenizerProxyServer(s, serviceVar11)

	serviceVar12, err := wire4.InitializeDebitCardMandateService(debitCardPGDB)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	dcmandatepb.RegisterDebitCardMandateServiceServer(s, serviceVar12)

	serviceVar13 := wire4.InitializeConsumerService(cardProvisioningClient, epifiCRDB, debitCardPGDB, creationDelayPublisher, piClient, accountPIRelationClient, actorClient, piCreationPublisher, authClient, usersClient, savingsClient, cardControlClient, broker, cardProvisioningClientVar7, commsClientVar2, cardConf, renewCardPublisher, creationPublisher, shipmentRegisterEventPublisher, shipwayClient, deliveryDelayEventDelayPublisher, deliveryDelayEventPublisher, groupClient, deliveredEventDelayPublisher, cardProvisioningClientVar2, cardGenConf, cardDispatchRequestDelayPublisher, cardDispatchRequestPublisher, orderPhysicalCardCriticalNotificationPublisher, bankCustomerServiceClient, cardCreationEventPublisher, healthEngineServiceClient, celestialClient, nudgeServiceClient, dcDocS3Client, cardsRedisStore, locationClient, locationClientVar12)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		provisioning2.RegisterCardConsumerServer(s, serviceVar13)
	}

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CreationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardCreationMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.PiCreationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardPiCreationMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.PinSetEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardPinSetEventMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.RenewCardSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardRenewalEventMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardOnboardingEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardOnboardingEventMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardAuthFactorUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessAuthFactorUpdateEventMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.ShipmentRegisterEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardShipmentRegisterEventMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.DeliveryDelayEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardDeliveryDelayEventMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.DeliveredEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardDeliveredEventMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.FetchTrackingDetailsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterGetTrackingDetailsMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardDispatchRequestSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardDispatchMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.InitDispatchWithAddressUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessShippingAddressUpdateAndDispatchPhysicalCardMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardsDispatchedCsvFileSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardsDispatchedCsvFileMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.DcAmcChargesEventQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessCardAmcChargesEligibleUserFileMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.OrderPhysicalCardCriticalNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterOrderPhysicalCardCriticalNotificationMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.UserDevicePropertiesUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			provisioning2.RegisterProcessUserDevicePropertiesUpdateEventMethodToSubscriber(subscriber, serviceVar13)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}

	callbackService := wire4.InitializeCallbackConsumerService(epifiCRDB, debitCardPGDB, piCreationPublisher, broker, cardConf, actorClient, commsClientVar2, savingsClient, usersClient, deliveryDelayEventPublisher, deliveredEventPublisher, groupClient, bankCustomerServiceClient, cardCreationEventPublisher, cardGenConf, celestialClient, cardsRedisStore, orderPhysicalCardCriticalNotificationPublisher, cardControlClient, nudgeServiceClient)

	provisioning2.RegisterCallBackConsumerServer(s, callbackService)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CreationCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		provisioning2.RegisterProcessCardCreationCallBackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.TrackingCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		provisioning2.RegisterProcessCardTrackingCallbackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardDispatchRequestCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		provisioning2.RegisterProcessDispatchPhysicalCardCallbackMethodToSubscriber(subscriber, callbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar14 := wire4.InitializeNotificationConsumerService(debitCardPGDB, cardProvisioningClientVar7, cardControlClient, savingsClient, payClient, celestialClient, orderServiceClient, commsClientVar2, actorClient, cardConf, rateLimiterRedisStore, groupClient, usersClient, cardGenConf, dcDocS3ClientVar2, broker, rawDataS3Client, cardsRedisStore, cardSwitchNotificationRewardsPublisher)

	notificationpb.RegisterNotificationConsumerServiceServer(s, serviceVar14)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardSwitchFinancialNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationpb.RegisterProcessCardSwitchFinancialNotificationsMethodToSubscriber(subscriber, serviceVar14)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardSwitchNonFinancialNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationpb.RegisterProcessCardSwitchNonFinancialNotificationsMethodToSubscriber(subscriber, serviceVar14)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardForexTxnNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationpb.RegisterProcessForexTransactionsRefundMethodToSubscriber(subscriber, serviceVar14)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, cardGenConf.CardTxnNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notificationpb.RegisterProcessCardTransactionsMethodToSubscriber(subscriber, serviceVar14)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.CARD_SERVICE)] = &commonexplorer.Config{StaticConf: &cardconf.Config{}, QuestIntegratedConfig: cardGenConf}

	return nil

}

// nolint: funlen
func setupDocs(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	eSignClient esignvgclient.ESignClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	docsConf, err := docsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.DOCS_SERVICE))
		return err
	}
	_ = docsConf

	serviceVar15 := wire5.InitializeDocsService(docsConf, awsConf)

	docspb.RegisterDocsServer(s, serviceVar15)

	serviceVar16 := wire5.InitializeESignService(epifiCRDB, docsConf, awsConf, eSignClient)

	esign2.RegisterESignServer(s, serviceVar16)

	configNameToConfMap[cfg.ConfigName(cfg.DOCS_SERVICE)] = &commonexplorer.Config{StaticConf: &docsconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupPaymentinstrument(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	paymentInstrumentPGDB types.PaymentInstrumentPGDB,
	savingsClient savingspb.SavingsClient,
	vendormappingPiRedisStore piwiretypes.VendormappingPiRedisStore,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	useCaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	useCaseDbResourceProviderTxnExec *usecase.DBResourceProvider[storage2.IdempotentTxnExecutor],
	actorClient actor.ActorClient,
	usersClient user.UsersClient,
	groupClient usergrouppb.GroupClient,
	piClient pipb.PiClient,
	accountAggregatorClient aaorderpb.AccountAggregatorClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	paymentinstrumentConf, err := paymentinstrumentconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAYMENT_INSTRUMENT_SERVICE))
		return err
	}
	_ = paymentinstrumentConf

	paymentinstrumentGenConf, err := dynconf.LoadConfig(paymentinstrumentconf.Load, genconf6.NewConfig, cfg.PAYMENT_INSTRUMENT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAYMENT_INSTRUMENT_SERVICE))
		return err
	}

	_ = paymentinstrumentGenConf

	aaTxnPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, paymentinstrumentGenConf.AaTxnPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	piEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, paymentinstrumentGenConf.PiEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	serviceVar17 := wire6.InitializePiService(paymentinstrumentConf, paymentInstrumentPGDB, piEventPublisher, savingsClient, vendormappingPiRedisStore, accountPIRelationClient, paymentinstrumentGenConf, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec, actorClient, usersClient, groupClient)

	pipb.RegisterPiServer(s, serviceVar17)

	serviceVar18 := wire6.InitializeAccountPiService(paymentInstrumentPGDB, piEventPublisher, piClient, vendormappingPiRedisStore, paymentinstrumentConf, paymentinstrumentGenConf)

	accountpipb.RegisterAccountPIRelationServer(s, serviceVar18)

	pIDevService := wire6.InitializeDbStatesPiService(paymentInstrumentPGDB, vendormappingPiRedisStore, paymentinstrumentConf, paymentinstrumentGenConf, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec)

	developer10.RegisterDevPaymentIntrumentServer(s, pIDevService)

	serviceVar19 := wire6.InitializePiConsumerService(paymentInstrumentPGDB, aaTxnPurgePublisher, accountAggregatorClient, vendormappingPiRedisStore, paymentinstrumentConf, paymentinstrumentGenConf, useCaseDbResourceProvider, useCaseDbResourceProviderTxnExec)

	piconsumerpb.RegisterConsumerServer(s, serviceVar19)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, paymentinstrumentGenConf.AaAccountPiPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		piconsumerpb.RegisterPurgeAaAccountPiRelationMethodToSubscriber(subscriber, serviceVar19)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, paymentinstrumentGenConf.PiPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		piconsumerpb.RegisterPurgeWealthPisMethodToSubscriber(subscriber, serviceVar19)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.PAYMENT_INSTRUMENT_SERVICE)] = &commonexplorer.Config{StaticConf: &paymentinstrumentconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupTimeline(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	timelinePGDB types.TimelinePGDB,
	merchantServiceClient mpb.MerchantServiceClient,
	actorClient actor.ActorClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	userTimelineRedisStore timelinetypes.UserTimelineRedisStore) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	timelineConf, err := timelineconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.TIMELINE_SERVICE))
		return err
	}
	_ = timelineConf

	timelineGenConf, err := dynconf.LoadConfig(timelineconf.Load, genconf7.NewConfig, cfg.TIMELINE_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.TIMELINE_SERVICE))
		return err
	}

	_ = timelineGenConf

	timeLineEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, timelineGenConf.TimeLineEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	timelineMerchantMergeEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, timelineGenConf.TimelineMerchantMergeEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	backFillTxnTimelineEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, timelineGenConf.BackFillTxnTimelineEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	serviceVar20 := wire7.InitializeService(timelinePGDB, timeLineEventPublisher, timelineConf, timelineMerchantMergeEventPublisher, merchantServiceClient, actorClient, groupClient, usersClient, userTimelineRedisStore, backFillTxnTimelineEventPublisher, timelineGenConf)

	timeline.RegisterTimelineServiceServer(s, serviceVar20)

	timelineDevService := wire7.InitializeTimelineDevService(timelinePGDB, timelineConf, userTimelineRedisStore, timelineGenConf)

	developer12.RegisterDevTimelineServer(s, timelineDevService)

	serviceVar21 := wire7.InitializeConsumerService(timelinePGDB, timeLineEventPublisher, timelineConf, userTimelineRedisStore, timelineGenConf)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, timelineGenConf.OrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		timeline.RegisterUpdateTimelineMethodToSubscriber(subscriber, serviceVar21)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, timelineGenConf.InPaymentOrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		timeline.RegisterUpdateInPaymentOrderTimelineMethodToSubscriber(subscriber, serviceVar21)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.TIMELINE_SERVICE)] = &commonexplorer.Config{StaticConf: &timelineconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupParser(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	parserClient vgparserpb.ParserClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	parserConf, err := parserconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PARSER_SERVICE))
		return err
	}
	_ = parserConf

	parserGenConf, err := dynconf.LoadConfig(parserconf.Load, parsergenconf.NewConfig, cfg.PARSER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PARSER_SERVICE))
		return err
	}

	_ = parserGenConf

	parserClientVar2, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.Parser, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	parserClientVar3, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.Parser, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	serviceVar22, err := wire8.InitializeService(ctx, parserConf, parserClient, parserClientVar2)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	parserpb.RegisterParserServer(s, serviceVar22)

	serviceVar23 := wire8.InitializeConsumerService(parserClientVar3)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		_, err = sqs.NewSubscriberWithGenConfigV1(ctx, parserGenConf.RawTxnProcessorSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			consumer6.RegisterInitiateTxnBackfillMethodToSubscriber(subscriber, serviceVar23)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return err
		}
	}
	configNameToConfMap[cfg.ConfigName(cfg.PARSER_SERVICE)] = &commonexplorer.Config{StaticConf: &parserconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupOrder(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	epifiCRDB types.EpifiCRDB,
	epifiWealthCRDB types.EpifiWealthCRDB,
	piClient pipb.PiClient,
	authClient authpb.AuthClient,
	timelineServiceClient timeline.TimelineServiceClient,
	depositClient depositpb.DepositClient,
	disputeClient disputepb.DisputeClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	kycClient kyc.KycClient,
	savingsClient savingspb.SavingsClient,
	commsClientVar7 types3.OrderCommsClientWithInterceptors,
	uNNameCheckClient vgtypes.UNNameCheckClientWithInterceptors,
	merchantServiceClient mpb.MerchantServiceClient,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	uPIClient upipb.UPIClient,
	p2PInvestmentClient p2ppb.P2PInvestmentClient,
	paymentClient vgtypes.VgPaymentClientWithInterceptors,
	celestialClient celestial.CelestialClient,
	internationalFundTransferClient internationalfundtransfer.InternationalFundTransferClient,
	merchantResolutionClientVar2 vgtypes.MerchantResolutionClientWithInterceptors,
	bankCustomerServiceClient bcpb.BankCustomerServiceClient,
	cardProvisioningClientVar7 provisioning2.CardProvisioningClient,
	txnCategorizerClient categorizer.TxnCategorizerClient,
	parserClientVar4 parserpb.ParserClient,
	recurringPaymentServiceClient rppb.RecurringPaymentServiceClient,
	enachServiceClient enachpb.EnachServiceClient,
	orderRedisStore types.OrderRedisStore,
	uPIClientVar3 vgupipb.UPIClient,
	payClient paypb.PayClient,
	uPIClientVar4 vgtypes.VgUpiClientWithInterceptors,
	delayQueue1RedisStore types3.DelayQueue1RedisStore,
	consumerClient paymentpb.ConsumerClient,
	healthEngineServiceClient healthenginepb.HealthEngineServiceClient,
	locationClient locationpb.LocationClient,
	savingsLedgerRedisStore types3.SavingsLedgerRedisStore,
	orderServiceClient orderpb.OrderServiceClient,
	paymentClientVar3 paymentpb.PaymentClient,
	beneficiaryManagementClient beneficiarymanagementpb.BeneficiaryManagementClient,
	operationalStatusServiceClient operstatuspb.OperationalStatusServiceClient,
	accountAggregatorClient aaorderpb.AccountAggregatorClient,
	paymentClientVar4 vgtypes.VgB2CPaymentClientWithInterceptors,
	b2CPaymentLockRedisStore types3.B2CPaymentLockRedisStore,
	payIncidentManagerClient payincidentmanagerconsumer.PayIncidentManagerClient,
	onboardingClient onboardingpb.OnboardingClient,
	parserClientVar5 vgtypes.VgParserClientWithInterceptors,
	balanceClient accountbalancepb.BalanceClient,
	tieringClient tieringpb.TieringClient,
	accountsClientVar4 vgtypes.VgAccountsClientWithInterceptors,
	accountStatementClient accountstatementpb.AccountStatementClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	orderConf, err := orderconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ORDER_SERVICE))
		return err
	}
	_ = orderConf

	orderGenConf, err := dynconf.LoadConfigWithQuestConfig(orderconf.Load, config2.NewConfigWithQuest, cfg.ORDER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ORDER_SERVICE))
		return err
	}

	if !conf.QuestSdk.Disable {
		orderGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: orderGenConf, SdkConfig: orderGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{orderGenConfAppConfig}, string(cfg.PAY_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = orderGenConf

	workflowProcessingPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.WorkflowProcessingPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	workflowProcessingDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.WorkflowProcessingPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	eventsCompletedTnCPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.EventsCompletedTnCPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	txnNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.TxnNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderOrchestrationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderOrchestrationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderSearchPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderSearchPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderVpaVerificationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderVpaVerificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	intraBankEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.IntraBankEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	iMPSEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.IMPSEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	nEFTEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.NEFTEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	rTGSEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.RTGSEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.UPIEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	paymentOrchestrationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.PaymentOrchestrationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	inPaymentOrderUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.InPaymentOrderUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	savingsLedgerReconPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.SavingsLedgerReconPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	deemedTransactionUPIEnquiryPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.DeemedTransactionUPIEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	aaAccountPiPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.AaAccountPiPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	actorPiRelationPurgePublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.ActorPiRelationPurgePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	aaDataPurgeOrchestrationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.AaDataPurgeOrchestrationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderCollectNotificationPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderCollectNotificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	orderNotificationFallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, orderGenConf.OrderNotificationFallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	orderUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, orderGenConf.OrderUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	orderMerchantMergeEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, orderGenConf.OrderMerchantMergeEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	txnDetailedStatusUpdateSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, orderGenConf.TxnDetailedStatusUpdateSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	aATxnPublisher, err := sns.NewSnsPublisherWithConfig(ctx, orderGenConf.AATxnPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	serviceVar24 := wire9.InitializeService(orderGenConf, gconf.VendorApiConf(), epifiCRDB, epifiWealthCRDB, piClient, actorClient, authClient, timelineServiceClient, depositClient, disputeClient, accountPIRelationClient, connectedAccountClient, workflowProcessingPublisher, workflowProcessingDelayPublisher, orderUpdateEventPublisher, orderMerchantMergeEventPublisher, broker, kycClient, savingsClient, usersClient, groupClient, commsClientVar7, uNNameCheckClient, merchantServiceClient, upiOnboardingClient, uPIClient, eventsCompletedTnCPublisher, txnNotificationPublisher, orderOrchestrationPublisher, orderNotificationPublisher, orderSearchPublisher, p2PInvestmentClient, orderVpaVerificationPublisher, paymentClient, celestialClient, internationalFundTransferClient, merchantResolutionClientVar2, bankCustomerServiceClient, cardProvisioningClientVar7, txnCategorizerClient, parserClientVar4, recurringPaymentServiceClient, enachServiceClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, uPIClientVar3, crdbResourceMap, crdbTxnResourceMap, payClient)

	orderpb.RegisterOrderServiceServer(s, serviceVar24)

	serviceVar25 := wire9.InitializePaymentService(epifiCRDB, paymentClient, piClient, savingsClient, authClient, actorClient, usersClient, uPIClientVar4, intraBankEnquiryPublisher, iMPSEnquiryPublisher, nEFTEnquiryPublisher, rTGSEnquiryPublisher, uPIEnquiryPublisher, orderOrchestrationPublisher, paymentOrchestrationPublisher, broker, orderGenConf, commsClientVar7, delayQueue1RedisStore, consumerClient, accountPIRelationClient, uPIClient, groupClient, inPaymentOrderUpdatePublisher, merchantServiceClient, merchantResolutionClientVar2, bankCustomerServiceClient, healthEngineServiceClient, upiOnboardingClient, celestialClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, locationClient, crdbResourceMap, crdbTxnResourceMap, timelineServiceClient)

	paymentpb.RegisterPaymentServer(s, serviceVar25)

	serviceVar26 := wire9.InitializeReconService(orderGenConf, epifiCRDB, savingsLedgerReconPublisher, usersClient, healthEngineServiceClient, groupClient, actorClient, broker, savingsLedgerRedisStore, bankCustomerServiceClient, crdbResourceMap)

	reconpb.RegisterLedgerReconciliationServer(s, serviceVar26)

	serviceVar27 := wire9.InitializePaymentDecisionService(actorClient, piClient, accountPIRelationClient, uPIClient, usersClient, groupClient, authClient, orderServiceClient, paymentClientVar3, orderGenConf, payClient, upiOnboardingClient, savingsClient, healthEngineServiceClient, beneficiaryManagementClient, crdbResourceMap, operationalStatusServiceClient)

	paymentpb.RegisterDecisionEngineServer(s, serviceVar27)

	serviceVar28 := wire9.InitializeActorActivityService(orderServiceClient, actorClient, accountPIRelationClient, piClient, depositClient, orderGenConf, timelineServiceClient, accountAggregatorClient, usersClient, groupClient, connectedAccountClient, crdbResourceMap)

	actoractivitypb.RegisterActorActivityServer(s, serviceVar28)

	serviceVar29 := wire9.InitializeCxService(epifiCRDB, orderGenConf, accountPIRelationClient, recurringPaymentServiceClient, orderRedisStore, crdbResourceMap)

	cxpb.RegisterCXServer(s, serviceVar29)

	serviceVar30 := wire9.InitializeBusinessService(epifiCRDB, piClient, paymentClientVar4, savingsClient, orderGenConf, b2CPaymentLockRedisStore, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, crdbResourceMap, crdbTxnResourceMap)

	paymentpb.RegisterBusinessServer(s, serviceVar30)

	orderDevService := wire9.InitializeDevService(epifiCRDB, epifiWealthCRDB, accountPIRelationClient, savingsLedgerRedisStore, orderGenConf, orderRedisStore, crdbResourceMap, crdbTxnResourceMap)

	developer14.RegisterDevServer(s, orderDevService)

	serviceVar31 := wire9.InitializePaymentConsumerService(epifiCRDB, paymentClient, authClient, orderOrchestrationPublisher, uPIClientVar4, broker, orderGenConf, piClient, orderServiceClient, uPIClient, actorClient, usersClient, groupClient, deemedTransactionUPIEnquiryPublisher, healthEngineServiceClient, accountPIRelationClient, merchantServiceClient, savingsClient, merchantResolutionClientVar2, orderUpdateEventPublisher, payIncidentManagerClient, upiOnboardingClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, crdbResourceMap, crdbTxnResourceMap)

	paymentpb.RegisterConsumerServer(s, serviceVar31)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.UPIEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar31)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.IMPSEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar31)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.PaymentOrchestrationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar31)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.IntraBankEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar31)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.NEFTEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar31)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.RTGSEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessPaymentMethodToSubscriber(subscriber, serviceVar31)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.DeemedTransactionUpiEnquirySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessDeemedPaymentsMethodToSubscriber(subscriber, serviceVar31)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar32 := wire9.InitializeCampaignService(orderGenConf, epifiCRDB, onboardingClient, commsClientVar7, orderRedisStore, crdbResourceMap)

	campaignpb.RegisterCampaignServer(s, serviceVar32)

	serviceVar33 := wire9.InitializeAccountAggregatorService(orderGenConf, epifiWealthCRDB, connectedAccountClient, accountPIRelationClient)

	aaorderpb.RegisterAccountAggregatorServer(s, serviceVar33)

	serviceVar34 := wire9.InitializeAAConsumerService(orderGenConf, epifiCRDB, epifiWealthCRDB, aATxnPublisher, authClient, uPIClient, piClient, merchantServiceClient, accountPIRelationClient, actorClient, connectedAccountClient, timelineServiceClient, parserClientVar5, aaAccountPiPurgePublisher, actorPiRelationPurgePublisher, aaDataPurgeOrchestrationPublisher, delayQueue1RedisStore, paymentClient, usersClient, groupClient, savingsClient, merchantResolutionClientVar2, upiOnboardingClient, locationClient, uPIClientVar3, gconf.VendorApiConf())

	aaorderpb.RegisterConsumerServer(s, serviceVar34)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.AATxnSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aaorderpb.RegisterProcessAATxnMethodToSubscriber(subscriber, serviceVar34)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.PurgeAaDataSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aaorderpb.RegisterProcessPurgeAaDataEventMethodToSubscriber(subscriber, serviceVar34)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.AaTxnPurgeSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aaorderpb.RegisterPurgeAaTransactionMethodToSubscriber(subscriber, serviceVar34)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.AaDataPurgeOrchestrationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aaorderpb.RegisterOrchestrateDataPurgingMethodToSubscriber(subscriber, serviceVar34)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.AAFirstDataPullTxnSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		aaorderpb.RegisterProcessAATxnMethodToSubscriber(subscriber, serviceVar34)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar35 := wire9.InitializeNotificationConsumer(orderGenConf, epifiCRDB, commsClientVar7, actorClient, piClient, timelineServiceClient, depositClient, savingsClient, accountPIRelationClient, usersClient, groupClient, orderCollectNotificationPublisher, orderNotificationFallbackPublisher, recurringPaymentServiceClient, balanceClient, uPIClient, authClient, upiOnboardingClient, orderRedisStore, cardProvisioningClientVar7, tieringClient, locationClient, crdbResourceMap, crdbTxnResourceMap, uPIClientVar3)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessOrderNotificationMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderCollectNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessCollectOrderNotificationMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderNotificationFallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessOrderNotificationFallbackMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar36, err := wire9.InitializeConsumerService(ctx, epifiCRDB, paymentClientVar3, actorClient, timelineServiceClient, savingsClient, accountPIRelationClient, piClient, depositClient, eventsCompletedTnCPublisher, uPIClient, authClient, broker, workflowProcessingPublisher, workflowProcessingDelayPublisher, orderOrchestrationPublisher, orderUpdateEventPublisher, orderNotificationPublisher, orderCollectNotificationPublisher, orderNotificationFallbackPublisher, orderSearchPublisher, merchantServiceClient, p2PInvestmentClient, orderGenConf, paymentClient, usersClient, groupClient, orderVpaVerificationPublisher, txnNotificationPublisher, awsConf, celestialClient, internationalFundTransferClient, merchantResolutionClientVar2, balanceClient, parserClientVar4, recurringPaymentServiceClient, enachServiceClient, upiOnboardingClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, payClient, crdbResourceMap, crdbTxnResourceMap, uPIClientVar3, gconf.VendorApiConf())
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderOrchestrationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessOrderMethodToSubscriber(subscriber, serviceVar36)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.InboundTxnSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessInboundTxnOrderMethodToSubscriber(subscriber, serviceVar36)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.OrderWorkflowProcessingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessWorkflowMethodToSubscriber(subscriber, serviceVar36)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.DeclineCardTransactionsProcessingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessDeclineCardTransactionsDataDumpMethodToSubscriber(subscriber, serviceVar36)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.InboundUpiTxnSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessUpiInboundTxnOrderMethodToSubscriber(subscriber, serviceVar36)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.TxnNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		orderpb.RegisterProcessTxnNotificationMethodToSubscriber(subscriber, serviceVar36)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar37 := wire9.InitializeDisputedTxnConsumerService(epifiCRDB, crdbResourceMap)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.DisputeEventProcessingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		disputes.RegisterProcessDisputeMethodToSubscriber(subscriber, serviceVar37)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	callbackServiceVar2 := wire9.InitializeCallbackConsumer(epifiCRDB, orderOrchestrationPublisher, piClient, broker, orderGenConf, celestialClient, txnDetailedStatusUpdateSnsPublisher, healthEngineServiceClient, accountPIRelationClient, merchantServiceClient, paymentClient, groupClient, usersClient, actorClient, merchantResolutionClientVar2, savingsClient, uPIClient, authClient, upiOnboardingClient, orderRedisStore, crdbResourceMap, crdbTxnResourceMap, uPIClientVar3)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.PaymentCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		paymentpb.RegisterProcessCallBackMethodToSubscriber(subscriber, callbackServiceVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar38 := wire9.InitializeReconConsumerService(epifiCRDB, accountsClientVar4, actorClient, authClient, timelineServiceClient, accountPIRelationClient, piClient, depositClient, uPIClient, broker, orderOrchestrationPublisher, orderUpdateEventPublisher, orderNotificationPublisher, orderSearchPublisher, eventsCompletedTnCPublisher, merchantServiceClient, p2PInvestmentClient, orderGenConf, savingsLedgerRedisStore, paymentClient, usersClient, groupClient, orderVpaVerificationPublisher, txnNotificationPublisher, celestialClient, internationalFundTransferClient, merchantResolutionClientVar2, accountStatementClient, savingsClient, parserClientVar4, recurringPaymentServiceClient, enachServiceClient, upiOnboardingClient, txnDetailedStatusUpdateSnsPublisher, orderRedisStore, crdbResourceMap, crdbTxnResourceMap, payClient, uPIClientVar3, gconf.VendorApiConf())

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, orderGenConf.SavingsLedgerReconSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		reconpb.RegisterReconcileAccountMethodToSubscriber(subscriber, serviceVar38)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.ORDER_SERVICE)] = &commonexplorer.Config{StaticConf: &orderconf.Config{}, QuestIntegratedConfig: orderGenConf}

	return nil

}

// nolint: funlen
func setupPay(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	payPGDB types.PayPGDB,
	internationalFundTransferClient internationalfundtransfer.InternationalFundTransferClient,
	payRedisStore types4.PayRedisStore,
	celestialClient celestial.CelestialClient,
	orderServiceClient orderpb.OrderServiceClient,
	actorClient actor.ActorClient,
	savingsClient savingspb.SavingsClient,
	decisionEngineClient paymentpb.DecisionEngineClient,
	usersClient user.UsersClient,
	authClient authpb.AuthClient,
	piClient pipb.PiClient,
	paymentClient vgtypes.VgPaymentClientWithInterceptors,
	paymentClientVar3 paymentpb.PaymentClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	txnAggregatesClient txnaggregatespb.TxnAggregatesClient,
	timelineServiceClient timeline.TimelineServiceClient,
	groupClient usergrouppb.GroupClient,
	bankCustomerServiceClient bcpb.BankCustomerServiceClient,
	onboardingClient onboardingpb.OnboardingClient,
	uPIClient upipb.UPIClient,
	balanceClient accountbalancepb.BalanceClient,
	paymentGatewayClient vgpgpb.PaymentGatewayClient,
	fireflyClient ffpb.FireflyClient,
	uNNameCheckClient vgtypes.UNNameCheckClientWithInterceptors,
	recurringPaymentServiceClient rppb.RecurringPaymentServiceClient,
	cardProvisioningClientVar7 provisioning2.CardProvisioningClient,
	tieringClient tieringpb.TieringClient,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	projectorServiceClient projectorpb.ProjectorServiceClient,
	fileGeneratorClient filegenpb.FileGeneratorClient,
	internationalFundTransferClientVar5 vgiftpb.InternationalFundTransferClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	orderManagerClient usstocksorderpb.OrderManagerClient,
	payClient paypb.PayClient,
	accountAggregatorClient aaorderpb.AccountAggregatorClient,
	locationClientVar16 location.LocationClient,
	forexServiceClient forex2.ForexServiceClient,
	docsClientVar2 docspb.DocsClient,
	accountManagerClient usstocksaccountpb.AccountManagerClient,
	watsonClient watsonpb.WatsonClient,
	merchantServiceClient mpb.MerchantServiceClient,
	payIncidentManagerClient payincidentmanagerconsumer.PayIncidentManagerClient,
	orchestratorClient authorchestratorpb.OrchestratorClient,
	enachServiceClient enachpb.EnachServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	payConf, err := payconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAY_SERVICE))
		return err
	}
	_ = payConf

	payGenConf, err := dynconf.LoadConfig(payconf.Load, payservergenconfig.NewConfig, cfg.PAY_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAY_SERVICE))
		return err
	}

	_ = payGenConf

	inPaymentOrderUpdatePublisherVar2, err := sqs.NewPublisherWithConfig(ctx, payGenConf.InPaymentOrderUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	generateSofLimitStrategiesValuesPublisher, err := sqs.NewPublisherWithConfig(ctx, payGenConf.GenerateSofLimitStrategiesValuesPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	orderUpdateEventPublisherVar2, err := sns.NewSnsPublisherWithConfig(ctx, payGenConf.OrderUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	iFTRemittanceFileProcessingEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, payGenConf.IFTRemittanceFileProcessingEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	txnBackfillS3Client := s3pkg.NewClient(awsConf, payGenConf.TxnBackfillBucketName())

	payClientVar7, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.Pay, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	payClientVar8, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.Pay, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	payClientVar13, err := temporalcl.NewWorkflowClient(getEnvNamespaceClient(namespace.Pay, env), true, gconf.Secrets().Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Error(ctx, "failed to celestial client", zap.Error(err))
		return err
	}

	payDevService := wire10.InitializeDevService(epifiCRDB, payPGDB, internationalFundTransferClient, payConf, payGenConf, payRedisStore, crdbResourceMap, crdbTxnResourceMap)

	paydeveloper.RegisterDevServer(s, payDevService)

	serviceVar39 := wire10.InitialisePayService(epifiCRDB, celestialClient, orderServiceClient, actorClient, payConf, savingsClient, decisionEngineClient, usersClient, authClient, piClient, paymentClient, paymentClientVar3, broker, accountPIRelationClient, payGenConf, upiOnboardingClient, txnAggregatesClient, timelineServiceClient, groupClient, inPaymentOrderUpdatePublisherVar2, bankCustomerServiceClient, onboardingClient, txnBackfillS3Client, uPIClient, balanceClient, paymentGatewayClient, fireflyClient, uNNameCheckClient, payClientVar7, payRedisStore, crdbTxnResourceMap, recurringPaymentServiceClient, crdbResourceMap, cardProvisioningClientVar7, orderUpdateEventPublisherVar2, tieringClient, rewardsAggregatesClient, projectorServiceClient)

	paypb.RegisterPayServer(s, serviceVar39)

	serviceVar40 := wire10.InitializeForexService(crdbTxnResourceMap, epifiCRDB, celestialClient, payGenConf)

	forex2.RegisterForexServiceServer(s, serviceVar40)

	serviceVar41 := wire10.InitializeInternationalFundTransferService(epifiCRDB, payGenConf, celestialClient, orderServiceClient, accountPIRelationClient, piClient, usersClient, savingsClient, actorClient, fileGeneratorClient, awsConf, timelineServiceClient, payClientVar8, internationalFundTransferClientVar5, groupClient, bankCustomerServiceClient, txnAggregatesClient, onboardingClient, connectedAccountClient, orderManagerClient, payClient, payRedisStore, balanceClient, accountAggregatorClient, locationClientVar16, generateSofLimitStrategiesValuesPublisher, authClient, crdbResourceMap, crdbTxnResourceMap, payConf, forexServiceClient)

	internationalfundtransfer.RegisterInternationalFundTransferServer(s, serviceVar41)

	serviceVar42 := wire10.InitializeInternationalFundTransferFileGeneratorService(epifiCRDB, payConf, payGenConf, awsConf, docsClientVar2, internationalFundTransferClient, celestialClient, iFTRemittanceFileProcessingEventPublisher, savingsClient, actorClient, orderManagerClient, accountManagerClient, balanceClient, payRedisStore, crdbResourceMap, crdbTxnResourceMap)

	filegenpb.RegisterFileGeneratorServer(s, serviceVar42)

	serviceVar43 := wire10.InitializePayIncidentManagerService(payConf, watsonClient, payClient, orderServiceClient, accountPIRelationClient, actorClient, usersClient, groupClient, uPIClient, upiOnboardingClient, merchantServiceClient)

	payincidentmanagerconsumer.RegisterPayIncidentManagerServer(s, serviceVar43)

	serviceVar44 := wire10.InitializeCxService(epifiCRDB, payGenConf, payConf, payRedisStore, crdbResourceMap, crdbTxnResourceMap)

	paycxpb.RegisterCXServer(s, serviceVar44)

	serviceVar45 := wire10.InitializeVelocityEngineService(payClient, payConf)

	velocityengine2.RegisterVelocityEngineServer(s, serviceVar45)

	serviceVar46 := wire10.InitializeFileConsumerService(epifiCRDB, awsConf, internationalFundTransferClient, payGenConf, iFTRemittanceFileProcessingEventPublisher, celestialClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.IFTProcessFileSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer8.RegisterProcessFileMethodToSubscriber(subscriber, serviceVar46)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	consumer := wire10.InitializeSavingsAccountConsumer(savingsClient, balanceClient)

	savingsconsumerpb.RegisterConsumerServer(s, consumer)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.OrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		savingsconsumerpb.RegisterProcessOrderUpdateEventMethodToSubscriber(subscriber, consumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar47 := wire10.InitializePayIncidentManagerConsumerService(payConf, payIncidentManagerClient, accountPIRelationClient, actorClient, merchantServiceClient, payGenConf, piClient, crdbResourceMap, orderServiceClient, watsonClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.PayIncidentMgrOrderUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		payincidentmanagerconsumer.RegisterProcessOrderUpdateMethodToSubscriber(subscriber, serviceVar47)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.TransactionDetailedStatusUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		payincidentmgrconsumerpb.RegisterProcessTransactionDetailedStatusUpdateMethodToSubscriber(subscriber, serviceVar47)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar48 := wire10.InitializeBeneficiaryManagementService(payPGDB, orchestratorClient)

	beneficiarymanagementpb.RegisterBeneficiaryManagementServer(s, serviceVar48)

	consumerServiceVar2 := wire10.InitialiseIFTConsumer(epifiCRDB, connectedAccountClient, balanceClient, payClient, accountAggregatorClient, savingsClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.GenerateSofLimitStrategiesValuesSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer9.RegisterGenerateSofLimitStrategiesValuesMethodToSubscriber(subscriber, consumerServiceVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	consumerVar2 := wire10.InitialisePaymentGatewayConsumer(payGenConf, epifiCRDB, payClientVar13, celestialClient, recurringPaymentServiceClient, enachServiceClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, payGenConf.ProcessPaymentGatewayWebhookEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		pgconsumerpb.RegisterProcessPaymentGatewayWebhookEventMethodToSubscriber(subscriber, consumerVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar49 := wire10.InitializePaymentRecommendationSystemService(actorClient, payGenConf, paymentClientVar3)

	paymentrecommendationsystem2.RegisterPaymentRecommendationSystemServer(s, serviceVar49)

	configNameToConfMap[cfg.ConfigName(cfg.PAY_SERVICE)] = &commonexplorer.Config{StaticConf: &payconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupRecurringpayment(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	paymentGatewayClient vgpgpb.PaymentGatewayClient,
	payClient paypb.PayClient,
	orderServiceClient orderpb.OrderServiceClient,
	epifiCRDB types.EpifiCRDB,
	usersClient user.UsersClient,
	celestialClient celestial.CelestialClient,
	recurringPaymentServiceClient rppb.RecurringPaymentServiceClient,
	piClient pipb.PiClient,
	useCaseDbResourceProvider *usecase.DBResourceProvider[*gorm.DB],
	tspUserServiceClient tspuserpb.TspUserServiceClient,
	groupClient usergrouppb.GroupClient,
	actorClient actor.ActorClient,
	enachServiceClient enachpb.EnachServiceClient,
	standingInstructionServiceClient sipb.StandingInstructionServiceClient,
	savingsClient savingspb.SavingsClient,
	authClient authpb.AuthClient,
	paymentClientVar3 paymentpb.PaymentClient,
	mandateServiceClient upimandatepb.MandateServiceClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	timelineServiceClient timeline.TimelineServiceClient,
	uPIClient upipb.UPIClient,
	commsClientVar11 types6.RecPaymentCommsClientWithInterceptors,
	upiRedisStore upitypes.UpiRedisStore,
	balanceClient accountbalancepb.BalanceClient,
	enachClient vgtypes.VgEnachClientWithInterceptors,
	operationalStatusServiceClient operstatuspb.OperationalStatusServiceClient,
	upcomingTransactionsClient upcomingtxnspb.UpcomingTransactionsClient,
	merchantServiceClient mpb.MerchantServiceClient,
	catalogManagerClient catalogmanagerpb.CatalogManagerClient,
	depositClient depositpb.DepositClient,
	paymentGatewayServiceClient paymentgateway2.PaymentGatewayServiceClient,
	catalogManagerClientVar2 usstockscatalogpb.CatalogManagerClient,
	standingInstructionClient vgtypes.SIVGClientWithInterceptors,
	paymentClient vgtypes.VgPaymentClientWithInterceptors,
	bankCustomerServiceClient bcpb.BankCustomerServiceClient,
	recurringPaymentPGDB types.RecurringPaymentPGDB,
	cardProvisioningClientVar7 provisioning2.CardProvisioningClient,
	debitCardMandateServiceClient dcmandatepb.DebitCardMandateServiceClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	recurringpaymentConf, err := recurringpaymentconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RECURRING_PAYMENT_SERVICE))
		return err
	}
	_ = recurringpaymentConf

	recurringpaymentGenConf, err := dynconf.LoadConfig(recurringpaymentconf.Load, rpservergenconf.NewConfig, cfg.RECURRING_PAYMENT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RECURRING_PAYMENT_SERVICE))
		return err
	}

	_ = recurringpaymentGenConf

	inPaymentOrderUpdatePublisherVar3, err := sqs.NewPublisherWithConfig(ctx, recurringpaymentGenConf.InPaymentOrderUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	fetchAndCreateOffAppRecurringPaymentPublisher, err := sqs.NewPublisherWithConfig(ctx, recurringpaymentGenConf.FetchAndCreateOffAppRecurringPaymentPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	fetchAndCreateFailedEnachTransactionPublisher, err := sqs.NewPublisherWithConfig(ctx, recurringpaymentGenConf.FetchAndCreateFailedEnachTransactionPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	serviceVar50 := wire11.InitialisePaymentGatewayService(recurringpaymentConf, paymentGatewayClient, payClient, orderServiceClient, epifiCRDB, usersClient, celestialClient, recurringPaymentServiceClient, piClient, crdbResourceMap, useCaseDbResourceProvider, tspUserServiceClient, groupClient, actorClient, enachServiceClient, recurringpaymentGenConf)

	paymentgateway2.RegisterPaymentGatewayServiceServer(s, serviceVar50)

	serviceVar51 := wire11.InitialiseRecurringPaymentService(epifiCRDB, standingInstructionServiceClient, orderServiceClient, savingsClient, actorClient, authClient, usersClient, paymentClientVar3, recurringpaymentConf, mandateServiceClient, accountPIRelationClient, piClient, timelineServiceClient, uPIClient, commsClientVar11, recurringpaymentGenConf, celestialClient, upiRedisStore, inPaymentOrderUpdatePublisherVar3, groupClient, enachServiceClient, balanceClient, enachClient, fetchAndCreateOffAppRecurringPaymentPublisher, operationalStatusServiceClient, upcomingTransactionsClient, merchantServiceClient, catalogManagerClient, depositClient, paymentGatewayServiceClient, crdbResourceMap, crdbTxnResourceMap, useCaseDbResourceProvider, tspUserServiceClient, payClient, broker, catalogManagerClientVar2)

	rppb.RegisterRecurringPaymentServiceServer(s, serviceVar51)

	serviceVar52 := wire11.InitialiseCxService(epifiCRDB, orderServiceClient, recurringpaymentConf, crdbResourceMap, recurringpaymentGenConf)

	recurringpaymentcxpb.RegisterCXServer(s, serviceVar52)

	serviceVar53 := wire11.InitialiseStandingInstructionService(epifiCRDB, standingInstructionClient, piClient, authClient, savingsClient, usersClient, actorClient, paymentClient, bankCustomerServiceClient, crdbResourceMap, recurringpaymentConf, recurringpaymentGenConf)

	sipb.RegisterStandingInstructionServiceServer(s, serviceVar53)

	recurringPaymentDevService := wire11.InitialiseDevService(epifiCRDB, crdbResourceMap, useCaseDbResourceProvider, recurringpaymentConf, recurringpaymentGenConf)

	recurringpaymentdeveloper.RegisterRecurringPaymentDevServer(s, recurringPaymentDevService)

	serviceVar54, err := wire11.InitialiseEnachService(recurringPaymentPGDB, useCaseDbResourceProvider, recurringpaymentConf, recurringpaymentGenConf, recurringPaymentServiceClient, actorClient, piClient, usersClient, celestialClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return err
	}

	enachpb.RegisterEnachServiceServer(s, serviceVar54)

	enachDevService := wire11.InitializeEnachDevService(recurringpaymentGenConf, recurringPaymentPGDB, useCaseDbResourceProvider)

	enachdevpb.RegisterEnachDevServer(s, enachDevService)

	consumerServiceVar3 := wire11.InitialiseRecurringPaymentConsumerService(recurringPaymentServiceClient, savingsClient, enachClient, piClient, enachServiceClient, usersClient, groupClient, epifiCRDB, actorClient, merchantServiceClient, crdbResourceMap, recurringpaymentConf, recurringpaymentGenConf, broker, cardProvisioningClientVar7, debitCardMandateServiceClient, timelineServiceClient, orderServiceClient, gconf.VendorApiConf(), fetchAndCreateFailedEnachTransactionPublisher)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.RecurringPaymentCreationAuthVendorCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		recurringpaymentconsumerpb.RegisterProcessRecurringPaymentCreationAuthorizationVendorCallbackMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.FetchAndCreateOffAppRecurringPaymentSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		recurringpaymentconsumerpb.RegisterFetchAndCreateOffAppRecurringPaymentsMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.OffAppRecurringPaymentExecutionSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		recurringpaymentconsumerpb.RegisterProcessOffAppRecurringPaymentExecutionMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.FetchAndCreateFailedEnachTransactionSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		recurringpaymentconsumerpb.RegisterFetchAndCreateFailedEnachTransactionsMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	consumerServiceVar4 := wire11.InitializeEnachConsumerService(celestialClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, recurringpaymentGenConf.EnachFundTransferOrderUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		enachconsumerpb.RegisterProcessOrderEventForExecutionStatusUpdateMethodToSubscriber(subscriber, consumerServiceVar4)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.RECURRING_PAYMENT_SERVICE)] = &commonexplorer.Config{StaticConf: &recurringpaymentconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupUpi(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	uPIClientVar4 vgtypes.VgUpiClientWithInterceptors,
	piClient pipb.PiClient,
	accountPIRelationClient accountpipb.AccountPIRelationClient,
	epifiCRDB types.EpifiCRDB,
	orderServiceClient orderpb.OrderServiceClient,
	paymentClientVar3 paymentpb.PaymentClient,
	actorClient actor.ActorClient,
	timelineServiceClient timeline.TimelineServiceClient,
	savingsClient savingspb.SavingsClient,
	authClient authpb.AuthClient,
	commsClientVar12 upitypes.UpiCommsClientWithInterceptors,
	uPIClient upipb.UPIClient,
	usersClient user.UsersClient,
	groupClient usergrouppb.GroupClient,
	onboardingClient onboardingpb.OnboardingClient,
	upiRedisStore upitypes.UpiRedisStore,
	upiOnboardingClient upionboardingpb.UpiOnboardingClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	locationClient locationpb.LocationClient,
	recurringPaymentServiceClient rppb.RecurringPaymentServiceClient,
	nudgeServiceClient nudgepb.NudgeServiceClient,
	uPIOnboardingRedisStore upitypes.UPIOnboardingRedisStore,
	celestialClient celestial.CelestialClient,
	consentClient consentpb.ConsentClient,
	commsClientVar15 comms.CommsClient) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	upiConf, err := upiconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.UPI_SERVICE))
		return err
	}
	_ = upiConf

	upiGenConf, err := dynconf.LoadConfig(upiconf.Load, upidyanmicconf.NewConfig, cfg.UPI_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.UPI_SERVICE))
		return err
	}

	_ = upiGenConf

	vPACreationPublisher, err := sqs.NewPublisherWithConfig(ctx, upiGenConf.VPACreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	vPAVerificationPublisher, err := sqs.NewPublisherWithConfig(ctx, upiGenConf.VPAVerificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	upiComplaintStatusAutoUpdateEventPublisher, err := sqs.NewPublisherWithConfig(ctx, upiGenConf.UpiComplaintStatusAutoUpdateEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}
	uPIEnquiryPublisherVar2, err := sqs.NewPublisherWithConfig(ctx, upiGenConf.UPIEnquiryPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	upiEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, upiGenConf.UpiEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}
	pinAttemptsExceededEventSnsPublisher, err := sns.NewSnsPublisherWithConfig(ctx, upiGenConf.PinAttemptsExceededEventSnsPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return err
	}

	serviceVar55 := wire12.InitializeService(uPIClientVar4, piClient, accountPIRelationClient, epifiCRDB, upiConf, orderServiceClient, paymentClientVar3, actorClient, timelineServiceClient, broker, savingsClient, vPACreationPublisher, authClient, commsClientVar12, uPIClient, usersClient, groupClient, onboardingClient, upiRedisStore, vPAVerificationPublisher, upiEventSnsPublisher, pinAttemptsExceededEventSnsPublisher, upiGenConf, upiOnboardingClient, connectedAccountClient, locationClient, recurringPaymentServiceClient)

	upipb.RegisterUPIServer(s, serviceVar55)

	serviceVar56 := wire12.InitializeUpiCxService(accountPIRelationClient, piClient, uPIClient, upiOnboardingClient)

	upicxpb.RegisterUpiCXServer(s, serviceVar56)

	upiDevService := wire12.InitializeDevService(epifiCRDB, upiRedisStore, upiConf)

	upideveloper.RegisterDevServer(s, upiDevService)

	serviceVar57 := wire12.InitializeConsumerService(paymentClientVar3, uPIClientVar4, piClient, accountPIRelationClient, actorClient, timelineServiceClient, orderServiceClient, epifiCRDB, broker, commsClientVar12, savingsClient, upiConf, authClient, uPIClient, recurringPaymentServiceClient, upiRedisStore, upiOnboardingClient, usersClient, groupClient, upiComplaintStatusAutoUpdateEventPublisher, uPIEnquiryPublisherVar2, upiEventSnsPublisher, onboardingClient, nudgeServiceClient, upiGenConf, locationClient)

	if func(conf *config.Config) bool {
		return cfg.IsSimulatedEnv(conf.Environment)
	}(conf) {
		upipb.RegisterConsumerServer(s, serviceVar57)
	}

	serviceVar58 := wire12.InitializeConsumerService(paymentClientVar3, uPIClientVar4, piClient, accountPIRelationClient, actorClient, timelineServiceClient, orderServiceClient, epifiCRDB, broker, commsClientVar12, savingsClient, upiConf, authClient, uPIClient, recurringPaymentServiceClient, upiRedisStore, upiOnboardingClient, usersClient, groupClient, upiComplaintStatusAutoUpdateEventPublisher, uPIEnquiryPublisherVar2, upiEventSnsPublisher, onboardingClient, nudgeServiceClient, upiGenConf, locationClient)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.VPAVerificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer11.RegisterVerifyVpaMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.UPIUserDevicePropertiesUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer11.RegisterProcessUserDevicePropertiesUpdateEventMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.OrderUpdateEventForComplianceSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer11.RegisterProcessOrderUpdateEventForComplianceMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqAuthEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessReqAuthMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.RespPayEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessResPayMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqTxnConfirmationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessReqTxnConfirmationMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqValAddressEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessReqValAddressMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ListPspKeysEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessListPspKeysMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.UPIOnboardingEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessOnboardingEventMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.UPIAuthFactorUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessAuthFactorUpdateEventMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.UpiComplaintReqTxnConfirmationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessReqTxnConfirmationComplaintMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.RespComplaintSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessRespComplaintMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = awswire.InitializeExtendedSubscriberWithGenConf(ctx, upiGenConf.ListVaeEventSubscriber(), awsConf, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upipb.RegisterProcessListVaeMethodToSubscriber(subscriber, serviceVar58)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors())
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar59 := wire12.InitializeMandateService(epifiCRDB, piClient, accountPIRelationClient, uPIClientVar4, orderServiceClient, paymentClientVar3, upiRedisStore, upiConf, usersClient, groupClient, actorClient, savingsClient, authClient, recurringPaymentServiceClient, locationClient, upiGenConf)

	upimandatepb.RegisterMandateServiceServer(s, serviceVar59)

	serviceVar60 := wire12.InitializeUpiOnboardingService(epifiCRDB, uPIClientVar4, piClient, accountPIRelationClient, uPIOnboardingRedisStore, upiRedisStore, upiConf, upiGenConf, celestialClient, savingsClient, actorClient, authClient, uPIClient, groupClient, usersClient, consentClient, upiEventSnsPublisher, broker, recurringPaymentServiceClient, orderServiceClient, onboardingClient, connectedAccountClient, nudgeServiceClient, locationClient)

	upionboardingpb.RegisterUpiOnboardingServer(s, serviceVar60)

	serviceVar61 := wire12.InitializeUpiOnboardingConsumerProcessor(epifiCRDB, upiOnboardingClient, uPIClientVar4, commsClientVar15, upiGenConf, broker)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.VpaMigrationConsentSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upionboardingconsumerpb.RegisterMigrateVpaMethodToSubscriber(subscriber, serviceVar61)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqMapperConfirmationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		upionboardingconsumerpb.RegisterProcessReqMapperConfirmationMethodToSubscriber(subscriber, serviceVar61)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	serviceVar62 := wire12.InitializeSimulationService(uPIClientVar4)

	if func(conf *config.Config) bool {
		return cfg.IsSimulatedEnv(conf.Environment)
	}(conf) {
		simulation2.RegisterSimulationServer(s, serviceVar62)
	}

	serviceVar63 := wire12.InitializeMandateConsumerService(epifiCRDB, piClient, accountPIRelationClient, uPIClientVar4, actorClient, recurringPaymentServiceClient, upiRedisStore, upiConf, usersClient, groupClient, commsClientVar12, savingsClient, authClient, locationClient, upiGenConf)

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqAuthMandateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer12.RegisterProcessReqAuthMandateMethodToSubscriber(subscriber, serviceVar63)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqMandateConfirmationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer12.RegisterProcessReqMandateConfirmationMethodToSubscriber(subscriber, serviceVar63)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.RespMandateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer12.RegisterProcessRespMandateMethodToSubscriber(subscriber, serviceVar63)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}

	_, err = sqs.NewSubscriberWithGenConfigV1(ctx, upiGenConf.ReqAuthValCustEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer12.RegisterProcessReqAuthValCustMethodToSubscriber(subscriber, serviceVar63)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.UPI_SERVICE)] = &commonexplorer.Config{StaticConf: &upiconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupHealthengine(ctx context.Context, s *grpc.Server, httpMux *http.ServeMux, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	healthEngineRedisStore types7.HealthEngineRedisStore) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	health_engineConf, err := healthengineconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.HEALTH_ENGINE_SERVICE))
		return err
	}
	_ = health_engineConf

	healthEngineWebhookPublisher, err := sqs.NewPublisherWithConfig(ctx, health_engineConf.HealthEngineWebhookPublisher, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return err
	}

	serviceVar64 := wire13.InitializeService(health_engineConf, healthEngineRedisStore, healthEngineWebhookPublisher)

	healthenginepb.RegisterHealthEngineServiceServer(s, serviceVar64)

	httpMux.HandleFunc("/health-engine/webhook", serviceVar64.HealthEngineWebhook)

	healthEngineDevService := wire13.IntializeDevService(health_engineConf, healthEngineRedisStore)

	hedeveloperpb.RegisterHealthEngineDevServer(s, healthEngineDevService)

	serviceVar65 := wire13.InitializeConsumerService(health_engineConf, healthEngineRedisStore, broker)

	_, err = sqs.NewSubscriberWithConfigV1(ctx, health_engineConf.HealthEngineWebhookSubscriber, sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer13.RegisterProcessHealthStatusMethodToSubscriber(subscriber, serviceVar65)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return err
	}
	configNameToConfMap[cfg.ConfigName(cfg.HEALTH_ENGINE_SERVICE)] = &commonexplorer.Config{StaticConf: &healthengineconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

// nolint: funlen
func setupBillpay(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker) error {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return err
	}
	_ = env

	billpayConf, err := billpayconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.BILL_PAY_SERVICE))
		return err
	}
	_ = billpayConf

	billpayGenConf, err := dynconf.LoadConfig(billpayconf.Load, genconf8.NewConfig, cfg.BILL_PAY_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.BILL_PAY_SERVICE))
		return err
	}

	_ = billpayGenConf

	serviceVar66 := wire14.InitializeService(billpayGenConf)

	billpaypb.RegisterBillPayServer(s, serviceVar66)

	configNameToConfMap[cfg.ConfigName(cfg.BILL_PAY_SERVICE)] = &commonexplorer.Config{StaticConf: &billpayconf.Config{}, QuestIntegratedConfig: nil}

	return nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.PAY_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
