package accounts

import (
	"github.com/epifi/be-common/tools/servergen/meta"
	accountwire "github.com/epifi/gamma/accounts/wire"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/developer"
	operStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	accountstatementpb "github.com/epifi/gamma/api/accounts/statement"
)

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     accountwire.InitialiseStatementService,
		GRPCRegisterMethods: []any{accountstatementpb.RegisterAccountStatementServer},
	},
	{
		WireInitializer:     accountwire.InitialiseOperationalStatusService,
		GRPCRegisterMethods: []any{operStatusPb.RegisterOperationalStatusServiceServer},
	},
	{
		WireInitializer:     accountwire.InitialiseBalanceService,
		GRPCRegisterMethods: []any{accountBalancePb.RegisterBalanceServer},
	},
	{
		WireInitializer: accountwire.InitialiseStatementConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  accountstatementpb.ProcessAccountStatementMethod,
				ConfigField: "AccountStmtSubscriber",
			},
			{
				MethodName:  accountstatementpb.ProcessMonthlyAccountStatementMethod,
				ConfigField: "AccountMonthlyStmtSubscriber",
			},
			{
				MethodName:  accountstatementpb.ProcessAccountStatementSftpEventMethod,
				ConfigField: "SftpStatementEventSubsriber",
			},
		},
	},
	{
		WireInitializer: accountwire.InitialiseOperStatusConsumerService,
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  operStatusPb.ProcessFederalAccountStatusCallBackMethod,
				ConfigField: "AccountStatusCallBackSub",
			},
		},
	},
	{
		WireInitializer:     accountwire.InitialiseAccountsDevService,
		GRPCRegisterMethods: []any{developer.RegisterAccountsDbStatesServer},
	},
}
