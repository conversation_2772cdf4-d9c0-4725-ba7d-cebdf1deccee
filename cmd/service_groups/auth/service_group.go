package auth

import (
	"github.com/epifi/be-common/tools/servergen/meta"

	"github.com/epifi/gamma/api/auth/totp"

	authorizerPb "github.com/epifi/gamma/api/auth/authorizer"
	biometricsPb "github.com/epifi/gamma/api/auth/biometrics"
	sessionPb "github.com/epifi/gamma/api/auth/session"
	biometricsWire "github.com/epifi/gamma/auth/biometrics/wire"

	authpb "github.com/epifi/gamma/api/auth"
	authconsumerpb "github.com/epifi/gamma/api/auth/consumer"
	authdevpb "github.com/epifi/gamma/api/auth/developer"
	locationpb "github.com/epifi/gamma/api/auth/location"
	authv2pb "github.com/epifi/gamma/api/auth/orchestrator"
	authorchdevpb "github.com/epifi/gamma/api/auth/orchestrator/developer"
	authpartnersdkpb "github.com/epifi/gamma/api/auth/partnersdk"
	authv2wire "github.com/epifi/gamma/auth/orchestrator/wire"
	authwire "github.com/epifi/gamma/auth/wire"
)

var shouldInitQuest = true

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer: authwire.InitializeService,
		GRPCRegisterMethods: []any{
			authconsumerpb.RegisterAuthFactorUpdateConsumerServer,
			authpb.RegisterAuthServer,
			authorizerPb.RegisterAuthorizerServer,
		},
		ConsumerMethods: []*meta.ConsumerMethod{
			{
				MethodName:  authconsumerpb.ProcessVendorUpdateMethod,
				ConfigField: "AFUVendorUpdateSubscriber",
			},
			{
				MethodName:  authconsumerpb.DeviceReregCallbackConsumerMethod,
				ConfigField: "DeviceReregCallbackSubscriber",
			},
			{
				MethodName:  authconsumerpb.ProcessAFUManualReviewMethod,
				ConfigField: "AFUManualReviewNotificationSubscriber",
			},
			{
				MethodName:  authconsumerpb.ProcessDevRegSMSAcknowledgementMethod,
				ConfigField: "DeviceRegSMSAckSubscriber",
			},
			{
				MethodName:  authconsumerpb.ProcessPinAttemptsExceededEventMethod,
				ConfigField: "ProcessPinAttemptsExceededEventSubscriber",
			},
		},
	},
	{
		WireInitializer:     authwire.InitializeDevAuthService,
		GRPCRegisterMethods: []any{authdevpb.RegisterDevAuthServer},
	},
	{
		WireInitializer:     authwire.InitializePartnerSDKService,
		GRPCRegisterMethods: []any{authpartnersdkpb.RegisterPartnerSDKServer},
	},
	{
		WireInitializer:     authwire.InitializeLocationService,
		GRPCRegisterMethods: []any{locationpb.RegisterLocationServer},
	},
	{
		WireInitializer:     authv2wire.InitializeService,
		GRPCRegisterMethods: []any{authv2pb.RegisterOrchestratorServer},
	},
	{
		WireInitializer:     authv2wire.InitializeOrchestratorDevEntityService,
		GRPCRegisterMethods: []any{authorchdevpb.RegisterDevOrchestratorServer},
	},
	{
		WireInitializer:     biometricsWire.InitializeBiometricsService,
		GRPCRegisterMethods: []any{biometricsPb.RegisterBiometricsServiceServer},
	},
	{
		WireInitializer:     authwire.InitializeTotpService,
		GRPCRegisterMethods: []any{totp.RegisterTotpServer},
	},
	{
		WireInitializer:     authwire.InitializeSessionManagerService,
		GRPCRegisterMethods: []any{sessionPb.RegisterSessionManagerServer},
	},
}
