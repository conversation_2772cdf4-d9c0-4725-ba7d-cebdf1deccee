Application:
  Environment: "prod"
  Namespace: "prod-savings"
  TaskQueue: "prod-savings-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "redis-15866.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:15866"
    AuthDetails:
      SecretPath: "prod/redis/centralgrowth/prefixaccess"
      Environment: "prod"
      Region: "ap-south-1"
    ClientName: savings-worker

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "savings-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "prod/cockroach/ca.crt"
    SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "savings-worker"
    GormV2:
      LogLevelGormV2: "WARN"
      SlowQueryLogThreshold: 200ms

Secrets:
  Ids:
    TemporalCodecAesKey: "prod/temporal/codec-encryption-key"

WorkflowUpdatePublisher:
  TopicName: "prod-celestial-workflow-update-topic"

WorkflowParamsList:
  - WorkflowName: "SchemeChange"
    ActivityParamsList:
      - ActivityName: "UpdateSchemeAtVendor"
        ScheduleToCloseTimeout: "48h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 24
        RateLimit:
          Rate: 100
          Period: "1m"
      - ActivityName: "EnquireSchemeAtVendor"
        ScheduleToCloseTimeout: "80h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "6h"
            MaxAttempts: 9
        RateLimit:
          Rate: 100
          Period: "1m"
      - ActivityName: "UpdateSchemeInDB"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "30s"
            MaxAttempts: 120
      - ActivityName: "WorkflowDecisionOrchestrator"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "24h"
            MaxAttempts: 3
      - ActivityName: "RemoveRequestIdFromSkuInfo"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "30s"
            MaxAttempts: 120
