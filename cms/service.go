package cms

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	cmsPb "github.com/epifi/gamma/api/cms"
	"github.com/epifi/gamma/cms/cryptor"
	"github.com/epifi/gamma/cms/dao"
)

type CmsService struct {
	redemptionDao  dao.RedemptionDao
	skuDao         dao.SkuDao
	couponDao      dao.CouponDao
	txnExecutor    storageV2.TxnExecutor
	cmsDataCryptor cryptor.CmsDataCryptor
	productDao     dao.ProductDao
}

func NewCmsService(
	redemptionDao dao.RedemptionDao,
	skuDao dao.SkuDao,
	couponDao dao.CouponDao,
	txnExecutor storageV2.TxnExecutor,
	cmsDataCryptor cryptor.CmsDataCryptor,
	productDao dao.ProductDao,
) *CmsService {
	return &CmsService{
		redemptionDao:  redemptionDao,
		skuDao:         skuDao,
		couponDao:      couponDao,
		txnExecutor:    txnExecutor,
		cmsDataCryptor: cmsDataCryptor,
		productDao:     productDao,
	}
}

var _ cmsPb.CmsServiceServer = &CmsService{}

// ProcessOrder
// 1. acquire lock on redemption request to prevent multiple request with same redemption id to process simultaneously
// 2. check if record corresponding to reference id exists in redemptions table, if not then create a new request with the reference id with status as PROCESSING
// 3. check the redemption status, if in terminal state i.e no further processing is required then return the status and coupon details after decryption
// 4. if the status is not terminal then we further try to process the redemption request
// 5. get the sku, if not found then we fail the request, else we check is the sku is valid now, if not we fail the redemption request
// 6. get available coupon corresponding to the sku id, if coupon is not found then we fail the request
// 7. we update coupons and redemptions table and return the status and coupon details after decrypting it
// todo(sresth) check if we can use ctx instead of txnCtx in some dao calls
func (cms *CmsService) ProcessOrder(ctx context.Context, req *cmsPb.ProcessOrderRequest) (*cmsPb.ProcessOrderResponse, error) {
	var (
		redemptionStatus cmsPb.RedemptionStatus
		order            *cmsPb.Order
	)

	txnErr := cms.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// getting RedemptionByReferenceId, if redemption record exists for redemptionId and is not in terminal state then try to make redemption,
		// else get the redemption status and coupon details and return it
		redemption, err := cms.redemptionDao.GetRedemptionByReferenceId(txnCtx, req.GetReferenceId())
		if err != nil && err != epifierrors.ErrRecordNotFound {
			return fmt.Errorf("GetRedemptionByReferenceId call failed, err: %w", err)
		}

		// if redemption entry is not found in redemptions table then make a new entry
		if err == epifierrors.ErrRecordNotFound {
			var createRedemptionErr error
			redemptionStatus = cmsPb.RedemptionStatus_REDEMPTION_STATUS_PROCESSING
			// creating a new redemption record, with the given reference id, with status as PROCESSING
			redemption, createRedemptionErr = cms.redemptionDao.CreateRedemption(txnCtx, &cmsPb.Redemption{
				ReferenceId: req.GetReferenceId(),
				Status:      cmsPb.RedemptionStatus_REDEMPTION_STATUS_PROCESSING,
			})
			if createRedemptionErr != nil {
				return fmt.Errorf("CreateRedemption call failed, err: %w", createRedemptionErr)
			}
		}

		if IsRedemptionInTerminalState(redemption.GetStatus()) {
			redemptionStatus = redemption.GetStatus()
			coupon, getCouponErr := cms.GetCoupon(txnCtx, redemption)
			if getCouponErr != nil {
				return getCouponErr
			}
			if coupon != nil {
				sku, getSkuErr := cms.GetSku(txnCtx, coupon.GetSkuId())
				if getSkuErr != nil {
					return getSkuErr
				}
				order = &cmsPb.Order{
					CouponDetails: coupon.GetCouponDetails(),
					ValidTill:     sku.GetValidTill(),
				}
			}
			return nil
		}

		// this is only used to get lock on the redemption entry to avoid multiple request for same reference id to be processed together
		_, getRedemptionByReferenceIdWithLockErr := cms.redemptionDao.GetRedemptionByReferenceIdWithLock(txnCtx, req.GetReferenceId())
		if getRedemptionByReferenceIdWithLockErr != nil {
			return fmt.Errorf("error in taking lock on redemption record, err: %w", getRedemptionByReferenceIdWithLockErr)
		}

		// getting the given sku and checking its validity, if not valid then fail the redemption request
		sku, getSkuByIdErr := cms.skuDao.GetSkuById(txnCtx, req.GetSkuId())
		if getSkuByIdErr != nil && getSkuByIdErr != epifierrors.ErrRecordNotFound {
			return fmt.Errorf("GetSkuById call failed, err: %w", getSkuByIdErr)
		}
		if getSkuByIdErr == epifierrors.ErrRecordNotFound {
			redemptionStatus = cmsPb.RedemptionStatus_REDEMPTION_STATUS_FAILED
			logger.Info(ctx, "updating redemption, setting redemption status as FAILED because sku id not found")
			updateRedemptionErr := cms.redemptionDao.UpdateRedemption(txnCtx, &cmsPb.Redemption{
				Id:     redemption.GetId(),
				Status: redemptionStatus,
			}, []cmsPb.RedemptionFieldMask{
				cmsPb.RedemptionFieldMask_REDEMPTION_FIELD_MASK_STATUS,
			})
			if updateRedemptionErr != nil {
				return fmt.Errorf("UpdateRedemption call failed, err: %w", updateRedemptionErr)
			}
			return nil
		}
		if sku.GetValidFrom().AsTime().After(time.Now()) || sku.GetValidTill().AsTime().Before(time.Now()) {
			redemptionStatus = cmsPb.RedemptionStatus_REDEMPTION_STATUS_FAILED
			logger.Info(ctx, "updating redemption, setting redemption status as FAILED because sku id not valid now")
			updateRedemptionErr := cms.redemptionDao.UpdateRedemption(txnCtx, &cmsPb.Redemption{
				Id:     redemption.GetId(),
				Status: redemptionStatus,
			}, []cmsPb.RedemptionFieldMask{
				cmsPb.RedemptionFieldMask_REDEMPTION_FIELD_MASK_STATUS,
			})
			if updateRedemptionErr != nil {
				return fmt.Errorf("UpdateRedemption call failed, err: %w", updateRedemptionErr)
			}
			return nil
		}

		// getting coupon with available inventory for given sku id, if not exists then fail the redemption request
		coupon, getAvailableCouponBySkuIdErr := cms.couponDao.GetAvailableCouponBySkuIdWithLock(txnCtx, req.GetSkuId())
		if getAvailableCouponBySkuIdErr != nil && getAvailableCouponBySkuIdErr != epifierrors.ErrRecordNotFound {
			return fmt.Errorf("GetAvailableCouponBySkuId call failed, err: %w", getAvailableCouponBySkuIdErr)
		}
		if getAvailableCouponBySkuIdErr == epifierrors.ErrRecordNotFound {
			redemptionStatus = cmsPb.RedemptionStatus_REDEMPTION_STATUS_FAILED
			logger.Info(ctx, "updating redemption, setting redemption status as FAILED because no available coupon found for the sku id")
			updateRedemptionErr := cms.redemptionDao.UpdateRedemption(txnCtx, &cmsPb.Redemption{
				Id:     redemption.GetId(),
				Status: redemptionStatus,
			}, []cmsPb.RedemptionFieldMask{
				cmsPb.RedemptionFieldMask_REDEMPTION_FIELD_MASK_STATUS,
			})
			if updateRedemptionErr != nil {
				return fmt.Errorf("UpdateRedemption call failed, err: %w", updateRedemptionErr)
			}
			return nil
		}

		// updating coupon after decrementing value of inventory left field by 1
		updateCouponErr := cms.couponDao.UpdateCoupon(txnCtx, &cmsPb.Coupon{
			Id:            coupon.GetId(),
			InventoryLeft: coupon.GetInventoryLeft() - 1,
		}, []cmsPb.CouponFieldMask{
			cmsPb.CouponFieldMask_COUPON_FIELD_MASK_INVENTORY_LEFT,
		})
		if updateCouponErr != nil {
			return fmt.Errorf("UpdateCoupon call faled, err: %w", updateCouponErr)
		}

		// updating redemption record with the coupon id and COMPLETED status
		redemptionStatus = cmsPb.RedemptionStatus_REDEMPTION_STATUS_COMPLETE
		order = &cmsPb.Order{
			CouponDetails: coupon.GetCouponDetails(),
			ValidTill:     sku.GetValidTill(),
		}
		updateRedemptionErr := cms.redemptionDao.UpdateRedemption(txnCtx, &cmsPb.Redemption{
			Id:       redemption.GetId(),
			CouponId: coupon.GetId(),
			Status:   redemptionStatus,
		}, []cmsPb.RedemptionFieldMask{
			cmsPb.RedemptionFieldMask_REDEMPTION_FIELD_MASK_COUPON_ID,
			cmsPb.RedemptionFieldMask_REDEMPTION_FIELD_MASK_STATUS,
		})
		if updateRedemptionErr != nil {
			return fmt.Errorf("UpdateRedemption call failed, err: %w", updateRedemptionErr)
		}

		return nil

	})

	if txnErr != nil {
		logger.Error(ctx, "Error in ProcessAndGetOrder rpc", zap.Error(txnErr))
		return &cmsPb.ProcessOrderResponse{
			Status:           rpc.StatusInternalWithDebugMsg(txnErr.Error()),
			RedemptionStatus: redemptionStatus,
		}, nil
	}
	if order.GetCouponDetails() != nil {
		decryptCouponDetailsErr := cms.DecryptCouponDetails(ctx, order.CouponDetails)
		if decryptCouponDetailsErr != nil {
			logger.Error(ctx, decryptCouponDetailsErr.Error())
			return &cmsPb.ProcessOrderResponse{
				Status: rpc.StatusInternalWithDebugMsg(decryptCouponDetailsErr.Error()),
			}, nil
		}
	}
	return &cmsPb.ProcessOrderResponse{
		Status:           rpc.StatusOk(),
		RedemptionStatus: redemptionStatus,
		Order:            order,
	}, nil
}

func (cms *CmsService) GetCoupon(ctx context.Context, redemption *cmsPb.Redemption) (*cmsPb.Coupon, error) {
	// if redemptions status is not complete then return nil
	if redemption.GetStatus() != cmsPb.RedemptionStatus_REDEMPTION_STATUS_COMPLETE {
		return nil, nil
	}

	// if redemption status is complete then get the coupon details decrypt it and add it to response
	coupon, err := cms.couponDao.GetCouponById(ctx, redemption.GetCouponId())
	if err != nil && err != epifierrors.ErrRecordNotFound {
		return nil, fmt.Errorf("GetCouponById call failed, err: %w", err)
	}
	if err == epifierrors.ErrRecordNotFound {
		return nil, fmt.Errorf("no coupon record corresponding to id, err: %w", err)
	}

	return coupon, nil
}

func (cms *CmsService) GetSku(ctx context.Context, skuId string) (*cmsPb.Sku, error) {
	sku, getSkuByIdErr := cms.skuDao.GetSkuById(ctx, skuId)
	if getSkuByIdErr != nil && getSkuByIdErr != epifierrors.ErrRecordNotFound {
		return nil, fmt.Errorf("GetSkuById call failed, err: %w", getSkuByIdErr)
	}
	if getSkuByIdErr == epifierrors.ErrRecordNotFound {
		return nil, fmt.Errorf("no sku record corresponding to id, err: %w", getSkuByIdErr)
	}

	return sku, nil
}

func IsRedemptionInTerminalState(redemptionStatus cmsPb.RedemptionStatus) bool {
	switch redemptionStatus {
	case cmsPb.RedemptionStatus_REDEMPTION_STATUS_COMPLETE, cmsPb.RedemptionStatus_REDEMPTION_STATUS_FAILED:
		return true
	}
	return false
}

func (cms *CmsService) DecryptCouponDetails(ctx context.Context, couponDetails *cmsPb.CouponDetails) error {
	var err error
	for idx, keyValuePair := range couponDetails.GetKeyValuePairs() {
		couponDetails.KeyValuePairs[idx].Value, err = cms.cmsDataCryptor.Decrypt(ctx, keyValuePair.GetValue(), couponDetails.GetEncryptedDataKey(), couponDetails.GetMasterKeyId())
		if err != nil {
			return fmt.Errorf("error in derypting coupon details, err: %w", err)
		}
	}
	return nil
}

func (cms *CmsService) EncryptCouponDetails(ctx context.Context, couponDetails *cmsPb.CouponDetails) (*cmsPb.CouponDetails, error) {
	var encryptedKeyValuePairs []*cmsPb.CouponDetails_KeyValuePair
	var encryptedDataKey, kmsKeyId, encryptedValue string
	for _, keyValuePair := range couponDetails.GetKeyValuePairs() {
		var err error
		encryptedValue, encryptedDataKey, kmsKeyId, err = cms.cmsDataCryptor.Encrypt(ctx, keyValuePair.GetValue())
		if err != nil {
			return nil, fmt.Errorf("error in encrypting coupon details, err: %w", err)
		}
		encryptedKeyValuePairs = append(encryptedKeyValuePairs, &cmsPb.CouponDetails_KeyValuePair{
			Key:   keyValuePair.GetKey(),
			Value: encryptedValue,
		})
	}
	return &cmsPb.CouponDetails{
		KeyValuePairs:    encryptedKeyValuePairs,
		EncryptedDataKey: encryptedDataKey,
		MasterKeyId:      kmsKeyId,
	}, nil
}

// GetOrder will get the redemption details from the redemptions table
func (cms *CmsService) GetOrder(ctx context.Context, req *cmsPb.GetOrderRequest) (*cmsPb.GetOrderResponse, error) {
	redemption, getRedemptionByReferenceIdErr := cms.redemptionDao.GetRedemptionByReferenceId(ctx, req.GetReferenceId())
	if getRedemptionByReferenceIdErr != nil && getRedemptionByReferenceIdErr != epifierrors.ErrRecordNotFound {
		logger.Error(ctx, fmt.Sprintf("error in GetRedemptionByReferenceId, err: %s", getRedemptionByReferenceIdErr.Error()))
		return &cmsPb.GetOrderResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in GetRedemptionByReferenceId, err: %s", getRedemptionByReferenceIdErr.Error())),
		}, nil
	}
	if getRedemptionByReferenceIdErr == epifierrors.ErrRecordNotFound {
		logger.Error(ctx, fmt.Sprintf("error in GetRedemptionByReferenceId, err: %s", getRedemptionByReferenceIdErr.Error()))
		return &cmsPb.GetOrderResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	coupon, getCouponErr := cms.GetCoupon(ctx, redemption)
	if getCouponErr != nil {
		logger.Error(ctx, fmt.Sprintf("error in GetCouponDetails, err: %s", getCouponErr.Error()))
		return &cmsPb.GetOrderResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in GetCouponDetails, err: %s", getCouponErr.Error())),
		}, nil
	}
	var order *cmsPb.Order
	if coupon != nil {
		couponDetails := coupon.GetCouponDetails()
		if decryptCouponDetailsErr := cms.DecryptCouponDetails(ctx, couponDetails); decryptCouponDetailsErr != nil {
			return &cmsPb.GetOrderResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("Error in decrypting coupon details, err: %s", decryptCouponDetailsErr.Error())),
			}, nil
		}
		sku, getSkuErr := cms.GetSku(ctx, coupon.GetSkuId())
		if getSkuErr != nil {
			return &cmsPb.GetOrderResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in getting sku from sku id, err: %s", getSkuErr.Error())),
			}, nil
		}
		order = &cmsPb.Order{
			CouponDetails: couponDetails,
			ValidTill:     sku.GetValidTill(),
		}
	}

	return &cmsPb.GetOrderResponse{
		Status:           rpc.StatusOk(),
		RedemptionStatus: redemption.GetStatus(),
		Order:            order,
	}, nil

}

func (cms *CmsService) CreateProduct(ctx context.Context, req *cmsPb.CreateProductRequest) (*cmsPb.CreateProductResponse, error) {
	product, createProductErr := cms.productDao.CreateProduct(ctx, &cmsPb.Product{
		BrandName:   req.GetBrandName(),
		Name:        req.GetName(),
		Description: req.GetDescription(),
		ProductType: req.GetProductType(),
		Value:       req.GetValue(),
	})
	if createProductErr != nil {
		logger.Error(ctx, "CreateProduct dao call has failed", zap.Error(createProductErr))
		return &cmsPb.CreateProductResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("CreateProduct dao call has failed, err: %s", createProductErr)),
		}, nil
	}
	return &cmsPb.CreateProductResponse{
		Status:  rpc.StatusOk(),
		Product: product,
	}, nil

}

func (cms *CmsService) CreateSku(ctx context.Context, req *cmsPb.CreateSkuRequest) (*cmsPb.CreateSkuResponse, error) {
	isReqValid, errString := validateCreateSkuRequest(ctx, req)
	if !isReqValid {
		logger.Error(ctx, errString)
		return &cmsPb.CreateSkuResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(errString),
		}, nil
	}
	sku, createSkuErr := cms.skuDao.CreateSku(ctx, &cmsPb.Sku{
		ProductId: req.GetProductId(),
		ValidFrom: req.GetValidFrom(),
		ValidTill: req.GetValidTill(),
		Tncs:      req.GetTncs(),
	})
	if createSkuErr != nil {
		logger.Error(ctx, "CreateSku dao call has failed", zap.Error(createSkuErr))
		return &cmsPb.CreateSkuResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("CreateSku dao call has failed, err: %s", createSkuErr.Error())),
		}, nil
	}
	return &cmsPb.CreateSkuResponse{
		Status: rpc.StatusOk(),
		Sku:    sku,
	}, nil
}

func validateCreateSkuRequest(ctx context.Context, req *cmsPb.CreateSkuRequest) (bool, string) {
	if req.GetProductId() == "" {
		return false, "empty productId not allowed"
	}
	return true, ""
}

func (cms *CmsService) CreateCouponsInBulk(ctx context.Context, req *cmsPb.CreateCouponsInBulkRequest) (*cmsPb.CreateCouponsInBulkResponse, error) {
	isReqValid, errString := validateCreateCouponsInBulkRequest(ctx, req)
	if !isReqValid {
		logger.Error(ctx, errString)
		return &cmsPb.CreateCouponsInBulkResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(errString),
		}, nil
	}
	var coupons []*cmsPb.Coupon
	for _, couponPayload := range req.GetCouponPayloads() {
		encryptedCouponDetails, encryptCouponDetailsErr := cms.EncryptCouponDetails(ctx, couponPayload.GetCouponDetails())
		if encryptCouponDetailsErr != nil {
			logger.Error(ctx, "error in coupon details encryption", zap.Error(encryptCouponDetailsErr))
			return &cmsPb.CreateCouponsInBulkResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in coupon details encryption, err: %s", encryptCouponDetailsErr.Error())),
			}, nil
		}
		coupons = append(coupons, &cmsPb.Coupon{
			SkuId:          couponPayload.GetSkuId(),
			InventoryTotal: couponPayload.GetInventoryTotal(),
			InventoryLeft:  couponPayload.GetInventoryTotal(),
			CouponDetails:  encryptedCouponDetails,
		})

	}
	coupons, createCouponInBulkErr := cms.couponDao.CreateCouponsInBulk(ctx, coupons)
	if createCouponInBulkErr != nil {
		logger.Error(ctx, "CreateCouponInBulk dao call has failed", zap.Error(createCouponInBulkErr))
		return &cmsPb.CreateCouponsInBulkResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("CreateCouponInBulk dao call has failed, err: %s", createCouponInBulkErr.Error())),
		}, nil
	}
	return &cmsPb.CreateCouponsInBulkResponse{
		Status:  rpc.StatusOk(),
		Coupons: coupons,
	}, nil

}

func validateCreateCouponsInBulkRequest(ctx context.Context, req *cmsPb.CreateCouponsInBulkRequest) (bool, string) {
	if len(req.GetCouponPayloads()) > 5000 {
		return false, "length of coupon payloads cannot be more than 5000"
	}

	for _, couponPayload := range req.GetCouponPayloads() {
		if couponPayload.GetSkuId() == "" {
			return false, "empty skuId not allowed"
		}
		if couponPayload.GetInventoryTotal() == 0 {
			return false, "total inventory cannot be zero"
		}
		if len(couponPayload.GetCouponDetails().GetKeyValuePairs()) == 0 {
			return false, "length of key value pair 0 not allowed"
		}
		for _, keyValuePair := range couponPayload.GetCouponDetails().GetKeyValuePairs() {
			if keyValuePair.GetKey() == "" || keyValuePair.GetValue() == "" {
				return false, "empty key value pair not allowed"
			}
		}

	}
	return true, ""
}
