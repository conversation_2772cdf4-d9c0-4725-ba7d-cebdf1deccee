package cms

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"github.com/google/uuid"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormV2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	cmsPb "github.com/epifi/gamma/api/cms"
	"github.com/epifi/gamma/cms/config"
	"github.com/epifi/gamma/cms/cryptor"
	"github.com/epifi/gamma/cms/dao"
	"github.com/epifi/gamma/cms/test"
	mockDao "github.com/epifi/gamma/cms/test/mocks/dao"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

type serviceTestSuite struct {
	config *config.Config
	db     *gormV2.DB
}

func newServiceTestSuite(config *config.Config, db *gormV2.DB) *serviceTestSuite {
	return &serviceTestSuite{
		config: config,
		db:     db,
	}
}

var (
	sts *serviceTestSuite
)

func TestCmsService_ProcessOrder(t *testing.T) {

	couponDao := dao.NewCouponDao(sts.db)
	redemptionDao := dao.NewRedemptionDao(sts.db)
	skuDao := dao.NewSkuDao(sts.db)
	productDao := dao.NewProductDao(sts.db)

	mockCmsDataCryptor := cryptor.NewMockCmsDataCryptorImpl()

	// cms service instance
	gormTxnExecutor := storageV2.NewGormTxnExecutor(sts.db)
	cmsService := NewCmsService(redemptionDao, skuDao, couponDao, gormTxnExecutor, mockCmsDataCryptor, productDao)

	validTill, _ := time.Parse("2006-01-02 15:04:05", "2050-08-20 00:00:00")

	type args struct {
		ctx context.Context
		req *cmsPb.ProcessOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *cmsPb.ProcessOrderResponse
		wantErr bool
	}{
		{
			name: "should return redemption details when existing reference id and sku id is given, for which state is is COMPLETE",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.ProcessOrderRequest{
					ReferenceId: "8a66de05-6599-438a-b18d-564d26398771",
					SkuId:       "85b3d25c-da25-4fe2-8ee3-e069197e6e2c",
				},
			},
			want: &cmsPb.ProcessOrderResponse{
				Status:           rpc.StatusOk(),
				RedemptionStatus: cmsPb.RedemptionStatus_REDEMPTION_STATUS_COMPLETE,
				Order: &cmsPb.Order{
					CouponDetails: &cmsPb.CouponDetails{
						KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
							{
								Key:   "key-1",
								Value: "value-1",
							},
						},
						EncryptedDataKey: "dummyEncryptionKey",
						MasterKeyId:      "dummykmskeyid",
					},
					ValidTill: timestampPb.New(validTill),
				},
			},
			wantErr: false,
		},
		{
			name: "should process and return redemption details when a new reference id and valid skuid is given",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.ProcessOrderRequest{
					ReferenceId: "bd1e4100-af27-4e7a-8cdd-2e889e2aa515",
					SkuId:       "85b3d25c-da25-4fe2-8ee3-e069197e6e2c",
				},
			},
			want: &cmsPb.ProcessOrderResponse{
				Status:           rpc.StatusOk(),
				RedemptionStatus: cmsPb.RedemptionStatus_REDEMPTION_STATUS_COMPLETE,
				Order: &cmsPb.Order{
					CouponDetails: &cmsPb.CouponDetails{
						KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
							{
								Key:   "key-1",
								Value: "value-1",
							},
						},
						EncryptedDataKey: "dummyEncryptionKey",
						MasterKeyId:      "dummykmskeyid",
					},
					ValidTill: timestampPb.New(validTill),
				},
			},
			wantErr: false,
		},
		{
			name: "should process and return redemption details when an existing reference id and valid skuid is given, for which the redemption is in processing state",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.ProcessOrderRequest{
					ReferenceId: "a44bae35-4738-4cb3-b7dc-3ef7954093cd",
					SkuId:       "85b3d25c-da25-4fe2-8ee3-e069197e6e2c",
				},
			},
			want: &cmsPb.ProcessOrderResponse{
				Status:           rpc.StatusOk(),
				RedemptionStatus: cmsPb.RedemptionStatus_REDEMPTION_STATUS_COMPLETE,
				Order: &cmsPb.Order{
					CouponDetails: &cmsPb.CouponDetails{
						KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
							{
								Key:   "key-1",
								Value: "value-1",
							},
						},
						EncryptedDataKey: "dummyEncryptionKey",
						MasterKeyId:      "dummykmskeyid",
					},
					ValidTill: timestampPb.New(validTill),
				},
			},
			wantErr: false,
		},
		{
			name: "should return coupon details when new reference id and sku id is given, with the given sku id having multiple coupons",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.ProcessOrderRequest{
					ReferenceId: "408e5e2f-2dd5-44a7-b55e-8927325a52f5",
					SkuId:       "15ccb918-7f63-4d1f-b6f7-57feae5947b1",
				},
			},
			want: &cmsPb.ProcessOrderResponse{
				Status:           rpc.StatusOk(),
				RedemptionStatus: cmsPb.RedemptionStatus_REDEMPTION_STATUS_COMPLETE,
				Order: &cmsPb.Order{
					CouponDetails: &cmsPb.CouponDetails{
						KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
							{
								Key:   "key-2",
								Value: "value-2",
							},
						},
						EncryptedDataKey: "dummyEncryptionKey",
						MasterKeyId:      "dummykmskeyid",
					},
					ValidTill: timestampPb.New(validTill),
				},
			},
			wantErr: false,
		},
		{
			name: "should return redemption details when a existing reference id and valid skuid is is given, for which the redemption is in falied state",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.ProcessOrderRequest{
					ReferenceId: "7f58ff63-6184-4ecd-b813-46da68ecb1a2",
					SkuId:       "85b3d25c-da25-4fe2-8ee3-e069197e6e2c",
				},
			},
			want: &cmsPb.ProcessOrderResponse{
				Status:           rpc.StatusOk(),
				RedemptionStatus: cmsPb.RedemptionStatus_REDEMPTION_STATUS_FAILED,
			},
			wantErr: false,
		},
		{
			name: "should return failed state when a new reference id and skuid is given, but skuid is not found",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.ProcessOrderRequest{
					ReferenceId: "a6821539-c58c-4760-9304-c0ddb9e756a1",
					SkuId:       "6e327e35-4e63-4b6d-ba13-98acf7a3115b",
				},
			},
			want: &cmsPb.ProcessOrderResponse{
				Status:           rpc.StatusOk(),
				RedemptionStatus: cmsPb.RedemptionStatus_REDEMPTION_STATUS_FAILED,
			},
			wantErr: false,
		},
		{
			name: "should return failed state when a new reference id and valid skuid is given, but no available coupons are present corresponding to skuid",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.ProcessOrderRequest{
					ReferenceId: "a6821539-c58c-4760-9304-c0ddb9e756a1",
					SkuId:       "5881523c-4fc7-4f70-86e6-4a28148a9b0c",
				},
			},
			want: &cmsPb.ProcessOrderResponse{
				Status:           rpc.StatusOk(),
				RedemptionStatus: cmsPb.RedemptionStatus_REDEMPTION_STATUS_FAILED,
			},
			wantErr: false,
		},
		{
			name: "should return failed state when a new reference id and skuid is given, but the skuid is not valid now",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.ProcessOrderRequest{
					ReferenceId: "a2dfce63-afbc-4411-ba56-4eef2f97bbf4",
					SkuId:       "457f0718-036f-4d0c-abc7-6180ba1285b2",
				},
			},
			want: &cmsPb.ProcessOrderResponse{
				Status:           rpc.StatusOk(),
				RedemptionStatus: cmsPb.RedemptionStatus_REDEMPTION_STATUS_FAILED,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, sts.db, sts.config.CmsDb.GetName(), test.AllTables)

			got, err := cmsService.ProcessOrder(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessOrGetOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ProcessOrGetOrder() got = %v\nwant %v", got, tt.want)
			}
		})
	}
}

func TestCmsService_GetOrder(t *testing.T) {

	couponDao := dao.NewCouponDao(sts.db)
	redemptionDao := dao.NewRedemptionDao(sts.db)
	skuDao := dao.NewSkuDao(sts.db)
	productDao := dao.NewProductDao(sts.db)

	mockCmsDataCryptor := cryptor.NewMockCmsDataCryptorImpl()

	// cms service instance
	gormTxnExecutor := storageV2.NewGormTxnExecutor(sts.db)
	cmsService := NewCmsService(redemptionDao, skuDao, couponDao, gormTxnExecutor, mockCmsDataCryptor, productDao)

	validTill, _ := time.Parse("2006-01-02 15:04:05", "2050-08-20 00:00:00")

	type args struct {
		ctx context.Context
		req *cmsPb.GetOrderRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *cmsPb.GetOrderResponse
		wantErr bool
	}{
		{
			name: "should return redemption details when existing reference id",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.GetOrderRequest{
					ReferenceId: "8a66de05-6599-438a-b18d-564d26398771",
				},
			},
			want: &cmsPb.GetOrderResponse{
				Status:           rpc.StatusOk(),
				RedemptionStatus: cmsPb.RedemptionStatus_REDEMPTION_STATUS_COMPLETE,
				Order: &cmsPb.Order{
					CouponDetails: &cmsPb.CouponDetails{
						KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
							{
								Key:   "key-1",
								Value: "value-1",
							},
						},
						EncryptedDataKey: "dummyEncryptionKey",
						MasterKeyId:      "dummykmskeyid",
					},
					ValidTill: timestampPb.New(validTill),
				},
			},
			wantErr: false,
		},
		{
			name: "should return redemption details when existing reference id",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.GetOrderRequest{
					ReferenceId: "0f87ee04-8864-4859-a02c-1b2b7494d9e1",
				},
			},
			want: &cmsPb.GetOrderResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, sts.db, sts.config.CmsDb.GetName(), test.AllTables)

			got, err := cmsService.GetOrder(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetOrder() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCmsService_CreateProduct(t *testing.T) {

	uuid := uuid.NewString()
	timestamp := timestampPb.Now()
	createProductRequest := &cmsPb.CreateProductRequest{
		BrandName:   "brand-name-1",
		Name:        "name-1",
		Description: "description-1",
		ProductType: cmsPb.ProductType_PRODUCT_TYPE_EGV,
		Value:       111,
	}
	product := &cmsPb.Product{
		Id:          uuid,
		BrandName:   "brand-name-1",
		Name:        "name-1",
		Description: "description-1",
		ProductType: cmsPb.ProductType_PRODUCT_TYPE_EGV,
		Value:       111,
		CreatedAt:   timestamp,
		UpdatedAt:   timestamp,
	}

	type args struct {
		ctx context.Context
		req *cmsPb.CreateProductRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockProductDao *mockDao.MockProductDao)
		want       *cmsPb.CreateProductResponse
		wantErr    bool
	}{
		{
			name: "should return success when valid parameters are given",
			args: args{
				ctx: context.Background(),
				req: createProductRequest,
			},
			setupMocks: func(mockProductDao *mockDao.MockProductDao) {
				mockProductDao.EXPECT().CreateProduct(context.Background(), &cmsPb.Product{
					BrandName:   createProductRequest.GetBrandName(),
					Name:        createProductRequest.GetName(),
					Description: createProductRequest.GetDescription(),
					ProductType: createProductRequest.GetProductType(),
					Value:       createProductRequest.GetValue(),
				}).Return(product, nil)
			},
			want: &cmsPb.CreateProductResponse{
				Status:  rpc.StatusOk(),
				Product: product,
			},
			wantErr: false,
		},
		{
			name: "should return error when dao call fails",
			args: args{
				ctx: context.Background(),
				req: createProductRequest,
			},
			setupMocks: func(mockProductDao *mockDao.MockProductDao) {
				mockProductDao.EXPECT().CreateProduct(context.Background(), &cmsPb.Product{
					BrandName:   createProductRequest.GetBrandName(),
					Name:        createProductRequest.GetName(),
					Description: createProductRequest.GetDescription(),
					ProductType: createProductRequest.GetProductType(),
					Value:       createProductRequest.GetValue(),
				}).Return(nil, fmt.Errorf("error"))
			},
			want: &cmsPb.CreateProductResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("CreateProduct dao call has failed, err: %s", "error")),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockProductDao := mockDao.NewMockProductDao(ctr)

			cms := &CmsService{
				productDao: mockProductDao,
			}

			// setupMocks
			tt.setupMocks(mockProductDao)

			got, err := cms.CreateProduct(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateProduct() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateProduct() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCmsService_CreateSku(t *testing.T) {

	validFrom := timestampPb.Now()
	validTill := timestampPb.New(time.Now().Add(8 * time.Hour))

	createSkuRequest := &cmsPb.CreateSkuRequest{
		ProductId: "product-1",
		ValidFrom: validFrom,
		ValidTill: validTill,
		Tncs:      &cmsPb.Tncs{},
	}

	sku := &cmsPb.Sku{
		Id:        uuid.NewString(),
		ProductId: "product-1",
		ValidFrom: validFrom,
		ValidTill: validTill,
		Tncs:      &cmsPb.Tncs{},
		CreatedAt: timestampPb.Now(),
		UpdatedAt: timestampPb.Now(),
	}

	type args struct {
		ctx context.Context
		req *cmsPb.CreateSkuRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockSkuDao *mockDao.MockSkuDao)
		want       *cmsPb.CreateSkuResponse
		wantErr    bool
	}{
		{
			name: "should return success when valid parameters are given",
			args: args{
				ctx: context.Background(),
				req: createSkuRequest,
			},
			setupMocks: func(mockSkuDao *mockDao.MockSkuDao) {
				mockSkuDao.EXPECT().CreateSku(context.Background(), &cmsPb.Sku{
					ProductId: createSkuRequest.GetProductId(),
					ValidFrom: createSkuRequest.GetValidFrom(),
					ValidTill: createSkuRequest.GetValidTill(),
					Tncs:      createSkuRequest.GetTncs(),
				}).Return(sku, nil)
			},
			want: &cmsPb.CreateSkuResponse{
				Status: rpc.StatusOk(),
				Sku:    sku,
			},
			wantErr: false,
		},
		{
			name: "should return error when productId is empty",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.CreateSkuRequest{
					ValidFrom: validFrom,
					ValidTill: validTill,
					Tncs:      &cmsPb.Tncs{},
				},
			},
			setupMocks: func(mockSkuDao *mockDao.MockSkuDao) {},
			want: &cmsPb.CreateSkuResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("empty productId not allowed"),
			},
			wantErr: false,
		},
		{
			name: "should return error when dao call fails",
			args: args{
				ctx: context.Background(),
				req: createSkuRequest,
			},
			setupMocks: func(mockSkuDao *mockDao.MockSkuDao) {
				mockSkuDao.EXPECT().CreateSku(context.Background(), &cmsPb.Sku{
					ProductId: createSkuRequest.GetProductId(),
					ValidFrom: createSkuRequest.GetValidFrom(),
					ValidTill: createSkuRequest.GetValidTill(),
					Tncs:      createSkuRequest.GetTncs(),
				}).Return(nil, fmt.Errorf("error"))
			},
			want: &cmsPb.CreateSkuResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("CreateSku dao call has failed, err: %s", "error")),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockSkuDao := mockDao.NewMockSkuDao(ctr)

			cms := &CmsService{
				skuDao: mockSkuDao,
			}

			// setupMocks
			tt.setupMocks(mockSkuDao)

			got, err := cms.CreateSku(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateSku() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateSku() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCmsService_CreateCouponsInBulk(t *testing.T) {

	createCouponsInBulkRequest := &cmsPb.CreateCouponsInBulkRequest{
		CouponPayloads: []*cmsPb.CreateCouponsInBulkRequest_CouponPayload{
			{
				SkuId:          "sku-id-1",
				InventoryTotal: 10,
				CouponDetails: &cmsPb.CouponDetails{
					KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
						{
							Key:   "key-1",
							Value: "value-1",
						},
					},
				},
			},
			{
				SkuId:          "sku-id-2",
				InventoryTotal: 10,
				CouponDetails: &cmsPb.CouponDetails{
					KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
						{
							Key:   "key-2",
							Value: "value-2",
						},
					},
				},
			},
		},
	}

	couponsRequest := []*cmsPb.Coupon{
		{
			SkuId:          "sku-id-1",
			InventoryTotal: 10,
			InventoryLeft:  10,
			CouponDetails: &cmsPb.CouponDetails{
				KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
					{
						Key:   "key-1",
						Value: "value-1",
					},
				},
				EncryptedDataKey: "dummyEncryptionKey",
				MasterKeyId:      "dummykmskeyid",
			},
		},
		{
			SkuId:          "sku-id-2",
			InventoryTotal: 10,
			InventoryLeft:  10,
			CouponDetails: &cmsPb.CouponDetails{
				KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
					{
						Key:   "key-2",
						Value: "value-2",
					},
				},
				EncryptedDataKey: "dummyEncryptionKey",
				MasterKeyId:      "dummykmskeyid",
			},
		},
	}

	coupons := []*cmsPb.Coupon{
		{
			Id:             uuid.NewString(),
			SkuId:          "sku-id-1",
			InventoryTotal: 10,
			CouponDetails: &cmsPb.CouponDetails{
				KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
					{
						Key:   "key-1",
						Value: "value-1",
					},
				},
				EncryptedDataKey: "dummyEncryptionKey",
				MasterKeyId:      "dummykmskeyid",
			},
			CreatedAt: timestampPb.Now(),
			UpdatedAt: timestampPb.Now(),
		},
		{
			Id:             uuid.NewString(),
			SkuId:          "sku-id-2",
			InventoryTotal: 10,
			CouponDetails: &cmsPb.CouponDetails{
				KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
					{
						Key:   "key-2",
						Value: "value-2",
					},
				},
				EncryptedDataKey: "dummyEncryptionKey",
				MasterKeyId:      "dummykmskeyid",
			},
			CreatedAt: timestampPb.Now(),
			UpdatedAt: timestampPb.Now(),
		},
	}

	type args struct {
		ctx context.Context
		req *cmsPb.CreateCouponsInBulkRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockCouponDao *mockDao.MockCouponDao)
		want       *cmsPb.CreateCouponsInBulkResponse
		wantErr    bool
	}{
		{
			name: "should return success when valid parameters are given",
			args: args{
				ctx: context.Background(),
				req: createCouponsInBulkRequest,
			},
			setupMocks: func(mockCouponDao *mockDao.MockCouponDao) {
				mockCouponDao.EXPECT().CreateCouponsInBulk(context.Background(), couponsRequest).Return(coupons, nil)
			},
			want: &cmsPb.CreateCouponsInBulkResponse{
				Status:  rpc.StatusOk(),
				Coupons: coupons,
			},
			wantErr: false,
		},
		{
			name: "should return error when valid skuId is empty",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.CreateCouponsInBulkRequest{
					CouponPayloads: []*cmsPb.CreateCouponsInBulkRequest_CouponPayload{
						{
							InventoryTotal: 10,
							CouponDetails: &cmsPb.CouponDetails{
								KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
									{
										Key:   "key-1",
										Value: "value-1",
									},
								},
								EncryptedDataKey: "dummyEncryptionKey",
								MasterKeyId:      "dummykmskeyid",
							},
						},
					},
				},
			},
			setupMocks: func(mockCouponDao *mockDao.MockCouponDao) {
			},
			want: &cmsPb.CreateCouponsInBulkResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("empty skuId not allowed"),
			},
			wantErr: false,
		},
		{
			name: "should return error when inventoryTotal is empty",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.CreateCouponsInBulkRequest{
					CouponPayloads: []*cmsPb.CreateCouponsInBulkRequest_CouponPayload{
						{
							SkuId:          "sku-id-1",
							InventoryTotal: 0,
							CouponDetails: &cmsPb.CouponDetails{
								KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
									{
										Key:   "key-1",
										Value: "value-1",
									},
								},
								EncryptedDataKey: "dummyEncryptionKey",
								MasterKeyId:      "dummykmskeyid",
							},
						},
					},
				},
			},
			setupMocks: func(mockCouponDao *mockDao.MockCouponDao) {
			},
			want: &cmsPb.CreateCouponsInBulkResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("total inventory cannot be zero"),
			},
			wantErr: false,
		},
		{
			name: "should return error when 0 length KeyValue pair is given",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.CreateCouponsInBulkRequest{
					CouponPayloads: []*cmsPb.CreateCouponsInBulkRequest_CouponPayload{
						{
							SkuId:          "sku-id-1",
							InventoryTotal: 10,
							CouponDetails: &cmsPb.CouponDetails{
								KeyValuePairs:    []*cmsPb.CouponDetails_KeyValuePair{},
								EncryptedDataKey: "dummyEncryptionKey",
								MasterKeyId:      "dummykmskeyid",
							},
						},
					},
				},
			},
			setupMocks: func(mockCouponDao *mockDao.MockCouponDao) {
			},
			want: &cmsPb.CreateCouponsInBulkResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("length of key value pair 0 not allowed"),
			},
			wantErr: false,
		},
		{
			name: "should return error when key is empty",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.CreateCouponsInBulkRequest{
					CouponPayloads: []*cmsPb.CreateCouponsInBulkRequest_CouponPayload{
						{
							SkuId:          "sku-id-1",
							InventoryTotal: 10,
							CouponDetails: &cmsPb.CouponDetails{
								KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
									{
										Key:   "",
										Value: "value-1",
									},
								},
								EncryptedDataKey: "dummyEncryptionKey",
								MasterKeyId:      "dummykmskeyid",
							},
						},
					},
				},
			},
			setupMocks: func(mockCouponDao *mockDao.MockCouponDao) {
			},
			want: &cmsPb.CreateCouponsInBulkResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("empty key value pair not allowed"),
			},
			wantErr: false,
		},
		{
			name: "should return error when value is empty",
			args: args{
				ctx: context.Background(),
				req: &cmsPb.CreateCouponsInBulkRequest{
					CouponPayloads: []*cmsPb.CreateCouponsInBulkRequest_CouponPayload{
						{
							SkuId:          "sku-id-1",
							InventoryTotal: 10,
							CouponDetails: &cmsPb.CouponDetails{
								KeyValuePairs: []*cmsPb.CouponDetails_KeyValuePair{
									{
										Key:   "key-1",
										Value: "",
									},
								},
								EncryptedDataKey: "dummyEncryptionKey",
								MasterKeyId:      "dummykmskeyid",
							},
						},
					},
				},
			},
			setupMocks: func(mockCouponDao *mockDao.MockCouponDao) {
			},
			want: &cmsPb.CreateCouponsInBulkResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("empty key value pair not allowed"),
			},
			wantErr: false,
		},
		{
			name: "should return error when dao call fails",
			args: args{
				ctx: context.Background(),
				req: createCouponsInBulkRequest,
			},
			setupMocks: func(mockCouponDao *mockDao.MockCouponDao) {
				mockCouponDao.EXPECT().CreateCouponsInBulk(context.Background(), couponsRequest).Return(nil, fmt.Errorf(""))
			},
			want: &cmsPb.CreateCouponsInBulkResponse{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("CreateCouponInBulk dao call has failed, err: %s", "")),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			ctr := gomock.NewController(t)
			defer ctr.Finish()
			mockCouponDao := mockDao.NewMockCouponDao(ctr)
			mockCmsDataCryptor := cryptor.NewMockCmsDataCryptorImpl()

			// setupMocks
			tt.setupMocks(mockCouponDao)

			cms := &CmsService{
				couponDao:      mockCouponDao,
				cmsDataCryptor: mockCmsDataCryptor,
			}
			got, err := cms.CreateCouponsInBulk(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateCouponsInBulk() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CreateCouponsInBulk() got = %v, want %v", got, tt.want)
			}
		})
	}
}
