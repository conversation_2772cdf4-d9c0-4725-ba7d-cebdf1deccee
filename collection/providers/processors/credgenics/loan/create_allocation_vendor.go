// nolint : govet
package loan

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/auth"
	collectionPb "github.com/epifi/gamma/api/collection"
	credit_report "github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/account/screening"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	profilePb "github.com/epifi/gamma/api/risk/profile"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	usersPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	vgCredgenicsPb "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	"github.com/epifi/gamma/collection/metrics"
	collectionUtils "github.com/epifi/gamma/collection/utils"
	address2 "github.com/epifi/gamma/pkg/address"
	gammanames "github.com/epifi/gamma/pkg/names"
	"github.com/epifi/gamma/vendorgateway/lending/lms/finflux/converters"
)

type RepaymentScheduleDerivedParams struct {
	TotalPaidAmount *moneyPb.Money
	PastRepayments  []*palPb.LoanInstallmentPayout
	TotalDefaults   int
}

// TODO: use generic names as a single account can be used for multiple loan programs
// each trust name corresponds to a different beneficiary account in credgenics
var VendorAndloanProgramToTrustName = map[commonvgpb.Vendor]map[palPb.LoanProgram]vgCredgenicsPb.LoanDetails_TrustName{
	commonvgpb.Vendor_LIQUILOANS: {
		palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:           vgCredgenicsPb.LoanDetails_TRUST_NAME_EARLY_SALARY,
		palPb.LoanProgram_LOAN_PROGRAM_STPL:                   vgCredgenicsPb.LoanDetails_TRUST_NAME_EARLY_SALARY,
		palPb.LoanProgram_LOAN_PROGRAM_FLDG:                   vgCredgenicsPb.LoanDetails_TRUST_NAME_FLDG_AND_ACQ_TO_LEND,
		palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:            vgCredgenicsPb.LoanDetails_TRUST_NAME_FLDG_AND_ACQ_TO_LEND,
		palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:             vgCredgenicsPb.LoanDetails_TRUST_NAME_FLDG_AND_ACQ_TO_LEND,
		palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:    vgCredgenicsPb.LoanDetails_TRUST_NAME_FLDG_AND_ACQ_TO_LEND,
		palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:          vgCredgenicsPb.LoanDetails_TRUST_NAME_EARLY_SALARY,
		palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:       vgCredgenicsPb.LoanDetails_TRUST_NAME_EARLY_SALARY,
		palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION: vgCredgenicsPb.LoanDetails_TRUST_NAME_FLDG_AND_ACQ_TO_LEND,
	},
	commonvgpb.Vendor_STOCK_GUARDIAN_LSP: {
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION: vgCredgenicsPb.LoanDetails_TRUST_NAME_STOCK_GUARDIANS_DISTRIBUTION_PL,
		palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:        vgCredgenicsPb.LoanDetails_TRUST_NAME_STOCK_GUARDIANS_DISTRIBUTION_PL,
	},
}

var vgVendorToDataSource = map[commonvgpb.Vendor]vgCredgenicsPb.DataSource{
	commonvgpb.Vendor_EXPERIAN: vgCredgenicsPb.DataSource_DATA_SOURCE_EXPERIAN,
	commonvgpb.Vendor_CIBIL:    vgCredgenicsPb.DataSource_DATA_SOURCE_CIBIL,
}

// nolint:gosec
const (
	experianDataSource        = "experian"
	epifiInternalDataSource   = "epifi_internal"
	primaryContactNumber      = "Primary Contact Number"
	alternateContactNumber    = "Alternate Contact Number"
	creditBureauContactNumber = "Credit Bureau Contact Number"
)

// TODO(mounish): clean up this function
// nolint: funlen,ineffassign
func (p *Provider) CreateAllocationAtCollectionVendor(ctx context.Context, accountId string, productVendor commonvgpb.Vendor) error {
	ctx = epificontext.WithOwnership(ctx, collectionUtils.GetDBOwnership(productVendor))
	lg := activity.GetLogger(ctx)

	loanVendor, err := collectionUtils.GetLoanVendorFromVendor(productVendor)
	if err != nil {
		lg.Error("error fetching lender from product vendor", zap.String("productVendor", productVendor.String()), zap.Error(err))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while getting loan vendor from product vendor, err: %v", err))
	}

	// get lead details from db.
	lead, getErr := p.leadDao.GetByVendorAndAccountId(ctx, p.GetCollectionVendor(), accountId)
	switch {
	case errors.Is(getErr, epifierrors.ErrRecordNotFound):
		lg.Error("no lead exists in db", zap.String(logger.ACCOUNT_ID, accountId))
		return errors.Wrap(epifierrors.ErrPermanent, "no lead found in db with given accountId")
	case getErr != nil:
		lg.Error("error fetching lead details from db", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(getErr))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error fetching lead details from db for given accountId, err: %v", err))
	}

	currentDate := datetimePkg.TimeToDateInLoc(p.time.Now(), datetimePkg.IST)

	// get allocation details from db.
	allocation, getErr := p.allocationDao.GetByLeadIdAndAllocationMonth(ctx, lead.GetId(), currentDate)
	switch {
	case errors.Is(getErr, epifierrors.ErrRecordNotFound):
		lg.Error("no allocation exists in db", zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.DATE, currentDate.String()))
		return errors.Wrap(epifierrors.ErrPermanent, "no allocation found in db")
	case getErr != nil:
		lg.Error("error fetching allocation from db", zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.DATE, currentDate.String()), zap.Error(getErr))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error fetching allocation from db, err: %v", getErr))

	}

	// if allocation is already created for current month, return and skip calling vendor
	if allocation.GetVendorStatus() == collectionPb.VendorStatus_VENDOR_STATUS_SUCCESS {
		lg.Info("current month allocation already created at vendor's end", zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		return nil
	}

	// get loan default details for creating allocation in CG.
	defaultDetailsRes, err := p.palClient.GetLoanDefaultDetails(ctx, &palPb.GetLoanDefaultDetailsRequest{
		LoanHeader:    &palPb.LoanHeader{Vendor: loanVendor},
		LoanAccountId: accountId,
	})
	if rpcErr := epifigrpc.RPCError(defaultDetailsRes, err); rpcErr != nil {
		lg.Error("palClient.GetLoanDefaultDetails rpc call failed", zap.Error(rpcErr), zap.String(logger.LOAN_ACCOUNT_ID, accountId), zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while getting loan default details, err: %v", rpcErr))
	}
	if defaultDetailsRes.GetForeclosureDetails() == nil {
		lg.Error("palClient.GetLoanDefaultDetails rpc response doesn't have foreclosure details", zap.String(logger.LOAN_ACCOUNT_ID, accountId), zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while getting foreclosure details from loan default details"))
	}

	loanAccount := defaultDetailsRes.GetLoanAccount()
	defaultDetails := defaultDetailsRes.GetDefaultDetails()
	derivedPayoutDetails, err := GetDerivedPayoutDetails(defaultDetailsRes.GetSchedule())
	if err != nil {
		lg.Error("error getting derived payout details", zap.Error(err), zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error getting derived payout details, err: %v", err))
	}

	// fetch user details for pushing to CG.
	userRes, err := p.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{ActorId: loanAccount.GetActorId()},
	})
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		lg.Error("usersClient.GetUser rpc call failed", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, loanAccount.GetActorId()), zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("usersClient.GetUser rpc call failed, err: %v", rpcErr))
	}

	userProfile := userRes.GetUser().GetProfile()
	chargesApplied := defaultDetails.GetChargesApplied()
	totalChargesApplied, err := collectionUtils.SumOfMoney(chargesApplied.GetLatePaymentInterest(), chargesApplied.GetBounceCharges(), chargesApplied.GetOtherCharges())
	if err != nil {
		lg.Error("error while calculating total charges applied for loan account", zap.Error(err), zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while calculating total charges applied for loan account, err: %v", err))
	}

	userName := gammanames.BestNameFromProfile(ctx, userProfile)
	if userName == nil {
		lg.Error("empty name found in user profile", zap.String(logger.ACTOR_ID_V2, loanAccount.GetActorId()), zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		return errors.Wrap(epifierrors.ErrTransient, "error while getting user name")
	}

	employmentType, incomeRange := p.getEmploymentDetails(ctx, loanAccount.GetActorId())
	trustName, ok := VendorAndloanProgramToTrustName[productVendor][loanAccount.GetLoanProgram()]
	if !ok {
		lg.Error("trust name not found for loan program and vendor", zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()), zap.String(logger.LOAN_PROGRAM, loanAccount.GetLoanProgram().String()))
		return errors.Wrap(epifierrors.ErrTransient, "trust name not found for loan program and vendor")
	}

	var customerAddress []*vgCredgenicsPb.CustomerAddress
	var customerContactDetails []*vgCredgenicsPb.CustomerContactDetails
	var customerEmailDetails []*vgCredgenicsPb.CustomerEmailDetails
	customerAddress = append(customerAddress, convertUserProfileAddressToCredgenicsVgCustomerAddresses(userProfile)...)
	customerContactDetails = append(customerContactDetails, &vgCredgenicsPb.CustomerContactDetails{
		CustomerContactNumber:     userProfile.GetPhoneNumber(),
		CustomerContactDataSource: vgCredgenicsPb.DataSource_DATA_SOURCE_EPIFI_INTERNAL,
		DataSource:                epifiInternalDataSource,
	})
	if defaultDetailsRes.GetContactDetails().GetAlternateContactNumber() != nil {
		customerContactDetails = append(customerContactDetails, &vgCredgenicsPb.CustomerContactDetails{
			CustomerContactNumber: defaultDetailsRes.GetContactDetails().GetAlternateContactNumber(),
			DataSource:            epifiInternalDataSource,
		})
	}
	customerEmailDetails = append(customerEmailDetails, &vgCredgenicsPb.CustomerEmailDetails{
		CustomerEmail:           userProfile.GetEmail(),
		CustomerEmailDataSource: vgCredgenicsPb.DataSource_DATA_SOURCE_EPIFI_INTERNAL,
		DataSource:              epifiInternalDataSource,
	})
	cgCustomerAddress, cgCustomerContactDetails, cgCustomerEmailDetails, err := p.fetchCustomerDetailsFromCreditBureauData(ctx, loanAccount.GetActorId())
	// pushing credit report data is done at best effort basis, any error encountered should be logged but should not block upload loan API call
	if err != nil {
		// since we do not want it to be blocking error, not returning error here.
		lg.Warn("failed to fetch email contact and address details", zap.Error(err), zap.String(logger.ALLOCATION_ID, allocation.GetId()), zap.String(logger.LEAD_ID, lead.GetId()))
	} else {
		customerAddress = append(customerAddress, cgCustomerAddress...)
		customerContactDetails = append(customerContactDetails, cgCustomerContactDetails...)
		customerEmailDetails = append(customerEmailDetails, cgCustomerEmailDetails...)
	}

	uploadLoanReq := &vgCredgenicsPb.UploadLoanRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CREDGENICS},
		Loan: &vgCredgenicsPb.LoanDetails{
			Id:   collectionUtils.GetVendorLoanIdentifier(lead),
			Type: vgCredgenicsPb.LoanType_LOAN_TYPE_PERSONAL_LOAN,
			Applicant: &vgCredgenicsPb.Applicant{
				User: &vgCredgenicsPb.User{
					Name:        userName.ToString(),
					Emails:      []string{userProfile.GetEmail()},
					Gender:      userProfile.GetKycGender(),
					DateOfBirth: userProfile.GetDateOfBirth(),
				},
				// addresses is immutable field created by credgenics, customer addresses is field created by us to pass data source and date related information
				Address:                 convertUserProfileAddressToCredgenicsVgAddresses(userProfile),
				CustomerEmailDetails:    customerEmailDetails,
				CustomerContactDetails:  customerContactDetails,
				CustomerAddresses:       customerAddress,
				ApplicantPhotoLink:      defaultDetailsRes.GetUserImageLink(),
				ApplicantContactDetails: getApplicantContactDetails(userRes.GetUser(), defaultDetailsRes, customerContactDetails),
			},
			TotalAmount:    loanAccount.GetLoanAmountInfo().GetLoanAmount(),
			TenureInMonths: uint32(loanAccount.GetDetails().GetTenureInMonths()),
			SanctionDate:   datetimePkg.TimestampToDateInLoc(loanAccount.GetCreatedAt(), datetimePkg.IST),
			EndDate:        loanAccount.GetMaturityDate(),
			InterestOnLoan: float32(loanAccount.GetDetails().GetInterestRate()),
			LoanProgram:    loanAccount.GetLoanProgram().String(),
			// as all the loans are unsecured currently
			SecurityType: vgCredgenicsPb.SecurityType_SECURITY_TYPE_UNSECURED,
			EmiAmount:    defaultDetailsRes.GetLoanInstallmentInfo().GetDetails().GetNextEmiAmount(),
			Defaults: []*vgCredgenicsPb.DefaultDetails{
				{
					AllocationMonth:       currentDate,
					LateFee:               totalChargesApplied,
					DateOfDefault:         defaultDetails.GetFirstDefaultDate(),
					ExpectedEmi:           defaultDetails.GetExpectedEmiAmount(),
					ClientAmountRecovered: defaultDetails.GetReceivedAmount(),
				},
			},
			// Disbursed amount along with fees
			DisbursedAmount: defaultDetailsRes.GetLoanAccount().GetLoanAmountInfo().GetDisbursedAmount(),
			// Total balance amount payable by the user
			OutstandingAmount: defaultDetailsRes.GetLoanAccount().GetLoanAmountInfo().GetOutstandingAmount(),
			// Total repaid amount
			RepaidAmount: derivedPayoutDetails.TotalPaidAmount,
			// Count of past defaults
			PastDefaultCount: uint32(derivedPayoutDetails.TotalDefaults),
			// Past repayments
			RepaymentHistory: convertPastRepaymentsToVendorFormat(derivedPayoutDetails.PastRepayments),
			LenderName:       productVendor.String(),
			// Ignoring error to not fail loan account creation at Credgenics
			IsRiskyUser:          p.isRiskyUser(ctx, loanAccount.GetActorId()),
			ActorId:              loanAccount.GetActorId(),
			CreditScore:          uint32(p.getCreditScore(ctx, loanAccount.GetActorId())),
			IsSalaryAccount:      p.getSalaryStatus(ctx, loanAccount.GetActorId()),
			PaymentLink:          p.getPaymentLinkByLoanHeader(loanAccount.GetLoanProgram(), productVendor),
			EmploymentStatus:     employmentType,
			IncomeRange:          incomeRange,
			VendorLoanIdentifier: loanAccount.GetAccountNumber(),
			LastAppActiveTime:    p.getAppActiveTime(ctx, loanAccount.GetActorId()),
			FreshdeskTicketIds:   p.getFreshdeskTickets(ctx, loanAccount.GetCreatedAt(), timestamp.New(p.time.Now()), loanAccount.GetActorId()),
			BankAccountDetails:   defaultDetailsRes.GetBankAccountDetails(),
			ForeclosureDetails: &vgCredgenicsPb.ForeclosureDetails{
				TotalOutstandingAmt:     defaultDetailsRes.GetForeclosureDetails().GetTotalOutstandingAmount(),
				PrincipalOutstandingAmt: defaultDetailsRes.GetForeclosureDetails().GetPrincipalOutstandingAmount(),
				InterestOutstandingAmt:  defaultDetailsRes.GetForeclosureDetails().GetInterestOutstandingAmount(),
				PenaltyAmt:              defaultDetailsRes.GetForeclosureDetails().GetPenaltyAmt(),
				FeesAmt:                 defaultDetailsRes.GetForeclosureDetails().GetFeesAmt(),
				OtherCharges:            defaultDetailsRes.GetForeclosureDetails().GetOtherCharges(),
			},
			TrustName: trustName,
		},
		ProductVendor: productVendor,
	}

	// if new definition for total claim amount is enabled that means we have to calculate total claim amount from LPI
	// rather than relying on total due amount available in GetLoanDefaultDetails API response
	// if this flag is set then we also need to calculate and populate fields like future emi in current allocation month and loan installment payouts
	if p.collectionWorkerConf.Allocation.IsTotalClaimAmtNewDefEnabled {
		totalClaimAmountFromRPS, tcaErr := p.getTotalClaimAmountFromRepaymentSchedule(defaultDetailsRes.GetSchedule(), currentDate)
		if tcaErr != nil {
			return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error while calculating total claim amount from loan installment payouts with error: %v", tcaErr))
		}
		uploadLoanReq.GetLoan().Defaults[0].TotalClaimAmount = totalClaimAmountFromRPS

		// futureEmiInCurrAllocMonth denotes additional outstanding amount which is added in total claim amount even before EMI was due
		// but EMI is in the same month as of allocation month
		futureEmiInCurrAllocMonth, futureEmiErr := p.getFutureEmiDueAmountInCurrentMonth(defaultDetailsRes.GetSchedule(), currentDate)
		if futureEmiErr != nil {
			return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error while calculating future emi in current allocation month with error: %v", futureEmiErr))
		}
		uploadLoanReq.GetLoan().Defaults[0].FutureEmiAmountInCurrentAllocationMonth = futureEmiInCurrAllocMonth

		// Pushing LPI data to give EMI level visibility to agents on CG
		uploadLoanReq.GetLoan().LoanInstallmentPayouts = convertToVgPayouts(getAllPayoutsTillCurrentMonthAndYear(defaultDetailsRes.GetSchedule(), currentDate))

	} else {
		uploadLoanReq.GetLoan().Defaults[0].TotalClaimAmount = defaultDetails.GetTotalDueAmount()
	}

	uploadLoanRes, err := p.vgCredgenicsClient.UploadLoan(ctx, uploadLoanReq)
	if rpcErr := epifigrpc.RPCError(uploadLoanRes, err); rpcErr != nil {
		lg.Error("vgCredgenicsClient.UploadLoan rpc call failed", zap.String(logger.LEAD_ID, lead.GetId()), zap.Error(rpcErr), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("vgCredgenicsClient.UploadLoan rpc call failed, err: %v", rpcErr))
	}

	// we have a case where upload loan API return success for allocation creation on credgenics but loan was not visible on their dashboard
	// making a call to get loan API after upload loan to check whether loan is present on credgenics or not
	// get loan API should return not found error if loan is not created
	// adding additional logging to debug and validate such cases
	// any error encountered here will be handled at best effort basis only
	getRes, getLoanErr := p.vgCredgenicsClient.GetLoan(ctx, &vgCredgenicsPb.GetLoanRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: p.GetCollectionVendor()},
		LoanId:        collectionUtils.GetVendorLoanIdentifier(lead),
		ProductVendor: productVendor,
	})
	if rpcErr := epifigrpc.RPCError(getRes, getLoanErr); rpcErr != nil {
		if getRes.GetStatus().IsRecordNotFound() {
			lg.Warn("Upload loan API gave success response but get loan call for same loan resulted in not found", zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		} else {
			lg.Warn("error while calling get loan API in create allocation at vendor activity", zap.Error(rpcErr), zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		}
	}

	// update lead and allocation creation after details are successful pushed to CG.
	if lead.GetState() != collectionPb.LeadState_LEAD_STATE_ACTIVE {
		_, updateErr := p.leadDao.UpdateById(ctx, lead.GetId(), &collectionPb.Lead{State: collectionPb.LeadState_LEAD_STATE_ACTIVE}, []collectionPb.LeadFieldMask{collectionPb.LeadFieldMask_LEAD_FIELD_MASK_STATE})
		if updateErr != nil {
			lg.Error("error updating lead to active state", zap.Error(updateErr), zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
			return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while updating lead, err: %v", updateErr))
		}
		metrics.RecordLeadStateUpdate(p.GetCollectionVendor().String(), collectionPb.LeadState_LEAD_STATE_ACTIVE.String())
	}

	_, updateErr := p.allocationDao.UpdateById(ctx, allocation.GetId(), &collectionPb.Allocation{VendorStatus: collectionPb.VendorStatus_VENDOR_STATUS_SUCCESS}, []collectionPb.AllocationFieldMask{collectionPb.AllocationFieldMask_ALLOCATION_FIELD_MASK_VENDOR_STATUS})
	if updateErr != nil {
		lg.Error("error updating allocation status in db", zap.Error(updateErr), zap.String(logger.LEAD_ID, lead.GetId()), zap.String(logger.ALLOCATION_ID, allocation.GetId()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while updating allocation status, err: %v", updateErr))
	}
	metrics.RecordAllocationVendorStatusUpdate(p.GetCollectionVendor().String(), collectionPb.VendorStatus_VENDOR_STATUS_SUCCESS.String())

	return nil
}

func convertUserProfileAddressToCredgenicsVgAddresses(profile *usersPb.Profile) []*vgCredgenicsPb.Address {
	var addresses []*vgCredgenicsPb.Address
	for _, address := range profile.GetAddresses() {
		addresses = append(addresses, &vgCredgenicsPb.Address{
			Type:    vgCredgenicsPb.AddressType_ADDRESS_TYPE_HOME,
			Text:    strings.Join(address.GetAddressLines(), ", "),
			State:   address.GetAdministrativeArea(),
			City:    address.GetLocality(),
			PinCode: address.GetPostalCode(),
		})
	}
	return addresses
}

func convertUserProfileAddressToCredgenicsVgCustomerAddresses(profile *usersPb.Profile) []*vgCredgenicsPb.CustomerAddress {
	var customerAddress []*vgCredgenicsPb.CustomerAddress
	for _, userAddress := range convertUserProfileAddressToCredgenicsVgAddresses(profile) {
		customerAddress = append(customerAddress, &vgCredgenicsPb.CustomerAddress{
			CustomerAddressType: vgCredgenicsPb.AddressType_ADDRESS_TYPE_HOME.String(),
			CustomerAddressText: userAddress.GetText(),
			CustomerState:       userAddress.GetState(),
			CustomerCity:        userAddress.GetCity(),
			AddressPinCode:      userAddress.GetPinCode(),
			AddressDataSource:   vgCredgenicsPb.DataSource_DATA_SOURCE_EPIFI_INTERNAL,
			DataSource:          epifiInternalDataSource,
		})
	}
	return customerAddress
}

func GetDerivedPayoutDetails(payouts []*palPb.LoanInstallmentPayout) (*RepaymentScheduleDerivedParams, error) {
	totalPaidAmount := moneyPkg.ZeroINR().GetPb()
	var pastRepayments []*palPb.LoanInstallmentPayout
	var (
		defaults = 0
		err      error
	)

	for _, payout := range payouts {
		totalPaidAmount, err = moneyPkg.Sum(totalPaidAmount, payout.GetAmount())
		if err != nil {
			return nil, err
		}

		// To include all the past installments
		if datetimePkg.IsDateBeforeTodayInLoc(payout.GetDueDate(), datetimePkg.IST) {
			if len(datetimePkg.GetDatesBetween(payout.GetDueDate(), datetimePkg.TimeToDateInLoc(time.Now(), datetimePkg.IST))) >= 28 {
				pastRepayments = append(pastRepayments, payout)
			}
		}
		if datetimePkg.IsDateAfter(payout.GetPayoutDate(), payout.GetDueDate()) {
			defaults++
		}
	}

	return &RepaymentScheduleDerivedParams{
		TotalPaidAmount: totalPaidAmount,
		PastRepayments:  pastRepayments,
		TotalDefaults:   defaults,
	}, nil
}

func convertPastRepaymentsToVendorFormat(payouts []*palPb.LoanInstallmentPayout) []*vgCredgenicsPb.RepaymentHistory {
	var pastRepayments []*vgCredgenicsPb.RepaymentHistory
	// Dpd will days between due date and payout date
	for _, payout := range payouts {
		dpd := int32(0)
		if payout.GetStatus() == palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS {
			dpd = getDpd(payout.GetDueDate(), payout.GetPayoutDate())
		} else {
			dpd = getDpd(payout.GetDueDate(), datetimePkg.TimeToDateInLoc(time.Now(), datetimePkg.IST))
		}
		pastRepayments = append(pastRepayments, &vgCredgenicsPb.RepaymentHistory{
			PayoutDate:   payout.GetPayoutDate(),
			RepaidAmount: payout.GetAmount(),
			Dpd:          dpd,
		})
	}
	return pastRepayments
}

func getDpd(startDate, endDate *date.Date) int32 {
	if startDate == nil || endDate == nil {
		return 0
	}
	if datetimePkg.IsDateAfter(endDate, startDate) {
		return int32(len(datetimePkg.GetDatesBetween(startDate, endDate)) - 1)
	}
	return 0
}

// this method tries to identify if this is a risky user using freeze status attribute from user profile
// if we encounter error while identifying riskiness then errors are handled at
// best effort basis where errors will be logged and not returned to the caller since this operation should not block lead creation on credgenics
func (p *Provider) isRiskyUser(ctx context.Context, actorId string) commontypes.BooleanEnum {
	lg := activity.GetLogger(ctx)
	isRiskyUser := commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED
	profileRes, err := p.profileClient.GetUserProfile(ctx, &profilePb.GetUserProfileRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(profileRes, err); err != nil {
		// Intentionally not failing in case of rpc failures
		lg.Error("failed to get userProfile info for Collections flow", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return isRiskyUser
	}
	if len(profileRes.GetAccountsInfo()) > 0 {
		isRiskyUser = commontypes.BoolToBooleanEnum(profileRes.GetAccountsInfo()[0].GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetPresentFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetImpendingFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetImpendingFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE ||
			profileRes.GetAccountsInfo()[0].GetImpendingFreezeStatus() == riskEnumsPb.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE)
	}
	return isRiskyUser
}

func (p *Provider) getCreditScore(ctx context.Context, actorId string) int32 {
	lg := activity.GetLogger(ctx)
	res, err := p.creditReportManagerClient.GetCreditReport(ctx, &credit_report.GetCreditReportRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(res, err); err != nil {
		lg.Error("failed to get credit report from credit report service in collections flow", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	if res.GetPresenceStatus() != credit_report.PresenceStatus_REPORT_PRESENCE_STATUS_PRESENT {
		return 0
	}
	return res.GetCreditReportData().GetCreditScore()
}

func (p *Provider) getSalaryStatus(ctx context.Context, actorId string) commontypes.BooleanEnum {
	lg := activity.GetLogger(ctx)
	salRes, err := p.salClient.GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actorId, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE})
	if err = epifigrpc.RPCError(salRes, err); err != nil {
		if salRes.GetStatus().IsRecordNotFound() {
			return commontypes.BooleanEnum_FALSE
		}
		lg.Error("failed to get salary registration status", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED
	}

	if salRes.GetRegistrationStatus() == salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		return commontypes.BooleanEnum_TRUE
	}
	return commontypes.BooleanEnum_FALSE
}

// TODO(Anupam): Use only one link that corresponds to a prepay screen for all loan programs and remove program specific handling from here
func (p *Provider) getPaymentLinkByLoanHeader(program palPb.LoanProgram, vendor commonvgpb.Vendor) string {
	switch {
	case vendor == commonvgpb.Vendor_LIQUILOANS && program == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return "https://fi.onelink.me/GvZH/7aji0qfx"
	case vendor == commonvgpb.Vendor_LIQUILOANS && program == palPb.LoanProgram_LOAN_PROGRAM_FLDG || program == palPb.LoanProgram_LOAN_PROGRAM_STPL || program == palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION || program == palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL ||
		(vendor == commonvgpb.Vendor_STOCK_GUARDIAN_LSP && (program == palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION || program == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2)):
		return "https://fi.onelink.me/GvZH/lnxrlql6"
	case vendor == commonvgpb.Vendor_LIQUILOANS && (program == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND || program == palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL ||
		program == palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL || program == palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION):
		return "https://fi.onelink.me/GvZH/ogydsee5"
	}
	return ""
}

func (p *Provider) getEmploymentDetails(ctx context.Context, actorId string) (employment.EmploymentType, *screening.AnnualSalaryRange) {
	lg := activity.GetLogger(ctx)
	empInfoRes, err := p.empClient.GetEmploymentInfo(ctx, &employment.GetEmploymentInfoRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(empInfoRes, err); err != nil {
		lg.Error("failed to get employment info in collections flow", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return employment.EmploymentType_EMPLOYMENT_TYPE_UNSPECIFIED, nil
	}
	return empInfoRes.GetEmploymentData().GetEmploymentType(), empInfoRes.GetEmploymentData().GetEmploymentInfo().GetAnnualSalary().GetRange()
}

func (p *Provider) getAppActiveTime(ctx context.Context, actorId string) *timestamp.Timestamp {
	lg := activity.GetLogger(ctx)
	// sign-out from app based on activity
	tokenRes, err := p.authClient.GetTokenDetails(ctx, &auth.GetTokenDetailsRequest{
		IdentifierOneof: &auth.GetTokenDetailsRequest_ActorId{
			ActorId: actorId,
		},
		TokenTypes: []auth.TokenType{
			auth.TokenType_ACCESS_TOKEN,
		},
		Limit:          1,
		IncludeDeleted: false,
	})
	if err = epifigrpc.RPCError(tokenRes, err); err != nil {
		lg.Error("failed to get app active time from token store", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil
	}

	if len(tokenRes.GetTokenDetails()) == 0 {
		return nil
	}
	return tokenRes.GetTokenDetails()[0].GetLastActivity()
}

func (p *Provider) getFreshdeskTickets(ctx context.Context, fromTime, toTime *timestamp.Timestamp, actorId string) []string {
	lg := activity.GetLogger(ctx)
	// TODO (Diparth): Call this API under paginated block
	cxRes, err := p.cxTicketClient.GetSupportTickets(ctx, &ticket.GetSupportTicketsRequest{
		TicketFilters: &ticket.TicketFilters{
			FromTime:    fromTime,
			ToTime:      toTime,
			ActorIdList: []string{actorId},
		},
	})
	if err = epifigrpc.RPCError(cxRes, err); err != nil {
		lg.Error("failed to get support tickets from BE in collections flow", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil
	}
	var ticketIds []string
	for _, ticket := range cxRes.GetTickets() {
		ticketIds = append(ticketIds, strconv.FormatInt(ticket.GetId(), 10))
	}
	return ticketIds
}

// Not returning error in case some entry fails to append so that rest of entries can still be appended.
//
//nolint:funlen
func (p *Provider) fetchCustomerDetailsFromCreditBureauData(ctx context.Context, actorId string) ([]*vgCredgenicsPb.CustomerAddress, []*vgCredgenicsPb.CustomerContactDetails, []*vgCredgenicsPb.CustomerEmailDetails, error) {
	lg := activity.GetLogger(ctx)
	// fetch details from credit report
	getCreditReportResp, err := p.creditReportManagerClient.GetCreditReport(ctx, &credit_report.GetCreditReportRequest{
		ActorId: actorId,
	})
	var customerAddress []*vgCredgenicsPb.CustomerAddress
	var customerContactDetails []*vgCredgenicsPb.CustomerContactDetails
	var customerEmailDetails []*vgCredgenicsPb.CustomerEmailDetails
	if te := epifigrpc.RPCError(getCreditReportResp, err); te != nil {
		return nil, nil, nil, errors.Wrap(te, fmt.Sprintf("failed to fetch credit report for the given actor: %v", actorId))
	}
	for _, address := range getCreditReportResp.GetCreditReportData().GetAddress() {
		postalAddress := &postaladdress.PostalAddress{
			AddressLines: []string{
				address.GetFirstLineOfAddressNonNormalized(), address.GetSecondLineOfAddressNonNormalized(), address.GetThirdLineOfAddressNonNormalized(), address.GetFifthLineOfAddressNonNormalized(),
			},
			PostalCode:         address.GetZipPostalCodeNonNormalized(),
			RegionCode:         address.GetCountryCodeNonNormalized(),
			AdministrativeArea: address.GetStateNonNormalized(),
			Locality:           address.GetCityNonNormalized(),
		}
		customerAddress = append(customerAddress, &vgCredgenicsPb.CustomerAddress{
			CustomerAddressText: address2.ConvertPostalAddressToString(postalAddress),
			CustomerCity:        address.GetCityNonNormalized(),
			AddressPinCode:      address.GetZipPostalCodeNonNormalized(),
			CustomerState:       address.GetStateNonNormalized(),
			AddressDataSource:   vgVendorToDataSource[getCreditReportResp.GetCreditReport().GetVendor()],
			DataSource:          getCreditReportResp.GetCreditReport().GetVendor().String(),
			SubmittedDate:       datetimePkg.TimeToDateInLoc(getCreditReportResp.GetCreditReport().GetCreatedAt().AsTime(), datetimePkg.IST),
		})
	}
	for _, capsApplicationDetails := range getCreditReportResp.GetCreditReport().GetCreditReportDataRaw().GetCaps().GetCapsApplicationDetailsArray() {
		customerEmailDetails = append(customerEmailDetails, &vgCredgenicsPb.CustomerEmailDetails{
			CustomerEmail:           capsApplicationDetails.GetCapsApplicantDetails().GetEmailId(),
			CustomerEmailDataSource: vgVendorToDataSource[getCreditReportResp.GetCreditReport().GetVendor()],
			SubmittedDate:           datetimePkg.TimeToDateInLoc(getCreditReportResp.GetCreditReport().GetCreatedAt().AsTime(), datetimePkg.IST),
		})
		if capsApplicationDetails.GetCapsApplicantDetails().GetMobilePhoneNumber() == "" {
			lg.Warn("empty phone number in caps application details", zap.String(logger.ACTOR_ID_V2, actorId))
			continue
		}
		phone, err := commontypes.ParsePhoneNumber(capsApplicationDetails.GetCapsApplicantDetails().GetMobilePhoneNumber())
		if err != nil {
			lg.Warn("failed to parse phone number caps application details", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			continue
		}
		if phone.GetCountryCode() != converters.CountryCodeIndia {
			lg.Warn("invalid country code in caps application details", zap.String(logger.PHONE_NUMBER, mask.GetMaskedPhoneNumber(phone, "")), zap.String(logger.ACTOR_ID_V2, actorId))
			continue
		}
		customerContactDetails = append(customerContactDetails, &vgCredgenicsPb.CustomerContactDetails{
			CustomerContactNumber:     phone,
			CustomerContactDataSource: vgVendorToDataSource[getCreditReportResp.GetCreditReport().GetVendor()],
			SubmittedDate:             datetimePkg.TimeToDateInLoc(getCreditReportResp.GetCreditReport().GetCreatedAt().AsTime(), datetimePkg.IST),
		})
	}
	// fetch details from experian analytics
	expAnalyticsResp, err := p.creditReportManagerClient.GetExperianAnalyticsDetails(ctx, &credit_report.GetExperianAnalyticsDetailsRequest{
		ActorId: actorId,
		FieldMaskList: []credit_report.ExperianAnalyticsDetailsFieldMask{
			credit_report.ExperianAnalyticsDetailsFieldMask_EXPERIAN_ANALYTICS_DETAILS_FIELD_MASK_RPT_ADDRESS,
			credit_report.ExperianAnalyticsDetailsFieldMask_EXPERIAN_ANALYTICS_DETAILS_FIELD_MASK_RPT_EMAIL,
			credit_report.ExperianAnalyticsDetailsFieldMask_EXPERIAN_ANALYTICS_DETAILS_FIELD_MASK_RPT_PHONE,
		},
	})
	if te := epifigrpc.RPCError(expAnalyticsResp, err); te != nil {
		lg.Error("failed to fetch exp analytics reports", zap.Error(te))
	} else {
		for _, expAddress := range expAnalyticsResp.GetReportAddresses() {
			customerAddress = append(customerAddress, &vgCredgenicsPb.CustomerAddress{
				CustomerAddressText: expAddress.GetAddress(),
				AddressPinCode:      expAddress.GetPinCode(),
				DataSource:          experianDataSource,
				SubmittedDate:       expAddress.GetDateReported(),
			})
		}
		for _, expPhone := range expAnalyticsResp.GetReportPhones() {
			customerContactDetails = append(customerContactDetails, &vgCredgenicsPb.CustomerContactDetails{
				CustomerContactNumber: expPhone.GetPhone(),
				DataSource:            experianDataSource,
				SubmittedDate:         expPhone.GetDateReported(),
			})
		}
		for _, expEmail := range expAnalyticsResp.GetReportEmails() {
			customerEmailDetails = append(customerEmailDetails, &vgCredgenicsPb.CustomerEmailDetails{
				CustomerEmail: expEmail.GetEmail(),
				DataSource:    experianDataSource,
				SubmittedDate: expEmail.GetDateReported(),
			})
		}
	}
	return customerAddress, customerContactDetails, customerEmailDetails, nil
}

// getTotalClaimAmountFromRepaymentSchedule: calculates total claim amount from LPI for all overdue EMIs
// total claim amount is calculated as sum of outstanding amount [DUE AMT- PAID AMT] at an EMI level
// How an EMI is identified as overdue or default?
//   - If due date of an EMI is already surpassed and payment status is not success
//   - If a due falls in the future, and it is in same allocation month and year and payment status is not success then that EMI is also considered as a default EMI
func (p *Provider) getTotalClaimAmountFromRepaymentSchedule(payouts []*palPb.LoanInstallmentPayout, allocationDate *date.Date) (*moneyPb.Money, error) {
	totalClaimAmount := moneyPkg.ZeroINR().GetPb()
	for _, payout := range payouts {
		if p.isDefaultedPayout(payout, allocationDate) || isPayoutInCurrentMonth(payout, allocationDate) {
			outStandingAmt, err := moneyPkg.Subtract(payout.GetDueAmount(), payout.GetAmount())
			if err != nil {
				return nil, errors.Wrap(err, "error while subtracting due amount and paid amount")
			}
			totalClaimAmount, err = moneyPkg.Sum(totalClaimAmount, outStandingAmt)
			if err != nil {
				return nil, errors.Wrap(err, "error while adding total claim amount and outstanding amount")
			}
		}
	}
	return totalClaimAmount, nil
}

func (p *Provider) isDefaultedPayout(payout *palPb.LoanInstallmentPayout, allocationDate *date.Date) bool {
	dueDate := payout.GetDueDate()
	if datetimePkg.IsDateAfter(dueDate, allocationDate) {
		return false
	}
	if payout.GetStatus() == palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING || payout.GetStatus() == palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID {
		return true
	}
	return false
}

func isPayoutInCurrentMonth(payout *palPb.LoanInstallmentPayout, currentDate *date.Date) bool {
	dueDate := payout.GetDueDate()
	return (dueDate.GetMonth() == currentDate.GetMonth()) && (dueDate.GetYear() == currentDate.GetYear())
}

// getFutureEmiDueAmountInCurrentMonth: calculates if there's any future EMI which is added to total claim amount
func (p *Provider) getFutureEmiDueAmountInCurrentMonth(payouts []*palPb.LoanInstallmentPayout, currentDate *date.Date) (*moneyPb.Money, error) {
	futureEmiDue := moneyPkg.ZeroINR().GetPb()
	var err error
	for _, payout := range payouts {
		// if allocations are created on 1st of the month then it would satisfy both the conditions:
		// - isPayoutInCurrentMonth: An EMI exists in current month
		// - due date is also after 1st [Example: LL loans has 5th as due date]
		// so in this case current month outstanding EMI would be returned
		// if allocations are created (after NACH execution) or updated (via sync lead) after due date then below code block
		// would never be true and this method will return zero as value
		if isPayoutInCurrentMonth(payout, currentDate) && datetimePkg.IsDateAfter(payout.GetDueDate(), currentDate) {
			futureEmiDue, err = moneyPkg.Subtract(payout.GetDueAmount(), payout.GetAmount())
			if err != nil {
				return nil, errors.Wrap(err, "error while subtracting due amt and paid amt")
			}
			return futureEmiDue, nil
		}
	}
	return futureEmiDue, nil
}

// getAllPayoutsTillCurrentMonthAndYear: returns all payouts from start of the loan till current allocation month
func getAllPayoutsTillCurrentMonthAndYear(payouts []*palPb.LoanInstallmentPayout, currDate *date.Date) []*palPb.LoanInstallmentPayout {
	var resPayout []*palPb.LoanInstallmentPayout
	for _, payout := range payouts {
		if datetimePkg.IsDateAfter(currDate, payout.GetDueDate()) || isPayoutInCurrentMonth(payout, currDate) {
			resPayout = append(resPayout, payout)
		}
	}
	// sort payout in ascending order of due date
	sort.Slice(resPayout, func(i, j int) bool {
		return datetimePkg.IsDateAfter(resPayout[j].GetDueDate(), resPayout[i].GetDueDate())
	})
	return resPayout
}

func convertToVgPayouts(payouts []*palPb.LoanInstallmentPayout) []*vgCredgenicsPb.LoanInstallmentPayout {
	var vgPayouts []*vgCredgenicsPb.LoanInstallmentPayout
	for _, payout := range payouts {
		vgPayouts = append(vgPayouts, &vgCredgenicsPb.LoanInstallmentPayout{
			InstallmentDueDate:    payout.GetDueDate(),
			InstallmentDueAmount:  payout.GetDueAmount(),
			InstallmentPaidAmount: payout.GetAmount(),
			InstallmentPaidDate:   payout.GetPayoutDate(),
			InstallmentStatus:     payout.GetStatus().String(),
		})
	}
	return vgPayouts
}

func getApplicantContactDetails(user *usersPb.User, defaultDetailsResp *palPb.GetLoanDefaultDetailsResponse, bureauContactDetails []*vgCredgenicsPb.CustomerContactDetails) []*vgCredgenicsPb.ApplicantContactDetails {
	var applicantContactDetails []*vgCredgenicsPb.ApplicantContactDetails
	// appendedMobileNumbers keeps unique set of phone numbers which are already added to result list
	// this is to avoid duplicate contacts coming from credit report data
	appendedMobileNumbers := make(map[uint64]bool)
	// high priority contact will be contact coming from user profile
	// this contact should always be first in the list
	if user.GetProfile().GetPhoneNumber() != nil {
		applicantContactDetails = append(applicantContactDetails, &vgCredgenicsPb.ApplicantContactDetails{
			ApplicantContactNumber:     user.GetProfile().GetPhoneNumber(),
			ApplicantContactNumberType: primaryContactNumber,
		})
		appendedMobileNumbers[user.GetProfile().GetPhoneNumber().GetNationalNumber()] = true
	}

	// second priority contact in list should be alternate contact detail shared by the customer
	if defaultDetailsResp.GetContactDetails().GetAlternateContactNumber() != nil {
		applicantContactDetails = append(applicantContactDetails, &vgCredgenicsPb.ApplicantContactDetails{
			ApplicantContactNumber:     defaultDetailsResp.GetContactDetails().GetAlternateContactNumber(),
			ApplicantContactNumberType: alternateContactNumber,
		})
		appendedMobileNumbers[defaultDetailsResp.GetContactDetails().GetAlternateContactNumber().GetNationalNumber()] = true
	}

	uniqueBureauContactCount := 1
	for _, contactDetail := range bureauContactDetails {
		contactNumber := contactDetail.GetCustomerContactNumber()
		// if given contact is already added earlier then skip adding that contact in list back
		_, isAlreadyPresent := appendedMobileNumbers[contactNumber.GetNationalNumber()]
		if isAlreadyPresent {
			continue
		}
		// identify data source ex: Experian, Cibil, etc
		dataSource := contactDetail.GetDataSource()
		if dataSource == "" {
			dataSource = contactDetail.GetCustomerContactDataSource().String()
		}
		applicantContactDetails = append(applicantContactDetails, &credgenics.ApplicantContactDetails{
			ApplicantContactNumber: contactDetail.GetCustomerContactNumber(),
			// this will generate unique contact type string for that contact
			// this is done so because credgenics does not allow same contact type for two different numbers
			// example values:
			// Credit Bureau Contact Number - EXPERIAN - 1
			// Credit Bureau Contact Number - EXPERIAN - 2
			// Credit Bureau Contact Number - CIBIL - 3
			ApplicantContactNumberType: fmt.Sprintf("%s - %s %d", creditBureauContactNumber, dataSource, uniqueBureauContactCount),
		})
		appendedMobileNumbers[contactDetail.GetCustomerContactNumber().GetNationalNumber()] = true
		uniqueBureauContactCount++
	}
	return applicantContactDetails
}
