Flags:
  TrimDebugMessageFromStatus: true

InternationalFundTransfer:
  EnableLRSCheckFromVendor: true

Payout:
  MaxNumberOfPayoutsAllowedPerTicket: 1
  CashPayout:
    MaxPayoutValueAllowedPerTicket: 1000
    MinPayoutValueAllowedPerTicket: 1
    SingleAmountLimitForApproval: 5000
    MaxPayoutAllowedInTimeframe: 1000
    TimeframeDuration: "2160h"
    FromActorIdForOrder: "actor-epifi-business-account"
    FromPiIdForOrder: "paymentinstrument-epifi-business-account"
    EnableB2CTransactionViaCelestial: true
  FiCoinsPayout:
    MaxPayoutValueAllowedPerTicket: 5000
    SingleAmountLimitForApproval: 2501
    MaxAmountDisbursalInDurationByRole:
      AGENT:
        PAYOUT_CONSTRAINT_TIMEFRAME_MONTH:
          MaxAmountAllowedToBeDisbursed: 100000
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_WEEK:
          MaxAmountAllowedToBeDisbursed: 30000
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_DAY:
          MaxAmountAllowedToBeDisbursed: 10000
          MaxAmountBreachFallbackAction: "ESCALATE"
      ADMIN:
        PAYOUT_CONSTRAINT_TIMEFRAME_MONTH:
          MaxAmountAllowedToBeDisbursed: 5000
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_WEEK:
          MaxAmountAllowedToBeDisbursed: 1500
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_DAY:
          MaxAmountAllowedToBeDisbursed: 600
          MaxAmountBreachFallbackAction: "ESCALATE"
      SUPER_ADMIN:
        PAYOUT_CONSTRAINT_TIMEFRAME_MONTH:
          MaxAmountAllowedToBeDisbursed: 2500000
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_WEEK:
          MaxAmountAllowedToBeDisbursed: 1500000
          MaxAmountBreachFallbackAction: "ESCALATE"
        PAYOUT_CONSTRAINT_TIMEFRAME_DAY:
          MaxAmountAllowedToBeDisbursed: 50000
          MaxAmountBreachFallbackAction: "ESCALATE"
    MaxAmountDisbursalPerUser:
      MaxAmountAllowedToBeDisbursed: 10000
      MaxAmountBreachFallbackAction: "ESCALATE"
    IsPayoutViaFiCoinsEnabled: true
    FiCoinsRewardOfferId: "17ca1e93-a0f0-4a47-a915-fb273e2a213a"
    AllowedValues:
      - 500
      - 1000
      - 1500
      - 2000
      - 5000
      - 10000

BulkAccValidationViaEmailConfig:
  FromEmailId: "<EMAIL>"
  FromEmailName: "Bulk Account Validations"
  ToEmailId: "<EMAIL>"
  ToEmailName: "Account Closure Settlements"

VendorAccountPennyDropViaEmailConfig:
  FromEmailId: "<EMAIL>"
  FromEmailName: "Vendor Account Penny Drop Verification"
  ToEmailId: "<EMAIL>"
  ToEmailName: "Vendor Account Verification"

DisputeNotificationTemplates:
  UPI:
    P2P:
      PENDING:
        SUCCESS:
          ACCEPTED:
            Title: "UPI payment status update"
            Template: "Your payment to {#to_vpa#} for INR {#amount#} is successful and received by the beneficiary"
        FAILED:
          ACCEPTED:
            Title: "UPI payment status update"
            Template: "Your payment to {#to_vpa#} for INR {#amount#} has failed and reversed back to your account"
      SUCCESS:
        SUCCESS:
          REJECTED:
            Title: "UPI payment status update"
            Template: "Your request for refund for payment done to {#to_vpa#} for INR {#amount#} has been rejected"
        REVERSED:
          ACCEPTED:
            Title: "UPI payment status update"
            Template: "Your payment to {#to_vpa#} for INR {#amount#} has been reversed"
    P2M:
      PENDING:
        SUCCESS:
          ACCEPTED:
            Title: "UPI payment status update"
            Template: "Your payment to {#to_vpa#} for INR {#amount#} is successful and received by the beneficiary"
        FAILED:
          ACCEPTED:
            Title: "UPI payment status update"
            Template: "Your payment to {#to_vpa#} for INR {#amount#} has failed and reversed back to your account"
      SUCCESS:
        SUCCESS:
          REJECTED:
            Title: "UPI payment status update"
            Template: "Your request for refund for payment done to {#to_vpa#} for INR {#amount#} has been rejected"
        REVERSED:
          ACCEPTED:
            Title: "UPI payment status update"
            Template: "Your payment to {#to_vpa#} for INR {#amount#} has been reversed by the merchant"
  IMPS:
    NA:
      PENDING:
        SUCCESS:
          ACCEPTED:
            Title: "IMPS payment status update"
            Template: "Your payment to <to_masked_acc_no> for INR <amount> is successful and received by the beneficiary"
        FAILED:
          ACCEPTED:
            Title: "IMPS payment status update"
            Template: "Your payment to <to_masked_acc_no> for INR <amount> has failed and reversed back to your account"
  NEFT:
    NA:
      PENDING:
        SUCCESS:
          ACCEPTED:
            Title: "NEFT payment status update"
            Template: "Your payment to <to_masked_acc_no> for INR <amount> is successful and received by the beneficiary"
        FAILED:
          ACCEPTED:
            Title: "NEFT payment status update"
            Template: "Your payment to <to_masked_acc_no> for INR <amount> has failed and reversed back to your account"
  DEBIT_CARD_ECOM:
    NA:
      PENDING:
        SUCCESS:
          ACCEPTED:
            Title: "Debitcard payment status update"
            Template: "Your payment to <merchant_name> for INR <amount> is successful and received by the beneficiary"
        FAILED:
          ACCEPTED:
            Title: "Debitcard payment status update"
            Template: "Your payment to <merchant_name> for INR <amount> has failed and reversed back to your account"
      SUCCESS:
        SUCCESS:
          REJECTED:
            Title: "Debitcard payment status update"
            Template: "Your request for refund for payment done to <merchant_name> for INR <amount> has been rejected"
        REVERSED:
          ACCEPTED:
            Title: "Debitcard payment status update"
            Template: "Your request for refund for payment done to <merchant_name> for INR <amount> has been approved and credited in your account"
  DEBIT_CARD_POS:
    NA:
      PENDING:
        SUCCESS:
          ACCEPTED:
            Title: "Debitcard payment status update"
            Template: "Your payment to <merchant_name> for INR <amount> is successful and received by the beneficiary"
        FAILED:
          ACCEPTED:
            Title: "Debitcard payment status update"
            Template: "Your payment to <merchant_name> for INR <amount> has failed and reversed back to your account"
      SUCCESS:
        SUCCESS:
          REJECTED:
            Title: "Debitcard payment status update"
            Template: "Your request for refund for payment done to <merchant_name> for INR <amount> has been rejected"
        REVERSED:
          ACCEPTED:
            Title: "Debitcard payment status update"
            Template: "Your request for refund for payment done to <merchant_name> for INR <amount> has been approved and credited in your account"
  DEBIT_CARD_ATM:
    NA:
      SUCCESS:
        REVERSED:
          ACCEPTED:
            Title: "ATM status update"
            Template: "Your request for refund for transaction done at <ATM> for INR <amount> has been approved and credited in your account"


OnboardingStageDetailsMapping:
  TNC_CONSENT:
    Order: 2
    Description: "After email screen, user consents to TnC"
  DEDUPE_CHECK:
    Order: 11
    Description: "Happens on screen where user enters PAN and Date of birth"
  INITIATE_CKYC:
    Order: 12
    Description: "To fetch ckyc records based on PAN"
  MOTHER_FATHER_NAME:
    Order: 14
    Description: "Post mobile otp, email & PAN entering"
  KYC_AND_LIVENESS_COMPLETION:
    Order: 15
    Description: "Once ckyc/ekyc and liveness is completed. Refer KYC history to understand details around KYC"
  CONFIRM_CARD_MAILING_ADDRESS:
    Order: 16
    Description: "User confirms the card mailing address"
  UPI_CONSENT:
    Order: 17
    Description: "Implicit consent"
  DEVICE_REGISTRATION:
    Order: 18
    Description: "Register the mobile device of the user, an SMS is triggered in the background"
  CUSTOMER_CREATION:
    Order: 19
    Description: "Create a customer in partner bank's end, step in the background"
  ACCOUNT_CREATION:
    Order: 20
    Description: "Creation of user's saving account"
  CARD_CREATION:
    Order: 21
    Description: "Create the debit card for the user"
  UPI_SETUP:
    Order: 22
    Description: "UPI registration, step in the background"
  DEBIT_CARD_PIN_SETUP:
    Order: 23
    Description: "User sets the ATM PIN"
  ADD_MONEY:
    Order: 24
    Description: "User adds the funds in savings account"
  ONBOARDING_COMPLETE:
    Order: 25
    Description: "Onboarding is completed"
  REFERRAL_FINITE_CODE:
    Order: 1
    Description: "This stage allows a user to enter a finite code."
  CHECK_CREDIT_REPORT_PRESENCE:
    Order: 3
    Description: "This stage checks the presence of credit report of user"
  CONSENT_CREDIT_REPORT_DOWNLOAD:
    Order: 4
    Description: "Stage to take user consent to download credit report"
  CREDIT_REPORT_VERIFICATION:
    Order: 5
    Description: "Stage to Verify credit report of user."
  EMPLOYMENT_VERIFICATION:
    Order: 6
    Description: "Employment type declaration and verification. In verification, we try to establish we user is good to be given access based on employment related checks."
  MANDATE_CONSENT_CREDIT_REPORT:
    Order: 7
    Description: "User is mandated to give consent to download and verify the credit report. If there is no consent, user cannot go further."
  MANUAL_SCREENING:
    Order: 8
    Description: "User needs to go through manual employment verification. "
  APP_SCREENING:
    Order: 9
    Description: "The overall status of app screening will be maintained at this stage"


AppLogsNotificationContent:
  Title: "Share your Fi app's Health Logs 🛠️"
  Template: "{#first_name#}, Health Logs help us identify & fix technical issues. Tap 'Yes' to grant permission."

KYCConfig:
  SummaryInterpretationMap:
    VKYC_SUMMARY_STATUS_UNREGISTERED:
      VKYC_SUMMARY_SUB_STATUS_UNSPECIFIED: "Customer is eligible, they need to do VKYC from app - home or profile"
    VKYC_SUMMARY_STATUS_REGISTERED:
      VKYC_SUMMARY_SUB_STATUS_ATTEMPT_PENDING: "Customer has registered for VKYC(via tapping on CTA in app to enter the flow OR the registration can happen in background after doing EKYC), but yet to start the process, check call info and schedule status to get more information"
    VKYC_SUMMARY_STATUS_IN_PROGRESS:
      VKYC_SUMMARY_SUB_STATUS_ATTEMPT_IN_PROGRESS: "Call in progress or slot is scheduled"
    VKYC_SUMMARY_STATUS_APPROVED:
      VKYC_SUMMARY_SUB_STATUS_ATTEMPT_APPROVED: "VKYC was approved, user's KYC status should be full now and their account would be full account now"
    VKYC_SUMMARY_STATUS_REJECTED:
      VKYC_SUMMARY_SUB_STATUS_ATTEMPT_REJECTED: "VKYC was rejected by auditor, please contact product or backend team"
    VKYC_SUMMARY_STATUS_IN_REVIEW:
      VKYC_SUMMARY_SUB_STATUS_ATTEMPT_IN_REVIEW: "Call is completed and documents are in review"
    VKYC_SUMMARY_STATUS_RE_REGISTER:
      VKYC_SUMMARY_SUB_STATUS_ATTEMPT_FAILED: "Failure in call, can happen due to ekyc expiry as of now"
  CallInfoInterpretationMap:
    VKYC_KARZA_CALL_INFO_STATUS_PENDING:
      VKYC_KARZA_CALL_INFO_SUB_STATUS_UNSPECIFIED: "VKYC registered, call yet to happen"
    VKYC_KARZA_CALL_INFO_STATUS_IN_PROGRESS:
      VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_ARRIVED: "VKYC call is in progress, customer has joined the call"
      VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_INITIATED: "Both RM and customer have joined the call and RM started the call"
      VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_DISCONNECTED: "Customer got disconnected from the call"
      VKYC_KARZA_CALL_INFO_SUB_STATUS_CUSTOMER_RECONNECTED: "Customer reconnected to the call"
      VKYC_KARZA_CALL_INFO_SUB_STATUS_AGENT_DISCONNECTED: "Agent got disconnected from the call"
    VKYC_KARZA_CALL_INFO_STATUS_IN_REVIEW:
      VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_COMPLETED: "Call is completed"
      VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_ENDED: "After agent approves or rejects the documents and uploaded the documents"
    VKYC_KARZA_CALL_INFO_STATUS_APPROVED:
      VKYC_KARZA_CALL_INFO_SUB_STATUS_AUDITOR_APPROVED: "VKYC application is approved"
    VKYC_KARZA_CALL_INFO_STATUS_REJECTED:
      VKYC_KARZA_CALL_INFO_SUB_STATUS_AUDITOR_REJECTED: "VKYC application is rejected"
    VKYC_KARZA_CALL_INFO_STATUS_FAILED:
      VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_FAILED: "Call failed due to some issues at either customer's or agent's end"
      VKYC_KARZA_CALL_INFO_SUB_STATUS_AGENT_NOT_AVAILABLE: "Agent was not available for the call"
      VKYC_KARZA_CALL_INFO_SUB_STATUS_FAILED_EKYC_EXPIRED: "User's ekyc got expired"
      VKYC_KARZA_CALL_INFO_SUB_STATUS_INCOMPATIBLE_CUSTOMER_DEVICE: "User's device is in-compatible to start vkyc. We can provide user with weblink to try on different device but user's ekyc has to be within 3 days"

CxEventAmountCategories:
  - Name: "category_1"
    MinAmount: 1
    MaxAmount: 2000

  - Name: "category_2"
    MinAmount: 2001
    MaxAmount: 10000

  - Name: "category_3"
    MinAmount: 10001
    MaxAmount: 50000

  - Name: "category_4"
    MinAmount: 50001
    MaxAmount: 100000


ReferralConfig:
  RefereePageSize: 10

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/cx/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

Dispute:
  GeneratedCsvFolder: "disputes" #temporary folder to generate dispute csv files and upload to s3 bucket
  UPIReceiverTypeQuestionCode: "UPI-10"
  ConfigVersionToDisputeConfigMap:
    DISPUTE_CONFIG_VERSION_V1:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp.csv"
        DecisionTreeCSVPath: "mappingCsv/decision-tree.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        INTRA_BANK: true
    DISPUTE_CONFIG_VERSION_V2:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp.csv"
        DecisionTreeCSVPath: "mappingCsv/sherlock-decision-tree-v2.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree-v2.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        INTRA_BANK: true
    DISPUTE_CONFIG_VERSION_V3:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp.csv"
        DecisionTreeCSVPath: "mappingCsv/sherlock-decision-tree-v3.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree-v3.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        NEFT: true
        RTGS: true
        DEBIT_CARD_ATM: true
        DEBIT_CARD_POS: true
        DEBIT_CARD_ECOM: true
        CHEQUE: true
        INTRA_BANK: true
        CARD: true
    DISPUTE_CONFIG_VERSION_V4:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp.csv"
        DecisionTreeCSVPath: "mappingCsv/sherlock-decision-tree-v4.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree-v4.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        NEFT: true
        RTGS: true
        DEBIT_CARD_ATM: true
        DEBIT_CARD_POS: true
        DEBIT_CARD_ECOM: true
        CHEQUE: true
        INTRA_BANK: true
        CARD: true
    DISPUTE_CONFIG_VERSION_V5:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp.csv"
        DecisionTreeCSVPath: "mappingCsv/sherlock-decision-tree-v5.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree-v5.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        NEFT: true
        RTGS: true
        DEBIT_CARD_ATM: true
        DEBIT_CARD_POS: true
        DEBIT_CARD_ECOM: true
        CHEQUE: true
        INTRA_BANK: true
        CARD: true
    DISPUTE_CONFIG_VERSION_V6:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp.csv"
        DecisionTreeCSVPath: "mappingCsv/sherlock-decision-tree-v6.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree-v6.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        NEFT: true
        RTGS: true
        DEBIT_CARD_ATM: true
        DEBIT_CARD_POS: true
        DEBIT_CARD_ECOM: true
        CHEQUE: true
        INTRA_BANK: true
        CARD: true
    DISPUTE_CONFIG_VERSION_V7:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp.csv"
        DecisionTreeCSVPath: "mappingCsv/sherlock-decision-tree-v7.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree-v7.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        NEFT: true
        RTGS: true
        DEBIT_CARD_ATM: true
        DEBIT_CARD_POS: true
        DEBIT_CARD_ECOM: true
        CHEQUE: true
        INTRA_BANK: true
        CARD: true
  DisputeUDIRConfig:
    ComplaintActionToDisputeStateMapping:
      UDIR_COMPLAINT_RESPONSE_ACTION_UNSPECIFIED: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_REFUND_REVERSAL_CONFIRMATION: "RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_ACCEPTANCE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_ACCEPTANCE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_CONTINUATION: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_VERDICT: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_WITHDRAWN: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_ACCEPTANCE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_RAISE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_RAISE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_CREDIT_ADJUSTMENT: "RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DEBIT_REVERSAL_CONFIRMATION: "RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_ACCEPTANCE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_ACCEPTANCE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_ARBITRATION_RAISE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_RAISE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_RAISE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_ACCEPT: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_REPRESENTMENT: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_RAISE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_DECLINED: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_RE_PRESENTMENT_RAISE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_MANUAL_ADJUSTMENT: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_RAISE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_DECLINED: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_RESPONSE_TO_COMPLAINT: "RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_RE_PRESENTMENT_RAISE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_RET: "RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_TCC: "RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_ACCEPTANCE: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_REPRESENTMENT: "ESCALATED"
      UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_RAISE: "ESCALATED"
    AllowForceRefreshInApp: true
    DisputeStageDetailsMap:
      DISPUTE_STAGE_FOR_APP_COMPLAINT_RAISED:
        DisplayName: "Complaint raised"
        Rank: 1
      DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS:
        DisplayName: "Resolution in progress"
        Rank: 2
      DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED:
        DisplayName: "Complaint resolved"
        Rank: 3
    UdirComplaintActionToDisputeStageMap:
      UDIR_COMPLAINT_RESPONSE_ACTION_UNSPECIFIED: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RAISED"
      UDIR_COMPLAINT_RESPONSE_ACTION_REFUND_REVERSAL_CONFIRMATION: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_ACCEPTANCE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_ACCEPTANCE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_CONTINUATION: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_VERDICT: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_WITHDRAWN: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_ACCEPTANCE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_ARBITRATION_RAISE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_CHARGEBACK_RAISE: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RAISED"
      UDIR_COMPLAINT_RESPONSE_ACTION_CREDIT_ADJUSTMENT: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DEBIT_REVERSAL_CONFIRMATION: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_ACCEPTANCE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_ACCEPTANCE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_ARBITRATION_RAISE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_CHARGEBACK_RAISE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_RAISE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_ACCEPT: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_FRAUD_CHARGEBACK_REPRESENTMENT: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_RAISE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_PRE_ARBITRATION_DECLINED: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_DIFFERED_RE_PRESENTMENT_RAISE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_MANUAL_ADJUSTMENT: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_RAISE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_PRE_ARBITRATION_DECLINED: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_RESPONSE_TO_COMPLAINT: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_RE_PRESENTMENT_RAISE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_RET: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_TCC: "DISPUTE_STAGE_FOR_APP_COMPLAINT_RESOLVED"
      UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_ACCEPTANCE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_REPRESENTMENT: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
      UDIR_COMPLAINT_RESPONSE_ACTION_WRONG_CREDIT_CHARGEBACK_RAISE: "DISPUTE_STAGE_FOR_APP_RESOLUTION_IN_PROGRESS"
    # check this proto documentation to see what these values mean: https://github.com/epiFi/protos/blob/master/api/upi/service.proto#L1415
    DisputeSourceToInitiationModeMap:
      APP: "U1"
      SHERLOCK: "U4"
      SOURCE_UNSPECIFIED: "U1" # return U1 as default if source is not present in dispute
  IsIssueResolutionFeedbackCommsEnabledForDispute: false
  DisputeAppCopies:
    QuestionnaireScreenTitle: "Dispute Details"
    PreviewScreenTitle: "Dispute Details"
    OptionalAnswerPlaceholderTextMap:
      TEXT: "Add comment (optional)"
      DATE: "Select date (optional)"
    MandatoryAnswerPlaceholderTextMap:
      TEXT: "Add comment"
      DATE: "Select date"
    DisputeDetailsTitle: "Dispute Raised On %s"
    BottomSheetCopies:
      OkCTALabel: "OK"
      RaiseDisputeCTALabel: "RAISE DISPUTE"
      HelpCTALabel: "I NEED HELP"
      ReportFraudCTALabel: "REPORT FRAUD"
      TxnInCoolOff:
        Title: "Raising a dispute"
        Description: "Banking systems sometimes take an hour or two to settle a transaction.\n\n We request you to please wait for at least %s before raising a dispute for a recent transaction."
      TxnChannelNotAllowed:
        Title: "Raising a dispute"
        Description: "Before raising a dispute make sure that you check account statement for latest status of transaction. \n\n In case you still want to raise a dispute, please contact support."
      RaisingDisputeAllowed:
        Title: "Raising a dispute"
        Description: "Before raising a dispute make sure that you check account statement for latest status of transaction.\n\nAre you sure you want to raise a dispute for this transaction?"
      RaisingDisputeAllowedWithReportFraud:
        Title: "Raising a dispute"
        Description: "Before raising an issue, please check your account statement to track this payment’s current status.\n\nReport as fraud only if the UPI/ATM PIN or OTP for this payment was NOT entered by you.\n\nFor issues with payments where the UPI/ATM PIN or OTP were entered by you, please tap on “Raise dispute”.\n\nKindly do not share any credentials/OTP/Passwords to anyone. Please be vigilant about the account details to avoid fraud in future."
      DisputeAlreadyExists:
        Title: "Dispute already exists!"
        Description: "Our team is actively working to resolve an open dispute for this transaction and you will hear from us soon\n\n In case you need updates, feel free to contact support"
      ActorIsReceiver:
        Title: "Raising a dispute"
        Description: "This transaction seems successful. Please raise dispute with the bank from where transaction was done"
      RaisingDisputeNotAllowed:
        Title: "Raising a dispute"
        Description: "Raising dispute is not allowed for successful p2p transaction as per NPCI guidelines"
      OffAppTxn:
        Title: "Raise dispute"
        Description: "Please raise dispute with the application used to initiate this transaction"
  DisputeJobConfig:
    ReverseProcessingDelayDurationBetweenTickets: "6s"
    ReverseProcessingJobTimeout: "30m"
    EscalationJobTimeout: "60m"
  MaxPageSize: 30
  MaximumDaysDuration: 2160h
  IsGetNextQuestionsForAppV2Enabled: false
  IsGetNextQuestionsV2Enabled: false
  IsUpiExternalProvenanceEvaluationEnabled: false
  TicketAlreadyExistsErrMsg: "Ticket with ID: %d is already marked for given dispute"
  ConfigVersionValidityToPreviousAttemptStatusEvaluationMap:
    DISPUTE_CONFIG_VERSION_V7: true
    DISPUTE_CONFIG_VERSION_V6: true
    DISPUTE_CONFIG_VERSION_V5: true
    DISPUTE_CONFIG_VERSION_V4: false
    DISPUTE_CONFIG_VERSION_V3: false
  DmpEmailConfig:
    FromEmailId: "<EMAIL>"
    FromEmailName: "Fi care"

  # As per communication with Federal, raising disputes is allowed till a certain time window
  # this window is dependent on dispute type and transaction channel
  # here the values used are as per communication with federal
  # refer email thread or monorail attachment: DMP API Failure Scenarios
  DMPRaiseDisputeWindowConfig:
    DisputeTypeRaiseDisputeWindowConfig:
      Authorised:
        UPI: "1440h"
        IMPS: "1440h"
        DEBIT_CARD_ECOM: "2880h"
        DEBIT_CARD_POS: "2880h"
        DEBIT_CARD_ATM: "2880h"
        RECYCLER: "2880h"
      Unauthorised:
        UPI: "1440h"
        IMPS: "1440h"
        DEBIT_CARD_ECOM: "720h"
        DEBIT_CARD_POS: "720h"
        DEBIT_CARD_ATM: "720h"
  MinThresholdDurationForStatusCheck: "24h"
  DisputeDocumentLinkTagForSherlock: "<a href='%s' target='_blank' rel='noreferrer'>View</a>"
  DisputeIdempotencyTTL: "2m" # While Making API Calls to DMP we have our timeout set to 2 minutes. For idempotency we mark the dispute active for 2 minutes
  IssueCategoryEligibilityForIssueResolutionFeedback:
    5f606559-3cbc-5954-8e4c-057eed3706eb: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  -
    0388c59c-1417-5f90-98dc-40965ef73ebd: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  UPI - Failed
    926297bd-422c-5e05-be9b-69f53546ceee: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  UPI - Success
    119a7b99-0413-51f0-87e9-474e70471cb2: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  UPI - Pending
    de742880-176e-51a9-9d51-5602f29eb2a7: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  IMPS - Failed
    fc9a13d1-7596-5c18-b9c1-3bfed38484bf: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  IMPS - Success
    5072bec8-5689-50ec-9bbb-800ef7110a80: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  IMPS - Pending
    1c2b815d-ce0c-5808-865c-2dc353ce62f8: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  RTGS - Failed
    898cf834-717d-5a0d-a573-c4c84af2121d: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  RTGS - Success
    92ed5512-25c5-5bb3-97b5-cb145cbfcd43: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  RTGS - Pending
    a217fd64-4ac2-53e0-ab09-4d4cb5b727be: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  NEFT - Failed
    acadbeb0-ed8f-55d7-b97c-adccd8c265a3: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  NEFT - Success
    89b6f8d9-6efc-5386-8653-d9b8e65bbcfc: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  NEFT - Pending
    cd32c697-e929-55e8-9f1c-7a6fd297c9dd: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  Intrabank - Failed
    737bb4b6-f8f1-521a-9c17-f5957f564b5e: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  Intrabank - Success
    254173f4-4724-569a-9dc2-d64cfd2feb0d: true     # In-App Transactions | Amount debited but not credited to the beneficiary  |  Intrabank - Pending
    8476ab49-bad2-5281-8b5b-168e5821b09f: true     # In-App Transactions | Amount debited but not credited to merchant         |  -
    660b0f6c-18d4-574a-b01a-6bc7a5fe65d6: true     # In-App Transactions | Amount debited but not credited to merchant         |  UPI - Failed
    63d17e48-9267-5fa8-8718-21fe37c82bd1: true     # In-App Transactions | Amount debited but not credited to merchant         |  UPI - Success
    0d1212df-29ae-5cb0-9910-b890648cf587: true     # In-App Transactions | Amount debited but not credited to merchant         |  UPI - Pending
    3166a312-52c0-515f-86a9-021ad19fe0a2: true     # In-App Transactions | Amount debited but not credited to merchant         |  IMPS - Failed
    90d75be5-7232-5f2a-be62-cd514daed62b: true     # In-App Transactions | Amount debited but not credited to merchant         |  IMPS - Success
    6be9ddfd-5f0e-517f-aac2-9634754a61c9: true     # In-App Transactions | Amount debited but not credited to merchant         |  IMPS - Pending
    71a63c42-a49f-5872-be03-5d578c13a414: true     # In-App Transactions | Amount debited but not credited to merchant         |  RTGS - Failed
    bf156ba6-8079-58d1-8529-87dc41596ab2: true     # In-App Transactions | Amount debited but not credited to merchant         |  RTGS - Success
    2f1553df-c80b-547d-af4d-9e70b8a5ce38: true     # In-App Transactions | Amount debited but not credited to merchant         |  RTGS - Pending
    5464f580-daa6-5948-8b10-08d651284bdf: true     # In-App Transactions | Amount debited but not credited to merchant         |  NEFT - Failed
    ff812b9e-64f8-51b3-ad55-cb5c9c98e789: true     # In-App Transactions | Amount debited but not credited to merchant         |  NEFT - Success
    884a898c-385f-568c-b609-bc97f06df7f3: true     # In-App Transactions | Amount debited but not credited to merchant         |  NEFT - Pending
    bab02cdd-609d-563f-abfc-ffcaa5fb27fe: true     # In-App Transactions | Amount debited but not credited to merchant         |  Intrabank - Failed
    964ef29e-0e77-59a8-8b81-9e46e37d0a47: true     # In-App Transactions | Amount debited but not credited to merchant         |  Intrabank - Success
    ********-1d07-5770-b893-22bd1dc0a4fd: true     # In-App Transactions | Amount debited but not credited to merchant         |  Intrabank - Pending
    97ed7999-bae8-5958-afe6-c9d3a2216a33: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | -
    93feaa9f-53a8-552a-bfe4-007f21a74b2a: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | UPI - Failed
    dd41759d-71a3-52fc-9d81-af0e4aa03130: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | UPI - Success
    c88334d7-30a4-5123-8702-4811df940189: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | UPI - Pending
    d3d015f2-6b99-57fe-96d4-baf2e2163285: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | IMPS - Failed
    86a6c9db-a033-54c0-a16d-995f49164a19: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | IMPS - Success
    e6bbf2ee-bab6-5030-afea-4ebb041e0afe: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | IMPS - Pending
    80cd928a-344b-5735-b89c-127928196e93: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | RTGS - Failed
    ab94d612-9c62-5653-96c5-aadfe69e9542: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | RTGS - Success
    32d68a04-1742-5baf-a05a-a1d231601d0f: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | RTGS - Pending
    02ebc40c-d29a-557b-8076-221f847661e0: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | NEFT - Failed
    165e7547-6150-5698-8f1e-87636a2ab950: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | NEFT - Success
    d72ed064-a464-5254-a17d-50fcb3c174b4: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | NEFT - Pending
    0d3ee1cb-5e28-5efe-9aba-1d6a762e31c7: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | Intrabank - Failed
    098212b8-4e05-516e-8f02-e1d42582ef9c: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | Intrabank - Success
    63c00d5b-e87f-5c93-86d6-9c6329a1ab70: true     # Off-App Transactions | Amount debited but not credited to the beneficiary | Intrabank - Pending
    e8a9d99b-1415-59c8-915d-499d86e07767: true     # Off-App Transactions | Amount debited but not credited to merchant | -
    72391a78-e4f1-5769-bc6c-8c643d367171: true     # Off-App Transactions | Amount debited but not credited to merchant | UPI - Failed
    6db07557-22fb-51b8-8f4a-4338332d0caf: true     # Off-App Transactions | Amount debited but not credited to merchant | UPI - Success
    c1054e1b-9d7e-50de-837c-58d55a295174: true     # Off-App Transactions | Amount debited but not credited to merchant | UPI - Pending
    1eef0459-e0a2-502b-8f64-cfb72edb81e7: true     # Off-App Transactions | Amount debited but not credited to merchant | IMPS - Failed
    6cd41015-61d0-5333-9587-4a803ab9fb1b: true     # Off-App Transactions | Amount debited but not credited to merchant | IMPS - Success
    1c3c8618-aac2-5f11-ab24-48c92ef4677a: true     # Off-App Transactions | Amount debited but not credited to merchant | IMPS - Pending
    48d56569-1d5f-59ca-980a-69340bf99a49: true     # Off-App Transactions | Amount debited but not credited to merchant | RTGS - Failed
    700e37b1-0033-58f6-98bc-f04782647b7f: true     # Off-App Transactions | Amount debited but not credited to merchant | RTGS - Success
    a0c1d6fd-62fa-5c6a-a5be-b7147dfa3218: true     # Off-App Transactions | Amount debited but not credited to merchant | RTGS - Pending
    c73dab73-6339-5a8b-b69e-67fa06520548: true     # Off-App Transactions | Amount debited but not credited to merchant | NEFT - Failed
    6bffb48d-6389-52d3-addd-f012b690af93: true     # Off-App Transactions | Amount debited but not credited to merchant | NEFT - Success
    225bb815-efb1-509d-9304-07a0944edd46: true     # Off-App Transactions | Amount debited but not credited to merchant | NEFT - Pending
    c5d33d37-f7e4-5914-bcf3-f68492606a98: true     # Off-App Transactions | Amount debited but not credited to merchant | Intrabank - Failed
    ef5a924a-3ca8-5891-8e21-a9b93396fe54: true     # Off-App Transactions | Amount debited but not credited to merchant | Intrabank - Success
    1ba83acb-3c2f-5fb9-a7f0-62e1e49815f6: true     # Off-App Transactions | Amount debited but not credited to merchant | Intrabank - Pending
IssueResolutionFeedbackConfig:
  IsEnabled: true
  DisputeConfig:
    WaitDurationAfterFinalComms: 5m #for testing
    IssueCategoryIdToTransactionType:
      5f606559-3cbc-5954-8e4c-057eed3706eb: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  -
      0388c59c-1417-5f90-98dc-40965ef73ebd: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  UPI - Failed
      926297bd-422c-5e05-be9b-69f53546ceee: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  UPI - Success
      119a7b99-0413-51f0-87e9-474e70471cb2: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  UPI - Pending
      de742880-176e-51a9-9d51-5602f29eb2a7: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  IMPS - Failed
      fc9a13d1-7596-5c18-b9c1-3bfed38484bf: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  IMPS - Success
      5072bec8-5689-50ec-9bbb-800ef7110a80: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  IMPS - Pending
      1c2b815d-ce0c-5808-865c-2dc353ce62f8: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  RTGS - Failed
      898cf834-717d-5a0d-a573-c4c84af2121d: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  RTGS - Success
      92ed5512-25c5-5bb3-97b5-cb145cbfcd43: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  RTGS - Pending
      a217fd64-4ac2-53e0-ab09-4d4cb5b727be: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  NEFT - Failed
      acadbeb0-ed8f-55d7-b97c-adccd8c265a3: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  NEFT - Success
      89b6f8d9-6efc-5386-8653-d9b8e65bbcfc: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  NEFT - Pending
      cd32c697-e929-55e8-9f1c-7a6fd297c9dd: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  Intrabank - Failed
      737bb4b6-f8f1-521a-9c17-f5957f564b5e: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  Intrabank - Success
      254173f4-4724-569a-9dc2-d64cfd2feb0d: TRANSACTION_TYPE_P2P     # In-App Transactions | Amount debited but not credited to the beneficiary  |  Intrabank - Pending
      8476ab49-bad2-5281-8b5b-168e5821b09f: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  -
      660b0f6c-18d4-574a-b01a-6bc7a5fe65d6: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  UPI - Failed
      63d17e48-9267-5fa8-8718-21fe37c82bd1: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  UPI - Success
      0d1212df-29ae-5cb0-9910-b890648cf587: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  UPI - Pending
      3166a312-52c0-515f-86a9-021ad19fe0a2: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  IMPS - Failed
      90d75be5-7232-5f2a-be62-cd514daed62b: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  IMPS - Success
      6be9ddfd-5f0e-517f-aac2-9634754a61c9: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  IMPS - Pending
      71a63c42-a49f-5872-be03-5d578c13a414: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  RTGS - Failed
      bf156ba6-8079-58d1-8529-87dc41596ab2: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  RTGS - Success
      2f1553df-c80b-547d-af4d-9e70b8a5ce38: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  RTGS - Pending
      5464f580-daa6-5948-8b10-08d651284bdf: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  NEFT - Failed
      ff812b9e-64f8-51b3-ad55-cb5c9c98e789: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  NEFT - Success
      884a898c-385f-568c-b609-bc97f06df7f3: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  NEFT - Pending
      bab02cdd-609d-563f-abfc-ffcaa5fb27fe: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  Intrabank - Failed
      964ef29e-0e77-59a8-8b81-9e46e37d0a47: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  Intrabank - Success
      ********-1d07-5770-b893-22bd1dc0a4fd: TRANSACTION_TYPE_P2M     # In-App Transactions | Amount debited but not credited to merchant         |  Intrabank - Pending
      97ed7999-bae8-5958-afe6-c9d3a2216a33: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | -
      93feaa9f-53a8-552a-bfe4-007f21a74b2a: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | UPI - Failed
      dd41759d-71a3-52fc-9d81-af0e4aa03130: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | UPI - Success
      c88334d7-30a4-5123-8702-4811df940189: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | UPI - Pending
      d3d015f2-6b99-57fe-96d4-baf2e2163285: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | IMPS - Failed
      86a6c9db-a033-54c0-a16d-995f49164a19: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | IMPS - Success
      e6bbf2ee-bab6-5030-afea-4ebb041e0afe: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | IMPS - Pending
      80cd928a-344b-5735-b89c-127928196e93: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | RTGS - Failed
      ab94d612-9c62-5653-96c5-aadfe69e9542: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | RTGS - Success
      32d68a04-1742-5baf-a05a-a1d231601d0f: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | RTGS - Pending
      02ebc40c-d29a-557b-8076-221f847661e0: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | NEFT - Failed
      165e7547-6150-5698-8f1e-87636a2ab950: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | NEFT - Success
      d72ed064-a464-5254-a17d-50fcb3c174b4: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | NEFT - Pending
      0d3ee1cb-5e28-5efe-9aba-1d6a762e31c7: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | Intrabank - Failed
      098212b8-4e05-516e-8f02-e1d42582ef9c: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | Intrabank - Success
      63c00d5b-e87f-5c93-86d6-9c6329a1ab70: TRANSACTION_TYPE_P2P     # Off-App Transactions | Amount debited but not credited to the beneficiary | Intrabank - Pending
      e8a9d99b-1415-59c8-915d-499d86e07767: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | -
      72391a78-e4f1-5769-bc6c-8c643d367171: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | UPI - Failed
      6db07557-22fb-51b8-8f4a-4338332d0caf: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | UPI - Success
      c1054e1b-9d7e-50de-837c-58d55a295174: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | UPI - Pending
      1eef0459-e0a2-502b-8f64-cfb72edb81e7: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | IMPS - Failed
      6cd41015-61d0-5333-9587-4a803ab9fb1b: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | IMPS - Success
      1c3c8618-aac2-5f11-ab24-48c92ef4677a: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | IMPS - Pending
      48d56569-1d5f-59ca-980a-69340bf99a49: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | RTGS - Failed
      700e37b1-0033-58f6-98bc-f04782647b7f: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | RTGS - Success
      a0c1d6fd-62fa-5c6a-a5be-b7147dfa3218: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | RTGS - Pending
      c73dab73-6339-5a8b-b69e-67fa06520548: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | NEFT - Failed
      6bffb48d-6389-52d3-addd-f012b690af93: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | NEFT - Success
      225bb815-efb1-509d-9304-07a0944edd46: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | NEFT - Pending
      c5d33d37-f7e4-5914-bcf3-f68492606a98: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | Intrabank - Failed
      ef5a924a-3ca8-5891-8e21-a9b93396fe54: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | Intrabank - Success
      1ba83acb-3c2f-5fb9-a7f0-62e1e49815f6: TRANSACTION_TYPE_P2M     # Off-App Transactions | Amount debited but not credited to merchant | Intrabank - Pending

PriorityRoutingConfig:
  ThresholdBalance: 3000

FreshChatConfig:
  UserCategoryEnumToFreshchatLabelMapping:
    HIGH_PRIORITY_ACCOUNT_HOLDERS: "High"
    LOW_PRIORITY_ACCOUNT_HOLDERS: "Low"
    CURRENTLY_ONBOARDING_USERS: "CurrentlyOnboarding"
    CATEGORY_UNSPECIFIED: "UnIdentified"
    SALARY_PROGRAM_USERS: "SalaryProgramUsers"
  FreshChatCustomUserEmailFormat: "%<EMAIL>"
  FreshChatDefaultEmailIdSuffix: "@freshchat.com"
  FreshChatCustomUserEmailSuffix: "@fi.money"
  # Freshdesk has hard limit of storing at max 9 email addresses in other email address list in contact object
  FreshChatMaxLengthOfOtherEmailAddressList: 9
  AgentCacheValidityDuration: "24h"
  FreshChatDomain: "msdk.in.freshchat.com"

Tracing:
  Enable: false

TicketConfig:
  IsDefaultTitleAndDescriptionEnabledForInAppTicket: true
  DefaultAppTicketTitle: "Fi Care Ticket"
  DefaultAppTicketDescription: "Support provided by Fi Care"
  URL: "https://ficaretesting.freshdesk.com/a/tickets/%d"
  BulkResolutionModeValue: "Bulk Resolution"
  IsStatusResolvedMap:
    4: true
    5: true
  ShowTicketsInAppConfig:
    CutOffDateToShowTickets:
      Year: 2022
      Month: 08
      Day: 01
    TimeLimitForUpdatingTicketDetails: 30m
    WhitelistedProductCategories: [
      "PRODUCT_CATEGORY_TRANSACTION",
      "PRODUCT_CATEGORY_ACCOUNTS",
      "PRODUCT_CATEGORY_ONBOARDING",
      "PRODUCT_CATEGORY_SAVE",
      "PRODUCT_CATEGORY_WAITLIST",
      "PRODUCT_CATEGORY_RE_ONBOARDING",
      "PRODUCT_CATEGORY_REWARDS",
      "PRODUCT_CATEGORY_FIT",
      "PRODUCT_CATEGORY_DEBIT_CARD",
      "PRODUCT_CATEGORY_REFERRALS",
      "PRODUCT_CATEGORY_CONNECTED_ACCOUNTS",
      "PRODUCT_CATEGORY_FRAUD_AND_RISK",
      "PRODUCT_CATEGORY_JUMP_P2P",
      "PRODUCT_CATEGORY_PROFILE",
      "PRODUCT_CATEGORY_SALARY_ACCOUNT",
      "PRODUCT_CATEGORY_SEARCH",
      "PRODUCT_CATEGORY_WEALTH_ONBOARDING",
      "PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS",
      "PRODUCT_CATEGORY_APP_CRASH",
      "PRODUCT_CATEGORY_DATA_DELETION",
      "PRODUCT_CATEGORY_SCREENER",
      "PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED",
      "PRODUCT_CATEGORY_LANGUAGE_CALLBACK",
      "PRODUCT_CATEGORY_CATEGORY_NOT_FOUND",
      "PRODUCT_CATEGORY_KYC_OUTCALL",
      "PRODUCT_CATEGORY_TRANSACTION_ISSUES",
      "PRODUCT_CATEGORY_REWARDS_NEW",
      "PRODUCT_CATEGORY_REFERRALS_NEW",
      "PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI",
      "PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT",
      "PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED",
      "PRODUCT_CATEGORY_INSTANT_LOANS",
      "PRODUCT_CATEGORY_TIERING_PLANS",
      "PRODUCT_CATEGORY_CREDIT_CARD",
      "PRODUCT_CATEGORY_US_STOCKS",
      "PRODUCT_CATEGORY_DEVICE",
      "PRODUCT_CATEGORY_RISK",
      "PRODUCT_CATEGORY_ON_APP_TRANSACTIONS",
      "PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS",
      "PRODUCT_CATEGORY_INSTANT_SALARY",
      "PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD",
      "PRODUCT_CATEGORY_LAMF",
      "PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD",
      "PRODUCT_CATEGORY_SALARY_LITE",
      "PRODUCT_CATEGORY_FI_STORE"
    ]
    # MandatoryFieldsRequiredToShowTicket are the field names from Ticket struct in api/cx/ticket.pb.go
    MandatoryFieldsRequiredToShowTicket: [ "ProductCategory" ]
    CreatedByEnumToValueMapping:
      CREATED_BY_CUSTOMER: "Customer"
      CREATED_BY_SYSTEM: "System"
      CREATED_BY_AGENT: "Agent"
    CreationModeEnumToValueMapping:
      CREATION_MODE_EMAIL: "Email"
      CREATION_MODE_CHAT: "Chat"
      CREATION_MODE_CALL: "Phone call"
      CREATION_MODE_AUTOMATIC: "Automatic"
      CREATION_MODE_DEFAULT: "Social escalation"
      CREATION_MODE_APP: "App"
  IsTicketEventLoggingEnabled: true
  CsatConfig:
    PushNotificationTitle: "Hi {#first_name#}, we’re glad your issue is resolved!"
    PushNotificationDescription: "Please take a moment to share feedback on your experience with our support team"
    AllowedTicketStatusesForCsat: [
      "STATUS_RESOLVED"
    ]
    AllowedCommsTypeForCsat: [
      "CSAT_COMMS_SYSTEM_TRAY_NOTIFICATION",
      "CSAT_COMMS_EMAIL"
    ]
    CommsExternalRefIdPrefix: "cx_ticket_csat_"
    WebFormUrl: "https://web.staging.pointz.in/feedback/feedback-collector?token=%s"
    IsCsatCollectionEnabledViaWeb: true
    CsatEligibilityWindow: 360h # 15 days
  IsTicketUpdateEventPublishingEnabled: true

  SLAConfig:
    IsSLACalculationEnabledInTicketConsumer: true
    IsExpectedResolutionByFieldDeterminedUsingSLA: true
    IsEventPublishedToUpdateExpectedResolutionTimeFieldOnFreshdesk: true
  EscalationTeamEnumToValueMapping:
    ESCALATION_TEAM_ONBOARDING_TECH: "Onboarding Tech"
    ESCALATION_TEAM_RISK_OPS: "Risk Ops"
    ESCALATION_TEAM_CNX: "CNX"
    ESCALATION_TEAM_DEBIT_CARD: "Debit Card"
    ESCALATION_TEAM_DEBIT_CARD_OPS: "Debit Card Ops"
    ESCALATION_TEAM_DEBIT_CARD_TECH: "Debit Card Tech"
    ESCALATION_TEAM_FEDERAL: "Federal"
    ESCALATION_TEAM_FEDERAL_OPS: "Federal Ops"
    ESCALATION_TEAM_FIT_TECH: "Fit Tech Team"
    ESCALATION_TEAM_JUMP_TECH: "Jump tech team"
    ESCALATION_TEAM_L2: "L2"
    ESCALATION_TEAM_MF_OPS: "MF Ops"
    ESCALATION_TEAM_MF_TECH: "MF tech team"
    ESCALATION_TEAM_CARDS_TECH: "Cards tech"
    ESCALATION_TEAM_PAY_TECH: "Pay tech team"
    ESCALATION_TEAM_REFERRALS: "Referrals"
    ESCALATION_TEAM_REWARDS: "Rewards"
    ESCALATION_TEAM_VKYC_OPS: "VKYC Ops"
    ESCALATION_TEAM_KARZA: "Karza"
    ESCALATION_TEAM_CNX_AND_FEDERAL: "CNX/ Federal"
    ESCALATION_TEAM_ONBOARDING_TECH_ROUTED_TO_FEDERAL: "Onboarding tech (escalated to Federal)"
    ESCALATION_TEAM_NOT_APPLICABLE: "NA"
  ProductCategoryFieldId: "82000240451"
  TicketFieldCacheValidityDuration: "24h"
  LatestTicketCacheValidityDuration: "2m"
  IsTicketListLoggingEnabled: false

LandingPageConfig:
  IsExpectedResolutionTimeFieldPopulatedInLandingPageService: true
  RecentUserQueryConfig:
    NumberOfQueriesToDisplay: 10
    DateFormat: "02 Jan 2006"
    TimeFormat: "3:04PM"
  UserDetailTabsEnumToStringMapping:
    USER_DETAILS_TAB_PROFILE: "Profile"
    USER_DETAILS_TAB_DEBIT_CARD: "Debit Card"
    USER_DETAILS_TAB_PAYMENTS: "Payments"

CallConfig:
  MaximumDaysDuration: 2160h
  MaxPageSize: 50
  CallStartTicketSubject: "Call ticket for agent %s on %s (%s)"
  CallStartTicketDescription: "<br/> New Call <br/> From: %s <br/> Agent: %s <br/> Monitor UCID: %s <br/> Call Type: %s <br/>"
  UpdateTicketErrMsg: "Error while updating ticket description or agent assignment, please do it manually on Freshdesk"
  TicketAlreadyExistsErrMsg: "Ticket with ID: %d already exists for given call"
  AgentAssignmentErrMsg: "Ticket created successfully but could not assign agent to it, please do it manually on Freshdesk"
  TicketDbUpdateErrMsg: "Ticket created successfully but could not update in db, please append it manually"
  FreshdeskTicketCreationErr: "Failed to create new ticket on Freshdesk"
  CallEndTicketDescription: "<br/> Start Time: %s <br/> End Time: %s <br/> Duration: %s <br/> Status: %s <br/> Disposition: %s"
  CallEndTicketPrivateNote: "<br/> Start Time: %s <br/> End Time: %s <br/> Duration: %s <br/> Status: %s <br/> Recording: %s <br/> Disposition: %s"
  IsConsumerEventLoggingEnabled: false
  RecordingFilePath: "/epifi_ccaas/%s/%s"
  RecordingFileNotFoundErrMsg: "Unable to fetch recording file, please check Ozonetel portal"
  RecordingLinkTagForSherlock: "<a href='%s' target='_blank' rel='noreferrer'>Play</a>"
  AbandonedCallConfig:
    IsAbandonedCallCommsEnabled: true
    NotificationTemplate:
      Title: "Looks like you tried to reach us 📲"
      Body: "Sorry, we missed it! We can still help — tap this to chat with us instantly."
  IsCallBlockerEnabled: true
  IsCallBlockingEnabledViaIvrFlow: true
  CallBlockerTestConfig:
    UnRegisteredUser: false
    AppAccessBlocked: false
    UserReportedIssue: false
    IsCreditFreeze: false
    IsStandardTier: false
    IsRiskBlocked: false
  CallBlockerConfig:
    IsCallBlockerEnabled: true
    IsCallBlockingEnabledViaIvrFlow: true
    CallDropOffNotificationTitle: "%s, did you try to call us?"
    CallDropOffNotificationBody: "You can chat with us 24x7. For faster resolutions, tap here to chat with us."
    ContactUsFlowSmsLink: "https://fi.onelink.me/FiMony/rpb03lqc"
    FiAppDownloadLink: "https://fi.onelink.me/FiMony/rpb03lqc"
    BlockTierList: [
        "TIER_FI_REGULAR",
        "TIER_FI_BASIC",
        "TIER_FI_PLUS"
    ]
    TriagedTierList: [
        "TIER_FI_INFINITE"
    ]
    RequestTimeout: 3s

SupportTicketFreshdeskConfig:
  GroupEnumToGroupIdMapping:
    GROUP_CALLBACK: 82000152984
    GROUP_EPIFI_ESCALATION: 82000121914
    GROUP_ESCALATED_CASES_CLOSURE: 82000121915
    GROUP_FEDERAL_ESCALATIONS: 82000121911
    GROUP_L1_SUPPORT: 82000121912
    GROUP_L2_SUPPORT: 82000152983
    GROUP_NON_SFTP_ESCALATIONS: 82000121913
    GROUP_SFTP_ESCALATIONS: 82000152982
    GROUP_SFTP_PENDING_GROUP: ***********
    GROUP_FEDERAL_UPDATES: ***********
    GROUP_L1_SUPPORT_WAITLIST: ***********
    GROUP_RISK_OPS: ***********
    GROUP_L1_SUPPORT_CALL: ***********
    GROUP_L1_SUPPORT_CHAT: ***********
    GROUP_L1_SUPPORT_EMAIL: ***********
    GROUP_L1_SUPPORT_SOCIAL_MEDIA: ***********
    GROUP_OUTBOUND_CALL_BACK: ***********
    GROUP_LOAN_OUTBOUND_CALL: ***********
  ProductCategoryEnumToValueMapping:
    PRODUCT_CATEGORY_TRANSACTION: "Transactions"
    PRODUCT_CATEGORY_ACCOUNTS: "Accounts"
    PRODUCT_CATEGORY_ONBOARDING: "Onboarding"
    PRODUCT_CATEGORY_SAVE: "Save"
    PRODUCT_CATEGORY_WAITLIST: "Waitlist"
    PRODUCT_CATEGORY_RE_ONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_REWARDS: "Rewards"
    PRODUCT_CATEGORY_FIT: "FIT"
    PRODUCT_CATEGORY_DEBIT_CARD: "Debit Card"
    PRODUCT_CATEGORY_REFERRALS: "Referrals"
    PRODUCT_CATEGORY_CONNECTED_ACCOUNTS: "Connected Accounts"
    PRODUCT_CATEGORY_FRAUD_AND_RISK: "Fraud & Risk"
    PRODUCT_CATEGORY_JUMP_P2P: "Jump P2P"
    PRODUCT_CATEGORY_PROFILE: "Profile"
    PRODUCT_CATEGORY_SALARY_ACCOUNT: "Salary account"
    PRODUCT_CATEGORY_SEARCH: "Search"
    PRODUCT_CATEGORY_WEALTH_ONBOARDING: "Wealth Onboarding"
    PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS: "Wealth Mutual Funds"
    PRODUCT_CATEGORY_APP_CRASH: "App Crash"
    PRODUCT_CATEGORY_DATA_DELETION: "Data deletion"
    PRODUCT_CATEGORY_SCREENER: "Screener"
    PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED: "Google Token expired"
    PRODUCT_CATEGORY_LANGUAGE_CALLBACK: "Language callback"
    PRODUCT_CATEGORY_CATEGORY_NOT_FOUND: "Category not found"
    PRODUCT_CATEGORY_KYC_OUTCALL: "KYC Outcall"
    PRODUCT_CATEGORY_TRANSACTION_ISSUES: "Transaction Issues"
    PRODUCT_CATEGORY_REWARDS_NEW: "Rewards New"
    PRODUCT_CATEGORY_REFERRALS_NEW: "Referrals New"
    PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI: "General Enquiries about Fi"
    PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT: "No response/ Blank chat"
    PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED: "Call drop/ disconnected"
    PRODUCT_CATEGORY_INSTANT_LOANS: "Instant Loans"
    PRODUCT_CATEGORY_TIERING_PLANS: "Tiering plans"
    PRODUCT_CATEGORY_CREDIT_CARD: "Credit Card"
    PRODUCT_CATEGORY_US_STOCKS: "US stocks"
    PRODUCT_CATEGORY_DEVICE: "Device"
    PRODUCT_CATEGORY_RISK: "Risk"
    PRODUCT_CATEGORY_ON_APP_TRANSACTIONS: "In-App Transactions"
    PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS: "Off-App Transactions"
    PRODUCT_CATEGORY_INSTANT_SALARY : "Instant Salary"
    PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD: "SimpliFi Credit Card"
    PRODUCT_CATEGORY_LAMF: "LAMF"
    PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD: "MagniFi Credit Card"
    PRODUCT_CATEGORY_SALARY_LITE: "Salary Lite"
    PRODUCT_CATEGORY_FI_STORE: "Fi-Store"
  TransactionTypeEnumToValueMapping:
    TRANSACTION_TYPE_DEBIT_CARD: "Debit Card"
    TRANSACTION_TYPE_IMPS: "IMPS"
    TRANSACTION_TYPE_NEFT: "NEFT"
    TRANSACTION_TYPE_RTGS: "RTGS"
    TRANSACTION_TYPE_UPI: "UPI"
    TRANSACTION_TYPE_INTRA_BANK: "Intra Bank"
    TRANSACTION_TYPE_UNKNOWN: "Unknown"
  DisputeStatusEnumToValueMapping:
    DISPUTE_STATUS_ACCEPTED: "Accepted"
    DISPUTE_STATUS_REJECTED: "Rejected"
  StatusEnumToValueMapping:
    STATUS_UNSPECIFIED: 0
    STATUS_OPEN: 2
    STATUS_PENDING: 3
    STATUS_RESOLVED: 4
    STATUS_CLOSED: 5
    STATUS_WAITING_ON_THIRD_PARTY: 7
    STATUS_ESCALATED_TO_L2: 8
    STATUS_ESCALATED_TO_FEDERAL: 9
    STATUS_SEND_TO_PRODUCT: 10
    STATUS_WAITING_ON_PRODUCT: 11
    STATUS_REOPEN: 12
    STATUS_NEEDS_CLARIFICATION_FROM_CX: 13
    STATUS_WAITING_ON_CUSTOMER: 6
  ProductCategoryDetailsEnumToValueMapping:
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_APP_DOWNLOAD_ISSUE: "App download issue"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_CHECK_FAILURE: "Device check failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_PHONE_NUMBER_OTP: "OTP"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_EMAIL_SELECTION_FAILURE: "Email selection failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_MOTHER_FATHER_NAME: ""
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_PAN_NAME_VALIDATION_FAILURE: "PAN Name validation failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_EXISTING_FEDERAL_ACCOUNT: "Existing Federal account"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_KYC: "Manual KYC"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_LIVENESS: "Liveness"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_FACEMATCH_FAIL: "Face-match fail"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_UN_NAME_CHECK: "UN Name blacklist"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CONFIRM_CARD_MAILING_ADDRESS: "Confirm Card Mailing address"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_CONSENT_FAILURE: "UPI Consent failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_REGISTRATION_FAILURE: "Device Registration Failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CUSTOMER_CREATION_FAILURE: "Customer creation failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED: "Account opening delayed"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_CREATION_FAILURE: "Card creation failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_PIN_SET_FAILURE: ""
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_SETUP_FAILURE: "UPI setup failure"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_VKYC: "VKYC"
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_REONBOARDING: "Re-onboarding"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN: "PIN"
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY: "Min KYC expiry"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION: "Activation"
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY: "Delivery"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP: "Debited via Fi app but"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP: "Debited from FI account (via Other App) but"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_CARDS_ATM: "Cards - ATM"
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT: "UPI - Unable to transact"
    PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL: "Investment Transaction Successful"
    PRODUCT_CATEGORY_DETAILS_SAVE_FIXED_DEPOSIT: "Fixed Deposit"
    PRODUCT_CATEGORY_DETAILS_SAVE_SMART_DEPOSIT: "Smart Deposit"

  SubCategoryDetailsEnumToValueMapping:
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_NEW: "New"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_APPROVED: "Approved"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_REJECTED: "Rejected"
    SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_ON_HOLD: "On-Hold"
    SUB_CATEGORY_PIN_UPI_PIN: "UPI PIN"
    SUB_CATEGORY_PIN_DEVICE_PIN: "Device PIN"
    SUB_CATEGORY_PIN_APP_PIN: "App PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING: "QR code not working"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN: "Unable to set PIN"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS: "Cannot enable POS"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS: "Cannot enable Contactless"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL: "Cannot enable ATM withdrawal"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED: "OTP not received"
    SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE: "How to activate"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING: "Tracking"
    SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD: "Did not receive card"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT: "Not credited to merchant"
    SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY: "Not credited to beneficiary"
    SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND: "Balance refund"
    SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_DEBITED_BUT_NOT_DISPENSED_AT_MACHINE: "Debited but not dispensed at machine"
    SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED: "UPI pin tries exceeded"
    SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED: "Units not allotted"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_PRE_CLOSURE: "Pre-Closure FD"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_MATURITY: "Mature FD"
    SUB_CATEGORY_SAVE_FIXED_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_PRE_CLOSURE: "Pre-Closure SD"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_MATURITY: "Mature SD"
    SUB_CATEGORY_SAVE_SMART_DEPOSIT_INTEREST_PAYOUT: "Interest Payout"

  OsTypeEnumToValueMapping:
    ANDROID: "Android"
    IOS: "iOS"
  ResolutionModeEnumToValueMapping:
    RESOLUTION_MODE_AUTO_RESOLUTION: "Auto Resolution"
    RESOLUTION_MODE_BULK_RESOLUTION: "Bulk Resolution"
    RESOLUTION_MODE_MANUAL_RESOLUTION: "Manual Resolution"
    RESOLUTION_MODE_WATSON_RESOLUTION: "Watson Resolution"

  TicketVisibilityEnumToValueMapping:
    TICKET_VISIBILITY_ONLY_AGENT: "Agent"
    TICKET_VISIBILITY_ONLY_CUSTOMER: "Customer"
    TICKET_VISIBILITY_ALL: "All"
  SavingsAccountBalanceEnumToValueMapping:
    SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1: "Less than 1"
    SAVINGS_ACCOUNT_BALANCE_OTHER: "Other"
  MonorailRaisedEnumToValueMapping:
    MONORAIL_RAISED_YES: "Yes"
    MONORAIL_RAISED_NO: "No"
  BooleanEnumToYesNoMapping:
    TRUE: "Yes"
    FALSE: "No"

CallRoutingConfig:
  CallLangPrefReleaseConfig:
    IsRestrictedReleaseEnabled: false
    # if IsRestrictedReleaseEnabled is false then IsEnabledForPlatform and IsEnabledForUserGroup will not be evaluated
    IsEnabledForPlatform:
      ANDROID: true
      IOS: true
    IsEnabledForUserGroup:
      INTERNAL: true
  CallLangPreferencesList: [ "LANGUAGE_ENGLISH","LANGUAGE_HINDI" ]
  CallLangSuggestionsList: [ "LANGUAGE_BENGALI","LANGUAGE_TELUGU","LANGUAGE_TAMIL","LANGUAGE_MALAYALAM","LANGUAGE_KANNADA","LANGUAGE_PUNJABI","LANGUAGE_ASSAMESE","LANGUAGE_MARATHI", "LANGUAGE_GUJARATI", "LANGUAGE_ODIA" ]
  IsManualRoutingEnabled: false
  BlockedAccountCallRoutingConfig:
    IsRoutingEnabled: false
    # status and reason code received from federal for blocked account
    FreezeStatusToReasonCodeMap:
      D: "51"
    AccessRevokeReasonMap:
      ACCESS_REVOKE_REASON_FRAUDULENT_ACCOUNT: true
      ACCESS_REVOKE_REASON_SPOOF: true
  SegmentIdToPriorityMapForRoutingChannel:
    ROUTING_CHANNEL_ACCOUNT_HOLDERS:
      # this is staging segment id for some group of user
      # just to act as a placeholder and as a reference, should be used accordingly in prod
      # setting priority to 1, since in ozonetel we can have priority from 1 to 9
      # 1 is the highest priority which can surpass any other priority in the routing channel
      # lower the priority value, higher the priority in the queue
      AWS_cee0112a884e4addad0434f64a929aa0: 1
  RoutingChannelToSegmentIdList:
    ROUTING_CHANNEL_ACCOUNT_HOLDERS: [ "AWS_cee0112a884e4addad0434f64a929aa0" ]
  IsSalaryProgramRoutingEnabled: true
  IsCreditCardRoutingEnabled: false
  IsPriorityRoutingEnabled: false
  IsActiveLoanRoutingEnabled: true
  # default priority for any user
  # priority value can range from 1 to 9
  # where 9 denotes the lowest priority
  # and 1 denotes the highest priority
  # default priority would be 9 (which is the lowest priority as per ozonetel)
  DefaultPriorityValue: 9
  MaxPriorityValue: 1
  QueueWaitTimeDurationThreshold: "45s"
  CallDropOffCountThreshold: 3
  HalvingFactor: 2
  PastCallRecordsLookupSize: 10
  PastCallRecordsLookupDuration: "24h"
  PreRecordedMessageConfig:
    IsHighRiskMessageEnabled: true
    IsScreenerRejectedMessageEnabled: true
  IsRecordedMessageEventEnabled: true
  IssuePriorityCacheConfig:
    IsEnabled: true
    Key: "cx:iss_prio:%s"
    AllowMaxPriorityForIssue: ["ISSUE_PRIORITY_CRITICAL"]

FeatureReleaseConfig:
  FeatureConstraints:
    DISPUTE_FOR_OFF_APP_TXN:
      AppVersionConstraintConfig:
        MinAndroidVersion: 1000
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    SENSEFORTH_CHATBOT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 14 # CX_INTERNAL
    FRESHDESK_MONORAIL_INTEGRATION:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 14 # CX_INTERNAL
    INAPPHELP_FEEDBACK_ENGINE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 14 # CX_INTERNAL
          - 1 # INTERNAL
    IN_APP_CSAT_SURVEY:
      AppVersionConstraintConfig:
        MinAndroidVersion: 295
        MinIOSVersion: 415
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 14 # CX_INTERNAL
          - 1 # INTERNAL
    FEATURE_CX_CALL_IVR:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 14 # CX_INTERNAL
    FEATURE_REPORT_FRAUD_IN_DISPUTE_BOTTOM_SHEET:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
    FEATURE_CX_CALL_RISK_IVR_FLOW:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_FRESHCHAT_CHATBOT_SDK_ENABLED:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100

BulkUserInfoViaEmailConfig:
  FromEmailId: "<EMAIL>"
  FromEmailName: "Bulk User Details"
  MaxCountThreshold: 3000
  FieldToCsvColNameMap:
    BULK_USER_INFO_FIELD_FRESHDESK_TICKET_ID: "Freshdesk Ticket ID"
    BULK_USER_INFO_FIELD_ACTOR_ID: "Actor ID"
    BULK_USER_INFO_FIELD_ACCOUNT_NUMBER: "Account Number"
    BULK_USER_INFO_FIELD_CUSTOMER_ID: "Customer ID"
    BULK_USER_INFO_FIELD_EMAIL_ID: "Email ID"
    BULK_USER_INFO_FIELD_FCM_ID: "FCM ID"
    BULK_USER_INFO_FIELD_PHONE_NUMBER: "Phone Number"
    BULK_USER_INFO_FIELD_NAME: "Name"
    BULK_USER_INFO_FIELD_KYC_LEVEL: "KYC Level"
    BULK_USER_INFO_FIELD_IS_SAVINGS_ACCOUNT_CLOSURE_ALLOWED: "Is Savings Account closure allowed?"
    BULK_USER_INFO_FIELD_ACCOUNT_CREATION_DATE_AND_TIME: "Account Creation Date & Time(IST)"
  RoleToAllowedRespFieldsMap:
    BIZ_ADMIN:
      BULK_USER_INFO_FIELD_ACTOR_ID: true
      BULK_USER_INFO_FIELD_ACCOUNT_NUMBER: true
      BULK_USER_INFO_FIELD_CUSTOMER_ID: true
      BULK_USER_INFO_FIELD_EMAIL_ID: true
      BULK_USER_INFO_FIELD_FCM_ID: true
      BULK_USER_INFO_FIELD_PHONE_NUMBER: true
      BULK_USER_INFO_FIELD_NAME: true
      BULK_USER_INFO_FIELD_KYC_LEVEL: true
      BULK_USER_INFO_FIELD_IS_SAVINGS_ACCOUNT_CLOSURE_ALLOWED: true
      BULK_USER_INFO_FIELD_ACCOUNT_CREATION_DATE_AND_TIME: true
    BIZ_ADMIN_RESTRICTED:
      BULK_USER_INFO_FIELD_ACTOR_ID: true
      BULK_USER_INFO_FIELD_ACCOUNT_NUMBER: true
      BULK_USER_INFO_FIELD_CUSTOMER_ID: true
      BULK_USER_INFO_FIELD_NAME: true
      BULK_USER_INFO_FIELD_IS_SAVINGS_ACCOUNT_CLOSURE_ALLOWED: true
      BULK_USER_INFO_FIELD_ACCOUNT_CREATION_DATE_AND_TIME: true

CustomerAuth:
  AuthFactorPriorityByPlatform:
    ANDROID:
      MinAppVersion: 0
      PriorityMap:
        MOBILE_PROMPT: 1
        EMAIL_VERIFICATION: 2
        TRANSACTION_AMOUNT: 3
        LAST_FIVE_PAN_CHARACTERS: 4
        MOTHERS_NAME: 5
        FATHERS_NAME: 6
        DOB: 7
        PERMANENT_ADDRESS_PIN_CODE: 8
    IOS:
      MinAppVersion: 0
      PriorityMap:
        EMAIL_VERIFICATION: 1
        MOBILE_PROMPT: 2
        TRANSACTION_AMOUNT: 3
        LAST_FIVE_PAN_CHARACTERS: 4
        MOTHERS_NAME: 5
        FATHERS_NAME: 6
        DOB: 7
        PERMANENT_ADDRESS_PIN_CODE: 8
    PLATFORM_UNSPECIFIED:
      MinAppVersion: 0
      PriorityMap:
        EMAIL_VERIFICATION: 1
        MOBILE_PROMPT: 2
        TRANSACTION_AMOUNT: 3
        LAST_FIVE_PAN_CHARACTERS: 4
        MOTHERS_NAME: 5
        FATHERS_NAME: 6
        DOB: 7
        PERMANENT_ADDRESS_PIN_CODE: 8
  AgentCacheValidityDuration: "72h"
  IsAgentCachingEnabled: true
  InAppNotificationTemplate:
    MobilePromptTitlePN: "%s! Are you on a call with Fi care now?"
    MobilePromptTitleInApp: "Are you on a call with Fi Care now? Please confirm here"
    MobilePromptBody: "Do confirm if you're talking to us from %s"
    NotificationTimestampFontColor: "#878A8D"
    IconUrl: "https://epifi-icons.pointz.in/in_app_auth_notification_v2"
    IconBGColor: "#FFFFFF"
    TitleFontColor: "#313234"
    BGColor: "#CED2D6"
    NotificationDismissIconBgColor: "#E7ECF0"
    NotificationDismissIconColor: "#606265"
    NotificationShadowColor: "#4A4A4A"
  AuthFactorCacheKey: "cx_auth_factor_%s"
  AuthFactorCacheValidityDuration: "30m" # product call to skip auth for 30 mins
  IsSkippingAuthEnabledAfterExternalAuth: true

CallIvrConfig:
  IsIvrEnabled: true
  IvrPollCountCacheKey: "cx_ivr_poll_count_%s"
  IvrPollCountCacheDuration: "60s"
  IvrPollCountThreshold: 8
  MaxInvalidInputCount: 4
  MaxRepeatInputCount: 2
  IsCardBlockingEnabled: true

ChatBotConfig:
  LiveChatFallbackConfig:
    ChannelId: "20e6b498-cef1-47fe-8692-2c890495e770"
  SenseforthChatInitInfo:
    WebViewURLMap:
      ANDROID: "https://chatbot-frontend.deploy.pointz.in/chatsdk/v1/index.html?userAgent=android"
      IOS: "https://chatbot-frontend.deploy.pointz.in/chatsdk/v1/index.html?userAgent=ios"
  IsForceFallbackToDefaultEnabled: false
  IsReleaseEvaluationEnabled: true
  DefaultInAppChatView: "IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK"
  MaxClientSideFailureCountAllowedForAutoRetry: 3
  MaxTimeDurationThresholdForLastSuccessfulSessionTime: 59m #Token inactivity expiry duration is 1 hr. So setting this to 59mins
  IsExtraLoggingEnabled: true
  NotificationTemplatesMap:
    LIVE_CHAT_EVENT_TYPE_CONVERSATION_ASSIGNMENT:
      Title: "Fi Care Specialist joined"
      Body: "%s is ready to chat with you"
    LIVE_CHAT_EVENT_TYPE_AGENT_REPLY:
      Title: "Message from Fi Care specialist"
      Body: "%s"
    LIVE_CHAT_EVENT_TYPE_CONVERSATION_RESOLUTION:
      Title: "Chat conversation resolved"
      Body: "Your conversation has been marked resolved by %s"
  CreateTicketTag: "Chatbot"
  NumOfTxnsToBeFetched: 15
  TxnListDisplayFormat:
    # NOTE: The fields preceding TxnId in the FieldsOrder should all be mandatory. This is to extract TxnId from the display string
    FieldsOrder: ["CreatedAt","TxnId","TxnType","Amount","PaymentProtocol","TxnStatus","UserEntityName"]
    Delimiter: " | "
  NumOfChargesToBeDisplayed: 15
  TxnReasonDisplayMap:
    ECS_ENACH_CHARGES: "You were charged a ECS/NACH Return Fee of %s due to insufficient account balance during a scheduled auto-payment 🚨 \n To avoid such charges, maintain a sufficient account balance. This helps smoothly process bills, EMIs, SIPs, and more."
    CHEQUE_BOOK_CHARGES: "You were charged %s for ordering a 10-leaf cheque book, but don't worry, this charge is typically refunded within 30 days. No further action is usually needed from your end."
    NACH_RETURN_CHARGE: "You were charged a NACH Return Fee of %s due to insufficient account balance during a scheduled auto-payment 🚨 \n To avoid such charges, maintain a sufficient account balance. This helps smoothly process bills, EMIs, SIPs, and more."
    ECS_RETURN_CHARGE:  "You were charged an ECS Return Fee of %s due to insufficient account balance during a scheduled auto-payment 🔴 \n To avoid such charges, maintain a sufficient account balance. This helps smoothly process bills, EMIs, SIPs, and more."
    ECS_ENACH_MANDATE_SETUP_CHARGE: "You were charged %s as per your ECS NACH mandate."
    ANYWHERE_BANKING_CHARGE:  "You were charged an Anywhere Banking (AWB) fee of %s for making transactions at a physical bank branch but not online. \n To avoid such charges in the future, you can add and send money, open and close deposits, and more on the app."
    ATM_DECLINE_CHARGE: "It looks like you were not able to withdraw money from the ATM. You can regularly deposit money into your Savings Account to ensure you have sufficient funds for successful ATM withdrawals."
    DUPLICATE_CARD_FEE: "You were charged a Duplicate card fee of %s for requesting a replacement of your Debit Card. The first replacement is free, while for the second replacement and onwards, there is a charge of ₹199 + GST."
    OTHER_BANK_ATM_CHARGE: "As you exceed the monthly limit of 5 free transactions at non-Federal Bank ATMs, an 'Other bank ATM usage fee' of %s was charged. \n Use Federal Bank ATMs for cash withdrawals to avoid such charges."
    CASH_HAND_CHARGE: "You were charged a Cash Handling fee of %s for making physical bank transactions, such as cash deposits or withdrawals. \n To avoid these charges, make the most of the Fi app and try to avoid visiting physical bank branches. You can access Federal Bank's services via Fi, including adding/sending money, opening/closing deposits, and more, without incurring such charges."
  NumOfFailedTxnsToBeDisplayed: 5
  NumberOfATMTxnsToBeFetched: 5
  # This map contains the PredefinedMessage Type and the static message which needs to be shown to the user.
  # This has to be moved to templates if the requirement for dynamic content comes in.
  PostOnboardingHighRiskUserMessage: "Your account has been flagged for unusual activity. Please check your registered email for a <NAME_EMAIL> and reply with the required documents. If other issues, fill out <a href='https://forms.gle/zFqMFqf2Z9SJ25999'>https://forms.gle/zFqMFqf2Z9SJ25999</a> and we will assist you."
  IsPredefinedMessageTemplateEnabled: true
  IsPostOnboardingHighRiskMessageEnabled: true
  IsContextCodePassingFromClientEnabled: true
  IsFreshChatExperimentEnabled: false
  ActorIdsEnabledFreshchatIssueTreeExperiment: ["AC2ueP7mgwiH250521","ACVmxH5hoAq5240731"]

SherlockFeedbackDetailsConfig:
  PageSize: 20

CxS3Config:
  BucketName: "epifi-cx"
  BulkUserDetailsFolderName: "bulk-user-details"
  MarketingCampaignUsersFolderName: "mktg-campaign"
  RiskCasesForProcessingFolderName: "risk-cases-for-processing"
  RiskCasesProcessedFolderName: "risk-cases-processed"
  # NOTE: Do not add anything other than app logs in AppLogsFolderName folder. It has S3 lifecycle rules setup to delete after 15 days.
  AppLogsFolderName: "app-logs"
  DataExtractionFolderName: "data-extraction"

RiskConfig:
  DevActionConfig:
    RiskCaseEventBatchSize: 10
    SupportedPayloadTypes: [ "PAYLOAD_TYPE_LIVENESS_SAMPLE", "PAYLOAD_TYPE_USER_REVIEW", "PAYLOAD_TYPE_LIVENESS", "PAYLOAD_TYPE_TRANSACTION_REVIEW", "PAYLOAD_TYPE_SCREENER" ]

DataS3Config:
  BucketName: "epifi-raw-dev"
  S3Prefix: "qa/data/vendor/segmentation_service"
  StaticSegmentSrcFolderPath: "manual_dump/static_segment"
  StaticSegmentDestFolderPath: "static_segments"
  SegmentMasterTableSchemaName: "dp_segmentation_svc"
  SegmentMasterTableName: "sgmt_master_customer_table"
  SegmentMasterTableSelectedField: "actor_id"

SalaryOpsConfig:
  MaxBEPaginatedCallsForFiltering: 50
  VerificationFailureReasonsCategoryToDisplayString:
    - EMPLOYER_IS_NOT_AN_ORG: Employer is not an Org
    - REMITTER_IS_NOT_AN_ENTITY: Remitter is not an entity
    - NOT_PART_OF_EMPLOYER_DB: Not part of employer DB
    - INSUFFICIENT_INFO: Insufficient info
    - EMPLOYER_IS_A_FINTECH_COMPANY: Employer is a fintech company
    - REMITTER_IS_A_FINTECH_COMPANY: Remitter is a fintech company
    - NOT_A_SALARY_TRANSACTION: Not a salary transaction
    - TRANSACTION_SOURCE_ISSUE: Transaction source issue
  VerificationFailureReasonsSubCategoryToDisplayString:
    - EMPLOYER_NAME_SAME_AS_THE_ACCOUNT_HOLDER: Employer name same as the account holder
    - EMPLOYER_NAME_AS_ANOTHER_INDIVIDUAL: Employer name as another individual
    - REMITTER_NAME_SAME_AS_THE_ACCOUNT_HOLDER: Remitter name same as the account holder
    - REMITTER_NAME_AS_ANOTHER_INDIVIDUAL: Remitter name as another individual
    - NOT_VERIFICABLE_BY_GSTIN_ALSO: Not verificable by GSTIN also
    - REMITTER_NAME_NOT_AVAILABLE: Remitter name not available
    - PARTIAL_REMITTER_NAME: Partial remitter name
    - PAYMENT_PROTOCOL_IS_UNSPECIFIED: Payment protocol is unspecified
    - OTHERS: Others
    - MULTIPLE_REMITTER_NAME: Multiple remitter name
    - MERCHANT_ACCOUNT_TRANSFER: Merchant account transfer
    - EXPENSE_REIMBURSEMENT: Expense reimbursement
    - PF_WITHDRAWAL: Pf withdrawal
    - REMITTER_IS_A_LENDING_COMPANY: Remitter is a lending company
    - REMITTER_IS_A_FINANCIAL_INSTITUTION: Remitter is a financial institution
    - INVESTMENT_WITHDRAWAL: Investment withdrawal
  SalaryTransactionFilters:
    MinSalaryAmount: 20000
    # added PAYMENT_PROTOCOL_UNSPECIFIED to allow showing txns whose payment protocol was not captured in our system.
    AllowedTransactionProtocols: [ "NEFT", "IMPS", "RTGS", "PAYMENT_PROTOCOL_UNSPECIFIED" ]
    MinReqDurationFromLastVerification: **************** #25 days
    MaxAllowedDurationFromLastVerification: **************** #60 days
  NonIncomeRelatedTxnCategoryOntologyIds: [
      "cf22e28c-60f8-4a11-8d35-5dfca7d2839d",
      "42dc03e7-3756-4a4f-8e7e-7d625a9ca02e",
      "ca472d29-345e-42df-9e72-f049086635d8",
      "65ae8b75-258d-4176-838a-68bf79bdf2a0",
      "a9e3e865-4ca7-4866-8cc6-7707088843f4",
      "da014408-87c2-46ee-9888-5a2b3cb12566",
      "a4612765-9522-43f5-ac40-015982cb2272",
      "8725f90a-a092-46d8-8726-f142370db769",
      "3886d8a3-1e5e-4143-a750-0f824d300e1d",
      "382e94ee-d0f6-4d36-8cb7-ee70c11580b2",
  ]
  SalaryProgramHealthInsuranceConfig:
    PolicyFAQsDocS3Path: "healthinsurance/docs/healthInsuranceFAQs.pdf"
    PolicyClaimProcessDocS3Path: "healthinsurance/docs/claimsProcessDoc.pdf"
    InclusionExclusionAndHowItWorksDocS3Path: "healthinsurance/docs/InclusionExclusionAndHowItWorksDoc.pdf"
    TncsDocS3Path: "healthinsurance/docs/healthInsuranceTncs.pdf"

SprinklrConfig:
  FreshdeskTicketSubject: "Sprinklr Case Number - %s"
  FreshdeskTicketDescription: "<br/> <b>Product Category :</b> %s <br/> <b>Product Category Details :</b> %s <br/> <b>Subcategory :</b> %s <br/> <b>Preferred Language :</b> %s <br/> <b>Status :</b> %s <br/> <b>Group :</b> %s <br/> <b>Social Channel :</b> %s <br/> <b>Social Media Handle :</b> %s <br/> <b>Notes :</b> %s <br/> <b>Due By :</b> %s <br/>"

WatsonConfig:
  IsWatsonSystemEnabled: true
  IncidentCategoryDetailsConfig:
    PRODUCT_CATEGORY_TRANSACTIONS:
      PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP:
        IsProcessingEnabled: true
      PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP:
        IsProcessingEnabled: true
      PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT:
        IsProcessingEnabled: true
    PRODUCT_CATEGORY_ACCOUNTS:
      PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN:
        IsProcessingEnabled: true
      PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY:
        IsProcessingEnabled: true
    PRODUCT_CATEGORY_ONBOARDING:
      PRODUCT_CATEGORY_DETAILS_ONBOARDING_APP_DOWNLOAD_ISSUE:
        IsProcessingEnabled: true
      PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED:
        IsProcessingEnabled: true
      PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_CHECK_FAILURE:
        IsProcessingEnabled: false
    PRODUCT_CATEGORY_DEBIT_CARD:
      PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION:
        IsProcessingEnabled: true
      PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY:
        IsProcessingEnabled: false
    PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS:
      PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL:
        IsProcessingEnabled: true
    PRODUCT_CATEGORY_SAVE:
      PRODUCT_CATEGORY_DETAILS_SAVE_FIXED_DEPOSIT:
        IsProcessingEnabled: true
      PRODUCT_CATEGORY_DETAILS_SAVE_SMART_DEPOSIT:
        IsProcessingEnabled: true
  AlternativeIncidentCategoryIdMapping:
    8a2d37b2-c4ab-5449-967a-87a4dd698404:
      "PRODUCT_CATEGORY_TRANSACTIONS:PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT:SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED"
    119a7b99-0413-51f0-87e9-474e70471cb2:
      "PRODUCT_CATEGORY_TRANSACTIONS:PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP:SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY"
    0d1212df-29ae-5cb0-9910-b890648cf587:
      "PRODUCT_CATEGORY_TRANSACTIONS:PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP:SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT"
    46c4f9a2-4c2c-5e03-8ffc-2b818a6c1cb5:
      "PRODUCT_CATEGORY_ACCOUNTS::PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY::SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND"
    c86c3d74-6a82-542b-bafe-7743a4721618:
      "PRODUCT_CATEGORY_ONBOARDING::PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED"
    3781d578-a92f-54ed-8a54-febeac0ff102:
      "PRODUCT_CATEGORY_ONBOARDING::PRODUCT_CATEGORY_DETAILS_ONBOARDING_APP_DOWNLOAD_ISSUE"
    e596e7ea-0d2f-5818-b54a-ef14724a7b62:
      "PRODUCT_CATEGORY_ONBOARDING::PRODUCT_CATEGORY_DETAILS_ONBOARDING_VKYC::SUB_CATEGORY_ONBOARDING_VKYC_VKYC_REVIEW_STATUS"
RlConfig:
  ResourceMap:
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP:
      Rate: 10
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN:
      Rate: 15
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION:
      Rate: 30
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_CHECK_FAILURE:
      Rate: 10
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_APP_DOWNLOAD_ISSUE:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_FIXED_DEPOSIT:
      Rate: 20
      Period: 1s
    PRODUCT_CATEGORY_DETAILS_SMART_DEPOSIT:
      Rate: 20
      Period: 1s
    watson_incidents:
      Rate: 20
      Period: 1s
  Namespace: "cx"

IsRedactionEnabledForDBStates: false

UsStocksOpsConfig:
  OrderTypeStageStatusMap:
    "BUY-POOL_ACCOUNT_TRANSFER-PROCESSING": "Check Payment status and if payment has failed, then inform the user that the payment has failed and order will not be placed, and the order will get updated soon. In case the order is stuck in this stage for more than 24 hours, please escalate to dev on-call"
    "BUY-POOL_ACCOUNT_TRANSFER-MANUAL_INTERVENTION": "Communicate to the user that their order placement is delayed and developer is looking into it. Escalate to Dev On-call"
    "BUY-POOL_ACCOUNT_TRANSFER-FAILED": "Communicate to the user that their payments has failed. Please check Payment to check for exact failure reason. Ask them to retry. "
    "BUY-POOL_ACCOUNT_TRANSFER-SUCCESSFUL": "There should not be any problem at this stage"
    "BUY-SEND_BUY_ORDER-INITIATED": "Tell user that the Order placement is in progress and should get placed momentarily. In case the order is stuck for more than 24 hours, then escalate to dev on-call"
    "BUY-SEND_BUY_ORDER-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "BUY-SEND_BUY_ORDER-FAILED": "Ask user to retry"
    "BUY-SEND_BUY_ORDER-SUCCESSFUL": "Success"
    "BUY-TRACK_ORDER-INITIATED": "ETA: Market open"
    "BUY-TRACK_ORDER-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "BUY-TRACK_ORDER-FAILED": "Ask user to retry"
    "BUY-TRACK_ORDER-FAILED-ORDER_CANCELED": "Tell the user they cancelled their order and that their money should get refunded in 3-4 business days"
    "BUY-TRACK_ORDER-SUCCESSFUL": "Success"
    "BUY-FOREIGN_FUND_TRANSFER-PROCESSING": "Tell the user that their remittance will go through normally and ask them to wait for a few days"
    "BUY-FOREIGN_FUND_TRANSFER-MANUAL_INTERVENTION": "Tell the user that their remittance is delayed and that the dev on-call is looking into it. Escalate to dev on-call"
    "BUY-FOREIGN_FUND_TRANSFER-FAILED": "Tell the user that their remittance has failed and will be retried by dev on-call"
    "BUY-FOREIGN_FUND_TRANSFER-SUCCESSFUL": "There should not be any problem at this stage"
    "BUY-RELEASE_SELL_LOCK-INITIATED": "Tell the user that they can't sell as their order is still under sell lock. This should get lifted as soon as the remittance reached the broker which should 1-2 business days. If it's more than that, please escalate to dev on-call"
    "BUY-RELEASE_SELL_LOCK-MANUAL_INTERVENTION": "Tell the user that they can't sell as their order is still under sell lock. Dev on-call is looking into it and they should be able to sell quickly. Escalate to dev on-call"
    "BUY-RELEASE_SELL_LOCK-FAILED": "Tell the user that they can't sell as their order is still under sell lock. Dev on-call is looking into it and they should be able to sell quickly. Escalate to dev on-call"
    "BUY-RELEASE_SELL_LOCK-SUCCESSFUL": "There should not be any problem at this stage"
    "BUY-REFUND_PAYMENT-PROCESSING": "Communicate to user that the refund has been initiated and that it will be refunded in 2-3 business days. In case it hasn't happened, please escalate to Dev on-call"
    "BUY-REFUND_PAYMENT-MANUAL_INTERVENTION": "Communicate to user that the refund has been delayed and that dev on-call is on top of it. Refund will be done in 2-3 business days. Escalate to dev on-call"
    "BUY-REFUND_PAYMENT-FAILED": "Communicate to user that the refund has failed and that dev on-call is on top of it. Refund will be done in 2-3 business days. Escalate to dev on-call"
    "BUY-REFUND_PAYMENT-SUCCESSFUL": "There should not be any problem at this stage"
    "SELL-SEND_SELL_ORDER-INITIATED": "Tell user that the Order placement is in progress and should get placed momentarily. In case the order is stuck for more than 24 hours, then escalate to dev on-call"
    "SELL-SEND_SELL_ORDER-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "SELL-SEND_SELL_ORDER-FAILED": "Ask user to retry"
    "SELL-SEND_SELL_ORDER-SUCCESSFUL": "Success"
    "SELL-TRACK_ORDER-INITIATED": "ETA: Market open"
    "SELL-TRACK_ORDER-MANUAL_INTERVENTION": "Tell user that the Order is being tracked and there is some delay in processing the order. Communicate that the developers are already looking into the problem and escalate to dev on-call"
    "SELL-TRACK_ORDER-FAILED": "Ask user to retry"
    "SELL-TRACK_ORDER-SUCCESSFUL": "Success"
    "SELL-PAYMENT_RECEIVED-PROCESSING": "Tell the user that the order is fulfilled and it will take 5 business days for money to reach back the account. Escalate to dev on-call in case of a problem"
    "SELL-PAYMENT_RECEIVED-INITIATED": "Tell the user that the order is fulfilled and it will take 5 business days for money to reach back the account. Escalate to dev on-call in case of a problem"
    "SELL-PAYMENT_RECEIVED-PENDING": "Tell the user that the order is fulfilled and ithas reached India and is under processing with the bank and will be processed in 2-3 business days"
    "SELL-PAYMENT_RECEIVED-MANUAL_INTERVENTION": "Tell the user that the order is fulfilled but there is some issue because of which there is a delay and money will reach the account soon. Escalate to dev on-call"
    "SELL-PAYMENT_RECEIVED-FAILED": "Tell the user that the order is fulfilled but there is some issue because of which there is a delay and money will reach the account soon. Escalate to dev on-call"
    "SELL-PAYMENT_RECEIVED-SUCCESSFUL": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_POA_AND_POI-PROCESSING": "The user needs to proceed and give consent to collect their POA and POI. In case the user says they are stuck in onboarding then please escalate to dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_POA_AND_POI-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_POA_AND_POI-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_POA_AND_POI-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_DISCLOSURES-PROCESSING": "The user needs to proceed and give disclosures. In case the user says they are stuck in onboarding then please escalate to dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_DISCLOSURES-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_DISCLOSURES-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_DISCLOSURES-SUCCESS": "In case this is success and they haven't moved to the next stage, it could be the case that they selected one of the options among PEP etc. which would move user into Future Scope. Explain to them what Politically exposed person, Shareholder etc. means and check with them whether they would like to revise their disclosure. Once that is done, escalate to dev on-call to reset their account opening. "
    "US_BROKER_ACCOUNT_OPENING_COLLECT_EMPLOYMENT_DETAILS-PROCESSING": "The user needs to proceed and give employment details or skip that. In case the user says they are stuck in onboarding then please escalate to dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_EMPLOYMENT_DETAILS-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_EMPLOYMENT_DETAILS-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_EMPLOYMENT_DETAILS-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_INVESTMENT_INTEREST-PROCESSING": "The user needs to proceed and give investment details or skip that. In case the user says they are stuck in onboarding then please escalate to dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_INVESTMENT_INTEREST-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_INVESTMENT_INTEREST-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_INVESTMENT_INTEREST-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_AGREEMENTS-PROCESSING": "The user needs to proceed and give agreement consents. In case the user says they are stuck in onboarding then please escalate to dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_AGREEMENTS-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_AGREEMENTS-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_AGREEMENTS-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_WAIT_FOR_DOCUMENTS_UPLOAD-PROCESSING": "There should not be any problem at this stage since this is a backend state. However, if this does show as PROCESSING, please escalate to dev on-call"
    "US_BROKER_ACCOUNT_OPENING_WAIT_FOR_DOCUMENTS_UPLOAD-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_WAIT_FOR_DOCUMENTS_UPLOAD-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_WAIT_FOR_DOCUMENTS_UPLOAD-SUCCESS": "There should not be any problem at this stage, but it could happen that the PAN fetch has failed and the user is asked to upload their PAN card. In such a case, please ask the user to upload their PAN to open their account. "
    "US_BROKER_ACCOUNT_OPENING_MANUAL_PAN_UPLOAD-PROCESSING": "In such a case, please ask the user to upload their PAN to open their account. Communicate to user that their automated PAN fetch failed and hence their PAN needs to be uploaded manually"
    "US_BROKER_ACCOUNT_OPENING_MANUAL_PAN_UPLOAD-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_MANUAL_PAN_UPLOAD-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_MANUAL_PAN_UPLOAD-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_FETCH_ADDRESS-PROCESSING": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_FETCH_ADDRESS-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_FETCH_ADDRESS-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_FETCH_ADDRESS-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_AML_CHECK-PROCESSING": "The user has probably got a hit on AML and their user will be manually checked by the operations team. However, this should not be communicated to the user. The communication to the user is that there are some background checks being performed for them and their account will be open soon."
    "US_BROKER_ACCOUNT_OPENING_AML_CHECK-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_AML_CHECK-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_AML_CHECK-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_SOURCE_OF_FUNDS-PROCESSING": "In such a case, the user needs to upload their Source of funds document. Communicate to user to "
    "US_BROKER_ACCOUNT_OPENING_COLLECT_SOURCE_OF_FUNDS-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_SOURCE_OF_FUNDS-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_COLLECT_SOURCE_OF_FUNDS-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_ACCOUNT_OPENING_WITH_VENDOR-PROCESSING": ""
    "US_BROKER_ACCOUNT_OPENING_ACCOUNT_OPENING_WITH_VENDOR-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_ACCOUNT_OPENING_WITH_VENDOR-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_ACCOUNT_OPENING_WITH_VENDOR-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_UPLOAD_KYC_DATA-PROCESSING": ""
    "US_BROKER_ACCOUNT_OPENING_UPLOAD_KYC_DATA-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_UPLOAD_KYC_DATA-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_UPLOAD_KYC_DATA-SUCCESS": "There should not be any problem at this stage"
    "US_BROKER_ACCOUNT_OPENING_POLL_ACCOUNT_STATUS-PROCESSING": ""
    "US_BROKER_ACCOUNT_OPENING_POLL_ACCOUNT_STATUS-INITIATED": "There should not be any problem at this stage. However, if the user reports they are stuck in this stage for a long time i.e. 2-3 days, then please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_POLL_ACCOUNT_STATUS-MANUAL_INTERVENTION": "Communicate to user that there is some error which happened in the system and the developers are already on it. Simultaneously please escalate to Dev on-call"
    "US_BROKER_ACCOUNT_OPENING_POLL_ACCOUNT_STATUS-SUCCESS": "There should not be any problem at this stage"
    "ACCOUNT_ACTIVITY_TYPE_DIVIDEND_ACCOUNT_ACTIVITY_STATE_DECLARED": "The dividend is being declared to user and expect to received in 2 market days"
    "ACCOUNT_ACTIVITY_TYPE_DIVIDEND_ACCOUNT_ACTIVITY_STATE_COMPLETED": "The dividend is credited user account"
    "ACCOUNT_ACTIVITY_TYPE_DIVIDEND_ACCOUNT_ACTIVITY_STATE_CANCELED": "The dividend is canceled for user"
    "ADD_FUNDS-POOL_ACCOUNT_TRANSFER-FAILED": "Payment failed due to any of the following reasons: \n 1. UPI PIN entered incorrectly \n 2. UPI PIN not entered \n 3. Any failure in the payment process on Backend"
    "ADD_FUNDS-POOL_ACCOUNT_TRANSFER-CANCELED": "Payment could not be initiated itself because of some bug, BE team will be checking"
    "ADD_FUNDS-POOL_ACCOUNT_TRANSFER-BLOCKED": "Payment under process, if this is showing up for a long time, user may have dropped off"
    "ADD_FUNDS-POOL_ACCOUNT_TRANSFER-SUCCESSFUL": "Successful"
    "ADD_FUNDS-POOL_ACCOUNT_TRANSFER-TIMEOUT": "Failed due to timeout, we did not receive a success signal. In case user's money is debited, escalate immediately"
    "ADD_FUNDS-POOL_ACCOUNT_TRANSFER-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "ADD_FUNDS-FOREIGN_FUND_TRANSFER-BLOCKED": "The IFT process is under process and the ETA is 1-2 working days. If this is in processing for a long time, please escalate to developers."
    "ADD_FUNDS-FOREIGN_FUND_TRANSFER-FAILED": "If this reaches failed stage, the user should receive a refund for the same within 2-3 working days."
    "ADD_FUNDS-FOREIGN_FUND_TRANSFER-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "ADD_FUNDS-FOREIGN_FUND_TRANSFER-SUCCESSFUL": "Successful"
    "ADD_FUNDS-INITIATE_WALLET_FUND_TRANSFER-INITIATED": "Payment is being processed and will take 1 working day to be credited"
    "ADD_FUNDS-INITIATE_WALLET_FUND_TRANSFER-FAILED": "Processing is delayed because of some BE bug, developers are already looking into it"
    "ADD_FUNDS-INITIATE_WALLET_FUND_TRANSFER-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "ADD_FUNDS-INITIATE_WALLET_FUND_TRANSFER-SUCCESSFUL": "It will take 2 hours for wallet credit to happen"
    "ADD_FUNDS-TRACK_WALLET_FUND_TRANSFER-FAILED": "It will take 2 hours for wallet credit to happen"
    "ADD_FUNDS-TRACK_WALLET_FUND_TRANSFER-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "ADD_FUNDS-TRACK_WALLET_FUND_TRANSFER-SUCCESSFUL": "Successful"
    "ADD_FUNDS-REFUND_PAYMENT-BLOCKED": "Refund is under process and will be processed within 2 days"
    "ADD_FUNDS-REFUND_PAYMENT-FAILED": "Refund has failed for some reason. Please escalate to developer"
    "ADD_FUNDS-REFUND_PAYMENT-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "ADD_FUNDS-REFUND_PAYMENT-SUCCESSFUL": "Refund was successful"
    "WITHDRAW_FUNDS-INITIATE_WALLET_FUND_TRANSFER-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "WITHDRAW_FUNDS-INITIATE_WALLET_FUND_TRANSFER-SUCCESSFUL": "Order is under processing, money will be received in 2-3 working days"
    "WITHDRAW_FUNDS-INITIATE_WALLET_FUND_TRANSFER-FAILED": "Ask person to retry withdrawal"
    "WITHDRAW_FUNDS-TRACK_WALLET_FUND_TRANSFER-FAILED": "Ask person to retry withdrawal"
    "WITHDRAW_FUNDS-TRACK_WALLET_FUND_TRANSFER-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "WITHDRAW_FUNDS-TRACK_WALLET_FUND_TRANSFER-SUCCESSFUL": "Success"
    "WITHDRAW_FUNDS-INITIATE_INWARDS_REMITTANCE_STATUS-BLOCKED/ INITIATED": "Inward remittance is under process and will take 2 days"
    "WITHDRAW_FUNDS-INITIATE_INWARDS_REMITTANCE_STATUS-SUCCESSFUL": "Inward remittance is under process and will take 6 hours"
    "WITHDRAW_FUNDS-INITIATE_INWARDS_REMITTANCE_STATUS-FAILED": "Inward remittance has failed. Please escalate to dev"
    "WITHDRAW_FUNDS-INITIATE_INWARDS_REMITTANCE_STATUS-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"
    "WITHDRAW_FUNDS-TRACK_INWARDS_REMITTANCE_STATUS-SUCCESSFUL": "Success"
    "WITHDRAW_FUNDS-TRACK_INWARDS_REMITTANCE_STATUS-FAILED": "Inward remittance has failed. Please escalate to dev"
    "WITHDRAW_FUNDS-TRACK_INWARDS_REMITTANCE_STATUS-MANUAL_INTERVENTION": "Processing is delayed because of some BE bug, developers are already looking into it"

  ExpectedEtaForDividend: 48h # 2days is the eta
  UseAccountActivities: true

InternationalFundsTransferConfig:
  ForexRateReportSlackChannelId: "C058MNMBMMK" # save-reports-testing

DevActionHelperConfig:
  IsBulkResourceAccessibilityCheckEnabled: true

ClosedAccountConfig:
  AutoResolveAmount: 1
  IssueCategoryId: "46c4f9a2-4c2c-5e03-8ffc-2b818a6c1cb5"

OverrideBankActions:
  MaxRequests: 100

ReviewAction:
  CommonQuestionsToUser: [
      "1. What is the Source of Funds of the user?",
      "2. Reason for Utilisation of funds?",
      "3. What purpose the account is being used for?",
      "4. Who is the end user of this account?",
      "5. What was the purpose of this transaction? Amount: <>,  Date: <>",
  ]

MonorailConfig:
  ServiceAccountEmail: "<EMAIL>"
  AuthScopes: ["https://www.googleapis.com/auth/userinfo.email"]
  AuthorEmail: "<EMAIL>"
  IssueURL: "https://monorail-prod-321008.el.r.appspot.com/_ah/api/monorail/v1/projects/%s/issues"
  CommentURL: "https://monorail-prod-321008.el.r.appspot.com/_ah/api/monorail/v1/projects/%s/issues/%s/comments"
  ProjectId: "staging-fi-app"

FreshdeskMonorailIntegrationConfig:
  # the format for monorail issue subject is “FD - “+FD Ticket number+ ”FD Title” (Ref: https://docs.google.com/document/d/1ykqPcXa4AiGubvEGqPJAUF6IyXZ_QBVudG5MB3WvmwE/edit#bookmark=id.se3xjg5mzhxj)
  MonorailIssueSubjectFormat: "FD - %d : %s"
  # using lower case for keys to avoid compatibility issues
  # mappings are as per the sheet shared by PE team: https://docs.google.com/spreadsheets/d/1R4KpUp1-Pse1N2LLz4fShAXDlQhi2dWR8m0m31bYnq8/edit#gid=*********
  MonorailComponentOwnerMap:
    cx: ["<EMAIL>"]
    cx>chat: ["<EMAIL>"]
    lending>creditcard: ["<EMAIL>"]
    pay: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
          "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
          "<EMAIL>", "<EMAIL>"]
    pay>balance: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                  "<EMAIL>", "<EMAIL>"]
    pay>cx: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
             "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
             "<EMAIL>", "<EMAIL>"]
    pay>celestial: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                    "<EMAIL>", "<EMAIL>"]
    pay>decisionengine: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                         "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                         "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>fundstransfer: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>"]
    pay>landing: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                  "<EMAIL>", "<EMAIL>"]
    pay>merchant: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                   "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                   "<EMAIL>", "<EMAIL>"]
    pay>notifications: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>"]
    pay>orderworkflow: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                        "<EMAIL>", "<EMAIL>"]
    pay>parser: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                 "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                 "<EMAIL>", "<EMAIL>"]
    pay>partnersdk: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                     "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                     "<EMAIL>", "<EMAIL>"]
    pay>payflow: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                  "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                  "<EMAIL>", "<EMAIL>"]
    pay>paymentinstruments: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                             "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                             "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>recon: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                "<EMAIL>", "<EMAIL>"]
    pay>recurringpayment: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                           "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                           "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>standinginstruction: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                              "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                              "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>statements: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                     "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                     "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>timeline: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                   "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                   "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>upi: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
              "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
              "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>upi>addfunds: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                       "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                       "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>upi>global: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                     "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                     "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>upi>mandates: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                       "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                       "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>upi>mapper: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                     "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                     "<EMAIL>", "<EMAIL>", "<EMAIL>"]
    pay>upi>tpap: ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                   "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>",
                   "<EMAIL>", "<EMAIL>"]
    creditcard: ["<EMAIL>"]
    onboarding>cx: ["<EMAIL>"]
    debitcard: ["<EMAIL>"]
    rewards: ["<EMAIL>"]
    onboarding: ["<EMAIL>"]
    lending>lamf>cx: ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
  MonorailIssueDefaultLabels: ["Freshdesk-escalation"]
  # source: https://docs.google.com/document/d/1ykqPcXa4AiGubvEGqPJAUF6IyXZ_QBVudG5MB3WvmwE/edit#bookmark=id.4pqzcirhsi49
  PrefixForForwardingMonorailComment: "[External]"
  # NOTE: MonorailToFreshdeskPrivateNotePrefix is used to check whether a given private note has been published from monorail
  MonorailToFreshdeskPrivateNotePrefix: "[Comment(s) from Monorail Ticket]"
  FreshdeskToMonorailCommentPrefix: "[FD - %d] "
  # Monorail attachments for non-prod project (staging-fi-app) are stored in https://drive.google.com/drive/folders/1xBXTxK2lB6Ysz73F6yZwFAdnYJ0nK3BB
  GoogleDriveFolderIdForAttachments: "1xBXTxK2lB6Ysz73F6yZwFAdnYJ0nK3BB"

EmployerDbConfig:
  EsHostUrl: "https://vpc-deploy-search-access-j3gbgxj77i5tyqkoopmi5pklzq.ap-south-1.es.amazonaws.com/"
  IndexingTimeDuration: "24h"

AirflowConfig:
  TriggerDagUrl: "http://**********:8080/api/v1/dags/%s/dagRuns"

EmailValidationRegex: (?:[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*|\"(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21\x23-\x5b\x5d-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\x01-\x08\x0b\x0c\x0e-\x1f\x21-\x5a\x53-\x7f]|\\[\x01-\x09\x0b\x0c\x0e-\x7f])+)\])

GRPCWebServerConfig:
  JarvisInterceptorConf:
    DisableAuth: false
  HttpCorsOptions:
    AllowAll: true
  KeycloakAuth:
    TokenURL: "https://keycloak.pointz.in/realms/InternalNonProd/protocol/openid-connect/token"
    OIDC:
      ProviderURL: "https://keycloak.pointz.in/realms/InternalNonProd"
      GoOIDC:
        ClientID: "jarvis-deploy"
        SkipClientIDCheck: true


SherlockUserRequestsConfig:
  AccountTypeEnumToString:
    ACCOUNT_TYPE_SAVINGS: "Federal Savings"
  DateTimePickerConfig:
    MinValue:
      Year: 2019
      Month: 01
      Day: 01
    DateFormat: "dd MMMM, yyyy"
    IsTimeAllowed: false
    IsDisabled: false

IssueCategoryIdForCategory:
  L1InAppTransactions: "********-c93f-5a1a-b0f7-b350e45850ad"    # In-App Transactions        | -                                                  | -
  L1OffAppTransactions: "18ac3a53-2c95-5bce-9e09-5736323a5b32"   # Off-App Transactions       | -                                                  | -
  L1DebitCard: "99b0068f-04a5-549e-8685-aa2d3abd8083"            # Debit Card                 | -                                                  / -
  L1DebitCardL2Atm: "738704aa-e68a-512e-ac6b-456ff6bf6324"       # Debit Card                 | ATM Related                                        | -
  L1DebitCardL2PosEcom: "25cc67df-058f-57d5-8fc2-245be2e39cb6"   # Debit Card                 | POS/ECOM                                           | -

SalaryProgramLeadManagementConfig:
  SalaryProgramS3BucketName: "epifi-salaryprogram"
  SalaryProgramB2BS3BucketName: "epifi-salaryprogram-b2b"
  LeadDetailsExcelSheetPathB2B: "LeadDetailsTest.xlsx"

SherlockBannersConfig:
  IsServiceEnabledForDynamicFetching:
    CX_SERVICE: true
    PRE_APPROVED_LOAN_SERVICE: true
    RISK_SERVICE: true
    TIERING_SERVICE: true
  PriorityOrder:
    # in decreasing order of priority. eg: A banner belonging to RISK_SERVICE gets higher priority than compared to CX_SERVICE
    ServiceName: ["PRE_APPROVED_LOAN_SERVICE", "TIERING_SERVICE", "RISK_SERVICE", "CX_SERVICE"]

RudderEventKafkaConsumerGroup:
  # These fields will remain common for a Kafka consumer group across all envs
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  Whitelist:
    Enabled: true
    KeyPath: "properties.is_watson_event"
    AllowedValues:
      - "true":

Transaction:
  PageSizeForExistingMandates: 10

AgentPromptConfig:
  AgentPromptInfoMap:
    AGENT_PROMPT_TYPE_DEBIT_CARD_REQUEST_AND_TRACKING:
      IsPromptEnabled: true
      PromptValueForAgent: "Debit Card Request & Tracking"
      Description: "Request for physical debit card or tracking debit card"
      PromptCommsTemplate:
        Title: "Track your debit card delivery"
        Description: "Click to track your debit card delivery"
    AGENT_PROMPT_TYPE_DEBIT_CARD_USAGE:
      IsPromptEnabled: true
      PromptValueForAgent: "Debit Card Usage"
      Description: "Enable online, enable contactless, enable ATM withdrawals, enable POS, Enable International Usage"
      PromptCommsTemplate:
        Title: "Change your debit card settings"
        Description: "Click to change debit card settings"
    AGENT_PROMPT_TYPE_CHEQUEBOOK_REQUEST_AND_TRACKING:
      IsPromptEnabled: true
      PromptValueForAgent: "Chequebook Request & Tracking"
      Description: "Request chequebook, Request cancelled chequebook and track chequebook, Tax statement ELSS"
      PromptCommsTemplate:
        Title: "Chequebook request & tracking"
        Description: "Click to request and track your chequebook"

RiskOpsInstalledAppsConfig:
  IsEnabled: true
  RiskScoreMinThreshold: 10
  NumberOfLEAWithAppInstalledMinThreshold: 5

RiskFennelConfig:
  APIVersion: 3

StageWiseCommsConfig:
  IsGenericCommsEnabled: true
  IsIssueConfigSpecificCommsEnabled: true
  IsPublishingManualTicketCreationEventEnabled: true
  IsPublishingManualTicketUpdateEventEnabled: true

IssueConfigServiceConfig:
  ConfigTypeMapping:
    CONFIG_TYPE_WATSON: "Watson incident lifecycle"
    CONFIG_TYPE_WATSON_TICKET_DETAILS: "Watson ticket details"
    CONFIG_TYPE_INCIDENT_STAGE_BASED_COMMS_DETAILS: "Watson stage-wise comms"
    CONFIG_TYPE_MANUAL_TICKET_STAGE_BASED_COMMS_DETAILS: "Manual ticket stage-wise comms"
    CONFIG_TYPE_TICKET_SLA: "Ticket resolution SLA"
    CONFIG_TYPE_IS_FCR_FLAG: "Is FCR"
    CONFIG_TYPE_IN_APP_TICKET_DETAILS: "In-app ticket details"
    CONFIG_TYPE_AGENT_REFERENCE_MATERIAL: "Agent reference material"
    CONFIG_TYPE_ISSUE_RESOLUTION_CSAT_SURVEY: "CSAT survey"
    CONFIG_TYPE_MONORAIL_COMPONENTS: "Monorail component"
    CONFIG_TYPE_PRIORITY: "Priority"
    CONFIG_TYPE_APP_RESOLUTION_DEEPLINK: "App resolution deeplink"
    CONFIG_TYPE_USER_FRIENDLY_CATEGORY_TEXT: "User friendly category text"
    CONFIG_TYPE_RELATED_FAQ_ARTICLES: "Related FAQ article"
    CONFIG_TYPE_IS_DEPRECATED: "Is deprecated"
    CONFIG_TYPE_CAN_LLM_RESOLVE: "Can LLM resolve"
  IssueConfigLevelCacheKey: "cx_issue_config_level:%s:%s:%s:%s"
  IssueConfigLevelCacheValidityDuration: "72h"

S3EventConsumerConfig:
  BucketName: "epifi-cx"
  CallSummarizationFilePath: "test/call_categorization/"
  IsCallSummarizationProcessingEnabled: true

RiskTxnReviewRolloutConfig:
  IsSelectedOrderRpcEnabledForAll: false

StrapiConfig:
  HttpClientConfig:
    Transport:
      DialContext:
        Timeout: 30s
        KeepAlive: 30s
      TLSHandshakeTimeout: 10s
      MaxIdleConns: 100
      IdleConnTimeout: 90s
    Timeout: 10s
  FilterParamFields: ["slug"]
  PopulateFields: ["solution.picture_guide.guides.picture","solution.post_call_steps","keywords"]

CaseManagementActorActivities:
  IsEnabled: true
  Timeout: 3s
  AllowedReviewTypes: ["REVIEW_TYPE_USER_REVIEW", "REVIEW_TYPE_LEA_COMPLAINT_REVIEW", "REVIEW_TYPE_TRANSACTION_REVIEW", "REVIEW_TYPE_AFU_REVIEW"]

ErrorActivityConfig:
  DefaultIncidentCreationCoolOffPeriod: "10m"
  IssueCategoryIdEventPayloadKey: "watson_issue_category_id"
  ClientRequestIdEventPayloadKey: "watson_client_request_id"
  IsResolutionEventBooleanEventPayloadKey: "is_watson_resolution_event"

ContactUsModelResponseConfig:
  ResponseCacheKey: "cx:model_response:%s"
  ResponseCacheValidityDuration: "168h"

WhiteListAccessLevelForActorEnrichmentByPhoneOrEmail:
  AllowedAccessLevels: ["SALARY_WHITELIST_B2B","SALARY_DATA_OPS","SALARY_ADMIN"]

EnableBalanceMetricsOnCaseManagement: true
EnableOutCallDataInCaseManagementForRiskOps: true

DbStateConfig:
  IsRbacEnabled: true

FederalEscalationConfig:
  IsUpdateConsumerEnabled: true
  QueueId: "300000412618875"
  QPHRateLimit: 500

SaClosureEligibilityConf:
  #  Maximum processing time in minutes.
  #  With a rate limit of 2 QPS, we can process up to 3600 requests in 30 minutes (2 * 60 * 30).
  #  With vendor API calls taking 10s each, we need 20 workers to fully utilize our 2 QPS rate limit.
  #  This is because: 2 QPS * 10s processing time = 20 concurrent workers needed.
  #  At 2 QPS, processing 3000 actors would take at least 1500 seconds (25 minutes).
  MaxProcessingDuration: "40m"
  QpsRateLimit: 2
  #  Number of workers to process jobs
  #  With 10s API calls and 2 QPS rate limit, we need 20 workers to maximize throughput
  MaxConcurrentWorkers: 20
  MaxActorsAllowedInCsv: 3000

LienConfig:
  LienAmount:
    CurrencyCode: "INR"
    Units: 10
  LienDurationInHours: 48
  AllowedReasons:
    - "Income Discrepancy"
  LienRolloutPercentage: 100

IsNewOperationalStatusAPIEnabled: true
