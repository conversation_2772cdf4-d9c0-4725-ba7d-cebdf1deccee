Application:
  Environment: "test"
  Name: "cx"

Server:
  Ports:
    GrpcPort: 8095
    GrpcSecurePort: 9508
    HttpPort: 9896
    HttpSecurePort: 9785

EpifiDb:
  AppName: "cx"
  StatementTimeout: 5m
  Name: "sherlock_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Cognito:
  UserPoolId: "ap-south-1_PuPQfko25"
  ClientId: "2f29r98to1sukqs5tu4ontka2v"

EmailVerification:
  VerificationUrl: "http://localhost:8150/verify"
  FromEmail: "<EMAIL>"
  FromEmailName: "Fi support"

MobilePromptVerification:
  NotificationTitle: "Verify Yourself"
  NotificationBody: "Please choose yes if the request was initiated by you"
  Validity: 300 #validity of mobile prompt in seconds

AuthFactorRetryLimit:
  DOB: 3
  MobilePrompt: 3
  EmailVerification: 3
  TransactionAmount: 3
  LastFivePanCharacters: 3
  PermanentAddressPinCode: 3
  FathersName: 3
  MothersName: 3
  Default: 3

CustomerAuth:
  AuthValidityDuration: "10m"
  EmailValidityDuration: "5m"
  MobilePromptValidityDuration: "2m"
  MaxResetCount: 1
  IsInAppNotificationEnabled: true
  AuthFactorPriorityByPlatform:
    ANDROID:
      MinAppVersion: 0
      PriorityMap:
        MOBILE_PROMPT: 1
        EMAIL_VERIFICATION: 2
        TRANSACTION_AMOUNT: 3
        LAST_FIVE_PAN_CHARACTERS: 4
        MOTHERS_NAME: 5
        FATHERS_NAME: 6
        DOB: 7
        PERMANENT_ADDRESS_PIN_CODE: 8
    IOS:
      MinAppVersion: 361
      PriorityMap:
        EMAIL_VERIFICATION: 1
        MOBILE_PROMPT: 2
        TRANSACTION_AMOUNT: 3
        LAST_FIVE_PAN_CHARACTERS: 4
        MOTHERS_NAME: 5
        FATHERS_NAME: 6
        DOB: 7
        PERMANENT_ADDRESS_PIN_CODE: 8
    PLATFORM_UNSPECIFIED:
      MinAppVersion: 0
      PriorityMap:
        EMAIL_VERIFICATION: 1
        MOBILE_PROMPT: 2
        TRANSACTION_AMOUNT: 3
        LAST_FIVE_PAN_CHARACTERS: 4
        MOTHERS_NAME: 5
        FATHERS_NAME: 6
        DOB: 7
        PERMANENT_ADDRESS_PIN_CODE: 8
  IsSkippingAuthEnabledAfterExternalAuth: false

FreshdeskTicketPublisher:
  QueueName: "freshdesk-ticket-queue"

FreshdeskTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "freshdesk-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskContactPublisher:
  QueueName: "freshdesk-contact-queue"

FreshdeskContactSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "freshdesk-contact-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputePublisher:
  QueueName: "cx-dispute-queue"

DisputeSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-dispute-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputeCreateTicketPublisher:
  QueueName: "cx-dispute-create-ticket-queue"

DisputeCreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-dispute-create-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

DisputeUpdateTicketPublisher:
  QueueName: "cx-dispute-update-ticket-queue"

DisputeUpdateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-dispute-update-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1     # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 6s  # 10 per minute
    Namespace: "cx"

DisputeAddNoteTicketPublisher:
  QueueName: "cx-dispute-add-note-ticket-queue"

DisputeAddNoteTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-dispute-add-note-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

WatsonIncidentReportingPublisher:
  QueueName: "cx-watson-incident-reporting-queue"

WatsonIncidentReportingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-watson-incident-reporting-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

WatsonIncidentResolutionPublisher:
  QueueName: "cx-watson-incident-resolution-queue"

WatsonIncidentResolutionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-watson-incident-resolution-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1      # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 3s   # 20 per minute
    Namespace: "cx"

WatsonTicketEventPublisher:
  QueueName: "cx-watson-ticket-event-queue"

WatsonTicketEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-watson-ticket-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "cx"

WatsonCreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-watson-create-ticket-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "cx"

CrmIssueTrackerIntegrationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-crm-issue-tracker-integration-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

CrmIssueTrackerIntegrationPublisher:
  QueueName: "cx-crm-issue-tracker-integration-queue"

DisputeExternalPublisher:
  QueueName: "cx-dispute-events-external-queue"

RMSEventPublisher:
  QueueName: "rms-event-queue"

RewardsManualGiveawayEventPublisher:
  QueueName: "rewards-manual-giveaway-event-queue"

DevActionPublisher:
  QueueName: "dev-action-delay-queue"

IFTFileProcessorEventPublisher:
  QueueName: "pay-international-fund-transfer-process-file-queue"

DevActionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-action-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 60
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskTicketDataEventSubscriberFifo:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-fd-events-cx-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FreshdeskTicketDataEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-freshdesk-events"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

AuditLog:
  DefaultLimit: 5

Sherlock:
  SherlockCallbackURL: "http://localhost:8150/external-api/v1/customer-auth"
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30

Secrets:
  Ids:
    SherlockApiKey: "{\"api-key\": \"57b7c4e5-b6e8-46f0-a5f2-a173e120cf8b\"}"
    DbUsernamePassword: "{\"username\": \"root\", \"password\": \"\"}"
    FreshchatAppId: "ece35ccc-cf4d-4f25-aba2-2ac4a893eea4"
    FreshchatAppKey: "34411d8f-0a2a-41d5-a282-63ca27c82f74"
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    FederalPoolAccountNo: "****************"
    MonorailServiceAccountKey: "{\n  \"private_key_id\": \"xyz\",\n  \"private_key\": \"xyz-key\"\n}"
    AirflowUsernamePassword: "{\n  \"username\": \"xyz\",\n  \"password\": \"xyz-key\"\n}"
    IFTReportsSlackBotOauthToken: "dummy/ift/slack-bot-oauth-token"
    StrapiApiKey: ""


Transaction:
  ToleranceValue: 10
  NumTxnToFetch: 3
  PageSize: 20

Comms:
  PageSize: 20

Dispute:
  DefaultDisputeConfigVersion: "DISPUTE_CONFIG_VERSION_V3"
  ConfigVersionToDisputeConfigMap:
    DISPUTE_CONFIG_VERSION_V1:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp-test.csv"
        DecisionTreeCSVPath: "mappingCsv/decision-tree-test.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree-test.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        INTRA_BANK: true
    DISPUTE_CONFIG_VERSION_V2:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp-test.csv"
        DecisionTreeCSVPath: "mappingCsv/decision-tree-test.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree-test.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        INTRA_BANK: true
    DISPUTE_CONFIG_VERSION_V3:
      DecisionTreeConfig:
        QuestionsCSVPath: "mappingCsv/questionnaire-dmp-test.csv"
        DecisionTreeCSVPath: "mappingCsv/decision-tree-test.csv"
        AppDecisionTreeCSVPath: "mappingCsv/app-decision-tree-test.csv"
      PaymentProtocolAllowedFromApp:
        UPI: true
        IMPS: true
        NEFT: true
        RTGS: true
        DEBIT_CARD_ATM: true
        DEBIT_CARD_POS: true
        DEBIT_CARD_ECOM: true
        CHEQUE: true
        INTRA_BANK: true
        CARD: true
  IsRestrictedReleaseEnabledForConfigVersion: false
  S3BucketName: "epifi-federal-disputes"
  MaxThresholdDurationForEscalation: "2h"
  MaxAttemptCountForReverseProcessing: 5
  IsUpiExternalProvenanceEvaluationEnabled: true

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0

AppLog:
  LogTTL: "24h"
  MaxLogCountPerUser: 5
  # RPC size limit is 4 MB. So using 3.5MB as chunk size
  LogChunkSize: 3500000

Flags:
  TrimDebugMessageFromStatus: false

Payout:
  StatusCheckDelay: "30m"
PayoutStatusCheckPublisher:
  QueueName: "cx-payout-status-check-event-queue"
PayoutStatusCheckSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-payout-status-check-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

WaitlistSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "freelancer-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

RateLimit:
  MaxRequestsPerMinPerUser: 1000
  MaxRequestPerMinPerUserPerApiDefault: 500
  # replace all occurrences of . and / with _ in the full method name before adding it here
  # need to do this because . and / are interpreted differently by config loader and should not be used in key
  MaxRequestPerMinPerUserPerApiMap:
    _cx_app_log_AppLog_GetLogsData: 1000
    _cx_data_collector_preapprovedloan_PreApprovedLoan_GetLoanDetails: 10
    _cx_data_collector_preapprovedloan_PreApprovedLoan_GetLoanUserDetails: 10

RlConfig:
  ResourceMap:
    sherlock_user:
      Rate: 1000
      Period: 1m
    _cx_app_log_AppLog_GetLogsData:
      Rate: 1000
      Period: 1m
    api_default:
      Rate: 1000
      Period: 1m
  Namespace: "cx"

UsePkgRateLimiter: true

CallRecording:
  CallRecordingBucketName: "epifi-data-prod-ozonetel-call-recs"
  CallTranscriptionBucketName: "epifi-ozonetel-transcription"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

OrderConfig:
  TxnCountForLastNTxns: 5

LivenessVideoConfig:
  S3BucketName: "epifi-liveness"

ProcessTicketJobConfig:
  MaxTicketsThresholdMap:
    ONBOARDING: 10
    RE_ONBOARDING: 10
    UPI_PINSET: 10
    DEBIT_CARD: 10
  JobStatsEmailParam:
    FromEmailId: "<EMAIL>"
    FromEmailName: "Process Ticket Automation non-prod"
    ReceiverMailIdList:
      ONBOARDING:
        ReceiverMailInfo1:
          EmailName: "Diparth"
          EmailId: "<EMAIL>"
        ReceiverMailInfo2:
          EmailName: "Sachin"
          EmailId: "<EMAIL>"
        ReceiverMailInfo3:
          EmailName: "Hardik"
          EmailId: "<EMAIL>"
    EmailMsg:
      ONBOARDING: "Please find troubleshooting details in attachment."
  NumberOfDays: 89

MaxCountThresholdForFetchingBulkUserInfo: 10

BulkUserInfoViaEmailConfig:
  MaxCountThreshold: 2

UpiDisputeAutoUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-upi-dispute-auto-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

AuthValidation:
  SkipAuthForTestAutomationAgent: true
  TestAutomationAgentEmails: ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
  MethodListForSkippingAccessControlValidation: [
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
  ]

UpdateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-update-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 80    # Keeping cumulative limit across relevant queues below freshdesk Update API (PUT) rate limit of 160 per minute
        Period: 1m
    Namespace: "cx"

UpdateTicketPublisher:
  QueueName: "cx-update-ticket-queue"

BulkTicketJobConfig:
  MaxTicketThreshold: 1000

UploadCreditMISToVendorPublisher:
  QueueName: "mf-upload-credit-mis-non-prod-queue"

OzonetelCallEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vn-ozonetel-call-details-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

TicketReconciliationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-ticket-data-reconciliation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 3
      TimeUnit: "Minute"

FreshchatEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vn-freshchat-action-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "cx"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CelestialSignalWorkflowPublisher:
  QueueName: "celestial-signal-workflow-queue"

CallRoutingConfig:
  IsManualRoutingEnabled: true
  IsSalaryProgramRoutingEnabled: true
  IsPriorityRoutingEnabled: true
  IsCreditCardRoutingEnabled: true
  CallLangPrefReleaseConfig:
    IsRestrictedReleaseEnabled: true
    # if IsRestrictedReleaseEnabled is false then IsEnabledForPlatform and IsEnabledForUserGroup will not be evaluated
    IsEnabledForPlatform:
      ANDROID: true
      IOS: true
    IsEnabledForUserGroup:
      INTERNAL: true

ChatBotConfig:
  IsReleaseEvaluationEnabled: true

SherlockFeedbackDetailsConfig:
  PageSize: 20

RiskCasePublisher:
  QueueName: "risk-cases-ingestion-queue"

RiskDisputePublisher:
  QueueName: "risk-dispute-upload-queue"

RiskS3Config:
  BucketName: "epifi-risk"

DataS3Config:
  BucketName: "epifi-raw-dev"
  S3Prefix: "qa/data/vendor/segmentation_service"
  StaticSegmentSrcFolderPath: "manual_dump/static_segment"
  StaticSegmentDestFolderPath: "static_segments"

InternationalFundTransfer:
  DocumentsBucketName: "epifi-pay-international-fund-transfer"

FreshdeskMonorailIntegrationConfig:
  MonorailComponentOwnerMap:
    area1>subarea1: ["<EMAIL>"]
    area2: ["<EMAIL>"]
  MonorailIssueDefaultLabels: ["Freshdesk-escalation"]

EmployerDbConfig:
  EsHostUrl: "http://localhost:9200/"

AirflowConfig:
  TriggerDagUrl: "http://**********:8080/api/v1/dags/%s/dagRuns"

SherlockBannersConfig:
  IsServiceEnabledForDynamicFetching:
    CX_SERVICE: true
    RISK_SERVICE: true
    TIERING_SERVICE: true
    PRE_APPROVED_LOAN_SERVICE: true
  PriorityOrder:
    # in decreasing order of priority. eg: A banner belonging to RISK_SERVICE gets higher priority than compared to CX_SERVICE
    ServiceName: ["PRE_APPROVED_LOAN_SERVICE","TIERING_SERVICE", "RISK_SERVICE", "CX_SERVICE"]

CallRoutingEventPublisher:
  TopicName: "cx-call-routing-event-topic"

TicketUpdateEventPublisher:
  TopicName: "cx-ticket-update-event-topic"

CreateTicketEventPublisher:
  TopicName: "cx-ticket-create-event-topic"

FederalEscalationCreateEventPublisher:
  QueueName: "cx-escalation-creation-queue"

FederalEscalationUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-escalation-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

FederalEscalationCreationEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-escalation-creation-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

ErrorActivityConfig:
  IsPipingErrorEventToWatsonEnabled: true


CreateTicketPublisher:
  QueueName: "cx-create-ticket-queue"

CreateTicketSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-create-ticket-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

RewardsCreditCardTxnEventQueuePublisher:
  QueueName: "rewards-credit-card-txn-event-queue"

RewardsOrderUpdateEventQueuePublisher:
  QueueName : "rewards-order-update-queue"

CasperItcDownloadFileQueuePublisher:
  QueueName: "casper-itc-download-file-queue"

OrderUpdateEventForTxnCategorizationPublisher:
  QueueName: "categorizer-update-order-queue"

AATxnCategorizationPublisher:
  QueueName: "categorizer-aa-txn-queue"

CCTxnCategorizationPublisher:
  QueueName: "categorizer-cc-transaction-event-queue"


SalaryOpsConfig:
  SalaryProgramS3BucketName: "epifi-salaryprogram"

S3EventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cx-s3-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 10
      MaxAttempts: 5
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1m
    Namespace: "cx"

StrapiConfig:
  BaseURL: "https://strapi.staging.pointz.in/api/"
  ApiKey: "staging/cx/strapi"
  HttpClientConfig:
    Transport:
      DialContext:
        Timeout: 30s
        KeepAlive: 30s
      TLSHandshakeTimeout: 10s
      MaxIdleConns: 100
      IdleConnTimeout: 90s
    Timeout: 10s

RiskOutcallFormRolloutConfig:
  WhitelistedQuestionnaireTemplates: ["QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_AMOUNT",
                                      "QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT",
                                      "QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT_AND_AMOUNT",
                                      "QUESTIONNAIRE_TEMPLATE_STOP_BUSINESS_ACTIVITY_ACK"]
  MaxFormsPerUser: 1
  WhitelistedReviewTypes: ["REVIEW_TYPE_TRANSACTION_REVIEW"]

CXFreshdeskTicketBaseURL: "https://ficaretesting.freshdesk.com/a/tickets/%s"

WatchlistReasons:
  - "WATCHLIST_REASON_UNSPECIFIED"
  - "WATCHLIST_REASON_FOR_TESTING"

CallConfig:
  IsCallBlockingEnabledViaIvrFlow: false
  IsCallBlockerEnabled: false
  CallBlockerConfig:
    IsCallBlockerEnabled: false

S3BucketNameForFileGenerator:
  CamsS3Bucket: "epifi-mutualfund"
  KarvyS3Bucket: "epifi-mutualfund-karvy"

FederalEscalationConfig:
  FederalEscalationAttachmentBucketName: "epifi-cx-ticket-attachments"
  QueueId: "300000412618875"
  QPHRateLimit: 500

EpifiIconS3Config:
  BucketName: "epifi-icons"
