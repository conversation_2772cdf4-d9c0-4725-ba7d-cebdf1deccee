package sherlock_banners

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	types "github.com/epifi/gamma/api/typesv2"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

const (
	defaultMaxUserBannersCount = 1
)

// nolint:funlen
func (s *Service) GetBannersForCustomer(ctx context.Context, req *sbPb.GetBannersForCustomerRequest) (*sbPb.GetBannersForCustomerResponse, error) {
	maxBanners := req.GetMaxNumOfBanners()
	if maxBanners == 0 {
		maxBanners = defaultMaxUserBannersCount
	}
	actorId := req.GetHeader().GetActor().GetId()
	if actorId == "" {
		cxLogger.Error(ctx, "actor info was no enriched", zap.Int64(logger.TICKET_ID, req.GetHeader().GetTicketId()))
		return &sbPb.GetBannersForCustomerResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	// Retrieve list of services which need to be invoked for fetching banners.
	serviceNameList := s.getEnabledServiceNames()
	serviceEnumList := convertServiceNamesToEnumsList(ctx, serviceNameList)
	// Get banners from all services
	bannersList, err := s.GetBannersFromAllServices(ctx, actorId, serviceEnumList)
	if err != nil {
		cxLogger.Error(ctx, "failed to get sherlock banners for user", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &sbPb.GetBannersForCustomerResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &sbPb.GetBannersForCustomerResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// Sort the banners according to the priority order criteria
	// in the decreasing order of precedence i.e. first element has the highest priority.
	// Stable sort to make sure the order given by a particular BE services is not changed.
	sort.SliceStable(bannersList, func(i, j int) bool {
		return comparePriorities(bannersList[i], bannersList[j], getPriorityRanksFromOrderedList(s.conf.SherlockBannersConfig().PriorityOrder()["ServiceName"]))
	})

	respListLen := integer.Min(int(maxBanners), len(bannersList))
	return &sbPb.GetBannersForCustomerResponse{
		Status:  rpcPb.StatusOk(),
		Banners: bannersList[:respListLen],
	}, nil
}

func (s *Service) GetBannersFromAllServices(ctx context.Context, actorId string, serviceList []types.ServiceName) ([]*sbPb.SherlockBanner, error) {
	fetchRequest := &sbPb.FetchSherlockBannersRequest{
		ActorId:           actorId,
		StructureTypeList: []sbPb.SherlockBannerStructureType{sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST},
	}
	// make async calls to each service in serviceList
	var allBanners []*sbPb.SherlockBanner
	errGroup, _ := errgroup.WithContext(ctx)
	numOfServices := len(serviceList)
	errGroup.SetLimit(numOfServices)
	bannersChan := make(chan []*sbPb.SherlockBanner, numOfServices)
	errorsChan := make(chan error, numOfServices)
	for _, service := range serviceList {
		// Copy the service name to a new variable to have it dedicated to the current goroutine.
		// Otherwise, by the time the goroutine executes, the service variable may have a different value
		srvcCopy := service
		errGroup.Go(func() error {
			// Not using error group ctx because, the err group Context is canceled the first time a function passed to Go
			// returns a non-nil error. But we would want to continue other goroutines even if one fails.
			bannerList, srvcErr := s.fetchBannersFromService(ctx, srvcCopy, fetchRequest)
			if srvcErr != nil {
				cxLogger.Error(ctx, "failed to fetch sherlock banners from service",
					zap.String(logger.SERVICE, srvcCopy.String()), zap.Error(srvcErr))
				err := errors.Wrap(srvcErr, fmt.Sprintf("failed to fetch sherlock banners from %s", srvcCopy.String()))
				errorsChan <- err
				return err
			}
			if len(bannerList) != 0 {
				bannersChan <- bannerList
			}
			return nil
		})
	}
	// wait until all goroutines are executed
	// not catching error here since we are pushing errors into a channel
	_ = errGroup.Wait()
	// not deferring to close the channel since we need to read the data from the channel
	close(bannersChan)
	close(errorsChan)
	// check if we should fail on the errors received in the error channel
	if errFromChan := getErrFromChannel(errorsChan, numOfServices); errFromChan != nil {
		cxLogger.Error(ctx, "failed to fetch sherlock banners from all services", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(errFromChan))
		return nil, errFromChan
	}
	for banners := range bannersChan {
		allBanners = append(allBanners, banners...)
	}
	if len(allBanners) == 0 {
		cxLogger.Info(ctx, "no sherlock banners found", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no sherlock banners fetched")
	}
	return allBanners, nil
}

func getErrFromChannel(errorsChan chan error, numOfServices int) error {
	errCount := 0
	errStr := ""
	for err := range errorsChan {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			errCount++
			errStr += err.Error() + "\n"
		}
	}
	// We would want to return internal error if all the services have failed with some error other than record not found
	if errCount == numOfServices {
		return errors.New("no sherlock banners fetched: " + errStr)
	}
	return nil
}

func (s *Service) fetchBannersFromService(ctx context.Context, srvc types.ServiceName, fetchRequest *sbPb.FetchSherlockBannersRequest) ([]*sbPb.SherlockBanner, error) {
	collectorService, err := s.bannerCollectorFactory.GetSherlockBannersCollector(srvc)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting collector for service")
	}
	// adding timeout for BE services to prevent single service increasing the overall latency
	// Timeout value can be changed per service if needed in future
	ctxWithTimeout, ctxCancel := context.WithTimeout(ctx, 10*time.Second)
	defer ctxCancel()
	serviceResp, err := collectorService.FetchSherlockBanners(ctxWithTimeout, fetchRequest)
	if te := epifigrpc.RPCError(serviceResp, err); te != nil {
		if serviceResp.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "unable to fetch banners for service: "+te.Error())
		}
		return nil, errors.Wrap(te, "unable to fetch banners for service")
	}
	return postProcessBannersList(ctx, serviceResp.GetBanners(), srvc), nil
}

// This will do any post-processing on the banners and returns the list of banners available on the channel
func postProcessBannersList(ctx context.Context, banners []*sbPb.SherlockBanner, service types.ServiceName) []*sbPb.SherlockBanner {
	var postProcessedBanners []*sbPb.SherlockBanner
	for _, banner := range banners {
		if validateErr := validateBannerFetchedFromOtherServices(banner); validateErr != nil {
			logger.Error(ctx, "validation failed for banner fetched from the service",
				zap.String(logger.SERVICE, service.String()), zap.Error(validateErr))
			continue
		}
		if banner.GetOwnerService() == types.ServiceName_SERVICE_UNSPECIFIED {
			banner.OwnerService = service
		}
		postProcessedBanners = append(postProcessedBanners, banner)
	}
	return postProcessedBanners
}

func validateBannerFetchedFromOtherServices(banner *sbPb.SherlockBanner) error {
	if banner == nil {
		return errors.New("banner object is nil")
	}
	switch banner.GetStructureType() {
	case sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_UNSPECIFIED:
		return errors.New("banner structure type is unspecified")
	case sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST:
		if err := validateLabelMessageBannerContent(banner.GetBannerContentV2().GetLabelMessageListContent()); err != nil {
			return errors.Wrap(err, "invalid content for SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST")
		}
	}
	return nil
}

func validateLabelMessageBannerContent(content *sbPb.SherlockBannerLabelMessageListContent) error {
	if content.GetBannerHeading() == "" && len(content.GetLabelMessageList()) == 0 {
		return errors.New("both heading and label-message list are empty")
	}
	return nil
}

func (s *Service) getEnabledServiceNames() []string {
	var serviceNameList []string
	s.conf.SherlockBannersConfig().IsServiceEnabledForDynamicFetching().Range(func(service string, isEnabled bool) bool {
		if !isEnabled {
			return true
		}
		serviceNameList = append(serviceNameList, strings.ToUpper(service))
		return true
	})
	return serviceNameList
}

// comparePriorities will return true if banner1 has higher priority than banner2. Otherwise, returns false.
// serviceRanks represent the ranks of the services i.e. lower the number higher the priority
//
// This is a Less func implementation for sort interface. As per the doc of this interface, If both Less(i, j)
// and Less(j, i) are false then the elements at index i and j are considered equal.
func comparePriorities(banner1 *sbPb.SherlockBanner, banner2 *sbPb.SherlockBanner, serviceRanks map[string]int) bool {
	// currently ordering is based solely on owner service
	rankOfService1, ok := serviceRanks[banner1.OwnerService.String()]
	if !ok {
		// if rank is not found, give it the last rank
		rankOfService1 = math.MaxInt
	}
	rankOfService2, ok := serviceRanks[banner2.OwnerService.String()]
	if !ok {
		// if rank is not found, give it the last rank
		rankOfService2 = math.MaxInt
	}
	// If both Less(i, j) and Less(j, i) are false then the elements at
	// index i and j are considered equal. Hence use < instead of <=.
	return rankOfService1 < rankOfService2
}

// getPriorityRanksFromOrderedList Converts priority order in list form to a Map form
// i.e. gives ranks to the items in the given ordered list.
// priorityOrderList contains the elements in decreasing priority order
// eg: the priorityOrderList ["xyz", "abc"] will be converted to the rank map {"xyz":1, "abc":2}
// To get the priority ranks for given criteria eg: ServiceName
func getPriorityRanksFromOrderedList(priorityOrderList []string) map[string]int {
	priorityRankMap := make(map[string]int)
	priority := 1
	for _, item := range priorityOrderList {
		priorityRankMap[item] = priority
		priority++
	}
	return priorityRankMap
}

func convertServiceNamesToEnumsList(ctx context.Context, serviceNameList []string) []types.ServiceName {
	var serviceEnumList []types.ServiceName
	for _, serviceName := range serviceNameList {
		serviceNameEnum, ok := types.ServiceName_value[serviceName]
		if !ok {
			cxLogger.Info(ctx, "invalid service name read from conf", zap.String(logger.SERVICE, serviceName))
			continue
		}
		serviceEnumList = append(serviceEnumList, types.ServiceName(serviceNameEnum))
	}
	return serviceEnumList
}
