package sherlock_banners

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/cx"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	types "github.com/epifi/gamma/api/typesv2"
	mock_collector "github.com/epifi/gamma/cx/test/mocks/sherlock_banners/collector"
)

var (
	fetchBannersReq1 = &sbPb.FetchSherlockBannersRequest{
		ActorId:           actorId1,
		StructureTypeList: []sbPb.SherlockBannerStructureType{sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST},
	}
	errMock    = errors.New("mock err")
	reqHeader1 = &cx.Header{
		Actor: &types.Actor{
			Id: actorId1,
		},
	}
	labelMessageListBannerPostValidationFailed = &sbPb.SherlockBanner{
		Id: "banner-id-1",
		BannerContentV2: &sbPb.SherlockBannerContentV2{
			Content: &sbPb.SherlockBannerContentV2_LabelMessageListContent{
				LabelMessageListContent: &sbPb.SherlockBannerLabelMessageListContent{
					LabelMessageList: []*sbPb.SherlockBannerLabelMessageListContent_LabelMessage{
						{Label: "user_issue", Message: "user issue 1"},
						{Label: "cx_script", Message: "cx script 1"},
					},
				},
			},
		},
		StartTime: "2023-01-23T15:30:00",
		EndTime:   "2122-01-25T15:30:00",
		MappingItems: &sbPb.MappingItems{
			ActorIdList: []string{actorId1, actorId2},
		},
		OwnerService: types.ServiceName_CX_SERVICE,
	}
)

func TestUserSpecificBannersService_GetBannersForCustomer(t *testing.T) {
	t.Parallel()

	type args struct {
		setupMocks func(
			mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory,
			mockCollectorForPalService *mock_collector.MockSherlockBannersCollector,
			mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector,
			mockCollectorForCXService *mock_collector.MockSherlockBannersCollector,
			mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector,
		)
		ctx context.Context
		req *sbPb.GetBannersForCustomerRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.GetBannersForCustomerResponse
		wantErr bool
	}{
		{
			name: "Invalid arg: actor Id not enriched",
			args: args{
				setupMocks: func(mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory, mockCollectorForPalService *mock_collector.MockSherlockBannersCollector, mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector, mockCollectorForCXService *mock_collector.MockSherlockBannersCollector, mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector) {
					// No mocks needed for this test case
				},
				ctx: context.Background(),
				req: &sbPb.GetBannersForCustomerRequest{MaxNumOfBanners: 1},
			},
			want: &sbPb.GetBannersForCustomerResponse{
				Status: rpcPb.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "No active banners found: No implementations",
			args: args{
				setupMocks: func(mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory, mockCollectorForPalService *mock_collector.MockSherlockBannersCollector, mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector, mockCollectorForCXService *mock_collector.MockSherlockBannersCollector, mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector) {
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE).Return(nil, errMock)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_TIERING_SERVICE).Return(nil, errMock)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_RISK_SERVICE).Return(nil, errMock)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_CX_SERVICE).Return(nil, errMock)
				},
				ctx: context.Background(),
				req: &sbPb.GetBannersForCustomerRequest{Header: reqHeader1, MaxNumOfBanners: 1},
			},
			want: &sbPb.GetBannersForCustomerResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "No active banners found: with one service call error",
			args: args{
				setupMocks: func(mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory, mockCollectorForPalService *mock_collector.MockSherlockBannersCollector, mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector, mockCollectorForCXService *mock_collector.MockSherlockBannersCollector, mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector) {
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE).Return(nil, errMock)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_TIERING_SERVICE).Return(nil, errMock)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_RISK_SERVICE).Return(nil, errMock)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_CX_SERVICE).Return(mockCollectorForCXService, nil)
					mockCollectorForCXService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(nil, errors.New("mock err"))
				},
				ctx: context.Background(),
				req: &sbPb.GetBannersForCustomerRequest{Header: reqHeader1, MaxNumOfBanners: 1},
			},
			want: &sbPb.GetBannersForCustomerResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "No active banners found: with multiple service errors including record not found and internal errors",
			args: args{
				setupMocks: func(mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory, mockCollectorForPalService *mock_collector.MockSherlockBannersCollector, mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector, mockCollectorForCXService *mock_collector.MockSherlockBannersCollector, mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector) {
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE).Return(mockCollectorForPalService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_TIERING_SERVICE).Return(mockCollectorForTieringService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_RISK_SERVICE).Return(mockCollectorForRiskService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_CX_SERVICE).Return(mockCollectorForCXService, nil)
					mockCollectorForPalService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
					mockCollectorForTieringService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
					mockCollectorForRiskService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
					mockCollectorForCXService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status: rpcPb.StatusDeadlineExceeded(),
					}, nil)
				},
				ctx: context.Background(),
				req: &sbPb.GetBannersForCustomerRequest{Header: reqHeader1, MaxNumOfBanners: 1},
			},
			want: &sbPb.GetBannersForCustomerResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "success: fetched from one service",
			args: args{
				setupMocks: func(mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory, mockCollectorForPalService *mock_collector.MockSherlockBannersCollector, mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector, mockCollectorForCXService *mock_collector.MockSherlockBannersCollector, mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector) {
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE).Return(nil, errMock)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_TIERING_SERVICE).Return(nil, errMock)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_RISK_SERVICE).Return(nil, errMock)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_CX_SERVICE).Return(mockCollectorForCXService, nil)
					mockCollectorForCXService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner1},
					}, nil)
				},
				ctx: context.Background(),
				req: &sbPb.GetBannersForCustomerRequest{Header: reqHeader1},
			},
			want: &sbPb.GetBannersForCustomerResponse{
				Status:  rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{lableMessageListBanner1},
			},
			wantErr: false,
		},
		{
			name: "success: fetched from more than one service with an error",
			args: args{
				setupMocks: func(mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory, mockCollectorForPalService *mock_collector.MockSherlockBannersCollector, mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector, mockCollectorForCXService *mock_collector.MockSherlockBannersCollector, mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector) {
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE).Return(mockCollectorForPalService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_TIERING_SERVICE).Return(mockCollectorForTieringService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_RISK_SERVICE).Return(mockCollectorForRiskService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_CX_SERVICE).Return(mockCollectorForCXService, nil)
					mockCollectorForPalService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(nil, errors.New("mock err"))
					mockCollectorForTieringService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(nil, errors.New("mock err"))
					mockCollectorForRiskService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner2},
					}, nil)
					mockCollectorForCXService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(nil, errors.New("mock err"))
				},
				ctx: context.Background(),
				req: &sbPb.GetBannersForCustomerRequest{Header: reqHeader1},
			},
			want: &sbPb.GetBannersForCustomerResponse{
				Status:  rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{lableMessageListBanner2},
			},
			wantErr: false,
		},
		{
			name: "success: fetched from more than one service with no errors but limited to one banner",
			args: args{
				setupMocks: func(mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory, mockCollectorForPalService *mock_collector.MockSherlockBannersCollector, mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector, mockCollectorForCXService *mock_collector.MockSherlockBannersCollector, mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector) {
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE).Return(mockCollectorForPalService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_TIERING_SERVICE).Return(mockCollectorForTieringService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_RISK_SERVICE).Return(mockCollectorForRiskService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_CX_SERVICE).Return(mockCollectorForCXService, nil)
					mockCollectorForPalService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner3},
					}, nil)
					mockCollectorForTieringService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
					mockCollectorForRiskService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner2},
					}, nil)
					mockCollectorForCXService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner1},
					}, nil)
				},
				ctx: context.Background(),
				req: &sbPb.GetBannersForCustomerRequest{Header: reqHeader1},
			},
			want: &sbPb.GetBannersForCustomerResponse{
				Status:  rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{lableMessageListBanner3},
			},
			wantErr: false,
		},
		{
			name: "success: fetched from more than one service - post validation failed for one",
			args: args{
				setupMocks: func(mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory, mockCollectorForPalService *mock_collector.MockSherlockBannersCollector, mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector, mockCollectorForCXService *mock_collector.MockSherlockBannersCollector, mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector) {
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE).Return(mockCollectorForPalService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_TIERING_SERVICE).Return(mockCollectorForTieringService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_CX_SERVICE).Return(mockCollectorForCXService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_RISK_SERVICE).Return(mockCollectorForRiskService, nil)
					mockCollectorForPalService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner3},
					}, nil)
					mockCollectorForTieringService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status: rpcPb.StatusRecordNotFound(),
					}, nil)
					mockCollectorForCXService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{labelMessageListBannerPostValidationFailed},
					}, nil)
					mockCollectorForRiskService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner2},
					}, nil)
				},
				ctx: context.Background(),
				req: &sbPb.GetBannersForCustomerRequest{Header: reqHeader1, MaxNumOfBanners: 3},
			},
			want: &sbPb.GetBannersForCustomerResponse{
				Status:  rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{lableMessageListBanner3, lableMessageListBanner2},
			},
			wantErr: false,
		},
		{
			name: "success: fetched from more than one service with no errors multiple banners",
			args: args{
				setupMocks: func(mockBannerCollectorFactory *mock_collector.MockISherlockBannersCollectorFactory, mockCollectorForPalService *mock_collector.MockSherlockBannersCollector, mockCollectorForTieringService *mock_collector.MockSherlockBannersCollector, mockCollectorForCXService *mock_collector.MockSherlockBannersCollector, mockCollectorForRiskService *mock_collector.MockSherlockBannersCollector) {
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE).Return(mockCollectorForPalService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_TIERING_SERVICE).Return(mockCollectorForTieringService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_CX_SERVICE).Return(mockCollectorForCXService, nil)
					mockBannerCollectorFactory.EXPECT().GetSherlockBannersCollector(types.ServiceName_RISK_SERVICE).Return(mockCollectorForRiskService, nil)
					mockCollectorForPalService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner3},
					}, nil)
					mockCollectorForTieringService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{titleBodyBanner1},
					}, nil)
					mockCollectorForCXService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner1},
					}, nil)
					mockCollectorForRiskService.EXPECT().FetchSherlockBanners(gomock.Any(), fetchBannersReq1).Return(&sbPb.FetchSherlockBannersResponse{
						Status:  rpcPb.StatusOk(),
						Banners: []*sbPb.SherlockBanner{lableMessageListBanner2},
					}, nil)
				},
				ctx: context.Background(),
				req: &sbPb.GetBannersForCustomerRequest{Header: reqHeader1, MaxNumOfBanners: 4},
			},
			want: &sbPb.GetBannersForCustomerResponse{
				Status:  rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{lableMessageListBanner1, lableMessageListBanner3, lableMessageListBanner2, titleBodyBanner1},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Initialize mocks for each test run
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockBannerCollectorFactory := mock_collector.NewMockISherlockBannersCollectorFactory(ctr)
			mockCollectorForPalService := mock_collector.NewMockSherlockBannersCollector(ctr)
			mockCollectorForTieringService := mock_collector.NewMockSherlockBannersCollector(ctr)
			mockCollectorForCXService := mock_collector.NewMockSherlockBannersCollector(ctr)
			mockCollectorForRiskService := mock_collector.NewMockSherlockBannersCollector(ctr)

			// Setup mocks for this specific test
			tt.args.setupMocks(
				mockBannerCollectorFactory,
				mockCollectorForPalService,
				mockCollectorForTieringService,
				mockCollectorForCXService,
				mockCollectorForRiskService,
			)

			s := NewSherlockBannersService(sbTS.genConf, nil, nil, sbTS.txnExecutor, nil, mockBannerCollectorFactory)
			got, err := s.GetBannersForCustomer(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBanners() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualGetBannersForCustomerResp(got, tt.want) {
				t.Errorf("GetBanners() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isDeepEqualGetBannersForCustomerResp(got, want *sbPb.GetBannersForCustomerResponse) bool {
	return proto.Equal(got.GetStatus(), want.GetStatus()) &&
		isDeepEqualSherlockBannersList(got.GetBanners(), want.GetBanners())
}
