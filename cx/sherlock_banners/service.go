package sherlock_banners

import (
	"bytes"
	"context"
	"encoding/csv"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	sbCollector "github.com/epifi/gamma/cx/sherlock_banners/collector"
	"github.com/epifi/gamma/cx/sherlock_banners/dao"
	"github.com/epifi/gamma/cx/sherlock_banners/helper"
)

type Service struct {
	sbPb.UnimplementedSherlockBannersServer
	conf                   *cxGenConf.Config
	sherlockBannerDao      dao.ISherlockBannerDao
	bannerMappingDao       dao.IBannerMappingDao
	txnExecutor            storageV2.TxnExecutor
	getBannersHelper       helper.GetBannersHelper
	bannerCollectorFactory sbCollector.ISherlockBannersCollectorFactory
}

func NewSherlockBannersService(conf *cxGenConf.Config, sherlockBannerDao dao.ISherlockBannerDao,
	bannerMappingDao dao.IBannerMappingDao, txnExecutor storageV2.TxnExecutor, getBannersHelper helper.GetBannersHelper,
	bannerCollectorFactory sbCollector.ISherlockBannersCollectorFactory) *Service {
	return &Service{
		conf:                   conf,
		sherlockBannerDao:      sherlockBannerDao,
		bannerMappingDao:       bannerMappingDao,
		txnExecutor:            txnExecutor,
		getBannersHelper:       getBannersHelper,
		bannerCollectorFactory: bannerCollectorFactory,
	}
}

var _ sbPb.SherlockBannersServer = &Service{}

var (
	userIssueInfoCsvColsRequired = map[string]bool{
		UserIssueInfoCsvColActorId:   true,
		UserIssueInfoCsvColUserIssue: true,
		UserIssueInfoCsvColCxScript:  true,
	}
	defaultBannerDuration = 2 * 24 * time.Hour // 48 hrs
)

const (
	istSuffix                    = "+05:30"
	UserIssueInfoCsvColActorId   = "actor_id"
	UserIssueInfoCsvColUserIssue = "user_issue"
	UserIssueInfoCsvColCxScript  = "cx_script"
)

func (s *Service) CreateBanner(ctx context.Context, req *sbPb.CreateBannerRequest) (*sbPb.CreateBannerResponse, error) {
	if err := ValidateBannerForCreate(req.GetBanner()); err != nil {
		logger.Error(ctx, "Banner validation failed for create", zap.Error(err))
		return &sbPb.CreateBannerResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(err.Error()),
		}, nil
	}
	banner := req.GetBanner()
	banner.Id = uuid.New().String()
	mappingList := getMappingListForBanner(banner)
	if txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		_, err := s.sherlockBannerDao.Create(txnCtx, req.GetBanner())
		if err != nil {
			logger.Error(ctx, "error while creating sherlock banner entry in db", zap.Error(err))
			return errors.Wrap(err, "error creating sherlock banner")
		}
		if _, mappingErr := s.bannerMappingDao.CreateBatch(txnCtx, mappingList); mappingErr != nil {
			return errors.Wrap(mappingErr, "error creating mapping for the banner")
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "error in transaction executor block in create banner", zap.Error(txnErr))
		return &sbPb.CreateBannerResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(txnErr.Error()),
		}, nil
	}
	return &sbPb.CreateBannerResponse{
		Status:   rpcPb.StatusOk(),
		BannerId: banner.GetId(),
	}, nil
}

func (s *Service) BulkCreateBanners(ctx context.Context, req *sbPb.BulkCreateBannersRequest) (*sbPb.BulkCreateBannersResponse, error) {
	sherlockBannerList, err := getSherlockBannerListFromReq(req)
	if err != nil {
		logger.Error(ctx, "failed to get banner list from csv file", zap.Error(err))
		return &sbPb.BulkCreateBannersResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(err.Error()),
		}, nil
	}
	var mappingList []*sbPb.BannerMapping
	for _, banner := range sherlockBannerList {
		if validateErr := ValidateBannerForCreate(banner); validateErr != nil {
			logger.Error(ctx, "failed to validate banner parsed from csv file", zap.Error(validateErr))
			return &sbPb.BulkCreateBannersResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg(validateErr.Error()),
			}, nil
		}
		mappingList = append(mappingList, getMappingListForBanner(banner)...)
	}
	if txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		sherlockBannerList, err = s.sherlockBannerDao.CreateBatch(txnCtx, sherlockBannerList)
		if err != nil {
			logger.Error(ctx, "error while creating sherlock banners in db", zap.Error(err))
			return errors.Wrap(err, "error creating sherlock banners")
		}
		if _, mappingErr := s.bannerMappingDao.CreateBatch(txnCtx, mappingList); mappingErr != nil {
			logger.Error(ctx, "error while creating mappings for sherlock banners in db", zap.Error(err))
			return errors.Wrap(mappingErr, "error creating mappings for the banners")
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "error in transaction executor block in bulk create banner", zap.Error(txnErr))
		return &sbPb.BulkCreateBannersResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(txnErr.Error()),
		}, nil
	}
	return &sbPb.BulkCreateBannersResponse{
		Status:      rpcPb.StatusOk(),
		BannersList: sherlockBannerList,
	}, nil
}

//nolint:funlen
func getSherlockBannerListFromReq(req *sbPb.BulkCreateBannersRequest) ([]*sbPb.SherlockBanner, error) {
	csvReader := csv.NewReader(bytes.NewReader(req.GetCsvFile().GetContent()))
	list, err := csvReader.ReadAll()
	if err != nil {
		return nil, errors.Wrap(err, "could not read csv")
	}
	if len(list) == 0 {
		return nil, errors.New("empty csv file")
	}
	var sherlockBannerList []*sbPb.SherlockBanner
	headerRow := list[0]
	// store the column indices for the csv columns
	csvColNameToIndexMap := make(map[string]int)
	csvColIndexToNameMap := make(map[int]string)
	for i, colName := range headerRow {
		csvColNameToIndexMap[colName] = i
		csvColIndexToNameMap[i] = colName
	}
	// check if required columns are present in the csv file
	for requiredCol := range userIssueInfoCsvColsRequired {
		// check if we registered the index of the required column
		_, ok := csvColNameToIndexMap[requiredCol]
		if !ok {
			return nil, errors.New(fmt.Sprintf("\"%s\" column is missing from csv file", requiredCol))
		}
	}
	// for each row populate the userIssueInfoList
	bannersMap := make(map[string]*sbPb.SherlockBanner)

	for i, row := range list {
		// skip the header row
		if i == 0 {
			continue
		}
		bannerDedupeKey, labelMessageList := getDedupeKeyAndLabelMessageList(row, csvColIndexToNameMap)
		actorId := row[getColIndexForUserIssueInfoCsv(csvColNameToIndexMap, UserIssueInfoCsvColActorId)]
		banner, ok := bannersMap[bannerDedupeKey]
		if ok {
			// If a row with same banner content already exists, just add this actorId to its mapping list
			if actorId != "" {
				banner.MappingItems.ActorIdList = append(banner.MappingItems.ActorIdList, actorId)
			}
		} else {
			// If this row requires creating a new banner, then add a new entry to bannersMap
			newBanner := &sbPb.SherlockBanner{
				Id:            uuid.New().String(),
				StructureType: sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST,
				BannerContentV2: &sbPb.SherlockBannerContentV2{
					Content: &sbPb.SherlockBannerContentV2_LabelMessageListContent{
						LabelMessageListContent: &sbPb.SherlockBannerLabelMessageListContent{
							LabelMessageList: labelMessageList,
						},
					},
				},
			}
			newBanner.StartTime = getBannerStartTimeOrDefault(req.GetStartTime())
			newBanner.EndTime, err = getBannerEndTimeOrDefault(newBanner.GetStartTime(), req.GetEndTime())
			if err != nil {
				return nil, errors.Wrap(err, "failed to get end time")
			}
			if actorId != "" {
				newBanner.MappingItems = &sbPb.MappingItems{
					ActorIdList: []string{actorId},
				}
			}
			bannersMap[bannerDedupeKey] = newBanner
			sherlockBannerList = append(sherlockBannerList, newBanner)
		}
	}
	return sherlockBannerList, nil
}

func getBannerEndTimeOrDefault(startTimeStr string, endTimeStr string) (string, error) {
	if endTimeStr != "" {
		return endTimeStr, nil
	}
	// default value is start_time + defaultBannerDuration
	startTime, err := time.Parse(time.RFC3339, strings.TrimSpace(startTimeStr)+istSuffix)
	if err != nil {
		return "", errors.Wrap(err, "error parsing Start time string format")
	}
	endTime := startTime.Add(defaultBannerDuration)
	return strings.TrimSuffix(endTime.In(datetime.IST).Format(time.RFC3339), istSuffix), nil
}

func getBannerStartTimeOrDefault(startTime string) string {
	if startTime != "" {
		return startTime
	}
	// default value is current time
	// trimming ist suffix here because this was the contract agreed upon with client for creating banner.
	return strings.TrimSuffix(time.Now().In(datetime.IST).Format(time.RFC3339), istSuffix)
}

func getDedupeKeyAndLabelMessageList(row []string, colIndexToNameMap map[int]string) (string, []*sbPb.SherlockBannerLabelMessageListContent_LabelMessage) {
	var labelMessageList []*sbPb.SherlockBannerLabelMessageListContent_LabelMessage
	bannerDedupeKey := &strings.Builder{}
	var colIdxList []int
	for colIdx, colName := range colIndexToNameMap {
		if !isMappingColumn(colName) {
			colIdxList = append(colIdxList, colIdx)
		}
	}
	sort.Ints(colIdxList)
	for _, colIdx := range colIdxList {
		labelMessageList = append(labelMessageList, &sbPb.SherlockBannerLabelMessageListContent_LabelMessage{
			Label:   colIndexToNameMap[colIdx],
			Message: row[colIdx],
		})
		bannerDedupeKey.WriteString(row[colIdx])
	}
	return bannerDedupeKey.String(), labelMessageList
}

// to check if a given column falls under a mapping item rather than a banner attribute
func isMappingColumn(col string) bool {
	// As of yet only actor Id is a mapping column
	return col == UserIssueInfoCsvColActorId
}

func getColIndexForUserIssueInfoCsv(csvColNameToIndexMap map[string]int, field string) int {
	return csvColNameToIndexMap[field]
}

func (s *Service) UpdateBanner(ctx context.Context, req *sbPb.UpdateBannerRequest) (*sbPb.UpdateBannerResponse, error) {
	banner := req.GetBanner()
	updateMask := req.GetUpdateMask()
	if banner.GetId() == "" || (len(updateMask) == 0) {
		logger.Error(ctx, "banner Id or update mask is empty")
		return &sbPb.UpdateBannerResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("banner Id and update field mask are mandatory params"),
		}, nil
	}
	isBannerContentUpdate, isBannerUpdate, isMappingUpdate := isContentOrMappingUpdate(updateMask)
	if isBannerContentUpdate {
		currBanner, _, err := s.sherlockBannerDao.GetAllWithFilters(ctx, nil, 1, dao.WithRowIdList([]string{banner.GetId()}))
		if err != nil {
			logger.Error(ctx, "error in getting banner from db", zap.Error(err))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &sbPb.UpdateBannerResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			return &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in getting banner from db"),
			}, nil
		}
		updateBannerContent(currBanner[0], banner, updateMask)
	}
	if txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		if isBannerUpdate {
			err := s.sherlockBannerDao.Update(ctx, banner, updateMask)
			if err != nil {
				return errors.Wrap(err, "error updating sherlock banner")
			}
		}
		if isMappingUpdate {
			_, err := s.bannerMappingDao.DeleteBatch(ctx, dao.WithBannerIdList([]string{banner.GetId()}))
			if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
				return errors.Wrap(err, "error deleting existing mapping")
			}
			_, err = s.bannerMappingDao.CreateBatch(ctx, getMappingListForBanner(banner))
			if err != nil {
				return errors.Wrap(err, "failed to add banner mappings")
			}
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "error in txn exec block while updating sherlock banner details", zap.Error(txnErr))
		if errors.Is(txnErr, epifierrors.ErrRecordNotFound) {
			return &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &sbPb.UpdateBannerResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(txnErr.Error()),
		}, nil
	}
	return &sbPb.UpdateBannerResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

// nolint:funlen
func (s *Service) GetBanners(ctx context.Context, req *sbPb.GetBannersRequest) (*sbPb.GetBannersResponse, error) {
	// check for valid page token for pagination
	return s.getBannersHelper.GetBanners(ctx, req)
}

// ValidateBannerForCreate verifies if the given banner has valid details for adding to DB
// returns nil if it is a valid input
func ValidateBannerForCreate(banner *sbPb.SherlockBanner) error {
	if banner == nil {
		return errors.New("Banner object cannot be nil")
	}
	if banner.GetBannerContentV2() == nil {

		// for backward compatibility allow banner content also
		if banner.GetBannerContent() != nil {
			if banner.GetBannerContent().GetTitle() == "" {
				return errors.New("Banner title cannot be empty")
			}
			if banner.GetBannerContent().GetBody() == "" {
				return errors.New("Banner body cannot be empty")
			}
			// Copy the banner content v1 to v2
			banner.StructureType = sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_STANDARD_TITLE_BODY
			banner.BannerContentV2 = &sbPb.SherlockBannerContentV2{
				Content: &sbPb.SherlockBannerContentV2_TitleBodyContent{
					TitleBodyContent: &sbPb.SherlockBannerTitleBodyContent{
						Title: banner.GetBannerContent().GetTitle(),
						Body:  banner.GetBannerContent().GetBody(),
					},
				},
			}
		} else {
			return errors.New("Banner content cannot be nil")
		}
	} else {
		switch banner.GetStructureType() {
		case sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST:
			if banner.GetBannerContentV2().GetLabelMessageListContent() == nil {
				return errors.New("label message list cannot be empty for STRUCTURE_TYPE_LABEL_MESSAGE_LIST")
			}
		case sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_STANDARD_TITLE_BODY:
			if banner.GetBannerContentV2().GetTitleBodyContent() == nil {
				return errors.New("title and body cannot be empty for STRUCTURE_TYPE_STANDARD_TITLE_BODY")
			}
		default:
			return errors.New("Structure type must be specified for V2 banner content")
		}
	}
	startTime, err := time.Parse(time.RFC3339, strings.TrimSpace(banner.GetStartTime())+istSuffix)
	if err != nil {
		return errors.Wrap(err, "error parsing Start time string format")
	}
	endTime, err := time.Parse(time.RFC3339, strings.TrimSpace(banner.GetEndTime())+istSuffix)
	if err != nil {
		return errors.Wrap(err, "error parsing End time string format")
	}
	if !startTime.Before(endTime) {
		return errors.New("start time value must be before end time")
	}
	// NOTE: this check must be modified accordingly if any new type of mapping items is added in future
	if len(banner.GetMappingItems().GetSherlockUserRoles()) == 0 && len(banner.GetMappingItems().GetActorIdList()) == 0 {
		return errors.New("Mappings must be specified for the Banner")
	}
	return nil
}

func getMappingListForBanner(banner *sbPb.SherlockBanner) []*sbPb.BannerMapping {
	var mappingList []*sbPb.BannerMapping
	bannerId := banner.GetId()

	for _, role := range banner.GetMappingItems().GetSherlockUserRoles() {
		mappingList = append(mappingList, &sbPb.BannerMapping{
			BannerId:     bannerId,
			MappingType:  sbPb.MappingType_MAPPING_TYPE_SHERLOCK_USER_ROLE,
			MappingValue: role.String(),
		})
	}
	for _, actorId := range banner.GetMappingItems().GetActorIdList() {
		mappingList = append(mappingList, &sbPb.BannerMapping{
			BannerId:     banner.GetId(),
			MappingType:  sbPb.MappingType_MAPPING_TYPE_ACTOR_ID,
			MappingValue: actorId,
		})
	}
	return mappingList
}

func isContentOrMappingUpdate(updateMask []sbPb.BannerUpdateFieldMask) (isBannerContentUpdate, isBannerUpdate, isMappingUpdate bool) {
	isBannerContentUpdate = false
	isBannerUpdate = false
	isMappingUpdate = false
	for _, field := range updateMask {
		switch field {
		case sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_TITLE, sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_BODY,
			sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_LABEL_MESSAGE_LIST:
			isBannerContentUpdate = true
			isBannerUpdate = true
		case sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_START_TIME, sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_END_TIME:
			isBannerUpdate = true
		case sbPb.BannerUpdateFieldMask_BANNER_UPDATE_MAPPING_SHERLOCK_USER_ROLES:
			isMappingUpdate = true
		default:
		}
	}
	return
}

func updateBannerContent(currBanner *sbPb.SherlockBanner, reqBanner *sbPb.SherlockBanner, updateMask []sbPb.BannerUpdateFieldMask) {
	// copy the content currently in the DB
	currBannerContent := *currBanner.GetBannerContent()
	currBannerContentV2 := *currBanner.GetBannerContentV2()
	// update the fields present in the updateMask from reqBanner
	reqBannerContent := reqBanner.GetBannerContent()
	reqBannerContentV2 := reqBanner.GetBannerContentV2()
	for _, field := range updateMask {
		switch field {
		case sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_TITLE:
			currBannerContent.Title = reqBannerContent.GetTitle()
			currBannerContentV2.GetTitleBodyContent().Title = reqBannerContentV2.GetTitleBodyContent().GetTitle()
		case sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_BODY:
			currBannerContent.Body = reqBannerContent.GetBody()
			currBannerContentV2.GetTitleBodyContent().Body = reqBannerContentV2.GetTitleBodyContent().GetBody()
		case sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_LABEL_MESSAGE_LIST:
			currBannerContentV2.GetLabelMessageListContent().LabelMessageList = reqBannerContentV2.GetLabelMessageListContent().GetLabelMessageList()
		default:
		}
	}
	// update the reqBanner's content
	reqBanner.BannerContent = &currBannerContent
	reqBanner.BannerContentV2 = &currBannerContentV2
}
