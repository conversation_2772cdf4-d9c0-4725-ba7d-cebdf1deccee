package sherlock_banners

import (
	"context"
	"flag"
	"os"
	"reflect"
	"sort"
	"testing"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	casbinPb "github.com/epifi/gamma/api/casbin"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/cx/config"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/test"
	dao_mock "github.com/epifi/gamma/cx/test/mocks/sherlock_banners/dao"
	mock_get_banners_helper "github.com/epifi/gamma/cx/test/mocks/sherlock_banners/helper"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, genConf, db, teardown := test.InitTestServer(true)
	txnExecutor := storageV2.NewGormTxnExecutor(db)

	sbTS = SherlockBannersTestSuite{
		conf:        conf,
		genConf:     genConf,
		txnExecutor: txnExecutor,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type SherlockBannersTestSuite struct {
	conf        *config.Config
	genConf     *cxGenConf.Config
	txnExecutor storageV2.TxnExecutor
}

const (
	actorId1 = "actor-1"
	actorId2 = "actor-2"
)

var (
	sbTS SherlockBannersTestSuite

	banner1 = &sbPb.SherlockBanner{
		Id:            "banner-id-1",
		BannerContent: &sbPb.SherlockBannerContent{Title: "t1", Body: "b1"},
		StartTime:     "2022-06-13T15:30:00",
		EndTime:       "2122-03-13T15:30:00",
		MappingItems: &sbPb.MappingItems{
			SherlockUserRoles: []casbinPb.AccessLevel{casbinPb.AccessLevel_AGENT, casbinPb.AccessLevel_FEDERAL_AGENT},
		},
	}
	csvFileInvalid = &sbPb.BulkCreateBannersRequest_File{
		Content: []byte("user_issue,cx_script,\n" +
			"accounts,xyz\n" +
			"profile,\n"),
	}
	csvFileMissingCols = &sbPb.BulkCreateBannersRequest_File{
		Content: []byte("user_issue,cx_script\n" +
			"accounts,xyz\n" +
			"profile,\n"),
	}
	csvFileNoMappings = &sbPb.BulkCreateBannersRequest_File{
		Content: []byte("user_issue,cx_script,actor_id\n" +
			"user issue 1,cx script 1,\n" +
			"user issue 1,cx script 1,\n"),
	}
	csvFileSingleBanner = &sbPb.BulkCreateBannersRequest_File{
		Content: []byte("user_issue,cx_script,actor_id\n" +
			"user issue 1,cx script 1," + actorId1 + "\n" +
			"user issue 1,cx script 1," + actorId2 + "\n"),
	}
	csvFileMultipleBanners = &sbPb.BulkCreateBannersRequest_File{
		Content: []byte("user_issue,cx_script,actor_id\n" +
			"user issue 1,cx script 1," + actorId1 + "\n" +
			"user issue 1,cx script 1," + actorId2 + "\n" +
			"profile,," + actorId2 + "\n"),
	}

	lableMessageListBanner1 = &sbPb.SherlockBanner{
		Id:            "banner-id-1",
		StructureType: sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST,
		BannerContentV2: &sbPb.SherlockBannerContentV2{
			Content: &sbPb.SherlockBannerContentV2_LabelMessageListContent{
				LabelMessageListContent: &sbPb.SherlockBannerLabelMessageListContent{
					LabelMessageList: []*sbPb.SherlockBannerLabelMessageListContent_LabelMessage{
						{Label: "user_issue", Message: "user issue 1"},
						{Label: "cx_script", Message: "cx script 1"},
					},
				},
			},
		},
		StartTime: "2023-01-23T15:30:00",
		EndTime:   "2122-01-25T15:30:00",
		MappingItems: &sbPb.MappingItems{
			ActorIdList: []string{actorId1, actorId2},
		},
		OwnerService: types.ServiceName_CX_SERVICE,
	}
	lableMessageListBanner2 = &sbPb.SherlockBanner{
		Id:            "banner-id-2",
		StructureType: sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST,
		BannerContentV2: &sbPb.SherlockBannerContentV2{
			Content: &sbPb.SherlockBannerContentV2_LabelMessageListContent{
				LabelMessageListContent: &sbPb.SherlockBannerLabelMessageListContent{
					LabelMessageList: []*sbPb.SherlockBannerLabelMessageListContent_LabelMessage{
						{Label: "user_issue", Message: "profile"},
						{Label: "cx_script", Message: ""},
					},
				},
			},
		},
		StartTime: "2023-01-23T15:30:00",
		EndTime:   "2122-01-25T15:30:00",
		MappingItems: &sbPb.MappingItems{
			ActorIdList: []string{actorId2},
		},
		OwnerService: types.ServiceName_RISK_SERVICE,
	}
	lableMessageListBanner3 = &sbPb.SherlockBanner{
		Id:            "banner-id-3",
		StructureType: sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_LABEL_MESSAGE_LIST,
		BannerContentV2: &sbPb.SherlockBannerContentV2{
			Content: &sbPb.SherlockBannerContentV2_LabelMessageListContent{
				LabelMessageListContent: &sbPb.SherlockBannerLabelMessageListContent{
					LabelMessageList: []*sbPb.SherlockBannerLabelMessageListContent_LabelMessage{
						{Label: "user_issue", Message: "profile"},
						{Label: "cx_script", Message: ""},
					},
				},
			},
		},
		StartTime: "2023-01-23T15:30:00",
		EndTime:   "2122-01-25T15:30:00",
		MappingItems: &sbPb.MappingItems{
			ActorIdList: []string{actorId2},
		},
		OwnerService: types.ServiceName_PRE_APPROVED_LOAN_SERVICE,
	}

	titleBodyBanner1 = &sbPb.SherlockBanner{
		StructureType: sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_STANDARD_TITLE_BODY,
		BannerContentV2: &sbPb.SherlockBannerContentV2{
			Content: &sbPb.SherlockBannerContentV2_TitleBodyContent{
				TitleBodyContent: &sbPb.SherlockBannerTitleBodyContent{
					Title: "Title text",
					Body:  "",
				},
			},
		},
		OwnerService: types.ServiceName_TIERING_SERVICE,
	}
)

func TestSherlockBannersService_CreateBanner(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	bannerDao := dao_mock.NewMockISherlockBannerDao(ctr)
	bannerMappingDao := dao_mock.NewMockIBannerMappingDao(ctr)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *sbPb.CreateBannerRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.CreateBannerResponse
		wantErr bool
	}{
		{
			name: "invalid argument: no banner object",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{},
			},
			want: &sbPb.CreateBannerResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Banner object cannot be nil"),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: missing banner content",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{Banner: &sbPb.SherlockBanner{}},
			},
			want: &sbPb.CreateBannerResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Banner content cannot be nil"),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: missing banner title",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{Banner: &sbPb.SherlockBanner{
					BannerContent: &sbPb.SherlockBannerContent{Body: "body"},
				}},
			},
			want: &sbPb.CreateBannerResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Banner title cannot be empty"),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: missing banner body",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{Banner: &sbPb.SherlockBanner{
					BannerContent: &sbPb.SherlockBannerContent{Title: "title"},
				}},
			},
			want: &sbPb.CreateBannerResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Banner body cannot be empty"),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: invalid start time",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{Banner: &sbPb.SherlockBanner{
					BannerContent: &sbPb.SherlockBannerContent{Body: "body", Title: "title"},
					StartTime:     "invalidTime",
				}},
			},
			want: &sbPb.CreateBannerResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("error parsing Start time string format: parsing time \"invalidTime+05:30\" as \"2006-01-02T15:04:05Z07:00\": cannot parse \"invalidTime+05:30\" as \"2006\""),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: invalid end time",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{Banner: &sbPb.SherlockBanner{
					BannerContent: &sbPb.SherlockBannerContent{Body: "body", Title: "title"},
					StartTime:     "2022-06-13T15:30:00",
					EndTime:       "invalidTime",
				}},
			},
			want: &sbPb.CreateBannerResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("error parsing End time string format: parsing time \"invalidTime+05:30\" as \"2006-01-02T15:04:05Z07:00\": cannot parse \"invalidTime+05:30\" as \"2006\""),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: Sherlock user roles must be specified",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{Banner: &sbPb.SherlockBanner{
					BannerContent: &sbPb.SherlockBannerContent{Body: "body", Title: "title"},
					StartTime:     "2022-06-13T15:30:00",
					EndTime:       "2122-06-13T15:30:00",
				}},
			},
			want: &sbPb.CreateBannerResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Mappings must be specified for the Banner"),
			},
			wantErr: false,
		},
		{
			name: "error while creating sherlock banner record",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{Banner: banner1},
				mocks: []interface{}{
					bannerDao.EXPECT().Create(gomock.Any(), banner1).Return(nil, errors.New("fail")),
				},
			},
			want: &sbPb.CreateBannerResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error creating sherlock banner: fail"),
			},
			wantErr: false,
		},
		{
			name: "error while adding banner mapping",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{Banner: banner1},
				mocks: []interface{}{
					bannerDao.EXPECT().Create(gomock.Any(), banner1).Return(banner1, nil),
					bannerMappingDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return(nil, errors.New("fail")),
				},
			},
			want: &sbPb.CreateBannerResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error creating mapping for the banner: fail"),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &sbPb.CreateBannerRequest{Banner: banner1},
				mocks: []interface{}{
					bannerDao.EXPECT().Create(gomock.Any(), banner1).Return(banner1, nil),
					bannerMappingDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return(nil, nil),
				},
			},
			want: &sbPb.CreateBannerResponse{
				Status:   rpcPb.StatusOk(),
				BannerId: banner1.GetId(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewSherlockBannersService(sbTS.genConf, bannerDao, bannerMappingDao, sbTS.txnExecutor, nil, nil)
			got, err := s.CreateBanner(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateBanner() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isCreateBannerRespEqual(got, tt.want) {
				t.Errorf("CreateBanner() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isCreateBannerRespEqual(got *sbPb.CreateBannerResponse, want *sbPb.CreateBannerResponse) bool {
	// not comparing id because uuid is generated inside this func and is not being mocked
	return reflect.DeepEqual(got.GetStatus(), want.GetStatus())
}

func TestSherlockBannersService_BulkCreateBanners(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	bannerDao := dao_mock.NewMockISherlockBannerDao(ctr)
	bannerMappingDao := dao_mock.NewMockIBannerMappingDao(ctr)
	mockGetBannersHelper := mock_get_banners_helper.NewMockGetBannersHelper(ctr)
	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *sbPb.BulkCreateBannersRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.BulkCreateBannersResponse
		wantErr bool
	}{
		{
			name: "invalid argument: empty csv file",
			args: args{
				ctx: context.Background(),
				req: &sbPb.BulkCreateBannersRequest{},
			},
			want: &sbPb.BulkCreateBannersResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty csv file"),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: invalid csv file",
			args: args{
				ctx: context.Background(),
				req: &sbPb.BulkCreateBannersRequest{CsvFile: csvFileInvalid},
			},
			want: &sbPb.BulkCreateBannersResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("could not read csv: record on line 2: wrong number of fields"),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: missing mandatory columns in csv",
			args: args{
				ctx: context.Background(),
				req: &sbPb.BulkCreateBannersRequest{CsvFile: csvFileMissingCols},
			},
			want: &sbPb.BulkCreateBannersResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("\"actor_id\" column is missing from csv file"),
			},
			wantErr: false,
		},
		{
			name: "invalid argument: parsed banner validation failed",
			args: args{
				ctx: context.Background(),
				req: &sbPb.BulkCreateBannersRequest{CsvFile: csvFileNoMappings},
			},
			want: &sbPb.BulkCreateBannersResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Mappings must be specified for the Banner"),
			},
			wantErr: false,
		},
		{
			name: "error while creating sherlock banners",
			args: args{
				ctx: context.Background(),
				req: &sbPb.BulkCreateBannersRequest{CsvFile: csvFileSingleBanner},
				mocks: []interface{}{
					bannerDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return(nil, errors.New("fail")),
				},
			},
			want: &sbPb.BulkCreateBannersResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error creating sherlock banners: fail"),
			},
			wantErr: false,
		},
		{
			name: "error while adding banner mappings",
			args: args{
				ctx: context.Background(),
				req: &sbPb.BulkCreateBannersRequest{CsvFile: csvFileSingleBanner},
				mocks: []interface{}{
					bannerDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return([]*sbPb.SherlockBanner{lableMessageListBanner1}, nil),
					bannerMappingDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return(nil, errors.New("fail")),
				},
			},
			want: &sbPb.BulkCreateBannersResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error creating mappings for the banners: fail"),
			},
			wantErr: false,
		},
		{
			name: "success single banner",
			args: args{
				ctx: context.Background(),
				req: &sbPb.BulkCreateBannersRequest{CsvFile: csvFileSingleBanner},
				mocks: []interface{}{
					bannerDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return([]*sbPb.SherlockBanner{lableMessageListBanner1}, nil),
					bannerMappingDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return([]*sbPb.BannerMapping{}, nil),
				},
			},
			want: &sbPb.BulkCreateBannersResponse{
				Status:      rpcPb.StatusOk(),
				BannersList: []*sbPb.SherlockBanner{lableMessageListBanner1},
			},
			wantErr: false,
		},
		{
			name: "success multiple banners",
			args: args{
				ctx: context.Background(),
				req: &sbPb.BulkCreateBannersRequest{CsvFile: csvFileMultipleBanners},
				mocks: []interface{}{
					bannerDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return([]*sbPb.SherlockBanner{lableMessageListBanner1, lableMessageListBanner2}, nil),
					bannerMappingDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return([]*sbPb.BannerMapping{}, nil),
				},
			},
			want: &sbPb.BulkCreateBannersResponse{
				Status:      rpcPb.StatusOk(),
				BannersList: []*sbPb.SherlockBanner{lableMessageListBanner1, lableMessageListBanner2},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewSherlockBannersService(sbTS.genConf, bannerDao, bannerMappingDao, sbTS.txnExecutor, mockGetBannersHelper, nil)
			got, err := s.BulkCreateBanners(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("BulkCreateBanners() error = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if !isBulkCreateBannersRespEqual(got, tt.want) {
				t.Errorf("BulkCreateBanners() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func isBulkCreateBannersRespEqual(got *sbPb.BulkCreateBannersResponse, want *sbPb.BulkCreateBannersResponse) bool {
	return reflect.DeepEqual(got.GetStatus(), want.GetStatus()) && isDeepEqualSherlockBannersList(got.GetBannersList(), want.GetBannersList())
}

func TestSherlockBannersService_UpdateBanner(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	bannerDao := dao_mock.NewMockISherlockBannerDao(ctr)
	bannerMappingDao := dao_mock.NewMockIBannerMappingDao(ctr)
	mockGetBannersHelper := mock_get_banners_helper.NewMockGetBannersHelper(ctr)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *sbPb.UpdateBannerRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.UpdateBannerResponse
		wantErr bool
	}{
		{
			name: "invalid argument",
			args: args{
				ctx: context.Background(),
				req: &sbPb.UpdateBannerRequest{},
			},
			want: &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("banner Id and update field mask are mandatory params"),
			},
			wantErr: false,
		},
		{
			name: "is content update: record not found in Get from banner dao",
			args: args{
				ctx: context.Background(),
				req: &sbPb.UpdateBannerRequest{Banner: banner1,
					UpdateMask: []sbPb.BannerUpdateFieldMask{sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_TITLE}},
				mocks: []interface{}{
					bannerDao.EXPECT().GetAllWithFilters(context.Background(), nil, 1, gomock.Any()).
						Return(nil, nil, epifierrors.ErrRecordNotFound),
				},
			},
			want: &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "is content update: error in Get from banner dao",
			args: args{
				ctx: context.Background(),
				req: &sbPb.UpdateBannerRequest{Banner: banner1,
					UpdateMask: []sbPb.BannerUpdateFieldMask{sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_TITLE}},
				mocks: []interface{}{
					bannerDao.EXPECT().GetAllWithFilters(context.Background(), nil, 1, gomock.Any()).
						Return(nil, nil, errors.New("fail")),
				},
			},
			want: &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in getting banner from db"),
			},
			wantErr: false,
		},
		{
			name: "error while updating: record not found",
			args: args{
				ctx: context.Background(),
				req: &sbPb.UpdateBannerRequest{Banner: banner1,
					UpdateMask: []sbPb.BannerUpdateFieldMask{sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_START_TIME}},
				mocks: []interface{}{
					bannerDao.EXPECT().Update(context.Background(), banner1, gomock.Any()).Return(epifierrors.ErrRecordNotFound),
				},
			},
			want: &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "error while updating in banners table",
			args: args{
				ctx: context.Background(),
				req: &sbPb.UpdateBannerRequest{Banner: banner1,
					UpdateMask: []sbPb.BannerUpdateFieldMask{sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_START_TIME}},
				mocks: []interface{}{
					bannerDao.EXPECT().Update(context.Background(), banner1, gomock.Any()).Return(errors.New("fail")),
				},
			},
			want: &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error updating sherlock banner: fail"),
			},
			wantErr: false,
		},
		{
			name: "error while deleting in mapping table",
			args: args{
				ctx: context.Background(),
				req: &sbPb.UpdateBannerRequest{Banner: banner1,
					UpdateMask: []sbPb.BannerUpdateFieldMask{sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_CONTENT_TITLE, sbPb.BannerUpdateFieldMask_BANNER_UPDATE_MAPPING_SHERLOCK_USER_ROLES}},
				mocks: []interface{}{
					bannerDao.EXPECT().GetAllWithFilters(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*sbPb.SherlockBanner{banner1}, nil, nil),
					bannerDao.EXPECT().Update(context.Background(), banner1, gomock.Any()).Return(nil),
					bannerMappingDao.EXPECT().DeleteBatch(gomock.Any(), gomock.Any()).Return(int64(1), errors.New("fail")),
				},
			},
			want: &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error deleting existing mapping: fail"),
			},
			wantErr: false,
		},
		{
			name: "error while adding in mapping table",
			args: args{
				ctx: context.Background(),
				req: &sbPb.UpdateBannerRequest{Banner: banner1,
					UpdateMask: []sbPb.BannerUpdateFieldMask{sbPb.BannerUpdateFieldMask_BANNER_UPDATE_MAPPING_SHERLOCK_USER_ROLES}},
				mocks: []interface{}{
					bannerMappingDao.EXPECT().DeleteBatch(gomock.Any(), gomock.Any()).Return(int64(1), nil),
					bannerMappingDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return(nil, errors.New("fail")),
				},
			},
			want: &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to add banner mappings: fail"),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &sbPb.UpdateBannerRequest{Banner: banner1,
					UpdateMask: []sbPb.BannerUpdateFieldMask{sbPb.BannerUpdateFieldMask_BANNER_UPDATE_FIELD_START_TIME, sbPb.BannerUpdateFieldMask_BANNER_UPDATE_MAPPING_SHERLOCK_USER_ROLES}},
				mocks: []interface{}{
					bannerDao.EXPECT().Update(context.Background(), banner1, gomock.Any()).Return(nil),
					bannerMappingDao.EXPECT().DeleteBatch(gomock.Any(), gomock.Any()).Return(int64(1), nil),
					bannerMappingDao.EXPECT().CreateBatch(gomock.Any(), gomock.Any()).Return(nil, nil),
				},
			},
			want: &sbPb.UpdateBannerResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewSherlockBannersService(sbTS.genConf, bannerDao, bannerMappingDao, sbTS.txnExecutor, mockGetBannersHelper, nil)
			got, err := s.UpdateBanner(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateBanner() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("UpdateBanner() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSherlockBannersService_GetBanners(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	sherlockBannerDao := dao_mock.NewMockISherlockBannerDao(ctr)
	bannerMappingDao := dao_mock.NewMockIBannerMappingDao(ctr)
	mockGetBannersHelper := mock_get_banners_helper.NewMockGetBannersHelper(ctr)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *sbPb.GetBannersRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *sbPb.GetBannersResponse
		wantErr bool
	}{
		{
			name: "failed",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{ActiveNowFlag: true, PageContextRequest: &rpcPb.PageContextRequest{PageSize: 1}},
				mocks: []interface{}{
					mockGetBannersHelper.EXPECT().GetBanners(context.Background(), &sbPb.GetBannersRequest{ActiveNowFlag: true, PageContextRequest: &rpcPb.PageContextRequest{PageSize: 1}}).
						Return(&sbPb.GetBannersResponse{
							Status: rpcPb.StatusInternal(),
						}, nil),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &sbPb.GetBannersRequest{ActiveNowFlag: true, PageContextRequest: &rpcPb.PageContextRequest{PageSize: 1}},
				mocks: []interface{}{
					mockGetBannersHelper.EXPECT().GetBanners(context.Background(), &sbPb.GetBannersRequest{ActiveNowFlag: true, PageContextRequest: &rpcPb.PageContextRequest{PageSize: 1}}).
						Return(&sbPb.GetBannersResponse{
							Status:  rpcPb.StatusOk(),
							Banners: []*sbPb.SherlockBanner{banner1},
						}, nil),
				},
			},
			want: &sbPb.GetBannersResponse{
				Status:  rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{banner1},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewSherlockBannersService(sbTS.genConf, sherlockBannerDao, bannerMappingDao, sbTS.txnExecutor, mockGetBannersHelper, nil)
			got, err := s.GetBanners(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetBanners() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !isDeepEqualGetBannersResp(got, tt.want) {
				t.Errorf("GetBanners() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func isDeepEqualGetBannersResp(got, want *sbPb.GetBannersResponse) bool {
	return reflect.DeepEqual(got.GetStatus(), want.GetStatus()) &&
		reflect.DeepEqual(got.GetPageContextResponse(), want.GetPageContextResponse()) &&
		isDeepEqualSherlockBannersList(got.GetBanners(), want.GetBanners())
}

func isDeepEqualSherlockBannersList(got, want []*sbPb.SherlockBanner) bool {
	if len(got) != len(want) {
		return false
	}
	sort.Slice(got, func(i, j int) bool {
		return got[i].GetId() < got[j].GetId()
	})
	sort.Slice(want, func(i, j int) bool {
		return want[i].GetId() < want[j].GetId()
	})
	for i := 0; i < len(got); i++ {
		if !(isSherlockBannerEqual(got[i], want[i])) {
			return false
		}
	}
	return true
}

func isSherlockBannerEqual(got, want *sbPb.SherlockBanner) bool {
	return reflect.DeepEqual(got.GetBannerContent(), want.GetBannerContent()) &&
		got.GetStartTime() == want.GetStartTime() && got.GetEndTime() == want.GetEndTime() &&
		isDeepEqualMappingItems(got.GetMappingItems(), want.GetMappingItems())
}

func isDeepEqualMappingItems(got, want *sbPb.MappingItems) bool {
	gotRoles := got.GetSherlockUserRoles()
	wantRoles := want.GetSherlockUserRoles()
	if len(gotRoles) != len(wantRoles) {
		return false
	}
	sort.Slice(gotRoles, func(i, j int) bool {
		return int(gotRoles[i]) < int(gotRoles[j])
	})
	sort.Slice(wantRoles, func(i, j int) bool {
		return int(wantRoles[i]) < int(wantRoles[j])
	})
	for i := 0; i < len(gotRoles); i++ {
		if gotRoles[i] != wantRoles[i] {
			return false
		}
	}
	return true
}
