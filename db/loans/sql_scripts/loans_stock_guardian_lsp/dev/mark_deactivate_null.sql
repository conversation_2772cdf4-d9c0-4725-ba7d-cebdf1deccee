-- SQL script to activate loan offers back
UPDATE loan_offers
SET deactivated_at = NULL,
	updated_at = current_timestamp
WHERE id IN (
			 'PALOqPhhwJx6RFGg3F+QSVgaJA250513==',
			 'PALOxh0Bqv9dRT+YKi0mfSsw1Q250511==',
			 'PALOsIgXbXXZRLSz52re8ly7Kw250425==',
			 'PALODDme8VJLQOGpssb2iAyhRw250612==',
			 'PALOjdJoJjo5REeiBDe/5ghiVw250609==',
			 'PALO6yRWRAXnRgOulF4899sg9A250609==',
			 'PALOPtkwtXPhQv+H05t50jCq9Q250517==',
			 'PALOrVN8R0n9RGClaABDVLIxuw250520==',
			 'PALOmsVKkDEITESM+pOcDEXxSA250511==',
			 'PALOCGCwn1oFTX2RTC67hc5HOg250610==',
			 'PALOjBEzg9GjSd6oz07m1nFXWw250510==',
			 'PALOvx6fA1XER/y/OptrRQad+Q250609==',
			 'PALOeMj+X0BrTUSPaal2iRmNNQ250604=='
	);
