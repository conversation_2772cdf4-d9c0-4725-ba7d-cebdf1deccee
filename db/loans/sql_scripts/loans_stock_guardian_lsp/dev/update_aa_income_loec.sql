--- Updating AA income in LOEC for LSP whose data was not present in DB.

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'28200',
		true
					)
WHERE id = 'PALECHUQQeGYfTDWqB3rwE/yoTA250506==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'79173',
		true
					)
WHERE id = 'PALEC3qpq7KniSd+o8CHcC6Odvw250511==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'40000',
		true
					)
WHERE id = 'PALECSFTn5YurTOqQfIn9rkyRxw250425==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'46777',
		true
					)
WHERE id = 'PALECamwMX/0JRwq5jM+TLaRToA250612==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'25976',
		true
					)
WHERE id = 'PALECDcXIaVYmTN6V11x6/nmeUw250609==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'26907',
		true
					)
WHERE id = 'PALECxKBMvS0NQ5KIPFYSvZbbpg250609==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'33000',
		true
					)
WHERE id = 'PALECWtm0VAZyQwOI+/AaArl+gg250517==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'47461.99',
		true
					)
WHERE id = 'PALECFZzFYIZYTPKP3pfTKdOBpQ250520==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'71200',
		true
					)
WHERE id = 'PALECGR1qufo0RnqB/bShsN74JQ250506==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'33858',
		true
					)
WHERE id = 'PALECFDGProSIQW+luAkhjieWwg250610==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'78042',
		true
					)
WHERE id = 'PALECCXX4i/iIRNu9KDV0rOGbVw250506==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'42000',
		true
					)
WHERE id = 'PALEC2WlyBHSETAamJENWqLYWFg250506==';

UPDATE public.loan_offer_eligibility_criteria
SET status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED',
	sub_status = 'LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_APPROVED_BY_FI',
	policy_params = jsonb_set(
		policy_params,
		'{dataInfo,aaData,medianAmountSalaryLast180Days}',
		'39607.19',
		true
					)
WHERE id = 'PALECeL1Y2CfMSkqrtE0enGLRLg250604==';
