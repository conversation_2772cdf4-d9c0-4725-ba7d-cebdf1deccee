-- Date: 2025-06-16
-- Updating the order IDs to new symbols as the orders are affected by symbol change
-- The Gap, Inc. (GPS) performed a ticker change to GAP

UPDATE orders
SET symbol = 'GAP', updated_at = now()
WHERE id in ('USSO2nPG71JYE8240417','USSO47dLhHWrfQ240330','USSO2EUdJDuRtb240418','USSOkWsmb5tySa240505','USSO2303223N5cJChUbf','USSO6438k1fBhi240227','USSOrc7sE9Ub3h240422','USSO2kRdjHotKk240305','USSO4BexuUwTLw230329','USSO3vDHuz1AoY240307','USSOCP6tEFjjUz240307','USSOX81LVNd2iU240311','USSO23keaYs9zw240405','USSO26MkQaaAZi240319','USSOQzvHxpJhow240517','USSO32ZF5KbNya240311','USSOJRPsEvSkku240514','USSO37HpXQ1TTU240320','USSO3L8bb1fJfE240530','USSO2W5UXSqVHE240531','USSO2kgDdsKpmU240601','USSO46Rbjh4ooK240606','USSOuGkQiZJP5R240621','USSO2EA2FP7aVY240628','USSO31VVQjT3gd240630','USSO32aUd5DGir240701','USSO2qnjy5jtaa240703','USSO69P7YPknbg240705','USSO3WHtcmMwje240708','USSOAnhxeCeHX7240711','USSO3iZm2yyBNG240718','USSO2EMouZT15M240723','USSO2uFdjR3Eeg240803','USSO2n27xAtJ8A240805','USSO3sGTe4C72j240827','USSO4Bvj1f8AqZ240827','USSO3UZQ5QnKkH240828','USSO4AEd4Ag8co240904','USSO29URYavsUb240904','USSObKqPhofcmF240904','USSO3wCVUbjnpW240906','USSO3YX6baJ9ag240906','USSO33wpH44aKC240906','USSO2Xt9o9p9Ky240909','USSO3Vs7zYcP6h240909','USSO3bfFhqvGVD240913','USSO3NsNZYKGMR240914','USSO38vNxsnLg4240917','USSO3yoyxPfLCT240924','USSOmRHzim6jng241005','USSO2Pdub4yq9y241009','USSO3eZU9dvyvc241009','USSO2BwyC7VYMP241015','USSO2k3NciJEwD241029');

UPDATE account_activities
SET symbol = 'GAP', updated_at = now()
WHERE id in ('3a4c06d2-fcdf-45a5-9fba-8354fa3776c4','1bbec31d-52f8-43e6-9ce7-03633d1c33b4','12e71f32-97af-493e-98ca-9d0c70b4149a','8b99a861-28a2-45a3-8a14-330bebf61718','7409c472-4d08-453a-ae2f-b774daaf4c23','1baa0fab-a885-4d6f-bf53-85cd06c08c57','b4d0b5c7-9152-42ce-8520-ca6347fa5b05','78c98e24-888b-4680-b24e-bf03d7741d15','8c633040-7d38-43b7-8816-3d1415654dc8','69a278a6-0849-46d0-ba33-486a99a477e0','fbe9a68e-00d2-4f66-a125-019038b764c6','b9841df8-bb7b-466c-99e7-5afd6db50e29','e9dbd4da-f0b8-4d22-b653-2dc15df0bd22','87c9b9ea-70bd-4b58-8d03-495e464ec780','0d8c6462-c2cd-4e0f-91db-680336ed1202','15208c75-56d8-4e3b-af29-b776a4bbca25','fb788c61-a6df-4fab-93fc-18e234be6287','fc176563-7504-4356-9392-6d7eadd07f3b','58366b38-b13a-4d8e-b6d1-e4a0cb7a950e','a1143dfc-c8e8-40c5-9365-4d1d2f067ac3','7f1f91a8-64cc-4540-a934-40abe292e8a2','0d7ba28c-203c-4efd-b2a9-99ecf52cf680','8d6439a6-21ff-4d3b-9240-1eed824a97f4','de902c36-565f-4464-8b00-47dea66cb6d4','a2175b63-a06d-455b-8667-d6e39d9fc1aa','66bb5a94-a17e-4cf6-8087-9d8eb6640095','70fd8c14-61fd-450d-80e8-e943f869cabb','68608c6c-70d0-4e01-8859-9248c43ef18f','f683bb4b-ac39-46fa-8d0b-6a4ef4670f6b','1c7c4650-db54-4212-93e3-77887a5b7b2f','06d825ca-5bf4-4058-aec4-5eaf48499c0a','0f426655-b20c-4fc8-b2d5-aa0983c47ff7','796db1bf-aa89-42de-905f-411b9ad46167','5654f2dc-3afc-47b9-a410-1bb7cd632de7','fb0f81cd-ba42-42af-9256-434940e7a7f7','61f5a514-b706-4474-bfa8-655b620e1ddf','39b81621-bb62-4bae-aaba-75ae30f8f174','a8746902-5c41-4a3b-b4a0-673b69ce8e36','b423d45d-91c6-47b3-934d-560b6b8fa579','0534ee1e-799c-4f64-9d82-5069be473c69','11ea144f-43b4-4379-b3be-01c8af33bc5f','8b9c04ba-df9a-4100-8397-815e4494315c','28a5bfaa-b23f-4fc9-afae-9d1b2674985a','658d555a-0629-4466-9b2e-c58b75b3eab6','8e9c4db2-8bb2-43d7-a7d1-c3c9f845c46f','19664346-4e6a-4fef-ab23-810b4d915d65','e32b53f8-996a-418a-9353-08c4fed0aadb','17078978-387d-4e36-ba5c-d0226e821c62','742d5ad0-c453-4960-99dc-560effd84794','f7d928eb-57a7-4680-9ccc-0053996b841f','df66ffd7-a00d-405f-b37d-46c3413f2349','b6c1f191-026f-46bf-be72-de6072231526','35cacd28-4d59-47bd-ae67-02668a9d78ff','e62fb294-bb38-49cf-949d-886334445782','a9d99ab3-d02e-49f3-ae7d-68c31bea2593','9bda08f7-22fc-471d-9e88-9b9a768dd98a','7683adef-3cbe-47ac-ac38-54105dc4e4c0','27c4a759-100e-4539-8ec3-e493ba19368c','457c0cdc-2236-494b-a96a-a35c5e62cb86','a4aa8bfd-68c2-43f0-b40d-855a5b52e780','dbc58dad-9c81-4b34-9130-44bb59b08b54','729b8622-7710-4bc5-ad86-3d908022b051','b68f08a0-4f3b-4864-89a2-f95ebdd7c841','90fbccf3-2c5f-4883-ad6c-dcf3017c3c0a','166791b8-59be-4821-806a-381358811246','c1e0f183-2736-4145-aa01-3948b8d08532','f0dbcfca-e149-47bf-b529-5ba048cf7c13','3f6a578b-c77c-4647-a912-f67dc6acd5a5','3c5c038e-d281-441e-a59d-3a9644859928','a226776c-f166-44aa-8efe-5b810f685097','75f152d1-4b82-46f0-a2bc-e8c438810a59','28d52d0e-b6ea-4020-8bda-b38782daa462','befa8b33-25c7-49ea-9703-48fa57654cfc','3f490297-2bb8-4c32-831b-640004dff0f3','4f4dac98-23f6-40af-91e8-5219900a6e38','136ad01f-0b82-4110-b04c-3507b97edaee');
