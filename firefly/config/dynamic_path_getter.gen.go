// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "usepgdbconnforccdb":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UsePgdbConnForCcDb\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UsePgdbConnForCcDb, nil
	case "disablehorizontallayoutbyquestengine":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableHorizontalLayoutByQuestEngine\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableHorizontalLayoutByQuestEngine, nil
	case "enablenewcvpforunsecuredcreditcard":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.E<PERSON><PERSON>("invalid path %q for primitive field \"EnableNewCvpForUnsecuredCreditCard\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableNewCvpForUnsecuredCreditCard, nil
	case "disablecreditcardonboarding":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableCreditCardOnboarding\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableCreditCardOnboarding, nil
	case "skipeligibilitycheckfordisabledcconboarding":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipEligibilityCheckForDisabledCCOnboarding\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipEligibilityCheckForDisabledCCOnboarding, nil
	case "enablemassunsecuredonboardingv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableMassUnsecuredOnboardingV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableMassUnsecuredOnboardingV2, nil
	case "enableunsecuredonboardingv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableUnsecuredOnboardingV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableUnsecuredOnboardingV2, nil
	case "enableunsecuredconsentflowonboarding":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableUnsecuredConsentFlowOnboarding\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableUnsecuredConsentFlowOnboarding, nil
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "cctransactionnotificationsubscriber":
		return obj.CCTransactionNotificationSubscriber.Get(dynamicFieldPath[1:])
	case "ccacsnotificationsubscriber":
		return obj.CCAcsNotificationSubscriber.Get(dynamicFieldPath[1:])
	case "ccstatementnotificationsubscriber":
		return obj.CCStatementNotificationSubscriber.Get(dynamicFieldPath[1:])
	case "cardssentforprintingcsvfilesubscriber":
		return obj.CardsSentForPrintingCsvFileSubscriber.Get(dynamicFieldPath[1:])
	case "cardsdispatchedcsvfilesubscriber":
		return obj.CardsDispatchedCsvFileSubscriber.Get(dynamicFieldPath[1:])
	case "cctransactionsforpinotsubscriber":
		return obj.CCTransactionsForPinotSubscriber.Get(dynamicFieldPath[1:])
	case "categorizerupdateforpinotsubscriber":
		return obj.CategorizerUpdateForPinotSubscriber.Get(dynamicFieldPath[1:])
	case "ccauthfactorupdatenotificationsubscriber":
		return obj.CCAuthFactorUpdateNotificationSubscriber.Get(dynamicFieldPath[1:])
	case "cccreditreportdownloadeventsubscriber":
		return obj.CCCreditReportDownloadEventSubscriber.Get(dynamicFieldPath[1:])
	case "processcreditcardofferfilesubscriber":
		return obj.ProcessCreditCardOfferFileSubscriber.Get(dynamicFieldPath[1:])
	case "ccnonfinancialnotificationsubscriber":
		return obj.CCNonFinancialNotificationSubscriber.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "creditcardoffersscreenoptions":
		return obj.CreditCardOffersScreenOptions.Get(dynamicFieldPath[1:])
	case "creditcardoffersscreenoptionsv2":
		return obj.CreditCardOffersScreenOptionsV2.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "ratelimiterconfig":
		return obj.RateLimiterConfig.Get(dynamicFieldPath[1:])
	case "disabledonboardingscreenoptions":
		return obj.DisabledOnboardingScreenOptions.Get(dynamicFieldPath[1:])
	case "unsecuredccrenewalfeereversalconfig":
		return obj.UnsecuredCCRenewalFeeReversalConfig.Get(dynamicFieldPath[1:])
	case "cconboardingstateupdateeventsqssubscriber":
		return obj.CCOnboardingStateUpdateEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "fireflyv2config":
		return obj.FireflyV2Config.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "trimdebugmessagefromstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TrimDebugMessageFromStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TrimDebugMessageFromStatus, nil
	case "enableemiconversion":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableEmiConversion\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableEmiConversion, nil
	case "enablereissuecardratelimiter":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableReissueCardRateLimiter\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableReissueCardRateLimiter, nil
	case "minversionforcconboarding":
		return obj.MinVersionForCCOnboarding.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CreditCardOffersScreenOptions) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "creditlimitamountstring":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreditLimitAmountString\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreditLimitAmountString, nil
	case "cardimageurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CardImageUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CardImageUrl, nil
	case "termsandconditions":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TermsAndConditions\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TermsAndConditions, nil
	case "partnershipurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PartnershipUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PartnershipUrl, nil
	case "getcreditcardctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GetCreditCardCtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GetCreditCardCtaText, nil
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "headerdescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HeaderDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HeaderDescription, nil
	case "feesinfoheader":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FeesInfoHeader\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FeesInfoHeader, nil
	case "broadvisualelementimgurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BroadVisualElementImgUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BroadVisualElementImgUrl, nil
	case "infoblock":
		return obj.InfoBlock.Get(dynamicFieldPath[1:])
	case "popuptextblock":
		return obj.PopUpTextBlock.Get(dynamicFieldPath[1:])
	case "staticimages":
		return obj.StaticImages.Get(dynamicFieldPath[1:])
	case "feesinfo":
		return obj.FeesInfo.Get(dynamicFieldPath[1:])
	case "allfeescta":
		return obj.AllFeesCta.Get(dynamicFieldPath[1:])
	case "rewardsworthctainfo":
		return obj.RewardsWorthCtaInfo.Get(dynamicFieldPath[1:])
	case "joiningfeevoucherinfo":
		return obj.JoiningFeeVoucherInfo.Get(dynamicFieldPath[1:])
	case "acceleratedrewardsinfo":
		return obj.AcceleratedRewardsInfo.Get(dynamicFieldPath[1:])
	case "fullscreenblock":
		return obj.FullScreenBlock.Get(dynamicFieldPath[1:])
	case "lefthalfblock":
		return obj.LeftHalfBlock.Get(dynamicFieldPath[1:])
	case "righthalfblock":
		return obj.RightHalfBlock.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CreditCardOffersScreenOptions", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *InfoItem) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iconlink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink, nil
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "desc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc, nil
	case "iconlink1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink1, nil
	case "title1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title1, nil
	case "desc1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc1, nil
	case "iconlink2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink2, nil
	case "title2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title2, nil
	case "desc2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc2, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for InfoItem", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PopUpTextBlock) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "infotitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InfoTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InfoTitle, nil
	case "infodesc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InfoDesc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InfoDesc, nil
	case "ctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaText, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PopUpTextBlock", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FeesInfoItem) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "desc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc, nil
	case "ctatitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaTitle, nil
	case "ctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaText, nil
	case "ctaweblink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaWebLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaWebLink, nil
	case "title1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title1, nil
	case "desc1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc1, nil
	case "ctatitle1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaTitle1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaTitle1, nil
	case "ctatext1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaText1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaText1, nil
	case "ctaweblink1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaWebLink1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaWebLink1, nil
	case "ctainfoitem":
		return obj.CtaInfoItem.Get(dynamicFieldPath[1:])
	case "bottominfoitem":
		return obj.BottomInfoItem.Get(dynamicFieldPath[1:])
	case "ctainfoitem1":
		return obj.CtaInfoItem1.Get(dynamicFieldPath[1:])
	case "bottominfoitem1":
		return obj.BottomInfoItem1.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FeesInfoItem", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CtaInfoItem) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iconlink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink, nil
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "desc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc, nil
	case "iconlink1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink1, nil
	case "title1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title1, nil
	case "desc1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc1, nil
	case "iconlink2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink2, nil
	case "title2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title2, nil
	case "desc2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc2, nil
	case "iconlink3":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink3\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink3, nil
	case "title3":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title3\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title3, nil
	case "desc3":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc3\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc3, nil
	case "iconlink4":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink4\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink4, nil
	case "title4":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title4\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title4, nil
	case "desc4":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc4\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc4, nil
	case "iconlink5":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink5\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink5, nil
	case "title5":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title5\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title5, nil
	case "desc5":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc5\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc5, nil
	case "iconlink6":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink6\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink6, nil
	case "title6":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title6\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title6, nil
	case "desc6":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc6\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc6, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CtaInfoItem", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *BottomInfoItem) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iconlink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink, nil
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "desc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for BottomInfoItem", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AllFeesCta) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaText, nil
	case "ctaweblink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaWebLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaWebLink, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AllFeesCta", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RewardsWorthCtaInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaText, nil
	case "dplinkscreentitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DpLinkScreenTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DpLinkScreenTitle, nil
	case "dplinkdescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DpLinkDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DpLinkDescription, nil
	case "dplinksubtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DpLinkSubtitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DpLinkSubtitle, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RewardsWorthCtaInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *JoiningFeeVoucherInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iconlink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink, nil
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "ctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaText, nil
	case "ctaweblink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaWebLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaWebLink, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for JoiningFeeVoucherInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AcceleratedRewardsInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "infoiconlink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InfoIconLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InfoIconLink, nil
	case "ctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaText, nil
	case "dplinkscreentitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DpLinkScreenTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DpLinkScreenTitle, nil
	case "dplinkdescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DpLinkDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DpLinkDescription, nil
	case "dplinksubtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DpLinkSubtitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DpLinkSubtitle, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AcceleratedRewardsInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CtaBlockInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iconlink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink, nil
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "subtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SubTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SubTitle, nil
	case "ctaname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaName, nil
	case "ctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaText, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CtaBlockInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CreditCardOffersScreenOptionsV2) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "creditlimitamountstring":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreditLimitAmountString\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreditLimitAmountString, nil
	case "cardimageurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CardImageUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CardImageUrl, nil
	case "termsandconditions":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TermsAndConditions\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TermsAndConditions, nil
	case "partnershipurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PartnershipUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PartnershipUrl, nil
	case "getcreditcardctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GetCreditCardCtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GetCreditCardCtaText, nil
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "headerdescription":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HeaderDescription\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HeaderDescription, nil
	case "feesinfoheader":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FeesInfoHeader\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FeesInfoHeader, nil
	case "broadvisualelementimgurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BroadVisualElementImgUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BroadVisualElementImgUrl, nil
	case "broadvisualelementimgurlios":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BroadVisualElementImgUrlIOS\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BroadVisualElementImgUrlIOS, nil
	case "cta1name":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Cta1Name\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Cta1Name, nil
	case "cta2name":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Cta2Name\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Cta2Name, nil
	case "cta3name":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Cta3Name\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Cta3Name, nil
	case "staticimages":
		return obj.StaticImages.Get(dynamicFieldPath[1:])
	case "feesinfo":
		return obj.FeesInfo.Get(dynamicFieldPath[1:])
	case "allfeescta":
		return obj.AllFeesCta.Get(dynamicFieldPath[1:])
	case "rewardsworthctainfo":
		return obj.RewardsWorthCtaInfo.Get(dynamicFieldPath[1:])
	case "rewardsworthinfo":
		return obj.RewardsWorthInfo.Get(dynamicFieldPath[1:])
	case "feesinfov2":
		return obj.FeesInfoV2.Get(dynamicFieldPath[1:])
	case "joiningfeevoucherinfo":
		return obj.JoiningFeeVoucherInfo.Get(dynamicFieldPath[1:])
	case "acceleratedrewardsinfo":
		return obj.AcceleratedRewardsInfo.Get(dynamicFieldPath[1:])
	case "welcomevouchercta":
		return obj.WelcomeVoucherCta.Get(dynamicFieldPath[1:])
	case "topbrandscta":
		return obj.TopBrandsCta.Get(dynamicFieldPath[1:])
	case "rewardestimationcta":
		return obj.RewardEstimationCta.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CreditCardOffersScreenOptionsV2", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RewardsWorthInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "subtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SubTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SubTitle, nil
	case "desc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RewardsWorthInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FessInfoV2) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "subtitle":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SubTitle\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SubTitle, nil
	case "desc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Desc\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Desc, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FessInfoV2", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CtaBlockInfoV2) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "iconlink":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IconLink\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IconLink, nil
	case "title":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Title\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Title, nil
	case "ctatext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CtaText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CtaText, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CtaBlockInfoV2", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DisabledOnboardingScreenOptions) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "text":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Text\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Text, nil
	case "subtext":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SubText\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SubText, nil
	case "icon":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Icon\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Icon, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DisabledOnboardingScreenOptions", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UnsecuredCCRenewalFeeReversalConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ineligibleuserssegmentid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IneligibleUsersSegmentId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IneligibleUsersSegmentId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UnsecuredCCRenewalFeeReversalConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FireflyV2Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "onboardingretrycooloff":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OnboardingRetryCoolOff\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OnboardingRetryCoolOff, nil
	case "ccineligiblesegments":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CcIneligibleSegments\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CcIneligibleSegments, nil
	case "introscreenv2config":
		return obj.IntroScreenV2Config.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FireflyV2Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *IntroScreenV2Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "introscreenv2loaderanimationheight":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IntroScreenV2LoaderAnimationHeight\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IntroScreenV2LoaderAnimationHeight, nil
	case "introscreenv2loaderanimationwidth":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IntroScreenV2LoaderAnimationWidth\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IntroScreenV2LoaderAnimationWidth, nil
	case "introscreenv2bgimageheight":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IntroScreenV2BgImageHeight\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IntroScreenV2BgImageHeight, nil
	case "introscreenv2bgimagewidth":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IntroScreenV2BgImageWidth\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IntroScreenV2BgImageWidth, nil
	case "introscreenv2loaderanimation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IntroScreenV2LoaderAnimation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IntroScreenV2LoaderAnimation, nil
	case "introscreenv2bgimage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IntroScreenV2BgImage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IntroScreenV2BgImage, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for IntroScreenV2Config", strings.Join(dynamicFieldPath, "."))
	}
}
