// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	app "github.com/epifi/be-common/pkg/frontend/app"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig2 "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/firefly/config"
	genconfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	pinot "github.com/epifi/gamma/pkg/pinot"
)

type Config struct {
	callbacks                             *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                              questsdk.Client
	questFieldPath                        string
	_UsePgdbConnForCcDb                   uint32
	_DisableHorizontalLayoutByQuestEngine uint32
	_EnableNewCvpForUnsecuredCreditCard   uint32
	_DisableCreditCardOnboarding          uint32
	// SkipEligibilityCheckForDisabledCCOnboarding will skip eligibility check when DisableCreditCardOnboarding is true
	_SkipEligibilityCheckForDisabledCCOnboarding uint32
	_EnableMassUnsecuredOnboardingV2             uint32
	_EnableUnsecuredOnboardingV2                 uint32
	_EnableUnsecuredConsentFlowOnboarding        uint32
	_Flags                                       *Flags
	_CCTransactionNotificationSubscriber         *gencfg.SqsSubscriber
	_CCAcsNotificationSubscriber                 *gencfg.SqsSubscriber
	_CCStatementNotificationSubscriber           *gencfg.SqsSubscriber
	_CardsSentForPrintingCsvFileSubscriber       *gencfg.SqsSubscriber
	_CardsDispatchedCsvFileSubscriber            *gencfg.SqsSubscriber
	_CCTransactionsForPinotSubscriber            *gencfg.SqsSubscriber
	_CategorizerUpdateForPinotSubscriber         *gencfg.SqsSubscriber
	_CCAuthFactorUpdateNotificationSubscriber    *gencfg.SqsSubscriber
	_CCCreditReportDownloadEventSubscriber       *gencfg.SqsSubscriber
	_ProcessCreditCardOfferFileSubscriber        *gencfg.SqsSubscriber
	_CCNonFinancialNotificationSubscriber        *gencfg.SqsSubscriber
	_FeatureReleaseConfig                        *genconfig.FeatureReleaseConfig
	_CreditCardOffersScreenOptions               *CreditCardOffersScreenOptions
	_CreditCardOffersScreenOptionsV2             *CreditCardOffersScreenOptionsV2
	_QuestSdk                                    *genconfig2.Config
	_RateLimiterConfig                           *gencfg.RateLimitConfig
	_DisabledOnboardingScreenOptions             *DisabledOnboardingScreenOptions
	_UnsecuredCCRenewalFeeReversalConfig         *UnsecuredCCRenewalFeeReversalConfig
	_CCOnboardingStateUpdateEventSqsSubscriber   *gencfg.SqsSubscriber
	_FireflyV2Config                             *FireflyV2Config
	_Application                                 *config.Application
	_Server                                      *config.Server
	_Logging                                     *cfg.Logging
	_SecureLogging                               *cfg.SecureLogging
	_EpifiDb                                     *cfg.DB
	_CreditCardDb                                *cfg.DB
	_PinotConfig                                 *pinot.Config
	_AWS                                         *config.Aws
	_RudderStack                                 *cfg.RudderStackBroker
	_Secrets                                     *cfg.Secrets
	_Tracing                                     *cfg.Tracing
	_SignalWorkflowPublisher                     *cfg.SqsPublisher
	_Vendor                                      *config.Vendor
	_RedisCachePrefix                            string
	_CreditCardUrl                               *config.CreditCardUrl
	_CreditCardBillPaymentInfo                   []*config.CreditCardBillPaymentInfo
	_CCTransactionEventPublisher                 *cfg.SnsPublisher
	_TransactionsProducer                        *cfg.KinesisProducer
	_CardsSentForPrintingBucketName              string
	_CardsDispatchedBucketName                   string
	_AcsBucketName                               string
	_AcsNotificationRawDataStore                 *config.AcsNotificationRawDataStore
	_EnableViewCardDetailsViaVgPciServer         bool
	_DepositConfigMap                            map[string]*config.DepositConfig
	_QuestRedisOptions                           *cfg.RedisOptions
	_FireflyRedisStore                           *cfg.RedisOptions
	_EnableCcOffersScreenOlderStringAlignment    *app.FeatureConfig
	_EnableCreditReportDownloadConsumer          *app.FeatureConfig
	_SkipWorkflowInitiationForViewCardDetails    *app.FeatureConfig
	_EnableExpiryFromTokenizer                   *app.FeatureConfig
	_DbConnectionAliases                         *config.DbConnectionAlias
	_CreditCardPgDb                              *cfg.DB
	_CardRecommendationConfig                    *config.CardRecommendationConfig
	_CardProgramToSegmentIdMap                   map[string]map[string]string
	_SimplifiConfig                              *config.SimplifiConfig
	_CreditCardCacheConfig                       *config.CreditCardCacheConfig
	_CardProgramToBeneficiaryDetails             map[string]*config.BeneficiaryDetails
	_DpdThirtySegmentId                          string
	_CreditCardFederalPgDb                       *cfg.DB
	_CcOnboardingStateUpdateEventPublisher       *cfg.SqsPublisher
}

func (obj *Config) UsePgdbConnForCcDb() bool {
	if atomic.LoadUint32(&obj._UsePgdbConnForCcDb) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) disableHorizontalLayoutByQuestEngine() bool {
	if atomic.LoadUint32(&obj._DisableHorizontalLayoutByQuestEngine) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) DisableHorizontalLayoutByQuestEngine(ctx context.Context) bool {
	defVal := obj.disableHorizontalLayoutByQuestEngine()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DisableHorizontalLayoutByQuestEngine"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *Config) EnableNewCvpForUnsecuredCreditCard() bool {
	if atomic.LoadUint32(&obj._EnableNewCvpForUnsecuredCreditCard) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) DisableCreditCardOnboarding() bool {
	if atomic.LoadUint32(&obj._DisableCreditCardOnboarding) == 0 {
		return false
	} else {
		return true
	}
}

// SkipEligibilityCheckForDisabledCCOnboarding will skip eligibility check when DisableCreditCardOnboarding is true
func (obj *Config) SkipEligibilityCheckForDisabledCCOnboarding() bool {
	if atomic.LoadUint32(&obj._SkipEligibilityCheckForDisabledCCOnboarding) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) enableMassUnsecuredOnboardingV2() bool {
	if atomic.LoadUint32(&obj._EnableMassUnsecuredOnboardingV2) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableMassUnsecuredOnboardingV2(ctx context.Context) bool {
	defVal := obj.enableMassUnsecuredOnboardingV2()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "EnableMassUnsecuredOnboardingV2"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *Config) enableUnsecuredOnboardingV2() bool {
	if atomic.LoadUint32(&obj._EnableUnsecuredOnboardingV2) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableUnsecuredOnboardingV2(ctx context.Context) bool {
	defVal := obj.enableUnsecuredOnboardingV2()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "EnableUnsecuredOnboardingV2"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *Config) enableUnsecuredConsentFlowOnboarding() bool {
	if atomic.LoadUint32(&obj._EnableUnsecuredConsentFlowOnboarding) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableUnsecuredConsentFlowOnboarding(ctx context.Context) bool {
	defVal := obj.enableUnsecuredConsentFlowOnboarding()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "EnableUnsecuredConsentFlowOnboarding"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) CCTransactionNotificationSubscriber() *gencfg.SqsSubscriber {
	return obj._CCTransactionNotificationSubscriber
}
func (obj *Config) CCAcsNotificationSubscriber() *gencfg.SqsSubscriber {
	return obj._CCAcsNotificationSubscriber
}
func (obj *Config) CCStatementNotificationSubscriber() *gencfg.SqsSubscriber {
	return obj._CCStatementNotificationSubscriber
}
func (obj *Config) CardsSentForPrintingCsvFileSubscriber() *gencfg.SqsSubscriber {
	return obj._CardsSentForPrintingCsvFileSubscriber
}
func (obj *Config) CardsDispatchedCsvFileSubscriber() *gencfg.SqsSubscriber {
	return obj._CardsDispatchedCsvFileSubscriber
}
func (obj *Config) CCTransactionsForPinotSubscriber() *gencfg.SqsSubscriber {
	return obj._CCTransactionsForPinotSubscriber
}
func (obj *Config) CategorizerUpdateForPinotSubscriber() *gencfg.SqsSubscriber {
	return obj._CategorizerUpdateForPinotSubscriber
}
func (obj *Config) CCAuthFactorUpdateNotificationSubscriber() *gencfg.SqsSubscriber {
	return obj._CCAuthFactorUpdateNotificationSubscriber
}
func (obj *Config) CCCreditReportDownloadEventSubscriber() *gencfg.SqsSubscriber {
	return obj._CCCreditReportDownloadEventSubscriber
}
func (obj *Config) ProcessCreditCardOfferFileSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessCreditCardOfferFileSubscriber
}
func (obj *Config) CCNonFinancialNotificationSubscriber() *gencfg.SqsSubscriber {
	return obj._CCNonFinancialNotificationSubscriber
}
func (obj *Config) FeatureReleaseConfig() *genconfig.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) CreditCardOffersScreenOptions() *CreditCardOffersScreenOptions {
	return obj._CreditCardOffersScreenOptions
}
func (obj *Config) CreditCardOffersScreenOptionsV2() *CreditCardOffersScreenOptionsV2 {
	return obj._CreditCardOffersScreenOptionsV2
}
func (obj *Config) QuestSdk() *genconfig2.Config {
	return obj._QuestSdk
}
func (obj *Config) RateLimiterConfig() *gencfg.RateLimitConfig {
	return obj._RateLimiterConfig
}
func (obj *Config) DisabledOnboardingScreenOptions() *DisabledOnboardingScreenOptions {
	return obj._DisabledOnboardingScreenOptions
}
func (obj *Config) UnsecuredCCRenewalFeeReversalConfig() *UnsecuredCCRenewalFeeReversalConfig {
	return obj._UnsecuredCCRenewalFeeReversalConfig
}
func (obj *Config) CCOnboardingStateUpdateEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._CCOnboardingStateUpdateEventSqsSubscriber
}
func (obj *Config) FireflyV2Config() *FireflyV2Config {
	return obj._FireflyV2Config
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) SecureLogging() *cfg.SecureLogging {
	return obj._SecureLogging
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) CreditCardDb() *cfg.DB {
	return obj._CreditCardDb
}
func (obj *Config) PinotConfig() *pinot.Config {
	return obj._PinotConfig
}
func (obj *Config) AWS() *config.Aws {
	return obj._AWS
}
func (obj *Config) RudderStack() *cfg.RudderStackBroker {
	return obj._RudderStack
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) SignalWorkflowPublisher() *cfg.SqsPublisher {
	return obj._SignalWorkflowPublisher
}
func (obj *Config) Vendor() *config.Vendor {
	return obj._Vendor
}
func (obj *Config) RedisCachePrefix() string {
	return obj._RedisCachePrefix
}
func (obj *Config) CreditCardUrl() *config.CreditCardUrl {
	return obj._CreditCardUrl
}
func (obj *Config) CreditCardBillPaymentInfo() []*config.CreditCardBillPaymentInfo {
	return obj._CreditCardBillPaymentInfo
}
func (obj *Config) CCTransactionEventPublisher() *cfg.SnsPublisher {
	return obj._CCTransactionEventPublisher
}
func (obj *Config) TransactionsProducer() *cfg.KinesisProducer {
	return obj._TransactionsProducer
}
func (obj *Config) CardsSentForPrintingBucketName() string {
	return obj._CardsSentForPrintingBucketName
}
func (obj *Config) CardsDispatchedBucketName() string {
	return obj._CardsDispatchedBucketName
}
func (obj *Config) AcsBucketName() string {
	return obj._AcsBucketName
}
func (obj *Config) AcsNotificationRawDataStore() *config.AcsNotificationRawDataStore {
	return obj._AcsNotificationRawDataStore
}
func (obj *Config) EnableViewCardDetailsViaVgPciServer() bool {
	return obj._EnableViewCardDetailsViaVgPciServer
}
func (obj *Config) DepositConfigMap() map[string]*config.DepositConfig {
	return obj._DepositConfigMap
}
func (obj *Config) QuestRedisOptions() *cfg.RedisOptions {
	return obj._QuestRedisOptions
}
func (obj *Config) FireflyRedisStore() *cfg.RedisOptions {
	return obj._FireflyRedisStore
}
func (obj *Config) EnableCcOffersScreenOlderStringAlignment() *app.FeatureConfig {
	return obj._EnableCcOffersScreenOlderStringAlignment
}
func (obj *Config) EnableCreditReportDownloadConsumer() *app.FeatureConfig {
	return obj._EnableCreditReportDownloadConsumer
}
func (obj *Config) SkipWorkflowInitiationForViewCardDetails() *app.FeatureConfig {
	return obj._SkipWorkflowInitiationForViewCardDetails
}
func (obj *Config) EnableExpiryFromTokenizer() *app.FeatureConfig {
	return obj._EnableExpiryFromTokenizer
}
func (obj *Config) DbConnectionAliases() *config.DbConnectionAlias {
	return obj._DbConnectionAliases
}
func (obj *Config) CreditCardPgDb() *cfg.DB {
	return obj._CreditCardPgDb
}
func (obj *Config) CardRecommendationConfig() *config.CardRecommendationConfig {
	return obj._CardRecommendationConfig
}
func (obj *Config) CardProgramToSegmentIdMap() map[string]map[string]string {
	return obj._CardProgramToSegmentIdMap
}
func (obj *Config) SimplifiConfig() *config.SimplifiConfig {
	return obj._SimplifiConfig
}
func (obj *Config) CreditCardCacheConfig() *config.CreditCardCacheConfig {
	return obj._CreditCardCacheConfig
}
func (obj *Config) CardProgramToBeneficiaryDetails() map[string]*config.BeneficiaryDetails {
	return obj._CardProgramToBeneficiaryDetails
}
func (obj *Config) DpdThirtySegmentId() string {
	return obj._DpdThirtySegmentId
}
func (obj *Config) CreditCardFederalPgDb() *cfg.DB {
	return obj._CreditCardFederalPgDb
}
func (obj *Config) CcOnboardingStateUpdateEventPublisher() *cfg.SqsPublisher {
	return obj._CcOnboardingStateUpdateEventPublisher
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// A flag to determine if the debug message in Status is to be trimmed
	_TrimDebugMessageFromStatus uint32
	// Deprecated: Flag for enabling EMI conversion
	_EnableEmiConversion uint32
	// flag to enable/disable reissue card rate limiter
	_EnableReissueCardRateLimiter uint32
	_MinVersionForCCOnboarding    *gencfg.PlatformVersionCheck
}

// A flag to determine if the debug message in Status is to be trimmed
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	if atomic.LoadUint32(&obj._TrimDebugMessageFromStatus) == 0 {
		return false
	} else {
		return true
	}
}

// Deprecated: Flag for enabling EMI conversion
func (obj *Flags) EnableEmiConversion() bool {
	if atomic.LoadUint32(&obj._EnableEmiConversion) == 0 {
		return false
	} else {
		return true
	}
}

// flag to enable/disable reissue card rate limiter
func (obj *Flags) EnableReissueCardRateLimiter() bool {
	if atomic.LoadUint32(&obj._EnableReissueCardRateLimiter) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) MinVersionForCCOnboarding() *gencfg.PlatformVersionCheck {
	return obj._MinVersionForCCOnboarding
}

type CreditCardOffersScreenOptions struct {
	callbacks                      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                       questsdk.Client
	questFieldPath                 string
	_CreditLimitAmountString       string
	_CreditLimitAmountStringMutex  *sync.RWMutex
	_CardImageUrl                  string
	_CardImageUrlMutex             *sync.RWMutex
	_TermsAndConditions            string
	_TermsAndConditionsMutex       *sync.RWMutex
	_PartnershipUrl                string
	_PartnershipUrlMutex           *sync.RWMutex
	_GetCreditCardCtaText          string
	_GetCreditCardCtaTextMutex     *sync.RWMutex
	_Title                         string
	_TitleMutex                    *sync.RWMutex
	_HeaderDescription             string
	_HeaderDescriptionMutex        *sync.RWMutex
	_FeesInfoHeader                string
	_FeesInfoHeaderMutex           *sync.RWMutex
	_BroadVisualElementImgUrl      string
	_BroadVisualElementImgUrlMutex *sync.RWMutex
	_InfoBlock                     *InfoItem
	_PopUpTextBlock                *PopUpTextBlock
	_StaticImages                  *InfoItem
	_FeesInfo                      *FeesInfoItem
	_AllFeesCta                    *AllFeesCta
	_RewardsWorthCtaInfo           *RewardsWorthCtaInfo
	_JoiningFeeVoucherInfo         *JoiningFeeVoucherInfo
	_AcceleratedRewardsInfo        *AcceleratedRewardsInfo
	_FullScreenBlock               *CtaBlockInfo
	_LeftHalfBlock                 *CtaBlockInfo
	_RightHalfBlock                *CtaBlockInfo
}

func (obj *CreditCardOffersScreenOptions) creditLimitAmountString() string {
	obj._CreditLimitAmountStringMutex.RLock()
	defer obj._CreditLimitAmountStringMutex.RUnlock()
	return obj._CreditLimitAmountString
}
func (obj *CreditCardOffersScreenOptions) CreditLimitAmountString(ctx context.Context) string {
	defVal := obj.creditLimitAmountString()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CreditLimitAmountString"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptions) cardImageUrl() string {
	obj._CardImageUrlMutex.RLock()
	defer obj._CardImageUrlMutex.RUnlock()
	return obj._CardImageUrl
}
func (obj *CreditCardOffersScreenOptions) CardImageUrl(ctx context.Context) string {
	defVal := obj.cardImageUrl()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CardImageUrl"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptions) termsAndConditions() string {
	obj._TermsAndConditionsMutex.RLock()
	defer obj._TermsAndConditionsMutex.RUnlock()
	return obj._TermsAndConditions
}
func (obj *CreditCardOffersScreenOptions) TermsAndConditions(ctx context.Context) string {
	defVal := obj.termsAndConditions()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "TermsAndConditions"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptions) partnershipUrl() string {
	obj._PartnershipUrlMutex.RLock()
	defer obj._PartnershipUrlMutex.RUnlock()
	return obj._PartnershipUrl
}
func (obj *CreditCardOffersScreenOptions) PartnershipUrl(ctx context.Context) string {
	defVal := obj.partnershipUrl()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "PartnershipUrl"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptions) getCreditCardCtaText() string {
	obj._GetCreditCardCtaTextMutex.RLock()
	defer obj._GetCreditCardCtaTextMutex.RUnlock()
	return obj._GetCreditCardCtaText
}
func (obj *CreditCardOffersScreenOptions) GetCreditCardCtaText(ctx context.Context) string {
	defVal := obj.getCreditCardCtaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "GetCreditCardCtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptions) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *CreditCardOffersScreenOptions) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptions) headerDescription() string {
	obj._HeaderDescriptionMutex.RLock()
	defer obj._HeaderDescriptionMutex.RUnlock()
	return obj._HeaderDescription
}
func (obj *CreditCardOffersScreenOptions) HeaderDescription(ctx context.Context) string {
	defVal := obj.headerDescription()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "HeaderDescription"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptions) feesInfoHeader() string {
	obj._FeesInfoHeaderMutex.RLock()
	defer obj._FeesInfoHeaderMutex.RUnlock()
	return obj._FeesInfoHeader
}
func (obj *CreditCardOffersScreenOptions) FeesInfoHeader(ctx context.Context) string {
	defVal := obj.feesInfoHeader()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "FeesInfoHeader"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptions) broadVisualElementImgUrl() string {
	obj._BroadVisualElementImgUrlMutex.RLock()
	defer obj._BroadVisualElementImgUrlMutex.RUnlock()
	return obj._BroadVisualElementImgUrl
}
func (obj *CreditCardOffersScreenOptions) BroadVisualElementImgUrl(ctx context.Context) string {
	defVal := obj.broadVisualElementImgUrl()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "BroadVisualElementImgUrl"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptions) InfoBlock() *InfoItem {
	return obj._InfoBlock
}
func (obj *CreditCardOffersScreenOptions) PopUpTextBlock() *PopUpTextBlock {
	return obj._PopUpTextBlock
}
func (obj *CreditCardOffersScreenOptions) StaticImages() *InfoItem {
	return obj._StaticImages
}
func (obj *CreditCardOffersScreenOptions) FeesInfo() *FeesInfoItem {
	return obj._FeesInfo
}
func (obj *CreditCardOffersScreenOptions) AllFeesCta() *AllFeesCta {
	return obj._AllFeesCta
}
func (obj *CreditCardOffersScreenOptions) RewardsWorthCtaInfo() *RewardsWorthCtaInfo {
	return obj._RewardsWorthCtaInfo
}
func (obj *CreditCardOffersScreenOptions) JoiningFeeVoucherInfo() *JoiningFeeVoucherInfo {
	return obj._JoiningFeeVoucherInfo
}
func (obj *CreditCardOffersScreenOptions) AcceleratedRewardsInfo() *AcceleratedRewardsInfo {
	return obj._AcceleratedRewardsInfo
}
func (obj *CreditCardOffersScreenOptions) FullScreenBlock() *CtaBlockInfo {
	return obj._FullScreenBlock
}
func (obj *CreditCardOffersScreenOptions) LeftHalfBlock() *CtaBlockInfo {
	return obj._LeftHalfBlock
}
func (obj *CreditCardOffersScreenOptions) RightHalfBlock() *CtaBlockInfo {
	return obj._RightHalfBlock
}

type InfoItem struct {
	callbacks       *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk        questsdk.Client
	questFieldPath  string
	_IconLink       string
	_IconLinkMutex  *sync.RWMutex
	_Title          string
	_TitleMutex     *sync.RWMutex
	_Desc           string
	_DescMutex      *sync.RWMutex
	_IconLink1      string
	_IconLink1Mutex *sync.RWMutex
	_Title1         string
	_Title1Mutex    *sync.RWMutex
	_Desc1          string
	_Desc1Mutex     *sync.RWMutex
	_IconLink2      string
	_IconLink2Mutex *sync.RWMutex
	_Title2         string
	_Title2Mutex    *sync.RWMutex
	_Desc2          string
	_Desc2Mutex     *sync.RWMutex
}

func (obj *InfoItem) iconLink() string {
	obj._IconLinkMutex.RLock()
	defer obj._IconLinkMutex.RUnlock()
	return obj._IconLink
}
func (obj *InfoItem) IconLink(ctx context.Context) string {
	defVal := obj.iconLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *InfoItem) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *InfoItem) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *InfoItem) desc() string {
	obj._DescMutex.RLock()
	defer obj._DescMutex.RUnlock()
	return obj._Desc
}
func (obj *InfoItem) Desc(ctx context.Context) string {
	defVal := obj.desc()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *InfoItem) iconLink1() string {
	obj._IconLink1Mutex.RLock()
	defer obj._IconLink1Mutex.RUnlock()
	return obj._IconLink1
}
func (obj *InfoItem) IconLink1(ctx context.Context) string {
	defVal := obj.iconLink1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *InfoItem) title1() string {
	obj._Title1Mutex.RLock()
	defer obj._Title1Mutex.RUnlock()
	return obj._Title1
}
func (obj *InfoItem) Title1(ctx context.Context) string {
	defVal := obj.title1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *InfoItem) desc1() string {
	obj._Desc1Mutex.RLock()
	defer obj._Desc1Mutex.RUnlock()
	return obj._Desc1
}
func (obj *InfoItem) Desc1(ctx context.Context) string {
	defVal := obj.desc1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *InfoItem) iconLink2() string {
	obj._IconLink2Mutex.RLock()
	defer obj._IconLink2Mutex.RUnlock()
	return obj._IconLink2
}
func (obj *InfoItem) IconLink2(ctx context.Context) string {
	defVal := obj.iconLink2()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink2"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *InfoItem) title2() string {
	obj._Title2Mutex.RLock()
	defer obj._Title2Mutex.RUnlock()
	return obj._Title2
}
func (obj *InfoItem) Title2(ctx context.Context) string {
	defVal := obj.title2()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title2"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *InfoItem) desc2() string {
	obj._Desc2Mutex.RLock()
	defer obj._Desc2Mutex.RUnlock()
	return obj._Desc2
}
func (obj *InfoItem) Desc2(ctx context.Context) string {
	defVal := obj.desc2()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc2"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type PopUpTextBlock struct {
	callbacks       *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk        questsdk.Client
	questFieldPath  string
	_InfoTitle      string
	_InfoTitleMutex *sync.RWMutex
	_InfoDesc       string
	_InfoDescMutex  *sync.RWMutex
	_CtaText        string
	_CtaTextMutex   *sync.RWMutex
}

func (obj *PopUpTextBlock) infoTitle() string {
	obj._InfoTitleMutex.RLock()
	defer obj._InfoTitleMutex.RUnlock()
	return obj._InfoTitle
}
func (obj *PopUpTextBlock) InfoTitle(ctx context.Context) string {
	defVal := obj.infoTitle()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "InfoTitle"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *PopUpTextBlock) infoDesc() string {
	obj._InfoDescMutex.RLock()
	defer obj._InfoDescMutex.RUnlock()
	return obj._InfoDesc
}
func (obj *PopUpTextBlock) InfoDesc(ctx context.Context) string {
	defVal := obj.infoDesc()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "InfoDesc"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *PopUpTextBlock) ctaText() string {
	obj._CtaTextMutex.RLock()
	defer obj._CtaTextMutex.RUnlock()
	return obj._CtaText
}
func (obj *PopUpTextBlock) CtaText(ctx context.Context) string {
	defVal := obj.ctaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type FeesInfoItem struct {
	callbacks         *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk          questsdk.Client
	questFieldPath    string
	_Title            string
	_TitleMutex       *sync.RWMutex
	_Desc             string
	_DescMutex        *sync.RWMutex
	_CtaTitle         string
	_CtaTitleMutex    *sync.RWMutex
	_CtaText          string
	_CtaTextMutex     *sync.RWMutex
	_CtaWebLink       string
	_CtaWebLinkMutex  *sync.RWMutex
	_Title1           string
	_Title1Mutex      *sync.RWMutex
	_Desc1            string
	_Desc1Mutex       *sync.RWMutex
	_CtaTitle1        string
	_CtaTitle1Mutex   *sync.RWMutex
	_CtaText1         string
	_CtaText1Mutex    *sync.RWMutex
	_CtaWebLink1      string
	_CtaWebLink1Mutex *sync.RWMutex
	_CtaInfoItem      *CtaInfoItem
	_BottomInfoItem   *BottomInfoItem
	_CtaInfoItem1     *CtaInfoItem
	_BottomInfoItem1  *BottomInfoItem
}

func (obj *FeesInfoItem) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *FeesInfoItem) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) desc() string {
	obj._DescMutex.RLock()
	defer obj._DescMutex.RUnlock()
	return obj._Desc
}
func (obj *FeesInfoItem) Desc(ctx context.Context) string {
	defVal := obj.desc()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) ctaTitle() string {
	obj._CtaTitleMutex.RLock()
	defer obj._CtaTitleMutex.RUnlock()
	return obj._CtaTitle
}
func (obj *FeesInfoItem) CtaTitle(ctx context.Context) string {
	defVal := obj.ctaTitle()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaTitle"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) ctaText() string {
	obj._CtaTextMutex.RLock()
	defer obj._CtaTextMutex.RUnlock()
	return obj._CtaText
}
func (obj *FeesInfoItem) CtaText(ctx context.Context) string {
	defVal := obj.ctaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) ctaWebLink() string {
	obj._CtaWebLinkMutex.RLock()
	defer obj._CtaWebLinkMutex.RUnlock()
	return obj._CtaWebLink
}
func (obj *FeesInfoItem) CtaWebLink(ctx context.Context) string {
	defVal := obj.ctaWebLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaWebLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) title1() string {
	obj._Title1Mutex.RLock()
	defer obj._Title1Mutex.RUnlock()
	return obj._Title1
}
func (obj *FeesInfoItem) Title1(ctx context.Context) string {
	defVal := obj.title1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) desc1() string {
	obj._Desc1Mutex.RLock()
	defer obj._Desc1Mutex.RUnlock()
	return obj._Desc1
}
func (obj *FeesInfoItem) Desc1(ctx context.Context) string {
	defVal := obj.desc1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) ctaTitle1() string {
	obj._CtaTitle1Mutex.RLock()
	defer obj._CtaTitle1Mutex.RUnlock()
	return obj._CtaTitle1
}
func (obj *FeesInfoItem) CtaTitle1(ctx context.Context) string {
	defVal := obj.ctaTitle1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaTitle1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) ctaText1() string {
	obj._CtaText1Mutex.RLock()
	defer obj._CtaText1Mutex.RUnlock()
	return obj._CtaText1
}
func (obj *FeesInfoItem) CtaText1(ctx context.Context) string {
	defVal := obj.ctaText1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaText1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) ctaWebLink1() string {
	obj._CtaWebLink1Mutex.RLock()
	defer obj._CtaWebLink1Mutex.RUnlock()
	return obj._CtaWebLink1
}
func (obj *FeesInfoItem) CtaWebLink1(ctx context.Context) string {
	defVal := obj.ctaWebLink1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaWebLink1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FeesInfoItem) CtaInfoItem() *CtaInfoItem {
	return obj._CtaInfoItem
}
func (obj *FeesInfoItem) BottomInfoItem() *BottomInfoItem {
	return obj._BottomInfoItem
}
func (obj *FeesInfoItem) CtaInfoItem1() *CtaInfoItem {
	return obj._CtaInfoItem1
}
func (obj *FeesInfoItem) BottomInfoItem1() *BottomInfoItem {
	return obj._BottomInfoItem1
}

type CtaInfoItem struct {
	callbacks       *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk        questsdk.Client
	questFieldPath  string
	_IconLink       string
	_IconLinkMutex  *sync.RWMutex
	_Title          string
	_TitleMutex     *sync.RWMutex
	_Desc           string
	_DescMutex      *sync.RWMutex
	_IconLink1      string
	_IconLink1Mutex *sync.RWMutex
	_Title1         string
	_Title1Mutex    *sync.RWMutex
	_Desc1          string
	_Desc1Mutex     *sync.RWMutex
	_IconLink2      string
	_IconLink2Mutex *sync.RWMutex
	_Title2         string
	_Title2Mutex    *sync.RWMutex
	_Desc2          string
	_Desc2Mutex     *sync.RWMutex
	_IconLink3      string
	_IconLink3Mutex *sync.RWMutex
	_Title3         string
	_Title3Mutex    *sync.RWMutex
	_Desc3          string
	_Desc3Mutex     *sync.RWMutex
	_IconLink4      string
	_IconLink4Mutex *sync.RWMutex
	_Title4         string
	_Title4Mutex    *sync.RWMutex
	_Desc4          string
	_Desc4Mutex     *sync.RWMutex
	_IconLink5      string
	_IconLink5Mutex *sync.RWMutex
	_Title5         string
	_Title5Mutex    *sync.RWMutex
	_Desc5          string
	_Desc5Mutex     *sync.RWMutex
	_IconLink6      string
	_IconLink6Mutex *sync.RWMutex
	_Title6         string
	_Title6Mutex    *sync.RWMutex
	_Desc6          string
	_Desc6Mutex     *sync.RWMutex
}

func (obj *CtaInfoItem) iconLink() string {
	obj._IconLinkMutex.RLock()
	defer obj._IconLinkMutex.RUnlock()
	return obj._IconLink
}
func (obj *CtaInfoItem) IconLink(ctx context.Context) string {
	defVal := obj.iconLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *CtaInfoItem) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) desc() string {
	obj._DescMutex.RLock()
	defer obj._DescMutex.RUnlock()
	return obj._Desc
}
func (obj *CtaInfoItem) Desc(ctx context.Context) string {
	defVal := obj.desc()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) iconLink1() string {
	obj._IconLink1Mutex.RLock()
	defer obj._IconLink1Mutex.RUnlock()
	return obj._IconLink1
}
func (obj *CtaInfoItem) IconLink1(ctx context.Context) string {
	defVal := obj.iconLink1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) title1() string {
	obj._Title1Mutex.RLock()
	defer obj._Title1Mutex.RUnlock()
	return obj._Title1
}
func (obj *CtaInfoItem) Title1(ctx context.Context) string {
	defVal := obj.title1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) desc1() string {
	obj._Desc1Mutex.RLock()
	defer obj._Desc1Mutex.RUnlock()
	return obj._Desc1
}
func (obj *CtaInfoItem) Desc1(ctx context.Context) string {
	defVal := obj.desc1()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc1"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) iconLink2() string {
	obj._IconLink2Mutex.RLock()
	defer obj._IconLink2Mutex.RUnlock()
	return obj._IconLink2
}
func (obj *CtaInfoItem) IconLink2(ctx context.Context) string {
	defVal := obj.iconLink2()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink2"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) title2() string {
	obj._Title2Mutex.RLock()
	defer obj._Title2Mutex.RUnlock()
	return obj._Title2
}
func (obj *CtaInfoItem) Title2(ctx context.Context) string {
	defVal := obj.title2()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title2"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) desc2() string {
	obj._Desc2Mutex.RLock()
	defer obj._Desc2Mutex.RUnlock()
	return obj._Desc2
}
func (obj *CtaInfoItem) Desc2(ctx context.Context) string {
	defVal := obj.desc2()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc2"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) iconLink3() string {
	obj._IconLink3Mutex.RLock()
	defer obj._IconLink3Mutex.RUnlock()
	return obj._IconLink3
}
func (obj *CtaInfoItem) IconLink3(ctx context.Context) string {
	defVal := obj.iconLink3()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink3"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) title3() string {
	obj._Title3Mutex.RLock()
	defer obj._Title3Mutex.RUnlock()
	return obj._Title3
}
func (obj *CtaInfoItem) Title3(ctx context.Context) string {
	defVal := obj.title3()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title3"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) desc3() string {
	obj._Desc3Mutex.RLock()
	defer obj._Desc3Mutex.RUnlock()
	return obj._Desc3
}
func (obj *CtaInfoItem) Desc3(ctx context.Context) string {
	defVal := obj.desc3()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc3"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) iconLink4() string {
	obj._IconLink4Mutex.RLock()
	defer obj._IconLink4Mutex.RUnlock()
	return obj._IconLink4
}
func (obj *CtaInfoItem) IconLink4(ctx context.Context) string {
	defVal := obj.iconLink4()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink4"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) title4() string {
	obj._Title4Mutex.RLock()
	defer obj._Title4Mutex.RUnlock()
	return obj._Title4
}
func (obj *CtaInfoItem) Title4(ctx context.Context) string {
	defVal := obj.title4()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title4"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) desc4() string {
	obj._Desc4Mutex.RLock()
	defer obj._Desc4Mutex.RUnlock()
	return obj._Desc4
}
func (obj *CtaInfoItem) Desc4(ctx context.Context) string {
	defVal := obj.desc4()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc4"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) iconLink5() string {
	obj._IconLink5Mutex.RLock()
	defer obj._IconLink5Mutex.RUnlock()
	return obj._IconLink5
}
func (obj *CtaInfoItem) IconLink5(ctx context.Context) string {
	defVal := obj.iconLink5()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink5"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) title5() string {
	obj._Title5Mutex.RLock()
	defer obj._Title5Mutex.RUnlock()
	return obj._Title5
}
func (obj *CtaInfoItem) Title5(ctx context.Context) string {
	defVal := obj.title5()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title5"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) desc5() string {
	obj._Desc5Mutex.RLock()
	defer obj._Desc5Mutex.RUnlock()
	return obj._Desc5
}
func (obj *CtaInfoItem) Desc5(ctx context.Context) string {
	defVal := obj.desc5()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc5"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) iconLink6() string {
	obj._IconLink6Mutex.RLock()
	defer obj._IconLink6Mutex.RUnlock()
	return obj._IconLink6
}
func (obj *CtaInfoItem) IconLink6(ctx context.Context) string {
	defVal := obj.iconLink6()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink6"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) title6() string {
	obj._Title6Mutex.RLock()
	defer obj._Title6Mutex.RUnlock()
	return obj._Title6
}
func (obj *CtaInfoItem) Title6(ctx context.Context) string {
	defVal := obj.title6()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title6"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaInfoItem) desc6() string {
	obj._Desc6Mutex.RLock()
	defer obj._Desc6Mutex.RUnlock()
	return obj._Desc6
}
func (obj *CtaInfoItem) Desc6(ctx context.Context) string {
	defVal := obj.desc6()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc6"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type BottomInfoItem struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_IconLink      string
	_IconLinkMutex *sync.RWMutex
	_Title         string
	_TitleMutex    *sync.RWMutex
	_Desc          string
	_DescMutex     *sync.RWMutex
}

func (obj *BottomInfoItem) iconLink() string {
	obj._IconLinkMutex.RLock()
	defer obj._IconLinkMutex.RUnlock()
	return obj._IconLink
}
func (obj *BottomInfoItem) IconLink(ctx context.Context) string {
	defVal := obj.iconLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *BottomInfoItem) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *BottomInfoItem) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *BottomInfoItem) desc() string {
	obj._DescMutex.RLock()
	defer obj._DescMutex.RUnlock()
	return obj._Desc
}
func (obj *BottomInfoItem) Desc(ctx context.Context) string {
	defVal := obj.desc()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type AllFeesCta struct {
	callbacks        *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk         questsdk.Client
	questFieldPath   string
	_CtaText         string
	_CtaTextMutex    *sync.RWMutex
	_CtaWebLink      string
	_CtaWebLinkMutex *sync.RWMutex
}

func (obj *AllFeesCta) ctaText() string {
	obj._CtaTextMutex.RLock()
	defer obj._CtaTextMutex.RUnlock()
	return obj._CtaText
}
func (obj *AllFeesCta) CtaText(ctx context.Context) string {
	defVal := obj.ctaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *AllFeesCta) ctaWebLink() string {
	obj._CtaWebLinkMutex.RLock()
	defer obj._CtaWebLinkMutex.RUnlock()
	return obj._CtaWebLink
}
func (obj *AllFeesCta) CtaWebLink(ctx context.Context) string {
	defVal := obj.ctaWebLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaWebLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type RewardsWorthCtaInfo struct {
	callbacks               *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                questsdk.Client
	questFieldPath          string
	_CtaText                string
	_CtaTextMutex           *sync.RWMutex
	_DpLinkScreenTitle      string
	_DpLinkScreenTitleMutex *sync.RWMutex
	_DpLinkDescription      string
	_DpLinkDescriptionMutex *sync.RWMutex
	_DpLinkSubtitle         string
	_DpLinkSubtitleMutex    *sync.RWMutex
}

func (obj *RewardsWorthCtaInfo) ctaText() string {
	obj._CtaTextMutex.RLock()
	defer obj._CtaTextMutex.RUnlock()
	return obj._CtaText
}
func (obj *RewardsWorthCtaInfo) CtaText(ctx context.Context) string {
	defVal := obj.ctaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *RewardsWorthCtaInfo) dpLinkScreenTitle() string {
	obj._DpLinkScreenTitleMutex.RLock()
	defer obj._DpLinkScreenTitleMutex.RUnlock()
	return obj._DpLinkScreenTitle
}
func (obj *RewardsWorthCtaInfo) DpLinkScreenTitle(ctx context.Context) string {
	defVal := obj.dpLinkScreenTitle()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DpLinkScreenTitle"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *RewardsWorthCtaInfo) dpLinkDescription() string {
	obj._DpLinkDescriptionMutex.RLock()
	defer obj._DpLinkDescriptionMutex.RUnlock()
	return obj._DpLinkDescription
}
func (obj *RewardsWorthCtaInfo) DpLinkDescription(ctx context.Context) string {
	defVal := obj.dpLinkDescription()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DpLinkDescription"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *RewardsWorthCtaInfo) dpLinkSubtitle() string {
	obj._DpLinkSubtitleMutex.RLock()
	defer obj._DpLinkSubtitleMutex.RUnlock()
	return obj._DpLinkSubtitle
}
func (obj *RewardsWorthCtaInfo) DpLinkSubtitle(ctx context.Context) string {
	defVal := obj.dpLinkSubtitle()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DpLinkSubtitle"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type JoiningFeeVoucherInfo struct {
	callbacks        *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk         questsdk.Client
	questFieldPath   string
	_IconLink        string
	_IconLinkMutex   *sync.RWMutex
	_Title           string
	_TitleMutex      *sync.RWMutex
	_CtaText         string
	_CtaTextMutex    *sync.RWMutex
	_CtaWebLink      string
	_CtaWebLinkMutex *sync.RWMutex
}

func (obj *JoiningFeeVoucherInfo) iconLink() string {
	obj._IconLinkMutex.RLock()
	defer obj._IconLinkMutex.RUnlock()
	return obj._IconLink
}
func (obj *JoiningFeeVoucherInfo) IconLink(ctx context.Context) string {
	defVal := obj.iconLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *JoiningFeeVoucherInfo) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *JoiningFeeVoucherInfo) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *JoiningFeeVoucherInfo) ctaText() string {
	obj._CtaTextMutex.RLock()
	defer obj._CtaTextMutex.RUnlock()
	return obj._CtaText
}
func (obj *JoiningFeeVoucherInfo) CtaText(ctx context.Context) string {
	defVal := obj.ctaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *JoiningFeeVoucherInfo) ctaWebLink() string {
	obj._CtaWebLinkMutex.RLock()
	defer obj._CtaWebLinkMutex.RUnlock()
	return obj._CtaWebLink
}
func (obj *JoiningFeeVoucherInfo) CtaWebLink(ctx context.Context) string {
	defVal := obj.ctaWebLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaWebLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type AcceleratedRewardsInfo struct {
	callbacks               *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                questsdk.Client
	questFieldPath          string
	_Title                  string
	_TitleMutex             *sync.RWMutex
	_InfoIconLink           string
	_InfoIconLinkMutex      *sync.RWMutex
	_CtaText                string
	_CtaTextMutex           *sync.RWMutex
	_DpLinkScreenTitle      string
	_DpLinkScreenTitleMutex *sync.RWMutex
	_DpLinkDescription      string
	_DpLinkDescriptionMutex *sync.RWMutex
	_DpLinkSubtitle         string
	_DpLinkSubtitleMutex    *sync.RWMutex
}

func (obj *AcceleratedRewardsInfo) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *AcceleratedRewardsInfo) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *AcceleratedRewardsInfo) infoIconLink() string {
	obj._InfoIconLinkMutex.RLock()
	defer obj._InfoIconLinkMutex.RUnlock()
	return obj._InfoIconLink
}
func (obj *AcceleratedRewardsInfo) InfoIconLink(ctx context.Context) string {
	defVal := obj.infoIconLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "InfoIconLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *AcceleratedRewardsInfo) ctaText() string {
	obj._CtaTextMutex.RLock()
	defer obj._CtaTextMutex.RUnlock()
	return obj._CtaText
}
func (obj *AcceleratedRewardsInfo) CtaText(ctx context.Context) string {
	defVal := obj.ctaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *AcceleratedRewardsInfo) dpLinkScreenTitle() string {
	obj._DpLinkScreenTitleMutex.RLock()
	defer obj._DpLinkScreenTitleMutex.RUnlock()
	return obj._DpLinkScreenTitle
}
func (obj *AcceleratedRewardsInfo) DpLinkScreenTitle(ctx context.Context) string {
	defVal := obj.dpLinkScreenTitle()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DpLinkScreenTitle"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *AcceleratedRewardsInfo) dpLinkDescription() string {
	obj._DpLinkDescriptionMutex.RLock()
	defer obj._DpLinkDescriptionMutex.RUnlock()
	return obj._DpLinkDescription
}
func (obj *AcceleratedRewardsInfo) DpLinkDescription(ctx context.Context) string {
	defVal := obj.dpLinkDescription()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DpLinkDescription"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *AcceleratedRewardsInfo) dpLinkSubtitle() string {
	obj._DpLinkSubtitleMutex.RLock()
	defer obj._DpLinkSubtitleMutex.RUnlock()
	return obj._DpLinkSubtitle
}
func (obj *AcceleratedRewardsInfo) DpLinkSubtitle(ctx context.Context) string {
	defVal := obj.dpLinkSubtitle()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "DpLinkSubtitle"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type CtaBlockInfo struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_IconLink      string
	_IconLinkMutex *sync.RWMutex
	_Title         string
	_TitleMutex    *sync.RWMutex
	_SubTitle      string
	_SubTitleMutex *sync.RWMutex
	_CtaName       string
	_CtaNameMutex  *sync.RWMutex
	_CtaText       string
	_CtaTextMutex  *sync.RWMutex
}

func (obj *CtaBlockInfo) iconLink() string {
	obj._IconLinkMutex.RLock()
	defer obj._IconLinkMutex.RUnlock()
	return obj._IconLink
}
func (obj *CtaBlockInfo) IconLink(ctx context.Context) string {
	defVal := obj.iconLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaBlockInfo) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *CtaBlockInfo) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaBlockInfo) subTitle() string {
	obj._SubTitleMutex.RLock()
	defer obj._SubTitleMutex.RUnlock()
	return obj._SubTitle
}
func (obj *CtaBlockInfo) SubTitle(ctx context.Context) string {
	defVal := obj.subTitle()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "SubTitle"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaBlockInfo) ctaName() string {
	obj._CtaNameMutex.RLock()
	defer obj._CtaNameMutex.RUnlock()
	return obj._CtaName
}
func (obj *CtaBlockInfo) CtaName(ctx context.Context) string {
	defVal := obj.ctaName()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaName"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaBlockInfo) ctaText() string {
	obj._CtaTextMutex.RLock()
	defer obj._CtaTextMutex.RUnlock()
	return obj._CtaText
}
func (obj *CtaBlockInfo) CtaText(ctx context.Context) string {
	defVal := obj.ctaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type CreditCardOffersScreenOptionsV2 struct {
	callbacks                         *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                          questsdk.Client
	questFieldPath                    string
	_CreditLimitAmountString          string
	_CreditLimitAmountStringMutex     *sync.RWMutex
	_CardImageUrl                     string
	_CardImageUrlMutex                *sync.RWMutex
	_TermsAndConditions               string
	_TermsAndConditionsMutex          *sync.RWMutex
	_PartnershipUrl                   string
	_PartnershipUrlMutex              *sync.RWMutex
	_GetCreditCardCtaText             string
	_GetCreditCardCtaTextMutex        *sync.RWMutex
	_Title                            string
	_TitleMutex                       *sync.RWMutex
	_HeaderDescription                string
	_HeaderDescriptionMutex           *sync.RWMutex
	_FeesInfoHeader                   string
	_FeesInfoHeaderMutex              *sync.RWMutex
	_BroadVisualElementImgUrl         string
	_BroadVisualElementImgUrlMutex    *sync.RWMutex
	_BroadVisualElementImgUrlIOS      string
	_BroadVisualElementImgUrlIOSMutex *sync.RWMutex
	// These are deprecated, we are still having them to support backward compatibility for older screens
	_Cta1Name               string
	_Cta1NameMutex          *sync.RWMutex
	_Cta2Name               string
	_Cta2NameMutex          *sync.RWMutex
	_Cta3Name               string
	_Cta3NameMutex          *sync.RWMutex
	_StaticImages           *InfoItem
	_FeesInfo               *FeesInfoItem
	_AllFeesCta             *AllFeesCta
	_RewardsWorthCtaInfo    *RewardsWorthCtaInfo
	_RewardsWorthInfo       *RewardsWorthInfo
	_FeesInfoV2             *FessInfoV2
	_JoiningFeeVoucherInfo  *JoiningFeeVoucherInfo
	_AcceleratedRewardsInfo *AcceleratedRewardsInfo
	_WelcomeVoucherCta      *CtaBlockInfoV2
	_TopBrandsCta           *CtaBlockInfoV2
	_RewardEstimationCta    *CtaBlockInfoV2
}

func (obj *CreditCardOffersScreenOptionsV2) creditLimitAmountString() string {
	obj._CreditLimitAmountStringMutex.RLock()
	defer obj._CreditLimitAmountStringMutex.RUnlock()
	return obj._CreditLimitAmountString
}
func (obj *CreditCardOffersScreenOptionsV2) CreditLimitAmountString(ctx context.Context) string {
	defVal := obj.creditLimitAmountString()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CreditLimitAmountString"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) cardImageUrl() string {
	obj._CardImageUrlMutex.RLock()
	defer obj._CardImageUrlMutex.RUnlock()
	return obj._CardImageUrl
}
func (obj *CreditCardOffersScreenOptionsV2) CardImageUrl(ctx context.Context) string {
	defVal := obj.cardImageUrl()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CardImageUrl"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) termsAndConditions() string {
	obj._TermsAndConditionsMutex.RLock()
	defer obj._TermsAndConditionsMutex.RUnlock()
	return obj._TermsAndConditions
}
func (obj *CreditCardOffersScreenOptionsV2) TermsAndConditions(ctx context.Context) string {
	defVal := obj.termsAndConditions()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "TermsAndConditions"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) partnershipUrl() string {
	obj._PartnershipUrlMutex.RLock()
	defer obj._PartnershipUrlMutex.RUnlock()
	return obj._PartnershipUrl
}
func (obj *CreditCardOffersScreenOptionsV2) PartnershipUrl(ctx context.Context) string {
	defVal := obj.partnershipUrl()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "PartnershipUrl"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) getCreditCardCtaText() string {
	obj._GetCreditCardCtaTextMutex.RLock()
	defer obj._GetCreditCardCtaTextMutex.RUnlock()
	return obj._GetCreditCardCtaText
}
func (obj *CreditCardOffersScreenOptionsV2) GetCreditCardCtaText(ctx context.Context) string {
	defVal := obj.getCreditCardCtaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "GetCreditCardCtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *CreditCardOffersScreenOptionsV2) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) headerDescription() string {
	obj._HeaderDescriptionMutex.RLock()
	defer obj._HeaderDescriptionMutex.RUnlock()
	return obj._HeaderDescription
}
func (obj *CreditCardOffersScreenOptionsV2) HeaderDescription(ctx context.Context) string {
	defVal := obj.headerDescription()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "HeaderDescription"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) feesInfoHeader() string {
	obj._FeesInfoHeaderMutex.RLock()
	defer obj._FeesInfoHeaderMutex.RUnlock()
	return obj._FeesInfoHeader
}
func (obj *CreditCardOffersScreenOptionsV2) FeesInfoHeader(ctx context.Context) string {
	defVal := obj.feesInfoHeader()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "FeesInfoHeader"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) broadVisualElementImgUrl() string {
	obj._BroadVisualElementImgUrlMutex.RLock()
	defer obj._BroadVisualElementImgUrlMutex.RUnlock()
	return obj._BroadVisualElementImgUrl
}
func (obj *CreditCardOffersScreenOptionsV2) BroadVisualElementImgUrl(ctx context.Context) string {
	defVal := obj.broadVisualElementImgUrl()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "BroadVisualElementImgUrl"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) broadVisualElementImgUrlIOS() string {
	obj._BroadVisualElementImgUrlIOSMutex.RLock()
	defer obj._BroadVisualElementImgUrlIOSMutex.RUnlock()
	return obj._BroadVisualElementImgUrlIOS
}
func (obj *CreditCardOffersScreenOptionsV2) BroadVisualElementImgUrlIOS(ctx context.Context) string {
	defVal := obj.broadVisualElementImgUrlIOS()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "BroadVisualElementImgUrlIOS"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

// These are deprecated, we are still having them to support backward compatibility for older screens
func (obj *CreditCardOffersScreenOptionsV2) cta1Name() string {
	obj._Cta1NameMutex.RLock()
	defer obj._Cta1NameMutex.RUnlock()
	return obj._Cta1Name
}

// These are deprecated, we are still having them to support backward compatibility for older screens
func (obj *CreditCardOffersScreenOptionsV2) Cta1Name(ctx context.Context) string {
	defVal := obj.cta1Name()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Cta1Name"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) cta2Name() string {
	obj._Cta2NameMutex.RLock()
	defer obj._Cta2NameMutex.RUnlock()
	return obj._Cta2Name
}
func (obj *CreditCardOffersScreenOptionsV2) Cta2Name(ctx context.Context) string {
	defVal := obj.cta2Name()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Cta2Name"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) cta3Name() string {
	obj._Cta3NameMutex.RLock()
	defer obj._Cta3NameMutex.RUnlock()
	return obj._Cta3Name
}
func (obj *CreditCardOffersScreenOptionsV2) Cta3Name(ctx context.Context) string {
	defVal := obj.cta3Name()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Cta3Name"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CreditCardOffersScreenOptionsV2) StaticImages() *InfoItem {
	return obj._StaticImages
}
func (obj *CreditCardOffersScreenOptionsV2) FeesInfo() *FeesInfoItem {
	return obj._FeesInfo
}
func (obj *CreditCardOffersScreenOptionsV2) AllFeesCta() *AllFeesCta {
	return obj._AllFeesCta
}
func (obj *CreditCardOffersScreenOptionsV2) RewardsWorthCtaInfo() *RewardsWorthCtaInfo {
	return obj._RewardsWorthCtaInfo
}
func (obj *CreditCardOffersScreenOptionsV2) RewardsWorthInfo() *RewardsWorthInfo {
	return obj._RewardsWorthInfo
}
func (obj *CreditCardOffersScreenOptionsV2) FeesInfoV2() *FessInfoV2 {
	return obj._FeesInfoV2
}
func (obj *CreditCardOffersScreenOptionsV2) JoiningFeeVoucherInfo() *JoiningFeeVoucherInfo {
	return obj._JoiningFeeVoucherInfo
}
func (obj *CreditCardOffersScreenOptionsV2) AcceleratedRewardsInfo() *AcceleratedRewardsInfo {
	return obj._AcceleratedRewardsInfo
}
func (obj *CreditCardOffersScreenOptionsV2) WelcomeVoucherCta() *CtaBlockInfoV2 {
	return obj._WelcomeVoucherCta
}
func (obj *CreditCardOffersScreenOptionsV2) TopBrandsCta() *CtaBlockInfoV2 {
	return obj._TopBrandsCta
}
func (obj *CreditCardOffersScreenOptionsV2) RewardEstimationCta() *CtaBlockInfoV2 {
	return obj._RewardEstimationCta
}

type RewardsWorthInfo struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_Title         string
	_TitleMutex    *sync.RWMutex
	_SubTitle      string
	_SubTitleMutex *sync.RWMutex
	_Desc          string
	_DescMutex     *sync.RWMutex
}

func (obj *RewardsWorthInfo) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *RewardsWorthInfo) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *RewardsWorthInfo) subTitle() string {
	obj._SubTitleMutex.RLock()
	defer obj._SubTitleMutex.RUnlock()
	return obj._SubTitle
}
func (obj *RewardsWorthInfo) SubTitle(ctx context.Context) string {
	defVal := obj.subTitle()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "SubTitle"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *RewardsWorthInfo) desc() string {
	obj._DescMutex.RLock()
	defer obj._DescMutex.RUnlock()
	return obj._Desc
}
func (obj *RewardsWorthInfo) Desc(ctx context.Context) string {
	defVal := obj.desc()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type FessInfoV2 struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_Title         string
	_TitleMutex    *sync.RWMutex
	_SubTitle      string
	_SubTitleMutex *sync.RWMutex
	_Desc          string
	_DescMutex     *sync.RWMutex
}

func (obj *FessInfoV2) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *FessInfoV2) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FessInfoV2) subTitle() string {
	obj._SubTitleMutex.RLock()
	defer obj._SubTitleMutex.RUnlock()
	return obj._SubTitle
}
func (obj *FessInfoV2) SubTitle(ctx context.Context) string {
	defVal := obj.subTitle()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "SubTitle"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *FessInfoV2) desc() string {
	obj._DescMutex.RLock()
	defer obj._DescMutex.RUnlock()
	return obj._Desc
}
func (obj *FessInfoV2) Desc(ctx context.Context) string {
	defVal := obj.desc()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Desc"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type CtaBlockInfoV2 struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_IconLink      string
	_IconLinkMutex *sync.RWMutex
	_Title         string
	_TitleMutex    *sync.RWMutex
	_CtaText       string
	_CtaTextMutex  *sync.RWMutex
}

func (obj *CtaBlockInfoV2) iconLink() string {
	obj._IconLinkMutex.RLock()
	defer obj._IconLinkMutex.RUnlock()
	return obj._IconLink
}
func (obj *CtaBlockInfoV2) IconLink(ctx context.Context) string {
	defVal := obj.iconLink()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "IconLink"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaBlockInfoV2) title() string {
	obj._TitleMutex.RLock()
	defer obj._TitleMutex.RUnlock()
	return obj._Title
}
func (obj *CtaBlockInfoV2) Title(ctx context.Context) string {
	defVal := obj.title()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Title"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

func (obj *CtaBlockInfoV2) ctaText() string {
	obj._CtaTextMutex.RLock()
	defer obj._CtaTextMutex.RUnlock()
	return obj._CtaText
}
func (obj *CtaBlockInfoV2) CtaText(ctx context.Context) string {
	defVal := obj.ctaText()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "CtaText"}, defVal)
	val, ok := res.(string)
	if ok {
		return val
	}
	return defVal
}

type DisabledOnboardingScreenOptions struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Text         string
	_TextMutex    *sync.RWMutex
	_SubText      string
	_SubTextMutex *sync.RWMutex
	_Icon         string
	_IconMutex    *sync.RWMutex
}

func (obj *DisabledOnboardingScreenOptions) Text() string {
	obj._TextMutex.RLock()
	defer obj._TextMutex.RUnlock()
	return obj._Text
}
func (obj *DisabledOnboardingScreenOptions) SubText() string {
	obj._SubTextMutex.RLock()
	defer obj._SubTextMutex.RUnlock()
	return obj._SubText
}
func (obj *DisabledOnboardingScreenOptions) Icon() string {
	obj._IconMutex.RLock()
	defer obj._IconMutex.RUnlock()
	return obj._Icon
}

type UnsecuredCCRenewalFeeReversalConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IneligibleUsersSegmentId      string
	_IneligibleUsersSegmentIdMutex *sync.RWMutex
}

func (obj *UnsecuredCCRenewalFeeReversalConfig) IneligibleUsersSegmentId() string {
	obj._IneligibleUsersSegmentIdMutex.RLock()
	defer obj._IneligibleUsersSegmentIdMutex.RUnlock()
	return obj._IneligibleUsersSegmentId
}

type FireflyV2Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Cool-off applicable before user can re-initiate onboarding in case last attempt resulted in failure
	_OnboardingRetryCoolOff    int64
	_CcIneligibleSegments      roarray.ROArray[string]
	_CcIneligibleSegmentsMutex *sync.RWMutex
	_IntroScreenV2Config       *IntroScreenV2Config
}

// Cool-off applicable before user can re-initiate onboarding in case last attempt resulted in failure
func (obj *FireflyV2Config) OnboardingRetryCoolOff() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._OnboardingRetryCoolOff))
}
func (obj *FireflyV2Config) CcIneligibleSegments() roarray.ROArray[string] {
	obj._CcIneligibleSegmentsMutex.RLock()
	defer obj._CcIneligibleSegmentsMutex.RUnlock()
	return obj._CcIneligibleSegments
}
func (obj *FireflyV2Config) IntroScreenV2Config() *IntroScreenV2Config {
	return obj._IntroScreenV2Config
}

type IntroScreenV2Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IntroScreenV2LoaderAnimationHeight int32
	_IntroScreenV2LoaderAnimationWidth  int32
	_IntroScreenV2BgImageHeight         int32
	_IntroScreenV2BgImageWidth          int32
	_IntroScreenV2LoaderAnimation       string
	_IntroScreenV2LoaderAnimationMutex  *sync.RWMutex
	_IntroScreenV2BgImage               string
	_IntroScreenV2BgImageMutex          *sync.RWMutex
}

func (obj *IntroScreenV2Config) IntroScreenV2LoaderAnimationHeight() int32 {
	return int32(atomic.LoadInt32(&obj._IntroScreenV2LoaderAnimationHeight))
}
func (obj *IntroScreenV2Config) IntroScreenV2LoaderAnimationWidth() int32 {
	return int32(atomic.LoadInt32(&obj._IntroScreenV2LoaderAnimationWidth))
}
func (obj *IntroScreenV2Config) IntroScreenV2BgImageHeight() int32 {
	return int32(atomic.LoadInt32(&obj._IntroScreenV2BgImageHeight))
}
func (obj *IntroScreenV2Config) IntroScreenV2BgImageWidth() int32 {
	return int32(atomic.LoadInt32(&obj._IntroScreenV2BgImageWidth))
}
func (obj *IntroScreenV2Config) IntroScreenV2LoaderAnimation() string {
	obj._IntroScreenV2LoaderAnimationMutex.RLock()
	defer obj._IntroScreenV2LoaderAnimationMutex.RUnlock()
	return obj._IntroScreenV2LoaderAnimation
}
func (obj *IntroScreenV2Config) IntroScreenV2BgImage() string {
	obj._IntroScreenV2BgImageMutex.RLock()
	defer obj._IntroScreenV2BgImageMutex.RUnlock()
	return obj._IntroScreenV2BgImage
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["usepgdbconnforccdb"] = _obj.SetUsePgdbConnForCcDb
	_setters["disablehorizontallayoutbyquestengine"] = _obj.SetDisableHorizontalLayoutByQuestEngine
	_setters["enablenewcvpforunsecuredcreditcard"] = _obj.SetEnableNewCvpForUnsecuredCreditCard
	_setters["disablecreditcardonboarding"] = _obj.SetDisableCreditCardOnboarding
	_setters["skipeligibilitycheckfordisabledcconboarding"] = _obj.SetSkipEligibilityCheckForDisabledCCOnboarding
	_setters["enablemassunsecuredonboardingv2"] = _obj.SetEnableMassUnsecuredOnboardingV2
	_setters["enableunsecuredonboardingv2"] = _obj.SetEnableUnsecuredOnboardingV2
	_setters["enableunsecuredconsentflowonboarding"] = _obj.SetEnableUnsecuredConsentFlowOnboarding
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_CCTransactionNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCTransactionNotificationSubscriber = _CCTransactionNotificationSubscriber
	helper.AddFieldSetters("cctransactionnotificationsubscriber", _fieldSetters, _setters)
	_CCAcsNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCAcsNotificationSubscriber = _CCAcsNotificationSubscriber
	helper.AddFieldSetters("ccacsnotificationsubscriber", _fieldSetters, _setters)
	_CCStatementNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCStatementNotificationSubscriber = _CCStatementNotificationSubscriber
	helper.AddFieldSetters("ccstatementnotificationsubscriber", _fieldSetters, _setters)
	_CardsSentForPrintingCsvFileSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CardsSentForPrintingCsvFileSubscriber = _CardsSentForPrintingCsvFileSubscriber
	helper.AddFieldSetters("cardssentforprintingcsvfilesubscriber", _fieldSetters, _setters)
	_CardsDispatchedCsvFileSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CardsDispatchedCsvFileSubscriber = _CardsDispatchedCsvFileSubscriber
	helper.AddFieldSetters("cardsdispatchedcsvfilesubscriber", _fieldSetters, _setters)
	_CCTransactionsForPinotSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCTransactionsForPinotSubscriber = _CCTransactionsForPinotSubscriber
	helper.AddFieldSetters("cctransactionsforpinotsubscriber", _fieldSetters, _setters)
	_CategorizerUpdateForPinotSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CategorizerUpdateForPinotSubscriber = _CategorizerUpdateForPinotSubscriber
	helper.AddFieldSetters("categorizerupdateforpinotsubscriber", _fieldSetters, _setters)
	_CCAuthFactorUpdateNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCAuthFactorUpdateNotificationSubscriber = _CCAuthFactorUpdateNotificationSubscriber
	helper.AddFieldSetters("ccauthfactorupdatenotificationsubscriber", _fieldSetters, _setters)
	_CCCreditReportDownloadEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCCreditReportDownloadEventSubscriber = _CCCreditReportDownloadEventSubscriber
	helper.AddFieldSetters("cccreditreportdownloadeventsubscriber", _fieldSetters, _setters)
	_ProcessCreditCardOfferFileSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessCreditCardOfferFileSubscriber = _ProcessCreditCardOfferFileSubscriber
	helper.AddFieldSetters("processcreditcardofferfilesubscriber", _fieldSetters, _setters)
	_CCNonFinancialNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCNonFinancialNotificationSubscriber = _CCNonFinancialNotificationSubscriber
	helper.AddFieldSetters("ccnonfinancialnotificationsubscriber", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_CreditCardOffersScreenOptions, _fieldSetters := NewCreditCardOffersScreenOptions()
	_obj._CreditCardOffersScreenOptions = _CreditCardOffersScreenOptions
	helper.AddFieldSetters("creditcardoffersscreenoptions", _fieldSetters, _setters)
	_CreditCardOffersScreenOptionsV2, _fieldSetters := NewCreditCardOffersScreenOptionsV2()
	_obj._CreditCardOffersScreenOptionsV2 = _CreditCardOffersScreenOptionsV2
	helper.AddFieldSetters("creditcardoffersscreenoptionsv2", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig2.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_RateLimiterConfig, _fieldSetters := gencfg.NewRateLimitConfig()
	_obj._RateLimiterConfig = _RateLimiterConfig
	helper.AddFieldSetters("ratelimiterconfig", _fieldSetters, _setters)
	_DisabledOnboardingScreenOptions, _fieldSetters := NewDisabledOnboardingScreenOptions()
	_obj._DisabledOnboardingScreenOptions = _DisabledOnboardingScreenOptions
	helper.AddFieldSetters("disabledonboardingscreenoptions", _fieldSetters, _setters)
	_UnsecuredCCRenewalFeeReversalConfig, _fieldSetters := NewUnsecuredCCRenewalFeeReversalConfig()
	_obj._UnsecuredCCRenewalFeeReversalConfig = _UnsecuredCCRenewalFeeReversalConfig
	helper.AddFieldSetters("unsecuredccrenewalfeereversalconfig", _fieldSetters, _setters)
	_CCOnboardingStateUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCOnboardingStateUpdateEventSqsSubscriber = _CCOnboardingStateUpdateEventSqsSubscriber
	helper.AddFieldSetters("cconboardingstateupdateeventsqssubscriber", _fieldSetters, _setters)
	_FireflyV2Config, _fieldSetters := NewFireflyV2Config()
	_obj._FireflyV2Config = _FireflyV2Config
	helper.AddFieldSetters("fireflyv2config", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["usepgdbconnforccdb"] = _obj.SetUsePgdbConnForCcDb
	_setters["disablehorizontallayoutbyquestengine"] = _obj.SetDisableHorizontalLayoutByQuestEngine
	_setters["enablenewcvpforunsecuredcreditcard"] = _obj.SetEnableNewCvpForUnsecuredCreditCard
	_setters["disablecreditcardonboarding"] = _obj.SetDisableCreditCardOnboarding
	_setters["skipeligibilitycheckfordisabledcconboarding"] = _obj.SetSkipEligibilityCheckForDisabledCCOnboarding
	_setters["enablemassunsecuredonboardingv2"] = _obj.SetEnableMassUnsecuredOnboardingV2
	_setters["enableunsecuredonboardingv2"] = _obj.SetEnableUnsecuredOnboardingV2
	_setters["enableunsecuredconsentflowonboarding"] = _obj.SetEnableUnsecuredConsentFlowOnboarding
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_CCTransactionNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCTransactionNotificationSubscriber = _CCTransactionNotificationSubscriber
	helper.AddFieldSetters("cctransactionnotificationsubscriber", _fieldSetters, _setters)
	_CCAcsNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCAcsNotificationSubscriber = _CCAcsNotificationSubscriber
	helper.AddFieldSetters("ccacsnotificationsubscriber", _fieldSetters, _setters)
	_CCStatementNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCStatementNotificationSubscriber = _CCStatementNotificationSubscriber
	helper.AddFieldSetters("ccstatementnotificationsubscriber", _fieldSetters, _setters)
	_CardsSentForPrintingCsvFileSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CardsSentForPrintingCsvFileSubscriber = _CardsSentForPrintingCsvFileSubscriber
	helper.AddFieldSetters("cardssentforprintingcsvfilesubscriber", _fieldSetters, _setters)
	_CardsDispatchedCsvFileSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CardsDispatchedCsvFileSubscriber = _CardsDispatchedCsvFileSubscriber
	helper.AddFieldSetters("cardsdispatchedcsvfilesubscriber", _fieldSetters, _setters)
	_CCTransactionsForPinotSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCTransactionsForPinotSubscriber = _CCTransactionsForPinotSubscriber
	helper.AddFieldSetters("cctransactionsforpinotsubscriber", _fieldSetters, _setters)
	_CategorizerUpdateForPinotSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CategorizerUpdateForPinotSubscriber = _CategorizerUpdateForPinotSubscriber
	helper.AddFieldSetters("categorizerupdateforpinotsubscriber", _fieldSetters, _setters)
	_CCAuthFactorUpdateNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCAuthFactorUpdateNotificationSubscriber = _CCAuthFactorUpdateNotificationSubscriber
	helper.AddFieldSetters("ccauthfactorupdatenotificationsubscriber", _fieldSetters, _setters)
	_CCCreditReportDownloadEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCCreditReportDownloadEventSubscriber = _CCCreditReportDownloadEventSubscriber
	helper.AddFieldSetters("cccreditreportdownloadeventsubscriber", _fieldSetters, _setters)
	_ProcessCreditCardOfferFileSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessCreditCardOfferFileSubscriber = _ProcessCreditCardOfferFileSubscriber
	helper.AddFieldSetters("processcreditcardofferfilesubscriber", _fieldSetters, _setters)
	_CCNonFinancialNotificationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCNonFinancialNotificationSubscriber = _CCNonFinancialNotificationSubscriber
	helper.AddFieldSetters("ccnonfinancialnotificationsubscriber", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	_CreditCardOffersScreenOptions, _fieldSetters := NewCreditCardOffersScreenOptionsWithQuest(questFieldPath + "/" + "CreditCardOffersScreenOptions")
	_obj._CreditCardOffersScreenOptions = _CreditCardOffersScreenOptions
	helper.AddFieldSetters("creditcardoffersscreenoptions", _fieldSetters, _setters)
	_CreditCardOffersScreenOptionsV2, _fieldSetters := NewCreditCardOffersScreenOptionsV2WithQuest(questFieldPath + "/" + "CreditCardOffersScreenOptionsV2")
	_obj._CreditCardOffersScreenOptionsV2 = _CreditCardOffersScreenOptionsV2
	helper.AddFieldSetters("creditcardoffersscreenoptionsv2", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig2.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_RateLimiterConfig, _fieldSetters := gencfg.NewRateLimitConfig()
	_obj._RateLimiterConfig = _RateLimiterConfig
	helper.AddFieldSetters("ratelimiterconfig", _fieldSetters, _setters)
	_DisabledOnboardingScreenOptions, _fieldSetters := NewDisabledOnboardingScreenOptions()
	_obj._DisabledOnboardingScreenOptions = _DisabledOnboardingScreenOptions
	helper.AddFieldSetters("disabledonboardingscreenoptions", _fieldSetters, _setters)
	_UnsecuredCCRenewalFeeReversalConfig, _fieldSetters := NewUnsecuredCCRenewalFeeReversalConfig()
	_obj._UnsecuredCCRenewalFeeReversalConfig = _UnsecuredCCRenewalFeeReversalConfig
	helper.AddFieldSetters("unsecuredccrenewalfeereversalconfig", _fieldSetters, _setters)
	_CCOnboardingStateUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CCOnboardingStateUpdateEventSqsSubscriber = _CCOnboardingStateUpdateEventSqsSubscriber
	helper.AddFieldSetters("cconboardingstateupdateeventsqssubscriber", _fieldSetters, _setters)
	_FireflyV2Config, _fieldSetters := NewFireflyV2Config()
	_obj._FireflyV2Config = _FireflyV2Config
	helper.AddFieldSetters("fireflyv2config", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *Config) Init(questFieldPath string) {
	newObj, _ := NewConfig()
	*obj = *newObj
}
func (obj *Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._CreditCardOffersScreenOptions.SetQuestSDK(questSdk)
	obj._CreditCardOffersScreenOptionsV2.SetQuestSDK(questSdk)
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DisableHorizontalLayoutByQuestEngine",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "CreditCard", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "EnableMassUnsecuredOnboardingV2",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "CreditCard", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "EnableUnsecuredOnboardingV2",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "CreditCard", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "EnableUnsecuredConsentFlowOnboarding",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "CreditCard", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._CreditCardOffersScreenOptions.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "CreditCard" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	childVars, childVarsErr = obj._CreditCardOffersScreenOptionsV2.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "CreditCard" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "usepgdbconnforccdb":
		return obj.SetUsePgdbConnForCcDb(v.UsePgdbConnForCcDb, true, nil)
	case "disablehorizontallayoutbyquestengine":
		return obj.SetDisableHorizontalLayoutByQuestEngine(v.DisableHorizontalLayoutByQuestEngine, true, nil)
	case "enablenewcvpforunsecuredcreditcard":
		return obj.SetEnableNewCvpForUnsecuredCreditCard(v.EnableNewCvpForUnsecuredCreditCard, true, nil)
	case "disablecreditcardonboarding":
		return obj.SetDisableCreditCardOnboarding(v.DisableCreditCardOnboarding, true, nil)
	case "skipeligibilitycheckfordisabledcconboarding":
		return obj.SetSkipEligibilityCheckForDisabledCCOnboarding(v.SkipEligibilityCheckForDisabledCCOnboarding, true, nil)
	case "enablemassunsecuredonboardingv2":
		return obj.SetEnableMassUnsecuredOnboardingV2(v.EnableMassUnsecuredOnboardingV2, true, nil)
	case "enableunsecuredonboardingv2":
		return obj.SetEnableUnsecuredOnboardingV2(v.EnableUnsecuredOnboardingV2, true, nil)
	case "enableunsecuredconsentflowonboarding":
		return obj.SetEnableUnsecuredConsentFlowOnboarding(v.EnableUnsecuredConsentFlowOnboarding, true, nil)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "cctransactionnotificationsubscriber":
		return obj._CCTransactionNotificationSubscriber.Set(v.CCTransactionNotificationSubscriber, true, path)
	case "ccacsnotificationsubscriber":
		return obj._CCAcsNotificationSubscriber.Set(v.CCAcsNotificationSubscriber, true, path)
	case "ccstatementnotificationsubscriber":
		return obj._CCStatementNotificationSubscriber.Set(v.CCStatementNotificationSubscriber, true, path)
	case "cardssentforprintingcsvfilesubscriber":
		return obj._CardsSentForPrintingCsvFileSubscriber.Set(v.CardsSentForPrintingCsvFileSubscriber, true, path)
	case "cardsdispatchedcsvfilesubscriber":
		return obj._CardsDispatchedCsvFileSubscriber.Set(v.CardsDispatchedCsvFileSubscriber, true, path)
	case "cctransactionsforpinotsubscriber":
		return obj._CCTransactionsForPinotSubscriber.Set(v.CCTransactionsForPinotSubscriber, true, path)
	case "categorizerupdateforpinotsubscriber":
		return obj._CategorizerUpdateForPinotSubscriber.Set(v.CategorizerUpdateForPinotSubscriber, true, path)
	case "ccauthfactorupdatenotificationsubscriber":
		return obj._CCAuthFactorUpdateNotificationSubscriber.Set(v.CCAuthFactorUpdateNotificationSubscriber, true, path)
	case "cccreditreportdownloadeventsubscriber":
		return obj._CCCreditReportDownloadEventSubscriber.Set(v.CCCreditReportDownloadEventSubscriber, true, path)
	case "processcreditcardofferfilesubscriber":
		return obj._ProcessCreditCardOfferFileSubscriber.Set(v.ProcessCreditCardOfferFileSubscriber, true, path)
	case "ccnonfinancialnotificationsubscriber":
		return obj._CCNonFinancialNotificationSubscriber.Set(v.CCNonFinancialNotificationSubscriber, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	case "creditcardoffersscreenoptions":
		return obj._CreditCardOffersScreenOptions.Set(v.CreditCardOffersScreenOptions, true, path)
	case "creditcardoffersscreenoptionsv2":
		return obj._CreditCardOffersScreenOptionsV2.Set(v.CreditCardOffersScreenOptionsV2, true, path)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "ratelimiterconfig":
		return obj._RateLimiterConfig.Set(v.RateLimiterConfig, true, path)
	case "disabledonboardingscreenoptions":
		return obj._DisabledOnboardingScreenOptions.Set(v.DisabledOnboardingScreenOptions, true, path)
	case "unsecuredccrenewalfeereversalconfig":
		return obj._UnsecuredCCRenewalFeeReversalConfig.Set(v.UnsecuredCCRenewalFeeReversalConfig, true, path)
	case "cconboardingstateupdateeventsqssubscriber":
		return obj._CCOnboardingStateUpdateEventSqsSubscriber.Set(v.CCOnboardingStateUpdateEventSqsSubscriber, true, path)
	case "fireflyv2config":
		return obj._FireflyV2Config.Set(v.FireflyV2Config, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetUsePgdbConnForCcDb(v.UsePgdbConnForCcDb, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableHorizontalLayoutByQuestEngine(v.DisableHorizontalLayoutByQuestEngine, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableNewCvpForUnsecuredCreditCard(v.EnableNewCvpForUnsecuredCreditCard, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableCreditCardOnboarding(v.DisableCreditCardOnboarding, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipEligibilityCheckForDisabledCCOnboarding(v.SkipEligibilityCheckForDisabledCCOnboarding, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableMassUnsecuredOnboardingV2(v.EnableMassUnsecuredOnboardingV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableUnsecuredOnboardingV2(v.EnableUnsecuredOnboardingV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableUnsecuredConsentFlowOnboarding(v.EnableUnsecuredConsentFlowOnboarding, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CCTransactionNotificationSubscriber.Set(v.CCTransactionNotificationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CCAcsNotificationSubscriber.Set(v.CCAcsNotificationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CCStatementNotificationSubscriber.Set(v.CCStatementNotificationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CardsSentForPrintingCsvFileSubscriber.Set(v.CardsSentForPrintingCsvFileSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CardsDispatchedCsvFileSubscriber.Set(v.CardsDispatchedCsvFileSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CCTransactionsForPinotSubscriber.Set(v.CCTransactionsForPinotSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CategorizerUpdateForPinotSubscriber.Set(v.CategorizerUpdateForPinotSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CCAuthFactorUpdateNotificationSubscriber.Set(v.CCAuthFactorUpdateNotificationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CCCreditReportDownloadEventSubscriber.Set(v.CCCreditReportDownloadEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessCreditCardOfferFileSubscriber.Set(v.ProcessCreditCardOfferFileSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CCNonFinancialNotificationSubscriber.Set(v.CCNonFinancialNotificationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditCardOffersScreenOptions.Set(v.CreditCardOffersScreenOptions, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditCardOffersScreenOptionsV2.Set(v.CreditCardOffersScreenOptionsV2, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RateLimiterConfig.Set(v.RateLimiterConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DisabledOnboardingScreenOptions.Set(v.DisabledOnboardingScreenOptions, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UnsecuredCCRenewalFeeReversalConfig.Set(v.UnsecuredCCRenewalFeeReversalConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CCOnboardingStateUpdateEventSqsSubscriber.Set(v.CCOnboardingStateUpdateEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FireflyV2Config.Set(v.FireflyV2Config, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._SecureLogging = v.SecureLogging
	obj._EpifiDb = v.EpifiDb
	obj._CreditCardDb = v.CreditCardDb
	obj._PinotConfig = v.PinotConfig
	obj._AWS = v.AWS
	obj._RudderStack = v.RudderStack
	obj._Secrets = v.Secrets
	obj._Tracing = v.Tracing
	obj._SignalWorkflowPublisher = v.SignalWorkflowPublisher
	obj._Vendor = v.Vendor
	obj._RedisCachePrefix = v.RedisCachePrefix
	obj._CreditCardUrl = v.CreditCardUrl
	obj._CreditCardBillPaymentInfo = v.CreditCardBillPaymentInfo
	obj._CCTransactionEventPublisher = v.CCTransactionEventPublisher
	obj._TransactionsProducer = v.TransactionsProducer
	obj._CardsSentForPrintingBucketName = v.CardsSentForPrintingBucketName
	obj._CardsDispatchedBucketName = v.CardsDispatchedBucketName
	obj._AcsBucketName = v.AcsBucketName
	obj._AcsNotificationRawDataStore = v.AcsNotificationRawDataStore
	obj._EnableViewCardDetailsViaVgPciServer = v.EnableViewCardDetailsViaVgPciServer
	obj._DepositConfigMap = v.DepositConfigMap
	obj._QuestRedisOptions = v.QuestRedisOptions
	obj._FireflyRedisStore = v.FireflyRedisStore
	obj._EnableCcOffersScreenOlderStringAlignment = v.EnableCcOffersScreenOlderStringAlignment
	obj._EnableCreditReportDownloadConsumer = v.EnableCreditReportDownloadConsumer
	obj._SkipWorkflowInitiationForViewCardDetails = v.SkipWorkflowInitiationForViewCardDetails
	obj._EnableExpiryFromTokenizer = v.EnableExpiryFromTokenizer
	obj._DbConnectionAliases = v.DbConnectionAliases
	obj._CreditCardPgDb = v.CreditCardPgDb
	obj._CardRecommendationConfig = v.CardRecommendationConfig
	obj._CardProgramToSegmentIdMap = v.CardProgramToSegmentIdMap
	obj._SimplifiConfig = v.SimplifiConfig
	obj._CreditCardCacheConfig = v.CreditCardCacheConfig
	obj._CardProgramToBeneficiaryDetails = v.CardProgramToBeneficiaryDetails
	obj._DpdThirtySegmentId = v.DpdThirtySegmentId
	obj._CreditCardFederalPgDb = v.CreditCardFederalPgDb
	obj._CcOnboardingStateUpdateEventPublisher = v.CcOnboardingStateUpdateEventPublisher
	return nil
}

func (obj *Config) SetUsePgdbConnForCcDb(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.UsePgdbConnForCcDb", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UsePgdbConnForCcDb, 1)
	} else {
		atomic.StoreUint32(&obj._UsePgdbConnForCcDb, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UsePgdbConnForCcDb")
	}
	return nil
}
func (obj *Config) SetDisableHorizontalLayoutByQuestEngine(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DisableHorizontalLayoutByQuestEngine", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableHorizontalLayoutByQuestEngine, 1)
	} else {
		atomic.StoreUint32(&obj._DisableHorizontalLayoutByQuestEngine, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableHorizontalLayoutByQuestEngine")
	}
	return nil
}
func (obj *Config) SetEnableNewCvpForUnsecuredCreditCard(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableNewCvpForUnsecuredCreditCard", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableNewCvpForUnsecuredCreditCard, 1)
	} else {
		atomic.StoreUint32(&obj._EnableNewCvpForUnsecuredCreditCard, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableNewCvpForUnsecuredCreditCard")
	}
	return nil
}
func (obj *Config) SetDisableCreditCardOnboarding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DisableCreditCardOnboarding", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableCreditCardOnboarding, 1)
	} else {
		atomic.StoreUint32(&obj._DisableCreditCardOnboarding, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableCreditCardOnboarding")
	}
	return nil
}
func (obj *Config) SetSkipEligibilityCheckForDisabledCCOnboarding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.SkipEligibilityCheckForDisabledCCOnboarding", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipEligibilityCheckForDisabledCCOnboarding, 1)
	} else {
		atomic.StoreUint32(&obj._SkipEligibilityCheckForDisabledCCOnboarding, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipEligibilityCheckForDisabledCCOnboarding")
	}
	return nil
}
func (obj *Config) SetEnableMassUnsecuredOnboardingV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableMassUnsecuredOnboardingV2", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableMassUnsecuredOnboardingV2, 1)
	} else {
		atomic.StoreUint32(&obj._EnableMassUnsecuredOnboardingV2, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableMassUnsecuredOnboardingV2")
	}
	return nil
}
func (obj *Config) SetEnableUnsecuredOnboardingV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableUnsecuredOnboardingV2", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableUnsecuredOnboardingV2, 1)
	} else {
		atomic.StoreUint32(&obj._EnableUnsecuredOnboardingV2, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableUnsecuredOnboardingV2")
	}
	return nil
}
func (obj *Config) SetEnableUnsecuredConsentFlowOnboarding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableUnsecuredConsentFlowOnboarding", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableUnsecuredConsentFlowOnboarding, 1)
	} else {
		atomic.StoreUint32(&obj._EnableUnsecuredConsentFlowOnboarding, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableUnsecuredConsentFlowOnboarding")
	}
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["trimdebugmessagefromstatus"] = _obj.SetTrimDebugMessageFromStatus
	_setters["enableemiconversion"] = _obj.SetEnableEmiConversion
	_setters["enablereissuecardratelimiter"] = _obj.SetEnableReissueCardRateLimiter
	_MinVersionForCCOnboarding, _fieldSetters := gencfg.NewPlatformVersionCheck()
	_obj._MinVersionForCCOnboarding = _MinVersionForCCOnboarding
	helper.AddFieldSetters("minversionforcconboarding", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "trimdebugmessagefromstatus":
		return obj.SetTrimDebugMessageFromStatus(v.TrimDebugMessageFromStatus, true, nil)
	case "enableemiconversion":
		return obj.SetEnableEmiConversion(v.EnableEmiConversion, true, nil)
	case "enablereissuecardratelimiter":
		return obj.SetEnableReissueCardRateLimiter(v.EnableReissueCardRateLimiter, true, nil)
	case "minversionforcconboarding":
		return obj._MinVersionForCCOnboarding.Set(v.MinVersionForCCOnboarding, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj.SetTrimDebugMessageFromStatus(v.TrimDebugMessageFromStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableEmiConversion(v.EnableEmiConversion, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableReissueCardRateLimiter(v.EnableReissueCardRateLimiter, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._MinVersionForCCOnboarding.Set(v.MinVersionForCCOnboarding, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	return nil
}

func (obj *Flags) SetTrimDebugMessageFromStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.TrimDebugMessageFromStatus", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._TrimDebugMessageFromStatus, 1)
	} else {
		atomic.StoreUint32(&obj._TrimDebugMessageFromStatus, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "TrimDebugMessageFromStatus")
	}
	return nil
}
func (obj *Flags) SetEnableEmiConversion(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableEmiConversion", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableEmiConversion, 1)
	} else {
		atomic.StoreUint32(&obj._EnableEmiConversion, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableEmiConversion")
	}
	return nil
}
func (obj *Flags) SetEnableReissueCardRateLimiter(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableReissueCardRateLimiter", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableReissueCardRateLimiter, 1)
	} else {
		atomic.StoreUint32(&obj._EnableReissueCardRateLimiter, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableReissueCardRateLimiter")
	}
	return nil
}

func NewCreditCardOffersScreenOptions() (_obj *CreditCardOffersScreenOptions, _setters map[string]dynconf.SetFunc) {
	_obj = &CreditCardOffersScreenOptions{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["creditlimitamountstring"] = _obj.SetCreditLimitAmountString
	_obj._CreditLimitAmountStringMutex = &sync.RWMutex{}
	_setters["cardimageurl"] = _obj.SetCardImageUrl
	_obj._CardImageUrlMutex = &sync.RWMutex{}
	_setters["termsandconditions"] = _obj.SetTermsAndConditions
	_obj._TermsAndConditionsMutex = &sync.RWMutex{}
	_setters["partnershipurl"] = _obj.SetPartnershipUrl
	_obj._PartnershipUrlMutex = &sync.RWMutex{}
	_setters["getcreditcardctatext"] = _obj.SetGetCreditCardCtaText
	_obj._GetCreditCardCtaTextMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["headerdescription"] = _obj.SetHeaderDescription
	_obj._HeaderDescriptionMutex = &sync.RWMutex{}
	_setters["feesinfoheader"] = _obj.SetFeesInfoHeader
	_obj._FeesInfoHeaderMutex = &sync.RWMutex{}
	_setters["broadvisualelementimgurl"] = _obj.SetBroadVisualElementImgUrl
	_obj._BroadVisualElementImgUrlMutex = &sync.RWMutex{}
	_InfoBlock, _fieldSetters := NewInfoItem()
	_obj._InfoBlock = _InfoBlock
	helper.AddFieldSetters("infoblock", _fieldSetters, _setters)
	_PopUpTextBlock, _fieldSetters := NewPopUpTextBlock()
	_obj._PopUpTextBlock = _PopUpTextBlock
	helper.AddFieldSetters("popuptextblock", _fieldSetters, _setters)
	_StaticImages, _fieldSetters := NewInfoItem()
	_obj._StaticImages = _StaticImages
	helper.AddFieldSetters("staticimages", _fieldSetters, _setters)
	_FeesInfo, _fieldSetters := NewFeesInfoItem()
	_obj._FeesInfo = _FeesInfo
	helper.AddFieldSetters("feesinfo", _fieldSetters, _setters)
	_AllFeesCta, _fieldSetters := NewAllFeesCta()
	_obj._AllFeesCta = _AllFeesCta
	helper.AddFieldSetters("allfeescta", _fieldSetters, _setters)
	_RewardsWorthCtaInfo, _fieldSetters := NewRewardsWorthCtaInfo()
	_obj._RewardsWorthCtaInfo = _RewardsWorthCtaInfo
	helper.AddFieldSetters("rewardsworthctainfo", _fieldSetters, _setters)
	_JoiningFeeVoucherInfo, _fieldSetters := NewJoiningFeeVoucherInfo()
	_obj._JoiningFeeVoucherInfo = _JoiningFeeVoucherInfo
	helper.AddFieldSetters("joiningfeevoucherinfo", _fieldSetters, _setters)
	_AcceleratedRewardsInfo, _fieldSetters := NewAcceleratedRewardsInfo()
	_obj._AcceleratedRewardsInfo = _AcceleratedRewardsInfo
	helper.AddFieldSetters("acceleratedrewardsinfo", _fieldSetters, _setters)
	_FullScreenBlock, _fieldSetters := NewCtaBlockInfo()
	_obj._FullScreenBlock = _FullScreenBlock
	helper.AddFieldSetters("fullscreenblock", _fieldSetters, _setters)
	_LeftHalfBlock, _fieldSetters := NewCtaBlockInfo()
	_obj._LeftHalfBlock = _LeftHalfBlock
	helper.AddFieldSetters("lefthalfblock", _fieldSetters, _setters)
	_RightHalfBlock, _fieldSetters := NewCtaBlockInfo()
	_obj._RightHalfBlock = _RightHalfBlock
	helper.AddFieldSetters("righthalfblock", _fieldSetters, _setters)
	return _obj, _setters
}

func NewCreditCardOffersScreenOptionsWithQuest(questFieldPath string) (_obj *CreditCardOffersScreenOptions, _setters map[string]dynconf.SetFunc) {
	_obj = &CreditCardOffersScreenOptions{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["creditlimitamountstring"] = _obj.SetCreditLimitAmountString
	_obj._CreditLimitAmountStringMutex = &sync.RWMutex{}
	_setters["cardimageurl"] = _obj.SetCardImageUrl
	_obj._CardImageUrlMutex = &sync.RWMutex{}
	_setters["termsandconditions"] = _obj.SetTermsAndConditions
	_obj._TermsAndConditionsMutex = &sync.RWMutex{}
	_setters["partnershipurl"] = _obj.SetPartnershipUrl
	_obj._PartnershipUrlMutex = &sync.RWMutex{}
	_setters["getcreditcardctatext"] = _obj.SetGetCreditCardCtaText
	_obj._GetCreditCardCtaTextMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["headerdescription"] = _obj.SetHeaderDescription
	_obj._HeaderDescriptionMutex = &sync.RWMutex{}
	_setters["feesinfoheader"] = _obj.SetFeesInfoHeader
	_obj._FeesInfoHeaderMutex = &sync.RWMutex{}
	_setters["broadvisualelementimgurl"] = _obj.SetBroadVisualElementImgUrl
	_obj._BroadVisualElementImgUrlMutex = &sync.RWMutex{}
	_InfoBlock, _fieldSetters := NewInfoItemWithQuest(questFieldPath + "/" + "InfoBlock")
	_obj._InfoBlock = _InfoBlock
	helper.AddFieldSetters("infoblock", _fieldSetters, _setters)
	_PopUpTextBlock, _fieldSetters := NewPopUpTextBlockWithQuest(questFieldPath + "/" + "PopUpTextBlock")
	_obj._PopUpTextBlock = _PopUpTextBlock
	helper.AddFieldSetters("popuptextblock", _fieldSetters, _setters)
	_StaticImages, _fieldSetters := NewInfoItemWithQuest(questFieldPath + "/" + "StaticImages")
	_obj._StaticImages = _StaticImages
	helper.AddFieldSetters("staticimages", _fieldSetters, _setters)
	_FeesInfo, _fieldSetters := NewFeesInfoItemWithQuest(questFieldPath + "/" + "FeesInfo")
	_obj._FeesInfo = _FeesInfo
	helper.AddFieldSetters("feesinfo", _fieldSetters, _setters)
	_AllFeesCta, _fieldSetters := NewAllFeesCtaWithQuest(questFieldPath + "/" + "AllFeesCta")
	_obj._AllFeesCta = _AllFeesCta
	helper.AddFieldSetters("allfeescta", _fieldSetters, _setters)
	_RewardsWorthCtaInfo, _fieldSetters := NewRewardsWorthCtaInfoWithQuest(questFieldPath + "/" + "RewardsWorthCtaInfo")
	_obj._RewardsWorthCtaInfo = _RewardsWorthCtaInfo
	helper.AddFieldSetters("rewardsworthctainfo", _fieldSetters, _setters)
	_JoiningFeeVoucherInfo, _fieldSetters := NewJoiningFeeVoucherInfoWithQuest(questFieldPath + "/" + "JoiningFeeVoucherInfo")
	_obj._JoiningFeeVoucherInfo = _JoiningFeeVoucherInfo
	helper.AddFieldSetters("joiningfeevoucherinfo", _fieldSetters, _setters)
	_AcceleratedRewardsInfo, _fieldSetters := NewAcceleratedRewardsInfoWithQuest(questFieldPath + "/" + "AcceleratedRewardsInfo")
	_obj._AcceleratedRewardsInfo = _AcceleratedRewardsInfo
	helper.AddFieldSetters("acceleratedrewardsinfo", _fieldSetters, _setters)
	_FullScreenBlock, _fieldSetters := NewCtaBlockInfoWithQuest(questFieldPath + "/" + "FullScreenBlock")
	_obj._FullScreenBlock = _FullScreenBlock
	helper.AddFieldSetters("fullscreenblock", _fieldSetters, _setters)
	_LeftHalfBlock, _fieldSetters := NewCtaBlockInfoWithQuest(questFieldPath + "/" + "LeftHalfBlock")
	_obj._LeftHalfBlock = _LeftHalfBlock
	helper.AddFieldSetters("lefthalfblock", _fieldSetters, _setters)
	_RightHalfBlock, _fieldSetters := NewCtaBlockInfoWithQuest(questFieldPath + "/" + "RightHalfBlock")
	_obj._RightHalfBlock = _RightHalfBlock
	helper.AddFieldSetters("righthalfblock", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *CreditCardOffersScreenOptions) Init(questFieldPath string) {
	newObj, _ := NewCreditCardOffersScreenOptions()
	*obj = *newObj
}
func (obj *CreditCardOffersScreenOptions) InitWithQuest(questFieldPath string) {
	newObj, _ := NewCreditCardOffersScreenOptionsWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *CreditCardOffersScreenOptions) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._InfoBlock.SetQuestSDK(questSdk)
	obj._PopUpTextBlock.SetQuestSDK(questSdk)
	obj._StaticImages.SetQuestSDK(questSdk)
	obj._FeesInfo.SetQuestSDK(questSdk)
	obj._AllFeesCta.SetQuestSDK(questSdk)
	obj._RewardsWorthCtaInfo.SetQuestSDK(questSdk)
	obj._JoiningFeeVoucherInfo.SetQuestSDK(questSdk)
	obj._AcceleratedRewardsInfo.SetQuestSDK(questSdk)
	obj._FullScreenBlock.SetQuestSDK(questSdk)
	obj._LeftHalfBlock.SetQuestSDK(questSdk)
	obj._RightHalfBlock.SetQuestSDK(questSdk)
}

func (obj *CreditCardOffersScreenOptions) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CreditCardOffersScreenOptions) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CreditLimitAmountString",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CardImageUrl",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "TermsAndConditions",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "PartnershipUrl",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "GetCreditCardCtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "HeaderDescription",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "FeesInfoHeader",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "BroadVisualElementImgUrl",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._InfoBlock.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._PopUpTextBlock.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._StaticImages.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._FeesInfo.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._AllFeesCta.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._RewardsWorthCtaInfo.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._JoiningFeeVoucherInfo.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._AcceleratedRewardsInfo.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._FullScreenBlock.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._LeftHalfBlock.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._RightHalfBlock.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	return vars, nil
}

func (obj *CreditCardOffersScreenOptions) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CreditCardOffersScreenOptions)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CreditCardOffersScreenOptions) setDynamicField(v *config.CreditCardOffersScreenOptions, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "creditlimitamountstring":
		return obj.SetCreditLimitAmountString(v.CreditLimitAmountString, true, nil)
	case "cardimageurl":
		return obj.SetCardImageUrl(v.CardImageUrl, true, nil)
	case "termsandconditions":
		return obj.SetTermsAndConditions(v.TermsAndConditions, true, nil)
	case "partnershipurl":
		return obj.SetPartnershipUrl(v.PartnershipUrl, true, nil)
	case "getcreditcardctatext":
		return obj.SetGetCreditCardCtaText(v.GetCreditCardCtaText, true, nil)
	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "headerdescription":
		return obj.SetHeaderDescription(v.HeaderDescription, true, nil)
	case "feesinfoheader":
		return obj.SetFeesInfoHeader(v.FeesInfoHeader, true, nil)
	case "broadvisualelementimgurl":
		return obj.SetBroadVisualElementImgUrl(v.BroadVisualElementImgUrl, true, nil)
	case "infoblock":
		return obj._InfoBlock.Set(v.InfoBlock, true, path)
	case "popuptextblock":
		return obj._PopUpTextBlock.Set(v.PopUpTextBlock, true, path)
	case "staticimages":
		return obj._StaticImages.Set(v.StaticImages, true, path)
	case "feesinfo":
		return obj._FeesInfo.Set(v.FeesInfo, true, path)
	case "allfeescta":
		return obj._AllFeesCta.Set(v.AllFeesCta, true, path)
	case "rewardsworthctainfo":
		return obj._RewardsWorthCtaInfo.Set(v.RewardsWorthCtaInfo, true, path)
	case "joiningfeevoucherinfo":
		return obj._JoiningFeeVoucherInfo.Set(v.JoiningFeeVoucherInfo, true, path)
	case "acceleratedrewardsinfo":
		return obj._AcceleratedRewardsInfo.Set(v.AcceleratedRewardsInfo, true, path)
	case "fullscreenblock":
		return obj._FullScreenBlock.Set(v.FullScreenBlock, true, path)
	case "lefthalfblock":
		return obj._LeftHalfBlock.Set(v.LeftHalfBlock, true, path)
	case "righthalfblock":
		return obj._RightHalfBlock.Set(v.RightHalfBlock, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CreditCardOffersScreenOptions) setDynamicFields(v *config.CreditCardOffersScreenOptions, dynamic bool, path []string) (err error) {

	err = obj.SetCreditLimitAmountString(v.CreditLimitAmountString, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCardImageUrl(v.CardImageUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTermsAndConditions(v.TermsAndConditions, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPartnershipUrl(v.PartnershipUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGetCreditCardCtaText(v.GetCreditCardCtaText, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHeaderDescription(v.HeaderDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFeesInfoHeader(v.FeesInfoHeader, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBroadVisualElementImgUrl(v.BroadVisualElementImgUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._InfoBlock.Set(v.InfoBlock, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PopUpTextBlock.Set(v.PopUpTextBlock, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._StaticImages.Set(v.StaticImages, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeesInfo.Set(v.FeesInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AllFeesCta.Set(v.AllFeesCta, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsWorthCtaInfo.Set(v.RewardsWorthCtaInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._JoiningFeeVoucherInfo.Set(v.JoiningFeeVoucherInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AcceleratedRewardsInfo.Set(v.AcceleratedRewardsInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FullScreenBlock.Set(v.FullScreenBlock, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LeftHalfBlock.Set(v.LeftHalfBlock, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RightHalfBlock.Set(v.RightHalfBlock, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CreditCardOffersScreenOptions) setStaticFields(v *config.CreditCardOffersScreenOptions) error {

	return nil
}

func (obj *CreditCardOffersScreenOptions) SetCreditLimitAmountString(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions.CreditLimitAmountString", reflect.TypeOf(val))
	}
	obj._CreditLimitAmountStringMutex.Lock()
	defer obj._CreditLimitAmountStringMutex.Unlock()
	obj._CreditLimitAmountString = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CreditLimitAmountString")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptions) SetCardImageUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions.CardImageUrl", reflect.TypeOf(val))
	}
	obj._CardImageUrlMutex.Lock()
	defer obj._CardImageUrlMutex.Unlock()
	obj._CardImageUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CardImageUrl")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptions) SetTermsAndConditions(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions.TermsAndConditions", reflect.TypeOf(val))
	}
	obj._TermsAndConditionsMutex.Lock()
	defer obj._TermsAndConditionsMutex.Unlock()
	obj._TermsAndConditions = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TermsAndConditions")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptions) SetPartnershipUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions.PartnershipUrl", reflect.TypeOf(val))
	}
	obj._PartnershipUrlMutex.Lock()
	defer obj._PartnershipUrlMutex.Unlock()
	obj._PartnershipUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PartnershipUrl")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptions) SetGetCreditCardCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions.GetCreditCardCtaText", reflect.TypeOf(val))
	}
	obj._GetCreditCardCtaTextMutex.Lock()
	defer obj._GetCreditCardCtaTextMutex.Unlock()
	obj._GetCreditCardCtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GetCreditCardCtaText")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptions) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptions) SetHeaderDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions.HeaderDescription", reflect.TypeOf(val))
	}
	obj._HeaderDescriptionMutex.Lock()
	defer obj._HeaderDescriptionMutex.Unlock()
	obj._HeaderDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "HeaderDescription")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptions) SetFeesInfoHeader(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions.FeesInfoHeader", reflect.TypeOf(val))
	}
	obj._FeesInfoHeaderMutex.Lock()
	defer obj._FeesInfoHeaderMutex.Unlock()
	obj._FeesInfoHeader = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FeesInfoHeader")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptions) SetBroadVisualElementImgUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptions.BroadVisualElementImgUrl", reflect.TypeOf(val))
	}
	obj._BroadVisualElementImgUrlMutex.Lock()
	defer obj._BroadVisualElementImgUrlMutex.Unlock()
	obj._BroadVisualElementImgUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BroadVisualElementImgUrl")
	}
	return nil
}

func NewInfoItem() (_obj *InfoItem, _setters map[string]dynconf.SetFunc) {
	_obj = &InfoItem{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	_setters["iconlink1"] = _obj.SetIconLink1
	_obj._IconLink1Mutex = &sync.RWMutex{}
	_setters["title1"] = _obj.SetTitle1
	_obj._Title1Mutex = &sync.RWMutex{}
	_setters["desc1"] = _obj.SetDesc1
	_obj._Desc1Mutex = &sync.RWMutex{}
	_setters["iconlink2"] = _obj.SetIconLink2
	_obj._IconLink2Mutex = &sync.RWMutex{}
	_setters["title2"] = _obj.SetTitle2
	_obj._Title2Mutex = &sync.RWMutex{}
	_setters["desc2"] = _obj.SetDesc2
	_obj._Desc2Mutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewInfoItemWithQuest(questFieldPath string) (_obj *InfoItem, _setters map[string]dynconf.SetFunc) {
	_obj = &InfoItem{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	_setters["iconlink1"] = _obj.SetIconLink1
	_obj._IconLink1Mutex = &sync.RWMutex{}
	_setters["title1"] = _obj.SetTitle1
	_obj._Title1Mutex = &sync.RWMutex{}
	_setters["desc1"] = _obj.SetDesc1
	_obj._Desc1Mutex = &sync.RWMutex{}
	_setters["iconlink2"] = _obj.SetIconLink2
	_obj._IconLink2Mutex = &sync.RWMutex{}
	_setters["title2"] = _obj.SetTitle2
	_obj._Title2Mutex = &sync.RWMutex{}
	_setters["desc2"] = _obj.SetDesc2
	_obj._Desc2Mutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *InfoItem) Init(questFieldPath string) {
	newObj, _ := NewInfoItem()
	*obj = *newObj
}
func (obj *InfoItem) InitWithQuest(questFieldPath string) {
	newObj, _ := NewInfoItemWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *InfoItem) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *InfoItem) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InfoItem) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink2",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title2",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc2",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *InfoItem) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.InfoItem)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InfoItem) setDynamicField(v *config.InfoItem, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iconlink":
		return obj.SetIconLink(v.IconLink, true, nil)
	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "desc":
		return obj.SetDesc(v.Desc, true, nil)
	case "iconlink1":
		return obj.SetIconLink1(v.IconLink1, true, nil)
	case "title1":
		return obj.SetTitle1(v.Title1, true, nil)
	case "desc1":
		return obj.SetDesc1(v.Desc1, true, nil)
	case "iconlink2":
		return obj.SetIconLink2(v.IconLink2, true, nil)
	case "title2":
		return obj.SetTitle2(v.Title2, true, nil)
	case "desc2":
		return obj.SetDesc2(v.Desc2, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InfoItem) setDynamicFields(v *config.InfoItem, dynamic bool, path []string) (err error) {

	err = obj.SetIconLink(v.IconLink, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc(v.Desc, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconLink1(v.IconLink1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle1(v.Title1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc1(v.Desc1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconLink2(v.IconLink2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle2(v.Title2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc2(v.Desc2, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InfoItem) setStaticFields(v *config.InfoItem) error {

	return nil
}

func (obj *InfoItem) SetIconLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem.IconLink", reflect.TypeOf(val))
	}
	obj._IconLinkMutex.Lock()
	defer obj._IconLinkMutex.Unlock()
	obj._IconLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink")
	}
	return nil
}
func (obj *InfoItem) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *InfoItem) SetDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem.Desc", reflect.TypeOf(val))
	}
	obj._DescMutex.Lock()
	defer obj._DescMutex.Unlock()
	obj._Desc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc")
	}
	return nil
}
func (obj *InfoItem) SetIconLink1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem.IconLink1", reflect.TypeOf(val))
	}
	obj._IconLink1Mutex.Lock()
	defer obj._IconLink1Mutex.Unlock()
	obj._IconLink1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink1")
	}
	return nil
}
func (obj *InfoItem) SetTitle1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem.Title1", reflect.TypeOf(val))
	}
	obj._Title1Mutex.Lock()
	defer obj._Title1Mutex.Unlock()
	obj._Title1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title1")
	}
	return nil
}
func (obj *InfoItem) SetDesc1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem.Desc1", reflect.TypeOf(val))
	}
	obj._Desc1Mutex.Lock()
	defer obj._Desc1Mutex.Unlock()
	obj._Desc1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc1")
	}
	return nil
}
func (obj *InfoItem) SetIconLink2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem.IconLink2", reflect.TypeOf(val))
	}
	obj._IconLink2Mutex.Lock()
	defer obj._IconLink2Mutex.Unlock()
	obj._IconLink2 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink2")
	}
	return nil
}
func (obj *InfoItem) SetTitle2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem.Title2", reflect.TypeOf(val))
	}
	obj._Title2Mutex.Lock()
	defer obj._Title2Mutex.Unlock()
	obj._Title2 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title2")
	}
	return nil
}
func (obj *InfoItem) SetDesc2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InfoItem.Desc2", reflect.TypeOf(val))
	}
	obj._Desc2Mutex.Lock()
	defer obj._Desc2Mutex.Unlock()
	obj._Desc2 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc2")
	}
	return nil
}

func NewPopUpTextBlock() (_obj *PopUpTextBlock, _setters map[string]dynconf.SetFunc) {
	_obj = &PopUpTextBlock{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["infotitle"] = _obj.SetInfoTitle
	_obj._InfoTitleMutex = &sync.RWMutex{}
	_setters["infodesc"] = _obj.SetInfoDesc
	_obj._InfoDescMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewPopUpTextBlockWithQuest(questFieldPath string) (_obj *PopUpTextBlock, _setters map[string]dynconf.SetFunc) {
	_obj = &PopUpTextBlock{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["infotitle"] = _obj.SetInfoTitle
	_obj._InfoTitleMutex = &sync.RWMutex{}
	_setters["infodesc"] = _obj.SetInfoDesc
	_obj._InfoDescMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *PopUpTextBlock) Init(questFieldPath string) {
	newObj, _ := NewPopUpTextBlock()
	*obj = *newObj
}
func (obj *PopUpTextBlock) InitWithQuest(questFieldPath string) {
	newObj, _ := NewPopUpTextBlockWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *PopUpTextBlock) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *PopUpTextBlock) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PopUpTextBlock) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "InfoTitle",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "InfoDesc",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *PopUpTextBlock) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PopUpTextBlock)
	if !ok {
		return fmt.Errorf("invalid data type %v *PopUpTextBlock", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PopUpTextBlock) setDynamicField(v *config.PopUpTextBlock, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "infotitle":
		return obj.SetInfoTitle(v.InfoTitle, true, nil)
	case "infodesc":
		return obj.SetInfoDesc(v.InfoDesc, true, nil)
	case "ctatext":
		return obj.SetCtaText(v.CtaText, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PopUpTextBlock) setDynamicFields(v *config.PopUpTextBlock, dynamic bool, path []string) (err error) {

	err = obj.SetInfoTitle(v.InfoTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInfoDesc(v.InfoDesc, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaText(v.CtaText, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PopUpTextBlock) setStaticFields(v *config.PopUpTextBlock) error {

	return nil
}

func (obj *PopUpTextBlock) SetInfoTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *PopUpTextBlock.InfoTitle", reflect.TypeOf(val))
	}
	obj._InfoTitleMutex.Lock()
	defer obj._InfoTitleMutex.Unlock()
	obj._InfoTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InfoTitle")
	}
	return nil
}
func (obj *PopUpTextBlock) SetInfoDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *PopUpTextBlock.InfoDesc", reflect.TypeOf(val))
	}
	obj._InfoDescMutex.Lock()
	defer obj._InfoDescMutex.Unlock()
	obj._InfoDesc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InfoDesc")
	}
	return nil
}
func (obj *PopUpTextBlock) SetCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *PopUpTextBlock.CtaText", reflect.TypeOf(val))
	}
	obj._CtaTextMutex.Lock()
	defer obj._CtaTextMutex.Unlock()
	obj._CtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaText")
	}
	return nil
}

func NewFeesInfoItem() (_obj *FeesInfoItem, _setters map[string]dynconf.SetFunc) {
	_obj = &FeesInfoItem{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	_setters["ctatitle"] = _obj.SetCtaTitle
	_obj._CtaTitleMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["ctaweblink"] = _obj.SetCtaWebLink
	_obj._CtaWebLinkMutex = &sync.RWMutex{}
	_setters["title1"] = _obj.SetTitle1
	_obj._Title1Mutex = &sync.RWMutex{}
	_setters["desc1"] = _obj.SetDesc1
	_obj._Desc1Mutex = &sync.RWMutex{}
	_setters["ctatitle1"] = _obj.SetCtaTitle1
	_obj._CtaTitle1Mutex = &sync.RWMutex{}
	_setters["ctatext1"] = _obj.SetCtaText1
	_obj._CtaText1Mutex = &sync.RWMutex{}
	_setters["ctaweblink1"] = _obj.SetCtaWebLink1
	_obj._CtaWebLink1Mutex = &sync.RWMutex{}
	_CtaInfoItem, _fieldSetters := NewCtaInfoItem()
	_obj._CtaInfoItem = _CtaInfoItem
	helper.AddFieldSetters("ctainfoitem", _fieldSetters, _setters)
	_BottomInfoItem, _fieldSetters := NewBottomInfoItem()
	_obj._BottomInfoItem = _BottomInfoItem
	helper.AddFieldSetters("bottominfoitem", _fieldSetters, _setters)
	_CtaInfoItem1, _fieldSetters := NewCtaInfoItem()
	_obj._CtaInfoItem1 = _CtaInfoItem1
	helper.AddFieldSetters("ctainfoitem1", _fieldSetters, _setters)
	_BottomInfoItem1, _fieldSetters := NewBottomInfoItem()
	_obj._BottomInfoItem1 = _BottomInfoItem1
	helper.AddFieldSetters("bottominfoitem1", _fieldSetters, _setters)
	return _obj, _setters
}

func NewFeesInfoItemWithQuest(questFieldPath string) (_obj *FeesInfoItem, _setters map[string]dynconf.SetFunc) {
	_obj = &FeesInfoItem{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	_setters["ctatitle"] = _obj.SetCtaTitle
	_obj._CtaTitleMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["ctaweblink"] = _obj.SetCtaWebLink
	_obj._CtaWebLinkMutex = &sync.RWMutex{}
	_setters["title1"] = _obj.SetTitle1
	_obj._Title1Mutex = &sync.RWMutex{}
	_setters["desc1"] = _obj.SetDesc1
	_obj._Desc1Mutex = &sync.RWMutex{}
	_setters["ctatitle1"] = _obj.SetCtaTitle1
	_obj._CtaTitle1Mutex = &sync.RWMutex{}
	_setters["ctatext1"] = _obj.SetCtaText1
	_obj._CtaText1Mutex = &sync.RWMutex{}
	_setters["ctaweblink1"] = _obj.SetCtaWebLink1
	_obj._CtaWebLink1Mutex = &sync.RWMutex{}
	_CtaInfoItem, _fieldSetters := NewCtaInfoItemWithQuest(questFieldPath + "/" + "CtaInfoItem")
	_obj._CtaInfoItem = _CtaInfoItem
	helper.AddFieldSetters("ctainfoitem", _fieldSetters, _setters)
	_BottomInfoItem, _fieldSetters := NewBottomInfoItemWithQuest(questFieldPath + "/" + "BottomInfoItem")
	_obj._BottomInfoItem = _BottomInfoItem
	helper.AddFieldSetters("bottominfoitem", _fieldSetters, _setters)
	_CtaInfoItem1, _fieldSetters := NewCtaInfoItemWithQuest(questFieldPath + "/" + "CtaInfoItem1")
	_obj._CtaInfoItem1 = _CtaInfoItem1
	helper.AddFieldSetters("ctainfoitem1", _fieldSetters, _setters)
	_BottomInfoItem1, _fieldSetters := NewBottomInfoItemWithQuest(questFieldPath + "/" + "BottomInfoItem1")
	_obj._BottomInfoItem1 = _BottomInfoItem1
	helper.AddFieldSetters("bottominfoitem1", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *FeesInfoItem) Init(questFieldPath string) {
	newObj, _ := NewFeesInfoItem()
	*obj = *newObj
}
func (obj *FeesInfoItem) InitWithQuest(questFieldPath string) {
	newObj, _ := NewFeesInfoItemWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *FeesInfoItem) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._CtaInfoItem.SetQuestSDK(questSdk)
	obj._BottomInfoItem.SetQuestSDK(questSdk)
	obj._CtaInfoItem1.SetQuestSDK(questSdk)
	obj._BottomInfoItem1.SetQuestSDK(questSdk)
}

func (obj *FeesInfoItem) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FeesInfoItem) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaTitle",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaWebLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaTitle1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaText1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaWebLink1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._CtaInfoItem.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._BottomInfoItem.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._CtaInfoItem1.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._BottomInfoItem1.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	return vars, nil
}

func (obj *FeesInfoItem) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FeesInfoItem)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FeesInfoItem) setDynamicField(v *config.FeesInfoItem, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "desc":
		return obj.SetDesc(v.Desc, true, nil)
	case "ctatitle":
		return obj.SetCtaTitle(v.CtaTitle, true, nil)
	case "ctatext":
		return obj.SetCtaText(v.CtaText, true, nil)
	case "ctaweblink":
		return obj.SetCtaWebLink(v.CtaWebLink, true, nil)
	case "title1":
		return obj.SetTitle1(v.Title1, true, nil)
	case "desc1":
		return obj.SetDesc1(v.Desc1, true, nil)
	case "ctatitle1":
		return obj.SetCtaTitle1(v.CtaTitle1, true, nil)
	case "ctatext1":
		return obj.SetCtaText1(v.CtaText1, true, nil)
	case "ctaweblink1":
		return obj.SetCtaWebLink1(v.CtaWebLink1, true, nil)
	case "ctainfoitem":
		return obj._CtaInfoItem.Set(v.CtaInfoItem, true, path)
	case "bottominfoitem":
		return obj._BottomInfoItem.Set(v.BottomInfoItem, true, path)
	case "ctainfoitem1":
		return obj._CtaInfoItem1.Set(v.CtaInfoItem1, true, path)
	case "bottominfoitem1":
		return obj._BottomInfoItem1.Set(v.BottomInfoItem1, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FeesInfoItem) setDynamicFields(v *config.FeesInfoItem, dynamic bool, path []string) (err error) {

	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc(v.Desc, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaTitle(v.CtaTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaText(v.CtaText, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaWebLink(v.CtaWebLink, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle1(v.Title1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc1(v.Desc1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaTitle1(v.CtaTitle1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaText1(v.CtaText1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaWebLink1(v.CtaWebLink1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._CtaInfoItem.Set(v.CtaInfoItem, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BottomInfoItem.Set(v.BottomInfoItem, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CtaInfoItem1.Set(v.CtaInfoItem1, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BottomInfoItem1.Set(v.BottomInfoItem1, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FeesInfoItem) setStaticFields(v *config.FeesInfoItem) error {

	return nil
}

func (obj *FeesInfoItem) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *FeesInfoItem) SetDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.Desc", reflect.TypeOf(val))
	}
	obj._DescMutex.Lock()
	defer obj._DescMutex.Unlock()
	obj._Desc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc")
	}
	return nil
}
func (obj *FeesInfoItem) SetCtaTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.CtaTitle", reflect.TypeOf(val))
	}
	obj._CtaTitleMutex.Lock()
	defer obj._CtaTitleMutex.Unlock()
	obj._CtaTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaTitle")
	}
	return nil
}
func (obj *FeesInfoItem) SetCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.CtaText", reflect.TypeOf(val))
	}
	obj._CtaTextMutex.Lock()
	defer obj._CtaTextMutex.Unlock()
	obj._CtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaText")
	}
	return nil
}
func (obj *FeesInfoItem) SetCtaWebLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.CtaWebLink", reflect.TypeOf(val))
	}
	obj._CtaWebLinkMutex.Lock()
	defer obj._CtaWebLinkMutex.Unlock()
	obj._CtaWebLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaWebLink")
	}
	return nil
}
func (obj *FeesInfoItem) SetTitle1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.Title1", reflect.TypeOf(val))
	}
	obj._Title1Mutex.Lock()
	defer obj._Title1Mutex.Unlock()
	obj._Title1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title1")
	}
	return nil
}
func (obj *FeesInfoItem) SetDesc1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.Desc1", reflect.TypeOf(val))
	}
	obj._Desc1Mutex.Lock()
	defer obj._Desc1Mutex.Unlock()
	obj._Desc1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc1")
	}
	return nil
}
func (obj *FeesInfoItem) SetCtaTitle1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.CtaTitle1", reflect.TypeOf(val))
	}
	obj._CtaTitle1Mutex.Lock()
	defer obj._CtaTitle1Mutex.Unlock()
	obj._CtaTitle1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaTitle1")
	}
	return nil
}
func (obj *FeesInfoItem) SetCtaText1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.CtaText1", reflect.TypeOf(val))
	}
	obj._CtaText1Mutex.Lock()
	defer obj._CtaText1Mutex.Unlock()
	obj._CtaText1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaText1")
	}
	return nil
}
func (obj *FeesInfoItem) SetCtaWebLink1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeesInfoItem.CtaWebLink1", reflect.TypeOf(val))
	}
	obj._CtaWebLink1Mutex.Lock()
	defer obj._CtaWebLink1Mutex.Unlock()
	obj._CtaWebLink1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaWebLink1")
	}
	return nil
}

func NewCtaInfoItem() (_obj *CtaInfoItem, _setters map[string]dynconf.SetFunc) {
	_obj = &CtaInfoItem{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	_setters["iconlink1"] = _obj.SetIconLink1
	_obj._IconLink1Mutex = &sync.RWMutex{}
	_setters["title1"] = _obj.SetTitle1
	_obj._Title1Mutex = &sync.RWMutex{}
	_setters["desc1"] = _obj.SetDesc1
	_obj._Desc1Mutex = &sync.RWMutex{}
	_setters["iconlink2"] = _obj.SetIconLink2
	_obj._IconLink2Mutex = &sync.RWMutex{}
	_setters["title2"] = _obj.SetTitle2
	_obj._Title2Mutex = &sync.RWMutex{}
	_setters["desc2"] = _obj.SetDesc2
	_obj._Desc2Mutex = &sync.RWMutex{}
	_setters["iconlink3"] = _obj.SetIconLink3
	_obj._IconLink3Mutex = &sync.RWMutex{}
	_setters["title3"] = _obj.SetTitle3
	_obj._Title3Mutex = &sync.RWMutex{}
	_setters["desc3"] = _obj.SetDesc3
	_obj._Desc3Mutex = &sync.RWMutex{}
	_setters["iconlink4"] = _obj.SetIconLink4
	_obj._IconLink4Mutex = &sync.RWMutex{}
	_setters["title4"] = _obj.SetTitle4
	_obj._Title4Mutex = &sync.RWMutex{}
	_setters["desc4"] = _obj.SetDesc4
	_obj._Desc4Mutex = &sync.RWMutex{}
	_setters["iconlink5"] = _obj.SetIconLink5
	_obj._IconLink5Mutex = &sync.RWMutex{}
	_setters["title5"] = _obj.SetTitle5
	_obj._Title5Mutex = &sync.RWMutex{}
	_setters["desc5"] = _obj.SetDesc5
	_obj._Desc5Mutex = &sync.RWMutex{}
	_setters["iconlink6"] = _obj.SetIconLink6
	_obj._IconLink6Mutex = &sync.RWMutex{}
	_setters["title6"] = _obj.SetTitle6
	_obj._Title6Mutex = &sync.RWMutex{}
	_setters["desc6"] = _obj.SetDesc6
	_obj._Desc6Mutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewCtaInfoItemWithQuest(questFieldPath string) (_obj *CtaInfoItem, _setters map[string]dynconf.SetFunc) {
	_obj = &CtaInfoItem{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	_setters["iconlink1"] = _obj.SetIconLink1
	_obj._IconLink1Mutex = &sync.RWMutex{}
	_setters["title1"] = _obj.SetTitle1
	_obj._Title1Mutex = &sync.RWMutex{}
	_setters["desc1"] = _obj.SetDesc1
	_obj._Desc1Mutex = &sync.RWMutex{}
	_setters["iconlink2"] = _obj.SetIconLink2
	_obj._IconLink2Mutex = &sync.RWMutex{}
	_setters["title2"] = _obj.SetTitle2
	_obj._Title2Mutex = &sync.RWMutex{}
	_setters["desc2"] = _obj.SetDesc2
	_obj._Desc2Mutex = &sync.RWMutex{}
	_setters["iconlink3"] = _obj.SetIconLink3
	_obj._IconLink3Mutex = &sync.RWMutex{}
	_setters["title3"] = _obj.SetTitle3
	_obj._Title3Mutex = &sync.RWMutex{}
	_setters["desc3"] = _obj.SetDesc3
	_obj._Desc3Mutex = &sync.RWMutex{}
	_setters["iconlink4"] = _obj.SetIconLink4
	_obj._IconLink4Mutex = &sync.RWMutex{}
	_setters["title4"] = _obj.SetTitle4
	_obj._Title4Mutex = &sync.RWMutex{}
	_setters["desc4"] = _obj.SetDesc4
	_obj._Desc4Mutex = &sync.RWMutex{}
	_setters["iconlink5"] = _obj.SetIconLink5
	_obj._IconLink5Mutex = &sync.RWMutex{}
	_setters["title5"] = _obj.SetTitle5
	_obj._Title5Mutex = &sync.RWMutex{}
	_setters["desc5"] = _obj.SetDesc5
	_obj._Desc5Mutex = &sync.RWMutex{}
	_setters["iconlink6"] = _obj.SetIconLink6
	_obj._IconLink6Mutex = &sync.RWMutex{}
	_setters["title6"] = _obj.SetTitle6
	_obj._Title6Mutex = &sync.RWMutex{}
	_setters["desc6"] = _obj.SetDesc6
	_obj._Desc6Mutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *CtaInfoItem) Init(questFieldPath string) {
	newObj, _ := NewCtaInfoItem()
	*obj = *newObj
}
func (obj *CtaInfoItem) InitWithQuest(questFieldPath string) {
	newObj, _ := NewCtaInfoItemWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *CtaInfoItem) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *CtaInfoItem) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CtaInfoItem) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc1",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink2",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title2",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc2",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink3",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title3",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc3",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink4",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title4",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc4",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink5",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title5",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc5",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink6",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title6",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc6",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *CtaInfoItem) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CtaInfoItem)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CtaInfoItem) setDynamicField(v *config.CtaInfoItem, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iconlink":
		return obj.SetIconLink(v.IconLink, true, nil)
	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "desc":
		return obj.SetDesc(v.Desc, true, nil)
	case "iconlink1":
		return obj.SetIconLink1(v.IconLink1, true, nil)
	case "title1":
		return obj.SetTitle1(v.Title1, true, nil)
	case "desc1":
		return obj.SetDesc1(v.Desc1, true, nil)
	case "iconlink2":
		return obj.SetIconLink2(v.IconLink2, true, nil)
	case "title2":
		return obj.SetTitle2(v.Title2, true, nil)
	case "desc2":
		return obj.SetDesc2(v.Desc2, true, nil)
	case "iconlink3":
		return obj.SetIconLink3(v.IconLink3, true, nil)
	case "title3":
		return obj.SetTitle3(v.Title3, true, nil)
	case "desc3":
		return obj.SetDesc3(v.Desc3, true, nil)
	case "iconlink4":
		return obj.SetIconLink4(v.IconLink4, true, nil)
	case "title4":
		return obj.SetTitle4(v.Title4, true, nil)
	case "desc4":
		return obj.SetDesc4(v.Desc4, true, nil)
	case "iconlink5":
		return obj.SetIconLink5(v.IconLink5, true, nil)
	case "title5":
		return obj.SetTitle5(v.Title5, true, nil)
	case "desc5":
		return obj.SetDesc5(v.Desc5, true, nil)
	case "iconlink6":
		return obj.SetIconLink6(v.IconLink6, true, nil)
	case "title6":
		return obj.SetTitle6(v.Title6, true, nil)
	case "desc6":
		return obj.SetDesc6(v.Desc6, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CtaInfoItem) setDynamicFields(v *config.CtaInfoItem, dynamic bool, path []string) (err error) {

	err = obj.SetIconLink(v.IconLink, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc(v.Desc, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconLink1(v.IconLink1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle1(v.Title1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc1(v.Desc1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconLink2(v.IconLink2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle2(v.Title2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc2(v.Desc2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconLink3(v.IconLink3, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle3(v.Title3, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc3(v.Desc3, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconLink4(v.IconLink4, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle4(v.Title4, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc4(v.Desc4, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconLink5(v.IconLink5, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle5(v.Title5, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc5(v.Desc5, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIconLink6(v.IconLink6, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle6(v.Title6, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc6(v.Desc6, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CtaInfoItem) setStaticFields(v *config.CtaInfoItem) error {

	return nil
}

func (obj *CtaInfoItem) SetIconLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.IconLink", reflect.TypeOf(val))
	}
	obj._IconLinkMutex.Lock()
	defer obj._IconLinkMutex.Unlock()
	obj._IconLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink")
	}
	return nil
}
func (obj *CtaInfoItem) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *CtaInfoItem) SetDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Desc", reflect.TypeOf(val))
	}
	obj._DescMutex.Lock()
	defer obj._DescMutex.Unlock()
	obj._Desc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc")
	}
	return nil
}
func (obj *CtaInfoItem) SetIconLink1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.IconLink1", reflect.TypeOf(val))
	}
	obj._IconLink1Mutex.Lock()
	defer obj._IconLink1Mutex.Unlock()
	obj._IconLink1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink1")
	}
	return nil
}
func (obj *CtaInfoItem) SetTitle1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Title1", reflect.TypeOf(val))
	}
	obj._Title1Mutex.Lock()
	defer obj._Title1Mutex.Unlock()
	obj._Title1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title1")
	}
	return nil
}
func (obj *CtaInfoItem) SetDesc1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Desc1", reflect.TypeOf(val))
	}
	obj._Desc1Mutex.Lock()
	defer obj._Desc1Mutex.Unlock()
	obj._Desc1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc1")
	}
	return nil
}
func (obj *CtaInfoItem) SetIconLink2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.IconLink2", reflect.TypeOf(val))
	}
	obj._IconLink2Mutex.Lock()
	defer obj._IconLink2Mutex.Unlock()
	obj._IconLink2 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink2")
	}
	return nil
}
func (obj *CtaInfoItem) SetTitle2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Title2", reflect.TypeOf(val))
	}
	obj._Title2Mutex.Lock()
	defer obj._Title2Mutex.Unlock()
	obj._Title2 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title2")
	}
	return nil
}
func (obj *CtaInfoItem) SetDesc2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Desc2", reflect.TypeOf(val))
	}
	obj._Desc2Mutex.Lock()
	defer obj._Desc2Mutex.Unlock()
	obj._Desc2 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc2")
	}
	return nil
}
func (obj *CtaInfoItem) SetIconLink3(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.IconLink3", reflect.TypeOf(val))
	}
	obj._IconLink3Mutex.Lock()
	defer obj._IconLink3Mutex.Unlock()
	obj._IconLink3 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink3")
	}
	return nil
}
func (obj *CtaInfoItem) SetTitle3(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Title3", reflect.TypeOf(val))
	}
	obj._Title3Mutex.Lock()
	defer obj._Title3Mutex.Unlock()
	obj._Title3 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title3")
	}
	return nil
}
func (obj *CtaInfoItem) SetDesc3(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Desc3", reflect.TypeOf(val))
	}
	obj._Desc3Mutex.Lock()
	defer obj._Desc3Mutex.Unlock()
	obj._Desc3 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc3")
	}
	return nil
}
func (obj *CtaInfoItem) SetIconLink4(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.IconLink4", reflect.TypeOf(val))
	}
	obj._IconLink4Mutex.Lock()
	defer obj._IconLink4Mutex.Unlock()
	obj._IconLink4 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink4")
	}
	return nil
}
func (obj *CtaInfoItem) SetTitle4(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Title4", reflect.TypeOf(val))
	}
	obj._Title4Mutex.Lock()
	defer obj._Title4Mutex.Unlock()
	obj._Title4 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title4")
	}
	return nil
}
func (obj *CtaInfoItem) SetDesc4(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Desc4", reflect.TypeOf(val))
	}
	obj._Desc4Mutex.Lock()
	defer obj._Desc4Mutex.Unlock()
	obj._Desc4 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc4")
	}
	return nil
}
func (obj *CtaInfoItem) SetIconLink5(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.IconLink5", reflect.TypeOf(val))
	}
	obj._IconLink5Mutex.Lock()
	defer obj._IconLink5Mutex.Unlock()
	obj._IconLink5 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink5")
	}
	return nil
}
func (obj *CtaInfoItem) SetTitle5(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Title5", reflect.TypeOf(val))
	}
	obj._Title5Mutex.Lock()
	defer obj._Title5Mutex.Unlock()
	obj._Title5 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title5")
	}
	return nil
}
func (obj *CtaInfoItem) SetDesc5(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Desc5", reflect.TypeOf(val))
	}
	obj._Desc5Mutex.Lock()
	defer obj._Desc5Mutex.Unlock()
	obj._Desc5 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc5")
	}
	return nil
}
func (obj *CtaInfoItem) SetIconLink6(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.IconLink6", reflect.TypeOf(val))
	}
	obj._IconLink6Mutex.Lock()
	defer obj._IconLink6Mutex.Unlock()
	obj._IconLink6 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink6")
	}
	return nil
}
func (obj *CtaInfoItem) SetTitle6(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Title6", reflect.TypeOf(val))
	}
	obj._Title6Mutex.Lock()
	defer obj._Title6Mutex.Unlock()
	obj._Title6 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title6")
	}
	return nil
}
func (obj *CtaInfoItem) SetDesc6(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaInfoItem.Desc6", reflect.TypeOf(val))
	}
	obj._Desc6Mutex.Lock()
	defer obj._Desc6Mutex.Unlock()
	obj._Desc6 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc6")
	}
	return nil
}

func NewBottomInfoItem() (_obj *BottomInfoItem, _setters map[string]dynconf.SetFunc) {
	_obj = &BottomInfoItem{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewBottomInfoItemWithQuest(questFieldPath string) (_obj *BottomInfoItem, _setters map[string]dynconf.SetFunc) {
	_obj = &BottomInfoItem{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *BottomInfoItem) Init(questFieldPath string) {
	newObj, _ := NewBottomInfoItem()
	*obj = *newObj
}
func (obj *BottomInfoItem) InitWithQuest(questFieldPath string) {
	newObj, _ := NewBottomInfoItemWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *BottomInfoItem) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *BottomInfoItem) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *BottomInfoItem) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *BottomInfoItem) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.BottomInfoItem)
	if !ok {
		return fmt.Errorf("invalid data type %v *BottomInfoItem", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *BottomInfoItem) setDynamicField(v *config.BottomInfoItem, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iconlink":
		return obj.SetIconLink(v.IconLink, true, nil)
	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "desc":
		return obj.SetDesc(v.Desc, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *BottomInfoItem) setDynamicFields(v *config.BottomInfoItem, dynamic bool, path []string) (err error) {

	err = obj.SetIconLink(v.IconLink, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc(v.Desc, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *BottomInfoItem) setStaticFields(v *config.BottomInfoItem) error {

	return nil
}

func (obj *BottomInfoItem) SetIconLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *BottomInfoItem.IconLink", reflect.TypeOf(val))
	}
	obj._IconLinkMutex.Lock()
	defer obj._IconLinkMutex.Unlock()
	obj._IconLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink")
	}
	return nil
}
func (obj *BottomInfoItem) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *BottomInfoItem.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *BottomInfoItem) SetDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *BottomInfoItem.Desc", reflect.TypeOf(val))
	}
	obj._DescMutex.Lock()
	defer obj._DescMutex.Unlock()
	obj._Desc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc")
	}
	return nil
}

func NewAllFeesCta() (_obj *AllFeesCta, _setters map[string]dynconf.SetFunc) {
	_obj = &AllFeesCta{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["ctaweblink"] = _obj.SetCtaWebLink
	_obj._CtaWebLinkMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewAllFeesCtaWithQuest(questFieldPath string) (_obj *AllFeesCta, _setters map[string]dynconf.SetFunc) {
	_obj = &AllFeesCta{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["ctaweblink"] = _obj.SetCtaWebLink
	_obj._CtaWebLinkMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *AllFeesCta) Init(questFieldPath string) {
	newObj, _ := NewAllFeesCta()
	*obj = *newObj
}
func (obj *AllFeesCta) InitWithQuest(questFieldPath string) {
	newObj, _ := NewAllFeesCtaWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *AllFeesCta) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *AllFeesCta) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AllFeesCta) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaWebLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *AllFeesCta) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AllFeesCta)
	if !ok {
		return fmt.Errorf("invalid data type %v *AllFeesCta", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AllFeesCta) setDynamicField(v *config.AllFeesCta, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ctatext":
		return obj.SetCtaText(v.CtaText, true, nil)
	case "ctaweblink":
		return obj.SetCtaWebLink(v.CtaWebLink, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AllFeesCta) setDynamicFields(v *config.AllFeesCta, dynamic bool, path []string) (err error) {

	err = obj.SetCtaText(v.CtaText, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaWebLink(v.CtaWebLink, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AllFeesCta) setStaticFields(v *config.AllFeesCta) error {

	return nil
}

func (obj *AllFeesCta) SetCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AllFeesCta.CtaText", reflect.TypeOf(val))
	}
	obj._CtaTextMutex.Lock()
	defer obj._CtaTextMutex.Unlock()
	obj._CtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaText")
	}
	return nil
}
func (obj *AllFeesCta) SetCtaWebLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AllFeesCta.CtaWebLink", reflect.TypeOf(val))
	}
	obj._CtaWebLinkMutex.Lock()
	defer obj._CtaWebLinkMutex.Unlock()
	obj._CtaWebLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaWebLink")
	}
	return nil
}

func NewRewardsWorthCtaInfo() (_obj *RewardsWorthCtaInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsWorthCtaInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["dplinkscreentitle"] = _obj.SetDpLinkScreenTitle
	_obj._DpLinkScreenTitleMutex = &sync.RWMutex{}
	_setters["dplinkdescription"] = _obj.SetDpLinkDescription
	_obj._DpLinkDescriptionMutex = &sync.RWMutex{}
	_setters["dplinksubtitle"] = _obj.SetDpLinkSubtitle
	_obj._DpLinkSubtitleMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewRewardsWorthCtaInfoWithQuest(questFieldPath string) (_obj *RewardsWorthCtaInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsWorthCtaInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["dplinkscreentitle"] = _obj.SetDpLinkScreenTitle
	_obj._DpLinkScreenTitleMutex = &sync.RWMutex{}
	_setters["dplinkdescription"] = _obj.SetDpLinkDescription
	_obj._DpLinkDescriptionMutex = &sync.RWMutex{}
	_setters["dplinksubtitle"] = _obj.SetDpLinkSubtitle
	_obj._DpLinkSubtitleMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *RewardsWorthCtaInfo) Init(questFieldPath string) {
	newObj, _ := NewRewardsWorthCtaInfo()
	*obj = *newObj
}
func (obj *RewardsWorthCtaInfo) InitWithQuest(questFieldPath string) {
	newObj, _ := NewRewardsWorthCtaInfoWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *RewardsWorthCtaInfo) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *RewardsWorthCtaInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RewardsWorthCtaInfo) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DpLinkScreenTitle",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DpLinkDescription",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DpLinkSubtitle",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *RewardsWorthCtaInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RewardsWorthCtaInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsWorthCtaInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RewardsWorthCtaInfo) setDynamicField(v *config.RewardsWorthCtaInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ctatext":
		return obj.SetCtaText(v.CtaText, true, nil)
	case "dplinkscreentitle":
		return obj.SetDpLinkScreenTitle(v.DpLinkScreenTitle, true, nil)
	case "dplinkdescription":
		return obj.SetDpLinkDescription(v.DpLinkDescription, true, nil)
	case "dplinksubtitle":
		return obj.SetDpLinkSubtitle(v.DpLinkSubtitle, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RewardsWorthCtaInfo) setDynamicFields(v *config.RewardsWorthCtaInfo, dynamic bool, path []string) (err error) {

	err = obj.SetCtaText(v.CtaText, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDpLinkScreenTitle(v.DpLinkScreenTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDpLinkDescription(v.DpLinkDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDpLinkSubtitle(v.DpLinkSubtitle, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RewardsWorthCtaInfo) setStaticFields(v *config.RewardsWorthCtaInfo) error {

	return nil
}

func (obj *RewardsWorthCtaInfo) SetCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsWorthCtaInfo.CtaText", reflect.TypeOf(val))
	}
	obj._CtaTextMutex.Lock()
	defer obj._CtaTextMutex.Unlock()
	obj._CtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaText")
	}
	return nil
}
func (obj *RewardsWorthCtaInfo) SetDpLinkScreenTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsWorthCtaInfo.DpLinkScreenTitle", reflect.TypeOf(val))
	}
	obj._DpLinkScreenTitleMutex.Lock()
	defer obj._DpLinkScreenTitleMutex.Unlock()
	obj._DpLinkScreenTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DpLinkScreenTitle")
	}
	return nil
}
func (obj *RewardsWorthCtaInfo) SetDpLinkDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsWorthCtaInfo.DpLinkDescription", reflect.TypeOf(val))
	}
	obj._DpLinkDescriptionMutex.Lock()
	defer obj._DpLinkDescriptionMutex.Unlock()
	obj._DpLinkDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DpLinkDescription")
	}
	return nil
}
func (obj *RewardsWorthCtaInfo) SetDpLinkSubtitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsWorthCtaInfo.DpLinkSubtitle", reflect.TypeOf(val))
	}
	obj._DpLinkSubtitleMutex.Lock()
	defer obj._DpLinkSubtitleMutex.Unlock()
	obj._DpLinkSubtitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DpLinkSubtitle")
	}
	return nil
}

func NewJoiningFeeVoucherInfo() (_obj *JoiningFeeVoucherInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &JoiningFeeVoucherInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["ctaweblink"] = _obj.SetCtaWebLink
	_obj._CtaWebLinkMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewJoiningFeeVoucherInfoWithQuest(questFieldPath string) (_obj *JoiningFeeVoucherInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &JoiningFeeVoucherInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["ctaweblink"] = _obj.SetCtaWebLink
	_obj._CtaWebLinkMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *JoiningFeeVoucherInfo) Init(questFieldPath string) {
	newObj, _ := NewJoiningFeeVoucherInfo()
	*obj = *newObj
}
func (obj *JoiningFeeVoucherInfo) InitWithQuest(questFieldPath string) {
	newObj, _ := NewJoiningFeeVoucherInfoWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *JoiningFeeVoucherInfo) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *JoiningFeeVoucherInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *JoiningFeeVoucherInfo) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaWebLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *JoiningFeeVoucherInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.JoiningFeeVoucherInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *JoiningFeeVoucherInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *JoiningFeeVoucherInfo) setDynamicField(v *config.JoiningFeeVoucherInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iconlink":
		return obj.SetIconLink(v.IconLink, true, nil)
	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "ctatext":
		return obj.SetCtaText(v.CtaText, true, nil)
	case "ctaweblink":
		return obj.SetCtaWebLink(v.CtaWebLink, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *JoiningFeeVoucherInfo) setDynamicFields(v *config.JoiningFeeVoucherInfo, dynamic bool, path []string) (err error) {

	err = obj.SetIconLink(v.IconLink, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaText(v.CtaText, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaWebLink(v.CtaWebLink, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *JoiningFeeVoucherInfo) setStaticFields(v *config.JoiningFeeVoucherInfo) error {

	return nil
}

func (obj *JoiningFeeVoucherInfo) SetIconLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *JoiningFeeVoucherInfo.IconLink", reflect.TypeOf(val))
	}
	obj._IconLinkMutex.Lock()
	defer obj._IconLinkMutex.Unlock()
	obj._IconLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink")
	}
	return nil
}
func (obj *JoiningFeeVoucherInfo) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *JoiningFeeVoucherInfo.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *JoiningFeeVoucherInfo) SetCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *JoiningFeeVoucherInfo.CtaText", reflect.TypeOf(val))
	}
	obj._CtaTextMutex.Lock()
	defer obj._CtaTextMutex.Unlock()
	obj._CtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaText")
	}
	return nil
}
func (obj *JoiningFeeVoucherInfo) SetCtaWebLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *JoiningFeeVoucherInfo.CtaWebLink", reflect.TypeOf(val))
	}
	obj._CtaWebLinkMutex.Lock()
	defer obj._CtaWebLinkMutex.Unlock()
	obj._CtaWebLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaWebLink")
	}
	return nil
}

func NewAcceleratedRewardsInfo() (_obj *AcceleratedRewardsInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &AcceleratedRewardsInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["infoiconlink"] = _obj.SetInfoIconLink
	_obj._InfoIconLinkMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["dplinkscreentitle"] = _obj.SetDpLinkScreenTitle
	_obj._DpLinkScreenTitleMutex = &sync.RWMutex{}
	_setters["dplinkdescription"] = _obj.SetDpLinkDescription
	_obj._DpLinkDescriptionMutex = &sync.RWMutex{}
	_setters["dplinksubtitle"] = _obj.SetDpLinkSubtitle
	_obj._DpLinkSubtitleMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewAcceleratedRewardsInfoWithQuest(questFieldPath string) (_obj *AcceleratedRewardsInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &AcceleratedRewardsInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["infoiconlink"] = _obj.SetInfoIconLink
	_obj._InfoIconLinkMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_setters["dplinkscreentitle"] = _obj.SetDpLinkScreenTitle
	_obj._DpLinkScreenTitleMutex = &sync.RWMutex{}
	_setters["dplinkdescription"] = _obj.SetDpLinkDescription
	_obj._DpLinkDescriptionMutex = &sync.RWMutex{}
	_setters["dplinksubtitle"] = _obj.SetDpLinkSubtitle
	_obj._DpLinkSubtitleMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *AcceleratedRewardsInfo) Init(questFieldPath string) {
	newObj, _ := NewAcceleratedRewardsInfo()
	*obj = *newObj
}
func (obj *AcceleratedRewardsInfo) InitWithQuest(questFieldPath string) {
	newObj, _ := NewAcceleratedRewardsInfoWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *AcceleratedRewardsInfo) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *AcceleratedRewardsInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AcceleratedRewardsInfo) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "InfoIconLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DpLinkScreenTitle",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DpLinkDescription",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "DpLinkSubtitle",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *AcceleratedRewardsInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AcceleratedRewardsInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *AcceleratedRewardsInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AcceleratedRewardsInfo) setDynamicField(v *config.AcceleratedRewardsInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "infoiconlink":
		return obj.SetInfoIconLink(v.InfoIconLink, true, nil)
	case "ctatext":
		return obj.SetCtaText(v.CtaText, true, nil)
	case "dplinkscreentitle":
		return obj.SetDpLinkScreenTitle(v.DpLinkScreenTitle, true, nil)
	case "dplinkdescription":
		return obj.SetDpLinkDescription(v.DpLinkDescription, true, nil)
	case "dplinksubtitle":
		return obj.SetDpLinkSubtitle(v.DpLinkSubtitle, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AcceleratedRewardsInfo) setDynamicFields(v *config.AcceleratedRewardsInfo, dynamic bool, path []string) (err error) {

	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInfoIconLink(v.InfoIconLink, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaText(v.CtaText, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDpLinkScreenTitle(v.DpLinkScreenTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDpLinkDescription(v.DpLinkDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDpLinkSubtitle(v.DpLinkSubtitle, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AcceleratedRewardsInfo) setStaticFields(v *config.AcceleratedRewardsInfo) error {

	return nil
}

func (obj *AcceleratedRewardsInfo) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AcceleratedRewardsInfo.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *AcceleratedRewardsInfo) SetInfoIconLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AcceleratedRewardsInfo.InfoIconLink", reflect.TypeOf(val))
	}
	obj._InfoIconLinkMutex.Lock()
	defer obj._InfoIconLinkMutex.Unlock()
	obj._InfoIconLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InfoIconLink")
	}
	return nil
}
func (obj *AcceleratedRewardsInfo) SetCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AcceleratedRewardsInfo.CtaText", reflect.TypeOf(val))
	}
	obj._CtaTextMutex.Lock()
	defer obj._CtaTextMutex.Unlock()
	obj._CtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaText")
	}
	return nil
}
func (obj *AcceleratedRewardsInfo) SetDpLinkScreenTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AcceleratedRewardsInfo.DpLinkScreenTitle", reflect.TypeOf(val))
	}
	obj._DpLinkScreenTitleMutex.Lock()
	defer obj._DpLinkScreenTitleMutex.Unlock()
	obj._DpLinkScreenTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DpLinkScreenTitle")
	}
	return nil
}
func (obj *AcceleratedRewardsInfo) SetDpLinkDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AcceleratedRewardsInfo.DpLinkDescription", reflect.TypeOf(val))
	}
	obj._DpLinkDescriptionMutex.Lock()
	defer obj._DpLinkDescriptionMutex.Unlock()
	obj._DpLinkDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DpLinkDescription")
	}
	return nil
}
func (obj *AcceleratedRewardsInfo) SetDpLinkSubtitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AcceleratedRewardsInfo.DpLinkSubtitle", reflect.TypeOf(val))
	}
	obj._DpLinkSubtitleMutex.Lock()
	defer obj._DpLinkSubtitleMutex.Unlock()
	obj._DpLinkSubtitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DpLinkSubtitle")
	}
	return nil
}

func NewCtaBlockInfo() (_obj *CtaBlockInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &CtaBlockInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["subtitle"] = _obj.SetSubTitle
	_obj._SubTitleMutex = &sync.RWMutex{}
	_setters["ctaname"] = _obj.SetCtaName
	_obj._CtaNameMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewCtaBlockInfoWithQuest(questFieldPath string) (_obj *CtaBlockInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &CtaBlockInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["subtitle"] = _obj.SetSubTitle
	_obj._SubTitleMutex = &sync.RWMutex{}
	_setters["ctaname"] = _obj.SetCtaName
	_obj._CtaNameMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *CtaBlockInfo) Init(questFieldPath string) {
	newObj, _ := NewCtaBlockInfo()
	*obj = *newObj
}
func (obj *CtaBlockInfo) InitWithQuest(questFieldPath string) {
	newObj, _ := NewCtaBlockInfoWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *CtaBlockInfo) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *CtaBlockInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CtaBlockInfo) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "SubTitle",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaName",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *CtaBlockInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CtaBlockInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CtaBlockInfo) setDynamicField(v *config.CtaBlockInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iconlink":
		return obj.SetIconLink(v.IconLink, true, nil)
	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "subtitle":
		return obj.SetSubTitle(v.SubTitle, true, nil)
	case "ctaname":
		return obj.SetCtaName(v.CtaName, true, nil)
	case "ctatext":
		return obj.SetCtaText(v.CtaText, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CtaBlockInfo) setDynamicFields(v *config.CtaBlockInfo, dynamic bool, path []string) (err error) {

	err = obj.SetIconLink(v.IconLink, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSubTitle(v.SubTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaName(v.CtaName, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaText(v.CtaText, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CtaBlockInfo) setStaticFields(v *config.CtaBlockInfo) error {

	return nil
}

func (obj *CtaBlockInfo) SetIconLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfo.IconLink", reflect.TypeOf(val))
	}
	obj._IconLinkMutex.Lock()
	defer obj._IconLinkMutex.Unlock()
	obj._IconLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink")
	}
	return nil
}
func (obj *CtaBlockInfo) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfo.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *CtaBlockInfo) SetSubTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfo.SubTitle", reflect.TypeOf(val))
	}
	obj._SubTitleMutex.Lock()
	defer obj._SubTitleMutex.Unlock()
	obj._SubTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SubTitle")
	}
	return nil
}
func (obj *CtaBlockInfo) SetCtaName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfo.CtaName", reflect.TypeOf(val))
	}
	obj._CtaNameMutex.Lock()
	defer obj._CtaNameMutex.Unlock()
	obj._CtaName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaName")
	}
	return nil
}
func (obj *CtaBlockInfo) SetCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfo.CtaText", reflect.TypeOf(val))
	}
	obj._CtaTextMutex.Lock()
	defer obj._CtaTextMutex.Unlock()
	obj._CtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaText")
	}
	return nil
}

func NewCreditCardOffersScreenOptionsV2() (_obj *CreditCardOffersScreenOptionsV2, _setters map[string]dynconf.SetFunc) {
	_obj = &CreditCardOffersScreenOptionsV2{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["creditlimitamountstring"] = _obj.SetCreditLimitAmountString
	_obj._CreditLimitAmountStringMutex = &sync.RWMutex{}
	_setters["cardimageurl"] = _obj.SetCardImageUrl
	_obj._CardImageUrlMutex = &sync.RWMutex{}
	_setters["termsandconditions"] = _obj.SetTermsAndConditions
	_obj._TermsAndConditionsMutex = &sync.RWMutex{}
	_setters["partnershipurl"] = _obj.SetPartnershipUrl
	_obj._PartnershipUrlMutex = &sync.RWMutex{}
	_setters["getcreditcardctatext"] = _obj.SetGetCreditCardCtaText
	_obj._GetCreditCardCtaTextMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["headerdescription"] = _obj.SetHeaderDescription
	_obj._HeaderDescriptionMutex = &sync.RWMutex{}
	_setters["feesinfoheader"] = _obj.SetFeesInfoHeader
	_obj._FeesInfoHeaderMutex = &sync.RWMutex{}
	_setters["broadvisualelementimgurl"] = _obj.SetBroadVisualElementImgUrl
	_obj._BroadVisualElementImgUrlMutex = &sync.RWMutex{}
	_setters["broadvisualelementimgurlios"] = _obj.SetBroadVisualElementImgUrlIOS
	_obj._BroadVisualElementImgUrlIOSMutex = &sync.RWMutex{}
	_setters["cta1name"] = _obj.SetCta1Name
	_obj._Cta1NameMutex = &sync.RWMutex{}
	_setters["cta2name"] = _obj.SetCta2Name
	_obj._Cta2NameMutex = &sync.RWMutex{}
	_setters["cta3name"] = _obj.SetCta3Name
	_obj._Cta3NameMutex = &sync.RWMutex{}
	_StaticImages, _fieldSetters := NewInfoItem()
	_obj._StaticImages = _StaticImages
	helper.AddFieldSetters("staticimages", _fieldSetters, _setters)
	_FeesInfo, _fieldSetters := NewFeesInfoItem()
	_obj._FeesInfo = _FeesInfo
	helper.AddFieldSetters("feesinfo", _fieldSetters, _setters)
	_AllFeesCta, _fieldSetters := NewAllFeesCta()
	_obj._AllFeesCta = _AllFeesCta
	helper.AddFieldSetters("allfeescta", _fieldSetters, _setters)
	_RewardsWorthCtaInfo, _fieldSetters := NewRewardsWorthCtaInfo()
	_obj._RewardsWorthCtaInfo = _RewardsWorthCtaInfo
	helper.AddFieldSetters("rewardsworthctainfo", _fieldSetters, _setters)
	_RewardsWorthInfo, _fieldSetters := NewRewardsWorthInfo()
	_obj._RewardsWorthInfo = _RewardsWorthInfo
	helper.AddFieldSetters("rewardsworthinfo", _fieldSetters, _setters)
	_FeesInfoV2, _fieldSetters := NewFessInfoV2()
	_obj._FeesInfoV2 = _FeesInfoV2
	helper.AddFieldSetters("feesinfov2", _fieldSetters, _setters)
	_JoiningFeeVoucherInfo, _fieldSetters := NewJoiningFeeVoucherInfo()
	_obj._JoiningFeeVoucherInfo = _JoiningFeeVoucherInfo
	helper.AddFieldSetters("joiningfeevoucherinfo", _fieldSetters, _setters)
	_AcceleratedRewardsInfo, _fieldSetters := NewAcceleratedRewardsInfo()
	_obj._AcceleratedRewardsInfo = _AcceleratedRewardsInfo
	helper.AddFieldSetters("acceleratedrewardsinfo", _fieldSetters, _setters)
	_WelcomeVoucherCta, _fieldSetters := NewCtaBlockInfoV2()
	_obj._WelcomeVoucherCta = _WelcomeVoucherCta
	helper.AddFieldSetters("welcomevouchercta", _fieldSetters, _setters)
	_TopBrandsCta, _fieldSetters := NewCtaBlockInfoV2()
	_obj._TopBrandsCta = _TopBrandsCta
	helper.AddFieldSetters("topbrandscta", _fieldSetters, _setters)
	_RewardEstimationCta, _fieldSetters := NewCtaBlockInfoV2()
	_obj._RewardEstimationCta = _RewardEstimationCta
	helper.AddFieldSetters("rewardestimationcta", _fieldSetters, _setters)
	return _obj, _setters
}

func NewCreditCardOffersScreenOptionsV2WithQuest(questFieldPath string) (_obj *CreditCardOffersScreenOptionsV2, _setters map[string]dynconf.SetFunc) {
	_obj = &CreditCardOffersScreenOptionsV2{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["creditlimitamountstring"] = _obj.SetCreditLimitAmountString
	_obj._CreditLimitAmountStringMutex = &sync.RWMutex{}
	_setters["cardimageurl"] = _obj.SetCardImageUrl
	_obj._CardImageUrlMutex = &sync.RWMutex{}
	_setters["termsandconditions"] = _obj.SetTermsAndConditions
	_obj._TermsAndConditionsMutex = &sync.RWMutex{}
	_setters["partnershipurl"] = _obj.SetPartnershipUrl
	_obj._PartnershipUrlMutex = &sync.RWMutex{}
	_setters["getcreditcardctatext"] = _obj.SetGetCreditCardCtaText
	_obj._GetCreditCardCtaTextMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["headerdescription"] = _obj.SetHeaderDescription
	_obj._HeaderDescriptionMutex = &sync.RWMutex{}
	_setters["feesinfoheader"] = _obj.SetFeesInfoHeader
	_obj._FeesInfoHeaderMutex = &sync.RWMutex{}
	_setters["broadvisualelementimgurl"] = _obj.SetBroadVisualElementImgUrl
	_obj._BroadVisualElementImgUrlMutex = &sync.RWMutex{}
	_setters["broadvisualelementimgurlios"] = _obj.SetBroadVisualElementImgUrlIOS
	_obj._BroadVisualElementImgUrlIOSMutex = &sync.RWMutex{}
	_setters["cta1name"] = _obj.SetCta1Name
	_obj._Cta1NameMutex = &sync.RWMutex{}
	_setters["cta2name"] = _obj.SetCta2Name
	_obj._Cta2NameMutex = &sync.RWMutex{}
	_setters["cta3name"] = _obj.SetCta3Name
	_obj._Cta3NameMutex = &sync.RWMutex{}
	_StaticImages, _fieldSetters := NewInfoItemWithQuest(questFieldPath + "/" + "StaticImages")
	_obj._StaticImages = _StaticImages
	helper.AddFieldSetters("staticimages", _fieldSetters, _setters)
	_FeesInfo, _fieldSetters := NewFeesInfoItemWithQuest(questFieldPath + "/" + "FeesInfo")
	_obj._FeesInfo = _FeesInfo
	helper.AddFieldSetters("feesinfo", _fieldSetters, _setters)
	_AllFeesCta, _fieldSetters := NewAllFeesCtaWithQuest(questFieldPath + "/" + "AllFeesCta")
	_obj._AllFeesCta = _AllFeesCta
	helper.AddFieldSetters("allfeescta", _fieldSetters, _setters)
	_RewardsWorthCtaInfo, _fieldSetters := NewRewardsWorthCtaInfoWithQuest(questFieldPath + "/" + "RewardsWorthCtaInfo")
	_obj._RewardsWorthCtaInfo = _RewardsWorthCtaInfo
	helper.AddFieldSetters("rewardsworthctainfo", _fieldSetters, _setters)
	_RewardsWorthInfo, _fieldSetters := NewRewardsWorthInfoWithQuest(questFieldPath + "/" + "RewardsWorthInfo")
	_obj._RewardsWorthInfo = _RewardsWorthInfo
	helper.AddFieldSetters("rewardsworthinfo", _fieldSetters, _setters)
	_FeesInfoV2, _fieldSetters := NewFessInfoV2WithQuest(questFieldPath + "/" + "FeesInfoV2")
	_obj._FeesInfoV2 = _FeesInfoV2
	helper.AddFieldSetters("feesinfov2", _fieldSetters, _setters)
	_JoiningFeeVoucherInfo, _fieldSetters := NewJoiningFeeVoucherInfoWithQuest(questFieldPath + "/" + "JoiningFeeVoucherInfo")
	_obj._JoiningFeeVoucherInfo = _JoiningFeeVoucherInfo
	helper.AddFieldSetters("joiningfeevoucherinfo", _fieldSetters, _setters)
	_AcceleratedRewardsInfo, _fieldSetters := NewAcceleratedRewardsInfoWithQuest(questFieldPath + "/" + "AcceleratedRewardsInfo")
	_obj._AcceleratedRewardsInfo = _AcceleratedRewardsInfo
	helper.AddFieldSetters("acceleratedrewardsinfo", _fieldSetters, _setters)
	_WelcomeVoucherCta, _fieldSetters := NewCtaBlockInfoV2WithQuest(questFieldPath + "/" + "WelcomeVoucherCta")
	_obj._WelcomeVoucherCta = _WelcomeVoucherCta
	helper.AddFieldSetters("welcomevouchercta", _fieldSetters, _setters)
	_TopBrandsCta, _fieldSetters := NewCtaBlockInfoV2WithQuest(questFieldPath + "/" + "TopBrandsCta")
	_obj._TopBrandsCta = _TopBrandsCta
	helper.AddFieldSetters("topbrandscta", _fieldSetters, _setters)
	_RewardEstimationCta, _fieldSetters := NewCtaBlockInfoV2WithQuest(questFieldPath + "/" + "RewardEstimationCta")
	_obj._RewardEstimationCta = _RewardEstimationCta
	helper.AddFieldSetters("rewardestimationcta", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *CreditCardOffersScreenOptionsV2) Init(questFieldPath string) {
	newObj, _ := NewCreditCardOffersScreenOptionsV2()
	*obj = *newObj
}
func (obj *CreditCardOffersScreenOptionsV2) InitWithQuest(questFieldPath string) {
	newObj, _ := NewCreditCardOffersScreenOptionsV2WithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *CreditCardOffersScreenOptionsV2) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._StaticImages.SetQuestSDK(questSdk)
	obj._FeesInfo.SetQuestSDK(questSdk)
	obj._AllFeesCta.SetQuestSDK(questSdk)
	obj._RewardsWorthCtaInfo.SetQuestSDK(questSdk)
	obj._RewardsWorthInfo.SetQuestSDK(questSdk)
	obj._FeesInfoV2.SetQuestSDK(questSdk)
	obj._JoiningFeeVoucherInfo.SetQuestSDK(questSdk)
	obj._AcceleratedRewardsInfo.SetQuestSDK(questSdk)
	obj._WelcomeVoucherCta.SetQuestSDK(questSdk)
	obj._TopBrandsCta.SetQuestSDK(questSdk)
	obj._RewardEstimationCta.SetQuestSDK(questSdk)
}

func (obj *CreditCardOffersScreenOptionsV2) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CreditCardOffersScreenOptionsV2) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CreditLimitAmountString",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CardImageUrl",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "TermsAndConditions",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "PartnershipUrl",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "GetCreditCardCtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "HeaderDescription",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "FeesInfoHeader",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "BroadVisualElementImgUrl",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "BroadVisualElementImgUrlIOS",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Cta1Name",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Cta2Name",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Cta3Name",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._StaticImages.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._FeesInfo.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._AllFeesCta.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._RewardsWorthCtaInfo.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._RewardsWorthInfo.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._FeesInfoV2.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._JoiningFeeVoucherInfo.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._AcceleratedRewardsInfo.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._WelcomeVoucherCta.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._TopBrandsCta.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	childVars, childVarsErr = obj._RewardEstimationCta.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	return vars, nil
}

func (obj *CreditCardOffersScreenOptionsV2) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CreditCardOffersScreenOptionsV2)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CreditCardOffersScreenOptionsV2) setDynamicField(v *config.CreditCardOffersScreenOptionsV2, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "creditlimitamountstring":
		return obj.SetCreditLimitAmountString(v.CreditLimitAmountString, true, nil)
	case "cardimageurl":
		return obj.SetCardImageUrl(v.CardImageUrl, true, nil)
	case "termsandconditions":
		return obj.SetTermsAndConditions(v.TermsAndConditions, true, nil)
	case "partnershipurl":
		return obj.SetPartnershipUrl(v.PartnershipUrl, true, nil)
	case "getcreditcardctatext":
		return obj.SetGetCreditCardCtaText(v.GetCreditCardCtaText, true, nil)
	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "headerdescription":
		return obj.SetHeaderDescription(v.HeaderDescription, true, nil)
	case "feesinfoheader":
		return obj.SetFeesInfoHeader(v.FeesInfoHeader, true, nil)
	case "broadvisualelementimgurl":
		return obj.SetBroadVisualElementImgUrl(v.BroadVisualElementImgUrl, true, nil)
	case "broadvisualelementimgurlios":
		return obj.SetBroadVisualElementImgUrlIOS(v.BroadVisualElementImgUrlIOS, true, nil)
	case "cta1name":
		return obj.SetCta1Name(v.Cta1Name, true, nil)
	case "cta2name":
		return obj.SetCta2Name(v.Cta2Name, true, nil)
	case "cta3name":
		return obj.SetCta3Name(v.Cta3Name, true, nil)
	case "staticimages":
		return obj._StaticImages.Set(v.StaticImages, true, path)
	case "feesinfo":
		return obj._FeesInfo.Set(v.FeesInfo, true, path)
	case "allfeescta":
		return obj._AllFeesCta.Set(v.AllFeesCta, true, path)
	case "rewardsworthctainfo":
		return obj._RewardsWorthCtaInfo.Set(v.RewardsWorthCtaInfo, true, path)
	case "rewardsworthinfo":
		return obj._RewardsWorthInfo.Set(v.RewardsWorthInfo, true, path)
	case "feesinfov2":
		return obj._FeesInfoV2.Set(v.FeesInfoV2, true, path)
	case "joiningfeevoucherinfo":
		return obj._JoiningFeeVoucherInfo.Set(v.JoiningFeeVoucherInfo, true, path)
	case "acceleratedrewardsinfo":
		return obj._AcceleratedRewardsInfo.Set(v.AcceleratedRewardsInfo, true, path)
	case "welcomevouchercta":
		return obj._WelcomeVoucherCta.Set(v.WelcomeVoucherCta, true, path)
	case "topbrandscta":
		return obj._TopBrandsCta.Set(v.TopBrandsCta, true, path)
	case "rewardestimationcta":
		return obj._RewardEstimationCta.Set(v.RewardEstimationCta, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CreditCardOffersScreenOptionsV2) setDynamicFields(v *config.CreditCardOffersScreenOptionsV2, dynamic bool, path []string) (err error) {

	err = obj.SetCreditLimitAmountString(v.CreditLimitAmountString, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCardImageUrl(v.CardImageUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTermsAndConditions(v.TermsAndConditions, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPartnershipUrl(v.PartnershipUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGetCreditCardCtaText(v.GetCreditCardCtaText, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHeaderDescription(v.HeaderDescription, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFeesInfoHeader(v.FeesInfoHeader, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBroadVisualElementImgUrl(v.BroadVisualElementImgUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBroadVisualElementImgUrlIOS(v.BroadVisualElementImgUrlIOS, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCta1Name(v.Cta1Name, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCta2Name(v.Cta2Name, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCta3Name(v.Cta3Name, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._StaticImages.Set(v.StaticImages, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeesInfo.Set(v.FeesInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AllFeesCta.Set(v.AllFeesCta, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsWorthCtaInfo.Set(v.RewardsWorthCtaInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsWorthInfo.Set(v.RewardsWorthInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeesInfoV2.Set(v.FeesInfoV2, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._JoiningFeeVoucherInfo.Set(v.JoiningFeeVoucherInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AcceleratedRewardsInfo.Set(v.AcceleratedRewardsInfo, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WelcomeVoucherCta.Set(v.WelcomeVoucherCta, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TopBrandsCta.Set(v.TopBrandsCta, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardEstimationCta.Set(v.RewardEstimationCta, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CreditCardOffersScreenOptionsV2) setStaticFields(v *config.CreditCardOffersScreenOptionsV2) error {

	return nil
}

func (obj *CreditCardOffersScreenOptionsV2) SetCreditLimitAmountString(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.CreditLimitAmountString", reflect.TypeOf(val))
	}
	obj._CreditLimitAmountStringMutex.Lock()
	defer obj._CreditLimitAmountStringMutex.Unlock()
	obj._CreditLimitAmountString = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CreditLimitAmountString")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetCardImageUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.CardImageUrl", reflect.TypeOf(val))
	}
	obj._CardImageUrlMutex.Lock()
	defer obj._CardImageUrlMutex.Unlock()
	obj._CardImageUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CardImageUrl")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetTermsAndConditions(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.TermsAndConditions", reflect.TypeOf(val))
	}
	obj._TermsAndConditionsMutex.Lock()
	defer obj._TermsAndConditionsMutex.Unlock()
	obj._TermsAndConditions = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "TermsAndConditions")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetPartnershipUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.PartnershipUrl", reflect.TypeOf(val))
	}
	obj._PartnershipUrlMutex.Lock()
	defer obj._PartnershipUrlMutex.Unlock()
	obj._PartnershipUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PartnershipUrl")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetGetCreditCardCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.GetCreditCardCtaText", reflect.TypeOf(val))
	}
	obj._GetCreditCardCtaTextMutex.Lock()
	defer obj._GetCreditCardCtaTextMutex.Unlock()
	obj._GetCreditCardCtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GetCreditCardCtaText")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetHeaderDescription(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.HeaderDescription", reflect.TypeOf(val))
	}
	obj._HeaderDescriptionMutex.Lock()
	defer obj._HeaderDescriptionMutex.Unlock()
	obj._HeaderDescription = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "HeaderDescription")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetFeesInfoHeader(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.FeesInfoHeader", reflect.TypeOf(val))
	}
	obj._FeesInfoHeaderMutex.Lock()
	defer obj._FeesInfoHeaderMutex.Unlock()
	obj._FeesInfoHeader = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FeesInfoHeader")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetBroadVisualElementImgUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.BroadVisualElementImgUrl", reflect.TypeOf(val))
	}
	obj._BroadVisualElementImgUrlMutex.Lock()
	defer obj._BroadVisualElementImgUrlMutex.Unlock()
	obj._BroadVisualElementImgUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BroadVisualElementImgUrl")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetBroadVisualElementImgUrlIOS(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.BroadVisualElementImgUrlIOS", reflect.TypeOf(val))
	}
	obj._BroadVisualElementImgUrlIOSMutex.Lock()
	defer obj._BroadVisualElementImgUrlIOSMutex.Unlock()
	obj._BroadVisualElementImgUrlIOS = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BroadVisualElementImgUrlIOS")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetCta1Name(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.Cta1Name", reflect.TypeOf(val))
	}
	obj._Cta1NameMutex.Lock()
	defer obj._Cta1NameMutex.Unlock()
	obj._Cta1Name = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Cta1Name")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetCta2Name(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.Cta2Name", reflect.TypeOf(val))
	}
	obj._Cta2NameMutex.Lock()
	defer obj._Cta2NameMutex.Unlock()
	obj._Cta2Name = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Cta2Name")
	}
	return nil
}
func (obj *CreditCardOffersScreenOptionsV2) SetCta3Name(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditCardOffersScreenOptionsV2.Cta3Name", reflect.TypeOf(val))
	}
	obj._Cta3NameMutex.Lock()
	defer obj._Cta3NameMutex.Unlock()
	obj._Cta3Name = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Cta3Name")
	}
	return nil
}

func NewRewardsWorthInfo() (_obj *RewardsWorthInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsWorthInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["subtitle"] = _obj.SetSubTitle
	_obj._SubTitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewRewardsWorthInfoWithQuest(questFieldPath string) (_obj *RewardsWorthInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsWorthInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["subtitle"] = _obj.SetSubTitle
	_obj._SubTitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *RewardsWorthInfo) Init(questFieldPath string) {
	newObj, _ := NewRewardsWorthInfo()
	*obj = *newObj
}
func (obj *RewardsWorthInfo) InitWithQuest(questFieldPath string) {
	newObj, _ := NewRewardsWorthInfoWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *RewardsWorthInfo) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *RewardsWorthInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RewardsWorthInfo) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "SubTitle",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *RewardsWorthInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RewardsWorthInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsWorthInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RewardsWorthInfo) setDynamicField(v *config.RewardsWorthInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "subtitle":
		return obj.SetSubTitle(v.SubTitle, true, nil)
	case "desc":
		return obj.SetDesc(v.Desc, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RewardsWorthInfo) setDynamicFields(v *config.RewardsWorthInfo, dynamic bool, path []string) (err error) {

	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSubTitle(v.SubTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc(v.Desc, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RewardsWorthInfo) setStaticFields(v *config.RewardsWorthInfo) error {

	return nil
}

func (obj *RewardsWorthInfo) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsWorthInfo.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *RewardsWorthInfo) SetSubTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsWorthInfo.SubTitle", reflect.TypeOf(val))
	}
	obj._SubTitleMutex.Lock()
	defer obj._SubTitleMutex.Unlock()
	obj._SubTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SubTitle")
	}
	return nil
}
func (obj *RewardsWorthInfo) SetDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsWorthInfo.Desc", reflect.TypeOf(val))
	}
	obj._DescMutex.Lock()
	defer obj._DescMutex.Unlock()
	obj._Desc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc")
	}
	return nil
}

func NewFessInfoV2() (_obj *FessInfoV2, _setters map[string]dynconf.SetFunc) {
	_obj = &FessInfoV2{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["subtitle"] = _obj.SetSubTitle
	_obj._SubTitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewFessInfoV2WithQuest(questFieldPath string) (_obj *FessInfoV2, _setters map[string]dynconf.SetFunc) {
	_obj = &FessInfoV2{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["subtitle"] = _obj.SetSubTitle
	_obj._SubTitleMutex = &sync.RWMutex{}
	_setters["desc"] = _obj.SetDesc
	_obj._DescMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *FessInfoV2) Init(questFieldPath string) {
	newObj, _ := NewFessInfoV2()
	*obj = *newObj
}
func (obj *FessInfoV2) InitWithQuest(questFieldPath string) {
	newObj, _ := NewFessInfoV2WithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *FessInfoV2) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *FessInfoV2) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FessInfoV2) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "SubTitle",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Desc",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *FessInfoV2) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FessInfoV2)
	if !ok {
		return fmt.Errorf("invalid data type %v *FessInfoV2", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FessInfoV2) setDynamicField(v *config.FessInfoV2, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "subtitle":
		return obj.SetSubTitle(v.SubTitle, true, nil)
	case "desc":
		return obj.SetDesc(v.Desc, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FessInfoV2) setDynamicFields(v *config.FessInfoV2, dynamic bool, path []string) (err error) {

	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSubTitle(v.SubTitle, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDesc(v.Desc, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FessInfoV2) setStaticFields(v *config.FessInfoV2) error {

	return nil
}

func (obj *FessInfoV2) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FessInfoV2.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *FessInfoV2) SetSubTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FessInfoV2.SubTitle", reflect.TypeOf(val))
	}
	obj._SubTitleMutex.Lock()
	defer obj._SubTitleMutex.Unlock()
	obj._SubTitle = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SubTitle")
	}
	return nil
}
func (obj *FessInfoV2) SetDesc(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FessInfoV2.Desc", reflect.TypeOf(val))
	}
	obj._DescMutex.Lock()
	defer obj._DescMutex.Unlock()
	obj._Desc = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Desc")
	}
	return nil
}

func NewCtaBlockInfoV2() (_obj *CtaBlockInfoV2, _setters map[string]dynconf.SetFunc) {
	_obj = &CtaBlockInfoV2{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	return _obj, _setters
}

func NewCtaBlockInfoV2WithQuest(questFieldPath string) (_obj *CtaBlockInfoV2, _setters map[string]dynconf.SetFunc) {
	_obj = &CtaBlockInfoV2{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["iconlink"] = _obj.SetIconLink
	_obj._IconLinkMutex = &sync.RWMutex{}
	_setters["title"] = _obj.SetTitle
	_obj._TitleMutex = &sync.RWMutex{}
	_setters["ctatext"] = _obj.SetCtaText
	_obj._CtaTextMutex = &sync.RWMutex{}
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *CtaBlockInfoV2) Init(questFieldPath string) {
	newObj, _ := NewCtaBlockInfoV2()
	*obj = *newObj
}
func (obj *CtaBlockInfoV2) InitWithQuest(questFieldPath string) {
	newObj, _ := NewCtaBlockInfoV2WithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *CtaBlockInfoV2) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *CtaBlockInfoV2) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CtaBlockInfoV2) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "IconLink",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Title",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "CtaText",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_STRING},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *CtaBlockInfoV2) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CtaBlockInfoV2)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfoV2", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CtaBlockInfoV2) setDynamicField(v *config.CtaBlockInfoV2, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "iconlink":
		return obj.SetIconLink(v.IconLink, true, nil)
	case "title":
		return obj.SetTitle(v.Title, true, nil)
	case "ctatext":
		return obj.SetCtaText(v.CtaText, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CtaBlockInfoV2) setDynamicFields(v *config.CtaBlockInfoV2, dynamic bool, path []string) (err error) {

	err = obj.SetIconLink(v.IconLink, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTitle(v.Title, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCtaText(v.CtaText, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CtaBlockInfoV2) setStaticFields(v *config.CtaBlockInfoV2) error {

	return nil
}

func (obj *CtaBlockInfoV2) SetIconLink(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfoV2.IconLink", reflect.TypeOf(val))
	}
	obj._IconLinkMutex.Lock()
	defer obj._IconLinkMutex.Unlock()
	obj._IconLink = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IconLink")
	}
	return nil
}
func (obj *CtaBlockInfoV2) SetTitle(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfoV2.Title", reflect.TypeOf(val))
	}
	obj._TitleMutex.Lock()
	defer obj._TitleMutex.Unlock()
	obj._Title = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Title")
	}
	return nil
}
func (obj *CtaBlockInfoV2) SetCtaText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CtaBlockInfoV2.CtaText", reflect.TypeOf(val))
	}
	obj._CtaTextMutex.Lock()
	defer obj._CtaTextMutex.Unlock()
	obj._CtaText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CtaText")
	}
	return nil
}

func NewDisabledOnboardingScreenOptions() (_obj *DisabledOnboardingScreenOptions, _setters map[string]dynconf.SetFunc) {
	_obj = &DisabledOnboardingScreenOptions{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["text"] = _obj.SetText
	_obj._TextMutex = &sync.RWMutex{}
	_setters["subtext"] = _obj.SetSubText
	_obj._SubTextMutex = &sync.RWMutex{}
	_setters["icon"] = _obj.SetIcon
	_obj._IconMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *DisabledOnboardingScreenOptions) Init() {
	newObj, _ := NewDisabledOnboardingScreenOptions()
	*obj = *newObj
}

func (obj *DisabledOnboardingScreenOptions) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DisabledOnboardingScreenOptions) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DisabledOnboardingScreenOptions)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisabledOnboardingScreenOptions", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DisabledOnboardingScreenOptions) setDynamicField(v *config.DisabledOnboardingScreenOptions, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "text":
		return obj.SetText(v.Text, true, nil)
	case "subtext":
		return obj.SetSubText(v.SubText, true, nil)
	case "icon":
		return obj.SetIcon(v.Icon, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DisabledOnboardingScreenOptions) setDynamicFields(v *config.DisabledOnboardingScreenOptions, dynamic bool, path []string) (err error) {

	err = obj.SetText(v.Text, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSubText(v.SubText, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIcon(v.Icon, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DisabledOnboardingScreenOptions) setStaticFields(v *config.DisabledOnboardingScreenOptions) error {

	return nil
}

func (obj *DisabledOnboardingScreenOptions) SetText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisabledOnboardingScreenOptions.Text", reflect.TypeOf(val))
	}
	obj._TextMutex.Lock()
	defer obj._TextMutex.Unlock()
	obj._Text = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Text")
	}
	return nil
}
func (obj *DisabledOnboardingScreenOptions) SetSubText(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisabledOnboardingScreenOptions.SubText", reflect.TypeOf(val))
	}
	obj._SubTextMutex.Lock()
	defer obj._SubTextMutex.Unlock()
	obj._SubText = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SubText")
	}
	return nil
}
func (obj *DisabledOnboardingScreenOptions) SetIcon(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *DisabledOnboardingScreenOptions.Icon", reflect.TypeOf(val))
	}
	obj._IconMutex.Lock()
	defer obj._IconMutex.Unlock()
	obj._Icon = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Icon")
	}
	return nil
}

func NewUnsecuredCCRenewalFeeReversalConfig() (_obj *UnsecuredCCRenewalFeeReversalConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UnsecuredCCRenewalFeeReversalConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ineligibleuserssegmentid"] = _obj.SetIneligibleUsersSegmentId
	_obj._IneligibleUsersSegmentIdMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *UnsecuredCCRenewalFeeReversalConfig) Init() {
	newObj, _ := NewUnsecuredCCRenewalFeeReversalConfig()
	*obj = *newObj
}

func (obj *UnsecuredCCRenewalFeeReversalConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UnsecuredCCRenewalFeeReversalConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UnsecuredCCRenewalFeeReversalConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UnsecuredCCRenewalFeeReversalConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UnsecuredCCRenewalFeeReversalConfig) setDynamicField(v *config.UnsecuredCCRenewalFeeReversalConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ineligibleuserssegmentid":
		return obj.SetIneligibleUsersSegmentId(v.IneligibleUsersSegmentId, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UnsecuredCCRenewalFeeReversalConfig) setDynamicFields(v *config.UnsecuredCCRenewalFeeReversalConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIneligibleUsersSegmentId(v.IneligibleUsersSegmentId, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UnsecuredCCRenewalFeeReversalConfig) setStaticFields(v *config.UnsecuredCCRenewalFeeReversalConfig) error {

	return nil
}

func (obj *UnsecuredCCRenewalFeeReversalConfig) SetIneligibleUsersSegmentId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *UnsecuredCCRenewalFeeReversalConfig.IneligibleUsersSegmentId", reflect.TypeOf(val))
	}
	obj._IneligibleUsersSegmentIdMutex.Lock()
	defer obj._IneligibleUsersSegmentIdMutex.Unlock()
	obj._IneligibleUsersSegmentId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IneligibleUsersSegmentId")
	}
	return nil
}

func NewFireflyV2Config() (_obj *FireflyV2Config, _setters map[string]dynconf.SetFunc) {
	_obj = &FireflyV2Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["onboardingretrycooloff"] = _obj.SetOnboardingRetryCoolOff
	_setters["ccineligiblesegments"] = _obj.SetCcIneligibleSegments
	_obj._CcIneligibleSegmentsMutex = &sync.RWMutex{}
	_IntroScreenV2Config, _fieldSetters := NewIntroScreenV2Config()
	_obj._IntroScreenV2Config = _IntroScreenV2Config
	helper.AddFieldSetters("introscreenv2config", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *FireflyV2Config) Init() {
	newObj, _ := NewFireflyV2Config()
	*obj = *newObj
}

func (obj *FireflyV2Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FireflyV2Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FireflyV2Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *FireflyV2Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FireflyV2Config) setDynamicField(v *config.FireflyV2Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "onboardingretrycooloff":
		return obj.SetOnboardingRetryCoolOff(v.OnboardingRetryCoolOff, true, nil)
	case "ccineligiblesegments":
		return obj.SetCcIneligibleSegments(v.CcIneligibleSegments, true, path)
	case "introscreenv2config":
		return obj._IntroScreenV2Config.Set(v.IntroScreenV2Config, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FireflyV2Config) setDynamicFields(v *config.FireflyV2Config, dynamic bool, path []string) (err error) {

	err = obj.SetOnboardingRetryCoolOff(v.OnboardingRetryCoolOff, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCcIneligibleSegments(v.CcIneligibleSegments, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IntroScreenV2Config.Set(v.IntroScreenV2Config, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FireflyV2Config) setStaticFields(v *config.FireflyV2Config) error {

	return nil
}

func (obj *FireflyV2Config) SetOnboardingRetryCoolOff(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *FireflyV2Config.OnboardingRetryCoolOff", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._OnboardingRetryCoolOff, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "OnboardingRetryCoolOff")
	}
	return nil
}
func (obj *FireflyV2Config) SetCcIneligibleSegments(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FireflyV2Config.CcIneligibleSegments", reflect.TypeOf(val))
	}
	obj._CcIneligibleSegmentsMutex.Lock()
	defer obj._CcIneligibleSegmentsMutex.Unlock()
	obj._CcIneligibleSegments = roarray.New[string](v)
	return nil
}

func NewIntroScreenV2Config() (_obj *IntroScreenV2Config, _setters map[string]dynconf.SetFunc) {
	_obj = &IntroScreenV2Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["introscreenv2loaderanimationheight"] = _obj.SetIntroScreenV2LoaderAnimationHeight
	_setters["introscreenv2loaderanimationwidth"] = _obj.SetIntroScreenV2LoaderAnimationWidth
	_setters["introscreenv2bgimageheight"] = _obj.SetIntroScreenV2BgImageHeight
	_setters["introscreenv2bgimagewidth"] = _obj.SetIntroScreenV2BgImageWidth
	_setters["introscreenv2loaderanimation"] = _obj.SetIntroScreenV2LoaderAnimation
	_obj._IntroScreenV2LoaderAnimationMutex = &sync.RWMutex{}
	_setters["introscreenv2bgimage"] = _obj.SetIntroScreenV2BgImage
	_obj._IntroScreenV2BgImageMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *IntroScreenV2Config) Init() {
	newObj, _ := NewIntroScreenV2Config()
	*obj = *newObj
}

func (obj *IntroScreenV2Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IntroScreenV2Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IntroScreenV2Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntroScreenV2Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IntroScreenV2Config) setDynamicField(v *config.IntroScreenV2Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "introscreenv2loaderanimationheight":
		return obj.SetIntroScreenV2LoaderAnimationHeight(v.IntroScreenV2LoaderAnimationHeight, true, nil)
	case "introscreenv2loaderanimationwidth":
		return obj.SetIntroScreenV2LoaderAnimationWidth(v.IntroScreenV2LoaderAnimationWidth, true, nil)
	case "introscreenv2bgimageheight":
		return obj.SetIntroScreenV2BgImageHeight(v.IntroScreenV2BgImageHeight, true, nil)
	case "introscreenv2bgimagewidth":
		return obj.SetIntroScreenV2BgImageWidth(v.IntroScreenV2BgImageWidth, true, nil)
	case "introscreenv2loaderanimation":
		return obj.SetIntroScreenV2LoaderAnimation(v.IntroScreenV2LoaderAnimation, true, nil)
	case "introscreenv2bgimage":
		return obj.SetIntroScreenV2BgImage(v.IntroScreenV2BgImage, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IntroScreenV2Config) setDynamicFields(v *config.IntroScreenV2Config, dynamic bool, path []string) (err error) {

	err = obj.SetIntroScreenV2LoaderAnimationHeight(v.IntroScreenV2LoaderAnimationHeight, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIntroScreenV2LoaderAnimationWidth(v.IntroScreenV2LoaderAnimationWidth, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIntroScreenV2BgImageHeight(v.IntroScreenV2BgImageHeight, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIntroScreenV2BgImageWidth(v.IntroScreenV2BgImageWidth, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIntroScreenV2LoaderAnimation(v.IntroScreenV2LoaderAnimation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIntroScreenV2BgImage(v.IntroScreenV2BgImage, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IntroScreenV2Config) setStaticFields(v *config.IntroScreenV2Config) error {

	return nil
}

func (obj *IntroScreenV2Config) SetIntroScreenV2LoaderAnimationHeight(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntroScreenV2Config.IntroScreenV2LoaderAnimationHeight", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._IntroScreenV2LoaderAnimationHeight, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IntroScreenV2LoaderAnimationHeight")
	}
	return nil
}
func (obj *IntroScreenV2Config) SetIntroScreenV2LoaderAnimationWidth(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntroScreenV2Config.IntroScreenV2LoaderAnimationWidth", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._IntroScreenV2LoaderAnimationWidth, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IntroScreenV2LoaderAnimationWidth")
	}
	return nil
}
func (obj *IntroScreenV2Config) SetIntroScreenV2BgImageHeight(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntroScreenV2Config.IntroScreenV2BgImageHeight", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._IntroScreenV2BgImageHeight, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IntroScreenV2BgImageHeight")
	}
	return nil
}
func (obj *IntroScreenV2Config) SetIntroScreenV2BgImageWidth(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntroScreenV2Config.IntroScreenV2BgImageWidth", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._IntroScreenV2BgImageWidth, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "IntroScreenV2BgImageWidth")
	}
	return nil
}
func (obj *IntroScreenV2Config) SetIntroScreenV2LoaderAnimation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntroScreenV2Config.IntroScreenV2LoaderAnimation", reflect.TypeOf(val))
	}
	obj._IntroScreenV2LoaderAnimationMutex.Lock()
	defer obj._IntroScreenV2LoaderAnimationMutex.Unlock()
	obj._IntroScreenV2LoaderAnimation = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IntroScreenV2LoaderAnimation")
	}
	return nil
}
func (obj *IntroScreenV2Config) SetIntroScreenV2BgImage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IntroScreenV2Config.IntroScreenV2BgImage", reflect.TypeOf(val))
	}
	obj._IntroScreenV2BgImageMutex.Lock()
	defer obj._IntroScreenV2BgImageMutex.Unlock()
	obj._IntroScreenV2BgImage = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IntroScreenV2BgImage")
	}
	return nil
}
