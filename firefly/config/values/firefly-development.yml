Application:
  Environment: "development"
  Name: "firefly"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

EpifiDb:
  Host: "localhost"
  Port: 26257
  DbType: "CRDB"
  Username: "root"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CreditCardDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  DbType: "CRDB"
  Password: ""
  Name: "credit_card"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - TableName: [ ]
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          Host: "localhost"
          Port: 5432
          DbType: "PGDB"
          AppName: "firefly"
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "{\"username\": \"root\", \"password\": \"\"}"

CreditCardPgDb:
  Host: "localhost"
  Port: 5432
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CreditCardFederalPgDb:
  Host: "localhost"
  Port: 5432
  Name: "credit_card_federal"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    ClientEncryptionKey: "ca978112ca1bbdcafac231b39a23dc4da786eff8147c4e72b9807785afee48bb"
    ClientEncryptionInitialisationVector: "87c03fb34a523cde5d9533d82f916bb2"
    MctClientEncryptionKey: "ca978112ca1bbdcafac231b39a23dc4da786eff8147c4e72b9807785afee48bb"
    MctClientEncryptionInitialisationVector: "87c03fb34a523cde5d9533d82f916bb2"
    VendorCardPinEncryptionKey: "C1FB4064FEF0F9D5EAAAD890D1BC2B0A"
    ClientPinRsaPrivateKey: |
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    StartreePinotAuthToken: "{\"AuthToken\": \"\"}"
    EpifiSeshaasaiPgpPrivateKey: |
      -----BEGIN PGP PRIVATE KEY BLOCK-----
      Version: BCPG v1.58

      lQIGBGVSVBUBBADSSH6crqsSKHBbAAgn7iTz9w9OzCk6OkJdTEvvnbJde3OpPpZm
      ZvMZPYbtGziUgQSphC6k6PlGH0kRQcCTnqeUGRDgcfkI+GIlaJds3TKoxNZqwuy7
      bckRFSpL10g/mQRZiMjHzaUocM1Pjj/NvSGkb1wjCHVyjzs+pDntSZGgTwARAQAB
      /gkDAibnVzF3WojxYA3Mz2eJPfnuFv2yIqZYXMMxTf3Rdw+20PB64kzpSMl0qg6R
      HAtXs4z78oGkaUs6okC9tfkOMvp0lvStiHgzzLIzMzRiO3GVcjCgPvUjDTCTbweD
      GI7aY695ViaFQU2W1FIRarHov3HOOoGOpob1fofQfU0Bn2kagw7QhKyrNuX/Eq86
      8w03RE6CcD74KB4eKfslxk2+g+ce1TDvQoJp8465KmfNSc2DHJQabEjXP7ifvc2d
      szR7nXaPCJDLf02Waz0179yyz7SOWSIwIBrZI9yC1j8GOuC7b5VLx7FNo6dbSv1J
      8+MdkTZvgQwq6rll38B/xQtDGAt9psp3YXKvnAYsyBrk62sXOVmgfbYv3v7wMKR5
      vK97kr/Ku+hGIbAeb3PBvczEa1q8PfoEjzdlFAIW9kjHZaeJrhmtg4zkaznrgB6B
      7LEfc5iXGbo6acK5pudX10blTbVkHpsAdWQbX+krUq5c6MJ7SmiKEia0CUNoYW5k
      cmVzaIicBBABAgAGBQJlUlQVAAoJEOMtQvTFbouuYmMEAMiadxS020gDeOf4bkxv
      MxpVHD/5O/MIXOFixbvVTMa6OdpSNfbhD/LcDAlB3+C5vNeTBMXux0MP3uHYICn6
      lqlFZc6MmAhk3TF/DsD39rovzA/E+AV7hFLsQhBap1dYEhE7YhknaVdCtQXtRnJP
      ef5kAtmvgji8Rmju/iMZsWV7
      =y1lq
      -----END PGP PRIVATE KEY BLOCK-----
    SeshaasaiPgpPublicKey: |
      -----BEGIN PGP PUBLIC KEY BLOCK-----
      Version: BCPG v1.58

      mI0EZVJUFQEEANJIfpyuqxIocFsACCfuJPP3D07MKTo6Ql1MS++dsl17c6k+lmZm
      8xk9hu0bOJSBBKmELqTo+UYfSRFBwJOep5QZEOBx+Qj4YiVol2zdMqjE1mrC7Ltt
      yREVKkvXSD+ZBFmIyMfNpShwzU+OP829IaRvXCMIdXKPOz6kOe1JkaBPABEBAAG0
      CUNoYW5kcmVzaIicBBABAgAGBQJlUlQVAAoJEOMtQvTFbouuYmMEAMiadxS020gD
      eOf4bkxvMxpVHD/5O/MIXOFixbvVTMa6OdpSNfbhD/LcDAlB3+C5vNeTBMXux0MP
      3uHYICn6lqlFZc6MmAhk3TF/DsD39rovzA/E+AV7hFLsQhBap1dYEhE7YhknaVdC
      tQXtRnJPef5kAtmvgji8Rmju/iMZsWV7
      =ES8J
      -----END PGP PUBLIC KEY BLOCK-----
    EpifiSeshaasaiPgpPassphrase: "query123"
    EpifiFederalPgpPassphrase: "qwerty1234"
    EpifiFederalPgpPrivateKey: |
      -----BEGIN PGP PRIVATE KEY BLOCK-----

      lQdGBF5qBvgBEADGijefHuDTXqiReXdZcxbknDmueW5XGWzR0EHec9kRYkxcQy+H
      gY/q7RJ+5VNNM8KfWtdM+K2zoTdkv6uIp6eYEcC6YcRV1MeX6KbxGGpMzRmb5f0c
      d+gePS1WOeOWjjS+3AXmH0DO+v8bXDxAullL8L0jDYK1sRxWlu9tdwglyJx7IXQi
      VibCaSfrNDrTI7HmrLw8PZ/rfHq5prhEQ9cCeI+kHKz2Amiuts8PGl5M1MEx2oC8
      nSMtb3YXg32FiYkyOi/vXZ5jmltcR9PyhJnMkNFrZRcBxU5dWhB/CpD/IebaKbir
      KuH2Ig/0d8PvvLlyILMyto5C2hfho2QWbFisMuSF23x798CYrFqLBDjFersDDfIW
      4yKsMD+2J+9T/7nBUYdIUmCEmg+khtIVanpJnQQP+iqb8NB7oTevBXKaIST95NhK
      b7B6J9/IDy0xFyPFPtXQyHSoufAjJPfNIgFb8cMTIuQEfN402Fp6eH5NwlvECFWy
      y8S00VFxr8mHXZ1zmvYK6Kp3OLR+q0tpUKmWu6KR4UdKY2Lce7ciuFVGlw1ufBgE
      nxlJNguLDCD23aod78QTWI6ubnCRKTdDLoR7H3CRkIQYzQme3xKJRHdEBfEOioTd
      6+rc1VHp1iLlo7uAlk01wGVdwRmO3deSi1y367TWXy1tyFscOvlGuF6r9QARAQAB
      /gcDAo8iYHB6tWca6/vYjx8IhXmfges2Lti8I805xB7+Ooilxat4ZtBUQHjMGm36
      CPTHwxpca4HfrGIYkCPQ7RmIQTYJBir1NWciD/Dvk0F3TeBicXz9Qzg8nsIsmNug
      SUgHFmbUshT9hMYXAp6no4a+0vJhOH+Wg5CbXV++fl6VhxOoesftfoenJ1JJ2G4Y
      HWElJWuaY4ozf5APC9JrC4KKyjJpZFWg7GoHjyPi6DYNOP1UhXghB0r2biokoJJt
      HErCBXpH+yA2/8L/j0tfDT31IiP/Ge0m2mFIKt6SpBzkbyeY5Pr/ZfMIt4rPwsf8
      2O2B1aL5JF358cKsv4DGGjIv1G3joqyYbrub8j8zL0shYmiBfPeLXdW39NQxplxy
      BDiSCSMRa3IV3J7YztR87GEeKbcqS6gY0sVQB9HrkNTxqh9DG0kNwovZRhGAbK1+
      +1ctahA+XEWghkXF2U2VBJjT42OgzYtgAkyD1+vPeThEKfzOxMARGcavuURX+kiI
      e19MLPuFdJhcZ8pOUv3Y3Otpa5BgsjBUWObGVwUFe1XJ9Zhe+JOIg2QUj35kVReR
      NiyvYrh6QHxnlG15HAgpica3a1gEnxf6M5nXP2XOsmGX+SKzlnBtvkxCy9+3zopJ
      S3NReIK52YWX/NPTK4rKahbv+17xFyRk5msmxsxVO1X3nZbGRgy+49Uv3D8fc7JD
      u3hnpj0DgIaQXKGv/vlEqFtHUsonABXG9ylPvsTJ4I7gbUbmpLV1t8LmF5x4xFfw
      n0O++iBRUqzkLi2+6e5ZPTna24F8hWkAq6k++nVKFCSz2NoDcTwrjLRM/fkkqJe3
      g2X1tug7x0B+/MwbY0IJwhEaHbZpeGVuXr+XIEs2uleWZ3kiJJYHSyRHHpcc/IxQ
      6N4gflA/tZKwFuBBBnIRXt97m2WseTGcxF+lAuVbCS7mB2PFscYfkQVEaq7enHrN
      Cx62RlIf8ZnmeeIVmU9VylFOy4BlFcg4lwjuY2cgoX+q/zNwkhaoGRXlp039CjFW
      0R6OMV2iY3tUDpQyTki9lc4seSIKyN+LOEd74R6mHZEKNpuDJFmGDla5GDPW9ZP3
      s57zvRXDOjgrxsQNcZUcF/cmaIc+pdVzgs32P/ojaCW5h7ncr66hxms4VBvi1JVD
      N3Q74BAcHbyJ/FYVW1lgHSzDVXJL8EqctV6/HGg9e+IRRgYBIf0q4fUWURrIes6d
      /TQ2S1O/u536ncDTzhuh7MoC5UUv97Cmjovk5iLbdsYXUapJKMZX+duDw2LhOUzy
      kukrLShRR8BQ4aQndStLBGzorVjMOrPMFZQivwmwFEB3n4rXBeG4YtMAUvOZeZvM
      tMvD3gKh+0hkRgYRuQi0ZKBb3OSaw7O/Dvb+68wUMlPfoK+i1x5TIx4wY5ws3A37
      XM3i10aFbWouk84RZ5ntF4aDcFZ+r/T1BkzHc7e4HrcuYz3nAudxx+zqkjCM/MZv
      4dmTnw5ODcN97tXhnxaLcRVT9M+2inMOtuSVYa48y7AbyR/cSk9N46CVpTwwioiI
      5fpvHCTGy52m5HxXVwwNXqliWESGc5PefoN5IoDUzX5J6RVFcDw7n0bTNBbvxFHT
      mbsu2w+biEmQXqovAIW0YZK0Xnnu4J0DwKCb+8loxnNOTzANyfkUa2eskjZ7H2Q6
      J4aq7sqH/lAQU439uHeuCPmzwK2jlDmrUhO1SgJyHglP/tnSPkk+rCEivqIawqgr
      ATFhWgNUXP9zPBHejhiNFOO65AXVTivAmlFkZm6+di6XBD9lCF5dtP+0I0F1dG9n
      ZW5lcmF0ZWQgS2V5IDxhbmFuZEBlcGlmaS5jb20+iQJOBBMBCAA4FiEEhUtW2MRo
      EDU7awr4z4B1fb7ViawFAl5qBvgCGy8FCwkIBwIGFQoJCAsCBBYCAwECHgECF4AA
      CgkQz4B1fb7Viaw0RBAAtLprII+/f3u53627dzrbyIE/z19ih34v/WVsDiMpyUYK
      qgXvL9a0P8dP+EO/rERxH9jbt5KtJ1zE4bGoT7zEQr/a36gwAu5DwaFInaPTrqPZ
      5FL/xytReUcjuPb5W6qWF2mO6iSd3ZwQbo/ZNjqgGIem+ouPfPtyyz7mdsM42FRF
      ANZ6JWSpqpuEl1l1GNFfVEmJMM0SgfsSm2XClGgTHQ2hyRyH0zcBoojjOigqkEHd
      ytM3MVovn2z2+HjqSU1GMCT21dRKZnV/FkN8GHW4ITOUd2X1Z4eBt7y8/n/WIFbx
      uwbSRtYKULe5LjgMA+Cv6bUBRqruUOdW52iFpxU0Eyr6+T2kEJeXtyWkGA297B5W
      LtCWRPvQAdoj8Qxky2ch5zyrKfSPwpPF3o/HcPvodk5hYXkhE/AHNqqu0ORmtevj
      20pbH84yUfjJEKJbLKu8MEuKysxSgNN00gHmjVEJa98Cl+qMAdzNBpDiJTlriM6y
      uwychPPrgZoe98YKy2/xRsZ2s/BPRazqllwZ8i+x/Ks8iYC+lSrmGlAtZulwOS8g
      +Flz7yPuYZB9IXNRyd7dZNRlxrGlcD1VHUkgyoZvL3EgpKtUofaNJ1WYtXigEqe8
      aLci0kOkZjjhA9FhikU3VUf+jlLKR5sGx2xWPZAnASrZU8/pIGI6sUsxWmSBHvg=
      =c5Qs
      -----END PGP PRIVATE KEY BLOCK-----
    FederalPgpPublicKey: |
      -----BEGIN PGP PUBLIC KEY BLOCK-----
      Version: GnuPG v2.0.9 (GNU/Linux)

      mQGiBF5yD5YRBACD6VQEWVX4RrEaeE3eLG82bYVaVFNHZJ+NvMsxiIitaLoeW6OQ
      4LJWd4+Vd3ORD3wr3ARn/45IP+TznjTDwUG1YoOhXwTGaDQtBBnOq8srYXktjoT9
      RHkxB7UbDMcK8N4/37grQ7VAopZT9BYDvnPutoVlOvbHiGVBosjrpk6/WwCgkNgZ
      9/geKpYpcWsgd51vmsmO4j0D/01aGvZUGB4LZ2wdpRvG4H0+Os+zExyoSi056fx/
      4KKeOTJlMSgcjN7/pT/LmZNR0TkCZTiMy9hYel/Ng7V3sabAu6HEM2YtUQV5WgjQ
      Lckr+y1API96qVi7SwtTLd3AKwO4HRVOt65sf+SCkoTMa4Q9QkkloEGHB6VKWAqQ
      1tQRA/oCDd8VNwR0wZOBsRj9y2vCqeqQFwcdh+8FgtAUVMD5wQ/BPfIQI+nqCQnM
      FeaecgII1IRbmxh8FhE0kB7lAmlRBLaxbAatoXGgdOJd9cg3pYLNCYOPnDUH+loG
      kgX7Dt+tXPJqBLTFt8NX70oRj+F4sFSCn/68FnZnd9p3lfshzbQ7RmVkZXJhbEVw
      ****************************************************************
      bj6IYAQTEQIAIAUCXnIPlgIbAwYLCQgHAwIEFQIIAwQWAgMBAh4BAheAAAoJEJes
      47GvuPWqm5UAnjZPYlkjPLxjzNv+7ZKoWT1Ib/O8AJ4uRV8b0UlGlHUNdhskVGgs
      hzb097kCDQRecg+WEAgArxNKk0ZftEgjE88SpZ22jbIzAZkQqMCtJQ1PZQcjW6g1
      HNAPaiascY5S/x2AMJ7ONOx72iAVyNd8bMcGSS1rYPrACbnxvVT8H7Siydy/XF5p
      KREF1UFW/u3kp7ZRC5GObMagf/Hn6iPUqoE5mdFZIScOlAUC6z1opYs5D4mFmcAT
      yDyuWRZibJcka+xJPchu2IVGZfMPsT8nmlMhI8ujaI0iGJJvNXKAs+1t1sAvlYAz
      6exUjPx9xPg+0J4aE1sLrQz/oStkh2+BXGBRe2wPY5PPsBGMGx72qx1dD/lOAAhh
      OwJteaUJSXtGbojrLKBCDAvuhsmEbBF50/z5Q0AalwADBQf/VW66HEfEtrMjVz5Q
      4YwCcLrd/PWLy2I9kYM9idrWpgTtv6kCTt0d1b9tXsVUYyGd3gcTBp9kxuLIEG96
      IWXv4WTjMLZWfBNwiyrP3k5v0z0wnILXQ7Tu7CKukgNpFWDFSCTXTQh9co1eLHrQ
      Z5S2u16KzHeRCd3FZ8BYO8a40PD46sZZuk+9pROgdG9U5T1Ws1i9+Sn5pK7TdK8T
      rx9oUesqpRMQNXp+sSlSCY9cmSOzPf6HbHQOQ9+lE5V9VAQcyAvri5nNB0fNrKj3
      Z7ddPsLh6tnWB/aVwbR+Itp1zw3jmfQdfBjfkFZ7DEUae86SGKmigJSEVs4u8KoX
      JbgKAIhJBBgRAgAJBQJecg+WAhsMAAoJEJes47GvuPWqG0UAn0bDsLK2QBuCqQSb
      Bi6ZSFfKvc2QAKCFEw53PkKS4PsO4n1b+rggZ6ZE0A==
      =HS45
      -----END PGP PUBLIC KEY BLOCK-----

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

SignalWorkflowPublisher:
  QueueName: "celestial-signal-workflow-queue"

CCNonFinancialNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-non-financial-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ProcessCreditCardOfferFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "process-cc-offers-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCTransactionNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-txn-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCAcsNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-acs-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CardsDispatchedCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-cards-dispatched-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CardsSentForPrintingCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-cards-sent-for-printing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCTransactionEventPublisher:
  TopicName: "cc-transaction-event-topic"

TransactionsProducer:
  StreamName: "credit-card-pinot-transactions-stream"

CCTransactionsForPinotSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-transactions-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CategorizerUpdateForPinotSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-categorizer-update-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCAuthFactorUpdateNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "credit-card-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCCreditReportDownloadEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-credit-report-download-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCStatementNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-statement-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCOnboardingStateUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "cc-onboarding-state-update-event-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

CardsSentForPrintingBucketName: "epifi-dev-creditcard-m2p-upload"
CardsDispatchedBucketName: "epifi-dev-creditcard-seshaasai-upload"
AcsBucketName : "epifi-dev-cc-acs-notification"
AcsNotificationRawDataStore:
  BucketName: "epifi-raw-dev"
  ObjectKey: "development/data/vendor/m2p_reports/acs_notifications/%s/%s-AcsNotification.csv"

FeatureReleaseConfig:
  FeatureConstraints:
    FEATURE_CREDIT_CARD_V2_FLOW:
      AppVersionConstraintConfig:
        MinAndroidVersion: 1000
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageAndroid: 100
          RolloutPercentageIOS: 100
      UserGroupConstraintConfig:
        AllowedUserGrp:
          - 17 # CREDIT_CARD_INTERNAL
    FEATURE_CC_REALTIME_ELIGIBILITY:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_AMPLIFI_CC_MINUTES:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_ONBOARDING_WITHOUT_BILL_GEN_DATE_CAPTURE_STAGE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_BIOMETRIC_REVALIDATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 200
        MinIOSVersion: 200
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_INTRO_SCREEN_HORIZONTAL_LAYOUT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    CREDIT_REPORT_ADDRESS_SELECTION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
    FEATURE_CC_PREAPPROVED_AMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_AMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_PREAPPROVED_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_PREAPPROVED_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.demo-common-cache-redis.wqltco.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: ff-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

FireflyRedisStore:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: ""
    DB: 0
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

CardProgramToSegmentIdMap:
  CARD_PROGRAM_TYPE_UNSECURED:
    CARD_PROGRAM_SOURCE_PREAPPROVED: "unsecured_seg_id"
    CARD_PROGRAM_SOURCE_FI_BRE_APPROVED: "unsecured_seg_id"
  CARD_PROGRAM_TYPE_MASS_UNSECURED:
    CARD_PROGRAM_SOURCE_PREAPPROVED: "dummy_segment_id"
    CARD_PROGRAM_SOURCE_FI_BRE_APPROVED: "dummy_segment_id"

CreditCardCacheConfig:
  CreditCardOfferCacheConfig:
    Prefix: "creditCardOfferId:"
    IsCachingEnabled: true
    CacheTTl: "30m"
  CardRequestCacheConfig:
    Prefix: "creditCardRequestId:"
    IsCachingEnabled: true
    CacheTTl: "30m"

CcOnboardingStateUpdateEventPublisher:
  QueueName: "cc-onboarding-state-update-event-callback-queue"
