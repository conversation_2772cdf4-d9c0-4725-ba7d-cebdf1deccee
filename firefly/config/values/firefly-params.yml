Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/lending/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

SecureLogging:
  EnableSecureLog: true
  SecureLogPath: "/var/log/lending/secure.log"
  MaxSizeInMBs: 100
  MaxBackups: 20

Flags:
  TrimDebugMessageFromStatus: true
  EnableReissueCardRateLimiter: false
  MinVersionForCCOnboarding:
    IsEnableOnAndroid: true
    MinAndroidVersion: 0
    IsEnableOnIos: true
    MinIosVersion: 0

RateLimiterConfig:
  ResourceMap:
    cc_reissue_card_api_1:
      Rate: 1
      Period: 24h
    cc_reissue_card_api_2:
      Rate: 2
      Period: 168h # 7 days
    cc_reissue_card_api_3:
      Rate: 5
      Period: 720h # 30 days
  Namespace: "firefly"

Tracing:
  Enable: false

EpifiDb:
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

CreditCardDb:
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

RedisCachePrefix: "creditCard"

CreditCardUrl:
  CardActivationPageImageUrl: "https://epifi-icons.pointz.in/credit_card_images/CardImage.png"
  GiftBoxIconUrl: "https://epifi-icons.pointz.in/credit_card_images/Gift-Box-2_4x.png"
  RupeeIconUrl: "https://epifi-icons.pointz.in/credit_card_images/Rupee-Reload-a+1_4x.png"
  CheckBoxIconUrl: "https://epifi-icons.pointz.in/credit_card_images/Checkbox-min_4x.png"
  PartnerShipUrl: "https://epifi-icons.pointz.in/credit_card_images/PartnerIcon.png"
  CardDeliveryScreenIconUrl: "https://epifi-icons.pointz.in/credit_card_images/CCShippingAddressSelectIcon.png"
  CCTermsAndConditionsUrl: "https://fi.money/T&C"
  CCDeliveryAddressSelectStepNumber: 1
  CCOnboardingTotalSteps: 3

CreditCardBillPaymentInfo:
  - {BillGenDate: 1, PaymentDueDate: 19}
  - {BillGenDate: 2, PaymentDueDate: 20}
  - {BillGenDate: 3, PaymentDueDate: 21}
  - {BillGenDate: 4, PaymentDueDate: 22}
  - {BillGenDate: 5, PaymentDueDate: 23}
  - {BillGenDate: 6, PaymentDueDate: 24}
  - {BillGenDate: 7, PaymentDueDate: 25}
  - {BillGenDate: 8, PaymentDueDate: 26}
  - {BillGenDate: 9, PaymentDueDate: 27}
  - {BillGenDate: 10, PaymentDueDate: 28}
  - {BillGenDate: 11, PaymentDueDate: 28}
  - {BillGenDate: 12, PaymentDueDate: 28}
  - {BillGenDate: 13, PaymentDueDate: 1}
  - {BillGenDate: 14, PaymentDueDate: 2}
  - {BillGenDate: 15, PaymentDueDate: 3}
  - {BillGenDate: 16, PaymentDueDate: 4}
  - {BillGenDate: 17, PaymentDueDate: 5}
  - {BillGenDate: 18, PaymentDueDate: 6}
  - {BillGenDate: 19, PaymentDueDate: 7}
  - {BillGenDate: 20, PaymentDueDate: 8}
  - {BillGenDate: 21, PaymentDueDate: 9, Default: true}
  - {BillGenDate: 22, PaymentDueDate: 10}
  - {BillGenDate: 23, PaymentDueDate: 11}
  - {BillGenDate: 24, PaymentDueDate: 12}
  - {BillGenDate: 25, PaymentDueDate: 13}
  - {BillGenDate: 26, PaymentDueDate: 14}
  - {BillGenDate: 27, PaymentDueDate: 15}
  - {BillGenDate: 28, PaymentDueDate: 16}

EnableViewCardDetailsViaVgPciServer: true

CreditCardOffersScreenOptions:
  CreditLimitAmountString: "Credit limit: <b>Up to ₹10 lakh</b>"
  CardImageUrl: "https://epifi-icons.pointz.in/credit_card_images/card_img2.png"
  InfoBlock:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/5x_golden_icon.png"
    Title: "5% valueback"
    Desc: "From rewards and milestone benefits"
    IconLink1: "https://epifi-icons.pointz.in/credit_card_images/blue_tick.png"
    Title1: "Accelerated rewards"
    Desc1: "Up to 5x Fi-Coins on every transaction"
    IconLink2: "https://epifi-icons.pointz.in/credit_card_images/heart_icon_green.png"
    Title2: "Welcome benefits"
    Desc2: "Vouchers worth more than ₹5,000"
  TermsAndConditions: "Read Federal Bank’s <a href=https://fi.money/credit-card/important-T&Cs> Most Important Terms & Conditions</a>, <a href=https://fi.money/credit-card/key-fact-statement>Key Fact Statement</a> and <a href=https://fi.money/credit-card/T&Cs>Terms & Conditions</a>"
  PartnershipUrl: "https://epifi-icons.pointz.in/credit_card_images/PartnerIcon.png"
  GetCreditCardCtaText: "Love rewards? Get this card now!"
  Title: "You spend, we reward. Every single time."
  HeaderDescription: "Earn yearly benefits worth ₹20,000"
  PopUpTextBlock:
    InfoTitle: "Does this card come at no cost?"
    InfoDesc: "You will be charged a joining fee of ₹2000 for the Fi-Federal Co-branded credit card, which will be added to your first bill.\nAn annual fee of ₹2000 for your credit card will also be charged, but if you spend approximately ₹2.5L in an year, it will get waived off."
    CtaText: "Okay, got it"
  StaticImages:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/coin_benifits_intro_4x.png"
    Title: "Don't just collect Fi-Coins.\nPut them to use."
    Desc: ""
    IconLink1: "https://epifi-icons.pointz.in/credit_card_images/annual_milestone_rewards_4x.png"
    Title1: "Annual milestone rewards \nworth over ₹10,000"
    Desc1: ""
    IconLink2: "https://epifi-icons.pointz.in/credit_card_images/extra_benifits_intro_4x.png"
    Title2: "That’s not all!"
    Desc2: ""
  FeesInfoHeader: "Great things come at a price.\nFees & Charges."
  FeesInfo:
    Title: "Joining fee: ₹2,000"
    Desc: "Get <a href=>welcome vouchers worth ₹5,000</a> 🎁"
    CtaTitle: "Welcome Vouchers worth over ₹5,000!"
    CtaInfoItem:
      IconLink: "https://epifi-icons.pointz.in/credit_card_images/sony_liv_img.png"
      Title: "1 year subscription to Sony Liv worth ₹999"
      Desc: ""
      IconLink1: "https://epifi-icons.pointz.in/credit_card_images/merchant_uber_4x.png"
      Title1: "Uber vouchers worth ₹500"
      Desc1: ""
      IconLink2: "https://epifi-icons.pointz.in/credit_card_images/merchant_myntra_4x.png"
      Title2: "Myntra vouchers worth ₹1000"
      Desc2: ""
      IconLink3: "https://epifi-icons.pointz.in/credit_card_images/merchant_croma_4x.png"
      Title3: "Croma voucher worth ₹500"
      Desc3: ""
      IconLink4: "https://epifi-icons.pointz.in/credit_card_images/merchant_zomato_4x.png"
      Title4: "Zomato vouchers worth ₹1000"
      Desc4: ""
      IconLink5: "https://epifi-icons.pointz.in/credit_card_images/merchant_urbancompany_4x.png"
      Title5: "6 month Urban Company Plus membership worth ₹699"
      Desc5: ""
      IconLink6: "https://epifi-icons.pointz.in/credit_card_images/tattva_brand_img.png"
      Title6: "₹1,000 off on a Tattva Wellness treatment for you or your loved ones"
      Desc6: ""
    BottomInfoItem:
      IconLink: "https://epifi-icons.pointz.in/credit_card_images/information.png"
      Title: "This offer will be unlocked once you pay the Joining Fee on your bill for your Fi-Federal Co-branded Credit Card."
    Title1: "Renewal fee: ₹2,000"
    Desc1: "Waived off if spends are more than ₹2.5 lakh in the year."
    CtaText1: "View all fees & charges"
    CtaWebLink1: "https://fi.money/credit-card/key-fact-statement"
  AllFeesCta:
    CtaText: "View all fees & charges"
    CtaWebLink: "https://fi.money/credit-card/key-fact-statement"
  BroadVisualElementImgUrl: "https://epifi-icons.pointz.in/credit_card_images/full_width_5x_valueback_img_4x.png"
  RewardsWorthCtaInfo:
    CtaText: "Know more"
    DpLinkScreenTitle: "Get 5% valueback annually"
    DpLinkDescription: "Valueback is the rupee value of the benefits you will receive. You can get 5% or more of your annual spends using this card."
    DpLinkSubtitle: "See how much you can earn "
  JoiningFeeVoucherInfo:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/merchant_all_cards_intro_4x.png"
    Title: "Get welcome gift cards\n <a href=>worth over ₹5,000!</a>"
    CtaText: "View details"
    CtaWebLink: ""
  AcceleratedRewardsInfo:
    InfoIconLink: "https://epifi-icons.pointz.in/credit_card_images/accelarated_reward_intro_4x.png"
    CtaText: "See how much you can earn"
    DpLinkScreenTitle: "Get 5% valueback annually"
    DpLinkDescription: "Valueback is the rupee value of the benefits you will receive. You can get 5% or more of your annual spends using this card."
    DpLinkSubtitle: "See how much you can earn "
  FullScreenBlock:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/Welcome_voucher_v2.png"
    Title: "Get welcome gift cards worth over ₹5,000!"
    CtaName: "WelcomeVoucherCta"
    CtaText: "View details"
  LeftHalfBlock:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/5%25_across.png"
    Title: "Get annual benefits worth up to ₹20,000"
    CtaName: "RewardEstimationCta"
    CtaText: "Know more"
  RightHalfBlock:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/top_brands_icons.png"
    Title: "Up to 5x rewards on 20+ top brands"
    CtaName: "TopBrandsCta"
    CtaText: "Know more"

PinotConfig:
  AppName: "firefly-txnaggregates"

CreditCardOffersScreenOptionsV2:
  CreditLimitAmountString: "Credit limit <br><b>Up to ₹10 lakh</b>"
  CardImageUrl: "https://epifi-icons.pointz.in/credit_card_images/VL_Two_Fi_cardsCross_WithOutCoin.png"
  TermsAndConditions: "Read Federal Bank’s <a href=https://fi.money/credit-card/important-T&Cs> Most Important Terms & Conditions</a>, <a href=https://fi.money/credit-card/key-fact-statement>Key Fact Statement</a> and <a href=https://fi.money/credit-card/T&Cs>Terms & Conditions</a>"
  PartnershipUrl: "https://epifi-icons.pointz.in/credit_card_images/PartnerIcon.png"
  GetCreditCardCtaText: "Get this card now"
  Title: "Meet the most rewarding Credit Card"
  HeaderDescription: "Accelerated rewards on India’s top brands"
  StaticImages:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/VL_Fi_coins_use.png"
    Title: "We’ve got a lot to offer"
    IconLink1: "https://epifi-icons.pointz.in/credit_card_images/milestone_benefits_v3_4x.png"
    Title1: "Annual milestone benefits"
    IconLink2: "https://epifi-icons.pointz.in/credit_card_images/launge_and_forex.png"
    Title2: "That’s not all!"
  FeesInfoHeader: "Fees & Charges"
  FeesInfo:
    Title: "Joining fee: ₹2,000"
    Desc: "Get <a href=>welcome vouchers worth ₹5,000</a> 🎁"
    CtaTitle: "Welcome gift cards worth over ₹5,000!"
    CtaInfoItem:
      IconLink: "https://epifi-icons.pointz.in/credit_card_images/merchant_zomato_4x.png"
      Title: "Zomato vouchers worth ₹1000"
      IconLink1: "https://epifi-icons.pointz.in/credit_card_images/merchant_myntra_4x.png"
      Title1: "Myntra vouchers worth ₹1000"
      IconLink2: "https://epifi-icons.pointz.in/credit_card_images/merchant_uber_4x.png"
      Title2: "Uber vouchers worth ₹500"
      IconLink3: "https://epifi-icons.pointz.in/credit_card_images/merchant_urbancompany_4x.png"
      Title3: "6 month Urban Company Plus membership worth ₹699"
      IconLink4: "https://epifi-icons.pointz.in/credit_card_images/merchant_croma_4x.png"
      Title4: "Croma voucher worth ₹500"
      IconLink5: "https://epifi-icons.pointz.in/credit_card_images/sony_liv_img.png"
      Title5: "1 year subscription to Sony Liv worth ₹999"
      IconLink6: "https://epifi-icons.pointz.in/credit_card_images/tattva_brand_img.png"
      Title6: "₹1,000 off on a Tattva Wellness treatment for you or your loved ones"
    BottomInfoItem:
      IconLink: "https://epifi-icons.pointz.in/credit_card_images/information.png"
      Title: "This offer will be unlocked once you pay the Joining Fee on your bill for your Fi-Federal Co-branded Credit Card."
    Title1: "Renewal fee: ₹2,000"
    Desc1: "Waived off if spends are more than ₹2.5 lakh in the year."
    CtaText1: "View all fees & charges"
    CtaWebLink1: "https://fi.money/credit-card/key-fact-statement"
  AllFeesCta:
    CtaText: "View all fees & charges"
    CtaWebLink: "https://fi.money/credit-card/key-fact-statement"
  BroadVisualElementImgUrl: "https://epifi-icons.pointz.in/credit_card_images/VL_5X_BVE.png"
  BroadVisualElementImgUrlIOS: "https://epifi-icons.pointz.in/credit_card_images/VL_5X_BVE_IOS.png"
  RewardsWorthCtaInfo:
    CtaText: "Know more"
    DpLinkScreenTitle: "Get 5% valueback annually"
    DpLinkDescription: "Valueback is the rupee value of the benefits you will receive. You can get 5% or more of your annual spends using this card."
    DpLinkSubtitle: "See how much you can earn "
  RewardsWorthInfo:
    Title: "5x rewards on your top 3 brands"
    Desc: ""
  FeesInfoV2:
    Title: "Annual Fee"
    SubTitle: "Waive off the second year fee when you spend ₹2.5L in the first year"
    Desc: "₹2,000"
  JoiningFeeVoucherInfo:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/merchant_all_cards_intro_4x.png"
    Title: "Get gift cards worth over ₹5,000"
    CtaText: "View details"
  AcceleratedRewardsInfo:
    Title: "You spend, we reward. Every single time."
    InfoIconLink: "https://epifi-icons.pointz.in/credit_card_images/VL_Top_20_brands.png"
    CtaText: "Check out the 20+ brands"
    DpLinkScreenTitle: "Get 5% valueback annually"
    DpLinkDescription: "Valueback is the rupee value of the benefits you will receive. You can get 5% or more of your annual spends using this card."
    DpLinkSubtitle: "See how much you can earn "
  WelcomeVoucherCta:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/Welcome_voucher_v2.png"
    Title: "Exclusive welcome gift cards worth ₹5,000+"
    CtaText: "Explore"
  TopBrandsCta:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/top_brands_icons.png"
    Title: "Up to 5x rewards on 20+ top brands"
    CtaText: "Know more"
  RewardEstimationCta:
    IconLink: "https://epifi-icons.pointz.in/credit_card_images/5%25_across.png"
    Title: "Get annual benefits worth up to ₹20,000"
    CtaText: "Know more"
  Cta1Name: "WelcomeVoucherCta"
  Cta2Name: "RewardEstimationCta"
  Cta3Name: "WelcomeVoucherCta"

EnableCcOffersScreenOlderStringAlignment:
  MinAndroidVersion: 2000
  MinIosVersion: 0
  FallbackToEnableFeature: true

EnableCreditReportDownloadConsumer:
  MinAndroidVersion: 0
  MinIosVersion: 0
  FallbackToEnableFeature: true
  DisableFeature: false

SkipWorkflowInitiationForViewCardDetails:
  MinAndroidVersion: 100
  MinIosVersion: 2213
  FallbackToEnableFeature: false

EnableExpiryFromTokenizer:
  MinAndroidVersion: 394
  MinIosVersion: 546
  FallbackToEnableFeature: false
  DisableFeature: false

DbConnectionAliases:
  CreditCardPgdbConnAlias: "credit_card_pgdb_conn"

UsePgdbConnForCcDb: true
DisableHorizontalLayoutByQuestEngine: false
EnableNewCvpForUnsecuredCreditCard: true
DisableCreditCardOnboarding: false

CardRecommendationConfig:
  # List of all the card recommendation rules
  RecommendationRulesLists:
    ActorSpecificRulesList:
      - IsIosUser
    CardProgramSpecificRulesList:
      # - CampaignNameHasCVPName
      - IsUserPartOfMaginifiSegment
      - IsUserPartOfAmplifiSegment
      - IsCardProgramSpecificAttributes

  # All rules for card recommendation with weightage for each card program based on rule output
  RecommendationRuleToCardProgramWeightageMap:
    # Rule name
    CampaignNameHasCVPName:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 1
        CARD_PROGRAM_TYPE_SECURED: 1
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 1
    IsIosUser:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 0.5
        CARD_PROGRAM_TYPE_SECURED: 0.5
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 0.5
    IsUserPartOfMaginifiSegment:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 0.01
        CARD_PROGRAM_TYPE_SECURED: 0.01
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 1
    IsCardProgramSpecificAttributes:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 1
        CARD_PROGRAM_TYPE_SECURED: 1
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 1
    IsUserPartOfAmplifiSegment:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 1
        CARD_PROGRAM_TYPE_SECURED: 0.01
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 0.01

  DefaultWeightageConfig:
    CARD_PROGRAM_VENDOR_FEDERAL:
      CARD_PROGRAM_TYPE_UNSECURED: 0.7
      CARD_PROGRAM_TYPE_SECURED: 0.5
      CARD_PROGRAM_TYPE_MASS_UNSECURED: 0.6

  # Card program to release config map
  CardProgramReleaseConfig:
    FEDERAL:FI_BRE_APPROVED:UNSECURED::FI:
      EnableCardProgram: true
      FeatureName: "FEATURE_CC_BRE_AMPLIFI"
    FEDERAL:REALTIME_BRE:UNSECURED::FI:
      EnableCardProgram: true
      FeatureName: "FEATURE_CC_BRE_AMPLIFI"
    FEDERAL:PREAPPROVED:UNSECURED::FI:
      EnableCardProgram: true
      FeatureName: "FEATURE_CC_PREAPPROVED_AMPLIFI"
    FEDERAL:FI_BRE_APPROVED:MASS_UNSECURED::FI:
      EnableCardProgram: true
      FeatureName: "FEATURE_CC_BRE_MAGNIFI"
    FEDERAL:REALTIME_BRE:MASS_UNSECURED::FI:
      EnableCardProgram: true
      FeatureName: "FEATURE_CC_BRE_MAGNIFI"
    FEDERAL:PREAPPROVED:MASS_UNSECURED::FI:
      EnableCardProgram: true
      FeatureName: "FEATURE_CC_PREAPPROVED_MAGNIFI"
    FEDERAL:FI_BRE_APPROVED:SECURED:FD:FI:
      EnableCardProgram: true
      FeatureName: "FEATURE_CC_BRE_SIMPLIFI"
    FEDERAL:REALTIME_BRE:SECURED:FD:FI:
      EnableCardProgram: true
      FeatureName: "FEATURE_CC_BRE_SIMPLIFI"
    FEDERAL:PREAPPROVED:SECURED:FD:FI:
      EnableCardProgram: true
      FeatureName: "FEATURE_CC_PREAPPROVED_SIMPLIFI"


SimplifiConfig:
  ControlConfig:
    # atm limit = x % of card limit
    AtmMaxLimitMultiplier: 0.2

DepositConfigMap:
  FEDERAL:
    MinCreditLimitInRs: 15000
    MaxDepositAmountInRs: 100000
    MinDepositAmountInRs: 16700
    # deposit limit = 1.1111 * fixed credit limit
    DepositLimitMultiplier: 1.1111
    # credit limit = 0.9 * deposit amount
    CreditLimitMultiplier: 0.9
    DefaultCreditLimitsInRs: [20000,30000,50000]
    DefaultSelectedCreditLimit: 40000
    DefaultDepositTerm:
      Days: 500
      Months: 0
    MinDepositTerm:
      Days: 185
      Months: 0
    MaxDepositTerm:
      Days: 0
      Months: 60
    DefaultDepositSliderAmount:
      Amount: 50000
      DisplayString: "50k"
    DepositSliderAmountsInRs:
      - Amount: 15000
        DisplayString: "15k"
      - Amount: 20000
      - Amount: 25000
      - Amount: 30000
        DisplayString: "30k"
      - Amount: 35000
      - Amount: 40000
      - Amount: 45000
      - Amount: 50000
        DisplayString: "50k"
      - Amount: 60000
      - Amount: 70000
      - Amount: 80000
      - Amount: 90000
      - Amount: 100000
        DisplayString: "1 Lakh"
      - Amount: 125000
      - Amount: 150000
      - Amount: 175000
      - Amount: 200000
      - Amount: 225000
      - Amount: 250000
      - Amount: 275000
      - Amount: 300000
      - Amount: 325000
      - Amount: 350000
      - Amount: 375000
      - Amount: 400000
      - Amount: 425000
      - Amount: 450000
      - Amount: 475000
      - Amount: 500000
        DisplayString: "5 Lakh"
      - Amount: 525000
      - Amount: 550000
      - Amount: 575000
      - Amount: 600000
      - Amount: 625000
      - Amount: 650000
      - Amount: 675000
      - Amount: 700000
      - Amount: 725000
      - Amount: 750000
      - Amount: 775000
      - Amount: 800000
      - Amount: 825000
      - Amount: 850000
      - Amount: 875000
      - Amount: 900000
      - Amount: 925000
      - Amount: 950000
      - Amount: 975000
      - Amount: 1000000
        DisplayString: "10 Lakh"

EnableMassUnsecuredOnboardingV2: true
EnableUnsecuredOnboardingV2: true
EnableUnsecuredConsentFlowOnboarding: true

UnsecuredCCRenewalFeeReversalConfig:
  IneligibleUsersSegmentId: "AWS_test-segment"

DpdThirtySegmentId: "65ea3cc4-87b1-4c45-9d7e-0901c5d4c530"

CardProgramToBeneficiaryDetails:
  FEDERAL:REALTIME_BRE:SECURED:FD:FI_LITE: {ActorId: "actor-creditcard-federal-pool-account", PaymentInstrumentId: "paymentinstrument-creditcard-federal-pool-account-1"}

DisabledOnboardingScreenOptions:
  Icon: "https://epifi-icons.pointz.in/credit_card_images/credit_card_no_offer.png"
  Text: "Issuance of Credit Cards paused"
  SubText: "New applications for Credit Cards are not accepted at the moment. We'll let you know when it's back."

FireflyV2Config:
  IntroScreenV2Config:
    IntroScreenV2LoaderAnimation: "https://epifi-icons.pointz.in/credit_card_v2/maginifi_lottie_updated_final.json"
    IntroScreenV2LoaderAnimationHeight: 1179
    IntroScreenV2LoaderAnimationWidth: 412
    IntroScreenV2BgImage: "https://epifi-icons.pointz.in/credit_card_v2/maginifi_bg.png"
    IntroScreenV2BgImageHeight: 0
    IntroScreenV2BgImageWidth: 0
  OnboardingRetryCoolOff: "2160h"
