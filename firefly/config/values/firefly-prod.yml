Application:
  Environment: "prod"
  Name: "firefly"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  DbType: "CRDB"
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

CreditCardDb:
  Username: "credit_card_dev_user"
  Password: ""
  Name: "credit_card"
  DbType: "CRDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.credit_card_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.credit_card_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false
    EnableMultiDBSupport: true
    DBResolverList:
      - TableName: [ ]
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "firefly"
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "verify-full"
          SSLRootCert: "prod/rds/rds-ca-root-2061"
          SecretName: "prod/rds/epifimetis/credit_card_pgdb_dev_user"

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  EnableReissueCardRateLimiter: true
  EnableSkipBillDateCaptureByUserForCardPrograms:
    CardPrograms:
      - "SECURED"
      - "UNSECURED"
      - "MASS_UNSECURED"
    FeatureConfig:
      MinIOSVersion: 445
      MinAndroidVersion: 311
      FallbackToEnableFeature: true
      DisableFeature: false
  MinVersionForCCOnboarding:
    IsEnableOnAndroid: true
    MinAndroidVersion: 317
    IsEnableOnIos: true
    MinIosVersion: 457

RateLimiterConfig:
  ResourceMap:
    cc_reissue_card_api_1:
      Rate: 1
      Period: 24h
    cc_reissue_card_api_2:
      Rate: 2
      Period: 168h # 7 days
    cc_reissue_card_api_3:
      Rate: 5
      Period: 720h # 30 days
  Namespace: "firefly"

CreditCardPgDb:
  Username: "credit_card_pgdb_dev_user"
  Password: ""
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "prod/rds/epifimetis/credit_card_pgdb_dev_user"
  GormV2:
    # TODO(abhishekprakash) Mark log level as WARN or DEBUG once this is stable
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

CreditCardFederalPgDb:
  Username: "credit_card_federal_dev_user"
  Password: ""
  Name: "credit_card_federal"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "prod/rds/epifimetis/credit_card_federal_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    ClientEncryptionKey: "prod/lending/card-qr-code-aes-cbc-key"
    ClientEncryptionInitialisationVector: "prod/lending/card-qr-code-aes-cbc-iv"
    MctClientEncryptionKey: "prod/lending/mct-card-qr-code-aes-cbc-key"
    MctClientEncryptionInitialisationVector: "prod/lending/mct-card-qr-code-aes-cbc-iv"
    VendorCardPinEncryptionKey: "prod/lending/pin-encryption-m2p-key"
    ClientPinRsaPrivateKey: "prod/lending/pin-encryption-private-key"
    StartreePinotAuthToken: "prod/pinot/startree"
    EpifiSeshaasaiPgpPrivateKey: "prod/pgp/epifi-seshaasai-api-private-key"
    EpifiSeshaasaiPgpPassphrase: "prod/pgp/epifi-seshaasai-api-passphrase"
    SeshaasaiPgpPublicKey: "prod/pgp/seshaasai-pgp-public-key-for-epifi"
    # Federal
    EpifiFederalPgpPrivateKey: "prod/pgp/pgp-epifi-fed-api-private-key"
    EpifiFederalPgpPassphrase: "prod/pgp/pgp-epifi-fed-api-password"
    FederalPgpPublicKey: "prod/pgp/federal-pgp-pub-key-for-epifi"

PinotConfig:
  AppName: "firefly-txnaggregates"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

SignalWorkflowPublisher:
  QueueName: "prod-celestial-signal-workflow-queue"

CCTransactionNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-txn-notification-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "firefly"

CCAcsNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-acs-notification-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10

CardsDispatchedCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-cards-dispatched-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CardsSentForPrintingCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-cards-sent-for-printing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCTransactionEventPublisher:
  TopicName: "prod-cc-transaction-event-topic"

TransactionsProducer:
  StreamName: "prod-credit-card-pinot-transactions-stream"

CCNonFinancialNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-non-financial-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ProcessCreditCardOfferFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-process-cc-offers-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCTransactionsForPinotSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-transactions-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CategorizerUpdateForPinotSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-categorizer-update-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "firefly"

CCAuthFactorUpdateNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-credit-card-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "firefly"

CCCreditReportDownloadEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-credit-report-download-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "firefly"

CCStatementNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-statement-notification-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~51 min post that regular interval of 2 hours is followed for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 10
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 2
          MaxAttempts: 83
          TimeUnit: "Hour"
      MaxAttempts: 93
      CutOff: 10
  # currently temporal supports 40 qps for initiating a workflow.
  # as this is an async flow and not real time user impacting, to maintain system stability
  # and prevent resource utilisation spike, we are limiting the message consumption to 5qps.
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "firefly"

CCOnboardingStateUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-cc-onboarding-state-update-event-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

CardsSentForPrintingBucketName: "epifi-prod-creditcard-m2p-upload"
CardsDispatchedBucketName: "epifi-prod-creditcard-seshaasai-upload"
AcsBucketName : "epifi-prod-cc-acs-notification"
AcsNotificationRawDataStore:
  BucketName: "epifi-raw"
  ObjectKey: "vendor/m2p_reports/acs_notifications/%s/%s-AcsNotification.csv"

EnableViewCardDetailsViaVgPciServer: true

FeatureReleaseConfig:
  FeatureConstraints:
    FEATURE_CREDIT_CARD_V2_FLOW:
      AppVersionConstraintConfig:
        MinAndroidVersion: 454
        MinIOSVersion: 621
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageAndroid: 0
          RolloutPercentageIOS: 0
      UserGroupConstraintConfig:
        AllowedUserGrp:
          - 17 # CREDIT_CARD_INTERNAL
    FEATURE_CC_REALTIME_ELIGIBILITY:
      AppVersionConstraintConfig:
        MinAndroidVersion: 29300
        MinIOSVersion: 40900
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
    FEATURE_CC_EMI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 335
        MinIOSVersion: 487
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 335
        MinIOSVersion: 487
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 335
        MinIOSVersion: 487
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_AMPLIFI_CC_MINUTES:
      AppVersionConstraintConfig:
        MinAndroidVersion: 350
        MinIOSVersion: 500
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_BIOMETRIC_REVALIDATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 287
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 100
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_INTRO_SCREEN_HORIZONTAL_LAYOUT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 290
        MinIOSVersion: 405
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 300
        MinIOSVersion: 410
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    MASS_UNSECURED_CARD:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
    FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    CREDIT_REPORT_ADDRESS_SELECTION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
    FEATURE_CC_PREAPPROVED_AMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_AMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_PREAPPROVED_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_PREAPPROVED_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

DepositConfigMap:
  FEDERAL:
    MinCreditLimitInRs: 15000
    MaxDepositAmountInRs: 1000000
    MinDepositAmountInRs: 16700
    # deposit limit = 1.1111 * fixed credit limit
    DepositLimitMultiplier: 1.1111
    # credit limit = 0.9 * deposit amount
    CreditLimitMultiplier: 0.9
    DefaultCreditLimitsInRs: [20000,30000,50000]
    DefaultSelectedCreditLimit: 40000
    DefaultDepositTerm:
      Days: 500
      Months: 0
    MinDepositTerm:
      Days: 185
      Months: 0
    MaxDepositTerm:
      Days: 0
      Months: 60
    DefaultDepositSliderAmount:
      Amount: 50000
      DisplayString: "50k"
    DepositSliderAmountsInRs:
      - Amount: 15000
        DisplayString: "15k"
      - Amount: 20000
      - Amount: 25000
      - Amount: 30000
        DisplayString: "30k"
      - Amount: 35000
      - Amount: 40000
      - Amount: 45000
      - Amount: 50000
        DisplayString: "50k"
      - Amount: 60000
      - Amount: 70000
      - Amount: 80000
      - Amount: 90000
      - Amount: 100000
        DisplayString: "1 Lakh"
      - Amount: 125000
      - Amount: 150000
      - Amount: 175000
      - Amount: 200000
      - Amount: 225000
      - Amount: 250000
      - Amount: 275000
      - Amount: 300000
      - Amount: 325000
      - Amount: 350000
      - Amount: 375000
      - Amount: 400000
      - Amount: 425000
      - Amount: 450000
      - Amount: 475000
      - Amount: 500000
        DisplayString: "5 Lakh"
      - Amount: 525000
      - Amount: 550000
      - Amount: 575000
      - Amount: 600000
      - Amount: 625000
      - Amount: 650000
      - Amount: 675000
      - Amount: 700000
      - Amount: 725000
      - Amount: 750000
      - Amount: 775000
      - Amount: 800000
      - Amount: 825000
      - Amount: 850000
      - Amount: 875000
      - Amount: 900000
      - Amount: 925000
      - Amount: 950000
      - Amount: 975000
      - Amount: 1000000
        DisplayString: "10 Lakh"


QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-12231.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12231"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/growth-infra/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: ff-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

UsePgdbConnForCcDb: true
EnableNewCvpForUnsecuredCreditCard: true
DisableCreditCardOnboarding: true
SkipEligibilityCheckForDisabledCCOnboarding: true

CardProgramToSegmentIdMap:
  CARD_PROGRAM_TYPE_UNSECURED:
    CARD_PROGRAM_SOURCE_PREAPPROVED: "c3a5890b-e3e8-4a55-a5e4-9c5a1dbfec7f"
  CARD_PROGRAM_TYPE_MASS_UNSECURED:
    CARD_PROGRAM_SOURCE_PREAPPROVED: "2431d2c0-df83-43a2-990c-c1fab5ad3f9f"
    CARD_PROGRAM_SOURCE_FI_BRE_APPROVED: "c0183261-fd27-46e5-8cd9-1589889bf3bc"

FireflyRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "redis-10246.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:10246"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/cards/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

CreditCardCacheConfig:
  CreditCardOfferCacheConfig:
    Prefix: "creditCardOfferId:"
    IsCachingEnabled: true
    CacheTTl: "168h"
  CardRequestCacheConfig:
    Prefix: "creditCardRequestId:"
    IsCachingEnabled: true
    CacheTTl: "30m"

CardRecommendationConfig:
  # List of all the card recommendation rules
  RecommendationRulesLists:
    ActorSpecificRulesList:
      - IsIosUser
    CardProgramSpecificRulesList:
      # - CampaignNameHasCVPName
      - IsUserPartOfMaginifiSegment
      - IsUserPartOfAmplifiSegment
      - IsCardProgramSpecificAttributes

  # All rules for card recommendation with weightage for each card program based on rule output
  RecommendationRuleToCardProgramWeightageMap:
    # Rule name
    CampaignNameHasCVPName:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 1
        CARD_PROGRAM_TYPE_SECURED: 1
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 1
    IsIosUser:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 0.5
        CARD_PROGRAM_TYPE_SECURED: 0.5
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 0.5
    IsUserPartOfMaginifiSegment:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 0.01
        CARD_PROGRAM_TYPE_SECURED: 0.01
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 1
    IsCardProgramSpecificAttributes:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 1
        CARD_PROGRAM_TYPE_SECURED: 1
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 1
    IsUserPartOfAmplifiSegment:
      CARD_PROGRAM_VENDOR_FEDERAL:
        CARD_PROGRAM_TYPE_UNSECURED: 1
        CARD_PROGRAM_TYPE_SECURED: 0.01
        CARD_PROGRAM_TYPE_MASS_UNSECURED: 0.01

  # Default weight for config
  DefaultWeightageConfig:
    CARD_PROGRAM_VENDOR_FEDERAL:
      CARD_PROGRAM_TYPE_UNSECURED: 0.7
      CARD_PROGRAM_TYPE_SECURED: 0.5
      CARD_PROGRAM_TYPE_MASS_UNSECURED: 0.6

  # Card program to release config map
  CardProgramReleaseConfig:
    FEDERAL:FI_BRE_APPROVED:UNSECURED::FI:
      EnableCardProgram: false
      FeatureName: "FEATURE_CC_BRE_AMPLIFI"
    FEDERAL:REALTIME_BRE:UNSECURED::FI:
      EnableCardProgram: false
      FeatureName: "FEATURE_CC_BRE_AMPLIFI"
    FEDERAL:PREAPPROVED:UNSECURED::FI:
      EnableCardProgram: false
      FeatureName: "FEATURE_CC_PREAPPROVED_AMPLIFI"
    FEDERAL:FI_BRE_APPROVED:MASS_UNSECURED::FI:
      EnableCardProgram: false
      FeatureName: "FEATURE_CC_BRE_MAGNIFI"
    FEDERAL:REALTIME_BRE:MASS_UNSECURED::FI:
      EnableCardProgram: false
      FeatureName: "FEATURE_CC_BRE_MAGNIFI"
    FEDERAL:PREAPPROVED:MASS_UNSECURED::FI:
      EnableCardProgram: false
      FeatureName: "FEATURE_CC_PREAPPROVED_MAGNIFI"
    FEDERAL:FI_BRE_APPROVED:SECURED:FD:FI:
      EnableCardProgram: false
      FeatureName: "FEATURE_CC_BRE_SIMPLIFI"
    FEDERAL:REALTIME_BRE:SECURED:FD:FI:
      EnableCardProgram: false
      FeatureName: "FEATURE_CC_BRE_SIMPLIFI"
    FEDERAL:PREAPPROVED:SECURED:FD:FI:
      EnableCardProgram: false
      FeatureName: "FEATURE_CC_PREAPPROVED_SIMPLIFI"

EnableMassUnsecuredOnboardingV2: false
EnableUnsecuredOnboardingV2: false
EnableUnsecuredConsentFlowOnboarding: false

EnableCreditReportDownloadConsumer:
  MinAndroidVersion: 10000
  MinIosVersion: 10000
  FallbackToEnableFeature: true
  DisableFeature: true

SkipWorkflowInitiationForViewCardDetails:
  MinAndroidVersion: 100
  MinIosVersion: 502
  FallbackToEnableFeature: false

EnableExpiryFromTokenizer:
  MinAndroidVersion: 397
  MinIosVersion: 547
  FallbackToEnableFeature: false
  DisableFeature: false

CcOnboardingStateUpdateEventPublisher:
  QueueName: "prod-cc-onboarding-state-update-event-callback-queue"
