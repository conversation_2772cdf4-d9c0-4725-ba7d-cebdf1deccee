Application:
  Environment: "staging"
  Name: "firefly"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  DbType: "CRDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

# TODO: update once db is created
CreditCardDb:
  DbType: "CRDB"
  Username: "credit_card_dev_user"
  Password: ""
  Name: "credit_card"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.credit_card_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.credit_card_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly-worker"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - TableName: [ ]
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "firefly"
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "staging/rds/epifimetis/credit_card_pgdb_dev_user"

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  EnableReissueCardRateLimiter: true

RateLimiterConfig:
  ResourceMap:
    cc_reissue_card_api_1:
      Rate: 1
      Period: 2m
    cc_reissue_card_api_2:
      Rate: 2
      Period: 4m # 7 days
    cc_reissue_card_api_3:
      Rate: 5
      Period: 10m # 30 days
  Namespace: "firefly"

CreditCardPgDb:
  Username: "credit_card_pgdb_dev_user"
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "staging/rds/epifimetis/credit_card_pgdb_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CreditCardFederalPgDb:
  Username: "credit_card_federal_dev_user"
  Name: "credit_card_federal"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "staging/rds/epifimetis/credit_card_federal_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Secrets:
  Ids:
    RudderWriteKey: "staging/rudder/internal-writekey"
    ClientEncryptionKey: "staging/lending/card-qr-code-aes-cbc-key"
    ClientEncryptionInitialisationVector: "staging/lending/card-qr-code-aes-cbc-iv"
    MctClientEncryptionKey: "staging/lending/card-qr-code-aes-cbc-key"
    MctClientEncryptionInitialisationVector: "staging/lending/card-qr-code-aes-cbc-iv"
    VendorCardPinEncryptionKey: "staging/lending/pin-encryption-m2p-key"
    ClientPinRsaPrivateKey: "staging/lending/pin-encryption-private-key"
    StartreePinotAuthToken: "staging/pinot/startree"
    CreditCardPgdbCredentials: "staging/rds/epifimetis/credit_card_pgdb_dev_user"
    EpifiSeshaasaiPgpPrivateKey: "staging/pgp/epifi-seshaasai-api-private-key"
    EpifiSeshaasaiPgpPassphrase: "staging/pgp/epifi-seshaasai-api-passphrase"
    SeshaasaiPgpPublicKey: "staging/pgp/seshaasai-pgp-public-key-for-epifi"
    # Federal
    EpifiFederalPgpPrivateKey: "staging/pgp/epifi-fed-api-private-key"
    EpifiFederalPgpPassphrase: "staging/pgp/epifi-fed-api-password"
    FederalPgpPublicKey: "staging/pgp/federal-pgp-public-key-for-epifi"

PinotConfig:
  AppName: "firefly-txnaggregates"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

SignalWorkflowPublisher:
  QueueName: "staging-celestial-signal-workflow-queue"

CCNonFinancialNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-non-financial-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ProcessCreditCardOfferFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-process-cc-offers-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCTransactionNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-txn-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCAcsNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-acs-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CardsDispatchedCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-cards-dispatched-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CardsSentForPrintingCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-cards-sent-for-printing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCTransactionEventPublisher:
  TopicName: "staging-cc-transaction-event-topic"

TransactionsProducer:
  StreamName: "staging-credit-card-pinot-transactions-stream"

CCTransactionsForPinotSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-transactions-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CategorizerUpdateForPinotSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-categorizer-update-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCAuthFactorUpdateNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-credit-card-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCCreditReportDownloadEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-credit-report-download-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCStatementNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-statement-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCOnboardingStateUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-cc-onboarding-state-update-event-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

CardsSentForPrintingBucketName: "epifi-staging-creditcard-m2p-upload"
CardsDispatchedBucketName: "epifi-staging-creditcard-seshaasai-upload"
AcsBucketName : "epifi-staging-cc-acs-notification"
AcsNotificationRawDataStore:
  BucketName: "epifi-raw-dev"
  ObjectKey: "staging/data/vendor/m2p_reports/acs_notifications/%s/%s-AcsNotification.csv"

FeatureReleaseConfig:
  FeatureConstraints:
    FEATURE_CREDIT_CARD_V2_FLOW:
      AppVersionConstraintConfig:
        MinAndroidVersion: 100
        MinIOSVersion: 100
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageAndroid: 100
          RolloutPercentageIOS: 100
      UserGroupConstraintConfig:
        AllowedUserGrp:
          - 17 # CREDIT_CARD_INTERNAL
    FEATURE_CC_REALTIME_ELIGIBILITY:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_AMPLIFI_CC_MINUTES:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_ONBOARDING_WITHOUT_BILL_GEN_DATE_CAPTURE_STAGE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_BIOMETRIC_REVALIDATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 200
        MinIOSVersion: 200
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_INTRO_SCREEN_HORIZONTAL_LAYOUT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    MASS_UNSECURED_CARD:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
    FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    CREDIT_REPORT_ADDRESS_SELECTION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
    FEATURE_CC_PREAPPROVED_AMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_AMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_PREAPPROVED_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_PREAPPROVED_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL


QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: ff-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

FireflyRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 12
    PoolSize: 20 # this is set to twice the size of default connection pool size
    MinIdleConns: 10 # this allows min. number of conn to be readily available
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 500ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

CardProgramToSegmentIdMap:
  CARD_PROGRAM_TYPE_UNSECURED:
    CARD_PROGRAM_SOURCE_PREAPPROVED: "unsecured_seg_id"
    CARD_PROGRAM_SOURCE_FI_BRE_APPROVED: "unsecured_seg_id"
  CARD_PROGRAM_TYPE_MASS_UNSECURED:
    CARD_PROGRAM_SOURCE_PREAPPROVED: "dummy_segment_id"
    CARD_PROGRAM_SOURCE_FI_BRE_APPROVED: "dummy_segment_id"


CreditCardCacheConfig:
  CreditCardOfferCacheConfig:
    Prefix: "creditCardOfferId:"
    IsCachingEnabled: true
    CacheTTl: "30m"
  CardRequestCacheConfig:
    Prefix: "creditCardRequestId:"
    IsCachingEnabled: true
    CacheTTl: "30m"

CcOnboardingStateUpdateEventPublisher:
  QueueName: "staging-cc-onboarding-state-update-event-callback-queue"
