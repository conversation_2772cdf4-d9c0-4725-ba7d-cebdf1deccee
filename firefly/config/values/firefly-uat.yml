Application:
  Environment: "uat"
  Name: "firefly"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999


CreditCardFederalPgDb:
  Username: "credit_card_federal_dev_user"
  Password: ""
  Name: "credit_card_federal"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "uat/rds/epifimetis/credit_card_federal_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CreditCardPgDb:
  Username: "credit_card_pgdb_dev_user"
  Password: ""
  Name: "credit_card_pgdb"
  DbType: "PGDB"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly"
  SecretName: "uat/rds/epifimetis/credit_card_pgdb_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

EpifiDb:
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  DbType: "CRDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

CreditCardDb:
  Username: "credit_card_dev_user"
  Password: ""
  Name: "credit_card"
  DbType: "CRDB"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.credit_card_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.credit_card_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "firefly-worker"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - TableName: [ ]
        Alias: "credit_card_pgdb_conn"
        DbDsn:
          DbType: "PGDB"
          AppName: "firefly"
          StatementTimeout: 1m
          Name: "credit_card_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "uat/rds/epifimetis/credit_card_pgdb_dev_user"

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "uat/rudder/internal-writekey"
    ClientEncryptionKey: "uat/lending/card-qr-code-aes-cbc-key"
    ClientEncryptionInitialisationVector: "uat/lending/card-qr-code-aes-cbc-iv"
    MctClientEncryptionKey: "uat/lending/card-qr-code-aes-cbc-key"
    MctClientEncryptionInitialisationVector: "uat/lending/card-qr-code-aes-cbc-iv"
    VendorCardPinEncryptionKey: "uat/lending/pin-encryption-m2p-key"
    ClientPinRsaPrivateKey: "uat/lending/pin-encryption-private-key"
    StartreePinotAuthToken: "uat/pinot/startree"
    EpifiSeshaasaiPgpPrivateKey: "uat/pgp/epifi-seshaasai-api-private-key"
    EpifiSeshaasaiPgpPassphrase: "uat/pgp/epifi-seshaasai-api-passphrase"
    SeshaasaiPgpPublicKey: "uat/pgp/seshaasai-pgp-public-key-for-epifi"
    # Federal
    EpifiFederalPgpPrivateKey: "uat/pgp/epifi-fed-api-private-key"
    EpifiFederalPgpPassphrase: "uat/pgp/epifi-fed-api-password"
    FederalPgpPublicKey: "uat/pgp/federal-pgp-public-key-for-epifi"

PinotConfig:
  AppName: "firefly-txnaggregates"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

SignalWorkflowPublisher:
  QueueName: "uat-celestial-signal-workflow-queue"

CCNonFinancialNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-non-financial-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

ProcessCreditCardOfferFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-process-cc-offers-file-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCTransactionNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-txn-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCAcsNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-acs-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CardsDispatchedCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-cards-dispatched-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CardsSentForPrintingCsvFileSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-cards-sent-for-printing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCTransactionEventPublisher:
  TopicName: "uat-cc-transaction-event-topic"

TransactionsProducer:
  StreamName: "uat-credit-card-pinot-transactions-stream"

CCTransactionsForPinotSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-transactions-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CategorizerUpdateForPinotSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-categorizer-update-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCAuthFactorUpdateNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-credit-card-auth-factor-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCCreditReportDownloadEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-credit-report-download-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCStatementNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-statement-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 9
      TimeUnit: "Second"

CCOnboardingStateUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "uat-cc-onboarding-state-update-event-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

CardsSentForPrintingBucketName: "epifi-uat-creditcard-m2p-upload"
CardsDispatchedBucketName: "epifi-uat-creditcard-seshaasai-upload"
AcsBucketName : "epifi-uat-cc-acs-notification"
AcsNotificationRawDataStore:
  BucketName: "epifi-raw-dev"
  ObjectKey: "uat/data/vendor/m2p_reports/acs_notifications/%s/%s-AcsNotification.csv"

FeatureReleaseConfig:
  FeatureConstraints:
    FEATURE_CREDIT_CARD_V2_FLOW:
      AppVersionConstraintConfig:
        MinAndroidVersion: 100
        MinIOSVersion: 100
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageAndroid: 100
          RolloutPercentageIOS: 100
      UserGroupConstraintConfig:
        AllowedUserGrp:
          - 17 # CREDIT_CARD_INTERNAL
    FEATURE_CC_REALTIME_ELIGIBILITY:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_EMI_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_AMPLIFI_CC_MINUTES:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_ONBOARDING_WITHOUT_BILL_GEN_DATE_CAPTURE_STAGE:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_BIOMETRIC_REVALIDATION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_INTRO_SCREEN_HORIZONTAL_LAYOUT:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_USER_INELIGIBLE_FEEDBACK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
        RolloutPercentage: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK:
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    CREDIT_REPORT_ADDRESS_SELECTION:
      AppVersionConstraintConfig:
        MinAndroidVersion: 2000
        MinIOSVersion: 2000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 0
    FEATURE_CC_PREAPPROVED_AMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_AMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_PREAPPROVED_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_MAGNIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_PREAPPROVED_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_CC_BRE_SIMPLIFI:
      AppVersionConstraintConfig:
        MinAndroidVersion: 198
        MinIOSVersion: 1000
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL


QuestSdk:
  Disable: true

FireflyRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 5
  ClientName: firefly
  HystrixCommand:
    CommandName: "firefly_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 200
      ExecutionTimeout: 100ms
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

CardProgramToSegmentIdMap:
  CARD_PROGRAM_TYPE_UNSECURED:
    CARD_PROGRAM_SOURCE_PREAPPROVED: "unsecured_seg_id"
    CARD_PROGRAM_SOURCE_FI_BRE_APPROVED: "unsecured_seg_id"
  CARD_PROGRAM_TYPE_MASS_UNSECURED:
    CARD_PROGRAM_SOURCE_PREAPPROVED: "dummy_segment_id"
    CARD_PROGRAM_SOURCE_FI_BRE_APPROVED: "dummy_segment_id"

CreditCardCacheConfig:
  CreditCardOfferCacheConfig:
    Prefix: "creditCardOfferId:"
    IsCachingEnabled: true
    CacheTTl: "30m"
  CardRequestCacheConfig:
    Prefix: "creditCardRequestId:"
    IsCachingEnabled: true
    CacheTTl: "30m"

CcOnboardingStateUpdateEventPublisher:
  QueueName: "uat-cc-onboarding-state-update-event-callback-queue"
