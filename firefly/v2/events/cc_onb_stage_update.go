package events

import (
	"time"

	"github.com/epifi/be-common/pkg/events"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/gamma/api/typesv2"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

type CcOnbStageUpdate struct {
	ActorId       string
	ProspectId    string
	OnbStage      string
	StageStatus   string
	ApplicantType string
	// Optional: will get populated only for pre-approved onboarding
	PreApprovedLimit float64
	// Optional: will get populated only for pre-approved onboarding
	PreApprovedOfferExpiry time.Time
	Timestamp              time.Time
	EventId                string
	EventType              string
	CardProgram            string
}

func NewCcOnbStageUpdate(actorId, prospectId, onbStage, stageStatus, applicantType string, cardProgram *typesv2.CardProgram,
	preApprovedLimit float64, preApprovedOfferExpiry time.Time) *CcOnbStageUpdate {
	return &CcOnbStageUpdate{
		ActorId:                actorId,
		ProspectId:             prospectId,
		OnbStage:               onbStage,
		StageStatus:            stageStatus,
		ApplicantType:          applicantType,
		PreApprovedLimit:       preApprovedLimit,
		PreApprovedOfferExpiry: preApprovedOfferExpiry,
		Timestamp:              time.Now(),
		EventId:                uuid.NewString(),
		EventType:              events.EventTrack,
		CardProgram:            ffPkg.GetCardProgramStringFromCardProgram(cardProgram),
	}
}

func (c *CcOnbStageUpdate) GetEventId() string {
	return c.EventId
}
func (c *CcOnbStageUpdate) GetUserId() string {
	return c.ActorId
}
func (c *CcOnbStageUpdate) GetProspectId() string {
	return c.ProspectId
}
func (c *CcOnbStageUpdate) GetEventName() string {
	return EventCreditCardOnbStageUpdate
}
func (c *CcOnbStageUpdate) GetEventType() string {
	return c.EventType
}
func (c *CcOnbStageUpdate) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}
func (c *CcOnbStageUpdate) GetEventTraits() map[string]interface{} {
	return nil
}
