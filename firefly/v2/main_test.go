// nolint: unparam, dogsled
package v2_test

import (
	"os"
	"testing"

	queueMock "github.com/epifi/be-common/pkg/queue/mocks"

	"github.com/golang/mock/gomock"

	mocksActor "github.com/epifi/gamma/api/actor/mocks"
	mockAuth "github.com/epifi/gamma/api/auth/mocks"
	mocksBankCust "github.com/epifi/gamma/api/bankcust/mocks"
	mocksUser "github.com/epifi/gamma/api/user/mocks"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/api/vendorgateway/creditcard/mocks"
	"github.com/epifi/gamma/firefly/config"
	genConf "github.com/epifi/gamma/firefly/config/genconf"
	ffV2Svc "github.com/epifi/gamma/firefly/v2"
	daoMocks "github.com/epifi/gamma/firefly/v2/dao/mocks"
	"github.com/epifi/gamma/firefly/v2/test"
)

var (
	ffConf    *config.Config
	ffGenConf *genConf.Config
)

func TestMain(m *testing.M) {
	conf, genConf, _, _, teardown := test.InitTestServerV2()
	ffConf = conf
	ffGenConf = genConf
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockDependencies struct {
	cardRequestDao                  *daoMocks.MockCardRequestDao
	creditCardDao                   *daoMocks.MockCreditCardDao
	ccOffersDao                     *daoMocks.MockCreditCardOffersDao
	mockVgClient                    *mocks.MockCreditCardClient
	mockUserClient                  *mocksUser.MockUsersClient
	mockAuthClient                  *mockAuth.MockAuthClient
	mockActorClient                 *mocksActor.MockActorClient
	mockBankCustClient              *mocksBankCust.MockBankCustomerServiceClient
	mockOnbClient                   *onbMocks.MockOnboardingClient
	mockCcStageUpdateEventPublisher *queueMock.MockPublisher
}

func getFireflyV2SvcWithMocks(t *testing.T) (*ffV2Svc.Service, *mockDependencies, func()) {
	ctr := gomock.NewController(t)

	mockCardRequestDao := daoMocks.NewMockCardRequestDao(ctr)
	mockCreditCardDao := daoMocks.NewMockCreditCardDao(ctr)
	mockCcOffersDao := daoMocks.NewMockCreditCardOffersDao(ctr)
	mockUserClient := mocksUser.NewMockUsersClient(ctr)
	mockVgClient := mocks.NewMockCreditCardClient(ctr)
	mockAuthClient := mockAuth.NewMockAuthClient(ctr)
	mockActorClient := mocksActor.NewMockActorClient(ctr)
	mockBankCustClient := mocksBankCust.NewMockBankCustomerServiceClient(ctr)
	mockOnbClient := onbMocks.NewMockOnboardingClient(ctr)
	mockCcStageUpdateEventPublisher := queueMock.NewMockPublisher(ctr)

	svc := ffV2Svc.NewService(ffGenConf, mockVgClient, mockAuthClient, mockCardRequestDao, mockCreditCardDao, mockCcOffersDao, mockUserClient, mockBankCustClient, mockOnbClient, mockCcStageUpdateEventPublisher, nil)

	md := &mockDependencies{
		cardRequestDao:                  mockCardRequestDao,
		creditCardDao:                   mockCreditCardDao,
		ccOffersDao:                     mockCcOffersDao,
		mockVgClient:                    mockVgClient,
		mockUserClient:                  mockUserClient,
		mockAuthClient:                  mockAuthClient,
		mockActorClient:                 mockActorClient,
		mockBankCustClient:              mockBankCustClient,
		mockOnbClient:                   mockOnbClient,
		mockCcStageUpdateEventPublisher: mockCcStageUpdateEventPublisher,
	}

	return svc, md, func() {
		ctr.Finish()
	}
}
