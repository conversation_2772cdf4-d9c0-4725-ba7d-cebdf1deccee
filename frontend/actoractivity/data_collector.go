package actoractivity

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"sync"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/connected_account"
	feActorActivityPb "github.com/epifi/gamma/api/frontend/actoractivity"
	actorActivityPb "github.com/epifi/gamma/api/order/actoractivity"
	activityPb "github.com/epifi/gamma/api/order/actoractivity/activity"
	activityEnums "github.com/epifi/gamma/api/order/actoractivity/enums"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/frontend/actoractivity/dataprovider"
	"github.com/epifi/gamma/pkg/pay"
)

var (
	sourceToDataWaitTimeDurationMap = map[activityEnums.ActivitySource]time.Duration{
		activityEnums.ActivitySource_FI_TRANSACTION:                20 * time.Second,
		activityEnums.ActivitySource_CONNECTED_ACCOUNT_TRANSACTION: 20 * time.Second,
		activityEnums.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD:   20 * time.Second,
	}
	defaultWaitTime = 10 * time.Second
)

//  1. PageTokenString must have offset for the new source.
//  2. Modify getSourceForAccount to categorise source of data for account-id and put into sourceToAccountMap
//  3. Modify getOffsetFromPageToken to return offset for source.
//  4. sourceToAccountMap will concurrently call all source and gather using sourceToDataProviderMap. It will check for DataProvider Implementation
//     for different source and call it concurrently to get the results.
//  5. Set offset in function setPageTokens function in GetActivities frontend RPC implementation against activity source.
//  6. Set source in actorActivitySourceToActivitySource function
//  7. Set view link and icon deeplink in getTransactionView function for redirection to receipt or timeline based on data providers
//
// nolint: funlen
func (s *Service) collectAllActivitiesFromSource(ctx context.Context, currentActorId string, accountFilters []*feActorActivityPb.GetActivitiesRequest_Account, startTimeStamp *timestampPb.Timestamp, pageSize int32, pageTokenString string, descending bool, pageLandingTimestamp *timestampPb.Timestamp) (*rpc.Status, []*actorActivityPb.GetActivitiesResponse_Activity) {
	var (
		sourceToAccountMap   = make(map[activityEnums.ActivitySource][]*feActorActivityPb.GetActivitiesRequest_Account)
		waitGroup            = &sync.WaitGroup{}
		dataCollectorChannel = make(chan *dataprovider.ActivitiesResponse)
		activities           []*activityPb.Activity
		pageToken            = PageToken{}
		// flag to check if we got error response from all source
		errFromAllSource = true
		maxTimeToWait    = defaultWaitTime
	)

	err := pageToken.UnmarshalToken(pageTokenString)
	if err != nil {
		logger.Error(ctx, "corrupted page token", zap.String(logger.PAGE_CONTEXT, pageTokenString))
		return rpc.StatusInternal(), nil
	}

	updatedAccountFilters, status := s.getFeAccountFiltersFromDerivedAccountId(ctx, currentActorId, accountFilters)
	if status != nil {
		logger.Error(ctx, "error fetching account filters from derived account id", zap.Error(err))
		return rpc.StatusInternal(), nil
	}

	if len(updatedAccountFilters) == 0 {
		logger.Info(ctx, "no account filter present for fetching txns", zap.String(logger.ACTOR_ID_V2, currentActorId))
		return rpc.StatusRecordNotFound(), nil
	}

	for accountFilterIdx := range updatedAccountFilters {
		source, err := s.getSourceForAccount(ctx, currentActorId, updatedAccountFilters[accountFilterIdx])
		if err != nil {
			logger.Error(ctx, "error while categorising account by source", zap.Error(err))
			return rpc.StatusInternal(), nil
		}
		sourceToAccountMap[source] = append(sourceToAccountMap[source], updatedAccountFilters[accountFilterIdx])
	}

	for accountSource, accounts := range sourceToAccountMap {
		dataProvider, ok := s.sourceToDataProviderMap[accountSource]
		if !ok {
			logger.Error(ctx, "source is not mapped with data provider", zap.String(logger.DATA_SOURCE, accountSource.String()),
				zap.String(logger.ACCOUNT_ID, getListOfAccount(accounts)))
			continue
		}

		offset, err := getOffsetFromPageToken(accountSource, pageToken)
		if err != nil {
			logger.Error(ctx, "offset is not set in page token", zap.Error(err))
			return rpc.StatusInternal(), nil
		}

		dataWaitTimeFromSource, ok := sourceToDataWaitTimeDurationMap[accountSource]
		if !ok {
			// source timeout is not added into map, we will consider default as 10 second
			dataWaitTimeFromSource = defaultWaitTime
		}

		// main thread will wait for maximum time to get data from source
		if maxTimeToWait < dataWaitTimeFromSource {
			maxTimeToWait = dataWaitTimeFromSource
		}

		var accountsFilter []*dataprovider.AccountFilterCriteria
		for _, account := range accounts {
			accountsFilter = append(accountsFilter, &dataprovider.AccountFilterCriteria{
				AccountId:   account.GetAccountId(),
				AccountType: account.GetAccountType(),
			})
		}

		filterCriteria := &dataprovider.FilterCriteria{AccountFilterCriteria: accountsFilter}
		waitGroup.Add(1)
		goroutine.Run(ctx, dataWaitTimeFromSource, func(ctx context.Context) {
			dataProvider.GetActivityData(ctx, currentActorId, filterCriteria, offset, pageSize, startTimeStamp, descending, pageLandingTimestamp, dataCollectorChannel, waitGroup)
		})
	}

	// by calling waitGroup.Wait() in separate go routine ensure that main thread never go to deadlock
	// If Wait() is called in main thread deadlock condition: channel will wait from receiver to read channel and receiver will wait to finish processing.
	goroutine.Run(ctx, maxTimeToWait, func(ctx context.Context) {
		//nocustomlint:waitgroup
		waitGroup.Wait()
		close(dataCollectorChannel)
	})

	for activityResponse := range dataCollectorChannel {
		switch {
		case activityResponse == nil:
			logger.Error(ctx, "invalid response from some source")
		case activityResponse.Err != nil && !errors.Is(activityResponse.Err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "error response from activity data source", zap.String(logger.DATA_SOURCE, activityResponse.ResponseSource.String()), zap.Error(activityResponse.Err))
		default:
			errFromAllSource = false
			if activityResponse.Response != nil {
				activities = append(activities, activityResponse.Response...)
			}
		}
	}

	logger.Debug(ctx, "total activities from all source", zap.Int(logger.COUNT, len(activities)))

	if errFromAllSource {
		logger.Error(ctx, "error from all source actor activity")
		return rpc.StatusInternal(), nil
	}

	if len(activities) == 0 {
		logger.Debug(ctx, "no record found for activities")
		return rpc.StatusRecordNotFound(), nil
	}

	// sort the merged transactions
	if descending {
		sort.Slice(activities, func(i, j int) bool {
			return activities[i].ActivityTimestamp.AsTime().After(activities[j].GetActivityTimestamp().AsTime())
		})
	} else {
		sort.Slice(activities, func(i, j int) bool {
			return activities[i].ActivityTimestamp.AsTime().Before(activities[j].GetActivityTimestamp().AsTime())
		})
	}

	actorActivitiesResponse := convertToActivitiesResponse(activities)
	if len(actorActivitiesResponse) >= int(pageSize) {
		return nil, actorActivitiesResponse[:pageSize]
	}

	return nil, actorActivitiesResponse
}

func (s *Service) getSourceForAccount(ctx context.Context, actorId string, account *feActorActivityPb.GetActivitiesRequest_Account) (activityEnums.ActivitySource, error) {

	accountType, err := s.getAccountType(ctx, account.GetAccountId(), account.GetAccountType())
	if err != nil {
		return activityEnums.ActivitySource_ACTIVITY_SOURCE_UNSPECIFIED, err
	}
	switch accountType {
	case accountsPb.Type_TYPE_UNSPECIFIED:
		isAaAccount, err := s.isConnectedAccount(ctx, actorId, account.GetAccountId(), account.GetAccountType())
		if err != nil {
			return activityEnums.ActivitySource_ACTIVITY_SOURCE_UNSPECIFIED, err
		}
		if isAaAccount {
			return activityEnums.ActivitySource_CONNECTED_ACCOUNT_TRANSACTION, err
		}
		return activityEnums.ActivitySource_ACTIVITY_SOURCE_UNSPECIFIED, err
	case accountsPb.Type_CREDIT_CARD_ACCOUNT:
		return activityEnums.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD, nil
	default:
		return activityEnums.ActivitySource_FI_TRANSACTION, nil
	}
}

// isConnectedAccount checks if this account is connected account or not.
// Return false in case of failure with error.
func (s *Service) isConnectedAccount(ctx context.Context, actorId, accountId string, _ accountsPb.Type) (bool, error) {
	caResp, caErr := s.connectedAccountClient.GetAccountDetails(ctx, &connected_account.GetAccountDetailsRequest{
		AccountId: accountId,
		ActorId:   actorId,
	})
	switch {
	case caErr != nil:
		return false, fmt.Errorf("error fetching account details for account Id %s, err %w", accountId, caErr)
	case caResp.GetStatus().IsRecordNotFound() || caResp.GetStatus().IsInvalidArgument():
		return false, nil
	case !caResp.GetStatus().IsSuccess():
		return false, fmt.Errorf("GetAccountDetails() failed with status %s", caResp.GetStatus())
	default:
		return true, nil
	}
}

// getAccountType checks if the account has an account pi created for it and return the account type for it
func (s *Service) getAccountType(ctx context.Context, accountId string, accountType accountsPb.Type) (accountsPb.Type, error) {
	accountPiRes, err := s.accountPiClient.GetByAccountId(ctx, &accountPiPb.GetByAccountIdRequest{
		AccountId:   accountId,
		AccountType: accountType,
	})
	switch {
	case err != nil:
		return accountsPb.Type_TYPE_UNSPECIFIED, fmt.Errorf("error fetching accountPis for internal account Id %s, err %w", accountId, err)
	case accountPiRes.GetStatus().IsRecordNotFound():
		return accountsPb.Type_TYPE_UNSPECIFIED, nil
	case !accountPiRes.GetStatus().IsSuccess():
		return accountsPb.Type_TYPE_UNSPECIFIED, fmt.Errorf("GetByAccountId() failed with status %s", accountPiRes.GetStatus())
	// accountPiClient do not return record not found if account is not present. It will need explicit handling to check length
	case len(accountPiRes.GetAccountPis()) == 0:
		return accountsPb.Type_TYPE_UNSPECIFIED, nil
	default:
		return accountPiRes.GetAccountPis()[0].GetAccountType(), nil
	}
}

func getOffsetFromPageToken(accountSource activityEnums.ActivitySource, token PageToken) (int32, error) {
	switch accountSource {
	case activityEnums.ActivitySource_CONNECTED_ACCOUNT_TRANSACTION:
		return token.AaTransactionOffset, nil
	case activityEnums.ActivitySource_FI_TRANSACTION:
		return token.OrderOffset, nil
	case activityEnums.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD:
		return token.CcTransactionOffset, nil
	default:
		return 0, fmt.Errorf("offset not present in page token")
	}
}

func convertToActivitiesResponse(actvities []*activityPb.Activity) []*actorActivityPb.GetActivitiesResponse_Activity {
	var activitiesResponse []*actorActivityPb.GetActivitiesResponse_Activity

	for activityIndex := range actvities {
		activitiesResponse = append(activitiesResponse, convertToActivityResponse(actvities[activityIndex]))
	}
	return activitiesResponse
}

func convertToActivityResponse(activity *activityPb.Activity) *actorActivityPb.GetActivitiesResponse_Activity {
	actorActivityResponse := &actorActivityPb.GetActivitiesResponse_Activity{
		IconUrl:            activity.GetIconUrl(),
		Title:              activity.GetTitle(),
		ShortDesc:          activity.GetShortDesc(),
		ShortDescIconUrl:   activity.GetShortDescIconUrl(),
		Amount:             activity.GetAmount(),
		AmountBadge:        actorActivityToActivityAmountBadge(activity.GetAmountBadge()),
		ActivityTimestamp:  activity.GetActivityTimestamp(),
		ActivityId:         activity.GetActivityId(),
		SecondActorId:      activity.GetSecondActorId(),
		ActivityType:       actorActivityToActivityType(activity.GetActivityType()),
		ActivityEntryPoint: actorActivitySourceToActivitySource(activity.GetActivitySource()),
		PartnerTag:         activity.GetPartnerTag(),
	}

	if activity.GetTimelineId() != "" {
		actorActivityResponse.DeeplinkIdentifier = &actorActivityPb.GetActivitiesResponse_Activity_TimelineId{
			TimelineId: activity.GetTimelineId(),
		}
	}
	if activity.GetDepositAccountIdentifier() != nil {
		actorActivityResponse.DeeplinkIdentifier = &actorActivityPb.GetActivitiesResponse_Activity_DepositAccountIdentifier_{
			DepositAccountIdentifier: &actorActivityPb.GetActivitiesResponse_Activity_DepositAccountIdentifier{
				AccountId:   activity.GetDepositAccountIdentifier().GetAccountId(),
				AccountType: activity.GetDepositAccountIdentifier().GetAccountType(),
			},
		}
	}
	return actorActivityResponse
}

func actorActivityToActivityAmountBadge(badge activityPb.Activity_AmountBadge) actorActivityPb.GetActivitiesResponse_Activity_AmountBadge {
	switch badge {
	case activityPb.Activity_CREDIT:
		return actorActivityPb.GetActivitiesResponse_Activity_CREDIT
	case activityPb.Activity_DEBIT:
		return actorActivityPb.GetActivitiesResponse_Activity_DEBIT
	case activityPb.Activity_SAVINGS:
		return actorActivityPb.GetActivitiesResponse_Activity_SAVINGS
	default:
		return actorActivityPb.GetActivitiesResponse_Activity_AMOUNT_BADGE_UNSPECIFIED
	}
}

func actorActivityToActivityType(actorActivityType activityEnums.ActivityType) actorActivityPb.GetActivitiesResponse_Activity_Type {
	switch actorActivityType {
	case activityEnums.ActivityType_IMPS_TRANSACTION_SUCCESS:
		return actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_SUCCESS
	case activityEnums.ActivityType_IMPS_TRANSACTION_FAILED:
		return actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_FAILED
	case activityEnums.ActivityType_IMPS_TRANSACTION_REVERSED:
		return actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_REVERSED
	case activityEnums.ActivityType_NEFT_TRANSACTION_SUCCESS:
		return actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_SUCCESS
	case activityEnums.ActivityType_NEFT_TRANSACTION_FAILED:
		return actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_FAILED
	case activityEnums.ActivityType_NEFT_TRANSACTION_REVERSED:
		return actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_REVERSED
	case activityEnums.ActivityType_RTGS_TRANSACTION_FAILED:
		return actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_FAILED
	case activityEnums.ActivityType_RTGS_TRANSACTION_REVERSED:
		return actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_REVERSED
	case activityEnums.ActivityType_RTGS_TRANSACTION_SUCCESS:
		return actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_SUCCESS
	case activityEnums.ActivityType_INTRABANK_TRANSACTION_REVERSED:
		return actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_REVERSED
	case activityEnums.ActivityType_INTRABANK_TRANSACTION_FAILED:
		return actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_FAILED
	case activityEnums.ActivityType_INTRABANK_TRANSACTION_SUCCESS:
		return actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_SUCCESS
	case activityEnums.ActivityType_ATM_TRANSACTION_FAILED:
		return actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_FAILED
	case activityEnums.ActivityType_ATM_TRANSACTION_SUCCESS:
		return actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_SUCCESS
	case activityEnums.ActivityType_SMART_DEPOSIT_AMT_ADDED:
		return actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_AMT_ADDED
	case activityEnums.ActivityType_SMART_DEPOSIT_CREATED:
		return actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_CREATED
	case activityEnums.ActivityType_SMART_DEPOSIT_MATURED:
		return actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_MATURED
	case activityEnums.ActivityType_SMART_DEPOSIT_PRECLOSED:
		return actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_PRECLOSED
	case activityEnums.ActivityType_SMART_DEPOSIT_INTEREST_CREDIT:
		return actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_INTEREST_CREDIT
	case activityEnums.ActivityType_FIXED_DEPOSIT_CREATED:
		return actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_CREATED
	case activityEnums.ActivityType_FIXED_DEPOSIT_MATURED:
		return actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_MATURED
	case activityEnums.ActivityType_FIXED_DEPOSIT_PRECLOSED:
		return actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_PRECLOSED
	case activityEnums.ActivityType_FIXED_DEPOSIT_INTEREST_CREDIT:
		return actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_INTEREST_CREDIT
	case activityEnums.ActivityType_ATM_TRANSACTION_REVERSED:
		return actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_REVERSED
	case activityEnums.ActivityType_DEBIT_CARD_TRANSACTION_SUCCESS:
		return actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_SUCCESS
	case activityEnums.ActivityType_DEBIT_CARD_TRANSACTION_FAILED:
		return actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_FAILED
	case activityEnums.ActivityType_DEBIT_CARD_TRANSACTION_REVERSED:
		return actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_REVERSED
	case activityEnums.ActivityType_UPI_TRANSACTION_SUCCESS:
		return actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_SUCCESS
	case activityEnums.ActivityType_UPI_TRANSACTION_FAILED:
		return actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_FAILED
	case activityEnums.ActivityType_UPI_TRANSACTION_REVERSED:
		return actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_REVERSED
	case activityEnums.ActivityType_NEFT_TRANSACTION_PENDING:
		return actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_PENDING
	case activityEnums.ActivityType_RTGS_TRANSACTION_PENDING:
		return actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_PENDING
	case activityEnums.ActivityType_IMPS_TRANSACTION_PENDING:
		return actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_PENDING
	case activityEnums.ActivityType_UPI_TRANSACTION_PENDING:
		return actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_PENDING
	case activityEnums.ActivityType_INTRABANK_TRANSACTION_PENDING:
		return actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_PENDING
	case activityEnums.ActivityType_ENACH_TRANSACTION_FAILED:
		return actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_FAILED
	case activityEnums.ActivityType_ENACH_TRANSACTION_SUCCESS:
		return actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_SUCCESS
	case activityEnums.ActivityType_ENACH_TRANSACTION_PENDING:
		return actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_PENDING
	case activityEnums.ActivityType_ENACH_TRANSACTION_REVERSED:
		return actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_REVERSED
	default:
		return actorActivityPb.GetActivitiesResponse_Activity_ACTIVITY_TYPE_UNSPECIFIED
	}
}

func actorActivitySourceToActivitySource(actorActivitySource activityEnums.ActivitySource) actorActivityPb.ActivityEntryPoint {
	switch actorActivitySource {
	case activityEnums.ActivitySource_FI_TRANSACTION:
		return actorActivityPb.ActivityEntryPoint_ORDER
	case activityEnums.ActivitySource_CONNECTED_ACCOUNT_TRANSACTION:
		return actorActivityPb.ActivityEntryPoint_AA
	case activityEnums.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD:
		return actorActivityPb.ActivityEntryPoint_CREDIT_CARD
	default:
		return actorActivityPb.ActivityEntryPoint_ACTIVITY_ENTRY_POINT_UNSPECIFIED
	}
}

// getListOfAccount return list of account and account type in string format for easy logging
func getListOfAccount(accounts []*feActorActivityPb.GetActivitiesRequest_Account) string {
	listOfAccount := ""
	for accountIdx := range accounts {
		listOfAccount = fmt.Sprintf("%s,[%s,%s]", listOfAccount, accounts[accountIdx].GetAccountId(), accounts[accountIdx].GetAccountType())
	}
	return listOfAccount
}

// nolint:funlen
// returns a list of fe account filters after extracting relevant filters from derived account id if it is present
func (s *Service) getFeAccountFiltersFromDerivedAccountId(ctx context.Context, actorId string, accountFilters []*feActorActivityPb.GetActivitiesRequest_Account) ([]*feActorActivityPb.GetActivitiesRequest_Account, *rpc.Status) {
	var updatedAccountFilters []*feActorActivityPb.GetActivitiesRequest_Account
	for accountFilterIdx := range accountFilters {
		if accountFilters[accountFilterIdx].GetDerivedAccountId() != "" {
			derivedAccountId := accountFilters[accountFilterIdx].GetDerivedAccountId()
			derivedAccountIdProto, err := pay.GetDecodedDerivedAccountId(derivedAccountId)
			if err != nil {
				logger.Error(ctx, "error decoding derived account id", zap.Error(err), zap.String(logger.DERIVED_ACCOUNT_ID, derivedAccountId))
				return nil, rpc.StatusInvalidArgument()
			}

			// populating internal account filter
			if derivedAccountIdProto.GetInternalAccountId() != "" {
				updatedAccountFilters = append(updatedAccountFilters, &feActorActivityPb.GetActivitiesRequest_Account{
					AccountId:        derivedAccountIdProto.GetInternalAccountId(),
					AccountType:      accountFilters[accountFilterIdx].GetAccountType(),
					DerivedAccountId: derivedAccountId,
				})
			}
			// populating tpap account filter if it is not an internal account
			if derivedAccountIdProto.GetTpapAccountId() != "" && derivedAccountIdProto.GetInternalAccountId() == "" {
				updatedAccountFilters = append(updatedAccountFilters, &feActorActivityPb.GetActivitiesRequest_Account{
					AccountId:        derivedAccountIdProto.GetTpapAccountId(),
					AccountType:      accountFilters[accountFilterIdx].GetAccountType(),
					DerivedAccountId: derivedAccountId,
				})
			}

			// populating connected account filter
			if derivedAccountIdProto.GetConnectedAccountId() != "" {
				shouldApplyAccFilterForAaActivities := s.shouldAccFilterApplyForAaActivities(ctx, actorId, derivedAccountId, derivedAccountIdProto.GetConnectedAccountId())
				if shouldApplyAccFilterForAaActivities {
					updatedAccountFilters = append(updatedAccountFilters, &feActorActivityPb.GetActivitiesRequest_Account{
						AccountId:        derivedAccountIdProto.GetConnectedAccountId(),
						AccountType:      accountFilters[accountFilterIdx].GetAccountType(),
						DerivedAccountId: derivedAccountId,
					})
				}
			}

			// populating deposit account filter
			if derivedAccountIdProto.GetDepositAccountId() != "" {
				updatedAccountFilters = append(updatedAccountFilters, &feActorActivityPb.GetActivitiesRequest_Account{
					AccountId:        derivedAccountIdProto.GetDepositAccountId(),
					AccountType:      accountFilters[accountFilterIdx].GetAccountType(),
					DerivedAccountId: derivedAccountId,
				})
			}

			// populating credit account filter
			if derivedAccountIdProto.GetCreditCardAccountId() != "" {
				updatedAccountFilters = append(updatedAccountFilters, &feActorActivityPb.GetActivitiesRequest_Account{
					AccountId:        derivedAccountIdProto.GetCreditCardAccountId(),
					AccountType:      accountFilters[accountFilterIdx].GetAccountType(),
					DerivedAccountId: derivedAccountId,
				})
			}
		} else {
			updatedAccountFilters = append(updatedAccountFilters, &feActorActivityPb.GetActivitiesRequest_Account{
				AccountId:   accountFilters[accountFilterIdx].GetAccountId(),
				AccountType: accountFilters[accountFilterIdx].GetAccountType(),
			})
		}
	}

	return updatedAccountFilters, nil
}
