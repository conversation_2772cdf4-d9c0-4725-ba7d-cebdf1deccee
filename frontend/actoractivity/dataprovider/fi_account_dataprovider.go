package dataprovider

import (
	"context"
	"fmt"
	"sync"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/epifierrors"
	actorActivityPb "github.com/epifi/gamma/api/order/actoractivity"
	acitivityPb "github.com/epifi/gamma/api/order/actoractivity/activity"
	activityEnums "github.com/epifi/gamma/api/order/actoractivity/enums"

	"google.golang.org/protobuf/types/known/timestamppb"
)

type FiAccountDataProvider struct {
	actorActivityClient actorActivityPb.ActorActivityClient
}

func NewFiAccountDataProvider(actorActivityClient actorActivityPb.ActorActivityClient) DataProvider {
	var dataProvider DataProvider = &FiAccountDataProvider{
		actorActivityClient: actorActivityClient,
	}
	return dataProvider
}

// GetActivityData for FiAccountDataProvider fetches data for FiAccounts and return activity.
// nolint: funlen
func (f *FiAccountDataProvider) GetActivityData(ctx context.Context, currentActorId string, filterCriteria *FilterCriteria, offset int32, pageSize int32, startTimeStamp *timestamppb.Timestamp, descending bool, pageLandingTimestamp *timestamppb.Timestamp, activitiesResponse chan<- *ActivitiesResponse, wg *sync.WaitGroup) {

	var (
		beAccountFilters []*actorActivityPb.GetActivitiesRequest_AccountFilter
		activities       []*acitivityPb.Activity
	)
	defer wg.Done()

	if filterCriteria == nil || filterCriteria.AccountFilterCriteria == nil {
		activitiesResponse <- &ActivitiesResponse{
			Response:       nil,
			ResponseSource: activityEnums.ActivitySource_FI_TRANSACTION,
			Err:            fmt.Errorf("filterCriteria.AccountFilterCriteria should not be nil %w", epifierrors.ErrInvalidArgument),
		}
		return
	}

	for _, accountFilterCriteria := range filterCriteria.AccountFilterCriteria {
		beAccountFilters = append(beAccountFilters, &actorActivityPb.GetActivitiesRequest_AccountFilter{
			AccountId:   accountFilterCriteria.AccountId,
			AccountType: accountFilterCriteria.AccountType,
		})
	}

	// by passing the entry point we are hinting the actor activity service to provide all activities to show on UI
	// which calls a different RPC in order service to return successful transactions along with pending, failed and reversed transactions
	actorActivityRes, err := f.actorActivityClient.GetActivities(ctx, &actorActivityPb.GetActivitiesRequest{
		CurrentActorId:           currentActorId,
		AccountFilter:            beAccountFilters,
		ActivitiesStartTimestamp: startTimeStamp,
		PageSize:                 pageSize,
		OrderOffset:              offset,
		Descending:               descending,
		PageLandingTimestamp:     pageLandingTimestamp,
		EntryPointType:           actorActivityPb.GetActivitiesRequest_ENTRY_POINT_TYPE_UI,
	})

	switch {
	case err != nil:
		activitiesResponse <- &ActivitiesResponse{
			Response:       nil,
			ResponseSource: activityEnums.ActivitySource_FI_TRANSACTION,
			Err:            fmt.Errorf("error in error in fetching Fi Account Activities %w", err),
		}
		return
	case actorActivityRes.GetStatus().IsRecordNotFound():
		activitiesResponse <- &ActivitiesResponse{
			Response:       nil,
			ResponseSource: activityEnums.ActivitySource_FI_TRANSACTION,
			Err:            epifierrors.ErrRecordNotFound,
		}
		return
	case !actorActivityRes.GetStatus().IsSuccess():
		activitiesResponse <- &ActivitiesResponse{
			Response:       nil,
			ResponseSource: activityEnums.ActivitySource_FI_TRANSACTION,
			Err:            fmt.Errorf("non-success error code in fetching Fi Account Activities %w", epifigrpc.RPCError(actorActivityRes, err)),
		}
		return
	}

	logger.Debug(ctx, "no of activity received", zap.Int(logger.COUNT, len(actorActivityRes.GetActivities())), zap.String(logger.DATA_CHANNEL, activityEnums.ActivitySource_FI_TRANSACTION.String()))

	for idx := range actorActivityRes.GetActivities() {
		activities = append(activities, convertToActivity(actorActivityRes.GetActivities()[idx]))
	}

	activitiesResponse <- &ActivitiesResponse{
		Response:       activities,
		Err:            nil,
		ResponseSource: activityEnums.ActivitySource_FI_TRANSACTION,
	}
}

func convertToActivity(actorActivity *actorActivityPb.GetActivitiesResponse_Activity) *acitivityPb.Activity {
	activity := &acitivityPb.Activity{
		IconUrl:           actorActivity.GetIconUrl(),
		Title:             actorActivity.GetTitle(),
		ShortDesc:         actorActivity.GetShortDesc(),
		ShortDescIconUrl:  actorActivity.GetShortDescIconUrl(),
		Amount:            actorActivity.GetAmount(),
		AmountBadge:       actorActivityToActivityAmountBadge(actorActivity.GetAmountBadge()),
		ActivityTimestamp: actorActivity.GetActivityTimestamp(),
		ActivityId:        actorActivity.GetActivityId(),
		SecondActorId:     actorActivity.GetSecondActorId(),
		ActivityType:      actorActivityToActivityType(actorActivity.GetActivityType()),
		ActivitySource:    actorActivitySourceToActivitySource(actorActivity.GetActivityEntryPoint()),
		PartnerTag:        actorActivity.GetPartnerTag(),
	}
	if actorActivity.GetTimelineId() != "" {
		activity.DeeplinkIdentifier = &acitivityPb.Activity_TimelineId{
			TimelineId: actorActivity.GetTimelineId(),
		}
	}
	if actorActivity.GetDepositAccountIdentifier() != nil {
		activity.DeeplinkIdentifier = &acitivityPb.Activity_DepositAccountIdentifier_{
			DepositAccountIdentifier: &acitivityPb.Activity_DepositAccountIdentifier{
				AccountId:   actorActivity.GetDepositAccountIdentifier().GetAccountId(),
				AccountType: actorActivity.GetDepositAccountIdentifier().GetAccountType(),
			},
		}
	}
	return activity
}

func actorActivityToActivityAmountBadge(badge actorActivityPb.GetActivitiesResponse_Activity_AmountBadge) acitivityPb.Activity_AmountBadge {
	switch badge {
	case actorActivityPb.GetActivitiesResponse_Activity_CREDIT:
		return acitivityPb.Activity_CREDIT
	case actorActivityPb.GetActivitiesResponse_Activity_DEBIT:
		return acitivityPb.Activity_DEBIT
	case actorActivityPb.GetActivitiesResponse_Activity_SAVINGS:
		return acitivityPb.Activity_SAVINGS
	default:
		return acitivityPb.Activity_AMOUNT_BADGE_UNSPECIFIED
	}
}

func actorActivityToActivityType(actorActivityType actorActivityPb.GetActivitiesResponse_Activity_Type) activityEnums.ActivityType {
	switch actorActivityType {
	case actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_SUCCESS:
		return activityEnums.ActivityType_IMPS_TRANSACTION_SUCCESS
	case actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_FAILED:
		return activityEnums.ActivityType_IMPS_TRANSACTION_FAILED
	case actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_REVERSED:
		return activityEnums.ActivityType_IMPS_TRANSACTION_REVERSED
	case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_SUCCESS:
		return activityEnums.ActivityType_NEFT_TRANSACTION_SUCCESS
	case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_FAILED:
		return activityEnums.ActivityType_NEFT_TRANSACTION_FAILED
	case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_REVERSED:
		return activityEnums.ActivityType_NEFT_TRANSACTION_REVERSED
	case actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_FAILED:
		return activityEnums.ActivityType_RTGS_TRANSACTION_FAILED
	case actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_REVERSED:
		return activityEnums.ActivityType_RTGS_TRANSACTION_REVERSED
	case actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_SUCCESS:
		return activityEnums.ActivityType_RTGS_TRANSACTION_SUCCESS
	case actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_REVERSED:
		return activityEnums.ActivityType_INTRABANK_TRANSACTION_REVERSED
	case actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_FAILED:
		return activityEnums.ActivityType_RTGS_TRANSACTION_FAILED
	case actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_SUCCESS:
		return activityEnums.ActivityType_INTRABANK_TRANSACTION_SUCCESS
	case actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_FAILED:
		return activityEnums.ActivityType_ATM_TRANSACTION_FAILED
	case actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_SUCCESS:
		return activityEnums.ActivityType_ATM_TRANSACTION_SUCCESS
	case actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_AMT_ADDED:
		return activityEnums.ActivityType_SMART_DEPOSIT_AMT_ADDED
	case actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_CREATED:
		return activityEnums.ActivityType_SMART_DEPOSIT_CREATED
	case actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_MATURED:
		return activityEnums.ActivityType_SMART_DEPOSIT_MATURED
	case actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_PRECLOSED:
		return activityEnums.ActivityType_SMART_DEPOSIT_PRECLOSED
	case actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_INTEREST_CREDIT:
		return activityEnums.ActivityType_SMART_DEPOSIT_INTEREST_CREDIT
	case actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_CREATED:
		return activityEnums.ActivityType_FIXED_DEPOSIT_CREATED
	case actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_MATURED:
		return activityEnums.ActivityType_FIXED_DEPOSIT_MATURED
	case actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_PRECLOSED:
		return activityEnums.ActivityType_FIXED_DEPOSIT_PRECLOSED
	case actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_INTEREST_CREDIT:
		return activityEnums.ActivityType_FIXED_DEPOSIT_INTEREST_CREDIT
	case actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_REVERSED:
		return activityEnums.ActivityType_ATM_TRANSACTION_REVERSED
	case actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_SUCCESS:
		return activityEnums.ActivityType_DEBIT_CARD_TRANSACTION_SUCCESS
	case actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_FAILED:
		return activityEnums.ActivityType_DEBIT_CARD_TRANSACTION_FAILED
	case actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_REVERSED:
		return activityEnums.ActivityType_DEBIT_CARD_TRANSACTION_REVERSED
	case actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_SUCCESS:
		return activityEnums.ActivityType_UPI_TRANSACTION_SUCCESS
	case actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_FAILED:
		return activityEnums.ActivityType_UPI_TRANSACTION_FAILED
	case actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_REVERSED:
		return activityEnums.ActivityType_UPI_TRANSACTION_REVERSED
	case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_PENDING:
		return activityEnums.ActivityType_NEFT_TRANSACTION_PENDING
	case actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_PENDING:
		return activityEnums.ActivityType_IMPS_TRANSACTION_PENDING
	case actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_PENDING:
		return activityEnums.ActivityType_UPI_TRANSACTION_PENDING
	case actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_PENDING:
		return activityEnums.ActivityType_RTGS_TRANSACTION_PENDING
	case actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_PENDING:
		return activityEnums.ActivityType_INTRABANK_TRANSACTION_PENDING
	case actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_FAILED:
		return activityEnums.ActivityType_ENACH_TRANSACTION_FAILED
	case actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_SUCCESS:
		return activityEnums.ActivityType_ENACH_TRANSACTION_SUCCESS
	case actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_PENDING:
		return activityEnums.ActivityType_ENACH_TRANSACTION_PENDING
	case actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_REVERSED:
		return activityEnums.ActivityType_ENACH_TRANSACTION_REVERSED
	default:
		return activityEnums.ActivityType_ACTIVITY_TYPE_UNSPECIFIED
	}
}

func actorActivitySourceToActivitySource(actorActivityEntryPoint actorActivityPb.ActivityEntryPoint) activityEnums.ActivitySource {
	switch actorActivityEntryPoint {
	case actorActivityPb.ActivityEntryPoint_ORDER:
		return activityEnums.ActivitySource_FI_TRANSACTION
	case actorActivityPb.ActivityEntryPoint_AA:
		return activityEnums.ActivitySource_CONNECTED_ACCOUNT_TRANSACTION
	default:
		return activityEnums.ActivitySource_ACTIVITY_SOURCE_UNSPECIFIED
	}
}
