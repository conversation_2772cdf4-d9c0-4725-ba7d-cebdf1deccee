package actoractivity

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	beOrderPb "github.com/epifi/gamma/api/order"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"

	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/epifi/be-common/pkg/constants"
	feCaFeatures "github.com/epifi/gamma/api/frontend/connected_account/features"
	"github.com/epifi/gamma/api/frontend/header"
	feSearch "github.com/epifi/gamma/api/frontend/search"
	types "github.com/epifi/gamma/api/typesv2"
	feCaConnectFiToFi "github.com/epifi/gamma/frontend/connected_account/fi_to_fi"
	"github.com/epifi/gamma/pkg/feature/release"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	actorPb "github.com/epifi/gamma/api/actor"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	beCategorizerPb "github.com/epifi/gamma/api/categorizer"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	depositPb "github.com/epifi/gamma/api/deposit"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	feActorActivityPb "github.com/epifi/gamma/api/frontend/actoractivity"
	feDeeplink "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/search/widget"
	actorActivityPb "github.com/epifi/gamma/api/order/actoractivity"
	activityEnums "github.com/epifi/gamma/api/order/actoractivity/enums"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	timelinePb "github.com/epifi/gamma/api/timeline"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/frontend/actoractivity/dataprovider"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	feCa "github.com/epifi/gamma/frontend/connected_account"
	fePayTransaction "github.com/epifi/gamma/frontend/pay/transaction"
	featureCfg "github.com/epifi/gamma/pkg/feature"
	"github.com/epifi/gamma/pkg/pay"
)

const (
	pageSize = 30
	// todo: where to keep this config not sure was not able to see the structe in frontend-params
)

var (
	statusCorruptedToken rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(
			uint32(feActorActivityPb.GetActivitiesResponse_PAGE_TOKEN_CORRUPTED),
			"page token corrupted",
			"unable to unmarhsal/decode page token",
		)
	}

	beAmountBadgeToFeAmountBadgeMap = map[actorActivityPb.GetActivitiesResponse_Activity_AmountBadge]widget.AmountBadge{
		actorActivityPb.GetActivitiesResponse_Activity_CREDIT:  widget.AmountBadge_CREDIT,
		actorActivityPb.GetActivitiesResponse_Activity_DEBIT:   widget.AmountBadge_DEBIT,
		actorActivityPb.GetActivitiesResponse_Activity_SAVINGS: widget.AmountBadge_SAVINGS,
	}

	permissionDeniedErr = errors.New("permission denied")
)

type Service struct {
	// UnimplementedUPIServer is embedded to have forward compatible implementations.
	feActorActivityPb.UnimplementedActorActivityServer
	actorActivityClient          actorActivityPb.ActorActivityClient
	savingsClient                savingsPb.SavingsClient
	piClient                     piPb.PiClient
	cardProvisioningClient       cardPb.CardProvisioningClient
	amountColourMap              *config.AmountColourMap
	actorActivityTimeStampLayout string
	depositClient                depositPb.DepositClient
	connectedAccountClient       connectedAccountPb.ConnectedAccountClient
	categorizerClient            beCategorizerPb.TxnCategorizerClient
	searchClient                 feSearch.SearchClient
	similarActivityParams        *config.SimilarActivityParams
	amountBadgeIconUrlMap        *config.AmountBadgeIconUrlMap
	userGroupClient              userGroupPb.GroupClient
	userClient                   userPb.UsersClient
	actorClient                  actorPb.ActorClient
	accountPiClient              accountPiPb.AccountPIRelationClient
	sourceToDataProviderMap      map[activityEnums.ActivitySource]dataprovider.DataProvider
	dynconf                      *genconf.Config
	ffAccountingClient           ffAccountsPb.AccountingClient
	upiOnboardingClient          upiOnboardingPb.UpiOnboardingClient
	orderClient                  beOrderPb.OrderServiceClient
	rewardsGeneratorClient       rewardsPb.RewardsGeneratorClient
	connectedAccountFiToFiSvc    feCaConnectFiToFi.IConnectFiToFiSvc
	rewardsAggregatesClient      rewardspinotpb.RewardsAggregatesClient
	releaseEvaluator             release.IEvaluator
}

func NewService(
	actorActivityClient actorActivityPb.ActorActivityClient,
	savingsClient savingsPb.SavingsClient,
	piClient piPb.PiClient,
	cardProvisioningClient cardPb.CardProvisioningClient,
	conf *config.Config,
	depositClient depositPb.DepositClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	categorizerClient beCategorizerPb.TxnCategorizerClient,
	userGroupClient userGroupPb.GroupClient,
	userClient userPb.UsersClient,
	actorClient actorPb.ActorClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	feSearchClient feSearch.SearchClient,
	dynconf *genconf.Config,
	ffAccountingClient ffAccountsPb.AccountingClient,
	timelineClient timelinePb.TimelineServiceClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	orderClient beOrderPb.OrderServiceClient,
	rewardsGeneratorClient rewardsPb.RewardsGeneratorClient,
	connectedAccountFiToFiSvc feCaConnectFiToFi.IConnectFiToFiSvc,
	rewardsAggregatesClient rewardspinotpb.RewardsAggregatesClient,
	releaseEvaluator release.IEvaluator,
) *Service {

	sourceToDataProviderMap := make(map[activityEnums.ActivitySource]dataprovider.DataProvider)
	aaAccountDataProvider := dataprovider.NewAaAccountDataProvider(actorActivityClient)
	fiAccountDataProvider := dataprovider.NewFiAccountDataProvider(actorActivityClient)
	ccAccountDataProvider := dataprovider.NewCcAccountDataProvider(ffAccountingClient, timelineClient, actorClient, conf,
		dynconf, userGroupClient, userClient)
	sourceToDataProviderMap[activityEnums.ActivitySource_FI_TRANSACTION] = fiAccountDataProvider
	sourceToDataProviderMap[activityEnums.ActivitySource_CONNECTED_ACCOUNT_TRANSACTION] = aaAccountDataProvider
	sourceToDataProviderMap[activityEnums.ActivitySource_ACTIVITY_SOURCE_CREDIT_CARD] = ccAccountDataProvider
	return &Service{
		actorActivityClient:          actorActivityClient,
		savingsClient:                savingsClient,
		amountColourMap:              conf.ActorActivityAmountColourMap,
		piClient:                     piClient,
		cardProvisioningClient:       cardProvisioningClient,
		actorActivityTimeStampLayout: conf.ActorActivityTimeStampLayout,
		depositClient:                depositClient,
		connectedAccountClient:       connectedAccountClient,
		categorizerClient:            categorizerClient,
		searchClient:                 feSearchClient,
		similarActivityParams:        conf.SimilarActivityParams,
		amountBadgeIconUrlMap:        conf.ActorActivityAmountBadgeIconUrlMap,
		userGroupClient:              userGroupClient,
		actorClient:                  actorClient,
		userClient:                   userClient,
		accountPiClient:              accountPiClient,
		sourceToDataProviderMap:      sourceToDataProviderMap,
		dynconf:                      dynconf,
		ffAccountingClient:           ffAccountingClient,
		upiOnboardingClient:          upiOnboardingClient,
		orderClient:                  orderClient,
		rewardsGeneratorClient:       rewardsGeneratorClient,
		connectedAccountFiToFiSvc:    connectedAccountFiToFiSvc,
		rewardsAggregatesClient:      rewardsAggregatesClient,
		releaseEvaluator:             releaseEvaluator,
	}
}

// A paginated rpc to fetch the activities belonging to a particular actor.
// If page size is not specified in the request, default page size is 30
//
// Along with activity response contains
// before_token, after_token representing the location of page fetched.
// response also contains a list of filter options to provided to the user
//
// Currently all the activity details will be fetched from order
// If FetchLatestPage is marked true. All the latest activities are
// returned in descending order from current time.
// This RPC also handles the search on transctions. If the length of query
// is greater than 1, then the search on transactions is triggered to handle.
// nolint:funlen, gocritic
func (s *Service) GetActivities(ctx context.Context, req *feActorActivityPb.GetActivitiesRequest) (*feActorActivityPb.GetActivitiesResponse, error) {
	var (
		res                           = &feActorActivityPb.GetActivitiesResponse{}
		beforeToken                   *beforePageToken
		afterToken                    *afterPageToken
		orderOffset                   int32
		aaTxnOffset                   int32
		ccTxnOffset                   int32
		orderStartTimeStamp           *timestampPb.Timestamp
		pageLandingTimestamp          *timestampPb.Timestamp
		descending                    = true
		getActorActivitiesStatus      = &rpc.Status{}
		actorActivities               []*actorActivityPb.GetActivitiesResponse_Activity
		activityIdActivityCategoryMap map[string]*beCategorizerPb.ActivityCategories
		err                           error
		actorId                       = req.GetReq().GetAuth().GetActorId()
		appVersion                    = req.GetReq().GetAppVersionCode()
		appPlatform                   = req.GetReq().GetAuth().GetDevice().GetPlatform()
		activityIdToRewardEarnedMap   map[string]*widget.RewardsEarnedValueChip
		rewardSummaryWidgetsMap       map[string]*widget.RewardSummaryWidget
		// this secondaryWg is used for secondary purposes and shouldn't be combined with primary use-cases of concurrency for generating actor activities.
		secondaryWg = &sync.WaitGroup{}
	)
	logger.Debug(ctx, "request to GetActivities", zap.Any("req", req))

	// populate the partner tag for the screen
	res.PartnerTag = getPartnerTag(req)

	// client does not pass any account identifiers for fetching the transactions
	// this is the case when user has onboarded as Fi-lite user and has not connected any Tpap account or Connected account(AA)
	if req.GetSearchCriteria() == nil && len(req.GetAccountFilter()) == 0 {
		// todo(Harleen Singh): remove this when Fi-lite is rolled out to external user
		logger.Info(ctx, "no search criteria or account filter in the request")
		res.Status = rpc.StatusRecordNotFoundWithDebugMsg("no search criteria or account filter in the request")
		return res, nil
	}

	// If the search query is at least 2 characters then we start searching on
	// all the transactions.
	if len(req.GetQuerySearch().GetQuery()) > 1 {
		logger.Debug(ctx, "searching from ES", zap.Any("query", req.GetQuerySearch().GetQuery()))
		var derivedAccountIDs []string
		for _, eachAccount := range req.GetAccountCriteria().GetAccounts() {
			derivedAccountIDs = append(derivedAccountIDs, eachAccount.GetDerivedAccountId())
		}
		searchTxnsReq := &feSearch.SearchTransactionsRequest{
			Req:   req.GetReq(),
			Query: req.GetQuerySearch().GetQuery(),
			Filter: &feSearch.SearchTransactionsFilter{
				DerivedAccountIds: derivedAccountIDs,
			},
			BeforeToken: req.GetBeforeToken(),
			AfterToken:  req.GetAfterToken(),
		}
		searchTransactions, searchTransactionsErr := s.searchClient.SearchTransactions(ctx, searchTxnsReq)
		logger.Debug(ctx, "request to SearchTransactions", zap.Any("searchTxnsReq", searchTxnsReq), zap.Error(searchTransactionsErr))
		if searchTransactionsErr != nil {
			logger.Error(ctx, "error while searching through transactions", zap.Error(searchTransactionsErr))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusOk(),
		}
		res.Status = rpc.StatusOk()
		res.Transactions = searchTransactions.GetTransactions()
		res.BeforeToken = searchTransactions.GetBeforeToken()
		res.AfterToken = searchTransactions.GetAfterToken()
		return res, nil
	}

	switch req.GetToken().(type) {
	case *feActorActivityPb.GetActivitiesRequest_BeforeToken:
		beforeToken = &beforePageToken{}
		err = beforeToken.UnmarshalToken(req.GetBeforeToken())
		if err != nil {
			logger.Error(ctx, "corrupted page token",
				zap.String("before-token", req.GetAfterToken()),
				zap.Error(err))

			res.Status = statusCorruptedToken()
			return res, nil
		}
		orderStartTimeStamp = beforeToken.FirstOrderTimeStamp
		orderOffset = beforeToken.OrderOffset
		aaTxnOffset = beforeToken.AaTransactionOffset
		ccTxnOffset = beforeToken.CcTransactionOffset
		pageLandingTimestamp = beforeToken.PageLandingTimestamp
	case *feActorActivityPb.GetActivitiesRequest_AfterToken:
		afterToken = &afterPageToken{}
		descending = false
		err = afterToken.UnmarshalToken(req.GetAfterToken())
		if err != nil {
			logger.Error(ctx, "corrupted page token",
				zap.String("after-token", req.GetAfterToken()), zap.Error(err))
			res.Status = statusCorruptedToken()
			return res, nil
		}
		orderStartTimeStamp = afterToken.LastOrderTimestamp
		orderOffset = afterToken.OrderOffset
		aaTxnOffset = afterToken.AaTransactionOffset
		ccTxnOffset = afterToken.CcTransactionOffset
		pageLandingTimestamp = beforeToken.PageLandingTimestamp
	case *feActorActivityPb.GetActivitiesRequest_LatestPage:
		descending = true
		orderOffset = 0
		aaTxnOffset = 0
		ccTxnOffset = 0
		orderStartTimeStamp = timestampPb.Now()
		pageLandingTimestamp = timestampPb.Now()
	default:
		logger.Error(ctx, "page token type not supported")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	if req.GetPageSize() == 0 {
		req.PageSize = pageSize
	}

	actorId = req.GetReq().GetAuth().GetActorId()

	expiredConsentsAccountsDetails, err := s.checkIfConsentExpiredForAllAaAccounts(ctx, req)
	if err != nil {
		// suppressing the error to continue fetching requested activities
		logger.Error(ctx, "failed to check if consent of requested connected account are expired", zap.String(logger.ACTOR_ID_V2, actorId))
	}

	if len(expiredConsentsAccountsDetails) > 0 {
		var consentRenewalWidget *feCaFeatures.AaConsentRenewalWidget
		consentRenewalWidget, err = feCa.GetConsentRenewalWidget(expiredConsentsAccountsDetails[0].GetAccountDetails().GetFipId())
		if err != nil {
			logger.Error(ctx, "failed to get consent renewal widget", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		res.ConsentRenewalWidget = consentRenewalWidget
		res.Status = rpc.NewStatusWithoutDebug(uint32(feActorActivityPb.GetActivitiesResponse_AA_CONSENT_EXPIRED), "aa consent expired for selected accounts")
		return res, nil
	}

	// linkFiToFiBottomSheet represents bottom sheet on all transactions page to initiate the linking of Fi To Fi via connected accounts Flow.
	// This is a blocking step for all transactions when user has selected Fi-Federal account as one of the account filter. For eg.:
	// the users selects All banks filter or Fi account as a filter.
	// Note: Any error received while getting the bottom sheet will be suppressed and logged only.
	linkFiToFiBottomSheet, linkFiToFiBottomSheetErr := s.getFiToFiBottomSheet(ctx, actorId, appVersion, appPlatform, req)
	if linkFiToFiBottomSheetErr != nil {
		// error logged and normal flow for fetching all activities is continued
		logger.Error(ctx, "failed to getFiToFiBottomSheet", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(linkFiToFiBottomSheetErr))
	}
	res.InitiateFiToFiFlowBottomSheet = linkFiToFiBottomSheet

	getActorActivitiesStatus, actorActivities = s.getActivitiesByFilter(ctx, req, orderOffset, aaTxnOffset, ccTxnOffset, orderStartTimeStamp, descending, pageLandingTimestamp)

	switch {
	case getActorActivitiesStatus.IsRecordNotFound():
		switch req.GetToken().(type) {
		case *feActorActivityPb.GetActivitiesRequest_LatestPage:
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		res.Status = rpc.StatusOk()
		return res, nil
	case getActorActivitiesStatus != nil:
		res.Status = getActorActivitiesStatus
		return res, nil
	}

	secondaryWg.Add(1)
	goroutine.Run(ctx, 1*time.Second, func(ctx context.Context) {
		defer secondaryWg.Done()
		activityIdToRewardEarnedMap, err = s.getActorActivityIdToRewardsMap(ctx, actorId, actorActivities)
		if err != nil {
			logger.Error(ctx, "failed to fetch the rewards details from actor activities", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return
		}
	})
	secondaryWg.Add(1)
	goroutine.Run(ctx, 1*time.Second, func(ctx context.Context) {
		defer secondaryWg.Done()
		rewardSummaryWidgetsMap, err = s.getRewardSummaryWidgetsMap(ctx, actorId, actorActivities)
		if err != nil {
			logger.Error(ctx, "failed to fetch reward summary widget in actor activities page", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return
		}
	})
	// fetched map of activity Id and activity category based on actor Id and activity id.
	authHeader := req.GetReq().GetAuth()
	activityIdActivityCategoryMap, err = s.getCategoriesRelatedToActorActivity(ctx, authHeader.GetActorId(), actorActivities)
	if err != nil {
		logger.Error(ctx, "Not able to fetch activity category for actor", zap.String(logger.ACTOR_ID_V2, authHeader.GetActorId()), zap.Error(err))
	}

	// purpose is to wait for go-routines which serve secondary use-cases,
	// thus, the secondaryWg.Wait() should not be moved elsewhere in the code (to prevent blocking the execution midway)
	if secondaryWg != nil {
		waitgroup.SafeWaitCtx(ctx, secondaryWg)
	}

	transactionWidgets := s.getTransactionsWidgetList(actorActivities, activityIdActivityCategoryMap, activityIdToRewardEarnedMap, rewardSummaryWidgetsMap)
	err = setPageTokens(ctx, res, actorActivities, beforeToken, afterToken, pageLandingTimestamp)
	if err != nil {
		logger.Error(ctx, "failed to generate after token for current page", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.Transactions = transactionWidgets
	res.Status = rpc.StatusOk()
	return res, nil
}

// getCategoriesRelatedToActorActivity fetches map of activity id and corresponding list of display category for given actor id and its activity.
func (s *Service) getCategoriesRelatedToActorActivity(ctx context.Context, actorId string, actorActivities []*actorActivityPb.GetActivitiesResponse_Activity) (map[string]*beCategorizerPb.ActivityCategories, error) {
	// get BatchCategory ActivityIds Request for BatchGetCategories RPC.
	activityIdsReq, err := getBatchCategoryActivityIdsRequest(actorActivities)
	if err != nil {
		return nil, fmt.Errorf("error while fetching ActivityIds Request for BatchGetCategories RPC: %w", err)
	}

	if len(activityIdsReq) == 0 {
		return nil, fmt.Errorf("No txnIds or orderIds or aaTxnIds")
	}

	getBatchCategoriesResp, err := s.categorizerClient.BatchGetCategories(ctx, &beCategorizerPb.BatchGetCategoriesRequest{
		ActorId:     actorId,
		ActivityIds: activityIdsReq,
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("error fetching Categories list of activities for actor Id: %s :%w", actorId, err)
	case !getBatchCategoriesResp.GetStatus().IsSuccess():
		return nil, fmt.Errorf("getBatchCategoriesResp() rpc call failed with status: %s", getBatchCategoriesResp.GetStatus().String())
	}

	return getBatchCategoriesResp.GetActivitiesCategories(), nil
}

// getBatchCategoryActivityIdsRequest fetches BatchCategory ActivityIds Request for BatchGetCategories RPC.
func getBatchCategoryActivityIdsRequest(actorActivities []*actorActivityPb.GetActivitiesResponse_Activity) ([]*beCategorizerPb.ActivityId, error) {
	var (
		activityId     string
		activityIdsReq []*beCategorizerPb.ActivityId
	)

	// for IN_PAYMENT, FAILED and REVERSED transactions we would not want to show transaction categorisation as, we want to show short desc of transaction type than transaction category
	orderStatusToIgnoreTransactionCategorisation := []actorActivityPb.GetActivitiesResponse_Activity_Type{
		actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_REVERSED,
		actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_REVERSED,
		actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_REVERSED,
		actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_REVERSED,
		actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_REVERSED,

		actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_FAILED,
		actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_FAILED,
		actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_FAILED,
		actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_FAILED,
		actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_FAILED,

		actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_PENDING,
		actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_PENDING,
		actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_PENDING,
		actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_PENDING,
		actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_PENDING,
	}

	for _, actorActivity := range actorActivities {
		activityId = actorActivity.GetActivityId()
		switch actorActivity.GetActivityEntryPoint() {
		case actorActivityPb.ActivityEntryPoint_ORDER:
			if lo.Contains(orderStatusToIgnoreTransactionCategorisation, actorActivity.GetActivityType()) {
				// don't get the activity category status for FAILED, IN_PAYMENT and REVERSED transactions
				continue
			}
			activityIdsReq = append(activityIdsReq, &beCategorizerPb.ActivityId{
				Id: &beCategorizerPb.ActivityId_OrderId{
					OrderId: activityId,
				},
			})
		case actorActivityPb.ActivityEntryPoint_AA:
			activityIdsReq = append(activityIdsReq, &beCategorizerPb.ActivityId{
				Id: &beCategorizerPb.ActivityId_AaTxnId{
					AaTxnId: activityId,
				},
			})
		case actorActivityPb.ActivityEntryPoint_CREDIT_CARD:
			// TODO(priyansh) : Change this once we have support for CC txns from cartegorizer service
			activityIdsReq = append(activityIdsReq, &beCategorizerPb.ActivityId{
				Id: &beCategorizerPb.ActivityId_AaTxnId{
					AaTxnId: activityId,
				},
			})
		default:
			return nil, fmt.Errorf("unspecified Activity Entry Point for activity id: %s", activityId)
		}
	}
	return activityIdsReq, nil
}

// nolint: funlen
// getActivitiesByFilter fetches activities according to the search criteria passed in the request
func (s *Service) getActivitiesByFilter(ctx context.Context, req *feActorActivityPb.GetActivitiesRequest, orderOffset, aaTxnOffset, ccTxnOffset int32, orderStartTimeStamp *timestampPb.Timestamp, descending bool, queryStartTimestamp *timestampPb.Timestamp) (*rpc.Status, []*actorActivityPb.GetActivitiesResponse_Activity) {
	var (
		beAccountFilter []*actorActivityPb.GetActivitiesRequest_AccountFilter
		piIds           []string
		entryPointType  = actorActivityPb.GetActivitiesRequest_ENTRY_POINT_TYPE_UNSPECIFIED
	)
	// Keeping this for backward compatibility
	actorId := req.GetReq().GetAuth().GetActorId()
	actorResp, errResp := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
	if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
		logger.Error(ctx, "Error in finding actor", zap.Error(err))
		return rpc.StatusInternal(), nil
	}
	actor := actorResp.GetActor()
	if len(req.GetAccountFilter()) != 0 {
		status := s.beValidateRequest(ctx, actor.GetEntityId(), req.GetAccountFilter(), nil, actorId)
		if status != nil {
			return status, nil
		}
		beAccountFilter = getBeAccountFilter(req.GetAccountFilter())
		return s.beGetActorActivities(ctx, actorId, beAccountFilter, piIds, orderStartTimeStamp, req.GetPageSize(), orderOffset, aaTxnOffset, descending, queryStartTimestamp, entryPointType)
	}

	switch req.GetSearchCriteria().(type) {
	case *feActorActivityPb.GetActivitiesRequest_AccountCriteria_:
		if featureCfg.IsFeatureEnabledForUser(ctx, actorId, &cfg.FeatureReleaseConfig{
			IsFeatureRestricted: s.dynconf.Flags().AllTransactionWithCCFeatureFlag().IsFeatureRestricted(),
			AllowedUserGroups:   s.dynconf.Flags().AllTransactionWithCCFeatureFlag().AllowedUserGroups(),
		}, s.userGroupClient, s.userClient, s.actorClient) {
			pageToken := PageToken{
				Timestamp:            orderStartTimeStamp,
				OrderOffset:          orderOffset,
				AaTransactionOffset:  aaTxnOffset,
				CcTransactionOffset:  ccTxnOffset,
				PageLandingTimestamp: queryStartTimestamp,
			}
			pageTokenString, err := pageToken.MarshalToken()
			if err != nil {
				logger.Error(ctx, "error in marshalling page token", zap.Error(err))
				return rpc.StatusInternal(), nil
			}

			status := s.beValidateRequest(ctx, actor.GetEntityId(), nil, req.GetAccountCriteria().GetAccounts(), actorId)
			if status != nil {
				return status, nil
			}

			return s.collectAllActivitiesFromSource(ctx, actorId, req.GetAccountCriteria().GetAccounts(), orderStartTimeStamp, req.GetPageSize(), pageTokenString, descending, queryStartTimestamp)
		}
		status := s.beValidateRequest(ctx, actor.GetEntityId(), nil, req.GetAccountCriteria().GetAccounts(), actorId)
		if status != nil {
			return status, nil
		}
		beAccountFilter, status = s.getBeAccountFilterFromAccount(ctx, actorId, req.GetAccountCriteria().GetAccounts())
		if status != nil {
			return status, nil
		}
	case *feActorActivityPb.GetActivitiesRequest_DebitCardCriteria:
		cardPIIds, status := s.fetchCardPiIds(ctx, req.GetDebitCardCriteria().GetDebitCardIds(), actorId)
		if status != nil {
			return status, nil
		}
		piIds = cardPIIds
		entryPointType = actorActivityPb.GetActivitiesRequest_ENTRY_POINT_TYPE_UI
	}

	return s.beGetActorActivities(
		ctx,
		actorId,
		beAccountFilter,
		piIds,
		orderStartTimeStamp,
		req.GetPageSize(),
		orderOffset,
		aaTxnOffset,
		descending,
		queryStartTimestamp,
		entryPointType,
	)
}

// checkIfConsentExpiredForAllAaAccounts : does the following checks:
// 1. If all selected accounts are of AA, else return nil,nil
// 2. If for all selected AA accounts consent has expired, else return nil,nil
func (s *Service) checkIfConsentExpiredForAllAaAccounts(ctx context.Context, req *feActorActivityPb.GetActivitiesRequest) ([]*connectedAccountPb.RenewalAccountDetails, error) {
	var (
		connectedAccIds           []string
		filteredRenewalAccDetails []*connectedAccountPb.RenewalAccountDetails
	)

	actorId := req.GetReq().GetAuth().GetActorId()

	releaseConstraint := release.NewCommonConstraintData(types.Feature_AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET).WithActorId(actorId)
	isReleased, err := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate feature AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET, %w", err)
	}

	if !isReleased {
		return nil, nil
	}

	if req.GetAccountCriteria() == nil {
		return nil, nil
	}

	accountsList := req.GetAccountCriteria().GetAccounts()

	if len(accountsList) == 0 {
		return nil, nil
	}

	for _, account := range accountsList {
		derivedAccId, err := pay.GetDecodedDerivedAccountId(account.GetDerivedAccountId())
		if err != nil {
			return nil, fmt.Errorf("error decoding derived account id, %w", err)
		}

		if derivedAccId.GetConnectedAccountId() == "" {
			// skip proceeding further even if one account in request is not a connected account
			return nil, nil
		}

		connectedAccIds = append(connectedAccIds, derivedAccId.GetConnectedAccountId())
	}

	getAccForRenewalList, err := s.connectedAccountClient.GetAccountsForRenewal(ctx, &connectedAccountPb.GetAccountsForRenewalRequest{
		ActorId:                    actorId,
		OnlyExpired:                true,
		CheckSegmentForEligibility: true,
		SortAccounts:               true,
	})
	if rpcErr := epifigrpc.RPCError(getAccForRenewalList, err); rpcErr != nil {
		return nil, fmt.Errorf("failed to get accounts details for consent renewal, %w", rpcErr)
	}

	renewalAccList := getAccForRenewalList.GetRenewalAccountDetailsList()

	if len(renewalAccList) == 0 {
		return nil, nil
	}

	// lookup map for accounts that are eligible for renewal
	renewalAccMap := make(map[string]*connectedAccountPb.RenewalAccountDetails)
	for _, renewalAcc := range renewalAccList {
		renewalAccMap[renewalAcc.GetAccountDetails().GetAccountId()] = renewalAcc
	}

	for _, accountId := range connectedAccIds {
		renewalAccDetail, isEligible := renewalAccMap[accountId]
		if !isEligible {
			return nil, nil
		}

		filteredRenewalAccDetails = append(filteredRenewalAccDetails, renewalAccDetail)
	}

	return filteredRenewalAccDetails, nil
}

// fetchCardPiIds fetches card pi id's for given card id's. If we get error or non success status from backend rpc
// we will return that status accordingly
func (s *Service) fetchCardPiIds(ctx context.Context, cardIds []string, actorId string) ([]string, *rpc.Status) {
	var (
		piIds []string
	)
	fetchCardDetailsResp, err := s.cardProvisioningClient.FetchCardDetails(ctx, &cardPb.FetchCardDetailsRequest{
		IssuingBank: commonvgpb.Vendor_FEDERAL_BANK,
		CardIds:     cardIds,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while fetching card details", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		return nil, rpc.StatusInternal()
	case !fetchCardDetailsResp.GetStatus().IsSuccess():
		logger.Error(ctx, "non success state while fetching card details")
		return nil, fetchCardDetailsResp.GetStatus()
	}

	for _, cardId := range cardIds {
		card, ok := fetchCardDetailsResp.GetCards()[cardId]
		if !ok {
			logger.Error(ctx, "error while fetching card details for card id", zap.String(logger.CARD_ID, cardId))
			return nil, rpc.StatusInternal()
		}

		if card.GetActorId() != actorId {
			logger.Error(ctx, "user does not have access to this card", zap.String(logger.CARD_ID, cardId))
			return nil, rpc.StatusPermissionDenied()
		}

		getPiRes, err := s.piClient.GetPi(ctx, &piPb.GetPiRequest{
			Type: piPb.PaymentInstrumentType_DEBIT_CARD,
			Identifier: &piPb.GetPiRequest_DebitCardRequestParams_{
				DebitCardRequestParams: &piPb.GetPiRequest_DebitCardRequestParams{
					TokenizedCardNumber: card.GetBasicInfo().GetCardNumber(),
					Expiry:              card.GetBasicInfo().GetExpiry(),
					Name:                card.GetBasicInfo().GetCustomerName(),
				},
			},
		})
		switch {
		case err != nil:
			logger.Error(ctx, "error while fetching card Pi", zap.String(logger.CARD_ID, cardId), zap.Error(err))
			return nil, rpc.StatusInternal()
		case !getPiRes.GetStatus().IsSuccess():
			logger.Error(ctx, "non success state while fetching card Pi", zap.String(logger.CARD_ID, cardId))
			return nil, getPiRes.GetStatus()
		}
		piIds = append(piIds, getPiRes.GetPaymentInstrument().GetId())
	}
	return piIds, nil
}

// returns transaction widget list given a list of actor activities
func (s *Service) getTransactionsWidgetList(actorActivities []*actorActivityPb.GetActivitiesResponse_Activity, actorActivityCategoryMap map[string]*beCategorizerPb.ActivityCategories, actorActivityToRewardsEarnedMap map[string]*widget.RewardsEarnedValueChip, rewardSummaryWidgetsMap map[string]*widget.RewardSummaryWidget) []*widget.TransactionsWidget {
	var (
		transactionWidgetList []*widget.TransactionsWidget
		transactionWidget     = &widget.TransactionsWidget{}
		prevDate              *date.Date
	)
	for _, actorActivity := range actorActivities {
		var categories []beCategorizerPb.DisplayCategory
		dayTimeStampString := s.getDayFromTimeStamp(actorActivity.GetActivityTimestamp())
		activityTimestamp := actorActivity.GetActivityTimestamp().AsTime().In(datetime.IST)
		currentDate := datetime.TimeToDate(&activityTimestamp)

		if !datetime.DateEquals(prevDate, currentDate) {
			if prevDate != nil {
				transactionWidgetList = append(transactionWidgetList, transactionWidget)
			}
			prevDate = currentDate
			transactionWidget = &widget.TransactionsWidget{}
			transactionWidget.Title = dayTimeStampString
		}
		if activityCategories, ok := actorActivityCategoryMap[actorActivity.GetActivityId()]; ok {
			categories = activityCategories.GetDisplayCategories()
		}
		transactionView := s.getTransactionView(actorActivity, categories)
		transactionWidget.Transactions = append(transactionWidget.Transactions, transactionView)

		// get the reward details from actor activity id
		if _, ok := actorActivityToRewardsEarnedMap[actorActivity.GetActivityId()]; ok {
			transactionView.RewardsEarnedValueChip = actorActivityToRewardsEarnedMap[actorActivity.GetActivityId()]
		}

		// get the reward summary widget from actor activity id
		// NOTE: we pass the total month's reward summary details widget in last day of the month's transaction widget.
		if val, ok := rewardSummaryWidgetsMap[actorActivity.GetActivityId()]; ok {
			transactionWidget.RewardMonthSummary = val
		}
	}
	transactionWidgetList = append(transactionWidgetList, transactionWidget)
	return transactionWidgetList
}

// returns a string to represent a day given a timestamp
// eg. WEDNESDAY, 12 MAY
func (s *Service) getDayFromTimeStamp(ts *timestampPb.Timestamp) string {
	dayTimeStamp := ts.AsTime().In(datetime.IST).Format(s.actorActivityTimeStampLayout)

	return strings.ToUpper(dayTimeStamp)
}

// getTransactionView returns transaction view given an actor activity
// we are setting view link and icon deeplink here which will be used for redirection on clicking any of the activity or activity icon,
// in case of a new data provider support has to be added for the same.
// nolint: funlen
func (s *Service) getTransactionView(actorActivity *actorActivityPb.GetActivitiesResponse_Activity, categories []beCategorizerPb.DisplayCategory) *widget.TransactionView {

	var (
		deeplink *feDeeplink.Deeplink
	)

	transactionView := &widget.TransactionView{
		IconUrl:              actorActivity.GetIconUrl(),
		Title:                actorActivity.GetTitle(),
		ShortDesc:            s.getShortDesc(actorActivity.GetShortDesc(), actorActivity.GetActivityType()),
		ShortIconUrl:         actorActivity.GetShortDescIconUrl(),
		Amount:               money.ToDisplayStringWithoutSymbol(actorActivity.GetAmount()),
		AmountBadge:          beAmountBadgeToFeAmountBadgeMap[actorActivity.GetAmountBadge()],
		TransactionTimestamp: actorActivity.GetActivityTimestamp(),
		AmountColour:         s.getAmountColour(actorActivity.GetAmountBadge(), actorActivity.GetActivityType()),
		IconColour:           actor.GetColourCodeForActor(actorActivity.GetSecondActorId()),
		TxnCategories:        s.convertBeCategoriesToFeCategories(categories),
		AmountBadgeIconUrl:   s.getAmountBadgeIconUrl(actorActivity.GetAmountBadge(), actorActivity.GetActivityType()),
	}

	switch {
	case actorActivity.GetActivityEntryPoint() == actorActivityPb.ActivityEntryPoint_ORDER:
		deeplink = &feDeeplink.Deeplink{
			Screen: feDeeplink.Screen_TRANSACTION_RECEIPT,
			ScreenOptions: &feDeeplink.Deeplink_TransactionReceiptScreenOptions{
				TransactionReceiptScreenOptions: &feDeeplink.TransactionReceiptScreenOptions{
					OrderId: actorActivity.GetActivityId(),
					Identifier: &feDeeplink.TransactionReceiptScreenOptions_OrdersId{
						OrdersId: actorActivity.GetActivityId(),
					},
				},
			},
		}
	case actorActivity.GetActivityEntryPoint() == actorActivityPb.ActivityEntryPoint_AA:
		deeplink = &feDeeplink.Deeplink{
			Screen: feDeeplink.Screen_TRANSACTION_RECEIPT,
			ScreenOptions: &feDeeplink.Deeplink_TransactionReceiptScreenOptions{
				TransactionReceiptScreenOptions: &feDeeplink.TransactionReceiptScreenOptions{
					OrderId: actorActivity.GetActivityId(),
					Identifier: &feDeeplink.TransactionReceiptScreenOptions_AaTxnId{
						AaTxnId: actorActivity.GetActivityId(),
					},
				},
			},
		}
	case actorActivity.GetActivityEntryPoint() == actorActivityPb.ActivityEntryPoint_CREDIT_CARD:
		deeplink = &feDeeplink.Deeplink{
			Screen: feDeeplink.Screen_CREDIT_CARD_TRANSACTION_RECEIPT,
			ScreenOptions: &feDeeplink.Deeplink_CreditCardTransactionReceiptScreenOptions{
				CreditCardTransactionReceiptScreenOptions: &feDeeplink.CreditCardTransactionReceiptScreenOptions{
					TxnId: actorActivity.GetActivityId(),
				},
			},
		}
	}

	transactionView.ViewLink = &widget.DeepLinkElement{Link: deeplink}

	switch actorActivity.GetDeeplinkIdentifier().(type) {
	case *actorActivityPb.GetActivitiesResponse_Activity_TimelineId:
		transactionView.IconDeepLink = &widget.DeepLinkElement{
			Link: &feDeeplink.Deeplink{
				Screen: feDeeplink.Screen_TIMELINE,
				ScreenOptions: &feDeeplink.Deeplink_TimelineScreenOptions{
					TimelineScreenOptions: &feDeeplink.TimelineScreenOptions{
						TimelineId: actorActivity.GetTimelineId(),
					},
				},
			},
		}
	case *actorActivityPb.GetActivitiesResponse_Activity_DepositAccountIdentifier_:
		// TODO(harish): handle FD interest credit txns
		//  Monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=19555
		if actorActivity.GetDepositAccountIdentifier().GetAccountId() == "" {
			transactionView.IconDeepLink = &widget.DeepLinkElement{
				Link: &feDeeplink.Deeplink{
					Screen: feDeeplink.Screen_DEPOSIT_LANDING_SCREEN,
					ScreenOptions: &feDeeplink.Deeplink_DepositAccountLandingScreenOption{
						DepositAccountLandingScreenOption: &feDeeplink.DepositAccountLandingScreenOptions{
							DepositType: actorActivity.GetDepositAccountIdentifier().GetAccountType(),
						},
					},
				},
			}
		} else {
			transactionView.IconDeepLink = &widget.DeepLinkElement{
				Link: &feDeeplink.Deeplink{
					Screen: feDeeplink.Screen_DEPOSIT_ACCOUNT_DETAILS,
					ScreenOptions: &feDeeplink.Deeplink_DepositDetailsScreenOptions{
						DepositDetailsScreenOptions: &feDeeplink.DepositAccountDetailsScreenOptions{
							AccountId:   actorActivity.GetDepositAccountIdentifier().GetAccountId(),
							DepositType: actorActivity.GetDepositAccountIdentifier().GetAccountType(),
						},
					},
				},
			}
		}
	}
	transactionView.PartnerTag = actorActivity.GetPartnerTag()
	return transactionView
}

// convert backend category proto to frontend widget category proto
func (s *Service) convertBeCategoriesToFeCategories(categories []beCategorizerPb.DisplayCategory) []*widget.TxnCategory {
	var txnCategories []*widget.TxnCategory
	for _, category := range categories {
		if displayCategory, ok := fePayTransaction.DisplayEnumToCategoryDetailMap[category]; ok {
			txnCategory := &widget.TxnCategory{
				CategoryName: displayCategory.GetDisplayName(),
				IconUrl:      displayCategory.GetIconUrl(),
			}
			txnCategories = append(txnCategories, txnCategory)
		} else {
			logger.ErrorNoCtx("missing category in DisplayEnumToCategoryDetailMap", zap.String(logger.TXN_CATEGORY, category.String()))
		}
	}
	return txnCategories
}

// setPageTokens sets before and after page token value in the response.
// it calculates before and after token after traversing the slice of actorActivities
// nolint: funlen
func setPageTokens(ctx context.Context, res *feActorActivityPb.GetActivitiesResponse, actorActivities []*actorActivityPb.GetActivitiesResponse_Activity, prevBeforeToken *beforePageToken, prevAfterToken *afterPageToken, pageLandingTimestamp *timestampPb.Timestamp) error {
	var (
		err         error
		beforeToken = &beforePageToken{}
		before      string
		afterToken  = &afterPageToken{}
		after       string
	)

	// reversed actor activities
	reverseActorActivities := lo.Reverse(actorActivities)

	switch {
	// In case prevAfterToken is not nil, the activities are in ASCENDING time-ordered sequence.
	case prevAfterToken != nil:
		// assigning prev token ensures carry forward in case of no activities in current page.
		*afterToken = *prevAfterToken
		beforeToken.FirstOrderTimeStamp, beforeToken.OrderOffset = getFirstOccurrenceWithOffset(actorActivities, actorActivityPb.ActivityEntryPoint_ORDER)
		beforeToken.FirstOrderTimeStamp, beforeToken.AaTransactionOffset = getFirstOccurrenceWithOffset(actorActivities, actorActivityPb.ActivityEntryPoint_AA)
		beforeToken.FirstOrderTimeStamp, beforeToken.CcTransactionOffset = getFirstOccurrenceWithOffset(actorActivities, actorActivityPb.ActivityEntryPoint_CREDIT_CARD)
		tm, orderOffset := getFirstOccurrenceWithOffset(reverseActorActivities, actorActivityPb.ActivityEntryPoint_ORDER)
		_, aaOffset := getFirstOccurrenceWithOffset(reverseActorActivities, actorActivityPb.ActivityEntryPoint_AA)
		_, ccOffset := getFirstOccurrenceWithOffset(reverseActorActivities, actorActivityPb.ActivityEntryPoint_CREDIT_CARD)
		afterToken.PageLandingTimestamp = pageLandingTimestamp

		// set only if non-nil as nil represents no activities present
		if tm != nil {
			afterToken.LastOrderTimestamp = tm
			afterToken.OrderOffset = orderOffset
			afterToken.AaTransactionOffset = aaOffset
			afterToken.CcTransactionOffset = ccOffset

			// ensures offset is carry forwarded
			// e.g. prev page had 5 activities with timestamp tm and now current page had 15 activities in the same tm timestamp
			// so for next query offset should be 20
			if tm.AsTime().Equal(prevAfterToken.LastOrderTimestamp.AsTime()) {
				afterToken.OrderOffset += prevAfterToken.OrderOffset
				afterToken.AaTransactionOffset += prevAfterToken.AaTransactionOffset
				afterToken.CcTransactionOffset += prevAfterToken.CcTransactionOffset
			}
		}

	// In case prevBeforeToken is not nil, the activities are in DESCENDING time-ordered sequence.
	case prevBeforeToken != nil:
		// assigning prev token ensures carry forward in case of no activities in current page.
		*beforeToken = *prevBeforeToken
		afterToken.LastOrderTimestamp, afterToken.OrderOffset = getFirstOccurrenceWithOffset(actorActivities, actorActivityPb.ActivityEntryPoint_ORDER)
		afterToken.LastOrderTimestamp, afterToken.AaTransactionOffset = getFirstOccurrenceWithOffset(actorActivities, actorActivityPb.ActivityEntryPoint_AA)
		afterToken.LastOrderTimestamp, afterToken.CcTransactionOffset = getFirstOccurrenceWithOffset(actorActivities, actorActivityPb.ActivityEntryPoint_CREDIT_CARD)
		tm, orderOffset := getFirstOccurrenceWithOffset(reverseActorActivities, actorActivityPb.ActivityEntryPoint_ORDER)
		_, aaOffset := getFirstOccurrenceWithOffset(reverseActorActivities, actorActivityPb.ActivityEntryPoint_AA)
		_, ccOffset := getFirstOccurrenceWithOffset(reverseActorActivities, actorActivityPb.ActivityEntryPoint_CREDIT_CARD)
		beforeToken.PageLandingTimestamp = pageLandingTimestamp

		// set only if non-nil as nil represents no activities found
		if tm != nil {
			beforeToken.FirstOrderTimeStamp = tm
			beforeToken.OrderOffset = orderOffset
			beforeToken.AaTransactionOffset = aaOffset
			beforeToken.CcTransactionOffset = ccOffset

			// ensures offset is carry forwarded
			// e.g. prev page had 5 activities with timestamp tm and now current page had 15 activities in the same tm timestamp
			// so for next query offset should be 20
			if tm.AsTime().Equal(prevBeforeToken.FirstOrderTimeStamp.AsTime()) {
				beforeToken.OrderOffset += prevBeforeToken.OrderOffset
				beforeToken.AaTransactionOffset += prevBeforeToken.AaTransactionOffset
				beforeToken.CcTransactionOffset += prevBeforeToken.CcTransactionOffset
			}
		}

	// If both are nil then FetchLatestPage boolean flag must be true
	// Hence activities contains latest activities in DESCENDING time-ordered sequence from current timestamp
	default:
		afterToken.LastOrderTimestamp, afterToken.OrderOffset = getFirstOccurrenceWithOffset(actorActivities, actorActivityPb.ActivityEntryPoint_ORDER)
		_, afterToken.AaTransactionOffset = getFirstOccurrenceWithOffset(actorActivities, actorActivityPb.ActivityEntryPoint_AA)
		_, afterToken.CcTransactionOffset = getFirstOccurrenceWithOffset(actorActivities, actorActivityPb.ActivityEntryPoint_CREDIT_CARD)
		afterToken.PageLandingTimestamp = pageLandingTimestamp

		beforeToken.FirstOrderTimeStamp, beforeToken.OrderOffset = getFirstOccurrenceWithOffset(reverseActorActivities, actorActivityPb.ActivityEntryPoint_ORDER)
		_, beforeToken.AaTransactionOffset = getFirstOccurrenceWithOffset(reverseActorActivities, actorActivityPb.ActivityEntryPoint_AA)
		_, beforeToken.CcTransactionOffset = getFirstOccurrenceWithOffset(reverseActorActivities, actorActivityPb.ActivityEntryPoint_CREDIT_CARD)
		beforeToken.PageLandingTimestamp = pageLandingTimestamp
	}

	before, err = beforeToken.MarshalToken()
	if err != nil {
		return fmt.Errorf("failed to generate before token for current page: %w", err)
	}

	after, err = afterToken.MarshalToken()
	if err != nil {
		return fmt.Errorf("failed to generate after token for current page: %w", err)
	}

	logger.Debug(ctx, "tokens", zap.Any("after", afterToken), zap.Any("before", beforeToken))
	res.BeforeToken = before
	res.AfterToken = after
	return nil
}

// getFirstOccurrenceWithOffset returns first activity timestamp and count of activities whose timestamp == first event timestamp
func getFirstOccurrenceWithOffset(actorActivities []*actorActivityPb.GetActivitiesResponse_Activity, activityEntry actorActivityPb.ActivityEntryPoint) (*timestampPb.Timestamp, int32) {
	var (
		firstFound          bool
		firstTimeStamp      *timestampPb.Timestamp
		firstTimeStampCount int32
	)

	for _, actorActivity := range actorActivities {
		if !firstFound {
			firstFound = true
			firstTimeStamp = actorActivity.GetActivityTimestamp()
			if actorActivity.GetActivityEntryPoint() == activityEntry {
				firstTimeStampCount++
			}
			continue
		}

		if actorActivity.GetActivityTimestamp().AsTime().Equal(firstTimeStamp.AsTime()) {
			if actorActivity.GetActivityEntryPoint() == activityEntry {
				firstTimeStampCount++
			}
		} else {
			break
		}
	}
	return firstTimeStamp, firstTimeStampCount
}

// beGetActorActivities returns nil, list of actor activities for a combination of actor and account ids
// returns the relevant status, nil in case of failures/errors
func (s *Service) beGetActorActivities(ctx context.Context, actorId string, beAccountFilter []*actorActivityPb.GetActivitiesRequest_AccountFilter, piIds []string, startTimestamp *timestampPb.Timestamp, pageSize, offset, aaTxnOffset int32, descending bool, queryStartTimestamp *timestampPb.Timestamp, entryPointType actorActivityPb.GetActivitiesRequest_EntryPointType) (*rpc.Status, []*actorActivityPb.GetActivitiesResponse_Activity) {
	actorActivityRes, err := s.actorActivityClient.GetActivities(ctx, &actorActivityPb.GetActivitiesRequest{
		CurrentActorId:           actorId,
		AccountFilter:            beAccountFilter,
		ActivitiesStartTimestamp: startTimestamp,
		PageSize:                 pageSize,
		ActivityOffset:           offset,
		OrderOffset:              offset,
		AaTxnOffset:              aaTxnOffset,
		Descending:               descending,
		PiFilter:                 piIds,
		PageLandingTimestamp:     queryStartTimestamp,
		EntryPointType:           entryPointType,
		FetchSoftDeletedUsers:    true,
	})

	switch {
	case err != nil:
		logger.Error(ctx, "error fetching activities for actor",
			zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		return rpc.StatusInternal(), nil
	case !actorActivityRes.GetStatus().IsSuccess():
		logger.Error(ctx, fmt.Sprintf("GetActivities() rpc failed with status %s", actorActivityRes.GetStatus()))
		return actorActivityRes.GetStatus(), nil
	}

	return nil, actorActivityRes.GetActivities()
}

// beValidateRequest validates if tha actor has access to all the accounts present in the list
// returns nil in case the user has access to all the accounts
// returns relevant status in case the user dosen't have access to one/more accounts
func (s *Service) beValidateRequest(
	ctx context.Context,
	entityId string,
	deprecatedAccountFilters []*feActorActivityPb.GetActivitiesRequest_AccountFilter,
	accountFilters []*feActorActivityPb.GetActivitiesRequest_Account,
	actorID string,
) *rpc.Status {
	switch {
	case len(deprecatedAccountFilters) != 0:
		for _, accountFilter := range deprecatedAccountFilters {
			status := s.beValidateAccount(ctx, accountFilter.GetAccountId(), accountFilter.GetAccountType(), actorID, entityId)
			if status != nil {
				return status
			}
		}
	case len(accountFilters) != 0:
		for _, accountFilter := range accountFilters {
			var status *rpc.Status
			if accountFilter.GetDerivedAccountId() != "" {
				status = s.beValidateAccountWithDerivedAccountId(
					ctx,
					accountFilter.GetDerivedAccountId(),
					accountFilter.GetAccountType(),
					actorID,
					entityId,
				)
			} else {
				status = s.beValidateAccount(ctx, accountFilter.GetAccountId(), accountFilter.GetAccountType(), actorID, entityId)
			}
			if status != nil {
				return status
			}
		}
	default:
		return nil
	}
	return nil
}

// beValidateAccount validates if account exists for given id and whether the account belongs to existing user or not
func (s *Service) beValidateAccount(ctx context.Context, acctID string, accType accounts.Type, actorID, userID string) *rpc.Status {
	// check if the account id belongs to connected account
	connectAccountId, err := s.isConnectedAccountId(ctx, acctID, actorID)
	if err != nil {
		logger.Error(ctx, "error checking if connected account",
			zap.String(logger.ACCOUNT_ID, acctID), zap.Error(err))
		if errors.Is(err, permissionDeniedErr) {
			return rpc.StatusPermissionDenied()
		}
		return rpc.StatusInternal()
	}
	if connectAccountId {
		return nil
	}
	switch accType {
	case accounts.Type_SAVINGS:
		getAccountRes, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_Id{Id: acctID},
		})
		switch {
		case err != nil:
			logger.Error(ctx, "error fetching account for id",
				zap.String(logger.ACCOUNT_ID, acctID), zap.Error(err))
			return rpc.StatusInternal()
		case getAccountRes.GetAccount().GetPrimaryAccountHolder() != userID:
			logger.Error(ctx, "user does not have access to account", zap.String(logger.ACCOUNT_ID, acctID),
				zap.String(logger.USER_ID, userID))
			return rpc.StatusPermissionDenied()
		}
	case accounts.Type_FIXED_DEPOSIT, accounts.Type_SMART_DEPOSIT:
		getAccountRes, err := s.depositClient.GetById(ctx, &depositPb.GetByIdRequest{
			Id: acctID,
		})
		switch {
		case err != nil:
			logger.Error(ctx, "error fetching deposit account for id",
				zap.String(logger.ACCOUNT_ID, acctID), zap.Error(err))
			return rpc.StatusInternal()
		case !getAccountRes.GetStatus().IsSuccess():
			logger.Error(ctx, "depositClient.GetById returned non-ok status", zap.String(logger.ACCOUNT_ID, acctID))
			return rpc.StatusInternal()
		case getAccountRes.GetAccount().GetActorId() != actorID:
			logger.Error(ctx, "deposit account doesnt belong to the actor", zap.String(logger.ACCOUNT_ID, acctID),
				zap.String(logger.ACTOR_ID, actorID))
			return rpc.StatusPermissionDenied()
		}
	case accounts.Type_CREDIT_CARD_ACCOUNT:
		getAccountRes, err := s.ffAccountingClient.GetAccount(ctx, &ffAccountsPb.GetAccountRequest{
			GetBy: &ffAccountsPb.GetAccountRequest_AccountId{AccountId: acctID},
		})
		switch {
		case err != nil:
			logger.Error(ctx, "error fetching cc account for id",
				zap.String(logger.ACCOUNT_ID, acctID), zap.Error(err))
			return rpc.StatusInternal()
		case !getAccountRes.GetStatus().IsSuccess():
			logger.Error(ctx, "GetAccount returned non-ok status", zap.String(logger.ACCOUNT_ID, acctID))
			return rpc.StatusInternal()
		case getAccountRes.GetAccount().GetActorId() != actorID:
			logger.Error(ctx, "credit account doesnt belong to the actor", zap.String(logger.ACCOUNT_ID, acctID),
				zap.String(logger.ACTOR_ID, actorID))
			return rpc.StatusPermissionDenied()
		}
	default:
		logger.Error(ctx, fmt.Sprintf("unsupported account type %s", accType))
		return rpc.StatusInvalidArgument()
	}

	return nil
}

// beValidateAccountWithDerivedAccountId validates if the accounts belongs
// to the actorId passed for the given derived account id
func (s *Service) beValidateAccountWithDerivedAccountId(
	ctx context.Context,
	derivedAccountId string,
	accType accounts.Type,
	actorId, userId string,
) *rpc.Status {
	derivedAccountIdProto, err := pay.GetDecodedDerivedAccountId(derivedAccountId)
	if err != nil {
		logger.Error(ctx, "error decoding derived account id")
		return rpc.StatusInternal()
	}
	switch {
	case derivedAccountIdProto.GetTpapAccountId() != "":
		getAccountRes, err := s.upiOnboardingClient.GetAccount(ctx, &upiOnboardingPb.GetAccountRequest{AccountId: derivedAccountIdProto.GetTpapAccountId()})
		if rpcErr := epifigrpc.RPCError(getAccountRes, err); rpcErr != nil {
			logger.Error(ctx, "error fetching tpap account", zap.Error(err),
				zap.String(logger.DERIVED_ACCOUNT_ID, derivedAccountId))
			return rpc.StatusInternal()
		}
		if getAccountRes.GetAccount().GetActorId() != actorId {
			logger.Error(ctx, "account does not belong to the actor",
				zap.String(logger.ACTOR_ID_V2, getAccountRes.GetAccount().GetActorId()),
				zap.String(logger.PRIMARY_ACTOR_ID, actorId))
			return rpc.StatusPermissionDenied()
		}
		return nil
	case derivedAccountIdProto.GetConnectedAccountId() != "":
		return s.beValidateAccount(ctx, derivedAccountIdProto.GetConnectedAccountId(), accType, actorId, userId)
	case derivedAccountIdProto.GetInternalAccountId() != "":
		return s.beValidateAccount(ctx, derivedAccountIdProto.GetInternalAccountId(), accType, actorId, userId)
	case derivedAccountIdProto.GetDepositAccountId() != "":
		return s.beValidateAccount(ctx, derivedAccountIdProto.GetDepositAccountId(), accType, actorId, userId)
	case derivedAccountIdProto.GetCreditCardAccountId() != "":
		return s.beValidateAccount(ctx, derivedAccountIdProto.GetCreditCardAccountId(), accType, actorId, userId)
	}

	logger.Error(ctx, "invalid derived account id passed", zap.String(logger.DERIVED_ACCOUNT_ID, derivedAccountId))
	return rpc.StatusInvalidArgument()
}

// getBeAccountFilter returns a list of be account filters given a list of fe account filters.
// Keeping this for backward compatibility
// Deprecated
func getBeAccountFilter(feAccountFilters []*feActorActivityPb.GetActivitiesRequest_AccountFilter) []*actorActivityPb.GetActivitiesRequest_AccountFilter {
	var beAccountFilters []*actorActivityPb.GetActivitiesRequest_AccountFilter
	for _, feAccountFilter := range feAccountFilters {
		beAccountFilter := &actorActivityPb.GetActivitiesRequest_AccountFilter{}
		beAccountFilter.AccountId = feAccountFilter.GetAccountId()
		beAccountFilter.AccountType = feAccountFilter.GetAccountType()
		beAccountFilters = append(beAccountFilters, beAccountFilter)
	}
	return beAccountFilters
}

// getBeAccountFilterFromAccount returns a list of be account filters given a list of fe accounts
func (s *Service) getBeAccountFilterFromAccount(
	ctx context.Context, actorId string,
	feAccountFilters []*feActorActivityPb.GetActivitiesRequest_Account,
) ([]*actorActivityPb.GetActivitiesRequest_AccountFilter, *rpc.Status) {
	var beAccountFilters []*actorActivityPb.GetActivitiesRequest_AccountFilter
	for _, feAccountFilter := range feAccountFilters {
		if feAccountFilter.GetDerivedAccountId() != "" {
			accountFilters, status := s.getBeAccountFilterForDerivedAccountId(ctx, feAccountFilter.GetDerivedAccountId(), feAccountFilter.GetAccountType(), actorId)
			if status != nil {
				return nil, status
			}
			beAccountFilters = append(beAccountFilters, accountFilters...)
		} else {
			beAccountFilter := &actorActivityPb.GetActivitiesRequest_AccountFilter{}
			beAccountFilter.AccountId = feAccountFilter.GetAccountId()
			beAccountFilter.AccountType = feAccountFilter.GetAccountType()
			beAccountFilters = append(beAccountFilters, beAccountFilter)
		}
	}
	return beAccountFilters, nil
}

// getBeAccountFilterForDerivedAccountId returns a list of account filters for the given derived account id
func (s *Service) getBeAccountFilterForDerivedAccountId(ctx context.Context, derivedAccountId string, accType accounts.Type, actorId string,
) ([]*actorActivityPb.GetActivitiesRequest_AccountFilter, *rpc.Status) {
	var (
		beAccountFilters []*actorActivityPb.GetActivitiesRequest_AccountFilter
	)
	derivedAccountIdProto, err := pay.GetDecodedDerivedAccountId(derivedAccountId)
	if err != nil {
		logger.Error(ctx, "error decoding derived account id", zap.Error(err), zap.String(logger.DERIVED_ACCOUNT_ID, derivedAccountId))
		return nil, rpc.StatusInvalidArgument()
	}
	switch {
	case derivedAccountIdProto.GetInternalAccountId() != "":
		beAccountFilters = append(beAccountFilters, &actorActivityPb.GetActivitiesRequest_AccountFilter{
			AccountId:   derivedAccountIdProto.GetInternalAccountId(),
			AccountType: accType,
		})
		fallthrough
	case derivedAccountIdProto.GetTpapAccountId() != "" && derivedAccountIdProto.GetInternalAccountId() == "":
		beAccountFilters = append(beAccountFilters, &actorActivityPb.GetActivitiesRequest_AccountFilter{
			AccountId:   derivedAccountIdProto.GetTpapAccountId(),
			AccountType: accType,
		})
		fallthrough
	case derivedAccountIdProto.GetConnectedAccountId() != "":
		shouldApplyAccFilterForAaActivities := s.shouldAccFilterApplyForAaActivities(ctx, actorId, derivedAccountId, derivedAccountIdProto.GetConnectedAccountId())
		if shouldApplyAccFilterForAaActivities {
			beAccountFilters = append(beAccountFilters, &actorActivityPb.GetActivitiesRequest_AccountFilter{
				AccountId:   derivedAccountIdProto.GetConnectedAccountId(),
				AccountType: accType,
			})
		}
		fallthrough
	case derivedAccountIdProto.GetDepositAccountId() != "":
		beAccountFilters = append(beAccountFilters, &actorActivityPb.GetActivitiesRequest_AccountFilter{
			AccountId:   derivedAccountIdProto.GetDepositAccountId(),
			AccountType: accType,
		})
		fallthrough
	case derivedAccountIdProto.GetCreditCardAccountId() != "":
		beAccountFilters = append(beAccountFilters, &actorActivityPb.GetActivitiesRequest_AccountFilter{
			AccountId:   derivedAccountIdProto.GetCreditCardAccountId(),
			AccountType: accType,
		})
	}
	return beAccountFilters, nil
}

// getAmountColour returns the amount colour to be displayed on the client given and amount badge
func (s *Service) getAmountColour(amountBadge actorActivityPb.GetActivitiesResponse_Activity_AmountBadge, activityType actorActivityPb.GetActivitiesResponse_Activity_Type) string {
	switch amountBadge {
	case actorActivityPb.GetActivitiesResponse_Activity_DEBIT:
		switch activityType {
		case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_PENDING,
			actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_PENDING,
			actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_PENDING:
			return s.amountColourMap.PendingColour
		case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_REVERSED,
			actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_REVERSED,
			actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_REVERSED:
			return s.amountColourMap.ReversedColour
		case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_FAILED,
			actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_FAILED,
			actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_FAILED:
			return s.amountColourMap.FailedColour
		default:
			return s.amountColourMap.DefaultColour
		}
		return s.amountColourMap.DebitColour
	case actorActivityPb.GetActivitiesResponse_Activity_CREDIT:
		return s.amountColourMap.CreditColour
	case actorActivityPb.GetActivitiesResponse_Activity_SAVINGS:
		return s.amountColourMap.SavingsColour
	default:
		return s.amountColourMap.DefaultColour
	}
}

// isConnectedAccountId checks if the account id belongs to actor's connected account
func (s *Service) isConnectedAccountId(ctx context.Context, accountId, actorId string) (bool, error) {
	res, err := s.connectedAccountClient.GetAccountDetails(ctx, &connectedAccountPb.GetAccountDetailsRequest{
		AccountId: accountId,
		ActorId:   actorId,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Debug(ctx, "error fetching connected account", zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		// TODO(Raunak) handle the error once we have is connected account flag from client
		return false, nil
	}
	if res.GetAccountDetails().GetActorId() != actorId {
		return false, permissionDeniedErr
	}
	return true, nil
}

// getShortDesc in case of IN_PAYMENT, FAILED, PENDING transactions we need to show the order status instead of txn category
func (s *Service) getShortDesc(defaultDesc string, actorActivityType actorActivityPb.GetActivitiesResponse_Activity_Type) string {
	switch actorActivityType {
	case actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_REVERSED:
		return s.dynconf.AllTransactionsShortDesc().ReversedShortDesc
	case actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_FAILED:
		return s.dynconf.AllTransactionsShortDesc().FailedShortDesc
	case actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_PENDING:
		return s.dynconf.AllTransactionsShortDesc().PendingShortDesc
	default:
		return defaultDesc
	}
}

// getAmountBadgeIconUrl returns the txn arrow icon url which will be displayed on the client
func (s *Service) getAmountBadgeIconUrl(amountBadge actorActivityPb.GetActivitiesResponse_Activity_AmountBadge, activityType actorActivityPb.GetActivitiesResponse_Activity_Type) string {
	switch amountBadge {
	case actorActivityPb.GetActivitiesResponse_Activity_DEBIT:
		switch activityType {
		case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_PENDING,
			actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_PENDING,
			actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_PENDING, actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_PENDING:
			return s.amountBadgeIconUrlMap.PendingIconUrl
		case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_REVERSED,
			actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_REVERSED,
			actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_REVERSED, actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_REVERSED:
			return s.amountBadgeIconUrlMap.ReversedIconUrl
		case actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_FAILED,
			actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_FAILED,
			actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_FAILED,
			actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_FAILED, actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_FAILED:
			return s.amountBadgeIconUrlMap.FailedIconUrl
		default:
			return s.amountBadgeIconUrlMap.DebitIconUrl
		}
	case actorActivityPb.GetActivitiesResponse_Activity_CREDIT:
		return s.amountBadgeIconUrlMap.CreditIconUrl
	case actorActivityPb.GetActivitiesResponse_Activity_SAVINGS:
		return s.amountBadgeIconUrlMap.SavingIconUrl
	default:
		logger.ErrorNoCtx("unexpected amount badge", zap.String("amountBadge", amountBadge.String()))
		return ""
	}
}

// getFiToFiBottomSheet returns the bottom sheet to initiate Fi to Fi flow.
func (s *Service) getFiToFiBottomSheet(ctx context.Context, actorId string, appVersion uint32, appPlatform commontypes.Platform,
	req *feActorActivityPb.GetActivitiesRequest) (*feCaFeatures.InitiateFiToFiFlowBottomSheet, error) {
	var connectedAccountFiToFiBottomSheetResp *feCaFeatures.InitiateFiToFiFlowBottomSheet
	switch req.GetSearchCriteria().(type) {
	case *feActorActivityPb.GetActivitiesRequest_AccountCriteria_:
		accountFilters := req.GetAccountCriteria().GetAccounts()
		for accountFilterIdx := range accountFilters {
			derivedAccountId := accountFilters[accountFilterIdx].GetDerivedAccountId()
			if derivedAccountId != "" {
				decodedDerivedAccountId, err := pay.GetDecodedDerivedAccountId(derivedAccountId)
				if err != nil {
					return nil, fmt.Errorf("connectedAccountFiToFiBottomSheetResp: error decoding derived account"+
						" id: %s : %w", derivedAccountId, err)
				}
				if decodedDerivedAccountId.GetInternalAccountId() != "" {
					connectedAccountFiToFiBottomSheetResp, err = s.connectedAccountFiToFiSvc.GetFiToFiBottomSheet(ctx,
						actorId, appVersion, appPlatform, req.GetReq().GetAuth().GetDevice().GetOsApiVersion())
					if err != nil {
						return nil, fmt.Errorf("connectedAccountFiToFiBottomSheetResp: Error getting connect Fi"+
							" to Fi flow bottom sheet, actorId: %s, err: %w", actorId, err)
					}
				}
			}
		}
	default:
		connectedAccountFiToFiBottomSheetResp = nil
	}

	if connectedAccountFiToFiBottomSheetResp != nil {
		return &feCaFeatures.InitiateFiToFiFlowBottomSheet{
			Title:                      connectedAccountFiToFiBottomSheetResp.GetTitle(),
			Description:                connectedAccountFiToFiBottomSheetResp.GetDescription(),
			WealthAndAaTnc:             connectedAccountFiToFiBottomSheetResp.GetWealthAndAaTnc(),
			AaEntity:                   connectedAccountFiToFiBottomSheetResp.GetAaEntity(),
			Cta:                        connectedAccountFiToFiBottomSheetResp.GetCta(),
			SecureSpendsIcon:           connectedAccountFiToFiBottomSheetResp.GetSecureSpendsIcon(),
			MaxRetryAllowedFailureCase: connectedAccountFiToFiBottomSheetResp.GetMaxRetryAllowedFailureCase(),
			InvalidateNumRetryDuration: connectedAccountFiToFiBottomSheetResp.GetInvalidateNumRetryDuration(),
		}, nil
	}
	return nil, nil
}

// getPartnerTag : generates the partner tag for the screen based on the passed filter requests
func getPartnerTag(req *feActorActivityPb.GetActivitiesRequest) *commontypes.VisualElement {
	if len(req.GetAccountCriteria().GetAccounts()) > 0 {
		return commontypes.GetVisualElementFromUrlHeightAndWidth(
			constants.PoweredByBhimUpi,
			16,
			121,
		)
	}
	return nil
}
