package totp

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	beTotpPb "github.com/epifi/gamma/api/auth/totp"
	beTotpEnumsPb "github.com/epifi/gamma/api/auth/totp/enums"
	totpPb "github.com/epifi/gamma/api/frontend/auth/totp"
	headerPb "github.com/epifi/gamma/api/frontend/header"
)

type Service struct {
	totpPb.UnimplementedTotpServer
	totpClient beTotpPb.TotpClient
}

func NewService(
	totpClient beTotpPb.TotpClient,
) *Service {
	return &Service{
		totpClient: totpClient,
	}
}

func (s *Service) GenerateTotp(ctx context.Context, req *totpPb.GenerateTotpRequest) (*totpPb.GenerateTotpResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	generateTotpResp, generateTotpErr := s.totpClient.GenerateTotp(ctx, &beTotpPb.GenerateTotpRequest{
		ActorId: actorId,
		Purpose: beTotpEnumsPb.Purpose_PURPOSE_NET_WORTH_MCP,
	})
	if rpcErr := epifigrpc.RPCError(generateTotpResp, generateTotpErr); rpcErr != nil {
		logger.Error(ctx, "error in generating totp rpc", zap.Error(rpcErr))
		return &totpPb.GenerateTotpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("error in generating totp rpc"),
			},
		}, nil
	}
	return &totpPb.GenerateTotpResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Totp:            generateTotpResp.GetTotp(),
		ExpiryTimestamp: generateTotpResp.GetExpiryTimestamp(),
	}, nil
}
