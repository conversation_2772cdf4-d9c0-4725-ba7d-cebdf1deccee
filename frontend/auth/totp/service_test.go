// nolint
package totp

import (
	"context"
	"errors"
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
	beTotpPb "github.com/epifi/gamma/api/auth/totp"
	beTotpEnumsPb "github.com/epifi/gamma/api/auth/totp/enums"
	beTotpMocks "github.com/epifi/gamma/api/auth/totp/mocks"
	totpPb "github.com/epifi/gamma/api/frontend/auth/totp"
	headerPb "github.com/epifi/gamma/api/frontend/header"
)

// TestMain initializes test components, runs tests and exits
func TestMain(m *testing.M) {
	// Setup logger for tests
	logger.Init(cfg.TestEnv)

	exitCode := m.Run()
	os.Exit(exitCode)
}

func TestService_GenerateTotp(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockTotpClient := beTotpMocks.NewMockTotpClient(ctrl)
	service := NewService(mockTotpClient)

	tests := []struct {
		name             string
		request          *totpPb.GenerateTotpRequest
		mockSetup        func()
		validateResponse func(t *testing.T, resp *totpPb.GenerateTotpResponse)
	}{
		{
			name: "successful_totp_generation",
			request: &totpPb.GenerateTotpRequest{
				Req: &headerPb.RequestHeader{
					Auth: &headerPb.AuthHeader{
						ActorId: "test-actor-123",
					},
				},
			},
			mockSetup: func() {
				mockTotpClient.EXPECT().
					GenerateTotp(gomock.Any(), &beTotpPb.GenerateTotpRequest{
						ActorId: "test-actor-123",
						Purpose: beTotpEnumsPb.Purpose_PURPOSE_NET_WORTH_MCP,
					}).
					Return(&beTotpPb.GenerateTotpResponse{
						Status: rpcPb.StatusOk(),
						Totp:   "123456",
						ExpiryTimestamp: &timestamppb.Timestamp{
							Seconds: 1640995200,
							Nanos:   0,
						},
					}, nil)
			},
			validateResponse: func(t *testing.T, resp *totpPb.GenerateTotpResponse) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.RespHeader)
				assert.Equal(t, uint32(0), resp.RespHeader.Status.Code)
				assert.Equal(t, "123456", resp.Totp)
				assert.NotNil(t, resp.ExpiryTimestamp)
				assert.Equal(t, int64(1640995200), resp.ExpiryTimestamp.Seconds)
			},
		},
		{
			name: "backend_client_returns_error",
			request: &totpPb.GenerateTotpRequest{
				Req: &headerPb.RequestHeader{
					Auth: &headerPb.AuthHeader{
						ActorId: "test-actor-123",
					},
				},
			},
			mockSetup: func() {
				mockTotpClient.EXPECT().
					GenerateTotp(gomock.Any(), &beTotpPb.GenerateTotpRequest{
						ActorId: "test-actor-123",
						Purpose: beTotpEnumsPb.Purpose_PURPOSE_NET_WORTH_MCP,
					}).
					Return(nil, errors.New("backend error"))
			},
			validateResponse: func(t *testing.T, resp *totpPb.GenerateTotpResponse) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.RespHeader)
				assert.Equal(t, uint32(13), resp.RespHeader.Status.Code) // INTERNAL
				assert.Contains(t, resp.RespHeader.Status.DebugMessage, "error in generating totp rpc")
				assert.Empty(t, resp.Totp)
				assert.Nil(t, resp.ExpiryTimestamp)
			},
		},
		{
			name: "backend_client_returns_error_status",
			request: &totpPb.GenerateTotpRequest{
				Req: &headerPb.RequestHeader{
					Auth: &headerPb.AuthHeader{
						ActorId: "test-actor-123",
					},
				},
			},
			mockSetup: func() {
				mockTotpClient.EXPECT().
					GenerateTotp(gomock.Any(), &beTotpPb.GenerateTotpRequest{
						ActorId: "test-actor-123",
						Purpose: beTotpEnumsPb.Purpose_PURPOSE_NET_WORTH_MCP,
					}).
					Return(&beTotpPb.GenerateTotpResponse{
						Status: &rpcPb.Status{
							Code:         uint32(3), // INVALID_ARGUMENT
							ShortMessage: "invalid actor id",
							DebugMessage: "actor id validation failed",
						},
					}, nil)
			},
			validateResponse: func(t *testing.T, resp *totpPb.GenerateTotpResponse) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.RespHeader)
				assert.Equal(t, uint32(13), resp.RespHeader.Status.Code) // INTERNAL
				assert.Contains(t, resp.RespHeader.Status.DebugMessage, "error in generating totp rpc")
				assert.Empty(t, resp.Totp)
				assert.Nil(t, resp.ExpiryTimestamp)
			},
		},
		{
			name: "empty_actor_id",
			request: &totpPb.GenerateTotpRequest{
				Req: &headerPb.RequestHeader{
					Auth: &headerPb.AuthHeader{
						ActorId: "",
					},
				},
			},
			mockSetup: func() {
				mockTotpClient.EXPECT().
					GenerateTotp(gomock.Any(), &beTotpPb.GenerateTotpRequest{
						ActorId: "",
						Purpose: beTotpEnumsPb.Purpose_PURPOSE_NET_WORTH_MCP,
					}).
					Return(&beTotpPb.GenerateTotpResponse{
						Status: &rpcPb.Status{
							Code:         uint32(3), // INVALID_ARGUMENT
							ShortMessage: "actor id is required",
							DebugMessage: "empty actor id provided",
						},
					}, nil)
			},
			validateResponse: func(t *testing.T, resp *totpPb.GenerateTotpResponse) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.RespHeader)
				assert.Equal(t, uint32(13), resp.RespHeader.Status.Code) // INTERNAL
			},
		},
		{
			name: "successful_totp_with_different_actor",
			request: &totpPb.GenerateTotpRequest{
				Req: &headerPb.RequestHeader{
					Auth: &headerPb.AuthHeader{
						ActorId: "test-actor-456",
					},
				},
			},
			mockSetup: func() {
				mockTotpClient.EXPECT().
					GenerateTotp(gomock.Any(), &beTotpPb.GenerateTotpRequest{
						ActorId: "test-actor-456",
						Purpose: beTotpEnumsPb.Purpose_PURPOSE_NET_WORTH_MCP,
					}).
					Return(&beTotpPb.GenerateTotpResponse{
						Status: rpcPb.StatusOk(),
						Totp:   "654321",
						ExpiryTimestamp: &timestamppb.Timestamp{
							Seconds: 1640995800,
							Nanos:   500000000,
						},
					}, nil)
			},
			validateResponse: func(t *testing.T, resp *totpPb.GenerateTotpResponse) {
				assert.NotNil(t, resp)
				assert.NotNil(t, resp.RespHeader)
				assert.Equal(t, uint32(0), resp.RespHeader.Status.Code)
				assert.Equal(t, "654321", resp.Totp)
				assert.NotNil(t, resp.ExpiryTimestamp)
				assert.Equal(t, int64(1640995800), resp.ExpiryTimestamp.Seconds)
				assert.Equal(t, int32(500000000), resp.ExpiryTimestamp.Nanos)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockSetup()

			ctx := context.Background()
			resp, err := service.GenerateTotp(ctx, tt.request)

			assert.NoError(t, err)
			if tt.validateResponse != nil {
				tt.validateResponse(t, resp)
			}
		})
	}
}

func TestNewService(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockTotpClient := beTotpMocks.NewMockTotpClient(ctrl)

	service := NewService(mockTotpClient)

	require.NotNil(t, service)
	assert.Equal(t, mockTotpClient, service.totpClient)
}
