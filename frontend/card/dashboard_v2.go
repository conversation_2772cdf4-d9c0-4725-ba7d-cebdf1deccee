// nolint: goimports
package card

import (
	"context"
	"fmt"
	"sort"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	cmap "github.com/orcaman/concurrent-map"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	beCardPb "github.com/epifi/gamma/api/card"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	fePb "github.com/epifi/gamma/api/frontend/card"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	externalPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	dashboardSections "github.com/epifi/gamma/frontend/card/dashboard_sections"
	"github.com/epifi/gamma/frontend/card/metrics"
	cardPkg "github.com/epifi/gamma/pkg/card"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgOnb "github.com/epifi/gamma/pkg/onboarding"
)

func (s *Service) GetDashboardV2Sections(ctx context.Context, req *fePb.GetDashboardV2SectionsRequest) (*fePb.GetDashboardV2SectionsResponse, error) {
	var (
		res = &fePb.GetDashboardV2SectionsResponse{
			RespHeader: &header.ResponseHeader{},
		}
		// thread safe map for concurrent writes
		layoutSectionTypeToSectionMap = cmap.New()
		// cardDesignEnhancementEnabled Flag determines whether to use design fixit ui or fallback to old UI
		cardDesignEnhancementEnabled bool
		actorId                      = req.GetReq().GetAuth().GetActorId()
	)

	cardDesignEnhancementEnabled = cardPkg.DesignEnhancementEnabled(ctx, actorId, s.releaseEvaluator, s.onboardingClient, s.questSdkClient, s.userAttributeFetcher, s.networthClient, s.dynamicConf)

	layoutResponse, layoutErr := s.cardProvisioningClient.GetHomeLayoutConfiguration(ctx, &cardPb.GetHomeLayoutConfigurationRequest{
		ActorId: actorId,
	})
	currentCard := layoutResponse.GetCardUsageData().GetCurrentCard()

	switch {
	case epifigrpc.RPCError(layoutResponse, layoutErr) != nil && !layoutResponse.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "error while calling GetHomeLayoutConfiguration() in fe", zap.Error(layoutErr))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	case layoutResponse.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "no card found for actor", zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader.Status = rpc.StatusOk()
		return res, nil
	case currentCard.GetActorId() != actorId: // sanity check
		res.RespHeader.Status = rpc.StatusPermissionDenied()
		return res, nil
	default:
	}

	// build request to call layout sections builders
	buildSectionRequest, err := s.constructBuildSectionRequest(ctx, fePb.CardActionType_CARD_ACTION_UNSPECIFIED, layoutResponse.GetCardUsageData())
	if err != nil {
		logger.Error(ctx, "error fetching data to construct BuildSectionRequest",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}
	buildSectionRequest.FeRequestHeader = req.GetReq()
	buildSectionRequest.LayoutId = layoutResponse.GetLayoutId()
	buildSectionRequest.CardDesignEnhancementEnabled = cardDesignEnhancementEnabled

	layoutSections, layoutErr := s.cardDynamicConf.DashboardV2Config().GetLayoutOrdering(layoutResponse.GetLayoutId())
	if layoutErr != nil {
		logger.Error(ctx, "error while fetching layout sections ordering from config", zap.Error(layoutErr), zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	grp, grpCtx := errgroup.WithContext(ctx)
	// limiting number of goroutines to control resource utilization
	grp.SetLimit(3)
	for _, layoutSection := range layoutSections {
		layoutSection := layoutSection
		// this will be powered using a GetDashboardV2CardSection rpc
		if layoutSection == types.CardHomeSectionType_CARD_HOME_SECTION_TYPE_TOP_SECTION {
			continue
		}
		grp.Go(func() error {
			sectionBuilder, sectionErr := s.uiSectionBuilderFactory.GetUiSectionBuilder(grpCtx, layoutSection)
			if sectionErr != nil {
				logger.Error(grpCtx, "error fetching section builder", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(sectionErr))
				// In case of failure to get section builder, we'll log error and continue and not block the dashboard load
				metrics.FeCardMetricsRecorder.RecordCardDashboardSectionBuildFailureCounter(layoutSection.String())
				return nil
			}

			sectionResponse, sectionBuildErr := sectionBuilder.BuildSection(grpCtx, buildSectionRequest)
			if sectionBuildErr != nil {
				logger.Error(grpCtx, "error building section", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(sectionBuildErr))
				// In case of section build failures, we'll log error and continue and not block the dashboard load
				metrics.FeCardMetricsRecorder.RecordCardDashboardSectionBuildFailureCounter(layoutSection.String())
				return nil
			}
			// append section in response if applicable,
			// since builders will return response as nil if section is not relevant for user
			if sectionResponse != nil && sectionResponse.Section != nil {
				layoutSectionTypeToSectionMap.Set(layoutSection.String(), sectionResponse.Section)
			}
			return nil
		})
	}
	if waitErr := grp.Wait(); waitErr != nil {
		logger.Error(ctx, "error while building dashboard sections", zap.Error(waitErr))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Sections = getLayoutSectionsInSortedOrder(ctx, layoutSectionTypeToSectionMap, layoutSections)
	res.RespHeader.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) GetDashboardV2CardSection(ctx context.Context, req *fePb.GetDashboardV2CardSectionRequest) (*fePb.GetDashboardV2CardSectionResponse, error) {
	var (
		res = &fePb.GetDashboardV2CardSectionResponse{
			RespHeader: &header.ResponseHeader{},
		}
		// cardDesignEnhancementEnabled Flag determines whether to use design fixit ui or fallback to old UI
		cardDesignEnhancementEnabled bool
		actorId                      = req.GetReq().GetAuth().GetActorId()
	)

	cardDesignEnhancementEnabled = cardPkg.DesignEnhancementEnabled(ctx, actorId, s.releaseEvaluator, s.onboardingClient, s.questSdkClient, s.userAttributeFetcher, s.networthClient, s.dynamicConf)

	layoutResponse, layoutErr := s.cardProvisioningClient.GetHomeLayoutConfiguration(ctx, &cardPb.GetHomeLayoutConfigurationRequest{
		ActorId: actorId,
	})
	currentCard := layoutResponse.GetCardUsageData().GetCurrentCard()

	switch {
	case epifigrpc.RPCError(layoutResponse, layoutErr) != nil && !layoutResponse.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "error while calling GetHomeLayoutConfiguration() in fe", zap.Error(layoutErr))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	case layoutResponse.GetStatus().IsRecordNotFound():
		logger.Error(ctx, "no card found for actor", zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader.Status = rpc.StatusOk()
		res.RedirectionAction, _ = pkgOnb.GetDCBenefitsScreen()
		return res, nil
	case currentCard.GetActorId() != actorId: // sanity check
		res.RespHeader.Status = rpc.StatusPermissionDenied()
		return res, nil
	default:
	}

	sectionBuilder, sectionErr := s.uiSectionBuilderFactory.GetUiSectionBuilder(ctx, types.CardHomeSectionType_CARD_HOME_SECTION_TYPE_TOP_SECTION)
	if sectionErr != nil {
		logger.Error(ctx, "error fetching section builder", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(sectionErr))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	// build request to call layout sections builders
	buildSectionRequest, err := s.constructBuildSectionRequest(ctx, req.GetCardSectionPreferenceByAction(), layoutResponse.GetCardUsageData())
	if err != nil {
		logger.Error(ctx, "error fetching data to construct BuildSectionRequest",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}
	buildSectionRequest.CardDesignEnhancementEnabled = cardDesignEnhancementEnabled

	sectionResponse, sectionBuildErr := sectionBuilder.BuildSection(ctx, buildSectionRequest)
	if sectionBuildErr != nil {
		logger.Error(ctx, "error building section", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(sectionBuildErr))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}
	res.CardDetails = &fePb.CardDetail{
		Id: currentCard.GetId(),
	}
	res.TopHeaderSection = sectionResponse.CardSection
	res.RespHeader.Status = rpc.StatusOk()
	res.PageBackground = commontypes.GetVisualElementFromUrlHeightAndWidth(
		dcCcBgImage,
		572,
		412,
	)
	return res, nil
}

// constructBuildSectionRequest - fetches all the mandatory data required to build `dashboardSections.SectionBuilderRequest`
// and returns an object of type `dashboardSections.SectionBuilderRequest` with all the fields populated with the fetched data.
// nolint:funlen
func (s *Service) constructBuildSectionRequest(ctx context.Context, cardSectionPreferenceByAction fePb.CardActionType, userCardData *cardPb.UserCardData) (*dashboardSections.SectionBuilderRequest, error) {
	var (
		currentCard                           *beCardPb.Card
		allCardsList                          []*beCardPb.Card
		currentTier                           externalPb.Tier
		latestPhysicalDispatchReq             *cardPb.PhysicalCardDispatchRequest
		trackingDetails                       *cardPb.CardTrackingRequest
		physicalCardDeliveryStatus            cardPb.CardDeliveryTrackingState
		renewCardTypeSelectionScreenDl        *deeplinkPb.Deeplink
		vkycSummary                           *vkycPb.VKYCSummary
		getPhysicalCardDispatchStatusResponse *cardPb.GetPhysicalCardDispatchStatusResponse
	)

	currentCard = userCardData.GetCurrentCard()
	allCardsList = userCardData.GetCards()
	latestPhysicalDispatchReq = userCardData.GetLatestPhysicalDispatchRequest()
	physicalCardDeliveryStatus = userCardData.GetCardDeliveryTrackingInfo().GetState()
	currentTier = userCardData.GetCurrentTier()

	// in case of a layout preference by action, we will send a hardcoded response for the same.
	// this is done for cases such as view card details where client requires a layout to render  the
	// 16/4/3 on the dashboard.
	// We don't need any other details in such cases.
	if cardSectionPreferenceByAction == fePb.CardActionType_VIEW_DETAILS {
		return &dashboardSections.SectionBuilderRequest{
			CardSectionPreferenceByAction: cardSectionPreferenceByAction,
			CurrentCard:                   currentCard,
		}, nil
	}

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.SetLimit(3)

	// fetch tracking details
	grp.Go(func() error {
		fetchTrackingResp, err := s.cardProvisioningClient.GetCardShipmentTrackingDetails(grpCtx, &cardPb.GetCardShipmentTrackingDetailsRequest{
			CardId: currentCard.GetId(),
		})
		if te := epifigrpc.RPCError(fetchTrackingResp, err); te != nil {
			if !fetchTrackingResp.GetStatus().IsRecordNotFound() {
				return fmt.Errorf("error while fetching tracking for cardID %s: %w", currentCard.GetId(), te)
			}
		}
		trackingDetails = fetchTrackingResp.GetTrackingDetails()
		return nil
	})

	grp.Go(func() error {
		vkycStatusResp, vkycErr := s.vKYCClient.GetVKYCStatus(grpCtx, &vkycPb.GetVKYCStatusRequest{ActorId: currentCard.GetActorId()})
		if rpcErr := epifigrpc.RPCError(vkycStatusResp, vkycErr); rpcErr != nil && !vkycStatusResp.GetStatus().IsRecordNotFound() {
			/*
			 * Note: We're intentionally skipping the VKYC status error check here.
			 *
			 * Impact of this change:
			 * - If there's an error while fetching VKYC status, users may still see
			 *   the "Debit Card Order" entry points, regardless of their actual VKYC status.
			 *
			 * Why this is acceptable:
			 * - Users with VKYC_REJECTED status (classified as risky) are not eligible for physical debit cards.
			 * - Even if these users see the entry points, they won't be able to proceed with the order. A separate eligibility check in `FetchPhysicalCardChargesForUser`
			 *   ensures that such users do not receive the deeplink to the order card screen.
			 *
			 * Net impact on card ordering flow: None.
			 */
			logger.Error(grpCtx, "error fetching vkyc summary for actor",
				zap.String(logger.ACTOR_ID_V2, currentCard.GetActorId()), zap.Error(rpcErr))
		}
		vkycSummary = vkycStatusResp.GetVkycSummary()
		return nil
	})

	grp.Go(func() error {
		dispatchRequestStatusRes, dispatchRequestStatusResErr := s.cardProvisioningClient.GetPhysicalCardDispatchStatus(grpCtx, &cardPb.GetPhysicalCardDispatchStatusRequest{
			CardId: currentCard.GetId(),
		})
		if dispatchRequestStatusResErr != nil {
			logger.Error(grpCtx, "error getting physical card dispatch status", zap.Error(dispatchRequestStatusResErr), zap.String(logger.ACTOR_ID_V2, currentCard.GetActorId()))
			return nil
		}
		getPhysicalCardDispatchStatusResponse = dispatchRequestStatusRes
		return nil
	})

	if waitErr := grp.Wait(); waitErr != nil {
		return nil, fmt.Errorf("error fetching card-user data from BE: %w", waitErr)
	}

	// populate renew card dl if card is in blocked state
	if currentCard.GetState() == beCardPb.CardState_BLOCKED {
		renewCardScreenDl, err := s.getRequestNewCardScreenCtaInfo(ctx, currentCard, vkycSummary)
		if err != nil {
			return nil, fmt.Errorf("error while fetching renew card type selection screen dl: %w", err)
		}
		renewCardTypeSelectionScreenDl = renewCardScreenDl
	}

	// sort cards by createdAt Desc order (list will be sorted on updatedAt desc)
	sort.Slice(allCardsList, func(i, j int) bool {
		return allCardsList[i].GetCreatedAt().AsTime().After(allCardsList[j].GetCreatedAt().AsTime())
	})

	hadPhysicalCardInPast := func() bool {
		for _, card := range allCardsList {
			if card.GetForm() == beCardPb.CardForm_PHYSICAL {
				return true
			}
		}
		return false
	}()

	return &dashboardSections.SectionBuilderRequest{
		ActorId:                               currentCard.GetActorId(),
		CurrentCard:                           currentCard,
		AllCardsList:                          allCardsList,
		LatestPhysicalDispatchRequest:         latestPhysicalDispatchReq,
		PhysicalCardDeliveryState:             physicalCardDeliveryStatus,
		TrackingDetails:                       trackingDetails,
		CurrentTier:                           currentTier,
		RenewCardSelectionTypeScreenDl:        renewCardTypeSelectionScreenDl,
		HadPhysicalCardInPast:                 hadPhysicalCardInPast,
		VKYCSummary:                           vkycSummary,
		IsTravelModeOn:                        userCardData.GetIsTravelModeOn(),
		GetPhysicalCardDispatchStatusResponse: getPhysicalCardDispatchStatusResponse,
	}, nil
}

func (s *Service) checkFeatureReleaseConstraints(ctx context.Context, feature types.Feature, actorId string) bool {
	isFeatureEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(feature).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error evaluating feature release constraints", zap.String(logger.FEATURE, feature.String()), zap.Error(err))
		isFeatureEnabled = false
	}
	return isFeatureEnabled
}

func getLayoutSectionsInSortedOrder(ctx context.Context, layoutSectionTypeToSectionMap cmap.ConcurrentMap, sortingOrder []types.CardHomeSectionType) []*fePb.Section {
	orderedLayoutSections := make([]*fePb.Section, 0)
	for _, layoutSectionType := range sortingOrder {
		layoutSection, found := layoutSectionTypeToSectionMap.Get(layoutSectionType.String())
		if !found {
			continue
		}
		layoutSectionProto, ok := layoutSection.(*fePb.Section)
		if !ok {
			logger.Error(ctx, "error converting section interface to proto", zap.String("cardHomeSectionType", layoutSectionType.String()))
			continue
		}
		orderedLayoutSections = append(orderedLayoutSections, layoutSectionProto)
	}
	return orderedLayoutSections
}
