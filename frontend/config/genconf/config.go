// nolint: dupl
package genconf

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/json"
	"fmt"
	"runtime"
	"sync"

	"github.com/mohae/deepcopy"
	"github.com/pkg/errors"

	questtypes "github.com/epifi/be-common/api/quest/types"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	"github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"

	gammaquestsdk "github.com/epifi/gamma/quest/sdk"

	beCaEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	caScreensPb "github.com/epifi/gamma/api/frontend/connected_account/screens"
	timelinePb "github.com/epifi/gamma/api/frontend/deeplink/timeline"
	afv2Pb "github.com/epifi/gamma/api/frontend/pay/add_funds_v2"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config"
)

var (
	once       sync.Once
	genConf    *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

// Load initializes the object of the generated conf struct by copying the values from the Static conf.
// Then it starts watcher to update remote config.
func Load() (*Config, error) {
	once.Do(func() {
		genConf, err = loadConfig(false)
	})
	if err != nil {
		return nil, err
	}
	return genConf, err
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		genConf, err = loadConfig(true)
	})
	if err != nil {
		return nil, err
	}
	return genConf, err
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {
	var staticConf *config.Config
	if onlyStaticFiles {
		// load config with only static config files without loading any secrets
		staticConf, err = config.LoadOnlyStaticConf()
	} else {
		staticConf, err = config.Load()
	}
	if err != nil {
		return nil, err
	}
	// clone staticConf to avoid any potential write on the object when the remote config changes are applied.
	// Since static conf is not concurrent safe, these write may lead to race condition.
	staticConfClone := deepcopy.Copy(staticConf).(*config.Config)
	gconf, setters := NewConfigWithQuest(string(cfg.FRONTEND_SERVICE))
	err = gconf.Set(staticConfClone, false, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to set statis staticConf in dynamic config: %w", err)
	}
	if onlyStaticFiles {
		return gconf, nil
	}
	sc := dynconf.NewSafeConfigV2(staticConfClone, setters)
	envName, err := cfg.GetEnvironment()
	if err != nil {
		return nil, fmt.Errorf("failed to get environment: %w", err)
	}
	err = dynconf.LoadAndUpdateFromRemoteStoreV2(envName, cfg.FRONTEND_SERVICE, sc)
	if err != nil {
		return nil, err
	}

	return gconf, nil
}

func GetDeviceIdsForSafetynetV2Flow(feConf *Config) (map[string]bool, error) {
	var deviceIds []string
	safetyNetV2EnabledDeviceIds := make(map[string]bool)
	err := json.Unmarshal([]byte(feConf.Secrets().Ids[config.DeviceIdsEnabledForSafetyNetV2]), &deviceIds)
	if err != nil {
		return nil, err
	}
	for _, id := range deviceIds {
		safetyNetV2EnabledDeviceIds[id] = true
	}
	return safetyNetV2EnabledDeviceIds, nil
}

func (c *Config) GetUrnPrefixForUpiApp(upiApp afv2Pb.UpiApp) (string, error) {
	val, ok := c.AddFundsV2Params().UpiAppUrnPrefixMap()[upiApp.String()]
	if !ok {
		return "", errors.New(fmt.Sprintf("error getting urn prefix for upi app %s ", upiApp.String()))
	}
	return val.UrnPrefix, nil
}

func (obj *HomeLayoutV2Params) GetExperimentsFromQuest(ctx context.Context, questVariable string) *questtypes.ExperimentList {
	if obj.questSdk == nil {
		logger.Error(ctx, "quest sdk object is nil")
		return &questtypes.ExperimentList{}
	}
	res, err := obj.questSdk.(*gammaquestsdk.Client).GetExperiments(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + questVariable})
	if err != nil {
		return &questtypes.ExperimentList{}
	}
	return res
}

func (b *BenefitsScreenOptions) GetCloseIconImage() *commontypes.VisualElement {
	closeIconImage := &commontypes.VisualElement{
		Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Properties: &commontypes.VisualElementProperties{
					Width:  b.CloseIconImage().Properties().Width(),
					Height: b.CloseIconImage().Properties().Height(),
				},
				ImageType: commontypes.ImageType_PNG,
				RenderingType: &commontypes.ImageRenderingType{
					Type: &commontypes.ImageRenderingType_RegularImage{},
				},
			},
		},
	}

	if b.CloseIconImage().Url() != "" {
		closeIconImage.GetImage().Source = &commontypes.VisualElement_Image_Url{
			Url: b.CloseIconImage().Url(),
		}
	}

	if b.CloseIconImage().DataString() != "" {
		closeIconImage.GetImage().Source = &commontypes.VisualElement_Image_DataString{
			DataString: b.CloseIconImage().DataString(),
		}
	}
	return closeIconImage
}

func (b *BenefitsScreenOptions) GetCloseIconLottie() *commontypes.VisualElement {
	closeIconLottie := &commontypes.VisualElement{
		Asset: &commontypes.VisualElement_Lottie_{
			Lottie: &commontypes.VisualElement_Lottie{
				RepeatCount: b.CloseIconLottie().RepeatCount(),
				Properties: &commontypes.VisualElementProperties{
					Width:  b.CloseIconLottie().Properties().Width(),
					Height: b.CloseIconLottie().Properties().Height(),
				},
			},
		},
	}

	if b.CloseIconLottie().LottieUrl() != "" {
		closeIconLottie.GetLottie().Source = &commontypes.VisualElement_Lottie_Url{
			Url: b.CloseIconLottie().LottieUrl(),
		}
	}

	if b.CloseIconLottie().LottieJsonString() != "" {
		closeIconLottie.GetLottie().Source = &commontypes.VisualElement_Lottie_JsonString{
			JsonString: b.CloseIconLottie().LottieJsonString(),
		}
	}
	return closeIconLottie
}

func (b *BenefitsScreenOptions) GetFipContainer() *caScreensPb.BenefitScreen_BenefitsFipContainer {
	return &caScreensPb.BenefitScreen_BenefitsFipContainer{
		BackgroundColor: b.FipContainer().BackgroundColor(),
		Title: &ui.IconTextComponent{
			Texts: NewTextProtosFromConfigsList(b.FipContainer().Title().Texts()),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       b.FipContainer().Title().ContainerProperties().BgColor(),
				CornerRadius:  b.FipContainer().Title().ContainerProperties().CornerRadius(),
				Height:        b.FipContainer().Title().ContainerProperties().Height(),
				Width:         b.FipContainer().Title().ContainerProperties().Width(),
				LeftPadding:   b.FipContainer().Title().ContainerProperties().Padding().left(),
				RightPadding:  b.FipContainer().Title().ContainerProperties().Padding().right(),
				TopPadding:    b.FipContainer().Title().ContainerProperties().Padding().top(),
				BottomPadding: b.FipContainer().Title().ContainerProperties().Padding().bottom(),
				BorderColor:   b.FipContainer().Title().ContainerProperties().BorderColor(),
				BorderWidth:   b.FipContainer().Title().ContainerProperties().BorderWidth(),
			},
		},
		Subtitle: &ui.IconTextComponent{
			Texts: NewTextProtosFromConfigsList(b.FipContainer().SubTitle().Texts()),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       b.FipContainer().SubTitle().ContainerProperties().BgColor(),
				CornerRadius:  b.FipContainer().SubTitle().ContainerProperties().CornerRadius(),
				Height:        b.FipContainer().SubTitle().ContainerProperties().Height(),
				Width:         b.FipContainer().SubTitle().ContainerProperties().Width(),
				LeftPadding:   b.FipContainer().SubTitle().ContainerProperties().Padding().left(),
				RightPadding:  b.FipContainer().SubTitle().ContainerProperties().Padding().right(),
				TopPadding:    b.FipContainer().SubTitle().ContainerProperties().Padding().top(),
				BottomPadding: b.FipContainer().SubTitle().ContainerProperties().Padding().bottom(),
				BorderColor:   b.FipContainer().SubTitle().ContainerProperties().BorderColor(),
				BorderWidth:   b.FipContainer().SubTitle().ContainerProperties().BorderWidth(),
			},
			LeftVisualElement:  nil,
			RightVisualElement: nil,
		},
		Fips: convertFipConfigToProto(b.FipContainer().Fips()),
	}
}

func NewTextProtosFromConfigsList(textConfigs []*config.Text) []*commontypes.Text {
	var textProtos []*commontypes.Text
	for _, textConfig := range textConfigs {
		textProtos = append(textProtos, &commontypes.Text{
			FontColor: textConfig.FontColor,
			BgColor:   textConfig.BgColor,
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: textConfig.Content,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[textConfig.FontStyle]),
			},
		})
	}
	return textProtos
}

func convertFipConfigToProto(fipConfigs []*config.VerticalIconTextComponent) []*ui.VerticalIconTextComponent {
	var fipProtos []*ui.VerticalIconTextComponent
	for _, fipConfig := range fipConfigs {
		vitc := &ui.VerticalIconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor: fipConfig.Text.FontColor,
					BgColor:   fipConfig.Text.BgColor,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: fipConfig.Text.Content,
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[fipConfig.Text.FontStyle]),
					},
				},
			},
			ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
				BgColor:       fipConfig.ContainerProperties.BgColor,
				CornerRadius:  fipConfig.ContainerProperties.CornerRadius,
				Height:        fipConfig.ContainerProperties.Height,
				Width:         fipConfig.ContainerProperties.Width,
				LeftPadding:   fipConfig.ContainerProperties.Padding.Left,
				RightPadding:  fipConfig.ContainerProperties.Padding.Right,
				TopPadding:    fipConfig.ContainerProperties.Padding.Top,
				BottomPadding: fipConfig.ContainerProperties.Padding.Bottom,
				BorderColor:   fipConfig.ContainerProperties.BorderColor,
				BorderWidth:   fipConfig.ContainerProperties.BorderWidth,
			},
			TopVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: fipConfig.TopVisualElement.Url,
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  fipConfig.TopVisualElement.Properties.Width,
							Height: fipConfig.TopVisualElement.Properties.Height,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
		}
		fipProtos = append(fipProtos, vitc)
	}
	return fipProtos
}

func (c *Config) GetBenefitScreenOptions(benefitScreenOptions *BenefitsScreenOptions) *caScreensPb.BenefitScreen {
	benefitsScreen := &caScreensPb.BenefitScreen{
		CloseIcon:       benefitScreenOptions.GetCloseIconImage(),
		BackgroundColor: benefitScreenOptions.BackgroundColor(),
		FipContainer:    benefitScreenOptions.GetFipContainer(),
		Title: &ui.IconTextComponent{
			Texts: NewTextProtosFromConfigsList(benefitScreenOptions.Title().Texts()),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       benefitScreenOptions.Title().ContainerProperties().BgColor(),
				CornerRadius:  benefitScreenOptions.Title().ContainerProperties().CornerRadius(),
				Height:        benefitScreenOptions.Title().ContainerProperties().Height(),
				Width:         benefitScreenOptions.Title().ContainerProperties().Width(),
				LeftPadding:   benefitScreenOptions.Title().ContainerProperties().Padding().left(),
				RightPadding:  benefitScreenOptions.Title().ContainerProperties().Padding().right(),
				TopPadding:    benefitScreenOptions.Title().ContainerProperties().Padding().top(),
				BottomPadding: benefitScreenOptions.Title().ContainerProperties().Padding().bottom(),
				BorderColor:   benefitScreenOptions.Title().ContainerProperties().BorderColor(),
				BorderWidth:   benefitScreenOptions.Title().ContainerProperties().BorderWidth(),
			},
		},
		Desc: &ui.IconTextComponent{
			Texts: NewTextProtosFromConfigsList(benefitScreenOptions.Desc().Texts()),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       benefitScreenOptions.Desc().ContainerProperties().BgColor(),
				CornerRadius:  benefitScreenOptions.Desc().ContainerProperties().CornerRadius(),
				Height:        benefitScreenOptions.Desc().ContainerProperties().Height(),
				Width:         benefitScreenOptions.Desc().ContainerProperties().Width(),
				LeftPadding:   benefitScreenOptions.Desc().ContainerProperties().Padding().left(),
				RightPadding:  benefitScreenOptions.Desc().ContainerProperties().Padding().right(),
				TopPadding:    benefitScreenOptions.Desc().ContainerProperties().Padding().top(),
				BottomPadding: benefitScreenOptions.Desc().ContainerProperties().Padding().bottom(),
				BorderColor:   benefitScreenOptions.Desc().ContainerProperties().BorderColor(),
				BorderWidth:   benefitScreenOptions.Desc().ContainerProperties().BorderWidth(),
			},
		},
		Benefits: NewITCPbFromConfigList(benefitScreenOptions.Benefits()),
	}
	return benefitsScreen
}

func NewITCPbFromConfigList(itcConfigs []*config.IconTextComponent) []*ui.IconTextComponent {
	var itcProtos []*ui.IconTextComponent
	for _, itcConfig := range itcConfigs {
		itcProto := &ui.IconTextComponent{
			Texts:             NewTextProtosFromConfigsList(itcConfig.Texts),
			LeftImgTxtPadding: itcConfig.LeftImgTxtPadding,
		}
		if itcConfig.ContainerProperties != nil {
			itcProto.WithContainerProperties(&ui.IconTextComponent_ContainerProperties{
				BgColor:       itcConfig.ContainerProperties.BgColor,
				CornerRadius:  itcConfig.ContainerProperties.CornerRadius,
				Height:        itcConfig.ContainerProperties.Height,
				Width:         itcConfig.ContainerProperties.Width,
				LeftPadding:   itcConfig.ContainerProperties.Padding.Left,
				RightPadding:  itcConfig.ContainerProperties.Padding.Right,
				TopPadding:    itcConfig.ContainerProperties.Padding.Top,
				BottomPadding: itcConfig.ContainerProperties.Padding.Bottom,
				BorderColor:   itcConfig.ContainerProperties.BorderColor,
				BorderWidth:   itcConfig.ContainerProperties.BorderWidth,
			})
		}
		if itcConfig.LeftVisualElementImage != nil {
			itcProto.WithLeftVisualElement(&commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: itcConfig.LeftVisualElementImage.Url,
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  itcConfig.LeftVisualElementImage.Properties.Width,
							Height: itcConfig.LeftVisualElementImage.Properties.Height,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			})
		}
		itcProtos = append(itcProtos, itcProto)
	}
	return itcProtos
}

func (c *Config) GetBenefitsScreenOptionsFromMap(caFlowName string) (*BenefitsScreenOptions, error) {
	val, ok := c.ConnectedAccount().DisplayScreenOptions().BenefitsScreenOptionsMap().Load(caFlowName)
	if !ok {
		defaultConfig, isDefaultConfigPresent := c.ConnectedAccount().DisplayScreenOptions().BenefitsScreenOptionsMap().Load(beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String())
		if !isDefaultConfigPresent {
			return nil, fmt.Errorf("key not found for caFlowname: %s in BenefitsScreenOptionsMap", caFlowName)
		}
		return defaultConfig, nil
	}
	return val, nil
}

func (c *Config) GetDiscoveryScreenOptionsFromMap(caFlowName string) (*AccountDiscoveryScreenOptions, error) {
	val, ok := c.ConnectedAccount().DisplayScreenOptions().AccountDiscoveryScreenOptionMap().Load(caFlowName)
	if !ok {
		defaultConfig, isDefaultConfigPresent := c.ConnectedAccount().DisplayScreenOptions().AccountDiscoveryScreenOptionMap().Load(beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String())
		if !isDefaultConfigPresent {
			return nil, fmt.Errorf("key not found for caFlowname: %s in AccountDiscoveryScreenOptionMap", caFlowName)
		}
		return defaultConfig, nil
	}
	return val, nil
}

func (c *Config) GetNoAccDiscoverScreenParamsFromMap(caFlowName string) (*NoAccDiscoveryScreenOptions, error) {
	val, ok := c.ConnectedAccount().DisplayScreenOptions().NoAccDiscoveryScreenOptionsMap().Load(caFlowName)
	if !ok {
		defaultConfig, isDefaultConfigPresent := c.ConnectedAccount().DisplayScreenOptions().NoAccDiscoveryScreenOptionsMap().Load(beCaEnumPb.CAFlowName_CA_FLOW_NAME_CONNECTED_ACCOUNT_DEFAULT.String())
		if !isDefaultConfigPresent {
			return nil, fmt.Errorf("key not found for caFlowname: %s in NoAccDiscoveryScreenOptionsMap", caFlowName)
		}
		return defaultConfig, nil
	}
	return val, nil
}

func (bc *BgColor) NewBgColorPb() *widgetPb.BackgroundColour {
	// Linear gradient takes priority over block colour
	if bc.BgColorLinearGradientStart() != "" && bc.BgColorLinearGradientEnd() != "" {
		return &widgetPb.BackgroundColour{
			Colour: &widgetPb.BackgroundColour_LinearGradient{
				LinearGradient: &widgetPb.LinearGradient{
					Degree: 0,
					LinearColorStops: []*widgetPb.ColorStop{
						{
							Color:          bc.BgColorLinearGradientStart(),
							StopPercentage: 100,
						},
						{
							Color:          bc.BgColorLinearGradientEnd(),
							StopPercentage: 0,
						},
					},
				},
			},
		}
	}
	return &widgetPb.BackgroundColour{
		Colour: &widgetPb.BackgroundColour_BlockColour{
			BlockColour: bc.BgColorBlockColor(),
		},
	}
}

func (v *VisualElementTitleSubtitleElement) NewVisualElementTitleSubtitleElementPb() *widgetPb.VisualElementTitleSubtitleElement {
	// Visual element takes priority if it is present
	if v.VisualElement().ImageUrl() != "" || v.VisualElement().DataString() != "" {
		return &widgetPb.VisualElementTitleSubtitleElement{
			VisualElement: genconf.NewVisualElementImagePb(v.VisualElement()),
		}
	}
	return &widgetPb.VisualElementTitleSubtitleElement{
		TitleText:       genconf.NewTextPb(v.TitleText(), ""),
		SubtitleText:    genconf.NewTextPb(v.SubtitleText(), ""),
		BackgroundColor: v.BackgroundColor(),
	}
}

func (c *Config) GetOnbAddFundsSuccessScreenOptionsFromMap(tier string) (*OnbAddFundsSuccessScreenOptions, error) {
	val, ok := c.Tiering().DisplayConfig().OnbAddFundsSuccessScreenOptionsMap().Load(tier)
	if !ok {
		return nil, fmt.Errorf("key not found for tier: %s in OnbAddFundsSuccessScreenOptionsMap", tier)
	}
	return val, nil
}

// GetDataCollectorConfig gets the DataCollector Config for a given entrypoint,
// If config for given entrypoint not present, returns default config
func (o *PaymentOptionsConfig) GetDataCollectorConfig(entrypoint timelinePb.TransactionUIEntryPoint) (*PaymentOptionsDataCollectorConfig, error) {
	dataCollectorConfig, isEntryPointPresent := o.EntryPointToDataCollectorConfigMap().Load(entrypoint.String())
	if !isEntryPointPresent {
		defaultDataCollectorConfig, isDefaultEntryPointPresent := o.EntryPointToDataCollectorConfigMap().Load("DEFAULT")
		if !isDefaultEntryPointPresent {
			return nil, fmt.Errorf("default entrypoint not present in EntryPointToDataCollectorConfigMap")
		}
		dataCollectorConfig = defaultDataCollectorConfig
	}
	return dataCollectorConfig, nil
}

// GetDisplayInfoConfig - get the PaymentOptions display config for a given entrypoint,
// if config for given entrypoint is not present, returns default config
func (o *PaymentOptionsConfig) GetDisplayInfoConfig(entrypoint timelinePb.TransactionUIEntryPoint) (*PaymentOptionDisplayInfo, error) {
	displayConfig, isEntryPointPresent := o.EntryPointToPaymentOptionsDisplayInfoMap().Load(entrypoint.String())
	if !isEntryPointPresent {
		defaultDisplayConfig, isDefaultEntryPointPresent := o.EntryPointToPaymentOptionsDisplayInfoMap().Load("DEFAULT")
		if !isDefaultEntryPointPresent {
			return nil, fmt.Errorf("default entrypoint not present in EntryPointToPaymentOptionsDisplayInfoMap")
		}
		displayConfig = defaultDisplayConfig
	}
	return displayConfig, nil
}

func (o *DcDashboardV2Config) GetLayoutOrdering(layoutId string) ([]types.CardHomeSectionType, error) {
	sectionsOrderConfig, isLayoutIdPresent := o.LayoutSectionOrderMap().Load(layoutId)
	if !isLayoutIdPresent {
		defaultSectionOrderConfig, isDefaultLayoutPresent := o.LayoutSectionOrderMap().Load("LAYOUT_ID_DEFAULT")
		if !isDefaultLayoutPresent {
			return nil, fmt.Errorf("default layout order config not present")
		}
		sectionsOrderConfig = defaultSectionOrderConfig
	}
	sectionsOrderList := make([]types.CardHomeSectionType, 0)
	for _, component := range sectionsOrderConfig.LayoutSectionsOrder().Slice() {
		componentEnum := types.CardHomeSectionType(types.CardHomeSectionType_value[component])
		sectionsOrderList = append(sectionsOrderList, componentEnum)
	}
	return sectionsOrderList, nil
}

func (o *CCIntroScreenV2Config) GetScreenOptionForCardProgram(cardProgramType string) (*CCIntroScreenV2Options, error) {
	screenOptions, isPresent := o.CardProgramTypeToScreenConfigMap().Load(cardProgramType)
	if !isPresent {
		return nil, fmt.Errorf("screenOption not present in CardProgramTypeToScreenConfigMap")
	}
	return screenOptions, nil
}
