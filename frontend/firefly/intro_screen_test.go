package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	ffBePb "github.com/epifi/gamma/api/firefly"
	mocksffRePb "github.com/epifi/gamma/api/firefly/card_recommendation/mocks"
	ffEnumsBePb "github.com/epifi/gamma/api/firefly/enums"
	mockffPb "github.com/epifi/gamma/api/firefly/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	"github.com/epifi/gamma/api/frontend/header"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/frontend/firefly/fees_and_benefits"
	"github.com/epifi/gamma/frontend/firefly/rewards"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseEvaluatorMocks "github.com/epifi/gamma/pkg/feature/release/mocks"
)

var (
	actorId       = "sampleActor"
	pageIndicator = &ffPb.PageIndicatorWidget{
		SelectedColor:   "#EFF2F6",
		UnSelectedColor: "#57595D",
		DrawableProperties: &ffPb.DrawableProperties{
			BorderProperty: &ffPb.BorderProperty{
				BorderThickness: 0,
			},
			CornerProperty: &ffPb.CornerProperty{
				TopStartCornerRadius: 0,
				TopEndCornerRadius:   0,
				BottomStartCorner:    0,
				BottomEndCorner:      0,
			},
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{},
			},
			Shadow: &widget.Shadow{
				Height:  0,
				Blur:    0,
				Opacity: 0,
				Colour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{},
				},
			},
		},
		Properties: &ffPb.LayoutProperties{
			Size: &ffPb.Size{
				Width:  -1,
				Height: -2,
			},
			Paddings: &ffPb.PaddingProperty{
				Start:  10,
				Top:    10,
				End:    10,
				Bottom: 10,
			},
			Margins: &ffPb.MarginProperty{
				Start:  0,
				Top:    0,
				End:    0,
				Bottom: 16,
			},
		},
	}
	autoScrollProperties = &ffPb.PagerAutoScrollProperties{
		AutoScrollDelay:      6000,
		UserInteractionDelay: 7000,
	}
	consentText = &ffPb.WrappedHyperLinksWidget{
		ConsentText: &ui.TextWithHyperlinks{
			Text: &commontypes.Text{
				FontColor:    "#A4A4A4",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Read Federal Bank’s Most Important Terms & Conditions, Key Fact Statement and Terms and Conditions"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_5},
			},
			HyperlinkMap: map[string]*ui.HyperLink{
				"Terms and Conditions": {
					Link:           &ui.HyperLink_Url{Url: "https://fi.money/credit-card/T&Cs"},
					EventParameter: "T&CButton",
				},
				"Key Fact Statement": {
					Link:           &ui.HyperLink_Url{Url: "https://fi.money/credit-card/key-fact-statement"},
					EventParameter: "KeyFactStatementButton",
				},
				"Most Important Terms & Conditions": {
					Link:           &ui.HyperLink_Url{Url: "https://fi.money/credit-card/important-T&Cs"},
					EventParameter: "MostImpT&CButton",
				},
			},
		},
		DrawableProperties: &ffPb.DrawableProperties{
			BorderProperty: &ffPb.BorderProperty{
				BorderThickness: 0,
			},
			CornerProperty: &ffPb.CornerProperty{
				TopStartCornerRadius: 0,
				TopEndCornerRadius:   0,
				BottomStartCorner:    0,
				BottomEndCorner:      0,
			},
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{},
			},
			Shadow: &widget.Shadow{
				Height:  0,
				Blur:    0,
				Opacity: 0,
				Colour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{},
				},
			},
		},
		Properties: &ffPb.LayoutProperties{
			Size: &ffPb.Size{
				Width:  -1,
				Height: -2,
			},
			Paddings: &ffPb.PaddingProperty{
				Start:  0,
				Top:    0,
				End:    0,
				Bottom: 0,
			},
			Margins: &ffPb.MarginProperty{
				Start:  0,
				Top:    0,
				End:    0,
				Bottom: 0,
			},
		},
		EventParameter: "T&CHyperLinkButton",
	}
	partnerIcon = &ffPb.WrappedVisualElement{
		VisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/credit_card_images/federal_co_branded_intro_footer.png",
					},
					ImageType: commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{
						Width:  412,
						Height: 32,
					},
				},
			},
		},
		DrawableProperties: &ffPb.DrawableProperties{
			BorderProperty: &ffPb.BorderProperty{
				BorderThickness: 0,
			},
			CornerProperty: &ffPb.CornerProperty{
				TopStartCornerRadius: 0,
				TopEndCornerRadius:   0,
				BottomStartCorner:    0,
				BottomEndCorner:      0,
			},
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#282828"},
			},
			Shadow: &widget.Shadow{
				Height:  0,
				Blur:    0,
				Opacity: 0,
				Colour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{},
				},
			},
		},
		Properties: &ffPb.LayoutProperties{
			Size: &ffPb.Size{
				Width:  -1,
				Height: -2,
			},
			Paddings: &ffPb.PaddingProperty{
				Start:  0,
				Top:    0,
				End:    0,
				Bottom: 4,
			},
			Margins: &ffPb.MarginProperty{
				Start:  0,
				Top:    0,
				End:    0,
				Bottom: 0,
			},
		},
	}
	wrappedButtonInfo = &ffPb.WrappedButtonInfo{
		Text: &commontypes.Text{
			FontColor:    "#FFFFFF",
			DisplayValue: &commontypes.Text_Html{Html: "Get this Credit Card"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_2},
		},
		DrawableProperties: &ffPb.DrawableProperties{
			BorderProperty: &ffPb.BorderProperty{
				BorderThickness: 0,
			},
			CornerProperty: &ffPb.CornerProperty{
				TopStartCornerRadius: 20,
				TopEndCornerRadius:   20,
				BottomStartCorner:    20,
				BottomEndCorner:      20,
			},
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#00B899"},
			},
			Shadow: &widget.Shadow{
				Height:  0,
				Blur:    0,
				Opacity: 0,
				Colour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{},
				},
			},
		},
		Properties: &ffPb.LayoutProperties{
			Size: &ffPb.Size{
				Width:  -1,
				Height: -2,
			},
			Paddings: &ffPb.PaddingProperty{
				Start:  10,
				Top:    12,
				End:    10,
				Bottom: 12,
			},
			Margins: &ffPb.MarginProperty{
				Start:  16,
				Top:    16,
				End:    16,
				Bottom: 16,
			},
		},
	}
	textIconToolBar = &ffPb.WrappedIconTextToolBar{
		Title: &ffPb.WrappedTextInfo{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{Html: ""},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  0,
					Height: 0,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
			},
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 0,
					TopEndCornerRadius:   0,
					BottomStartCorner:    0,
					BottomEndCorner:      0,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
		},
		BackArrowColor: "#18191B",
		VisualElement: &ffPb.WrappedVisualElement{
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/credit_card_images/Text_Icon_Toolbar_fi_icon.png",
						},
						ImageType: commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -2,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
			},
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 0,
					TopEndCornerRadius:   0,
					BottomStartCorner:    0,
					BottomEndCorner:      0,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
		},
		Properties: &ffPb.LayoutProperties{
			Size: &ffPb.Size{
				Width:  -1,
				Height: -2,
			},
			Paddings: &ffPb.PaddingProperty{
				Start:  0,
				Top:    0,
				End:    0,
				Bottom: 0,
			},
			Margins: &ffPb.MarginProperty{
				Start:  0,
				Top:    0,
				End:    0,
				Bottom: 0,
			},
		},
		DrawableProperties: &ffPb.DrawableProperties{
			BorderProperty: &ffPb.BorderProperty{
				BorderThickness: 0,
			},
			CornerProperty: &ffPb.CornerProperty{
				TopStartCornerRadius: 0,
				TopEndCornerRadius:   0,
				BottomStartCorner:    0,
				BottomEndCorner:      0,
			},
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{},
			},
			Shadow: &widget.Shadow{
				Height:  0,
				Blur:    0,
				Opacity: 0,
				Colour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{},
				},
			},
		},
	}

	benefitCardInfo = []*ffPb.BenefitCardInfo{
		{
			Title: &commontypes.Text{
				FontColor:    "#FFFFFF",
				DisplayValue: &commontypes.Text_Html{Html: "Lounge access & flight class upgrades"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Desc: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{Html: ""},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED},
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/credit_card_images/HL_plane_Cta_4x.png",
						},
						ImageType: commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{
							Width:  52,
							Height: 34,
						},
					},
				},
			},
			Chevron: &ffPb.WrappedChevron{
				VisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/credit_card_images/HL_Benefit_chevron.png",
							},
							ImageType: commontypes.ImageType_PNG,
							Properties: &commontypes.VisualElementProperties{
								Width:  20,
								Height: 20,
							},
						},
					},
				},
				Properties: &ffPb.LayoutProperties{
					Size: &ffPb.Size{
						Width:  -2,
						Height: -2,
					},
					Paddings: &ffPb.PaddingProperty{
						Start:  0,
						Top:    0,
						End:    0,
						Bottom: 0,
					},
					Margins: &ffPb.MarginProperty{
						Start:  0,
						Top:    0,
						End:    0,
						Bottom: 0,
					},
				},
				DrawableProperties: &ffPb.DrawableProperties{
					BorderProperty: &ffPb.BorderProperty{
						BorderThickness: 0,
					},
					CornerProperty: &ffPb.CornerProperty{
						TopStartCornerRadius: 10,
						TopEndCornerRadius:   10,
						BottomStartCorner:    10,
						BottomEndCorner:      10,
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#38393B"},
					},
					Shadow: &widget.Shadow{
						Height:  0,
						Blur:    0,
						Opacity: 0,
						Colour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
					},
				},
			},
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CC_INTRO_BOTTOM_SHEET,
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  140,
					Height: 150,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  12,
					Top:    12,
					End:    12,
					Bottom: 12,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    0,
					End:    10,
					Bottom: 0,
				},
			},
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#313234"},
				},
				Shadow: &widget.Shadow{
					Height:  4,
					Blur:    0,
					Opacity: 100,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#141414"},
					},
				},
			},
			EventParameter: "TravelAndLoungeAccessBottomSheetButton",
		},
		{
			Title: &commontypes.Text{
				FontColor:    "#FFFFFF",
				DisplayValue: &commontypes.Text_Html{Html: "Milestone rewards over ₹10,000"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Desc: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{Html: ""},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED},
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/credit_card_images/tag_HL_4x.png",
						},
						ImageType: commontypes.ImageType_PNG,
						Properties: &commontypes.VisualElementProperties{
							Width:  32,
							Height: 40,
						},
					},
				},
			},
			Chevron: &ffPb.WrappedChevron{
				VisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/credit_card_images/HL_Benefit_chevron.png",
							},
							ImageType: commontypes.ImageType_PNG,
							Properties: &commontypes.VisualElementProperties{
								Width:  20,
								Height: 20,
							},
						},
					},
				},
				Properties: &ffPb.LayoutProperties{
					Size: &ffPb.Size{
						Width:  -2,
						Height: -2,
					},
					Paddings: &ffPb.PaddingProperty{
						Start:  0,
						Top:    0,
						End:    0,
						Bottom: 0,
					},
					Margins: &ffPb.MarginProperty{
						Start:  0,
						Top:    0,
						End:    0,
						Bottom: 0,
					},
				},
				DrawableProperties: &ffPb.DrawableProperties{
					BorderProperty: &ffPb.BorderProperty{
						BorderThickness: 0,
					},
					CornerProperty: &ffPb.CornerProperty{
						TopStartCornerRadius: 10,
						TopEndCornerRadius:   10,
						BottomStartCorner:    10,
						BottomEndCorner:      10,
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#38393B"},
					},
					Shadow: &widget.Shadow{
						Height:  0,
						Blur:    0,
						Opacity: 0,
						Colour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
					},
				},
			},
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CC_INTRO_BOTTOM_SHEET,
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  140,
					Height: 150,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  12,
					Top:    12,
					End:    12,
					Bottom: 12,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    0,
					End:    10,
					Bottom: 0,
				},
			},
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#313234"},
				},
				Shadow: &widget.Shadow{
					Height:  4,
					Blur:    0,
					Opacity: 100,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#141414"},
					},
				},
			},
			EventParameter: "MilestoneBenefitsBottomSheetButton",
		},
	}

	fees = []*ffPb.FeeCardInfo{
		{
			Title: &commontypes.Text{
				FontColor:    "#F6F9FD",
				DisplayValue: &commontypes.Text_Html{Html: "Joining fee"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
			},
			SubTitle: &ffPb.WrappedTextInfo{
				Text: &commontypes.Text{
					FontColor:    "#6A6D70",
					DisplayValue: &commontypes.Text_Html{Html: "₹2000"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_L},
				},
				Properties: &ffPb.LayoutProperties{
					Size: &ffPb.Size{
						Width:  -2,
						Height: -2,
					},
					Paddings: &ffPb.PaddingProperty{
						Start:  0,
						Top:    0,
						End:    0,
						Bottom: 0,
					},
					Margins: &ffPb.MarginProperty{
						Start:  0,
						Top:    0,
						End:    0,
						Bottom: 0,
					},
				},
				DrawableProperties: &ffPb.DrawableProperties{
					BorderProperty: &ffPb.BorderProperty{
						BorderThickness: 0,
					},
					CornerProperty: &ffPb.CornerProperty{
						TopStartCornerRadius: 0,
						TopEndCornerRadius:   0,
						BottomStartCorner:    0,
						BottomEndCorner:      0,
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
					Shadow: &widget.Shadow{
						Height:  0,
						Blur:    0,
						Opacity: 0,
						Colour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
					},
				},
			},
			Desc: &commontypes.Text{
				FontColor:    "#F6F9FD",
				DisplayValue: &commontypes.Text_Html{Html: "Get welcome gift cards worth ₹5000"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -2,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 8,
				},
				Margins: &ffPb.MarginProperty{
					Start:  0,
					Top:    8,
					End:    0,
					Bottom: 0,
				},
			},
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 0,
					TopEndCornerRadius:   0,
					BottomStartCorner:    0,
					BottomEndCorner:      0,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#313234"},
				},
				Shadow: &widget.Shadow{
					Height:  1,
					Blur:    0,
					Opacity: 100,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#141414"},
					},
				},
			},
		},
		{
			Title: &commontypes.Text{
				FontColor:    "#F6F9FD",
				DisplayValue: &commontypes.Text_Html{Html: "Renewal fee"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
			},
			SubTitle: &ffPb.WrappedTextInfo{
				Text: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_Html{Html: "₹2000 ₹0"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_L},
				},
				Properties: &ffPb.LayoutProperties{
					Size: &ffPb.Size{
						Width:  -2,
						Height: -2,
					},
					Paddings: &ffPb.PaddingProperty{
						Start:  0,
						Top:    0,
						End:    0,
						Bottom: 0,
					},
					Margins: &ffPb.MarginProperty{
						Start:  0,
						Top:    0,
						End:    0,
						Bottom: 0,
					},
				},
				DrawableProperties: &ffPb.DrawableProperties{
					BorderProperty: &ffPb.BorderProperty{
						BorderThickness: 0,
					},
					CornerProperty: &ffPb.CornerProperty{
						TopStartCornerRadius: 0,
						TopEndCornerRadius:   0,
						BottomStartCorner:    0,
						BottomEndCorner:      0,
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
					Shadow: &widget.Shadow{
						Height:  0,
						Blur:    0,
						Opacity: 0,
						Colour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
					},
				},
				TextSpannableProperties: map[string]*ffPb.SpannableProperties{
					"₹2000": {
						HasStrikeThrough: true,
						TextColor:        "#6A6D70",
					},
				},
			},
			Desc: &commontypes.Text{
				FontColor:    "#F6F9FD",
				DisplayValue: &commontypes.Text_Html{Html: "Waived off on spends of over ₹2.5 lakh a year"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -2,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  0,
					Top:    8,
					End:    0,
					Bottom: 8,
				},
			},
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 0,
					TopEndCornerRadius:   0,
					BottomStartCorner:    0,
					BottomEndCorner:      0,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
		},
	}

	widgetsT0 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/HL_Two_Fi_cardsCross_WithOutCoin.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 268,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "Meet the most<br> rewarding Credit Card"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 8,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#B9B9B9",
						DisplayValue: &commontypes.Text_Html{Html: "Accelerated rewards on<br>India’s top brands"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 28,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "Credit limit<br><b>Up to ₹10 lakh</b>"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -2,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  43,
							Top:    11,
							End:    43,
							Bottom: 11,
						},
						Margins: &ffPb.MarginProperty{
							Start:  81,
							Top:    0,
							End:    81,
							Bottom: 76,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 12,
							TopEndCornerRadius:   12,
							BottomStartCorner:    12,
							BottomEndCorner:      12,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#313234"},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
	}

	widgetsT1 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "5x rewards on your<br>top 3 brands"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    48,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 12,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/HL_5X_Rewards_image.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 365,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
	}

	widgetsT2 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/HL_On_over_20Plus_Brands.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 268,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "You spend, we reward.<br>Every single time."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 8,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedButtonInfo{
				WrappedButtonInfo: &ffPb.WrappedButtonInfo{
					Text: &commontypes.Text{
						FontColor:    "#00B899",
						DisplayValue: &commontypes.Text_Html{Html: "Check out the 20+ brands"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  16,
							Top:    16,
							End:    16,
							Bottom: 16,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 16,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CC_INTRO_BOTTOM_SHEET,
					},
					EventParameter: "TopBrandsBottomSheetButton",
				},
			},
		},
	}

	widgetsT3 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/HL_Fi_Coins_Use_Template_image.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 438,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    36,
							End:    0,
							Bottom: 37,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
	}

	widgetsT4 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/HL_Welcome_voucher_4x.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 268,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "Welcome gift cards<br>worth over ₹5,000!"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 8,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#B9B9B9",
						DisplayValue: &commontypes.Text_Html{Html: "Pay joining fee: ₹2000<br>You earn: ₹3000!"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 8,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedButtonInfo{
				WrappedButtonInfo: &ffPb.WrappedButtonInfo{
					Text: &commontypes.Text{
						FontColor:    "#00B899",
						DisplayValue: &commontypes.Text_Html{Html: "Know more"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  16,
							Top:    16,
							End:    16,
							Bottom: 16,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 16,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CC_INTRO_BOTTOM_SHEET,
					},
					EventParameter: "WelcomeVoucherBottomSheetButton",
				},
			},
		},
	}

	widgetsT5 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "That’s not all"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    42,
							End:    16,
							Bottom: 16,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_Benefits{
				Benefits: &ffPb.MultiBenefitCard{
					BenefitCardInfo: benefitCardInfo,
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  10,
							Top:    0,
							End:    10,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 36,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "Fees & Charges"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 16,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_Fees{
				Fees: &ffPb.MultiFeeCardInfo{
					Fees: fees,
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  20,
							Top:    16,
							End:    20,
							Bottom: 16,
						},
						Margins: &ffPb.MarginProperty{
							Start:  20,
							Top:    0,
							End:    20,
							Bottom: 16,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 20,
							TopEndCornerRadius:   20,
							BottomStartCorner:    20,
							BottomEndCorner:      20,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#313234"},
						},
						Shadow: &widget.Shadow{
							Height:  4,
							Blur:    0,
							Opacity: 100,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#141414"},
							},
						},
					},
				},
			},
		},
	}

	templates = []*ffPb.IntroScreenTemplate{
		{
			Widgets: widgetsT0,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#28292B"},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    8,
					End:    0,
					Bottom: 8,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    28,
					End:    10,
					Bottom: 16,
				},
			},
		},
		{
			Widgets: widgetsT1,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#28292B"},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    8,
					End:    0,
					Bottom: 8,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    28,
					End:    10,
					Bottom: 16,
				},
			},
		},
		{
			Widgets: widgetsT2,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#28292B"},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    8,
					End:    0,
					Bottom: 8,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    28,
					End:    10,
					Bottom: 16,
				},
			},
		},
		{
			Widgets: widgetsT3,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#28292B"},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    8,
					End:    0,
					Bottom: 8,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    28,
					End:    10,
					Bottom: 16,
				},
			},
		},
		{
			Widgets: widgetsT4,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#28292B"},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    8,
					End:    0,
					Bottom: 8,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    28,
					End:    10,
					Bottom: 16,
				},
			},
		},
		{
			Widgets: widgetsT5,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#28292B"},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    8,
					End:    0,
					Bottom: 8,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    28,
					End:    10,
					Bottom: 16,
				},
			},
		},
	}

	widgetsV1T0 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v2_meet_amplify_all_brand.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  328,
									Height: 427,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  6,
							Top:    26,
							End:    6,
							Bottom: 26,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 4,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
	}

	widgetsV1T1 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v2_top_brands.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 424,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedButtonInfo{
				WrappedButtonInfo: &ffPb.WrappedButtonInfo{
					Text: &commontypes.Text{
						FontColor:    "#00B899",
						DisplayValue: &commontypes.Text_Html{Html: "View brands"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CC_INTRO_BOTTOM_SHEET,
					},
					EventParameter: "TopBrandsBottomSheetButton",
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#929599",
						DisplayValue: &commontypes.Text_Html{Html: "Maximum rewards per transaction: 25,000 Fi-Coins"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    30,
							End:    0,
							Bottom: 12,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
	}

	widgetsV1T2 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v1_forex.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 200,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "0 Forex charges"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    20,
							End:    16,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#929599",
						DisplayValue: &commontypes.Text_Html{Html: "on all international spends"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    8,
							End:    16,
							Bottom: 24,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v2_lounge_access.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 116,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#929599",
						DisplayValue: &commontypes.Text_Html{Html: "*One lounge access is given every quarter if previous quarter spends exceed ₹10,000."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    46,
							End:    0,
							Bottom: 20,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    0,
							End:    16,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
	}

	widgetsV1T3 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v1_house.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 220,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "Get Fi-Coins on every spend, every time"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  45,
							Top:    0,
							End:    45,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    8,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#929599",
						DisplayValue: &commontypes.Text_Html{Html: "even on rent & fuel"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    8,
							End:    16,
							Bottom: 46,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v1_strikeLine.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  180,
									Height: 4,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#929599",
						DisplayValue: &commontypes.Text_Html{Html: "No cap on how much you can earn!"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    46,
							End:    16,
							Bottom: 40,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
	}

	widgetsV1T4 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "Convert your Fi-Coins to"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    30,
							End:    16,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v1_fiCoins_usage.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 362,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    8,
							End:    0,
							Bottom: 24,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
	}

	widgetsV1T5 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "Year-round offers on your favourite brands"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  45,
							Top:    30,
							End:    45,
							Bottom: 16,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v1_fav_brands.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 300,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 4,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
	}

	widgetsV1T6 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "Plus, milestone rewards!"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    30,
							End:    16,
							Bottom: 24,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v1_milestoneRewards.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 324,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 28,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedButtonInfo{
				WrappedButtonInfo: &ffPb.WrappedButtonInfo{
					Text: &commontypes.Text{
						FontColor:    "#00B899",
						DisplayValue: &commontypes.Text_Html{Html: "Know more"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  16,
							Top:    10,
							End:    16,
							Bottom: 10,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    4,
							End:    16,
							Bottom: 16,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CC_INTRO_BOTTOM_SHEET,
					},
					EventParameter: "AnnualMilestoneBottomSheetButton",
				},
			},
		},
	}

	widgetsV1T7 = []*ffPb.CardIntroWidget{
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedVisualElement{
				WrappedVisualElement: &ffPb.WrappedVisualElement{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/HL_Welcome_voucher_4x.png",
								},
								ImageType: commontypes.ImageType_PNG,
								Properties: &commontypes.VisualElementProperties{
									Width:  340,
									Height: 268,
								},
							},
						},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "Welcome gift cards worth over ₹4,000"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  0,
							Top:    0,
							End:    0,
							Bottom: 0,
						},
						Margins: &ffPb.MarginProperty{
							Start:  45,
							Top:    0,
							End:    45,
							Bottom: 20,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedTextInfo{
				WrappedTextInfo: &ffPb.WrappedTextInfo{
					Text: &commontypes.Text{
						FontColor:    "#F6F9FD",
						DisplayValue: &commontypes.Text_Html{Html: "<b>Joining fee: ₹2,000 + GST</b><br>You earn ₹2,000!"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -2,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  30,
							Top:    14,
							End:    30,
							Bottom: 14,
						},
						Margins: &ffPb.MarginProperty{
							Start:  50,
							Top:    0,
							End:    50,
							Bottom: 0,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 12,
							TopEndCornerRadius:   12,
							BottomStartCorner:    12,
							BottomEndCorner:      12,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: "#18191B",
							},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
				},
			},
		},
		{
			IntroWidget: &ffPb.CardIntroWidget_WrappedButtonInfo{
				WrappedButtonInfo: &ffPb.WrappedButtonInfo{
					Text: &commontypes.Text{
						FontColor:    "#00B899",
						DisplayValue: &commontypes.Text_Html{Html: "Know more"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					},
					Properties: &ffPb.LayoutProperties{
						Size: &ffPb.Size{
							Width:  -1,
							Height: -2,
						},
						Paddings: &ffPb.PaddingProperty{
							Start:  16,
							Top:    16,
							End:    16,
							Bottom: 16,
						},
						Margins: &ffPb.MarginProperty{
							Start:  16,
							Top:    8,
							End:    16,
							Bottom: 10,
						},
					},
					DrawableProperties: &ffPb.DrawableProperties{
						BorderProperty: &ffPb.BorderProperty{
							BorderThickness: 0,
						},
						CornerProperty: &ffPb.CornerProperty{
							TopStartCornerRadius: 0,
							TopEndCornerRadius:   0,
							BottomStartCorner:    0,
							BottomEndCorner:      0,
						},
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{},
						},
						Shadow: &widget.Shadow{
							Height:  0,
							Blur:    0,
							Opacity: 0,
							Colour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{},
							},
						},
					},
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CC_INTRO_BOTTOM_SHEET,
					},
					EventParameter: "WelcomeVoucherBottomSheetButton",
				},
			},
		},
	}

	templatesV1 = []*ffPb.IntroScreenTemplate{
		{
			Widgets: widgetsV1T0,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
						Degree: 45,
						LinearColorStops: []*widget.ColorStop{
							{
								Color:          "#3B2E56",
								StopPercentage: 0,
							},
							{
								Color:          "#1B1F22",
								StopPercentage: 50,
							},
							{
								Color:          "#234646",
								StopPercentage: 100,
							},
						},
					}},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    4,
					End:    10,
					Bottom: 4,
				},
			},
		},
		{
			Widgets: widgetsV1T1,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
						Degree: 45,
						LinearColorStops: []*widget.ColorStop{
							{
								Color:          "#294C4B",
								StopPercentage: 0,
							},
							{
								Color:          "#1C2427",
								StopPercentage: 50,
							},
							{
								Color:          "#234144",
								StopPercentage: 100,
							},
						},
					}},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    4,
					End:    10,
					Bottom: 4,
				},
			},
		},
		{
			Widgets: widgetsV1T2,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#28292B",
					},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    4,
					End:    10,
					Bottom: 4,
				},
			},
		},
		{
			Widgets: widgetsV1T4,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
						Degree: 45,
						LinearColorStops: []*widget.ColorStop{
							{
								Color:          "#294C4B",
								StopPercentage: 0,
							},
							{
								Color:          "#1C2427",
								StopPercentage: 50,
							},
							{
								Color:          "#234144",
								StopPercentage: 100,
							},
						},
					}},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    4,
					End:    10,
					Bottom: 4,
				},
			},
		},
		{
			Widgets: widgetsV1T5,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
						Degree: 45,
						LinearColorStops: []*widget.ColorStop{
							{
								Color:          "#403D57",
								StopPercentage: 0,
							},
							{
								Color:          "#1F2428",
								StopPercentage: 50,
							},
							{
								Color:          "#264247",
								StopPercentage: 100,
							},
						},
					}},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    4,
					End:    10,
					Bottom: 4,
				},
			},
		},
		{
			Widgets: widgetsV1T6,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
						Degree: 45,
						LinearColorStops: []*widget.ColorStop{
							{
								Color:          "#294C4B",
								StopPercentage: 0,
							},
							{
								Color:          "#1C2427",
								StopPercentage: 50,
							},
							{
								Color:          "#234144",
								StopPercentage: 100,
							},
						},
					}},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    4,
					End:    10,
					Bottom: 4,
				},
			},
		},
		{
			Widgets: widgetsV1T7,
			DrawableProperties: &ffPb.DrawableProperties{
				BorderProperty: &ffPb.BorderProperty{
					BorderThickness: 0,
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
						Degree: 45,
						LinearColorStops: []*widget.ColorStop{
							{
								Color:          "#403D57",
								StopPercentage: 0,
							},
							{
								Color:          "#1F2428",
								StopPercentage: 50,
							},
							{
								Color:          "#264247",
								StopPercentage: 100,
							},
						},
					}},
				},
				Shadow: &widget.Shadow{
					Height:  0,
					Blur:    0,
					Opacity: 0,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{},
					},
				},
			},
			Properties: &ffPb.LayoutProperties{
				Size: &ffPb.Size{
					Width:  -1,
					Height: -1,
				},
				Paddings: &ffPb.PaddingProperty{
					Start:  0,
					Top:    0,
					End:    0,
					Bottom: 0,
				},
				Margins: &ffPb.MarginProperty{
					Start:  10,
					Top:    4,
					End:    10,
					Bottom: 4,
				},
			},
		},
	}

	getCcIntroScreenAmplifiV0Response = &ffPb.GetCCIntroScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		PageIndicator:        pageIndicator,
		BackgroundColour:     &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#18191B"}},
		AutoScrollProperties: autoScrollProperties,
		ConsentText:          consentText,
		PartnerIcon:          partnerIcon,
		WrappedBtnInfo:       wrappedButtonInfo,
		Templates:            templates,
	}
	getCcIntroScreenAmplifiV1Response = &ffPb.GetCCIntroScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		PageIndicator:        pageIndicator,
		BackgroundColour:     &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#18191B"}},
		AutoScrollProperties: autoScrollProperties,
		ConsentText:          consentText,
		PartnerIcon:          partnerIcon,
		WrappedBtnInfo:       wrappedButtonInfo,
		Templates:            templatesV1,
	}
)

func TestService_GetCCIntroScreen(t *testing.T) {
	ctr := gomock.NewController(t)
	mockFireflyClient := mockffPb.NewMockFireflyClient(ctr)
	mockOnbClient := mocks.NewMockOnboardingClient(ctr)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
	rewardsProvider := rewards.NewRewardProvider(rewards.NewUnsecuredRewards(genConf, nil, nil, nil), nil, nil)
	mockCardRecommendationSvcClient := mocksffRePb.NewMockCardRecommendationServiceClient(ctr)
	feesAndBenefitsProvider := fees_and_benefits.NewFeesAndBenefitsProvider(
		fees_and_benefits.NewUnsecuredFeesAndBenefitsProvider(genConf, nil,
			nil, nil, nil, nil, nil, nil),
		nil, nil)
	fireflyFrontendService := NewService(mockFireflyClient, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, genConf, nil, nil,
		nil, mockOnbClient, nil, nil, rewardsProvider,
		nil, nil, feesAndBenefitsProvider, mockCardRecommendationSvcClient,
		nil, nil, mockReleaseEvaluator, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	type args struct {
		ctx context.Context
		req *ffPb.GetCCIntroScreenRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func()
		want           *ffPb.GetCCIntroScreenResponse
		wantErr        bool
	}{
		{
			name: "success, got response from rpc",
			args: args{
				ctx: context.Background(),
				req: &ffPb.GetCCIntroScreenRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
					ScreenIdentifier: 0,
				},
			},
			setupMockCalls: func() {
				mockFireflyClient.EXPECT().FetchCreditCardEligibility(context.Background(), &ffBePb.FetchCreditCardEligibilityRequest{
					ActorId: actorId,
					Vendor:  ffEnumsBePb.Vendor_FEDERAL,
				}).Return(&ffBePb.FetchCreditCardEligibilityResponse{
					Status:           rpc.StatusOk(),
					IsUserCcEligible: true,
					AvailableLimit: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100000,
					},
					CardProgram: &types.CardProgram{
						CardProgramType: types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
					},
				}, nil)
				mockReleaseEvaluator.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(types.Feature_FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK).
					WithActorId(actorId)).Return(false, nil).AnyTimes()
			},
			want: &ffPb.GetCCIntroScreenResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
			},
		},
		{
			name: "failure, error in fetchCreditCardEligibility",
			args: args{
				ctx: context.Background(),
				req: &ffPb.GetCCIntroScreenRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
					ScreenIdentifier: 0,
				},
			},
			setupMockCalls: func() {
				mockFireflyClient.EXPECT().FetchCreditCardEligibility(context.Background(), &ffBePb.FetchCreditCardEligibilityRequest{
					ActorId: actorId,
					Vendor:  ffEnumsBePb.Vendor_FEDERAL,
				}).Return(nil, errors.New("error"))
				mockReleaseEvaluator.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(types.Feature_FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK).
					WithActorId(actorId)).Return(false, nil).AnyTimes()
			},
			want: &ffPb.GetCCIntroScreenResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
			},
			wantErr: false,
		},
		{
			name: "success, got response from rpc for fi lite user",
			args: args{
				ctx: context.Background(),
				req: &ffPb.GetCCIntroScreenRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
					ScreenIdentifier: int32(deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API),
				},
			},
			setupMockCalls: func() {
				mockReleaseEvaluator.EXPECT().Evaluate(context.Background(), release.NewCommonConstraintData(types.Feature_FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK).
					WithActorId(actorId)).Return(false, nil).AnyTimes()
			},
			want: &ffPb.GetCCIntroScreenResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls()
			if tt.want != nil {
				switch {
				case !tt.want.GetRespHeader().GetStatus().IsSuccess():
					// do nothing
				case fireflyFrontendService.dynamicConf.CreditCard().EnableNewCvpForUnsecuredCreditCard() &&
					tt.args.req.GetScreenIdentifier() == int32(deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API):
					tt.want = getCcIntroScreenAmplifiV1Response
					tt.want.ToolBar = textIconToolBar
					tt.want.WrappedBtnInfo.Text.DisplayValue = &commontypes.Text_Html{Html: "Check your eligibility"}
					tt.want.ConsentText = nil
					tt.want.WrappedBtnInfo.Properties.Margins.Bottom = 40
				case !fireflyFrontendService.dynamicConf.CreditCard().EnableNewCvpForUnsecuredCreditCard() &&
					tt.args.req.GetScreenIdentifier() == int32(deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API):
					tt.want = getCcIntroScreenAmplifiV0Response
					tt.want.ToolBar = textIconToolBar
				case fireflyFrontendService.dynamicConf.CreditCard().EnableNewCvpForUnsecuredCreditCard():
					tt.want = getCcIntroScreenAmplifiV1Response
				default:
					tt.want = getCcIntroScreenAmplifiV0Response
				}
			}
			got, err := fireflyFrontendService.GetCCIntroScreen(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCCIntroScreen() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&deeplink.CreditCardIntroScreenBrandScreenOptions{}, "all_brand_list"),
				protocmp.IgnoreFields(&deeplink.Deeplink{}, "screen_options_v2"),
				protocmp.IgnoreFields(&ffPb.WrappedButtonInfo{}, "deeplink"),
				protocmp.IgnoreFields(&header.ResponseHeader{}, "feedback_engine_info"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Mismatch in GetCcIntroResponse response got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}
