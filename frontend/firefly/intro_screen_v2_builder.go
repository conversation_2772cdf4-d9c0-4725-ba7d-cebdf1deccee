package firefly

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	commontypes "github.com/epifi/gamma/api/types"
	"github.com/epifi/gamma/api/types/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"

	moneyPkg "github.com/epifi/be-common/pkg/money"

	ccEnumsV2Pb "github.com/epifi/gamma/api/firefly/v2/enums"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/config"
	genconf2 "github.com/epifi/gamma/frontend/config/genconf"

	commontypesv2 "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	componentsv2 "github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
)

type CcIntroScreenBuilder interface {
	BuildScreen(ctx context.Context, data *introScreenV2Data) (section *sections.Section, floatingSection *sections.Section, backgroundImage *commontypesv2.VisualElement, err error)
}

type CcIntroScreenBuilderImpl struct{}

func NewCcIntroScreenBuilder() CcIntroScreenBuilder {
	return &CcIntroScreenBuilderImpl{}
}

func (c *CcIntroScreenBuilderImpl) BuildScreen(_ context.Context, data *introScreenV2Data) (*sections.Section, *sections.Section, *commontypesv2.VisualElement, error) {
	if data.cardState == ccEnumsV2Pb.CardState_CARD_STATE_CLOSED {
		return buildCreditCardClosedErrorScreen(data)
	}

	switch data.cardOnbReqStatus {
	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS:
		return nil, nil, nil, errors.New(fmt.Sprintf("invalid card onboarding request status to load intro screen v2: %s", data.cardOnbReqStatus.String()))
	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS:
		return buildCreditCardOnboardScreen(data)
	case ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED:
		return buildCreditCardRejectedErrorScreen(data)
	default:
		if !data.isCcEligible {
			return buildCreditCardIneligibleErrorScreen(data)
		}
		return buildCreditCardOnboardScreen(data)
	}
}

// createColorStopsFromLinearGradient creates color stops from a config.LinearGradient
func createColorStopsFromLinearGradient(gradient *config.LinearGradient) []*widget.ColorStop {
	colorStops := make([]*widget.ColorStop, 0, len(gradient.LinearColorStops))
	for _, stop := range gradient.LinearColorStops {
		colorStops = append(colorStops, &widget.ColorStop{
			Color:          stop.Color,
			StopPercentage: stop.StopPercentage,
		})
	}
	return colorStops
}

// createFullWidthImageComponent creates a component with a full-width image
func createFullWidthImageComponent(imageUrl string) *componentsv2.Component {
	return &componentsv2.Component{
		Content: getAnyWithoutError(&commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: imageUrl,
					},
					Properties: &commontypes.VisualElementProperties{
						Width: -1, // Full width
					},
				},
			},
		}),
	}
}

func buildCreditCardOnboardScreen(data *introScreenV2Data) (*sections.Section, *sections.Section, *commontypesv2.VisualElement, error) {
	introScreenOptionConfig := data.screenOptions.IntroScreenContent()

	// Create visual elements using the helper function
	visualElementTitle := createFullWidthImageComponent(introScreenOptionConfig.CardProgramTitleSubtitleImageUrl())
	visualElementCard := createFullWidthImageComponent(introScreenOptionConfig.CardImageUrl())
	visualElementSubText := createFullWidthImageComponent(introScreenOptionConfig.SubTextContent())

	// Determine which banner image to use based on card request status
	var visualElementWeekendBenefits *componentsv2.Component
	var bannerImageUrl string
	if data.cardOnbReqStatus == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS {
		bannerImageUrl = introScreenOptionConfig.JoiningFeeImageUrl()
		// Create image component
		imageComponent := createFullWidthImageComponent(bannerImageUrl)
		// Wrap in VerticalListSection with interaction behaviors
		visualElementWeekendBenefits = &componentsv2.Component{
			Content: getAnyWithoutError(&sections.VerticalListSection{
				Components: []*componentsv2.Component{imageComponent},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
								},
							},
						},
					},
				},
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
							OnClickBehavior: &behaviors.OnClickBehavior{
								Action: getAnyWithoutError(data.primaryCtaDeeplink),
							},
						},
						AnalyticsEvent: &analytics.AnalyticsEvent{
							EventName: "CCV2_Clicked",
							Properties: data.GetAnalyticsProperties(AnalyticsComponentContext{
								entryPoint: "CardsTab", // TODO change this in all places before new entry points are introduced
								component:  "CompleteApplication",
							}),
						},
					},
				},
			}),
		}
	} else {
		bannerImageUrl = introScreenOptionConfig.DualBenefitsImageUrl()
		visualElementWeekendBenefits = createFullWidthImageComponent(bannerImageUrl)
	}

	// Create component list starting with the standard components
	componentsList := []*componentsv2.Component{
		visualElementTitle,
		{Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_S})},
		visualElementCard,
	}

	// Add text components if pre-approved offer value is not zero
	if !moneyPkg.IsZero(data.preApprovedOfferValue) {
		subTextHeader := introScreenOptionConfig.SubTextHeader()
		subTextHeaderColor := introScreenOptionConfig.SubTextHeaderColor()
		subTextContent := moneyPkg.ToDisplayStringWithPrecision(data.preApprovedOfferValue, 0)

		// Add spacer and text components in a single append
		componentsList = append(componentsList,
			&componentsv2.Component{
				Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_M}),
			},
			&componentsv2.Component{
				Content: getAnyWithoutError(&commontypes.Text{
					FontColor: subTextHeaderColor,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: subTextHeader,
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
					},
				}),
			},
			&componentsv2.Component{
				Content: getAnyWithoutError(&commontypes.Text{
					FontColor: "#FFDB9E",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: subTextContent,
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_XL,
					},
				}),
			},
			&componentsv2.Component{
				Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_M}),
			},
		)
	}

	// Add components, spacers, and expandable section spacer in a single append
	componentsList = append(componentsList,
		visualElementWeekendBenefits,
		&componentsv2.Component{
			Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_M}),
		},
	)

	// Create a new list for components that will be wrapped in a dark background
	darkBgComponents := make([]*componentsv2.Component, 0)

	// Add visualElementSubText at the beginning of the dark background components
	darkBgComponents = append(darkBgComponents,
		visualElementSubText,
		&componentsv2.Component{
			Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_M}),
		},
	)

	// Add expandable sections for each item in ExpandableComponents
	for i, expandableComponent := range introScreenOptionConfig.ExpandableComponents() {
		// Create the header text
		headerText := &commontypesv2.Text{
			FontColor: expandableComponent.HeadingTextColor,
			DisplayValue: &commontypesv2.Text_PlainString{
				PlainString: expandableComponent.HeadingText,
			},
			FontStyle: &commontypesv2.Text_StandardFontStyle{
				StandardFontStyle: commontypesv2.FontStyle_HEADLINE_M,
			},
		}

		// Create the header
		header := &sections.ExpandableSection_Header{
			Text:               headerText,
			RightIconExpanded:  commontypesv2.GetVisualElementFromUrlHeightAndWidth(introScreenOptionConfig.ChevronUp(), 28, 28),
			RightIconCollapsed: commontypesv2.GetVisualElementFromUrlHeightAndWidth(introScreenOptionConfig.ChevronDown(), 28, 28),
		}

		// Create content component with the image
		contentComponent := &componentsv2.Component{
			Content: getAnyWithoutError(&commontypesv2.VisualElement{
				Asset: &commontypesv2.VisualElement_Image_{
					Image: &commontypesv2.VisualElement_Image{
						Source: &commontypesv2.VisualElement_Image_Url{
							Url: expandableComponent.ContentImageUrl,
						},
						Properties: &commontypesv2.VisualElementProperties{
							Width: -1, // Full width
						},
					},
				},
			}),
		}

		// TODO click on chevron event handling solutioning
		// Create expandable section - directly use ExpandableSection instead of wrapping in Section
		expandableSection := &componentsv2.Component{
			Content: getAnyWithoutError(&sections.ExpandableSection{
				Header:            header,
				ExpandableContent: contentComponent,
				IsExpanded:        expandableComponent.IsExpanded,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Border: &properties.BorderProperty{
									BorderBgColor: widget.GetLinearGradientBackgroundColour(
										expandableComponent.ContainerBorderColor.Degree, // Use 90 degree angle for the gradient
										createColorStopsFromLinearGradient(expandableComponent.ContainerBorderColor),
									),
									BorderThickness: 1,
									CornerRadius:    16,
								},
								Margin: &properties.PaddingProperty{
									Top:    8,
									Bottom: 8,
									Left:   16,
									Right:  16,
								},
								Padding: &properties.PaddingProperty{
									Left:  12,
									Right: 12,
								},
							},
						},
					},
				},
			}),
		}

		darkBgComponents = append(darkBgComponents, expandableSection)

		// Add a spacer after each expandable section except the last one
		if i < len(introScreenOptionConfig.ExpandableComponents())-1 {
			darkBgComponents = append(darkBgComponents, &componentsv2.Component{
				Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_S}),
			})
		}
	}

	// Add spacer after expandable sections
	darkBgComponents = append(darkBgComponents, &componentsv2.Component{
		Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_L}),
	})

	// Create title text component
	titleTextComponent := &componentsv2.Component{
		Content: getAnyWithoutError(&commontypes.Text{
			FontColor: "#C0723D",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: introScreenOptionConfig.HorizontalScrollableComponentTitle(),
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
			},
			Alignment: commontypes.Text_ALIGNMENT_LEFT,
		}),
	}

	// Wrap title in a vertical list section to control alignment
	titleSection := &sections.VerticalListSection{
		Components: []*componentsv2.Component{
			titleTextComponent,
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Padding: &properties.PaddingProperty{
							Left: 16,
						},
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
						},
					},
				},
			},
		},
	}

	// Add title, spacers, and padding to darkBgComponents
	darkBgComponents = append(darkBgComponents,
		&componentsv2.Component{
			Content: getAnyWithoutError(titleSection),
		},
		&componentsv2.Component{
			Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_S}),
		},
		&componentsv2.Component{
			Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_S}),
		},
		&componentsv2.Component{
			Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_S}),
		},
	)

	// Spacer with left padding was moved to the append above

	// Add horizontal scrollable components
	horizontalComponents := make([]*componentsv2.Component, 0, len(introScreenOptionConfig.HorizontalScrollableComponents()))
	for i, horizontalComponent := range introScreenOptionConfig.HorizontalScrollableComponents() {
		// Create a card component for each horizontal item
		component := &componentsv2.Component{
			Content: getAnyWithoutError(&sections.VerticalListSection{
				Components: []*componentsv2.Component{
					// Heading text
					{
						Content: getAnyWithoutError(&commontypes.Text{
							FontColor: horizontalComponent.HeadingColor,
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: horizontalComponent.HeadingText,
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
							},
							Alignment: commontypes.Text_ALIGNMENT_LEFT,
						}),
					},
					{
						Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_M}),
					},
					// Body text
					{
						Content: getAnyWithoutError(
							commontypes.GetTextFromHtmlStringWithStyleAndAlignment(horizontalComponent.BodyText, horizontalComponent.BodyTextColor, commontypes.FontStyle_SUBTITLE_S, commontypes.Text_TextAlignment(commontypesv2.Text_ALIGNMENT_LEFT))),
					},
				},
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
										ExactValue: 290,
									},
									Height: &properties.Size_Dimension{
										Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
										ExactValue: 252,
									},
								},
								Border: &properties.BorderProperty{
									BorderColor:     horizontalComponent.ContainerBorderColor,
									BorderThickness: 1,
									CornerRadius:    16,
								},
								Padding: &properties.PaddingProperty{
									Left:   16,
									Right:  16,
									Top:    16,
									Bottom: 16,
								},
								Margin: &properties.PaddingProperty{
									Right: 12,
								},
							},
						},
					},
				},
				VisibleBehavior: &behaviors.LifecycleBehavior{
					Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "CCV2_Visible",
						Properties: data.GetAnalyticsProperties(AnalyticsComponentContext{
							entryPoint: "CardsTab",
							component:  "AdditionalDetails",
							index:      strconv.Itoa(i + 1),
						}),
					},
				},
			}),
		}
		horizontalComponents = append(horizontalComponents, component)
	}

	// Create horizontal list section
	horizontalListSection := &sections.HorizontalListSection{
		Components:   horizontalComponents,
		IsScrollable: true,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Padding: &properties.PaddingProperty{
							Left:  16,
							Right: 16,
						},
						Size: &properties.Size{
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
						},
					},
				},
			},
		},
	}

	// Add horizontal list section and spacer to darkBgComponents
	darkBgComponents = append(darkBgComponents,
		&componentsv2.Component{
			Content: getAnyWithoutError(horizontalListSection),
		},
		&componentsv2.Component{
			Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_M}),
		},
	)

	// Add footer image if URL is provided
	footerImageUrl := introScreenOptionConfig.FooterImageUrl()
	if footerImageUrl != "" {
		darkBgComponents = append(darkBgComponents,
			&componentsv2.Component{
				Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_L}),
			},
			&componentsv2.Component{
				Content: getAnyWithoutError(&commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: footerImageUrl,
							},
						},
					},
				}),
			},
			&componentsv2.Component{
				Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_XXXL}),
			},
		)
		// Add extra spacers only for footer section if present
		if data.cardOnbReqStatus == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED {
			darkBgComponents = append(darkBgComponents,
				&componentsv2.Component{
					Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_XXXL}),
				},
				&componentsv2.Component{
					Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_XXXL}),
				},
			)
		}
	}

	// Create a vertical list section with dark background
	darkBgSection := &sections.VerticalListSection{
		Components:   darkBgComponents,
		IsScrollable: false,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: "#0E0705",
							},
						},
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
						},
						Padding: &properties.PaddingProperty{
							Top:    20,
							Bottom: 20,
						},
					},
				},
			},
		},
	}

	// Add the dark background section to the main components list
	componentsList = append(componentsList, &componentsv2.Component{
		Content: getAnyWithoutError(darkBgSection),
	})

	// Create a vertical list section that will be used as the main section content
	verticalListSection := &sections.VerticalListSection{
		Components:   componentsList,
		IsScrollable: true,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
						},
						Padding: &properties.PaddingProperty{
							Top:    20,
							Bottom: 20,
						},
					},
				},
			},
		},
		LoadBehavior: &behaviors.LifecycleBehavior{
			Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
			AnalyticsEvent: &analytics.AnalyticsEvent{
				EventName: "CCV2_Landed",
				Properties: data.GetAnalyticsProperties(AnalyticsComponentContext{
					entryPoint: "CardsTab",
				}),
			},
		},
	}

	// Create a section with the vertical list section content
	mainSection := &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: verticalListSection,
		},
	}

	// Initialize floatingSection as nil
	var floatingSection *sections.Section

	// Only create floatingSection if card request status is UNSPECIFIED
	if data.cardOnbReqStatus == ccEnumsV2Pb.CardRequestStatus_CARD_REQUEST_STATUS_UNSPECIFIED {
		cta := introScreenOptionConfig.InitiateOnboardingCta()

		// Create CTA components
		var ctaComponents []*componentsv2.Component

		// Add TnC Text component and spacer in a single append
		ctaComponents = append(ctaComponents,
			&componentsv2.Component{
				Content: getAnyWithoutError(&commontypes.Text{
					FontColor: "#929599",
					DisplayValue: &commontypes.Text_Html{
						Html: cta.CtaTncText(),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_S,
					},
					Alignment: commontypes.Text_ALIGNMENT_LEFT,
				}),
			},
			&componentsv2.Component{
				Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_M}),
			},
		)

		// Create a button text component
		buttonText := &componentsv2.Component{
			Content: getAnyWithoutError(&commontypes.Text{
				FontColor: "#313234",
				DisplayValue: &commontypes.Text_Html{
					Html: cta.CtaText(),
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
				},
				Alignment: commontypes.Text_ALIGNMENT_CENTER,
			}),
		}

		// Wrap the button in a vertical list section to control its appearance
		buttonSection := &sections.VerticalListSection{
			Components: []*componentsv2.Component{buttonText},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#E8AD62",
								},
							},
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
							},
							Border: &properties.BorderProperty{
								BorderBgColor: widget.GetLinearGradientBackgroundColour(
									cta.CtaBorder().Degree(),
									createColorStopsFromLinearGradient(&config.LinearGradient{
										LinearColorStops: cta.CtaBorder().LinearColorStops(),
									}),
								),
								BorderThickness: 1,
								CornerRadius:    16,
							},
							Padding: &properties.PaddingProperty{
								Top:    16,
								Bottom: 16,
								Left:   16,
								Right:  16,
							},
							Corner: &properties.CornerProperty{
								TopLeftCornerRadius:  40,
								TopRightCornerRadius: 40,
								BottomLeftCorner:     40,
								BottomRightCorner:    40,
							},
						},
					},
				},
			},
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: getAnyWithoutError(data.primaryCtaDeeplink),
						},
					},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName: "CCV2_Clicked",
						Properties: data.GetAnalyticsProperties(AnalyticsComponentContext{
							entryPoint: "CardsTab",
							component:  "GetCardCTA",
						}),
					},
				},
			},
		}

		// Add the styled button to components
		ctaComponents = append(ctaComponents, &componentsv2.Component{
			Content: getAnyWithoutError(buttonSection),
		})

		floatingSection = &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					Components:          ctaComponents,
					IsScrollable:        false,
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									BgColor: widget.GetLinearGradientBackgroundColour(
										cta.ContainerBgColor().Degree(),
										createColorStopsFromLinearGradient(&config.LinearGradient{
											LinearColorStops: cta.ContainerBgColor().LinearColorStops(),
										}),
									),
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
										},
									},
									Padding: &properties.PaddingProperty{
										Top:    24,
										Left:   16,
										Right:  16,
										Bottom: 24,
									},
								},
							},
						},
					},
				},
			},
		}
	}

	visualElementCardRaysBg := commontypesv2.GetVisualElementImageFromUrl(introScreenOptionConfig.CardLightRays())

	return mainSection, floatingSection, visualElementCardRaysBg, nil
}

func buildCreditCardRejectedErrorScreen(data *introScreenV2Data) (*sections.Section, *sections.Section, *commontypesv2.VisualElement, error) {
	errorScreenOptionConfig := data.screenOptions.CardRejectedErrorOptions()
	analyticsProperties := data.GetAnalyticsProperties(AnalyticsComponentContext{entryPoint: "CardsTab"})
	return buildCcIntroV2ErrorScreen(errorScreenOptionConfig, analyticsProperties, data.primaryCtaDeeplink)
}

func buildCreditCardClosedErrorScreen(data *introScreenV2Data) (*sections.Section, *sections.Section, *commontypesv2.VisualElement, error) {
	errorScreenOptionConfig := data.screenOptions.CardClosedErrorOptions()
	analyticsProperties := data.GetAnalyticsProperties(AnalyticsComponentContext{entryPoint: "CardsTab"})
	return buildCcIntroV2ErrorScreen(errorScreenOptionConfig, analyticsProperties, data.primaryCtaDeeplink)
}

func buildCreditCardIneligibleErrorScreen(data *introScreenV2Data) (*sections.Section, *sections.Section, *commontypesv2.VisualElement, error) {
	errorScreenOptionConfig := data.screenOptions.IneligibilityScreenErrorOptions()
	analyticsProperties := data.GetAnalyticsProperties(AnalyticsComponentContext{entryPoint: "CardsTab"})
	return buildCcIntroV2ErrorScreen(errorScreenOptionConfig, analyticsProperties, data.primaryCtaDeeplink)
}

func buildCcIntroV2ErrorScreen(errorScreenOptionConfig *genconf2.CCIntroScreenErrorOptions, analyticsProperties map[string]string, redirectionDl *deeplinkPb.Deeplink) (*sections.Section, *sections.Section, *commontypesv2.VisualElement, error) {
	// Create errorComponent for the vertical list section
	errorComponent := make([]*componentsv2.Component, 0)
	errorComponent = append(errorComponent,
		&componentsv2.Component{Content: getAnyWithoutError(&components.Spacer{
			Weight: &components.Spacer_Weight{
				Value: 90,
			},
		})},
		// Icon component
		&componentsv2.Component{
			Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(errorScreenOptionConfig.IconUrl(), 180, 180)),
		},
		// Spacer after icon
		&componentsv2.Component{
			Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_M}),
		},
		// Title text
		&componentsv2.Component{
			Content: getAnyWithoutError(&commontypes.Text{
				FontColor: errorScreenOptionConfig.TitleColor(),
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: errorScreenOptionConfig.Title(),
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
				},
				Alignment: commontypes.Text_ALIGNMENT_CENTER,
			}),
		},
		// Spacer after title
		&componentsv2.Component{
			Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_L}),
		},
		// Body text with border
		&componentsv2.Component{
			Content: getAnyWithoutError(&sections.VerticalListSection{
				Components: []*componentsv2.Component{
					{
						Content: getAnyWithoutError(&commontypes.Text{
							FontColor: errorScreenOptionConfig.BodyColor(),
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: errorScreenOptionConfig.Body(),
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
							Alignment: commontypes.Text_ALIGNMENT_CENTER,
						}),
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Border: &properties.BorderProperty{
									BorderColor:     errorScreenOptionConfig.BodyColor(), // Using title color for border as fallback
									BorderThickness: 1,
									CornerRadius:    16,
								},
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
								},
								Padding: &properties.PaddingProperty{
									Top:    16,
									Bottom: 16,
									Left:   16,
									Right:  16,
								},
							},
						},
					},
				},
			}),
		},
		// Flexible spacer to push content to bottom
		&componentsv2.Component{Content: getAnyWithoutError(&components.Spacer{
			Weight: &components.Spacer_Weight{
				Value: 100,
			},
		})},
	)
	// Info text if applicable
	if errorScreenOptionConfig.Cta().InfoText() != "" {
		errorComponent = append(errorComponent, &componentsv2.Component{
			Content: getAnyWithoutError(&commontypes.Text{
				FontColor: errorScreenOptionConfig.Cta().InfoTextColor(),
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: errorScreenOptionConfig.Cta().InfoText(),
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_S,
				},
				Alignment: commontypes.Text_ALIGNMENT_CENTER,
			}),
		})
	}

	errorComponent = append(errorComponent,
		// Spacer before CTA
		&componentsv2.Component{
			Content: getAnyWithoutError(&componentsv2.Spacer{SpacingValue: componentsv2.Spacing_SPACING_M}),
		},
		// CTA button
		&componentsv2.Component{
			Content: getAnyWithoutError(&sections.VerticalListSection{
				Components: []*componentsv2.Component{
					{
						Content: getAnyWithoutError(&commontypes.Text{
							FontColor: errorScreenOptionConfig.Cta().CtaColor(),
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: errorScreenOptionConfig.Cta().CtaText(),
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
							},
							Alignment: commontypes.Text_ALIGNMENT_CENTER,
						}),
					},
				},
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
							OnClickBehavior: &behaviors.OnClickBehavior{
								Action: getAnyWithoutError(redirectionDl),
							},
						},
						//	TODO: add analytics event.
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Top:    16,
									Bottom: 16,
									Left:   16,
									Right:  16,
								},
								Border: &properties.BorderProperty{
									BorderBgColor: widget.GetLinearGradientBackgroundColour(
										errorScreenOptionConfig.Cta().CtaBorder().Degree(),
										createColorStopsFromLinearGradient(&config.LinearGradient{
											LinearColorStops: errorScreenOptionConfig.Cta().CtaBorder().LinearColorStops(),
										}),
									),
									BorderThickness: 1,
									CornerRadius:    40,
								},
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
								},
								Corner: &properties.CornerProperty{
									TopLeftCornerRadius:  40,
									TopRightCornerRadius: 40,
									BottomLeftCorner:     40,
									BottomRightCorner:    40,
								},
								BgColor: widget.GetLinearGradientBackgroundColour(
									errorScreenOptionConfig.Cta().CtaBackground().Degree(),
									createColorStopsFromLinearGradient(&config.LinearGradient{
										LinearColorStops: errorScreenOptionConfig.Cta().CtaBackground().LinearColorStops(),
									}),
								),
							},
						},
					},
				},
			}),
		})

	// Create the main vertical list section
	mainSection := &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				Components:          errorComponent,
				IsScrollable:        false,
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_SPACE_BETWEEN,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
								},
								Padding: &properties.PaddingProperty{
									Top:    24,
									Bottom: 50,
									Left:   16,
									Right:  16,
								},
							},
						},
					},
				},
				LoadBehavior: &behaviors.LifecycleBehavior{
					Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
					AnalyticsEvent: &analytics.AnalyticsEvent{
						EventName:  "CCV2_Landed",
						Properties: analyticsProperties,
					},
				},
			},
		},
	}

	// Return the section without a floating section or background image
	return mainSection, nil, nil, nil
}
