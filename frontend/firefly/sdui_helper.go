package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"

	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	beCasperPb "github.com/epifi/gamma/api/casper"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/firefly/helper"

	cardPkg "github.com/epifi/gamma/pkg/card"
)

type TotalRewardsEarningBanner struct {
	Title *ui.VerticalKeyValuePair
}

type RewardCard struct {
	BackgroundImage  *commontypes.VisualElement
	TopLeftTag       *ui.IconTextComponent
	CardIcon         *commontypes.VisualElement
	DescriptionLabel *commontypes.Text
	Deeplink         *deepLinkPb.Deeplink
}

type RewardHistoryItemInput struct {
	VisualElement      *commontypes.VisualElement
	VerticalKeyValue   *ui.VerticalKeyValuePair
	ActionIconTextComp *ui.IconTextComponent
}

type RewardsHistoryListInput struct {
	ShowActionButton bool
	Items            []*RewardHistoryItemInput
}

// nolint
var rewardsLandingPage = func() *deepLinkPb.Deeplink {
	return &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_OfferCatalogScreenOptions{
			OfferCatalogScreenOptions: &deepLinkPb.OfferCatalogScreenOptions{
				// optional filters
			},
		},
	}
}

func getGenericOfferCard(offer *beCasperPb.Offer, bgColor string, _ *anyPb.Any, leftMargin int) *sections.DepthWiseListSection {

	var brandImg, backgroundImg string
	for _, img := range offer.GetImages() {
		if img.GetImageType() == beCasperPb.ImageType_BRAND_IMAGE && img.GetUrl() != "" && brandImg == "" {
			brandImg = img.GetUrl()
		}
		if img.GetImageType() == beCasperPb.ImageType_BACKGROUND_IMAGE && img.GetUrl() != "" && backgroundImg == "" {
			backgroundImg = img.GetUrl()
		}
	}

	title := offer.GetName()
	if offer.GetAdditionalDetails().GetHomeTitle() != "" {
		title = offer.GetAdditionalDetails().GetHomeTitle()
	}

	deeplink := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFER_DETAILS_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOfferDetailsScreenOptions{
			CardOfferDetailsScreenOptions: &deepLinkPb.CardOfferDetailsScreenOptions{OfferId: offer.GetId()},
		},
	}

	return &sections.DepthWiseListSection{
		IsScrollable:     false,
		VisualProperties: getGenericOfferCardVisualProps(leftMargin),
		Alignment:        sections.DepthWiseListSection_TOP_CENTER,
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
					OnClickBehavior: &behaviors.OnClickBehavior{
						Action: helper.GetAnyWithoutError(deeplink),
					},
				},
			},
		},
		Components: []*components.Component{
			{
				// Actual content without bottom shadow
				Content: helper.GetAnyWithoutError(&sections.VerticalListSection{
					IsScrollable:        false,
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
					Components: []*components.Component{
						{
							Content: helper.GetAnyWithoutError(&sections.DepthWiseListSection{
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: &properties.ContainerProperty{
												Size: &properties.Size{
													Width: &properties.Size_Dimension{
														Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
													},
													Height: &properties.Size_Dimension{
														Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
														ExactValue: 76,
													},
												},
												BgColor: widget.GetBlockBackgroundColour("#F0F3F7"),
												Margin: &properties.PaddingProperty{
													Left:  3,
													Top:   3,
													Right: 3,
												},
												Corner: &properties.CornerProperty{
													TopLeftCornerRadius:  16,
													TopRightCornerRadius: 16,
													BottomLeftCorner:     16,
													BottomRightCorner:    16,
												},
											},
										},
									},
								},
								Components: []*components.Component{
									{
										Content: helper.GetAnyWithoutError(&sections.HorizontalListSection{
											HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
											VisualProperties: []*properties.VisualProperty{
												{
													Properties: &properties.VisualProperty_ContainerProperty{
														ContainerProperty: &properties.ContainerProperty{
															Size: &properties.Size{
																Width: &properties.Size_Dimension{
																	Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																},
																Height: &properties.Size_Dimension{
																	Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																	ExactValue: 76,
																},
															},
														},
													},
												},
											},
											Components: []*components.Component{
												{
													// Card top image
													Content: helper.GetAnyWithoutError(
														&commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
															Source:    &commontypes.VisualElement_Image_Url{Url: backgroundImg},
															ImageType: commontypes.ImageType_PNG,
															Properties: &commontypes.VisualElementProperties{
																Height: 76,
																Width:  94,
															},
														}}}),
												},
											},
										}),
									},
									{
										Content: helper.GetAnyWithoutError(&sections.VerticalListSection{
											VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
											VisualProperties: []*properties.VisualProperty{
												{
													Properties: &properties.VisualProperty_ContainerProperty{
														ContainerProperty: &properties.ContainerProperty{
															Size: &properties.Size{
																Width: &properties.Size_Dimension{
																	Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																},
																Height: &properties.Size_Dimension{
																	Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																},
															},
															Padding: &properties.PaddingProperty{
																Top:  8,
																Left: 16,
															},
														},
													},
												},
											},
											Components: []*components.Component{
												{
													Content: helper.GetAnyWithoutError(&sections.HorizontalListSection{
														Components: []*components.Component{cardPkg.GetVisualElementComponent(brandImg, 34, 34, commontypes.ImageType_PNG)},
														VisualProperties: []*properties.VisualProperty{
															{
																Properties: &properties.VisualProperty_ContainerProperty{
																	ContainerProperty: &properties.ContainerProperty{
																		Size: &properties.Size{
																			Width: &properties.Size_Dimension{
																				Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																			},
																		},
																	},
																},
															},
														},
														VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
														HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
														ListElementOverlapProps: &properties.ListElementOverlapProps{
															OverlapDevicePixels:       12,
															OverlapPaddingPixels:      2,
															OverlapCornerRadiusPixels: 50,
															PaddingBgColor:            widget.GetBlockBackgroundColour(bgColor),
														},
													}),
												},
											},
										}),
									},
								},
							}),
						},
						{
							Content: helper.GetAnyWithoutError(&components.Spacer{
								SpacingValue: components.Spacing_SPACING_S,
							}),
						},
						{
							// Offer title
							Content: helper.GetAnyWithoutError(&sections.HorizontalListSection{
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: &properties.ContainerProperty{
												Padding: &properties.PaddingProperty{
													Left:  12,
													Right: 12,
												},
											},
										},
									},
								},
								Components: []*components.Component{
									{
										Content: helper.GetAnyWithoutError(&commontypes.Text{
											FontColor: colors.ColorInk,
											DisplayValue: &commontypes.Text_PlainString{
												PlainString: title,
											},
											FontStyle: &commontypes.Text_StandardFontStyle{
												StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
											},
											Alignment: commontypes.Text_ALIGNMENT_LEFT,
											MaxLines:  2,
										}),
									},
								},
							}),
						},
						{
							Content: helper.GetAnyWithoutError(&components.Spacer{}),
						},
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
											ExactValue: offerWidgetCardWidth,
										},
										Height: &properties.Size_Dimension{
											Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
											ExactValue: offerWidgetCardHeight,
										},
									},
									BgColor: widget.GetBlockBackgroundColour(bgColor),
									Corner: &properties.CornerProperty{
										TopLeftCornerRadius:  20,
										TopRightCornerRadius: 20,
										BottomLeftCorner:     20,
										BottomRightCorner:    20,
									},
									Padding: &properties.PaddingProperty{
										Bottom: 16,
									},
									Margin: &properties.PaddingProperty{
										Bottom: 4,
									},
								},
							},
						},
					},
				}),
			},
		},
		LoadBehavior: &behaviors.LifecycleBehavior{
			Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
		},
		VisibleBehavior: &behaviors.LifecycleBehavior{
			Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
		},
	}
}

func getGenericOfferCardVisualProps(leftMargin int) []*properties.VisualProperty {
	return []*properties.VisualProperty{
		{
			Properties: &properties.VisualProperty_ContainerProperty{
				ContainerProperty: &properties.ContainerProperty{
					Size: &properties.Size{
						Width: &properties.Size_Dimension{
							Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
							ExactValue: offerWidgetCardWidth,
						},
						Height: &properties.Size_Dimension{
							Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
							ExactValue: offerWidgetCardHeight + offerWidgetCardShadowHeight,
						},
					},
					BgColor: widget.GetBlockBackgroundColour("#D9DEE3"),
					Corner: &properties.CornerProperty{
						TopLeftCornerRadius:  20,
						TopRightCornerRadius: 20,
						BottomLeftCorner:     20,
						BottomRightCorner:    20,
					},
					Margin: &properties.PaddingProperty{
						Left: int32(leftMargin),
					},
				},
			},
		},
	}
}

func getHorizontalSection(componentList []*components.Component, bgColor string) *sections.HorizontalListSection {
	return &sections.HorizontalListSection{
		Components:   componentList,
		IsScrollable: true,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_BgColor{
					BgColor: widget.GetBlockBackgroundColour(bgColor),
				},
			},
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
						},
					},
				},
			},
		},
	}
}

// Structure:
//
// DepthWiseListSection
//   - VerticalListSection
//   - DepthWiseListSection
//   - HorizontalListSection
//   - VisualElement (Background Image)
//   - VerticalListSection
//   - HorizontalListSection
//   - VisualElement (Brand Logo)
//   - VerticalListSection
//   - HorizontalListSection
//   - Tag
//   - HorizontalListSection
//   - Text (Title)
func getGenericFiCoinsExchangeOfferCard(offer *beCasperPb.Offer, bgColor string, tag *anyPb.Any, _ int) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var brandLogoUrl, bgImageUrl string
	for _, img := range offer.GetImages() {
		if img.GetImageType() == beCasperPb.ImageType_BRAND_IMAGE && img.GetUrl() != "" && brandLogoUrl == "" {
			brandLogoUrl = img.GetUrl()
		}
		if img.GetImageType() == beCasperPb.ImageType_BACKGROUND_IMAGE && img.GetUrl() != "" && bgImageUrl == "" {
			bgImageUrl = img.GetUrl()
		}
	}

	title := offer.GetName()
	if offer.GetAdditionalDetails().GetHomeTitle() != "" {
		title = offer.GetAdditionalDetails().GetHomeTitle()
	}

	deeplink := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFER_DETAILS_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOfferDetailsScreenOptions{
			CardOfferDetailsScreenOptions: &deepLinkPb.CardOfferDetailsScreenOptions{OfferId: offer.GetId()},
		},
	}

	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard_{
			GenericOfferCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard{
				Section: &sections.Section{
					Content: &sections.Section_DepthWiseListSection{
						DepthWiseListSection: &sections.DepthWiseListSection{
							IsScrollable:     false,
							VisualProperties: nil,
							Alignment:        sections.DepthWiseListSection_TOP_CENTER,
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: getAnyWithoutError(deeplink),
										},
									},
									AnalyticsEvent: nil,
								},
							},
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(&sections.VerticalListSection{
										IsScrollable:        false,
										HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
										VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: properties.GetContainerProperty().
														WithBlockBgColor(bgColor).
														WithAllCornerRadii(16, 16, 16, 16).
														WithPadding(4, 4, 4, 4).
														WithMargin(0, 0, 0, 4).
														WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardHeight).
														WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardWidth),
												},
											},
										},
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(&sections.DepthWiseListSection{
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithBlockBgColor(colors.ColorMonochromeChalk).
																	WithAllCornerRadii(14, 14, 14, 14).
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 80),
															},
														},
													},
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(&sections.HorizontalListSection{
																HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
																VisualProperties: []*properties.VisualProperty{
																	{
																		Properties: &properties.VisualProperty_ContainerProperty{
																			ContainerProperty: properties.GetContainerProperty().
																				WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																				WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 80),
																		},
																	}},
																Components: []*components.Component{
																	cardPkg.GetVisualElementComponent(bgImageUrl, 80, 98, commontypes.ImageType_PNG),
																},
															}),
														},
														{
															Content: getAnyWithoutError(&sections.VerticalListSection{
																VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
																VisualProperties: []*properties.VisualProperty{
																	{
																		Properties: &properties.VisualProperty_ContainerProperty{
																			ContainerProperty: properties.GetContainerProperty().
																				WithPadding(8, 8, 0, 0).
																				WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																				WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
																		},
																	},
																},
																Components: []*components.Component{
																	{
																		Content: getAnyWithoutError(&sections.HorizontalListSection{
																			Components: []*components.Component{cardPkg.GetVisualElementComponent(brandLogoUrl, 34, 34, commontypes.ImageType_PNG)},
																			VisualProperties: []*properties.VisualProperty{
																				{
																					Properties: &properties.VisualProperty_ContainerProperty{
																						ContainerProperty: properties.GetContainerProperty().
																							WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
																					},
																				},
																			},
																			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
																			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
																		}),
																	},
																},
															}),
														},
														{
															Content: getAnyWithoutError(&sections.VerticalListSection{
																Components: []*components.Component{
																	{
																		Content: getAnyWithoutError(&sections.HorizontalListSection{
																			Components: []*components.Component{
																				{
																					Content: tag,
																				},
																			},
																			VisualProperties: []*properties.VisualProperty{
																				{
																					Properties: &properties.VisualProperty_ContainerProperty{
																						ContainerProperty: properties.GetContainerProperty().
																							WithPadding(0, 4, 4, 0).
																							WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																							WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0),
																					},
																				},
																			},
																			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
																		}),
																	},
																},
																VisualProperties: []*properties.VisualProperty{
																	{
																		Properties: &properties.VisualProperty_ContainerProperty{
																			ContainerProperty: properties.GetContainerProperty().
																				WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																				WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
																		},
																	},
																},
																VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
															}),
														},
													},
												}),
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													IsScrollable: false,
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(&commontypes.Text{
																FontColor: "#282828",
																DisplayValue: &commontypes.Text_PlainString{
																	PlainString: title,
																},
																FontStyle: &commontypes.Text_StandardFontStyle{
																	StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
																},
																Alignment: commontypes.Text_ALIGNMENT_LEFT,
																MaxLines:  2,
															}),
														},
													},
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithPadding(8, 8, 8, 0).
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
															},
														},
													},
													VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
												}),
											},
										},
									},
									),
								},
							},
							LoadBehavior: &behaviors.LifecycleBehavior{
								Behavior:       &behaviors.LifecycleBehavior_LoadBehavior{},
								AnalyticsEvent: nil,
							},
							VisibleBehavior: &behaviors.LifecycleBehavior{
								Behavior:       &behaviors.LifecycleBehavior_ViewedBehavior{},
								AnalyticsEvent: nil,
							},
						},
					},
				},
			},
		},
	}
}

func getSduiViewAllOffersCard() *sections.Section {
	return &sections.Section{
		Content: &sections.Section_DepthWiseListSection{
			DepthWiseListSection: &sections.DepthWiseListSection{
				IsScrollable:     false,
				VisualProperties: getGenericOfferCardVisualProps(12),
				Alignment:        sections.DepthWiseListSection_TOP_CENTER,
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
							OnClickBehavior: &behaviors.OnClickBehavior{
								Action: getAnyWithoutError(&deepLinkPb.Deeplink{
									Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
								}),
							},
						},
						AnalyticsEvent: nil,
					},
				},
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(&sections.VerticalListSection{
							IsScrollable:        false,
							HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
							VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(&sections.HorizontalListSection{
										IsScrollable: false,
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/home/<USER>", 104, 116)),
											},
										},
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: &properties.ContainerProperty{
														Size: &properties.Size{
															Width: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: offerWidgetCardWidth,
															},
															Height: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: 104,
															},
														},
													},
												},
											},
										},
										VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
										HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
									}),
								},
								{
									Content: getAnyWithoutError(&sections.HorizontalListSection{
										IsScrollable: false,
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("View all", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)),
											},
											{
												Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/chevron-right-lead.png", 16, 16)),
											},
										},
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: &properties.ContainerProperty{
														Size: &properties.Size{
															Width: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: offerWidgetCardWidth,
															},
															Height: &properties.Size_Dimension{
																Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
															},
														},
														Padding: &properties.PaddingProperty{
															Left: 15,
														},
														Margin: &properties.PaddingProperty{
															Top: 12,
														},
														Position: nil,
													},
												},
											},
										},
										VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
										HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
									}),
								},
							},
							VisualProperties: []*properties.VisualProperty{
								{
									Properties: &properties.VisualProperty_ContainerProperty{
										ContainerProperty: &properties.ContainerProperty{
											Size: &properties.Size{
												Width: &properties.Size_Dimension{
													Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
													ExactValue: offerWidgetCardWidth,
												},
												Height: &properties.Size_Dimension{
													Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
													ExactValue: 152,
												},
											},
											BgColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
											Corner: &properties.CornerProperty{
												TopLeftCornerRadius:  20,
												TopRightCornerRadius: 20,
												BottomLeftCorner:     20,
												BottomRightCorner:    20,
											},
											Margin: &properties.PaddingProperty{
												Bottom: 4,
											},
										},
									},
								},
							},
						},
						),
					},
				},
			},
		},
	}
}

func getAnyWithoutError(msg proto.Message) *anyPb.Any {
	res, _ := anyPb.New(msg)
	return res
}

func getGenericOffersSection(
	title string,
	titleColor string,
	viewAllText string,
	viewAllColor string,
	offersTabData []*components.Component,
	viewAllDl *deepLinkPb.Deeplink,
) *sections.Section {
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(&sections.HorizontalListSection{
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(title, titleColor, commontypes.FontStyle_DISPLAY_M)),
								},
								{
									Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(viewAllText, viewAllColor, commontypes.FontStyle_OVERLINE_XS_CAPS)),
									InteractionBehaviors: []*behaviors.InteractionBehavior{
										{
											Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
												OnClickBehavior: &behaviors.OnClickBehavior{
													Action: getAnyWithoutError(viewAllDl),
												},
											},
										},
									},
								},
							},
							VisualProperties: []*properties.VisualProperty{
								{
									Properties: &properties.VisualProperty_BgColor{
										BgColor: widget.GetBlockBackgroundColour(colors.ColorLightLayer1),
									},
								},
								{
									Properties: &properties.VisualProperty_ContainerProperty{
										ContainerProperty: &properties.ContainerProperty{
											Size: &properties.Size{
												Width: &properties.Size_Dimension{
													Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
												},
											},
										},
									},
								},
							},
							HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
						}),
					},
					{
						Content: getAnyWithoutError(&sections.Section{
							Content: &sections.Section_HorizontalListSection{
								HorizontalListSection: getHorizontalSection(offersTabData, "#EFF2F6"),
							},
						}),
					},
				},
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_SPACE_BETWEEN,
			},
		},
	}
}

// nolint
func getRewardsSection(
	totalRewardsEarningBanner *TotalRewardsEarningBanner,
	rewardCards []*RewardCard,
	rewardsHistoryListInput *RewardsHistoryListInput,
) *sections.Section {
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Left:   16,
									Right:  16,
									Top:    22,
									Bottom: 22,
								},
							},
						},
					},
				},
				Components: []*components.Component{
					{
						Content: getRewardSectionHeader(),
					},
					{
						Content: getAnyWithoutError(&components.Spacer{
							SpacingValue: components.Spacing_SPACING_M,
						}),
					},
					{
						Content: getTotalRewardsEarningBannerWithInput(totalRewardsEarningBanner),
					},
					{
						Content: getAnyWithoutError(&components.Spacer{
							SpacingValue: components.Spacing_SPACING_M,
						}),
					},
					{
						Content: getRewardsSectionCardsSection(rewardCards),
					},
					{
						Content: getAnyWithoutError(&components.Spacer{
							SpacingValue: components.Spacing_SPACING_XXL,
						}),
					},
					{
						Content: getRewardsHistoryList(rewardsHistoryListInput),
					},
				},
			},
		},
	}
}

// Figma link: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-•-FFF?node-id=1397-5716&t=tuHo1lNHvklVPYI8-0
func getRewardSectionHeader() *anyPb.Any {
	return getAnyWithoutError(&sections.HorizontalListSection{
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
		Components: []*components.Component{
			{
				Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Your rewards and offers", "#313234", commontypes.FontStyle_DISPLAY_L)),
			},
			{
				Content: getAnyWithoutError(commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/vkyc/information-icon.png").WithProperties(&commontypes.VisualElementProperties{
					Width:  20,
					Height: 20,
				})),
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
							OnClickBehavior: &behaviors.OnClickBehavior{
								Action: getAnyWithoutError(&deepLinkPb.Deeplink{
									Screen: deepLinkPb.Screen_CARD_OFFERS_CATALOG_SCREEN,
									ScreenOptions: &deepLinkPb.Deeplink_CardOffersCatalogScreenOptions{
										CardOffersCatalogScreenOptions: &deepLinkPb.CardOffersCatalogScreenOptions{
											CardTypeId: "CREDIT_CARD_ID",
										},
									},
								}),
							},
						},
					},
				},
			},
		},
	})
}

func getTotalRewardsEarningBannerWithInput(input *TotalRewardsEarningBanner) *anyPb.Any {
	return getAnyWithoutError(&sections.VerticalListSection{
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
						Padding: &properties.PaddingProperty{
							Left:  13,
							Right: 13,
							Top:   20,
						},
						BgColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
						Border: &properties.BorderProperty{
							BorderColor:     "#EFF2F6",
							BorderThickness: 1,
							CornerRadius:    16,
						},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  16,
							TopRightCornerRadius: 16,
							BottomLeftCorner:     16,
							BottomRightCorner:    16,
						},
					},
				},
			},
		},
		Components: []*components.Component{
			{
				Content: getAnyWithoutError(&sections.HorizontalListSection{
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
										},
									},
								},
							},
						},
					},
					Components: []*components.Component{
						{
							Content: getAnyWithoutError(input.Title),
						},
						{
							Content: getAnyWithoutError(&ui.IconTextComponent{
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									CornerRadius:     40,
									LeftPadding:      16,
									RightPadding:     16,
									TopPadding:       8,
									BottomPadding:    8,
									BackgroundColour: widget.GetBlockBackgroundColour("#00B899"),
								},
								Texts: []*commontypes.Text{
									{
										FontColor:    "#FFFFFF",
										DisplayValue: &commontypes.Text_PlainString{PlainString: "Redeem Fi-coins"},
										FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_XS},
									},
								},
								Deeplink: rewardsLandingPage(),
							}),
						},
					},
				}),
			},
			{
				Content: getAnyWithoutError(&components.Spacer{
					SpacingValue: components.Spacing_SPACING_M,
				}),
			},
			{
				Content: getAnyWithoutError(&sections.VerticalListSection{
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
										},
										Height: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
										},
									},
									BgColor: widget.GetLinearGradientBackgroundColour(0, []*widget.ColorStop{
										{Color: "#FFFFFF", StopPercentage: 0},
										{Color: "#FBF3E6", StopPercentage: 20},
										{Color: "#FBF3E6", StopPercentage: 20},
										{Color: "#FBF3E6", StopPercentage: 20},
										{Color: "#FFFFFF", StopPercentage: 100},
									}),
								},
							},
						},
					},
					Components: []*components.Component{
						{
							Content: getAnyWithoutError(&ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/2x_fi_coins_icon.png").WithProperties(&commontypes.VisualElementProperties{
									Width:  12,
									Height: 12,
								}),
								Texts: []*commontypes.Text{
									commontypes.GetTextFromStringFontColourFontStyle("2000 fi-coins is worth ₹20", "#98712F", commontypes.FontStyle_SUBTITLE_XS),
								},
							}),
						},
					},
				}),
			},
		},
	})
}

func getRewardsSectionCardsSection(rewardCards []*RewardCard) *anyPb.Any {
	cards := make([]*components.Component, 0, len(rewardCards))
	for _, card := range rewardCards {
		cards = append(cards, &components.Component{Content: getRewardCard(card)})
	}
	return getAnyWithoutError(&sections.HorizontalListSection{
		IsScrollable: true,
		Components:   cards,
	})
}

func getRewardCard(card *RewardCard) *anyPb.Any {
	return getAnyWithoutError(&sections.DepthWiseListSection{
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: 184,
							},
							Height: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: 152,
							},
						},
						BgColor: widget.GetBlockBackgroundColour("#F0F3F7"),
						Margin: &properties.PaddingProperty{
							Left:  3,
							Top:   3,
							Right: 3,
						},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  16,
							TopRightCornerRadius: 16,
							BottomLeftCorner:     16,
							BottomRightCorner:    16,
						},
					},
				},
			},
		},
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
					OnClickBehavior: &behaviors.OnClickBehavior{
						Action: getAnyWithoutError(card.Deeplink),
					},
				},
				AnalyticsEvent: nil,
			},
		},
		Components: []*components.Component{
			// Background Image
			{
				Content: getAnyWithoutError(card.BackgroundImage),
			},
			// Content
			{
				Content: getAnyWithoutError(&sections.VerticalListSection{
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Top:    16,
										Left:   12,
										Right:  12,
										Bottom: 12,
									},
								},
							},
						},
					},
					Components: []*components.Component{
						{
							Content: getAnyWithoutError(&sections.HorizontalListSection{
								HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
								Components: []*components.Component{
									{
										Content: getAnyWithoutError(card.TopLeftTag),
									},
									{
										Content: getAnyWithoutError(commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/rewards/chevron-right-lead.png").WithProperties(&commontypes.VisualElementProperties{
											Width:  20,
											Height: 20,
										})),
									},
								},
							}),
						},
						{
							Content: getAnyWithoutError(&components.Spacer{
								SpacingValue: components.Spacing_SPACING_S,
							}),
						},
						{
							Content: getAnyWithoutError(card.CardIcon),
						},
						{
							Content: getAnyWithoutError(&components.Spacer{
								SpacingValue: components.Spacing_SPACING_S,
							}),
						},
						{
							Content: getAnyWithoutError(card.DescriptionLabel),
						},
					},
				}),
			},
		},
	})
}

func getRewardHistoryItem(input *RewardHistoryItemInput) *anyPb.Any {
	return getAnyWithoutError(&sections.HorizontalListSection{
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Padding: &properties.PaddingProperty{
							Top:    20,
							Bottom: 20,
						},
					},
				},
			},
		},
		Components: []*components.Component{
			{
				Content: getAnyWithoutError(&sections.HorizontalListSection{
					Components: []*components.Component{
						{
							// Left Visual Icon
							Content: getAnyWithoutError(input.VisualElement),
						},
						{Content: getAnyWithoutError(&components.Spacer{
							SpacingValue: 4,
						})},
						{
							// Title and Subtitle
							Content: getAnyWithoutError(input.VerticalKeyValue),
						},
					},
				}),
			},
			{Content: getAnyWithoutError(&components.Spacer{
				Weight: &components.Spacer_Weight{
					Value: 100,
				},
			})},
			{
				// Right Action Button
				Content: getAnyWithoutError(input.ActionIconTextComp),
			},
		},
	})
}

// returns a component with a full-width divider image
// nolint:unused
func getDividerComponent(dividerImageUrl string) *components.Component {
	if dividerImageUrl == "" {
		dividerImageUrl = "https://epifi-icons.pointz.in/rewards/light_divider.png"
	}

	return &components.Component{
		Content: getAnyWithoutError(&commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: dividerImageUrl,
					},
					Properties: &commontypes.VisualElementProperties{
						Width: -1, // Full width
					},
				},
			},
		}),
	}
}

func getRewardsHistoryList(input *RewardsHistoryListInput) *anyPb.Any {
	list := []*components.Component{
		{
			Content: getAnyWithoutError(&sections.HorizontalListSection{
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("History", "#6A6D70", commontypes.FontStyle_DISPLAY_M).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)),
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
								},
							},
						},
					},
				},
			}),
		},
	}

	for i, item := range input.Items {
		list = append(list, &components.Component{
			Content: getRewardHistoryItem(item),
		})
		// Add divider after each item except the last one
		if i < len(input.Items)-1 {
			list = append(list, getDividerComponent("https://epifi-icons.pointz.in/rewards/light_divider.png"))
		}
	}

	if input.ShowActionButton {
		list = append(list, &components.Component{
			Content: getAnyWithoutError(&ui.IconTextComponent{
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					CornerRadius:     40,
					LeftPadding:      16,
					RightPadding:     16,
					TopPadding:       10,
					BottomPadding:    10,
					BackgroundColour: widget.GetBlockBackgroundColour("#F6F9FD"),
				},
				Texts: []*commontypes.Text{
					{
						FontColor:    "#6A6D70",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "View past earning"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
						Alignment:    commontypes.Text_ALIGNMENT_LEFT,
					},
				},
			}),
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
						OnClickBehavior: &behaviors.OnClickBehavior{
							Action: getAnyWithoutError(&deepLinkPb.Deeplink{
								Screen: deepLinkPb.Screen_REDEEMED_REWARDS_SCREEN,
							}),
						},
					},
				},
			},
		})
	}

	return getAnyWithoutError(&sections.VerticalListSection{
		Components: list,
	})
}
