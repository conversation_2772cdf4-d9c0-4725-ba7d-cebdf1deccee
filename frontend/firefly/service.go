// nolint:gosec,dupl,funlen,gocritic,unparam,govet
package firefly

import (
	"context"
	"fmt"
	"math"
	"time"

	pkgErr "github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	typesPkg "github.com/epifi/be-common/pkg/types"
	beNetWorthPb "github.com/epifi/gamma/api/insights/networth"

	"github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountBalEnumsPb "github.com/epifi/gamma/api/accounts/balance/enums"
	actorPb "github.com/epifi/gamma/api/actor"
	beCasperPb "github.com/epifi/gamma/api/casper"
	categorizerBePb "github.com/epifi/gamma/api/categorizer"
	consentPb "github.com/epifi/gamma/api/consent"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	depositPb "github.com/epifi/gamma/api/deposit"
	ffBePb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	ffBeAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffBeAccountsEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffBeBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ffBeBillingEnumsPb "github.com/epifi/gamma/api/firefly/billing/enums"
	ffRePb "github.com/epifi/gamma/api/firefly/card_recommendation"
	ffEnumsBePb "github.com/epifi/gamma/api/firefly/enums"
	ffLmsPb "github.com/epifi/gamma/api/firefly/lms"
	ffPinotPb "github.com/epifi/gamma/api/firefly/pinot"
	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	categorizerFePb "github.com/epifi/gamma/api/frontend/categorizer"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/errors"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/frontend/firefly/enums"
	"github.com/epifi/gamma/api/frontend/header"
	homePb "github.com/epifi/gamma/api/frontend/home"
	payFePb "github.com/epifi/gamma/api/frontend/pay"
	payFeTxnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	merchantPb "github.com/epifi/gamma/api/merchant"
	payPb "github.com/epifi/gamma/api/pay"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ffScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly"
	upiScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	billInfoValidator "github.com/epifi/gamma/frontend/firefly/billinfo_v2"
	"github.com/epifi/gamma/frontend/firefly/fees_and_benefits"
	feHelper "github.com/epifi/gamma/frontend/firefly/helper"
	"github.com/epifi/gamma/frontend/firefly/homedashboard"
	"github.com/epifi/gamma/frontend/firefly/internal"
	"github.com/epifi/gamma/frontend/firefly/rewards"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	header2 "github.com/epifi/gamma/pkg/frontend/header"
	pkgOnb "github.com/epifi/gamma/pkg/onboarding"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

type controlInfo struct {
	Text                           string
	PhysicalCardActivationRequired bool
}

var (
	feRequestTypeToBeRequestTypeMap = map[ffEnumsPb.CardRequestType]ffEnumsBePb.CardRequestType{
		ffEnumsPb.CardRequestType_CARD_REQUEST_TYPE_FREEZE_CARD:   ffEnumsBePb.CardRequestType_REQUEST_TYPE_FREEZE_CARD,
		ffEnumsPb.CardRequestType_CARD_REQUEST_TYPE_UNFREEZE_CARD: ffEnumsBePb.CardRequestType_REQUEST_TYPE_UNFREEZE_CARD,
	}

	beTxnTypeToFeTxnTypeMap = map[ffEnumsBePb.CardControlType]ffEnumsPb.CardControlType{
		ffEnumsBePb.CardControlType_CARD_CONTROL_TYPE_ATM:           ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM,
		ffEnumsBePb.CardControlType_CARD_CONTROL_TYPE_POS:           ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS,
		ffEnumsBePb.CardControlType_CARD_CONTROL_TYPE_ECOM:          ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
		ffEnumsBePb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS:   ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS,
		ffEnumsBePb.CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL: ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL,
	}

	beCardProgramTypeToFeCardProgramType = map[types.CardProgramType]ffEnumsPb.CardProgramType{
		types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:     ffEnumsPb.CardProgramType_CARD_PROGRAM_TYPE_SECURED,
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:   ffEnumsPb.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED: ffEnumsPb.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED,
	}

	genericErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{
			ErrorCode:   "CARD_0013",
			Title:       "Request Failed",
			Description: "Something went wrong!! No worries please try again",
		}},
	}

	notFoundErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{
			Title:       "No Credit Card found",
			Description: "The user does not have a credit card",
		}},
	}

	insufficientFundBottomSheetErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{
			Title: "Insufficient funds! Top up your Fi Account now",
			Ctas: []*errors.CTA{{
				Type: errors.CTA_CUSTOM,
				Text: "ADD MONEY",
				Action: &deeplink.Deeplink{
					Screen: deeplink.Screen_TRANSFER_IN,
				},
			}},
		}},
	}

	exportEmailErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{
			ErrorCode:   "",
			Title:       "Unable to send statement",
			Subtitle:    "",
			Description: "Uh-oh! We faced an issue while doing this. Something’s up at our end. Please try again.",
			Ctas: []*errors.CTA{{
				Type:         errors.CTA_CUSTOM,
				Text:         "Retry",
				Action:       nil,
				DisplayTheme: errors.CTA_PRIMARY,
			}},
		}},
	}
	feRequestWorkflowTypeToBeWorkflowTypeMap = map[ffEnumsPb.CardRequestWorkflow]ffEnumsBePb.CardRequestWorkFlow{
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_CARD_ONBOARDING:        ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PIN_RESET:              ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PIN_RESET,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_REISSUE_CARD:           ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_REISSUE_CARD,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_CONTROL_CHANGE:         ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CONTROL_CHANGE,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_LIMIT_CHANGE:           ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_LIMIT_CHANGE,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_VIEW_CARD_DETAILS:      ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_VIEW_CARD_DETAILS,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_FREEZE_UNFREEZE:        ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_FREEZE_UNFREEZE,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PROCESS_DISPUTE:        ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PROCESS_DISPUTE,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_CARD_ACTIVATION:        ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ACTIVATION,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_BILL_PAYMENTS:          ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PAYMENT,
		ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_BIOMETRIC_REVALIDATION: ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_BIOMETRIC_REVALIDATION,
	}
	feControlTypeToBeControlTypeMap = map[ffEnumsPb.CardControlType]ffEnumsBePb.CardControlType{
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM:         ffEnumsBePb.CardControlType_CARD_CONTROL_TYPE_ATM,
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS:         ffEnumsBePb.CardControlType_CARD_CONTROL_TYPE_POS,
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS: ffEnumsBePb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS,
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM:        ffEnumsBePb.CardControlType_CARD_CONTROL_TYPE_ECOM,
	}
	feAuthFlowToBeAuthFlowMap = map[ffEnumsPb.CardAuthFlow]ffEnumsBePb.CardAuthFlow{
		ffEnumsPb.CardAuthFlow_CARD_AUTH_FLOW_OTP_AND_LIVENESS:           ffEnumsBePb.CardAuthFlow_CARD_AUTH_FLOW_OTP_AND_LIVENESS,
		ffEnumsPb.CardAuthFlow_CARD_AUTH_FLOW_OTP_AND_UPI_PIN_VALIDATION: ffEnumsBePb.CardAuthFlow_CARD_AUTH_FLOW_OTP_AND_UPI_PIN_VALIDATION,
		ffEnumsPb.CardAuthFlow_CARD_AUTH_FLOW_LIVENESS:                   ffEnumsBePb.CardAuthFlow_CARD_AUTH_FLOW_LIVENESS,
		ffEnumsPb.CardAuthFlow_CARD_AUTH_FLOW_UPI_PIN_VALIDATION:         ffEnumsBePb.CardAuthFlow_CARD_AUTH_FLOW_UPI_PIN_VALIDATION,
	}
	feToBeDisputeTypeMap = map[ffEnumsPb.DisputeType]ffEnumsBePb.DisputeType{
		ffEnumsPb.DisputeType_DISPUTE_TYPE_TRANSACTION: ffEnumsBePb.DisputeType_DISPUTE_TYPE_TRANSACTION,
	}
	beCardNetworkTypeToBeCardNetworkType = map[ffEnumsBePb.CardNetworkType]ffEnumsPb.CardNetworkType{
		ffEnumsBePb.CardNetworkType_CARD_NETWORK_TYPE_VISA:        ffEnumsPb.CardNetworkType_CARD_NETWORK_TYPE_VISA,
		ffEnumsBePb.CardNetworkType_CARD_NETWORK_TYPE_MASTER_CARD: ffEnumsPb.CardNetworkType_CARD_NETWORK_TYPE_MASTER_CARD,
	}
	feCardNetworkTypeToBeCardNetworkType = map[ffEnumsPb.CardNetworkType]ffEnumsBePb.CardNetworkType{
		ffEnumsPb.CardNetworkType_CARD_NETWORK_TYPE_VISA:        ffEnumsBePb.CardNetworkType_CARD_NETWORK_TYPE_VISA,
		ffEnumsPb.CardNetworkType_CARD_NETWORK_TYPE_MASTER_CARD: ffEnumsBePb.CardNetworkType_CARD_NETWORK_TYPE_MASTER_CARD,
	}
	beTxnTypeToFeTxnTransferTypeMap = map[ffBeAccountsEnumsPb.TransactionType]types.TransactionTransferType{
		ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT:  types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_DEBIT,
		ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT: types.TransactionTransferType_TRANSACTION_TRANSFER_TYPE_CREDIT,
	}
	beTxnStatusToFeTxnStatus = map[ffBeAccountsEnumsPb.TransactionStatus]ffEnumsPb.TransactionStatus{
		ffBeAccountsEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS: ffEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS,
		ffBeAccountsEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE: ffEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE,
	}
	bePaymentStatusMap = map[ffEnumsBePb.PaymentStatus]ffEnumsPb.PaymentStatus{
		ffEnumsBePb.PaymentStatus_PAYMENT_STATUS_FAILED:      ffEnumsPb.PaymentStatus_PAYMENT_STATUS_FAILED,
		ffEnumsBePb.PaymentStatus_PAYMENT_STATUS_SUCCESS:     ffEnumsPb.PaymentStatus_PAYMENT_STATUS_SUCCESS,
		ffEnumsBePb.PaymentStatus_PAYMENT_STATUS_IN_PROGRESS: ffEnumsPb.PaymentStatus_PAYMENT_STATUS_IN_PROGRESS,
		ffEnumsBePb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED: ffEnumsPb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED,
	}
	// map to get a darker shade of a given color
	tintVsShade = map[string]string{
		internal.BillInfoDashboardRed:    internal.BillInfoDashboardDarkRed,
		internal.BillInfoDashboardYellow: internal.BillInfoDashboardDarkYellow,
	}
	// map to get a lighter shade of a given color
	shadeVsTint = map[string]string{
		internal.BillInfoDashboardYellow:     internal.BillInfoDashboardLightYellow,
		internal.BillInfoDashboardGreen:      internal.BillInfoDashboardLightGreen,
		internal.BillInfoDashboardRed:        internal.BillInfoDashboardLightRed,
		internal.BillInfoDashboardDarkYellow: internal.BillInfoDashboardYellow,
		internal.BillInfoDashboardDarkRed:    internal.BillInfoDashboardRed,
	}

	rewardTypeToDisplayStringMap = map[ffBeBillingEnumsPb.MerchantRewardType]string{
		ffBeBillingEnumsPb.MerchantRewardType_MERCHANT_REWARD_TYPE_UNSPECIFIED: "",
		ffBeBillingEnumsPb.MerchantRewardType_MERCHANT_REWARD_TYPE_TWO_X:       "2X rewards on your top merchants",
		ffBeBillingEnumsPb.MerchantRewardType_MERCHANT_REWARD_TYPE_FIVE_X:      "5X rewards on your top merchants",
	}

	ccRewardTypeToLockedRewardIcon = map[ffEnumsBePb.CCRewardType]string{
		ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_GIFT_VOUCHERS: "https://epifi-icons.pointz.in/credit_card_images/welcome_vouchers_locked_img_carousel.png",
		ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_FI_COINS:      "https://epifi-icons.pointz.in/credit_card_images/locked_fi_coins_image_carousel.png",
	}

	ccRewardTypeToUnlockedRewardIcon = map[ffEnumsBePb.CCRewardType]string{
		ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_GIFT_VOUCHERS: "https://epifi-icons.pointz.in/credit_card_images/welcome_vouchers_unlocked_img_carousel.png",
		ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_FI_COINS:      "https://epifi-icons.pointz.in/credit_card_images/unlocked_fi_coins_image_carousel.png",
	}

	ccRewardTypeToUnlockedRewardText = map[ffEnumsBePb.CCRewardType]string{
		ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_GIFT_VOUCHERS: "Your welcome vouchers have been unlocked!\nRedeem now.",
		ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_FI_COINS:      "Your 50,000 Fi-Coins have been unlocked! Spend them now.",
	}

	ccRewardTypeToUnlockedRewardDeeplink = map[ffEnumsBePb.CCRewardType]*deeplink.Deeplink{
		ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_GIFT_VOUCHERS: {Screen: deeplink.Screen_REDEEMED_OFFERS_SCREEN},
		ffEnumsBePb.CCRewardType_CC_REWARD_TYPE_FI_COINS:      {Screen: deeplink.Screen_MY_REWARDS_SCREEN},
	}

	q3TimeStamp       = timestamppb.New(time.Date(2023, 07, 01, 0, 0, 0, 0, datetime.IST))
	thresholdForLimit = &money.Money{
		CurrencyCode: moneyPb.RupeeCurrencyCode,
		Units:        200000,
	}

	creditCardOnboardingConsentToConsentTypesListMap = map[ffEnumsPb.CreditCardOnboardingConsent][]consentPb.ConsentType{
		ffEnumsPb.CreditCardOnboardingConsent_CREDIT_CARD_ONBOARDING_CONSENT_AMPLIFI: {
			consentPb.ConsentType_CREDIT_REPORT_CHECK,
			consentPb.ConsentType_UNSECURED_CREDIT_CARD_FEES,
			consentPb.ConsentType_UNSECURED_CREDIT_CARD_MOST_IMP_TNC,
			consentPb.ConsentType_UNSECURED_CREDIT_CARD_KFS,
			consentPb.ConsentType_FI_CREDIT_CARD_TNC,
		},
		ffEnumsPb.CreditCardOnboardingConsent_CREDIT_CARD_ONBOARDING_CONSENT_MAGNIFI: {
			consentPb.ConsentType_CREDIT_REPORT_CHECK,
			consentPb.ConsentType_MASS_UNSECURED_CREDIT_CARD_MOST_IMP_TNC,
			consentPb.ConsentType_MASS_UNSECURED_CREDIT_CARD_KFS,
			consentPb.ConsentType_FI_CREDIT_CARD_TNC,
		},
		ffEnumsPb.CreditCardOnboardingConsent_CREDIT_CARD_ONBOARDING_CONSENT_SIMPLIFI: {
			consentPb.ConsentType_OPEN_FIXED_DEPOSIT,
			consentPb.ConsentType_SECURED_CREDIT_CARD_MOST_IMP_TNC,
			consentPb.ConsentType_SECURED_CREDIT_CARD_KFS,
			consentPb.ConsentType_FI_CREDIT_CARD_TNC,
		},
		ffEnumsPb.CreditCardOnboardingConsent_CREDIT_CARD_ONBOARDING_CONSENT_CREDIT_REPORT_CHECK: {
			consentPb.ConsentType_CREDIT_REPORT_CHECK,
		},
	}

	feControlLocTypeToBeControlLocType = map[ffEnumsPb.CardControlLocationType]ffEnumsBePb.CardUsageLocationType{
		// mapping FE unspecified to BE domestic to keep it backward compatible
		ffEnumsPb.CardControlLocationType_CARD_CONTROL_LOCATION_TYPE_UNSPECIFIED:   ffEnumsBePb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC,
		ffEnumsPb.CardControlLocationType_CARD_CONTROL_LOCATION_TYPE_DOMESTIC:      ffEnumsBePb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC,
		ffEnumsPb.CardControlLocationType_CARD_CONTROL_LOCATION_TYPE_INTERNATIONAL: ffEnumsBePb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL,
	}
	beControlLocTypeToFeControlLocType = map[ffEnumsBePb.CardUsageLocationType]ffEnumsPb.CardControlLocationType{
		ffEnumsBePb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_DOMESTIC:      ffEnumsPb.CardControlLocationType_CARD_CONTROL_LOCATION_TYPE_DOMESTIC,
		ffEnumsBePb.CardUsageLocationType_CARD_USAGE_LOCATION_TYPE_INTERNATIONAL: ffEnumsPb.CardControlLocationType_CARD_CONTROL_LOCATION_TYPE_INTERNATIONAL,
	}
	cardLimitTabOrder = []ffEnumsPb.CardControlLocationType{
		ffEnumsPb.CardControlLocationType_CARD_CONTROL_LOCATION_TYPE_DOMESTIC,
		ffEnumsPb.CardControlLocationType_CARD_CONTROL_LOCATION_TYPE_INTERNATIONAL,
	}

	cardUsageLocTypeToTabHeading = map[ffEnumsPb.CardControlLocationType]*commontypes.Text{
		ffEnumsPb.CardControlLocationType_CARD_CONTROL_LOCATION_TYPE_DOMESTIC: {
			FontColor:    "#6A6D70",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Domestic"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
		ffEnumsPb.CardControlLocationType_CARD_CONTROL_LOCATION_TYPE_INTERNATIONAL: {
			FontColor:    "#6A6D70",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "International"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
	}
	feControlTypeToTextMap = map[ffEnumsPb.CardControlType]controlInfo{
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM:  {Text: "ATM usage", PhysicalCardActivationRequired: true},
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM: {Text: "ECOM usage"},
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS:  {Text: "POS usage", PhysicalCardActivationRequired: true},
	}

	cardProgramTypeToRewardCardTypeMap = map[types.CardProgramType]string{
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED:    "UNSPECIFIED",
		types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:        "SIMPLIFI_CREDIT_CARD_ID",
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:      "AMPLIFI_CREDIT_CARD_ID",
		types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED: "MAGNIFI_CREDIT_CARD_ID",
	}

	validDashboardCardTypesToShowCcSummary = []ffEnumsPb.HomeDashboardCardType{
		ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_BILL_DUE,
		ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_BILL_DUE_PAST_DUE_DATE,
		ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_CARD_BLOCKED_AND_BILL_DUE,
		ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_CARD_BLOCKED_AND_NO_BILL_DUE,
		ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_NO_BILL_DUE,
	}
)

type Service struct {
	ffPb.UnimplementedFireflyServer
	fireFlyClient               ffBePb.FireflyClient
	fireflyAccountingClient     ffBeAccountsPb.AccountingClient
	txnCategorizerFeClient      categorizerFePb.TxnCategorizerClient
	ffBillingClient             ffBeBillingPb.BillingClient
	actorClient                 actorPb.ActorClient
	savingsClient               savingsPb.SavingsClient
	rewardsClient               rewardsPb.RewardsGeneratorClient
	txnCategorizerClient        categorizerBePb.TxnCategorizerClient
	merchantClient              merchantPb.MerchantServiceClient
	userClient                  userPb.UsersClient
	fireflyLmsClient            ffLmsPb.LoanManagementSystemClient
	consentClient               consentPb.ConsentClient
	conf                        *config.Config
	dynamicConf                 *genconf.Config
	pinotClient                 ffPinotPb.TxnAggregatesClient
	homeDashboardFactory        *homedashboard.Factory
	limitEstimator              limitEstimatorPb.CreditLimitEstimatorClient
	onbClient                   onbPb.OnboardingClient
	depositClient               depositPb.DepositClient
	billInfoValidationProcessor *billInfoValidator.BillInfoValidationProcessor
	rewardsProvider             *rewards.RewardProvider
	feesAndBenefitsProvider     *fees_and_benefits.FeesAndBenefitsProvider
	accountBalanceClient        accountBalancePb.BalanceClient
	rewardsProjectionClient     rewardsProjectionPb.ProjectorServiceClient
	cardRecommendationClient    ffRePb.CardRecommendationServiceClient
	payFeTxnClient              payFeTxnPb.TransactionClient
	payBeClient                 payPb.PayClient
	releaseEvaluator            release.IEvaluator
	creditReportV2Client        creditReportV2Pb.CreditReportManagerClient
	segmentationServiceClient   segment.SegmentationServiceClient
	rewardsListingClient        beCasperPb.OfferListingServiceClient
	fireflyV2Client             ffBeV2Pb.FireflyV2Client
	questSdkClient              *questSdk.Client
	userAttributeFetcher        pkgUser.UserAttributesFetcher
	ccIntroScrenBuilder         CcIntroScreenBuilder
	rewardAggrClient            rewardsPinotPb.RewardsAggregatesClient
	networthClient              beNetWorthPb.NetWorthClient
}

func NewService(fireFlyClient ffBePb.FireflyClient, fireflyAccountingClient ffBeAccountsPb.AccountingClient,
	txnCategorizerFeClient categorizerFePb.TxnCategorizerClient, billingClient ffBeBillingPb.BillingClient,
	actorClient actorPb.ActorClient, rewardsClient rewardsPb.RewardsGeneratorClient,
	savingsClient savingsPb.SavingsClient, txnCategorizerClient categorizerBePb.TxnCategorizerClient,
	userClient userPb.UsersClient, merchantClient merchantPb.MerchantServiceClient, fireflyLmsClient ffLmsPb.LoanManagementSystemClient, consentClient consentPb.ConsentClient,
	conf *config.Config, dynamicConf *genconf.Config, pinotClient ffPinotPb.TxnAggregatesClient, homeDashboardFactory *homedashboard.Factory,
	limitEstimator limitEstimatorPb.CreditLimitEstimatorClient, onbClient onbPb.OnboardingClient, depositClient depositPb.DepositClient,
	billInfoValidationProcessor *billInfoValidator.BillInfoValidationProcessor, rewardsProvider *rewards.RewardProvider,
	accountBalanceClient accountBalancePb.BalanceClient, rewardsProjectionClient rewardsProjectionPb.ProjectorServiceClient,
	feesAndBenefitsProvider *fees_and_benefits.FeesAndBenefitsProvider, cardRecommendationClient ffRePb.CardRecommendationServiceClient,
	payTxnClient payFeTxnPb.TransactionClient, payBeClient payPb.PayClient, releaseEvaluator release.IEvaluator, creditReportV2Client creditReportV2Pb.CreditReportManagerClient,
	segmentationServiceClient segment.SegmentationServiceClient, rewardsListingClient beCasperPb.OfferListingServiceClient,
	fireflyV2Client ffBeV2Pb.FireflyV2Client, questSdkClient *questSdk.Client, userAttributeFetcher pkgUser.UserAttributesFetcher,
	ccIntroScrenBuilder CcIntroScreenBuilder, rewardAggrClient rewardsPinotPb.RewardsAggregatesClient, networthClient beNetWorthPb.NetWorthClient) *Service {
	return &Service{
		fireFlyClient:               fireFlyClient,
		fireflyAccountingClient:     fireflyAccountingClient,
		txnCategorizerFeClient:      txnCategorizerFeClient,
		ffBillingClient:             billingClient,
		rewardsClient:               rewardsClient,
		savingsClient:               savingsClient,
		actorClient:                 actorClient,
		txnCategorizerClient:        txnCategorizerClient,
		userClient:                  userClient,
		merchantClient:              merchantClient,
		fireflyLmsClient:            fireflyLmsClient,
		consentClient:               consentClient,
		conf:                        conf,
		dynamicConf:                 dynamicConf,
		pinotClient:                 pinotClient,
		homeDashboardFactory:        homeDashboardFactory,
		limitEstimator:              limitEstimator,
		onbClient:                   onbClient,
		depositClient:               depositClient,
		billInfoValidationProcessor: billInfoValidationProcessor,
		rewardsProvider:             rewardsProvider,
		feesAndBenefitsProvider:     feesAndBenefitsProvider,
		accountBalanceClient:        accountBalanceClient,
		rewardsProjectionClient:     rewardsProjectionClient,
		cardRecommendationClient:    cardRecommendationClient,
		payFeTxnClient:              payTxnClient,
		payBeClient:                 payBeClient,
		releaseEvaluator:            releaseEvaluator,
		creditReportV2Client:        creditReportV2Client,
		segmentationServiceClient:   segmentationServiceClient,
		rewardsListingClient:        rewardsListingClient,
		fireflyV2Client:             fireflyV2Client,
		questSdkClient:              questSdkClient,
		userAttributeFetcher:        userAttributeFetcher,
		ccIntroScrenBuilder:         ccIntroScrenBuilder,
		rewardAggrClient:            rewardAggrClient,
		networthClient:              networthClient,
	}
}

func (s *Service) UpdateStatementDate(ctx context.Context, req *ffPb.UpdateStatementDateRequest) (*ffPb.UpdateStatementDateResponse, error) {
	res := &ffPb.UpdateStatementDateResponse{RespHeader: &header.ResponseHeader{}}
	updRes, updErr := s.ffBillingClient.UpdateStatementDate(ctx, &ffBeBillingPb.UpdateStatementDateRequest{
		CardId:          req.GetCardId(),
		NewStatementDay: req.GetNewStatementDate(),
		NewDueDate:      req.GetNewDueDay(),
	})
	switch {
	case updErr != nil:
		logger.Error(ctx, "error in hitting BE api for stmt date update",
			zap.Error(updErr),
			zap.String(logger.CARD_ID, req.GetCardId()))
		res.RespHeader.Status = rpc.StatusInternal()
		res.RespHeader.ErrorView = &errors.ErrorView{
			Type: errors.ErrorViewType_FULL_SCREEN,
			Options: &errors.ErrorView_FullScreenErrorView{
				FullScreenErrorView: &errors.FullScreenErrorView{
					ErrorCode: "An error occurred",
					Title:     "An error occurred",
					Subtitle:  "We are facing some issues at our end. Please try again later",
				},
			},
		}
		return res, nil
	case updRes.GetStatus().IsFailedPrecondition():
		logger.Error(ctx, "failed precondition in stmt date update", zap.String(logger.CARD_ID, req.GetCardId()))
		res.RespHeader.Status = rpc.StatusInternal()
		res.RespHeader.ErrorView = &errors.ErrorView{
			Type: errors.ErrorViewType_FULL_SCREEN,
			Options: &errors.ErrorView_FullScreenErrorView{
				FullScreenErrorView: &errors.FullScreenErrorView{
					ErrorCode: "Invalid request",
					Title:     "Non zero outstanding",
					Subtitle:  "You have a non zero outstanding amount/active loan/active EMIs. Please clear those and try again later",
				},
			},
		}
		return res, nil
	case updRes.GetStatus().IsAlreadyExists():
		res.RespHeader.Status = rpc.StatusInternal()
		res.RespHeader.ErrorView = &errors.ErrorView{
			Type: errors.ErrorViewType_FULL_SCREEN,
			Options: &errors.ErrorView_FullScreenErrorView{
				FullScreenErrorView: &errors.FullScreenErrorView{
					ErrorCode: "Invalid request",
					Title:     "No new statement date selected",
					Subtitle:  "Updated statement date same as current one, try with different date",
				},
			},
		}
		return res, nil
	case !updRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success response from UpdateStatementDate",
			zap.Error(epifigrpc.RPCError(updRes, updErr)),
			zap.String(logger.CARD_ID, req.GetCardId()))
		res.RespHeader.Status = rpc.StatusInternal()
		res.RespHeader.ErrorView = &errors.ErrorView{
			Type: errors.ErrorViewType_FULL_SCREEN,
			Options: &errors.ErrorView_FullScreenErrorView{
				FullScreenErrorView: &errors.FullScreenErrorView{
					ErrorCode: "Some error occurred",
					Title:     "Error in updating billing day",
					Subtitle:  "We faced some issues. Please try again later",
				},
			},
		}
		return res, nil
	default:
		res.RespHeader.Status = rpc.StatusOk()
		res.NextAction = updRes.GetNextAction()
		return res, nil
	}
}

func (s *Service) FreezeUnfreezeCard(ctx context.Context, req *ffPb.FreezeUnfreezeCardRequest) (*ffPb.FreezeUnfreezeCardResponse, error) {
	var (
		res = &ffPb.FreezeUnfreezeCardResponse{}
	)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.FreezeUnfreezeCardResponse, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}
	beRequestType, ok := feRequestTypeToBeRequestTypeMap[req.GetRequestType()]
	if !ok {
		logger.Error(ctx, "invalid request type for freeze unfreeze card", zap.String(logger.REQUEST_TYPE,
			req.GetRequestType().String()))
		return responseWithStatus(rpc.StatusInvalidArgument(), genericErrorView)
	}
	beRes, err := s.fireFlyClient.FreezeUnfreezeCard(ctx, &ffBePb.FreezeUnfreezeCardRequest{
		CardId:                        req.GetCardId(),
		RequestType:                   beRequestType,
		Provenance:                    ffEnumsBePb.Provenance_PROVENANCE_APP,
		DeviceUnlockMechanism:         req.GetDeviceUnlockMechanism(),
		DeviceUnlockMechanismStrength: req.GetDeviceUnlockMechanismStrength(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in freeze unfreeze card", zap.Error(te), zap.String(logger.CARD_ID, req.GetCardId()))
		return responseWithStatus(rpc.StatusFromErrorWithDefaultInternal(te), genericErrorView)
	}
	res.NextAction = beRes.GetNextAction()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func (s *Service) GetRequestStatus(ctx context.Context, req *ffPb.GetRequestStatusRequest) (*ffPb.GetRequestStatusResponse, error) {
	var (
		res = &ffPb.GetRequestStatusResponse{}
	)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.GetRequestStatusResponse, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}
	beRes, err := s.fireFlyClient.GetRequestStatus(ctx, &ffBePb.GetRequestStatusRequest{
		CardRequestId: req.GetCardRequestId(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in freeze unfreeze card", zap.Error(te), zap.String(logger.CARD_REQUEST_ID, req.GetCardRequestId()))
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}
	res.NextAction = beRes.GetNextAction()
	if beRes.GetNextAction().GetScreen() != deeplink.Screen_FIREFLY_GET_REQUEST_STATUS {
		return s.addFeedbackEngineInfo(res, beRes), nil
	}
	if req.GetAttemptNumber() >= s.dynamicConf.CreditCard().OnboardingRetryAttemptCutoff() && (beRes.GetWorkflow() == ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING || beRes.GetWorkflow() == ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_REALTIME_CARD_ELIGIBILITY_CHECK) {
		res.NextAction = helper.GetOnboardingRetryAttemptHaltDeeplink(isUserFiLite(beRes.GetWorkflow(), beRes.GetCardRequestDetails()), beRes.GetNextAction())
		return responseWithStatus(rpc.StatusOk(), nil)
	}
	screenOptions := beRes.GetNextAction().GetScreenOptions().(*deeplink.Deeplink_FireflyGetRequestStatusScreenOptions)
	screenOptions.FireflyGetRequestStatusScreenOptions.RetryDelay = 1000
	screenOptions.FireflyGetRequestStatusScreenOptions.RetryAttemptNumber = 1 + req.GetAttemptNumber()
	res.NextAction.ScreenOptions = screenOptions
	return responseWithStatus(rpc.StatusOk(), nil)
}

func (s *Service) addFeedbackEngineInfo(res *ffPb.GetRequestStatusResponse, beRes *ffBePb.GetRequestStatusResponse) *ffPb.GetRequestStatusResponse {
	switch res.GetNextAction().GetScreen() {
	case deeplink.Screen_CC_INELIGIBLE_USER_SCREEN:
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusOk(),
			FeedbackEngineInfo: &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA,
					FlowIdentifier:     types.FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_CREDIT_CARD_USER_INELIGIBLE.String(),
				},
			},
		}
	case deeplink.Screen_CREDIT_CARD_ADDRESS_SELECTION_V2_SCREEN:
		res.RespHeader = &header.ResponseHeader{
			Status:             rpc.StatusOk(),
			FeedbackEngineInfo: s.getFeedBackEngineInfoForAddressSelectionScreen(beRes.GetCardRequestDetails().GetCardProgram()),
		}
	case deeplink.Screen_FIREFLY_CARD_ACTIVATION_SCREEN:
		res.RespHeader = &header.ResponseHeader{
			Status:             rpc.StatusOk(),
			FeedbackEngineInfo: s.getFeedBackEngineInfoForConsentScreen(beRes.GetCardRequestDetails().GetCardProgram()),
		}
	default:
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusOk(),
		}
	}
	return res
}

func (s *Service) getFeedBackEngineInfoForAddressSelectionScreen(cardProgram *types.CardProgram) *header.FeedbackEngineInfo {
	if !s.dynamicConf.CreditCard().EnableFeedbackFormsForCCScreens() || cardProgram == nil {
		return nil
	}
	switch {
	case ffPkg.IsCreditCardProgramMassUnsecured(cardProgram):
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_MAGNIFI.String(),
			},
		}
	case ffPkg.IsCreditCardProgramUnsecured(cardProgram):
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_AMPLIFI.String(),
			},
		}
	default:
		return nil
	}
}

func (s *Service) getFeedBackEngineInfoForConsentScreen(cardProgram *types.CardProgram) *header.FeedbackEngineInfo {
	if !s.dynamicConf.CreditCard().EnableFeedbackFormsForCCScreens() || cardProgram == nil {
		return nil
	}
	switch {
	case ffPkg.IsCreditCardProgramMassUnsecured(cardProgram):
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_MAGNIFI.String(),
			},
		}
	case ffPkg.IsCreditCardProgramSecured(cardProgram):
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_SIMPLIFI.String(),
			},
		}
	default:
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_AMPLIFI.String(),
			},
		}

	}
}

func (s *Service) StartCardOnboarding(ctx context.Context, req *ffPb.StartCardOnboardingRequest) (*ffPb.StartCardOnboardingResponse, error) {
	var (
		res = &ffPb.StartCardOnboardingResponse{}
	)

	screenIdentifier := req.GetScreenIdentifier()
	switch {
	case !ffPkg.IsCreditCardProgramSecured(req.GetCardProgram()) && screenIdentifier == int32(deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API):
		// Fi lite users will have to go through realtime eligibility compulsorily
		res = s.startRealtimeEligibilityCheck(ctx, req)
		return res, nil
	default:
	}

	if (ffPkg.IsCreditCardProgramSecured(req.GetCardProgram()) && req.GetCardOnboardingConsent() == ffEnumsPb.CreditCardOnboardingConsent_CREDIT_CARD_ONBOARDING_CONSENT_SIMPLIFI) ||
		((ffPkg.IsCreditCardProgramMassUnsecured(req.GetCardProgram()) || ffPkg.IsCreditCardProgramUnsecured(req.GetCardProgram())) && req.GetCardOnboardingConsent() == ffEnumsPb.CreditCardOnboardingConsent_CREDIT_CARD_ONBOARDING_CONSENT_CREDIT_REPORT_CHECK) {
		consentResp, err := s.RecordCreditCardOnboardingConsent(ctx, &ffPb.RecordCreditCardOnboardingConsentRequest{
			Req:                         req.GetReq(),
			CreditCardOnboardingConsent: req.GetCardOnboardingConsent(),
		})
		if err != nil {
			logger.Error(ctx, "error in recording consent", zap.Error(err))
			res.RespHeader = &header.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: genericErrorView,
			}
			return res, nil
		}
		if !consentResp.GetRespHeader().GetStatus().IsSuccess() {
			logger.Error(ctx, "non success state in recording consent")
			res.RespHeader = &header.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: genericErrorView,
			}
			return res, nil
		}
	}

	if s.checkFeatureReleaseConstraints(ctx, types.Feature_FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK, req.GetReq().GetAuth().GetActorId()) {
		if req.GetCardProgram() == nil {
			logger.Error(ctx, "invalid card program passed from client", zap.String(logger.CARD_PROGRAM, ffPkg.GetCardProgramStringFromCardProgram(req.GetCardProgram())))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
			return res, nil
		}
	}
	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.CreditCard().CcNetworkSelectionScreenVersionCheck()) {
		if req.GetCardNetworkType() == ffEnumsPb.CardNetworkType_CARD_NETWORK_TYPE_UNSPECIFIED {
			logger.Error(ctx, "invalid card network type passed from client", zap.String("card_network_type", req.GetCardNetworkType().String()))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
			return res, nil
		}
	}
	if req.GetCardNetworkType() == ffEnumsPb.CardNetworkType_CARD_NETWORK_TYPE_UNSPECIFIED {
		req.CardNetworkType = ffEnumsPb.CardNetworkType_CARD_NETWORK_TYPE_VISA
	}

	if req.GetCardProgram() == nil {
		// if the card program is empty then fetch latest offer for user
		offerDetails, err := s.fireFlyClient.GetCreditCardOffers(ctx, &ffBePb.GetCreditCardOffersRequest{
			ActorId: req.GetReq().GetAuth().GetActorId(),
			Vendor:  ffEnumsBePb.Vendor_FEDERAL,
		})
		if te := epifigrpc.RPCError(offerDetails, err); te != nil {
			logger.Error(ctx, "error in fetching offer for actor", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.Error(te))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
			return res, nil
		}
		req.CardProgram = offerDetails.GetOffers()[0].GetCardProgram()
	}

	switch req.GetCardProgram().GetCardProgramOrigin() {
	case types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE:
		return s.startFiLiteCardOnboarding(ctx, req)
	default:
	}

	cardProgramType := req.GetCardProgram().GetCardProgramType()
	switch cardProgramType {
	case types.CardProgramType_CARD_PROGRAM_TYPE_SECURED, types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		res = s.startCardOnboardingV2(ctx, req)
		return res, nil
	case types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:
		enableUnsecuredOnboardingV2String, ok := req.GetCreditCardRequestHeader().GetAttributes()[enableUnsecuredOnboardingV2]
		if ok && enableUnsecuredOnboardingV2String == trueString {
			res = s.startCardOnboardingV2(ctx, req)
			return res, nil
		}
	default:
	}

	beRes, err := s.fireFlyClient.StartCardOnboarding(ctx, &ffBePb.StartCardOnboardingRequest{
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		Provenance:      ffEnumsBePb.Provenance_PROVENANCE_APP,
		CardNetworkType: feCardNetworkTypeToBeCardNetworkType[req.GetCardNetworkType()],
		CardProgram:     req.GetCardProgram(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in be rpc for starting card onboarding", zap.Error(te))
		// handle statuses for failed limit estimation
		if beRes.GetStatus().IsRecordNotFound() || beRes.GetStatus().IsResourceExhausted() {
			res.RespHeader = header2.BottomSheetErrResp(beRes.GetStatus(), "", "Failed to start on-boarding", "Limit exhausted", "")
		}
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
			ErrorView: &errors.ErrorView{
				Type: errors.ErrorViewType_BOTTOM_SHEET,
				// TODO(priyansh) : Check for error string and CTA from product
				Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{
					ErrorCode:        "",
					Title:            "",
					Subtitle:         "",
					Description:      "",
					Ctas:             nil,
					ScreenIdentifier: "",
				}},
			},
		}
		return res, nil
	}
	res.CardRequestId = beRes.GetCardRequestId()
	res.NextAction = beRes.GetNextAction()
	res.OfferId = beRes.GetOfferId()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	res.CreditCardHeader = s.GetCreditCardHeader(beRes.GetCardProgram())
	return res, nil
}

func (s *Service) startFiLiteCardOnboarding(ctx context.Context, req *ffPb.StartCardOnboardingRequest) (*ffPb.StartCardOnboardingResponse, error) {
	res := &ffPb.StartCardOnboardingResponse{}
	beRes, err := s.fireFlyClient.StartCardOnboardingV2(ctx, &ffBePb.StartCardOnboardingV2Request{
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		Provenance:      ffEnumsBePb.Provenance_PROVENANCE_APP,
		CardNetworkType: feCardNetworkTypeToBeCardNetworkType[req.GetCardNetworkType()],
		CardProgram:     req.GetCardProgram(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in be rpc for starting fi lite card onboarding", zap.Error(te))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
			ErrorView: &errors.ErrorView{
				Type:    errors.ErrorViewType_BOTTOM_SHEET,
				Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{}},
			},
		}
		return res, nil
	}
	res.CardRequestId = beRes.GetCardRequestId()
	res.NextAction = beRes.GetNextAction()
	res.OfferId = beRes.GetOfferId()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func (s *Service) GetLandingInfo(ctx context.Context, req *ffPb.GetLandingInfoRequest) (*ffPb.GetLandingInfoResponse, error) {
	var (
		res = &ffPb.GetLandingInfoResponse{RespHeader: &header.ResponseHeader{}}
	)
	beRes, err := s.fireFlyClient.GetLandingInfo(ctx, &ffBePb.GetLandingInfoRequest{
		ActorId:                 req.GetReq().GetAuth().GetActorId(),
		CreditCardRequestHeader: req.GetCreditCardRequestHeader(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in be rpc for getting landing details", zap.Error(te))
		res.RespHeader = getRpcInternalStatusResponseHeader()
		return res, nil
	}
	if beRes.GetIsCcSdkFlowEnabledForUser() {
		return s.getLandingInfoV2(ctx, req)
	}

	res.OfferId = beRes.GetOfferId()
	res.CardProgramType = beCardProgramTypeToFeCardProgramType[beRes.GetCardProgramType()]
	// in case the user sees the waitlist deeplink, we need to put that behind a version check since it is a recent
	// client side development
	res.CreditCardHeader = s.GetCreditCardHeader(beRes.GetCardProgram())
	if beRes.GetNextAction().GetScreen() == deeplink.Screen_IN_APP_WAITLIST_SCREEN ||
		beRes.GetNextAction().GetScreen() == deeplink.Screen_CARD_OFFERS_SCREEN {
		hasThresholdAppVersionForCc := true
		updateAppTitle := ""
		updateAppDescription := ""
		displayImage := ""

		switch beRes.GetCardProgram().GetCardProgramType() {
		case types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
			isWfEnabled, err := ffPkg.EvaluateConstraintsForWorkflow(ctx, s.dynamicConf.CreditCard(), ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_CARD_ONBOARDING)
			if err != nil {
				logger.Error(ctx, "Error in performing workflow constraints evaluation for", zap.String(logger.WORKFLOW, ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_CARD_ONBOARDING.String()), zap.Error(err))
				res.NextAction = helper.GetRetryableFailureDeeplink(&deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
				}, &types.CardProgram{
					CardProgramVendor:     types.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
					CardProgramSource:     types.CardProgramSource_CARD_PROGRAM_SOURCE_REALTIME_BRE,
					CardProgramType:       types.CardProgramType_CARD_PROGRAM_TYPE_SECURED,
					CardProgramCollateral: types.CardProgramCollateral_CARD_PROGRAM_COLLATERAL_FD,
					CardProgramOrigin:     types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI,
				})
				res.RespHeader = &header.ResponseHeader{
					Status: rpc.StatusOk(),
				}
				return res, nil
			}
			if !isWfEnabled {
				res.NextAction = s.getForceUpdateScreenNextAction(req.GetReq(), "We’ve rolled out some new fixes that are necessary to avail a credit card.")
				res.RespHeader = &header.ResponseHeader{
					Status: rpc.StatusOk(),
				}
				return res, nil
			}
		default:
		}

		var updateCta *deeplink.Cta
		switch req.GetReq().GetAuth().GetDevice().GetPlatform() {
		case commontypes.Platform_IOS:
			if beRes.GetNextAction().GetScreen() == deeplink.Screen_IN_APP_WAITLIST_SCREEN && req.GetReq().GetAuth().GetDevice().GetAppVersion() < s.dynamicConf.CreditCard().AppVersionSupport().MinIosVersionForCreditCard() {
				hasThresholdAppVersionForCc = false
				updateAppTitle = "Update your Fi app to continue"
				updateAppDescription = "We’ve rolled out some new fixes that are necessary to avail a credit card."
				updateCta = &deeplink.Cta{
					Text:     "Update Now",
					Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_UPDATE_APP_SCREEN},
				}
				displayImage = "https://epifi-icons.pointz.in/credit_card_images/ufo_ladder_image.png"
			}
			if beRes.GetNextAction().GetScreen() == deeplink.Screen_CARD_OFFERS_SCREEN && req.GetReq().GetAuth().GetDevice().GetAppVersion() < s.dynamicConf.CreditCard().AppVersionSupport().MinIosVersionForCreditCardIntroV2() {
				res.NextAction = helper.GetWaitlistIntroDeeplinkV2(beRes.GetCardProgram())
				res.RespHeader = &header.ResponseHeader{
					Status: rpc.StatusOk(),
				}
				return res, nil
			}
		case commontypes.Platform_ANDROID:
			if beRes.GetNextAction().GetScreen() == deeplink.Screen_IN_APP_WAITLIST_SCREEN && req.GetReq().GetAuth().GetDevice().GetAppVersion() < s.dynamicConf.CreditCard().AppVersionSupport().MinAndroidVersionForCreditCard() {
				hasThresholdAppVersionForCc = false
				updateAppTitle = "Update your Fi app to continue"
				updateAppDescription = "We’ve rolled out some new fixes that are necessary to avail a credit card. Please update your Fi app from the play store."
				displayImage = "https://epifi-icons.pointz.in/credit_card_images/ufo_ladder_image.png"
			}
			if beRes.GetNextAction().GetScreen() == deeplink.Screen_CARD_OFFERS_SCREEN && req.GetReq().GetAuth().GetDevice().GetAppVersion() < s.dynamicConf.CreditCard().AppVersionSupport().MinAndroidVersionForCreditCardIntroV2() {
				hasThresholdAppVersionForCc = false
				updateAppTitle = "Update your app to access a credit card you manifested!"
				updateAppDescription = "Get 5% valueback and unlock exclusive benefits."
				displayImage = "https://epifi-icons.pointz.in/credit_card_images/card_img2.png"
			}
		}
		if !hasThresholdAppVersionForCc {
			res.RespHeader.Status = rpc.StatusOk()
			res.NextAction = &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
					CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
						IconUrl: displayImage,
						Text:    updateAppTitle,
						SubText: updateAppDescription,
						Cta:     updateCta,
					},
				},
			}
			return res, nil
		}
	}

	res.NextAction = beRes.GetNextAction()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	if beRes.GetNextAction().GetScreen() == deeplink.Screen_CC_INELIGIBLE_USER_SCREEN {
		res.RespHeader.FeedbackEngineInfo = &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA,
				FlowIdentifier:     types.FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_CREDIT_CARD_USER_INELIGIBLE.String(),
			},
		}
	}
	return res, nil
}

func getRpcInternalStatusResponseHeader() *header.ResponseHeader {
	return &header.ResponseHeader{
		Status: rpc.StatusInternal(),
		ErrorView: &errors.ErrorView{
			Type:    errors.ErrorViewType_BOTTOM_SHEET,
			Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{}},
		},
	}
}

func (s *Service) GetCreditCardHeader(cardProgram *types.CardProgram) *ffPb.CreditCardHeader {
	return &ffPb.CreditCardHeader{
		CardProgram:           ffPkg.GetCardProgramStringFromCardProgram(ffPkg.GetCardProgramWithFallback(cardProgram)),
		CardProgramAttributes: ffPkg.GetCardProgramAttributes(ffPkg.GetCardProgramWithFallback(cardProgram)),
	}
}

func (s *Service) getLandingInfoV2(ctx context.Context, req *ffPb.GetLandingInfoRequest) (*ffPb.GetLandingInfoResponse, error) {
	var (
		res = &ffPb.GetLandingInfoResponse{RespHeader: &header.ResponseHeader{}}
	)
	landingInfoV2Resp, err := s.fireflyV2Client.GetLandingInfo(ctx, &ffBeV2Pb.GetLandingInfoRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})
	if te := epifigrpc.RPCError(landingInfoV2Resp, err); te != nil {
		logger.Error(ctx, "error in be rpc for getting landing v2 details", zap.Error(te))
		res.RespHeader = getRpcInternalStatusResponseHeader()
		return res, nil
	}
	res.NextAction = landingInfoV2Resp.GetNextAction()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func (s *Service) CollectCardDeliveryAddress(ctx context.Context, req *ffPb.CollectCardDeliveryAddressRequest) (*ffPb.CollectCardDeliveryAddressResponse, error) {
	var (
		res = &ffPb.CollectCardDeliveryAddressResponse{}
	)
	beRes, err := s.fireFlyClient.CollectCardDeliveryAddress(ctx, &ffBePb.CollectCardDeliveryAddressRequest{
		AddressType:   req.GetAddressType(),
		CardRequestId: req.GetCardRequestId(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in be rpc for collecting card delivery address", zap.Error(te))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
			ErrorView: &errors.ErrorView{
				Type: errors.ErrorViewType_BOTTOM_SHEET,
				// TODO(priyansh) : Check for error string and CTA from product
				Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{
					ErrorCode:        "",
					Title:            "",
					Subtitle:         "",
					Description:      "",
					Ctas:             nil,
					ScreenIdentifier: "",
				}},
			},
		}
		return res, nil
	}
	res.NextAction = beRes.GetNextAction()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func (s *Service) CreateCard(ctx context.Context, req *ffPb.CreateCardRequest) (*ffPb.CreateCardResponse, error) {
	// TODO(akk) - remove once onboarding restarts
	if !cfg.IsSimulatedEnv(s.dynamicConf.Application().Environment) {
		return &ffPb.CreateCardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusUnavailable(),
			},
		}, nil
	}
	consentResp, err := s.consentClient.RecordConsents(ctx, &consentPb.RecordConsentsRequest{
		Consents: []*consentPb.ConsentRequestInfo{
			{
				ConsentType: consentPb.ConsentType_FI_CREDIT_CARD_TNC,
			},
			{
				ConsentType: consentPb.ConsentType_FI_CREDIT_CARD_KFS,
			},
			{
				ConsentType: consentPb.ConsentType_FI_CREDIT_CARD_MOST_IMP_TNC,
			},
		},
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Device:  req.GetReq().GetAuth().GetDevice(),
		Owner:   commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(consentResp, err); te != nil {
		logger.Error(ctx, "error while calling RecordConsents", zap.Error(te))
		return &ffPb.CreateCardResponse{
			RespHeader: &header.ResponseHeader{
				Status:    consentResp.GetStatus(),
				ErrorView: genericErrorView,
			},
		}, nil
	}
	resp, err := s.fireFlyClient.CreateCard(ctx, &ffBePb.CreateCardRequest{
		CardReqeustId:          req.GetCardRequestId(),
		BillGenDate:            req.GetBillGenDate(),
		SelectedPaymentDueDate: req.GetPaymentDueDate(),
		SelectedBillGenDate:    req.GetBillGenDateInLong(),
		AddressType:            req.GetAddressType(),
	})
	if epifigrpc.RPCError(resp, err) != nil {
		return &ffPb.CreateCardResponse{
			NextAction: resp.GetNextAction(),
			RespHeader: &header.ResponseHeader{
				Status:    resp.GetStatus(),
				ErrorView: genericErrorView,
			},
		}, nil
	}
	return &ffPb.CreateCardResponse{
		NextAction: resp.GetNextAction(),
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		CreditCardHeader: s.GetCreditCardHeader(resp.GetCardProgram()),
	}, nil
}

// this function will return a list of Dashboard banners by calling the segmentation service to filter segment IDs
// The dashboard banner values will be picked from the config environment files
func (s *Service) getCCDashboardSegmentationCarousel(ctx context.Context, req *ffPb.GetDashboardRequest) []*deeplink.InfoItemWithCta {
	if s.dynamicConf.CreditCard().EnableDashboardSegmentationCarousels() {
		return nil
	}
	carousel := make([]*deeplink.InfoItemWithCta, 0)
	// add items to carousel according to segment
	ccDashboardSegmentIdToCarouselMap := s.dynamicConf.CreditCard().SegmentIdToCarouselObjectMap()
	segmentIDsList := make([]string, 0, len(ccDashboardSegmentIdToCarouselMap))
	for segmentID, segmentValues := range ccDashboardSegmentIdToCarouselMap {
		if startTime, expiryTime := segmentValues.StartTimestamp, segmentValues.ExpiryTimeStamp; (startTime.IsZero() ||
			startTime.Before(time.Now())) && (expiryTime.IsZero() || expiryTime.After(time.Now())) {
			segmentIDsList = append(segmentIDsList, segmentID)
		}
	}
	if len(segmentIDsList) == 0 {
		return nil
	}
	userSegmentRes, err := s.segmentationServiceClient.IsMember(ctx, &segment.IsMemberRequest{
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		SegmentIds: segmentIDsList,
	})

	switch {
	case err != nil:
		logger.Error(ctx, "error checking with Segmentation Service for actor's segment IDs", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
	case !userSegmentRes.GetStatus().IsSuccess():
		logger.Error(ctx, "failed invoking segment service for validation of segmentIds", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
	default:
		for segmentId := range userSegmentRes.GetSegmentMembershipMap() {
			if _, keyExists := ccDashboardSegmentIdToCarouselMap[segmentId]; !keyExists {
				logger.Error(ctx, "The segment ID does not exist", zap.String(logger.ID, segmentId))
				continue
			}
			if userSegmentRes.GetSegmentMembershipMap()[segmentId].GetSegmentStatus() == segment.SegmentStatus_SEGMENT_INSTANCE_FOUND {
				// TODO: initialise screen options here depending on the screen value. Since ScreenOptions is an interface & hence dynamic, it cannot be added in the conf
				carousel = append(carousel, &deeplink.InfoItemWithCta{
					Info: &deeplink.InfoItem{
						Title:       ccDashboardSegmentIdToCarouselMap[segmentId].InfoTitle,
						Icon:        ccDashboardSegmentIdToCarouselMap[segmentId].InfoIcon,
						Desc:        ccDashboardSegmentIdToCarouselMap[segmentId].InfoDescription,
						CopyAllowed: ccDashboardSegmentIdToCarouselMap[segmentId].InfoCopyAllowed,
						ToolTip: &deeplink.InfoToolTip{
							IconUrl: ccDashboardSegmentIdToCarouselMap[segmentId].InfoToolTipIconUrl,
							Info: &deeplink.InfoBlock{
								Title: ccDashboardSegmentIdToCarouselMap[segmentId].InfoToolTipInfoBlockTitle,
								Desc:  ccDashboardSegmentIdToCarouselMap[segmentId].InfoToolTipInfoBlockDescription,
							},
						},
					},
					Cta: &deeplink.Cta{
						Type: deeplink.Cta_Type(ccDashboardSegmentIdToCarouselMap[segmentId].CtaType),
						Text: ccDashboardSegmentIdToCarouselMap[segmentId].CtaText,
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen(ccDashboardSegmentIdToCarouselMap[segmentId].DeeplinkScreenName),
						},
					},
				})

			}
		}
	}
	return carousel
}

func (s *Service) GetDashboard(ctx context.Context, req *ffPb.GetDashboardRequest) (*ffPb.GetDashboardResponse, error) {
	var (
		res = &ffPb.GetDashboardResponse{RespHeader: &header.ResponseHeader{}}
	)
	beRes, err := s.fireFlyClient.GetDashboard(ctx, &ffBePb.GetDashboardRequest{ActorId: req.GetReq().GetAuth().GetActorId()})
	if epifigrpc.RPCError(beRes, err) != nil {
		logger.Error(ctx, "error in backend GetDashboard API", zap.Error(epifigrpc.RPCError(beRes, err)))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	var (
		billInfo            *ffPb.GetDashboardResponse_BillInfo
		cardDeliveryDetails *ffBePb.GetCardDeliveryDetailsResponse
		latestBill          *ffBeBillingPb.CreditCardBill
		cardResp            *ffBePb.GetCreditCardResponse
		cardInfo            *ffBePb.CreditCard
		ccOnboardingRequest *ffBePb.GetCardRequestByActorIdAndWorkflowResponse
		accountInfo         *ffBeAccountsPb.GetAccountResponse
		dueInfo             *ffBeAccountsPb.GetCreditAccountDueInformationResponse
		cardErr             error
		savingsAccountRes   *savingsPb.GetAccountResponse
		savingsErr          error
	)

	grp, grpCtx := errgroup.WithContext(ctx)

	grp.Go(func() error {
		// if the feature is enabled we will not send bill dashboard info v1, else we fetch details
		if pkggenconf.IsFeatureEnabledOnPlatform(grpCtx, s.dynamicConf.Flags().EnableCCBillDashboardV2FeatureFlag()) {
			cardResp, cardErr = s.fireFlyClient.GetCreditCard(grpCtx, &ffBePb.GetCreditCardRequest{
				GetBy: &ffBePb.GetCreditCardRequest_CreditCardId{CreditCardId: beRes.GetBasicInfo().GetCardId()},
			})
			if te := epifigrpc.RPCError(cardResp, cardErr); te != nil {
				return pkgErr.Wrap(te, "error in fetching card info")
			}
			cardInfo = cardResp.GetCreditCard()

			billResp, billErr := s.ffBillingClient.GetBillAndBillPaymentInfo(grpCtx, &ffBeBillingPb.GetBillAndBillPaymentInfoRequest{
				ActorId: req.GetReq().GetAuth().GetActorId(),
			})
			if te := epifigrpc.RPCError(billResp, billErr); te != nil {
				logger.Error(grpCtx, "error in fetching bill info", zap.Error(te))
				return nil
			}
			latestBill = billResp.GetLatestBill()
			return nil
		}
		var billInfoErr error
		billInfo, latestBill, cardInfo, dueInfo, billInfoErr = s.getDashboardBillingInfo(grpCtx, beRes.GetBasicInfo().GetCardId(), req.GetReq().GetAuth().GetActorId())
		if billInfoErr != nil {
			logger.Error(grpCtx, "error in fetching billing info for dashboard", zap.Error(billInfoErr), zap.String(logger.CARD_ID, beRes.GetBasicInfo().GetCardId()))
			// not returning a non success status to avoid billing info being a SPOF for
			// cc dashboard API
		}
		return nil
	})

	grp.Go(func() error {
		var cardDeliveryDetailsErr error
		cardDeliveryDetails, cardDeliveryDetailsErr = s.fireFlyClient.GetCardDeliveryDetails(grpCtx, &ffBePb.GetCardDeliveryDetailsRequest{
			CardId: beRes.GetBasicInfo().GetCardId(),
		})
		if te := epifigrpc.RPCError(cardDeliveryDetails, cardDeliveryDetailsErr); te != nil {
			logger.Error(grpCtx, "error in GetCardDeliveryDetails", zap.Error(te), zap.String(logger.CARD_ID, beRes.GetBasicInfo().GetCardId()))
			// not returning a non success status to avoid this being a SPOF for
			// cc dashboard API
		}
		return nil
	})

	grp.Go(func() error {
		ccOnboardingRequest, err = s.fireFlyClient.GetCardRequestByActorIdAndWorkflow(grpCtx, &ffBePb.GetCardRequestByActorIdAndWorkflowRequest{
			ActorId:             req.GetReq().GetAuth().GetActorId(),
			CardRequestWorkFlow: ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
		})
		if te := epifigrpc.RPCError(ccOnboardingRequest, err); te != nil {
			logger.Error(ctx, "error in fetching the cc onboarding request", zap.Error(te), zap.String(logger.CARD_ID, beRes.GetBasicInfo().GetCardId()))
			// not returning a non success status to avoid this being a SPOF for cc dashboard API
		}
		return nil
	})

	grp.Go(func() error {
		savingsAccountRes, savingsErr = s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorId{
				ActorId: req.GetReq().GetAuth().GetActorId(),
			},
		})
		if savingsErr != nil {
			logger.Info(ctx, "error in fetching savings account for the user", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		}
		return nil
	})

	if err = grp.Wait(); err != nil {
		return nil, err
	}

	accountInfo, accErr := s.fireflyAccountingClient.GetAccount(ctx, &ffBeAccountsPb.GetAccountRequest{
		GetBy: &ffAccPb.GetAccountRequest_AccountId{AccountId: cardInfo.GetAccountId()},
	})
	if te := epifigrpc.RPCError(accountInfo, accErr); te != nil {
		logger.Error(ctx, pkgErr.Wrap(te, "error in fetching card account info").Error())
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	var additionalInfo *deeplink.InfoItemWithCta
	carousel := make([]*deeplink.InfoItemWithCta, 0)

	// add 0-forex charges banner for amplifi to the carousel
	// figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=45639-32026&t=yGdeaPZkeSyU0KJ9-4
	if accountInfo.GetAccount().GetCardProgram().GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED && s.dynamicConf.CreditCard().ShowAmplifiZeroForexBanner() {
		carousel = append(carousel, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/money-globe.png",
				Title: "Hassle free 0% Forex Fees on Credit Card transactions!",
			},
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_STORY_SCREEN,
					ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
						StoryScreenOptions: &deeplink.StoryScreenOptions{
							StoryUrl: "https://stories.fi.money/forex-fee-amplifi",
						},
					},
				},
			},
		})
	}

	carousel = append(carousel, s.getRenewalFeeWaiverCarousel(ctx, accountInfo.GetAccount().GetActorId(), latestBill)...)
	if beRes.GetCarousel() != nil {
		carousel = append(carousel, beRes.GetCarousel()...)
	}

	if ccOnboardingRequest.GetCardRequest().GetRequestDetails().GetSelectedRewardId() == "" &&
		accountInfo.GetAccount().GetCardProgram().GetCardProgramType() != types.CardProgramType_CARD_PROGRAM_TYPE_SECURED &&
		// adding below condition in order to avoid showing this tab to amplifi users who had onboarded pre welcome offers integration
		accountInfo.GetAccount().GetCardProgram().GetCardProgramOrigin() == types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE {
		carousel = append(carousel, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/welcome_offers_claim_img.png",
				Title: "Your welcome gift is ready for you! Claim it now",
				Desc:  "Your welcome gift is ready for you! Claim it now",
			},
			Cta: &deeplink.Cta{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_REWARDS_POLLING_SCREEN,
					ScreenOptions: &deeplink.Deeplink_CardRewardsPollingScreenOptions{
						CardRewardsPollingScreenOptions: &deeplink.CreditCardRewardsPollingScreenOptions{
							RewardOfferType: ffEnumsPb.CCRewardOfferType_CC_REWARD_OFFER_TYPE_WELCOME,
							DisplayMessage:  "Loading exclusive welcome offers!",
						}},
				},
			},
		})
	}

	noAdditionalInfoCardStates := []ffEnumsBePb.CardState{
		ffEnumsBePb.CardState_CARD_STATE_ACTIVATED,
		ffEnumsBePb.CardState_CARD_STATE_SUSPENDED,
		ffEnumsBePb.CardState_CARD_STATE_BLOCKED,
		ffEnumsBePb.CardState_CARD_STATE_CLOSED,
	}
	switch {
	case lo.Contains(noAdditionalInfoCardStates, beRes.GetBasicInfo().GetCardState()):
		additionalInfo = nil
	case cardDeliveryDetails.GetPhysicalCardStatus() == ffEnumsBePb.PhysicalCardStatus_PHYSICAL_CARD_STATUS_DELIVERED:
		additionalInfo = getCardActivationTile(beRes.GetBasicInfo().GetCardId())
		carousel = append(carousel, additionalInfo)
	default:
		additionalInfo = getCardTrackingTile(beRes.GetBasicInfo().GetCardId())
		activationTile := getCardActivationTile(beRes.GetBasicInfo().GetCardId())
		carousel = append(carousel, additionalInfo, activationTile)
	}

	// No reward carousel in case of card closure
	if beRes.GetBasicInfo().GetCardState() != ffEnumsBePb.CardState_CARD_STATE_CLOSED {
		rewardResp, err := s.rewardsClient.GetRewardsByRewardId(ctx, &rewardsPb.RewardsByRewardIdRequest{
			RewardId: ccOnboardingRequest.GetCardRequest().GetRequestDetails().GetSelectedRewardId()})
		rewardFetchErr := epifigrpc.RPCError(rewardResp, err)
		switch {
		// skip adding welcome offer carousel if there is failure in getting reward status
		case rewardFetchErr != nil:
			logger.Error(ctx, "failed to get rewards by reward id from rewards client", zap.Error(rewardFetchErr), zap.String(logger.REWARD_ID, ccOnboardingRequest.GetCardRequest().GetRequestDetails().GetSelectedRewardId()))
		default:
			selectedRewardType := ccOnboardingRequest.GetCardRequest().GetRequestDetails().GetSelectedRewardType()
			switch {
			// if welcome offer is claimed successfully
			case rewardResp.GetReward().GetStatus() == rewardsPb.RewardStatus_PROCESSED:
				welcomeOfferImage, ok := ccRewardTypeToUnlockedRewardIcon[selectedRewardType]
				if ok {
					carousel = append(carousel, &deeplink.InfoItemWithCta{
						Info: &deeplink.InfoItem{
							Title: ccRewardTypeToUnlockedRewardText[selectedRewardType],
							Icon:  welcomeOfferImage,
						},
						Cta: &deeplink.Cta{
							Deeplink: ccRewardTypeToUnlockedRewardDeeplink[selectedRewardType],
						},
					})
				}

			default:
				welcomeOfferImage, ok := ccRewardTypeToLockedRewardIcon[selectedRewardType]
				if ok && ccOnboardingRequest.GetCardRequest().GetRequestDetails().GetSelectedRewardId() != "" {
					carousel = append(carousel, &deeplink.InfoItemWithCta{
						Info: &deeplink.InfoItem{
							Title: "Pay the minimum amount due of the first bill to unlock your welcome offer.",
							Icon:  welcomeOfferImage,
						},
					})
				}
			}
		}
	}

	carousel = append(carousel, s.getCCOtherWaysToPayCarousel())
	// add dashboard CC banners based on segment IDs
	carousel = append(carousel, s.getCCDashboardSegmentationCarousel(ctx, req)...)

	res = &ffPb.GetDashboardResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		BasicInfo: &ffPb.GetDashboardResponse_BasicInfo{
			CardId:             beRes.GetBasicInfo().GetCardId(),
			OutstandingBalance: beRes.GetBasicInfo().GetOutstandingBalance(),
			AvailableBalance:   beRes.GetBasicInfo().GetAvailableBalance(),
			TotalBalance:       beRes.GetBasicInfo().GetTotalBalance(),
			CardDetailsCta: &deeplink.Cta{
				Text: "View card details",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_DETAILS_SCREEN,
					ScreenOptions: &deeplink.Deeplink_CreditCardDetailsScreenOptions{
						CreditCardDetailsScreenOptions: &deeplink.CreditCardDetailsScreenOptions{
							RewardsCardTypeId: cardProgramTypeToRewardCardTypeMap[accountInfo.GetAccount().GetCardProgram().GetCardProgramType()],
						}},
				},
			},
		},
		BillInfo:           billInfo,
		BottomInfoList:     s.getBottomInfoList(ctx, beRes, latestBill, cardInfo, accountInfo, dueInfo, req.GetReq().GetAuth().GetDevice().GetPlatform(), savingsAccountRes),
		TopNote:            nil,
		CardAdditionalInfo: additionalInfo,
		Carousel:           carousel,
		PartnershipIconUrl: "https://epifi-icons.pointz.in/credit_card_images/PartnerIcon.png",
		PageBackground: commontypes.GetVisualElementFromUrlHeightAndWidth(
			dcCcBgImage,
			572,
			412,
		),
	}

	res.CardVisualElementHalf, res.CardVisualElementFull = feHelper.GetCardImageByCardProgram(accountInfo.GetAccount().GetCardProgram())
	res.FallBackgroundColour = feHelper.GetFallBackgroundColourForCcImage(accountInfo.GetAccount().GetCardProgram())
	return res, nil
}

// constructs the BottomInfoList
func (s *Service) getBottomInfoList(ctx context.Context, beRes *ffBePb.GetDashboardResponse, latestBill *ffBeBillingPb.CreditCardBill, cardInfo *ffBePb.CreditCard, accountInfo *ffBeAccountsPb.GetAccountResponse, dueInfo *ffBeAccountsPb.GetCreditAccountDueInformationResponse, platform commontypes.Platform, savingsAccountRes *savingsPb.GetAccountResponse) []*deeplink.InfoItemWithCta {
	var bottomInfoList []*deeplink.InfoItemWithCta
	// Get statements row
	if beRes.GetBasicInfo().GetCardState() != ffEnumsBePb.CardState_CARD_STATE_CLOSED {
		viewStatementDeeplink := s.getViewStatementDeeplinkFromBillingInfo(latestBill, beRes.GetBasicInfo().GetCardId(), s.getNextBillGenerationDate(cardInfo))
		bottomInfoList = append(bottomInfoList, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  internal.StatementDashboardIcon,
				Title: "Get Statements",
				Desc:  "View your monthly statements",
			},
			Cta: &deeplink.Cta{
				Deeplink: viewStatementDeeplink,
			},
		})
	}

	// billing details
	if beRes.GetBasicInfo().GetCardState() != ffEnumsBePb.CardState_CARD_STATE_CLOSED {
		editVisualElement, vsErr := s.getBillingDateEditVisualElement(ctx, accountInfo.GetAccount(), cardInfo, dueInfo)
		if vsErr != nil {
			logger.Error(ctx, "error in fetching billing date edit cta on dashboard", zap.Error(vsErr), zap.String(logger.CARD_ID, cardInfo.GetId()))
		}
		bottomInfoList = append(bottomInfoList, getBillGenDateUpdateDeeplink(editVisualElement, cardInfo))
	}

	// CC controls
	if beRes.GetBasicInfo().GetCardState() != ffEnumsBePb.CardState_CARD_STATE_CLOSED {
		bottomInfoList = append(bottomInfoList, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/card+controls+icon.png",
				Title: "Credit Card controls",
				Desc:  "Manage your card, set limits, etc.",
			},
			Cta: &deeplink.Cta{
				Deeplink: helper.GetCreditCardControlsScreenDeeplink(beRes.GetBasicInfo().GetCardId()),
			},
		})
	}

	// FD or rewards row depending upon card type
	if accountInfo.GetAccount().GetCardProgram().GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED {
		bottomInfoList = append(bottomInfoList, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  internal.FiFixedDepositIconUrl,
				Title: "Fixed Deposit",
				Desc:  "View your Credit Card Deposit details",
			},
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_DEPOSIT_ACCOUNT_DETAILS,
					ScreenOptions: &deeplink.Deeplink_DepositDetailsScreenOptions{
						DepositDetailsScreenOptions: &deeplink.DepositAccountDetailsScreenOptions{
							AccountId:   accountInfo.GetAccount().GetCollateralDetails().GetCollateralId(),
							DepositType: accounts.Type_FIXED_DEPOSIT,
						},
					},
				},
			},
		})
	} else {
		bottomInfoList = append(bottomInfoList, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  internal.FiDashboardRewardsIcon,
				Title: "Rewards",
				Desc:  "View all your Fi-Coin rewards in one place",
			},
			Cta: helper.GetMyRewardsCta(),
		})
	}

	// CC details and benefits screen entry point
	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().EnableCcDetailsAndBenefitsFeatureFlag()) &&
		accountInfo.GetAccount().GetCardProgram().GetCardProgramType() != types.CardProgramType_CARD_PROGRAM_TYPE_SECURED {
		screenOptionV2, err := deeplinkv3.GetScreenOptionV2(&ffScreenTypes.CreditCardDetailsAndBenefitsScreenOptions{})
		// if no err, populate below deeplink
		// empty screen option V2 is needed to avoid crashes at Android client
		if err == nil {
			if !pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().EnableUnsecuredNewCvpCcDetailsAndBenefitsFeatureFlag()) {
				bottomInfoList = append(bottomInfoList, &deeplink.InfoItemWithCta{
					Info: &deeplink.InfoItem{
						Icon:  internal.CreditCardIconUrl,
						Title: "Card details & benefits",
						Desc:  "View your credit limit, fees and benefits",
					},
					Cta: &deeplink.Cta{
						Deeplink: &deeplink.Deeplink{
							Screen:          deeplink.Screen_CREDIT_CARD_DETAILS_AND_BENEFITS_SCREEN,
							ScreenOptionsV2: screenOptionV2,
						},
					},
				})
			}
		}
	}

	// EMI conversion dashboard entry point
	if beRes.GetFeatureFlags().GetEnableEmiConversion() {
		bottomInfoList = append(bottomInfoList, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  internal.EmiDashboardIcon,
				Title: "Your EMIs",
				Desc:  "Manage your monthly payments",
			},
			Cta: &deeplink.Cta{
				Deeplink: helper.GetYourEmiScreenDeeplink(),
			},
		})
	}

	switch {
	case pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().EnableLoungeAccessV2FeatureFlag()) &&
		ffPkg.IsUnsecuredCardProgram(accountInfo.GetAccount().GetCardProgram()):
		bottomInfoList = append(bottomInfoList, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/lounge_access_icon.png",
				Title: "Lounge access",
				Desc:  "Get lounge access once every quarter",
			},
			Cta: &deeplink.Cta{
				Type:     deeplink.Cta_CUSTOM,
				Deeplink: s.getLoungeAccessV2Screen(ctx, cardInfo.GetActorId()),
			},
		})
	case pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().EnableCcLoungeAccessFeatureFlag()) &&
		ffPkg.IsUnsecuredCardProgram(accountInfo.GetAccount().GetCardProgram()):
		bottomInfoList = append(bottomInfoList, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/lounge_access_icon.png",
				Title: "Lounge access",
				Desc:  "Get lounge access once every quarter",
			},
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_LOUNGE_ACCESS_SCREEN,
				},
			},
		})
	default:
	}

	// Custom reminders dashboard entry point. Disabled for Fi lite temporarily
	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().EnableCcCustomRemindersFeatureFlag()) && savingsAccountRes.GetAccount() != nil && beRes.GetBasicInfo().GetCardState() != ffEnumsBePb.CardState_CARD_STATE_CLOSED {
		bottomInfoList = append(bottomInfoList, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Icon:  internal.BellReminderDashboardIconUrl,
				Title: "Custom reminders",
				Desc:  "Get reminded to pay your bill on a specific day",
			},
			Cta: &deeplink.Cta{
				Deeplink: helper.GetCustomReminderDeeplink(),
			},
		})
	}

	return bottomInfoList
}

func (s *Service) getCCOtherWaysToPayCarousel() *deeplink.InfoItemWithCta {
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/two_cards.png",
			Title: "3 convenient ways to pay your Credit Card bill",
		},
		Cta: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_STORY_SCREEN,
				ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
					StoryScreenOptions: &deeplink.StoryScreenOptions{
						StoryUrl: "https://storifyme.com/stories/g-prateek-gauba-22176/164351/preview",
					},
				},
			},
		},
	}
}

// getRenewalFeeWaiverCarousel generates a carousel of info items with call-to-action links for renewal fee waiver scenarios.
func (s *Service) getRenewalFeeWaiverCarousel(ctx context.Context, actorId string, latestBill *ffBeBillingPb.CreditCardBill) []*deeplink.InfoItemWithCta {
	var (
		res                        = make([]*deeplink.InfoItemWithCta, 0)
		defaulterUserCarouselTitle string
	)

	// Check if renewal fee waiver carousels are enabled in dynamic configuration.
	if !s.dynamicConf.CreditCard().EnableRenewalFeeWaiverCarousels() {
		return res
	}

	// Retrieve card request information from the FireFly client.
	cardReqResponse, err := s.fireFlyClient.GetCardRequestByActorIdAndWorkflow(ctx, &ffBePb.GetCardRequestByActorIdAndWorkflowRequest{
		ActorId:             actorId,
		CardRequestWorkFlow: ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_CARD_FEE_WAIVER,
	})

	// Handle errors from the FireFly client.
	switch {
	case cardReqResponse == nil:
		return res
	case cardReqResponse.GetStatus().IsRecordNotFound():
		return res
	case epifigrpc.RPCError(cardReqResponse, err) != nil:
		// not blocking the call
		logger.Error(ctx, "error getting card request for renewal fee waiver carousel",
			zap.Error(epifigrpc.RPCError(cardReqResponse, err)), zap.String(logger.ACTOR_ID_V2, actorId))
		return res
	}

	if latestBill != nil {
		// Get the day of the month in two digits format (e.g., "07").
		dayOfMonth := latestBill.GetSoftDueDate().AsTime().Format("02")
		// Get the month in long form (e.g., "January").
		month := latestBill.GetSoftDueDate().AsTime().Format("January")
		// Get the year in four digits format (e.g., "2000").
		year := latestBill.GetSoftDueDate().AsTime().Format("2006")

		// Combine the components to form the formatted date string.
		formattedDueDate := fmt.Sprintf("%s %s %s", dayOfMonth, month, year)

		defaulterUserCarouselTitle = fmt.Sprintf("A card renewal fee of ₹2,000+ GST will be charged on %v. Know more", formattedDueDate)
	} else {
		defaulterUserCarouselTitle = "A card renewal fee of ₹2,000+ GST will be charged on due date. Know more"
	}

	// Calculate days since the last action on the card request.
	updateTime := cardReqResponse.GetCardRequest().GetUpdatedAt().AsTime()
	daysPastAction := math.Floor(time.Since(updateTime).Hours() / 24)

	// Determine the appropriate response based on the card request status and fee waiver type.
	switch {
	case cardReqResponse.GetCardRequest().GetStatus() == ffEnumsBePb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS &&
		cardReqResponse.GetCardRequest().GetRequestDetails().GetRenewalFeeWaiverRequestDetails().GetRenewalFeeWaiverType() ==
			ffEnumsBePb.FeeWaiverType_FEE_WAIVER_TYPE_THRIVE_VOUCHERS:
		// Add info item with CTA for successful gift card unlock.
		res = append(res, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Title: "Renewal vouchers worth 4250 unlocked! Redeem now",
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/renewal_fee_waiver_voucher_ready.png",
			},
			Cta: &deeplink.Cta{
				Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_REDEEMED_OFFERS_SCREEN},
			},
		})
	case cardReqResponse.GetCardRequest().GetStatus() == ffEnumsBePb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS &&
		cardReqResponse.GetCardRequest().GetRequestDetails().GetRenewalFeeWaiverRequestDetails().GetRenewalFeeWaiverType() ==
			ffEnumsBePb.FeeWaiverType_FEE_WAIVER_TYPE_THRIVE_VOUCHERS:
		// Add info item with CTA for ongoing gift card unlock.
		res = append(res, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Title: "Unlock renewal vouchers worth 4250 when you pay the minimum due!",
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/renewal_fee_waiver_voucher.png",
			},
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_STORY_SCREEN,
					ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
						StoryScreenOptions: &deeplink.StoryScreenOptions{
							StoryUrl: "https://stories.fi.money/renewal-gift-cards",
						},
					},
				},
			},
		})
	case cardReqResponse.GetCardRequest().GetStatus() == ffEnumsBePb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED && daysPastAction <= 20:
		// Add info item with CTA for failed renewal fee waiver.
		res = append(res, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Title: defaulterUserCarouselTitle,
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/renewal_fee_waiver_no_waive.png",
			},
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_STORY_SCREEN,
					ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
						StoryScreenOptions: &deeplink.StoryScreenOptions{
							StoryUrl: "https://stories.fi.money/renewal-fee-charge-dpd-30-plus",
						},
					},
				},
			},
		})
	case cardReqResponse.GetCardRequest().GetStatus() == ffEnumsBePb.CardRequestStatus_CARD_REQUEST_STATUS_SUCCESS &&
		cardReqResponse.GetCardRequest().GetRequestDetails().GetRenewalFeeWaiverRequestDetails().GetRenewalFeeWaiverType() ==
			ffEnumsBePb.FeeWaiverType_FEE_WAIVER_TYPE_FEE_REVERSAL && daysPastAction <= 20:
		// Add info item with CTA for successful renewal fee reversal.
		res = append(res, &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Title: "We're refunding your renewal fee of ₹2,360! Know more",
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/renewal_fee_waiver_reversal.png",
			},
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_STORY_SCREEN,
					ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
						StoryScreenOptions: &deeplink.StoryScreenOptions{
							StoryUrl: "https://stories.fi.money/renewal-fee-waiver",
						},
					},
				},
			},
		})
	default:
		// No action needed for other cases.
	}

	return res
}

// getBillingDateEditVisualElement checks for the completion of the bill gen date selection activity and returns the edit CTA if
// the activity has not been done yet, else null
func (s *Service) getBillingDateEditVisualElement(ctx context.Context, accountInfo *ffAccPb.CreditAccount, card *ffBePb.CreditCard, dueInfo *ffBeAccountsPb.GetCreditAccountDueInformationResponse) (*deeplink.VisualElementCta, error) {
	// if positive outstanding, do not allow bill gen date change
	if moneyPb.IsPositive(dueInfo.GetTotalOutstandingAmount()) {
		return nil, nil
	}

	doOnceTaskId := helper.GetStatementUpdateDoOnceTaskId(accountInfo.GetId())
	doOnceRes, err := s.fireFlyClient.CheckDoOnceActivityStatus(ctx, &ffBePb.CheckDoOnceActivityStatusRequest{
		DoOnceTaskId: doOnceTaskId,
	})
	if te := epifigrpc.RPCError(doOnceRes, err); te != nil {
		return nil, pkgErr.Wrap(te, "error in checking do once activity status")
	}
	if doOnceRes.GetIsTaskDone() {
		return nil, nil
	}

	return &deeplink.VisualElementCta{
		Icon: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/edit.png"},
					Properties: &commontypes.VisualElementProperties{
						Width:  24,
						Height: 24,
					},
					ImageType: commontypes.ImageType_PNG,
					RenderingType: &commontypes.ImageRenderingType{
						Type: &commontypes.ImageRenderingType_RegularImage{
							RegularImage: &commontypes.RegularImage{},
						},
					},
				},
			},
		},
		Deeplink: helper.GetBillGenBottomSheetDeeplink(card.GetId(), true),
	}, nil
}

// getLoungeAccessV2Screen fetches the lounge deeplink by checking if the user already has active lounge access
func (s *Service) getLoungeAccessV2Screen(ctx context.Context, actorId string) *deeplink.Deeplink {
	loungePassRes, err := s.fireFlyClient.GetLoungePassesForUser(ctx, &ffBePb.GetLoungePassesForUserRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(loungePassRes, err); te != nil {
		logger.Error(ctx, "error in fetching lounge passes for the user", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		return &deeplink.Deeplink{Screen: deeplink.Screen_CC_LOUNGE_ACCESS_V2_SCREEN}
	}
	doesActivePassExist := false
	for _, pass := range loungePassRes.GetLoungePasses() {
		if datetime.IsAfter(pass.GetExpirationTime(), timestamppb.Now()) {
			doesActivePassExist = true
			break
		}
	}
	if doesActivePassExist {
		return &deeplink.Deeplink{Screen: deeplink.Screen_CC_COLLECTED_LOUNGE_PASSES_SCREEN}
	}
	return &deeplink.Deeplink{Screen: deeplink.Screen_CC_LOUNGE_ACCESS_V2_SCREEN}
}

func (s *Service) getDashboardBillingInfo(ctx context.Context, cardId, actorId string) (*ffPb.GetDashboardResponse_BillInfo, *ffBeBillingPb.CreditCardBill, *ffBePb.CreditCard, *ffBeAccountsPb.GetCreditAccountDueInformationResponse, error) {
	var (
		billInfo                        = &ffBeBillingPb.GetBillAndBillPaymentInfoResponse{}
		accountInfo                     = &ffBeAccountsPb.GetAccountResponse{}
		cardInfo                        = &ffBePb.GetCreditCardResponse{}
		dueInfo                         = &ffBeAccountsPb.GetCreditAccountDueInformationResponse{}
		limitInfo                       = &ffBeAccountsPb.GetCreditAccountLimitUtilisationResponse{}
		cardRequestInfo                 = &ffBePb.GetCardRequestByActorIdAndWorkflowResponse{}
		res                             = &ffPb.GetDashboardResponse_BillInfo{}
		cardErr, billErr, err           error
		accErr, fetchDueErr             error
		fetchLimitErr, cardRequestError error
	)

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		billInfo, billErr = s.ffBillingClient.GetBillAndBillPaymentInfo(grpCtx, &ffBeBillingPb.GetBillAndBillPaymentInfoRequest{
			ActorId: actorId,
		})
		if te := epifigrpc.RPCError(billInfo, billErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching bill info")
		}
		return nil
	})
	grp.Go(func() error {
		cardInfo, cardErr = s.fireFlyClient.GetCreditCard(grpCtx, &ffBePb.GetCreditCardRequest{
			GetBy: &ffBePb.GetCreditCardRequest_CreditCardId{CreditCardId: cardId},
		})
		if te := epifigrpc.RPCError(cardInfo, cardErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching card info")
		}
		accountInfo, accErr = s.fireflyAccountingClient.GetAccount(ctx, &ffBeAccountsPb.GetAccountRequest{
			GetBy: &ffBeAccountsPb.GetAccountRequest_AccountId{AccountId: cardInfo.GetCreditCard().GetAccountId()},
		})
		if te := epifigrpc.RPCError(accountInfo, accErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching account info for the user")
		}
		return nil
	})
	grp.Go(func() error {
		cardRequestInfo, cardRequestError = s.fireFlyClient.GetCardRequestByActorIdAndWorkflow(ctx, &ffBePb.GetCardRequestByActorIdAndWorkflowRequest{
			ActorId:             actorId,
			CardRequestWorkFlow: ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_PAYMENT,
		})
		if te := epifigrpc.RPCError(cardRequestInfo, cardRequestError); te != nil {
			if cardRequestInfo.GetStatus().IsRecordNotFound() {
				return nil
			}
			return pkgErr.Wrap(te, "error in fetching card request for the user")
		}
		return nil
	})
	if err = grp.Wait(); err != nil {
		return nil, nil, nil, nil, err
	}

	grp, grpCtx = errgroup.WithContext(ctx)
	grp.Go(func() error {
		dueInfo, fetchDueErr = s.fireflyAccountingClient.GetCreditAccountDueInformation(ctx, &ffBeAccountsPb.GetCreditAccountDueInformationRequest{
			GetBy: &ffBeAccountsPb.GetCreditAccountDueInformationRequest_ReferenceId{ReferenceId: accountInfo.GetAccount().GetReferenceId()},
		})
		if te := epifigrpc.RPCError(dueInfo, fetchDueErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching due info for the user")
		}
		return nil
	})
	grp.Go(func() error {
		limitInfo, fetchLimitErr = s.fireflyAccountingClient.GetCreditAccountLimitUtilisation(ctx, &ffBeAccountsPb.GetCreditAccountLimitUtilisationRequest{
			GetBy: &ffBeAccountsPb.GetCreditAccountLimitUtilisationRequest_ReferenceId{ReferenceId: accountInfo.GetAccount().GetReferenceId()},
		})
		if te := epifigrpc.RPCError(limitInfo, fetchLimitErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching limit info for the user")
		}
		return nil
	})
	if err = grp.Wait(); err != nil {
		return nil, nil, nil, nil, err
	}
	switch {
	// case where the last payment is still in progress
	case cardRequestInfo.GetCardRequest() != nil && cardRequestInfo.GetCardRequest().GetStatus() == ffEnumsBePb.CardRequestStatus_CARD_REQUEST_STATUS_IN_PROGRESS:
		res, err = s.fetchPaymentStatusResponse(ffEnumsBePb.TransactionStatus_IN_PROCESS)
	case len(billInfo.GetPayments()) > 0 && billInfo.GetPayments()[0].GetStatus() == ffEnumsBePb.TransactionStatus_IN_PROCESS:
		res, err = s.fetchPaymentStatusResponse(billInfo.GetPayments()[0].GetStatus())
	case len(billInfo.GetPayments()) > 0 && billInfo.GetPayments()[0].GetStatus() == ffEnumsBePb.TransactionStatus_PAYMENT_FAILURE &&
		billInfo.GetPayments()[0].GetSubStatus() == ffEnumsBePb.TransactionSubStatus_TRANSACTION_SUB_STATUS_FAILURE_AT_VENDOR:
		res, err = s.fetchPaymentStatusResponse(billInfo.GetPayments()[0].GetStatus())
	case billInfo.GetLatestBill() == nil:
		if moneyPb.IsPositive(limitInfo.GetLimitUtilized()) {
			res, err = s.fetchNonZeroUnbilledAmountResponse(limitInfo.GetLimitUtilized(), cardId)
		} else {
			res, err = s.fetchNoOutstandingResponse(billInfo, cardInfo.GetCreditCard())
		}
	case moneyPb.IsPositive(dueInfo.GetUnpaidTotalDue()):
		res, err = s.fetchBilledAmountResponse(billInfo, dueInfo.GetTotalDueAmount(), dueInfo.GetUnpaidTotalDue(), cardId)
	case moneyPb.IsPositive(limitInfo.GetLimitUtilized()):
		res, err = s.fetchNonZeroUnbilledAmountResponse(limitInfo.GetLimitUtilized(), cardId)
	case billInfo.GetLatestBill() != nil && moneyPb.IsZero(limitInfo.GetLimitUtilized()):
		res, err = s.fetchZeroBillGeneratedResponse()
	default:
		res, err = s.fetchNoOutstandingResponse(billInfo, cardInfo.GetCreditCard())
	}
	return res, billInfo.GetLatestBill(), cardInfo.GetCreditCard(), dueInfo, err
}

func (s *Service) fetchZeroBillGeneratedResponse() (*ffPb.GetDashboardResponse_BillInfo, error) {
	return &ffPb.GetDashboardResponse_BillInfo{
		BannerText: &commontypes.Text{
			BgColor:      internal.BillInfoDashboardGreen,
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Total amount due"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_2},
			FontColor:    internal.BillDashboardFontColorDarkGrey,
		},
		BannerSubText: &commontypes.Text{
			BgColor:      internal.BillInfoDashboardGreen,
			DisplayValue: &commontypes.Text_PlainString{PlainString: "₹0"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
		},
		BannerIcon: &ffPb.GetDashboardResponse_BillInfo_ImgUrl{ImgUrl: internal.BlueNoteIconUrl},
	}, nil
}

// nolint:unparam
func (s *Service) fetchNonZeroUnbilledAmountResponse(amount *money.Money, cardId string) (*ffPb.GetDashboardResponse_BillInfo, error) {
	bgColor := internal.BillInfoDashboardGreen
	text := &commontypes.Text{
		BgColor:      bgColor,
		DisplayValue: &commontypes.Text_PlainString{PlainString: "Limit Utilised"},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_2},
		FontColor:    internal.BillDashboardFontColorDarkGrey,
	}
	subText := &commontypes.Text{
		BgColor:      bgColor,
		DisplayValue: &commontypes.Text_PlainString{PlainString: moneyPb.ToDisplayString(amount)},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
	}
	return &ffPb.GetDashboardResponse_BillInfo{
		BannerText:    text,
		BannerSubText: subText,
		ActionCta: &deeplink.Cta{
			Text: "Pay early",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_BILL_REPAYMENT_SELECTION_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CreditCardBillRepaymentSelectionScreenOptions{
					CreditCardBillRepaymentSelectionScreenOptions: &deeplink.CreditCardBillRepaymentSelectionScreenOptions{
						CardId:       cardId,
						BgColorLight: shadeVsTint[bgColor],
						BgColorDark:  bgColor,
					},
				},
			},
		},
		BannerIcon: &ffPb.GetDashboardResponse_BillInfo_ImgUrl{
			ImgUrl: internal.GreenNoteIconUrl,
		},
	}, nil
}

// nolint:funlen,unparam
func (s *Service) fetchBilledAmountResponse(billInfo *ffBeBillingPb.GetBillAndBillPaymentInfoResponse, totalDueAmount *money.Money, totalUnpaidAmount *money.Money, cardId string) (*ffPb.GetDashboardResponse_BillInfo, error) {
	textVal := "Total amount due"
	subTextVal := moneyPb.ToDisplayString(totalUnpaidAmount)
	bgColor := internal.BillInfoDashboardYellow
	// we need to make eod of due date instead of beginning of due date.
	endOfDueDate := datetime.EndOfDay(billInfo.GetLatestBill().GetSoftDueDate().AsTime())
	diff := endOfDueDate.Sub(timestamppb.Now().AsTime())
	absDayDiff := int32(math.Ceil(math.Abs(diff.Hours()) / 24))
	beforeDueString := "Days left"
	afterDueString := "Days ago"
	if absDayDiff == 1 {
		beforeDueString = "Day left"
		afterDueString = "Day ago"
	}
	bannerIconText := beforeDueString
	showCta := true

	switch {
	// In case the user has made a non-zero payment, the banner won't turn red and
	// the due amount will be labelled as "Remaining due"
	// also, the LHS timer will show "Day(s) Ago" instead of "Day(s) left"
	case moneyPb.Compare(totalDueAmount, totalUnpaidAmount) != 0 && diff.Hours() < 0:
		// if partial due is paid, the banner won't turn red
		bannerIconText = afterDueString
		textVal = internal.RemainingDue
		// In case the user has made a non-zero payment, the banner won't turn red and
		// the due amount will be labelled as "Remaining due"
	case moneyPb.Compare(totalDueAmount, totalUnpaidAmount) != 0:
		textVal = internal.RemainingDue
		// In case the due date has passed, the banner will turn red and the Text will
		// show "Day(s) Ago" instead of "Day(s) left"
	case diff.Hours() < 0:
		bgColor = internal.BillInfoDashboardRed
		bannerIconText = afterDueString
		// In case the payment date has not passed but the due date is less than 3 days away,
		// the banner will turn red .
	case diff.Hours() <= 72:
		bgColor = internal.BillInfoDashboardRed
	}
	var cta *deeplink.Cta
	if showCta {
		cta = &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Pay Now",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_BILL_REPAYMENT_SELECTION_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CreditCardBillRepaymentSelectionScreenOptions{
					CreditCardBillRepaymentSelectionScreenOptions: &deeplink.CreditCardBillRepaymentSelectionScreenOptions{
						CardId:       cardId,
						BgColorLight: shadeVsTint[bgColor],
						BgColorDark:  bgColor,
					},
				},
			},
			DisplayTheme: 0,
			Status:       0,
			Weblink:      "",
		}
	}

	var feesApplyBadge *commontypes.Text
	if datetime.IsBefore(billInfo.GetLatestBill().GetSoftDueDate(), timestamppb.Now()) {
		feesApplyBadge = &commontypes.Text{
			FontColor:    internal.BillInfoDashboardWhite,
			BgColor:      tintVsShade[bgColor],
			DisplayValue: &commontypes.Text_PlainString{PlainString: "FEES APPLY"},
		}
	}

	return &ffPb.GetDashboardResponse_BillInfo{
		BannerText: &commontypes.Text{
			BgColor:      bgColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: textVal},
		},
		BannerSubText: &commontypes.Text{
			BgColor:      bgColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: subTextVal},
		},
		ActionCta: cta,
		BannerIcon: &ffPb.GetDashboardResponse_BillInfo_BannerIconInfo{
			BannerIconInfo: &ffPb.GetDashboardResponse_BannerIconInfo{
				DaysValue: &commontypes.Text{
					FontColor:    tintVsShade[bgColor],
					BgColor:      shadeVsTint[bgColor],
					DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%d", absDayDiff)},
				},
				IconText: &commontypes.Text{
					FontColor:    tintVsShade[bgColor],
					BgColor:      shadeVsTint[bgColor],
					DisplayValue: &commontypes.Text_PlainString{PlainString: bannerIconText},
				},
				ImgUrl: "https://epifi-icons.pointz.in/credit_card_images/hourglass.png",
			},
		},
		FeesApplyBadge: feesApplyBadge,
	}, nil
}

func (s *Service) fetchPaymentStatusResponse(paymentStatus ffEnumsBePb.TransactionStatus) (*ffPb.GetDashboardResponse_BillInfo, error) {
	bgColor := ""
	textVal := ""
	subTextVal := ""
	iconUrl := ""
	switch {
	case paymentStatus == ffEnumsBePb.TransactionStatus_IN_PROCESS:
		bgColor = internal.BillInfoDashboardGreen
		textVal = "Bill payment in progress"
		subTextVal = "This should take upto 2 hours"
		iconUrl = internal.HourGlassIconUrl
	case paymentStatus == ffEnumsBePb.TransactionStatus_PAYMENT_FAILURE:
		bgColor = internal.BillInfoDashboardRed
		textVal = "We’re sorry, your payment failed"
		subTextVal = "Know more"
		iconUrl = internal.WarningRedTriangleIconUrl
	default:
		return nil, pkgErr.New("Payment status is not in process/failed")
	}
	return &ffPb.GetDashboardResponse_BillInfo{
		BannerText: &commontypes.Text{
			BgColor:      bgColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: textVal},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
			FontColor:    internal.BillDashboardFontColorBlack,
		},
		BannerSubText: &commontypes.Text{
			BgColor:      bgColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: subTextVal},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
		},
		BannerIcon: &ffPb.GetDashboardResponse_BillInfo_ImgUrl{
			ImgUrl: iconUrl,
		},
	}, nil
}

func (s *Service) fetchNoOutstandingResponse(billInfo *ffBeBillingPb.GetBillAndBillPaymentInfoResponse, card *ffBePb.CreditCard) (*ffPb.GetDashboardResponse_BillInfo, error) {
	textVal := "No current dues. Next bill available on"
	iconUrl := internal.VioletSpecsIconUrl
	if len(billInfo.GetPayments()) != 0 && billInfo.GetPayments()[0].GetStatus() == ffEnumsBePb.TransactionStatus_PAYMENT_SUCCESS && int32(timestamppb.Now().AsTime().Sub(billInfo.GetPayments()[0].GetPaymentDate().AsTime()).Minutes()) <= s.dynamicConf.CreditCard().PaymentSuccessBannerTimeInMinutes() {
		textVal = "Payment successful. Next bill available on"
		iconUrl = internal.RedTickIconUrl
	}
	nextBillGenDate := s.getNextBillGenerationDate(card)
	subTextVal := datetime.DateToString(nextBillGenDate, "2 January", datetime.IST)
	text := &commontypes.Text{
		BgColor:      internal.BillInfoDashboardGreen,
		DisplayValue: &commontypes.Text_PlainString{PlainString: textVal},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
	}
	subText := &commontypes.Text{
		BgColor:      internal.BillInfoDashboardGreen,
		DisplayValue: &commontypes.Text_PlainString{PlainString: subTextVal},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
	}
	bannerIconInfo := &ffPb.GetDashboardResponse_BillInfo_ImgUrl{
		ImgUrl: iconUrl,
	}
	return &ffPb.GetDashboardResponse_BillInfo{
		BannerText:    text,
		BannerSubText: subText,
		BannerIcon:    bannerIconInfo,
	}, nil
}

func (s *Service) getNextBillGenerationDate(card *ffBePb.CreditCard) *date.Date {
	timeNow := time.Now()
	billGenDay := card.GetBasicInfo().GetBillGenDate()
	// initialising bill gen date as today's date
	// will update based on conditions
	resultantBillGenDate := datetime.TimeToDateInLoc(timeNow, datetime.IST)
	// in case the bill gen date has passed for this month, the bill gen date
	// to be shown would be of next month
	if resultantBillGenDate.GetDay() > billGenDay {
		resultantBillGenDate.Month = resultantBillGenDate.GetMonth()%12 + 1
		// below check is to handle the case where next month would fall in the next year
		if resultantBillGenDate.GetMonth() == 1 {
			resultantBillGenDate.Year = resultantBillGenDate.GetYear() + 1
		}
	}
	resultantBillGenDate.Day = billGenDay
	return resultantBillGenDate
}

func (s *Service) GetBillingDatesInfo(ctx context.Context, req *ffPb.GetBillingDatesInfoRequest) (*ffPb.GetBillingDatesInfoResponse, error) {
	res, err := s.fireFlyClient.GetBillingDatesInfo(ctx, &ffBePb.GetBillingDatesInfoRequest{})
	if epifigrpc.RPCError(res, err) != nil {
		logger.Error(ctx, "error in selectBillDate BE rpc : "+(epifigrpc.RPCError(res, err).Error()))
		return &ffPb.GetBillingDatesInfoResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: genericErrorView,
			},
			Days: nil,
		}, nil
	}
	dayStrings := make([]*ffPb.CreditCardPaymentInfo, 0)
	if req.GetScreenIdentifier() == int32(deeplink.Screen_CC_BILL_GENERATION_DATE_SELECTION_SCREEN) {
		// checking for already existing credit cards to identify if the billing dates are being displayed to a pre-onboarded
		// user or an onboarded user
		currentlySelectedDate := int32(0)
		if req.GetCardId() != "" {
			ccRes, err := s.fireFlyClient.GetCreditCard(ctx, &ffBePb.GetCreditCardRequest{
				GetBy: &ffBePb.GetCreditCardRequest_CreditCardId{CreditCardId: req.GetCardId()}})
			if te := epifigrpc.RPCError(ccRes, err); te != nil && !ccRes.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "error in fetching credit card entry", zap.Error(te), zap.String(logger.CARD_ID, req.GetCardId()))
			}
			currentlySelectedDate = ccRes.GetCreditCard().GetBasicInfo().GetBillGenDate()
		}
		for _, day := range res.GetDates() {
			billGenDateStr := fmt.Sprintf("Generated on: %d%s of the month", day.GetBillGenDate(), getDaySuffix(day.GetBillGenDate()))
			paymentDueDateStr := fmt.Sprintf("Payment due: %d%s of the %s month", day.GetPaymentDueDate(), getDaySuffix(day.GetPaymentDueDate()), getMonthPrefix(day.GetBillGenDate()))
			billInfo := &ffPb.CreditCardPaymentInfo{
				BillGenDateStr: billGenDateStr,
				PaymentDateStr: paymentDueDateStr,
				BillGenDate:    day.GetBillGenDate(),
				PaymentDueDate: day.GetPaymentDueDate(),
				Default:        getDefaultSelection(currentlySelectedDate, int32(day.GetBillGenDate()), day.GetDefault()),
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: billGenDateStr},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_2},
					FontColor:    "#383838",
				},
				SubTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: paymentDueDateStr},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
					FontColor:    "#646464",
				},
				CardBgColor: "#F5F5F5",
			}
			if int32(day.GetBillGenDate()) == currentlySelectedDate {
				billInfo.Badge = &ui.IconTextComponent{
					Texts: []*commontypes.Text{
						{
							FontColor:    "#5D7D4C",
							BgColor:      "#C5E9B2",
							DisplayValue: &commontypes.Text_PlainString{PlainString: "CURRENT"},
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_MICRO_1},
						},
					},
					ContainerProperties: &ui.IconTextComponent_ContainerProperties{
						BgColor:       "#C5E9B2",
						CornerRadius:  9,
						LeftPadding:   8,
						RightPadding:  8,
						TopPadding:    2,
						BottomPadding: 2,
					},
				}
			}

			dayStrings = append(dayStrings, billInfo)
		}
		return &ffPb.GetBillingDatesInfoResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
			Days:       dayStrings,
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Choose your own billing cycle"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
			},
			Desc: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Take control of your Credit Card payments."},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
			},
		}, nil
	}
	for _, day := range res.GetDates() {
		billGenDateStr := fmt.Sprintf("Generated on: %d%s of the month", day.GetBillGenDate(), getDaySuffix(day.GetBillGenDate()))
		paymentDueDateStr := fmt.Sprintf("Payment due: %d%s of the %s month", day.GetPaymentDueDate(), getDaySuffix(day.GetPaymentDueDate()), getMonthPrefix(day.GetBillGenDate()))
		dayStrings = append(dayStrings, &ffPb.CreditCardPaymentInfo{
			BillGenDateStr: billGenDateStr,
			PaymentDateStr: paymentDueDateStr,
			BillGenDate:    day.GetBillGenDate(),
			PaymentDueDate: day.GetPaymentDueDate(),
			Default:        day.GetDefault(),
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: billGenDateStr},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_2},
				FontColor:    "#FFFFFF",
			},
			SubTitle: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: paymentDueDateStr},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
				FontColor:    "#B9B9B9",
			},
			CardBgColor: "#383838",
		})
	}
	return &ffPb.GetBillingDatesInfoResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Days:       dayStrings,
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Choose your own billing cycle"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
		},
		Desc: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Take control of your Credit Card payments."},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
		},
	}, nil
}

// getDefaultSelection is function responsible for assigning default tag to a date on the basis of already selected bill gen date
// if there is already selected bill gen date available then it marks true to that date otherwise the default date coming from config
func getDefaultSelection(ccBillGenDate int32, configBillDate int32, configDefaultTag bool) bool {
	if ccBillGenDate != 0 {
		return ccBillGenDate == configBillDate
	}
	return configDefaultTag
}

func (s *Service) GetCardDetails(ctx context.Context, req *ffPb.GetCardDetailsRequest) (*ffPb.GetCardDetailsResponse, error) {
	var (
		res       = &ffPb.GetCardDetailsResponse{RespHeader: &header.ResponseHeader{}}
		errorView = func(status *rpc.Status, title, description string) *ffPb.GetCardDetailsResponse {
			return &ffPb.GetCardDetailsResponse{
				RespHeader: &header.ResponseHeader{
					Status: status,
					ErrorView: &errors.ErrorView{
						Type: errors.ErrorViewType_BOTTOM_SHEET,
						Options: &errors.ErrorView_BottomSheetErrorView{
							BottomSheetErrorView: &errors.BottomSheetErrorView{
								Title:       title,
								Description: description,
							},
						},
					},
				},
			}
		}
	)

	beRes, err := s.fireFlyClient.GetCardDetails(ctx, &ffBePb.GetCardDetailsRequest{CardRequestId: req.GetCardRequestId()})
	switch {
	case err != nil:
		logger.Error(ctx, "error in fetching backend api resp for GetCardDetails", zap.Error(err))
		return errorView(rpc.StatusInternal(), "Request Failed", "Something went wrong!! No worries please try again"), nil
	case beRes.GetStatus().IsFailedPrecondition():
		logger.Error(ctx, "failed precondition status from backend api resp for GetCardDetails", zap.String(logger.ACTOR_ID_V2,
			req.GetReq().GetAuth().GetActorId()))
		return errorView(beRes.GetStatus(), "Request Failed", "Looks like your card is closed. We are unable to process your request."), nil
	case !beRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success status from backend api resp for GetCardDetails", zap.String(logger.STATUS_CODE,
			beRes.GetStatus().String()))
		return errorView(beRes.GetStatus(), "Request Failed", "Something went wrong!! No worries please try again"), nil
	}

	res = &ffPb.GetCardDetailsResponse{
		RespHeader:            &header.ResponseHeader{Status: rpc.StatusOk()},
		ExpiryDate:            beRes.GetExpiryToken(),
		ClearCardNumber:       beRes.GetClearCardNumber(),
		CardNetworkType:       beCardNetworkTypeToBeCardNetworkType[beRes.GetCardNetworkType()],
		Cvv:                   beRes.GetCvv(),
		Name:                  beRes.GetName(),
		ViewCardDetailsExpiry: beRes.GetViewCardDetailsExpiry(),
	}
	res.CardVisualElementHalf, res.CardVisualElementFull = feHelper.GetCardImageByCardProgram(beRes.GetCardProgram())
	res.FallBackgroundColour = feHelper.GetFallBackgroundColourForCcImage(beRes.GetCardProgram())
	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().EnableCCCopyCardDetails()) {
		res.EnableCopyCardDetails = true
		res.CopyCardDetailsExpiry = beRes.GetCopyCardDetailsExpiry()
	}
	return res, nil
}

func getBillGenDayPrefix(billGenDate, paymentDueDate int32) string {
	if paymentDueDate <= billGenDate {
		return "next "
	}
	return ""
}

func getMonthPrefix(day int64) string {
	if day <= 12 {
		return "same"
	} else {
		return "next"
	}
}

func getDaySuffix(day int64) string {
	suffix := "th"
	switch {
	case day%100 >= 11 && day%100 <= 20:
		return suffix
	case day%10 == 1:
		suffix = "st"
	case day%10 == 2:
		suffix = "nd"
	case day%10 == 3:
		suffix = "rd"
	}
	return suffix
}

// nolint: funlen
func (s *Service) InitiateCardReq(ctx context.Context, req *ffPb.InitiateCardReqRequest) (*ffPb.InitiateCardReqResponse, error) {
	var (
		res   = &ffPb.InitiateCardReqResponse{}
		beReq = &ffBePb.InitiateCardReqRequest{}
	)

	isWfEnabled, err := ffPkg.EvaluateConstraintsForWorkflow(ctx, s.dynamicConf.CreditCard(), req.GetCardRequestWorkflow())
	if err != nil {
		logger.Error(ctx, "error in checking if workflow enabled", zap.String(logger.CARD_ID, req.GetCardId()),
			zap.String(logger.WORKFLOW, req.GetCardRequestWorkflow().String()), zap.Error(err))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInvalidArgument(),
		}
		return res, nil
	}

	if !isWfEnabled {
		// TODO(team) - add specific handling for workflows if required
		logger.Info(ctx, "workflow not enabled for device platform and version", zap.String(logger.WORKFLOW, req.GetCardRequestWorkflow().String()))

		var updateCta *deeplink.Cta
		// Update now cta works only in IOS
		if req.GetReq().GetAuth().GetDevice().GetPlatform() == commontypes.Platform_IOS {
			updateCta = &deeplink.Cta{
				Text:     "Update Now",
				Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_UPDATE_APP_SCREEN},
			}
		}

		res.RespHeader = header2.SuccessRespHeader()
		res.NextAction = &deeplink.Deeplink{
			Screen: deeplink.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN,
			ScreenOptions: &deeplink.Deeplink_PreApprovedLoanErrorScreenOptions{
				PreApprovedLoanErrorScreenOptions: &deeplink.PreApprovedLoanErrorScreenOptions{
					IconUrl: internal.UpdateAppDisplayImage,
					Details: []*deeplink.InfoItem{
						{
							Title: internal.UpdateAppTitle,
							Desc:  internal.UpdateAppDescription,
						},
					},
					Cta: updateCta,
				},
			},
		}
		return res, nil
	}

	beRequestWorkflow, ok := feRequestWorkflowTypeToBeWorkflowTypeMap[req.GetCardRequestWorkflow()]
	if !ok {
		logger.Error(ctx, "error in fetching be workflow from fe workflow", zap.String(logger.CARD_ID, req.GetCardId()),
			zap.String(logger.WORKFLOW, req.GetCardRequestWorkflow().String()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInvalidArgument(),
		}
		return res, nil
	}

	// TODO(team) : use factory pattern here for converting FE request details to BE request details
	switch req.GetCardRequestWorkflow() {
	case ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_REISSUE_CARD:
		beReq.RequestData = &ffBePb.InitiateCardReqRequest_ReissueCardData{
			ReissueCardData: &ffBePb.ReissueCardData{BlockCardReason: req.GetCardReissueData().GetBlockCardReason()},
		}

	case ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_CONTROL_CHANGE:
		beReq.RequestData = &ffBePb.InitiateCardReqRequest_ControlsChangeData{
			ControlsChangeData: &ffBePb.ControlsChangeData{
				International: req.GetControlsChangeData().GetInternational(),
				Contactless:   req.GetControlsChangeData().GetContactless(),
				Atm:           req.GetControlsChangeData().GetAtm(),
				Pos:           req.GetControlsChangeData().GetPos(),
				Ecom:          req.GetControlsChangeData().GetEcom(),
			},
		}

	case ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_LIMIT_CHANGE:
		beControlType, ok := feControlTypeToBeControlTypeMap[req.GetLimitsChangeData().GetControlType()]
		if !ok {
			logger.Error(ctx, "error in fetching be control type from fe control type", zap.String(logger.CARD_ID, req.GetCardId()),
				zap.String(logger.WORKFLOW, req.GetCardRequestWorkflow().String()), zap.String(logger.CONTROL_TYPE,
					req.GetLimitsChangeData().GetControlType().String()))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInvalidArgument(),
			}
			return res, nil
		}
		beControlLocType, ok := feControlLocTypeToBeControlLocType[req.GetLimitsChangeData().GetCardControlLocationType()]
		if !ok {
			logger.Error(ctx, "error in fetching be control location type from fe control type", zap.String(logger.CARD_ID, req.GetCardId()),
				zap.String(logger.WORKFLOW, req.GetCardRequestWorkflow().String()), zap.String(logger.CONTROL_TYPE,
					req.GetLimitsChangeData().GetControlType().String()))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInvalidArgument(),
			}
			return res, nil
		}
		beReq.RequestData = &ffBePb.InitiateCardReqRequest_LimitsChangeData{
			LimitsChangeData: &ffBePb.LimitsChangeData{
				CardUsageLocationType: beControlLocType,
				IsValueIncrease:       req.GetLimitsChangeData().GetIsValueIncrease(),
				ControlType:           beControlType,
				UpdatedLimitValue:     req.GetLimitsChangeData().GetUpdatedLimitValue().GetBeMoney(),
			},
		}

	case ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PROCESS_DISPUTE:
		bePayload, err := ffPkg.GetBeDisputePayload(req.GetDisputeData())
		if err != nil {
			logger.Error(ctx, "error in getting payload", zap.Error(err), zap.String(logger.CARD_ID, req.GetCardId()), zap.String(logger.WORKFLOW, req.GetCardRequestWorkflow().String()))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
			return res, nil
		}
		beReq.RequestData = &ffBePb.InitiateCardReqRequest_DisputeData{
			DisputeData: bePayload,
		}
	case ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_BILL_PAYMENTS:
		feAmount := req.GetBillPaymentData().GetAmount()
		reqData := &ffBePb.InitiateCardReqRequest_PaymentData{PaymentData: &ffBePb.PaymentData{
			ActorId: req.GetReq().GetAuth().GetActorId(),
			CardId:  req.GetCardId(),
			Amount:  feAmount.GetBeMoney(),
		}}
		if req.GetBillPaymentData().GetDerivedAccountId() != "" {
			accountId, paymentAccType, err := ffPkg.GetAccountIdAndAccountTypeFromDerivedAccountId(req.GetBillPaymentData().GetDerivedAccountId())
			if err != nil {
				logger.Error(ctx, "error in decoding derived account id", zap.Error(err))
			}
			reqData.PaymentData.AccountId = accountId
			reqData.PaymentData.PaymentAccountType = paymentAccType
			reqData.PaymentData.DerivedAccountId = req.GetBillPaymentData().GetDerivedAccountId()
		}
		beReq.RequestData = reqData
	default:
	}

	beReq.CardRequestWorkFlow = beRequestWorkflow
	beReq.CardId = req.GetCardId()
	beReq.Provenance = ffEnumsBePb.Provenance_PROVENANCE_APP
	beReq.DeviceUnlockMechanism = req.GetDeviceUnlockMechanism()
	beReq.DeviceUnlockMechanismStrength = req.GetDeviceUnlockMechanismStrength()
	beRes, err := s.fireFlyClient.InitiateCardReq(ctx, beReq)
	switch {
	case err != nil:
		logger.Error(ctx, "error in initiating be card request", zap.String(logger.CARD_ID, req.GetCardId()),
			zap.String(logger.WORKFLOW, req.GetCardRequestWorkflow().String()), zap.Error(err))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	case beRes.GetStatus().IsPermissionDenied():
		logger.Error(ctx, "permission denied for renew card", zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusPermissionDenied(),
			ErrorView: reissueCardRateLimitReachedErrorView,
		}
		return res, nil
	case !beRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success status while initiating be card request", zap.String(logger.CARD_ID, req.GetCardId()),
			zap.String(logger.WORKFLOW, req.GetCardRequestWorkflow().String()), zap.String(logger.STATUS, beRes.GetStatus().String()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	default:
	}

	res.NextAction = beRes.GetNextAction()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func (s *Service) CollectAuthFlow(ctx context.Context, req *ffPb.CollectAuthFlowRequest) (*ffPb.CollectAuthFlowResponse, error) {
	var (
		res = &ffPb.CollectAuthFlowResponse{}
	)
	beAuthFlowType, ok := feAuthFlowToBeAuthFlowMap[req.GetCardAuthFlow()]
	if !ok {
		logger.Error(ctx, "error in fetching be auth flow", zap.String(logger.AUTH_TYPE,
			req.GetCardAuthFlow().String()), zap.String(logger.CARD_REQUEST_ID, req.GetCardRequestId()))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInvalidArgument()}
		return res, nil
	}
	beRes, err := s.fireFlyClient.CollectAuthFlow(ctx, &ffBePb.CollectAuthFlowRequest{
		CardRequestId: req.GetCardRequestId(),
		CardAuthFlow:  beAuthFlowType,
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in collecting auth flow be rpc", zap.String(logger.CARD_REQUEST_ID,
			req.GetCardRequestId()), zap.Error(te))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}
	res.NextAction = beRes.GetNextAction()
	res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
	return res, nil
}

// TODO(team) : Move constants to config
func (s *Service) FetchCardControlsCTAs(ctx context.Context, req *ffPb.FetchCardControlsCTAsRequest) (*ffPb.FetchCardControlsCTAsResponse, error) {
	res := &ffPb.FetchCardControlsCTAsResponse{RespHeader: &header.ResponseHeader{}}

	// Card freeze/unfreeze InfoItemWithCTA
	getCardRes, err := s.fireFlyClient.GetCreditCard(ctx, &ffBePb.GetCreditCardRequest{
		GetBy: &ffBePb.GetCreditCardRequest_ActorId{
			ActorId: req.GetReq().GetAuth().GetActorId(),
		},
		SelectFieldMasks: []ffEnumsBePb.CreditCardFieldMask{
			ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE,
			ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
			ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID,
			ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID,
		},
	})

	te := epifigrpc.RPCError(getCardRes, err)
	switch {
	case pkgErr.Is(te, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "card not found for actor", zap.Error(te), zap.String(logger.CARD_ID, req.GetCreditCardId()), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusFailedPrecondition(),
			ErrorView: notFoundErrorView,
		}
		return res, nil
	case te != nil:
		logger.Error(ctx, "error in fetching credit card by id", zap.Error(te), zap.String(logger.CARD_ID, req.GetCreditCardId()), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
	if req.GetCreditCardId() == "" {
		req.CreditCardId = getCardRes.GetCreditCard().GetId()
	}

	if getCardRes.GetCreditCard().GetActorId() != "" && req.GetReq().GetAuth().GetActorId() != "" && getCardRes.GetCreditCard().GetActorId() != req.GetReq().GetAuth().GetActorId() {
		logger.Error(ctx, "the actor id in request header does not match with the one in cc")
		res.RespHeader.Status = rpc.StatusPermissionDenied()
		return res, nil
	}
	accountDetails, err := s.fireflyAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{GetBy: &ffAccPb.GetAccountRequest_AccountId{
		AccountId: getCardRes.GetCreditCard().GetAccountId(),
	}})
	if te := epifigrpc.RPCError(accountDetails, err); te != nil {
		logger.Error(ctx, "error in fetching account details for the user", zap.Error(te), zap.String(logger.ACCOUNT_ID, getCardRes.GetCreditCard().GetAccountId()))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}

	physicalCardCtaStatus := deeplink.Cta_CTA_STATUS_ENABLED
	if getCardRes.GetCreditCard().GetCardState() != ffEnumsBePb.CardState_CARD_STATE_ACTIVATED {
		physicalCardCtaStatus = deeplink.Cta_CTA_STATUS_DISABLED
	}
	var cardFreezeUnfreezeInfoItem = &deeplink.InfoItemWithCta{}
	if getCardRes.GetCreditCard().GetCardState() == ffEnumsBePb.CardState_CARD_STATE_SUSPENDED {
		cardFreezeUnfreezeInfoItem = &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Title: "Unfreeze Card",
				Desc:  "Resume your card transactions",
			},
			Cta: &deeplink.Cta{
				Status: deeplink.Cta_CTA_STATUS_ENABLED,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_FREEZE_UNFREEZE_SCREEN,
					ScreenOptions: &deeplink.Deeplink_CreditCardFreezeUnfreezeScreenOptions{CreditCardFreezeUnfreezeScreenOptions: &deeplink.CreditCardFreezeUnfreezeScreenOptions{
						CreditCardId:    req.GetCreditCardId(),
						Title:           "Do you want to unfreeze this card?",
						Description:     "To unfreeze this card, you will need to confirm your identity. Once verification is complete, you’ll be able to use your card again.",
						ImageUrl:        "",
						CardRequestType: ffEnumsPb.CardRequestType_CARD_REQUEST_TYPE_UNFREEZE_CARD,
						Cta: &deeplink.Cta{
							Text: "Unfreeze Card",
						},
						IsDeviceUnlockRequired: true,
					}},
				},
			},
		}
	} else {
		cardFreezeUnfreezeInfoItem = &deeplink.InfoItemWithCta{
			Info: &deeplink.InfoItem{
				Title: "Freeze Card",
				Desc:  "Temporarily freeze your card",
			},
			Cta: &deeplink.Cta{
				Status: deeplink.Cta_CTA_STATUS_ENABLED,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_FREEZE_UNFREEZE_SCREEN,
					ScreenOptions: &deeplink.Deeplink_CreditCardFreezeUnfreezeScreenOptions{CreditCardFreezeUnfreezeScreenOptions: &deeplink.CreditCardFreezeUnfreezeScreenOptions{
						CreditCardId:    req.GetCreditCardId(),
						Title:           "Do you want to freeze this card?",
						Description:     "Freezing this card pauses all outgoing card transactions temporarily. You can start using your card anytime you want by tapping unfreeze.",
						ImageUrl:        "",
						CardRequestType: ffEnumsPb.CardRequestType_CARD_REQUEST_TYPE_FREEZE_CARD,
						Cta: &deeplink.Cta{
							Text: "Freeze Card",
						},
					}},
				},
			},
		}
	}
	// Card usage InfoItemWithCTA
	cardUsageInfoItem := &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Title: "Credit Card Usage",
			Desc:  "Control how your card is used in India and abroad",
		},
		Cta: &deeplink.Cta{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_USAGE_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CreditCardUsageScreenOptions{
					CreditCardUsageScreenOptions: &deeplink.CreditCardUsageScreenOptions{
						CreditCardId: req.GetCreditCardId(),
					},
				},
			},
		},
	}

	// Card limits InfoItemWithCTA
	cardLimitsInfoItem := &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Title: "Credit Card Limits",
			Desc:  "View all spend limits set on your Credit Card",
		},
		Cta: &deeplink.Cta{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_FETCH_LIMITS,
				ScreenOptions: &deeplink.Deeplink_CreditCardFetchLimitsOptions{
					CreditCardFetchLimitsOptions: &deeplink.CreditCardFetchLimitsOptions{
						CreditCardId: req.GetCreditCardId(),
					},
				},
			},
		},
	}
	// Reissue card InfoItemWithCTA
	maskedCardNumber := getCardRes.GetCreditCard().GetBasicInfo().GetMaskedCardNumber()
	if len(maskedCardNumber) < 4 {
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg("the length of masked card number is less than 4")
		return res, nil
	}
	cardReissueInfoItem := &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Title: "Request New Card",
			Desc:  "Card lost or damaged? Get a new one",
		},
		Cta: &deeplink.Cta{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_NEW_CARD_REQUEST,
				ScreenOptions: &deeplink.Deeplink_CardNewCardRequestScreenOptions{
					CardNewCardRequestScreenOptions: &deeplink.CreditCardNewCardRequestScreenOptions{
						CreditCardId: req.GetCreditCardId(),
						Title:        "New Card Request",
						Description:  fmt.Sprintf("This will permanently deactivate your card ending with %s and a new card will be \nsent to you in 3-10 days. \n\nYour first replacement card is free of charge, following which you will be charged ₹250 \nfor a new card.", maskedCardNumber[len(maskedCardNumber)-4:]),
						BlockCardReasons: []*deeplink.CreditCardNewCardRequestScreenOptions_BlockCardReason{
							{
								Id:            1,
								DisplayString: "My card is lost/stolen",
							},
							{
								Id:            2,
								DisplayString: "My card is damaged",
							},
							{
								Id:            3,
								DisplayString: "I’m having transaction issues",
							},
							{
								Id:            100,
								DisplayString: "Other (I’ll write my reason)",
							},
						},
					},
				},
			},
		},
	}

	// Card reset pin InfoItemWithCTA
	resetPinInfoItem := &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Title: "Reset Credit Card PIN",
			Desc:  "Change your Credit Card PIN here",
		},
		Cta: &deeplink.Cta{
			Status: physicalCardCtaStatus,
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_INITIATE_CARD_REQUEST_SCREEN,
				ScreenOptions: &deeplink.Deeplink_InitiateCardRequestScreenOptions{
					InitiateCardRequestScreenOptions: &deeplink.InitiateCardRequestScreenOptions{
						CardId:                 req.GetCreditCardId(),
						Workflow:               ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_PIN_RESET,
						IsDeviceUnlockRequired: true,
					},
				},
			},
		},
	}

	// If the card state is closed, all the cta have to be disabled
	if getCardRes.GetCreditCard().GetCardState() == ffEnumsBePb.CardState_CARD_STATE_CLOSED {
		cardUsageInfoItem.GetCta().Status = deeplink.Cta_CTA_STATUS_DISABLED
		cardLimitsInfoItem.GetCta().Status = deeplink.Cta_CTA_STATUS_DISABLED
		cardReissueInfoItem.GetCta().Status = deeplink.Cta_CTA_STATUS_DISABLED
		cardFreezeUnfreezeInfoItem.GetCta().Status = deeplink.Cta_CTA_STATUS_DISABLED
	}

	res.ControlsCtas = append(res.ControlsCtas, cardUsageInfoItem, cardLimitsInfoItem, cardReissueInfoItem, cardFreezeUnfreezeInfoItem)
	// reset pin should be available only if
	if getCardRes.GetCreditCard().GetCardState() == ffEnumsBePb.CardState_CARD_STATE_ACTIVATED {
		res.ControlsCtas = append(res.ControlsCtas, resetPinInfoItem)
	}
	res.CreditCardHeader = s.GetCreditCardHeader(accountDetails.GetAccount().GetCardProgram())
	res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
	return res, nil
}

// nolint: funlen
func (s *Service) FetchCardLimits(ctx context.Context, req *ffPb.FetchCardLimitsRequest) (*ffPb.FetchCardLimitsResponse, error) {
	res := &ffPb.FetchCardLimitsResponse{}

	beResp, err := s.fireFlyClient.FetchCardLimits(ctx, &ffBePb.FetchCardLimitsRequest{CreditCardId: req.GetCreditCardId(), ActorId: req.GetReq().GetAuth().GetActorId()})
	te := epifigrpc.RPCError(beResp, err)
	switch {
	case pkgErr.Is(te, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "card not found for actor", zap.Error(te), zap.String(logger.CARD_ID, req.GetCreditCardId()), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusFailedPrecondition(),
			ErrorView: notFoundErrorView,
		}
		return res, nil
	case te != nil:
		logger.Error(ctx, "error in fetch card limits", zap.Error(te), zap.String(logger.CARD_ID, req.GetCreditCardId()), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusInternal(),
			ErrorView: genericErrorView,
		}
		return res, nil
	}
	if req.GetCreditCardId() == "" {
		req.CreditCardId = beResp.GetCreditCardId()
	}

	hasAllLimitsEnabled := true
	for _, limit := range beResp.GetControlLimits() {
		txnType, ok := beTxnTypeToFeTxnTypeMap[limit.GetCardControlType()]
		if !ok {
			logger.Error(ctx, "invalid control type for fetch card limits", zap.String(logger.TXN_TYPE, limit.GetCardControlType().String()))
			res.RespHeader = &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgument(),
				ErrorView: genericErrorView,
			}
			return res, nil
		}
		controlTypeInfo, ok := feControlTypeToTextMap[txnType]
		if !ok {
			logger.Error(ctx, "control type to Text mapping failure", zap.String(logger.TXN_TYPE, limit.GetCardControlType().String()))
			res.RespHeader = &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgument(),
				ErrorView: genericErrorView,
			}
			return res, nil
		}
		ctaState := deeplink.Cta_CTA_STATUS_ENABLED
		if controlTypeInfo.PhysicalCardActivationRequired && beResp.GetCardState() != ffEnumsBePb.CardState_CARD_STATE_ACTIVATED {
			ctaState = deeplink.Cta_CTA_STATUS_DISABLED
		}
		if limit.GetLimitDisabled() {
			ctaState = deeplink.Cta_CTA_STATUS_DISABLED
		}
		if ctaState == deeplink.Cta_CTA_STATUS_DISABLED {
			hasAllLimitsEnabled = false
		}
		dailyLimitValue := limit.GetDailyLimitValue()
		compareDailyLimit, compareErr := moneyPb.CompareV2(dailyLimitValue, limit.GetMaxDailyLimitValue())
		if compareErr != nil {
			logger.Error(ctx, "error comparing limit value and daily limit value", zap.Error(compareErr))
			res.RespHeader = &header.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: genericErrorView,
			}
			return res, nil
		}
		if compareDailyLimit > 0 {
			dailyLimitValue = limit.GetMaxDailyLimitValue()
		}
		res.ControlLimits = append(res.ControlLimits, &ffPb.FetchCardLimitsResponse_ControlLimits{
			ControlType:     txnType,
			DailyLimitValue: types.GetFromBeMoney(dailyLimitValue),
			Cta: &deeplink.Cta{
				Status: ctaState,
				Text:   controlTypeInfo.Text,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_SET_LIMITS,
					ScreenOptions: &deeplink.Deeplink_CreditCardSetLimitsOptions{
						CreditCardSetLimitsOptions: &deeplink.CreditCardSetLimitsOptions{
							Title:                  controlTypeInfo.Text,
							DailyLimitValue:        types.GetFromBeMoney(dailyLimitValue),
							MinLimitValue:          types.GetFromBeMoney(limit.GetMinDailyLimitValue()),
							MaxLimitValue:          types.GetFromBeMoney(limit.GetMaxDailyLimitValue()),
							CreditCardId:           req.GetCreditCardId(),
							CardControlType:        txnType,
							IsDeviceUnlockRequired: true,
						},
					},
				},
			},
		})
	}
	res.CardLimits = make([]*ffPb.FetchCardLimitsResponse_ControlLimitDetails, 0)
	for idx, locType := range cardLimitTabOrder {
		beLocType, ok := feControlLocTypeToBeControlLocType[locType]
		if !ok {
			continue
		}
		beLimitInfo, ok := beResp.GetCardLimits()[beLocType.String()]
		if !ok {
			continue
		}
		isDefault := false
		if idx == 0 {
			isDefault = true
		}
		res.CardLimits = append(res.GetCardLimits(), getFeLimitDetailsFromBeLimitDetails(beLimitInfo, locType, isDefault, req.GetCreditCardId(), beResp.GetCardState()))
	}
	if !hasAllLimitsEnabled {
		res.DisabledLimitsExplanationInfoItem = &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/information.png",
			Title: "Trying to modify your limits? Head to Card Settings and toggle ON these features in Credit Card Usage.",
		}
	}
	if beResp.GetCardState() == ffEnumsBePb.CardState_CARD_STATE_SUSPENDED ||
		beResp.GetCardState() == ffEnumsBePb.CardState_CARD_STATE_BLOCKED {
		res.DisabledLimitsExplanationInfoItem = &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/information.png",
			Title: "Your card is frozen. So, you can't modify these limits.",
		}
	}
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func getFeLimitDetailsFromBeLimitDetails(beLimitDetails *ffBePb.FetchCardLimitsResponse_CardLimitDetails, locType ffEnumsPb.CardControlLocationType, isDefault bool, cardId string, cardState ffEnumsBePb.CardState) *ffPb.FetchCardLimitsResponse_ControlLimitDetails {
	res := &ffPb.FetchCardLimitsResponse_ControlLimitDetails{
		TabTitle:                cardUsageLocTypeToTabHeading[locType],
		CardControlLocationType: locType,
		BottomInfoItem:          nil,
		IsDefault:               isDefault,
	}
	controlLimits := make([]*ffPb.FetchCardLimitsResponse_ControlLimits, 0)
	for _, lim := range beLimitDetails.GetControlLimits() {
		controlTypeInfo, ok := feControlTypeToTextMap[beTxnTypeToFeTxnTypeMap[lim.GetCardControlType()]]
		if !ok {
			continue
		}
		ctaStatus := deeplink.Cta_CTA_STATUS_ENABLED
		if lim.GetLimitDisabled() {
			ctaStatus = deeplink.Cta_CTA_STATUS_DISABLED
		}
		if controlTypeInfo.PhysicalCardActivationRequired && cardState != ffEnumsBePb.CardState_CARD_STATE_ACTIVATED {
			ctaStatus = deeplink.Cta_CTA_STATUS_DISABLED
		}
		feLim := &ffPb.FetchCardLimitsResponse_ControlLimits{
			ControlType:     beTxnTypeToFeTxnTypeMap[lim.GetCardControlType()],
			DailyLimitValue: types.GetFromBeMoney(lim.GetDailyLimitValue()),
			Cta: &deeplink.Cta{
				Status: ctaStatus,
				Text:   controlTypeInfo.Text,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_SET_LIMITS,
					ScreenOptions: &deeplink.Deeplink_CreditCardSetLimitsOptions{
						CreditCardSetLimitsOptions: &deeplink.CreditCardSetLimitsOptions{
							Title:                  controlTypeInfo.Text,
							DailyLimitValue:        types.GetFromBeMoney(lim.GetDailyLimitValue()),
							MinLimitValue:          types.GetFromBeMoney(lim.GetMinDailyLimitValue()),
							MaxLimitValue:          types.GetFromBeMoney(lim.GetMaxDailyLimitValue()),
							CreditCardId:           cardId,
							CardControlType:        beTxnTypeToFeTxnTypeMap[lim.GetCardControlType()],
							IsDeviceUnlockRequired: true,
						},
					},
				},
			},
		}
		controlLimits = append(controlLimits, feLim)
	}
	res.ControlLimits = controlLimits
	bottomText := &commontypes.Text{
		FontColor: "#333333",
		FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4_PARA},
		Alignment: commontypes.Text_ALIGNMENT_LEFT,
	}
	res.BottomInfoItem = &ui.IconTextComponent{
		Texts:             []*commontypes.Text{bottomText},
		LeftImgTxtPadding: 12,
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:       "#FFFFFF",
			CornerRadius:  8,
			Height:        56,
			LeftPadding:   20,
			RightPadding:  20,
			TopPadding:    12,
			BottomPadding: 12,
		},
		LeftVisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/information.png"},
					Properties: &commontypes.VisualElementProperties{
						Width:  24,
						Height: 24,
					},
					ImageType:     commontypes.ImageType_PNG,
					RenderingType: nil,
				},
			},
		},
	}
	switch beLimitDetails.GetCardLimitDisablementType() {
	case ffEnumsBePb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CARD_BLOCKED,
		ffEnumsBePb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CARD_FROZEN:
		bottomText.DisplayValue = &commontypes.Text_PlainString{PlainString: "Your card is frozen. So, you can't modify these limits."}
	case ffEnumsBePb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_CONTROL_DISABLED:
		bottomText.DisplayValue = &commontypes.Text_PlainString{PlainString: "Trying to modify your limits? Head to Card Settings and toggle ON these features in Card Usage."}
	case ffEnumsBePb.CardLimitDisablementType_CARD_LIMIT_DISABLEMENT_TYPE_INTERNATIONAL_DISABLED:
		bottomText.DisplayValue = &commontypes.Text_PlainString{PlainString: "To modify your International limits: Head to card usage and tap on International usage."}
	default:
		res.BottomInfoItem = nil
	}
	return res
}

func (s *Service) FetchCardUsage(ctx context.Context, req *ffPb.FetchCardUsageRequest) (*ffPb.FetchCardUsageResponse, error) {
	res := &ffPb.FetchCardUsageResponse{}

	type usageParameters struct {
		title, subTitle             string
		needsPhysicalCardActivation bool
	}
	controlTypeVsUsageParameters := map[ffEnumsPb.CardControlType]usageParameters{
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM: {
			title:    "Enable ATM Withdrawals",
			subTitle: "Permanently disabled for the Fi-Federal Co-branded Credit Card",
		},
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM: {
			title:    "Enable Online Transactions",
			subTitle: "Use this card to make purchases online",
		},
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_POS: {
			title:                       "Enable Card Swiping (POS)",
			subTitle:                    "Use this card at any merchant outlet",
			needsPhysicalCardActivation: true,
		},
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_CONTACTLESS: {
			title:                       "Enable Contactless",
			subTitle:                    "Tap to pay at any accepting merchant outlet",
			needsPhysicalCardActivation: true,
		},
		ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_INTERNATIONAL: {
			title:    "Enable International Usage",
			subTitle: "Use this card to make international payments",
		},
	}
	ccAccount, err := s.fireflyAccountingClient.GetAccounts(ctx,
		&ffBeAccountsPb.GetAccountsRequest{
			GetBy: &ffBeAccountsPb.GetAccountsRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
		})
	if te := epifigrpc.RPCError(ccAccount, err); te != nil {
		logger.Error(ctx, "error in fetch credit account", zap.Error(te))
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusInternal(),
			ErrorView: genericErrorView,
		}
		return res, nil
	}

	beResp, err := s.fireFlyClient.FetchCardUsage(ctx, &ffBePb.FetchCardUsageRequest{CreditCardId: req.GetCreditCardId(), ActorId: req.GetReq().GetAuth().GetActorId()})
	te := epifigrpc.RPCError(beResp, err)
	switch {
	case pkgErr.Is(te, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "card not found for actor", zap.Error(te), zap.String(logger.CARD_ID, req.GetCreditCardId()), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusFailedPrecondition(),
			ErrorView: notFoundErrorView,
		}
		return res, nil
	case te != nil:
		logger.Error(ctx, "error in fetch card usage", zap.Error(te), zap.String(logger.CARD_ID, req.GetCreditCardId()), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusInternal(),
			ErrorView: genericErrorView,
		}
		return res, nil
	}
	if req.GetCreditCardId() == "" {
		req.CreditCardId = beResp.GetCreditCardId()
	}

	for _, usage := range beResp.GetCardUsages() {
		controlType, ok := beTxnTypeToFeTxnTypeMap[usage.GetCardControlType()]
		if !ok {
			logger.Error(ctx, "invalid control type for fetch card usage", zap.String(logger.TXN_TYPE, usage.GetCardControlType().String()))
			res.RespHeader = &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgument(),
				ErrorView: genericErrorView,
			}
			return res, nil
		}

		parameters, ok := controlTypeVsUsageParameters[controlType]
		if !ok {
			logger.Error(ctx, "control type to parameters/subtitle mapping failure", zap.String(logger.TXN_TYPE, controlType.String()))
			res.RespHeader = &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgument(),
				ErrorView: genericErrorView,
			}
			return res, nil
		}
		usageSettingBlocked := false
		settingEnabled := usage.GetIsEnabled()
		if beResp.GetCardState() == ffEnumsBePb.CardState_CARD_STATE_SUSPENDED {
			usageSettingBlocked = true
			parameters.subTitle = "This will be available after card unfreeze"
		}
		if parameters.needsPhysicalCardActivation && beResp.GetCardState() != ffEnumsBePb.CardState_CARD_STATE_ACTIVATED {
			usageSettingBlocked = true
			settingEnabled = false
			parameters.subTitle = "This will be available after physical card is activated"
		}
		if controlType == ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ATM {
			switch {
			case ffPkg.IsCreditCardProgramSecured(ccAccount.GetAccounts()[0].GetCardProgram()):
				parameters.subTitle = "Use this to withdraw cash"
				atmEnabled, evalErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_ENABLE_SIMPLIFI_CREDIT_CARD_ATM).WithActorId(req.GetReq().GetAuth().GetActorId()))
				if evalErr != nil {
					logger.Error(ctx, "error in checking atm enabled feature", zap.Error(evalErr))
					usageSettingBlocked = true
					settingEnabled = false
				}
				if !atmEnabled || beResp.GetCardState() != ffEnumsBePb.CardState_CARD_STATE_ACTIVATED {
					usageSettingBlocked = true
					settingEnabled = false
				}
			default:
				usageSettingBlocked = true
				settingEnabled = false
			}
		}
		res.CardUsages = append(res.CardUsages, &ffPb.FetchCardUsageResponse_CardUsage{
			ControlType: controlType,
			IsEnabled:   settingEnabled,
			Title:       parameters.title,
			SubTitle:    parameters.subTitle,
			// device unlock is required if we are enabling any control and not required if are disabling controls
			IsDeviceUnlockRequired: !settingEnabled,
			SettingBlocked:         usageSettingBlocked,
		})
	}

	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func (s *Service) SetCardBillingDates(ctx context.Context, req *ffPb.SetCardBillingDatesRequest) (*ffPb.SetCardBillingDatesResponse, error) {
	var (
		res = &ffPb.SetCardBillingDatesResponse{RespHeader: &header.ResponseHeader{}}
	)
	beRes, err := s.fireFlyClient.SetCardBillingDates(ctx, &ffBePb.SetCardBillingDatesRequest{
		BillingDates: &ffBePb.CreditCardBillingDates{
			BillGenDate:    req.GetBillGenDate(),
			PaymentDueDate: req.GetPaymentDueDate(),
		},
		CardRequestId: req.GetCardRequestId(),
	})
	if epifigrpc.RPCError(beRes, err) != nil {
		logger.Error(ctx, "error in setting billing dates in backend", zap.Error(epifigrpc.RPCError(beRes, err)))
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg(epifigrpc.RPCError(beRes, err).Error())
		return res, nil
	}
	res.RespHeader.Status = rpc.StatusOk()
	res.NextAction = beRes.GetNextAction()
	return res, nil
}

func (s *Service) GetKnowMoreInfo(ctx context.Context, req *ffPb.GetKnowMoreInfoRequest) (*ffPb.GetKnowMoreInfoResponse, error) {
	return &ffPb.GetKnowMoreInfoResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Icon:       "https://epifi-icons.pointz.in/credit_card_images/two_cards.png",
		Title:      "About the Fi-Federal Co-branded Credit Card",
		AdditionalInfo: []*deeplink.InfoBlock{
			{
				Title: "I see an offer for a credit card in the Fi app. What are the next steps to get a credit card? ",
				Desc:  "View your card details and T&Cs, confirm your card delivery address, and select your billing cycle. Next, complete a quick liveliness & face match test. Swipe to accept the credit card and a digital credit card is generated for you to use online.",
			},
			{
				Title: "What are the fees and charges for using the credit card?",
				Desc:  fmt.Sprintf("You can find all the fees and charges applicable on your credit card <a href=%s>here</a>.", "https://fi.money/credit-card/key-fact-statement"),
			},
			{
				Title: "What are the benefits of using the Fi-Federal Co-branded Credit Card?",
				Desc:  fmt.Sprintf("You earn Fi-Coins for your credit card spends! You also get up to 5X the rewards on your top 3 merchants each month. You can then use these Fi-Coins for a cashback, or to erase a part of your bill, or even to redeem them for cool merchandise. Plus, enjoy amazing perks like a limited edition metal card, vouchers for spending over certain amounts, access to lounges, low forex markup at just 1%s, and fully personalised reminders for better money management. Head over to <a href=%s>our website</a> for more details.", "%", "https://fi.money/apply-credit-card-online"),
			},
			{
				Title: "Will I receive the base reward rate on all transactions?",
				Desc:  "Yes! In fact, you can earn Fi-Coins on all merchant transactions, including fuel. The only excluded transactions are wallet reloads.",
			},
			{
				Title: "Am I eligible to convert a transaction to EMI?",
				Desc:  "Yes. You can convert any purchase to EMI as long as it’s a minimum of ₹2,500 and a maximum of ₹7,50,000. Some transactions, such as jewellery, fuel etc., may not be eligible for EMI. You can see these transactions and opt for an EMI through the Fi app. Please note that amounts equal to the transaction amount converted to EMI shall be reduced from your available credit limit. Let’s say your credit limit is ₹50,000, and you’ve converted a purchase of ₹12,500 to EMI. For the next billing cycle, your available credit limit will only be ₹37,500. Your original credit limit is gradually restored as you pay off the EMIs.",
			},
			{
				Title: "What is the annual membership fee on my Fi-Federal Co-branded Credit Card?",
				Desc:  "There’s an annual fee of ₹2,000, which will be waived if you have spent ₹2,50,000 (Rupees Two and a Half Lakhs) in the previous year.",
			},
			{
				Title: "Is the Fi-Federal Co-branded Credit Card safe to use?",
				Desc:  "Yes, absolutely. Your credit card is issued by Federal Bank. Also, Fi is PCI-DSS and ISO certified. Your Fi-Federal Co-branded Credit Card uses cutting-edge fraud and risk monitoring systems to ensure the safety of your card. Your credit card also provides an effortless way to freeze and unfreeze the card without the need to reach out to our customer care.",
			},
		},
	}, nil
}

// getCardByActorId returns card for an actorId
func (s *Service) getCardByActorId(ctx context.Context, actorId string) (*ffBePb.CreditCard, error) {
	getCreditCardRes, err := s.fireFlyClient.GetCreditCard(ctx, &ffBePb.GetCreditCardRequest{GetBy: &ffBePb.GetCreditCardRequest_ActorId{ActorId: actorId}})
	if te := epifigrpc.RPCError(getCreditCardRes, err); te != nil {
		return nil, fmt.Errorf("error in fetching credit card for actor %w", te)
	}
	return getCreditCardRes.GetCreditCard(), err
}

func (s *Service) GetDashboardCardInfo(ctx context.Context, req *ffPb.GetDashboardCardInfoRequest) (*ffPb.GetDashboardCardInfoResponse, error) {
	var (
		res = &ffPb.GetDashboardCardInfoResponse{RespHeader: &header.ResponseHeader{}}
	)
	activatedStates := []ffEnumsBePb.CardState{
		ffEnumsBePb.CardState_CARD_STATE_DIGITALLY_ACTIVATED,
		ffEnumsBePb.CardState_CARD_STATE_ACTIVATED,
	}
	beRes, err := s.fireFlyClient.GetDashboardCardInfo(ctx, &ffBePb.GetDashboardCardInfoRequest{
		CardId: req.GetCardId(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in fetching card view in cc dashboard API", zap.String(logger.CARD_ID, req.GetCardId()), zap.Error(te))
		res.RespHeader.Status = rpc.StatusFromError(te)
		return res, nil
	}
	res.PartnerUrl = internal.VisaFederalPartnerIconUrl
	switch {
	case beRes.GetCardState() == ffEnumsBePb.CardState_CARD_STATE_CLOSED:
		res.RespHeader.Status = rpc.StatusOk()
		res.CardComponent = &ffPb.GetDashboardCardInfoResponse_BlockedCardDetailsComponent{
			BlockedCardDetailsComponent: &ffPb.BlockedCardDetailsComponent{
				Text: "Card has been closed",
			},
		}
	case beRes.GetCardBlocked():
		res.RespHeader.Status = rpc.StatusOk()
		res.CardComponent = &ffPb.GetDashboardCardInfoResponse_BlockedCardDetailsComponent{
			BlockedCardDetailsComponent: &ffPb.BlockedCardDetailsComponent{
				Text: "All transactions on this card have been blocked",
				ReissueCard: &deeplink.Cta{
					Text: "Request new card",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_INITIATE_CARD_REQUEST_SCREEN,
						ScreenOptions: &deeplink.Deeplink_InitiateCardRequestScreenOptions{
							InitiateCardRequestScreenOptions: &deeplink.InitiateCardRequestScreenOptions{
								CardId:                 req.GetCardId(),
								Workflow:               ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_REISSUE_CARD,
								IsDeviceUnlockRequired: true,
							},
						},
					},
				},
			},
		}
	case beRes.GetCardFrozen():
		res.RespHeader.Status = rpc.StatusOk()
		res.CardComponent = &ffPb.GetDashboardCardInfoResponse_FrozenCardDetailsComponent{
			FrozenCardDetailsComponent: &ffPb.FrozenCardDetailsComponent{
				Text: "All outgoing card transactions are temporarily paused",
				UnfreezeCard: &deeplink.Cta{
					Text: "Unfreeze card",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CREDIT_CARD_FREEZE_UNFREEZE_SCREEN,
						ScreenOptions: &deeplink.Deeplink_CreditCardFreezeUnfreezeScreenOptions{CreditCardFreezeUnfreezeScreenOptions: &deeplink.CreditCardFreezeUnfreezeScreenOptions{
							CreditCardId:    req.GetCardId(),
							Title:           "Do you want to unfreeze this card?",
							Description:     "To unfreeze this card, you will need to confirm your identity. Once verification is complete, you’ll be able to use your card again.",
							ImageUrl:        "",
							CardRequestType: ffEnumsPb.CardRequestType_CARD_REQUEST_TYPE_UNFREEZE_CARD,
							Cta: &deeplink.Cta{
								Text: "Unfreeze Card",
							},
							IsDeviceUnlockRequired: true,
						}},
					},
				},
			},
		}
	case !beRes.GetEcomEnabled() && !lo.Contains[ffEnumsBePb.CardState](activatedStates, beRes.GetCardState()):
		res.RespHeader.Status = rpc.StatusOk()
		res.CardComponent = &ffPb.GetDashboardCardInfoResponse_EcomDisabledCardComponent{
			EcomDisabledCardComponent: &ffPb.EcomDisabledCardComponent{
				CardControlType:  ffEnumsPb.CardControlType_CARD_CONTROL_TYPE_ECOM,
				EcomDisabledText: fmt.Sprintf("Enable online payments to start using your %s credit limit", moneyPb.ToDisplayStringInIndianFormat(beRes.GetTotalLimit(), 0, true)),
				EnableOnlineTxn: &deeplink.Cta{
					Text: "Enable online use",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_INITIATE_CARD_REQUEST_SCREEN,
						ScreenOptions: &deeplink.Deeplink_InitiateCardRequestScreenOptions{
							InitiateCardRequestScreenOptions: &deeplink.InitiateCardRequestScreenOptions{
								CardId:                 req.GetCardId(),
								Workflow:               ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_CONTROL_CHANGE,
								IsDeviceUnlockRequired: true,
							},
						},
					},
				},
			},
		}
	default:
		limitAvailableFloat, _ := moneyPb.ToDecimal(beRes.GetLimitAvailable()).Float64()
		totalLimitFloat, _ := moneyPb.ToDecimal(beRes.GetTotalLimit()).Float64()
		fractionLimitAvailable := 0.0
		if totalLimitFloat != 0 {
			fractionLimitAvailable = limitAvailableFloat / totalLimitFloat
		}
		res.RespHeader.Status = rpc.StatusOk()
		res.CardComponent = &ffPb.GetDashboardCardInfoResponse_MaskedDetailsCardComponent{
			MaskedDetailsCardComponent: &ffPb.MaskedDetailsCardComponent{
				MaskedCardNumber:   beRes.GetMaskedCardNumber(),
				OutstandingBalance: types.GetFromBeMoney(beRes.GetLimitUtilised()),
				AvailableBalance:   types.GetFromBeMoney(beRes.GetLimitAvailable()),
				TotalBalance:       types.GetFromBeMoney(beRes.GetTotalLimit()),
				LimitUtilised: &deeplink.InfoItem{
					Title: "Limit Utilised",
					Desc:  moneyPb.ToDisplayStringInIndianFormat(beRes.GetLimitUtilised(), 0, true),
				},
				LimitAvailable: &deeplink.InfoItem{
					Title: "Available: ",
					Desc:  moneyPb.ToDisplayStringInIndianFormat(beRes.GetLimitAvailable(), 0, true),
				},
				FractionLimitAvailable: fractionLimitAvailable,
				ViewCardDetails: &deeplink.Cta{
					Text: "View card",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_INITIATE_CARD_REQUEST_SCREEN,
						ScreenOptions: &deeplink.Deeplink_InitiateCardRequestScreenOptions{InitiateCardRequestScreenOptions: &deeplink.InitiateCardRequestScreenOptions{
							CardId:                 req.GetCardId(),
							Workflow:               ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_VIEW_CARD_DETAILS,
							IsDeviceUnlockRequired: true,
						},
						},
					},
				},
				Settings: &deeplink.Cta{
					Deeplink: helper.GetCreditCardControlsScreenDeeplink(beRes.GetCardId()),
				},
				OutstandingBalanceTooltip: &deeplink.InfoToolTip{
					Info: &deeplink.InfoBlock{
						Title: "Limit Utilised",
						Desc:  fmt.Sprintf("The limit utilised on a credit card is the total amount of money that you have charged on your credit card that you have not yet paid off. \n\nIt includes any charges, fees, and interest that have been added to your account. \n\nYour total credit limit: %s", moneyPb.ToDisplayStringInIndianFormat(beRes.GetTotalLimit(), 0, true)),
					},
				},
			},
		}
	}
	return res, nil
}

func (s *Service) getEmiDetailsForTxn(ctx context.Context, actorId, txnId string) (*deeplink.InfoItemWithCtaV2, error) {
	laRes, err := s.fireflyLmsClient.GetLoanAccountByTransactionId(ctx, &ffLmsPb.GetLoanAccountByTransactionIdRequest{
		TransactionId: txnId,
	})
	if grpcErr := epifigrpc.RPCError(laRes, err); grpcErr != nil {
		if laRes.GetStatus().IsRecordNotFound() {
			return nil, nil
		}
		logger.Error(ctx, "error in GetLoanAccountByTransactionId", zap.Error(grpcErr))
		return nil, grpcErr
	}

	return &deeplink.InfoItemWithCtaV2{
		Info: &deeplink.InfoItemV2{
			Title: typesPkg.NewText(&typesPkg.Text{
				FontColor:         "#ECEEF0",
				PlainString:       "Transaction converted to EMI.",
				StandardFontStyle: "BODY_XS",
				CustomFontStyle:   nil,
			}, ""),
		},
		Cta: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "View Details.",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_EMI_LOAN_ACCOUNT_DETAILS_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CreditCardEmiLoanAccountDetailsScreenOptions{
					CreditCardEmiLoanAccountDetailsScreenOptions: &deeplink.CreditCardEmiLoanAccountDetailsScreenOptions{
						LoanAccountId: laRes.GetLoanAccount().GetId(),
					},
				},
			},
		},
	}, nil
}

// nolint: funlen
func (s *Service) FetchTxnReceipt(ctx context.Context, req *ffPb.FetchTxnReceiptRequest) (*ffPb.FetchTxnReceiptResponse, error) {
	var (
		res     = &ffPb.FetchTxnReceiptResponse{RespHeader: &header.ResponseHeader{}}
		lmsRes  = &ffLmsPb.CheckEmiEligibilityOfTransactionResponse{IsEligible: false}
		emiInfo = &deeplink.InfoItemWithCtaV2{}
		err     error
	)

	// async check for emi eligibility of a transaction
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 7*time.Second)
	defer cancel()
	g, gctx := errgroup.WithContext(ctxWithTimeout)
	g.Go(func() error {
		lmsRes, err = s.fireflyLmsClient.CheckEmiEligibilityOfTransaction(gctx, &ffLmsPb.CheckEmiEligibilityOfTransactionRequest{
			ActorId:       req.GetReq().GetAuth().GetActorId(),
			TransactionId: req.GetTxnId(),
		})
		if te := epifigrpc.RPCError(lmsRes, err); te != nil {
			return te
		}
		return nil
	})

	g.Go(func() error {
		emiInfo, err = s.getEmiDetailsForTxn(ctx, req.GetReq().GetAuth().GetActorId(), req.GetTxnId())
		if err != nil {
			// making failures here as non-blocking - show the best effort screen for txn receipt
			logger.Error(ctx, "error in getEmiDetailsForTxn", zap.Error(err))
		}

		return nil
	})

	beRes, err := s.fireflyAccountingClient.FetchTxnReceipt(ctx, &ffBeAccountsPb.FetchTxnReceiptRequest{TxnId: req.GetTxnId()})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in FetchTxnReceipt BE RPC", zap.Error(te))
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg(te.Error())
		return res, nil
	}

	paidText := "PAID TO"
	if beRes.GetTxnType() == ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT {
		paidText = "RECEIVED FROM"
	}
	dateAndTimeStr := fetchTxnDateAndTime(beRes.GetTxnTime())
	displayCategories := make([]*ffPb.TransactionDisplayCategory, 0)
	if len(beRes.GetTransactionDisplayCategories()) != 0 {
		displayCategories, err = s.getDisplayCategories(ctx, beRes.GetTransactionDisplayCategories(), req.GetReq())
		if err != nil {
			logger.Error(ctx, "error in fetching display categories", zap.Error(err))
			res.RespHeader.Status = rpc.StatusFromError(err)
			return res, nil
		}
	}

	additionalCtas := make([]*deeplink.Cta, 0)
	disputeRes, err := s.IsDisputeAllowed(ctx, &ffPb.IsDisputeAllowedRequest{
		Req:   req.GetReq(),
		TxnId: req.GetTxnId(),
	})
	switch {
	case disputeRes.GetRespHeader().GetStatus().IsSuccess():
		additionalCtas = append(additionalCtas, &deeplink.Cta{
			Text:     "Raise dispute",
			Deeplink: disputeRes.GetNextAction(),
		})
	case disputeRes.GetRespHeader().GetStatus().IsAlreadyExists():
		logger.Info(ctx, "dispute already exists for the given txn id", zap.String(logger.TXN_ID, req.GetTxnId()))
	default:
		logger.Error(ctx, fmt.Sprintf("error in checking if dispute is allowed for the txn : %v", disputeRes.GetRespHeader().GetStatus()), zap.String(logger.TXN_ID, req.GetTxnId()))
	}
	rewardInfo, err := s.getReceiptRewardInfo(ctx, beRes.GetExternalTxnId(), req.GetReq().GetAuth().GetActorId())
	if err != nil {
		logger.Error(ctx, "error in fetching reward info for receipt", zap.String(logger.TXN_ID, beRes.GetExternalTxnId()), zap.Error(err))
	}

	additionalCtas = append(additionalCtas, &deeplink.Cta{
		Text: "Refresh",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_TRANSACTION_RECEIPT,
			ScreenOptions: &deeplink.Deeplink_CreditCardTransactionReceiptScreenOptions{
				CreditCardTransactionReceiptScreenOptions: &deeplink.CreditCardTransactionReceiptScreenOptions{
					TxnId: req.GetTxnId(),
				},
			},
		},
	})

	txnStatus, ok := beTxnStatusToFeTxnStatus[beRes.GetTxnStatus()]
	if !ok {
		logger.Error(ctx, "unknown txn status received from txn", zap.String(logger.TXN_ID, req.GetTxnId()))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	// wait for emi check response
	// ignore the error; in case of any error, emi eligibility will be false
	// and no cta will be returned
	if err = g.Wait(); err != nil {
		logger.Error(ctx, "error in checking EMI eligibility from lms client", zap.Error(err))
	}

	res = &ffPb.FetchTxnReceiptResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Receipt: &ffPb.TransactionReceipt{
			IconUrl:   beRes.GetIconUrl(),
			TxnStatus: txnStatus,
			ReceiptInfoItems: []*deeplink.InfoItem{
				{
					Title: "FROM",
					Desc:  beRes.GetFromName(),
				},
				{
					Title: "TO",
					Desc:  beRes.GetToName(),
				},
				{
					Title: "TRANSACTION MODE",
					Desc:  "Credit Card",
				},
				{
					Title: "FI TRANSACTION ID",
					Desc:  beRes.GetExternalTxnId(),
				},
			},
			TxnTimeString:        dateAndTimeStr,
			PartnerUrl:           beRes.GetPartnerUrl(),
			HelpUrl:              "Something wrong? <a href=\"" + "" + "\">Get Help</a>",
			TxnDisplayCategories: displayCategories,
			TxnReceiptHead: &ffPb.ReceiptHead{
				PaidText:       paidText,
				OtherPartyName: beRes.GetOtherPartyName(),
				OtherPartyIcon: beRes.GetIconUrl(),
				Amount:         types.GetFromBeMoney(beRes.GetAmount()),
				Remarks:        beRes.GetRemarks(),
			},
			AdditionalActions: additionalCtas,
			RewardInfo:        rewardInfo,
			EmiDetailsInfo:    emiInfo,
		},
	}
	if beRes.GetVendorExternalTxnId() != "" {
		res.Receipt.ReceiptInfoItems = append(res.GetReceipt().GetReceiptInfoItems(), &deeplink.InfoItem{
			Title: "TRANSACTION ID",
			Desc:  beRes.GetVendorExternalTxnId(),
		})
	}

	if beRes.GetReceiptAdditionalDetails() != nil {
		switch beRes.GetReceiptAdditionalDetails().GetDetails().(type) {
		case *ffBeAccountsPb.ReceiptAdditionalDetails_FeesAdditionalDetails:
			feesAdditionalDetails := beRes.GetReceiptAdditionalDetails().GetFeesAdditionalDetails()
			res.Receipt.ReceiptInfoItems = append(res.Receipt.ReceiptInfoItems, &deeplink.InfoItem{
				Title: "ADDITIONAL DETAIL",
				Desc:  feesAdditionalDetails.GetDescription(),
			})
		}
	}

	// EMI conversion from txn receipt entry point
	if lmsRes.GetIsEligible() {
		res.Receipt.EmiCta = &deeplink.Cta{
			Text: "Convert to EMI",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_EMI_TRANSACTION_LOAN_OFFERS_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CreditCardEmiTransactionLoanOffersScreenOptions{
					CreditCardEmiTransactionLoanOffersScreenOptions: &deeplink.CreditCardEmiTransactionLoanOffersScreenOptions{
						ExternalTransactionId: beRes.GetExternalTxnId(),
					},
				},
			},
		}
	}

	return res, nil
}

func (s *Service) getReceiptRewardInfo(ctx context.Context, extTxnId string, actorId string) (*ffPb.TransactionReceipt_RewardInfo, error) {

	rewardsResp, err := s.rewardsClient.GetAllRewardsAndProjections(ctx, &rewardsPb.GetAllRewardsAndProjectionRequest{
		ActorId:    actorId,
		ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION,
		RefIds:     []string{extTxnId},
	})
	if te := epifigrpc.RPCError(rewardsResp, err); te != nil {
		logger.Error(ctx, "error in GetAllRewardsAndProjections api", zap.Error(te))
		return nil, pkgErr.Wrap(te, "error fetching rewards")
	}

	fiCoinUrl := ""
	rewardUnitsNegative := false
	var receiptRewardInfo *ffPb.TransactionReceipt_RewardInfo
	var selectedRewardForDisplay *rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity
	for _, rewardInfo := range rewardsResp.GetRefIdToRewardEntitiesMap() {
		selectedRewardForDisplay, fiCoinUrl = s.getSelectedRewardAndIconFromRewardEntity(rewardInfo)
	}
	if selectedRewardForDisplay != nil && len(selectedRewardForDisplay.GetRewardOptions()) != 0 &&
		selectedRewardForDisplay.GetRewardOptions()[0].GetRewardType() == rewardsPb.RewardType_FI_COINS {
		rewardUnits := selectedRewardForDisplay.GetRewardOptions()[0].GetUnits()
		if rewardUnits < 0 {
			rewardUnitsNegative = true
		}
		receiptRewardInfo = &ffPb.TransactionReceipt_RewardInfo{
			Title:    "Fi-Coins",
			Icon:     fiCoinUrl,
			FiCoins:  moneyPb.ToDisplayStringFromFloatValue(rewardUnits, 0),
			Negative: rewardUnitsNegative,
		}
	}
	return receiptRewardInfo, nil
}

// processing of display category API response to fetch the text and icon for each display category
func (s *Service) getDisplayCategories(ctx context.Context, displayCategories []categorizerBePb.DisplayCategory, reqHeader *header.RequestHeader) ([]*ffPb.TransactionDisplayCategory, error) {
	strDispCategories := make([]string, 0)
	for _, category := range displayCategories {
		strDispCategories = append(strDispCategories, category.String())
	}
	displayCategory, err := s.txnCategorizerFeClient.GetCategoryDetailsBatch(ctx, &categorizerFePb.GetCategoryDetailsBatchRequest{
		Req:             reqHeader,
		DisplayCategory: strDispCategories,
	})
	if te := epifigrpc.RPCError(displayCategory.GetRespHeader(), err); te != nil {
		return nil, te
	}

	displayCategoriesFe := make([]*ffPb.TransactionDisplayCategory, 0)
	for key, category := range displayCategory.GetCategoryDetails() {
		displayCategoriesFe = append(displayCategoriesFe, &ffPb.TransactionDisplayCategory{
			CategoryTitle:          category.GetDisplayName(),
			TxnDisplayCategoryIcon: category.GetIconUrl(),
			CategoryId:             key,
		})
	}
	return displayCategoriesFe, nil
}

// fetching the formatted string to display in the receipt
func fetchTxnDateAndTime(txnTimeStamp *timestamppb.Timestamp) string {
	_, txnMonth, txnDay := txnTimeStamp.AsTime().Date()
	txnTime := txnTimeStamp.AsTime().In(datetime.IST)
	txnTimeString, _ := datetime.GetTimeStringWithMeridianInfo(&txnTime)
	return fmt.Sprintf("%s %d at %s", txnMonth, txnDay, txnTimeString)
}

// nolint: dupl
func (s *Service) GetCreditCardPaymentStatus(ctx context.Context, req *ffPb.GetCreditCardPaymentStatusRequest) (*ffPb.GetCreditCardPaymentStatusResponse, error) {
	var (
		res        = &ffPb.GetCreditCardPaymentStatusResponse{}
		nextAction *deeplink.Deeplink
		statusErr  error
	)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.GetCreditCardPaymentStatusResponse, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}

	if req.GetOrderId() != "" {
		nextAction, statusErr = s.getOrderStatus(ctx, req)
	} else {
		nextAction, statusErr = s.getFundTransferStatus(ctx, req)
	}
	if statusErr != nil {
		logger.Error(ctx, "error in fetching payment status", zap.Error(statusErr))
		return responseWithStatus(rpc.StatusInternal(), nil)
	}
	res.Deeplink = nextAction
	return responseWithStatus(rpc.StatusOk(), nil)
}

func (s *Service) getOrderStatus(ctx context.Context, req *ffPb.GetCreditCardPaymentStatusRequest) (*deeplink.Deeplink, error) {
	payRes, err := s.payFeTxnClient.GetOrderStatus(ctx, &payFeTxnPb.GetOrderStatusRequest{
		Req:     req.GetReq(),
		OrderId: req.GetOrderId(),
	})
	if te := epifigrpc.RPCError(payRes.GetRespHeader(), err); te != nil &&
		payRes.GetRespHeader().GetStatus().GetCode() != uint32(payFeTxnPb.InitiatePaymentResponse_PAYMENT_FAILED) {
		return nil, pkgErr.Wrap(te, "error in fetching order status")
	}
	// handling payment failure status separately since GetOrderStatus RPC send status in ResponseHeader for payment failure
	if payRes.GetRespHeader().GetStatus().GetCode() == uint32(payFeTxnPb.InitiatePaymentResponse_PAYMENT_FAILED) {
		return helper.GetPaymentStatusNextAction(ffEnumsBePb.PaymentStatus_PAYMENT_STATUS_FAILED), nil
	}
	switch payRes.GetOrder().GetStatus() {
	case payFePb.OrderStatus_PAYMENT_SUCCESS:
		return helper.GetPaymentStatusNextAction(ffEnumsBePb.PaymentStatus_PAYMENT_STATUS_SUCCESS), nil
	case payFePb.OrderStatus_COLLECT_REGISTRATION_FAILED,
		payFePb.OrderStatus_COLLECT_DECLINED,
		payFePb.OrderStatus_COLLECT_CANCELLED,
		payFePb.OrderStatus_PAYMENT_FAILED,
		payFePb.OrderStatus_COLLECT_EXPIRED:
		return helper.GetPaymentStatusNextAction(ffEnumsBePb.PaymentStatus_PAYMENT_STATUS_FAILED), nil
	default:
		return helper.GetCreditCardPaymentStatusPollingScreen(req.GetPaymentReqId(), req.GetOrderId(), req.GetAttemptNumber()), nil
	}
}

func (s *Service) getFundTransferStatus(ctx context.Context, req *ffPb.GetCreditCardPaymentStatusRequest) (*deeplink.Deeplink, error) {
	beRes, err := s.fireFlyClient.GetCreditCardPaymentStatus(ctx, &ffBePb.GetCreditCardPaymentStatusRequest{
		PaymentReqId:  req.GetPaymentReqId(),
		AttemptNumber: req.GetAttemptNumber(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		return nil, pkgErr.Wrap(te, "error in getting fund transfer status")
	}
	return beRes.GetDeeplink(), nil
}

func (s *Service) GetBillPaymentDetails(ctx context.Context, req *ffPb.GetBillPaymentDetailsRequest) (*ffPb.GetBillPaymentDetailsResponse, error) {

	var (
		res                = &ffPb.GetBillPaymentDetailsResponse{}
		cardInfo           = &ffBePb.GetCreditCardResponse{}
		cardErr            error
		actorInfo          = &actorPb.GetActorByIdResponse{}
		actorErr           error
		savingsAccInfo     = &savingsPb.GetAccountResponse{}
		savingsAccErr      error
		billInfo           = &ffBeBillingPb.GetCreditCardBillResponse{}
		billErr            error
		accountInfo        = &ffBeAccountsPb.GetAccountResponse{}
		accountErr         error
		dueInfo            = &ffBeAccountsPb.GetCreditAccountDueInformationResponse{}
		limitInfo          = &ffBeAccountsPb.GetCreditAccountLimitUtilisationResponse{}
		fetchDueErr        error
		fetchLimitErr      error
		accountBalanceInfo = &accountBalancePb.GetAccountBalanceResponse{}
		accountBalanceErr  error
	)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.GetBillPaymentDetailsResponse, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		cardInfo, cardErr = s.fireFlyClient.GetCreditCard(grpCtx, &ffBePb.GetCreditCardRequest{
			GetBy: &ffBePb.GetCreditCardRequest_CreditCardId{CreditCardId: req.GetCardId()},
		})
		if te := epifigrpc.RPCError(cardInfo, cardErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching card info").Error())
			return pkgErr.Wrap(te, "error in fetching card info")
		}
		if cardInfo.GetCreditCard().GetActorId() != req.GetReq().GetAuth().GetActorId() {
			return pkgErr.Wrap(epifierrors.ErrPermissionDenied, "actor id from request does not match the one in card")
		}
		accountInfo, accountErr = s.fireflyAccountingClient.GetAccount(ctx, &ffBeAccountsPb.GetAccountRequest{
			GetBy: &ffBeAccountsPb.GetAccountRequest_AccountId{AccountId: cardInfo.GetCreditCard().GetAccountId()},
		})
		if te := epifigrpc.RPCError(accountInfo, accountErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching account info").Error())
			return pkgErr.Wrap(te, "error in fetching account info")
		}

		return nil
	})
	grp.Go(func() error {
		actorInfo, actorErr = s.actorClient.GetActorById(grpCtx, &actorPb.GetActorByIdRequest{
			Id: req.GetReq().GetAuth().GetActorId(),
		})
		if te := epifigrpc.RPCError(actorInfo, actorErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching actor info").Error())
			return pkgErr.Wrap(te, "error in fetching actor info")
		}
		savingsAccInfo, savingsAccErr = s.savingsClient.GetAccount(grpCtx,
			&savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
				PrimaryUserId: actorInfo.GetActor().GetEntityId(),
			}})
		statusFromErr, ok := status.FromError(savingsAccErr)
		switch {
		case ok && statusFromErr.Code() == codes.NotFound:
			// not returning error for cases such as fi-lite where we will allow payment
			// using a tpap
			logger.Info(ctx, "savings account does not exist for the user")
			return nil
		case savingsAccErr != nil:
			return pkgErr.Wrap(savingsAccErr, "error in fetching saving account details")
		case savingsAccInfo.GetAccount() == nil:
			return pkgErr.Wrap(fmt.Errorf("error: %s", "No savings account record found"), "error in fetching saving account details")
		default:
			accountBalanceInfo, accountBalanceErr = s.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
				Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: savingsAccInfo.GetAccount().GetId()},
				ActorId:       actorInfo.GetActor().GetId(),
				DataFreshness: accountBalEnumsPb.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
			})
			if te := epifigrpc.RPCError(accountBalanceInfo, accountBalanceErr); te != nil {
				logger.Error(ctx, pkgErr.Wrap(te, "error in fetching account balance").Error())
				return pkgErr.Wrap(te, "error in fetching account balance")
			}
			return nil
		}
	})
	grp.Go(func() error {
		billInfo, billErr = s.ffBillingClient.GetCreditCardBill(grpCtx, &ffBeBillingPb.GetCreditCardBillRequest{
			GetBy: &ffBeBillingPb.GetCreditCardBillRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
		})
		if te := epifigrpc.RPCError(billInfo, billErr); te != nil {
			if billInfo.GetStatus().IsRecordNotFound() {
				logger.Info(ctx, "No billing info available for given actor id.")
				return nil
			}
			logger.Error(ctx, pkgErr.Wrap(te, "error in latest bill details info from billing client").Error())
			return pkgErr.Wrap(te, "error in fetching latest bill details info from billing client")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in data aggregation", zap.Error(err), zap.String(logger.CARD_ID, req.GetCardId()))
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}
	grp, grpCtx = errgroup.WithContext(ctx)
	grp.Go(func() error {
		dueInfo, fetchDueErr = s.fireflyAccountingClient.GetCreditAccountDueInformation(ctx, &ffBeAccountsPb.GetCreditAccountDueInformationRequest{
			GetBy: &ffBeAccountsPb.GetCreditAccountDueInformationRequest_ReferenceId{ReferenceId: accountInfo.GetAccount().GetReferenceId()},
		})
		if te := epifigrpc.RPCError(dueInfo, fetchDueErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching due info for the user")
		}
		return nil
	})
	grp.Go(func() error {
		limitInfo, fetchLimitErr = s.fireflyAccountingClient.GetCreditAccountLimitUtilisation(ctx, &ffBeAccountsPb.GetCreditAccountLimitUtilisationRequest{
			GetBy: &ffBeAccountsPb.GetCreditAccountLimitUtilisationRequest_ReferenceId{ReferenceId: accountInfo.GetAccount().GetReferenceId()},
		})
		if te := epifigrpc.RPCError(limitInfo, fetchLimitErr); te != nil {
			return pkgErr.Wrap(te, "error in fetching limit info for the user")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}

	res.AmountDetails = getCardPaymentFeAmountDetails(dueInfo, limitInfo)
	if savingsAccInfo != nil {
		res.AccountDetails = getCardPaymentFeAccountDetails(savingsAccInfo.GetAccount(), accountBalanceInfo.GetAvailableBalance())
	}
	res.BottomBannerDetails = getCardPaymentFeBottomBannerDetails()
	res.TopBannerDetails = getCardPaymentFeTopBannerDetailsByDueDate(cardInfo.GetCreditCard().GetBasicInfo().GetPaymentDueDate(), dueInfo, s.dynamicConf.Flags().EnableCreditCardRepaymentScreenTopBanner())
	res.PartnerLogoUrl = internal.PartnerLogoUrl
	res.InsufficientBalance = getInsufficientBalanceCta()
	// In cases we don't have a bill, swipe to total due will be zero
	swipeToPayDefaultAmount := moneyPb.ZeroINR().GetPb()
	if billInfo.GetCreditCardBill() != nil {
		// In cases we have a bill, swipe to pay cta will send unpaid total due as default.
		swipeToPayDefaultAmount = dueInfo.GetUnpaidTotalDue()
	}
	swipeToPayCta, err := getSwipeToPayCta(swipeToPayDefaultAmount, req.GetCardId())
	if err != nil {
		logger.Error(ctx, pkgErr.Wrap(err, "Error in fetching swipe to pay cta").Error())
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}
	res.SwipeToPay = swipeToPayCta
	res.CustomAmount = s.getCustomAmountCta(ctx, req.GetCardId(), req.GetReq().GetAuth().GetActorId(), res.GetAccountDetails())
	res.CreditCardHeader = s.GetCreditCardHeader(accountInfo.GetAccount().GetCardProgram())
	res.AddAccount = getAddAccountCta()
	tpapEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_CREDIT_CARD_TPAP_PAYMENTS).WithActorId(req.GetReq().GetAuth().GetActorId()))
	if err != nil {
		logger.Error(ctx, "error in checking tpap eligibility", zap.Error(err))
		// not returning error to avoid SPOF for credit card payments
	}
	res.EnableTpap = tpapEnabled
	return responseWithStatus(rpc.StatusOk(), nil)
}

func getAddAccountCta() *deeplink.Cta {
	return &deeplink.Cta{
		Text: "Connect bank account",
		Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplink.Screen_LIST_ACCOUNT_PROVIDER_SCREEN, &upiScreenOptions.ListAccountProviderScreenOptions{
			UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
		}),
	}
}

func (s *Service) GetCardSummaryForHome(ctx context.Context, req *ffPb.GetCardSummaryForHomeRequest) (*ffPb.GetCardSummaryForHomeResponse, error) {
	var (
		res = &ffPb.GetCardSummaryForHomeResponse{
			RespHeader:    &header.ResponseHeader{},
			DashboardInfo: &homePb.HomeDashboard{},
		}
		err     error
		actorId = req.GetReq().GetAuth().GetActorId()
	)

	isCCUserResp, err := s.fireFlyClient.IsCreditCardUser(ctx, &ffBePb.IsCreditCardUserRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(isCCUserResp, err); te != nil {
		logger.Error(ctx, "error in credit card user check", zap.Error(te))
		return &ffPb.GetCardSummaryForHomeResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(te.Error()),
			},
		}, nil
	}
	if !isCCUserResp.GetIsCreditCardUser() {
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusRecordNotFound(),
		}
		return res, nil
	}

	homeDashboardData, err := s.getHomeDashboardData(ctx, req.GetReq().GetAuth().GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getHomeDashboardData", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}
	if !lo.Contains(validDashboardCardTypesToShowCcSummary, homeDashboardData.GetHomeDashboardCardType()) {
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusRecordNotFound(),
		}
		return res, nil
	}

	homeDashboardData.DashboardVersion = req.GetDashboardVersion()
	homeDashboardData.ZeroStateDashboardCardVariant = req.GetZeroStateDashboardVariant()
	homeDashboardComponentsFetcher, err := s.homeDashboardFactory.GetCardSummaryForHomeImpl(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in homeDashboardComponentsFetcher", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.DashboardInfo.Title, err = homeDashboardComponentsFetcher.GetTitle(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in getting title for home dashboard", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.DashboardInfo.Body, err = homeDashboardComponentsFetcher.GetBody(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in getting body for home dashboard", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.DashboardInfo.Footer, err = homeDashboardComponentsFetcher.GetFooter(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in getting footer for home dashboard", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.DashboardInfo.Deeplink, err = homeDashboardComponentsFetcher.GetDeeplink(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in getting bodyDeeplink for home dashboard", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.DashboardInfo.AdditionalSettingsIcons, err = homeDashboardComponentsFetcher.GetAdditionalSettingsIcons(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in getting additionalSettingsIcons for home dashboard", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.DashboardInfo.Shadow, err = homeDashboardComponentsFetcher.GetShadow(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in getting shadow for home dashboard", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.DashboardInfo.PrivacyModeFooter, err = homeDashboardComponentsFetcher.GetPrivacyModeFooter(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in getting privacyModeFooter for home dashboard", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.DashboardInfo.ZeroStateImage, err = homeDashboardComponentsFetcher.GetZeroStateImage(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in getting zeroStateImage for home dashboard", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}

	res.DashboardInfo.FooterTicker, err = homeDashboardComponentsFetcher.GetFooterTicker(ctx, homeDashboardData)
	if err != nil {
		logger.Error(ctx, "error in getting footerTicker for home dashboard", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusFromError(err)
		return res, nil
	}
	res.DashboardInfo.DashboardFooter = &homePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: res.GetDashboardInfo().GetFooterTicker()}

	res.DashboardInfo.DashboardBackground = homePb.GetHomeDashboardSectionBackground()

	res.GetRespHeader().Status = rpc.StatusOk()
	return res, nil
}

func getFiLiteZeroState(onbStatus onbPb.FeatureStatus) (*ffPb.GetCardSummaryForHomeResponse, error) {
	dl, err := pkgOnb.GetCCBenefitsScreen()
	if err != nil {
		return nil, err
	}

	if onbStatus == onbPb.FeatureStatus_FEATURE_STATUS_ONBOARDING_FAILURE {
		dl = pkgOnb.GetOnboardingFailureScreenForCC()
	}

	footerTicker := &homePb.HomeDashboard_FooterTicker{
		TickerItems: []*homePb.HomeDashboard_FooterTicker_TickerItem{
			{
				TickerContent: ui.NewITC().
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Join Waitlist", "#00B899", commontypes.FontStyle_SUBTITLE_S)),
			},
		},
	}
	return &ffPb.GetCardSummaryForHomeResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		DashboardInfo: &homePb.HomeDashboard{
			Title: commontypes.GetTextFromStringFontColourFontStyle("Credit Card", "#878A8D", commontypes.FontStyle_SUBTITLE_S),
			Body: &homePb.HomeDashboard_Body{
				DashboardState: homePb.HomeDashboard_Body_STATE_ZERO,
				MoneyValueV2: []*ui.IconTextComponent{
					{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Get 5% valueback on credit \ncard spends", "#FFFFFF", commontypes.FontStyle_HEADLINE_M),
						},
						Deeplink: dl,
					},
				},
			},
			Footer: []*ui.IconTextComponent{
				{
					RightIcon: commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"),
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle("Join Waitlist", "#00B899", commontypes.FontStyle_SUBTITLE_S),
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"),
				},
			},
			FooterTicker:        footerTicker,
			DashboardFooter:     &homePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
			Deeplink:            dl,
			Shadow:              ui.GetDashboardShadow(),
			ZeroStateImage:      commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/credit_card.png"),
			DashboardBackground: homePb.GetHomeDashboardSectionBackground(),
			BorderColor:         homePb.GetHomeDashboardBorderColor(),
		},
	}, nil
}

// There are three stages to get the Be Data for Home Dashboard
// Stage-1:
// -Fetch the credit card using GetCreditCard Be RPC.
// -If credit card exists return the response with card amount info.
// -Otherwise proceed to the next stage
// Stage-2:
// -Fetch the card request and card request stages using GetCardRequestAndCardRequestStage Be RPC
// -If user has started application, return the response based on active card stage sub status
// -Otherwise proceed to the next stage
// Stage-3:
// -Fetch the credit card conservative limit using GetCreditCardConservativeLimit Be RPC.
// -If the RPC response error code is StatusRecordNotFound or StatusResourceExhausted, return Hop on wait-list/not eligible for credit card response.
// -Otherwise return Get your card today response.
func (s *Service) getHomeDashboardData(ctx context.Context, actorId string) (*ffPb.HomeDashboardData, error) {
	var (
		res               = &ffPb.HomeDashboardData{}
		cardRequestStatus ffEnumsBePb.CardRequestStatus
		err               error
	)

	// Stage-1:
	ccRes, rpcErr := s.fireFlyClient.GetCreditCard(ctx, &ffBePb.GetCreditCardRequest{
		GetBy: &ffBePb.GetCreditCardRequest_ActorId{ActorId: actorId},
	})
	switch {
	case ccRes.GetStatus().IsRecordNotFound():
		// credit card does not exist
	default:
		if err = epifigrpc.RPCError(ccRes, rpcErr); err != nil {
			return nil, pkgErr.Wrap(err, "error in the GetCreditCard Be RPC")
		}
	}

	// credit card already exists
	if ccRes.GetCreditCard() != nil {
		getAccountRes, err := s.fireflyAccountingClient.GetAccount(ctx, &ffAccPb.GetAccountRequest{
			GetBy: &ffAccPb.GetAccountRequest_AccountId{
				AccountId: ccRes.GetCreditCard().GetAccountId(),
			},
		})
		if te := epifigrpc.RPCError(getAccountRes, err); te != nil {
			logger.Error(ctx, "error in fetching account by id", zap.Error(te), zap.String(logger.ACCOUNT_ID,
				ccRes.GetCreditCard().GetAccountId()))
			return nil, pkgErr.Wrap(err, "error in getting credit account details for card issued")
		}

		isCardFreeze := false

		// card is freeze
		if ccRes.GetCreditCard().GetCardState() == ffEnumsBePb.CardState_CARD_STATE_SUSPENDED {
			isCardFreeze = true
		}

		if ccRes.GetCreditCard().GetCardState() == ffEnumsBePb.CardState_CARD_STATE_CLOSED {
			return &ffPb.HomeDashboardData{
				HomeDashboardCardType: ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_CREDIT_CARD_CLOSED,
				CardProgram:           getAccountRes.GetAccount().GetCardProgram(),
			}, nil
		}

		res, err := s.getHomeCardAmountInfo(ctx, actorId, ccRes.GetCreditCard().GetId(), isCardFreeze,
			getAccountRes.GetAccount().GetCardProgram())
		if err != nil {
			return nil, pkgErr.Wrap(err, "error in getting credit card details for card issued")
		}
		return res, nil
	}

	// Stage-2:
	cardRequestAndCardRequestStage, rpcErr := s.fireFlyClient.GetCardRequestAndCardRequestStage(ctx, &ffBePb.GetCardRequestAndCardRequestStageRequest{
		ActorId:             actorId,
		CardRequestWorkflow: ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	switch {
	case cardRequestAndCardRequestStage.GetStatus().IsRecordNotFound():
	// user has not started credit card application
	default:
		if err = epifigrpc.RPCError(cardRequestAndCardRequestStage, rpcErr); err != nil {
			return nil, pkgErr.Wrap(err, "error in the GetCardRequestAndCardRequestStage Be RPC")
		}
	}

	// user has started the credit card application
	if len(cardRequestAndCardRequestStage.GetCardRequestStages()) > 0 {
		res, cardRequestStatus, err = getCardApplicationStatus(cardRequestAndCardRequestStage)
		if err != nil {
			return nil, pkgErr.Wrap(err, "error in getting credit card details for card not issued")
		}

		// if application stage is NOT in_progress, return the response
		// for application_in_progress state, check if active offer exists for the user or not
		if res.GetHomeDashboardCardType() != ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_APPLICATION_IN_PROGRESS {
			return res, nil
		}
	}

	// Stage-3:
	limitResp, rpcErr := s.limitEstimator.GetCreditCardConservativeLimit(ctx, &limitEstimatorPb.GetCreditCardConservativeLimitRequest{
		ActorId: actorId,
		Vendor:  ffEnumsBePb.Vendor_FEDERAL,
	})

	switch {
	case limitResp.GetStatus().IsRecordNotFound(), limitResp.GetStatus().IsResourceExhausted():
		// user is not currently eligible for credit card
		return &ffPb.HomeDashboardData{
			HomeDashboardCardType: ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_NOT_ELIGIBLE_FOR_CARD,
		}, nil
	default:
		if err = epifigrpc.RPCError(limitResp, rpcErr); err != nil {
			return nil, pkgErr.Wrap(err, "error in the GetCreditCardConservativeLimit Be RPC")
		}

		// user has the active offer
		// if the application is in progress, return the application_in_progress card type
		if res.GetHomeDashboardCardType() == ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_APPLICATION_IN_PROGRESS &&
			cardRequestStatus != ffEnumsBePb.CardRequestStatus_CARD_REQUEST_STATUS_FAILED {
			return res, nil
		}
	}

	// user has not started the credit card application
	return &ffPb.HomeDashboardData{
		HomeDashboardCardType: ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_GET_YOUR_CARD,
		CardProgram:           limitResp.GetCardProgram(),
	}, nil
}

func (s *Service) getHomeCardAmountInfo(ctx context.Context, actorId string, creditCardId string, isCardFreeze bool,
	cardProgram *types.CardProgram) (*ffPb.HomeDashboardData, error) {
	var (
		err                   error
		cardResp              = &ffBePb.GetDashboardCardInfoResponse{}
		billInfo              = &ffBeBillingPb.GetCreditCardBillResponse{}
		dueInfoResp           = &ffBeAccountsPb.GetCreditAccountDueInformationResponse{}
		homeDashboardCardType ffEnumsPb.HomeDashboardCardType
		billAmount            = &money.Money{}
		billFromDate          *date.Date
		billToDate            *date.Date
		isBillPartiallyPaid   bool
		softDueDate           *date.Date
	)

	g, gctx := errgroup.WithContext(ctx)
	g.Go(func() error {
		cardResp, err = s.fireFlyClient.GetDashboardCardInfo(gctx, &ffBePb.GetDashboardCardInfoRequest{
			CardId: creditCardId,
		})
		if te := epifigrpc.RPCError(cardResp, err); te != nil {
			return pkgErr.Wrap(te, "Error in GetDashboardCardInfo Be RPC")
		}
		// If card total limit is nil, then returning error
		// as limits can not be nil in home dashboard cards
		if cardResp.GetTotalLimit() == nil {
			return pkgErr.New("Error in fetching card limits from GetDashboardCardInfo Be RPC")
		}
		return nil
	})

	g.Go(func() error {
		billInfo, err = s.ffBillingClient.GetCreditCardBill(gctx, &ffBeBillingPb.GetCreditCardBillRequest{
			GetBy: &ffBeBillingPb.GetCreditCardBillRequest_ActorId{ActorId: actorId},
		})
		if te := epifigrpc.RPCError(billInfo, err); te != nil {
			// If bill is not found, that means no bill is due, so we will skip the error
			if !billInfo.GetStatus().IsRecordNotFound() {
				return pkgErr.Wrap(te, "Error in GetCreditCardBill Be RPC")
			}
		}
		return nil
	})

	g.Go(func() error {
		dueInfoResp, err = s.fireflyAccountingClient.GetCreditAccountDueInformation(gctx, &ffBeAccountsPb.GetCreditAccountDueInformationRequest{
			GetBy: &ffBeAccountsPb.GetCreditAccountDueInformationRequest_ActorId{
				ActorId: actorId,
			},
		})
		if te := epifigrpc.RPCError(dueInfoResp, err); te != nil {
			return pkgErr.Wrap(te, "Error in GetCreditAccountDueInformation Be RPC")
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, pkgErr.Wrap(err, "failed to get card, bill info and dueInfoResp")
	}

	if billInfo.GetCreditCardBill() != nil {
		billFromDate = datetime.TimeToDateInLoc(datetime.TimestampToTime(billInfo.GetCreditCardBill().GetStatementDate()).AddDate(0, -1, 0), datetime.IST)
		billToDate = datetime.TimeToDateInLoc(datetime.TimestampToTime(billInfo.GetCreditCardBill().GetStatementDate()).AddDate(0, 0, -1), datetime.IST)
	}

	compareDueAmount, dueAmountCompareError := moneyPb.CompareV2(dueInfoResp.GetUnpaidTotalDue(), moneyPb.ZeroINR().GetPb())
	if dueAmountCompareError != nil {
		return nil, pkgErr.Wrap(dueAmountCompareError, "Error in comparing due")
	}

	compareMinDueAmount, minDueAmountCompareError := moneyPb.CompareV2(dueInfoResp.GetUnpaidTotalDue(), dueInfoResp.GetTotalDueAmount())
	if minDueAmountCompareError != nil {
		return nil, pkgErr.Wrap(minDueAmountCompareError, "Error in comparing min due")
	}
	if compareMinDueAmount != 0 {
		isBillPartiallyPaid = true
	}

	switch {
	// Bill is past due date
	case billToDate != nil && datetime.IsDateBeforeTodayInLoc(datetime.TimeToDateInLoc(datetime.TimestampToTime(billInfo.GetCreditCardBill().GetSoftDueDate()), datetime.IST), nil) && compareDueAmount > 0:
		billAmount = dueInfoResp.GetUnpaidTotalDue()
		homeDashboardCardType = ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_BILL_DUE_PAST_DUE_DATE
		softDueDate = datetime.TimeToDateInLoc(datetime.TimestampToTime(billInfo.GetCreditCardBill().GetSoftDueDate()), datetime.IST)
	// Bill is due
	case billInfo.GetCreditCardBill() != nil && compareDueAmount > 0:
		switch cardResp.GetCardState() {
		case ffEnumsBePb.CardState_CARD_STATE_BLOCKED:
			homeDashboardCardType = ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_CARD_BLOCKED_AND_BILL_DUE
		default:
			homeDashboardCardType = ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_BILL_DUE
		}
		billAmount = dueInfoResp.GetUnpaidTotalDue()
	// No Bill due
	default:
		switch cardResp.GetCardState() {
		case ffEnumsBePb.CardState_CARD_STATE_BLOCKED:
			homeDashboardCardType = ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_CARD_BLOCKED_AND_NO_BILL_DUE
		default:
			homeDashboardCardType = ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_NO_BILL_DUE
		}
	}

	return &ffPb.HomeDashboardData{
		HomeDashboardCardType: homeDashboardCardType,
		HomeCardInfo: &ffPb.HomeDashboardData_CardInfo{
			CardInfo: &ffPb.HomeDashboardData_CreditCardInfo{
				AvailableLimit:      cardResp.GetLimitAvailable(),
				UtilisedLimit:       cardResp.GetLimitUtilised(),
				BillAmount:          billAmount,
				IsCardFreeze:        isCardFreeze,
				CardId:              creditCardId,
				BillToDate:          billToDate,
				BillFromDate:        billFromDate,
				IsBillPartiallyPaid: isBillPartiallyPaid,
				SoftDueDate:         softDueDate,
			},
		},
		CardProgram: cardProgram,
	}, nil
}

func getCardApplicationStatus(cardRequestAndCardRequestStage *ffBePb.GetCardRequestAndCardRequestStageResponse) (*ffPb.HomeDashboardData, ffEnumsBePb.CardRequestStatus, error) {
	cardProgram := cardRequestAndCardRequestStage.GetCardRequest().GetRequestDetails().GetCardProgram()
	// If the card request status is Permanent failure, then credit card application is rejected
	if cardRequestAndCardRequestStage.GetCardRequest().GetStatus() == ffEnumsBePb.CardRequestStatus_CARD_REQUEST_STATUS_PERMANENT_FAILURE {
		return &ffPb.HomeDashboardData{
			HomeDashboardCardType: ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_APPLICATION_NOT_APPROVED,
			CardProgram:           cardProgram,
		}, ffEnumsBePb.CardRequestStatus_CARD_REQUEST_STATUS_PERMANENT_FAILURE, nil
	}

	activeStageSubStatus := getActiveStageSubStatusForCardRequest(cardRequestAndCardRequestStage.GetCardRequestStages())

	var homeDashboardCardType ffEnumsPb.HomeDashboardCardType
	switch activeStageSubStatus {
	case ffEnumsBePb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_AWAITING_USER_ACTION:
		homeDashboardCardType = ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_COMPLETE_APPLICATION
	default:
		homeDashboardCardType = ffEnumsPb.HomeDashboardCardType_HOME_DASHBOARD_CARD_TYPE_APPLICATION_IN_PROGRESS
	}

	return &ffPb.HomeDashboardData{
		HomeDashboardCardType: homeDashboardCardType,
		CardProgram:           cardProgram,
	}, cardRequestAndCardRequestStage.GetCardRequest().GetStatus(), nil
}

// nolint:funlen
func (s *Service) GetBenefitsForMonthlySpendValue(ctx context.Context, req *ffPb.GetBenefitsForMonthlySpendValueRequest) (*ffPb.GetBenefitsForMonthlySpendValueResponse, error) {
	res := &ffPb.GetBenefitsForMonthlySpendValueResponse{RespHeader: &header.ResponseHeader{}}
	monthlySpendValue := req.GetMonthlySpendValue()
	avgAnnualSpendsValue := 12 * monthlySpendValue
	benefits2x := getRupeesBenefitFromSpendValue(float32(avgAnnualSpendsValue), internal.Ratio2xSpend, internal.InrToFiCoin2x)
	benefits5x := getRupeesBenefitFromSpendValue(float32(avgAnnualSpendsValue), internal.Ratio5xSpend, internal.InrToFiCoin5x)
	fiCoinsBenefits := benefits2x + benefits5x
	avgAnnualBenefits, avgMonthlyBenefits, valuebackPercentage := getTotalAnnualBenefitsForAnnualSpendValue(avgAnnualSpendsValue, benefits5x, benefits2x)
	valuebackPercentageStr := moneyPb.ToDisplayStringFromFloatValue(float32(valuebackPercentage), 0)
	res.ValueBack = &ffPb.GetBenefitsForMonthlySpendValueResponse_ValueBack{
		PercentReturn: &ffPb.GetBenefitsForMonthlySpendValueResponse_ValueBack_IconWithText{
			Text: &commontypes.Text{
				FontColor:    internal.TertiaryOceanColor,
				BgColor:      internal.MidOceanColor,
				DisplayValue: &commontypes.Text_PlainString{PlainString: valuebackPercentageStr + "%"},
			},
			Icon:      "https://epifi-icons.pointz.in/credit_card_images/Star-1+1.png",
			BgColor:   internal.MidOceanColor,
			TitleText: "VALUEBACK",
		},
		MonthlyBenefits: &deeplink.InfoItem{
			Title: "AVG MONTHLY BENEFITS",
			Desc:  fmt.Sprintf("₹%s", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(avgMonthlyBenefits, "INR"), 0, false)),
		},
		AnnualBenefits: &deeplink.InfoItem{
			Title: "AVG ANNUAL BENEFITS",
			Desc:  fmt.Sprintf("₹%s", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(avgAnnualBenefits, "INR"), 0, false)),
		},
		FooterText: "Get valueback from rewards, milestone benefits, etc.",
	}
	res.ValueBackCalculation = &ffPb.GetBenefitsForMonthlySpendValueResponse_ValueBackCalculation{
		Text:    "How is valueback calculated?",
		SubText: fmt.Sprintf("On average monthly spends of ₹%s, here’s a breakdown of the valueback you get.", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(req.GetMonthlySpendValue(), "INR"), 0, false)),
		Columns: []string{"BENEFITS", "SPENDS", "ANNUAL\nREWARD VALUE"},
		Resultant: &ffPb.GetBenefitsForMonthlySpendValueResponse_ValueBackCalculation_Row{ColumnValues: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Total"},
			},
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: ""},
			},
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("₹%s\n/ year", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(avgAnnualBenefits, "INR"), 0, false))},
			},
		}},
	}
	rows := make([]*ffPb.GetBenefitsForMonthlySpendValueResponse_ValueBackCalculation_Row, 0)
	rows = append(rows, &ffPb.GetBenefitsForMonthlySpendValueResponse_ValueBackCalculation_Row{
		ColumnValues: []*commontypes.Text{
			{
				FontColor:    internal.BlackColor,
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Welcome vouchers"},
			},
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: ""},
			},
			{
				FontColor:    internal.BlackColor,
				DisplayValue: &commontypes.Text_PlainString{PlainString: "₹5,000"},
			},
		},
	}, &ffPb.GetBenefitsForMonthlySpendValueResponse_ValueBackCalculation_Row{ColumnValues: []*commontypes.Text{
		{
			FontColor:    internal.BlackColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Fi-Coins"},
		},
		{
			FontColor:    internal.BlackColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("₹%s\n/ month", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(req.GetMonthlySpendValue(), "INR"), 0, false))},
		},
		{
			FontColor:    internal.BlackColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("₹%s", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(fiCoinsBenefits, "INR"), 0, false))},
		},
	}})
	totalMissedAmount := 0
	textFontColor := internal.BlackColor
	if avgAnnualSpendsValue < internal.Milestone1Spend {
		textFontColor = internal.GreyedOutColor
		totalMissedAmount += internal.Milestone1SpendBenefit
	}
	rows = append(rows, &ffPb.GetBenefitsForMonthlySpendValueResponse_ValueBackCalculation_Row{ColumnValues: []*commontypes.Text{
		{
			FontColor:    internal.BlackColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Milestone 1\nvoucher"},
		},
		{
			FontColor:    textFontColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("₹%s\n/ year", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(internal.Milestone1Spend, "INR"), 0, false))},
		},
		{
			FontColor:    textFontColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("₹%s", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(internal.Milestone1SpendBenefit, "INR"), 0, false))},
		},
	}})
	if avgAnnualSpendsValue < internal.Milestone2Spend {
		textFontColor = internal.GreyedOutColor
		totalMissedAmount += internal.Milestone2SpendBenefit
	}
	rows = append(rows, &ffPb.GetBenefitsForMonthlySpendValueResponse_ValueBackCalculation_Row{ColumnValues: []*commontypes.Text{
		{
			FontColor:    internal.BlackColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Milestone 2\nvoucher"},
		},
		{
			FontColor:    textFontColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("₹%s\n/ year", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(internal.Milestone2Spend, "INR"), 0, false))},
		},
		{
			FontColor:    textFontColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("₹%s", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseFloat(internal.Milestone2SpendBenefit, "INR"), 0, false))},
		},
	}})
	res.ValueBackCalculation.Rows = rows
	if totalMissedAmount != 0 {
		res.BottomInfoItem = &deeplink.InfoItem{
			Icon:  internal.InfoIconUrl,
			Title: fmt.Sprintf("You are missing out on milestone vouchers worth ₹%s.", moneyPb.ToDisplayStringInIndianFormat(moneyPb.ParseInt(int32(totalMissedAmount), "INR"), 0, false)),
		}
	}
	res.RespHeader.Status = rpc.StatusOk()
	return res, nil
}

// getRupeesBenefitFromSpendValue method calculates the benefits value wrt to the spend value
func getRupeesBenefitFromSpendValue(spendValue, spendRatio, inrToFiCoinRatio float32) float64 {
	return float64(spendValue * spendRatio * inrToFiCoinRatio * internal.FiCoinToInrExchangeRate)
}

// getTotalAnnualBenefitsForAnnualSpendValue calculates the annual and monthly benefits along with the
// valueback percentage
func getTotalAnnualBenefitsForAnnualSpendValue(avgAnnualSpendsValue, benefits5x, benefits2x float64) (float64, float64, float64) {
	fiCoinsBenefits := benefits2x + benefits5x
	avgAnnualBenefits := fiCoinsBenefits + internal.WelcomeVouchersWorthInr
	if avgAnnualSpendsValue >= internal.Milestone1Spend {
		avgAnnualBenefits += internal.Milestone1SpendBenefit
	}
	if avgAnnualSpendsValue >= internal.Milestone2Spend {
		avgAnnualBenefits += internal.Milestone2SpendBenefit
	}
	avgMonthlyBenefits := avgAnnualBenefits / 12
	valuebackPercentage := 0.0
	if avgAnnualSpendsValue != 0 {
		valuebackPercentage = (avgAnnualBenefits / avgAnnualSpendsValue) * 100
	}
	return avgAnnualBenefits, avgMonthlyBenefits, valuebackPercentage
}

func getActiveStageSubStatusForCardRequest(cardRequestStages []*ffBePb.CardRequestStage) ffEnumsBePb.CardRequestStageSubStatus {
	for _, cardRequestStage := range cardRequestStages {
		if cardRequestStage.GetStatus() == ffEnumsBePb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_IN_PROGRESS ||
			cardRequestStage.GetStatus() == ffEnumsBePb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_FAILED ||
			cardRequestStage.GetStatus() == ffEnumsBePb.CardRequestStageStatus_CARD_REQUEST_STAGE_STATUS_MANUAL_INTERVENTION {
			return cardRequestStage.GetSubStatus()
		}
	}
	return ffEnumsBePb.CardRequestStageSubStatus_CARD_REQUEST_STAGE_SUB_STATUS_UNSPECIFIED
}

func (s *Service) GetLoungeAccessInfo(ctx context.Context, req *ffPb.GetLoungeAccessInfoRequest) (*ffPb.GetLoungeAccessInfoResponse, error) {
	var (
		res = &ffPb.GetLoungeAccessInfoResponse{}
	)
	actorId := req.GetReq().GetAuth().GetActorId()
	beAccountRes, err := s.fireflyAccountingClient.GetAccounts(ctx, &ffBeAccountsPb.GetAccountsRequest{GetBy: &ffBeAccountsPb.GetAccountsRequest_ActorId{ActorId: actorId}})
	if te := epifigrpc.RPCError(beAccountRes, err); te != nil {
		logger.Error(ctx, "error fetching credit account", zap.Error(te))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
	ccAccount := beAccountRes.GetAccounts()[0]

	beRewardRes, err := s.rewardsClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId: actorId,
		Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
			RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_QUARTERLY_AIRPORT_LOUNGE_OFFER,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 10,
		},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in fetching lounge access reward info", zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	case !beRewardRes.GetStatus().IsSuccess():
		logger.Error(ctx, "error in be rewards res", zap.String(logger.STATUS, beRewardRes.GetStatus().String()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	default:
	}
	if len(beRewardRes.GetRewards()) == 0 {
		unlockConditionText := "Unlock lounge access when you spend a minimum of ₹10,000 in the current or previous quarter."
		if datetime.IsAfter(ccAccount.GetCreatedAt(), q3TimeStamp) {
			unlockConditionText = "Unlock lounge access when you spend a minimum of ₹10,000 in the current quarter."
		}
		res.NextAction = getLoungeNotEligibleScreen(unlockConditionText)
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusOk(),
		}
		return res, nil
	}
	rewardStatus := beRewardRes.GetRewards()[0].GetStatus()
	switch rewardStatus {
	case rewardsPb.RewardStatus_CREATED:
		res.NextAction = getLoungeToBeClaimedScreen()
	case rewardsPb.RewardStatus_PROCESSED:
		res.NextAction = getLoungeClaimedScreen()
	default:
		res.NextAction = getLoungeAccessClaimInProgress()
	}
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func getLoungeNotEligibleScreen(unlockConditionText string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_LOUNGE_ACCESS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardLoungeAccessScreenOptions{
			CreditCardLoungeAccessScreenOptions: &deeplink.CreditCardLoungeAccessScreenOptions{
				ToolbarTitle: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Lounge access"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				},
				ScreenIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/credit_card_images/lounge_access.png",
					Width:     296,
					Height:    244,
				},
				ScreenTitle: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Enjoy access to the airport lounge"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
				},
				InformationTexts: []*commontypes.Text{
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: unlockConditionText},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "You will receive a mail with a Barcode within 24-48 hours. This is valid for 1 year."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Show the Barcode at any international and national airport lounges available at https://loungefinder.loungekey.com/en/indiacorporate to get access."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
				},
				ActionButton: &deeplink.Button{
					Text: &commontypes.Text{
						FontColor:    "#606265",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim Now"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
					},
					Margin: &deeplink.Button_Margin{
						LeftMargin:  24,
						RightMargin: 24,
					},
					Cta: &deeplink.Cta{
						Type:   deeplink.Cta_CUSTOM,
						Text:   "Claim Now",
						Status: deeplink.Cta_CTA_STATUS_DISABLED,
					},
				},
			},
		},
	}
}

func getLoungeClaimedScreen() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_LOUNGE_ACCESS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardLoungeAccessScreenOptions{
			CreditCardLoungeAccessScreenOptions: &deeplink.CreditCardLoungeAccessScreenOptions{
				ToolbarTitle: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Lounge access"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				},
				ScreenIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/credit_card_images/lounge_access.png",
					Width:     296,
					Height:    244,
				},
				ScreenTitle: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Lounge Access has been claimed"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
				},
				InformationTexts: []*commontypes.Text{
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "You will receive a <NAME_EMAIL> with a LoungeKey containing a Barcode within 24-48 hours. The Barcode will only be valid for a year."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Show the Barcode at any international and national airport lounges available at https://loungefinder.loungekey.com/en/indiacorporate to get access."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
				},
				ActionButton: &deeplink.Button{
					Text: &commontypes.Text{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "View collected offer"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
						BgColor:      "#00B899",
					},
					Margin: &deeplink.Button_Margin{
						LeftMargin:  24,
						RightMargin: 24,
					},
					Cta: &deeplink.Cta{
						Type: deeplink.Cta_CUSTOM,
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_REDEEMED_OFFERS_SCREEN,
						},
						Status: deeplink.Cta_CTA_STATUS_ENABLED,
					},
				},
			},
		},
	}
}

func getLoungeToBeClaimedScreen() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_LOUNGE_ACCESS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardLoungeAccessScreenOptions{
			CreditCardLoungeAccessScreenOptions: &deeplink.CreditCardLoungeAccessScreenOptions{
				ToolbarTitle: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Lounge access"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				},
				ScreenIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/credit_card_images/lounge_access.png",
					Width:     296,
					Height:    244,
				},
				ScreenTitle: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Enjoy access to the airport lounge"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
				},
				InformationTexts: []*commontypes.Text{
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "You can get free access to the lounge once every 3 months"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim it & you will get a <NAME_EMAIL> with a LoungeKey containing a Barcode within 24-48 hours. The Barcode will only be valid for a year."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Show the Barcode at any international and national airport lounges available at https://loungefinder.loungekey.com/en/indiacorporate to get access."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
				},
				ActionButton: &deeplink.Button{
					Text: &commontypes.Text{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim now"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
						BgColor:      "#00B899",
					},
					Margin: &deeplink.Button_Margin{
						LeftMargin:  24,
						RightMargin: 24,
					},
					Cta: &deeplink.Cta{
						Type: deeplink.Cta_CUSTOM,
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_CREDIT_CARD_LOUNGE_ACCESS_SCREEN,
						},
						Status: deeplink.Cta_CTA_STATUS_ENABLED,
					},
				},
			},
		},
	}
}

func getLoungeAccessClaimInProgress() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_LOUNGE_ACCESS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardLoungeAccessScreenOptions{
			CreditCardLoungeAccessScreenOptions: &deeplink.CreditCardLoungeAccessScreenOptions{
				ToolbarTitle: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Lounge access"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
				},
				ScreenIcon: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  "https://epifi-icons.pointz.in/credit_card_images/lounge_access.png",
					Width:     296,
					Height:    244,
				},
				ScreenTitle: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim in progress"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
				},
				InformationTexts: []*commontypes.Text{
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "You will receive a <NAME_EMAIL> with a LoungeKey containing a Barcode within 24-48 hours. The Barcode will only be valid for a year."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
					{
						FontColor:    "#FFFFFF",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Show the Barcode at any international and national airport lounges available at https://loungefinder.loungekey.com/en/indiacorporate to get access."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					},
				},
				ActionButton: &deeplink.Button{
					Text: &commontypes.Text{
						FontColor:    "#606265",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Claiming lounge..."},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
						BgColor:      "#00B899",
					},
					Margin: &deeplink.Button_Margin{
						LeftMargin:  24,
						RightMargin: 24,
					},
					Cta: &deeplink.Cta{
						Type:   deeplink.Cta_CUSTOM,
						Status: deeplink.Cta_CTA_STATUS_DISABLED,
					},
				},
			},
		},
	}
}

func (s *Service) ClaimLoungeAccess(ctx context.Context, req *ffPb.ClaimLoungeAccessRequest) (*ffPb.ClaimLoungeAccessResponse, error) {
	var (
		res = &ffPb.ClaimLoungeAccessResponse{}
	)
	actorId := req.GetReq().GetAuth().GetActorId()
	getRewardRes, err := s.rewardsClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId: actorId,
		Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
			RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_QUARTERLY_AIRPORT_LOUNGE_OFFER,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 10,
		},
	})
	if te := epifigrpc.RPCError(getRewardRes, err); te != nil {
		logger.Error(ctx, "error in fetching credit card lounge access reward", zap.Error(te),
			zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
	beRewardsRes, err := s.rewardsClient.ChooseReward(ctx, &rewardsPb.ChooseRewardRequest{
		RewardId:       getRewardRes.GetRewards()[0].GetId(),
		RewardOptionId: getRewardRes.GetRewards()[0].GetRewardOptions().GetOptions()[0].GetId(),
	})
	if te := epifigrpc.RPCError(beRewardsRes, err); te != nil {
		logger.Error(ctx, "error in claiming reward", zap.Error(te), zap.String(logger.ACTOR_ID_V2,
			actorId), zap.String(logger.REWARD_ID, getRewardRes.GetRewards()[0].GetId()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().EnableLoungeAccessV2FeatureFlag()) {
		res.NextAction = &deeplink.Deeplink{Screen: deeplink.Screen_CC_LOUNGE_ACCESS_V2_SCREEN}
	} else {
		res.NextAction = getLoungeAccessClaimInProgress()
	}
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func (s *Service) TriggerRealtimeCardEligibilityCheck(ctx context.Context, req *ffPb.TriggerRealtimeCardEligibilityCheckRequest) (*ffPb.TriggerRealtimeCardEligibilityCheckResponse, error) {
	var (
		res = &ffPb.TriggerRealtimeCardEligibilityCheckResponse{}
	)
	actorId := req.GetReq().GetAuth().GetActorId()
	beRes, err := s.fireFlyClient.TriggerRealtimeCardEligibilityCheck(ctx, &ffBePb.TriggerRealtimeCardEligibilityCheckRequest{
		ActorId: actorId,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in triggering real time eligibility check", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	case !beRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success status for triggering real time eligibility check for the user",
			zap.String(logger.STATUS, beRes.GetStatus().String()))
		res.RespHeader = &header.ResponseHeader{
			Status: beRes.GetStatus(),
		}
		return res, nil
	default:
		res.NextAction = beRes.GetNextAction()
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusOk(),
		}
		return res, nil
	}
}

func getBillGenDateUpdateDeeplink(editVisualElement *deeplink.VisualElementCta, cardInfo *ffBePb.CreditCard) *deeplink.InfoItemWithCta {
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/billing_details_icon.png",
			Title: "Billing details",
			Desc:  "View your bill generation details",
		},
		Cta: &deeplink.Cta{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_BILLING_DETAILS_BOTTOM_VIEW_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&ffScreenTypes.CreditCardBillingDetailsBottomViewScreenOptions{
					Header: &deeplink_screen_option.ScreenOptionHeader{},
					ScreenHeading: &commontypes.Text{
						FontColor:    "#313234",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Billing details"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
					},
					BillingInfos: []*deeplink.InfoItemV2{
						{
							Title: &commontypes.Text{
								FontColor:    "#8D8D8D",
								DisplayValue: &commontypes.Text_PlainString{PlainString: "BILL GENERATION DATE"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
							},
							SubTitle: &commontypes.Text{
								FontColor:    "#383838",
								DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%d%s of the month", cardInfo.GetBasicInfo().GetBillGenDate(), getDaySuffix(int64(cardInfo.GetBasicInfo().GetBillGenDate())))},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
							},
							IconCta: editVisualElement,
							BgColor: "#F5F5F5",
						},
						{
							Title: &commontypes.Text{
								FontColor:    "#8D8D8D",
								DisplayValue: &commontypes.Text_PlainString{PlainString: "PAYMENT DUE DATE"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
							},
							SubTitle: &commontypes.Text{
								FontColor:    "#383838",
								DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%d%s of the %smonth", cardInfo.GetBasicInfo().GetPaymentDueDate(), getDaySuffix(int64(cardInfo.GetBasicInfo().GetPaymentDueDate())), getBillGenDayPrefix(cardInfo.GetBasicInfo().GetBillGenDate(), cardInfo.GetBasicInfo().GetPaymentDueDate()))},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
							},
							BgColor: "#F5F5F5",
						},
					},
				}),
			},
		},
	}
}

func (s *Service) checkFeatureReleaseConstraints(ctx context.Context, feature types.Feature, actorId string) bool {
	isFeatureEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(feature).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error evaluating feature release constraints", zap.String(logger.FEATURE, feature.String()), zap.Error(err))
		isFeatureEnabled = false
	}
	return isFeatureEnabled
}

func (s *Service) RecordCreditCardOnboardingConsent(ctx context.Context, req *ffPb.RecordCreditCardOnboardingConsentRequest) (*ffPb.RecordCreditCardOnboardingConsentResponse, error) {
	var (
		res                = &ffPb.RecordCreditCardOnboardingConsentResponse{}
		consentRequestInfo []*consentPb.ConsentRequestInfo
	)

	consentTypesList, ok := creditCardOnboardingConsentToConsentTypesListMap[req.GetCreditCardOnboardingConsent()]
	if !ok {
		logger.Error(ctx, "invalid CreditCardOnboardingConsent",
			zap.String("creditCardOnboardingConsent", req.GetCreditCardOnboardingConsent().String()))
		return &ffPb.RecordCreditCardOnboardingConsentResponse{
			RespHeader: &header.ResponseHeader{
				// todo: should we send internal instead
				Status:    rpc.StatusRecordNotFound(),
				ErrorView: genericErrorView,
			},
		}, nil
	}

	for _, consentType := range consentTypesList {
		consentRequestInfo = append(consentRequestInfo, &consentPb.ConsentRequestInfo{
			ConsentType: consentType,
		})
	}

	consentResp, err := s.consentClient.RecordConsents(ctx, &consentPb.RecordConsentsRequest{
		Consents: consentRequestInfo,
		ActorId:  req.GetReq().GetAuth().GetActorId(),
		Device:   req.GetReq().GetAuth().GetDevice(),
		Owner:    commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(consentResp, err); te != nil {
		logger.Error(ctx, "error while calling RecordConsents", zap.Error(te))
		return &ffPb.RecordCreditCardOnboardingConsentResponse{
			RespHeader: &header.ResponseHeader{
				Status:    consentResp.GetStatus(),
				ErrorView: genericErrorView,
			},
		}, nil
	}
	logger.Info(ctx, "successfully recorded consent for the user for credit card", zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.String("ConsentType", req.GetCreditCardOnboardingConsent().String()))
	res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
	return res, nil
}

// RecordConsentAndProceedWithUserAction records consent and proceeds with the user action.
func (s *Service) RecordConsentAndProceedWithUserAction(ctx context.Context, req *ffPb.RecordConsentAndProceedWithUserActionRequest) (*ffPb.RecordConsentAndProceedWithUserActionResponse, error) {
	var (
		res = &ffPb.RecordConsentAndProceedWithUserActionResponse{}
	)

	// Call the internal gRPC client to record consent and proceed with the user action
	recordConsentResp, err := s.fireFlyClient.RecordConsentAndProceedWithUserAction(ctx, &ffBePb.RecordConsentAndProceedWithUserActionRequest{
		CreditCardOnboardingConsent: req.GetCreditCardOnboardingConsent(),
		CardRequestId:               req.GetCardRequestId(),
		ActorId:                     req.GetReq().GetAuth().GetActorId(),
		Device:                      req.GetReq().GetAuth().GetDevice(),
	})

	// Handle any error response from the gRPC call
	if resErr := epifigrpc.RPCError(recordConsentResp, err); resErr != nil {
		logger.Error(ctx, "Error in recording consent and proceed with user action", zap.Error(resErr),
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.String(logger.CARD_REQUEST_ID, req.GetCardRequestId()))

		// Prepare the response with error details
		res.RespHeader = &header.ResponseHeader{
			Status:    recordConsentResp.GetStatus(),
			ErrorView: genericErrorView,
		}
		return res, nil
	}

	// Log successful recording of consent and user action
	logger.Info(ctx, "successfully recorded consent and proceeded with user action", zap.String(logger.CARD_REQUEST_ID, req.GetCardRequestId()))

	// Prepare the response with the next action and success status
	res.NextAction = recordConsentResp.GetNextAction()
	res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
	return res, nil
}

// StopCreditCardOnboarding stops the process of credit card onboarding.
func (s *Service) StopCreditCardOnboarding(ctx context.Context, req *ffPb.StopCreditCardOnboardingRequest) (*ffPb.StopCreditCardOnboardingResponse, error) {
	var (
		res = &ffPb.StopCreditCardOnboardingResponse{}
	)

	// Call the internal gRPC client to stop credit card onboarding
	stopCCOnbResp, err := s.fireFlyClient.StopCreditCardOnboarding(ctx, &ffBePb.StopCreditCardOnboardingRequest{
		CardRequestId: req.GetCardRequestId(),
	})

	// Handle any error response from the gRPC call
	if resErr := epifigrpc.RPCError(stopCCOnbResp, err); resErr != nil {
		logger.Error(ctx, "Error in stopping credit card onboarding ", zap.Error(resErr),
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()), zap.String(logger.CARD_REQUEST_ID, req.GetCardRequestId()))

		// Prepare the response with error details
		res.RespHeader = &header.ResponseHeader{
			Status:    stopCCOnbResp.GetStatus(),
			ErrorView: genericErrorView,
		}
		return res, nil
	}

	// Log successful stopping of credit card onboarding
	logger.Info(ctx, "successfully stopped credit card onboarding", zap.String(logger.CARD_REQUEST_ID, req.GetCardRequestId()))

	// Prepare the response with the next action and success status
	res.NextAction = stopCCOnbResp.GetNextAction()
	res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
	return res, nil
}
