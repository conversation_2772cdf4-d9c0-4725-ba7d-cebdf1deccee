package home

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	mockNetworthPb "github.com/epifi/gamma/api/insights/networth/mocks"

	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"

	"context"
	"errors"
	"flag"
	"fmt"
	"os"
	"testing"
	"time"

	onbPkg "github.com/epifi/gamma/pkg/onboarding"
	pkgUser "github.com/epifi/gamma/pkg/user"
	mockUserAttributeFetcher "github.com/epifi/gamma/pkg/user/mocks"

	"github.com/epifi/gamma/frontend/user"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"github.com/google/go-cmp/cmp"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/durationpb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/logger"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountBalanceMock "github.com/epifi/gamma/api/accounts/balance/mocks"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/actor/mocks"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	mocks7 "github.com/epifi/gamma/api/bankcust/mocks"
	connectedAccountMocks "github.com/epifi/gamma/api/connected_account/mocks"
	"github.com/epifi/gamma/api/employment"
	mocks9 "github.com/epifi/gamma/api/employment/mocks"
	ffPb "github.com/epifi/gamma/api/firefly"
	mockffPb "github.com/epifi/gamma/api/firefly/mocks"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	mocks5 "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	mocks4 "github.com/epifi/gamma/api/salaryprogram/mocks"
	"github.com/epifi/gamma/api/savings"
	mocks2 "github.com/epifi/gamma/api/savings/mocks"
	segmentMock "github.com/epifi/gamma/api/segment/mocks"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	mocks6 "github.com/epifi/gamma/api/tiering/mocks"
	homeTypesPb "github.com/epifi/gamma/api/typesv2/home"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	userPb "github.com/epifi/gamma/api/user"
	mocks3 "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/user/onboarding"
	mockOnb "github.com/epifi/gamma/api/user/onboarding/mocks"
	homeConstants "github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>"
	mocks8 "github.com/epifi/gamma/frontend/tiering/test/mocks"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseMock "github.com/epifi/gamma/pkg/feature/release/mocks"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	feHomePb "github.com/epifi/gamma/api/frontend/home"
	pb "github.com/epifi/gamma/api/frontend/home"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	feTest "github.com/epifi/gamma/frontend/test"
)

var (
	conf       *config.Config
	gconf      *genconf.Config
	SpacerNone = &components.Spacer{
		SpacingValue: components.Spacing_SPACING_UNSPECIFIED,
		BgColour:     &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}},
	}
	SpacerL = &components.Spacer{
		SpacingValue: components.Spacing_SPACING_L,
		BgColour:     &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}},
	}
	SpacerXl = &components.Spacer{
		SpacingValue: components.Spacing_SPACING_XL,
		BgColour:     &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}},
	}
	SpacerXxl = &components.Spacer{
		SpacingValue: components.Spacing_SPACING_XXL,
		BgColour:     &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}},
	}
)

func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, gconf, teardown = feTest.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_GetAppBottomNavBar(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockEvaluator := releaseMock.NewMockIEvaluator(ctr)
	onbClient := mockOnb.NewMockOnboardingClient(ctr)
	fireflyClient := mockffPb.NewMockFireflyClient(ctr)

	type mockEvaluate struct {
		enable      bool
		constraints *release.CommonConstraintData
		res         bool
		err         error
	}

	type test struct {
		name         string
		mockEvaluate mockEvaluate
		req          *pb.GetAppBottomNavBarRequest
		res          *pb.GetAppBottomNavBarResponse
	}
	tests := []test{
		{
			name: "success get app bottom navbar response",
			req:  &pb.GetAppBottomNavBarRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: "actor-id"}}},
			res: &pb.GetAppBottomNavBarResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
				BottomBarIcons: []*pb.Icon{
					{
						IconType:             homeTypesPb.IconType_HOME,
						IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/BlackHome.png"},
						IconImageOnSelection: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/GreenHome.png"},
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Home"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: GraySteel,
						},
						TitleOnSelection: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Home"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: Green,
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_HOME,
						}},
						VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/BlackHome.png"}}}},
					},
					{
						IconType:             homeTypesPb.IconType_PAY,
						IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/BlackPay.png"},
						IconImageOnSelection: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/GreenPay.png"},
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Pay"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: GraySteel,
						},
						TitleOnSelection: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Pay"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: Green,
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_PAY_LANDING_SCREEN,
						}},
						VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/BlackPay.png"}}}},
					},
					{
						IconType:             homeTypesPb.IconType_INVEST,
						IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/BlackInvest.png"},
						IconImageOnSelection: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/GreenInvest.png"},
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Invest"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: GraySteel,
						},
						TitleOnSelection: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Invest"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: Green,
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_INVESTMENT_LANDING_SCREEN,
						}},
						VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/BlackInvest.png"}}}},
					},
					{
						IconType:             homeTypesPb.IconType_ANALYSER,
						IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/Analyser.png"},
						IconImageOnSelection: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/GreenAnalyser.png"},
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Insights"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: GraySteel,
						},
						TitleOnSelection: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Insights"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: Green,
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_ANALYSER_SCREEN,
						}},
						VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/Analyser.png"}}}}},
					{
						IconType:             homeTypesPb.IconType_DISCOVER,
						IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/BlackExplore.png"},
						IconImageOnSelection: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/GreenExplore.png"},
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Explore"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: GraySteel,
						},
						TitleOnSelection: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Explore"},
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							FontColor: Green,
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_HOME_EXPLORE,
						}},
						VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/BlackExplore.png"}}}}},
				},
				StickyIconMapping: map[string]*pb.Icon{
					"HOME": {
						IconType:             homeTypesPb.IconType_QR_CODE,
						IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/QRCode.png"},
						IconImageOnSelection: &commontypes.Image{ImageUrl: ""},
						ActionType:           pb.Icon_ACTION_TYPE_DEEPLINK,
						Title: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{""},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED,
							},
						},
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_PAY_QR_SCREEN,
						}},
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: FiGreen}},
						VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/home-v2/QRCode.png"}}}}},
				},
				DefaultSelectedIconType:  homeTypesPb.IconType_HOME,
				OccurrencesOfScanPayText: 5,
			},

			mockEvaluate: mockEvaluate{
				enable:      true,
				constraints: release.NewCommonConstraintData(types.Feature_ML_KIT_QR).WithActorId("actor-id"),
				res:         false,
			},
		},
	}

	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{conf: conf, genconf: gconf, evaluator: mockEvaluator, onboardingClient: onbClient, beFireflyClient: fireflyClient}

			if tt.mockEvaluate.enable {
				onbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onboarding.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				mockEvaluator.EXPECT().Evaluate(context.Background(), tt.mockEvaluate.constraints).
					Return(tt.mockEvaluate.res, tt.mockEvaluate.err)
			}

			fireflyClient.EXPECT().FetchCreditCardEligibility(gomock.Any(), gomock.Any()).Return(&ffPb.FetchCreditCardEligibilityResponse{
				Status:           rpc.StatusOk(),
				IsUserCcEligible: true,
			}, nil)

			res, err := s.GetAppBottomNavBar(ctx, tt.req)
			if err != nil {
				t.Error("failed to get app bottom bar response", zap.Error(err))
			}
			if diff := cmp.Diff(res, tt.res, protocmp.Transform()); diff != "" {
				t.Errorf("got: %v, want: %v, diff: %s", res, tt.res, diff)
			}
		})
	}
}

func TestService_HomeLayout(t *testing.T) {
	var (
		dashboardBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{RadialGradient: &ui.RadialGradient{
			Center: &ui.CenterCoordinates{
				CenterX: 1,
				CenterY: 0,
			},
			OuterRadius: 82,
			Colours:     []string{"#484848", "#353434"},
		}}}
		actorId = "actorId"
		// timestamp of current date-1
	)

	type mockEvaluate struct {
		enable      bool
		constraints *release.CommonConstraintData
		res         bool
		err         error
	}

	type test struct {
		name             string
		req              *pb.HomeLayoutRequest
		res              *pb.HomeLayoutResponse
		onbMock          func(client *mockOnb.MockOnboardingClient)
		mockEvaluateList []mockEvaluate
	}
	tests := []test{
		{
			name: "success get home layout response - ten day old user",
			req:  &pb.HomeLayoutRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actorId}}},
			res: &pb.HomeLayoutResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
				HomeWidgetsTopSection: []*pb.HomeWidget{
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_MAINTENANCE,
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_TOP_NAV_BAR,
						Widget: &pb.HomeWidget_TopBarWidget{TopBarWidget: &pb.TopNavBarWidget{
							RightIcons: []*pb.Icon{
								{
									IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/announcement.png"},
									IconImageOnSelection: &commontypes.Image{},
									IconType:             homeTypesPb.IconType_REFER,
									ActionType:           pb.Icon_ACTION_TYPE_DEEPLINK,
									Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_REFERRALS_ELIGIBILITY_LANDING_SCREEN,
									}},
									Title:         &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{}, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED}},
									VisualElement: commontypes.GetVisualElementLottieFromUrl("https://epifi-icons.pointz.in/home-v2/refer-nav-bar-lottie.json").WithProperties(&commontypes.VisualElementProperties{Width: 32, Height: 32}).WithRepeatCount(1),
								},
								{
									IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/tablet.png"},
									IconType:             homeTypesPb.IconType_CARD,
									ActionType:           pb.Icon_ACTION_TYPE_DEEPLINK,
									IconImageOnSelection: &commontypes.Image{},
									Title:                &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{}, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED}},
									Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
									}},
									VisualElement: commontypes.GetVisualElementLottieFromUrl(homeConstants.CCEligibleLottieUrl).WithProperties(&commontypes.VisualElementProperties{Width: 32, Height: 32}).WithRepeatCount(1),
								},
								{
									IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/Notification.png"},
									IconType:             homeTypesPb.IconType_NOTIFICATION,
									ActionType:           pb.Icon_ACTION_TYPE_DEEPLINK,
									IconImageOnSelection: &commontypes.Image{},
									Title:                &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{}, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED}},
									Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_NOTIFICATION_CENTER,
									}},
									VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/Notification.png").WithProperties(&commontypes.VisualElementProperties{Width: 32, Height: 32}),
								},
							},
							LeftIcons: []*pb.Icon{
								{
									IconType:             homeTypesPb.IconType_PROFILE,
									ActionType:           pb.Icon_ACTION_TYPE_DEEPLINK,
									IconImageOnSelection: &commontypes.Image{},
									Title:                &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{}, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED}},
									IconImage:            &commontypes.Image{},
									Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_PROFILE_SCREEN,
									}},
									VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: ""}}}},
								},
							},
						}},
						WidgetBg: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#282828"}},
					},
				},
				HomeWidgetsBottomSection: []*pb.HomeWidget{
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_CRITICAL_NOTIFICATION,
						TopSpacer:  SpacerNone,
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_DASHBOARD,
						TopSpacer:  SpacerNone,
						Widget: &pb.HomeWidget_DashboardWidget{DashboardWidget: &pb.DashboardWidget{
							DashboardViews: []*pb.DashboardWidget_DashboardView{
								{

									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_INTRO,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{},
									Background:         dashboardBg,
								},
								{
									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_PRIMARY_SAVINGS,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/FederalBankWatermark.png"},
									Background:         dashboardBg,
								},
								{
									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_CONNECTED_ACCOUNTS,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{},
									Background:         dashboardBg,
								},
								{
									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_INVESTMENTS,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{},
									Background:         dashboardBg,
								},
								{
									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_CREDIT_CARDS,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{},
									Background:         dashboardBg,
								},
							},
						}},
						WidgetBg: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#282828"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_SEARCH_BAR,
						TopSpacer:  SpacerL,
						WidgetBg:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#D6DBE0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER,
						TopSpacer:  SpacerXl,
						WidgetBg:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_SUGGESTED_FOR_YOU,
						TopSpacer:  SpacerXl,
						WidgetBg:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_SHORTCUTS,
						TopSpacer:  SpacerXl,
						WidgetBg:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_SALARY_ACCOUNT,
						TopSpacer:  SpacerXxl,
						WidgetBg: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_RadialGradient{
								RadialGradient: &ui.RadialGradient{
									Colours: []string{"#EFF2F6", "#EFF2F6"},
								},
							},
						},
					},
					{
						WidgetType:        pb.HomeWidget_WIDGET_TYPE_RECENT_UPCOMING_ACTIVITIES,
						TopSpacer:         SpacerXxl,
						WidgetBg:          &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
						WidgetBgSeparator: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
						Widget: &pb.HomeWidget_ActivitiesWidget{ActivitiesWidget: &pb.RecentAndUpcomingWidget{
							Filter: &ui.Filter{
								Tabs: []*ui.Tab{
									{
										Id: "RecentUpcomingTabs:0",
										InactiveCta: &ui.IconTextComponent{Texts: []*commontypes.Text{
											{
												DisplayValue: &commontypes.Text_PlainString{PlainString: "Recent Activity"},
												FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_S},
												FontColor:    Lead,
											}},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor: Chalk,
											},
										},
										ActiveCta: &ui.IconTextComponent{Texts: []*commontypes.Text{
											{
												DisplayValue: &commontypes.Text_PlainString{PlainString: "Recent Activity"},
												FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_S},
												FontColor:    MonochromeNight,
											}},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor: Snow,
											},
										},
										BorderColor: feHomePb.GetHomeWidgetBorderColor(),
									},
									{
										Id: "RecentUpcomingTabs:1",
										InactiveCta: &ui.IconTextComponent{Texts: []*commontypes.Text{
											{
												DisplayValue: &commontypes.Text_PlainString{PlainString: "Upcoming"},
												FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_S},
												FontColor:    Lead,
											}},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor: Chalk,
											},
										},
										ActiveCta: &ui.IconTextComponent{Texts: []*commontypes.Text{
											{
												DisplayValue: &commontypes.Text_PlainString{PlainString: "Upcoming"},
												FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_S},
												FontColor:    MonochromeNight,
											}},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor: Snow,
											},
										},
										BorderColor: feHomePb.GetHomeWidgetBorderColor(),
									},
								},
								FilterType:        ui.Filter_FILTER_TYPE_SWITCH,
								DefaultTabSection: "RecentUpcomingTabs:0",
							},
							TabToActivityType: map[string]pb.RecentAndUpcomingWidget_ActivityType{
								"RecentUpcomingTabs:0": pb.RecentAndUpcomingWidget_ACTIVITY_TYPE_RECENT,
								"RecentUpcomingTabs:1": pb.RecentAndUpcomingWidget_ACTIVITY_TYPE_UPCOMING,
							},
						}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER_2,
						TopSpacer:  SpacerXl,
					},
					{
						WidgetType:        pb.HomeWidget_WIDGET_TYPE_REWARDS,
						TopSpacer:         SpacerXxl,
						WidgetBg:          &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
						WidgetBgSeparator: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType:        pb.HomeWidget_WIDGET_TYPE_INVEST_HOME_ELEMENT,
						TopSpacer:         SpacerXxl,
						WidgetBg:          &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
						WidgetBgSeparator: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_REFERRAL,
						TopSpacer:  SpacerXxl,
						WidgetBg: &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{
							RadialGradient: &ui.RadialGradient{
								Colours: []string{"#9287BD", "#6F62A4"},
							},
						},
						},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_HELP,
						TopSpacer:  SpacerNone,
						WidgetBg: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_RadialGradient{
								RadialGradient: &ui.RadialGradient{
									Colours: []string{"#00B899", "#006D5B"},
								},
							}},
					},
				},
			},
			mockEvaluateList: []mockEvaluate{
				{
					enable:      true,
					constraints: release.NewCommonConstraintData(types.Feature_ML_KIT_QR).WithActorId("actorId"),
					res:         false,
				},
			},
			onbMock: func(client *mockOnb.MockOnboardingClient) {
				client.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onboarding.GetFeatureDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
		{
			name: "success get home layout response - 1 day old user",
			req:  &pb.HomeLayoutRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actorId}}},
			res: &pb.HomeLayoutResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
				HomeWidgetsTopSection: []*pb.HomeWidget{
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_MAINTENANCE,
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_TOP_NAV_BAR,
						Widget: &pb.HomeWidget_TopBarWidget{TopBarWidget: &pb.TopNavBarWidget{
							RightIcons: []*pb.Icon{
								{
									IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/announcement.png"},
									IconImageOnSelection: &commontypes.Image{},
									IconType:             homeTypesPb.IconType_REFER,
									ActionType:           pb.Icon_ACTION_TYPE_DEEPLINK,
									Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_REFERRALS_ELIGIBILITY_LANDING_SCREEN,
									}},
									Title:         &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{}, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED}},
									VisualElement: commontypes.GetVisualElementLottieFromUrl("https://epifi-icons.pointz.in/home-v2/refer-nav-bar-lottie.json").WithProperties(&commontypes.VisualElementProperties{Width: 32, Height: 32}).WithRepeatCount(1),
								},
								{
									IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/tablet.png"},
									IconType:             homeTypesPb.IconType_CARD,
									ActionType:           pb.Icon_ACTION_TYPE_DEEPLINK,
									IconImageOnSelection: &commontypes.Image{},
									Title:                &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{}, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED}},
									Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
									}},
									VisualElement: commontypes.GetVisualElementLottieFromUrl(homeConstants.CCEligibleLottieUrl).WithProperties(&commontypes.VisualElementProperties{Width: 32, Height: 32}).WithRepeatCount(1),
								},
								{
									IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/Notification.png"},
									IconType:             homeTypesPb.IconType_NOTIFICATION,
									ActionType:           pb.Icon_ACTION_TYPE_DEEPLINK,
									IconImageOnSelection: &commontypes.Image{},
									Title:                &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{}, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED}},
									Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_NOTIFICATION_CENTER,
									}},
									VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/Notification.png").WithProperties(&commontypes.VisualElementProperties{Width: 32, Height: 32}),
								},
							},
							LeftIcons: []*pb.Icon{
								{
									IconType:             homeTypesPb.IconType_PROFILE,
									ActionType:           pb.Icon_ACTION_TYPE_DEEPLINK,
									IconImageOnSelection: &commontypes.Image{},
									Title:                &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{}, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_FONT_STYLE_UNSPECIFIED}},
									IconImage:            &commontypes.Image{},
									Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_PROFILE_SCREEN,
									}},
									VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{Source: &commontypes.VisualElement_Image_Url{Url: ""}}}},
								},
							},
						}},
						WidgetBg: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#282828"}},
					},
				},
				HomeWidgetsBottomSection: []*pb.HomeWidget{
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_CRITICAL_NOTIFICATION,
						TopSpacer:  SpacerNone,
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_DASHBOARD,
						TopSpacer:  SpacerNone,
						Widget: &pb.HomeWidget_DashboardWidget{DashboardWidget: &pb.DashboardWidget{
							DashboardViews: []*pb.DashboardWidget_DashboardView{
								{
									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_INTRO,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{},
									Background:         dashboardBg,
								},
								{
									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_PRIMARY_SAVINGS,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/FederalBankWatermark.png"},
									Background:         dashboardBg,
								},
								{
									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_CONNECTED_ACCOUNTS,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{},
									Background:         dashboardBg,
								},
								{
									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_INVESTMENTS,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{},
									Background:         dashboardBg,
								},
								{
									DashboardViewType:  pb.DashboardWidget_DashboardView_DASHBOARD_VIEW_CREDIT_CARDS,
									OverridingPriority: pb.DashboardWidget_DASHBOARD_PRIORITY_LOW,
									DashboardWaterMark: &commontypes.Image{},
									Background:         dashboardBg,
								},
							},
						}},
						WidgetBg: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#282828"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_SEARCH_BAR,
						TopSpacer:  SpacerL,
						WidgetBg:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#D6DBE0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER,
						TopSpacer:  SpacerXl,
						WidgetBg:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_SUGGESTED_FOR_YOU,
						TopSpacer:  SpacerXl,
						WidgetBg:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_SHORTCUTS,
						TopSpacer:  SpacerXl,
						WidgetBg:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_SALARY_ACCOUNT,
						TopSpacer:  SpacerXxl,
						WidgetBg: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_RadialGradient{
								RadialGradient: &ui.RadialGradient{
									Colours: []string{"#EFF2F6", "#EFF2F6"},
								},
							},
						},
					},
					{
						WidgetType:        pb.HomeWidget_WIDGET_TYPE_RECENT_UPCOMING_ACTIVITIES,
						TopSpacer:         SpacerXxl,
						WidgetBg:          &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
						WidgetBgSeparator: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
						Widget: &pb.HomeWidget_ActivitiesWidget{ActivitiesWidget: &pb.RecentAndUpcomingWidget{
							Filter: &ui.Filter{
								Tabs: []*ui.Tab{
									{
										Id: "RecentUpcomingTabs:0",
										InactiveCta: &ui.IconTextComponent{Texts: []*commontypes.Text{
											{
												DisplayValue: &commontypes.Text_PlainString{PlainString: "Recent Activity"},
												FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_S},
												FontColor:    Lead,
											}},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor: Chalk,
											},
										},
										ActiveCta: &ui.IconTextComponent{Texts: []*commontypes.Text{
											{
												DisplayValue: &commontypes.Text_PlainString{PlainString: "Recent Activity"},
												FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_S},
												FontColor:    MonochromeNight,
											}},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor: Snow,
											},
										},
										BorderColor: feHomePb.GetHomeWidgetBorderColor(),
									},
									{
										Id: "RecentUpcomingTabs:1",
										InactiveCta: &ui.IconTextComponent{Texts: []*commontypes.Text{
											{
												DisplayValue: &commontypes.Text_PlainString{PlainString: "Upcoming"},
												FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_S},
												FontColor:    Lead,
											}},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor: Chalk,
											},
										},
										ActiveCta: &ui.IconTextComponent{Texts: []*commontypes.Text{
											{
												DisplayValue: &commontypes.Text_PlainString{PlainString: "Upcoming"},
												FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_S},
												FontColor:    MonochromeNight,
											}},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor: Snow,
											},
										},
										BorderColor: feHomePb.GetHomeWidgetBorderColor(),
									},
								},
								FilterType:        ui.Filter_FILTER_TYPE_SWITCH,
								DefaultTabSection: "RecentUpcomingTabs:0",
							},
							TabToActivityType: map[string]pb.RecentAndUpcomingWidget_ActivityType{
								"RecentUpcomingTabs:0": pb.RecentAndUpcomingWidget_ACTIVITY_TYPE_RECENT,
								"RecentUpcomingTabs:1": pb.RecentAndUpcomingWidget_ACTIVITY_TYPE_UPCOMING,
							},
						}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER_2,
						TopSpacer:  SpacerXl,
					},
					{
						WidgetType:        pb.HomeWidget_WIDGET_TYPE_REWARDS,
						TopSpacer:         SpacerXxl,
						WidgetBg:          &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
						WidgetBgSeparator: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType:        pb.HomeWidget_WIDGET_TYPE_INVEST_HOME_ELEMENT,
						TopSpacer:         SpacerXxl,
						WidgetBg:          &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
						WidgetBgSeparator: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_REFERRAL,
						TopSpacer:  SpacerXxl,
						WidgetBg: &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{
							RadialGradient: &ui.RadialGradient{
								Colours: []string{"#9287BD", "#6F62A4"},
							}}},
					},
					{
						WidgetType: pb.HomeWidget_WIDGET_TYPE_HELP,
						TopSpacer:  SpacerNone,
						WidgetBg: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_RadialGradient{
								RadialGradient: &ui.RadialGradient{
									Colours: []string{"#00B899", "#006D5B"},
								},
							}},
					},
				},
			},
			mockEvaluateList: []mockEvaluate{
				{
					enable:      true,
					constraints: release.NewCommonConstraintData(types.Feature_ML_KIT_QR).WithActorId("actorId"),
					res:         false,
				},
			},
			onbMock: func(client *mockOnb.MockOnboardingClient) {
				client.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onboarding.GetFeatureDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
	}

	ctrl := gomock.NewController(t)
	onboardingMockClient := mockOnb.NewMockOnboardingClient(ctrl)
	mockEvaluator := releaseMock.NewMockIEvaluator(ctrl)
	fireflyClient := mockffPb.NewMockFireflyClient(ctrl)

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{conf: conf, genconf: gconf, onboardingClient: onboardingMockClient, evaluator: mockEvaluator, beFireflyClient: fireflyClient}
			if tt.onbMock != nil {
				tt.onbMock(onboardingMockClient)
			}

			fireflyClient.EXPECT().FetchCreditCardEligibility(gomock.Any(), gomock.Any()).Return(&ffPb.FetchCreditCardEligibilityResponse{
				Status:           rpc.StatusOk(),
				IsUserCcEligible: true,
			}, nil)

			for _, mockEvaluate_ := range tt.mockEvaluateList {
				if mockEvaluate_.enable {
					mockEvaluator.EXPECT().Evaluate(context.Background(), mockEvaluate_.constraints).
						Return(mockEvaluate_.res, mockEvaluate_.err)
				}
			}
			res, err := s.HomeLayout(context.Background(), tt.req)
			if err != nil {
				t.Error("failed to get app bottom bar response", zap.Error(err))
			}
			if !proto.Equal(res, tt.res) {
				t.Errorf("got: %v, want: %v, diff: %v", res, tt.res, cmp.Diff(res, tt.res, protocmp.Transform()))
			}
		})
	}
}

// nolint: dupl
func TestService_GetHomeAppWalkThroughFlows(t *testing.T) {
	var (
		ctx                         = context.Background()
		actor1                      = "actor1"
		entity1                     = "entity1"
		name1                       = "userName1"
		walkThroughStartIconOldUser = &pb.Icon{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Get a tour"},
				FontColor:    FiGreen,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Ink}},
			IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/GetStartedIcon.png"},
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/GetStartedIcon.png"),
		}
		walkThroughStartIconNewUser = &pb.Icon{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Get started"},
				FontColor:    FiGreen,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Ink}},
			IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/GetStartedIcon.png"},
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/GetStartedIcon.png"),
		}
		newUserWalkThrough = &pb.OnboardingWalkthrough{
			Enabled: true,
			OnboardingWalkThrough: []*pb.WalkThroughPopUp{
				{
					Message:   NudgePopupMsg,
					PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_NUDGE,
				},
			},
			WalkThroughType: pb.OnboardingWalkthrough_TYPE_NEW_USER_WALK_THROUGH,
		}
		oldUserWalkThrough = &pb.OnboardingWalkthrough{
			Enabled: true,
			OnboardingWalkThrough: []*pb.WalkThroughPopUp{
				{
					Message:   DashboardPopupMsg,
					PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_DASHBOARD_VIEW,
				},
				{
					Message:   FitPopupMsg,
					PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_FIT_RULES,
				},
				{
					Message:   InvestPopupMsg,
					PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_SAVE_AND_INVEST,
				},
				{
					Message:   NudgePopupMsg,
					PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_NUDGE,
				},
			},
			WalkThroughType: pb.OnboardingWalkthrough_TYPE_OLD_USER_WALK_THROUGH,
		}
	)
	oldUserOnboardingTimestamp := timestampPb.New(time.Now().Add(-1 * 48 * time.Hour))
	newUserOnboardingTimestamp := timestampPb.Now()

	type test struct {
		name           string
		req            *pb.GetHomeAppWalkThroughFlowsRequest
		actorMock      func(client *mocks.MockActorClient)
		onboardingMock func(client *mockOnb.MockOnboardingClient)
		res            *pb.GetHomeAppWalkThroughFlowsResponse
	}
	tests := []test{
		{
			name: "success scenario, old walkthrough",
			req:  &pb.GetHomeAppWalkThroughFlowsRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			actorMock: func(client *mocks.MockActorClient) {
				client.EXPECT().GetEntityDetailsByActorId(ctx, &actor.GetEntityDetailsByActorIdRequest{ActorId: actor1}).Return(&actor.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: entity1, Name: &commontypes.Name{FirstName: name1}}, nil)
			},
			onboardingMock: func(client *mockOnb.MockOnboardingClient) {
				client.EXPECT().GetDetails(ctx, &onboarding.GetDetailsRequest{ActorId: actor1, CachedData: true}).Return(&onboarding.GetDetailsResponse{
					Status:  rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{CompletedAt: oldUserOnboardingTimestamp},
				}, nil)
				client.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onboarding.GetFeatureDetailsResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			res: &pb.GetHomeAppWalkThroughFlowsResponse{
				RespHeader:            &header.ResponseHeader{Status: rpc.StatusOk()},
				OnboardingWalkthrough: oldUserWalkThrough,
				PrivacySettingWalkthrough: &pb.PrivacySettingWalkthrough{
					Enabled: true,
					PrivacySettingWalkThrough: []*pb.WalkThroughPopUp{
						{
							Message:   AdditionalSettingsMsg,
							PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_ADDITIONAL_SETTINGS,
						},
						{
							Message:   HideShowBalanceMsg,
							PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_HIDE_UN_HIDE,
						},
					},
				},
				DashboardIntroCardDetails: &pb.DashboardIntroCardDetails{
					StartIcon:              walkThroughStartIconOldUser,
					ShowCardMaxImpressions: MaxWalkThroughImpressionsPerUser,
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Hey %v!\nFi’s got a refresh", name1)},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L}},
					Image:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/IntroWave.png"},
					Shadow: ui.GetDashboardShadow(),
				},
			},
		},
		{
			name: "success scenario, new walkthrough",
			req:  &pb.GetHomeAppWalkThroughFlowsRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			actorMock: func(client *mocks.MockActorClient) {
				client.EXPECT().GetEntityDetailsByActorId(ctx, &actor.GetEntityDetailsByActorIdRequest{ActorId: actor1}).Return(&actor.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: entity1, Name: &commontypes.Name{FirstName: name1}}, nil)
			},
			onboardingMock: func(client *mockOnb.MockOnboardingClient) {
				client.EXPECT().GetDetails(ctx, &onboarding.GetDetailsRequest{ActorId: actor1, CachedData: true}).Return(&onboarding.GetDetailsResponse{
					Status:  rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{CompletedAt: newUserOnboardingTimestamp},
				}, nil)
			},
			res: &pb.GetHomeAppWalkThroughFlowsResponse{
				RespHeader:            &header.ResponseHeader{Status: rpc.StatusOk()},
				OnboardingWalkthrough: newUserWalkThrough,
				PrivacySettingWalkthrough: &pb.PrivacySettingWalkthrough{
					Enabled: true,
					PrivacySettingWalkThrough: []*pb.WalkThroughPopUp{
						{
							Message:   AdditionalSettingsMsg,
							PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_ADDITIONAL_SETTINGS,
						},
						{
							Message:   HideShowBalanceMsg,
							PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_HIDE_UN_HIDE,
						},
					},
				},
				DashboardIntroCardDetails: &pb.DashboardIntroCardDetails{
					StartIcon:              walkThroughStartIconNewUser,
					ShowCardMaxImpressions: MaxWalkThroughImpressionsPerUser,
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Hey %v!\nGlad you’re here", name1)},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L}},
					Image:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/IntroWave.png"},
					Shadow: ui.GetDashboardShadow(),
				},
			},
		},
		{
			name: "success scenario, zero balance, new user onboarding",
			req:  &pb.GetHomeAppWalkThroughFlowsRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			actorMock: func(client *mocks.MockActorClient) {
				client.EXPECT().GetEntityDetailsByActorId(ctx, &actor.GetEntityDetailsByActorIdRequest{ActorId: actor1}).Return(&actor.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: entity1, Name: &commontypes.Name{FirstName: name1}}, nil)
			},
			onboardingMock: func(client *mockOnb.MockOnboardingClient) {
				client.EXPECT().GetDetails(ctx, &onboarding.GetDetailsRequest{ActorId: actor1, CachedData: true}).Return(&onboarding.GetDetailsResponse{
					Status:  rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{CompletedAt: newUserOnboardingTimestamp},
				}, nil)
			},
			res: &pb.GetHomeAppWalkThroughFlowsResponse{
				RespHeader:            &header.ResponseHeader{Status: rpc.StatusOk()},
				OnboardingWalkthrough: newUserWalkThrough,
				PrivacySettingWalkthrough: &pb.PrivacySettingWalkthrough{
					Enabled: true,
					PrivacySettingWalkThrough: []*pb.WalkThroughPopUp{
						{
							Message:   AdditionalSettingsMsg,
							PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_ADDITIONAL_SETTINGS,
						},
						{
							Message:   HideShowBalanceMsg,
							PopUpType: pb.WalkThroughPopUp_POP_UP_TYPE_HIDE_UN_HIDE,
						},
					},
				},
				DashboardIntroCardDetails: &pb.DashboardIntroCardDetails{
					StartIcon:              walkThroughStartIconNewUser,
					ShowCardMaxImpressions: MaxWalkThroughImpressionsPerUser,
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Hey %v!\nGlad you’re here", name1)},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L}},
					Image:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/IntroWave.png"},
					Shadow: ui.GetDashboardShadow(),
				},
			},
		},
	}

	// Mock client initialisation
	ctrl := gomock.NewController(t)
	actorClient := mocks.NewMockActorClient(ctrl)
	onboardingClient := mockOnb.NewMockOnboardingClient(ctrl)
	savingsClient := mocks2.NewMockSavingsClient(ctrl)
	connectedAccountsClient := connectedAccountMocks.NewMockConnectedAccountClient(ctrl)

	s := Service{savClient: savingsClient, actorClient: actorClient, onboardingClient: onboardingClient, conf: conf, genconf: gconf, beConnectedAccountClient: connectedAccountsClient}
	for _, tt := range tests {
		t.Run(t.Name(), func(t *testing.T) {
			if tt.actorMock != nil {
				tt.actorMock(actorClient)
			}
			if tt.onboardingMock != nil {
				tt.onboardingMock(onboardingClient)
			}
			res, err := s.GetHomeAppWalkThroughFlows(ctx, tt.req)
			if err != nil {
				logger.Error(ctx, "failed rpc", zap.Error(err))
			}
			if !proto.Equal(res, tt.res) {
				t.Errorf("got: %v, want: %v", res, tt.res)
			}
		})
	}
}

/*
// todo (himanshu) see if this test is reqd or not
func TestService_GetHomeExplore(t *testing.T) {
	type test struct {
		name string
		res  *pb.GetHomeExploreResponse
	}

	tests := []test{
		{
			name: "success",
			res: &pb.GetHomeExploreResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
				Sections: []*pb.HomeExploreSection{
					{
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Must Try"},
							FontColor: Ink, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M}},
						BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
						Icon: []*pb.Icon{
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Salary Benefits"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconImages/SalaryBenefit.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_SALARY_PROGRAM_INTRO_SCREEN},
								},
							},
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Invest in Jump"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreInvestJump.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN},
								},
							},
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Instant Loan"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/InstantLoan.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN},
								},
							},
						},
					},
					{
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Investments"},
							FontColor: Ink, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M}},
						BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#F7F9FC"}},
						Icon: []*pb.Icon{
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Set-up SIP"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/AutoInvest.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_FIT_ALL_COLLECTIONS_PAGE,
										ScreenOptions: &deeplink.Deeplink_FitAllCollectionsPageScreenOptions{
											FitAllCollectionsPageScreenOptions: &deeplink.FitAllCollectionsPageScreenOptions{
												CollectionType: "COLLECTION_TYPE_AUTO_INVEST",
											},
										},
									},
								},
							},
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Jump"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreP2P.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN},
								},
							},
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Mutual Funds"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreMutualFunds.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN},
								},
							},
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Fixed Deposits"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreFixedDeposit.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{
										Screen: deeplink.Screen_DEPOSIT_LANDING_SCREEN,
										ScreenOptions: &deeplink.Deeplink_DepositAccountLandingScreenOption{
											DepositAccountLandingScreenOption: &deeplink.DepositAccountLandingScreenOptions{
												DepositType: accounts.Type_FIXED_DEPOSIT,
											},
										},
									},
								},
							},
						},
					},
					{
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Payments"},
							FontColor: Ink, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M}},
						BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#F0F3F7"}},
						Icon: []*pb.Icon{
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Pay by UPI"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PayViaUpi.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_PAY_VIA_SEARCH},
								},
							},
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Bank Transfer"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/BankTransfer.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_PAY_VIA_BANK_TRANSFER},
								},
							},
						},
					},
					{
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Cards"},
							FontColor: Ink, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M}},
						BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}},
						Icon: []*pb.Icon{
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Debit Card"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/DebitCard.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_CARD_HOME_SCREEN},
								},
							},
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Debit Card Offers"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/DebitCardOffers.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN},
								},
							},
						},
					},
					{
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Rewards & Referrals"},
							FontColor: Ink, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M}},
						BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E3E7EC"}},
						Icon: []*pb.Icon{
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Earn Rewards"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreEarnRewards.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_REWARDS_WAYS_TO_EARN},
								},
							},
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Use your Fi-Coins"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreOfferCatalog.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_OFFERS_LANDING_SCREEN},
								},
							},
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Refer and earn ₹300"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreReferral.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_REFERRALS_ELIGIBILITY_LANDING_SCREEN},
								},
							},
						},
					},
					{
						Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Others"},
							FontColor: Ink, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M}},
						BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#D9DEE3"}},
						Icon: []*pb.Icon{
							{
								Title: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Complete KYC"},
									FontColor:    Slate, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
								IconImage:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/CompleteKYC.png"},
								ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
								Action: &pb.Icon_Deeplink{
									Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_VKYC_STATUS_SCREEN},
								},
							},
						},
					},
				},
			},
		},
	}

	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{conf: conf}
			res, err := s.GetHomeExplore(ctx, nil)
			if err != nil {
				t.Errorf("failed to get home explore")
			}
			if !proto.Equal(res, tt.res) {
				t.Errorf("got: %v, want: %v", res, tt.res)
			}
		})
	}
}
*/

func TestService_GetHomeProfileInfo(t *testing.T) {
	type mockStruct struct {
		onbClient             *mockOnb.MockOnboardingClient
		userClient            *mocks3.MockUsersClient
		salaryClient          *mocks4.MockSalaryProgramClient
		vkycFeClient          *mocks5.MockVKYCFeClient
		tieringClient         *mocks6.MockTieringClient
		bcClient              *mocks7.MockBankCustomerServiceClient
		feReleaseClient       *mocks8.MockFeManager
		employmentClient      *mocks9.MockEmploymentClient
		actorClient           *mocks.MockActorClient
		savingsClient         *mocks2.MockSavingsClient
		accountBalanceClient  *accountBalanceMock.MockBalanceClient
		segmentClient         *segmentMock.MockSegmentationServiceClient
		releaseEvaluator      *releaseMock.MockIEvaluator
		mocUserClient         mocks3.MockUsersClient
		userAttributesFetcher *mockUserAttributeFetcher.MockUserAttributesFetcher
		mockNetworthClient    *mockNetworthPb.MockNetWorthClient
	}
	type test struct {
		name        string
		req         *pb.GetHomeProfileInfoRequest
		possibleRes []*pb.GetHomeProfileInfoResponse
		mockFunc    func(*mockStruct)
	}

	var (
		actor1        = "actor1"
		profileImage1 = "image url"
		dl, _         = onbPkg.GetSABenefitsScreen(context.Background())
	)

	ctx := gomock.Any()

	tests := []test{
		{
			name: "min kyc user, tiering disabled",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.NewStatusWithoutDebug(uint32(beTieringPb.GetTieringPitchV2Response_DISABLED), "tiering is disabled for actor"),
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						Id:         "customerId1",
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_MIN_KYC},
					},
				}, nil)
				m.vkycFeClient.EXPECT().GetProfileExtension(ctx, &vkyc.GetProfileExtensionRequest{
					ActorId: actor1,
				}).Return(&vkyc.GetProfileExtensionResponse{
					Status: rpc.StatusOk(),
					Deeplink: &deeplink.Deeplink{
						Screen:        deeplink.Screen_VKYC_STATUS_SCREEN,
						ScreenOptions: &deeplink.Deeplink_VkycStatusScreenOptions{VkycStatusScreenOptions: &deeplink.VKYCStatusScreenOptions{IgnoreRpcCall: false}},
					},
					Title: MinKycPrompt1,
				}, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/MinKycBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    UserIsMinKycColour,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: MinKycPrompt1},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen:        deeplink.Screen_VKYC_STATUS_SCREEN,
							ScreenOptions: &deeplink.Deeplink_VkycStatusScreenOptions{VkycStatusScreenOptions: &deeplink.VKYCStatusScreenOptions{IgnoreRpcCall: false}},
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevronRavenSteel.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevronRavenSteel.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "min kyc user, tiering enabled",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        10000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        50000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Salary{
													Salary: &criteriaPb.Salary{
														MinSalary: &money.Money{
															CurrencyCode: "INR",
															Units:        20000,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_MIN_KYC},
					},
				}, nil)
				m.vkycFeClient.EXPECT().GetProfileExtension(ctx, &vkyc.GetProfileExtensionRequest{
					ActorId: actor1,
				}).Return(&vkyc.GetProfileExtensionResponse{
					Status: rpc.StatusOk(),
					Deeplink: &deeplink.Deeplink{
						Screen:        deeplink.Screen_VKYC_STATUS_SCREEN,
						ScreenOptions: &deeplink.Deeplink_VkycStatusScreenOptions{VkycStatusScreenOptions: &deeplink.VKYCStatusScreenOptions{IgnoreRpcCall: false}},
					},
					Title: MinKycPrompt1,
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/MinKycBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    UserIsMinKycColour,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: MinKycPrompt1},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen:        deeplink.Screen_VKYC_STATUS_SCREEN,
							ScreenOptions: &deeplink.Deeplink_VkycStatusScreenOptions{VkycStatusScreenOptions: &deeplink.VKYCStatusScreenOptions{IgnoreRpcCall: false}},
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevronRavenSteel.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevronRavenSteel.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "min kyc user, error in pitch v2",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.StatusInternal(),
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_MIN_KYC},
					},
				}, nil)
				m.vkycFeClient.EXPECT().GetProfileExtension(ctx, &vkyc.GetProfileExtensionRequest{
					ActorId: actor1,
				}).Return(&vkyc.GetProfileExtensionResponse{
					Status: rpc.StatusOk(),
					Deeplink: &deeplink.Deeplink{
						Screen:        deeplink.Screen_VKYC_STATUS_SCREEN,
						ScreenOptions: &deeplink.Deeplink_VkycStatusScreenOptions{VkycStatusScreenOptions: &deeplink.VKYCStatusScreenOptions{IgnoreRpcCall: false}},
					},
					Title: MinKycPrompt1,
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/MinKycBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    UserIsMinKycColour,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: MinKycPrompt1},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen:        deeplink.Screen_VKYC_STATUS_SCREEN,
							ScreenOptions: &deeplink.Deeplink_VkycStatusScreenOptions{VkycStatusScreenOptions: &deeplink.VKYCStatusScreenOptions{IgnoreRpcCall: false}},
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevronRavenSteel.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevronRavenSteel.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, error fetching employment info",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.NewStatusWithoutDebug(uint32(beTieringPb.GetTieringPitchV2Response_DISABLED), "tiering is disabled for actor"),
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_INITIATED,
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/FullKycBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    Snow,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: FullKycNonSalaryRegPrompt1},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_SALARY_PROGRAM_INTRO_SCREEN,
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevron.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Charcoal}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, non salary registered user, tiering disabled",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.NewStatusWithoutDebug(uint32(beTieringPb.GetTieringPitchV2Response_DISABLED), "tiering is disabled for actor"),
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_INITIATED,
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/FullKycBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    Snow,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: FullKycNonSalaryRegPrompt1},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_SALARY_PROGRAM_INTRO_SCREEN,
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevron.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Charcoal}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, sal-reg but not sal-active user, tiering disabled",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.NewStatusWithoutDebug(uint32(beTieringPb.GetTieringPitchV2Response_DISABLED), "tiering is disabled for actor"),
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/FullKycBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    Ink,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: FullKycSalaryInactivePrompt1},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevronDarkLemon.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevronDarkLemon.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DarkLemon}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, sal-active user, tiering disabled",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.NewStatusWithoutDebug(uint32(beTieringPb.GetTieringPitchV2Response_DISABLED), "tiering is disabled for actor"),
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SalaryBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    Snow,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: SalaryActivePrompt},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevron.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Charcoal}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, sal-active user, error in release evaluator",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, errors.New("some random error"))
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.NewStatusWithoutDebug(uint32(beTieringPb.GetTieringPitchV2Response_DISABLED), "tiering is disabled for actor"),
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SalaryBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    Snow,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: SalaryActivePrompt},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevron.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Charcoal}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, employment info not found",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        10000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        50000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Salary{
													Salary: &criteriaPb.Salary{
														MinSalary: &money.Money{
															CurrencyCode: "INR",
															Units:        20000,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
				m.salaryClient.EXPECT().GetAaSalaryDetails(ctx, gomock.Any()).Return(
					&salaryPb.GetAaSalaryDetailsResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: tierBadgePlusIconUrl},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    user.PlusTierTitleColor,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: tierTextPlus},
						},
						ActionType:    pb.Icon_ACTION_TYPE_DEEPLINK,
						Action:        &pb.Icon_Deeplink{Deeplink: tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_PLUS, false)},
						IconImage:     &commontypes.Image{ImageUrl: promptChevronRavenSteel},
						VisualElement: commontypes.GetVisualElementImageFromUrl(promptChevronRavenSteel),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, plus user, non grace",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        10000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        50000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Salary{
													Salary: &criteriaPb.Salary{
														MinSalary: &money.Money{
															CurrencyCode: "INR",
															Units:        20000,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
				m.salaryClient.EXPECT().GetAaSalaryDetails(ctx, gomock.Any()).Return(
					&salaryPb.GetAaSalaryDetailsResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: tierBadgePlusIconUrl},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    user.PlusTierTitleColor,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: tierTextPlus},
						},
						ActionType:    pb.Icon_ACTION_TYPE_DEEPLINK,
						Action:        &pb.Icon_Deeplink{Deeplink: tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_PLUS, false)},
						IconImage:     &commontypes.Image{ImageUrl: promptChevronRavenSteel},
						VisualElement: commontypes.GetVisualElementImageFromUrl(promptChevronRavenSteel),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, plus user, cool off",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        10000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: false,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        50000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
							IsMovementAllowed: false,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Salary{
													Salary: &criteriaPb.Salary{
														MinSalary: &money.Money{
															CurrencyCode: "INR",
															Units:        20000,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
				m.salaryClient.EXPECT().GetAaSalaryDetails(ctx, gomock.Any()).Return(
					&salaryPb.GetAaSalaryDetailsResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: tierBadgePlusIconUrl},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    user.PlusTierTitleColor,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: tierTextPlus},
						},
						ActionType:    pb.Icon_ACTION_TYPE_DEEPLINK,
						Action:        &pb.Icon_Deeplink{Deeplink: tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_PLUS, false)},
						IconImage:     &commontypes.Image{ImageUrl: promptChevronRavenSteel},
						VisualElement: commontypes.GetVisualElementImageFromUrl(promptChevronRavenSteel),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, plus user, error in getting balance",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        10000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        50000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Salary{
													Salary: &criteriaPb.Salary{
														MinSalary: &money.Money{
															CurrencyCode: "INR",
															Units:        20000,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SalaryBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    Snow,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: SalaryActivePrompt},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevron.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Charcoal}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, plus user, error in getting employment info",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        10000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        50000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Salary{
													Salary: &criteriaPb.Salary{
														MinSalary: &money.Money{
															CurrencyCode: "INR",
															Units:        20000,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SalaryBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    Snow,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: SalaryActivePrompt},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevron.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Charcoal}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, NR account",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_BASIC,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        10000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
							GraceParams: &tieringExtPb.GraceParams{Period: 1296000},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        50000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Salary{
													Salary: &criteriaPb.Salary{
														MinSalary: &money.Money{
															CurrencyCode: "INR",
															Units:        20000,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser:   true,
					ResidentCountryCode: types.CountryCode_COUNTRY_CODE_ARE,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SalaryBadge.png"},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    Snow,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: SalaryActivePrompt},
						},
						ActionType: pb.Icon_ACTION_TYPE_DEEPLINK,
						Action: &pb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
						}},
						IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"},
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/PromptChevron.png"),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Charcoal}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, plus user, grace",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: false,
							MovementTimestamp: timestampPb.New(time.Now().
								Add(gconf.HomeRevampParams().HomeNudgeParams().TieringParams().GraceWindowDuration()).Add(time.Second * 1)),
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        10000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
							GraceParams: &tieringExtPb.GraceParams{Period: 1296000},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        50000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Salary{
													Salary: &criteriaPb.Salary{
														MinSalary: &money.Money{
															CurrencyCode: "INR",
															Units:        20000,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: warningGraceBadge},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    UserInGraceColour,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: graceTextPlus},
						},
						ActionType:    pb.Icon_ACTION_TYPE_DEEPLINK,
						Action:        &pb.Icon_Deeplink{Deeplink: addFundsDeeplink()},
						IconImage:     &commontypes.Image{ImageUrl: promptChevronRavenSteel},
						VisualElement: commontypes.GetVisualElementImageFromUrl(promptChevronRavenSteel),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "full kyc user, plus user, grace, initial 3 days, tier benefit notch shown",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_SA.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.feReleaseClient.EXPECT().IsTieringEnabledForActor(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
				m.employmentClient.EXPECT().GetEmploymentInfo(gomock.Any(), gomock.Any()).Return(&employment.GetEmploymentInfoResponse{
					EmploymentData: &employment.EmploymentData{
						EmploymentType: employment.EmploymentType_SALARIED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				m.actorClient.EXPECT().GetActorById(gomock.Any(), gomock.Any()).Return(&actor.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: "user-1",
					},
				}, nil)
				m.savingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savings.GetAccountResponse{
					Account: &savings.Account{
						Id: "account-1",
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.accountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{
					Status: rpc.StatusOk(),
					AvailableBalance: &money.Money{
						CurrencyCode: "INR",
						Units:        500,
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.tieringClient.EXPECT().GetTieringPitchV2(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
							IsMovementAllowed: false,
							MovementTimestamp: timestampPb.New(time.Now().
								Add(15*24*time.Hour - time.Hour)),
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        10000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
							GraceParams: &tieringExtPb.GraceParams{Period: 1296000},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Balance{
													Balance: &criteriaPb.Balance{
														MinBalance: &money.Money{
															CurrencyCode: "INR",
															Units:        50000,
														},
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
									},
								},
							},
						},
						{
							TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
							IsMovementAllowed: true,
							Options: []*criteriaPb.Option{
								{
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Kyc{
													Kyc: &criteriaPb.Kyc{
														KycLevel: kyc.KYCLevel_FULL_KYC,
													},
												},
											},
										},
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{
												Criteria: &criteriaPb.QualifyingCriteria_Salary{
													Salary: &criteriaPb.Salary{
														MinSalary: &money.Money{
															CurrencyCode: "INR",
															Units:        20000,
														},
													},
												},
											},
										},
									},
								},
							},
						},
					},
				}, nil)
				m.tieringClient.EXPECT().GetConfigParams(gomock.Any(), gomock.Any()).Return(&beTieringPb.GetConfigParamsResponse{
					Status:                     rpc.StatusOk(),
					DowngradeWindowDuration:    durationpb.New(5 * 24 * time.Hour),
					GraceWindowDuration:        durationpb.New(5 * 24 * time.Hour),
					GraceInitialWindowDuration: durationpb.New(3 * 24 * time.Hour),
				}, nil).AnyTimes()
				m.userClient.EXPECT().GetUser(ctx, &userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_ActorId{ActorId: actor1}, WantProfileImageUrl: true}).
					Return(&userPb.GetUserResponse{User: &userPb.User{Profile: &userPb.Profile{ProfileImageUrl: profileImage1}}, Status: rpc.StatusOk()}, nil)
				m.bcClient.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actor1},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						ActorId:    actor1,
						DedupeInfo: &bankCustomerPb.DedupeInfo{KycLevel: kyc.KYCLevel_FULL_KYC},
					},
				}, nil)
				m.salaryClient.EXPECT().GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actor1, FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE}).
					Return(&salaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationStatus: salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				m.salaryClient.EXPECT().GetLatestActivationDetailsActiveAtTime(ctx, gomock.Any()).
					Return(&salaryPb.LatestActivationDetailsActiveAtTimeResponse{
						Status: rpc.StatusOk(),
					}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
				m.salaryClient.EXPECT().GetAaSalaryDetails(ctx, gomock.Any()).Return(
					&salaryPb.GetAaSalaryDetailsResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader:   &header.ResponseHeader{Status: rpc.StatusOk()},
					Badge:        &commontypes.Image{ImageUrl: tierBadgePlusIconUrl},
					ProfileImage: &commontypes.Image{ImageUrl: profileImage1},
					ProfileNudge: &pb.GetHomeProfileInfoResponse_ProfileNudge{ExpandedIcon: &pb.Icon{
						Title: &commontypes.Text{
							FontColor:    user.PlusTierTitleColor,
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							DisplayValue: &commontypes.Text_PlainString{PlainString: tierTextPlus},
						},
						ActionType:    pb.Icon_ACTION_TYPE_DEEPLINK,
						Action:        &pb.Icon_Deeplink{Deeplink: tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_PLUS, false)},
						IconImage:     &commontypes.Image{ImageUrl: promptChevronRavenSteel},
						VisualElement: commontypes.GetVisualElementImageFromUrl(promptChevronRavenSteel),
						BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}},
						UiVariant:   pb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "wealth analyser user",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_WEALTH_ANALYSER.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.mockNetworthClient.EXPECT().GetNetWorthValue(gomock.Any(), &networthPb.GetNetWorthValueRequest{
					ActorId: actor1,
				}).Return(&networthPb.GetNetWorthValueResponse{
					Status: rpc.StatusOk(),
					AssetValues: []*networthPb.AssetValue{
						{
							ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
						},
					},
				}, nil)
				m.userClient.EXPECT().GetUserProfile(gomock.Any(), &userPb.GetUserProfileRequest{
					ActorId: actor1,
				}).Return(&userPb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
					Image: &commontypes.Image{
						ImageUrl: profileImage1,
					},
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusOk(),
					},
					ProfileImage: &commontypes.Image{
						ImageUrl: profileImage1,
					},
					UserName:    &commontypes.Name{},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
		{
			name: "fi lite user",
			req:  &pb.GetHomeProfileInfoRequest{Req: &header.RequestHeader{Auth: &header.AuthHeader{ActorId: actor1}}},
			mockFunc: func(m *mockStruct) {
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						StageMetadata: &onboarding.StageMetadata{
							IntentSelectionMetadata: &onboarding.IntentSelectionMetadata{
								Selection: onboarding.OnboardingIntent_ONBOARDING_INTENT_FI_LITE,
							},
						},
						FeatureDetails: &onboarding.FeatureDetails{FeatureInfo: map[string]*onboarding.FeatureInfo{
							onboarding.Feature_FEATURE_FI_LITE.String(): {
								FeatureStatus: onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
							},
						}},
					},
				}, nil)
				m.releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()
				m.userClient.EXPECT().GetUserProfile(gomock.Any(), &userPb.GetUserProfileRequest{
					ActorId: actor1,
				}).Return(&userPb.GetUserProfileResponse{
					Status: rpc.StatusOk(),
					Image: &commontypes.Image{
						ImageUrl: profileImage1,
					},
				}, nil)
				m.onbClient.EXPECT().GetDetails(gomock.Any(), &onboarding.GetDetailsRequest{
					ActorId:    actor1,
					CachedData: true,
				}).Return(&onboarding.GetDetailsResponse{
					Status: rpc.StatusOk(),
					Details: &onboarding.OnboardingDetails{
						ActorId: actor1,
					},
				}, nil)
				m.userAttributesFetcher.EXPECT().IsNonResidentUser(gomock.Any(), &pkgUser.IsNonResidentUserRequest{
					ActorId: actor1,
				}).Return(&pkgUser.IsNonResidentUserResponse{
					IsNonResidentUser: false,
				}, nil).Times(2)
			},
			possibleRes: []*pb.GetHomeProfileInfoResponse{
				{
					RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
					ProfileImage: &commontypes.Image{
						ImageUrl: profileImage1,
					},
					UserName: &commontypes.Name{},
					ProfileNudge: &feHomePb.GetHomeProfileInfoResponse_ProfileNudge{
						ExpandedIcon: &feHomePb.Icon{
							IconImage: &commontypes.Image{
								ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png",
								Height:   24,
								Width:    24,
							},
							VisualElement: &commontypes.VisualElement{
								Asset: &commontypes.VisualElement_Image_{
									Image: &commontypes.VisualElement_Image{
										Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"},
										Properties: &commontypes.VisualElementProperties{Height: 24, Width: 24},
									},
								},
							},
							Title: commontypes.GetTextFromStringFontColourFontStyle("Upgrade your account", "#C0DAE0", commontypes.FontStyle_SUBTITLE_S),
							Action: &feHomePb.Icon_Deeplink{
								Deeplink: dl,
							},
							IconType:   homeTypesPb.IconType_PROFILE,
							ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
							BgColour:   ui.GetBlockColor("#313234"),
						},
						UiVariant:   feHomePb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
						BorderColor: feHomePb.GetHomeWidgetBorderColor(),
					},
					BorderColor: feHomePb.GetHomeWidgetBorderColor(),
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			userMock := mocks3.NewMockUsersClient(ctrl)
			salaryMock := mocks4.NewMockSalaryProgramClient(ctrl)
			vkycFeClientMock := mocks5.NewMockVKYCFeClient(ctrl)
			beTieringMock := mocks6.NewMockTieringClient(ctrl)
			bcMock := mocks7.NewMockBankCustomerServiceClient(ctrl)
			feReleaseMock := mocks8.NewMockFeManager(ctrl)
			employmentMock := mocks9.NewMockEmploymentClient(ctrl)
			actorMock := mocks.NewMockActorClient(ctrl)
			savingsMock := mocks2.NewMockSavingsClient(ctrl)
			onbClient := mockOnb.NewMockOnboardingClient(ctrl)
			accountBalanceClientMock := accountBalanceMock.NewMockBalanceClient(ctrl)
			segmentationClientMock := segmentMock.NewMockSegmentationServiceClient(ctrl)
			releaseEvaluatorMock := releaseMock.NewMockIEvaluator(ctrl)
			userAttributeFetcher := mockUserAttributeFetcher.NewMockUserAttributesFetcher(ctrl)
			mockNetworthClient := mockNetworthPb.NewMockNetWorthClient(ctrl)

			if tt.mockFunc != nil {
				tt.mockFunc(&mockStruct{
					onbClient:             onbClient,
					userClient:            userMock,
					salaryClient:          salaryMock,
					vkycFeClient:          vkycFeClientMock,
					tieringClient:         beTieringMock,
					bcClient:              bcMock,
					feReleaseClient:       feReleaseMock,
					employmentClient:      employmentMock,
					actorClient:           actorMock,
					savingsClient:         savingsMock,
					accountBalanceClient:  accountBalanceClientMock,
					segmentClient:         segmentationClientMock,
					releaseEvaluator:      releaseEvaluatorMock,
					userAttributesFetcher: userAttributeFetcher,
					mockNetworthClient:    mockNetworthClient,
				})
			}
			s := Service{usersClient: userMock, salaryProgramClient: salaryMock, conf: conf, vkycFeClient: vkycFeClientMock,
				beTieringClient: beTieringMock, bcClient: bcMock, genconf: gconf, feTieringReleaseEvaluator: feReleaseMock,
				employmentClient: employmentMock, actorClient: actorMock, savClient: savingsMock, onboardingClient: onbClient,
				accountBalanceClient: accountBalanceClientMock, segmentClient: segmentationClientMock, evaluator: releaseEvaluatorMock,
				userAttributeFetcher: userAttributeFetcher, networthClient: mockNetworthClient}

			res, err := s.GetHomeProfileInfo(context.Background(), tt.req)
			if err != nil {
				t.Errorf("failed to get home profile info, %v", err)
			}
			matched := false
			for _, ttRes := range tt.possibleRes {
				if proto.Equal(res, ttRes) {
					matched = true
				}
			}
			if !matched {
				println(cmp.Diff(res, tt.possibleRes), protocmp.Transform())
				t.Errorf("got: %v, \n want: %v", res, tt.possibleRes)
			}
		})
	}
}

func TestService_GetLayoutFromString(t *testing.T) {
	type test struct {
		name         string
		layoutString string
		res          *layoutconfiguration.HomeLayout
		wantErr      bool
	}

	tests := []test{
		{
			name: "success",
			layoutString: "{" +
				"\"BottomWidget\":[1, 7]," +
				"\"TopWidgetLeftIcons\":[\"referral-icon\"]," +
				"\"TopWidgetRightIcons\":[\"notification-icon\", \"profile-icon\"]," +
				"\"BottomBarIcons\":[]" +
				"}",
			res: &layoutconfiguration.HomeLayout{
				BottomWidget: []pb.HomeWidget_WidgetType{
					pb.HomeWidget_WIDGET_TYPE_TOP_NAV_BAR, pb.HomeWidget_WIDGET_TYPE_REWARDS,
				},
				TopWidgetLeftIcons: []string{
					"referral-icon",
				},
				TopWidgetRightIcons: []string{
					"notification-icon", "profile-icon",
				},
				BottomBarIcons: []string{},
			},
			wantErr: false,
		},
		{
			name:         "failure",
			layoutString: "",
			wantErr:      true,
		},
		{
			name:         "emptyJson",
			layoutString: "{}",
			wantErr:      false,
			res: &layoutconfiguration.HomeLayout{
				BottomWidget:        nil,
				TopWidgetLeftIcons:  nil,
				TopWidgetRightIcons: nil,
				BottomBarIcons:      nil,
			},
		},
	}

	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{conf: conf}
			res, err := s.GetLayoutFromString(ctx, tt.layoutString)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLayoutFromString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(tt.res, res, opts...); diff != "" {
				t.Errorf("got: %v, want: %v, diff: %v", res, tt.res, diff)
			}
		})
	}
}

func TestService_GetExploreSectionsFromExperiment(t *testing.T) {
	type test struct {
		name                  string
		exploreSectionsString string
		res                   *[]*config.ExploreSection
		wantErr               bool
	}

	tests := []test{
		{
			name: "success",
			exploreSectionsString: "[{\"Title\":\"section-1\",\"BgColour\":  \"color1\", \"IconTypes\": [\"SHORTCUT_1\", \"SHORTCUT_2\"]}," +
				"{\"Title\":\"section-2\",\"BgColour\":  \"color2\", \"IconTypes\": [\"SHORTCUT_3\", \"SHORTCUT_4\"]}]",
			res: &[]*config.ExploreSection{
				&config.ExploreSection{
					Title:     "section-1",
					BgColour:  "color1",
					IconTypes: []string{"SHORTCUT_1", "SHORTCUT_2"},
				},
				&config.ExploreSection{
					Title:     "section-2",
					BgColour:  "color2",
					IconTypes: []string{"SHORTCUT_3", "SHORTCUT_4"},
				},
			},
			wantErr: false,
		},
		{
			name:                  "failure",
			exploreSectionsString: "",
			wantErr:               true,
		},
	}

	ctx := context.Background()
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{conf: conf}
			res, err := s.GetExploreSectionsFromExperiment(ctx, tt.exploreSectionsString)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExploreSectionsFromExperiment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(tt.res, res, opts...); diff != "" {
				t.Errorf("got: %v, want: %v, diff: %v", res, tt.res, diff)
			}
		})
	}
}
