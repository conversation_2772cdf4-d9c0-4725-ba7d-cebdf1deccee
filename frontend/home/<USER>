package home

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"sort"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	depositPb "github.com/epifi/gamma/api/deposit"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/fittt"
	feHdr "github.com/epifi/gamma/api/frontend/header"
	homePb "github.com/epifi/gamma/api/frontend/home"
	beSvc "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	rmsPb "github.com/epifi/gamma/api/rms/manager"
	rmsUiPb "github.com/epifi/gamma/api/rms/ui"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/investment"
)

func (s *Service) GetUpcomingUserActivities(ctx context.Context, req *homePb.GetUpcomingUserActivitiesRequest) (*homePb.GetUpcomingUserActivitiesResponse, error) {
	var (
		actorId                                = req.GetReq().GetAuth().GetActorId()
		isFeatureHomeDesignEnhancementsEnabled = featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            s.releaseEvaluator,
				OnboardingClient:     s.onboardingClient,
				QuestSdkClient:       s.questSdkClient,
				UserAttributeFetcher: s.userAttributeFetcher,
				NetWorthClient:       s.networthClient,
			},
		})
	)
	isUpcomingTxnV2Released, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_UPCOMING_TRANSACTIONS_V2).WithActorId(req.GetReq().GetAuth().GetActorId()))
	if isUpcomingTxnV2Released && err == nil {
		return s.GetUpcomingUserActivitiesV2(ctx, actorId, isFeatureHomeDesignEnhancementsEnabled)
	}
	if err != nil {
		logger.Error(ctx, "failed to evaluate if upcoming txn v2 impl is release for user")
	}
	upcomingSubs, err := s.getAllUpcomingSubs(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error getting all upcoming subs", zap.Error(err))
		return &homePb.GetUpcomingUserActivitiesResponse{RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	sort.SliceStable(upcomingSubs, func(idx1, idx2 int) bool {
		return upcomingSubs[idx1].GetNextExecutionTime().AsTime().Before(upcomingSubs[idx2].GetNextExecutionTime().AsTime())
	})
	// if max number of slots is set to 0 in conf, return all upcoming subscriptions
	if s.conf.HomeRevampParams.UpcomingActivitiesParams.MaxNumberOfSlots > 0 &&
		len(upcomingSubs) > int(s.conf.HomeRevampParams.UpcomingActivitiesParams.MaxNumberOfSlots) {
		upcomingSubs = upcomingSubs[:s.conf.HomeRevampParams.UpcomingActivitiesParams.MaxNumberOfSlots]
	}

	upcomingActivities, err := s.getUpcomingActivities(ctx, upcomingSubs)
	if err != nil {
		logger.Error(ctx, "error getting upcoming activities", zap.Error(err))
		return &homePb.GetUpcomingUserActivitiesResponse{RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}
	numberOfEmptySlotsToFill := int(s.conf.HomeRevampParams.UpcomingActivitiesParams.MaxNumberOfEmptySlots) - len(upcomingSubs)
	var dummyUpcomingSubs *homePb.UpcomingUserActivitiesZeroStateParams
	if numberOfEmptySlotsToFill > 0 {
		dummyUpcomingSubs = s.getEmptyUpcomingSubs(numberOfEmptySlotsToFill)
	}

	return &homePb.GetUpcomingUserActivitiesResponse{
		RespHeader:         &feHdr.ResponseHeader{Status: rpc.StatusOk()},
		ZeroStateParams:    dummyUpcomingSubs,
		UpcomingActivities: upcomingActivities,
		ManageButton:       getManageActivitiesComponent(isFeatureHomeDesignEnhancementsEnabled),
	}, nil
}

func (s *Service) getAllUpcomingSubs(ctx context.Context, actorId string) ([]*rmsPb.RuleSubscription, error) {
	var allRecurringRules []string
	allRecurringRules = append(allRecurringRules, s.conf.Fittt.RuleIds.AutoSaveDaily, s.conf.Fittt.RuleIds.AutoSaveWeekly, s.conf.Fittt.RuleIds.AutoSaveMonthly)
	allRecurringRules = append(allRecurringRules, s.conf.Fittt.RuleIds.AutoInvestDaily, s.conf.Fittt.RuleIds.AutoInvestWeekly, s.conf.Fittt.RuleIds.AutoInvestMonthly)
	allRecurringRules = append(allRecurringRules, s.conf.Fittt.RuleIds.AutoPayOneTime, s.conf.Fittt.RuleIds.AutoPayRecurring)
	subsRes, err := s.rmsRuleManagerClient.GetSubscriptionsByActorForRules(ctx,
		&rmsPb.GetSubscriptionsByActorForRulesRequest{
			ActorId: actorId,
			RuleIds: allRecurringRules,
			States:  []rmsPb.RuleSubscriptionState{rmsPb.RuleSubscriptionState_ACTIVE},
			// Note: GetSubscriptionsByActorForRules paginates based on created_at / updated_at col, but we require pagination by next_execution_at
			ShouldNotUsePagination: true,
		})
	if te := epifigrpc.RPCError(subsRes, err); te != nil {
		return nil, errors.Wrap(te, "error getting subs by actor for rules")
	}

	var upcomingSubs []*rmsPb.RuleSubscription
	for _, subsForRule := range subsRes.GetRuleSubscriptions() {
		for _, sub := range subsForRule.GetRuleSubscriptions() {
			if sub.GetExecutionState() != rmsPb.SubscriptionExecutionState_SUBSCRIPTION_EXECUTION_NOT_SPECIFIED &&
				sub.GetExecutionState() != rmsPb.SubscriptionExecutionState_SUBSCRIPTION_EXECUTION_ALLOWED {
				continue
			}
			// NOTE: time can be 00:00 hence not compared with current time is not done
			// TODO(Brijesh): Subs executed on a day might keep appearing on the same day, needs fix
			upcomingSubs = append(upcomingSubs, sub)
		}
	}
	return upcomingSubs, nil
}

func (s *Service) getUpcomingActivities(ctx context.Context, subs []*rmsPb.RuleSubscription) (*homePb.UpcomingUserActivities, error) {
	var upcomingActivities []*homePb.UpcomingActivity
	for _, sub := range subs {
		subscriptionTitleText, err := s.getSubscriptionTitleText(sub)
		if err != nil {
			return nil, fmt.Errorf("error getting subscription title text for sub id %s: %w", sub.GetId(), err)
		}
		amt, err := getSubAmt(sub)
		if err != nil {
			return nil, err
		}
		if !sub.GetNextExecutionTime().IsValid() {
			// TODO(Brijesh): Add alert on below log line or revert to returning error after making the appropriate fix in RMS/FIT
			logger.Error(ctx, "error getting sub next exec time", zap.String(logger.SUBSCRIPTION_ID, sub.GetId()))
			continue
		}
		nextExecutionAt := sub.GetNextExecutionTime().AsTime().In(datetime.IST)
		imgURL, err := s.getSubImgURL(ctx, sub)
		if err != nil {
			return nil, fmt.Errorf("error getting sub img url for sub id %s: %w", sub.GetId(), err)
		}
		var autoPayRecipientBgColorCode string
		if imgURL == "" && isRecurringAutoPayRule(sub, s.conf) {
			recipientActorId, err := s.getAutoPayRecipientActorId(ctx, sub)
			if err != nil {
				return nil, fmt.Errorf("error getting autopay recipient actor id for sub id %s: %w", sub.GetId(), err)
			}
			autoPayRecipientBgColorCode = actorPb.GetColourCodeForActor(recipientActorId)
		}
		upcomingActivity := &homePb.UpcomingActivity{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: subscriptionTitleText},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
			},
			Amount:              amt,
			Date:                &types.Date{Year: int32(nextExecutionAt.Year()), Month: int32(nextExecutionAt.Month()), Day: int32(nextExecutionAt.Day())},
			Icon:                &commontypes.Image{ImageUrl: imgURL},
			ImageColourCode:     &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: autoPayRecipientBgColorCode}},
			TextColourCode:      Snow,
			TransactionTypeIcon: &commontypes.Image{ImageUrl: getSubTxnTypeIconURL(sub, s.conf)},
			Link: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_FIT_SUBSCRIPTION_INFO_PAGE,
				ScreenOptions: &deeplinkPb.Deeplink_FitSubscriptionInfoRuleScreenOptions{
					FitSubscriptionInfoRuleScreenOptions: &deeplinkPb.FitSubscriptionInfoRuleScreenOptions{SubscriptionId: sub.GetId()},
				},
			},
			BorderColor: homePb.GetHomeWidgetBorderColor(),
		}
		upcomingActivities = append(upcomingActivities, upcomingActivity)
	}
	return &homePb.UpcomingUserActivities{UpcomingActivity: upcomingActivities}, nil
}

func (s *Service) getSubscriptionTitleText(sub *rmsPb.RuleSubscription) (string, error) {
	switch sub.GetRuleId() {
	case s.conf.Fittt.RuleIds.AutoSaveDaily:
		return "Daily AutoSave", nil
	case s.conf.Fittt.RuleIds.AutoSaveWeekly:
		return "Weekly AutoSave", nil
	case s.conf.Fittt.RuleIds.AutoSaveMonthly:
		return "Monthly AutoSave", nil
	case s.conf.Fittt.RuleIds.AutoInvestDaily:
		return "Daily SIP", nil
	case s.conf.Fittt.RuleIds.AutoInvestWeekly:
		return "Weekly SIP", nil
	case s.conf.Fittt.RuleIds.AutoInvestMonthly:
		return "Monthly SIP", nil
	case s.conf.Fittt.RuleIds.AutoPayRecurring:
		freq, err := getRecurringAutoPayFreq(sub)
		if err != nil {
			return "", err
		}
		return freq + " AutoPay", nil
	case s.conf.Fittt.RuleIds.AutoPayOneTime:
		return "OneTime AutoPay", nil
	default:
		return "", errors.New("unhandled rule id for subscription title text")
	}
}

func getRecurringAutoPayFreq(sub *rmsPb.RuleSubscription) (string, error) {
	var freqVal *rmsUiPb.FrequencyVal
	for _, paramValue := range sub.GetRuleParamValues().GetRuleParamValues() {
		if paramValue.GetFrequencyVal() != nil {
			freqVal = paramValue.GetFrequencyVal()
		}
	}

	freqText := freqVal.GetDisplayText()
	if freqText == "" {
		return "", errors.New("empty frequency")
	}
	return freqText, nil
}

func getSubAmt(sub *rmsPb.RuleSubscription) (*types.Money, error) {
	for _, paramValue := range sub.GetRuleParamValues().GetRuleParamValues() {
		if paramValue.GetMoneyVal() != nil {
			return paramValue.GetMoneyVal(), nil
		}
	}
	return nil, errors.New("error getting subscription amount")
}

func (s *Service) getSubImgURL(ctx context.Context, sub *rmsPb.RuleSubscription) (string, error) {
	if isRecurringAutoInvestRule(sub, s.conf) {
		url, err := s.getAutoInvestMFIconURL(ctx, sub)
		if err != nil {
			return "", err
		}
		return url, nil
	}
	if isRecurringAutoSaveRule(sub, s.conf) {
		url, err := s.getAutoSaveSDIconURL(ctx, sub)
		if err != nil {
			return "", err
		}
		return url, nil
	}
	if isRecurringAutoPayRule(sub, s.conf) {
		url, err := s.getAutoPayIconURL(ctx, sub)
		if err != nil {
			return "", err
		}
		return url, nil
	}
	return "", errors.New("unhandled rule ID for subscription image URL")
}

func getSubTxnTypeIconURL(sub *rmsPb.RuleSubscription, feConf *config.Config) string {
	if isRecurringAutoSaveRule(sub, feConf) || isRecurringAutoInvestRule(sub, feConf) {
		return feConf.HomeRevampParams.UpcomingActivitiesParams.ZigZagArrowNESmallImgURL
	} else {
		return feConf.HomeRevampParams.UpcomingActivitiesParams.StraightArrowNESmallImgURL
	}
}

func isRecurringAutoInvestRule(sub *rmsPb.RuleSubscription, feConf *config.Config) bool {
	return sub.GetRuleId() == feConf.Fittt.RuleIds.AutoInvestDaily ||
		sub.GetRuleId() == feConf.Fittt.RuleIds.AutoInvestWeekly ||
		sub.GetRuleId() == feConf.Fittt.RuleIds.AutoInvestMonthly
}

func isRecurringAutoSaveRule(sub *rmsPb.RuleSubscription, feConf *config.Config) bool {
	return sub.GetRuleId() == feConf.Fittt.RuleIds.AutoSaveDaily ||
		sub.GetRuleId() == feConf.Fittt.RuleIds.AutoSaveWeekly ||
		sub.GetRuleId() == feConf.Fittt.RuleIds.AutoSaveMonthly
}

func isRecurringAutoPayRule(sub *rmsPb.RuleSubscription, feConf *config.Config) bool {
	return sub.GetRuleId() == feConf.Fittt.RuleIds.AutoPayRecurring ||
		sub.GetRuleId() == feConf.Fittt.RuleIds.AutoPayOneTime
}

// If FIT doesn't provide an icon, get it from MF catalog
func (s *Service) getAutoInvestMFIconURL(ctx context.Context, sub *rmsPb.RuleSubscription) (string, error) {
	var mfVal *rmsPb.MutualFundValue
	for _, paramValue := range sub.GetRuleParamValues().GetRuleParamValues() {
		if paramValue.GetMutualFundVal() != nil {
			mfVal = paramValue.GetMutualFundVal()
			break
		}
	}
	if mfVal.GetIconUrl() != "" {
		return mfVal.GetIconUrl(), nil
	}
	if mfVal.GetMfId() == "" {
		return "", errors.New("error getting MF ID from FIT rule params")
	}
	// TODO(Brijesh): Use one RPC to get all MF IDs
	res, err := s.mfCatalogManagerClient.GetMutualFund(ctx, &beSvc.GetMutualFundRequest{Id: mfVal.GetMfId()})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return "", errors.Wrap(te, "error getting MF")
	}
	return investment.IconsForAmc[res.GetMutualFund().GetAmc()], nil
}

func (s *Service) getAutoSaveSDIconURL(ctx context.Context, sub *rmsPb.RuleSubscription) (string, error) {
	sdAccount, err := s.getAutoSaveSD(ctx, sub)
	if err != nil {
		return "", err
	}
	return sdAccount.GetDepositIcon().GetImageUrl(), nil
}

func (s *Service) getAutoSaveSD(ctx context.Context, sub *rmsPb.RuleSubscription) (*depositPb.DepositAccount, error) {
	var sdVal *rmsPb.SdParamValue
	for _, paramValue := range sub.GetRuleParamValues().GetRuleParamValues() {
		if paramValue.GetSdValue() != nil {
			sdVal = paramValue.GetSdValue()
			break
		}
	}
	if sdVal.GetAccountId() == "" {
		return nil, errors.New("error getting SD account ID from FIT rule params")
	}
	// TODO(Brijesh): Use one RPC to get all SDs
	res, err := s.depositClient.GetById(ctx, &depositPb.GetByIdRequest{Id: sdVal.GetAccountId()})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, errors.Wrap(te, "error getting deposit")
	}
	return res.GetAccount(), nil
}

// If there is no profile image of recipient (e.g. non-Fi user), we do not send error
// caller is expected to use a fallback if profile image is not present in response
func (s *Service) getAutoPayIconURL(ctx context.Context, sub *rmsPb.RuleSubscription) (string, error) {
	recipientActorId, err := s.getAutoPayRecipientActorId(ctx, sub)
	if err != nil {
		return "", err
	}
	entityRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: recipientActorId,
	})
	if te := epifigrpc.RPCError(entityRes, err); te != nil {
		return "", errors.Wrap(te, "error getting actor entity details")
	}
	if entityRes.GetType() != types.ActorType_USER {
		// recipient is a non-Fi bank actor
		return "", nil
	}
	return entityRes.GetProfileImageUrl(), nil
}

func (s *Service) getAutoPayRecipientActorId(ctx context.Context, sub *rmsPb.RuleSubscription) (string, error) {
	var rpVal *rmsPb.RecurringPaymentInfo
	for _, paramValue := range sub.GetRuleParamValues().GetRuleParamValues() {
		if paramValue.GetRecurringPaymentInfo() != nil {
			rpVal = paramValue.GetRecurringPaymentInfo()
			break
		}
	}
	if rpVal.GetRecurringPaymentId() == "" {
		return "", errors.New("error getting recurring payment ID from FIT rule params")
	}
	rpRes, err := s.recurringPaymentClient.GetRecurringPaymentById(ctx, &rpPb.GetRecurringPaymentByIdRequest{
		Id: rpVal.GetRecurringPaymentId(),
	})
	if te := epifigrpc.RPCError(rpRes, err); te != nil {
		return "", errors.Wrap(te, "error getting recurring payment by id")
	}
	var recipientActorId string
	if rpRes.GetRecurringPayment().GetFromActorId() != sub.GetActorId() {
		recipientActorId = rpRes.GetRecurringPayment().GetFromActorId()
	} else {
		recipientActorId = rpRes.GetRecurringPayment().GetToActorId()
	}
	return recipientActorId, nil
}

func (s *Service) getEmptyUpcomingSubs(numSlots int) *homePb.UpcomingUserActivitiesZeroStateParams {
	var emptySlotImages []*commontypes.Image
	numOfEmptyImgURLsInConfig := len(s.conf.HomeRevampParams.UpcomingActivitiesParams.EmptySlotImages)
	for i := 0; i < numSlots; i++ {
		emptySlotImages = append(emptySlotImages, &commontypes.Image{ImageUrl: s.conf.HomeRevampParams.UpcomingActivitiesParams.EmptySlotImages[i%numOfEmptyImgURLsInConfig]})
	}
	return &homePb.UpcomingUserActivitiesZeroStateParams{
		AdditionalZeroSlots: int32(numSlots),
		Image:               emptySlotImages,
		ZeroStateMsg:        "Your scheduled SIPs, AutoSave & AutoPay will show here",
		ZeroStateDeeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_FIT_ALL_COLLECTIONS_PAGE,
			ScreenOptions: &deeplinkPb.Deeplink_FitAllCollectionsPageScreenOptions{
				FitAllCollectionsPageScreenOptions: &deeplinkPb.FitAllCollectionsPageScreenOptions{CollectionType: fePb.CollectionType_COLLECTION_TYPE_AUTO_SAVE.String()},
			}},
	}
}

func getManageActivitiesComponent(isFeatureHomeDesignEnhancementsEnabled bool) *ui.IconTextComponent {
	itc := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle("MANAGE", "#646464", commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(
			&deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_FIT_MY_RULES_PAGE,
				ScreenOptions: &deeplinkPb.Deeplink_FitMyRulesPage{
					FitMyRulesPage: &deeplinkPb.FitMyRulesPage{
						RuleCategory: rmsPb.RuleCategory_AUTO_INVEST.String(),
						SubStates: []deeplinkPb.RuleSubscriptionState{
							deeplinkPb.RuleSubscriptionState_ACTIVE_SUBSCRIPTION,
							deeplinkPb.RuleSubscriptionState_PAUSED_SUBSCRIPTION,
						},
					},
				},
			})

	if isFeatureHomeDesignEnhancementsEnabled {
		itc.Texts = []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle("MANAGE", "#00B899", commontypes.FontStyle_OVERLINE_2XS_CAPS),
		}
		itc.RightImgTxtPadding = 4
		itc.ContainerProperties = &ui.IconTextComponent_ContainerProperties{
			BgColor:       "#DCF3EE",
			CornerRadius:  24,
			Height:        28,
			Width:         80,
			LeftPadding:   15,
			RightPadding:  10,
			TopPadding:    4,
			BottomPadding: 4,
		}
		itc.RightVisualElement = commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home-v2/green-chevron-icon-2.png", 20, 20)
	}

	return itc
}
