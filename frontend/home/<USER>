package home

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/frontend/pkg/common"
	homePkg "github.com/epifi/gamma/frontend/pkg/home"
)

func (s *Service) getCommonUserDetails(ctx context.Context, actorId string) (*CommonUserDetails, error) {
	userType, userDetails, _, err := homePkg.GetSuitableUserType(ctx, &homePkg.GetSuitableUserTypeRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.evaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	})
	if err != nil {
		return nil, fmt.Errorf("error in get suitable user type: %w", err)
	}
	return &CommonUserDetails{
		UserType:      userType,
		OnbDetailsRes: userDetails,
	}, nil
}
