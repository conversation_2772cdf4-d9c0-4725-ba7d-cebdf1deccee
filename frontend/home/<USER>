package home

import (
	"context"
	json2 "encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	networthPb "github.com/epifi/gamma/api/insights/networth"
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	insightsDeeplinkPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	qrScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay/qr"
	"github.com/epifi/gamma/frontend/configsvc"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	homePkg "github.com/epifi/gamma/frontend/pkg/home"
	gammanames "github.com/epifi/gamma/pkg/names"

	"github.com/google/uuid"
	"github.com/mohae/deepcopy"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	questtypes "github.com/epifi/be-common/api/quest/types"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	colorsPkg "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/frontend/pkg/upi"

	"github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	paySavingsEnums "github.com/epifi/gamma/api/accounts/balance/enums"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/analyser/enums"
	"github.com/epifi/gamma/api/analyser/txnaggregates"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/categorizer"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/employment"
	ffBePb "github.com/epifi/gamma/api/firefly"
	ffBeEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	"github.com/epifi/gamma/api/frontend/header"
	feHomePb "github.com/epifi/gamma/api/frontend/home"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
	ffScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly"
	onbScreenOpts "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	homeTypesPb "github.com/epifi/gamma/api/typesv2/home"
	"github.com/epifi/gamma/api/typesv2/pay/user_identifier"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/walkthrough"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	homeConstants "github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>"
	feUpiPkg "github.com/epifi/gamma/frontend/pkg/upi"
	feTieringErrors "github.com/epifi/gamma/frontend/tiering/errors"
	"github.com/epifi/gamma/frontend/tiering/helper"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	onbPkg "github.com/epifi/gamma/pkg/onboarding"
	payPkg "github.com/epifi/gamma/pkg/pay"
	savingsPkg "github.com/epifi/gamma/pkg/savings"
	pkgUser "github.com/epifi/gamma/pkg/user"
)

// turning of cool off to force display walkthrough for loans
const (
	walkthroughCoolOffPeriodInDays = 30
)

var (
	mapOfFeToBEBalanceUpdateEnum = map[feHomePb.ForceBalanceUpdate]savingsPb.GetAccountBalanceWithSummaryRequest_ForceBalanceUpdate{
		feHomePb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_UNSPECIFIED: savingsPb.GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_UNSPECIFIED,
		feHomePb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_NEEDED:      savingsPb.GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_NEEDED,
		feHomePb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_NOT_NEEDED:  savingsPb.GetAccountBalanceWithSummaryRequest_FORCE_BALANCE_UPDATE_NOT_NEEDED,
	}
	verticalScreenElementPositionToSpacerMapping = map[int]components.Spacing{
		1:  components.Spacing_SPACING_L,
		2:  components.Spacing_SPACING_XL,
		3:  components.Spacing_SPACING_XL,
		4:  components.Spacing_SPACING_XL,
		5:  components.Spacing_SPACING_XL,
		6:  components.Spacing_SPACING_XXL,
		7:  components.Spacing_SPACING_XXL,
		8:  components.Spacing_SPACING_XXL,
		9:  components.Spacing_SPACING_XXL,
		10: components.Spacing_SPACING_XXL,
		11: components.Spacing_SPACING_XXL,
		12: components.Spacing_SPACING_XXL,
		13: components.Spacing_SPACING_UNSPECIFIED,
	}
	pdCategoryForHomeProfileLoanNotch = []lendabilityPb.PDCategory{
		lendabilityPb.PDCategory_PD_CATEGORY_UNSPECIFIED,
		lendabilityPb.PDCategory_PD_CATEGORY_LOW,
		lendabilityPb.PDCategory_PD_CATEGORY_MEDIUM,
	}
)

// HomeLayout api return the layout for home page
func (s *Service) HomeLayout(ctx context.Context, req *feHomePb.HomeLayoutRequest) (*feHomePb.HomeLayoutResponse, error) {
	var (
		res                                    = &feHomePb.HomeLayoutResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()}}
		iconIdMapping                          = map[string]*feHomePb.Icon{}
		homeConf                               = deepcopy.Copy(s.conf.HomeRevampParams).(*config.HomeRevampParams)
		actorId                                = req.GetReq().GetAuth().GetActorId()
		isFeatureHomeDesignEnhancementsEnabled = featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            s.evaluator,
				OnboardingClient:     s.onboardingClient,
				QuestSdkClient:       s.questSdkClient,
				UserAttributeFetcher: s.userAttributeFetcher,
				NetWorthClient:       s.networthClient,
			},
		})
	)
	var bottomWidgetList []feHomePb.HomeWidget_WidgetType
	if isLiteUser, featStatus, errLite := isFiLiteUser(ctx, s.onboardingClient, req.GetReq().GetAuth().GetActorId()); errLite == nil && isLiteUser {
		homeConf = deepcopy.Copy(s.conf.LiteHomeRevampParams).(*config.HomeRevampParams)
		if featStatus == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE || featStatus == onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS {
			homeConf.ExploreSections = s.conf.ExploreSectionsForCC
		}
		// Static widget list for fi lite users. Experiment and layout segment overrides will not work for Fi Lite Users.
		bottomWidgetList = getLayoutConfigurationFromConfig(homeConf)
	} else {
		bottomWidgetList = s.computeHomeLayoutConfigurationAndGetBottomWidgetList(ctx, homeConf, actorId)
	}
	homeIcons := homeConf.AllHomeIcons

	// fetch all icons from config
	for iconId, iconValue := range homeIcons {
		t, err := s.getIconResponseFromConfig(ctx, iconId, iconValue, actorId)
		if err != nil {
			logger.Error(ctx, "error in fetching Icon response from config", zap.Error(err))
			res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
			return res, nil
		}
		iconIdMapping[iconId] = t
	}

	// Add top widgets
	for _, w := range homeConf.HomeWidgetParams.TopWidgetList {
		// Include the widget only if it passes the criteria
		widgetType := feHomePb.HomeWidget_WidgetType(feHomePb.HomeWidget_WidgetType_value[w])

		res.HomeWidgetsTopSection = append(res.HomeWidgetsTopSection, UpdateHomeWidgetParamsByType(ctx, homeConf, widgetType, homeConf.HomeWidgetParams, iconIdMapping, homeConf.RecentUpcomingWidgetParams, isFeatureHomeDesignEnhancementsEnabled))
	}

	var previousWidgetType feHomePb.HomeWidget_WidgetType
	// Add bottom Widgets
	for _, widgetType := range bottomWidgetList {
		var nextWidget = UpdateHomeWidgetParamsByType(ctx, homeConf, widgetType, homeConf.HomeWidgetParams, iconIdMapping, homeConf.RecentUpcomingWidgetParams, isFeatureHomeDesignEnhancementsEnabled)
		nextWidget.TopSpacer = s.GetSpacerComponent(widgetType, previousWidgetType, isFeatureHomeDesignEnhancementsEnabled)
		previousWidgetType = nextWidget.WidgetType
		res.HomeWidgetsBottomSection = append(res.HomeWidgetsBottomSection, nextWidget)
	}

	return res, nil
}

// Method to construct top, middle and bottom widgets of home layout v2 given the best computed config for the user
func (s *Service) constructHomeLayout(ctx context.Context, actorId string, layout *config.SlotIdToScreenElementIdMap, layoutV2Params *genconf.HomeLayoutV2Params) (*feHomePb.HomeLayoutV2, error) {
	homeConf := deepcopy.Copy(s.conf.HomeRevampParams).(*config.HomeRevampParams)
	homeLayoutConfigV2Params := homeConf.HomeLayoutConfigurationV2Params

	topNavBarSectionLeftScreenElements, topNavBarSectionRightScreenElements, err := s.getTopNavBarSectionScreenElementList(homeLayoutConfigV2Params, layout)
	if err != nil {
		return nil, fmt.Errorf("error in getting top nav bar section screen element list, err: %w", err)
	}
	dashboardSectionScreenElements, err := s.getDashboardSectionScreenElementList(homeLayoutConfigV2Params, layout)
	if err != nil {
		return nil, fmt.Errorf("error in getting dashboard section screen element list, err: %w", err)
	}
	middleSectionScreenElements, err := s.getMiddleSectionScreenElementList(homeLayoutConfigV2Params, layout)
	if err != nil {
		return nil, fmt.Errorf("error in getting middle section screen element list, err: %w", err)
	}
	bottomBarSectionScreenElements, bottomBarSectionStickyElements, err := s.getBottomBarSectionScreenElementList(homeLayoutConfigV2Params, layout)
	if err != nil {
		return nil, fmt.Errorf("error in getting bottom bar section screen element list, err: %w", err)
	}

	var (
		res                                    = &feHomePb.HomeLayoutV2{}
		isFeatureHomeDesignEnhancementsEnabled = featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            s.evaluator,
				OnboardingClient:     s.onboardingClient,
				QuestSdkClient:       s.questSdkClient,
				UserAttributeFetcher: s.userAttributeFetcher,
				NetWorthClient:       s.networthClient,
			},
		})
	)
	// Add top widgets
	s.constructHomeWidgetsForTopSectionInResponse(ctx, actorId, res, homeLayoutConfigV2Params, topNavBarSectionLeftScreenElements, topNavBarSectionRightScreenElements, isFeatureHomeDesignEnhancementsEnabled)
	// Add Middle Widgets
	s.constructHomeWidgetsForMiddleSectionInResponse(ctx, actorId, res, homeConf, dashboardSectionScreenElements, middleSectionScreenElements, isFeatureHomeDesignEnhancementsEnabled)
	// Add Bottom Widget
	s.constructHomeWidgetsForBottomSectionInResponse(ctx, actorId, res, bottomBarSectionScreenElements, bottomBarSectionStickyElements, layoutV2Params)

	res.Id = layout.LayoutId
	return res, nil
}

// Fetch all icons from config
func (s *Service) getIconsFromConfig(ctx context.Context, homeConf *config.HomeRevampParams, actorId string) (map[string]*feHomePb.Icon, error) {
	var iconIdMapping map[string]*feHomePb.Icon
	homeIcons := homeConf.AllHomeIcons
	// fetch all icons from config
	for iconId, iconValue := range homeIcons {
		iconResponse, err := s.getIconResponseFromConfig(ctx, iconId, iconValue, actorId)
		if err != nil {
			logger.Error(ctx, "error in fetching Icon response from config", zap.Error(err))
			return nil, err
		}
		iconIdMapping[iconId] = iconResponse
	}
	return iconIdMapping, nil
}

// Method to construct the top section in home widgets of HomeLayoutV2 response
func (s *Service) constructHomeWidgetsForTopSectionInResponse(ctx context.Context, actorId string, res *feHomePb.HomeLayoutV2, homeLayoutConfigurationV2Params *config.HomeLayoutConfigurationV2Params, topNavBarSectionLeftScreenElements []*config.ScreenElementProperties, topNavBarSectionRightScreenElements []*config.ScreenElementProperties, isFeatureHomeDesignEnhancementsEnabled bool) {
	// Add maintenance bar and top navigation bar
	res.HomeWidgetsTopSection = append(res.HomeWidgetsTopSection,
		&feHomePb.HomeWidget{
			Id:         homeConstants.MaintenanceStickyTopComponentId.String(),
			WidgetType: feHomePb.HomeWidget_WIDGET_TYPE_MAINTENANCE,
		},
		s.UpdateHomeWidgetParamsByTypeV2ForTopNavbar(ctx, actorId, homeLayoutConfigurationV2Params, topNavBarSectionLeftScreenElements, topNavBarSectionRightScreenElements, isFeatureHomeDesignEnhancementsEnabled))
}

// Method to construct the middle section in home widgets of HomeLayoutV2 response
func (s *Service) constructHomeWidgetsForMiddleSectionInResponse(ctx context.Context, actorId string, res *feHomePb.HomeLayoutV2, homeConf *config.HomeRevampParams, dashboardSectionWidgetList []*config.ScreenElementProperties, middleSectionWidgetList []*config.ScreenElementProperties, isFeatureHomeDesignEnhancementsEnabled bool) {
	// Add critical notification and dashboard section
	res.HomeWidgetsMiddleSection = append(res.HomeWidgetsMiddleSection,
		&feHomePb.HomeWidget{
			Id:         homeConstants.CriticalNotificationScrollableComponentId.String(),
			WidgetType: feHomePb.HomeWidget_WIDGET_TYPE_CRITICAL_NOTIFICATION,
		})

	dashboardSection := s.UpdateHomeWidgetParamsByTypeV2ForDashboardSection(ctx, actorId, dashboardSectionWidgetList)
	if dashboardSection != nil {
		res.HomeWidgetsMiddleSection = append(res.HomeWidgetsMiddleSection, dashboardSection)
	}

	// Add middle widgets
	for idx, screenElement := range middleSectionWidgetList {
		var (
			widgetType = feHomePb.HomeWidget_WidgetType(feHomePb.HomeWidget_WidgetType_value[screenElement.WidgetType])
			nextWidget = s.UpdateHomeWidgetParamsByTypeV2ForMiddleSection(ctx, homeConf, widgetType, homeConf.RecentUpcomingWidgetParams, screenElement, isFeatureHomeDesignEnhancementsEnabled)
		)
		nextWidget.TopSpacer = s.GetTopSpacerComponent(idx+1, widgetType, isFeatureHomeDesignEnhancementsEnabled)
		nextWidget.BottomSpacer = s.GetBottomSpacerComponent(widgetType, isFeatureHomeDesignEnhancementsEnabled)
		res.HomeWidgetsMiddleSection = append(res.HomeWidgetsMiddleSection, nextWidget)
	}
}

// Method to create icons from screen element properties
func (s *Service) getIconFromScreenElementProperties(ctx context.Context, actorId string, screenElement *config.ScreenElementProperties, isFeatureHomeDesignEnhancementsEnabled bool) *feHomePb.Icon {
	// TODO: add icon version check and properties
	icon := &feHomePb.Icon{
		Id:                   screenElement.Id,
		IconImage:            &commontypes.Image{ImageUrl: screenElement.IconWithVersionConstraints.ImageUrl},
		IconImageOnSelection: &commontypes.Image{ImageUrl: screenElement.IconWithVersionConstraints.OnclickImageUrl},
		VisualElement:        commontypes.GetVisualElementImageFromUrl(screenElement.IconWithVersionConstraints.ImageUrl),
		Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: screenElement.IconWithVersionConstraints.Title},
			FontColor: screenElement.IconWithVersionConstraints.FontColour, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[screenElement.IconWithVersionConstraints.FontStyle])}},
		TitleOnSelection: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: screenElement.IconWithVersionConstraints.TitleOnSelection},
			FontColor: screenElement.IconWithVersionConstraints.FontColourOnSelection, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[screenElement.IconWithVersionConstraints.FontStyleOnSelection])}},
		IconType:   homeTypesPb.IconType(homeTypesPb.IconType_value[screenElement.IconWithVersionConstraints.IconType]),
		ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
		Action: &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen(deeplink.Screen_value[screenElement.IconActionInfoProperties.Action.Deeplink.Screen]),
		}},
		BgColour:    &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: screenElement.WidgetBg.BackgroundColour.BlockColour.BlockColour}},
		BgColourV2:  widget.GetBlockBackgroundColour(screenElement.WidgetBg.BackgroundColour.BlockColour.BlockColour),
		BorderColor: feHomePb.GetHomeIconBorderColor(),
	}
	if screenElement.IconWithVersionConstraints.LottieUrl != "" {
		icon.VisualElement = commontypes.GetVisualElementLottieFromUrl(screenElement.IconWithVersionConstraints.LottieUrl).WithRepeatCount(1)
	}
	if isFeatureHomeDesignEnhancementsEnabled {
		icon.VisualElement = commontypes.GetVisualElementImageFromUrl(screenElement.IconWithVersionConstraints.ImageUrl)
		icon.BgColour = nil
		icon.BgColourV2 = nil
	}
	if screenElement.IconWithVersionConstraints.VisualElementWidth != 0 {
		icon.VisualElement = icon.VisualElement.WithProperties(&commontypes.VisualElementProperties{Width: screenElement.IconWithVersionConstraints.VisualElementWidth, Height: screenElement.IconWithVersionConstraints.VisualElementHeight})
	}

	switch screenElement.Id {
	case "qr_code-1", "qr_code-2", "qr_code-3":
		qrDeeplink := s.getQrDeeplink(ctx, actorId)
		icon.BgColourV2 = widget.GetLinearGradientBackgroundColour(45, []*widget.ColorStop{{Color: "#006D5B", StopPercentage: 0}, {Color: "#00B899", StopPercentage: 100}})
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: qrDeeplink}
	case "card-1":
		ctxWithTimeout, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
		defer cancel()
		_, userAlreadyHasCard, err := s.checkCreditCardEligibility(ctxWithTimeout, actorId)
		// in case of error we will fall back to debit card
		if err != nil {
			logger.Error(ctx, "error in checking cc eligibility", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		}
		if s.genconf.CreditCard().ShowCreditCardTabByDefaultFromCardTab() && userAlreadyHasCard {
			icon.Action = &feHomePb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
				},
			}
			// TODO(priyansh) : Turn this on once we start cc onboarding again. Redirecting eligible user to debit
			//  card tab till then
			/*
				if isUserEligibleForCC {
					icon.VisualElement = commontypes.GetVisualElementLottieFromUrl(homeConstants.CCEligibleLottieUrl).WithRepeatCount(1)
					if screenElement.IconWithVersionConstraints.VisualElementWidth != 0 {
						icon.VisualElement = icon.VisualElement.WithProperties(&commontypes.VisualElementProperties{Width: screenElement.IconWithVersionConstraints.VisualElementWidth, Height: screenElement.IconWithVersionConstraints.VisualElementHeight})
					}
				}
			*/
		}
		if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.genconf.CreditCard().EnableCardTabsScreen()) {
			selectedTab := ffScreenTypes.CardTabScreenOptions_DEBIT_CARD
			if userAlreadyHasCard {
				selectedTab = ffScreenTypes.CardTabScreenOptions_CREDIT_CARD
			}
			screenOptionsV2 := &ffScreenTypes.CardTabScreenOptions{
				Header:      nil,
				SelectedTab: selectedTab,
				TabToDeeplink: map[string]*deeplink.Deeplink{
					"CREDIT_CARD": {
						Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
					},
					"DEBIT_CARD": {
						Screen: s.getDebitCardScreenWithFeatureCheck(ctx, actorId),
					},
				},
			}
			icon.Action = &feHomePb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen:          deeplink.Screen_CARD_TABS_SCREEN,
					ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(screenOptionsV2),
				},
			}
		}
	case "analyser-1":
		if isDevicePlatformVersionValidForAnalyserScreenDeeplink(ctx) {
			icon.Action = &feHomePb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_ANALYSER_SCREEN,
				},
			}
		}
	case "savings-account-benefits":
		icon.Action = &feHomePb.Icon_Deeplink{
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_FEATURE_BENEFITS,
				ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&onbScreenOpts.FeatureBenefitsScreenOptions{
					FeatureOnboardingEntryPoint: onbScreenOpts.FeatureOnboardingEntryPoint_FEATURE_ONBOARDING_ENTRY_POINT_BOTTOM_NAV_BAR.String(),
					ToFetchScreenOptionsData:    commontypes.BooleanEnum_TRUE,
				}),
			},
		}
	case "networth-2":
		if isDevicePlatformVersionValidForScreenElementProperties(ctx, screenElement) {
			icon.Action = &feHomePb.Icon_Deeplink{
				Deeplink: insightsDeeplinkPb.GetNetworthHubScreenDeeplinkWithoutError(),
			}
		}
	}
	return icon
}

// Method to update home widget params by type for top nav bar, for home layout v2
func (s *Service) UpdateHomeWidgetParamsByTypeV2ForTopNavbar(ctx context.Context, actorId string, homeLayoutConfigurationV2Params *config.HomeLayoutConfigurationV2Params,
	topNavLeftScreenElements []*config.ScreenElementProperties, topNavRightScreenElements []*config.ScreenElementProperties, isFeatureHomeDesignEnhancementsEnabled bool) *feHomePb.HomeWidget {
	res := &feHomePb.HomeWidget{WidgetType: feHomePb.HomeWidget_WIDGET_TYPE_TOP_NAV_BAR}
	leftIcons := []*feHomePb.Icon{}
	for _, screenElement := range topNavLeftScreenElements {
		leftIcons = append(leftIcons, s.getIconFromScreenElementProperties(ctx, actorId, screenElement, isFeatureHomeDesignEnhancementsEnabled))
	}
	rightIcons := []*feHomePb.Icon{}
	for _, screenElement := range topNavRightScreenElements {
		rightIcons = append(rightIcons, s.getIconFromScreenElementProperties(ctx, actorId, screenElement, isFeatureHomeDesignEnhancementsEnabled))
	}
	res.Widget = &feHomePb.HomeWidget_TopBarWidget{TopBarWidget: &feHomePb.TopNavBarWidget{
		RightIcons: rightIcons,
		LeftIcons:  leftIcons,
	}}
	res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#282828"}}
	dashboardVersionV2ReleaseConfig := s.genconf.HomeRevampParams().DashboardVersionV2ReleaseConfig()
	if isDevicePlatformVersionValidForDashboardV2(ctx, dashboardVersionV2ReleaseConfig) {
		res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#18191B"}}
	}
	res.Id = s.genconf.HomeRevampParams().HomeLayoutV2Params().WidgetToIdMapping().TopNavBarSection
	return res
}

func (s *Service) getQrDeeplink(ctx context.Context, actorId string) *deeplink.Deeplink {
	var (
		qrScreenOptions *qrScreenOptionsPb.QRScanScreenOptions
		qrScreen        = deeplink.Screen_PAY_QR_SCREEN
	)
	isQrEnhancementsEnabled, err := s.evaluator.Evaluate(ctx, &release.CommonConstraintData{ActorId: actorId, Feature: types.Feature_FEATURE_QR_SCAN_ENHANCEMENTS})
	if err != nil {
		logger.Error(ctx, "error in evaluating qr enhancements", zap.Error(err))
	}
	if isQrEnhancementsEnabled {
		qrScreenOptions = &qrScreenOptionsPb.QRScanScreenOptions{
			PayBottomSheet: &qrScreenOptionsPb.QRScanScreenOptions_PayBottomSheet{
				IsSearchBarEnabled:          true,
				IsRecentTransactionsEnabled: true,
			},
		}
	}

	if feUpiPkg.IsMlKitQrEnabled(ctx, actorId, s.evaluator) {
		qrScreen = deeplink.Screen_PAY_ML_KIT_QR_SCREEN
	}

	return &deeplink.Deeplink{
		Screen:          qrScreen,
		ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(qrScreenOptions),
	}
}

// Method to update home widget params by type for dashboard section, for home layout v2
func (s *Service) UpdateHomeWidgetParamsByTypeV2ForDashboardSection(ctx context.Context, actorId string,
	dashboardScreenElements []*config.ScreenElementProperties) *feHomePb.HomeWidget {
	views := []*feHomePb.DashboardWidget_DashboardView{}
	res := &feHomePb.HomeWidget{WidgetType: feHomePb.HomeWidget_WIDGET_TYPE_DASHBOARD}
	backgroundColour := &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{RadialGradient: &ui.RadialGradient{
		Center: &ui.CenterCoordinates{
			CenterX: 1,
			CenterY: 0,
		},
		OuterRadius: 82,
		Colours:     []string{"#484848", "#353434"},
	}}}
	dashboardVersionV2ReleaseConfig := s.genconf.HomeRevampParams().DashboardVersionV2ReleaseConfig()

	var dashboardVersionResponse feHomePb.DashboardVersion
	if isDevicePlatformVersionValidForDashboardV2(ctx, dashboardVersionV2ReleaseConfig) {
		dashboardVersionResponse = feHomePb.DashboardVersion_DASHBOARD_VERSION_V2
	}

	if dashboardVersionResponse == feHomePb.DashboardVersion_DASHBOARD_VERSION_V2 {
		backgroundColour = &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{RadialGradient: &ui.RadialGradient{
			Center: &ui.CenterCoordinates{
				CenterX: 1,
				CenterY: 0,
			},
			OuterRadius: 82,
			Colours:     []string{"#3E4043", "#242527"},
		}}}
	}
	for _, screenElement := range dashboardScreenElements {
		homeDashboardView := screenElement.HomeWidgetDashboardParams.HomeDashboardView
		dashViewPb := feHomePb.DashboardWidget_DashboardView_DashboardViewType(feHomePb.DashboardWidget_DashboardView_DashboardViewType_value[homeDashboardView.Name])
		if dashViewPb == feHomePb.DashboardWidget_DashboardView_DASHBOARD_VIEW_LOANS && !LoanDashboardViewVersionCheck(ctx) {
			continue
		}
		viewPb := &feHomePb.DashboardWidget_DashboardView{
			DashboardViewType:  dashViewPb,
			DashboardWaterMark: &commontypes.Image{ImageUrl: homeDashboardView.WaterMarkUrl},
			OverridingPriority: feHomePb.DashboardWidget_OverridingDashboardPriority(feHomePb.DashboardWidget_OverridingDashboardPriority_value[homeDashboardView.Priority]),
			Background:         backgroundColour,
			Id:                 screenElement.Id,
		}
		views = append(views, viewPb)
	}
	zeroStateDashboardCardVariantString := s.genconf.HomeRevampParams().HomeLayoutV2Params().ZeroStateDashboardCardVariant(ctx)
	zeroStateDashboardCardVariant := feHomePb.ZeroStateDashboardCardVariant(feHomePb.ZeroStateDashboardCardVariant_value[zeroStateDashboardCardVariantString])

	res.Widget = &feHomePb.HomeWidget_DashboardWidget{
		DashboardWidget: &feHomePb.DashboardWidget{
			DashboardViews:            views,
			DashboardVersion:          dashboardVersionResponse,
			ZeroStateDashboardVariant: zeroStateDashboardCardVariant,
			UnderlayDisplayComponent:  s.getUnderlayDisplayComponentForActor(ctx, actorId),
		},
	}
	res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#282828"}}
	if isDevicePlatformVersionValidForDashboardV2(ctx, dashboardVersionV2ReleaseConfig) && dashboardVersionResponse == feHomePb.DashboardVersion_DASHBOARD_VERSION_V2 {
		res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#18191B"}}
	}
	res.Id = s.genconf.HomeRevampParams().HomeLayoutV2Params().WidgetToIdMapping().DashboardSection

	// if no views are present in the dashboard section, then return empty dashboard section
	// NOTE: this case is handled to show wealth-builder net worth dashboard in place of dashboard sections.
	// figma - https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=700-20711&t=11KDud8jTRttDYUc-4
	if len(res.GetDashboardWidget().GetDashboardViews()) == 0 {
		return nil
	}
	return res
}

func (s *Service) getUnderlayDisplayComponentForActor(ctx context.Context, actorId string) *feHomePb.UnderlayDisplayComponent {
	// flag to enable/disable underlay upi component
	if !s.genconf.HomeFeatureQuestFlags().EnableUnderlayUpiComponent(ctx) {
		return nil
	}

	vpaName, _, err := upi.GetVPAForActor(ctx, actorId, s.upiOnboardingClient, s.accountPiClient, s.savClient)
	if err != nil {
		logger.Error(ctx, "error in fetching vpa for actor for showing in home screen", zap.Error(err))
		// return nil in case of error in fetching vpa for actor.
		return nil
	}

	if vpaName == "" {
		// return nil in case of empty vpa for actor.
		return nil
	}

	return &feHomePb.UnderlayDisplayComponent{
		Key: &commontypes.Text{
			FontColor: "#6A6D70",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "UPI ID:",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
			},
		},
		Value: &commontypes.Text{
			FontColor: "#F6F9FD",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: vpaName,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
			},
		},
		IsCopyable: true,
	}
}

// Method to update home widget params by type for middle section, for home layout v2
func (s *Service) UpdateHomeWidgetParamsByTypeV2ForMiddleSection(ctx context.Context, homeConf *config.HomeRevampParams,
	widgetType feHomePb.HomeWidget_WidgetType, recentWidgetParams *config.RecentUpcomingWidgetParams,
	screenElement *config.ScreenElementProperties, isFeatureHomeDesignEnhancementsEnabled bool) *feHomePb.HomeWidget {

	res := &feHomePb.HomeWidget{WidgetType: widgetType}

	switch widgetType {
	case feHomePb.HomeWidget_WIDGET_TYPE_MAINTENANCE, feHomePb.HomeWidget_WIDGET_TYPE_CRITICAL_NOTIFICATION:
	case feHomePb.HomeWidget_WIDGET_TYPE_SEARCH_BAR, feHomePb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER, feHomePb.HomeWidget_WIDGET_TYPE_SUGGESTED_FOR_YOU,
		feHomePb.HomeWidget_WIDGET_TYPE_SHORTCUTS, feHomePb.HomeWidget_WIDGET_TYPE_ANALYSER_CARDS, feHomePb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER_2,
		feHomePb.HomeWidget_WIDGET_TYPE_PRIMARY_FEATURE, feHomePb.HomeWidget_WIDGET_TYPE_SECONDARY_FEATURE:
		res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: screenElement.WidgetBg.BackgroundColour.BlockColour.BlockColour}}
	case feHomePb.HomeWidget_WIDGET_TYPE_REWARDS, feHomePb.HomeWidget_WIDGET_TYPE_CATALOG_OFFERS, feHomePb.HomeWidget_WIDGET_TYPE_CARD_OFFERS,
		feHomePb.HomeWidget_WIDGET_TYPE_INVEST_HOME_ELEMENT, feHomePb.HomeWidget_WIDGET_TYPE_UPI_CONNECT, feHomePb.HomeWidget_WIDGET_TYPE_JOURNEYS, feHomePb.HomeWidget_WIDGET_TYPE_WEALTH_ANALYSER,
		feHomePb.HomeWidget_WIDGET_TYPE_DC_INTERNATIONAL_WIDGET, feHomePb.HomeWidget_WIDGET_TYPE_ACTIVATION_WIDGET:
		res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: screenElement.WidgetBg.BackgroundColour.BlockColour.BlockColour}}
		res.WidgetBgSeparator = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: screenElement.WidgetBg.BackgroundColour.BlockColour.BlockColour}}
	case feHomePb.HomeWidget_WIDGET_TYPE_RECENT_UPCOMING_ACTIVITIES:
		res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: screenElement.WidgetBg.BackgroundColour.BlockColour.BlockColour}}
		res.WidgetBgSeparator = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: screenElement.WidgetBg.BackgroundColour.BlockColour.BlockColour}}
		filterType := ui.Filter_FilterType(ui.Filter_FilterType_value[recentWidgetParams.FilterViewType])
		if isFeatureHomeDesignEnhancementsEnabled {
			filterType = ui.Filter_FILTER_TYPE_CHIPS
		}
		filter, mapping := getHomeRecentUpcomingActivitiesFilter(filterType, []feHomePb.RecentAndUpcomingWidget_ActivityType{feHomePb.RecentAndUpcomingWidget_ACTIVITY_TYPE_RECENT,
			feHomePb.RecentAndUpcomingWidget_ACTIVITY_TYPE_UPCOMING}, feHomePb.RecentAndUpcomingWidget_ACTIVITY_TYPE_RECENT, cfg.IsFeatureEnabledOnPlatform(ctx, homeConf.HomeLayoutUIRevamp))
		res.Widget = &feHomePb.HomeWidget_ActivitiesWidget{ActivitiesWidget: &feHomePb.RecentAndUpcomingWidget{
			Filter:            filter,
			TabToActivityType: mapping,
			Title:             commontypes.GetTextFromStringFontColourFontStyle("Transactions", "#38393B", commontypes.FontStyle_DISPLAY_M),
		}}
	case feHomePb.HomeWidget_WIDGET_TYPE_REFERRAL, feHomePb.HomeWidget_WIDGET_TYPE_TRUST_MARKER,
		feHomePb.HomeWidget_WIDGET_TYPE_SALARY_ACCOUNT:
		res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{
			RadialGradient: &ui.RadialGradient{
				Colours: screenElement.WidgetBg.BackgroundColour.RadialGradient.Colours,
			},
		}}
	case feHomePb.HomeWidget_WIDGET_TYPE_HELP:
		res.WidgetBg = helpSectionByVersionCheck(ctx, homeConf)
	default:
		logger.Error(ctx, "unhandled home widget type", zap.String("widgetType", widgetType.String()))
	}
	if isFeatureHomeDesignEnhancementsEnabled && widgetType != feHomePb.HomeWidget_WIDGET_TYPE_REFERRAL {
		res.WidgetBg = ui.GetBlockColor("#FFFFFF")
	}
	res.Id = screenElement.Id
	return res
}

// Method to construct the bottom section in home widgets of HomeLayoutV2 response
func (s *Service) constructHomeWidgetsForBottomSectionInResponse(ctx context.Context, actorId string, res *feHomePb.HomeLayoutV2,
	bottomSectionWidgetList []*config.ScreenElementProperties, bottomBarSectionStickyElements []*config.ScreenElementProperties, layoutV2Params *genconf.HomeLayoutV2Params) {
	var iconsPb []*feHomePb.Icon
	for _, screenElement := range bottomSectionWidgetList {
		iconsPb = append(iconsPb,
			s.getIconFromScreenElementProperties(ctx, actorId, screenElement, false))
	}

	res.HomeWidgetsBottomSection = &feHomePb.HomeWidget{
		Widget: &feHomePb.HomeWidget_BottomBarWidget{
			BottomBarWidget: &feHomePb.BottomNavBarWidget{
				BottomBarIcons: make([]*feHomePb.Icon, len(iconsPb)),
			},
		},
		WidgetType: feHomePb.HomeWidget_WIDGET_TYPE_BOTTOM_NAV_BAR,
		Id:         s.genconf.HomeRevampParams().HomeLayoutV2Params().WidgetToIdMapping().BottomNavBarSection,
	}

	bottomBarWidget := res.HomeWidgetsBottomSection.GetBottomBarWidget()
	// icon list in bottom bar
	bottomBarIcons := bottomBarWidget.GetBottomBarIcons()
	for idx, icon := range iconsPb {
		bottomBarIcons[idx] = icon
	}
	// Setting default selected icon
	bottomBarWidget.DefaultSelectedIconId = s.getDefaultSelectedIconIdForBottomNavBarWidget(ctx, actorId, layoutV2Params)
	// Setting background color for bottom bar
	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.genconf.HomeRevampParams().BgColorForBottomNavBarReleaseConfig()) {
		bottomBarWidget.BgColor = widget.GetBlockBackgroundColour(s.genconf.HomeRevampParams().HomeLayoutV2Params().BgColorForBottomNavBarWidget(ctx))
	}
	// mapping for sticky icon
	bottomBarWidget.StickyIconMapping = map[string]*feHomePb.Icon{}
	for _, stickyElement := range bottomBarSectionStickyElements {
		bottomBarWidget.StickyIconMapping[stickyElement.IconMappingKey] =
			s.getIconFromScreenElementProperties(ctx, actorId, stickyElement, false)
	}

	bottomBarWidget.OccurrencesOfScanPayText = 25
}

// Method to compute the best layout based on segment or from experiment in quest, and return it.
func (s *Service) computeBestLayoutForUser(ctx context.Context, actorId string, layoutV2Params *genconf.HomeLayoutV2Params) (*config.SlotIdToScreenElementIdMap, error) {
	if simulatorLayout := s.getSimulatorLayoutIfApplicable(ctx, actorId); simulatorLayout != nil {
		return simulatorLayout, nil
	}

	bestLayout := layoutV2Params.SlotIdToScreenElementIdMap()

	dynamicLayout, err := s.layoutEngine.EvaluateLayout(ctx, actorId, layoutV2Params)
	switch {
	case err != nil:
		logger.Error(ctx, "error while evaluating dynamic layout", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, fmt.Errorf("error while evaluating dynamic layout, err: %w", err)
	case dynamicLayout != nil:
		logger.Debug(ctx, "evaluated dynamic layout", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("slotIdToScreenElementIdMap", dynamicLayout))
		bestLayout = dynamicLayout
	}

	// set layout based on user segment, if any
	layoutV2FromSegment := s.getLayoutV2FromSegment(ctx, actorId, layoutV2Params)
	if layoutV2FromSegment != nil {
		bestLayout = layoutV2FromSegment
	}

	// override with layout from experiment, if any
	experimentsForLayoutV2 := layoutV2Params.GetExperimentsFromQuest(ctx, "LayoutV2Json")

	// Note: This is for quick fix for enabling experiments to run for home layout.
	// Issue: Quest uses map to store variable to experiment value in context which will be evaluated once in context cycle.
	// So in home layout logic, it first calls for each experiment and then gets the quest evaluated value,
	// but this makes the first experiment value to be stored in context & takes this value everytime for other experiments in context cycle.
	layoutJsonFromExperiment := layoutV2Params.LayoutV2Json(ctx)

	if s.isLayoutV2QuestExperimentEnabledForUser(ctx, actorId, experimentsForLayoutV2) {
		layoutFromExperiment, err := s.getSlotIdToScreenElementIdMapFromString(layoutJsonFromExperiment)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("misconfigured layout in experiment for actor: %v", actorId))
		} else {
			logger.Debug(ctx, fmt.Sprintf("layout from experiment loaded for actor: %v", actorId))
			bestLayout = layoutFromExperiment
		}
	}

	return deepcopy.Copy(bestLayout).(*config.SlotIdToScreenElementIdMap), nil
}

// Method to check if any of the given list of layout quest experiments is enabled for the user
func (s *Service) isLayoutV2QuestExperimentEnabledForUser(ctx context.Context, actorId string, experiments *questtypes.ExperimentList) bool {
	if experiments == nil || len(experiments.GetElements()) == 0 {
		return false
	}
	for _, experiment := range experiments.GetElements() {
		if experiment.GetStatus() == questtypes.ExperimentStatus_EXPERIMENT_STATUS_ENABLED {
			variantEvaluated, err := s.questSdkClient.GetVariantEvaluatedForActor(ctx, s.usersClient, experiment.GetId(), actorId)
			if err != nil {
				logger.Error(ctx, "error while fetching variant for actor", zap.String("actorId", actorId), zap.Error(err))
				continue
			}
			if variantEvaluated != nil && !variantEvaluated.IsControlVariant {
				return true
			}
		}
	}
	return false
}

func (s *Service) getSimulatorLayoutIfApplicable(ctx context.Context, actorId string) *config.SlotIdToScreenElementIdMap {
	// don't use simulator in production environment
	if s.conf.Application.Environment == cfg.ProductionEnv {
		return nil
	}

	layoutString, err := s.cacheStorage.Get(ctx, fmt.Sprintf(LayoutSimulatorRedisKeyTemplate, actorId))
	if err != nil {
		// no layout has been set in simulator for actor
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil
		}
		logger.Error(ctx, "error while fetching home layout from simulator", zap.Error(err))
		return nil
	}

	layoutFromSimulator, err := s.getSlotIdToScreenElementIdMapFromString(layoutString)
	if err != nil {
		// log error and return nil since we don't want the flow to fail
		logger.Error(ctx, fmt.Sprintf("misconfigured layout in simulator for actor: %v", actorId))
		return nil
	}

	return layoutFromSimulator
}

func (s *Service) computeHomeLayoutConfigurationAndGetBottomWidgetList(ctx context.Context, homeConf *config.HomeRevampParams, actorId string) []feHomePb.HomeWidget_WidgetType {
	// Evaluate layout from redis, and experiment.
	// If a user is part of both - segments & experiment, we override the layout found from experiment
	bestLayout := s.getBestLayoutForUser(ctx, homeConf, actorId)
	layoutJsonFromExperiment := s.genconf.HomeRevampParams().HomeLayoutParams().LayoutJson(ctx)
	if layoutJsonFromExperiment != "" {
		layoutFromExperiment, err := s.GetLayoutFromString(ctx, layoutJsonFromExperiment)
		if err != nil {
			logger.Debug(ctx, fmt.Sprintf("misconfigured layout in experiment for actor: %v", actorId))
		} else {
			logger.Info(ctx, fmt.Sprintf("layout from experiment loaded for actor: %v", actorId))
			bestLayout = layoutFromExperiment
		}
	}
	bottomWidgetList := getLayoutConfigurationFromConfig(homeConf)
	/* This flow is when layout was obtained from redis configuration or experiment. It is possible in the following cases
	1. When the user was not part of any segmentation or experimentation
	2. When there was an experiment running and no error faced fetching data from experiments
	2. When there was no error faced fetching data from redis
	*/
	if bestLayout != nil {
		if bestLayout.BottomWidget != nil && len(bestLayout.BottomWidget) != 0 {
			logger.Debug(ctx, "using layout from redis/experiment")
			bottomWidgetList = bestLayout.BottomWidget
		}
		if bestLayout.TopWidgetRightIcons != nil && len(bestLayout.TopWidgetRightIcons) != 0 {
			logger.Debug(ctx, "using right icons from redis/experiment")
			homeConf.HomeWidgetParams.HomeWidgetTopNavBarParams.RightIconIds = bestLayout.TopWidgetRightIcons
		}

		if bestLayout.TopWidgetLeftIcons != nil && len(bestLayout.TopWidgetLeftIcons) != 0 {
			logger.Debug(ctx, "using left icons from redis/experiment")
			homeConf.HomeWidgetParams.HomeWidgetTopNavBarParams.LeftIconIds = bestLayout.TopWidgetLeftIcons
		}
	}
	return bottomWidgetList
}

func getLayoutConfigurationFromConfig(homeConf *config.HomeRevampParams) []feHomePb.HomeWidget_WidgetType {
	bottomWidgetList := homeConf.HomeWidgetParams.BottomWidgetList

	var widgetList []feHomePb.HomeWidget_WidgetType
	for _, widgetString := range bottomWidgetList {
		widgetList = append(widgetList, feHomePb.HomeWidget_WidgetType(feHomePb.HomeWidget_WidgetType_value[widgetString]))
	}
	return widgetList
}

// nolint:dupl
// Method to get list of top nav bar section screen elements present in target layout
func (s *Service) getTopNavBarSectionScreenElementList(homeLayoutConfigurationV2Params *config.HomeLayoutConfigurationV2Params, layout *config.SlotIdToScreenElementIdMap) ([]*config.ScreenElementProperties, []*config.ScreenElementProperties, error) {
	// Get the topNavBarSlotSection for the target layout
	topNavBarSlotSection := layout.TopNavBarSlotSection
	// Get section screen elements config object to retrieve slot list
	sectionSlotsConfig := homeLayoutConfigurationV2Params.SectionSlotsConfig
	// Get list of objects of screen elements, containing properties
	screenElementProperties := homeLayoutConfigurationV2Params.SectionScreenElementsConfig.TopNavBarSection

	var leftScreenElementList []*config.ScreenElementProperties
	leftSlotsList := sectionSlotsConfig.TopNavBarSectionSlotsConfig.LeftSlots
	for _, slot := range leftSlotsList {
		screenElementId, found := topNavBarSlotSection.LeftSlots[slot]
		if !found {
			return nil, nil, fmt.Errorf("screen element id not found for given left slot in topNavBarSlotSection, IdNotFoundInSlot: %s", slot)
		}
		if screenElementId == "" {
			continue
		}
		screenElementProperty, found := screenElementProperties[screenElementId]
		if !found {
			return nil, nil, fmt.Errorf("screen element property not found for elementId %s in screenElementProperties map for topNavBarSlotSection.LeftSlots", screenElementId)
		}
		leftScreenElementList = append(leftScreenElementList, screenElementProperty)
	}

	var rightScreenElementList []*config.ScreenElementProperties
	rightSlotsList := sectionSlotsConfig.TopNavBarSectionSlotsConfig.RightSlots
	for _, slot := range rightSlotsList {
		screenElementId, found := topNavBarSlotSection.RightSlots[slot]
		if !found {
			return nil, nil, fmt.Errorf("screen element id not found for given right slot in topNavBarSlotSection.LeftSlots, IdNotFoundInSlot: %s", slot)
		}
		if screenElementId == "" {
			continue
		}
		screenElementProperty, found := screenElementProperties[screenElementId]
		if !found {
			return nil, nil, fmt.Errorf("screen element property not found for elementId %s in screenElementProperties map for topNavBarSlotSection.RightSlots", screenElementId)
		}
		rightScreenElementList = append(rightScreenElementList, screenElementProperty)
	}
	return leftScreenElementList, rightScreenElementList, nil
}

// nolint:dupl
// Method to get list of dashboard section screen elements present in target layout
func (s *Service) getDashboardSectionScreenElementList(homeLayoutConfigurationV2Params *config.HomeLayoutConfigurationV2Params,
	layout *config.SlotIdToScreenElementIdMap) ([]*config.ScreenElementProperties, error) {
	// Get the DashboardSlotSection for the target layout
	dashboardSlotSection := layout.DashboardSlotSection
	// Get slots list in dashboard section
	dashboardSectionSlotsConfig := homeLayoutConfigurationV2Params.SectionSlotsConfig.DashboardSectionSlotsConfig
	// Get list of objects of screen elements, containing properties
	screenElementProperties := homeLayoutConfigurationV2Params.SectionScreenElementsConfig.DashboardSection

	var screenElementList []*config.ScreenElementProperties
	for _, slot := range dashboardSectionSlotsConfig {
		screenElementId, found := dashboardSlotSection[slot]
		if !found {
			return nil, fmt.Errorf("screen element id not found for given slot in dashboardSlotSection, IdNotFoundInSlot: %s", slot)
		}
		if screenElementId == "" {
			continue
		}
		screenElementProperty, found := screenElementProperties[screenElementId]
		if !found {
			return nil, fmt.Errorf("screen element property not found for elementId %s in screenElementProperties map for dashboardSlotSection", screenElementId)
		}
		screenElementList = append(screenElementList, screenElementProperty)
	}
	return screenElementList, nil
}

// nolint:dupl
// Method to get list of middle section screen elements present in target layout
func (s *Service) getMiddleSectionScreenElementList(homeLayoutConfigurationV2Params *config.HomeLayoutConfigurationV2Params,
	layout *config.SlotIdToScreenElementIdMap) ([]*config.ScreenElementProperties, error) {
	// Get the VerticalSlotSection for the target layout
	verticalSlotSection := layout.VerticalSlotSection
	// Get list of objects of screen elements, containing properties
	screenElementProperties := homeLayoutConfigurationV2Params.SectionScreenElementsConfig.VerticalSection
	// Get slots list in vertical section
	verticalSectionSlotsConfig := homeLayoutConfigurationV2Params.SectionSlotsConfig.VerticalSectionSlotsConfig

	var screenElementList []*config.ScreenElementProperties

	for _, slot := range verticalSectionSlotsConfig {
		screenElementId, found := verticalSlotSection[slot]
		if !found {
			return nil, fmt.Errorf("screen element id not found for given slot in verticalSlotSection, IdNotFoundInSlot: %s", slot)
		}
		if screenElementId == "" {
			continue
		}
		screenElementProperty, found := screenElementProperties[screenElementId]
		if !found {
			return nil, fmt.Errorf("screen element property not found for elementId %s in screenElementProperties map for verticalSlotSection", screenElementId)
		}
		screenElementList = append(screenElementList, screenElementProperty)
	}
	return screenElementList, nil
}

// nolint:dupl
// Method to get list of bottom bar section screen elements present in target layout
func (s *Service) getBottomBarSectionScreenElementList(homeLayoutConfigurationV2Params *config.HomeLayoutConfigurationV2Params,
	layout *config.SlotIdToScreenElementIdMap) ([]*config.ScreenElementProperties, []*config.ScreenElementProperties, error) {
	// Get the BottomNavBarSlotSection for the target layout
	bottomBarSlotSection := layout.BottomNavBarSlotSection
	// Get list of objects of screen elements, containing properties
	screenElementProperties := homeLayoutConfigurationV2Params.SectionScreenElementsConfig.BottomNavBarSection
	// Get slots list in bottom bar section
	bottomBarSectionSlotsConfig := homeLayoutConfigurationV2Params.SectionSlotsConfig.BottomNavBarSectionSlotsConfig

	var screenElementList []*config.ScreenElementProperties

	for _, slot := range bottomBarSectionSlotsConfig {
		screenElementId, found := bottomBarSlotSection[slot]
		if !found {
			return nil, nil, fmt.Errorf("screen element id not found for given slot in bottomBarSlotSection, IdNotFoundInSlot: %s", slot)
		}
		if screenElementId == "" {
			continue
		}
		screenElementProperty, found := screenElementProperties.ScreenElementsMapping[screenElementId]
		if !found {
			return nil, nil, fmt.Errorf("screen element property not found for elementId %s in screenElementProperties map for bottomBarSlotSection", screenElementId)
		}
		screenElementList = append(screenElementList, screenElementProperty)
	}

	// Get the StickyIconSlotSection for the target layout
	stickyIconSlotSection := layout.StickyIconSlotSection
	// get slots list in sticky icon section
	stickyIconSectionSlotsConfig := homeLayoutConfigurationV2Params.SectionSlotsConfig.StickyIconSectionSlotsConfig

	var stickyElementList []*config.ScreenElementProperties

	for _, slot := range stickyIconSectionSlotsConfig {
		screenElementId, found := stickyIconSlotSection[slot]
		if !found {
			return nil, nil, fmt.Errorf("screen element id not found for given slot in stickyIconSlotSection, IdNotFoundInSlot: %s", slot)
		}
		if screenElementId == "" {
			continue
		}
		screenElementProperty, found := screenElementProperties.StickyIconMapping[screenElementId]
		if !found {
			return nil, nil, fmt.Errorf("screen element property not found for elementId %s in screenElementProperties map for stickyIconSlotSection", screenElementId)
		}
		stickyElementList = append(stickyElementList, screenElementProperty)
	}

	return screenElementList, stickyElementList, nil
}

func (s *Service) GetLayoutFromString(ctx context.Context, layoutString string) (*layoutconfiguration.HomeLayout, error) {
	layout := &layoutconfiguration.HomeLayout{}
	err := json2.Unmarshal([]byte(layoutString), layout)
	if err != nil {
		logger.Error(ctx, "failed to unmarshal val")
		return nil, err
	}
	return layout, nil
}

// GetAppBottomNavBar rpc will return the bottom navigation bar icons with ordering and deeplink
func (s *Service) GetAppBottomNavBar(ctx context.Context, req *feHomePb.GetAppBottomNavBarRequest) (*feHomePb.GetAppBottomNavBarResponse, error) {

	var (
		res = &feHomePb.GetAppBottomNavBarResponse{RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		}}
		iconsPb       []*feHomePb.Icon
		iconIdMapping = map[string]*feHomePb.Icon{}
		homeConf      = deepcopy.Copy(s.conf.HomeRevampParams).(*config.HomeRevampParams)
		actorId       = req.GetReq().GetAuth().GetActorId()
	)

	if isLiteUser, _, errLite := isFiLiteUser(ctx, s.onboardingClient, actorId); errLite == nil && isLiteUser {
		homeConf = s.conf.LiteHomeRevampParams
	}

	homeIcons := homeConf.AllHomeIcons
	appBottomNavBarConf := homeConf.AppBottomBarParams

	// Evaluate layout from redis, and experiment.
	// If a user is part of both - segments & experiment, we override the layout found from experiment
	bestLayout := s.getBestLayoutForUser(ctx, homeConf, actorId)
	layoutJsonFromExperiment := s.genconf.HomeRevampParams().HomeLayoutParams().LayoutJson(ctx)
	if layoutJsonFromExperiment != "" {
		layoutFromExperiment, err := s.GetLayoutFromString(ctx, layoutJsonFromExperiment)
		if err != nil {
			logger.Info(ctx, fmt.Sprintf("layout from experiment loaded for actor: %v", actorId))
			bestLayout = layoutFromExperiment
		}
	}

	/* This flow is when layout was obtained from redis configuration or experiment. It is possible in the following cases
	1. When the user was not part of any segmentation or experimentation
	2. When there was an experiment running and no error faced fetching data from experiments
	2. When there was no error faced fetching data from redis
	*/
	if bestLayout != nil {
		if bestLayout.BottomBarIcons != nil && len(bestLayout.BottomBarIcons) != 0 {
			logger.Debug(ctx, "using icons from redis/experiment")
			appBottomNavBarConf.AppBarIcons = bestLayout.BottomBarIcons
		}
	}

	// Load all icons from the config
	for iconId, iconValue := range homeIcons {
		t, err := s.getIconResponseFromConfig(ctx, iconId, iconValue, actorId)
		if err != nil {
			logger.Error(ctx, "error in fetching Icon response from config", zap.Error(err))
			return nil, err
		}
		iconIdMapping[iconId] = t
	}

	for _, iconId := range appBottomNavBarConf.AppBarIcons {
		var iconValue *feHomePb.Icon
		var ok bool
		if iconValue, ok = iconIdMapping[iconId]; !ok {
			logger.Error(ctx, fmt.Sprintf("failed to find icon information in config for expected id, id: '%v'", iconId))
			continue
		}
		// Updating styling for app bottom bar icons
		iconValue.Title = &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: iconValue.GetTitle().GetPlainString()},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			FontColor: GraySteel,
		}
		iconValue.TitleOnSelection = &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: iconValue.GetTitle().GetPlainString()},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			FontColor: Green,
		}
		iconsPb = append(iconsPb, iconValue)
	}
	// icon list in bottom bar
	res.BottomBarIcons = iconsPb
	// Setting default selected icon
	res.DefaultSelectedIconType = homeTypesPb.IconType(homeTypesPb.IconType_value[homeConf.AppBottomBarParams.DefaultIconType])
	// mapping for sticky icon
	res.StickyIconMapping = map[string]*feHomePb.Icon{}
	for i, icon := range homeConf.AppBottomBarParams.StickyIconMapping {
		res.StickyIconMapping[homeTypesPb.IconType(homeTypesPb.IconType_value[strings.ToUpper(i)]).String()] = iconIdMapping[icon]
	}

	res.OccurrencesOfScanPayText = 5
	return res, nil
}

func (s *Service) GetFiAccountSummary(ctx context.Context, req *feHomePb.GetFiAccountSummaryRequest) (*feHomePb.GetFiAccountSummaryResponse, error) {
	getAccountsListResp, getAccountsErr := s.savClient.GetAccountsList(ctx, &savingsPb.GetAccountsListRequest{
		Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
			BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
				ActorIds:                []string{req.GetReq().GetAuth().GetActorId()},
				AccountProductOfferings: savingsPkg.SupportedSavingsAccountProductOfferings,
				PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
			},
		},
	})
	if getAccountsErr != nil {
		return &feHomePb.GetFiAccountSummaryResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("error getting accounts for actor"),
			},
		}, nil
	}
	var fiAccountSummaryList []*feHomePb.GetFiAccountSummaryResponse_FiAccountSummaryTabData
	for _, account := range getAccountsListResp.GetAccounts() {
		fiAccountSummaryTabData, getDataErr := s.getFiAccountSummaryTabData(ctx, account, req.GetForceBalanceUpdate())
		if getDataErr != nil {
			logger.Error(ctx, "error getting account summary tab data from account")
			return &feHomePb.GetFiAccountSummaryResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error getting account summary tab data from account"),
				},
			}, nil
		}
		fiAccountSummaryList = append(fiAccountSummaryList, fiAccountSummaryTabData)
	}

	tabWidgetList, defaultTabId, getTabWidgetListErr := getTabWidgetListAndDefaultTab(ctx, fiAccountSummaryList)
	if getTabWidgetListErr != nil {
		logger.Error(ctx, "error getting tab widget list from account summary list")
		return &feHomePb.GetFiAccountSummaryResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("error getting tab widget list from account summary list"),
			},
		}, nil
	}
	if len(fiAccountSummaryList) == 0 {
		return &feHomePb.GetFiAccountSummaryResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("no account summaries found for actor"),
			},
		}, nil
	}
	var tabWidget *ui.TabWidget
	if len(tabWidgetList) > 1 {
		// Tab widget to be sent only if there are multiple tabs
		tabWidget = &ui.TabWidget{
			Theme: ui.TabWidget_THEME_DARK,
			Size:  ui.TabWidget_SIZE_REGULAR,
			Tabs:  tabWidgetList,
		}
	}
	// Get the AMB Entrypoint Banner for the account summary
	actorId := req.GetReq().GetAuth().GetActorId()
	ambEntrypointBanner := s.getAMBEntrypointBanner(ctx, actorId)

	return &feHomePb.GetFiAccountSummaryResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		AccountIcon:               fiAccountSummaryList[0].GetAccountIcon(),
		Balance:                   fiAccountSummaryList[0].GetBalance(),
		Title:                     fiAccountSummaryList[0].GetTitle(),
		AccountInfo:               fiAccountSummaryList[0].GetAccountInfo(),
		AccountActions:            fiAccountSummaryList[0].GetAccountActions(),
		AccountSummary:            fiAccountSummaryList[0].GetAccountSummary(),
		AdditionalAccountInfo:     fiAccountSummaryList[0].GetAdditionalAccountInfo(),
		LastSyncedMsg:             fiAccountSummaryList[0].GetLastSyncedMsg(),
		PartnerBank:               fiAccountSummaryList[0].GetPartnerBank(),
		BalIcon:                   fiAccountSummaryList[0].GetBalIcon(),
		RefreshCta:                fiAccountSummaryList[0].GetRefreshCta(),
		LastSyncedMsgForceRefresh: fiAccountSummaryList[0].GetLastSyncedMsgForceRefresh(),
		FiAccountSummaryList:      fiAccountSummaryList,
		TabWidget:                 tabWidget,
		SelectedTabId:             defaultTabId,
		AmbEntrypointBanner:       ambEntrypointBanner,
	}, nil
}

// nolint:unparam
func getTabWidgetListAndDefaultTab(ctx context.Context, accountSummaryDataList []*feHomePb.GetFiAccountSummaryResponse_FiAccountSummaryTabData) ([]*ui.TabWidget_Tab, string, error) {
	if len(accountSummaryDataList) == 1 {
		return nil, "", nil
	}
	var tabWidgetList []*ui.TabWidget_Tab
	var defaultDisplayTabId string
	for _, acctSummary := range accountSummaryDataList {
		displayText, getDisplayTextErr := getDisplayTextFromAPO(acctSummary.GetAccountProductOffering())
		if getDisplayTextErr != nil {
			logger.Error(ctx, "error getting display text for apo", zap.Any("AccountProductOffering", acctSummary.GetAccountProductOffering()), zap.Error(getDisplayTextErr))
			continue
		}
		if defaultDisplayTabId == "" {
			defaultDisplayTabId = acctSummary.GetId()
		}
		tabWidgetList = append(tabWidgetList, &ui.TabWidget_Tab{
			Id:   acctSummary.GetId(),
			Text: displayText,
		})
	}
	return tabWidgetList, defaultDisplayTabId, nil
}

func getDisplayTextFromAPO(accProductOffering accountTypesPb.AccountProductOffering) (string, error) {
	switch accProductOffering {
	case accountTypesPb.AccountProductOffering_APO_NRE:
		return "NRE", nil
	case accountTypesPb.AccountProductOffering_APO_NRO:
		return "NRO", nil
	case accountTypesPb.AccountProductOffering_APO_REGULAR, accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED:
		return "Regular", nil
	default:
		return "", errors.New("this apo is not eligible for widget display")
	}
}

// nolint:funlen, gocritic
func (s *Service) getFiAccountSummaryTabData(ctx context.Context, account *savingsPb.Account, forceBalanceUpdateType feHomePb.ForceBalanceUpdate) (*feHomePb.GetFiAccountSummaryResponse_FiAccountSummaryTabData, error) {

	// Following info are presented in Fi Account Summary
	// 1. Account details
	// 2. Current Balance
	// 3. Opening balance
	// 4. Spent in this month
	// 5. Invested this month
	// 6. Money received this month

	var (
		actorId         = account.GetActorId()
		spentAmount     = pkgMoney.ZeroINR().GetPb()
		investedAmount  = pkgMoney.ZeroINR().GetPb()
		moneyIn         = pkgMoney.ZeroINR().GetPb()
		askFiSource     = "home_summary"
		accountInfoList = make([]*feHomePb.FiAccountInfo, 0)
		summaryEntries  = make([]*feHomePb.FiAccountSummary_SummaryEntry, 0)
		explainer       *ui.IconTextComponent
		title           = homeConstants.SummaryPageTitle
	)

	actorRes, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err = epifigrpc.RPCError(actorRes, err); err != nil {
		logger.Error(ctx, "failed to get actor by Id", zap.Error(err))
		return nil, errors.Errorf("failed to get actor by Id : %v", actorId)
	}

	accountId := account.GetId()
	accountType := accounts.Type_SAVINGS
	accountOffering := account.GetSkuInfo().GetAccountProductOffering()
	upiIds := []string{}
	// Fetch upi id from BE
	accountPiReq := &accountPiPb.GetPiByAccountIdRequest{
		AccountId:   accountId,
		AccountType: accountType,
		PiTypes:     []piPb.PaymentInstrumentType{piPb.PaymentInstrumentType_UPI},
	}
	piByAccountId, err := s.accountPiClient.GetPiByAccountId(ctx, accountPiReq)
	if err != nil || !piByAccountId.Status.IsSuccess() {
		logger.Debug(ctx, fmt.Sprintf("error in fetching PIs for account Id %v Response status: %v",
			accountId, piByAccountId.GetStatus()), zap.Error(err))
		return nil, errors.Errorf("error in fetching PIs for account Id %v Response status: %v",
			accountId, piByAccountId.GetStatus())
	}
	for _, pi := range piByAccountId.GetPaymentInstruments() {
		switch pi.GetType() {
		case piPb.PaymentInstrumentType_UPI:
			if !pi.IsMandateVPA() && pi.IsActive() {
				upiIds = append(upiIds, pi.GetUpi().GetVpa())
			}
		}
	}

	balSummaryRes, err := s.savClient.GetAccountBalanceWithSummary(ctx, &savingsPb.GetAccountBalanceWithSummaryRequest{
		Identifier:         &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: accountId},
		ActorId:            actorId,
		TimeRange:          savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
		ForceBalanceUpdate: mapOfFeToBEBalanceUpdateEnum[forceBalanceUpdateType],
	})
	if gRPCErr := epifigrpc.RPCError(balSummaryRes, err); gRPCErr != nil {
		if s.genconf.Flags().EnableDevDeactivatedHandling() &&
			balSummaryRes.GetStatus().GetCode() == uint32(savingsPb.GetAccountBalanceWithSummaryResponse_DEVICE_TEMPORARILY_DEACTIVATED) {
			s.handleDeviceDeactivated(ctx, actorRes.GetActor().GetEntityId())
		}
		logger.Error(ctx, "failed to fetch account balance summary", zap.Error(gRPCErr))
		return nil, fmt.Errorf("failed to fetch account balance summary: %w", gRPCErr)
	}

	firstUpiId := ""
	if len(upiIds) > 0 {
		firstUpiId = upiIds[0]
	}

	// 2. Current Balance:
	currentBalance := balSummaryRes.GetAvailableBalance()

	// 3. Opening Balance:
	openingBalance := balSummaryRes.GetOpeningBalance()

	discrepancyIcon := s.getDiscrepancyIcon(ctx, balSummaryRes.GetAvailableBalance(), balSummaryRes.GetLedgerBalance(), false, nil)

	lastSyncedTime := balSummaryRes.GetBalanceAt().AsTime().In(datetime.IST).Format("02 Jan 03:04 PM")

	partnerBank := &feHomePb.GetFiAccountSummaryResponse_PartnerBank{
		Title:    "BANKING PARTNER",
		BankName: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/FederalbankLogo.png"},
	}

	fromTimestamp := timestamppb.New(datetime.StartOfMonth(time.Now().In(datetime.IST)))
	toTimestamp := timestamppb.New(time.Now().In(datetime.IST))

	txnReq := &txnaggregates.GetTopCategoriesRequest{
		ActorId:            actorId,
		FromTime:           fromTimestamp,
		ToTime:             toTimestamp,
		AccountIds:         &txnaggregates.AccountIds{FiAccountIds: []string{accountId}},
		AggregationMetrics: []*txnaggregates.TxnMetric{{Type: enums.AggregateType_AGGREGATE_TYPE_SUM, Column: txnaggregates.TxnAggregationColumn_TXN_AGGREGATION_COLUMN_AMOUNT}},
		CategoryL0S:        []categorizer.L0{categorizer.L0_L0_SPEND, categorizer.L0_L0_INVESTMENTS, categorizer.L0_L0_INCOME},
	}
	ctxWithTimeout, cancelCtx := context.WithTimeout(ctx, 2*time.Second)
	defer cancelCtx()
	txnRes, err := s.txnAggClient.GetTopCategories(ctxWithTimeout, txnReq)
	if err = epifigrpc.RPCError(txnRes, err); err != nil {
		logger.Error(ctx, "failed to get txn aggregates response", zap.Error(err))
	}

	for _, aggr := range txnRes.GetCategoryAggregates() {
		switch {
		// 4. Spent amount
		case aggr.GetCategoryL0() == categorizer.L0_L0_SPEND:
			spentAmount, err = pkgMoney.Sum(spentAmount, aggr.GetSumAmount().GetBeMoney())
			if err != nil {
				logger.Error(ctx, "failed to add spent amount", zap.Error(err))
				continue
			}
		// 5. Invested amount
		case aggr.GetCategoryL0() == categorizer.L0_L0_INVESTMENTS:
			investedAmount, err = pkgMoney.Sum(investedAmount, aggr.GetSumAmount().GetBeMoney())
			if err != nil {
				logger.Error(ctx, "failed to add invested amount", zap.Error(err))
				continue
			}
		// 6. Money In
		case aggr.GetCategoryAccountingEntry() == payment.AccountingEntryType_CREDIT:
			moneyIn, err = pkgMoney.Sum(moneyIn, aggr.GetSumAmount().GetBeMoney())
			if err != nil {
				logger.Error(ctx, "failed to add credit amount", zap.Error(err))
				continue
			}
		default:
			logger.Info(ctx, fmt.Sprintf("txn not in any category, L0 cat: %v, Cat: %v, AccEntry: %v", aggr.GetCategoryL0(), aggr.GetCategory(), aggr.GetCategoryAccountingEntry()))
		}
	}

	// Title
	switch accountOffering {
	case accountTypesPb.AccountProductOffering_APO_NRE:
		title = homeConstants.NRESummaryPageTitle
	case accountTypesPb.AccountProductOffering_APO_NRO:
		title = homeConstants.NROSummaryPageTitle
	}

	// 1. Account No
	accountInfoList = append(accountInfoList, &feHomePb.FiAccountInfo{
		Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Account No."},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			FontColor: "#646464",
		},
		Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: formatAccountNumber(account.GetAccountNo())},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			FontColor: "#CED2D6",
		},
		Action:      feHomePb.FiAccountInfo_COPY,
		ActionImage: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/CopyImage.png"},
	})
	// 2. IFSC
	accountInfoList = append(accountInfoList, &feHomePb.FiAccountInfo{
		Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "IFSC"},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			FontColor: "#646464",
		},
		Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: account.GetIfscCode()},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			FontColor: "#CED2D6",
		},
		Action:      feHomePb.FiAccountInfo_COPY,
		ActionImage: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/CopyImage.png"},
	})
	// 3. Swift code
	if accountOffering == accountTypesPb.AccountProductOffering_APO_NRE ||
		accountOffering == accountTypesPb.AccountProductOffering_APO_NRO {
		accountInfoList = append(accountInfoList, &feHomePb.FiAccountInfo{
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Swift Code"},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				FontColor: "#646464",
			},
			Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "FDRLINBBIBD"},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				FontColor: "#CED2D6",
			},
			Action:      feHomePb.FiAccountInfo_COPY,
			ActionImage: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/CopyImage.png"},
		})
	}
	// 4.UPI ID
	accountInfoList = append(accountInfoList, &feHomePb.FiAccountInfo{
		Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "UPI ID"},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			FontColor: "#646464",
		},
		Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: firstUpiId},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			FontColor: "#CED2D6",
		},
		Action:      feHomePb.FiAccountInfo_COPY,
		ActionImage: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/CopyImage.png"},
	})
	// 5. MICR
	if accountOffering == accountTypesPb.AccountProductOffering_APO_REGULAR {
		accountInfoList = append(accountInfoList, &feHomePb.FiAccountInfo{
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "MICR"},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				FontColor: "#646464",
			},
			Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "*********"},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				FontColor: "#CED2D6",
			},
			Action:      feHomePb.FiAccountInfo_COPY,
			ActionImage: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/CopyImage.png"},
		})
	}
	// 6. Branch Name
	if accountOffering == accountTypesPb.AccountProductOffering_APO_REGULAR {
		accountInfoList = append(accountInfoList, &feHomePb.FiAccountInfo{
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Branch Name"},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				FontColor: "#646464",
			},
			Value: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Epifi Federal Neo Banking"},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				FontColor: "#CED2D6",
			},
			Action:      feHomePb.FiAccountInfo_COPY,
			ActionImage: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/CopyImage.png"},
		})
	}

	// 1. Opening balance
	summaryEntries = append(summaryEntries, &feHomePb.FiAccountSummary_SummaryEntry{
		Title:        &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Opening Balance"}},
		MoneyPrimary: types.GetFromBeMoney(openingBalance),
		Icon:         &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SavSummaryBalance.png"},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_ASK_FI_LANDING_PAGE,
			ScreenOptions: &deeplink.Deeplink_AskFiLandingPageOptions{
				AskFiLandingPageOptions: &deeplink.AskFiLandingPageOptions{
					Query:  "Account balance",
					Source: askFiSource,
				},
			},
		},
	})

	// 2. Spent this month
	summaryEntries = append(summaryEntries, &feHomePb.FiAccountSummary_SummaryEntry{
		Title:        &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Spent this month"}},
		MoneyPrimary: types.GetFromBeMoney(spentAmount),
		Icon:         &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SavSummarySpentThisMonth.png"},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_ASK_FI_LANDING_PAGE,
			ScreenOptions: &deeplink.Deeplink_AskFiLandingPageOptions{
				AskFiLandingPageOptions: &deeplink.AskFiLandingPageOptions{
					Query:  "Spent this month",
					Source: askFiSource,
				},
			},
		},
	})

	// 3. Invested this month
	if accountOffering == accountTypesPb.AccountProductOffering_APO_REGULAR {
		summaryEntries = append(summaryEntries, &feHomePb.FiAccountSummary_SummaryEntry{
			Title:        &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Invested this month"}},
			MoneyPrimary: types.GetFromBeMoney(investedAmount),
			Icon:         &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SavSummaryInvestedThisMonth.png"},
		})
	}

	// 4. Money In
	summaryEntries = append(summaryEntries, &feHomePb.FiAccountSummary_SummaryEntry{
		Title:        &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Received this month"}},
		MoneyPrimary: types.GetFromBeMoney(moneyIn),
		Icon:         &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SavSummaryMoneyInThisMonth.png"},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_ASK_FI_LANDING_PAGE,
			ScreenOptions: &deeplink.Deeplink_AskFiLandingPageOptions{
				AskFiLandingPageOptions: &deeplink.AskFiLandingPageOptions{
					Query:  "Credit this month",
					Source: askFiSource,
				},
			},
		},
	})

	if accountOffering == accountTypesPb.AccountProductOffering_APO_NRE ||
		accountOffering == accountTypesPb.AccountProductOffering_APO_NRO {
		text := ""
		if accountOffering == accountTypesPb.AccountProductOffering_APO_NRE {
			text = homeConstants.NreExplainer
		} else {
			text = homeConstants.NroExplainer
		}
		explainer = ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, colorsPkg.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_XS)).
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(homeConstants.SavingsAccountSummaryExplainerInfoIcon, 20, 20)).
			WithLeftImagePadding(8).
			WithContainerCornerRadius(15).
			WithContainerPaddingSymmetrical(20, 8).
			WithContainerBackgroundColor(colorsPkg.ColorNight)
	}

	return &feHomePb.GetFiAccountSummaryResponse_FiAccountSummaryTabData{
		AccountIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/FiFederalAccountImage.png"},
		Balance:     types.GetFromBeMoney(currentBalance),
		Title:       &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: title}},
		AccountInfo: accountInfoList,
		AccountActions: []*feHomePb.AccountAction{
			{
				Title:  &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "ADD\nMONEY"}},
				Icon:   &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SummaryAddMoneyAction.png"},
				Action: &feHomePb.AccountAction_DlAction{DlAction: &deeplink.Deeplink{Screen: deeplink.Screen_TRANSFER_IN}},
			},
			{
				Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "GET\nSTATEMENT"}},
				Icon:  &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SummaryStatementAction.png"},
				Action: &feHomePb.AccountAction_DlAction{DlAction: &deeplink.Deeplink{Screen: deeplink.Screen_STATEMENT_REQUEST_SCREEN,
					ScreenOptions: &deeplink.Deeplink_StatementRequestOptions{StatementRequestOptions: &deeplink.StatementRequestOptions{
						AccountId:   accountId,
						AccountType: accountType,
					}}}},
			},
			{
				Title:  &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "ACCOUNT\nSETTINGS"}},
				Icon:   &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SummarySettingsAction.png"},
				Action: &feHomePb.AccountAction_DlAction{DlAction: &deeplink.Deeplink{Screen: deeplink.Screen_PROFILE_SETTINGS}},
			},
			{
				Title:  &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "SHARE\nA/C DETAILS"}},
				Icon:   &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SummaryAccountDetailsAction.png"},
				Action: &feHomePb.AccountAction_ClientAction_{ClientAction: feHomePb.AccountAction_SHARE_ACCOUNT_DETAILS},
			},
		},
		AccountSummary: &feHomePb.FiAccountSummary{
			Title:          &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Account Summary"}},
			SummaryEntries: summaryEntries,
		},
		AdditionalAccountInfo: &feHomePb.AdditionalAccountInfo{Infos: []*feHomePb.AdditionalAccountInfo_Info{
			{
				Title:        &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Interest rates & fees"}},
				RedirectLink: &feHomePb.AdditionalAccountInfo_Info_Url{Url: "https://fi.money/fees"},
				Placeholder:  &feHomePb.AdditionalAccountInfo_Info_Image{Image: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/LinkImage.png"}},
			},
		}},
		LastSyncedMsg: fmt.Sprintf("Last Synced on %v", lastSyncedTime),
		PartnerBank:   partnerBank,
		BalIcon:       discrepancyIcon,
		RefreshCta: &feHomePb.AccountAction{
			Icon:   &commontypes.Image{ImageUrl: s.conf.HomeParams.RefreshAccountSummaryCTA.IconUrl},
			Action: &feHomePb.AccountAction_ClientAction_{ClientAction: feHomePb.AccountAction_REFRESH_FI_ACCOUNT_SUMMARY_RPC},
		},
		LastSyncedMsgForceRefresh: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: fmt.Sprintf(s.conf.HomeParams.LastSyncedAccountSummaryInfo.DisplayString, lastSyncedTime),
			},
			FontColor: s.conf.HomeParams.LastSyncedAccountSummaryInfo.TextColor,
		},
		AccountProductOffering: accountOffering,
		Id:                     accountId,
		Explainer:              explainer,
	}, nil
}

// GetFiAccountDashboard fetches fi account dashboard details
// todo (himanshu) refactor this method to make it more readable
// nolint:funlen
func (s *Service) GetFiAccountDashboard(ctx context.Context, req *feHomePb.GetFiAccountDashboardRequest) (*feHomePb.GetFiAccountDashboardResponse, error) {
	if req.GetDashboardVersion() == feHomePb.DashboardVersion_DASHBOARD_VERSION_V2 {
		return s.getNewFiAccountDashboard(ctx, req)
	}
	var (
		actorId                                 = req.GetReq().GetAuth().GetActorId()
		fiSummaryTitle                          = "Federal Bank Savings A/C"
		privacyModeImageUrl                     = "https://epifi-icons.pointz.in/home-v2/PrivacyImage1.png"
		spentAmount                             = pkgMoney.ZeroINR().GetPb()
		investedAmount                          = pkgMoney.ZeroINR().GetPb()
		accountType                             = accounts.Type_SAVINGS
		showLastSynced                          = true
		minimumTimeDifferenceToShowLastSyncTime = 2 * time.Minute
		currFontColour                          = "#66ECEEF0" // with 40% opacity
		unitsFontColour                         = "#ECEEF0"
		decimalFontColour                       = "#66ECEEF0" // with 40% opacity
	)

	tieringErrGrp, gCtx := errgroup.WithContext(ctx)
	var tieringPitchResp *beTieringPb.GetTieringPitchV2Response
	tieringErrGrp.Go(func() error {
		var rpcErr error
		tieringPitchResp, rpcErr = s.beTieringClient.GetTieringPitchV2(gCtx, &beTieringPb.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if tieringPitchErr := epifigrpc.RPCError(tieringPitchResp, rpcErr); tieringPitchErr != nil {
			return errors.Wrap(tieringPitchErr, "error in fetching tiering pitch details")
		}
		return nil
	})

	switch req.GetAccountProductOffering() {
	case accountTypesPb.AccountProductOffering_APO_NRE:
		fiSummaryTitle = "NRE Savings Account"
	case accountTypesPb.AccountProductOffering_APO_NRO:
		fiSummaryTitle = "NRO Savings Account"
	}

	var getAccountResp *savingsPb.GetAccountResponse
	var getAccountErr error
	if req.GetAccountProductOffering() == accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED {
		getAccountResp, getAccountErr = s.savClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorId{
				ActorId: actorId,
			},
		})
	} else {
		getAccountResp, getAccountErr = s.savClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
				ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
					ActorId:                actorId,
					AccountProductOffering: req.GetAccountProductOffering(),
					PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		})
	}
	if getAccountErr != nil && status.Code(getAccountErr) != codes.NotFound {
		logger.Error(ctx, "failed to get account by user id", zap.Error(getAccountErr))
		return &feHomePb.GetFiAccountDashboardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(getAccountErr.Error()),
			},
		}, nil
	}

	// If no account found, check if the user is a fi lite user and return zero state
	if status.Code(getAccountErr) == codes.NotFound {
		// If status is not found and request Account Product Offering is NRE/NRO return record not found
		if req.GetAccountProductOffering() == accountTypesPb.AccountProductOffering_APO_NRE || req.GetAccountProductOffering() == accountTypesPb.AccountProductOffering_APO_NRO {
			return &feHomePb.GetFiAccountDashboardResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
			}, nil
		}
		if isLiteUser, onbStatus, errLite := isFiLiteUser(ctx, s.onboardingClient, actorId); errLite == nil {
			if isLiteUser {
				// No need to send icon from backend for FiLite users
				return getFiAccountZeroState(ctx, onbStatus)
			}
			return &feHomePb.GetFiAccountDashboardResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusPermissionDenied(),
				},
			}, nil
		}
	}

	accountId := getAccountResp.GetAccount().GetId()
	balSummaryRes, err := s.savClient.GetAccountBalanceWithSummary(ctx, &savingsPb.GetAccountBalanceWithSummaryRequest{
		Identifier:         &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: accountId},
		ActorId:            actorId,
		TimeRange:          savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
		ForceBalanceUpdate: mapOfFeToBEBalanceUpdateEnum[req.GetForceBalanceUpdate()],
	})
	if gRPCErr := epifigrpc.RPCError(balSummaryRes, err); gRPCErr != nil {
		logger.Error(ctx, "failed to fetch account balance summary", zap.Error(gRPCErr))
		return &feHomePb.GetFiAccountDashboardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(gRPCErr.Error()),
			},
		}, nil
	}

	currentBalance := balSummaryRes.GetAvailableBalance()
	lastSyncedTime := balSummaryRes.GetBalanceAt().AsTime().In(datetime.IST).Format("02 Jan 03:04 PM")

	if time.Now().Sub(balSummaryRes.GetBalanceAt().AsTime()) < minimumTimeDifferenceToShowLastSyncTime {
		showLastSynced = false
	}
	tieringErr := tieringErrGrp.Wait()
	toShowTieringRelatedInfo := true
	if tieringErr != nil {
		toShowTieringRelatedInfo = false
		logger.Error(ctx, "error in fetching tiering related info", zap.Error(tieringErr), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	// Switch on the flag iff
	// -> Config flag is true
	// -> Users current tier is either PLUS or INFINITE since SALARY is independent of savings balance
	toShowTieringRelatedInfo = toShowTieringRelatedInfo && s.genconf.HomeRevampParams().ToShowTieringInfoInSavingsDashboard() &&
		(tieringPitchResp.GetCurrentTier() == tieringExtPb.Tier_TIER_FI_PLUS || tieringPitchResp.GetCurrentTier() == tieringExtPb.Tier_TIER_FI_INFINITE)
	discrepancyIcon := s.getDiscrepancyIcon(ctx, balSummaryRes.GetAvailableBalance(), balSummaryRes.GetLedgerBalance(), toShowTieringRelatedInfo, tieringPitchResp)
	// If balance is below a threshold, we alert the user only when there is no balance discrepancy message
	if currentBalance.GetUnits() < 500 && discrepancyIcon == nil {
		discrepancyIcon = getLowBalanceAlertPopup(ctx, s.genconf.HomeRevampParams().LowBalAlertParams())
		// Update the money value too based on the experimentation group
		currFontColour = "#80EAD8A3" // With Opacity 50%
		unitsFontColour = DarkLemon
		decimalFontColour = "#80EAD8A3" // With Opacity 50%
	}

	fromTimestamp := timestamppb.New(datetime.StartOfMonth(time.Now().In(datetime.IST)))
	toTimestamp := timestamppb.New(time.Now().In(datetime.IST))

	// Get dashboard icons based on the current balance
	// If the value is zero, show Add money icon
	txnReq := &txnaggregates.GetTopCategoriesRequest{
		ActorId:            actorId,
		FromTime:           fromTimestamp,
		ToTime:             toTimestamp,
		AccountIds:         &txnaggregates.AccountIds{FiAccountIds: []string{accountId}},
		AggregationMetrics: []*txnaggregates.TxnMetric{{Type: enums.AggregateType_AGGREGATE_TYPE_SUM, Column: txnaggregates.TxnAggregationColumn_TXN_AGGREGATION_COLUMN_AMOUNT}},
		CategoryL0S:        []categorizer.L0{categorizer.L0_L0_SPEND, categorizer.L0_L0_INVESTMENTS},
	}
	txnRes, err := s.txnAggClient.GetTopCategories(ctx, txnReq)
	if err = epifigrpc.RPCError(txnRes, err); err != nil {
		logger.Error(ctx, "failed to get txn aggregates response", zap.Error(err))
	}

	for _, aggr := range txnRes.GetCategoryAggregates() {
		switch aggr.GetCategoryL0() {
		// 4. Spent amount
		case categorizer.L0_L0_SPEND:
			spentAmount, err = pkgMoney.Sum(spentAmount, aggr.GetSumAmount().GetBeMoney())
			if err != nil {
				logger.Error(ctx, "failed to add spent amount", zap.Error(err))
				continue
			}
		// 5. Invested amount
		case categorizer.L0_L0_INVESTMENTS:
			investedAmount, err = pkgMoney.Sum(investedAmount, aggr.GetSumAmount().GetBeMoney())
			if err != nil {
				logger.Error(ctx, "failed to add invested amount", zap.Error(err))
				continue
			}
		}
	}

	// Dashboard icons
	dashboardSettingsIcon := []*feHomePb.Icon{
		{
			IconImage:            &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/HideBalanceOn.png"},
			VisualElement:        commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/HideBalanceOn.png"),
			IconImageOnSelection: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/HideBalanceOff.png"},
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "HIDE BALANCES"},
				FontColor:    "#878A8D",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			},
			TitleOnSelection: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "SHOW BALANCES"},
				FontColor:    "#878A8D",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			},
			IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
			ActionType: feHomePb.Icon_ACTION_TYPE_TOGGLE_PRIVACY,
		},
		{
			IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/AccountStatement.png"},
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/AccountStatement.png"),
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "GET YOUR STATEMENT"},
				FontColor: "#878A8D",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			},
			IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
			ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
			Action: &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_STATEMENT_REQUEST_SCREEN,
				ScreenOptions: &deeplink.Deeplink_StatementRequestOptions{StatementRequestOptions: &deeplink.StatementRequestOptions{
					AccountId:   accountId,
					AccountType: accountType,
				}}}},
		},
		{
			IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/AccountDetails.png"},
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/AccountDetails.png"),
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "SHARE A/C\nDETAILS"},
				FontColor: "#878A8D",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			},
			IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
			ActionType: feHomePb.Icon_ACTION_TYPE_SHARE_ACCOUNT_DETAILS,
		},
		{
			IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/AccountSettings.png"},
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/AccountSettings.png"),
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "ACCOUNT SETTINGS"},
				FontColor: "#878A8D",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			},
			IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
			ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
			Action:     &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_PROFILE_SETTINGS}},
		},
	}

	// Dashboard icon
	addMoneyIcon := &feHomePb.Icon{
		IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/AddMoneyPlusWhite.png"},
		VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/AddMoneyPlusWhite.png"),
		Title:         &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Add Money"}, FontColor: "#FFFFFF", FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
		IconType:      homeTypesPb.IconType_DASHBOARD_ACTION,
		ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
		Action:        &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_TRANSFER_IN, ScreenOptions: getTransferInScreenOptions(ctx, getAccountResp.GetAccount())}},
		BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#00B899"}},
	}

	// Below 1000 we will show prominent AddMoney button
	if currentBalance.GetUnits() >= 1000 {
		addMoneyIcon.IconImage = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/plusAddMoneyDashboardIconNonZeroState.png"}
		addMoneyIcon.VisualElement = commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/plusAddMoneyDashboardIconNonZeroState.png")
		addMoneyIcon.BgColour = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}}
		addMoneyIcon.Title = nil
	}

	dashboardIcon := []*feHomePb.Icon{addMoneyIcon, discrepancyIcon}

	// footer
	spentAmountFooter := &ui.IconTextComponent{}
	investedAmountFooter := &ui.IconTextComponent{}
	spentAmountImage := &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SpentZeroState.png"}
	investedAmountImage := &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/InvestedZeroState.png"}
	spentFooterFontColour := Lead
	investedFooterFontColour := Lead
	if spentAmount.Units > 0 {
		spentAmountImage = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SpentNonZeroStateFooter.png"}
		spentFooterFontColour = Smoke
	}
	spentAmountFooter = &ui.IconTextComponent{
		LeftIcon: spentAmountImage,
		Texts: []*commontypes.Text{{
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Spent %v", convertMoneyToReadableString(spentAmount))},
			FontColor:    spentFooterFontColour,
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
		},
		},
	}

	if investedAmount.GetUnits() > 0 {
		investedAmountImage = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/InvestedNonZeroStateFooter.png"}
		investedFooterFontColour = Smoke
	}

	investedAmountFooter = &ui.IconTextComponent{
		LeftIcon: investedAmountImage,
		Texts: []*commontypes.Text{{
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Invested %v", convertMoneyToReadableString(investedAmount))},
			FontColor:    investedFooterFontColour,
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
		}},
	}

	footer := []*ui.IconTextComponent{
		spentAmountFooter,
		investedAmountFooter,
	}

	footerFontColour := Lead
	if spentAmount.GetUnits() > 0 || investedAmount.GetUnits() > 0 {
		footerFontColour = Smoke
	}

	footer = append(footer, &ui.IconTextComponent{
		Texts: []*commontypes.Text{{
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("in %v", time.Now().Format("Jan"))},
			FontColor:    footerFontColour,
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
		}},
	})

	// privacy mode footer
	spentAmountPrivacyModeFooter := &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SpentPrivacyModeState.png"},
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Spent ••"},
				FontColor:    Lead,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
	}
	investedAmountPrivacyModeFooter := &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/InvestedPrivacyModeState.png"},
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Invested ••"},
				FontColor:    Lead,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
	}
	monthPrivacyModeFooter := &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("in %v", time.Now().Format("Jan"))},
				FontColor:    Lead,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
	}

	privacyModeFooter := []*ui.IconTextComponent{
		spentAmountPrivacyModeFooter,
		investedAmountPrivacyModeFooter,
		monthPrivacyModeFooter,
	}

	shadow := ui.GetDashboardShadow()

	res := &feHomePb.GetFiAccountDashboardResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		DashboardInfo: &feHomePb.HomeDashboard{
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: fiSummaryTitle}, FontColor: "#A4A4A4", FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3}},
			Body: &feHomePb.HomeDashboard_Body{
				PrivacyModeImage: &commontypes.Image{ImageUrl: privacyModeImageUrl},
				DashboardIcons:   dashboardIcon,
				MoneyValue: s.convertMoneyBalanceToUIFormat(ctx, currentBalance, currFontColour, unitsFontColour, decimalFontColour,
					toShowTieringRelatedInfo, tieringPitchResp),
				MoneyValueV2: []*ui.IconTextComponent{
					{
						Texts:    s.convertMoneyBalanceToUIFormat(ctx, currentBalance, currFontColour, unitsFontColour, decimalFontColour, toShowTieringRelatedInfo, tieringPitchResp),
						Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_PRIMARY_SAVINGS_SUMMARY_SCREEN},
					},
				},
				Message:           fmt.Sprintf("Last Synced on %v", lastSyncedTime),
				DashboardState:    feHomePb.HomeDashboard_Body_STATE_NON_ZERO,
				ShowLastSyncedMsg: showLastSynced,
				WatermarkImages: []*commontypes.Image{{
					ImageUrl: "https://epifi-icons.pointz.in/home-v2/FederalBankWatermark.png",
				}},
			},
			AdditionalSettingsIcons: dashboardSettingsIcon,
			Footer:                  footer,
			PrivacyModeFooter:       privacyModeFooter,
			Deeplink:                &deeplink.Deeplink{Screen: deeplink.Screen_PRIMARY_SAVINGS_SUMMARY_SCREEN},
			Shadow:                  shadow,
			DashboardBackground:     feHomePb.GetHomeDashboardSectionBackground(),
			BorderColor:             feHomePb.GetHomeDashboardBorderColor(),
		},
		Balance: types.GetFromBeMoney(currentBalance),
	}
	return res, nil
}

// nolint:funlen
func (s *Service) getNewFiAccountDashboard(ctx context.Context, req *feHomePb.GetFiAccountDashboardRequest) (*feHomePb.GetFiAccountDashboardResponse, error) {
	var (
		actorId                                 = req.GetReq().GetAuth().GetActorId()
		fiSummaryTitle                          = "Federal Bank Savings A/C"
		fiSummaryTitleColour                    = colorsPkg.ColorOnDarkLowEmphasis
		privacyModeImageUrl                     = "https://epifi-icons.pointz.in/home-v2/PrivacyImage1.png"
		accountType                             = accounts.Type_SAVINGS
		showLastSynced                          = true
		minimumTimeDifferenceToShowLastSyncTime = 2 * time.Minute
		currFontColour                          = "#66ECEEF0" // with 40% opacity
		unitsFontColour                         = "#ECEEF0"
		decimalFontColour                       = "#66ECEEF0" // with 40% opacity
		isFeatureHomeDesignEnhancementsEnabled  = featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            s.evaluator,
				OnboardingClient:     s.onboardingClient,
				QuestSdkClient:       s.questSdkClient,
				UserAttributeFetcher: s.userAttributeFetcher,
				NetWorthClient:       s.networthClient,
			},
		})
		ambResponse *beTieringPb.GetAMBInfoResponse
		// Flag to show amb entrypoint on home card
		shouldShowAmbInfo = false
	)

	if isFeatureHomeDesignEnhancementsEnabled {
		fiSummaryTitleColour = colorsPkg.ColorOnDarkHighEmphasis
	}

	ctxWithTimeout, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()

	tieringErrGrp, gCtx := errgroup.WithContext(ctxWithTimeout)
	var tieringPitchResp *beTieringPb.GetTieringPitchV2Response
	tieringErrGrp.Go(func() error {
		var rpcErr error
		tieringPitchResp, rpcErr = s.beTieringClient.GetTieringPitchV2(gCtx, &beTieringPb.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if tieringPitchErr := epifigrpc.RPCError(tieringPitchResp, rpcErr); tieringPitchErr != nil {
			return errors.Wrap(tieringPitchErr, "error in fetching tiering pitch details")
		}
		return nil
	})

	// Best effort fetch for AMB data
	tieringErrGrp.Go(func() error {
		var rpcErr error
		// Check if AMB is enabled for this actor
		isAmbEnabled := s.isAmbEnabledForActor(gCtx, actorId)
		if !isAmbEnabled {
			return nil
		}

		ambResponse, rpcErr = s.beTieringClient.GetAMBInfo(gCtx, &beTieringPb.GetAMBInfoRequest{
			ActorId: actorId,
		})
		if te := epifigrpc.RPCError(ambResponse, rpcErr); te != nil {
			// not returning error since this is best effort
			logger.Error(gCtx, "error fetching amb data for account dashboard", zap.Error(te))
			return nil
		}
		shouldShowAmbInfo = true
		return nil
	})

	// Get entity_id from actor_id
	actorRes, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if grpcErr := epifigrpc.RPCError(actorRes, err); grpcErr != nil {
		logger.Error(ctx, "failed to get actor by id", zap.Error(grpcErr))
		return &feHomePb.GetFiAccountDashboardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(grpcErr.Error()),
			},
		}, nil
	}

	switch req.GetAccountProductOffering() {
	case accountTypesPb.AccountProductOffering_APO_NRE:
		fiSummaryTitle = "NRE Savings Account"
	case accountTypesPb.AccountProductOffering_APO_NRO:
		fiSummaryTitle = "NRO Savings Account"
	}

	var getAccountResp *savingsPb.GetAccountResponse
	var getAccountErr error
	if req.GetAccountProductOffering() == accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED {
		getAccountResp, getAccountErr = s.savClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorId{
				ActorId: actorId,
			},
		})
	} else {
		getAccountResp, getAccountErr = s.savClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
				ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
					ActorId:                actorId,
					AccountProductOffering: req.GetAccountProductOffering(),
					PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		})
	}
	if getAccountErr != nil && status.Code(getAccountErr) != codes.NotFound {
		logger.Error(ctx, "failed to get account by user id", zap.Error(getAccountErr))
		return &feHomePb.GetFiAccountDashboardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(getAccountErr.Error()),
			},
		}, nil
	}
	// If no account found, check if the user is a fi lite user and return zero state
	if status.Code(getAccountErr) == codes.NotFound {
		// If status is not found and request Account Product Offering is NRE return record not found
		if req.GetAccountProductOffering() == accountTypesPb.AccountProductOffering_APO_NRE {
			return &feHomePb.GetFiAccountDashboardResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
			}, nil
		}

		// If status is not found and request Account Product Offering is NRO, check if user is an NRE user then return zero state dashboard for NRO
		if req.GetAccountProductOffering() == accountTypesPb.AccountProductOffering_APO_NRO {
			return &feHomePb.GetFiAccountDashboardResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
			}, nil
			// TODO : remove above return statement and uncomment below once DL for NRO onboarding is available
			/*
				getAccountResp, getAccountErr = s.savClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
							ActorId:                actorId,
							AccountProductOffering: accountTypesPb.AccountProductOffering_APO_NRE,
							PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				})
				if getAccountErr != nil && status.Code(getAccountErr) != codes.NotFound {
					logger.Error(ctx, "failed to get account by user id", zap.Error(getAccountErr))
					return &feHomePb.GetFiAccountDashboardResponse{
						RespHeader: &header.ResponseHeader{
							Status: rpc.StatusInternalWithDebugMsg(getAccountErr.Error()),
						},
					}, nil
				}
				if status.Code(getAccountErr) == codes.NotFound {
					return &feHomePb.GetFiAccountDashboardResponse{
						RespHeader: &header.ResponseHeader{
							Status: rpc.StatusRecordNotFound(),
						},
					}, nil
				}
				// create zero state for NRO dashboard
				return getZeroStateForNRODashboard(), nil
			*/
		}
		if isLiteUser, onbStatus, errLite := isFiLiteUser(ctx, s.onboardingClient, actorId); errLite == nil {
			if isLiteUser {
				return getFiLiteAccountDashboard(ctx, onbStatus)
			}
			return &feHomePb.GetFiAccountDashboardResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusPermissionDenied(),
				},
			}, nil
		}
	}

	accountId := getAccountResp.GetAccount().GetId()
	balSummaryRes, err := s.savClient.GetAccountBalanceWithSummary(ctx, &savingsPb.GetAccountBalanceWithSummaryRequest{
		Identifier:         &savingsPb.GetAccountBalanceWithSummaryRequest_Id{Id: accountId},
		ActorId:            actorId,
		TimeRange:          savingsPb.GetAccountBalanceWithSummaryRequest_MONTH,
		ForceBalanceUpdate: mapOfFeToBEBalanceUpdateEnum[req.GetForceBalanceUpdate()],
	})
	if gRPCErr := epifigrpc.RPCError(balSummaryRes, err); gRPCErr != nil {
		logger.Error(ctx, "failed to fetch account balance summary", zap.Error(gRPCErr))
		return &feHomePb.GetFiAccountDashboardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(gRPCErr.Error()),
			},
		}, nil
	}

	if time.Now().Sub(balSummaryRes.GetBalanceAt().AsTime()) < minimumTimeDifferenceToShowLastSyncTime {
		showLastSynced = false
	}
	tieringErr := tieringErrGrp.Wait()
	toShowTieringRelatedInfo := true
	if tieringErr != nil {
		toShowTieringRelatedInfo = false
		logger.Error(ctx, "error in fetching tiering related info", zap.Error(tieringErr), zap.String(logger.ACTOR_ID_V2, actorId))
	}

	// Switch on the flag iff
	// -> Config flag is true
	// -> Users current tier is either PLUS or INFINITE since SALARY is independent of savings balance
	toShowTieringRelatedInfo = toShowTieringRelatedInfo && s.genconf.HomeRevampParams().ToShowTieringInfoInSavingsDashboard() &&
		(tieringPitchResp.GetCurrentTier() == tieringExtPb.Tier_TIER_FI_PLUS || tieringPitchResp.GetCurrentTier() == tieringExtPb.Tier_TIER_FI_INFINITE)

	// declare footer ticker object
	footerTicker := &feHomePb.HomeDashboard_FooterTicker{
		TickerItems:        make([]*feHomePb.HomeDashboard_FooterTicker_TickerItem, 0),
		TickerItemsPrivacy: make([]*feHomePb.HomeDashboard_FooterTicker_TickerItem, 0),
	}

	// get the current (available) balance
	currentBalance := balSummaryRes.GetAvailableBalance()
	// get critical warning ticker items
	criticalWarningTickerItem, criticalWarningPrivacyModeTickerItem := s.getCriticalWarningTickerItems(ctx, shouldShowAmbInfo, ambResponse)
	// get spent and invested amounts
	spentAmount, investedAmount := s.getSpentAndInvestedAmount(ctx, actorId, accountId)
	// If critical (red) warning item is present, then we have to show ticker for that only
	if criticalWarningTickerItem != nil {
		footerTicker.TickerItems = append(footerTicker.TickerItems, criticalWarningTickerItem)
		footerTicker.TickerItemsPrivacy = append(footerTicker.TickerItemsPrivacy, criticalWarningPrivacyModeTickerItem)
	} else { // if critical warning is not present, we check if non critical warning or any other info to be shown, is present
		nonCriticalWarningTicker, nonCriticalWarningPrivacyModeTickerItem :=
			s.getNonCriticalWarningTickerItems(ctx, actorId, toShowTieringRelatedInfo, tieringPitchResp, balSummaryRes)

		if nonCriticalWarningTicker != nil {
			footerTicker.TickerItems = append(footerTicker.TickerItems, nonCriticalWarningTicker)
			footerTicker.TickerItemsPrivacy = append(footerTicker.TickerItemsPrivacy, nonCriticalWarningPrivacyModeTickerItem)
		}
		// get the spent amount ticker items and populate in footer ticker object
		spentAmountTickerItem, spentAmountPrivacyModeTickerItem := getSpentAmountTickerItems(spentAmount)
		footerTicker.TickerItems = append(footerTicker.TickerItems, spentAmountTickerItem)
		footerTicker.TickerItemsPrivacy = append(footerTicker.TickerItemsPrivacy, spentAmountPrivacyModeTickerItem)

		// get the invested amount ticker items and populate in footer ticker object
		investedAmountTickerItem, investedAmountPrivacyModeTickerItem := getInvestedAmountTickerItems(investedAmount)
		footerTicker.TickerItems = append(footerTicker.TickerItems, investedAmountTickerItem)
		footerTicker.TickerItemsPrivacy = append(footerTicker.TickerItemsPrivacy, investedAmountPrivacyModeTickerItem)

		if currentBalance.GetUnits() < 500 {
			lowBalanceColourCombination := getLowBalanceColourCombination()
			currFontColour = lowBalanceColourCombination.CurrFontColour
			unitsFontColour = lowBalanceColourCombination.UnitsFontColour
			decimalFontColour = lowBalanceColourCombination.DecimalFontColour
		}
	}
	// get the last synced time
	lastSyncedTime := balSummaryRes.GetBalanceAt().AsTime().In(datetime.IST).Format("02 Jan 03:04 PM")
	res := &feHomePb.GetFiAccountDashboardResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		DashboardInfo: &feHomePb.HomeDashboard{
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: fiSummaryTitle}, FontColor: fiSummaryTitleColour, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3}},
			Body: &feHomePb.HomeDashboard_Body{
				PrivacyModeImage: &commontypes.Image{ImageUrl: privacyModeImageUrl},
				DashboardIcons:   s.getDashboardIcons(ctx, currentBalance),
				MoneyValue: s.convertMoneyBalanceToUIFormat(ctx, currentBalance, currFontColour, unitsFontColour, decimalFontColour,
					toShowTieringRelatedInfo, tieringPitchResp),
				MoneyValueV2: []*ui.IconTextComponent{
					{
						Texts:    s.convertMoneyBalanceToUIFormat(ctx, currentBalance, currFontColour, unitsFontColour, decimalFontColour, toShowTieringRelatedInfo, tieringPitchResp),
						Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_PRIMARY_SAVINGS_SUMMARY_SCREEN},
					},
				},
				Message:           fmt.Sprintf("Last Synced on %v", lastSyncedTime),
				DashboardState:    feHomePb.HomeDashboard_Body_STATE_NON_ZERO,
				ShowLastSyncedMsg: showLastSynced,
				WatermarkImages: []*commontypes.Image{{
					ImageUrl: "https://epifi-icons.pointz.in/home-v2/FederalBankWatermark.png",
				}},
			},
			AdditionalSettingsIcons: getDashboardSettingOptions(accountId, accountType),
			Deeplink:                &deeplink.Deeplink{Screen: deeplink.Screen_PRIMARY_SAVINGS_SUMMARY_SCREEN},
			Shadow:                  ui.GetDashboardShadow(),
			FooterTicker:            footerTicker,
			DashboardFooter:         &feHomePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
			DashboardBackground:     feHomePb.GetHomeDashboardSectionBackground(),
			BorderColor:             feHomePb.GetHomeDashboardBorderColor(),
		},
		Balance: types.GetFromBeMoney(currentBalance),
	}
	logger.Debug(ctx, fmt.Sprintf("footerticker: %v", res.DashboardInfo.FooterTicker))
	return res, nil
}

// Method to compute and get the spend and invested amounts
func (s *Service) getSpentAndInvestedAmount(ctx context.Context, actorId string, accountId string) (*money.Money, *money.Money) {
	fromTimestamp := timestamppb.New(datetime.StartOfMonth(time.Now().In(datetime.IST)))
	toTimestamp := timestamppb.New(time.Now().In(datetime.IST))
	spentAmount := pkgMoney.ZeroINR().GetPb()
	investedAmount := pkgMoney.ZeroINR().GetPb()
	// Get dashboard icons based on the current balance
	// If the value is zero, show Add money icon
	txnReq := &txnaggregates.GetTopCategoriesRequest{
		ActorId:            actorId,
		FromTime:           fromTimestamp,
		ToTime:             toTimestamp,
		AccountIds:         &txnaggregates.AccountIds{FiAccountIds: []string{accountId}},
		AggregationMetrics: []*txnaggregates.TxnMetric{{Type: enums.AggregateType_AGGREGATE_TYPE_SUM, Column: txnaggregates.TxnAggregationColumn_TXN_AGGREGATION_COLUMN_AMOUNT}},
		CategoryL0S:        []categorizer.L0{categorizer.L0_L0_SPEND, categorizer.L0_L0_INVESTMENTS},
	}
	txnRes, err := s.txnAggClient.GetTopCategories(ctx, txnReq)
	if err = epifigrpc.RPCError(txnRes, err); err != nil {
		logger.Error(ctx, "failed to get txn aggregates response", zap.Error(err))
	}

	for _, aggr := range txnRes.GetCategoryAggregates() {
		switch aggr.GetCategoryL0() {
		// 4. Spent amount
		case categorizer.L0_L0_SPEND:
			spentAmount, err = pkgMoney.Sum(spentAmount, aggr.GetSumAmount().GetBeMoney())
			if err != nil {
				logger.Error(ctx, "failed to add spent amount", zap.Error(err))
				continue
			}
		// 5. Invested amount
		case categorizer.L0_L0_INVESTMENTS:
			investedAmount, err = pkgMoney.Sum(investedAmount, aggr.GetSumAmount().GetBeMoney())
			if err != nil {
				logger.Error(ctx, "failed to add invested amount", zap.Error(err))
				continue
			}
		}
	}
	return spentAmount, investedAmount
}

// method to get critical warning ticker icon text component, currently it doesn't return any data, but is extendable
// for future use
func (s *Service) getCriticalWarningTickerIconTextComponent(ctx context.Context, ambResponse *beTieringPb.GetAMBInfoResponse) (*ui.IconTextComponent, *ui.IconTextComponent) {
	doesShortfallExist, err := pkgMoney.IsGreaterThan(ambResponse.GetShortfallAmount(), pkgMoney.ZeroINR().GetPb())
	if err != nil {
		logger.Error(ctx, "error checking if shortfall exists", zap.Error(err))
		return nil, nil
	}

	if !doesShortfallExist {
		return s.getGenericAMBTickerIconTextComponent(ambResponse.GetCurrentTier())
	}
	return s.getLowAMBTickerIconTextComponent(ctx, ambResponse.GetCurrentTier())
}

func getLowBalanceColourCombination() *layoutconfiguration.DashboardBalanceColourCombination {
	return &layoutconfiguration.DashboardBalanceColourCombination{
		CurrFontColour:    "#80EAD8A3",
		UnitsFontColour:   DarkLemon,
		DecimalFontColour: "#80EAD8A3",
	}
}

func getLowBalanceTickerIconTextComponent(currentBalance *money.Money) (*ui.IconTextComponent, *ui.IconTextComponent) {
	var displayText string
	if currentBalance.GetUnits() < 0 {
		displayText = fmt.Sprintf("Your balance is %v. Some funds will be available soon", currentBalance.GetUnits())
	} else {
		displayText = "Low balance"
	}
	lowBalanceTicker := getLowBalanceTicker(displayText)
	return lowBalanceTicker, lowBalanceTicker
}

func (s *Service) getAmbTickerBackgroundColor(bgColorConf *genconf.BgColor, platform commontypes.Platform, tier beTieringExtPb.Tier) *widget.BackgroundColour {
	var degree int32
	degreeConf := bgColorConf.BgColorLinearGradientDegree()
	degree, _ = degreeConf.Load("Platform_ANDROID")
	if platform == commontypes.Platform_IOS {
		degree, _ = degreeConf.Load(platform.String())
	}
	if tier == beTieringExtPb.Tier_TIER_FI_REGULAR {
		return widget.GetLinearGradientBackgroundColour(degree, []*widget.ColorStop{
			{
				Color:          bgColorConf.BgColorLinearGradientStart(),
				StopPercentage: 0,
			},
			{
				Color:          bgColorConf.BgColorLinearGradientEnd(),
				StopPercentage: 100,
			},
		})
	}
	return nil
}

func (s *Service) getLowAMBTickerIconTextComponent(ctx context.Context, currentTier beTieringExtPb.Tier) (*ui.IconTextComponent, *ui.IconTextComponent) {
	tickerConf := lo.Ternary(currentTier == beTieringExtPb.Tier_TIER_FI_REGULAR, s.genconf.Tiering().AMBConfig().HomeFooterTickerParams().RegularShortfall(), s.genconf.Tiering().AMBConfig().HomeFooterTickerParams().NonRegularShortfall())
	bgColorConf := tickerConf.BgColor()
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	backgroundColor := s.getAmbTickerBackgroundColor(bgColorConf, platform, currentTier)

	lowAmbTicker := &ui.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementImageFromUrl(tickerConf.ImageUrl()),
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: tickerConf.DisplayText()},
				FontColor:    "#F6F9FD",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
		RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/WhiteRightArrow.png"),
		Deeplink:           &deeplink.Deeplink{Screen: deeplink.Screen_AMB_DETAILS_SCREEN},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BackgroundColour: backgroundColor,
		},
	}
	return lowAmbTicker, lowAmbTicker
}

func (s *Service) getGenericAMBTickerIconTextComponent(currentTier beTieringExtPb.Tier) (*ui.IconTextComponent, *ui.IconTextComponent) {
	tickerParams := s.genconf.Tiering().AMBConfig().HomeFooterTickerParams()
	var displayText string
	var imageUrl string

	if currentTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
		// regular tier
		displayText = tickerParams.RegularGeneric().DisplayText()
		imageUrl = tickerParams.RegularGeneric().ImageUrl()
	} else {
		// non regular tier
		displayText = tickerParams.NonRegularGeneric().DisplayText()
		imageUrl = tickerParams.NonRegularGeneric().ImageUrl()
	}

	AmbTicker := &ui.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementImageFromUrl(imageUrl),
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: displayText},
				FontColor:    "#929599",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
		RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
		Deeplink:           &deeplink.Deeplink{Screen: deeplink.Screen_AMB_DETAILS_SCREEN},
	}
	return AmbTicker, AmbTicker
}

// getNonCriticalWarningTickerIconTextComponent returns two icon text components (first one is when privacy mode is disabled,
// second one is when privacy mode is enabled) that are displayed as message on the footer of AccountBalance card on home.
// If nil is returned then no text is displayed.
// https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=18049-147875&t=tsGmmVfxEyIMNu5N-0
func (s *Service) getNonCriticalWarningTickerIconTextComponent(ctx context.Context, curActorId string, toShowTieringRelatedInfo bool,
	tieringPitchResp *beTieringPb.GetTieringPitchV2Response, balSummaryRes *savingsPb.GetAccountBalanceWithSummaryResponse) (*ui.IconTextComponent, *ui.IconTextComponent) {
	graceTickerIconTextComponent := getGraceTickerIconTextComponent(ctx, toShowTieringRelatedInfo, tieringPitchResp)
	if balSummaryRes.GetAvailableBalance().GetUnits() < 0 {

	}
	if balSummaryRes.GetAvailableBalance().GetUnits() < 500 {
		lowBalanceTickerIconTextComponent, lowBalancePrivacyModeTickerIconTextComponent := getLowBalanceTickerIconTextComponent(balSummaryRes.GetAvailableBalance())
		return lowBalanceTickerIconTextComponent, lowBalancePrivacyModeTickerIconTextComponent
	}
	if graceTickerIconTextComponent != nil {
		return graceTickerIconTextComponent, graceTickerIconTextComponent
	}
	// ledger mismatch
	if pkgMoney.Compare(balSummaryRes.GetAvailableBalance(), balSummaryRes.GetLedgerBalance()) != 0 {
		ledgerDiffTickerIconTextComponent := s.getLedgerDiffTickerIconTextComponent(ctx, balSummaryRes.GetAvailableBalance(), balSummaryRes.GetLedgerBalance())
		ledgerDiffPrivacyModeTickerIconTextComponent := s.getLedgerDiffPrivacyModeTickerIconTextComponent(ctx, balSummaryRes.GetAvailableBalance(), balSummaryRes.GetLedgerBalance())
		return ledgerDiffTickerIconTextComponent, ledgerDiffPrivacyModeTickerIconTextComponent
	}

	if s.genconf.HomeBalanceSummaryWidgetUiConfig().StaleBalanceWarningTickerIconTextComponent().IsEnabled() &&
		balSummaryRes.GetIsBalanceStale() {
		staleBalanceIconTextComponent := s.getStaleBalanceIconTextComponent(ctx, balSummaryRes)
		return staleBalanceIconTextComponent, staleBalanceIconTextComponent
	}

	shouldDisplayStaleComputedBalWarning, err := s.evaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_SHOW_STALE_COMPUTED_BALANCE_WARNING).WithActorId(curActorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluate feature stale computed balance warning text", zap.Error(err))
		shouldDisplayStaleComputedBalWarning = false
	}
	if shouldDisplayStaleComputedBalWarning &&
		s.genconf.HomeBalanceSummaryWidgetUiConfig().StaleComputedBalanceWarningTickerIconTextComponent().IsEnabled() &&
		balSummaryRes.GetIsComputedBalanceStale() {
		logger.Info(ctx, "computed balance stale warning enabled", zap.String(logger.ACTOR_ID_V2, curActorId))
		staleComputedBalanceIconTextComponent := s.getStaleComputedBalanceIconTextComponent(ctx, balSummaryRes)
		return staleComputedBalanceIconTextComponent, staleComputedBalanceIconTextComponent
	}
	return nil, nil
}

func getDeeplinkForLowBalance() *deeplink.Deeplink {
	var (
		title    = "Your account balance is\nrunning low"
		subTitle = "To make the best of Fi's features, we recommend maintaining a minimum of ₹5,000 as your account balance."
	)

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplink.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplink.InformationPopupOptions{
				IconUrl: "https://epifi-icons.pointz.in/home-v2/LowBalancePopupImage.png",
				Ctas: []*deeplink.Cta{
					{
						Type: deeplink.Cta_CUSTOM,
						Text: "Add Money",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_TRANSFER_IN,
						},
						DisplayTheme: deeplink.Cta_PRIMARY,
						Status:       deeplink.Cta_CTA_STATUS_ENABLED,
					},
				},
				IsNonDismissible: false,
				TextTitle:        &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: title}},
				TextSubTitle:     &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: subTitle}},
				BgColor:          Snow,
			},
		},
	}
}

func getLowBalanceTicker(displayValueText string) *ui.IconTextComponent {
	return &ui.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-red-warning.png"),
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: displayValueText},
				FontColor:    "#929599",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
		RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
		Deeplink:           getDeeplinkForLowBalance(),
	}
}

func getGraceTickerIconTextComponent(ctx context.Context, toShowTieringRelatedInfo bool,
	tieringPitchResp *beTieringPb.GetTieringPitchV2Response) *ui.IconTextComponent {
	isUserInGrace, currentTier, minBalance, getTieringInfoErr := getTieringInfoFromRespForSavingsDash(tieringPitchResp)
	if getTieringInfoErr != nil {
		if !errors.Is(getTieringInfoErr, feTieringErrors.ErrTierHasNoMinBalanceCriteria) {
			logger.Error(ctx, "error getting tiering info from pitch response", zap.Error(getTieringInfoErr))
		}
		return nil
	}
	if !toShowTieringRelatedInfo || !isUserInGrace {
		return nil
	}

	return &ui.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: getGraceInfoTitleForNewDashboard(currentTier)},
				FontColor:    "#929599",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
		RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
		Deeplink:           getGraceDiscrepancyDeeplink(ctx, currentTier, minBalance),
	}
}

func (s *Service) getLedgerDiffTickerIconTextComponent(ctx context.Context, availableBalance, ledgerBalance *money.Money) *ui.IconTextComponent {
	return &ui.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Your actual balance is %v. "+
					"Some funds will be available soon.", convertMoneyToReadableString(ledgerBalance))},
				FontColor: "#929599",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
		RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
		Deeplink:           s.getPersonalisedAccountDiscrepancyDeeplink(ctx, availableBalance, ledgerBalance),
	}
}

func (s *Service) getLedgerDiffPrivacyModeTickerIconTextComponent(ctx context.Context, availableBalance, ledgerBalance *money.Money) *ui.IconTextComponent {
	return &ui.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Balance isn't up-to-date. " +
					"Some funds will be available soon."},
				FontColor: "#929599",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
		RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
		Deeplink:           s.getPersonalisedAccountDiscrepancyDeeplink(ctx, availableBalance, ledgerBalance),
	}
}

func (s *Service) getStaleBalanceIconTextComponent(_ context.Context, balSummaryRes *savingsPb.GetAccountBalanceWithSummaryResponse) *ui.IconTextComponent {
	return &ui.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: fmt.Sprintf(
						s.genconf.HomeBalanceSummaryWidgetUiConfig().StaleBalanceWarningTickerIconTextComponent().TextContent(),
						balSummaryRes.GetBalanceAt().AsTime().In(datetime.IST).Format(s.homeParams.StaleBalanceTimeFormat),
					),
				},
				FontColor: "#929599",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
		RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
		Deeplink:           nil,
	}
}

func (s *Service) getStaleComputedBalanceIconTextComponent(_ context.Context, _ *savingsPb.GetAccountBalanceWithSummaryResponse) *ui.IconTextComponent {
	return &ui.IconTextComponent{
		LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: s.genconf.HomeBalanceSummaryWidgetUiConfig().StaleComputedBalanceWarningTickerIconTextComponent().TextContent()},
				FontColor:    "#929599",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
		RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
		Deeplink:           nil,
	}
}

func (s *Service) getCriticalWarningTickerItems(ctx context.Context, shouldShowAmbInfo bool, ambResponse *beTieringPb.GetAMBInfoResponse) (*feHomePb.HomeDashboard_FooterTicker_TickerItem, *feHomePb.HomeDashboard_FooterTicker_TickerItem) {
	if !shouldShowAmbInfo || ambResponse == nil || ambResponse.GetShortfallAmount() == nil {
		return nil, nil
	}
	criticalWarningTickerIconTextComponent, criticalWarningPrivacyModeTickerIconTextComponent := s.getCriticalWarningTickerIconTextComponent(ctx, ambResponse)
	if criticalWarningTickerIconTextComponent != nil {
		criticalWarningTickerItem := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
			TickerType:    feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_WARNING_RED,
			TickerContent: criticalWarningTickerIconTextComponent,
		}
		criticalWarningPrivacyModeTickerItem := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
			TickerType:    feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_WARNING_RED,
			TickerContent: criticalWarningPrivacyModeTickerIconTextComponent,
		}
		return criticalWarningTickerItem, criticalWarningPrivacyModeTickerItem
	}
	return nil, nil
}

func (s *Service) getNonCriticalWarningTickerItems(ctx context.Context, curActorId string, toShowTieringRelatedInfo bool,
	tieringPitchResp *beTieringPb.GetTieringPitchV2Response, balSummaryRes *savingsPb.GetAccountBalanceWithSummaryResponse) (*feHomePb.HomeDashboard_FooterTicker_TickerItem,
	*feHomePb.HomeDashboard_FooterTicker_TickerItem) {
	nonCriticalWarningTickerIconTextComponent, nonCriticalWarningPrivacyModeTickerIconTextComponent := s.getNonCriticalWarningTickerIconTextComponent(ctx,
		curActorId, toShowTieringRelatedInfo, tieringPitchResp, balSummaryRes)

	if nonCriticalWarningTickerIconTextComponent != nil {
		nonCriticalWarningTickerItem := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
			TickerType:    feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_WARNING_ORANGE,
			TickerContent: nonCriticalWarningTickerIconTextComponent,
		}
		nonCriticalWarningPrivacyModeTickerItem := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
			TickerType:    feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_WARNING_ORANGE,
			TickerContent: nonCriticalWarningPrivacyModeTickerIconTextComponent,
		}
		return nonCriticalWarningTickerItem, nonCriticalWarningPrivacyModeTickerItem
	}
	return nil, nil
}

func getSpentAmountTickerItems(spentAmount *money.Money) (*feHomePb.HomeDashboard_FooterTicker_TickerItem, *feHomePb.HomeDashboard_FooterTicker_TickerItem) {
	spentAmountTickerItem := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
		TickerContent: getSpentAmountTickerIconTextComponent(spentAmount),
	}
	spentAmountPrivacyModeTickerItem := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
		TickerContent: getSpentAmountPrivacyModeTickerIconTextComponent(),
	}
	return spentAmountTickerItem, spentAmountPrivacyModeTickerItem
}

func getSpentAmountTickerIconTextComponent(spentAmount *money.Money) *ui.IconTextComponent {
	spentAmountImageUrl := "https://epifi-icons.pointz.in/home/<USER>/spent-zero-state.png"
	spentFooterFontColour := "#57595D"
	if spentAmount.Units > 0 {
		spentAmountImageUrl = "https://epifi-icons.pointz.in/home/<USER>/spent-non-zero-state-footer.png"
		spentFooterFontColour = "#6A6D70"
	}

	return &ui.IconTextComponent{
		LeftVisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: spentAmountImageUrl,
					},
				},
			},
		},
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Spent %v in %v", convertMoneyToReadableString(spentAmount),
					time.Now().Format("Jan"))},
				FontColor: spentFooterFontColour,
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
			},
		},
	}
}

func getSpentAmountPrivacyModeTickerIconTextComponent() *ui.IconTextComponent {
	return &ui.IconTextComponent{
		LeftVisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/home-v2/SpentPrivacyModeState.png",
					},
				},
			},
		},
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Spent •• in %v",
					time.Now().Format("Jan"))},
				FontColor: "#57595D",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
	}
}

// Method to create the invested amount ticker items
// returns ticker item and privacy mode ticker item
func getInvestedAmountTickerItems(investedAmount *money.Money) (*feHomePb.HomeDashboard_FooterTicker_TickerItem,
	*feHomePb.HomeDashboard_FooterTicker_TickerItem) {
	investAmountTickerItem := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
		TickerContent: getInvestedAmountTickerIconTextComponent(investedAmount),
	}
	investedAmountPrivacyModeTickerItem := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
		TickerContent: getInvestedAmountPrivacyModeTickerIconTextComponent(),
	}
	return investAmountTickerItem, investedAmountPrivacyModeTickerItem
}

// Method to create the invested amount ticker icon text component using invested amount
func getInvestedAmountTickerIconTextComponent(investedAmount *money.Money) *ui.IconTextComponent {
	investedAmountImageUrl := "https://epifi-icons.pointz.in/home/<USER>/invested-amount-zero-state-footer-arrow.png"
	investedFooterFontColour := "#57595D"
	if investedAmount.GetUnits() > 0 {
		investedAmountImageUrl = "https://epifi-icons.pointz.in/home/<USER>/invested-amount-non-zero-state-footer-arrow.png"
		investedFooterFontColour = "#6A6D70"
	}

	return &ui.IconTextComponent{
		LeftVisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: investedAmountImageUrl,
					},
				},
			},
		},
		Texts: []*commontypes.Text{{
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Invested %v in %v",
				convertMoneyToReadableString(investedAmount), time.Now().Format("Jan"))},
			FontColor: investedFooterFontColour,
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
		}},
	}
}

// Method to create the invested amount ticker icon text component for privacy mode
func getInvestedAmountPrivacyModeTickerIconTextComponent() *ui.IconTextComponent {
	return &ui.IconTextComponent{
		LeftVisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/home-v2/InvestedPrivacyModeState.png",
					},
				},
			},
		},
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Invested •• in %v",
					time.Now().Format("Jan"))},
				FontColor: "#57595D",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
	}
}

// Method to get the options on clicking the settings (3-dots)
// returns account details, account statement and account settings options
func getDashboardSettingOptions(accountId string, accountType accounts.Type) []*feHomePb.Icon {
	return []*feHomePb.Icon{
		{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/share-ac-details-bank-icon.png"),
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "SHARE A/C\nDETAILS"},
				FontColor: "#878A8D",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			},
			IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
			ActionType: feHomePb.Icon_ACTION_TYPE_SHARE_ACCOUNT_DETAILS,
		},
		{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/ac-statement-clipboard-icon.png"),
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "GET YOUR STATEMENT"},
				FontColor: "#878A8D",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			},
			IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
			ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
			Action: &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_STATEMENT_REQUEST_SCREEN,
				ScreenOptions: &deeplink.Deeplink_StatementRequestOptions{StatementRequestOptions: &deeplink.StatementRequestOptions{
					AccountId:   accountId,
					AccountType: accountType,
				}}}},
		},
		{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/ac-settings-icon.png"),
			Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "ACCOUNT SETTINGS"},
				FontColor: "#878A8D",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
			},
			IconType:   homeTypesPb.IconType_DASHBOARD_SETTING,
			ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
			Action:     &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_PROFILE_SETTINGS}},
		},
	}
}

// Method to get the dashboard icons
// it gets add money icon and privacy toggle icon (eye)
func (s *Service) getDashboardIcons(ctx context.Context, currentBalance *money.Money) []*feHomePb.Icon {
	addMoneyIcon := s.getAddMoneyIcon(ctx, currentBalance)
	privacyToggleIcon := getPrivacyToggleIcon()
	return []*feHomePb.Icon{privacyToggleIcon, addMoneyIcon}
}

// Method to create the add money icon based on current balance
// if the current balance is equal to or greater than 1 crore, it doesn't have the title "Add", only the rupee icon
func (s *Service) getAddMoneyIcon(ctx context.Context, currentBalance *money.Money) *feHomePb.Icon {
	addMoneyIconUrl := "https://epifi-icons.pointz.in/home/<USER>/rupee.png"
	addMoneyIcon := &feHomePb.Icon{
		VisualElement: commontypes.GetVisualElementImageFromUrl(addMoneyIconUrl),
		Title:         &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Add"}, FontColor: "#00B899", FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S}},
		IconType:      homeTypesPb.IconType_DASHBOARD_ACTION,
		ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
		Action:        &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_TRANSFER_IN}},
		BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#28292B"}},
	}

	// https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=26035-5600&t=tRViUo0waZDADQk4-1
	if s.genconf.HomeRevampParams().EnableSADashboardAddMoneyV2(ctx) {
		addMoneyText := "Add"
		addMoneyIconUrl = "https://epifi-icons.pointz.in/home/<USER>/rupee-white.png"
		if currentBalance.GetUnits() < 10000 {
			addMoneyText = "Add Money"
		}
		addMoneyIcon = &feHomePb.Icon{
			VisualElement: commontypes.GetVisualElementImageFromUrl(addMoneyIconUrl),
			Title:         &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: addMoneyText}, FontColor: "#F6F9FD", FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S}},
			IconType:      homeTypesPb.IconType_DASHBOARD_ACTION,
			ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
			Action:        &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_TRANSFER_IN}},
			BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#00B899"}},
			BgColourV2:    widget.GetLinearGradientBackgroundColour(45, []*widget.ColorStop{{Color: "#006D5B", StopPercentage: 0}, {Color: "#00B899", StopPercentage: 100}}),
		}
	}

	if currentBalance.GetUnits() >= 100000 { // remove 'Add' text if amount is more than or equal to 1 lakh
		addMoneyIcon.Title = nil
	}
	return addMoneyIcon
}

// Method to create hide numbers icon (eye)
func getPrivacyToggleIcon() *feHomePb.Icon {
	return &feHomePb.Icon{
		VisualElement:        commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/hide-balance-on.png"),
		IconImageOnSelection: &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home/<USER>/hide-balance-off.png"},
		IconType:             homeTypesPb.IconType_DASHBOARD_SETTING,
		ActionType:           feHomePb.Icon_ACTION_TYPE_TOGGLE_PRIVACY,
		BgColour:             &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#28292B"}},
	}
}

// nolint:funlen
// Method to get fi lite footer ticker icon text component
// based on onboarding status
func getFiLiteFooterTickerIconTextComponent(onbStatus onboarding.FeatureStatus) *ui.IconTextComponent {
	switch onbStatus {
	case onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS:
		rightArrowGreenVector := &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/home/<USER>/right-arrow-green-vector.png",
					},
				},
			},
		}
		return &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "We’ll notify you in 24-48 hrs"},
					FontColor:    "#929599",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				},
			},
			RightVisualElement: rightArrowGreenVector,
		}
	case onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_FAILURE:
		rightArrowGreenVector := &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/home/<USER>/right-arrow-green-vector.png",
					},
				},
			},
		}
		return &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Know more"},
					FontColor:    FiGreen,
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				},
			},
			RightVisualElement: rightArrowGreenVector,
		}
	}
	rightArrowGreenVector := &commontypes.VisualElement{
		Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{
					Url: "https://epifi-icons.pointz.in/home/<USER>/right-arrow-green-vector.png",
				},
			},
		},
	}
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Open a Savings Account"},
				FontColor:    FiGreen,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
		},
		RightVisualElement: rightArrowGreenVector,
	}
}

// Method to get fi lite footer ticker item
func getFiLiteFooterTickerItem(onbStatus onboarding.FeatureStatus) *feHomePb.HomeDashboard_FooterTicker_TickerItem {
	return &feHomePb.HomeDashboard_FooterTicker_TickerItem{
		TickerType:    feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_WARNING_RED,
		TickerContent: getFiLiteFooterTickerIconTextComponent(onbStatus),
	}
}

func getFooterTickerItemForNroZeroState() *feHomePb.HomeDashboard_FooterTicker_TickerItem {
	return &feHomePb.HomeDashboard_FooterTicker_TickerItem{
		TickerType: feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_UNSPECIFIED,
		TickerContent: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Quick & effortless process"},
					FontColor:    "#57595D",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
				},
			},
		},
	}
}

// nolint: dupl, funlen
// Method to get fi lite account dashboard
func getFiLiteAccountDashboard(ctx context.Context, onbStatus onboarding.FeatureStatus) (*feHomePb.GetFiAccountDashboardResponse, error) {
	fiLiteFooterTickerItem := getFiLiteFooterTickerItem(onbStatus)
	footerTicker := &feHomePb.HomeDashboard_FooterTicker{}
	footerTicker.TickerItems = make([]*feHomePb.HomeDashboard_FooterTicker_TickerItem, 0)
	footerTicker.TickerItemsPrivacy = make([]*feHomePb.HomeDashboard_FooterTicker_TickerItem, 0)
	footerTicker.TickerItems = append(footerTicker.TickerItems, fiLiteFooterTickerItem)
	footerTicker.TickerItemsPrivacy = append(footerTicker.TickerItemsPrivacy, fiLiteFooterTickerItem)

	switch onbStatus {
	case onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS:
		dl, err := onbPkg.GetSABenefitsScreen(ctx)
		if err != nil {
			logger.Error(ctx, "error in getting savings account benefits screen", zap.Error(err))
			return nil, fmt.Errorf("error in getting savings account benefits screen, err: %w", err)
		}
		fiLiteFooterTickerItem.TickerContent.Deeplink = dl
		return &feHomePb.GetFiAccountDashboardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			DashboardInfo: &feHomePb.HomeDashboard{
				Title: commontypes.GetTextFromStringFontColourFontStyle("Federal Bank Savings A/C", "#6A6D70", commontypes.FontStyle_SUBTITLE_S),
				Body: &feHomePb.HomeDashboard_Body{
					DashboardState: feHomePb.HomeDashboard_Body_STATE_ZERO,
					MoneyValueV2: []*ui.IconTextComponent{
						{
							Texts: []*commontypes.Text{
								commontypes.GetTextFromStringFontColourFontStyle("Your account opening is \ntaking time ⏱️", "#F6F9FD", commontypes.FontStyle_HEADLINE_M),
							},
							Deeplink: dl,
						},
					},
				},
				Deeplink:            dl,
				Shadow:              ui.GetDashboardShadow(),
				ZeroStateImage:      commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/federal-bank-logo.png"),
				HideSettingsIcon:    true,
				FooterTicker:        footerTicker,
				DashboardFooter:     &feHomePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
				DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
				BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
			},
			Balance: types.GetFromBeMoney(&money.Money{CurrencyCode: "INR", Units: 0}),
		}, nil
	case onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_FAILURE:
		failureDeeplink := getFailureDeeplinkForFiLiteDashboard()
		fiLiteFooterTickerItem.TickerContent.Deeplink = failureDeeplink
		return &feHomePb.GetFiAccountDashboardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			DashboardInfo: &feHomePb.HomeDashboard{
				Title: commontypes.GetTextFromStringFontColourFontStyle("Federal Bank Savings A/C", "#6A6D70", commontypes.FontStyle_SUBTITLE_S),
				Body: &feHomePb.HomeDashboard_Body{
					DashboardState: feHomePb.HomeDashboard_Body_STATE_ZERO,
					MoneyValueV2: []*ui.IconTextComponent{
						{
							Texts: []*commontypes.Text{
								commontypes.GetTextFromStringFontColourFontStyle("Your account opening \nis paused ⏸️ ", "#F6F9FD", commontypes.FontStyle_HEADLINE_M),
							},
							Deeplink: failureDeeplink,
						},
					},
				},
				Deeplink:            failureDeeplink,
				Shadow:              ui.GetDashboardShadow(),
				ZeroStateImage:      commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/federal-bank-logo.png"),
				HideSettingsIcon:    true,
				FooterTicker:        footerTicker,
				DashboardFooter:     &feHomePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
				DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
				BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
			},
			Balance: types.GetFromBeMoney(&money.Money{CurrencyCode: "INR", Units: 0}),
		}, nil
	}
	dl, err := onbPkg.GetSABenefitsScreen(ctx)
	if err != nil {
		logger.Error(ctx, "error in getting savings account benefits screen", zap.Error(err))
		return nil, fmt.Errorf("error in getting savings account benefits screen, err: %w", err)
	}
	fiLiteFooterTickerItem.TickerContent.Deeplink = dl
	return &feHomePb.GetFiAccountDashboardResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		DashboardInfo: &feHomePb.HomeDashboard{
			Title: commontypes.GetTextFromStringFontColourFontStyle("Federal Bank Savings A/C", "#6A6D70", commontypes.FontStyle_SUBTITLE_S),
			Body: &feHomePb.HomeDashboard_Body{
				DashboardState: feHomePb.HomeDashboard_Body_STATE_ZERO,
				MoneyValueV2: []*ui.IconTextComponent{
					{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Get 2% back on UPI & \nDebit Card spends ", "#F6F9FD", commontypes.FontStyle_HEADLINE_M),
						},
						Deeplink: dl,
					},
				},
			},
			Shadow:              ui.GetDashboardShadow(),
			ZeroStateImage:      commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/fi-lite-bank.png"),
			HideSettingsIcon:    true,
			FooterTicker:        footerTicker,
			DashboardFooter:     &feHomePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
			DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
			BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
		},
		Balance: types.GetFromBeMoney(&money.Money{CurrencyCode: "INR", Units: 0}),
	}, nil
}

// Method to get failure deeplink for fi lite dashboard
func getFailureDeeplinkForFiLiteDashboard() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{
				Title:       "We are unable to create a savings account for you",
				Subtitle:    "Unfortunately, we are unable to create a savings account for you at this time. You can come back and try again in 3 months.",
				ImageUrl:    "https://epifi-icons.pointz.in/onboarding/door_with_nails.png",
				HasFeedback: true,
				Ctas: []*deeplink.Cta{
					{
						Type: deeplink.Cta_CUSTOM,
						Text: "Know more",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
							ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
								GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
									Feature: onboarding.Feature_FEATURE_SA.String(),
								},
							},
							ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&onbScreenOpts.GetNextOnbActionScreenOptions{
								Feature:         onboarding.Feature_FEATURE_SA.String(),
								BottomInfoCards: onbPkg.GenerateIconTextComponents(),
							}),
						},
						Status: deeplink.Cta_CTA_STATUS_ENABLED,
					},
				},
			},
		},
	}
}

// nolint: dupl, funlen
func getZeroStateForNRODashboard() *feHomePb.GetFiAccountDashboardResponse {
	var dl *deeplink.Deeplink
	// TODO : populate dl after getting deeplink for NRO onboarding
	nroZeroStateFooterTickerItem := getFooterTickerItemForNroZeroState()
	footerTicker := &feHomePb.HomeDashboard_FooterTicker{
		TickerItems:        []*feHomePb.HomeDashboard_FooterTicker_TickerItem{nroZeroStateFooterTickerItem},
		TickerItemsPrivacy: []*feHomePb.HomeDashboard_FooterTicker_TickerItem{nroZeroStateFooterTickerItem},
	}
	return &feHomePb.GetFiAccountDashboardResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		DashboardInfo: &feHomePb.HomeDashboard{
			Title: commontypes.GetTextFromStringFontColourFontStyle("NRO Savings Account", "#6A6D70", commontypes.FontStyle_SUBTITLE_S),
			Body: &feHomePb.HomeDashboard_Body{
				DashboardState: feHomePb.HomeDashboard_Body_STATE_ZERO,
				DashboardIcons: []*feHomePb.Icon{
					{
						Action: &feHomePb.Icon_Deeplink{
							Deeplink: dl,
						},
						IconType:   homeTypesPb.IconType_DASHBOARD_ACTION,
						ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
						Title: &commontypes.Text{
							FontColor: "#00B899",
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Open NRO account",
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
							},
						},
						BgColour:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#28292B"}},
						BgColourV2: widget.GetBlockBackgroundColour("#28292B"),
					},
				},
			},
			Deeplink:            dl,
			Shadow:              ui.GetDashboardShadow(),
			HideSettingsIcon:    true,
			FooterTicker:        footerTicker,
			DashboardFooter:     &feHomePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
			DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
			BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
		},
	}
}

// nolint: dupl
func getFiAccountZeroState(ctx context.Context, onbStatus onboarding.FeatureStatus) (*feHomePb.GetFiAccountDashboardResponse, error) {
	dl, err := onbPkg.GetSABenefitsScreen(ctx)
	if err != nil {
		return nil, err
	}

	failureDL := &deeplink.Deeplink{
		Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{
				Title:       "We are unable to create a savings account for you",
				Subtitle:    "Unfortunately, we are unable to create a savings account for you at this time. You can come back and try again in 3 months.",
				ImageUrl:    "https://epifi-icons.pointz.in/onboarding/door_with_nails.png",
				HasFeedback: true,
				Ctas: []*deeplink.Cta{
					{
						Type: deeplink.Cta_CUSTOM,
						Text: "Know more",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
							ScreenOptions: &deeplink.Deeplink_GetNextOnboardingActionScreenOptions{
								GetNextOnboardingActionScreenOptions: &deeplink.GetNextOnboardingActionScreenOptions{
									Feature: onboarding.Feature_FEATURE_SA.String(),
								},
							},
							ScreenOptionsV2: deeplinkV3.GetScreenOptionV2WithoutError(&onbScreenOpts.GetNextOnbActionScreenOptions{
								Feature:         onboarding.Feature_FEATURE_SA.String(),
								BottomInfoCards: onbPkg.GenerateIconTextComponents(),
							}),
						},
						Status: deeplink.Cta_CTA_STATUS_ENABLED,
					},
				},
			},
		},
	}

	switch onbStatus {
	case onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS:
		return &feHomePb.GetFiAccountDashboardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			DashboardInfo: &feHomePb.HomeDashboard{
				Title: commontypes.GetTextFromStringFontColourFontStyle("Federal Bank Savings A/C", "#878A8D", commontypes.FontStyle_SUBTITLE_S),
				Body: &feHomePb.HomeDashboard_Body{
					DashboardState: feHomePb.HomeDashboard_Body_STATE_ZERO,
					MoneyValueV2: []*ui.IconTextComponent{
						{
							Texts: []*commontypes.Text{
								commontypes.GetTextFromStringFontColourFontStyle("Your account opening is \ntaking time ⏱️", "#FFFFFF", commontypes.FontStyle_HEADLINE_M),
							},
							Deeplink: dl,
						},
					},
				},
				Footer: []*ui.IconTextComponent{
					{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("We’ll notify you when its ready", "#606265", commontypes.FontStyle_SUBTITLE_S),
						},
					},
				},
				Deeplink:            dl,
				Shadow:              ui.GetDashboardShadow(),
				ZeroStateImage:      commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/bank.png"),
				HideSettingsIcon:    true,
				DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
				BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
			},
			Balance: types.GetFromBeMoney(&money.Money{CurrencyCode: "INR", Units: 0}),
		}, nil
	case onboarding.FeatureStatus_FEATURE_STATUS_ONBOARDING_FAILURE:
		return &feHomePb.GetFiAccountDashboardResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			DashboardInfo: &feHomePb.HomeDashboard{
				Title: commontypes.GetTextFromStringFontColourFontStyle("Federal Bank Savings A/C", "#878A8D", commontypes.FontStyle_SUBTITLE_S),
				Body: &feHomePb.HomeDashboard_Body{
					DashboardState: feHomePb.HomeDashboard_Body_STATE_ZERO,
					MoneyValueV2: []*ui.IconTextComponent{
						{
							Texts: []*commontypes.Text{
								commontypes.GetTextFromStringFontColourFontStyle("Your account opening \nis paused ⏸️ ", "#FFFFFF", commontypes.FontStyle_HEADLINE_M),
							},
							Deeplink: failureDL,
						},
					},
				},
				Footer: []*ui.IconTextComponent{
					{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Know more", FiGreen, commontypes.FontStyle_SUBTITLE_S),
						},
						RightIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"),
						RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"),
					},
				},
				Deeplink:            failureDL,
				Shadow:              ui.GetDashboardShadow(),
				ZeroStateImage:      commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/bank.png"),
				HideSettingsIcon:    true,
				DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
				BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
			},
			Balance: types.GetFromBeMoney(&money.Money{CurrencyCode: "INR", Units: 0}),
		}, nil
	}

	return &feHomePb.GetFiAccountDashboardResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		DashboardInfo: &feHomePb.HomeDashboard{
			Title: commontypes.GetTextFromStringFontColourFontStyle("Federal Bank Savings A/C", "#878A8D", commontypes.FontStyle_SUBTITLE_S),
			Body: &feHomePb.HomeDashboard_Body{
				DashboardState: feHomePb.HomeDashboard_Body_STATE_ZERO,
				MoneyValueV2: []*ui.IconTextComponent{
					{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle("Get 2% back on UPI & \nDebit Card spends ", "#FFFFFF", commontypes.FontStyle_HEADLINE_M),
						},
						Deeplink: dl,
					},
				},
			},
			Footer: []*ui.IconTextComponent{
				{
					Texts: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle("Open a Savings Account", FiGreen, commontypes.FontStyle_SUBTITLE_S),
					},
					RightIcon:          commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"),
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"),
				},
			},
			Deeplink:            dl,
			Shadow:              ui.GetDashboardShadow(),
			ZeroStateImage:      commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/lite/bank.png"),
			HideSettingsIcon:    true,
			DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
			BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
		},
		Balance: types.GetFromBeMoney(&money.Money{CurrencyCode: "INR", Units: 0}),
	}, nil
}

// nolint: dupl
func (s *Service) GetHomeAppWalkThroughFlows(ctx context.Context, req *feHomePb.GetHomeAppWalkThroughFlowsRequest) (*feHomePb.GetHomeAppWalkThroughFlowsResponse, error) {

	var (
		oldUserOnboadingWalkthrough = &feHomePb.OnboardingWalkthrough{
			Enabled: true,
			OnboardingWalkThrough: []*feHomePb.WalkThroughPopUp{
				{
					Message:   DashboardPopupMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_DASHBOARD_VIEW,
				},
				{
					Message:   FitPopupMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_FIT_RULES,
				},
				{
					Message:   InvestPopupMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_SAVE_AND_INVEST,
				},
				{
					Message:   NudgePopupMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_NUDGE,
				},
			},
		}

		newUserOnboardingWalkthrough = &feHomePb.OnboardingWalkthrough{
			Enabled: true,
			OnboardingWalkThrough: []*feHomePb.WalkThroughPopUp{
				{
					Message:   NudgePopupMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_NUDGE,
				},
			},
		}
		privacySettingWalkthrough = &feHomePb.PrivacySettingWalkthrough{
			Enabled: true,
			PrivacySettingWalkThrough: []*feHomePb.WalkThroughPopUp{
				{
					Message:   AdditionalSettingsMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_ADDITIONAL_SETTINGS,
				},
				{
					Message:   HideShowBalanceMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_HIDE_UN_HIDE,
				},
			},
		}
		shortcutsWalkthrough = &feHomePb.HomeShortcutsWalkthrough{
			Enabled: true,
			HomeShortcutsWalkThrough: []*feHomePb.WalkThroughPopUp{
				{
					Message:   AddActionsMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_HOME_SHORTCUTS,
					InfoButton: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							{
								FontColor: "#00B899",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: NextBtnText,
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
							},
						},
					},
				},
				{
					Message:   PersonaliseShortcutsMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_ADD_AND_MANAGE_SHORTCUTS,
					InfoButton: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							{
								FontColor: "#00B899",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: NextBtnText,
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
							},
						},
					},
				},
				{
					Message:   ReorderMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_REORDER_SHORTCUTS,
					InfoButton: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							{
								FontColor: "#00B899",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: NextBtnText,
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
							},
						},
					},
				},
				{
					Message:   AddShortcutsMsg,
					PopUpType: feHomePb.WalkThroughPopUp_POP_UP_TYPE_ADD_SHORTCUTS,
					InfoButton: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							{
								FontColor: "#00B899",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: OkGotItBtnText,
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
							},
						},
					},
				},
			},
		}
		res                       = &feHomePb.GetHomeAppWalkThroughFlowsResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()}}
		showNewUserOnbWalkthrough = true
		actorId                   = req.GetReq().GetAuth().GetActorId()
	)

	// Based on onboarding timestamp, show old or new user onboarding flow
	onbRes, err := s.onboardingClient.GetDetails(ctx, &onboarding.GetDetailsRequest{ActorId: req.GetReq().GetAuth().GetActorId(), CachedData: true})
	if err = epifigrpc.RPCError(onbRes, err); err != nil {
		logger.Error(ctx, "failed to get details rpc", zap.Error(err))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}

	// Days since onboarding
	onboardingTime := onbRes.GetDetails().GetCompletedAt().AsTime()
	currentTime := time.Now()
	minutesSinceOnboarding := currentTime.Sub(onboardingTime).Minutes()

	if minutesSinceOnboarding > s.conf.HomeRevampParams.MaxDurationSinceOnbForNewAppWalkthrough.Minutes() {
		showNewUserOnbWalkthrough = false
	}

	// Get balance of user, then show hide/show balance flow
	entityRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(entityRes, err); err != nil {
		logger.Error(ctx, "failed to get actor by Id", zap.Error(err))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}

	actorName := entityRes.GetName()

	startWalkthroughTitle := fmt.Sprintf(OldUserWalkThroughTitle, actorName.GetFirstName())
	getStartedTitle := "Get a tour"
	if showNewUserOnbWalkthrough {
		res.OnboardingWalkthrough = newUserOnboardingWalkthrough
		startWalkthroughTitle = fmt.Sprintf(NewUserWalkThroughTitle, actorName.GetFirstName())
		res.OnboardingWalkthrough.WalkThroughType = feHomePb.OnboardingWalkthrough_TYPE_NEW_USER_WALK_THROUGH
		getStartedTitle = "Get started"
	} else {
		res.OnboardingWalkthrough = oldUserOnboadingWalkthrough
		res.OnboardingWalkthrough.WalkThroughType = feHomePb.OnboardingWalkthrough_TYPE_OLD_USER_WALK_THROUGH
		if s.isShortcutsWalkthroughRequired(ctx, req, actorId) {
			res.ShortcutsWalkthrough = shortcutsWalkthrough
			res.ShortcutsIntroCardDetails = getShortcutsIntroCardDetails()
		}
	}

	res.PrivacySettingWalkthrough = privacySettingWalkthrough

	res.DashboardIntroCardDetails = &feHomePb.DashboardIntroCardDetails{
		StartIcon: &feHomePb.Icon{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: getStartedTitle},
				FontColor:    FiGreen,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Ink}},
			IconImage:     &commontypes.Image{ImageUrl: homeConstants.DashboardIntroCardGettingStartedIconOnStartBtn},
			VisualElement: commontypes.GetVisualElementImageFromUrl(homeConstants.DashboardIntroCardGettingStartedIconOnStartBtn),
		},
		ShowCardMaxImpressions: MaxWalkThroughImpressionsPerUser,
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: startWalkthroughTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
		},
		Image:  &commontypes.Image{ImageUrl: homeConstants.WaveIconForOnboardingIntroTour},
		Shadow: ui.GetDashboardShadow(),
	}
	return res, nil
}

func (s *Service) isShortcutsWalkthroughRequired(ctx context.Context, req *feHomePb.GetHomeAppWalkThroughFlowsRequest, actorId string) bool {
	var homeConf = deepcopy.Copy(s.conf.HomeRevampParams).(*config.HomeRevampParams)
	if isLiteUser, _, errLite := isFiLiteUser(ctx, s.onboardingClient, req.GetReq().GetAuth().GetActorId()); errLite == nil && isLiteUser {
		homeConf = deepcopy.Copy(s.conf.LiteHomeRevampParams).(*config.HomeRevampParams)
		// Early returning false for fi lite users. Shortcuts walkthrough would not be required
		return false
	}
	bottomWidgetList := s.computeHomeLayoutConfigurationAndGetBottomWidgetList(ctx, homeConf, actorId)
	for _, widgetType := range bottomWidgetList {
		if widgetType == feHomePb.HomeWidget_WIDGET_TYPE_SHORTCUTS && isDevicePlatformVersionValidForHomeShortcuts(ctx, homeConf.HomeShortcutParams.PlatformVersionDetails) {
			return true
		}
	}
	return false
}

// nolint: dupl
func getShortcutsIntroCardDetails() *feHomePb.DashboardIntroCardDetails {
	return &feHomePb.DashboardIntroCardDetails{
		StartIcon: &feHomePb.Icon{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: SeeHowMsg},
				FontColor:    FiGreen,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Ink}},
			IconImage:     &commontypes.Image{ImageUrl: homeConstants.DashboardIntroCardGettingStartedIconOnStartBtn},
			VisualElement: commontypes.GetVisualElementImageFromUrl(homeConstants.DashboardIntroCardGettingStartedIconOnStartBtn),
		},
		ShowCardMaxImpressions: MaxWalkThroughImpressionsPerUser,
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: ShortcutsWalkthroughTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
		},
		Image:  &commontypes.Image{ImageUrl: homeConstants.LegoIconForHomeShortcutsOnDashboardIntroCard},
		Shadow: ui.GetDashboardShadow(),
	}
}

func (s *Service) getHomeProfileInfoForWealthAnalyserUser(ctx context.Context, actorId string) (*feHomePb.GetHomeProfileInfoResponse, error) {
	userProfileRes, err := s.usersClient.GetUserProfile(ctx, &userPb.GetUserProfileRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(userProfileRes, err); err != nil {
		logger.Error(ctx, "failed to get user profile from user service", zap.Error(err))
		return &feHomePb.GetHomeProfileInfoResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			},
		}, nil
	}

	return &feHomePb.GetHomeProfileInfoResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ProfileImage: &commontypes.Image{
			ImageUrl: userProfileRes.GetImage().GetImageUrl(),
		},
		UserName:    &commontypes.Name{FirstName: userProfileRes.GetDisplayName()},
		BorderColor: feHomePb.GetHomeWidgetBorderColor(),
	}, nil
}

// nolint:unparam
func (s *Service) getHomeProfileInfoForNoAssetConnectedWealthAnalyserUser(ctx context.Context, actorId string) (*feHomePb.GetHomeProfileInfoResponse, error) {
	userProfileRes, err := s.usersClient.GetUserProfile(ctx, &userPb.GetUserProfileRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(userProfileRes, err); err != nil {
		logger.Error(ctx, "failed to get user profile from user service", zap.Error(err))
		return &feHomePb.GetHomeProfileInfoResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			},
		}, nil
	}

	homeProfileResp := &feHomePb.GetHomeProfileInfoResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ProfileImage: &commontypes.Image{
			ImageUrl: userProfileRes.GetImage().GetImageUrl(),
		},
		UserName:    &commontypes.Name{FirstName: userProfileRes.GetDisplayName()},
		BorderColor: feHomePb.GetHomeWidgetBorderColor(),
	}
	actorLendabilityResp, err := s.lendabilityClient.GetActorLendability(ctx, &lendabilityPb.GetActorLendabilityRequest{
		ActorId: actorId,
		Flow:    lendabilityPb.Flow_FLOW_WEALTH_BUILDER,
	})
	if rpcErr := epifigrpc.RPCError(actorLendabilityResp, err); rpcErr != nil {
		logger.Error(ctx, "error in getting actor lendability", zap.Error(rpcErr))
		// not handling error here, will be showing loan notch if PdCategory data is missing
	}
	pdCategory := actorLendabilityResp.GetLendabilityDetails().GetPdCategory()

	if !lo.Contains(pdCategoryForHomeProfileLoanNotch, pdCategory) {
		return homeProfileResp, nil
	}

	// added loan profile notch
	homeProfileResp.ProfileNudge = &feHomePb.GetHomeProfileInfoResponse_ProfileNudge{
		ExpandedIcon: &feHomePb.Icon{
			VisualElement: commontypes.GetVisualElementImageFromUrl(promptChevronV2Image.GetImageUrl()),
			ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
			BgColour: &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{RadialGradient: &ui.RadialGradient{
				Center: &ui.CenterCoordinates{
					CenterX: 0,
					CenterY: 0,
				},
				OuterRadius: 100,
				Colours:     []string{"#0054BE", "#A6D9E9"},
			}}},
			Title: commontypes.GetTextFromStringFontColourFontStyle("Get instant cash up to 5L", Snow, commontypes.FontStyle_SUBTITLE_XS),
			Action: getV2StatesActionDeeplink(&deeplink.Deeplink{
				Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			}),
		},
		UiVariant:   feHomePb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
		BorderColor: feHomePb.GetHomeWidgetBorderColor(),
	}
	return homeProfileResp, nil
}

// nolint:dupl
func (s *Service) getHomeProfileInfoForLiteUser(ctx context.Context, actorId string) (*feHomePb.GetHomeProfileInfoResponse, error) {

	dl, errDL := onbPkg.GetSABenefitsScreen(ctx)
	if errDL != nil {
		logger.Error(ctx, "failed to get SA Benefit Screen", zap.Error(errDL))
		return &feHomePb.GetHomeProfileInfoResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(errDL.Error()),
			},
		}, nil
	}

	userProfileRes, err := s.usersClient.GetUserProfile(ctx, &userPb.GetUserProfileRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(userProfileRes, err); err != nil {
		logger.Error(ctx, "failed to get user profile from user service", zap.Error(err))
		return &feHomePb.GetHomeProfileInfoResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			},
		}, nil
	}

	userType, getDetailsRes, _, err := homePkg.GetSuitableUserType(ctx, &homePkg.GetSuitableUserTypeRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.evaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	})
	if err != nil {
		logger.Error(ctx, "failed to get suitable user type in getHomeProfileInfoForLiteUser", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &feHomePb.GetHomeProfileInfoResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("failed to get user type: %v", err)),
			},
		}, nil
	}

	return &feHomePb.GetHomeProfileInfoResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ProfileImage: &commontypes.Image{
			ImageUrl: userProfileRes.GetImage().GetImageUrl(),
		},
		ProfileNudge: &feHomePb.GetHomeProfileInfoResponse_ProfileNudge{
			ExpandedIcon: &feHomePb.Icon{
				IconImage: &commontypes.Image{
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png",
					Height:   24,
					Width:    24,
				},
				VisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source:     &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"},
							Properties: &commontypes.VisualElementProperties{Height: 24, Width: 24},
						},
					},
				},
				Title: getFiLiteProfileTitle(userType, getDetailsRes),
				Action: &feHomePb.Icon_Deeplink{
					Deeplink: dl,
				},
				IconType:   homeTypesPb.IconType_PROFILE,
				ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
				BgColour:   ui.GetBlockColor("#313234"),
			},
			UiVariant:   feHomePb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
			BorderColor: feHomePb.GetHomeWidgetBorderColor(),
		},
		UserName:    &commontypes.Name{FirstName: userProfileRes.GetDisplayName()},
		BorderColor: feHomePb.GetHomeWidgetBorderColor(),
	}, nil
}

func getFiLiteProfileTitle(userType homePkg.UserType, getDetailsRes *onboarding.GetDetailsResponse) *commontypes.Text {
	var (
		profileTitle = "Upgrade your account"
		fontColor    = "#C0DAE0"
	)

	// get the days since onboarding
	currentTime := time.Now()
	onboardingTime := getDetailsRes.GetDetails().GetStageDetails().GetStageMapping()[onboarding.OnboardingStage_SOFT_INTENT_SELECTION.String()].GetStartedAt().AsTime()
	daysSinceOnboarding := currentTime.Sub(onboardingTime).Hours() / 24

	// if user is pure fi-lite and onboarded less than 2 days, show below profile title
	if userType == homePkg.UserTypeFiLite && daysSinceOnboarding < 2 {
		profileTitle = "Launch offer:\nFree Trial"
		fontColor = "#9DC2D0"
	}

	return commontypes.GetTextFromStringFontColourFontStyle(profileTitle, fontColor, commontypes.FontStyle_SUBTITLE_S)
}

// nolint: govet
func (s *Service) GetHomeProfileInfo(ctx context.Context, req *feHomePb.GetHomeProfileInfoRequest) (*feHomePb.GetHomeProfileInfoResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
		res     = &feHomePb.GetHomeProfileInfoResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()}}
	)
	userType, _, _, err := homePkg.GetSuitableUserType(ctx, &homePkg.GetSuitableUserTypeRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.evaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	})
	if err != nil {
		logger.Error(ctx, "failed to check if user suitable type", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return &feHomePb.GetHomeProfileInfoResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			},
		}, nil
	}
	// If user is wealth analyser user, show wealth analyser profile info
	switch userType {
	case homePkg.UserTypeWealthAnalyser:
		return s.getHomeProfileInfoForWealthAnalyserUser(ctx, actorId)
	case homePkg.UserTypeNoAssetConnectedWealthAnalyser:
		return s.getHomeProfileInfoForNoAssetConnectedWealthAnalyserUser(ctx, actorId)
	case homePkg.UserTypeFiLitePL, homePkg.UserTypeFiLiteCC, homePkg.UserTypeFiLite:
		return s.getHomeProfileInfoForLiteUser(ctx, actorId)
	}

	// updating username and profile image in response
	userProfileRes, err := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
		WantProfileImageUrl: true,
	})
	if err = epifigrpc.RPCError(userProfileRes, err); err != nil {
		logger.Error(ctx, "failed to get user profile from user service", zap.Error(err))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, err
	}
	res.UserName = gammanames.BestNameFromProfile(ctx, userProfileRes.GetUser().GetProfile())
	res.ProfileImage = &commontypes.Image{
		ImageUrl: userProfileRes.GetUser().GetProfile().GetProfileImageUrl(),
	}
	res.BorderColor = feHomePb.GetHomeWidgetBorderColor()

	updatedCtxWithTimeout, cancel := context.WithTimeout(ctx, 3*time.Second)
	defer cancel()

	// todo: remove GetConfigParams and remove gatherTieringData method call after app adoption
	configParamsResp, getConfigErr := s.beTieringClient.GetConfigParams(updatedCtxWithTimeout, &beTieringPb.GetConfigParamsRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(configParamsResp, getConfigErr); rpcErr != nil {
		logger.Error(ctx, "failed to get config params", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
	}

	var badge *commontypes.Image
	var promptIcon *feHomePb.Icon
	var promptTitle *commontypes.Text
	if configParamsResp.GetIsMultipleWaysToEnterTieringEnabledForActor() {
		badge, promptIcon, promptTitle, err = s.getNotchComponents(updatedCtxWithTimeout, actorId)
		if err != nil {
			logger.Error(ctx, "failed to get notch components", zap.Error(err))
			// returning here as notch components are not critical for profile load
			// res already has username and profile image
			return res, nil
		}
	} else {
		// Fetching tiering data in parallel
		// todo: remove this else condition after app adoption
		badge, promptIcon, promptTitle, err = s.gatherTieringData(updatedCtxWithTimeout, req)
		if err != nil {
			logger.Error(ctx, "failed to gather tiering data", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			// returning here as tiering data is not critical for profile load
			// res already has username and profile image
			return res, nil
		}
	}

	res.Badge = badge
	promptIcon.Title = promptTitle
	if promptTitle != nil {
		res.ProfileNudge = &feHomePb.GetHomeProfileInfoResponse_ProfileNudge{
			ExpandedIcon: promptIcon,
			UiVariant:    feHomePb.GetHomeProfileInfoResponse_ProfileNudge_PROFILE_NUDGE_UI_VARIANT_STANDALONE,
			BorderColor:  feHomePb.GetHomeWidgetBorderColor(),
		}
	}
	return res, nil
}

// Deprecated: use getNotchComponents instead
// using this function for backward compatibility for older client versions
func (s *Service) gatherTieringData(ctx context.Context, req *feHomePb.GetHomeProfileInfoRequest) (*commontypes.Image, *feHomePb.Icon, *commontypes.Text, error) {
	gatherTieringDataErrGrp, _ := errgroup.WithContext(ctx)
	var (
		tieringPitchResp *beTieringPb.GetTieringPitchV2Response
		actorId          = req.GetReq().GetAuth().GetActorId()
		err              error
	)
	gatherTieringDataErrGrp.Go(func() error {
		var err error
		tieringPitchResp, err = s.beTieringClient.GetTieringPitchV2(ctx, &beTieringPb.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if tieringPitchErr := epifigrpc.RPCError(tieringPitchResp, err); tieringPitchErr != nil {
			return errors.Wrap(tieringPitchErr, "error fetching tiering pitch details")
		}
		return nil
	})

	var tieringConfigParamsResp *beTieringPb.GetConfigParamsResponse
	gatherTieringDataErrGrp.Go(func() error {
		var err error
		tieringConfigParamsResp, err = s.beTieringClient.GetConfigParams(ctx, &beTieringPb.GetConfigParamsRequest{ActorId: actorId})
		if tieringConfigErr := epifigrpc.RPCError(tieringConfigParamsResp, err); tieringConfigErr != nil {
			return errors.Wrap(tieringConfigErr, "error fetching tiering config params")
		}
		return nil
	})

	var curBalance *money.Money
	gatherTieringDataErrGrp.Go(func() error {
		// Get Actor by actorId
		actorResp, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
		if actorErr := epifigrpc.RPCError(actorResp, err); actorErr != nil {
			return errors.Wrap(actorErr, "error fetching actor by id")
		}
		// Get savings account by user id
		savingsAccResp, savingsAccErr := s.savClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: actorResp.GetActor().GetEntityId()}})
		if savingsAccErr != nil || savingsAccResp == nil || savingsAccResp.GetAccount() == nil {
			return errors.Wrap(savingsAccErr, "error fetching savings account by user id")
		}
		historicalBalResp, rpcErr := s.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
			Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: savingsAccResp.GetAccount().GetId()},
			ActorId:       actorId,
			DataFreshness: paySavingsEnums.DataFreshness_HISTORICAL,
		})
		if accBalErr := epifigrpc.RPCError(historicalBalResp, rpcErr); accBalErr != nil {
			return errors.Wrap(accBalErr, "error fetching account balance")
		}
		curBalance = historicalBalResp.GetAvailableBalance()
		return nil
	})

	var employmentType employment.EmploymentType
	gatherTieringDataErrGrp.Go(func() error {
		var err error
		employmentInfoReps, err := s.employmentClient.GetEmploymentInfo(ctx, &employment.GetEmploymentInfoRequest{
			ActorId: actorId,
		})
		if employmentInfoErr := epifigrpc.RPCError(employmentInfoReps, err); employmentInfoErr != nil {
			if employmentInfoReps.GetStatus().IsRecordNotFound() {
				logger.WarnWithCtx(ctx, "employment info not found for the actor", zap.String(logger.ACTOR_ID_V2, actorId))
				return nil
			}
			return errors.Wrap(employmentInfoErr, "error getting employment info")
		}
		employmentType = employmentInfoReps.GetEmploymentData().GetEmploymentType()
		return nil
	})

	var (
		bankCustInfo                *bankcust.GetBankCustomerResponse
		salaryRegistrationCompleted bool
		salaryActive                bool
		kycLevel                    kyc.KYCLevel
	)
	gatherTieringDataErrGrp.Go(func() error {
		bankCustInfo, err = s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankcust.GetBankCustomerRequest_ActorId{ActorId: actorId},
		})
		if rpcErr := epifigrpc.RPCError(bankCustInfo, err); rpcErr != nil {
			logger.Error(ctx, "failed to get bankcustomer info for user", zap.Error(rpcErr))
			// This case was being sent as internal previously, please handle accordingly if
			return errors.Wrap(rpcErr, "failed to get bankcustomer info for user")
		}

		kycLevel = bankCustInfo.GetBankCustomer().GetDedupeInfo().GetKycLevel()

		if kycLevel == kyc.KYCLevel_FULL_KYC {
			salRegRes, err := s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &salaryPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actorId,
				FlowType: salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE})
			if rpcErr := epifigrpc.RPCError(salRegRes, err); rpcErr != nil {
				logger.Error(ctx, "failed to get sal registration status GetCurrentRegStatusAndNextRegStage", zap.Error(rpcErr))
				// This case was being sent as internal previously, please handle accordingly if
				return errors.Wrapf(rpcErr, "failed to get sal registration status GetCurrentRegStatusAndNextRegStage")
			}

			salaryRegistrationCompleted = salRegRes.GetRegistrationStatus() == salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED

			if salRegRes.GetRegistrationStatus() == salaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
				salActiveRes, err := s.salaryProgramClient.GetLatestActivationDetailsActiveAtTime(ctx, &salaryPb.LatestActivationDetailsActiveAtTimeRequest{
					RegistrationId: salRegRes.GetRegistrationId(),
					ActiveAtTime:   timestamppb.Now(),
					// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user.
					ActivationKind: salaryPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
				})
				switch {
				case salActiveRes.GetStatus().IsRecordNotFound() || salActiveRes.GetStatus().IsSuccess():
					salaryActive = salActiveRes.GetStatus().IsSuccess()
				case epifigrpc.RPCError(salActiveRes, err) != nil:
					logger.Error(ctx, "failed to get sal activation status GetLatestActivationDetailsActiveAtTime", zap.Error(err))
					// This case was being sent as internal previously, please handle accordingly if
					return errors.Wrap(err, "failed to get sal activation status GetLatestActivationDetailsActiveAtTime")
				}
			}
		}
		return nil
	})

	activeNotchCampaigns := s.getActiveCampaigns()
	activeCampaignSegmentIds := getSegmentIdsFromCampaignDetails(activeNotchCampaigns)
	var campaignSegmentResp *segmentPb.IsMemberResponse
	gatherTieringDataErrGrp.Go(func() error {
		if len(activeCampaignSegmentIds) == 0 {
			return nil
		}

		var segmentMemberErr error
		campaignSegmentResp, segmentMemberErr = s.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: activeCampaignSegmentIds,
			LatestBy:   timestamppb.Now(),
		})
		if rpcErr := epifigrpc.RPCError(campaignSegmentResp, segmentMemberErr); rpcErr != nil {
			return fmt.Errorf("segmentClient.IsMember Rpc failed, %w", rpcErr)
		}

		return nil
	})

	var isTieringEarnedBenefitsEnabled bool
	gatherTieringDataErrGrp.Go(func() error {
		var evalErr error
		releaseConstraint := release.NewCommonConstraintData(types.Feature_TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH).WithActorId(actorId)
		isTieringEarnedBenefitsEnabled, evalErr = s.evaluator.Evaluate(ctx, releaseConstraint)
		if evalErr != nil {
			return fmt.Errorf("failed to evaluate TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH feature, %w", evalErr)
		}
		return nil
	})

	gatherTieringDataErr := gatherTieringDataErrGrp.Wait()

	isNrAccountRes, isNrAccountResErr := s.userAttributeFetcher.IsNonResidentUser(ctx, &pkgUser.IsNonResidentUserRequest{
		ActorId: actorId,
	})
	if isNrAccountResErr != nil {
		logger.Error(ctx, "error in IsNonResidentUser", zap.Error(isNrAccountResErr))
		// This case was being sent as internal previously, please handle accordingly if
		return nil, nil, nil, fmt.Errorf("error in IsNonResidentUser, %w", isNrAccountResErr)
	}

	isTieringEnabled := false
	isTieringV2StatesEnabled := s.genconf.HomeRevampParams().HomeNudgeParams().TieringParams().ToUseV2States()
	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := req.GetReq().GetAppVersionCode()
	isTieringEnabledFromEvaluator, tieringReleaseCheckErr := s.feTieringReleaseEvaluator.IsTieringEnabledForActor(tieringPitchResp,
		appVersion, appPlatform)
	tieringV2StatesResp, tieringV2StatesErr := s.getV2StatesResp(employmentType, curBalance, bankCustInfo, tieringPitchResp)
	switch {
	case isNrAccountRes.GetIsNonResidentUser():
		isTieringEnabled = false
	case tieringReleaseCheckErr != nil:
		logger.Error(ctx, "error in FE tiering release evaluator", zap.Error(tieringReleaseCheckErr),
			zap.String(logger.ACTOR_ID_V2, actorId))
	case gatherTieringDataErr != nil:
		logger.Error(ctx, "error in fetching tiering pitch details", zap.Error(gatherTieringDataErr),
			zap.String(logger.ACTOR_ID_V2, actorId))
	case tieringV2StatesErr != nil || tieringV2StatesResp == nil:
		logger.Error(ctx, "error in fetching tiering v2 states", zap.Error(tieringV2StatesErr),
			zap.String(logger.ACTOR_ID_V2, actorId))
		isTieringV2StatesEnabled = false
	case isTieringEnabledFromEvaluator:
		isTieringEnabled = true
	default:
		isTieringEnabled = false
	}

	isTieringEarnedBenefitsEnabled = isTieringEarnedBenefitsEnabled && isTieringEnabled
	var (
		isUserDowngraded bool
		previousTier     beTieringExtPb.Tier

		toShowGraceInfo, isUserInCoolOff bool
		currentTier                      beTieringExtPb.Tier
	)
	if isTieringEnabled {
		var tieringInfoErr error
		isUserDowngraded, previousTier, toShowGraceInfo, currentTier, isUserInCoolOff, tieringInfoErr = s.getTieringInfoFromResp(tieringPitchResp, tieringConfigParamsResp)
		if tieringInfoErr != nil {
			logger.Error(ctx, "error in fetching tiering related info from tiering response", zap.Error(tieringInfoErr),
				zap.String(logger.ACTOR_ID_V2, actorId))
			// This case was being sent as internal previously, please handle accordingly if
			return nil, nil, nil, fmt.Errorf("error in fetching tiering related info from tiering response, %w", tieringInfoErr)
		}
		if isUserInCoolOff {
			logger.Info(ctx, "user is in cool off, skipping v2 states", zap.String(logger.ACTOR_ID_V2, actorId))
			isTieringV2StatesEnabled = false
		}
	}

	var (
		minKycBadge                 = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/MinKycBadge.png"}
		fullKycBadge                = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/FullKycBadge.png"}
		salaryAccountBadge          = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/SalaryBadge.png"}
		promptChevronImage          = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevron.png"}
		promptChevronDarkLemonImage = &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/PromptChevronDarkLemon.png"}
		promptTitle                 = &commontypes.Text{
			FontColor: Snow,
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
		}
	)

	promptIcon := &feHomePb.Icon{
		IconImage:     promptChevronImage,
		VisualElement: commontypes.GetVisualElementImageFromUrl(promptChevronImage.GetImageUrl()),
		Title:         &commontypes.Text{},
		ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
		BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Charcoal}},
	}
	badge := &commontypes.Image{}

	switch true {
	// Min kyc user
	case kycLevel == kyc.KYCLevel_MIN_KYC:
		profileExtRes, profileExtErr := s.vkycFeClient.GetProfileExtension(ctx, &vkyc.GetProfileExtensionRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(profileExtRes, profileExtErr); rpcErr != nil {
			if !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
				logger.Error(ctx, "error in getting profile extension from GetProfileExtension rpc", zap.Error(rpcErr))
			}
			return nil, nil, nil, fmt.Errorf("error in getting profile extension from GetProfileExtension rpc, %w", rpcErr)
		}
		badge = minKycBadge
		promptTitle.FontColor = UserIsMinKycColour
		promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: profileExtRes.GetTitle()}
		promptIcon.Action = &feHomePb.Icon_Deeplink{Deeplink: profileExtRes.GetDeeplink()}
		promptIcon.BgColour = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{
			BlockColour: MonochromeNight,
		}}
		promptIcon.IconImage = nil
		if profileExtRes.GetDeeplink() != nil {
			promptIcon.IconImage = &commontypes.Image{ImageUrl: promptChevronRavenSteel}
			promptIcon.VisualElement = commontypes.GetVisualElementImageFromUrl(promptChevronRavenSteel)
		}

	case kycLevel == kyc.KYCLevel_FULL_KYC:
		if !isTieringEnabled {
			switch {
			case salaryRegistrationCompleted == false:
				badge = fullKycBadge
				promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: FullKycNonSalaryRegPrompt1}
				promptIcon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_SALARY_PROGRAM_INTRO_SCREEN}}
			case salaryRegistrationCompleted == true && salaryActive == false:
				badge = fullKycBadge
				promptTitle.FontColor = Ink
				promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: FullKycSalaryInactivePrompt1}
				promptIcon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN}}
				promptIcon.BgColour = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: DarkLemon}}
				promptIcon.IconImage = promptChevronDarkLemonImage
				promptIcon.VisualElement = commontypes.GetVisualElementImageFromUrl(promptChevronDarkLemonImage.GetImageUrl())
			case salaryActive == true:
				badge = salaryAccountBadge
				promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: SalaryActivePrompt}
				promptIcon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN}}
			default:
				logger.Error(ctx, "user doesn't fall under any of the nudge categories")
				// This case was being sent as internal previously, please handle accordingly if
				return nil, nil, nil, fmt.Errorf("user doesn't fall under any of the nudge categories")
			}
		} else {
			if isTieringV2StatesEnabled {
				badge = tieringV2StatesResp.Badge
				promptTitle.FontColor = tieringV2StatesResp.FontColor
				promptTitle.DisplayValue = tieringV2StatesResp.DisplayValue
				promptIcon.Action = tieringV2StatesResp.Action
				promptIcon.BgColour = tieringV2StatesResp.BgColor
				promptIcon.IconImage = tieringV2StatesResp.IconImage
				promptIcon.VisualElement = commontypes.GetVisualElementImageFromUrl(tieringV2StatesResp.IconImage.GetImageUrl())
			} else {
				badge = getTieringPromptBadge(isUserDowngraded, toShowGraceInfo, previousTier, currentTier)
				toShowPrime := helper.ShouldPitchAaSalaryTier(ctx, s.genconf, currentTier)
				var promptDeeplink *deeplink.Deeplink
				promptTitle, promptDeeplink = s.getTieringPromptTitleAndDeeplink(ctx, actorId, toShowPrime, isUserDowngraded, toShowGraceInfo, previousTier, currentTier, isTieringEarnedBenefitsEnabled)
				promptIcon.Action = &feHomePb.Icon_Deeplink{
					Deeplink: promptDeeplink,
				}
				promptIcon.BgColour = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: MonochromeNight,
				}}
				promptIcon.IconImage = &commontypes.Image{ImageUrl: promptChevronRavenSteel}
				promptIcon.VisualElement = commontypes.GetVisualElementImageFromUrl(promptChevronRavenSteel)

				campaignNotchCopy, campaignNotchDl, getCampaignErr := s.getCampaignCopyAndDeeplinkForActor(activeCampaignSegmentIds, campaignSegmentResp.GetSegmentMembershipMap())
				if getCampaignErr != nil {
					return nil, nil, nil, fmt.Errorf("failed to get campaign copy and deeplink for actor, %w", getCampaignErr)
				}

				if campaignNotchCopy != "" && campaignNotchDl != nil {
					logger.Debug(ctx, "profile notch campaign", zap.String("notch copy", campaignNotchCopy), zap.Any(logger.ACTOR_ID_V2, actorId))
					promptTitle.DisplayValue = &commontypes.Text_PlainString{PlainString: campaignNotchCopy}
					promptIcon.Action = &feHomePb.Icon_Deeplink{Deeplink: campaignNotchDl}
				}
			}
		}
	}
	return badge, promptIcon, promptTitle, nil
}

func (s *Service) getIconResponseFromConfig(ctx context.Context, id string, icon *config.Icon, actorId string) (*feHomePb.Icon, error) {
	if len(icon.IconWithVersionConstraints) == 0 {
		return nil, fmt.Errorf("empty list of icon properties")
	}
	iconProperties := &config.IconWithVersionConstraints{}
	for _, iconVersion := range icon.IconWithVersionConstraints {
		// If the app version satisfies the constraint, those icon properties will be used
		if iconVersion.VersionConstraints == nil || cfg.IsFeatureEnabledOnPlatform(ctx, iconVersion.VersionConstraints) {
			iconProperties = iconVersion
			break
		}
	}
	res := &feHomePb.Icon{
		IconImage:            &commontypes.Image{ImageUrl: iconProperties.ImageUrl},
		IconImageOnSelection: &commontypes.Image{ImageUrl: iconProperties.OnclickImageUrl},
		Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: iconProperties.Title},
			FontColor: iconProperties.FontColour, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle(commontypes.FontStyle_value[iconProperties.FontStyle])}},
		IconType:      homeTypesPb.IconType(homeTypesPb.IconType_value[iconProperties.IconType]),
		VisualElement: commontypes.GetVisualElementImageFromUrl(iconProperties.ImageUrl),
	}
	if iconProperties.LottieUrl != "" {
		res.VisualElement = commontypes.GetVisualElementLottieFromUrl(iconProperties.LottieUrl).WithRepeatCount(1)
	}
	if iconProperties.VisualElementWidth != 0 {
		res.VisualElement = res.VisualElement.WithProperties(&commontypes.VisualElementProperties{Width: iconProperties.VisualElementWidth, Height: iconProperties.VisualElementHeight})
	}

	s.updateIconSpecificDetails(ctx, id, res, actorId, iconProperties)

	return res, nil
}

//nolint:funlen
func (s *Service) updateIconSpecificDetails(ctx context.Context, id string, icon *feHomePb.Icon, actorId string, iconProperties *config.IconWithVersionConstraints) {
	switch id {
	case "home-fcdcd9a6-7bc3-4635-aa8d-84726833ef1b":
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_HOME,
		}}
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
	case "pay-a8af0213-04ee-4273-93c5-8580644f81ef":
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_PAY_LANDING_SCREEN,
		}}
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
	case "invest-56f1d8b2-343c-48cd-8c2e-1f345eeb7807":
		// Client would need to invoke rpc for this
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_INVESTMENT_LANDING_SCREEN,
		}}
	case "borrow-956660e6-715e-48fe-a8b8-73c4d440f4e5":
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
	case "discover-c8c6e297-f1c6-4ed4-8dc7-c0275e16a3fb":
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_HOME_EXPLORE,
		}}
	case "qr_code-69c444b6-cd1e-4b2d-8be3-7d9b2e2a25bb":
		if feUpiPkg.IsMlKitQrEnabled(ctx, actorId, s.evaluator) {
			icon.Action = &feHomePb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_PAY_ML_KIT_QR_SCREEN,
				},
			}
		} else {
			icon.Action = &feHomePb.Icon_Deeplink{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_PAY_QR_SCREEN,
				},
			}
		}
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
		icon.BgColour = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: FiGreen}}
	case "savings_summary-68efba81-3bc3-4331-a49d-3835f04ab320":
		icon.Action = &feHomePb.Icon_DropDown{DropDown: nil}
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_TOGGLE_DROP_DOWN
	case "rewards-0466e522-336b-11ed-a261-0242ac120002":
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_MY_REWARDS_SCREEN,
		}}
	case "refer-636a6c3e-7a28-11ed-a1eb-0242ac120002":
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_REFERRALS_ELIGIBILITY_LANDING_SCREEN,
		}}
	case "analyser-088c699c-336b-11ed-a261-0242ac120002":
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_ANALYSER_SCREEN,
		}}
	case "notification-0d472404-336b-11ed-a261-0242ac120002":
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_NOTIFICATION_CENTER,
		}}
	case "profile-309f8e77-662a-4570-87e1-c3e17493e149":
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_PROFILE_SCREEN,
		}}
	case "card-dd4bd856-3a6a-11ed-a261-0242ac120002":
		icon.ActionType = feHomePb.Icon_ACTION_TYPE_DEEPLINK
		iconDeeplink := &deeplink.Deeplink{Screen: deeplink.Screen_CARD_HOME_SCREEN}

		ctxWithTimeout, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
		defer cancel()
		isUserEligibleForCC, userAlreadyHasCard, err := s.checkCreditCardEligibility(ctxWithTimeout, actorId)
		// in case of error we will fall back to debit card
		if err != nil {
			logger.Error(ctx, "error in checking cc eligibility", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		}
		if s.genconf.CreditCard().ShowCreditCardTabByDefaultFromCardTab() && (isUserEligibleForCC || userAlreadyHasCard) {
			iconDeeplink = &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
			}
			if isUserEligibleForCC {
				icon.VisualElement = commontypes.GetVisualElementLottieFromUrl(homeConstants.CCEligibleLottieUrl).WithRepeatCount(1)
				if iconProperties.VisualElementWidth != 0 {
					icon.VisualElement = icon.VisualElement.WithProperties(&commontypes.VisualElementProperties{Width: iconProperties.VisualElementWidth, Height: iconProperties.VisualElementHeight})
				}
			}
		}
		icon.Action = &feHomePb.Icon_Deeplink{Deeplink: iconDeeplink}
	}
}

// checkCreditCardEligibility checks if user is eligible for cc and if cc is already present with the user
func (s *Service) checkCreditCardEligibility(ctx context.Context, actorId string) (bool, bool, error) {
	fetchCCEligibilityRes, err := s.beFireflyClient.FetchCreditCardEligibility(ctx, &ffBePb.FetchCreditCardEligibilityRequest{
		ActorId: actorId,
		Vendor:  ffBeEnumsPb.Vendor_FEDERAL,
	})
	switch {
	case err != nil:
		return false, false, fmt.Errorf("error in fetching cc eligibility for the user %w", err)
	case fetchCCEligibilityRes.GetStatus().IsRecordNotFound():
		return false, false, nil
	case fetchCCEligibilityRes.GetStatus().IsAlreadyExists():
		return false, true, nil
	case !fetchCCEligibilityRes.GetStatus().IsSuccess():
		return false, false, fmt.Errorf("non success state in fetching cc eligibility %v", fetchCCEligibilityRes.GetStatus())
	default:
		return fetchCCEligibilityRes.GetIsUserCcEligible(), false, nil
	}
}

func (s *Service) GetSpacerComponent(widgetType feHomePb.HomeWidget_WidgetType, previousWidgetType feHomePb.HomeWidget_WidgetType, isFeatureHomeDesignEnhancementsEnabled bool) *components.Spacer {
	spacingValue := components.Spacing_SPACING_UNSPECIFIED
	switch widgetType {
	case feHomePb.HomeWidget_WIDGET_TYPE_HELP:
		spacingValue = s.evaluateSpacerComponent(widgetType, previousWidgetType)
	case feHomePb.HomeWidget_WIDGET_TYPE_RECENT_UPCOMING_ACTIVITIES,
		feHomePb.HomeWidget_WIDGET_TYPE_REWARDS, feHomePb.HomeWidget_WIDGET_TYPE_SALARY_ACCOUNT,
		feHomePb.HomeWidget_WIDGET_TYPE_INVEST_HOME_ELEMENT, feHomePb.HomeWidget_WIDGET_TYPE_ANALYSER_CARDS,
		feHomePb.HomeWidget_WIDGET_TYPE_SUGGESTED_FOR_YOU, feHomePb.HomeWidget_WIDGET_TYPE_CATALOG_OFFERS,
		feHomePb.HomeWidget_WIDGET_TYPE_CARD_OFFERS, feHomePb.HomeWidget_WIDGET_TYPE_PRIMARY_FEATURE,
		feHomePb.HomeWidget_WIDGET_TYPE_SECONDARY_FEATURE:
		spacingValue = components.Spacing_SPACING_XXL
	case feHomePb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER, feHomePb.HomeWidget_WIDGET_TYPE_SHORTCUTS,
		feHomePb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER_2:
		spacingValue = components.Spacing_SPACING_XL
	case feHomePb.HomeWidget_WIDGET_TYPE_SEARCH_BAR:
		spacingValue = components.Spacing_SPACING_L
	case feHomePb.HomeWidget_WIDGET_TYPE_REFERRAL:
		spacingValue = components.Spacing_SPACING_XXL
		if isFeatureHomeDesignEnhancementsEnabled {
			spacingValue = components.Spacing_SPACING_UNSPECIFIED
		}
	default:
		// no-op
	}
	// Search should have XL spacing for next component
	if previousWidgetType == feHomePb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER {
		spacingValue = components.Spacing_SPACING_XL
	}
	return &components.Spacer{
		SpacingValue: spacingValue,
		BgColour:     &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}},
	}
}

func (s *Service) GetTopSpacerForNewDesignEnhancements(widgetType feHomePb.HomeWidget_WidgetType) *components.Spacer {
	spacingValue := components.Spacing_SPACING_L
	switch widgetType {
	case feHomePb.HomeWidget_WIDGET_TYPE_REFERRAL, feHomePb.HomeWidget_WIDGET_TYPE_TRUST_MARKER:
		return &components.Spacer{
			SpacingValue: components.Spacing_SPACING_UNSPECIFIED,
		}
	case feHomePb.HomeWidget_WIDGET_TYPE_WEALTH_ANALYSER:
		return &components.Spacer{
			BgColour:     widget.GetBlockBackgroundColour("#E6E9ED"),
			SpacingValue: components.Spacing_SPACING_S,
		}
	}

	return &components.Spacer{
		SpacingValue: spacingValue,
		BgColour:     widget.GetBlockBackgroundColour(Snow),
		Separator: components.NewSeparator().
			WithBackgroundColor(widget.GetBlockBackgroundColour("#F6F9FD")).
			WithThickness(4),
	}
}

func (s *Service) GetBottomSpacerComponent(widgetType feHomePb.HomeWidget_WidgetType, isFeatureHomeDesignEnhancementsEnabled bool) *components.Spacer {
	if !isFeatureHomeDesignEnhancementsEnabled {
		return nil
	}

	switch widgetType {
	case feHomePb.HomeWidget_WIDGET_TYPE_TRUST_MARKER:
		return &components.Spacer{
			SpacingValue: components.Spacing_SPACING_UNSPECIFIED,
		}
	case feHomePb.HomeWidget_WIDGET_TYPE_REFERRAL:
		return &components.Spacer{
			SpacingValue: components.Spacing_SPACING_UNSPECIFIED,
		}
	case feHomePb.HomeWidget_WIDGET_TYPE_WEALTH_ANALYSER:
		return &components.Spacer{
			SpacingValue: components.Spacing_SPACING_S,
			BgColour:     widget.GetBlockBackgroundColour("#E6E9ED"),
		}
	default:
		return &components.Spacer{
			SpacingValue: components.Spacing_SPACING_L,
			BgColour:     widget.GetBlockBackgroundColour(Snow),
		}
	}
}

func (s *Service) GetTopSpacerComponent(positionOfScreenElement int, widgetType feHomePb.HomeWidget_WidgetType, isFeatureHomeDesignEnhancementsEnabled bool) *components.Spacer {
	if isFeatureHomeDesignEnhancementsEnabled {
		return s.GetTopSpacerForNewDesignEnhancements(widgetType)
	}

	spacingValue := verticalScreenElementPositionToSpacerMapping[positionOfScreenElement]

	switch {
	// For Wealth analyser widget if widget is at first position, then no need to add spacer as it will be added by default by widget implementation.
	case positionOfScreenElement == 1 && widgetType == feHomePb.HomeWidget_WIDGET_TYPE_WEALTH_ANALYSER:
		return &components.Spacer{
			SpacingValue: components.Spacing_SPACING_UNSPECIFIED,
		}
	case widgetType == feHomePb.HomeWidget_WIDGET_TYPE_WEALTH_ANALYSER:
		return &components.Spacer{
			BgColour:     widget.GetBlockBackgroundColour("#E6E9ED"),
			SpacingValue: components.Spacing_SPACING_S,
		}
	// For wealth builder networth dashboard, no need to add spacer
	case widgetType == feHomePb.HomeWidget_WIDGET_TYPE_WEALTH_BUILDER_NETWORTH_DASHBOARD:
		return &components.Spacer{
			SpacingValue: components.Spacing_SPACING_UNSPECIFIED,
		}
	// For Trust Marker widget, no need to add spacer
	case widgetType == feHomePb.HomeWidget_WIDGET_TYPE_TRUST_MARKER:
		return &components.Spacer{
			SpacingValue: components.Spacing_SPACING_UNSPECIFIED,
		}
	// if above spacing value is unspecified due to no of components are greater than 13,
	// update spacing value to XXL for all the components except for trust marker (which is handled above).
	case spacingValue == components.Spacing_SPACING_UNSPECIFIED:
		spacingValue = components.Spacing_SPACING_XXL
	}

	return &components.Spacer{
		SpacingValue: spacingValue,
		BgColour:     widget.GetBlockBackgroundColour("#E6E9ED"),
	}
}

func UpdateHomeWidgetParamsByType(ctx context.Context, homeConf *config.HomeRevampParams, widgetType feHomePb.HomeWidget_WidgetType, widgetParams *config.HomeWidgetParams, iconIdMapping map[string]*feHomePb.Icon, recentWidgetParams *config.RecentUpcomingWidgetParams, isFeatureHomeDesignEnhancementsEnabled bool) *feHomePb.HomeWidget {

	res := &feHomePb.HomeWidget{WidgetType: widgetType}

	switch widgetType {
	case feHomePb.HomeWidget_WIDGET_TYPE_MAINTENANCE, feHomePb.HomeWidget_WIDGET_TYPE_CRITICAL_NOTIFICATION:
	case feHomePb.HomeWidget_WIDGET_TYPE_TOP_NAV_BAR:
		leftIcons := []*feHomePb.Icon{}
		for _, id := range widgetParams.HomeWidgetTopNavBarParams.LeftIconIds {
			leftIcons = append(leftIcons, iconIdMapping[id])
		}
		rightIcons := []*feHomePb.Icon{}
		for _, id := range widgetParams.HomeWidgetTopNavBarParams.RightIconIds {
			rightIcons = append(rightIcons, iconIdMapping[id])
		}
		res.Widget = &feHomePb.HomeWidget_TopBarWidget{TopBarWidget: &feHomePb.TopNavBarWidget{
			RightIcons: rightIcons,
			LeftIcons:  leftIcons,
		}}
		res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#282828"}}
	case feHomePb.HomeWidget_WIDGET_TYPE_SEARCH_BAR:
		if cfg.IsFeatureEnabledOnPlatform(ctx, homeConf.HomeLayoutUIRevamp) {
			res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}}
		} else {
			res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#D6DBE0"}}
		}
	case feHomePb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER, feHomePb.HomeWidget_WIDGET_TYPE_SUGGESTED_FOR_YOU,
		feHomePb.HomeWidget_WIDGET_TYPE_SHORTCUTS:
		if cfg.IsFeatureEnabledOnPlatform(ctx, homeConf.HomeLayoutUIRevamp) {
			res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}}
		} else {
			res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MFog}}
		}
	case feHomePb.HomeWidget_WIDGET_TYPE_REWARDS,
		feHomePb.HomeWidget_WIDGET_TYPE_INVEST_HOME_ELEMENT:
		if cfg.IsFeatureEnabledOnPlatform(ctx, homeConf.HomeLayoutUIRevamp) {
			res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}}
		} else {
			res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Snow}}
		}
		res.WidgetBgSeparator = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}}
	case feHomePb.HomeWidget_WIDGET_TYPE_RECENT_UPCOMING_ACTIVITIES:
		if cfg.IsFeatureEnabledOnPlatform(ctx, homeConf.HomeLayoutUIRevamp) {
			res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}}
		} else {
			res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Snow}}
		}
		res.WidgetBgSeparator = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E7ECF0"}}
		filterType := ui.Filter_FilterType(ui.Filter_FilterType_value[recentWidgetParams.FilterViewType])
		if isFeatureHomeDesignEnhancementsEnabled {
			filterType = ui.Filter_FILTER_TYPE_CHIPS
		}
		filter, mapping := getHomeRecentUpcomingActivitiesFilter(filterType, []feHomePb.RecentAndUpcomingWidget_ActivityType{feHomePb.RecentAndUpcomingWidget_ACTIVITY_TYPE_RECENT,
			feHomePb.RecentAndUpcomingWidget_ACTIVITY_TYPE_UPCOMING}, feHomePb.RecentAndUpcomingWidget_ACTIVITY_TYPE_RECENT, cfg.IsFeatureEnabledOnPlatform(ctx, homeConf.HomeLayoutUIRevamp))
		res.Widget = &feHomePb.HomeWidget_ActivitiesWidget{ActivitiesWidget: &feHomePb.RecentAndUpcomingWidget{
			Filter:            filter,
			TabToActivityType: mapping,
		}}
	case feHomePb.HomeWidget_WIDGET_TYPE_REFERRAL:
		res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{
			RadialGradient: &ui.RadialGradient{
				Colours: []string{"#9287BD", "#6F62A4"},
			},
		}}
	case feHomePb.HomeWidget_WIDGET_TYPE_DASHBOARD:
		views := []*feHomePb.DashboardWidget_DashboardView{}
		// TODO: Add this to config
		backgroundColour := &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{RadialGradient: &ui.RadialGradient{
			Center: &ui.CenterCoordinates{
				CenterX: 1,
				CenterY: 0,
			},
			OuterRadius: 82,
			Colours:     []string{"#484848", "#353434"},
		}}}
		for _, view := range widgetParams.HomeWidgetDashboardParams.HomeDashboardViews {
			dashViewPb := feHomePb.DashboardWidget_DashboardView_DashboardViewType(feHomePb.DashboardWidget_DashboardView_DashboardViewType_value[view.Name])

			// Skip loan dashboard if not enabled for the user's client version
			if dashViewPb == feHomePb.DashboardWidget_DashboardView_DASHBOARD_VIEW_LOANS && !LoanDashboardViewVersionCheck(ctx) {
				continue
			}

			viewPb := &feHomePb.DashboardWidget_DashboardView{
				DashboardViewType:  dashViewPb,
				DashboardWaterMark: &commontypes.Image{ImageUrl: view.WaterMarkUrl},
				OverridingPriority: feHomePb.DashboardWidget_OverridingDashboardPriority(feHomePb.DashboardWidget_OverridingDashboardPriority_value[view.Priority]),
				Background:         backgroundColour,
			}
			views = append(views, viewPb)
		}
		res.Widget = &feHomePb.HomeWidget_DashboardWidget{
			DashboardWidget: &feHomePb.DashboardWidget{
				DashboardViews: views,
			},
		}
		res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#282828"}}
	case feHomePb.HomeWidget_WIDGET_TYPE_SALARY_ACCOUNT:
		res.WidgetBg = &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_RadialGradient{
				RadialGradient: &ui.RadialGradient{
					Colours: []string{"#EFF2F6", "#EFF2F6"},
				},
			}}
	case feHomePb.HomeWidget_WIDGET_TYPE_HELP:
		res.WidgetBg = helpSectionByVersionCheck(ctx, homeConf)
	case feHomePb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER_2:
		if cfg.IsFeatureEnabledOnPlatform(ctx, homeConf.HomeLayoutUIRevamp) {
			res.WidgetBg = &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: "#E6E9ED"}}
		}
	default:
		logger.Error(ctx, "unhandled home widget type")
	}

	return res
}

func (s *Service) convertMoneyBalanceToUIFormat(ctx context.Context, m *money.Money, currSymbolColour, currUnitsColour, currDecimalColour string,
	toShowTieringRelatedInfo bool, tieringPitchResp *beTieringPb.GetTieringPitchV2Response) []*commontypes.Text {
	var (
		units           = m.GetUnits()
		nanos           = m.GetNanos()
		decimal         = int(float64(m.GetNanos()) / float64(10000000)) // For current home purpose we're rounding upto 2 decimal points
		currCode        = m.GetCurrencyCode()
		currSymbol      = ""
		currFontColour  = s.getColourForMoney(ctx, currSymbolColour, graceRedMidColor, graceLemonMidColor, toShowTieringRelatedInfo, tieringPitchResp)
		unitsFontColour = s.getColourForMoney(ctx, currUnitsColour, graceRedColor, graceLemonColor, toShowTieringRelatedInfo, tieringPitchResp)
	)

	switch currCode {
	case "INR":
		currSymbol = "₹"
	}

	res := []*commontypes.Text{
		{
			DisplayValue: &commontypes.Text_PlainString{PlainString: currSymbol},
			FontColor:    currFontColour,
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_RUPEE_XL},
		},
	}
	if units == 0 && decimal == 0 {
		unitsFontColour = currFontColour
	}
	// convert money to string readable format and show decimal value only if balance is less than 10
	formattedUnits := ""
	if units < 10 {
		formattedUnits = pkgMoney.ToDisplayStringInIndianFormat(&money.Money{CurrencyCode: currCode, Units: units, Nanos: nanos}, 2, false)
	} else {
		formattedUnits = pkgMoney.ToDisplayStringInIndianFormat(&money.Money{CurrencyCode: currCode, Units: units, Nanos: nanos}, 0, false)
	}
	res = append(res, &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%v", formattedUnits)},
		FontColor:    unitsFontColour,
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
	})
	return res
}

func convertMoneyToReadableString(money *money.Money) string {
	if money.GetUnits() == 0 {
		return "0"
	}
	var (
		lakh     = float32(100000)
		thousand = float32(1000)
	)

	switch {
	case money.GetUnits() >= int64(lakh):
		return fmt.Sprintf("%.2fL", float32(money.GetUnits())/lakh)
	case money.GetUnits() >= int64(thousand):
		return fmt.Sprintf("%.2fK", float32(money.GetUnits())/thousand)
	}
	return fmt.Sprintf("%v", money.GetUnits())
}

/*
Account number needs to be presented in the format: "************** 86"
https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=836%3A23784
*/
func formatAccountNumber(accNo string) string {
	s := ""
	accLength := len(accNo)

	for i := 0; i < accLength; i += 4 {
		start := i
		end := i + 4
		if end > accLength {
			end = accLength
		}
		switch {
		case len(s) == 0:
			s = accNo[start:end]
		case end > start:
			s = fmt.Sprintf("%s %s", s, accNo[start:end])
		}
	}
	return s
}

func (s *Service) getDiscrepancyIcon(ctx context.Context, availableBal, ledgerBal *money.Money, toShowTieringRelatedInfo bool,
	tieringPitchResp *beTieringPb.GetTieringPitchV2Response) *feHomePb.Icon {
	// If there is a discrepancy in the ledger vs available amount for use
	var homeIcon *feHomePb.Icon
	if pkgMoney.Compare(availableBal, ledgerBal) != 0 {
		homeIcon = &feHomePb.Icon{
			IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home-v2/Warning.png"},
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home-v2/Warning.png"),
			IconType:      homeTypesPb.IconType_DASHBOARD_ACTION,
			ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
			Action:        &feHomePb.Icon_Deeplink{Deeplink: s.getPersonalisedAccountDiscrepancyDeeplink(ctx, availableBal, ledgerBal)},
			BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}},
		}
	}
	if toShowTieringRelatedInfo {
		isUserInGrace, currentTier, minBalance, getTieringInfoErr := getTieringInfoFromRespForSavingsDash(tieringPitchResp)
		if getTieringInfoErr != nil {
			if !errors.Is(getTieringInfoErr, feTieringErrors.ErrTierHasNoMinBalanceCriteria) {
				logger.Error(ctx, "error getting tiering info from pitch response", zap.Error(getTieringInfoErr))
			}
			return homeIcon
		}
		cmpVal, cmpErr := pkgMoney.CompareV2(availableBal, minBalance)
		if cmpErr != nil {
			logger.Error(ctx, "error comparing current balance and minimum balance", zap.Error(cmpErr))
			return homeIcon
		}
		if !isUserInGrace || cmpVal > 0 {
			return homeIcon
		}
		return &feHomePb.Icon{
			IconImage:     &commontypes.Image{ImageUrl: "https://epifi-icons.pointz.in/home/<USER>/Danger_4x.png"},
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/Danger_4x.png"),
			IconType:      homeTypesPb.IconType_DASHBOARD_ACTION,
			ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
			Action:        &feHomePb.Icon_Deeplink{Deeplink: getGraceDiscrepancyDeeplink(ctx, currentTier, minBalance)},
			BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}},
		}
	}
	return homeIcon
}

func LoanDashboardViewVersionCheck(ctx context.Context) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	if (platform == commontypes.Platform_ANDROID && version >= 221) ||
		(platform == commontypes.Platform_IOS && version >= 315) {
		return true
	}
	return false
}

func helpSectionByVersionCheck(ctx context.Context, homeConf *config.HomeRevampParams) *ui.BackgroundColour {
	platform, version := epificontext.AppPlatformAndVersion(ctx)

	if cfg.IsFeatureEnabledOnPlatform(ctx, homeConf.HomeLayoutUIRevamp) {
		return &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#E6E9ED",
			}}
	} else if (platform == commontypes.Platform_ANDROID && version >= 232) ||
		(platform == commontypes.Platform_IOS && version >= 333) {
		return &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_BlockColour{
				BlockColour: "#E7ECF0",
			}}
	}
	return &ui.BackgroundColour{
		Colour: &ui.BackgroundColour_RadialGradient{
			RadialGradient: &ui.RadialGradient{
				Colours: []string{"#00B899", "#006D5B"},
			},
		}}
}

func GetHomeFilter(filterType ui.Filter_FilterType, tabTitles []string, defaultTabTitle string, tabIdPrefix string, newHomeUiRevamp bool) *ui.Filter {

	var (
		filter = &ui.Filter{Tabs: []*ui.Tab{}, FilterType: filterType}
	)

	for i, tabTitle := range tabTitles {
		id := fmt.Sprintf("%v:%v", tabIdPrefix, i)
		tab := &ui.Tab{
			Id:          id,
			InactiveCta: getInactiveCtaByFilterType(filterType, tabTitle, newHomeUiRevamp),
			ActiveCta:   getActiveCtaByFilterType(filterType, tabTitle, newHomeUiRevamp),
			BorderColor: feHomePb.GetHomeWidgetBorderColor(),
		}
		// Set the first tab as default tab is default tab to be selected is not provided
		if tabTitle == defaultTabTitle || i == 0 {
			filter.DefaultTabSection = id
		}
		filter.Tabs = append(filter.Tabs, tab)
	}
	return filter
}

func getHomeRecentUpcomingActivitiesFilter(filterType ui.Filter_FilterType,
	tabTypeOrder []feHomePb.RecentAndUpcomingWidget_ActivityType,
	defaultTabSelection feHomePb.RecentAndUpcomingWidget_ActivityType, newHomeUiRevamp bool) (filter *ui.Filter, mapping map[string]feHomePb.RecentAndUpcomingWidget_ActivityType) {
	var (
		activityTypeToTitleMapping = map[feHomePb.RecentAndUpcomingWidget_ActivityType]string{
			feHomePb.RecentAndUpcomingWidget_ACTIVITY_TYPE_RECENT:   "Recent Activity",
			feHomePb.RecentAndUpcomingWidget_ACTIVITY_TYPE_UPCOMING: "Upcoming",
		}
		tabTitle                 = []string{}
		defaultTabSelectionTitle = activityTypeToTitleMapping[defaultTabSelection]
	)

	for _, tabType := range tabTypeOrder {
		tabTitle = append(tabTitle, activityTypeToTitleMapping[tabType])
	}
	filter = GetHomeFilter(filterType, tabTitle, defaultTabSelectionTitle, "RecentUpcomingTabs", newHomeUiRevamp)
	mapping = map[string]feHomePb.RecentAndUpcomingWidget_ActivityType{}

	for i, tab := range filter.Tabs {
		mapping[tab.GetId()] = tabTypeOrder[i]
	}
	return
}

func getActiveCtaByFilterType(filterType ui.Filter_FilterType, tabTitle string, newHomeUiRevamp bool) *ui.IconTextComponent {
	fontStyle := commontypes.FontStyle_HEADLINE_S
	if newHomeUiRevamp {
		fontStyle = commontypes.FontStyle_HEADLINE_M
	}
	switch filterType {
	case ui.Filter_FILTER_TYPE_SWITCH:
		return ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(tabTitle, MonochromeNight, fontStyle)).
			WithContainerBackgroundColor(Snow)
	case ui.Filter_FILTER_TYPE_CHIPS:
		return ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(tabTitle, MonochromeNight, fontStyle, commontypes.Text_ALIGNMENT_CENTER)).
			WithContainerBackgroundColor(Chalk).
			WithContainerPaddingSymmetrical(10, 4).
			WithBorder(Chalk, 1)
	}
	return nil
}

func getInactiveCtaByFilterType(filterType ui.Filter_FilterType, tabTitle string, newHomeUiRevamp bool) *ui.IconTextComponent {
	fontStyle := commontypes.FontStyle_HEADLINE_S
	if newHomeUiRevamp {
		fontStyle = commontypes.FontStyle_HEADLINE_M
	}
	switch filterType {
	case ui.Filter_FILTER_TYPE_SWITCH:
		return ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(tabTitle, Lead, fontStyle)).
			WithContainerBackgroundColor(Chalk)
	case ui.Filter_FILTER_TYPE_CHIPS:
		return ui.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(tabTitle, Slate, fontStyle, commontypes.Text_ALIGNMENT_CENTER)).
			WithContainerBackgroundColor(Snow).
			WithContainerPaddingSymmetrical(10, 4).
			WithBorder(Chalk, 1)
	}
	return nil
}

//nolint:dupl
func getLowBalanceAlertPopup(ctx context.Context, params *genconf.LowBalAlertParams) *feHomePb.Icon {

	var (
		title    = "Your account balance is\nrunning low"
		subTitle = "To make the best of Fi's features, we recommend maintaining a minimum of ₹5,000 as your account balance."
	)

	dl := &deeplink.Deeplink{
		Screen: deeplink.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplink.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplink.InformationPopupOptions{
				IconUrl: "https://epifi-icons.pointz.in/home-v2/LowBalancePopupImage.png",
				Ctas: []*deeplink.Cta{
					{
						Type: deeplink.Cta_CUSTOM,
						Text: "Add Money",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_TRANSFER_IN,
						},
						DisplayTheme: deeplink.Cta_PRIMARY,
						Status:       deeplink.Cta_CTA_STATUS_ENABLED,
					},
				},
				IsNonDismissible: false,
				TextTitle:        &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{title}},
				TextSubTitle:     &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{subTitle}},
				BgColor:          Snow,
			},
		},
	}
	return &feHomePb.Icon{
		IconImage:     &commontypes.Image{ImageUrl: params.BalImage(ctx)},
		VisualElement: commontypes.GetVisualElementImageFromUrl(params.BalImage(ctx)),
		Title:         &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: params.BalTitle(ctx)}, FontColor: params.BalColor(ctx)},
		Action:        &feHomePb.Icon_Deeplink{Deeplink: dl},
		ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
		BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: MonochromeNight}},
	}
}

// handleDeviceDeactivated handles OBE0170 vendor error. We log out the user and send communication to push the user to login using his phone number registered with federal
func (s *Service) handleDeviceDeactivated(ctx context.Context, userId string) {
	logger.Debug(ctx, "showing error message")
	sendMessgRes, err := s.commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_GUARANTEED,
		Medium: commsPb.Medium_NOTIFICATION,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{
			UserId: userId,
		},
		Message: &commsPb.SendMessageRequest_Notification{
			Notification: &commsPb.NotificationMessage{
				Priority: commsPb.NotificationPriority_NORMAL,
				Notification: &fcm.Notification{
					NotificationType: fcm.NotificationType_IN_APP_FULLSCREEN,
					NotificationTemplates: &fcm.Notification_InAppFullScreenTemplate{
						InAppFullScreenTemplate: &fcm.InAppFullScreenTemplate{
							Title: constants.DeviceTemporarilyDeactivatedTitle,
							Body:  constants.DeviceTemporarilyDeactivatedBody,
							Image: &commontypes.Image{
								ImageUrl: constants.AFUFacePalmIllustrationImageUrl,
							},
							Cta: &fcm.NotificationCTA{
								Text: constants.BackToLoginText,
								Deeplink: &deeplink.Deeplink{
									Screen: deeplink.Screen_USER_SIGN_OUT,
								},
							},
						},
					},
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(sendMessgRes, err); rpcErr != nil {
		logger.Error(ctx, "error in sending message", zap.Error(rpcErr))
		return
	}
	logger.Info(ctx, "notification sent successfully for OBE0170")
}

func (s *Service) GetHomeLayoutAndWalkthrough(ctx context.Context, req *feHomePb.GetHomeLayoutAndWalkthroughRequest) (*feHomePb.GetHomeLayoutAndWalkthroughResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
	)
	userType, getDetailsRes, _, err := homePkg.GetSuitableUserType(ctx, &homePkg.GetSuitableUserTypeRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.evaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	})
	if err != nil {
		logger.Error(ctx, "failed to get suitable user type in GetHomeLayoutAndWalkthrough", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return &feHomePb.GetHomeLayoutAndWalkthroughResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("failed to get user type: %v", err))}}, nil
	}

	// get the most appropriate parameters to generate home layout based on user state
	layoutParams := s.getBestLayoutParamsForUser(userType)

	// compute the best layout based on segment or from experiment in quest
	slotIdToScreenElementIdMap, err := s.computeBestLayoutForUser(ctx, actorId, layoutParams)
	if err != nil {
		logger.Error(ctx, "error while computing best layout for the user", zap.Error(err))
		return &feHomePb.GetHomeLayoutAndWalkthroughResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}}, nil
	}

	// update screen element id if not supported on given app version
	s.updateScreenElementIdForAppVersion(ctx, slotIdToScreenElementIdMap, actorId)

	// check if user is an old user or a new user based on onboarding age
	isOldUser := s.isOldUser(getDetailsRes)
	if isOldUser {
		return s.getHomeLayoutAndWalkthroughForOldUser(ctx, actorId, userType, slotIdToScreenElementIdMap, layoutParams), nil
	} else {
		return s.getHomeLayoutAndWalkthroughForNewUser(ctx, actorId, userType, slotIdToScreenElementIdMap, layoutParams), nil
	}
}

func (s *Service) isNoAssetConnectedWealthBuilderUser(ctx context.Context, actorId string) bool {
	nwValueResp, err := s.networthClient.GetNetWorthValue(ctx, &networthPb.GetNetWorthValueRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(nwValueResp, err); rpcErr != nil {
		// Returning default shortcuts instead of failing the rpc
		logger.Error(ctx, "error in fetching net worth value", zap.Error(rpcErr))
		return false
	}
	if nwValueResp.GetStatus().IsSuccess() {
		for _, assetValue := range nwValueResp.GetAssetValues() {
			if assetValue.GetComputationStatus() == networthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND {
				continue
			}
			return false
		}
	}
	return true
}

// returns the most appropriate parameters to generate home layout based on user state
func (s *Service) getBestLayoutParamsForUser(userType homePkg.UserType) *genconf.HomeLayoutV2Params {
	var layoutV2Params *genconf.HomeLayoutV2Params

	switch userType {
	case homePkg.UserTypeFiNR:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2FiNRParams()
	case homePkg.UserTypeFiSA:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2Params()
	case homePkg.UserTypeFiLiteCC:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2LiteCCParams()
	case homePkg.UserTypeFiLitePL:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2LitePLParams()
	case homePkg.UserTypeFiLite:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2LiteParams()
	case homePkg.UserTypeDirectToFiLiteNetworth:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2DirectToLiteNetworthParams()
	case homePkg.UserTypeDirectToFiLiteUpsell:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2DirectToLiteUpsellParams()
	case homePkg.UserTypeFiSAWithD0To7:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2D0To7Params()
	case homePkg.UserTypeFiSAWithD8To14:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2D8To14Params()
	case homePkg.UserTypeFiSAWithD15To28:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2D15To28Params()
	case homePkg.UserTypeWealthAnalyser:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2WealthAnalyserParams()
	case homePkg.UserTypeNoAssetConnectedWealthAnalyser:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2WealthAnalyserNoAssetParams()
	default:
		layoutV2Params = s.genconf.HomeRevampParams().HomeLayoutV2LiteParams()
	}
	return layoutV2Params
}

//nolint:gocritic
func (s *Service) updateScreenElementIdForAppVersion(ctx context.Context, slotIdToScreenElementIdMap *config.SlotIdToScreenElementIdMap, actorId string) {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	var isWBNetworthDashboardEnabled bool
	for slotId, screenElementId := range slotIdToScreenElementIdMap.VerticalSlotSection {
		switch {
		case screenElementId == homeConstants.CatalogOffersScrollableComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().RewardsWidgetSeparationReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().RewardsWidgetSeparationReleaseConfig().MinIosVersion())):
			slotIdToScreenElementIdMap.VerticalSlotSection[slotId] = homeConstants.RewardsScrollableComponentId.String()
		case screenElementId == homeConstants.PrimaryFeatureScrollableComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().FeatureWidgetsReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().FeatureWidgetsReleaseConfig().MinIosVersion())):
			slotIdToScreenElementIdMap.VerticalSlotSection[slotId] = homeConstants.PromotionalBanner2ScrollableComponentId.String()
		case screenElementId == homeConstants.SecondaryFeatureScrollableComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().FeatureWidgetsReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().FeatureWidgetsReleaseConfig().MinIosVersion())):
			slotIdToScreenElementIdMap.VerticalSlotSection[slotId] = homeConstants.SalaryAccountScrollableComponentId.String()
		case screenElementId == homeConstants.MoneySecretsScrollableComponentId.String():
			slotIdToScreenElementIdMap.VerticalSlotSection[slotId] = s.checkMoneySecretSlotIsAvailable(ctx, actorId, platform, version)
		case screenElementId == homeConstants.WealthAnalyserScrollableComponentId.String():
			slotIdToScreenElementIdMap.VerticalSlotSection[slotId] = s.checkIfWealthAnalyserWidgetCanBeShown(platform, version)
		case screenElementId == homeConstants.WealthBuilderNetworthDashboardScrollableComponentId.String():
			slotIdToScreenElementIdMap.VerticalSlotSection[slotId], isWBNetworthDashboardEnabled = s.checkIfWealthBuilderNetworthDashBoardCanBeShown(ctx, actorId)
		case screenElementId == homeConstants.DcInternationalWidgetScrollableComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().DcInternationalWidgetReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().DcInternationalWidgetReleaseConfig().MinIosVersion())):
			slotIdToScreenElementIdMap.VerticalSlotSection[slotId] = ""
		}
	}
	for slotId, screenElementId := range slotIdToScreenElementIdMap.DashboardSlotSection {
		switch {
		case screenElementId == homeConstants.NetworthDashboardComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().NetWorthDashboardCardReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().NetWorthDashboardCardReleaseConfig().MinIosVersion())):
			slotIdToScreenElementIdMap.DashboardSlotSection[slotId] = homeConstants.ConnectedAccountsDashboardComponentId.String()
		case screenElementId == homeConstants.NreSavingDashboardComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().NreSavingsDashboardReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().NreSavingsDashboardReleaseConfig().MinIosVersion())):
			slotIdToScreenElementIdMap.DashboardSlotSection[slotId] = homeConstants.PrimarySavingDashboardComponentId.String()
		case screenElementId == homeConstants.NroSavingDashboardComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().NroSavingsDashboardReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().NroSavingsDashboardReleaseConfig().MinIosVersion())):
			// for older client versions, support does not exist to show 2 dashboards. also nre is a prerequisite for nro. so we'll show only nre for older client versions
			slotIdToScreenElementIdMap.DashboardSlotSection[slotId] = ""
		case screenElementId == homeConstants.MutualfundDashboardComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().MutualfundDashboardCardReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().MutualfundDashboardCardReleaseConfig().MinIosVersion())):
			slotIdToScreenElementIdMap.DashboardSlotSection[slotId] = ""
		case screenElementId == homeConstants.EpfDashboardComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().EpfDashboardCardReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().EpfDashboardCardReleaseConfig().MinIosVersion())):
			slotIdToScreenElementIdMap.DashboardSlotSection[slotId] = ""
		case screenElementId == homeConstants.CreditScoreDashboardComponentId.String() && ((platform == commontypes.Platform_ANDROID && version < s.genconf.HomeRevampParams().CreditScoreDashboardCardReleaseConfig().MinAndroidVersion()) || (platform == commontypes.Platform_IOS && version < s.genconf.HomeRevampParams().CreditScoreDashboardCardReleaseConfig().MinIosVersion())):
			// for older client versions, support does not exist to show mf, credit score and epf dashboards. so we'll show primarysavings for older client versions
			slotIdToScreenElementIdMap.DashboardSlotSection[slotId] = homeConstants.PrimarySavingDashboardComponentId.String()
		}
	}
	if isWBNetworthDashboardEnabled {
		// TEMPORARY FIX: Display only the WealthBuilderNetworkDashboard on the home screen
		// for users with this feature enabled. The dashboards section should be hidden
		// when the Wealth Builder NetWorth Dashboard is active.
		for slotId := range slotIdToScreenElementIdMap.DashboardSlotSection {
			slotIdToScreenElementIdMap.DashboardSlotSection[slotId] = ""
		}
	}
	for slotId, screenElementId := range slotIdToScreenElementIdMap.BottomNavBarSlotSection {
		switch {
		case screenElementId == homeConstants.NetWorthBottomNavBarId.String():
			slotIdToScreenElementIdMap.BottomNavBarSlotSection[slotId] = s.getBottomNavbarNetworthScreenElement(ctx, actorId)
		case screenElementId == homeConstants.WealthBuilderBottomNavBarId.String():
			slotIdToScreenElementIdMap.BottomNavBarSlotSection[slotId] = s.getNavbarWealthBuilderScreenElement(ctx, actorId)
		case screenElementId == homeConstants.USStocksBottomNavBarId.String():
			if obj, ok := s.genconf.HomeRevampParams().HomeLayoutConfigurationV2Params().SectionScreenElementsConfig().BottomNavBarSection.ScreenElementsMapping[screenElementId]; ok {
				if (platform == commontypes.Platform_ANDROID && version < obj.IconWithVersionConstraints.VersionConstraints.MinAndroidVersion) || (platform == commontypes.Platform_IOS && version < obj.IconWithVersionConstraints.VersionConstraints.MinIosVersion) {
					// for older version of app, show invest instead of US Stocks
					slotIdToScreenElementIdMap.BottomNavBarSlotSection[slotId] = homeConstants.InvestmentBottomNavBarId.String()
				}
			} else {
				slotIdToScreenElementIdMap.BottomNavBarSlotSection[slotId] = ""
			}
		}
	}

	for slotId, screenElementId := range slotIdToScreenElementIdMap.TopNavBarSlotSection.RightSlots {
		switch {
		case screenElementId == homeConstants.WealthBuilderTopNavBarId.String():
			slotIdToScreenElementIdMap.TopNavBarSlotSection.RightSlots[slotId] = s.getNavbarWealthBuilderScreenElement(ctx, actorId)
		}
	}
}

/*
If 'FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME' is enabled then we show Wealth Analyser widget instead of Money Secrets widget.
We are doing this to show the wealth analyser widget to users which are not marked 'Wealth Analyser' users.
For such users, we are overriding money secret widget with Wealth analyser widget
TODO (Himanshu): Remove this function and logic, once we add support of user group feature lifecycle functions
*/
func (s *Service) checkMoneySecretSlotIsAvailable(ctx context.Context, actorId string, platform commontypes.Platform, appversion int) string {
	appVersionConfig := s.genconf.FeatureReleaseConfig().FeatureConstraints().Get(types.Feature_FEATURE_MONEY_SECRET_HOME_SCREEN.String()).AppVersionConstraintConfig()
	if (platform == commontypes.Platform_ANDROID && appversion < appVersionConfig.MinAndroidVersion()) || (platform == commontypes.Platform_IOS && appversion < appVersionConfig.MinIOSVersion()) {
		return homeConstants.AnalyserCardsScrollableComponentId.String()
	}
	enable, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking feature flag for wealth analyser widget", zap.Error(err))
		return homeConstants.AnalyserCardsScrollableComponentId.String()
	}
	if enable {
		return homeConstants.WealthAnalyserScrollableComponentId.String()
	}

	// show wealth analyser widget for users for whom we've enabled money secrets locking
	isMoneySecretLockingFeatureEnabled, err := configsvc.IsMoneySecretLockingFeatureEnabled(ctx, actorId, s.onboardingClient, s.segmentClient, s.genconf)
	if err != nil {
		logger.Error(ctx, "error while checking feature flag for money secret locking", zap.Error(err))
		return homeConstants.MoneySecretsScrollableComponentId.String()
	}
	if isMoneySecretLockingFeatureEnabled {
		return homeConstants.WealthAnalyserScrollableComponentId.String()
	}

	return homeConstants.MoneySecretsScrollableComponentId.String()
}

/*
Checks the app version for displaying Wealth Analyser Widget.
If the app version is less than the minimum version required to show Wealth Analyser widget, then we show Money Secrets widget instead.
We assume that if we are checking this, other constraints are not important and we have to show Wealth analyser widget irrespective of roll out % or user group.
*/
func (s *Service) checkIfWealthAnalyserWidgetCanBeShown(platform commontypes.Platform, appVersion int) string {
	appVersionConfig := s.genconf.FeatureReleaseConfig().FeatureConstraints().Get(types.Feature_FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME.String()).AppVersionConstraintConfig()
	if (platform == commontypes.Platform_ANDROID && appVersion < appVersionConfig.MinAndroidVersion()) || (platform == commontypes.Platform_IOS && appVersion < appVersionConfig.MinIOSVersion()) {
		return homeConstants.MoneySecretsScrollableComponentId.String()
	}
	return homeConstants.WealthAnalyserScrollableComponentId.String()
}

// Checks if the feature for displaying Wealth Builder Networth Dashboard is enabled for the actor or not.
func (s *Service) checkIfWealthBuilderNetworthDashBoardCanBeShown(ctx context.Context, actorId string) (string, bool) {
	isEnabled, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_WEALTH_BUILDER_NETWORTH_PAGE, actorId)
	if !isEnabled {
		if err != nil {
			logger.Error(ctx, "error while checking feature flag for wealth builder networth dashboard", zap.Error(err))
		}
		return "", false
	}
	return homeConstants.WealthBuilderNetworthDashboardScrollableComponentId.String(), true
}

func (s *Service) getBottomNavbarNetworthScreenElement(ctx context.Context, actorId string) string {
	isEnabled, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_HOME_BOTTOM_NAV_BAR_NETWORTH_SECTION, actorId)
	if err != nil || !isEnabled {
		return homeConstants.AnalyserBottomNavBarId.String()
	}
	return homeConstants.NetWorthBottomNavBarId.String()
}

func (s *Service) getNavbarWealthBuilderScreenElement(ctx context.Context, actorId string) string {
	isEnabled, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION, actorId)
	if err != nil || !isEnabled {
		return homeConstants.NetWorthBottomNavBarId.String()
	}
	return homeConstants.WealthBuilderBottomNavBarId.String()
}

// nolint:funlen
// Method to get home layout and walkthrough for old user
func (s *Service) getHomeLayoutAndWalkthroughForOldUser(ctx context.Context, actorId string, userType homePkg.UserType, slotIdToScreenElementIdMap *config.SlotIdToScreenElementIdMap, layoutParams *genconf.HomeLayoutV2Params) *feHomePb.GetHomeLayoutAndWalkthroughResponse {
	var (
		walkthroughResponse                      *walkthrough.Walkthrough
		slotIdToScreenElementIdMapFromPreference *config.SlotIdToScreenElementIdMap
	)

	timeoutCtx, cancelFunc := context.WithTimeout(ctx, 1*time.Second)
	defer cancelFunc()
	slotIdToScreenElementIdMapStringFromPreference, slotIdToScreenElementIdMapStringPreferenceCreatedAt, err := s.getHomeLayoutPreferenceForUser(timeoutCtx, actorId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		// if error is record not found, set new home layout in user preference and return new home layout.
		logger.Debug(ctx, "home layout preference for user not found in DB", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
	case err != nil:
		// if unknown error, set new home layout in user preference and return new home layout.
		logger.Error(ctx, "error while fetching app home layout preference for user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
	default:
		// if home layout found in preference, check the conditions to generate walkthrough.
		slotIdToScreenElementIdMapFromPreference, err = s.getSlotIdToScreenElementIdMapFromString(slotIdToScreenElementIdMapStringFromPreference)
		if err != nil {
			logger.Error(ctx, "failed to get SlotIdToScreenElementIdMap from string", zap.Error(err))
			break
		}
		// generate walkthrough for old user if it is not under any cool off
		if s.shouldShowWalkthroughForOldUser(userType, slotIdToScreenElementIdMapStringPreferenceCreatedAt) {
			walkthroughResponse = s.generateWalkthroughForOldUser(ctx, slotIdToScreenElementIdMapFromPreference, slotIdToScreenElementIdMap, userType == homePkg.UserTypeFiLite)
		}
	}

	// generate home layout from slot id to screen element id map
	newHomeLayout, err := s.constructHomeLayout(ctx, actorId, slotIdToScreenElementIdMap, layoutParams)
	if err != nil {
		logger.Error(ctx, "error while constructing new home layout from slot id to screen element id map", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		switch {
		case slotIdToScreenElementIdMapFromPreference != nil:
			oldHomeLayout, err := s.constructHomeLayout(ctx, actorId, slotIdToScreenElementIdMapFromPreference, layoutParams)
			if err != nil {
				logger.Error(ctx, "error while constructing old home layout from slot id to screen element id map", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				return &feHomePb.GetHomeLayoutAndWalkthroughResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error while constructing old home layout from config")}}
			}

			return &feHomePb.GetHomeLayoutAndWalkthroughResponse{
				RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
				Layout:               oldHomeLayout,
				Walkthrough:          nil,
				TopSectionBackground: feHomePb.GetHomeTopSectionBackground(),
			}
		default:
			return &feHomePb.GetHomeLayoutAndWalkthroughResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error while constructing new home layout from slot id to screen element id map")},
			}
		}
	}

	// set new home layout for actor in user preference db, only if any of the below conditions are true.
	// 1. if we haven't received any old home layout from user preference, then update new home layout in DB.
	// 2. if old and new home layout id is different, then only we can update new layout in DB.
	if slotIdToScreenElementIdMapFromPreference == nil || (slotIdToScreenElementIdMapFromPreference.LayoutId != slotIdToScreenElementIdMap.LayoutId) {
		goroutine.Run(ctx, 2*time.Second, func(ctx context.Context) {
			logger.Debug(ctx, "setting home layout preference since old and new layout ids are different")
			s.setHomeLayoutPreferenceForUser(ctx, actorId, slotIdToScreenElementIdMap)
		})
	}

	return &feHomePb.GetHomeLayoutAndWalkthroughResponse{
		RespHeader: &header.ResponseHeader{
			Status:             rpc.StatusOk(),
			FeedbackEngineInfo: homePkg.GetFeedbackAppFlowHeaderForReVKYC(actorId),
		},
		Layout:               newHomeLayout,
		Walkthrough:          walkthroughResponse,
		TopSectionBackground: feHomePb.GetHomeTopSectionBackground(),
	}
}

// Method to generate walkthrough for new user
// It reuses flow for newly added elements walkthrough
func (s *Service) generateWalkthroughForNewUser(ctx context.Context, actorId string, userType homePkg.UserType, slotIdToScreenElementIdMapForNewLayout *config.SlotIdToScreenElementIdMap) *walkthrough.Walkthrough {

	newlyAddedElementsWalkthroughInfo := s.getWalkthroughInfoForNewUserLayout(ctx, slotIdToScreenElementIdMapForNewLayout)

	var dashboardForNewUserWalkthrough *walkthrough.DashboardIntroCardDetails
	// If there is atleast on element in newlyAddedElementsWalkthroughInfo then add dashboard intro card
	// This is just a safe check
	if len(newlyAddedElementsWalkthroughInfo) > 0 {
		dashboardForNewUserWalkthrough = s.getDashboardIntroCardForNewUserWalkthrough(ctx, actorId)
	}

	walkthroughStepResponse := make([]*walkthrough.WalkthroughStep, 0)
	walkthroughStepsForNewlyAddedElements := s.getWalkthroughStepsFromWalkthroughInfo(newlyAddedElementsWalkthroughInfo, true, true, userType == homePkg.UserTypeFiLite)
	// Just a safe check
	if len(walkthroughStepsForNewlyAddedElements) > 0 {
		walkthroughStepResponse = append(walkthroughStepResponse, s.generateFullScreenWalkthroughStepOnWalkthroughStartForNewUser())
	}
	walkthroughStepResponse = append(walkthroughStepResponse, walkthroughStepsForNewlyAddedElements...)

	if len(walkthroughStepResponse) == 0 {
		return nil
	}
	walkthroughResponse := &walkthrough.Walkthrough{
		Id:      uuid.NewString(),
		Steps:   walkthroughStepResponse,
		Trigger: walkthrough.WalkthroughTrigger_WALKTHROUGH_TRIGGER_ENTRY_POINT,
		EntryPoint: &walkthrough.WalkthroughEntryPoint{
			EntryPoint: &walkthrough.WalkthroughEntryPoint_DashboardIntroCardDetails{
				DashboardIntroCardDetails: dashboardForNewUserWalkthrough,
			},
		},
		IsEnabled: true,
	}
	return walkthroughResponse
}

// Method to get dashboard intro card for new user walkthrough
func (s *Service) getDashboardIntroCardForNewUserWalkthrough(ctx context.Context, actorId string) *walkthrough.DashboardIntroCardDetails {
	actorName := ""
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 500*time.Millisecond)
	defer cancel()
	entityRes, err := s.actorClient.GetEntityDetailsByActorId(ctxWithTimeout, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(entityRes, err); err != nil {
		logger.Error(ctx, "failed to get actor by Id", zap.Error(err))
	} else {
		actorName = entityRes.GetName().GetFirstName()
	}

	getStartedTitle := "Get started"
	startWalkthroughTitle := fmt.Sprintf(NewUserWalkThroughTitle, actorName)
	return &walkthrough.DashboardIntroCardDetails{
		StartCta: &walkthrough.CTA{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: getStartedTitle},
				FontColor:    FiGreen,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			BgColor: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Ink}},
			Image:   &commontypes.Image{ImageUrl: homeConstants.DashboardIntroCardGettingStartedIconOnStartBtn},
		},
		ShowCardMaxImpressions: MaxWalkThroughImpressionsPerUser,
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: startWalkthroughTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
		},
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
			Source: &commontypes.VisualElement_Image_Url{
				Url: homeConstants.WaveIconForOnboardingIntroTour,
			},
		}}},
		Shadow: ui.GetDashboardShadow(),
	}
}

// Method to get the dashboard intro card for old user walkthrough, when a new element is added in the new layout
func (s *Service) getDashboardIntroCardForOldUserWalkthroughNewlyAddedElement() *walkthrough.DashboardIntroCardDetails {
	return &walkthrough.DashboardIntroCardDetails{
		StartCta: &walkthrough.CTA{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Show me"},
				FontColor:    FiGreen,
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			BgColor: &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: Ink}},
			Image:   &commontypes.Image{ImageUrl: homeConstants.DashboardIntroCardGettingStartedIconOnStartBtn},
		},
		ShowCardMaxImpressions: MaxWalkThroughImpressionsPerUser,
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Something new is \nwaiting for you"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
		},
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
			Source: &commontypes.VisualElement_Image_Url{
				Url: homeConstants.WaveIconForOnboardingIntroTour,
			},
		}}},
		Shadow: ui.GetDashboardShadow(),
	}
}

// Method to generate walkthrough for old users
// It gets walkthrough steps for elements which have changed position in the new layout
// It also gets walkthrough steps for elements which are newly added in the new layout
func (s *Service) generateWalkthroughForOldUser(ctx context.Context, slotIdToScreenElementIdMapFromPreference *config.SlotIdToScreenElementIdMap, slotIdToScreenElementIdMapForNewLayout *config.SlotIdToScreenElementIdMap, isUserPureFiLite bool) *walkthrough.Walkthrough {
	newlyAddedElementsWalkthroughInfo, positionChangedElementsWalkthroughInfo :=
		s.calculateLayoutDiffForNewlyAddedAndLayoutChangedElements(ctx, slotIdToScreenElementIdMapFromPreference, slotIdToScreenElementIdMapForNewLayout)

	isElementsWalkthroughInfoEmpty := true
	isEntryPointPresent := false
	var dashboardForOldUserWalkthrough *walkthrough.DashboardIntroCardDetails

	// If any new element is added, we need dashboard intro card
	if len(newlyAddedElementsWalkthroughInfo) > 0 {
		isElementsWalkthroughInfoEmpty = false
		isEntryPointPresent = true
		dashboardForOldUserWalkthrough = s.getDashboardIntroCardForOldUserWalkthroughNewlyAddedElement()
	}
	if len(positionChangedElementsWalkthroughInfo) > 0 {
		isElementsWalkthroughInfoEmpty = false
	}

	walkthroughStepsForPositionChangedElements := s.getWalkthroughStepsFromWalkthroughInfo(positionChangedElementsWalkthroughInfo, false, false, isUserPureFiLite)
	walkthroughStepsForNewlyAddedElements := s.getWalkthroughStepsFromWalkthroughInfo(newlyAddedElementsWalkthroughInfo, true, false, isUserPureFiLite)

	walkthroughStepsResponse := make([]*walkthrough.WalkthroughStep, 0)
	if !isElementsWalkthroughInfoEmpty && !isUserPureFiLite {
		walkthroughStepsResponse = append(walkthroughStepsResponse, s.generateFullScreenWalkthroughStepOnWalkthroughStartForOldUser())
	}

	trigger := walkthrough.WalkthroughTrigger_WALKTHROUGH_TRIGGER_AUTO
	if isEntryPointPresent {
		trigger = walkthrough.WalkthroughTrigger_WALKTHROUGH_TRIGGER_AUTO_AND_ENTRY_POINT
	}
	walkthroughStepsResponse = append(walkthroughStepsResponse, walkthroughStepsForPositionChangedElements...)
	walkthroughStepsResponse = append(walkthroughStepsResponse, walkthroughStepsForNewlyAddedElements...)

	if len(walkthroughStepsResponse) == 0 {
		return nil
	}
	walkthroughResponse := &walkthrough.Walkthrough{
		Id:      uuid.NewString(),
		Steps:   walkthroughStepsResponse,
		Trigger: trigger,
		EntryPoint: &walkthrough.WalkthroughEntryPoint{
			EntryPoint: &walkthrough.WalkthroughEntryPoint_DashboardIntroCardDetails{
				DashboardIntroCardDetails: dashboardForOldUserWalkthrough,
			},
		},
		IsEnabled: true,
	}
	return walkthroughResponse
}

// Method to get full screen walkthrough on walkthrough start for new user
func (s *Service) generateFullScreenWalkthroughStepOnWalkthroughStartForNewUser() *walkthrough.WalkthroughStep {
	walkthroughStep := &walkthrough.WalkthroughStep{}
	fullScreenAnnouncementStep := &walkthrough.FullScreenAnnouncementStep{
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
			Source: &commontypes.VisualElement_Image_Url{
				Url: "https://epifi-icons.pointz.in/home/<USER>/hello.jpg",
			},
		}}},
		Message: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Welcome to Fi",
			},
		},
	}
	walkthroughStep.Step = &walkthrough.WalkthroughStep_FullScreenAnnouncementStep{FullScreenAnnouncementStep: fullScreenAnnouncementStep}
	return walkthroughStep
}

// nolint:dupl
// Method to get full screen walkthrough on walkthrough start for old user
func (s *Service) generateFullScreenWalkthroughStepOnWalkthroughStartForOldUser() *walkthrough.WalkthroughStep {
	walkthroughStep := &walkthrough.WalkthroughStep{}
	fullScreenAnnouncementStep := &walkthrough.FullScreenAnnouncementStep{
		VisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
			Source: &commontypes.VisualElement_Image_Url{
				Url: "https://epifi-icons.pointz.in/home/<USER>/lightening.png",
			},
		}}},
		Message: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "For a faster experience, \nwe’ve made some fresh \nchanges to your Home!",
			},
		},
		PrimaryCta: &walkthrough.CTA{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Show me",
				},
				FontColor: "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			},
			Action: &walkthrough.CTA_CustomAction{
				CustomAction: &walkthrough.CustomAction{
					Action: walkthrough.Action(walkthrough.Action_value["GO_TO_NEXT_WALKTHROUGH_STEP"]),
				},
			},
			BgColor: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#F7F9FC",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
		},
	}
	walkthroughStep.Step = &walkthrough.WalkthroughStep_FullScreenAnnouncementStep{FullScreenAnnouncementStep: fullScreenAnnouncementStep}
	return walkthroughStep
}

// nolint:funlen,dupl
// Method to generate walkthrough steps from walkthrough info
func (s *Service) getWalkthroughStepsFromWalkthroughInfo(walkthroughInfoForElements []*config.WalkthroughInfo, isLastSetOfWalkthroughInfos bool, isNewUserWalkthrough bool, isFiLiteUser bool) []*walkthrough.WalkthroughStep {
	walkthroughSteps := make([]*walkthrough.WalkthroughStep, 0)
	for idx, walkthroughInfo := range walkthroughInfoForElements {
		walkthroughStep := &walkthrough.WalkthroughStep{}
		primaryCtaTextDisplayValue := "Next"
		primaryCtaCustomActionString := "GO_TO_NEXT_WALKTHROUGH_STEP"
		secondaryCtaCustomActionString := "SKIP_ALL_WALKTHROUGH_STEPS"
		message := walkthroughInfo.LayoutChangeDescription
		switch {
		case isNewUserWalkthrough:
			message = walkthroughInfo.NewUserDescription
		case isFiLiteUser:
			message = walkthroughInfo.FiLiteLayoutChangeDescription
		}
		if isLastSetOfWalkthroughInfos && idx == len(walkthroughInfoForElements)-1 {
			primaryCtaTextDisplayValue = "Got it"
		}
		if walkthroughInfo.ElementId == "privacysettings-1" {
			walkthroughStep.Step = &walkthrough.WalkthroughStep_FocusScreenElementStep{FocusScreenElementStep: s.getPrivacySettingsFocusElementStep()}
			walkthroughSteps = append(walkthroughSteps, walkthroughStep)
		} else if message != "" {
			describeScreenElementStep := &walkthrough.DescribeScreenElementStep{
				ElementId: walkthroughInfo.ElementId,
				Message: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: message,
					},
				},
				PrimaryCta: &walkthrough.CTA{
					Text: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: primaryCtaTextDisplayValue,
						},
						FontColor: "#00B899",
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
					},
					Action: &walkthrough.CTA_CustomAction{
						CustomAction: &walkthrough.CustomAction{
							Action: walkthrough.Action(walkthrough.Action_value[primaryCtaCustomActionString]),
						},
					},
					BgColor: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_BlockColour{
							BlockColour: "#F7F9FC",
						},
					},
					Shadow: &ui.Shadow{
						Height: 4,
						Colour: &ui.BackgroundColour{
							Colour: &ui.BackgroundColour_BlockColour{
								BlockColour: "#CED2D6",
							},
						},
					},
				},
				SecondaryCta: &walkthrough.CTA{
					Text: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "Skip",
						},
					},
					Action: &walkthrough.CTA_CustomAction{
						CustomAction: &walkthrough.CustomAction{
							Action: walkthrough.Action(walkthrough.Action_value[secondaryCtaCustomActionString]),
						},
					},
					BgColor: &ui.BackgroundColour{
						Colour: &ui.BackgroundColour_BlockColour{
							BlockColour: "#28292B",
						},
					},
				},
			}
			walkthroughStep.Step = &walkthrough.WalkthroughStep_DescribeScreenElementStep{DescribeScreenElementStep: describeScreenElementStep}
			walkthroughSteps = append(walkthroughSteps, walkthroughStep)

			if walkthroughInfo.ElementId == "shortcuts-1" {
				focusWalkthroughStepForShortcuts := &walkthrough.WalkthroughStep{}
				focusWalkthroughStepForShortcuts.Step = &walkthrough.WalkthroughStep_FocusScreenElementStep{FocusScreenElementStep: s.getShortcutsFocusElementWalkthroughStep()}
				walkthroughSteps = append(walkthroughSteps, focusWalkthroughStepForShortcuts)
			}
		}
	}
	return walkthroughSteps
}

// nolint:dupl
// Method to get focus element step for privacy settings
func (s *Service) getPrivacySettingsFocusElementStep() *walkthrough.FocusScreenElementStep {
	return &walkthrough.FocusScreenElementStep{
		FocusElementType: walkthrough.FocusScreenElementStep_DASHBOARD_PRIVACY,
		Message: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "View your account details \nfrom here",
			},
		},
		PrimaryCta: &walkthrough.CTA{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Next",
				},
				FontColor: "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			},
			Action: &walkthrough.CTA_CustomAction{
				CustomAction: &walkthrough.CustomAction{
					Action: walkthrough.Action(walkthrough.Action_value["GO_TO_NEXT_WALKTHROUGH_STEP"]),
				},
			},
			BgColor: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#F7F9FC",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
		},
		SecondaryCta: &walkthrough.CTA{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Skip",
				},
				FontColor: "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			},
			Action: &walkthrough.CTA_CustomAction{
				CustomAction: &walkthrough.CustomAction{
					Action: walkthrough.Action(walkthrough.Action_value["SKIP_ALL_WALKTHROUGH_STEPS"]),
				},
			},
			BgColor: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#28292B",
				},
			},
		},
		ParentElementId: "primarysavings-1",
	}
}

// nolint:dupl
// Method to get focus element walkthrough step
func (s *Service) getShortcutsFocusElementWalkthroughStep() *walkthrough.FocusScreenElementStep {
	return &walkthrough.FocusScreenElementStep{
		FocusElementType: walkthrough.FocusScreenElementStep_ADD_AND_MANAGE_SHORTCUTS,
		Message: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Tap to personalise \nyour Shortcuts",
			},
		},
		PrimaryCta: &walkthrough.CTA{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Ok, got it",
				},
				FontColor: "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			},
			Action: &walkthrough.CTA_CustomAction{
				CustomAction: &walkthrough.CustomAction{
					Action: walkthrough.Action(walkthrough.Action_value["GO_TO_NEXT_WALKTHROUGH_STEP"]),
				},
			},
			BgColor: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#F7F9FC",
				},
			},
			Shadow: &ui.Shadow{
				Height: 4,
				Colour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#CED2D6",
					},
				},
			},
		},
		SecondaryCta: &walkthrough.CTA{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Skip",
				},
				FontColor: "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			},
			Action: &walkthrough.CTA_CustomAction{
				CustomAction: &walkthrough.CustomAction{
					Action: walkthrough.Action(walkthrough.Action_value["SKIP_ALL_WALKTHROUGH_STEPS"]),
				},
			},
			BgColor: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#28292B",
				},
			},
		},
		ParentElementId: "shortcuts-1",
	}
}

// Method to get home layout and walkthrough for new user
func (s *Service) getHomeLayoutAndWalkthroughForNewUser(ctx context.Context, actorId string, userType homePkg.UserType, slotIdToScreenElementIdMap *config.SlotIdToScreenElementIdMap, layoutParams *genconf.HomeLayoutV2Params) *feHomePb.GetHomeLayoutAndWalkthroughResponse {
	// generate home layout from slot id to screen element id map
	newHomeLayout, err := s.constructHomeLayout(ctx, actorId, slotIdToScreenElementIdMap, layoutParams)
	if err != nil {
		logger.Error(ctx, "error while constructing home layout from slot id to screen element id map", zap.Error(err))
		return &feHomePb.GetHomeLayoutAndWalkthroughResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("failed to construct home layout from slot id to screen element id map")},
		}
	}

	goroutine.Run(ctx, 2*time.Second, func(ctx context.Context) {
		logger.Debug(ctx, "setting home layout for new user")
		s.setHomeLayoutPreferenceForUser(ctx, actorId, slotIdToScreenElementIdMap)
	})

	walkthroughResponse := s.generateWalkthroughForNewUser(ctx, actorId, userType, slotIdToScreenElementIdMap)

	return &feHomePb.GetHomeLayoutAndWalkthroughResponse{
		RespHeader:           &header.ResponseHeader{Status: rpc.StatusOk()},
		Layout:               newHomeLayout,
		Walkthrough:          walkthroughResponse,
		TopSectionBackground: feHomePb.GetHomeTopSectionBackground(),
	}
}

// Method to get the walkthrough info for new user layout
// It just gets element id's from configs for new user
// It then sorts the layout diff elements - sorting is not much required in this case
// but the same method is being reused to get walkthrough info's
func (s *Service) getWalkthroughInfoForNewUserLayout(ctx context.Context, targetLayout *config.SlotIdToScreenElementIdMap) []*config.WalkthroughInfo {
	elementIdList := s.genconf.HomeRevampParams().HomeLayoutConfigurationV2Params().NewUserWalkthroughElementIdConfig()
	var newlyAddedElementIdsForWalkthroughGeneration []string
	for _, elementId := range elementIdList.ToStringArray() {
		newlyAddedElementIdsForWalkthroughGeneration = append(newlyAddedElementIdsForWalkthroughGeneration, elementId)
	}
	return s.sortLayoutDiffElementsWalkthroughInfo(ctx, newlyAddedElementIdsForWalkthroughGeneration, targetLayout, true)
}

// Method to return walkthrough infos for elements which are changed (new, position changed, etc).
// First it calculates for newly added elements in new layout.
// It then checks if any of the elements have different slots in new layout
func (s *Service) calculateLayoutDiffForNewlyAddedAndLayoutChangedElements(ctx context.Context, sourceLayout *config.SlotIdToScreenElementIdMap,
	targetLayout *config.SlotIdToScreenElementIdMap) ([]*config.WalkthroughInfo, []*config.WalkthroughInfo) {
	sourceLayoutScreenElementsToSlotMap := getScreenElementToSlotMap(sourceLayout)
	targetLayoutScreenElementsToSlotMap := getScreenElementToSlotMap(targetLayout)

	var newlyAddedElementIdsForWalkthroughGeneration []string
	var positionChangedElementIdsForWalkthroughGeneration []string
	// check if any of the element in target layout is present in source layout. If it's not present, that means it's
	// a new addition, hence show walkthrough
	newlyAddedElementIdsForWalkthroughGeneration = append(newlyAddedElementIdsForWalkthroughGeneration, getNewlyAddedScreenElementsInTargetLayout(targetLayout,
		sourceLayoutScreenElementsToSlotMap)...)

	// check if element has changed position in the target layout, based on threshold, check if its eligible for walkthrough
	positionChangedElementIdsForWalkthroughGeneration = append(positionChangedElementIdsForWalkthroughGeneration,
		s.getElementsWhichChangedSlotsFromSourceToTargetLayout(sourceLayoutScreenElementsToSlotMap, targetLayoutScreenElementsToSlotMap)...)

	// sort the list according to priority of items - who's walkthrough is to be shown first
	positionChangedElementsWalkthroughInfo := s.sortLayoutDiffElementsWalkthroughInfo(ctx, positionChangedElementIdsForWalkthroughGeneration, targetLayout, false)
	newlyAddedElementsWalkthroughInfo := s.sortLayoutDiffElementsWalkthroughInfo(ctx, newlyAddedElementIdsForWalkthroughGeneration, targetLayout, false)

	return newlyAddedElementsWalkthroughInfo, positionChangedElementsWalkthroughInfo
}

// nolint: funlen
// Method to sort the list according to priority of items - who's walkthrough is to be shown
// It returns the screen elements with walkthroughInfo
// first bottom nav bar, from left to right
// then vertical section from bottom to top
// then dashboard section from left to right
// then top nav bar from left to right
func (s *Service) sortLayoutDiffElementsWalkthroughInfo(ctx context.Context, listOfScreenElementsIdsForWalkthroughGeneration []string,
	targetLayout *config.SlotIdToScreenElementIdMap, isWalkthroughForNewUserLayout bool) []*config.WalkthroughInfo {
	var sortedElementWalkthroughList []*config.WalkthroughInfo
	homeLayoutConfigV2Params := s.genconf.HomeRevampParams().HomeLayoutConfigurationV2Params()
	sectionScreenElementsConfig := homeLayoutConfigV2Params.SectionScreenElementsConfig()
	// TODO: optimize the algo, maybe by converting into map and then searching the element
	slotsConfig := homeLayoutConfigV2Params.SectionSlotsConfig()
	for _, slotId := range slotsConfig.TopNavBarSectionSlotsConfig.LeftSlots {
		screenElementId := targetLayout.TopNavBarSlotSection.LeftSlots[slotId]
		if doesListContainsElementId(listOfScreenElementsIdsForWalkthroughGeneration, screenElementId) {
			screenElementWalkthroughInfo := sectionScreenElementsConfig.TopNavBarSection[screenElementId].WalkthroughInfo
			if screenElementWalkthroughInfo == nil {
				logger.Debug(ctx, fmt.Sprintf("WalkthroughInfo not present for screen element id %v", screenElementId))
				continue
			}
			sortedElementWalkthroughList = append(sortedElementWalkthroughList, screenElementWalkthroughInfo)
		}
	}
	for _, slotId := range slotsConfig.TopNavBarSectionSlotsConfig.RightSlots {
		screenElementId := targetLayout.TopNavBarSlotSection.RightSlots[slotId]
		if doesListContainsElementId(listOfScreenElementsIdsForWalkthroughGeneration, screenElementId) {
			screenElementWalkthroughInfo := sectionScreenElementsConfig.TopNavBarSection[screenElementId].WalkthroughInfo
			if screenElementWalkthroughInfo == nil {
				logger.Debug(ctx, fmt.Sprintf("WalkthroughInfo not present for screen element id %v", screenElementId))
				continue
			}
			sortedElementWalkthroughList = append(sortedElementWalkthroughList, screenElementWalkthroughInfo)
		}
	}
	for _, slotId := range slotsConfig.DashboardSectionSlotsConfig {
		screenElementId := targetLayout.DashboardSlotSection[slotId]
		if doesListContainsElementId(listOfScreenElementsIdsForWalkthroughGeneration, screenElementId) {
			screenElementWalkthroughInfo := sectionScreenElementsConfig.DashboardSection[screenElementId].WalkthroughInfo
			if screenElementWalkthroughInfo == nil {
				logger.Debug(ctx, fmt.Sprintf("WalkthroughInfo not present for screen element id %v", screenElementId))
				continue
			}
			sortedElementWalkthroughList = append(sortedElementWalkthroughList, screenElementWalkthroughInfo)
			// handle case for privacy settings for new user walkthrough
			if screenElementId == "primarysavings-1" && isWalkthroughForNewUserLayout &&
				doesListContainsElementId(listOfScreenElementsIdsForWalkthroughGeneration, "privacysettings-1") {
				screenElementWalkthroughInfo := &config.WalkthroughInfo{
					ElementId: "privacysettings-1",
				}
				sortedElementWalkthroughList = append(sortedElementWalkthroughList, screenElementWalkthroughInfo)
			}
		}
	}
	for _, slotId := range slotsConfig.VerticalSectionSlotsConfig {
		screenElementId := targetLayout.VerticalSlotSection[slotId]
		if doesListContainsElementId(listOfScreenElementsIdsForWalkthroughGeneration, screenElementId) {
			screenElementWalkthroughInfo := sectionScreenElementsConfig.VerticalSection[screenElementId].WalkthroughInfo
			if screenElementWalkthroughInfo == nil {
				logger.Debug(ctx, fmt.Sprintf("WalkthroughInfo not present for screen element id %v", screenElementId))
				continue
			}
			sortedElementWalkthroughList = append(sortedElementWalkthroughList, screenElementWalkthroughInfo)
		}
	}
	for _, slotId := range slotsConfig.BottomNavBarSectionSlotsConfig {
		screenElementId := targetLayout.BottomNavBarSlotSection[slotId]
		if doesListContainsElementId(listOfScreenElementsIdsForWalkthroughGeneration, screenElementId) {
			screenElementWalkthroughInfo := sectionScreenElementsConfig.BottomNavBarSection.ScreenElementsMapping[screenElementId].WalkthroughInfo
			if screenElementWalkthroughInfo == nil {
				logger.Debug(ctx, fmt.Sprintf("WalkthroughInfo not present for screen element id %v", screenElementId))
				continue
			}
			sortedElementWalkthroughList = append(sortedElementWalkthroughList, screenElementWalkthroughInfo)
		}
	}
	return sortedElementWalkthroughList
}

// Method to check if list of screen element id's contains the particular screen element id
// TODO: refactor this in view of optimisations as mentioned in caller method
func doesListContainsElementId(screenElementIds []string, screenElementId string) bool {
	for _, screenElement := range screenElementIds {
		if screenElement == screenElementId {
			return true
		}
	}
	return false
}

// check if any of the element in source layout is present in target layout. If it's not present, then no need to
// show walkthrough.
// If it's present, then if its present in another section, then show walkthrough
// if it's present in same section, then check its new slot, and if its farther than 2 slots, then show walkthrough
func (s *Service) getElementsWhichChangedSlotsFromSourceToTargetLayout(sourceLayoutScreenElementsToSlotMap *config.ScreenElementIdToSlotIdMap,
	targetLayoutScreenElementsToSlotMap *config.ScreenElementIdToSlotIdMap) []string {
	var diffElements []string
	var sourceSection map[string]string
	var targetSection map[string]string

	sourceSection = sourceLayoutScreenElementsToSlotMap.TopNavBarSlotSection.LeftSlots
	targetSection = targetLayoutScreenElementsToSlotMap.TopNavBarSlotSection.LeftSlots
	diffElements = append(diffElements, s.getDiffElementsForSectionInTargetLayout(targetLayoutScreenElementsToSlotMap, sourceSection, targetSection)...)

	sourceSection = sourceLayoutScreenElementsToSlotMap.TopNavBarSlotSection.RightSlots
	targetSection = targetLayoutScreenElementsToSlotMap.TopNavBarSlotSection.RightSlots
	diffElements = append(diffElements, s.getDiffElementsForSectionInTargetLayout(targetLayoutScreenElementsToSlotMap, sourceSection, targetSection)...)

	sourceSection = sourceLayoutScreenElementsToSlotMap.DashboardSlotSection
	targetSection = targetLayoutScreenElementsToSlotMap.DashboardSlotSection
	diffElements = append(diffElements, s.getDiffElementsForSectionInTargetLayout(targetLayoutScreenElementsToSlotMap, sourceSection, targetSection)...)

	sourceSection = sourceLayoutScreenElementsToSlotMap.VerticalSlotSection
	targetSection = targetLayoutScreenElementsToSlotMap.VerticalSlotSection
	diffElements = append(diffElements, s.getDiffElementsForSectionInTargetLayout(targetLayoutScreenElementsToSlotMap, sourceSection, targetSection)...)

	sourceSection = sourceLayoutScreenElementsToSlotMap.BottomNavBarSection
	targetSection = targetLayoutScreenElementsToSlotMap.BottomNavBarSection
	diffElements = append(diffElements, s.getDiffElementsForSectionInTargetLayout(targetLayoutScreenElementsToSlotMap, sourceSection, targetSection)...)

	return diffElements
}

// Method to get elements for walkthrough generation, which
// 1. Are now present in different section in new layout
// 2. Or are at different slot compared to original layout - returns based on threshold
func (s *Service) getDiffElementsForSectionInTargetLayout(targetLayoutScreenElementsToSlotMap *config.ScreenElementIdToSlotIdMap,
	sourceSection map[string]string, targetSection map[string]string) []string {
	var diffElements []string
	for screenElement := range sourceSection {
		if screenElement == "" {
			continue
		}
		if isScreenElementPresentInLayout(targetLayoutScreenElementsToSlotMap, screenElement) {
			if isScreenElementPresentInGivenSection(screenElement, targetSection) {
				// case when screen element is present in same section, but crosses threshold for source and target number difference
				if s.doesScreenElementSlotDiffCrossThreshold(screenElement, sourceSection, targetSection) {
					diffElements = append(diffElements, screenElement)
				}
			} else {
				// case when screen element is present in different section
				diffElements = append(diffElements, screenElement)
			}
		}
	}
	return diffElements
}

// Method to calculate the diff of slot numbers for the screen element, in the same section.
// Returns boolean according to threshold check
func (s *Service) doesScreenElementSlotDiffCrossThreshold(screenElement string, sourceSection map[string]string, targetSection map[string]string) bool {
	// calculate slot diff
	newSlot := sourceSection[screenElement]
	oldSlot := targetSection[screenElement]
	// get the number as string separately
	newSlotSplitString := strings.Split(newSlot, "_")
	oldSlotSplitString := strings.Split(oldSlot, "_")
	// get number as integer from string
	newSlotNumber, _ := strconv.Atoi(newSlotSplitString[len(newSlotSplitString)-1])
	oldSlotNumber, _ := strconv.Atoi(oldSlotSplitString[len(oldSlotSplitString)-1])

	return s.abs(newSlotNumber, oldSlotNumber) >= 5 // TODO: make this configurable
}

// Method to check if the screen element is present in the given section
func isScreenElementPresentInGivenSection(screenElement string, section map[string]string) bool {
	if _, found := section[screenElement]; found {
		return true
	}
	return false
}

// Method to get the screen elements which are newly added in the new layout
func getNewlyAddedScreenElementsInTargetLayout(targetLayout *config.SlotIdToScreenElementIdMap,
	sourceLayoutScreenElementsToSlotMap *config.ScreenElementIdToSlotIdMap) []string {
	var newlyAddedElements []string
	for _, screenElementId := range targetLayout.TopNavBarSlotSection.LeftSlots {
		if screenElementId != "" && !isScreenElementPresentInLayout(sourceLayoutScreenElementsToSlotMap, screenElementId) {
			newlyAddedElements = append(newlyAddedElements, screenElementId)
		}
	}
	for _, screenElementId := range targetLayout.TopNavBarSlotSection.RightSlots {
		if screenElementId != "" && !isScreenElementPresentInLayout(sourceLayoutScreenElementsToSlotMap, screenElementId) {
			newlyAddedElements = append(newlyAddedElements, screenElementId)
		}
	}
	for _, screenElementId := range targetLayout.DashboardSlotSection {
		if screenElementId != "" && !isScreenElementPresentInLayout(sourceLayoutScreenElementsToSlotMap, screenElementId) {
			newlyAddedElements = append(newlyAddedElements, screenElementId)
		}
	}
	for _, screenElementId := range targetLayout.VerticalSlotSection {
		if screenElementId != "" && !isScreenElementPresentInLayout(sourceLayoutScreenElementsToSlotMap, screenElementId) {
			newlyAddedElements = append(newlyAddedElements, screenElementId)
		}
	}
	for _, screenElementId := range targetLayout.BottomNavBarSlotSection {
		if screenElementId != "" && !isScreenElementPresentInLayout(sourceLayoutScreenElementsToSlotMap, screenElementId) {
			newlyAddedElements = append(newlyAddedElements, screenElementId)
		}
	}
	return newlyAddedElements
}

// Method to generate screen element to slot map
func getScreenElementToSlotMap(sourceLayout *config.SlotIdToScreenElementIdMap) *config.ScreenElementIdToSlotIdMap {
	ScreenElementIdToSlotIdMap := &config.ScreenElementIdToSlotIdMap{
		TopNavBarSlotSection: &config.TopNavBarSlotSection{
			LeftSlots:  make(map[string]string),
			RightSlots: make(map[string]string),
		},
		DashboardSlotSection: make(map[string]string),
		VerticalSlotSection:  make(map[string]string),
		BottomNavBarSection:  make(map[string]string),
	}

	for slot, screenElement := range sourceLayout.TopNavBarSlotSection.LeftSlots {
		ScreenElementIdToSlotIdMap.TopNavBarSlotSection.LeftSlots[screenElement] = slot
	}
	for slot, screenElement := range sourceLayout.TopNavBarSlotSection.RightSlots {
		ScreenElementIdToSlotIdMap.TopNavBarSlotSection.RightSlots[screenElement] = slot
	}
	for slot, screenElement := range sourceLayout.DashboardSlotSection {
		ScreenElementIdToSlotIdMap.DashboardSlotSection[screenElement] = slot
	}
	for slot, screenElement := range sourceLayout.VerticalSlotSection {
		ScreenElementIdToSlotIdMap.VerticalSlotSection[screenElement] = slot
	}
	for slot, screenElement := range sourceLayout.BottomNavBarSlotSection {
		ScreenElementIdToSlotIdMap.BottomNavBarSection[screenElement] = slot
	}
	return ScreenElementIdToSlotIdMap
}

// Method to check if the screen element in present in the layout, in any of the section
func isScreenElementPresentInLayout(layoutScreenElementsToSlotMap *config.ScreenElementIdToSlotIdMap, screenElement string) bool {
	if _, found := layoutScreenElementsToSlotMap.TopNavBarSlotSection.LeftSlots[screenElement]; found {
		return true
	}
	if _, found := layoutScreenElementsToSlotMap.TopNavBarSlotSection.RightSlots[screenElement]; found {
		return true
	}
	if _, found := layoutScreenElementsToSlotMap.DashboardSlotSection[screenElement]; found {
		return true
	}
	if _, found := layoutScreenElementsToSlotMap.VerticalSlotSection[screenElement]; found {
		return true
	}
	if _, found := layoutScreenElementsToSlotMap.BottomNavBarSection[screenElement]; found {
		return true
	}
	return false
}

// Method to set the app home layout preference in user preference table, for the user
func (s *Service) setHomeLayoutPreferenceForUser(ctx context.Context, actorId string, slotIdToScreenElementIdMapForNewLayout *config.SlotIdToScreenElementIdMap) {
	marshalledAppHomeLayoutPreferenceJson, _ := json2.Marshal(slotIdToScreenElementIdMapForNewLayout)
	preferenceTypeValuePair := &userPb.PreferenceTypeValuePair{
		PreferenceType: userPb.PreferenceType_PREFERENCE_TYPE_APP_HOME_LAYOUT,
		PreferenceValue: &userPb.PreferenceValue{
			PrefVal: &userPb.PreferenceValue_AppHomeLayoutPreference{
				AppHomeLayoutPreference: &userPb.AppHomeLayoutPreference{
					AppHomeLayout: string(marshalledAppHomeLayoutPreferenceJson),
				},
			},
		},
	}
	setUserPreferenceRequest := &userPb.SetUserPreferencesRequest{
		ActorId:     actorId,
		Preferences: []*userPb.PreferenceTypeValuePair{preferenceTypeValuePair},
	}
	_, err := s.usersClient.SetUserPreferences(ctx, setUserPreferenceRequest)
	if err != nil {
		logger.Error(ctx, "Error while saving user preference for app home layout")
	}
}

func (s *Service) getSlotIdToScreenElementIdMapFromString(layoutString string) (*config.SlotIdToScreenElementIdMap, error) {
	layout := &config.SlotIdToScreenElementIdMap{}
	err := json2.Unmarshal([]byte(layoutString), layout)
	if err != nil {
		metrics.RecordHomeLayoutUnmarshalFailureCount()
		return nil, fmt.Errorf("failed to unmarshal val, err: %w", err)
	}
	return layout, nil
}

// Method to get the app home layout preference from user preference table, for the user
func (s *Service) getHomeLayoutPreferenceForUser(ctx context.Context, actorId string) (string, *timestampPb.Timestamp, error) {
	userPrefResp, err := s.usersClient.GetUserPreferences(ctx, &userPb.GetUserPreferencesRequest{
		ActorId:         actorId,
		PreferenceTypes: []userPb.PreferenceType{userPb.PreferenceType_PREFERENCE_TYPE_APP_HOME_LAYOUT},
	})
	if rpcErr := epifigrpc.RPCError(userPrefResp, err); rpcErr != nil {
		if userPrefResp.GetStatus().IsRecordNotFound() {
			return "", nil, epifierrors.ErrRecordNotFound
		}
		return "", nil, fmt.Errorf("error in fetching app home layout, err: %w", rpcErr)
	}
	preferenceValues := userPrefResp.GetUserPreferences()[0].GetPreferenceValue().GetAppHomeLayoutPreference().GetAppHomeLayout()
	createdAt := userPrefResp.GetUserPreferences()[0].GetCreatedAt()

	return preferenceValues, createdAt, nil
}

func (s *Service) getDebitCardScreenWithFeatureCheck(ctx context.Context, actorId string) deeplink.Screen {
	enabled, err := s.IsFeatureEnabled(ctx, types.Feature_FEATURE_DC_DASHBOARD_V2_SCREEN, actorId)
	// in case of an error of feature not enabled, we will show the older dashboard ui
	if err != nil || !enabled {
		return deeplink.Screen_CARD_HOME_SCREEN
	}
	return deeplink.Screen_DC_DASHBOARD_V2_SCREEN
}

func (s *Service) shouldShowWalkthroughForOldUser(userType homePkg.UserType, homeLayoutPreferenceCreatedAt *timestampPb.Timestamp) bool {
	walkthroughCoolOffPeriod := float64(0)
	if userType != homePkg.UserTypeFiLite {
		walkthroughCoolOffPeriod = walkthroughCoolOffPeriodInDays
	}
	return time.Since(homeLayoutPreferenceCreatedAt.AsTime()).Hours()/24 > walkthroughCoolOffPeriod
}

func getTransferInScreenOptions(ctx context.Context, account *savingsPb.Account) *deeplink.Deeplink_TransferInScreenOptions {
	var (
		transferInScreenOptions *deeplink.Deeplink_TransferInScreenOptions
	)

	derivedAccountId, err := payPkg.GetEncodedDerivedAccountId(account.GetId(), "", "")
	if err != nil {
		logger.Error(ctx, "error getting derived account id from internal account id", zap.Error(err))
		return nil
	}

	if derivedAccountId != "" {
		transferInScreenOptions = &deeplink.Deeplink_TransferInScreenOptions{
			TransferInScreenOptions: &deeplink.TransferInScreenOptions{
				PayeeUserIdentifier: &user_identifier.UserIdentifier{
					DerivedAccountId:       derivedAccountId,
					AccountProductOffering: account.GetSkuInfo().GetAccountProductOffering(),
				},
			},
		}
	}
	return transferInScreenOptions
}

// getDefaultSelectedIconIdForBottomNavBarWidget returns default selected icon id for bottom nav bar widget in the following priority order -
// 1. If there's an active experiment running, we return the icon id coming from that experiment
// 2. If user belongs to any of the segment expression in the config, we return the icon id corresponding to the "last" expression
func (s *Service) getDefaultSelectedIconIdForBottomNavBarWidget(ctx context.Context, actorId string, layoutV2Params *genconf.HomeLayoutV2Params) string {
	// since experiments change default to some id other than home, using that as a check to see if the value is being returned from experiment
	// if yes, we return early since the experiments take priority over all the config driven segmented default selected icon identifiers
	iconId := layoutV2Params.DefaultSelectedIconIdForBottomNavBarWidget(ctx)
	if iconId != homeConstants.HomeBottomNavBarId {
		return iconId
	}

	// gather segment expression to icon ids in a map
	segmentExpressionToIconIdMap := make(map[string]string)
	for _, segmentExpressionToIconId := range layoutV2Params.SegmentExpressionToDefaultSelectedIconIdForBottomNavBarWidget() {
		segmentExpressionToIconIdMap[segmentExpressionToIconId.SegmentExpression] = segmentExpressionToIconId.DefaultSelectedIconIdForBottomNavBarWidget
	}
	if len(segmentExpressionToIconIdMap) == 0 {
		return iconId
	}

	// make bulk call to check actor belongs to which segment expressions
	isMemberOfExpressionsRes, err := s.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
		ActorId:              actorId,
		SegmentIdExpressions: lo.Keys(segmentExpressionToIconIdMap),
	})
	if rpcErr := epifigrpc.RPCError(isMemberOfExpressionsRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling is member of expressions rpc", zap.Error(err))
		return iconId
	}

	// return icon id corresponding to the last expression to which actor belongs
	segmentExpressionMembershipMap := isMemberOfExpressionsRes.GetSegmentExpressionMembershipMap()
	for _, segmentExpressionToIconId := range layoutV2Params.SegmentExpressionToDefaultSelectedIconIdForBottomNavBarWidget() {
		if segmentExpressionMembershipMap[segmentExpressionToIconId.SegmentExpression].GetIsActorMember() {
			iconId = segmentExpressionToIconId.DefaultSelectedIconIdForBottomNavBarWidget
		}
	}
	return iconId
}
