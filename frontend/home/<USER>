package home

import (
	"context"
	"fmt"
	"time"

	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	lendabilityPb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	"github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/frontend/pkg/featureflags"

	"github.com/redis/go-redis/v9"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/idgen"

	"github.com/epifi/gamma/api/upi/onboarding/enums"
	upiHelper "github.com/epifi/gamma/frontend/pkg/upi"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/user"

	"github.com/jinzhu/now"

	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>"
	fePkgUpi "github.com/epifi/gamma/frontend/pkg/upi"
	pkgUser "github.com/epifi/gamma/pkg/user"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	cpPb "github.com/epifi/gamma/api/card/provisioning"
	casperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/frontend/header"
	productPb "github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	vmPb "github.com/epifi/gamma/api/vendormapping"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/analyser/txnaggregates"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	beCaPb "github.com/epifi/gamma/api/connected_account"
	beDepositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/employment"
	ffBePb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/frontend/deeplink"
	homePb "github.com/epifi/gamma/api/frontend/home"
	"github.com/epifi/gamma/api/insights/story"
	mfCatalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/nudge"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	rmsPb "github.com/epifi/gamma/api/rms/manager"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	searchPb "github.com/epifi/gamma/api/search"
	segmentPb "github.com/epifi/gamma/api/segment"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/timeline"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/upcomingtransactions"
	userPb "github.com/epifi/gamma/api/user"
	groupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pkg/common" // Ensure common import
	feTieringRelease "github.com/epifi/gamma/frontend/tiering/release"
	wireTypes "github.com/epifi/gamma/frontend/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	questSdk "github.com/epifi/gamma/quest/sdk"
	"github.com/epifi/gamma/upcomingtransactions/helper"
)

type Service struct {
	homePb.UnimplementedHomeServer
	savClient                        savings.SavingsClient
	searchClient                     searchPb.ActionBarClient
	orderClient                      orderPb.OrderServiceClient
	actorClient                      actorPb.ActorClient
	timelineClient                   timeline.TimelineServiceClient
	piClient                         piPb.PiClient
	accountPiClient                  accountPiPb.AccountPIRelationClient
	depositClient                    beDepositPb.DepositClient
	depositIcons                     *config.DepositIcon
	homeV2DepositIcons               *config.DepositIcon
	addFundsCTA                      *config.CTA
	homeParams                       *config.HomeParams
	conf                             *config.Config
	genconf                          *genConf.Config
	usersClient                      userPb.UsersClient
	userGroupClient                  groupPb.GroupClient
	txnAggClient                     txnaggregates.TxnAggregatesClient
	rmsRuleManagerClient             rmsPb.RuleManagerClient
	mfCatalogManagerClient           mfCatalogPb.CatalogManagerClient
	recurringPaymentClient           recurringPaymentPb.RecurringPaymentServiceClient
	onboardingClient                 onboarding.OnboardingClient
	nudgesClient                     nudge.NudgeServiceClient
	recentActivitiesCollector        RecentActivitiesCollector
	releaseEvaluator                 *release.Evaluator
	kycClient                        kyc.KycClient
	salaryProgramClient              salaryPb.SalaryProgramClient
	vkycFeClient                     vkyc.VKYCFeClient
	beTieringClient                  beTieringPb.TieringClient
	evaluator                        release.IEvaluator
	bcClient                         bankcust.BankCustomerServiceClient
	feTieringReleaseEvaluator        feTieringRelease.FeManager
	storyClient                      story.StoryClient
	employmentClient                 employment.EmploymentClient
	segmentClient                    segmentPb.SegmentationServiceClient
	redisClient                      *redis.Client
	cacheStorage                     cache.CacheStorage
	commsClient                      commsPb.CommsClient
	beConnectedAccountClient         beCaPb.ConnectedAccountClient
	beFireflyClient                  ffBePb.FireflyClient
	ccDashboardWarningSvc            wireTypes.CcDashboardWarningSvc
	plDashboardWarningSvc            wireTypes.PlDashboardWarningSvc
	plNavigationBarHighlightSvc      wireTypes.PlNavigationBarHighlightSvc
	ussNavigationBarHighlightSvc     wireTypes.USSNavigationBarHighlightSvc
	rewardsNavigationBarHighlightSvc wireTypes.RewardsNavigationBarHighlightSvc
	accountBalanceClient             accountBalancePb.BalanceClient
	upcomingTxnClient                upcomingtransactions.UpcomingTransactionsClient
	upcomingTxnHelper                helper.IUpcomingTransactionsHelper
	timeClient                       datetime.Time
	authClient                       authPb.AuthClient
	questSdkClient                   *questSdk.Client
	vendorMappingClient              vmPb.VendorMappingServiceClient
	cardProvisioningClient           cpPb.CardProvisioningClient
	offerCatalogServiceClient        casperPb.OfferCatalogServiceClient
	upiOnboardingClient              upiOnboardingPb.UpiOnboardingClient
	layoutEngine                     layoutconfiguration.IEngine
	ffAccClient                      ffAccPb.AccountingClient
	userAttributeFetcher             pkgUser.UserAttributesFetcher
	productClient                    productPb.ProductClient
	tieringDataCollector             tieringData.DataCollector
	crossAttachClient                crossAttachPb.CrossAttachClient
	networthClient                   networthPb.NetWorthClient
	projectionClient                 rewardsProjectionPb.ProjectorServiceClient
	rewardsClient                    rewards.RewardsGeneratorClient
	lendabilityClient                lendabilityPb.LendabilityClient
}

const (
	oneMonth                   = "1 Month"
	openingBalanceTitle        = "Opening Balance"
	creditTitle                = "Money In"
	spentTitle                 = "Spent"
	savedTitle                 = "Saved"
	minArcLimit                = 40000 // TODO (keerthana): add this constant in money proto
	depositsTitle              = "Deposits"
	smartDepositTitle          = "Smart Deposit"
	fixedDepositTitle          = "Fixed Deposit"
	depositAllTimeSummaryRange = "All Time"
)

var (
	// NOTE: This is depricated and not used for fetching the recent activities of a user
	// Please refer the documenation on GetRecentActivity(Pay provider) to understand how pay recent activities are fetched
	whitelistedWorkflowsForRecentActivites = []orderPb.OrderWorkflow{
		orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		orderPb.OrderWorkflow_P2P_COLLECT,
		orderPb.OrderWorkflow_P2P_COLLECT_SHORT_CIRCUIT,
		orderPb.OrderWorkflow_NO_OP,
		orderPb.OrderWorkflow_OFF_APP_UPI,
		orderPb.OrderWorkflow_URN_TRANSFER,
		orderPb.OrderWorkflow_B2C_FUND_TRANSFER,
		orderPb.OrderWorkflow_REWARDS_CREATE_SD,
		orderPb.OrderWorkflow_REWARDS_ADD_FUNDS_SD,
		orderPb.OrderWorkflow_ADD_FUNDS,
		orderPb.OrderWorkflow_ADD_FUNDS_SD,
		orderPb.OrderWorkflow_ADD_FUNDS_COLLECT,
		orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT,
		orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH,
		orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH,
		orderPb.OrderWorkflow_P2P_INVESTMENT,
		orderPb.OrderWorkflow_P2P_WITHDRAWAL,
		orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER,
	}
)

func NewService(
	conf *config.Config,
	genconf *genConf.Config,
	savClient savings.SavingsClient,
	searchClient searchPb.ActionBarClient,
	orderClient orderPb.OrderServiceClient,
	actorClient actorPb.ActorClient,
	timelineClient timeline.TimelineServiceClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	depositClient beDepositPb.DepositClient,
	userGroupClient groupPb.GroupClient,
	usersClient userPb.UsersClient,
	txnAggClient txnaggregates.TxnAggregatesClient,
	rmsRuleManagerClient rmsPb.RuleManagerClient,
	mfCatalogManagerClient mfCatalogPb.CatalogManagerClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	onboardingClient onboarding.OnboardingClient,
	nudgesClient nudge.NudgeServiceClient,
	recentActivitiesCollector RecentActivitiesCollector,
	releaseEvaluator *release.Evaluator,
	kycClient kyc.KycClient,
	salaryProgramClient salaryPb.SalaryProgramClient,
	vkycFeClient vkyc.VKYCFeClient,
	beTieringClient beTieringPb.TieringClient,
	evaluator release.IEvaluator,
	bcClient bankcust.BankCustomerServiceClient,
	feTieringReleaseEvaluator feTieringRelease.FeManager,
	storyClient story.StoryClient,
	employmentClient employment.EmploymentClient,
	cacheStorage cache.CacheStorage,
	commsClient commsPb.CommsClient,
	beConnectedAccountClient beCaPb.ConnectedAccountClient,
	segmentClient segmentPb.SegmentationServiceClient,
	beFireflyClient ffBePb.FireflyClient,
	ccDashboardWarningSvc wireTypes.CcDashboardWarningSvc,
	plDashboardWarningSvc wireTypes.PlDashboardWarningSvc,
	plNavigationBarHighlightSvc wireTypes.PlNavigationBarHighlightSvc,
	ussNavigationBarHighlightSvc wireTypes.USSNavigationBarHighlightSvc,
	rewardsNavigationBarHighlightSvc wireTypes.RewardsNavigationBarHighlightSvc,
	accountBalanceClient accountBalancePb.BalanceClient,
	upcomingTxnClient upcomingtransactions.UpcomingTransactionsClient,
	upcomingTxnHelper helper.IUpcomingTransactionsHelper,
	timeClient datetime.Time,
	authClient authPb.AuthClient,
	questSdkClient *questSdk.Client,
	vendorMappingClient vmPb.VendorMappingServiceClient,
	cardProvisioningClient cpPb.CardProvisioningClient,
	offerCatalogServiceClient casperPb.OfferCatalogServiceClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	layoutEngine layoutconfiguration.IEngine,
	ffAccClient ffAccPb.AccountingClient,
	userAttributeFetcher pkgUser.UserAttributesFetcher,
	productClient productPb.ProductClient,
	dataCollector tieringData.DataCollector,
	crossAttachClient crossAttachPb.CrossAttachClient,
	networthClient networthPb.NetWorthClient,
	projectionClient rewardsProjectionPb.ProjectorServiceClient,
	rewardsClient rewards.RewardsGeneratorClient,
	lendabilityClient lendabilityPb.LendabilityClient,
) *Service {
	return &Service{
		homeParams:                       conf.HomeParams,
		savClient:                        savClient,
		searchClient:                     searchClient,
		orderClient:                      orderClient,
		actorClient:                      actorClient,
		timelineClient:                   timelineClient,
		piClient:                         piClient,
		accountPiClient:                  accountPiClient,
		depositClient:                    depositClient,
		depositIcons:                     conf.DepositIcons,
		homeV2DepositIcons:               conf.HomeV2DepositIcons,
		addFundsCTA:                      conf.AddFundsCTA,
		conf:                             conf,
		genconf:                          genconf,
		userGroupClient:                  userGroupClient,
		usersClient:                      usersClient,
		txnAggClient:                     txnAggClient,
		rmsRuleManagerClient:             rmsRuleManagerClient,
		mfCatalogManagerClient:           mfCatalogManagerClient,
		recurringPaymentClient:           recurringPaymentClient,
		onboardingClient:                 onboardingClient,
		nudgesClient:                     nudgesClient,
		recentActivitiesCollector:        recentActivitiesCollector,
		releaseEvaluator:                 releaseEvaluator,
		kycClient:                        kycClient,
		salaryProgramClient:              salaryProgramClient,
		vkycFeClient:                     vkycFeClient,
		beTieringClient:                  beTieringClient,
		evaluator:                        evaluator,
		bcClient:                         bcClient,
		feTieringReleaseEvaluator:        feTieringReleaseEvaluator,
		storyClient:                      storyClient,
		employmentClient:                 employmentClient,
		cacheStorage:                     cacheStorage,
		commsClient:                      commsClient,
		beConnectedAccountClient:         beConnectedAccountClient,
		segmentClient:                    segmentClient,
		beFireflyClient:                  beFireflyClient,
		ccDashboardWarningSvc:            ccDashboardWarningSvc,
		plDashboardWarningSvc:            plDashboardWarningSvc,
		plNavigationBarHighlightSvc:      plNavigationBarHighlightSvc,
		ussNavigationBarHighlightSvc:     ussNavigationBarHighlightSvc,
		rewardsNavigationBarHighlightSvc: rewardsNavigationBarHighlightSvc,
		accountBalanceClient:             accountBalanceClient,
		upcomingTxnHelper:                upcomingTxnHelper,
		upcomingTxnClient:                upcomingTxnClient,
		timeClient:                       timeClient,
		authClient:                       authClient,
		questSdkClient:                   questSdkClient,
		vendorMappingClient:              vendorMappingClient,
		cardProvisioningClient:           cardProvisioningClient,
		offerCatalogServiceClient:        offerCatalogServiceClient,
		upiOnboardingClient:              upiOnboardingClient,
		layoutEngine:                     layoutEngine,
		ffAccClient:                      ffAccClient,
		userAttributeFetcher:             userAttributeFetcher,
		productClient:                    productClient,
		tieringDataCollector:             dataCollector,
		crossAttachClient:                crossAttachClient,
		networthClient:                   networthClient,
		projectionClient:                 projectionClient,
		rewardsClient:                    rewardsClient,
		lendabilityClient:                lendabilityClient,
	}
}

// AccountBalanceSummary fetches savings and deposits balance summary for the user
// TODO(sakthi) revisit for lint check
// nolint: funlen
func (s *Service) AccountBalanceSummary(ctx context.Context,
	req *homePb.AccountBalanceSummaryRequest) (*homePb.AccountBalanceSummaryResponse, error) {
	var (
		res              = &homePb.AccountBalanceSummaryResponse{}
		accountSummaries []*homePb.AccountBalanceSummaryPerTimeRange
		actorId          = req.GetReq().GetAuth().GetActorId()
	)

	actorResp, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if epifigrpc.RPCError(actorResp, err); err != nil {
		logger.Error(ctx, "error in finding actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	accnt, err := s.savClient.GetAccount(ctx, &savings.GetAccountRequest{
		Identifier: &savings.GetAccountRequest_PrimaryUserId{
			PrimaryUserId: actorResp.GetActor().GetEntityId(),
		},
	})
	if err != nil {
		logger.Error(ctx,
			"failed to fetch account details", zap.Error(err),
			zap.String(logger.USER_ID, actorResp.GetActor().GetEntityId()),
		)
		res.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return res, nil
	}

	// TODO(keerthana): Need to get actual account creation date, or account PI creation date
	accountOpenTime := accnt.GetAccount().GetCreatedAt()
	if accountOpenTime == nil {
		logger.Error(ctx, "account CreatedAt cannot be null", zap.String(logger.ACCOUNT_ID, accnt.GetAccount().GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	fdPiIds, sdPiIds, err := s.getActiveDepositPiIDs(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "failed to get deposit pi ids", zap.String(logger.ACCOUNT_ID, accnt.GetAccount().GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	ds, err := s.getDepositsSummary(ctx, actorId, accountOpenTime, fdPiIds, sdPiIds)
	if err != nil {
		logger.Error(ctx, "failed to get deposit summary", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	var depositPiIds []string
	depositPiIds = append(depositPiIds, fdPiIds...)
	depositPiIds = append(depositPiIds, sdPiIds...)
	totalSaved, err := s.getCurrentMonthTotalSaved(ctx, actorId, depositPiIds)
	if err != nil {
		logger.Error(ctx, "failed to get total saved", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	ss, err := s.getSavingsSummary(ctx, actorId, accnt.GetAccount().GetId(), totalSaved)
	if err != nil {
		logger.Error(ctx, "failed to get savings summary", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	savingsSummary := &homePb.AccountBalanceSummaryPerTimeRange{}
	depositSummary := &homePb.AccountBalanceSummaryPerTimeRange{}
	convertToAccountSavingSummaryResponse(ss, savingsSummary)
	convertToAccountDepositSummaryResponse(ds, depositSummary)
	accountSummaries = append(accountSummaries, savingsSummary, depositSummary)
	res.AccountSummaryPerTimeRange = accountSummaries
	res.Status = rpc.StatusOk()
	return res, nil
}

func getAnyWithoutError(msg proto.Message) *anyPb.Any {
	res, _ := anyPb.New(msg)
	return res
}

// nolint: funlen, gocritic
func (s *Service) GetTrustMarker(ctx context.Context, req *homePb.GetTrustMarkerRequest) (*homePb.GetTrustMarkerResponse, error) {
	var (
		actorId              = req.GetReq().GetAuth().GetActorId()
		isWealthAnalyserUser = false
		err                  error
	)
	// Note: We check if the actor ID is empty because, in previous app builds, authentication was turned off, and the actor ID won't be passed in requests.
	if actorId != "" {
		isWealthAnalyserUser, err = featureflags.IsWealthAnalyserUser(ctx, &featureflags.IsWealthAnalyserUserRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				OnboardingClient:     s.onboardingClient,
				QuestSdkClient:       s.questSdkClient,
				UserAttributeFetcher: s.userAttributeFetcher,
				NetWorthClient:       s.networthClient,
			},
		})
		if err != nil {
			logger.Error(ctx, "failed to check if user is wealth analyser user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return &homePb.GetTrustMarkerResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg(err.Error()),
				},
			}, nil
		}
	}

	trustMarkerComponents := make([]*components.Component, 0)
	trustMarkerComponents = append(trustMarkerComponents, s.getTrustMarkerHeaderComponent())
	// If the user is not a wealth analyser user, then show the subtitle
	if !isWealthAnalyserUser {
		trustMarkerComponents = append(trustMarkerComponents, s.getTrustMarkerSubtitleComponent())
	}
	trustMarkerComponents = append(trustMarkerComponents, s.getTrustMarkerPartnerLogos())
	trustMarkerComponents = append(trustMarkerComponents, s.getTrustMarkerFiSecurityComponent())
	trustMarkerComponents = append(trustMarkerComponents, s.getTrustMarkerStoriesComponent())

	return &homePb.GetTrustMarkerResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Section: &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					IsScrollable: false,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
										},
										Height: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
										},
									},
									BgColor: widget.GetBlockBackgroundColour("#E1E5EB"),
									Padding: &properties.PaddingProperty{
										Top:    50,
										Left:   16,
										Bottom: 90,
										Right:  20,
									},
								},
							},
						},
					},
					Components:          trustMarkerComponents,
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
					VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_CENTER,
					LoadBehavior: &behaviors.LifecycleBehavior{
						Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
						AnalyticsEvent: &analytics.AnalyticsEvent{
							EventName: "LoadedHomeTrustMarker",
						},
					},
					VisibleBehavior: &behaviors.LifecycleBehavior{
						Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
						AnalyticsEvent: &analytics.AnalyticsEvent{
							EventName: "ViewedHomeTrustMarker",
						},
					},
					InteractionBehaviors: []*behaviors.InteractionBehavior{
						{
							Behavior:       s.getTrustMarkerClickBehaviour(),
							AnalyticsEvent: s.getAnalyticsForTrustMarker(),
						},
					},
				},
			},
		},
	}, nil
}

func (s *Service) getTrustMarkerClickBehaviour() *behaviors.InteractionBehavior_OnClickBehavior {
	if s.conf.TrustMarkerInfoConfig.DisableClickFeature != false {
		return &behaviors.InteractionBehavior_OnClickBehavior{
			OnClickBehavior: &behaviors.OnClickBehavior{
				Action: getAnyWithoutError(
					&deeplink.Deeplink{
						Screen: deeplink.Screen_STORY_SCREEN,
						ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
							StoryScreenOptions: &deeplink.StoryScreenOptions{
								StoryUrl:   s.conf.TrustMarkerInfoConfig.StoriesDeeplinkURL,
								StoryTitle: "Why Trust Fi?",
							},
						},
					},
				),
			},
		}
	}
	return nil
}

func (s *Service) getAnalyticsForTrustMarker() *analytics.AnalyticsEvent {
	return &analytics.AnalyticsEvent{
		EventName: "ClickedHomeTrustMarker",
		Properties: map[string]string{
			"sub_component_name": "ClickedOnTrustMarker",
		},
	}
}

func (s *Service) getTrustMarkerHeaderComponent() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(
			&sections.VerticalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(commontypes.GetTextFromStringWithCustomFontStyle(s.conf.TrustMarkerInfoConfig.FirstTitleHeader, "#b3b5b9", &commontypes.FontStyleInfo{
							FontFamily: "Gilroy",
							FontStyle:  "bold",
							FontSize:   "36",
						})),
					},
					{
						Content: getAnyWithoutError(commontypes.GetTextFromStringWithCustomFontStyle(s.conf.TrustMarkerInfoConfig.SecondTitleHeader, "#b3b5b9", &commontypes.FontStyleInfo{
							FontFamily: "Gilroy",
							FontStyle:  "bold",
							FontSize:   "36",
						})),
					},
				},
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_BOTTOM,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Padding: &properties.PaddingProperty{
									Bottom: 8,
								},
							},
						},
					},
				},
			},
		),
	}
}

func (s *Service) getTrustMarkerSubtitleComponent() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(s.conf.TrustMarkerInfoConfig.SubtitleText, "#8d8d8d", commontypes.FontStyle_HEADLINE_M)),
	}
}

func (s *Service) getTrustMarkerPartnerLogos() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(
			&sections.HorizontalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(constants.FederalBankLogo, 20, 80)),
					},
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(constants.VisaLogo, 20, 38)),
					},
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(constants.BhimUpiIconLogo, 20, 110)),
					},
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(constants.EpifihealthLogo, 20, 90)),
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
								},
								Padding: &properties.PaddingProperty{
									Top:    24,
									Bottom: 24,
								},
							},
						},
					},
				},
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
			},
		),
	}
}

func (s *Service) getTrustMarkerFiSecurityComponent() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(
			&sections.HorizontalListSection{
				IsScrollable: false,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(constants.FiSecurityAwardImage, 64, 300)),
					},
				},
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{OnClickBehavior: &behaviors.OnClickBehavior{Action: getAnyWithoutError(
							&deeplink.Deeplink{
								Screen: deeplink.Screen_STORY_SCREEN,
								ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
									StoryScreenOptions: &deeplink.StoryScreenOptions{
										StoryUrl:   s.conf.TrustMarkerInfoConfig.StoriesDeeplinkURL,
										StoryTitle: "Why Trust Fi?",
									},
								},
							})}},
						AnalyticsEvent: &analytics.AnalyticsEvent{
							EventName: "ClickedHomeTrustMarker",
							Properties: map[string]string{
								"sub_component_name": "TrustMarkerSecurityAwardImage",
							},
						},
					},
				},
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
								},
								Padding: &properties.PaddingProperty{
									Bottom: 24,
								},
							},
						},
					},
				},
				HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
			},
		),
	}
}

func (s *Service) getTrustMarkerStoriesComponent() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(
			&ui.IconTextComponent{
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(constants.PlayIcon, 16, 16),
				Texts:             []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle(s.conf.TrustMarkerInfoConfig.StoriesButtonText, "#00B899", commontypes.FontStyle_HEADLINE_XS)},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:       "#F6F9FD",
					Height:        28,
					TopPadding:    6,
					BottomPadding: 6,
					RightPadding:  12,
					LeftPadding:   12,
					CornerRadius:  13,
				},
			}),
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{OnClickBehavior: &behaviors.OnClickBehavior{Action: getAnyWithoutError(
					&deeplink.Deeplink{
						Screen: deeplink.Screen_STORY_SCREEN,
						ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
							StoryScreenOptions: &deeplink.StoryScreenOptions{
								StoryUrl:   s.conf.TrustMarkerInfoConfig.StoriesDeeplinkURL,
								StoryTitle: "Why Trust Fi?",
							},
						},
					})}},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: "ClickedHomeTrustMarker",
					Properties: map[string]string{
						"sub_component_name": "TrustMarkerStoriesButton",
					},
				},
			},
		},
	}
}

func convertToAccountSavingSummaryResponse(savingsSummary *homePb.GetSavingsBalanceSummaryResponse,
	accSummary *homePb.AccountBalanceSummaryPerTimeRange) {
	accSummary.CurrentBalance = savingsSummary.CurrentBalance
	accSummary.LedgerBalance = savingsSummary.LedgerBalance
	accSummary.CreditAmount = savingsSummary.CreditAmount
	accSummary.SpentAmount = savingsSummary.SpentAmount
	accSummary.SavedAmount = savingsSummary.SavedAmount
	accSummary.IsStaleBalance = savingsSummary.IsStaleBalance
	accSummary.TimeRangeFilter = savingsSummary.TimeRange
	accSummary.DetailedSummary = savingsSummary.DetailedSummary
	accSummary.AccountSummaryCTA = savingsSummary.AccountSummaryCta
}

func convertToAccountDepositSummaryResponse(depositSummary *homePb.GetDepositBalanceSummaryResponse,
	accSummary *homePb.AccountBalanceSummaryPerTimeRange) {
	accSummary.ProvidentFund = depositSummary.ProvidentFund
	accSummary.FixedDeposit = depositSummary.FixedDeposit
	accSummary.SmartDeposit = depositSummary.SmartDeposit
	accSummary.TotalDeposit = depositSummary.TotalDeposit
	accSummary.TimeRangeFilter = depositSummary.TimeRange
	accSummary.DetailedSummary = depositSummary.DetailedSummary
	accSummary.AccountSummaryCTA = depositSummary.AccountSummaryCta
}

// TODO(sakthi) revisit for lint check
// nolint: funlen
func (s *Service) RecentUserActivities(ctx context.Context, req *homePb.RecentUserActivitiesRequest) (*homePb.RecentUserActivitiesResponse,
	error) {
	var (
		response                               = &homePb.RecentUserActivitiesResponse{Status: rpc.StatusOk()}
		actorId                                = req.GetReq().GetAuth().GetActorId()
		isFeatureHomeDesignEnhancementsEnabled = featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId, // Assuming actorId is in scope
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            s.releaseEvaluator, // s.releaseEvaluator or s.evaluator
				OnboardingClient:     s.onboardingClient,
				QuestSdkClient:       s.questSdkClient,
				UserAttributeFetcher: s.userAttributeFetcher,
				NetWorthClient:       s.networthClient,
			},
		})
	)

	if !s.genconf.Flags().EnableRecentUserActivities() {
		response.Status = rpc.StatusUnavailable()
		return response, nil
	}

	eg, egCtx := errgroup.WithContext(ctx)

	eg.Go(func() error {
		responses, zeroStateParams, err := s.buildRecentActivities(egCtx, req)
		if err != nil {
			logger.Error(ctx, "error in getting response for recent activities", zap.Error(err))
			return err
		}
		response.Responses = responses
		response.ZeroStateParams = zeroStateParams
		return nil
	})
	eg.Go(func() error {
		isUpiMapperEnabled := upiHelper.IsUpiMapperEnabledForActor(egCtx, actorId, s.upiOnboardingClient)
		if isUpiMapperEnabled {
			// Get UPI information
			UpiInfoCard, err := s.getUpiInfoCardForActor(egCtx, actorId)
			if err != nil {
				logger.Error(egCtx, "error getting UPI info for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				// don't fail the request if UPI info card couldn't be fetched
			} else if UpiInfoCard != nil {
				response.UpiInfoCard = UpiInfoCard
			}
		}
		return nil
	})

	if err := eg.Wait(); err != nil {
		logger.Error(ctx, "error in getting response for recent activities", zap.Error(err))
		response.Status = rpc.StatusInternal()
		return response, nil
	}

	response.SeeAllActivities = getSeeAllActivitiesComponent(isFeatureHomeDesignEnhancementsEnabled)

	return response, nil
}

func (s *Service) buildRecentActivities(ctx context.Context, req *homePb.RecentUserActivitiesRequest) (responses []*homePb.UserActivityResponse, zeroStateParams *homePb.RecentUserActivitiesZeroStateParams, err error) {
	maxNumberOfSlots := s.conf.HomeRevampParams.RecentActivitiesParams.MaxNumberOfSlots

	responses, err = s.recentActivitiesCollector.GetRecentActivities(ctx, req.GetReq().GetAuth().GetActorId(), maxNumberOfSlots, timestamppb.Now(), s.conf)
	if err != nil {
		return nil, nil, err
	}

	recentActivityParams := s.conf.HomeRevampParams.RecentActivitiesParams
	numberOfEmptySlots := recentActivityParams.MaxNumberOfEmptySlots - int32(len(responses))

	// If number of entries is less than max number, add empty recent activities objects
	if numberOfEmptySlots > 0 {
		emptySlotImageUrls := s.conf.HomeRevampParams.RecentActivitiesParams.EmptySlotImages[0:numberOfEmptySlots]
		emptySlotImages := []*commontypes.Image{}
		for _, url := range emptySlotImageUrls {
			emptySlotImages = append(emptySlotImages, &commontypes.Image{ImageUrl: url})
		}
		zeroStateParams = &homePb.RecentUserActivitiesZeroStateParams{
			AdditionalZeroSlots: numberOfEmptySlots,
			Image:               emptySlotImages,
			ZeroStateMsg:        "As you start using Fi you'll see your activity here",
		}
	}
	return responses, zeroStateParams, nil
}

// getUpiInfoCardForActor - prepares the UPI Info Card consisting of Upi Number for an actor by fetching UPI info (Vpa & upi number) from getUpiInfoForActor method.
// Currently only UPI number is shown in the card, but we can show UPI ID as well if required.
func (s *Service) getUpiInfoCardForActor(ctx context.Context, actorId string) (*homePb.UpiInfoCard, error) {
	vpa, upiNumberDetailsRes, err := s.getUpiInfoForActor(ctx, actorId)
	if err != nil {
		return nil, err
	}
	if vpa == "" || upiNumberDetailsRes == nil {
		return nil, nil
	}

	upiNumberDetails := upiNumberDetailsRes.GetUpiNumberDetails()

	var upiNumber string
	for _, upiNumberDetail := range upiNumberDetails {
		upiNumber = upiNumberDetail.GetUpiNumber()
		if upiNumberDetail.GetUpiNumberType() == enums.UpiNumberType_UPI_NUMBER_TYPE_PHONE_NUMBER {
			break
		}
	}
	if upiNumber == "" {
		return nil, nil
	}

	return &homePb.UpiInfoCard{
		UpiInfo: []*homePb.UpiInfo{
			{
				LeftText:          commontypes.GetTextFromStringFontColourFontStyle("UPI number", "#929599", commontypes.FontStyle_SUBTITLE_S),
				CopyText:          commontypes.GetTextFromStringFontColourFontStyle(upiNumber, "#313234", commontypes.FontStyle_SUBTITLE_S),
				CopyVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(user.CopyIcon, 20, 20),
				PrivacyIcon:       true,
			},
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			CornerRadius: 19,
			BgColor:      "#EFF2F6",
		},
	}, nil
}

// getUpiInfoForActor retrieves UPI information for a given actor like VPA & UpiNumberDetails.
// Returns:
//   - VPA (Virtual Payment Address) as a string.
//   - UPI number details as a response object.
//   - An error if something goes wrong.
func (s *Service) getUpiInfoForActor(ctx context.Context, actorId string) (string, *upiOnboardingPb.GetUpiNumberDetailsResponse, error) {
	// Get VPA and derived account ID
	vpa, derivedAccId, _, err := upiHelper.GetVpaAndDerivedAccIdForPrimaryAccount(ctx, actorId, s.upiOnboardingClient, s.accountPiClient, s.savClient)
	switch {
	case err != nil:
		return "", nil, fmt.Errorf("failed to get VPA and derived account ID for actorId: %s, %w", actorId, err)
	case vpa == "", derivedAccId == "":
		return "", nil, nil
	}

	// Get AccountId from derived account ID.
	var accountId string
	derivedAccountIdProto := &accounts.DerivedAccountId{}
	err = idgen.DecodeProtoFromStdBase64(derivedAccId, derivedAccountIdProto)
	if err != nil {
		logger.Error(ctx, "failed to decode derived account id: ", zap.String(logger.DERIVED_ACCOUNT_ID, derivedAccId), zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return "", nil, err
	}
	if derivedAccountIdProto.GetTpapAccountId() == "" {
		logger.Error(ctx, "derived account id does not have tpap account id", zap.String(logger.DERIVED_ACCOUNT_ID, derivedAccId), zap.String(logger.ACTOR_ID_V2, actorId))
		return "", nil, nil
	}
	accountId = derivedAccountIdProto.GetTpapAccountId()
	// Get UPI number details by AccountId And VPA.
	upiNumberDetails, err := s.upiOnboardingClient.GetUpiNumberDetails(ctx, &upiOnboardingPb.GetUpiNumberDetailsRequest{
		AccountId: accountId,
		Vpa:       vpa,
	})
	if rpcErr := epifigrpc.RPCError(upiNumberDetails, err); rpcErr != nil &&
		// It is possible there is no upiNumberDetail for the user
		// when user has not linked any upiNumber
		!upiNumberDetails.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while calling GetUpiNumberDetails() backend rpc",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return "", nil, rpcErr
	}

	return vpa, upiNumberDetails, nil
}

// nolint: funlen
func (s *Service) orderHandling(ctx context.Context, actorId string, order *orderPb.Order) (*homePb.UserActivityResponse, error) {
	var (
		otherActorId     string
		textColour       string
		link             *deeplink.Deeplink
		title            string
		imageURL         string
		depositAccountId string
		accountType      accounts.Type
		err              error
		txnImageUrl      string
	)

	isP2PInvestment := order.GetWorkflow() == orderPb.OrderWorkflow_P2P_INVESTMENT || order.GetWorkflow() == orderPb.OrderWorkflow_P2P_WITHDRAWAL ||
		orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_JUMP_P2P_INVESTMENT)

	isDeposit := orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_DEPOSIT)
	switch {
	case isDeposit:
		otherActorId = order.GetToActorId()
		title, accountType, textColour, imageURL, depositAccountId, txnImageUrl, err = s.getDepositActivityDetails(ctx, order, actorId)
		if err != nil {
			return nil, err
		}
	case isP2PInvestment:
		textColour, imageURL, otherActorId, txnImageUrl, err = s.getP2PActivityDetails(order, actorId)
		if err != nil {
			return nil, err
		}
	case order.ToActorId == actorId:
		otherActorId = order.FromActorId
		textColour = s.homeParams.AmountColourMap.CreditColour
		txnImageUrl = s.genconf.HomeRevampParams().RecentActivitiesParams().HomeTxnTypeImageUrls().CreditUrl()
	default:
		otherActorId = order.ToActorId
		textColour = s.homeParams.AmountColourMap.DefaultColour
		txnImageUrl = s.genconf.HomeRevampParams().RecentActivitiesParams().HomeTxnTypeImageUrls().DebitUrl()
	}

	// get user of actor if title is empty
	if title == "" {
		actorRes, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: otherActorId})
		if err = epifigrpc.RPCError(actorRes, err); err != nil {
			return nil, err
		}

		title = actorRes.GetName().ToString()
		imageURL = actorRes.GetProfileImageUrl()
	}

	if !isDeposit {
		timeLineRes, err := s.timelineClient.GetByActorIds(ctx, &timeline.GetByActorIdsRequest{PrimaryActorId: actorId,
			SecondaryActorId: otherActorId})
		if err == nil && timeLineRes.GetStatus().IsSuccess() {
			link = &deeplink.Deeplink{
				Screen: deeplink.Screen_TIMELINE,
				ScreenOptions: &deeplink.Deeplink_TimelineScreenOptions{
					TimelineScreenOptions: &deeplink.TimelineScreenOptions{
						TimelineId: timeLineRes.GetTimeline().GetId()}}}
		}
	}
	if isDeposit {
		link = &deeplink.Deeplink{
			Screen: deeplink.Screen_DEPOSIT_ACCOUNT_DETAILS,
			ScreenOptions: &deeplink.Deeplink_DepositDetailsScreenOptions{
				DepositDetailsScreenOptions: &deeplink.DepositAccountDetailsScreenOptions{
					AccountId:   depositAccountId,
					DepositType: accountType,
				},
			},
		}
		if depositAccountId == "" {
			link = &deeplink.Deeplink{
				Screen: deeplink.Screen_DEPOSIT_LANDING_SCREEN,
				ScreenOptions: &deeplink.Deeplink_DepositAccountLandingScreenOption{
					DepositAccountLandingScreenOption: &deeplink.DepositAccountLandingScreenOptions{
						DepositType: accountType,
					},
				},
			}
		}
	}

	return &homePb.UserActivityResponse{
		Title:           title,
		Amount:          types.GetFromBeMoney(order.GetAmount()),
		Icon:            &commontypes.Image{ImageUrl: imageURL},
		Link:            link,
		ImageColourCode: actorPb.GetColourCodeForActor(otherActorId),
		TitleColourCode: textColour,
		TxnType:         &commontypes.Image{ImageUrl: txnImageUrl},
		BorderColor:     homePb.GetHomeWidgetBorderColor(),
	}, nil
}

func (s *Service) getAggregatedBalanceInTimeRange(ctx context.Context, actorId string, fromTime, toTime time.Time,
	piIds []string) (*moneyPb.Money, error) {
	ctxWithTimeout, cancelCtx := context.WithTimeout(ctx, 2*time.Second)
	defer cancelCtx()

	txnAggregatesResForAccountingErrGrp, grpCtx := errgroup.WithContext(ctxWithTimeout)
	var txnAggregatesResForCreditAccounting *txnaggregates.GetTransactionAggregatesResponse
	var txnAggregatesResForDebitAccounting *txnaggregates.GetTransactionAggregatesResponse

	txnAggregatesResForAccountingErrGrp.Go(func() error {
		var err error
		txnAggregatesResForCreditAccounting, err = s.txnAggClient.GetTransactionAggregates(grpCtx, &txnaggregates.GetTransactionAggregatesRequest{
			ActorId:             actorId,
			FromExecutedTime:    timestamppb.New(fromTime),
			ToExecutedTime:      timestamppb.New(toTime),
			AccountingEntryType: payment.AccountingEntryType_CREDIT,
			PiFilter:            piIds,
		})
		if txnAggregatesResForCreditAccountingErr := epifigrpc.RPCError(txnAggregatesResForCreditAccounting, err); txnAggregatesResForCreditAccountingErr != nil {
			return errors.Wrap(txnAggregatesResForCreditAccountingErr, "error fetching transaction aggregates for credit accounting")
		}
		return nil
	})

	txnAggregatesResForAccountingErrGrp.Go(func() error {
		var err error
		txnAggregatesResForDebitAccounting, err = s.txnAggClient.GetTransactionAggregates(grpCtx, &txnaggregates.GetTransactionAggregatesRequest{
			ActorId:             actorId,
			FromExecutedTime:    timestamppb.New(fromTime),
			ToExecutedTime:      timestamppb.New(toTime),
			AccountingEntryType: payment.AccountingEntryType_DEBIT,
			PiFilter:            piIds,
		})
		if txnAggregatesResForDebitAccountingErr := epifigrpc.RPCError(txnAggregatesResForDebitAccounting, err); txnAggregatesResForDebitAccountingErr != nil {
			return errors.Wrap(txnAggregatesResForDebitAccountingErr, "error fetching transaction aggregates for debit accounting")
		}
		return nil
	})

	txnAggregatesResForAccountingErr := txnAggregatesResForAccountingErrGrp.Wait()
	if txnAggregatesResForAccountingErr != nil {
		logger.Error(ctx, "error in fetching transaction aggregates for accounting", zap.Error(txnAggregatesResForAccountingErr))
		return nil, fmt.Errorf("failed to get transaction aggregates for accounting")
	}

	// balance = credit - debit
	balance, err := money.Subtract(txnAggregatesResForCreditAccounting.GetTransactionAggregates().GetSumAmount(),
		txnAggregatesResForDebitAccounting.GetTransactionAggregates().GetSumAmount())
	if err != nil {
		return nil, fmt.Errorf("failed to compute aggregated balance: %w", err)
	}

	return balance, nil
}

// getAccountDetailsFromPi fetches account number, ifsc code and type of account given a PI.
// returns error in case PI is not of account type of rpc call to PI service fails
func (s *Service) getAccountDetailsFromPi(ctx context.Context, piId string) (*piPb.Account, error) {

	pi, err := s.getPIDetails(ctx, piId)
	if err != nil {
		return nil, err
	}

	if pi.Type != piPb.PaymentInstrumentType_BANK_ACCOUNT && pi.Type != piPb.PaymentInstrumentType_GENERIC {
		return nil, fmt.Errorf("payment instrument is of type: %s expected %s : ", pi.Type.String(),
			piPb.PaymentInstrumentType_BANK_ACCOUNT.String())
	}

	return pi.GetAccount(), nil
}

// getPIDetails calls pi service's `GetPiByID` RPC and returns payment-instrument
func (s *Service) getPIDetails(ctx context.Context, piId string) (*piPb.PaymentInstrument, error) {
	piReq := &piPb.GetPiByIdRequest{
		Id: piId,
	}
	piRes, err := s.piClient.GetPiById(ctx, piReq)
	switch {
	case err != nil:
		return nil, fmt.Errorf("payment instrument details can't be fetched due to rpc failure: PI: %s : %w",
			piId, err)
	case piRes.Status.IsRecordNotFound():
		return nil, fmt.Errorf("PI record not found for PI: %s: ", piId)
	case !piRes.Status.IsSuccess():
		return nil, fmt.Errorf("got unexpected response from payment instrument service: %v", piRes.Status)
	}

	return piRes.PaymentInstrument, nil
}

// getDepositActivityDetails returns nil, activity title, icon url for a savings Payment instrument
// TODO(sakthi) revisit for lint check
// nolint: funlen
func (s *Service) getDepositActivityDetails(ctx context.Context, order *orderPb.Order, actorId string) (string, accounts.Type, string, string, string,
	string, error) {
	var (
		accountPiFrom  *piPb.Account
		accountPiTo    *piPb.Account
		depositTitle   string
		depositAccount *beDepositPb.DepositAccount
		accountType    accounts.Type
		textColour     string
		imageUrl       string
		err            error
		txnImageUrl    string
	)

	orderRes, err := s.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{OrderId: order.GetId()})
	if err = epifigrpc.RPCError(orderRes, err); err != nil {
		return "", 0, "", "", "", "", fmt.Errorf("failed to fetch order with txn: %w", err)
	}

	if len(orderRes.GetOrderWithTransactions().GetTransactions()) == 0 {
		return "", 0, "", "", "", "", fmt.Errorf("deposit order doesnt have txn")
	}

	piFrom := orderRes.GetOrderWithTransactions().GetTransactions()[0].GetPiFrom()
	piTo := orderRes.GetOrderWithTransactions().GetTransactions()[0].GetPiTo()

	accountPiFrom, err = s.getAccountDetailsFromPi(ctx, piFrom)
	if err != nil {
		return "", 0, "", "", "", "", fmt.Errorf("failed to get account details for pi : %s: %w", piFrom, err)
	}

	accountPiTo, err = s.getAccountDetailsFromPi(ctx, piTo)
	if err != nil {
		return "", 0, "", "", "", "", fmt.Errorf("failed to get account details for pi : %s: %w", piTo, err)
	}
	textColour = s.homeParams.AmountColourMap.SavingsColour
	txnImageUrl = s.genconf.HomeRevampParams().RecentActivitiesParams().HomeTxnTypeImageUrls().InvestUrl()

	// if PiTo belongs to a deposit account, the txn can either be an add funds to SD or deposit creation one
	// if PiFrom belongs to a deposit account, the txn can either be for deposit preclosure or deposit maturity
	if accounts.IsDepositAccount(accountPiTo.GetAccountType()) {
		depositAccount, err = s.getDepositAccount(ctx, accountPiTo.GetActualAccountNumber(), accountPiTo.GetIfscCode())
		if err != nil {
			return "", 0, "", "", "", "", fmt.Errorf("failed to get deposit account: %w", err)
		}
		// categorise deposit txns
		depositTitle = depositAccount.GetName()

	} else {
		// TODO(harish): handle FD interest credit txns in actor activity
		//  Monorail:https://monorail.pointz.in/p/fi-app/issues/detail?id=19555
		if !accounts.IsDepositAccount(accountPiFrom.GetAccountType()) {
			depositTitle = "Interest credited for FD"
			depositAccount = &beDepositPb.DepositAccount{Type: accounts.Type_FIXED_DEPOSIT}
			textColour = s.homeParams.AmountColourMap.CreditColour
			txnImageUrl = s.genconf.HomeRevampParams().RecentActivitiesParams().HomeTxnTypeImageUrls().CreditUrl()
		} else {
			depositAccount, err = s.getDepositAccount(ctx, accountPiFrom.GetActualAccountNumber(), accountPiFrom.GetIfscCode())
			if err != nil {
				return "", 0, "", "", "", "", fmt.Errorf("failed to get deposit account: %w", err)
			}
			// categorise deposit txns
			depositTitle = depositAccount.GetName()
			textColour = s.homeParams.AmountColourMap.CreditColour
			txnImageUrl = s.genconf.HomeRevampParams().RecentActivitiesParams().HomeTxnTypeImageUrls().CreditUrl()
		}
	}

	accountType = depositAccount.GetType()

	depositIcons := s.conf.HomeV2DepositIcons
	switch depositAccount.GetType() {
	case accounts.Type_FIXED_DEPOSIT:
		imageUrl = depositIcons.FDUrl
	case accounts.Type_SMART_DEPOSIT:
		imageUrl = depositIcons.SDUrl
	}
	return depositTitle, accountType, textColour, imageUrl, depositAccount.GetId(), txnImageUrl, nil
}

// getTimeRange returns time range for given time range filer
func getTimeRange(timeRange string) (time.Time, time.Time) {
	switch timeRange {
	case oneMonth:
		return now.With(time.Now().In(datetime.IST)).BeginningOfMonth(), time.Now()
	default:
		return time.Time{}, time.Time{}
	}
}

// getDepositAccount fetches deposit account via account number and ifsc code
func (s *Service) getDepositAccount(ctx context.Context, accountNumber, ifscCode string) (*beDepositPb.DepositAccount, error) {
	res, err := s.depositClient.GetByAccountNumberAndIfsc(ctx, &beDepositPb.GetByAccountNumberAndIfscRequest{
		AccountNumber: accountNumber,
		IfscCode:      ifscCode,
	})

	switch {
	case err != nil:
		return nil, fmt.Errorf("failed to get deposit account by account number and ifsc, err %w", err)
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("got unsuccessful status response from depositClient.GetByAccountNumberAndIfsc() %v", res.GetStatus())
	default:
		return res.GetAccount(), nil
	}
}

//nolint:unparam
func (s *Service) getP2PActivityDetails(order *orderPb.Order, actorId string) (string, string, string, string, error) {
	var (
		textColour   string
		imageUrl     string
		otherActorId string
		txnImageUrl  string
	)
	textColour = s.homeParams.AmountColourMap.SavingsColour
	imageUrl = s.conf.P2PIcons.LiquiloansIcon

	switch {
	case order.GetWorkflow() == orderPb.OrderWorkflow_P2P_INVESTMENT || order.GetFromActorId() == actorId:
		otherActorId = order.GetToActorId()
		txnImageUrl = s.genconf.HomeRevampParams().RecentActivitiesParams().HomeTxnTypeImageUrls().InvestUrl()
	case order.GetWorkflow() == orderPb.OrderWorkflow_P2P_WITHDRAWAL || order.GetToActorId() == actorId:
		otherActorId = order.GetFromActorId()
		txnImageUrl = s.genconf.HomeRevampParams().RecentActivitiesParams().HomeTxnTypeImageUrls().CreditUrl()
	}

	return textColour, imageUrl, otherActorId, txnImageUrl, nil
}

func (s *Service) IsFeatureEnabled(ctx context.Context, featureName types.Feature, actorId string) (bool, error) {
	var (
		enableFeature bool
		err           error
	)
	if s.releaseEvaluator == nil {
		enableFeature, err = s.evaluator.Evaluate(ctx,
			release.NewCommonConstraintData(featureName).WithActorId(actorId))
	} else {
		enableFeature, err = s.releaseEvaluator.Evaluate(ctx,
			release.NewCommonConstraintData(featureName).WithActorId(actorId))
	}
	if err != nil {
		return false, fmt.Errorf("failed to evaluate feature, %w", err)
	}
	return enableFeature, nil
}

func (s *Service) GetUpiWidget(ctx context.Context, req *homePb.GetUpiWidgetRequest) (*homePb.GetUpiWidgetResponse, error) {
	var (
		connectRuPayCreditDeeplink *deeplink.Deeplink
		isCcLinkingEnabledForActor bool
	)
	getProductsRes, err := s.productClient.GetProductsStatus(ctx, &productPb.GetProductsStatusRequest{
		ActorId:      req.GetReq().GetAuth().GetActorId(),
		ProductTypes: []productPb.ProductType{productPb.ProductType_PRODUCT_TYPE_TPAP},
	})
	if rpcErr := epifigrpc.RPCError(getProductsRes, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching products status details", zap.Error(rpcErr))
		return &homePb.GetUpiWidgetResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())},
		}, nil
	}

	if getProductsRes.GetProductInfoMap()[productPb.ProductType_PRODUCT_TYPE_TPAP.String()].GetProductStatus() == productPb.ProductStatus_PRODUCT_STATUS_ACTIVE {
		return &homePb.GetUpiWidgetResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFound()},
		}, nil
	}

	connectTpapAccountsDeeplink, err := fePkgUpi.GetDeeplinkToConnectTpapAccounts(ctx, req.GetReq().GetAuth().GetActorId(), s.conf.ConnectTpapBankAccountIntroScreenParams, s.actorClient)
	if err != nil {
		return &homePb.GetUpiWidgetResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())},
		}, nil
	}
	// Adding Connect-RuPay-Card Entry Point for users to connect RuPay Card without SA or TPAP Account.
	isCcLinkingEnabledForActor = fePkgUpi.IsCcLinkingEnabledForActor(ctx, req.GetReq().GetAuth().GetActorId(), s.upiOnboardingClient)
	if isCcLinkingEnabledForActor {
		connectRuPayCreditDeeplink, err = fePkgUpi.GetDeeplinkToConnectRupayAccount(ctx, req.GetReq().GetAuth().GetActorId())
		if err != nil {
			return &homePb.GetUpiWidgetResponse{
				RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())},
			}, nil
		}
	}

	return &homePb.GetUpiWidgetResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Section:    s.getHomeUpiConnectWidget(connectTpapAccountsDeeplink, connectRuPayCreditDeeplink),
	}, nil
}

func (s *Service) getHomeUpiConnectWidget(connectTpapAccountsDeeplink *deeplink.Deeplink, connectRupayCreditDeeplink *deeplink.Deeplink) *sections.Section {
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				IsScrollable: false,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
									},
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
									},
								},
								BgColor: widget.GetBlockBackgroundColour("#E6E9ED"),
								Padding: &properties.PaddingProperty{
									Left:  16,
									Right: 20,
								},
							},
						},
					},
				},
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_BOTTOM,
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(s.getHomeUpiConnectWidgetHeader()),
					},
					{
						Content: getAnyWithoutError(s.getHomeUpiConnectWidgetSDUIContent(connectTpapAccountsDeeplink, connectRupayCreditDeeplink)),
					},
				},
			},
		},
	}
}

func (s *Service) getHomeUpiConnectWidgetHeader() *sections.VerticalListSection {
	return &sections.VerticalListSection{
		IsScrollable: false,
		Components: []*components.Component{
			{
				Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Make Payments", "#313234", commontypes.FontStyle_HEADLINE_M)),
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
		VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_BOTTOM,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Padding: &properties.PaddingProperty{
							Bottom: 12,
						},
					},
				},
			},
		},
	}
}

// nolint: funlen
func (s *Service) getHomeUpiConnectWidgetSDUIContent(connectTpapAccountsDeeplink *deeplink.Deeplink, connectRupayCardDeeplink *deeplink.Deeplink) *sections.VerticalListSection {
	bankIcons := []string{
		constants.HsbcBankIconLogo,
		constants.IndusBankIconLogo,
		constants.HdfcBankIconLogo,
		constants.AxisBankIconLogo,
		constants.IciciBankIconLogo,
	}
	var (
		horizontalImgComponents  []*components.Component
		displayText              = "Pay from any bank account on Fi"
		textToConnectBankAccount = "Try it out"
	)
	if connectRupayCardDeeplink != nil {
		displayText = "Connect and pay seamlessly with any bank account or RuPay credit card on Fi"
		textToConnectBankAccount = "Bank account"
	}
	for _, img := range bankIcons {
		horizontalImgComponents = append(horizontalImgComponents, &components.Component{
			Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(img, 42, 42)),
		})
	}
	return &sections.VerticalListSection{
		IsScrollable: false,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
						BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
						Padding: &properties.PaddingProperty{
							Top:    24,
							Left:   32,
							Bottom: 20,
							Right:  32,
						},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  20,
							TopRightCornerRadius: 20,
							BottomLeftCorner:     20,
							BottomRightCorner:    20,
						},
					},
				},
			},
		},
		Components: []*components.Component{
			{
				Content: getAnyWithoutError(
					&sections.HorizontalListSection{
						IsScrollable: false,
						Components: []*components.Component{
							{
								Content: getAnyWithoutError(&sections.HorizontalListSection{
									IsScrollable:          false,
									Components:            horizontalImgComponents,
									VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
									HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
									ListElementOverlapProps: &properties.ListElementOverlapProps{
										OverlapDevicePixels:       12,
										OverlapCornerRadiusPixels: 50,
										PaddingBgColor:            widget.GetBlockBackgroundColour("#FFFFFF"),
									},
								}),
							},
						},
						HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: &properties.ContainerProperty{
										Padding: &properties.PaddingProperty{
											Bottom: 8,
										},
										Size: &properties.Size{
											Width: &properties.Size_Dimension{
												Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
											},
											Height: &properties.Size_Dimension{
												Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
											},
										},
									},
								},
							},
						},
					},
				),
			},
			{
				Content: getAnyWithoutError(
					&sections.HorizontalListSection{
						IsScrollable: false,
						Components: []*components.Component{
							{
								Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(displayText, "#8d8d8d", commontypes.FontStyle_HEADLINE_S)),
							},
						},
						HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: &properties.ContainerProperty{
										Padding: &properties.PaddingProperty{
											Bottom: 8,
										},
										Size: &properties.Size{
											Width: &properties.Size_Dimension{
												Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
											},
											Height: &properties.Size_Dimension{
												Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
											},
										},
									},
								},
							},
						},
					},
				),
			},
			{
				Content: getAnyWithoutError(
					&sections.HorizontalListSection{
						IsScrollable: false,
						Components: func() []*components.Component {
							componentList := []*components.Component{
								{
									Content: getAnyWithoutError(
										&ui.IconTextComponent{
											Texts: []*commontypes.Text{
												commontypes.GetTextFromStringFontColourFontStyle(textToConnectBankAccount, "#00B899", commontypes.FontStyle_HEADLINE_XS),
											},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor:       "#F6F9FD",
												Height:        28,
												TopPadding:    6,
												BottomPadding: 6,
												RightPadding:  12,
												LeftPadding:   12,
												CornerRadius:  13,
											},
										}),
									InteractionBehaviors: []*behaviors.InteractionBehavior{
										{
											Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
												OnClickBehavior: &behaviors.OnClickBehavior{
													Action: getAnyWithoutError(connectTpapAccountsDeeplink),
												},
											},
											AnalyticsEvent: &analytics.AnalyticsEvent{
												EventName: "ClickedConnectUPIWidget",
												Properties: map[string]string{
													"sub_component_name": "ClickedConnectUPIWidget",
												},
											},
										},
									},
								},
							}
							if connectRupayCardDeeplink != nil {
								componentList = append(componentList, &components.Component{
									Content: getAnyWithoutError(
										&ui.IconTextComponent{
											Texts: []*commontypes.Text{
												commontypes.GetTextFromStringFontColourFontStyle("RuPay Credit Card", "#00B899", commontypes.FontStyle_HEADLINE_XS),
											},
											ContainerProperties: &ui.IconTextComponent_ContainerProperties{
												BgColor:       "#F6F9FD",
												Height:        28,
												TopPadding:    6,
												BottomPadding: 6,
												RightPadding:  12,
												LeftPadding:   12,
												CornerRadius:  13,
											},
										}),
									InteractionBehaviors: []*behaviors.InteractionBehavior{
										{
											Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
												OnClickBehavior: &behaviors.OnClickBehavior{
													Action: getAnyWithoutError(connectRupayCardDeeplink),
												},
											},
											AnalyticsEvent: &analytics.AnalyticsEvent{
												EventName: "ClickedConnectUPIWidget",
												Properties: map[string]string{
													"sub_component_name": "ClickedConnectUPIWidget",
												},
											},
										},
									},
								})
							}
							return componentList
						}(),
						HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
					},
				),
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
		VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_BOTTOM,
		LoadBehavior: &behaviors.LifecycleBehavior{
			Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
			AnalyticsEvent: &analytics.AnalyticsEvent{
				EventName: "LoadedConnectUPIWidget",
			},
		},
		VisibleBehavior: &behaviors.LifecycleBehavior{
			Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
			AnalyticsEvent: &analytics.AnalyticsEvent{
				EventName: "ViewedConnectUPIWidget",
			},
		},
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
					OnClickBehavior: &behaviors.OnClickBehavior{
						Action: getAnyWithoutError(
							connectTpapAccountsDeeplink,
						),
					},
				},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: "ClickedConnectUPIWidget",
					Properties: map[string]string{
						"sub_component_name": "ClickedConnectUPIWidget",
					},
				},
			},
		},
	}
}

func getSeeAllActivitiesComponent(isFeatureHomeDesignEnhancementsEnabled bool) *ui.IconTextComponent {
	itc := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle("SEE ALL", "#646464", commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(
			&deeplink.Deeplink{
				Screen: deeplink.Screen_ALL_TRANSACTIONS_ACCOUNT_FILTER,
			})

	if isFeatureHomeDesignEnhancementsEnabled {
		itc.Texts = []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle("SEE ALL", "#00B899", commontypes.FontStyle_OVERLINE_2XS_CAPS),
		}
		itc.RightImgTxtPadding = 4
		itc.ContainerProperties = &ui.IconTextComponent_ContainerProperties{
			BgColor:       "#DCF3EE",
			CornerRadius:  24,
			Height:        28,
			Width:         80,
			LeftPadding:   15,
			RightPadding:  10,
			TopPadding:    4,
			BottomPadding: 4,
		}
		itc.RightVisualElement = commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/home-v2/green-chevron-icon-2.png", 18, 18)
	}

	return itc
}
