package common

import (
	"fmt"

	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	typesPb "github.com/epifi/gamma/api/typesv2"
)

var CategoryToAssetTypeMap = map[networthFePb.NetworthCategory]networthBePb.AssetType{
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_SAVINGS_ACCOUNTS:       networthBePb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS:           networthBePb.AssetType_ASSET_TYPE_MUTUAL_FUND,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_EPF:                    networthBePb.AssetType_ASSET_TYPE_EPF,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_JUMP:                   networthBePb.AssetType_ASSET_TYPE_P2P_LENDING,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_DEPOSITS:               networthBePb.AssetType_ASSET_TYPE_FIXED_DEPOSITS,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_FI_FD:                  networthBePb.AssetType_ASSET_TYPE_FIXED_DEPOSITS,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_INDIAN_SECURITIES:      networthBePb.AssetType_ASSET_TYPE_INDIAN_SECURITIES,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_AIF:                          networthBePb.AssetType_ASSET_TYPE_AIF,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ART_ARTEFACTS:                networthBePb.AssetType_ASSET_TYPE_ART_ARTEFACTS,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_BONDS:                        networthBePb.AssetType_ASSET_TYPE_BONDS,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_CASH:                         networthBePb.AssetType_ASSET_TYPE_CASH,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_GOLD:                 networthBePb.AssetType_ASSET_TYPE_DIGITAL_GOLD,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_SILVER:               networthBePb.AssetType_ASSET_TYPE_DIGITAL_SILVER,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_PRIVATE_EQUITY:               networthBePb.AssetType_ASSET_TYPE_PRIVATE_EQUITY,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_REAL_ESTATE:                  networthBePb.AssetType_ASSET_TYPE_REAL_ESTATE,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_US_SECURITIES:          networthBePb.AssetType_ASSET_TYPE_US_SECURITIES,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE: networthBePb.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_EMPLOYEE_STOCK_OPTION:        networthBePb.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_NPS:                    networthBePb.AssetType_ASSET_TYPE_NPS,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_GADGETS:                      networthBePb.AssetType_ASSET_TYPE_GADGETS,
}

var CategoryToLiabilityTypeMap = map[networthFePb.NetworthCategory]networthBePb.LiabilityType{
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_HOME_LOAN:               networthBePb.LiabilityType_LIABILITY_TYPE_HOME_LOAN,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_EDUCATION_LOAN:          networthBePb.LiabilityType_LIABILITY_TYPE_EDUCATION_LOAN,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_PERSONAL_LOAN:           networthBePb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_VEHICLE_LOAN:            networthBePb.LiabilityType_LIABILITY_TYPE_VEHICLE_LOAN,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_OTHER_LOANS:             networthBePb.LiabilityType_LIABILITY_TYPE_OTHER_LOAN,
	networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_CREDIT_CARD_OUTSTANDING: networthBePb.LiabilityType_LIABILITY_TYPE_CREDIT_CARD_OUTSTANDING,
}

var AssetTypeToCategoryMap = map[networthBePb.AssetType]networthFePb.NetworthCategory{
	networthBePb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS:             networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_SAVINGS_ACCOUNTS,
	networthBePb.AssetType_ASSET_TYPE_MUTUAL_FUND:                  networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_MUTUAL_FUNDS,
	networthBePb.AssetType_ASSET_TYPE_EPF:                          networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_EPF,
	networthBePb.AssetType_ASSET_TYPE_P2P_LENDING:                  networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_JUMP,
	networthBePb.AssetType_ASSET_TYPE_FIXED_DEPOSITS:               networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_DEPOSITS,
	networthBePb.AssetType_ASSET_TYPE_INDIAN_SECURITIES:            networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_INDIAN_SECURITIES,
	networthBePb.AssetType_ASSET_TYPE_AIF:                          networthFePb.NetworthCategory_NETWORTH_CATEGORY_AIF,
	networthBePb.AssetType_ASSET_TYPE_ART_ARTEFACTS:                networthFePb.NetworthCategory_NETWORTH_CATEGORY_ART_ARTEFACTS,
	networthBePb.AssetType_ASSET_TYPE_BONDS:                        networthFePb.NetworthCategory_NETWORTH_CATEGORY_BONDS,
	networthBePb.AssetType_ASSET_TYPE_CASH:                         networthFePb.NetworthCategory_NETWORTH_CATEGORY_CASH,
	networthBePb.AssetType_ASSET_TYPE_DIGITAL_GOLD:                 networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_GOLD,
	networthBePb.AssetType_ASSET_TYPE_DIGITAL_SILVER:               networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_SILVER,
	networthBePb.AssetType_ASSET_TYPE_PRIVATE_EQUITY:               networthFePb.NetworthCategory_NETWORTH_CATEGORY_PRIVATE_EQUITY,
	networthBePb.AssetType_ASSET_TYPE_REAL_ESTATE:                  networthFePb.NetworthCategory_NETWORTH_CATEGORY_REAL_ESTATE,
	networthBePb.AssetType_ASSET_TYPE_US_SECURITIES:                networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_US_SECURITIES,
	networthBePb.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE: networthFePb.NetworthCategory_NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE,
	networthBePb.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION:        networthFePb.NetworthCategory_NETWORTH_CATEGORY_EMPLOYEE_STOCK_OPTION,
	networthBePb.AssetType_ASSET_TYPE_NPS:                          networthFePb.NetworthCategory_NETWORTH_CATEGORY_ASSET_NPS,
	networthBePb.AssetType_ASSET_TYPE_GADGETS:                      networthFePb.NetworthCategory_NETWORTH_CATEGORY_GADGETS,
}

var LiabilityTypeToCategoryMap = map[networthBePb.LiabilityType]networthFePb.NetworthCategory{
	networthBePb.LiabilityType_LIABILITY_TYPE_HOME_LOAN:               networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_HOME_LOAN,
	networthBePb.LiabilityType_LIABILITY_TYPE_EDUCATION_LOAN:          networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_EDUCATION_LOAN,
	networthBePb.LiabilityType_LIABILITY_TYPE_PERSONAL_LOAN:           networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_PERSONAL_LOAN,
	networthBePb.LiabilityType_LIABILITY_TYPE_VEHICLE_LOAN:            networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_VEHICLE_LOAN,
	networthBePb.LiabilityType_LIABILITY_TYPE_OTHER_LOAN:              networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_OTHER_LOANS,
	networthBePb.LiabilityType_LIABILITY_TYPE_CREDIT_CARD_OUTSTANDING: networthFePb.NetworthCategory_NETWORTH_CATEGORY_LIABILITY_CREDIT_CARD_OUTSTANDING,
}

var InstrumentTypeToCategoryMap = map[typesPb.InvestmentInstrumentType]networthFePb.NetworthCategory{
	typesPb.InvestmentInstrumentType_AIF:                          networthFePb.NetworthCategory_NETWORTH_CATEGORY_AIF,
	typesPb.InvestmentInstrumentType_ART_AND_ARTEFACTS:            networthFePb.NetworthCategory_NETWORTH_CATEGORY_ART_ARTEFACTS,
	typesPb.InvestmentInstrumentType_BOND:                         networthFePb.NetworthCategory_NETWORTH_CATEGORY_BONDS,
	typesPb.InvestmentInstrumentType_CASH:                         networthFePb.NetworthCategory_NETWORTH_CATEGORY_CASH,
	typesPb.InvestmentInstrumentType_DIGITAL_GOLD:                 networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_GOLD,
	typesPb.InvestmentInstrumentType_DIGITAL_SILVER:               networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_SILVER,
	typesPb.InvestmentInstrumentType_PRIVATE_EQUITY:               networthFePb.NetworthCategory_NETWORTH_CATEGORY_PRIVATE_EQUITY,
	typesPb.InvestmentInstrumentType_REAL_ESTATE:                  networthFePb.NetworthCategory_NETWORTH_CATEGORY_REAL_ESTATE,
	typesPb.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE: networthFePb.NetworthCategory_NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE,
	typesPb.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION:        networthFePb.NetworthCategory_NETWORTH_CATEGORY_EMPLOYEE_STOCK_OPTION,
}

func GetCategoryDetailsFromNetworthConfig(category networthFePb.NetworthCategory, networthDashboardConfig *networthFePb.NetWorthDashboardConfig) (*networthFePb.WidgetDetails, error) {
	for _, section := range networthDashboardConfig.GetSections() {
		for _, widget := range section.GetWidgets() {
			if widget.GetCategory() == category {
				return widget, nil
			}
		}
	}
	return nil, fmt.Errorf("category details does not exist in the networth dashboard config")
}
