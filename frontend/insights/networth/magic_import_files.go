package networth

import (
	"context"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	headerPb "github.com/epifi/gamma/api/frontend/header"
	feNetworthPb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/pkg/frontend/header"
)

func (s *Service) MagicImportFiles(ctx context.Context, req *feNetworthPb.MagicImportFilesRequest) (*feNetworthPb.MagicImportFilesResponse, error) {
	// create context independent of client context, this will avoid context cancelling of rpc in case user closes the app
	var cancelCtx func()
	ctx, cancelCtx = context.WithDeadline(epificontext.CloneCtx(ctx), time.Now().Add(90*time.Second))
	defer cancelCtx()
	res, err := s.networthClient.MagicImportFiles(ctx, &networthPb.MagicImportFilesRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Files:   req.GetFiles(),
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in MagicImportFiles API", zap.Error(rpcErr))
		return &feNetworthPb.MagicImportFilesResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
			},
		}, nil
	}
	return &feNetworthPb.MagicImportFilesResponse{
		RespHeader: header.SuccessRespHeader(),
	}, nil
}
