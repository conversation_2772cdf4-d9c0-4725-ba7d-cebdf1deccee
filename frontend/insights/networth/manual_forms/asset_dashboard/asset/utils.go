package asset

import (
	"fmt"

	"github.com/epifi/gamma/api/insights/networth"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/utils"
)

var (
	AssetTypeToDashboardDetailsMap = map[networth.AssetType]*AssetDashboardDetails{
		networth.AssetType_ASSET_TYPE_AIF: {
			Title:                   "Alternate Investment Funds",
			SummaryButtonCtaText:    "+ Add alternate investment",
			CurrentValueTitleText:   "CURRENT VALUE (₹)",
			InvestedValueTitleText:  "INVESTED VALUE (₹)",
			InvestmentDateTitleText: "DATE OF INVESTMENT",
		},
		networth.AssetType_ASSET_TYPE_PRIVATE_EQUITY: {
			Title:                   "Private Equities",
			SummaryButtonCtaText:    "+ Add private equity",
			CurrentValueTitleText:   "CURRENT VALUE (₹)",
			InvestedValueTitleText:  "INVESTED VALUE (₹)",
			InvestmentDateTitleText: "DATE OF INVESTMENT",
		},
		networth.AssetType_ASSET_TYPE_REAL_ESTATE: {
			Title:                   "Real Estates",
			SummaryButtonCtaText:    "+ Add real estate",
			CurrentValueTitleText:   "CURRENT VALUE (₹)",
			InvestedValueTitleText:  "INVESTED VALUE (₹)",
			InvestmentDateTitleText: "DATE OF INVESTMENT",
		},
		networth.AssetType_ASSET_TYPE_ART_ARTEFACTS: {
			Title:                   "Art & Artefacts",
			SummaryButtonCtaText:    "+ Add art & artefact",
			CurrentValueTitleText:   "CURRENT VALUE (₹)",
			InvestedValueTitleText:  "INVESTED VALUE (₹)",
			InvestmentDateTitleText: "DATE OF INVESTMENT",
		},
		networth.AssetType_ASSET_TYPE_BONDS: {
			Title:                   "Bonds",
			SummaryButtonCtaText:    "+ Add bond",
			CurrentValueTitleText:   "CURRENT VALUE (₹)",
			InvestedValueTitleText:  "INVESTED VALUE (₹)",
			InvestmentDateTitleText: "DATE OF INVESTMENT",
		},
		// no cash here since, it does not follow this structure (no invested value)
		networth.AssetType_ASSET_TYPE_DIGITAL_GOLD: {
			Title:                   "Digital Gold",
			SummaryButtonCtaText:    "+ Add digital gold",
			CurrentValueTitleText:   "CURRENT VALUE (₹)",
			InvestedValueTitleText:  "INVESTED VALUE (₹)",
			InvestmentDateTitleText: "DATE OF INVESTMENT",
		},
		networth.AssetType_ASSET_TYPE_DIGITAL_SILVER: {
			Title:                   "Digital Silver",
			SummaryButtonCtaText:    "+ Add digital silver",
			CurrentValueTitleText:   "CURRENT VALUE (₹)",
			InvestedValueTitleText:  "INVESTED VALUE (₹)",
			InvestmentDateTitleText: "DATE OF INVESTMENT",
		},
		networth.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE: {
			Title:                   "Portfolio Management Service",
			SummaryButtonCtaText:    "+ Add portfolio management service",
			CurrentValueTitleText:   "CURRENT VALUE (₹)",
			InvestedValueTitleText:  "INVESTED VALUE (₹)",
			InvestmentDateTitleText: "DATE OF INVESTMENT",
		},
		networth.AssetType_ASSET_TYPE_GADGETS: {
			Title:                   "Gadgets",
			SummaryButtonCtaText:    "+ Add gadget",
			CurrentValueTitleText:   "CURRENT VALUE (₹)",
			InvestedValueTitleText:  "INVESTED VALUE (₹)",
			InvestmentDateTitleText: "YEAR OF INVESTMENT",
		},
	}
)

func GetAssetDashboardDetailsForInstrument(instrumentType types.InvestmentInstrumentType) (*AssetDashboardDetails, error) {
	assetType, found := utils.InstrumentTypeToAssetTypeMap[instrumentType]
	if !found {
		return nil, fmt.Errorf("instrument type %s not found in instrumentToAsset map", instrumentType.String())
	}
	details, found := AssetTypeToDashboardDetailsMap[assetType]
	if !found {
		return nil, fmt.Errorf("asset dashboard details not found in map for %s", assetType.String())
	}
	return details, nil
}

type AssetDashboardDetails struct {
	Title                   string
	SummaryButtonCtaText    string
	CurrentValueTitleText   string
	InvestedValueTitleText  string
	InvestmentDateTitleText string
}
