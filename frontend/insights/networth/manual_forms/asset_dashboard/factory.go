//go:generate mockgen -source=factory.go -destination=./mocks/mock_factory.go package=mocks
package asset_dashboard

import (
	"context"
	"fmt"

	"github.com/google/wire"

	"github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/asset_dashboard/asset"
)

var AssetDashboardGeneratorFactoryWireSet = wire.NewSet(NewAssetDashboardGeneratorFactoryImpl, wire.Bind(new(IAssetDashboardGeneratorFactory), new(*AssetDashboardGeneratorFactoryImpl)))

type IAssetDashboardGeneratorFactory interface {
	GetAssetDashboardGenerator(ctx context.Context, assetType networth.AssetType) (IAssetDashboardGenerator, error)
}

type AssetDashboardGeneratorFactoryImpl struct {
	genericDashboardGenerator *asset.GenericAssetDashboardGenerator
	cashDashboardGenerator    *asset.CashAssetDashboardGenerator
	esopDashboardGenerator    *asset.ESOPAssetDashboardGenerator
}

func NewAssetDashboardGeneratorFactoryImpl() *AssetDashboardGeneratorFactoryImpl {
	return &AssetDashboardGeneratorFactoryImpl{
		genericDashboardGenerator: asset.NewGenericAssetDashboardGenerator(),
		cashDashboardGenerator:    asset.NewCashAssetDashboardGenerator(),
		esopDashboardGenerator:    asset.NewESOPAssetDashboardGenerator(),
	}
}

func (f *AssetDashboardGeneratorFactoryImpl) GetAssetDashboardGenerator(_ context.Context, assetType networth.AssetType) (IAssetDashboardGenerator, error) {
	switch assetType {
	case networth.AssetType_ASSET_TYPE_AIF,
		networth.AssetType_ASSET_TYPE_PRIVATE_EQUITY,
		networth.AssetType_ASSET_TYPE_REAL_ESTATE,
		networth.AssetType_ASSET_TYPE_ART_ARTEFACTS,
		networth.AssetType_ASSET_TYPE_BONDS,
		networth.AssetType_ASSET_TYPE_DIGITAL_GOLD,
		networth.AssetType_ASSET_TYPE_DIGITAL_SILVER,
		networth.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE,
		networth.AssetType_ASSET_TYPE_GADGETS:
		return f.genericDashboardGenerator, nil
	case networth.AssetType_ASSET_TYPE_CASH:
		return f.cashDashboardGenerator, nil
	case networth.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION:
		return f.esopDashboardGenerator, nil
	default:
		return nil, fmt.Errorf("unhandled asset type : %s in asset dashboard generator factory", assetType.String())
	}
}
