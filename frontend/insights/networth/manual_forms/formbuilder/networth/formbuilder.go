//go:generate mockgen -source=formbuilder.go -destination=./mocks/mock_formbuilder.go package=mocks
package networth

import (
	"context"
	"fmt"

	"github.com/google/wire"

	"github.com/epifi/gamma/api/frontend/insights/networth"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	networthBeFePb "github.com/epifi/gamma/api/insights/networth/frontend"
	networthBeModelPb "github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
)

var FormBuilderFactoryWireSet = wire.NewSet(NewFormBuilderFactoryImpl, wire.Bind(new(FormBuilderFactory), new(*FormBuilderFactoryImpl)))

type FormBuilder interface {
	// BuildForm creates a form with the input components. The form can be an empty form or a pre-filled form.
	BuildForm(ctx context.Context, req *networthBeFePb.BuildFormRequest) (*networth.NetWorthManualForm, error)
	// ConvertFormInputToInvestmentDeclaration transforms a given form to corresponding investment declaration
	// converter expects the input components to be pre-validated
	ConvertFormInputToInvestmentDeclaration(ctx context.Context, inputComponents []*networth.NetWorthManualFormInputComponent) (*networthBeModelPb.InvestmentDeclaration, error)
}

type FormBuilderFactory interface {
	GetFormBuilder(ctx context.Context, assetType networthBePb.AssetType) (FormBuilder, error)
}

type FormBuilderFactoryImpl struct {
	aif                        *AIF
	artArtefacts               *ArtArtefacts
	bonds                      *Bonds
	cash                       *Cash
	digitalGold                *DigitalGold
	digitalSilver              *DigitalSilver
	privateEquity              *PrivateEquity
	realEstate                 *RealEstate
	portfolioManagementService *PortfolioManagementService
	employeeStockOption        *EmployeeStockOption
	deposit                    *Deposit
	networthConfig             *genconf.NetworthConfig
	networthClient             networthBePb.NetWorthClient
	gadgets                    *Gadgets
}

func NewFormBuilderFactoryImpl(
	networthConfig *genconf.NetworthConfig,
	networthClient networthBePb.NetWorthClient,
	releaseEvaluator release.IEvaluator,
) *FormBuilderFactoryImpl {
	return &FormBuilderFactoryImpl{
		aif:                        NewAIF(networthConfig, networthClient, releaseEvaluator),
		artArtefacts:               NewArtArtefacts(),
		bonds:                      NewBonds(),
		cash:                       NewCash(),
		digitalGold:                NewDigitalGold(),
		digitalSilver:              NewDigitalSilver(),
		privateEquity:              NewPrivateEquity(),
		realEstate:                 NewRealEstate(),
		portfolioManagementService: NewPortfolioManagementService(networthConfig, networthClient, releaseEvaluator),
		employeeStockOption:        NewEmployeeStockOption(),
		deposit:                    NewDeposit(),
		gadgets:                    NewGadgets(),
	}
}

func (f *FormBuilderFactoryImpl) GetFormBuilder(ctx context.Context, assetType networthBePb.AssetType) (FormBuilder, error) {
	switch assetType {
	case networthBePb.AssetType_ASSET_TYPE_AIF:
		return f.aif, nil
	case networthBePb.AssetType_ASSET_TYPE_ART_ARTEFACTS:
		return f.artArtefacts, nil
	case networthBePb.AssetType_ASSET_TYPE_BONDS:
		return f.bonds, nil
	case networthBePb.AssetType_ASSET_TYPE_CASH:
		return f.cash, nil
	case networthBePb.AssetType_ASSET_TYPE_DIGITAL_GOLD:
		return f.digitalGold, nil
	case networthBePb.AssetType_ASSET_TYPE_DIGITAL_SILVER:
		return f.digitalSilver, nil
	case networthBePb.AssetType_ASSET_TYPE_PRIVATE_EQUITY:
		return f.privateEquity, nil
	case networthBePb.AssetType_ASSET_TYPE_REAL_ESTATE:
		return f.realEstate, nil
	case networthBePb.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE:
		return f.portfolioManagementService, nil
	case networthBePb.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION:
		return f.employeeStockOption, nil
	case networthBePb.AssetType_ASSET_TYPE_FIXED_DEPOSITS:
		return f.deposit, nil
	case networthBePb.AssetType_ASSET_TYPE_GADGETS:
		return f.gadgets, nil
	default:
		return nil, fmt.Errorf("unhandled asset type: %s for form builder impl", assetType.String())
	}
}
