package utils

import (
	"github.com/epifi/gamma/api/insights/networth"
	types "github.com/epifi/gamma/api/typesv2"
)

var (
	AssetTypeToInstrumentTypeMap = map[networth.AssetType]types.InvestmentInstrumentType{
		networth.AssetType_ASSET_TYPE_AIF:                          types.InvestmentInstrumentType_AIF,
		networth.AssetType_ASSET_TYPE_PRIVATE_EQUITY:               types.InvestmentInstrumentType_PRIVATE_EQUITY,
		networth.AssetType_ASSET_TYPE_REAL_ESTATE:                  types.InvestmentInstrumentType_REAL_ESTATE,
		networth.AssetType_ASSET_TYPE_ART_ARTEFACTS:                types.InvestmentInstrumentType_ART_AND_ARTEFACTS,
		networth.AssetType_ASSET_TYPE_BONDS:                        types.InvestmentInstrumentType_BOND,
		networth.AssetType_ASSET_TYPE_CASH:                         types.InvestmentInstrumentType_CASH,
		networth.AssetType_ASSET_TYPE_DIGITAL_GOLD:                 types.InvestmentInstrumentType_DIGITAL_GOLD,
		networth.AssetType_ASSET_TYPE_DIGITAL_SILVER:               types.InvestmentInstrumentType_DIGITAL_SILVER,
		networth.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE: types.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE,
		networth.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION:        types.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION,
		networth.AssetType_ASSET_TYPE_GADGETS:                      types.InvestmentInstrumentType_GADGETS,
	}
	InstrumentTypeToAssetTypeMap = map[types.InvestmentInstrumentType]networth.AssetType{
		types.InvestmentInstrumentType_AIF:                          networth.AssetType_ASSET_TYPE_AIF,
		types.InvestmentInstrumentType_PRIVATE_EQUITY:               networth.AssetType_ASSET_TYPE_PRIVATE_EQUITY,
		types.InvestmentInstrumentType_REAL_ESTATE:                  networth.AssetType_ASSET_TYPE_REAL_ESTATE,
		types.InvestmentInstrumentType_ART_AND_ARTEFACTS:            networth.AssetType_ASSET_TYPE_ART_ARTEFACTS,
		types.InvestmentInstrumentType_BOND:                         networth.AssetType_ASSET_TYPE_BONDS,
		types.InvestmentInstrumentType_CASH:                         networth.AssetType_ASSET_TYPE_CASH,
		types.InvestmentInstrumentType_DIGITAL_GOLD:                 networth.AssetType_ASSET_TYPE_DIGITAL_GOLD,
		types.InvestmentInstrumentType_DIGITAL_SILVER:               networth.AssetType_ASSET_TYPE_DIGITAL_SILVER,
		types.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE: networth.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE,
		types.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION:        networth.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION,
		types.InvestmentInstrumentType_FIXED_DEPOSIT:                networth.AssetType_ASSET_TYPE_FIXED_DEPOSITS,
		types.InvestmentInstrumentType_RECURRING_DEPOSIT:            networth.AssetType_ASSET_TYPE_FIXED_DEPOSITS,
		types.InvestmentInstrumentType_GADGETS:                      networth.AssetType_ASSET_TYPE_GADGETS,
	}
)
