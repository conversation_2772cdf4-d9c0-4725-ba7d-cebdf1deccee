package networth

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	feError "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/insights/networth"
	"github.com/epifi/gamma/api/typesv2"
	formErrors "github.com/epifi/gamma/frontend/insights/networth/manual_forms/errors"
)

const (
	companyNotSupportedText          = "New company addition is not supported yet"
	investmentDateAfterMaturityDate  = "Maturity date before investment date is not allowed"
	investmentDateAfterCurrentDate   = "Future investment date is not allowed"
	firstDateOfIssueAfterCurrentDate = "First date of issue after current date is not allowed"
)

func (s *Service) SubmitManualForm(ctx context.Context, req *networth.SubmitManualFormRequest) (*networth.SubmitManualFormResponse, error) {
	err := s.submitManualForm(ctx, req.GetReq().GetAuth().GetActorId(), req.GetFormIdentifier(), req.GetFormData())
	if err != nil {
		logger.Error(ctx, "failed to submit manual form", zap.Any("form identifier", req.GetFormIdentifier()), zap.Error(err))
		return generateSubmitManualFormResponseFromErr(err)
	}
	return genSubmitManualFormResponseWithStatus(rpc.StatusOk()), nil
}

func (s *Service) SubmitManualForms(ctx context.Context, req *networth.SubmitManualFormsRequest) (*networth.SubmitManualFormsResponse, error) {
	for _, form := range req.GetFormSubmissionData() {
		err := s.submitManualForm(ctx, req.GetReq().GetAuth().GetActorId(), form.GetFormIdentifier(), form.GetFormData())
		if err != nil {
			logger.Error(ctx, "failed to submit manual forms", zap.Any("form identifier", form.GetFormIdentifier()), zap.Error(err))
		}
	}
	return &networth.SubmitManualFormsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

func (s *Service) submitManualForm(ctx context.Context, actorId string, formIdentifier *typesv2.ManualAssetFormIdentifier, formData []*networth.NetWorthManualInputData) error {
	emptyForm, err := s.formProcessor.BuildEmptyForm(ctx, &formbuilder.BuildFormRequest{
		ActorId:        actorId,
		FormIdentifier: formIdentifier,
	})
	if err != nil {
		return fmt.Errorf("failure in build empty form : %w", err)
	}

	formInputComponents, err := s.validateAndSetInputDataInForm(ctx, formData, emptyForm)
	if err != nil {
		return fmt.Errorf("failure in validate and set input data : %w", err)
	}
	err = s.formProcessor.SubmitForm(ctx, &formbuilder.SubmitFormRequest{
		ActorId:         actorId,
		FormIdentifier:  formIdentifier,
		InputComponents: formInputComponents,
		FormData:        formData,
	})
	if err != nil {
		return fmt.Errorf("failure in submit form : %w", err)
	}
	return nil
}

func (s *Service) validateAndSetInputDataInForm(ctx context.Context, formData []*networth.NetWorthManualInputData, form *networth.NetWorthManualForm) ([]*networth.NetWorthManualFormInputComponent, error) {
	allInputComponents := form.GetAllInputComponents()
	// note that validate input data also sets the input data into formInputComponents
	validateErr := s.formInputValidator.ValidateInputData(ctx, formData, allInputComponents)
	if validateErr != nil {
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, validateErr.Error())
	}
	return allInputComponents, nil
}

func generateSubmitManualFormResponseFromErr(err error) (*networth.SubmitManualFormResponse, error) {
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return genSubmitManualFormResponseWithStatus(rpc.StatusInvalidArgument()), nil
	case errors.Is(err, formErrors.CustomCompanyNotAllowedError):
		return genSubmitManualFormResponseWithBottomSheet(rpc.StatusInvalidArgument(), companyNotSupportedText), nil
	case errors.Is(err, formErrors.InvestmentDateAfterMaturityDateError):
		return genSubmitManualFormResponseWithBottomSheet(rpc.StatusInvalidArgument(), investmentDateAfterMaturityDate), nil
	case errors.Is(err, formErrors.InvestmentDateAfterCurrentDateError):
		return genSubmitManualFormResponseWithBottomSheet(rpc.StatusInvalidArgument(), investmentDateAfterCurrentDate), nil
	case errors.Is(err, formErrors.FirstDateOfIssueAfterCurrentDateError):
		return genSubmitManualFormResponseWithBottomSheet(rpc.StatusInvalidArgument(), firstDateOfIssueAfterCurrentDate), nil
	default:
		return genSubmitManualFormResponseWithStatus(rpc.StatusInternal()), nil
	}
}

func genSubmitManualFormResponseWithStatus(status *rpc.Status) *networth.SubmitManualFormResponse {
	return &networth.SubmitManualFormResponse{
		RespHeader: &header.ResponseHeader{
			Status: status,
		},
	}
}

func genSubmitManualFormResponseWithBottomSheet(status *rpc.Status, errorText string) *networth.SubmitManualFormResponse {
	return &networth.SubmitManualFormResponse{
		RespHeader: &header.ResponseHeader{
			Status: status,
			ErrorView: &feError.ErrorView{
				Type: feError.ErrorViewType_BOTTOM_SHEET,
				Options: &feError.ErrorView_BottomSheetErrorView{
					BottomSheetErrorView: &feError.BottomSheetErrorView{
						Title: errorText,
						Ctas: []*feError.CTA{
							{
								Type:         feError.CTA_DONE,
								Text:         "Ok, got it",
								DisplayTheme: feError.CTA_SECONDARY,
							},
						},
					},
				},
			},
		},
	}
}
