package nudge

import (
	"context"
	"math/rand"

	"github.com/epifi/be-common/pkg/colors"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	widget2 "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/hash"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/header"
	nudgeFePb "github.com/epifi/gamma/api/frontend/nudge"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	nudgeBePb "github.com/epifi/gamma/api/nudge"
	types "github.com/epifi/gamma/api/typesv2"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/nudge/widget"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

type Service struct {
	nudgeFePb.UnimplementedNudgeServiceServer
	nudgeClient                 nudgeBePb.NudgeServiceClient
	nudgeWidgetGeneratorFactory widget.INudgeWidgetGeneratorFactory
	dynConf                     *genconf.Config
	releaseEvaluator            release.IEvaluator
	onboardingClient            onboardingPb.OnboardingClient
	questSdkClient              *questSdk.Client
	userAttributeFetcher        pkgUser.UserAttributesFetcher
	networthClient              networthPb.NetWorthClient
}

func NewService(
	nudgeClient nudgeBePb.NudgeServiceClient,
	nudgeWidgetGeneratorFactory widget.INudgeWidgetGeneratorFactory,
	dynConf *genconf.Config,
	releaseEvaluator release.IEvaluator,
	onboardingClient onboardingPb.OnboardingClient,
	questSdkClient *questSdk.Client,
	userAttributeFetcher pkgUser.UserAttributesFetcher,
	networthClient networthPb.NetWorthClient,
) *Service {
	return &Service{
		nudgeClient:                 nudgeClient,
		nudgeWidgetGeneratorFactory: nudgeWidgetGeneratorFactory,
		dynConf:                     dynConf,
		releaseEvaluator:            releaseEvaluator,
		onboardingClient:            onboardingClient,
		questSdkClient:              questSdkClient,
		userAttributeFetcher:        userAttributeFetcher,
		networthClient:              networthClient,
	}
}

func (s *Service) GetNudges(ctx context.Context, request *nudgeFePb.GetNudgesRequest) (*nudgeFePb.GetNudgesResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()

	fetchNudgesReq := &nudgeBePb.FetchDisplayableNudgesRequest{
		ActorId:   actorId,
		Screen:    request.GetScreen(),
		SessionId: request.GetReq().GetSessionId(),
	}
	fetchNudgesRes, err := s.nudgeClient.FetchDisplayableNudges(ctx, fetchNudgesReq)
	if te := epifigrpc.RPCError(fetchNudgesRes, err); te != nil {
		logger.Error(ctx, "FetchDisplayableNudges call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(te))
		return &nudgeFePb.GetNudgesResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}

	nudgeWidgetGenerator, err := s.nudgeWidgetGeneratorFactory.GetNudgeWidgetGenerator(request.GetScreen())
	if err != nil {
		logger.Error(ctx, "error fetching nudge widget generator", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return &nudgeFePb.GetNudgesResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}

	appPlatform := request.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := request.GetReq().GetAuth().GetDevice().GetAppVersion()
	widgetNudges := s.updateWidgetNudges(ctx, actorId, nudgeWidgetGenerator.GetNudges(fetchNudgesRes.GetNudgeInstances(), appPlatform, appVersion))
	widgetBanners := nudgeWidgetGenerator.GetBanners(fetchNudgesRes.GetNudgeInstances(), appPlatform, appVersion)

	return &nudgeFePb.GetNudgesResponse{
		RespHeader:                 &header.ResponseHeader{Status: rpc.StatusOk()},
		Title:                      nudgeWidgetGenerator.GetTitle(ctx, actorId),
		Nudges:                     widgetNudges,
		NudgeDisplayType:           nudgeWidgetGenerator.GetDisplayType(),
		LastCompletedNudgeId:       fetchNudgesRes.GetLastCompletedNudgeInstanceId(),
		NudgeEntryAnimationEnabled: s.dynConf.HomeRevampParams().HomeNudgeParams().IsNudgeEntryAnimationEnabled(),
		Banners:                    widgetBanners,
		BannerStartIndex:           s.getBannerStartIndex(request.GetReq().GetSessionId(), len(widgetBanners)),
		BannersEnabled:             apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dynConf.HomeRevampParams().HomeNudgeParams().BannerFeatureReleaseConfig()),
	}, nil
}

func (s *Service) DismissNudge(ctx context.Context, req *nudgeFePb.DismissNudgeRequest) (*nudgeFePb.DismissNudgeResponse, error) {
	// here, nudge id from client side is actually nudge instance id for backend
	dismissNudgeForActorRes, err := s.nudgeClient.DismissNudgeForActor(ctx, &nudgeBePb.DismissNudgeForActorRequest{
		NudgeInstanceId: req.GetNudgeId(),
	})
	if rpcErr := epifigrpc.RPCError(dismissNudgeForActorRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling dismiss nudge for actor rpc", zap.Error(rpcErr))
		return &nudgeFePb.DismissNudgeResponse{RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()}}, nil
	}

	// add feedback engine info if backend asks to trigger feedback flow for dismissal
	var feedbackEngineInfo *header.FeedbackEngineInfo
	if dismissNudgeForActorRes.GetShouldTriggerFeedbackFlow() {
		feedbackEngineInfo = &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType:       types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_APP_FLOW,
				FlowIdentifier:           types.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_NUDGE_DISMISSAL.String(),
				FlowInvocationIdentifier: req.GetNudgeId(),
			},
		}
	}

	return &nudgeFePb.DismissNudgeResponse{
		RespHeader: &header.ResponseHeader{
			FeedbackEngineInfo: feedbackEngineInfo,
			Status:             rpc.StatusOk(),
		},
	}, nil
}

// getBannerStartIndex returns the start index for the widget banners
// if forceBannerStartIndex is set, it will return that value if it is less than the number of widget banners
// nolint:gosec
func (s *Service) getBannerStartIndex(sessionId string, noWidgetBanners int) int64 {
	var (
		bannerStartIndex      int64 = 0
		forceBannerStartIndex       = s.dynConf.HomeRevampParams().HomeNudgeParams().ForceBannerStartIndex()
	)
	if noWidgetBanners == 0 {
		return bannerStartIndex
	}
	if forceBannerStartIndex != -1 && forceBannerStartIndex < int32(noWidgetBanners) {
		bannerStartIndex = int64(forceBannerStartIndex)
	} else {
		bannerStartIndex = rand.New(rand.NewSource(int64(hash.Hash(sessionId, 0)))).Int63() % int64(noWidgetBanners)
	}
	return bannerStartIndex
}

// updateWidgetNudges updates the widget nudges with the block background color
// when new feature home design enhancements are enabled.
// TODO(sahil): remove after new feature is fully rolled out and update for all by default
func (s *Service) updateWidgetNudges(ctx context.Context, actorId string, nudges []*nudgeFePb.Nudge) []*nudgeFePb.Nudge {
	isFeatureHomeDesignEnhancementsEnabled := featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.releaseEvaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	})

	if !isFeatureHomeDesignEnhancementsEnabled {
		return nudges
	}

	for _, nudge := range nudges {
		nudge.BorderColor = widget2.GetBlockBackgroundColour(colors.ColorMonochromeChalk)
		nudge.Shadows = nil
	}
	return nudges

}
