//nolint:goimports
package pay

import (
	"context"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	beNetWorthPb "github.com/epifi/gamma/api/insights/networth"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	payPb "github.com/epifi/gamma/api/pay"
	bmsPb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	bmsEnums "github.com/epifi/gamma/api/pay/beneficiarymanagement/enums"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"

	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pay/internal"
	fePaySearch "github.com/epifi/gamma/frontend/pay/search/provider"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

type Service struct {
	bmsClient              bmsPb.BeneficiaryManagementClient
	upiOnboardingClient    upiOnboardingPb.UpiOnboardingClient
	conf                   *config.Config
	accountPiClient        accountPiPb.AccountPIRelationClient
	savingsClient          savingsPb.SavingsClient
	dynamicConf            *genconf.Config
	connectedAccountClient connectedAccountPb.ConnectedAccountClient
	payClient              payPb.PayClient
	searcherProvider       fePaySearch.ISearchProcessorProvider
	releaseEvaluator       release.IEvaluator
	actorClient            actorPb.ActorClient
	onboardingClient       onboardingPb.OnboardingClient
	questSdkClient         *questSdk.Client
	userAttributeFetcher   pkgUser.UserAttributesFetcher
	networthClient         beNetWorthPb.NetWorthClient
}

func NewService(
	bmsClient bmsPb.BeneficiaryManagementClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	conf *config.Config,
	accountPiClient accountPiPb.AccountPIRelationClient,
	savingsClient savingsPb.SavingsClient,
	dynamicConf *genconf.Config,
	payClient payPb.PayClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	searcherProvider fePaySearch.ISearchProcessorProvider,
	releaseEvaluator release.IEvaluator,
	actorClient actorPb.ActorClient,
	onboardingClient onboardingPb.OnboardingClient,
	questSdkClient *questSdk.Client,
	userAttributeFetcher pkgUser.UserAttributesFetcher,
	networthClient beNetWorthPb.NetWorthClient,
) *Service {
	return &Service{
		bmsClient:              bmsClient,
		upiOnboardingClient:    upiOnboardingClient,
		conf:                   conf,
		accountPiClient:        accountPiClient,
		savingsClient:          savingsClient,
		dynamicConf:            dynamicConf,
		connectedAccountClient: connectedAccountClient,
		payClient:              payClient,
		searcherProvider:       searcherProvider,
		releaseEvaluator:       releaseEvaluator,
		actorClient:            actorClient,
		onboardingClient:       onboardingClient,
		questSdkClient:         questSdkClient,
		userAttributeFetcher:   userAttributeFetcher,
		networthClient:         networthClient,
	}
}

var (
	defaultErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    "We faced a server error",
				Subtitle: "Uh-oh! We faced a server error while fetching the details. Please try again.",
			},
		},
	}
)

// InitiateAuthForBeneficiaryActivation is useful to initiate the auth for the beneficiary activation
// It will internally call the BE rpc and based upon the mode of the authentication the BE rpc will take the respective action
func (s *Service) InitiateAuthForBeneficiaryActivation(ctx context.Context, req *fePayPb.InitiateAuthForBeneficiaryActivationRequest) (*fePayPb.InitiateAuthForBeneficiaryActivationResponse, error) {
	// unique identifier for the auth request
	clientRequestId := uuid.NewString()
	initAutRes, initAuthErr := s.bmsClient.InitiateAuth(ctx, &bmsPb.InitiateAuthRequest{
		ActorFrom:   req.GetActorFrom(),
		PiTo:        req.GetPiTo(),
		AuthMode:    req.GetAuthMode(),
		ClientReqId: clientRequestId,
	})

	if err := epifigrpc.RPCError(initAutRes, initAuthErr); err != nil {
		logger.Error(ctx, "failed to initiate auth for beneficiary activation", zap.String(logger.PI_TO, req.GetPiTo()), zap.Error(err))
		return &fePayPb.InitiateAuthForBeneficiaryActivationResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("failed to initiate auth"),
			},
		}, nil
	}

	return &fePayPb.InitiateAuthForBeneficiaryActivationResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ClientRequestId: clientRequestId,
		NextAction:      initAutRes.GetNextAction(),
	}, nil
}

// GetBeneficiaryActivationPostAuthAction is useful to redirect the client to the respective screen post the auth action is in the terminal state
// It will internally call the BE rpc and based upon the mode of authentication the BE rpc will fetch the status and the rpc will return the respective screen
func (s *Service) GetBeneficiaryActivationPostAuthAction(ctx context.Context, req *fePayPb.GetBeneficiaryActivationPostAuthActionRequest) (*fePayPb.GetBeneficiaryActivationPostAuthActionResponse, error) {

	activationStatusRes, activationStatusErr := s.bmsClient.CheckAndUpdateActivationStatus(ctx, &bmsPb.CheckAndUpdateActivationStatusRequest{
		ClientReqId: req.GetClientRequestId(),
	})
	if err := epifigrpc.RPCError(activationStatusRes, activationStatusErr); err != nil {
		logger.Error(ctx, "failed to fetch the beneficiary actication post auth action", zap.String(logger.CLIENT_REQUEST_ID, req.GetClientRequestId()), zap.Error(err))
		return &fePayPb.GetBeneficiaryActivationPostAuthActionResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch the beneficiary actication post auth action"),
			},
		}, nil
	}

	var bottomSheet *fePayPb.BeneficiaryCoolDownBottomSheet
	if activationStatusRes.GetActivationStatus() != bmsEnums.ActivationStatus_ACTIVATION_STATUS_SUCCESS {
		bottomSheet = internal.GetBeneficiaryCoolDownBottomSheet(activationStatusRes.GetActorFrom(), activationStatusRes.GetPiTo(), fePayPb.BeneficiaryActivationAuthMode_ACTIVATION_AUTH_MODE_LIVENESS)
	}

	return &fePayPb.GetBeneficiaryActivationPostAuthActionResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		CooldownBottomSheet: bottomSheet,
	}, nil
}

func (s *Service) GetPayWidget(ctx context.Context, req *fePayPb.GetPayWidgetRequest) (*fePayPb.GetPayWidgetResponse, error) {
	return nil, nil
}

// shouldShowNewPayLandingScreen determines whether the user should see the new Pay landing screen [Design Fixit].
// This decision is based on user's app version, platform, and the `home_design_enhancements` feature flag.
//
// Context:
// Backend changes linked to the `home_design_enhancements` flag can cause the Pay landing screen
// to break on older app versions. To prevent this, we include explicit app version checks
// in addition to the flag check.
//
// Logic:
// 1. First, check if the user's app version meets the minimum required version for their platform:
//   - iOS: must be >= IOSMinVersionForDesignFixit
//   - Android: must be >= AndroidMinVersionForDesignFixit
//
// 2. If version checks are met, check if user is eligible for the new home design enhancements feature.
// NOTE: We are not using Separate Feature Flag for PAY to maintain consistency for both the Home and Pay screens.
func (s *Service) shouldShowNewPayLandingScreen(ctx context.Context, actorId string, platform commontypes.Platform, appVersion uint32) bool {
	// 1. Check platform and version constraints
	switch platform {
	case commontypes.Platform_ANDROID:
		if appVersion < s.dynamicConf.PayLandingScreenParams().AndroidMinVersionForDesignFixit() {
			logger.Info(ctx, "home design fixit disabled due to Android version constraint",
				zap.String(logger.PLATFORM, platform.String()),
				zap.Uint32(logger.APP_VERSION_CODE, appVersion),
				zap.String(logger.ACTOR_ID_V2, actorId))
			return false
		}
	case commontypes.Platform_IOS:
		if appVersion < s.dynamicConf.PayLandingScreenParams().IOSMinVersionForDesignFixit() {
			// TODO(Ashutosh) - Clean this log line
			logger.Info(ctx, "home design fixit disabled due to IOS version constraint",
				zap.String(logger.PLATFORM, platform.String()),
				zap.Uint32(logger.APP_VERSION_CODE, appVersion),
				zap.String(logger.ACTOR_ID_V2, actorId))
			return false
		}
	default:
		logger.Error(ctx, "home design fixit disabled due to unsupported platform", zap.String(logger.ACTOR_ID_V2, actorId))
		return false
	}

	// 2. Check if the new home design enhancements feature is enabled for the user
	return featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.releaseEvaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttributeFetcher,
			NetWorthClient:       s.networthClient,
		},
	})
}
