package common

import (
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

// ExternalDependencies holds common external client dependencies.
// Add new dependencies here as needed.
type ExternalDependencies struct {
	Evaluator            release.IEvaluator
	OnboardingClient     onboarding.OnboardingClient
	QuestSdkClient       *questSdk.Client
	UserAttributeFetcher pkgUser.UserAttributesFetcher
	NetWorthClient       networthPb.NetWorthClient
}

// GetEvaluator safely returns the Evaluator.
func (d *ExternalDependencies) GetEvaluator() release.IEvaluator {
	if d == nil {
		return nil
	}
	return d.Evaluator
}

// GetOnboardingClient safely returns the OnboardingClient.
func (d *ExternalDependencies) GetOnboardingClient() onboarding.OnboardingClient {
	if d == nil {
		return nil
	}
	return d.OnboardingClient
}

// GetQuestSdkClient safely returns the QuestSdkClient.
func (d *ExternalDependencies) GetQuestSdkClient() *questSdk.Client {
	if d == nil {
		return nil
	}
	return d.QuestSdkClient
}

// GetUserAttributeFetcher safely returns the UserAttributeFetcher.
func (d *ExternalDependencies) GetUserAttributeFetcher() pkgUser.UserAttributesFetcher {
	if d == nil {
		return nil
	}
	return d.UserAttributeFetcher
}

func (d *ExternalDependencies) GetNetWorthClient() networthPb.NetWorthClient {
	if d == nil {
		return nil
	}
	return d.NetWorthClient
}
