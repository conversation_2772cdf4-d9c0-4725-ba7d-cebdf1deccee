package featureflags

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/pkg/common"
	homePkg "github.com/epifi/gamma/frontend/pkg/home"
	"github.com/epifi/gamma/pkg/feature/release"
)

// IsFeatureHomeDesignEnhancementsEnabledRequest is the request struct for IsFeatureHomeDesignEnhancementsEnabled.
type IsFeatureHomeDesignEnhancementsEnabledRequest struct {
	ActorId      string
	ExternalDeps *common.ExternalDependencies
}

// GetActorId returns the ActorId from the request.
func (r *IsFeatureHomeDesignEnhancementsEnabledRequest) GetActorId() string {
	if r == nil {
		return ""
	}
	return r.ActorId
}

// GetExternalDeps returns the ExternalDependencies from the request.
func (r *IsFeatureHomeDesignEnhancementsEnabledRequest) GetExternalDeps() *common.ExternalDependencies {
	if r == nil {
		return nil
	}
	return r.ExternalDeps
}

// Validate checks if the request is valid.
func (r *IsFeatureHomeDesignEnhancementsEnabledRequest) Validate() error {
	env, _ := cfg.GetEnvironment()
	if cfg.IsTestEnv(env) {
		return fmt.Errorf("feature flags are not enabled in test environment")
	}

	if r == nil {
		return fmt.Errorf("request is nil")
	}
	if r.GetActorId() == "" {
		return fmt.Errorf("actorId is empty")
	}
	externalDeps := r.GetExternalDeps() // Assign to var to avoid multiple calls in checks
	if externalDeps == nil {
		return fmt.Errorf("external dependencies is nil")
	}
	if externalDeps.GetEvaluator() == nil {
		return fmt.Errorf("evaluator is nil")
	}
	return nil
}

// validUserTypesForFeatureHomeDesignEnhancements lists the user types eligible for the FEATURE_HOME_DESIGN_ENHANCEMENTS feature.
var validUserTypesForFeatureHomeDesignEnhancements = []homePkg.UserType{
	homePkg.UserTypeFiSA,
	homePkg.UserTypeFiSAWithD0To7,
	homePkg.UserTypeFiSAWithD8To14,
	homePkg.UserTypeFiSAWithD15To28,
}

// IsFeatureHomeDesignEnhancementsEnabled checks if the home design enhancements feature is enabled for a user
func IsFeatureHomeDesignEnhancementsEnabled(ctx context.Context, req *IsFeatureHomeDesignEnhancementsEnabledRequest) bool {
	if err := req.Validate(); err != nil {
		logger.Error(ctx, "Validation failed for IsFeatureHomeDesignEnhancementsEnabledRequest", zap.Error(err))
		return false
	}

	var (
		actorId      = req.GetActorId()
		externalDeps = req.GetExternalDeps()
		evaluator    = externalDeps.GetEvaluator()
	)

	// Get the user type first
	userType, _, _, err := homePkg.GetSuitableUserType(ctx, &homePkg.GetSuitableUserTypeRequest{
		ActorId:      actorId,
		ExternalDeps: externalDeps,
	})
	if err != nil {
		logger.Error(ctx, "error getting suitable user type for home design enhancements", zap.Error(err))
		return false // Default to false if user type cannot be determined
	}

	// Check if the user type is valid for this feature
	if !lo.Contains(validUserTypesForFeatureHomeDesignEnhancements, userType) {
		return false
	}

	var (
		feature           = types.Feature_FEATURE_HOME_DESIGN_ENHANCEMENTS
		featureConstraint = release.NewCommonConstraintData(feature).WithActorId(actorId)
	)
	isFeatureEnabled, errEval := evaluator.Evaluate(ctx, featureConstraint)
	if errEval != nil {
		logger.Error(ctx, "error evaluating feature flag", zap.String(logger.FEATURE, feature.String()), zap.Error(errEval))
		isFeatureEnabled = false
	}
	return isFeatureEnabled
}

// IsWealthAnalyserUserRequest is the request struct for IsWealthAnalyserUser.
type IsWealthAnalyserUserRequest struct {
	ActorId      string
	ExternalDeps *common.ExternalDependencies
}

// GetExternalDeps safely returns the common ExternalDependencies from the request.
func (r *IsWealthAnalyserUserRequest) GetExternalDeps() *common.ExternalDependencies {
	if r == nil {
		return nil
	}
	return r.ExternalDeps
}

// GetActorId safely returns the ActorId from the request.
func (r *IsWealthAnalyserUserRequest) GetActorId() string {
	if r == nil {
		return ""
	}
	return r.ActorId
}

// Validate checks if the request is valid.
func (r *IsWealthAnalyserUserRequest) Validate() error {
	if r == nil {
		return fmt.Errorf("request is nil")
	}
	if r.GetActorId() == "" {
		return fmt.Errorf("actorId is empty")
	}
	externalDeps := r.GetExternalDeps() // Assign to var to avoid multiple calls in checks
	if externalDeps == nil {
		return fmt.Errorf("external dependencies is nil")
	}
	if externalDeps.GetOnboardingClient() == nil {
		return fmt.Errorf("onboarding client is nil")
	}
	if externalDeps.GetUserAttributeFetcher() == nil {
		return fmt.Errorf("user attribute fetcher is nil")
	}
	if externalDeps.GetNetWorthClient() == nil {
		return fmt.Errorf("net worth client is nil")
	}
	return nil
}

// IsWealthAnalyserUser checks if the user is a wealth analyser user
func IsWealthAnalyserUser(ctx context.Context, req *IsWealthAnalyserUserRequest) (bool, error) {
	if err := req.Validate(); err != nil {
		logger.Error(ctx, "Validation failed for IsWealthAnalyserUserRequest", zap.Error(err))
		return false, err
	}

	var (
		actorId      = req.GetActorId()
		externalDeps = req.GetExternalDeps()
	)

	userType, _, _, err := homePkg.GetSuitableUserType(ctx, &homePkg.GetSuitableUserTypeRequest{
		ActorId:      actorId,
		ExternalDeps: externalDeps,
	})
	if err != nil {
		logger.Error(ctx, "error calling GetSuitableUserType for IsWealthAnalyserUser", zap.Error(err))
		return false, err
	}
	return userType == homePkg.UserTypeWealthAnalyser, nil
}
