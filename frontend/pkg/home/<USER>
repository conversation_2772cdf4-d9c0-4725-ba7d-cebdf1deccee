package home

type UserType int

const (
	UserTypeUnspecified            UserType = iota
	UserTypeFiSA                   UserType = 1
	UserTypeFiLite                 UserType = 2
	UserTypeFiLiteCC               UserType = 3
	UserTypeFiLitePL               UserType = 4
	UserTypeDirectToFiLiteNetworth UserType = 5
	UserTypeDirectToFiLiteUpsell   UserType = 6
	UserTypeFiNR                   UserType = 7
	UserTypeFiSAWithD0To7          UserType = 8
	UserTypeFiSAWithD8To14         UserType = 9
	UserTypeFiSAWithD15To28        UserType = 10
	// UserTypeWealthAnalyser this is asset connected wealth analyser user
	UserTypeWealthAnalyser                 UserType = 11
	UserTypeNoAssetConnectedWealthAnalyser UserType = 12
)

// String returns the string representation of UserType
func (u UserType) String() string {
	switch u {
	case UserTypeFiSA:
		return "UserTypeFiSA"
	case UserTypeFiLite:
		return "UserTypeFiLite"
	case UserTypeFiLiteCC:
		return "UserTypeFiLiteCC"
	case UserTypeFiLitePL:
		return "UserTypeFiLitePL"
	case UserTypeDirectToFiLiteNetworth:
		return "UserTypeDirectToFiLiteNetworth"
	case UserTypeDirectToFiLiteUpsell:
		return "UserTypeDirectToFiLiteUpsell"
	case UserTypeFiNR:
		return "UserTypeFiNR"
	case UserTypeFiSAWithD0To7:
		return "UserTypeFiSAWithD0To7"
	case UserTypeFiSAWithD8To14:
		return "UserTypeFiSAWithD8To14"
	case UserTypeFiSAWithD15To28:
		return "UserTypeFiSAWithD15To28"
	case UserTypeWealthAnalyser:
		return "UserTypeWealthAnalyser"
	case UserTypeNoAssetConnectedWealthAnalyser:
		return "UserTypeNoAssetConnectedWealthAnalyser"
	default:
		return "UserTypeUnspecified"
	}
}
