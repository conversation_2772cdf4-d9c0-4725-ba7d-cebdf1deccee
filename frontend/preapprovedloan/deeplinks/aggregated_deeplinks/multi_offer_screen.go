// nolint:dupl
package aggregated_deeplinks

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/preapprovedloan"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	palEvents "github.com/epifi/gamma/frontend/preapprovedloan/events"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	uiFrontend "github.com/epifi/gamma/frontend/preapprovedloan/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

func (adp *AggregatedDeeplinkProvider) GetMultiOfferScreen(ctx context.Context, loanOptions []*preapprovedloan.LoanOption, isLoansPreQualOfferFlowEnabled bool, currentLr *preapprovedloan.LoanRequest, actorId string) (*deeplinkPb.Deeplink, error) {
	screenOptionsMultiOfferScreen := &palTypesPb.LoansMultipleOfferSelectionScreenOptions{
		BgColor: "#EFF2F6",
		ToolbarHelp: ui.NewITC().WithLeftImageUrlHeightAndWidth(uiFrontend.HelpIconRound, 24, 24).WithDeeplink(&deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HELP_MAIN,
		}),
		ToolbarTitle:              provider.GetText("We found these offers for you", "#313234", commontypes.FontStyle_HEADLINE_L),
		SelectedBannerBorderColor: "#00B899",
		IndexOfDefaultSelected:    -1,
		CtaBanner: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				provider.GetText("Fi is trusted by 35+ Lakh Users ", "#007A56", commontypes.FontStyle_BUTTON_XS),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#DCF3EE",
				LeftPadding:   20,
				RightPadding:  20,
				TopPadding:    8,
				BottomPadding: 8,
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GreenPeopleIcon, 14, 14),
		},
		ChooseOfferCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Continue",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		},
		DefaultCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Continue",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_DISABLED,
		},
	}

	var isNonHardLoanOfferAvailable bool
	for _, lo := range loanOptions {
		if lo.GetLoanOffer() != nil && lo.GetLoanOffer().GetLoanOfferType() != preapprovedloan.LoanOfferType_LOAN_OFFER_TYPE_HARD {
			isNonHardLoanOfferAvailable = true
			break
		}
	}
	if isNonHardLoanOfferAvailable {
		screenOptionsMultiOfferScreen.FooterSecondaryLabel = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				provider.GetText("OFFER MAY GET UPDATED BASED ON YOUR DETAILS", "#929599", commontypes.FontStyle_OVERLINE_2XS_CAPS),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: "#F6F9FD",
			},
		}
	}

	for idx, option := range loanOptions {
		isFirstOption := idx == 0
		dlProvider, err := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
			Vendor:      helper.GetPalFeVendorFromBe(option.GetLoanHeader().GetVendor()),
			LoanProgram: helper.GetFeLoanProgramFromBe(option.GetLoanHeader().GetLoanProgram()),
		})
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("error while getting deeplink provider for offer id: %v", option.GetLoanHeader()))
		}
		offerCard, err := adp.getLoanOfferDetailsCard(ctx, option, isFirstOption, dlProvider, isLoansPreQualOfferFlowEnabled)
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("error while getting loan offer details card for offer id: %v", option.GetLoanHeader()))
		}

		// redirect to the application status screen if user selects any offer for which there is already an active application
		// or if user selects eligibility card and user already has eligibility request active
		continueExistingRequest := (currentLr.GetType() == preapprovedloan.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY && option.GetEligibilityHeader() != nil) ||
			(option.GetLoanOffer() != nil && currentLr.GetCompletedAt() == nil && currentLr.GetVendor() == option.GetLoanOffer().GetVendor() && currentLr.GetLoanProgram() == option.GetLoanOffer().GetLoanProgram())

		if continueExistingRequest {
			applStatusDl, dlErr := adp.GetLoansApplicationStatusPollDeeplink(ctx, helper.GetFeLoanHeaderByBeLoanHeader(&preapprovedloan.LoanHeader{
				LoanProgram: currentLr.GetLoanProgram(),
				Vendor:      currentLr.GetVendor(),
			}), "", currentLr.GetId(), nil)
			if dlErr != nil {
				return nil, errors.Wrap(dlErr, "error while getting appl status dl")
			}
			offerCard.LoansCta = &palTypesPb.LoansCta{
				CtaContent: offerCard.GetLoansCta().GetCtaContent(),
				CtaAction: &palTypesPb.LoansCtaAction{
					Action: &palTypesPb.LoansCtaAction_Deeplink{Deeplink: applStatusDl},
				},
			}
		}

		screenOptionsMultiOfferScreen.OfferDetailsCards = append(screenOptionsMultiOfferScreen.GetOfferDetailsCards(), offerCard)
	}

	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		adp.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), palEvents.NewMultiOfferScreen(actorId, loanOptions))
	})

	loDeepLink := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_MULTIPLE_OFFER_SELECTION_SCREEN, screenOptionsMultiOfferScreen)

	return loDeepLink, nil
}

func (adp *AggregatedDeeplinkProvider) getLoanOfferDetailsCard(ctx context.Context, loanOption *preapprovedloan.LoanOption, isFirstOption bool,
	deeplinkProvider provider.IDeeplinkProvider, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error) {
	switch loanOption.GetLoanOptionType().(type) {
	case *preapprovedloan.LoanOption_LoanOffer:
		loanOffer := loanOption.GetLoanOffer()
		switch loanOffer.GetLoanOfferType() {
		case preapprovedloan.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED:
			return deeplinkProvider.GetPreQualOfferCardMultiOfferScreenComponent(loanOffer, isFirstOption, isLoansPreQualOfferFlowEnabled)
		case preapprovedloan.LoanOfferType_LOAN_OFFER_TYPE_SOFT:
			return deeplinkProvider.GetSoftOfferCardMultiOfferScreenComponent(ctx, loanOffer, isFirstOption, isLoansPreQualOfferFlowEnabled)
		case preapprovedloan.LoanOfferType_LOAN_OFFER_TYPE_HARD:
			return deeplinkProvider.GetHardOfferCardMultiOfferScreenComponent(loanOffer, isFirstOption, isLoansPreQualOfferFlowEnabled)
		default:
			return nil, fmt.Errorf("unknown loan offer type: %v", loanOffer.GetLoanOfferType())
		}
	case *preapprovedloan.LoanOption_EligibilityHeader:
		return deeplinkProvider.GetEligibilityCardMultiOfferScreenComponent(loanOption.GetLoanHeader(), isFirstOption)
	default:
		return nil, fmt.Errorf("unknown loan option type: %v", loanOption.GetLoanOptionType())
	}
}
