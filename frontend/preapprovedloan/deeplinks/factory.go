package deeplinks

import (
	"context"

	"github.com/google/wire"

	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/abfl"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/epifitech"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/federal"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/fiftyfin"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/idfc"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/lenden"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/liquiloans"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/moneyview"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/realtimeetb"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/stockguardian"
)

var WireDeeplinkProviderFactorySet = wire.NewSet(NewDeeplinkProviderFactory, wire.Bind(new(IDeeplinkProviderFactory), new(*DeeplinkProviderFactory)))

type IDeeplinkProviderFactory interface {
	GetDeeplinkGenerator(ctx context.Context, req *GetDeeplinkProviderRequest) (provider.IDeeplinkProvider, error)
}

type GetDeeplinkProviderRequest struct {
	Vendor      palFeEnumsPb.Vendor
	LoanProgram palFeEnumsPb.LoanProgram
}

type DeeplinkProviderFactory struct {
	baseDeeplinkProvider         *baseprovider.BaseDeeplinkProvider
	liquiloansProvider           *liquiloans.Provider
	llEarlySalaryProvider        *liquiloans.EsProvider
	idfcProvider                 *idfc.Provider
	llFldgProvider               *liquiloans.FldgProvider
	llFlPlProvider               *liquiloans.FiLiteProvider
	llAcqToLendProvider          *liquiloans.AcqToLendProvider
	ffLamfProvider               *fiftyfin.LamfProvider
	abflProvider                 *abfl.Provider
	mvProvider                   *moneyview.Provider
	realTimeEtbProvider          *realtimeetb.Provider
	llRealTimeDistProvider       *liquiloans.RealTimeDistProvider
	fedPlProvider                *federal.LoansProvider
	llStplProvider               *liquiloans.StplProvider
	fedRealTimeProvider          *federal.RealTimeProvider
	llRealtimeSubventionProvider *liquiloans.RealTimeSubventionProvider
	llRealtimeStplProvider       *liquiloans.RealTimeStplProvider
	epifiTechEligibilityProvider *epifitech.EligibilityProvider
	llNonFiCoreStpl              *liquiloans.NonFiCoreStplProvider
	llNonFiCoreSubvention        *liquiloans.NonFiCoreSubventionProvider
	stockGuardianProvider        *stockguardian.Provider
	lendenProvider               *lenden.Provider
	abflPwaProvider              *abfl.PwaJourneyProvider
	fedRtdNtbProvider            *federal.RealTimeNtbFedProvider
	nonFiCoreMvProvider          *moneyview.NonFiCoreMvProvider
	earlySalarySgProvider        *stockguardian.StockGuardianProviderEarlySalary
}

func NewDeeplinkProviderFactory(
	baseDeeplinkProvider *baseprovider.BaseDeeplinkProvider,
	liquiloansProvider *liquiloans.Provider,
	llEarlySalaryProvider *liquiloans.EsProvider,
	idfcProvider *idfc.Provider,
	llFldgProvider *liquiloans.FldgProvider,
	llFlPlProvider *liquiloans.FiLiteProvider,
	llAcqToLendProvider *liquiloans.AcqToLendProvider,
	ffLamfProvider *fiftyfin.LamfProvider,
	abflProvider *abfl.Provider,
	mvProvider *moneyview.Provider,
	realTimeEtbProvider *realtimeetb.Provider,
	llRealTimeDistProvider *liquiloans.RealTimeDistProvider,
	fedPlProvider *federal.LoansProvider,
	llStplProvider *liquiloans.StplProvider,
	fedRealTimeProvider *federal.RealTimeProvider,
	llRealtimeSubventionProvider *liquiloans.RealTimeSubventionProvider,
	llRealtimeStplProvider *liquiloans.RealTimeStplProvider,
	epifiTechEligibilityProvider *epifitech.EligibilityProvider,
	llNonFiCoreStpl *liquiloans.NonFiCoreStplProvider,
	llNonFiCoreSubvention *liquiloans.NonFiCoreSubventionProvider,
	stockGuardianRealTimeSubvention *stockguardian.Provider,
	lendenProvider *lenden.Provider,
	abflPwaProvider *abfl.PwaJourneyProvider,
	fedRtdNtbProvider *federal.RealTimeNtbFedProvider,
	nonFiCoreMvProvider *moneyview.NonFiCoreMvProvider,
	earlySalarySgProvider *stockguardian.StockGuardianProviderEarlySalary,
) *DeeplinkProviderFactory {
	return &DeeplinkProviderFactory{
		baseDeeplinkProvider:         baseDeeplinkProvider,
		liquiloansProvider:           liquiloansProvider,
		llEarlySalaryProvider:        llEarlySalaryProvider,
		idfcProvider:                 idfcProvider,
		llFldgProvider:               llFldgProvider,
		llFlPlProvider:               llFlPlProvider,
		llAcqToLendProvider:          llAcqToLendProvider,
		ffLamfProvider:               ffLamfProvider,
		abflProvider:                 abflProvider,
		mvProvider:                   mvProvider,
		realTimeEtbProvider:          realTimeEtbProvider,
		llRealTimeDistProvider:       llRealTimeDistProvider,
		fedPlProvider:                fedPlProvider,
		llStplProvider:               llStplProvider,
		fedRealTimeProvider:          fedRealTimeProvider,
		llRealtimeSubventionProvider: llRealtimeSubventionProvider,
		llRealtimeStplProvider:       llRealtimeStplProvider,
		epifiTechEligibilityProvider: epifiTechEligibilityProvider,
		llNonFiCoreStpl:              llNonFiCoreStpl,
		llNonFiCoreSubvention:        llNonFiCoreSubvention,
		stockGuardianProvider:        stockGuardianRealTimeSubvention,
		lendenProvider:               lendenProvider,
		abflPwaProvider:              abflPwaProvider,
		fedRtdNtbProvider:            fedRtdNtbProvider,
		nonFiCoreMvProvider:          nonFiCoreMvProvider,
		earlySalarySgProvider:        earlySalarySgProvider,
	}
}

func (d *DeeplinkProviderFactory) GetDeeplinkGenerator(_ context.Context, req *GetDeeplinkProviderRequest) (provider.IDeeplinkProvider, error) {
	switch req.LoanProgram {
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		switch req.Vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return d.llRealtimeStplProvider, nil
		default:
			return d.baseDeeplinkProvider, nil
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
		switch req.Vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return d.llRealtimeSubventionProvider, nil
		default:
			return d.baseDeeplinkProvider, nil
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return d.llEarlySalaryProvider, nil
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		return d.llFlPlProvider, nil
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG:
		return d.llFldgProvider, nil
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL:
		return d.llStplProvider, nil
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		switch req.Vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return d.liquiloansProvider, nil
		case palFeEnumsPb.Vendor_IDFC:
			return d.idfcProvider, nil
		case palFeEnumsPb.Vendor_ABFL:
			return d.abflProvider, nil
		case palFeEnumsPb.Vendor_MONEYVIEW:
			return d.mvProvider, nil
		case palFeEnumsPb.Vendor_FEDERAL_BANK:
			return d.fedPlProvider, nil
		default:
			return d.baseDeeplinkProvider, nil
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
		return d.llAcqToLendProvider, nil
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF:
		switch req.Vendor {
		case palFeEnumsPb.Vendor_FIFTYFIN:
			return d.ffLamfProvider, nil
		default:
			return d.baseDeeplinkProvider, nil
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
		return d.realTimeEtbProvider, nil
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		switch req.Vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return d.llRealTimeDistProvider, nil
		case palFeEnumsPb.Vendor_FEDERAL_BANK:
			return d.fedRealTimeProvider, nil
		case palFeEnumsPb.Vendor_LENDEN:
			return d.lendenProvider, nil
		case palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP:
			return d.stockGuardianProvider, nil
		case palFeEnumsPb.Vendor_ABFL:
			return d.abflPwaProvider, nil
		case palFeEnumsPb.Vendor_MONEYVIEW:
			return d.nonFiCoreMvProvider, nil
		default:
			return d.baseDeeplinkProvider, nil
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		switch req.Vendor {
		case palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP:
			return d.earlySalarySgProvider, nil
		default:
			return d.baseDeeplinkProvider, nil
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		switch req.Vendor {
		case palFeEnumsPb.Vendor_EPIFI_TECH:
			return d.epifiTechEligibilityProvider, nil
		default:
			return d.baseDeeplinkProvider, nil
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		switch req.Vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return d.llNonFiCoreStpl, nil
		default:
			return d.baseDeeplinkProvider, nil
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
		switch req.Vendor {
		case palFeEnumsPb.Vendor_LIQUILOANS:
			return d.llNonFiCoreSubvention, nil
		default:
			return d.baseDeeplinkProvider, nil
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
		if req.Vendor == palFeEnumsPb.Vendor_FEDERAL_BANK {
			return d.fedRtdNtbProvider, nil
		}
		return d.baseDeeplinkProvider, nil
	default:
		return d.baseDeeplinkProvider, nil
	}
}
