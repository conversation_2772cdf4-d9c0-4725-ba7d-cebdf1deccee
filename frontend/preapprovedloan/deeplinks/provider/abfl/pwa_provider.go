package abfl

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	typesUiPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	fePalDlHelper "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/helper"

	consentPb "github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type PwaJourneyProvider struct {
	*Provider
	rpcHelper helper.IRpcHelper
}

var _ provider.IDeeplinkProvider = &PwaJourneyProvider{}

func NewPwaJourneyProvider(abflProvider *Provider, rpcHelper helper.IRpcHelper) *PwaJourneyProvider {
	return &PwaJourneyProvider{
		Provider:  abflProvider,
		rpcHelper: rpcHelper,
	}
}

func (abfl *PwaJourneyProvider) GetLoanHeader() *palPbFeEnums.LoanHeader {
	return &palPbFeEnums.LoanHeader{
		LoanProgram: palPbFeEnums.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palPbFeEnums.Vendor_ABFL,
	}
}

func (abfl *PwaJourneyProvider) GetLoanLandingScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, req *provider.LandingInfoRequest) (*deeplinkPb.Deeplink, error) {
	dl, err := abfl.BaseDeeplinkProvider.GetLoanLandingScreenV2DeepLink(ctx, lh, req)
	if err != nil {
		return nil, err
	}
	screenOptions := &palTypesPb.LoansLandingScreenV2{}
	err = dl.GetScreenOptionsV2().UnmarshalTo(screenOptions)
	if err != nil {
		return nil, err
	}

	applyForLoanDl := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_APPLY_FOR_LOAN_SCREEN, &palTypesPb.ApplyForLoanScreenOptions{
		LoanHeader: lh,
		OfferId:    req.GetLoanOffer().GetId(),
	})

	screenOptions.GetComponents()[1].GetVerticalItcListWithTopMarginComponent().GetVerticalIconTextComponents()[1] = &ui.VerticalIconTextComponent{
		Texts:            []*commontypes.Text{commontypes.GetPlainStringText("Get cash in 5 mins").WithFontColor("#38393B").WithFontStyle(commontypes.FontStyle_SUBTITLE_S)},
		TopVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/atl-grid-thunder.png", 30, 30),
		TopImgTxtPadding: 12,
	}

	dataPullConsentCheckBox := &typesUiPb.CheckboxItem{
		Id:          consentPb.ConsentType_CONSENT_TYPE_DATA_PULL_BY_PWA_ABFL_LENDER.String(),
		DisplayText: commontypes.GetHtmlText("<font color=\"#333333\">I hereby consent to Epifi Tech sharing my personal data with Aditya Birla Capital Ltd for the purposes of evaluating my loan application and providing related services</font>").WithFontStyle(commontypes.FontStyle_NUMBER_2XS),
		IsChecked:   true,
	}

	var consentsToShown []*typesUiPb.CheckboxItem
	_, err = abfl.rpcHelper.FetchActiveConsentForActor(ctx, req.GetLoanOffer().GetActorId(), consentPb.ConsentType_CONSENT_TYPE_DATA_PULL_BY_PWA_ABFL_LENDER)
	if err != nil {
		// error will come as record not found if its still not there
		consentsToShown = []*typesUiPb.CheckboxItem{dataPullConsentCheckBox}
	}

	// update the cta on the landing page with apply for loan screen as for abfl, we directly call apply for loan from landing screen
	for _, component := range screenOptions.GetComponents() {
		if _, ok := component.GetComponent().(*palTypesPb.LoansScreenUiComponents_FooterComponent); ok {
			component.GetFooterComponent().GetPartnershipComponent().PartnerLogos = []*commontypes.VisualElement{commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloans/abfl/abfl-newlogo", 16, 48)} // todo: need to ask afl
			component.GetFooterComponent().GetCta().GetCta().Deeplink = applyForLoanDl
			component.GetFooterComponent().GetCta().GetCta().Text = "Get this loan"
			component.GetFooterComponent().GetCta().GetText().DisplayValue = &commontypes.Text_PlainString{
				PlainString: "Get this loan",
			}
			if consentsToShown != nil {
				component.GetFooterComponent().MandatoryConsents = consentsToShown
			}
		}
	}

	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_LANDING_INFO_V2_SCREEN, screenOptions)
}

func (abfl *PwaJourneyProvider) GetLoanApplicationDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, lr *palBePb.LoanRequest, lses []*palBePb.LoanStepExecution, loanOffer *palBePb.LoanOffer) (*provider.GetDashboardLoanApplicationDetailsResponse, error) {
	baseDl, baseDlErr := abfl.BaseDeeplinkProvider.GetLoanApplicationDetailsForDashboard(ctx, lh, lr, lses, loanOffer)
	if baseDlErr != nil {
		return nil, fmt.Errorf("error in getting baseDl from abfl, err: %w", baseDlErr)
	}
	if baseDl == nil {
		return nil, nil
	}

	for i := range baseDl.GetActiveApplicationCards().GetComponents() {
		if baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress() != nil {
			baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().Rows = []*palTypesPb.CardWithLineProgress_Row{
				{
					LeftItem: &palTypesPb.CardWithLineProgress_ColumnItem{
						Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
							Title: &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Loan Amount", "#929599", commontypes.FontStyle_HEADLINE_XS)}},
							Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("₹ ", colors.ColorSlate, commontypes.FontStyle_HEADLINE_XS),
								commontypes.GetTextFromStringFontColourFontStyle("50k-5 lakhs", colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)),
						}},
					},
					RightItem: &palTypesPb.CardWithLineProgress_ColumnItem{
						Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
							Title: &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Duration", "#929599", commontypes.FontStyle_HEADLINE_XS)}},
							Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("6-36 months"),
								colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)),
						}},
					},
				},
			}
			baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().GetCtaTitleRow().RightHamburgerComponent = nil
		}
	}

	for _, lse := range lses {
		if lse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES &&
			lse.GetDetails().GetVendorPwaStagesStepData().GetPwaUrl() != "" {
			pwaCopyBottomSheetComponent, err := provider.GetPwaCopyUrlBottomSheetComponentForDashboard(lse.GetDetails().GetVendorPwaStagesStepData().GetPwaUrl(), lh)
			if err != nil {
				return nil, errors.Wrap(err, "error getting pwa copy url bottom sheet component for dashboard")
			}
			baseDl.GetActiveApplicationCards().Components = append(baseDl.GetActiveApplicationCards().GetComponents(), pwaCopyBottomSheetComponent)
		}
	}

	return baseDl, nil
}

func (abfl *PwaJourneyProvider) GetLoanDashboardScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, actorId string) (*deeplinkPb.Deeplink, error) {
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_DASHBOARD_SCREEN, &palTypesPb.LoansDashboardScreenOptions{
		LoanHeader: &palPbFeEnums.LoanHeader{
			LoanProgram: lh.GetLoanProgram(),
		},
	})
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "unable to create loans dashboard deeplinkV3")
	}
	return dl, nil
}

func (abfl *PwaJourneyProvider) GetLoanAccountDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, li *palBePb.LoanInfo) (*provider.GetDashboardLoanAccountDetailsResponse, error) {
	baseDl, baseDlErr := abfl.BaseDeeplinkProvider.GetLoanAccountDetailsForDashboard(ctx, lh, li)
	if baseDlErr != nil {
		return nil, fmt.Errorf("error in getting baseDl from abfl, err: %w", baseDlErr)
	}
	if baseDl == nil {
		return nil, nil
	}

	// ABFL app link
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	var appStoreLink string
	if platform == commontypes.Platform_IOS {
		appStoreLink = "https://apps.apple.com/in/app/aditya-birla-finance/id6443392029"
	} else {
		appStoreLink = "https://play.google.com/store/apps/details?id=com.adityabirla.finance&hl=en_IN"
	}

	ctaDl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
		ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
			ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{
				ExternalUrl: appStoreLink,
			},
		},
	}
	amount, err := money.ToString(li.GetLoanAccount().GetLoanAmountInfo().GetDisbursedAmount(), 2)
	if err != nil {
		return nil, fmt.Errorf("error converting disbursed amount to string: %w", err)
	}
	baseDl.DisbursalBanner = nil
	baseDl.GetActiveLoanCard().GetCardWithLineProgress().Rows = []*palTypesPb.CardWithLineProgress_Row{
		{
			LeftItem: &palTypesPb.CardWithLineProgress_ColumnItem{
				Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
					Title: &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Amount", "#929599", commontypes.FontStyle_HEADLINE_XS)}},
					Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("₹ ", colors.ColorSlate, commontypes.FontStyle_HEADLINE_XS),
						commontypes.GetTextFromStringFontColourFontStyle(amount, colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)),
				}},
			},
			RightItem: &palTypesPb.CardWithLineProgress_ColumnItem{
				Item: &palTypesPb.CardWithLineProgress_ColumnItem_VerticalTitleValuePair{VerticalTitleValuePair: &palTypesPb.VerticalTitleValuePair{
					Title: &ui.IconTextComponent{Texts: []*commontypes.Text{provider.GetText("Duration", "#929599", commontypes.FontStyle_HEADLINE_XS)}},
					Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(strconv.Itoa(int(li.GetLoanAccount().GetDetails().GetTenureInMonths())), colors.ColorDarkLayer2, commontypes.FontStyle_NUMBER_L)),
				}},
			},
		},
	}
	baseDl.GetActiveLoanCard().GetCardWithLineProgress().CtaBottomRow = nil
	baseDl.GetActiveLoanCard().GetCardWithLineProgress().BannerWithStageProgress = nil
	baseDl.GetActiveLoanCard().GetCardWithLineProgress().GetCtaTitleRow().GetTitle().Deeplink = ctaDl
	baseDl.GetActiveLoanCard().GetCardWithLineProgress().GetCtaTitleRow().GetCta().Deeplink = ctaDl

	return baseDl, nil
}

func (abfl *PwaJourneyProvider) GetSoftOfferCardMultiOfferScreenComponent(ctx context.Context, lo *palBePb.LoanOffer, isFirstOffer bool, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error) {
	baseLodCard, err := abfl.BaseDeeplinkProvider.GetSoftOfferCardMultiOfferScreenComponent(ctx, lo, isFirstOffer, isLoansPreQualOfferFlowEnabled)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting baseLodCard for mv provider")
	}

	if !isLoansPreQualOfferFlowEnabled {
		return baseLodCard, nil
	}

	lh := &palPbFeEnums.LoanHeader{
		LoanProgram: helper.GetFeLoanProgramFromBe(lo.GetLoanProgram()),
		Vendor:      helper.GetPalFeVendorFromBe(lo.GetVendor()),
	}

	dataPullConsentCheckBox := &typesUiPb.CheckboxItem{
		Id:          consentPb.ConsentType_CONSENT_TYPE_DATA_PULL_BY_PWA_ABFL_LENDER.String(),
		DisplayText: commontypes.GetHtmlText("<font color=\"#333333\">I hereby consent to Epifi Tech sharing my personal data with Aditya Birla Capital Ltd for the purposes of evaluating my loan application and providing related services</font>").WithFontStyle(commontypes.FontStyle_NUMBER_2XS),
		IsChecked:   true,
	}

	// Moneyview data pull consent is needed from the user before starting the loan application journey, so checking if consent was already taken from the user,
	// if consent was not already taken then show a checkbox to take consent from the user now.
	var consentsToShown []*typesUiPb.CheckboxItem
	_, err = abfl.rpcHelper.FetchActiveConsentForActor(ctx, lo.GetActorId(), consentPb.ConsentType_CONSENT_TYPE_DATA_PULL_BY_PWA_ABFL_LENDER)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		consentsToShown = []*typesUiPb.CheckboxItem{dataPullConsentCheckBox}
	case err != nil:
		// intentionally muting the error, since consent flows are idempotent, we can again show a consent checkbox to the user in case we see a failure while checking if the consent already exists.
		logger.WarnWithCtx(ctx, "error checking if user has already consented for moneyview data pull", zap.String(logger.ACTOR_ID_V2, lo.GetActorId()))
		consentsToShown = []*typesUiPb.CheckboxItem{dataPullConsentCheckBox}
	}

	if baseLodCard.GetLoansCta() != nil {
		if len(consentsToShown) > 0 {
			dl, err := getPreBreLoanConsentScreen(lh, lo.GetId(), consentsToShown, baseLodCard.GetLoansCta())
			if err != nil {
				return nil, errors.Wrap(err, "error getting pre-bre loan consent screen")
			}
			baseLodCard.GetLoansCta().CtaAction = &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_Deeplink{Deeplink: dl}}
		} else {
			baseLodCard.GetLoansCta().CtaAction = getApplyForLoanLoanCtaAction(lh, lo.GetId())
		}
	}

	return baseLodCard, nil
}

func getPreBreLoanConsentScreen(lh *palPbFeEnums.LoanHeader, loId string, consents []*typesUiPb.CheckboxItem, existingLoansCta *palTypesPb.LoansCta) (*deeplinkPb.Deeplink, error) {
	lh.DataOwner = palPbFeEnums.Vendor_ABFL
	ctaContent := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         "Continue",
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
		Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
	}
	if existingLoansCta != nil {
		existingLoansCta.CtaContent = ctaContent
		existingLoansCta.CtaAction = getApplyForLoanLoanCtaAction(lh, loId)
	}
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CONSENT_V2_SCREEN, &palTypesPb.LoansConsentV2ScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{FeedbackEngineInfo: &feHeaderPb.FeedbackEngineInfo{
			FlowIdDetails: &feHeaderPb.FlowIdentifierDetails{
				FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_CONSENT_V2_SCREEN.String(),
			},
		}},
		LoanHeader:     lh,
		Flow:           palPbFeEnums.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY,
		TopIcon:        commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloans/abfl/abfl-newlogo", 32, 96),
		Title:          fePalDlHelper.GetText("Confirm the following to apply for a loan", "#333333", "", commontypes.FontStyle_HEADLINE_L),
		Subtitle:       fePalDlHelper.GetText("Mandatory Step for Loan application evaluation", "#8D8D8D", "", commontypes.FontStyle_BODY_3),
		ConsentItems:   consents,
		Cta:            ctaContent,
		BgColor:        "#FFFFFF",
		ConsentBgColor: colors.ColorOnDarkHighEmphasis,
		LoansCta:       existingLoansCta,
	})
}

func getApplyForLoanLoanCtaAction(lh *palPbFeEnums.LoanHeader, loId string) *palTypesPb.LoansCtaAction {
	return &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
		RpcName: palTypesPb.LoansCtaAction_RPC_NAME_APPLY_FOR_LOAN,
		CommonMetaData: &palTypesPb.LoansCtaAction_CallRpc_CommonMetaData{
			LoanHeader: lh,
			LoId:       loId,
		},
	}}}
}
