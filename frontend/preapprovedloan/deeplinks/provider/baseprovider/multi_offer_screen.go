// nolint:dupl
package baseprovider

import (
	"context"
	"fmt"
	"math"
	"strconv"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/money"
	palPb "github.com/epifi/gamma/api/frontend/preapprovedloan"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	uiFrontend "github.com/epifi/gamma/frontend/preapprovedloan/ui"
)

func GetDummyOfferForEligibility(lh *palBePb.LoanHeader) *palBePb.LoanOffer {
	loanOffer := &palBePb.LoanOffer{
		Vendor: lh.GetVendor(),
		OfferConstraints: &palBePb.OfferConstraints{
			MaxLoanAmount:   money.AmountINR(500000).GetPb(),
			MaxEmiAmount:    money.AmountINR(25000).GetPb(),
			MaxTenureMonths: 48,
			MinLoanAmount:   money.AmountINR(100000).GetPb(),
			MinTenureMonths: 12,
		},
		ProcessingInfo: &palBePb.OfferProcessingInfo{},
		LoanProgram:    lh.GetLoanProgram(),
		LoanOfferType:  palBePb.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED,
	}
	if lh.GetLoanProgram() == palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2 {
		loanOffer.GetOfferConstraints().MaxLoanAmount = money.AmountINR(50000).GetPb()
		loanOffer.GetProcessingInfo().RateOfInterest = 0.0
		loanOffer.GetOfferConstraints().MaxTenureMonths = 1
		loanOffer.GetOfferConstraints().MinTenureMonths = 1
	}
	return loanOffer
}

func (bdp *BaseDeeplinkProvider) GetEligibilityCardMultiOfferScreenComponent(lh *palBePb.LoanHeader, isFirstOption bool) (*palTypesPb.LoanOfferDetailsCard, error) {
	dummyOffer := GetDummyOfferForEligibility(lh)
	offeredByKeyValueRow := getOfferedByKeyValueRow(dummyOffer)
	tenureRangeKeyValueRow := getTenureRangeKeyValueRow(dummyOffer)
	emiStartingFromKeyValueRow, emiErr := getEmiStartingFromKeyValueRow(dummyOffer)
	if emiErr != nil {
		return nil, emiErr
	}
	approvalChancesKeyValueRow := getApprovalChancesKeyValueRow(dummyOffer)
	var keyValueRows []*palTypesPb.KeyValueRow
	keyValueRows = appendIfNotNil(keyValueRows, offeredByKeyValueRow, tenureRangeKeyValueRow, emiStartingFromKeyValueRow, approvalChancesKeyValueRow)

	var cardTopBadge *palTypesPb.LoanOfferDetailsCard_CardTopBadge
	if isFirstOption {
		cardTopBadge = getCardTopBadge(dummyOffer)
	}

	rpcReq, err := anyPb.New(&palPb.CheckLoanEligibilityRequest{
		LoanHeader: helper.GetFeLoanHeaderByBeLoanHeader(lh),
	})
	if err != nil {
		return nil, errors.Wrap(err, "cannot convert check loan eligibility request to any type")
	}

	return &palTypesPb.LoanOfferDetailsCard{
		CardTopBadge: cardTopBadge,
		TopSection:   getTopSection(dummyOffer, isFirstOption),
		BottomSection: &palTypesPb.LoanOfferDetailsCard_BottomSection{
			ShowBorder:   true,
			KeyValueRows: keyValueRows,
		},
		LoanHeader: helper.GetFeLoanHeaderByBeLoanHeader(lh),
		Shadow: &widget.Shadow{
			Opacity: 10,
			Colour: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: "#000000",
				},
			},
		},
		LoansCta: &palTypesPb.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Continue",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
			CtaAction: &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
				RpcName:    palTypesPb.LoansCtaAction_RPC_NAME_CHECK_LOAN_ELIGIBILITY,
				RpcRequest: rpcReq,
			}}},
		},
		OfferMessage: []*ui.IconTextComponent{
			{Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Better offer awaits!", "#E8AD62", commontypes.FontStyle_HEADLINE_S)}},
			{Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Adding more data will unlock a better offer", "#929599", commontypes.FontStyle_SUBTITLE_2XS)}},
		},
	}, nil
}

func (bdp *BaseDeeplinkProvider) GetPreQualOfferCardMultiOfferScreenComponent(loanOffer *palBePb.LoanOffer, isFirstOffer bool, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error) {
	offeredByKeyValueRow := getOfferedByKeyValueRow(loanOffer)
	tenureRangeKeyValueRow := getTenureRangeKeyValueRow(loanOffer)
	emiStartingFromKeyValueRow, emiErr := getEmiStartingFromKeyValueRow(loanOffer)
	if emiErr != nil {
		return nil, emiErr
	}
	approvalChancesKeyValueRow := getApprovalChancesKeyValueRow(loanOffer)
	var keyValueRows []*palTypesPb.KeyValueRow
	keyValueRows = appendIfNotNil(keyValueRows, offeredByKeyValueRow, tenureRangeKeyValueRow, emiStartingFromKeyValueRow, approvalChancesKeyValueRow)

	var cardTopBadge *palTypesPb.LoanOfferDetailsCard_CardTopBadge
	if isFirstOffer {
		cardTopBadge = getCardTopBadge(loanOffer)
	}

	var loansCta *palTypesPb.LoansCta
	var err error
	if isLoansPreQualOfferFlowEnabled {
		loansCta, err = bdp.GetPreQualOfferLandingScreenLoansCta(loanOffer)
		if err != nil {
			return nil, fmt.Errorf("error while getting loan cta: %w", err)
		}
	}
	lh := &palPbFeEnums.LoanHeader{
		LoanProgram: helper.GetFeLoanProgramFromBe(loanOffer.GetLoanProgram()),
		Vendor:      helper.GetPalFeVendorFromBe(loanOffer.GetVendor()),
	}
	return &palTypesPb.LoanOfferDetailsCard{
		CardTopBadge: cardTopBadge,
		TopSection:   getTopSection(loanOffer, isFirstOffer),
		BottomSection: &palTypesPb.LoanOfferDetailsCard_BottomSection{
			ShowBorder:   true,
			KeyValueRows: keyValueRows,
		},
		LoanOfferId: loanOffer.GetId(),
		LoanHeader:  lh,
		Shadow: &widget.Shadow{
			Opacity: 10,
			Colour: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: "#000000",
				},
			},
		},
		LoansCta: loansCta,
	}, nil
}

func (bdp *BaseDeeplinkProvider) GetPreQualOfferLandingScreenLoansCta(loanOffer *palBePb.LoanOffer) (*palTypesPb.LoansCta, error) {
	return &palTypesPb.LoansCta{
		CtaContent: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Continue",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
		// Override this according to the flow, for e.g. check fed ntb flow
		CtaAction: &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
			RpcName: palTypesPb.LoansCtaAction_RPC_NAME_APPLY_FOR_LOAN,
			CommonMetaData: &palTypesPb.LoansCtaAction_CallRpc_CommonMetaData{
				LoanHeader: &palPbFeEnums.LoanHeader{
					LoanProgram: helper.GetFeLoanProgramFromBe(loanOffer.GetLoanProgram()),
					Vendor:      helper.GetPalFeVendorFromBe(loanOffer.GetVendor()),
				},
				LoId: loanOffer.GetId(),
			},
		}}},
	}, nil
}

func (bdp *BaseDeeplinkProvider) GetSoftOfferCardMultiOfferScreenComponent(ctx context.Context, loanOffer *palBePb.LoanOffer, isFirstOffer bool, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error) {
	offeredByKeyValueRow := getOfferedByKeyValueRow(loanOffer)
	maxTenureKeyValueRow := getMaxTenureKeyValueRow(loanOffer)
	emiStartingFromKeyValueRow, emiErr := getEmiStartingFromKeyValueRow(loanOffer)
	if emiErr != nil {
		return nil, emiErr
	}
	approvalChancesKeyValueRow := getApprovalChancesKeyValueRow(loanOffer)
	var keyValueRows []*palTypesPb.KeyValueRow
	keyValueRows = appendIfNotNil(keyValueRows, offeredByKeyValueRow, maxTenureKeyValueRow, emiStartingFromKeyValueRow, approvalChancesKeyValueRow)

	var cardTopBadge *palTypesPb.LoanOfferDetailsCard_CardTopBadge
	if isFirstOffer {
		cardTopBadge = getCardTopBadge(loanOffer)
	}

	lh := &palPbFeEnums.LoanHeader{
		LoanProgram: helper.GetFeLoanProgramFromBe(loanOffer.GetLoanProgram()),
		Vendor:      helper.GetPalFeVendorFromBe(loanOffer.GetVendor()),
	}
	card := &palTypesPb.LoanOfferDetailsCard{
		CardTopBadge: cardTopBadge,
		TopSection:   getTopSection(loanOffer, isFirstOffer),
		BottomSection: &palTypesPb.LoanOfferDetailsCard_BottomSection{
			ShowBorder:   true,
			KeyValueRows: keyValueRows,
		},
		LoanOfferId: loanOffer.GetId(),
		LoanHeader:  lh,
		Shadow: &widget.Shadow{
			Opacity: 10,
			Colour: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: "#000000",
				},
			},
		},
		LoansCta: &palTypesPb.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Continue",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
			CtaAction: &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
				RpcName: palTypesPb.LoansCtaAction_RPC_NAME_OFFER_DETAILS,
				CommonMetaData: &palTypesPb.LoansCtaAction_CallRpc_CommonMetaData{
					LoanHeader: lh,
					LoId:       loanOffer.GetId(),
				},
			}}},
		},
	}

	if !isLoansPreQualOfferFlowEnabled {
		card.LoansCta = nil
	}

	return card, nil
}

func (bdp *BaseDeeplinkProvider) GetHardOfferCardMultiOfferScreenComponent(loanOffer *palBePb.LoanOffer, isFirstOffer bool, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error) {
	offeredByKeyValueRow := getOfferedByKeyValueRow(loanOffer)
	expectedDisbursalTime := getExpectedDisbursalTimeKeyValueRow(loanOffer)
	maxAmountKeyValueRow := getMaxAmountKeyValueRow(loanOffer)
	approvalChancesKeyValueRow := getApprovalChancesKeyValueRow(loanOffer)
	var keyValueRows []*palTypesPb.KeyValueRow
	keyValueRows = appendIfNotNil(keyValueRows, offeredByKeyValueRow, expectedDisbursalTime, maxAmountKeyValueRow, approvalChancesKeyValueRow)

	var cardTopBadge *palTypesPb.LoanOfferDetailsCard_CardTopBadge
	if isFirstOffer {
		cardTopBadge = getCardTopBadge(loanOffer)
	}
	lh := &palPbFeEnums.LoanHeader{
		LoanProgram: helper.GetFeLoanProgramFromBe(loanOffer.GetLoanProgram()),
		Vendor:      helper.GetPalFeVendorFromBe(loanOffer.GetVendor()),
	}

	card := &palTypesPb.LoanOfferDetailsCard{
		CardTopBadge: cardTopBadge,
		TopSection:   getTopSection(loanOffer, isFirstOffer),
		BottomSection: &palTypesPb.LoanOfferDetailsCard_BottomSection{
			ShowBorder:   true,
			KeyValueRows: keyValueRows,
		},
		LoanOfferId: loanOffer.GetId(),
		LoanHeader:  lh,
		Shadow: &widget.Shadow{
			Opacity: 10,
			Colour: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: "#000000",
				},
			},
		},
		LoansCta: &palTypesPb.LoansCta{
			CtaContent: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Continue",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
			CtaAction: &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
				RpcName: palTypesPb.LoansCtaAction_RPC_NAME_OFFER_DETAILS,
				CommonMetaData: &palTypesPb.LoansCtaAction_CallRpc_CommonMetaData{
					LoanHeader: lh,
					LoId:       loanOffer.GetId(),
				},
			}}},
		},
	}
	if !isLoansPreQualOfferFlowEnabled {
		card.LoansCta = nil
	}

	return card, nil
}

func getCardTopBadge(loanOffer *palBePb.LoanOffer) *palTypesPb.LoanOfferDetailsCard_CardTopBadge {
	// todo: Move it behind a vendor interface
	// Dont add any other new vendor specific handling here
	shortlistedVendorStr := "FI"
	if loanOffer.GetVendor() == palBePb.Vendor_LENDEN {
		shortlistedVendorStr = loanOffer.GetVendor().String()
	}

	return &palTypesPb.LoanOfferDetailsCard_CardTopBadge{
		TriangleBgColor: "#AFD2A2",
		Text:            GetText(fmt.Sprintf("Shortlisted by %s for you", shortlistedVendorStr), "#38393B", commontypes.FontStyle_OVERLINE_XS_CAPS),
		BgColorGradient: &widget.LinearGradient{
			Degree: 0,
			LinearColorStops: []*widget.ColorStop{
				{
					Color:          "#AFD2A2",
					StopPercentage: 30,
				},
				{
					Color:          "#D5E6CE",
					StopPercentage: 60,
				},
				{
					Color:          "#AFD2A2",
					StopPercentage: 60,
				},
			},
		},
	}
}

func getTopSection(loanOffer *palBePb.LoanOffer, isFirstOffer bool) *palTypesPb.LoanOfferDetailsCard_TopSection {
	vendorSquareIcon, vendorIconWidth, vendorIconHeight, _ := uiFrontend.GetVendorSquareImageAndText(loanOffer.GetVendor())
	bgImage := commontypes.GetVisualElementFromUrlHeightAndWidth(vendorSquareIcon, vendorIconHeight, vendorIconWidth)
	var showBorder bool
	// tagBgColor, tagTextColor := "#FBF3E6", "#C0723D"
	if isFirstOffer {
		bgImage = uiFrontend.GetVendorGlowImg(loanOffer.GetVendor())
		showBorder = true
		// tagBgColor, tagTextColor = "#EDF5EB", "#37522A"
	}

	var textsValComponent *ui.IconTextComponent
	moneyInString := "₹ " + money.ToDisplayStringInIndianFormat(loanOffer.GetOfferConstraints().GetMaxLoanAmount(), 0, false)
	if loanOffer.GetLoanOfferType() == palBePb.LoanOfferType_LOAN_OFFER_TYPE_HARD {
		interestRate := loanOffer.GetProcessingInfo().GetInterestRate()[0].GetPercentage()
		if loanOffer.GetVendor() == palBePb.Vendor_LENDEN {
			interestRate = math.Round(interestRate*12*100) / 100
		}
		textsValComponent = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText(fmt.Sprintf("%.1f%% p.a.", interestRate), "#313234", commontypes.FontStyle_HEADLINE_L),
			},
		}
	} else {
		textsValComponent = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText("Upto  ", "#313234", commontypes.FontStyle_HEADLINE_S),
				GetText(moneyInString, "#313234", commontypes.FontStyle_HEADLINE_L),
			},
		}
	}

	return &palTypesPb.LoanOfferDetailsCard_TopSection{
		Type: &palTypesPb.LoanOfferDetailsCard_TopSection_OfferDetailsBanner{
			OfferDetailsBanner: &palTypesPb.LoanOfferDetailsCard_OfferBanner{
				OfferDescription: &ui.VerticalKeyValuePair{
					Title: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							// TODO: this needs to be added after discussing the logic with the product.
							// GetText("LOWEST INTEREST", tagTextColor, commontypes.FontStyle_OVERLINE_2XS_CAPS),
						},
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{
							// BgColor:       tagBgColor,
							LeftPadding:   8,
							RightPadding:  8,
							TopPadding:    2,
							BottomPadding: 2,
							CornerRadius:  12,
						},
					},
					VerticalPaddingBtwTitleValue: 8,
					Value:                        textsValComponent,
					HAlignment:                   ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
				},
				BackgroundImage: bgImage,
				ShowBorder:      showBorder,
			},
		},
	}
}

// nolint:dogsled
func getOfferedByKeyValueRow(loanOffer *palBePb.LoanOffer) *palTypesPb.KeyValueRow {
	_, _, _, vendorName := uiFrontend.GetVendorSquareImageAndText(loanOffer.GetVendor())

	return &palTypesPb.KeyValueRow{
		Key: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.StarIcon, 20, 20),
			Texts: []*commontypes.Text{
				GetText("Offered by", "#929599", commontypes.FontStyle_HEADLINE_S),
			},
			LeftImgTxtPadding: 4,
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText(vendorName, "#313234", commontypes.FontStyle_HEADLINE_S),
			},
		},
	}
}

func getExpectedDisbursalTimeKeyValueRow(loanOffer *palBePb.LoanOffer) *palTypesPb.KeyValueRow {
	if loanOffer.GetOfferDisplayInfo().GetExpectedTimeToGetFunding() == "" {
		return nil
	}
	return &palTypesPb.KeyValueRow{
		Key: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.HourGlassIcon, 20, 20),
			Texts: []*commontypes.Text{
				GetText("Expected Disbursal time", "#929599", commontypes.FontStyle_HEADLINE_S),
			},
			LeftImgTxtPadding: 4,
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText(loanOffer.GetOfferDisplayInfo().GetExpectedTimeToGetFunding(), "#313234", commontypes.FontStyle_HEADLINE_S),
			},
		},
	}
}

func getMaxAmountKeyValueRow(loanOffer *palBePb.LoanOffer) *palTypesPb.KeyValueRow {
	if loanOffer.GetOfferConstraints().GetMaxLoanAmount() == nil {
		return nil
	}
	moneyInString := "₹ " + money.ToDisplayStringInIndianFormat(loanOffer.GetOfferConstraints().GetMaxLoanAmount(), 0, false)

	return &palTypesPb.KeyValueRow{
		Key: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.MoneyBagBasketIcon, 16, 16),
			Texts: []*commontypes.Text{
				GetText("Max amount", "#929599", commontypes.FontStyle_HEADLINE_S),
			},
			LeftImgTxtPadding: 4,
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText(moneyInString, "#313234", commontypes.FontStyle_HEADLINE_S),
			},
		},
	}
}

func getTenureRangeKeyValueRow(loanOffer *palBePb.LoanOffer) *palTypesPb.KeyValueRow {
	tenureString := strconv.Itoa(int(loanOffer.GetOfferConstraints().GetMinTenureMonths())) + "-" + strconv.Itoa(int(loanOffer.GetOfferConstraints().GetMaxTenureMonths())) + " months"

	return &palTypesPb.KeyValueRow{
		Key: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ClockIcon, 16, 16),
			Texts: []*commontypes.Text{
				GetText("Tenure range", "#929599", commontypes.FontStyle_HEADLINE_S),
			},
			LeftImgTxtPadding: 4,
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText(tenureString, "#313234", commontypes.FontStyle_HEADLINE_S),
			},
		},
	}
}

func getMaxTenureKeyValueRow(loanOffer *palBePb.LoanOffer) *palTypesPb.KeyValueRow {
	maxTenureString := ConvertToYearsandMonths(loanOffer.GetOfferConstraints().GetMaxTenureMonths())

	return &palTypesPb.KeyValueRow{
		Key: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ClockIcon, 16, 16),
			Texts: []*commontypes.Text{
				GetText("Max Tenure", "#929599", commontypes.FontStyle_HEADLINE_S),
			},
			LeftImgTxtPadding: 4,
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText(maxTenureString, "#313234", commontypes.FontStyle_HEADLINE_S),
			},
		},
	}
}

func getApprovalChancesKeyValueRow(loanOffer *palBePb.LoanOffer) *palTypesPb.KeyValueRow {
	var approvalChancesString string
	switch loanOffer.GetOfferDisplayInfo().GetFundingProbability() {
	case palBePb.Probability_PROBABILITY_LOW:
		approvalChancesString = "Low"
	case palBePb.Probability_PROBABILITY_MEDIUM:
		approvalChancesString = "Medium"
	case palBePb.Probability_PROBABILITY_HIGH:
		approvalChancesString = "High"
	default:
		return nil
	}

	return &palTypesPb.KeyValueRow{
		Key: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.CoinsIcon, 16, 16),
			Texts: []*commontypes.Text{
				GetText("Approval chances", "#929599", commontypes.FontStyle_HEADLINE_S),
			},
			LeftImgTxtPadding: 4,
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText(approvalChancesString, "#313234", commontypes.FontStyle_HEADLINE_S),
			},
		},
	}
}

func getEmiStartingFromKeyValueRow(loanOffer *palBePb.LoanOffer) (*palTypesPb.KeyValueRow, error) {
	if loanOffer.GetOfferConstraints().GetMinLoanAmount().GetUnits() == 0 || loanOffer.GetOfferConstraints().GetMaxTenureMonths() == 0 {
		return nil, nil
	}

	emiStarting, err := money.Div(loanOffer.GetOfferConstraints().GetMinLoanAmount(), decimal.NewFromInt32(loanOffer.GetOfferConstraints().GetMaxTenureMonths()))
	if err != nil {
		return nil, fmt.Errorf("error in calculating emi starting value, err: %w", err)
	}
	emiStartingFromString := "₹ " + money.ToDisplayStringInIndianFormat(emiStarting, 0, false)

	return &palTypesPb.KeyValueRow{
		Key: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.MoneyBagWithStackIcon, 16, 16),
			Texts: []*commontypes.Text{
				GetText("EMI starting from", "#929599", commontypes.FontStyle_HEADLINE_S),
			},
			LeftImgTxtPadding: 4,
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				GetText(emiStartingFromString, "#313234", commontypes.FontStyle_HEADLINE_S),
			},
		},
	}, nil
}

func appendIfNotNil(slice []*palTypesPb.KeyValueRow, elements ...*palTypesPb.KeyValueRow) []*palTypesPb.KeyValueRow {
	for _, elem := range elements {
		if elem != nil {
			slice = append(slice, elem)
		}
	}
	return slice
}
