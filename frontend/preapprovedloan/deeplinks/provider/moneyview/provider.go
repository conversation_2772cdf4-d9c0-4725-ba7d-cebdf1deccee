package moneyview

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetUiPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"

	consentPb "github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	typesPkg "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	genConf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	fePalDlHelper "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/helper"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type Provider struct {
	*baseprovider.BaseDeeplinkProvider
	rpcHelper             helper.IRpcHelper
	preApprovedLoanClient palBePb.PreApprovedLoanClient
}

var _ provider.IDeeplinkProvider = &Provider{}

func NewMvProvider(
	baseProvider *baseprovider.BaseDeeplinkProvider,
	rpcHelper helper.IRpcHelper,
	preApprovedLoanClient palBePb.PreApprovedLoanClient,
) *Provider {
	return &Provider{
		BaseDeeplinkProvider:  baseProvider,
		rpcHelper:             rpcHelper,
		preApprovedLoanClient: preApprovedLoanClient,
	}
}

func (mv *Provider) GetLoanHeader() *palPbFeEnums.LoanHeader {
	return &palPbFeEnums.LoanHeader{
		LoanProgram: palPbFeEnums.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palPbFeEnums.Vendor_MONEYVIEW,
	}
}

func (mv *Provider) GetLoanLandingScreenDeepLink(ctx context.Context, lh *palPbFeEnums.LoanHeader, req *provider.LandingInfoRequest) (*deeplinkPb.Deeplink, error) {
	// todo need to add check if the user already have an active loan account or loan application request
	dl, err := mv.BaseDeeplinkProvider.GetLoanLandingScreenV2DeepLink(ctx, lh, req)
	if err != nil {
		return nil, err
	}
	screenOptions := &palTypesPb.LoansLandingScreenV2{}
	err = dl.GetScreenOptionsV2().UnmarshalTo(screenOptions)
	if err != nil {
		return nil, err
	}

	applyForLoanDl := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_APPLY_FOR_LOAN_SCREEN, &palTypesPb.ApplyForLoanScreenOptions{
		LoanHeader: lh,
		OfferId:    req.GetLoanOffer().GetId(),
	})

	dataPullConsentCheckBox := &widgetUiPb.CheckboxItem{
		Id:          consentPb.ConsentType_DATA_PULL_BY_MONEYVIEW_LENDER.String(),
		DisplayText: commontypes.GetHtmlText("<font color=\"#6A6D70\">I accept <a href=\"https://moneyview.in/terms-conditions-loans-partners\" style=\"color: #00B899;\">Moneyview's T&Cs</a> and authorise Whizdm Finance and Moneyview's lending partners to retrieve my credit bureau information for processing the loan</font>").WithFontStyle(commontypes.FontStyle_NUMBER_2XS),
		IsChecked:   true,
	}

	// Moneyview data pull consent is needed from the user before starting the loan application journey, so checking if consent was already taken from the user,
	// if consent was not already taken then show a checkbox to take consent from the user now.
	var consentsToShown []*widgetUiPb.CheckboxItem
	_, err = mv.rpcHelper.FetchActiveConsentForActor(ctx, req.GetLoanOffer().GetActorId(), consentPb.ConsentType_DATA_PULL_BY_MONEYVIEW_LENDER)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		consentsToShown = []*widgetUiPb.CheckboxItem{dataPullConsentCheckBox}
	case err != nil:
		// intentionally muting the error, since consent flows are idempotent, we can again show a consent checkbox to the user in case we see a failure while checking if the consent already exists.
		logger.WarnWithCtx(ctx, "error checking if user has already consented for moneyview data pull", zap.String(logger.ACTOR_ID_V2, req.GetLoanOffer().GetActorId()))
		consentsToShown = []*widgetUiPb.CheckboxItem{dataPullConsentCheckBox}
	}

	screenOptions.GetComponents()[1].GetVerticalItcListWithTopMarginComponent().GetVerticalIconTextComponents()[1] = &ui.VerticalIconTextComponent{
		Texts:            []*commontypes.Text{commontypes.GetPlainStringText("Get cash in 5 mins").WithFontColor("#38393B").WithFontStyle(commontypes.FontStyle_SUBTITLE_S)},
		TopVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/atl-grid-thunder.png", 30, 30),
		TopImgTxtPadding: 12,
	}

	// update the cta on the landing page with apply for loan screen as for money-view, we directly call apply for loan from landing screen
	for _, component := range screenOptions.GetComponents() {
		if _, ok := component.GetComponent().(*palTypesPb.LoansScreenUiComponents_FooterComponent); ok {
			component.GetFooterComponent().GetPartnershipComponent().PartnerLogos = []*commontypes.VisualElement{commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/landing_moneyview_logo.png", 16, 48)}
			component.GetFooterComponent().GetCta().GetCta().Deeplink = applyForLoanDl
			component.GetFooterComponent().GetCta().GetCta().Text = "Get this loan"
			component.GetFooterComponent().GetCta().GetText().DisplayValue = &commontypes.Text_PlainString{
				PlainString: "Get this loan",
			}
			if consentsToShown != nil {
				component.GetFooterComponent().MandatoryConsents = consentsToShown
			}
		}
	}

	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_LANDING_INFO_V2_SCREEN, screenOptions)
}

// nolint: funlen
func (mv *Provider) GetPWARedirectionDeeplink(ctx context.Context, pwaUrl string, lh *palPbFeEnums.LoanHeader) *deeplinkPb.Deeplink {
	dl := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_PWA_REDIRECTION_SCREEN, &typesPkg.PwaRedirectionScreenOptions{
		PwaUrl: pwaUrl,
		PwaHeader: &typesPkg.PwaRedirectionScreenOptions_HeaderSection{
			Title:    provider.GetText("Instant Loan", "#F6F9FD", commontypes.FontStyle_HEADLINE_M),
			Subtitle: provider.GetText("Powered by Moneyview", "#929599", commontypes.FontStyle_BODY_XS),
			BgColor:  "#28292B",
			LeftCta: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					provider.GetText("Exit", "#00B899", commontypes.FontStyle_SUBTITLE_XS),
				},
				RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/green-close-icon.png", 20, 20),
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:       "#313234",
					CornerRadius:  11,
					Height:        16,
					Width:         44,
					LeftPadding:   12,
					RightPadding:  12,
					TopPadding:    8,
					BottomPadding: 8,
				},
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
						PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
							LoanHeader: lh,
						},
					},
				},
				RightImgTxtPadding: 8,
			},
			RightCta: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					provider.GetText("Need help ?", "#00B899", commontypes.FontStyle_SUBTITLE_XS),
				},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:       "#313234",
					CornerRadius:  11,
					Height:        16,
					Width:         63,
					LeftPadding:   12,
					RightPadding:  12,
					TopPadding:    8,
					BottomPadding: 8,
				},
				Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_BOTTOM_SHEET_SCREEN, &palTypesPb.LoansBottomSheetScreenOptions{
					VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/call-icon.png", 80, 80),
					Title:         provider.GetText("Need help? Get in touch\nwith Moneyview support", "#313234", commontypes.FontStyle_HEADLINE_L),
					Infos: []*palTypesPb.LoansBottomSheetScreenOptions_Info{
						{
							DisplayValue: provider.GetText("080-69390476", "#00B899", commontypes.FontStyle_SUBTITLE_2),
							CopyValue:    "08069390476",
							IsCopyable:   true,
						},
						{
							DisplayValue: provider.GetText("<EMAIL>", "#00B899", commontypes.FontStyle_SUBTITLE_2),
							CopyValue:    "<EMAIL>",
							IsCopyable:   true,
						},
					},
				}),
			},
		},
		MandatoryDevicePermissionsInfo: &typesPkg.PwaRedirectionScreenOptions_DevicePermissionInfo{
			MandatoryPermissions:     []commontypes.DevicePermission{commontypes.DevicePermission_DEVICE_PERMISSION_CAMERA, commontypes.DevicePermission_DEVICE_PERMISSION_LOCATION, commontypes.DevicePermission_DEVICE_PERMISSION_RECORD_AUDIO},
			PermissionDialogTitle:    provider.GetText("Camera, location and microphone permissions required", "#333333", commontypes.FontStyle_BODY_3),
			PermissionDialogSubtitle: provider.GetText("This will be needed during KYC verification. To start loan application please ensure all permissions are granted to Fi app.", "#A4A4A4", commontypes.FontStyle_BODY_4_PARA),
		},
	})
	return dl
}

// nolint: funlen
func (mv *Provider) GetLoanDashboardScreenDeepLinkWithScreenOptionsV2(ctx context.Context, lh *palPbFeEnums.LoanHeader, res *palBePb.GetDashboardResponse, showOfferDetailScreenV2 bool, entryPoint string) (*deeplinkPb.Deeplink, error) {
	dl, err := mv.BaseDeeplinkProvider.GetLoanDashboardScreenDeepLinkWithScreenOptionsV2(ctx, lh, res, showOfferDetailScreenV2, entryPoint)
	dl.GetPreApprovedLoanDashboardScreenOptions().PartnershipUrl = ""
	dl.GetPreApprovedLoanDashboardScreenOptions().BottomInfoTiles = []*deeplinkPb.Tile{
		{
			Icon: "https://epifi-icons.pointz.in/preapprovedloan/landinginfo/landing-learn-more.png",
			Cta: &deeplinkPb.Cta{
				Text: "Frequently asked questions",
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_KNOW_MORE_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanKnowMoreScreenOptions{
						PreApprovedLoanKnowMoreScreenOptions: &deeplinkPb.PreApprovedLoanKnowMoreScreenOptions{
							LoanHeader: lh,
						},
					},
				},
			},
		},
	}
	dl.GetPreApprovedLoanDashboardScreenOptions().TopBanner = nil
	dl.GetPreApprovedLoanDashboardScreenOptions().GetGetAnotherLoanCta().Status = deeplinkPb.Cta_CTA_STATUS_DISABLED
	// if loan account is created will show dashboard content according to that
	if len(res.GetLoanInfoList()) > 0 {
		// for only active loan account over-writing loan details in dashboard screen options for moneyview
		// for closed account this will be nil
		lds := dl.GetPreApprovedLoanDashboardScreenOptions().GetLoanDetails()
		if len(lds) > 0 {
			for i, _ := range lds {
				dl.GetPreApprovedLoanDashboardScreenOptions().LoanDetails = append(dl.GetPreApprovedLoanDashboardScreenOptions().LoanDetails, &deeplinkPb.PreApprovedLoanDashboardScreenOptions_LoanDetails{
					Title: "Loan #" + strconv.Itoa(i),
					MoreDetails: &deeplinkPb.Cta{
						Type: deeplinkPb.Cta_CUSTOM,
						Text: "More details",
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_DETAILS_SCREEN,
							ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanDetailsScreenOptions{
								PreApprovedLoanDetailsScreenOptions: &deeplinkPb.PreApprovedLoanDetailsScreenOptions{
									LoanId:     lds[i].GetLoanId(),
									LoanHeader: lh,
								},
							},
						},
						DisplayTheme: deeplinkPb.Cta_TEXT,
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
					},
					Subtitle: provider.GetText("Powered by Moneyview", "#333333", commontypes.FontStyle_HEADLINE_3),
				},
				)

			}
			dl.GetPreApprovedLoanDashboardScreenOptions().LoanDetails = []*deeplinkPb.PreApprovedLoanDashboardScreenOptions_LoanDetails{
				{
					Title: "Loan #1",
					MoreDetails: &deeplinkPb.Cta{
						Type: deeplinkPb.Cta_CUSTOM,
						Text: "More details",
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_DETAILS_SCREEN,
							ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanDetailsScreenOptions{
								PreApprovedLoanDetailsScreenOptions: &deeplinkPb.PreApprovedLoanDetailsScreenOptions{
									LoanId:     res.GetLoanInfoList()[0].GetLoanAccount().GetId(),
									LoanHeader: lh,
								},
							},
						},
						DisplayTheme: deeplinkPb.Cta_TEXT,
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
					},
					Subtitle: provider.GetText("Powered by Moneyview", "#333333", commontypes.FontStyle_HEADLINE_3),
				},
			}

		}
	} else {
		lr := res.GetRecentLoanRequest()
		var nextStep *deeplinkPb.Cta
		if len(dl.GetPreApprovedLoanDashboardScreenOptions().GetLoanDetails()) > 0 {
			nextStep = dl.GetPreApprovedLoanDashboardScreenOptions().GetLoanDetails()[0].GetInProgressMessage().GetNextStep()
		}
		if len(res.GetLoanSteps()) > 0 && showPWACtaInLoanDashboard(res.GetLoanSteps()[0]) {
			nextStep = &deeplinkPb.Cta{
				Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_PWA_LANDING_SCREEN, &palTypesPb.LoansPWALandingScreenOptions{
					LoanHeader:     lh,
					LoanIdentifier: &palTypesPb.LoansPWALandingScreenOptions_LoanRequestId{LoanRequestId: res.GetRecentLoanRequest().GetId()},
				}),
			}
		}

		loanState := &deeplinkPb.PreApprovedLoanDashboardScreenOptions_LoanDetails_LoanState{
			Title:    baseprovider.ApplicationStatusInProgressStringGreen,
			BgColour: baseprovider.ColorMintGreen,
		}
		dashboardMessage := &deeplinkPb.PreApprovedLoanDashboardScreenOptions_LoanDetails_InProgressMessage{
			IconUrl:  "https://epifi-icons.pointz.in/preapprovedloan/ruppee-icon-in-circle.png",
			Message:  "Almost done! \nComplete the last few steps to get the loan",
			BgColour: baseprovider.ColorLimeYellow,
			NextStep: nextStep,
		}
		if lr.IsFailedTerminal() {
			loanState.Title = baseprovider.ApplicationStatusFailedString
			loanState.BgColour = baseprovider.ColorLightRed
			dashboardMessage.Message = "Your application has failed please retry after 24hr"
			dashboardMessage.BgColour = baseprovider.ColorLightRed
			dashboardMessage.NextStep = nil
		}
		dl.GetPreApprovedLoanDashboardScreenOptions().LoanDetails = []*deeplinkPb.PreApprovedLoanDashboardScreenOptions_LoanDetails{
			{
				Title:             "APPLICATION #1",
				LoanState:         loanState,
				InProgressMessage: dashboardMessage,
				Subtitle:          provider.GetText("Loan from Moneyview", "#333333", commontypes.FontStyle_HEADLINE_3),
			},
		}
	}
	return dl, err
}

func (mv *Provider) GetLoanDetailsScreenDeeplink(
	ctx context.Context,
	lh *palPbFeEnums.LoanHeader,
	beRes *palBePb.GetLoanDetailsResponse,
	_ *palBePb.GetDowntimeStatusResponse,
	prePayConfigMap *genConf.PrePayConfigMap,
) (*deeplinkPb.Deeplink, error) {
	dl := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_PWA_LANDING_SCREEN, &palTypesPb.LoansPWALandingScreenOptions{
		LoanHeader:     lh,
		LoanIdentifier: &palTypesPb.LoansPWALandingScreenOptions_LoanAccountId{LoanAccountId: beRes.GetLoanAccount().GetId()},
	})
	return dl, nil
}

func (mv *Provider) GetLoanDetailsScreenDeeplinkV2(
	ctx context.Context,
	lh *palPbFeEnums.LoanHeader,
	beRes *palBePb.GetLoanDetailsResponse,
	_ *palBePb.GetDowntimeStatusResponse,
	prePayConfigMap *genConf.PrePayConfigMap,
	req *provider.GetLoanDetailsScreenDeeplinkV2Request,
) (*deeplinkPb.Deeplink, error) {
	dl := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_PWA_LANDING_SCREEN, &palTypesPb.LoansPWALandingScreenOptions{
		LoanHeader:     lh,
		LoanIdentifier: &palTypesPb.LoansPWALandingScreenOptions_LoanAccountId{LoanAccountId: beRes.GetLoanAccount().GetId()},
	})
	return dl, nil
}

// decides whether the PWA Cta should be shown on not on loan dashboard screen based on the current loan application
// step e.g. if the vendor PWA stage hasn't reached, yet we shouldn't add the PWA cta in loan dashboard screen
func showPWACtaInLoanDashboard(currentStep *palBePb.LoanStepExecution) bool {
	allowedLoanApplicationCurrentStagesForEnablingPwaCta := []palBePb.LoanStepExecutionStepName{palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES, palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION}
	return lo.Contains(allowedLoanApplicationCurrentStagesForEnablingPwaCta, currentStep.GetStepName())
}

func (mv *Provider) GetLoanOfferOnClickDeeplink(ctx context.Context, lo *palBePb.LoanOffer) (*deeplinkPb.Deeplink, error) {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
			PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
				LoanHeader: &palPbFeEnums.LoanHeader{
					Vendor:      helper.GetPalFeVendorFromBe(lo.GetVendor()),
					LoanProgram: helper.GetFeLoanProgramFromBe(lo.GetLoanProgram()),
				},
			},
		},
	}, nil
}

func (mv *Provider) GetLoanApplicationDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, lr *palBePb.LoanRequest, lses []*palBePb.LoanStepExecution, loanOffer *palBePb.LoanOffer) (*provider.GetDashboardLoanApplicationDetailsResponse, error) {
	baseDl, baseDlErr := mv.BaseDeeplinkProvider.GetLoanApplicationDetailsForDashboard(ctx, lh, lr, lses, loanOffer)
	if baseDlErr != nil {
		return nil, fmt.Errorf("error in getting baseDl from mv, err: %w", baseDlErr)
	}
	if baseDl == nil {
		return nil, nil
	}

	message := "Almost done! \nComplete the last few steps to get the loan"
	textColour := baseprovider.ColorDarkOrange
	bgColour := baseprovider.ColorLightOrange

	var ctaNextAction *ui.IconTextComponent
	if len(baseDl.GetActiveApplicationCards().GetComponents()) > 0 {
		ctaNextAction = baseDl.GetActiveApplicationCards().GetComponents()[0].GetCardWithLineProgress().GetCtaBottomRow().GetCta()
	}
	if len(lses) > 0 && showPWACtaInLoanDashboard(lses[0]) {
		nextStep := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_PWA_LANDING_SCREEN, &palTypesPb.LoansPWALandingScreenOptions{
			LoanHeader:     lh,
			LoanIdentifier: &palTypesPb.LoansPWALandingScreenOptions_LoanRequestId{LoanRequestId: lr.GetId()},
		})
		ctaNextAction = ui.NewITC().WithTexts(provider.GetText("View", colors.ColorSnow, commontypes.FontStyle_BUTTON_XS)).
			WithDeeplink(nextStep).WithContainerBackgroundColor(textColour).
			WithContainerPadding(6, 12, 6, 12).WithContainerCornerRadius(13)
	}

	if lr.IsFailedTerminal() {
		message = "Your last loan application was cancelled"
		textColour = baseprovider.ColorDarkMaroon
		bgColour = baseprovider.ColorLightMaroon
		ctaNextAction = nil
	}

	for i := range baseDl.GetActiveApplicationCards().GetComponents() {
		baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().Rows = nil
		baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().GetCtaTitleRow().RightHamburgerComponent = nil
		baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().GetCtaBottomRow().Title = ui.NewITC().
			WithTexts(provider.GetText(message, textColour, commontypes.FontStyle_SUBTITLE_XS))
		baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().GetCtaBottomRow().Cta = ctaNextAction
		baseDl.GetActiveApplicationCards().GetComponents()[i].GetCardWithLineProgress().GetCtaBottomRow().BgColor = bgColour
	}

	for _, lse := range lses {
		if lse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES {
			// Get the fresh pwa url from vendor
			resp, err := mv.preApprovedLoanClient.GetPWARedirectionDetails(ctx, &palBePb.GetPWARedirectionDetailsRequest{
				LoanHeader:     helper.GetBeLoanHeaderByFeLoanHeader(lh),
				LoanIdentifier: &palBePb.GetPWARedirectionDetailsRequest_LoanRequestId{LoanRequestId: lr.GetId()},
			})
			if err = epifigrpc.RPCError(resp, err); err != nil {
				// if api fails, then don't show banner for best effort basis
				logger.Error(ctx, "error while getting pwa url", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, lr.GetId()))
				break
			}

			if resp.GetPwaUrl() != "" {
				pwaCopyBottomSheetComponent, err := provider.GetPwaCopyUrlBottomSheetComponentForDashboard(resp.GetPwaUrl(), lh)
				if err != nil {
					return nil, errors.Wrap(err, "error getting pwa copy url bottom sheet component for dashboard")
				}
				baseDl.GetActiveApplicationCards().Components = append(baseDl.GetActiveApplicationCards().GetComponents(), pwaCopyBottomSheetComponent)
			}
		}
	}

	return baseDl, nil
}

func (mv *Provider) GetLoanAccountDetailsForDashboard(ctx context.Context, lh *palPbFeEnums.LoanHeader, li *palBePb.LoanInfo) (*provider.GetDashboardLoanAccountDetailsResponse, error) {
	baseDl, baseDlErr := mv.BaseDeeplinkProvider.GetLoanAccountDetailsForDashboard(ctx, lh, li)
	if baseDlErr != nil {
		return nil, fmt.Errorf("error in getting baseDl from mv, err: %w", baseDlErr)
	}
	if baseDl == nil {
		return nil, nil
	}

	dl := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_PWA_LANDING_SCREEN, &palTypesPb.LoansPWALandingScreenOptions{
		LoanHeader:     lh,
		LoanIdentifier: &palTypesPb.LoansPWALandingScreenOptions_LoanAccountId{LoanAccountId: li.GetLoanAccount().GetId()},
	})

	baseDl.GetActiveLoanCard().GetCardWithLineProgress().Rows = nil
	baseDl.GetActiveLoanCard().GetCardWithLineProgress().CtaBottomRow = nil
	baseDl.GetActiveLoanCard().GetCardWithLineProgress().BannerWithStageProgress = nil
	baseDl.GetActiveLoanCard().GetCardWithLineProgress().GetCtaTitleRow().GetTitle().Deeplink = dl
	baseDl.GetActiveLoanCard().GetCardWithLineProgress().GetCtaTitleRow().GetCta().Deeplink = dl

	return baseDl, nil
}

func (mv *Provider) GetSoftOfferCardMultiOfferScreenComponent(ctx context.Context, lo *palBePb.LoanOffer, isFirstOffer bool, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoanOfferDetailsCard, error) {
	baseLodCard, err := mv.BaseDeeplinkProvider.GetSoftOfferCardMultiOfferScreenComponent(ctx, lo, isFirstOffer, isLoansPreQualOfferFlowEnabled)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting baseLodCard for mv provider")
	}

	if !isLoansPreQualOfferFlowEnabled {
		return baseLodCard, nil
	}

	lh := &palPbFeEnums.LoanHeader{
		LoanProgram: helper.GetFeLoanProgramFromBe(lo.GetLoanProgram()),
		Vendor:      helper.GetPalFeVendorFromBe(lo.GetVendor()),
	}

	dataPullConsentCheckBox := &widgetUiPb.CheckboxItem{
		Id:          consentPb.ConsentType_DATA_PULL_BY_MONEYVIEW_LENDER.String(),
		DisplayText: commontypes.GetHtmlText("<font color=\"#6A6D70\">I accept <a href=\"https://moneyview.in/terms-conditions-loans-partners\" style=\"color: #00B899;\">Moneyview's T&Cs</a> and authorise Whizdm Finance and Moneyview's lending partners to retrieve my credit bureau information for processing the loan</font>").WithFontStyle(commontypes.FontStyle_NUMBER_2XS),
		IsChecked:   true,
	}

	// Moneyview data pull consent is needed from the user before starting the loan application journey, so checking if consent was already taken from the user,
	// if consent was not already taken then show a checkbox to take consent from the user now.
	var consentsToShown []*widgetUiPb.CheckboxItem
	_, err = mv.rpcHelper.FetchActiveConsentForActor(ctx, lo.GetActorId(), consentPb.ConsentType_DATA_PULL_BY_MONEYVIEW_LENDER)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		consentsToShown = []*widgetUiPb.CheckboxItem{dataPullConsentCheckBox}
	case err != nil:
		// intentionally muting the error, since consent flows are idempotent, we can again show a consent checkbox to the user in case we see a failure while checking if the consent already exists.
		logger.WarnWithCtx(ctx, "error checking if user has already consented for moneyview data pull", zap.String(logger.ACTOR_ID_V2, lo.GetActorId()))
		consentsToShown = []*widgetUiPb.CheckboxItem{dataPullConsentCheckBox}
	}

	if baseLodCard.GetLoansCta() != nil {
		if len(consentsToShown) > 0 {
			dl, err := getPreBreLoanConsentScreen(lh, lo.GetId(), consentsToShown, baseLodCard.GetLoansCta())
			if err != nil {
				return nil, errors.Wrap(err, "error getting pre-bre loan consent screen")
			}
			baseLodCard.GetLoansCta().CtaAction = &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_Deeplink{Deeplink: dl}}
		} else {
			baseLodCard.GetLoansCta().CtaAction = getApplyForLoanLoanCtaAction(lh, lo.GetId())
		}
	}

	return baseLodCard, nil
}

func getPreBreLoanConsentScreen(lh *palPbFeEnums.LoanHeader, loId string, consents []*widgetUiPb.CheckboxItem, existingLoansCta *palTypesPb.LoansCta) (*deeplinkPb.Deeplink, error) {
	lh.DataOwner = palPbFeEnums.Vendor_MONEYVIEW
	ctaContent := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         "Continue",
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
		Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
	}
	if existingLoansCta != nil {
		existingLoansCta.CtaContent = ctaContent
		existingLoansCta.CtaAction = getApplyForLoanLoanCtaAction(lh, loId)
	}
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CONSENT_V2_SCREEN, &palTypesPb.LoansConsentV2ScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{FeedbackEngineInfo: &feHeaderPb.FeedbackEngineInfo{
			FlowIdDetails: &feHeaderPb.FlowIdentifierDetails{
				FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_CONSENT_V2_SCREEN.String(),
			},
		}},
		LoanHeader:     lh,
		Flow:           palPbFeEnums.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY,
		TopIcon:        commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/landing_moneyview_logo.png", 30, 90),
		Title:          fePalDlHelper.GetText("Confirm the following to apply for a loan", "#333333", "", commontypes.FontStyle_HEADLINE_L),
		Subtitle:       fePalDlHelper.GetText("Mandatory Step for Loan application evaluation", "#8D8D8D", "", commontypes.FontStyle_BODY_3),
		ConsentItems:   consents,
		Cta:            ctaContent,
		BgColor:        "#FFFFFF",
		ConsentBgColor: colors.ColorOnDarkHighEmphasis,
		LoansCta:       existingLoansCta,
	})
}

func getApplyForLoanLoanCtaAction(lh *palPbFeEnums.LoanHeader, loId string) *palTypesPb.LoansCtaAction {
	return &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
		RpcName: palTypesPb.LoansCtaAction_RPC_NAME_APPLY_FOR_LOAN,
		CommonMetaData: &palTypesPb.LoansCtaAction_CallRpc_CommonMetaData{
			LoanHeader: lh,
			LoId:       loId,
		},
	}}}
}
