package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
)

type MultiOfferCardInfo struct {
	CardType    string
	Vendor      string
	LoanProgram string
	Index       int
}

type MultiOfferScreen struct {
	ActorId    string
	ProspectId string
	SessionId  string
	EventId    string
	Timestamp  time.Time
	EventType  string
	CardInfos  []*MultiOfferCardInfo
}

func NewMultiOfferScreen(actorId string, options []*palPb.LoanOption) *MultiOfferScreen {
	var cardInfos []*MultiOfferCardInfo
	for idx, option := range options {
		if option.GetEligibilityHeader() != nil {
			cardInfos = append(cardInfos, &MultiOfferCardInfo{
				CardType:    "ELIGIBILITY",
				Vendor:      option.GetLoanHeader().GetVendor().String(),
				LoanProgram: option.GetLoanHeader().GetLoanProgram().String(),
				Index:       idx,
			})
			continue
		}
		cardInfos = append(cardInfos, &MultiOfferCardInfo{
			CardType:    option.GetLoanOffer().GetLoanOfferType().String(),
			Vendor:      option.GetLoanHeader().GetVendor().String(),
			LoanProgram: option.GetLoanHeader().GetLoanProgram().String(),
			Index:       idx,
		})
	}
	return &MultiOfferScreen{
		ActorId:   actorId,
		EventId:   uuid.New().String(),
		Timestamp: time.Now(),
		EventType: events.EventTrack,
		CardInfos: cardInfos,
	}
}

func (c *MultiOfferScreen) GetEventType() string {
	return c.EventType
}

func (c *MultiOfferScreen) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(c, properties)
	return properties
}

func (c *MultiOfferScreen) GetEventTraits() map[string]interface{} {
	return nil
}

func (c *MultiOfferScreen) GetEventId() string {
	return c.EventId
}

func (c *MultiOfferScreen) GetUserId() string {
	return c.ActorId
}

func (c *MultiOfferScreen) GetProspectId() string {
	return c.ProspectId
}

func (c *MultiOfferScreen) GetEventName() string {
	return EventMultiOfferScreen
}
