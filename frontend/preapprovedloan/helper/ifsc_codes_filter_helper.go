package helper

import (
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	searchPb "github.com/epifi/gamma/api/search"
)

// TODO(@Shivansh) Add the correct list of IFSCs here
var filterMap = map[pal_enums.SearchIfscType][]string{
	pal_enums.SearchIfscType_SEARCH_IFSC_TYPE_SUBVENTION_MIN_AND_RE_KYC_USERS: []string{"FDRL0005555", "FDRL0005556", "FDRL0007777"},
	pal_enums.SearchIfscType_SEARCH_IFSC_TYPE_FI_ACCOUNT_NOT_OPERATIONAL:      []string{"FDRL0005555", "FDRL0005556"},
}

// Federal neo-banking IFSCs are blocked from the bank account addition screen assuming that we would not surface this screen for such users.
func FilterIfscSuggestions(searchType pal_enums.SearchIfscType, res *searchPb.AutocompleteResponse) []*palFePb.GetIfscCodeSuggestionsResponse_Suggestion {
	var feSuggestions []*palFePb.GetIfscCodeSuggestionsResponse_Suggestion
	if len(res.GetResult().GetSuggestions()) == 0 {
		return feSuggestions
	}

	for _, suggestion := range res.GetResult().GetSuggestions() {
		feSuggestions = append(feSuggestions, &palFePb.GetIfscCodeSuggestionsResponse_Suggestion{
			Ifsc:         suggestion.GetIfsc(),
			BankName:     suggestion.GetBankName(),
			BankBranch:   suggestion.GetBankBranch(),
			BankDistrict: suggestion.GetBankDistrict(),
		})
	}

	// In case of lenden we are surfacing this screen even for Federal accounts. Removing this filter will allow user to add federal account details and setup mandate.
	//
	// blacklistedIfsc, ok := filterMap[searchType]
	// // if search type is not present, return the suggestions as such
	// if !ok {
	// 	return feSuggestions
	// }
	//
	// // apply the filter to edit the blacklisted IFSCs, and add the error msg that we need to show in those suggestions
	// for _, suggestion := range feSuggestions {
	// 	if lo.Contains(blacklistedIfsc, suggestion.GetIfsc()) {
	// 		suggestion.ReasonForUnavailability = commontypes.GetTextFromStringFontColourFontStyle("This bank account is not supported", "#A93D5B", commontypes.FontStyle_BODY_XS)
	// 	}
	// }
	return feSuggestions
}
