// nolint:dupl,unused,unparam
package offerwidget

import (
	"context"
	"math/rand"

	feHomePb "github.com/epifi/gamma/api/frontend/home"
	beNetWorthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/feature/release"

	"github.com/epifi/be-common/pkg/hash"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

type MyRewardsOffersWidgetGenerator struct {
	mappingsManager      *MappingsManager
	releaseEvaluator     release.IEvaluator
	onboardingClient     onboardingPb.OnboardingClient
	questSdkClient       *questSdk.Client
	userAttributeFetcher pkgUser.UserAttributesFetcher
	networthClient       beNetWorthPb.NetWorthClient
}

func NewMyRewardsOffersWidgetGenerator(
	mappingsManager *MappingsManager,
	releaseEvaluator release.IEvaluator,
	onboardingClient onboardingPb.OnboardingClient,
	questSdkClient *questSdk.Client,
	userAttributeFetcher pkgUser.UserAttributesFetcher,
	networthClient beNetWorthPb.NetWorthClient,
) *MyRewardsOffersWidgetGenerator {
	return &MyRewardsOffersWidgetGenerator{
		mappingsManager:      mappingsManager,
		releaseEvaluator:     releaseEvaluator,
		onboardingClient:     onboardingClient,
		questSdkClient:       questSdkClient,
		userAttributeFetcher: userAttributeFetcher,
		networthClient:       networthClient,
	}
}

func (h *MyRewardsOffersWidgetGenerator) GetOfferTypes(ctx context.Context) []OfferType {
	return []OfferType{CatalogOffers}
}

func (h *MyRewardsOffersWidgetGenerator) GetTitle(ctx context.Context) *commontypes.Text {
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: "Redeem your Fi-Coins",
		},
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
		},
		FontColor: "#313234",
	}
}

// todo(sresth): move these params to a struct
func (h *MyRewardsOffersWidgetGenerator) GetTabsData(ctx context.Context, actorId string, fiCoinsBalance uint32, rewardsCount uint32, offersParam *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, sessionId string, originScreen deepLinkPb.Screen) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	isFeatureHomeDesignEnhancementsEnabled := featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            h.releaseEvaluator,
			OnboardingClient:     h.onboardingClient,
			QuestSdkClient:       h.questSdkClient,
			UserAttributeFetcher: h.userAttributeFetcher,
			NetWorthClient:       h.networthClient,
		},
	})
	if h.mappingsManager.isV2OfferWidgetEnabled(ctx) {
		// using v2 feature config for v3 design, since an experiment is already live on v2 config, and we want to enable v3 design for the same experiment
		return h.getTabsDataV3(ctx, fiCoinsBalance, rewardsCount, offersParam, originScreen, appPlatform, isFeatureHomeDesignEnhancementsEnabled)
	}
	return h.getTabsData(ctx, fiCoinsBalance, rewardsCount, offersParam.FeCatalogOffers, isFeatureHomeDesignEnhancementsEnabled)
}

func (h *MyRewardsOffersWidgetGenerator) GetBottomCta(_ commontypes.Platform, _ uint32) *ui.IconTextComponent {
	return nil
}

func (h *MyRewardsOffersWidgetGenerator) getTabsData(ctx context.Context, fiCoinsBalance uint32, rewardsCount uint32, catalogOffers []*fePb.CatalogOfferV1, isFeatureHomeDesignEnhancementsEnabled bool) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	var offerCards []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card

	// convert to catalog offers for widget and interleave fi coin and exchanger offers according to config
	homeFiCoinOffers, homeExchangerOffers := h.mappingsManager.GetFiCoinOfferAndExchangerOfferCards(ctx, catalogOffers)
	var fiCoinOfferIter, exchangerOfferIter int
	orderedOfferTypes := []string{offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr, offerTypeNonCbr, offerTypeCbr}
	for _, offerType := range orderedOfferTypes {
		switch offerType {
		case offerTypeNonCbr:
			if fiCoinOfferIter < len(homeFiCoinOffers) {
				offerCards = append(offerCards, homeFiCoinOffers[fiCoinOfferIter])
				fiCoinOfferIter++
			}
		case offerTypeCbr:
			if exchangerOfferIter < len(homeExchangerOffers) {
				offerCards = append(offerCards, homeExchangerOffers[exchangerOfferIter])
				exchangerOfferIter++
			}
		}
	}

	// construct tab data for catalog offers
	catalogOffersTabData := make([]*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card, 0)
	catalogOffersTabData = append(catalogOffersTabData, offerCards...)
	catalogOffersTabData = append(catalogOffersTabData, h.mappingsManager.GetViewAllOffersCard(viewAllCatalogOffersImageUrl, &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
	}))

	tabs := []*typesUi.Tab{{Id: catalogOffersTabId}}
	tabDataMap := map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
		catalogOffersTabId: {
			TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN}, isFeatureHomeDesignEnhancementsEnabled),
			Cards:  catalogOffersTabData,
		},
	}

	filter := &typesUi.Filter{
		Tabs:              tabs,
		FilterType:        getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled),
		DefaultTabSection: catalogOffersTabId,
	}

	return filter, tabDataMap, false
}

func (h *MyRewardsOffersWidgetGenerator) getTabsDataV2(ctx context.Context, fiCoinsBalance uint32, rewardsCount uint32, offersParam *GetTabsDataOffersParam, sessionId string, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	// generate tabs
	var (
		tabsFilterType = getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled)
		tabs           = []*typesUi.Tab{
			{
				Id:          catalogShopOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(catalogShopOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(catalogShopOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
			{
				Id:          catalogConvertOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(catalogConverOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(catalogConverOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
		}

		tabDataMap = map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
			catalogShopOffersTabId: {
				TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN}, isFeatureHomeDesignEnhancementsEnabled),
				Cards:  h.mappingsManager.getShopTabCards(ctx, fiCoinsBalance, rewardsCount, offersParam, originScreen, isFeatureHomeDesignEnhancementsEnabled),
			},
			catalogConvertOffersTabId: {
				TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN}, isFeatureHomeDesignEnhancementsEnabled),
				Cards:  h.mappingsManager.getConvertTabCards(ctx, fiCoinsBalance, rewardsCount, offersParam.FeCatalogOffers, originScreen, isFeatureHomeDesignEnhancementsEnabled),
			},
		}
	)

	filter := &typesUi.Filter{
		Tabs:              tabs,
		FilterType:        tabsFilterType,
		DefaultTabSection: h.getDefaultTabId(sessionId),
	}

	return filter, tabDataMap, true
}

// getV2WidgetTabsDataWithDisabledTabsVisibility fetch v2 widget tabs data with tabs visibility disabled and putting all the offers in a single tab
func (h *MyRewardsOffersWidgetGenerator) getV2WidgetTabsDataWithDisabledTabsVisibility(ctx context.Context, fiCoinsBalance uint32, rewardsCount uint32, offersParam *GetTabsDataOffersParam, originScreen deepLinkPb.Screen, isFeatureHomeDesignEnhancementsEnabled bool) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	var (
		tabsFilterType = getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled)

		// generate tabs
		tabs = []*typesUi.Tab{
			{
				Id:          catalogOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(catalogOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(catalogOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
		}

		// generate tab data map
		tabDataMap = map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
			catalogOffersTabId: {
				TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN}, isFeatureHomeDesignEnhancementsEnabled),
				Cards:  h.mappingsManager.getOffersV2WidgetCards(ctx, fiCoinsBalance, rewardsCount, offersParam, originScreen, false, isFeatureHomeDesignEnhancementsEnabled),
			},
		}

		// construct filter
		filter = &typesUi.Filter{
			Tabs:              tabs,
			FilterType:        tabsFilterType,
			DefaultTabSection: catalogOffersTabId,
		}
	)

	return filter, tabDataMap, false
}

func (h *MyRewardsOffersWidgetGenerator) getTabsDataV3(ctx context.Context, fiCoinsBalance uint32, rewardsCount uint32, offersParam *GetTabsDataOffersParam, originScreen deepLinkPb.Screen, appPlatform commontypes.Platform, isFeatureHomeDesignEnhancementsEnabled bool) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	var (
		tabsFilterType = getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled)
		// generate tabs
		tabs = []*typesUi.Tab{
			{
				Id:          catalogOffersTabId,
				ActiveCta:   h.mappingsManager.GetActiveStateTabByFilterType(catalogOffersTabText, tabsFilterType),
				InactiveCta: h.mappingsManager.GetInactiveStateTabByFilterType(catalogOffersTabText, tabsFilterType),
				BorderColor: feHomePb.GetHomeWidgetBorderColor(),
			},
		}

		// generate tab data map
		tabDataMap = map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
			catalogOffersTabId: {
				TopCta: h.mappingsManager.GetSeeAllCta("SEE ALL", &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN}, isFeatureHomeDesignEnhancementsEnabled),
				Cards:  h.mappingsManager.getOffersV3WidgetCards(ctx, fiCoinsBalance, rewardsCount, offersParam, originScreen, appPlatform, false, isFeatureHomeDesignEnhancementsEnabled),
			},
		}

		// construct filter
		filter = &typesUi.Filter{
			Tabs:              tabs,
			FilterType:        tabsFilterType,
			DefaultTabSection: catalogOffersTabId,
		}
	)

	return filter, tabDataMap, false
}

// nolint:gosec
func (h *MyRewardsOffersWidgetGenerator) getDefaultTabId(sessionId string) string {
	rnd := rand.New(rand.NewSource(int64(hash.Hash(sessionId, 0))))

	// randomise default tab
	defaultTab := catalogConvertOffersTabId
	if rnd.Int()%2 == 0 {
		defaultTab = catalogShopOffersTabId
	}
	return defaultTab
}
