package offerwidget

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	beNetWorthPb "github.com/epifi/gamma/api/insights/networth"

	"context"

	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

type RewardDetailsOfferWidgetGenerator struct {
	mappingsManager      *MappingsManager
	releaseEvaluator     release.IEvaluator
	onboardingClient     onboardingPb.OnboardingClient
	questSdkClient       *questSdk.Client
	userAttributeFetcher pkgUser.UserAttributesFetcher
	networthClient       beNetWorthPb.NetWorthClient
}

func NewRewardDetailsOfferWidgetGenerator(
	mappingsManager *MappingsManager,
	releaseEvaluator release.IEvaluator,
	onboardingClient onboardingPb.OnboardingClient,
	questSdkClient *questSdk.Client,
	userAttributeFetcher pkgUser.UserAttributesFetcher,
	networthClient beNetWorthPb.NetWorthClient,
) *RewardDetailsOfferWidgetGenerator {
	return &RewardDetailsOfferWidgetGenerator{
		mappingsManager:      mappingsManager,
		releaseEvaluator:     releaseEvaluator,
		onboardingClient:     onboardingClient,
		questSdkClient:       questSdkClient,
		userAttributeFetcher: userAttributeFetcher,
		networthClient:       networthClient,
	}
}

func (r *RewardDetailsOfferWidgetGenerator) GetOfferTypes(ctx context.Context) []OfferType {
	return []OfferType{CatalogOffers}
}

func (r *RewardDetailsOfferWidgetGenerator) GetTitle(ctx context.Context) *commontypes.Text {
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: "Spend Fi-Coins here",
		},
		FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		FontColor: "#B9B9B9",
	}
}

func (r *RewardDetailsOfferWidgetGenerator) GetTabsData(ctx context.Context, actorId string, fiCoinsBalance uint32, rewardsCount uint32, offers *GetTabsDataOffersParam, creditCardTypeId string, appPlatform commontypes.Platform, appVersionCode uint32, sessionId string, originScreen deepLinkPb.Screen) (*typesUi.Filter, map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData, bool) {
	// generate tab data map
	var (
		tabs                                   = []*typesUi.Tab{{Id: catalogOffersTabId}}
		homeCatalogOffers                      []*fePb.GetRewardsAndOffersWidgetResponse_TabData_Card
		isFeatureHomeDesignEnhancementsEnabled = featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            r.releaseEvaluator,
				OnboardingClient:     r.onboardingClient,
				QuestSdkClient:       r.questSdkClient,
				UserAttributeFetcher: r.userAttributeFetcher,
				NetWorthClient:       r.networthClient,
			},
		})
	)

	// convert to catalog offers for widget and interleave fi coin and exchanger offers according to config
	homeFiCoinOffers, homeExchangerOffers := r.mappingsManager.GetFiCoinOfferAndExchangerOfferCards(ctx, offers.FeCatalogOffers)
	var fiCoinOfferIter, exchangerOfferIter int
	orderedOfferTypes := []string{offerTypeNonCbr, offerTypeCbr}
	for i := 0; i < len(orderedOfferTypes); i++ {
		offerType := orderedOfferTypes[i]
		switch offerType {
		case offerTypeNonCbr:
			if fiCoinOfferIter < len(homeFiCoinOffers) {
				homeCatalogOffers = append(homeCatalogOffers, homeFiCoinOffers[fiCoinOfferIter])
				fiCoinOfferIter++
			}
		case offerTypeCbr:
			if exchangerOfferIter < len(homeExchangerOffers) {
				homeCatalogOffers = append(homeCatalogOffers, homeExchangerOffers[exchangerOfferIter])
				exchangerOfferIter++
			}
		}
	}

	// construct tab data for catalog offers
	homeCatalogOffers = append(homeCatalogOffers, r.mappingsManager.GetViewAllOffersCard(viewAllCatalogOffersImageUrl, &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
	}))

	tabDataMap := map[string]*fePb.GetRewardsAndOffersWidgetResponse_TabData{
		catalogOffersTabId: {Cards: homeCatalogOffers},
	}

	filter := &typesUi.Filter{
		Tabs:              tabs,
		FilterType:        getTabsFilterType(isFeatureHomeDesignEnhancementsEnabled),
		DefaultTabSection: catalogOffersTabId,
	}

	return filter, tabDataMap, false
}

func (r *RewardDetailsOfferWidgetGenerator) GetBottomCta(_ commontypes.Platform, _ uint32) *ui.IconTextComponent {
	return nil
}
