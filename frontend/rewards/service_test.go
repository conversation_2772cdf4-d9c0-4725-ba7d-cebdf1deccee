package rewards

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"flag"
	"fmt"
	"os"
	"reflect"
	"testing"
	"time"

	pkgMoney "github.com/epifi/be-common/pkg/money"

	beCardPb "github.com/epifi/gamma/api/card"
	cardPb "github.com/epifi/gamma/api/card"
	"github.com/epifi/gamma/api/card/provisioning"
	provisioningMocks "github.com/epifi/gamma/api/card/provisioning/mocks"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	userGroupMocks "github.com/epifi/gamma/api/user/group/mocks"

	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/epificontext"

	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/gamma/api/accrual"
	accuralMocks "github.com/epifi/gamma/api/accrual/mocks"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	beCasperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/casper/exchanger"
	beExchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	exchangerMocks "github.com/epifi/gamma/api/casper/exchanger/mocks"
	evrMocks "github.com/epifi/gamma/api/casper/external_vendor_redemption/mocks"
	casperMocks "github.com/epifi/gamma/api/casper/mocks"
	beRedemptionPb "github.com/epifi/gamma/api/casper/redemption"
	casperRedemptionMocks "github.com/epifi/gamma/api/casper/redemption/mocks"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	errors2 "github.com/epifi/gamma/api/frontend/errors"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/mocks"
	beRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	mockRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers/mocks"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	mocks2 "github.com/epifi/gamma/api/tiering/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	beUserPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	vendorMappingMocks "github.com/epifi/gamma/api/vendormapping/mocks"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/rewards/tags"
	"github.com/epifi/gamma/frontend/test"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
)

var conf *config.Config
var dyconf *genconf.Config

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, dyconf, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestRewardService_GetActiveRewardOffers(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	beMockRewardOffersClient := mockRewardOffersPb.NewMockRewardOffersClient(ctr)

	type fields struct {
		rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient
		rewardOffersClient     beRewardOffersPb.RewardOffersClient
		accrualClient          accrual.AccrualClient
		offerListingClient     beCasperPb.OfferListingServiceClient
	}
	type args struct {
		ctx  context.Context
		req  *fePb.GetActiveRewardOffersRequest
		mock interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *fePb.GetActiveRewardOffersResponse
		wantErr bool
	}{
		{
			name: "Backend get offer call failed: Validation Failed",
			fields: fields{
				rewardOffersClient: beMockRewardOffersClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetActiveRewardOffersRequest{},
				mock: beMockRewardOffersClient.EXPECT().GetRewardOffersForActor(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("validation error")),
			},
			want: &fePb.GetActiveRewardOffersResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching displayable reward offers for actor"),
			},
			wantErr: false,
		},
		{
			name: "Backend get offer call failed: Failed Without Error",
			fields: fields{
				rewardOffersClient: beMockRewardOffersClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetActiveRewardOffersRequest{},
				mock: beMockRewardOffersClient.EXPECT().GetRewardOffersForActor(gomock.Any(), gomock.Any()).
					Return(&beRewardOffersPb.GetRewardOffersForActorResponse{Status: rpc.StatusInternal()}, nil),
			},
			want: &fePb.GetActiveRewardOffersResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching displayable reward offers for actor"),
			},
			wantErr: false,
		},
		{
			name: "Get offer call returned empty reward offer list",
			fields: fields{
				rewardOffersClient: beMockRewardOffersClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetActiveRewardOffersRequest{},
				mock: beMockRewardOffersClient.EXPECT().GetRewardOffersForActor(gomock.Any(), gomock.Any()).
					Return(&beRewardOffersPb.GetRewardOffersForActorResponse{Status: rpc.StatusOk()}, nil),
			},
			want: &fePb.GetActiveRewardOffersResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "Get offer call returned reward offer list",
			fields: fields{
				rewardOffersClient: beMockRewardOffersClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetActiveRewardOffersRequest{},
				mock: beMockRewardOffersClient.EXPECT().GetRewardOffersForActor(gomock.Any(), gomock.Any()).
					Return(&beRewardOffersPb.GetRewardOffersForActorResponse{
						Status: rpc.StatusOk(),
						RewardOffers: []*beRewardOffersPb.RewardOffer{
							{
								Id: "ro-1",
								DisplayMeta: &beRewardOffersPb.DisplayMeta{
									DisplayRank:  8,
									DisplaySince: "2020-05-10T10:04:05+05:30",
									DisplayTill:  "2025-05-10T10:04:05+05:30",
								},
							},
							{
								Id: "ro-2",
								DisplayMeta: &beRewardOffersPb.DisplayMeta{
									DisplayRank:  2,
									DisplaySince: "2020-05-10T10:04:05+05:30",
									DisplayTill:  "2025-05-10T10:04:05+05:30",
								},
							},
							{
								Id: "ro-3",
								DisplayMeta: &beRewardOffersPb.DisplayMeta{
									DisplayRank:  4,
									DisplaySince: "2020-05-10T10:04:05+05:30",
									DisplayTill:  "2025-05-10T10:04:05+05:30",
								},
							},
							{
								Id: "ro-4",
								DisplayMeta: &beRewardOffersPb.DisplayMeta{
									DisplayRank:  1,
									DisplaySince: "2020-05-10T10:04:05+05:30",
									DisplayTill:  "2025-05-10T10:04:05+05:30",
								},
							},
							{
								Id: "ro-5",
								DisplayMeta: &beRewardOffersPb.DisplayMeta{
									DisplayRank:  6,
									DisplaySince: "2020-05-10T10:04:05+05:30",
									DisplayTill:  "2025-05-10T10:04:05+05:30",
								},
							},
							{
								Id: "ro-6",
							},
						},
						ActionLevelRewardOfferInventory: []*beRewardOffersPb.RewardOfferInventory{
							{
								RewardOfferId:  "ro-1",
								RemainingCount: 1,
								TotalCount:     10,
							},
							{
								RewardOfferId: "ro-2",
								TotalCount:    0, // infinte inventory
							},
							{
								RewardOfferId:  "ro-3",
								RemainingCount: 0, // inventory exhausted
								TotalCount:     10,
							},
							{
								RewardOfferId:  "ro-4",
								RemainingCount: 4,
								TotalCount:     10,
							},
							{
								RewardOfferId:  "ro-5",
								RemainingCount: 5,
								TotalCount:     10,
							},
						},
					}, nil),
			},
			want: &fePb.GetActiveRewardOffersResponse{
				Status: rpc.StatusOk(),
				RewardOffers: []*fePb.RewardOffer{
					// expected order is according to current inventory and display rank based ordering logic.
					{
						Id: "ro-6",
					},
					{
						Id:             "ro-4",
						DisplayDetails: &fePb.DisplayDetails{},
					},
					{
						Id:             "ro-2",
						DisplayDetails: &fePb.DisplayDetails{},
					},
					{
						Id:             "ro-5",
						DisplayDetails: &fePb.DisplayDetails{},
					},
					{
						Id:             "ro-1",
						DisplayDetails: &fePb.DisplayDetails{},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := NewRewardsService(
				conf.RewardsFrontendMeta,
				tt.fields.rewardsGeneratorClient,
				tt.fields.rewardOffersClient,
				tt.fields.accrualClient,
				nil,
				tt.fields.offerListingClient,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				dyconf,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil,
				nil, nil, nil, nil, nil,
			)
			got, err := r.GetActiveRewardOffers(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActiveRewardOffers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetActiveRewardOffers() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRewardService_GetRewardsForActorV1(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	beMockRewardGeneratorClient := mocks.NewMockRewardsGeneratorClient(ctr)
	beMockExchangerOfferClient := exchangerMocks.NewMockExchangerOfferServiceClient(ctr)
	beMockTieringClient := mocks2.NewMockTieringClient(ctr)
	beMockUserGrpClient := userGroupMocks.NewMockGroupClient(ctr)

	feRequestHeader := &feHeaderPb.RequestHeader{
		// setting the platform field for the app specific checks to work.
		// todo: set the platform to iOS and trigger tests as well
		Auth: &feHeaderPb.AuthHeader{ActorId: "actor-1", Device: &commontypes.Device{Platform: commontypes.Platform_ANDROID}},
	}

	ctx1 := context.Background()
	ctx1 = epificontext.CtxWithActorId(ctx1, "actor-1")
	ctx1 = context.WithValue(ctx1, epificontext.CtxUserEmailKey, "<EMAIL>")

	type fields struct {
		rewardsFrontendMeta    *config.RewardsFrontendMeta
		rewardsGeneratorClient beRewardsPb.RewardsGeneratorClient
		exchangerOfferClient   exchanger.ExchangerOfferServiceClient
		tieringClient          beTieringPb.TieringClient
		beUserGrpClient        userGroupPb.GroupClient
	}
	type args struct {
		ctx context.Context
		req *fePb.GetRewardsForActorV1Request
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		setupMocks func()
		want       *fePb.GetRewardsForActorV1Response
		wantErr    bool
	}{
		{
			name: "empty page token is passed in the request, should be considered as request to fetch the first page",
			fields: fields{
				rewardsFrontendMeta:    &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				rewardsGeneratorClient: beMockRewardGeneratorClient,
				exchangerOfferClient:   beMockExchangerOfferClient,
				tieringClient:          beMockTieringClient,
				beUserGrpClient:        beMockUserGrpClient,
			},
			args: args{
				ctx: ctx1,
				req: &fePb.GetRewardsForActorV1Request{
					Req: feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockTieringClient.EXPECT().GetTieringPitchV2(ctx1, gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.StatusOk(),
				}, nil)

				beMockUserGrpClient.EXPECT().GetGroupsMappedToEmail(ctx1, gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil)
				beMockRewardGeneratorClient.EXPECT().GetRewardsByActorId(ctx1, &beRewardsPb.RewardsByActorIdRequest{
					ActorId:     "actor-1",
					PageContext: &rpc.PageContextRequest{PageSize: 10},
				}).Return(&beRewardsPb.RewardsResponse{
					Status:      rpc.StatusOk(),
					PageContext: &rpc.PageContextResponse{AfterToken: "after-token-1", HasAfter: true},
					Rewards: []*beRewardsPb.Reward{
						{Id: "reward-1", Status: beRewardsPb.RewardStatus_PROCESSING_FAILED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 10, 0, 0, datetime.IST))},
						{Id: "reward-2", Status: beRewardsPb.RewardStatus_PROCESSING_FAILED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 5, 0, 0, datetime.IST))},
					},
				}, nil)

				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(ctx1, &beExchangerPb.GetExchangerOfferOrdersForActorRequest{
					ActorId: "actor-1",
					Filters: &beExchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{
						States: []beExchangerPb.ExchangerOfferOrderState{beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTION_CHOSEN, beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
							beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED},
						RewardTypes: []beExchangerPb.RewardType{beExchangerPb.RewardType_REWARD_TYPE_CASH, beExchangerPb.RewardType_REWARD_TYPE_CREDIT_CARD_BILL_ERASER},
					},
					PageContext: &rpc.PageContextRequest{PageSize: 10},
				}).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:      rpc.StatusOk(),
					PageContext: &rpc.PageContextResponse{AfterToken: "after-token-2", HasAfter: true},
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{Id: "eo-1", State: beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 9, 0, 0, datetime.IST))},
						{Id: "eo-2", State: beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 1, 0, 0, datetime.IST))},
					},
				}, nil)
			},
			want: &fePb.GetRewardsForActorV1Response{
				Status: rpc.StatusOk(),
				// reward entries should be sorted by createdAt time
				Rewards: []*fePb.RewardWrapperV1{
					{Data: &fePb.RewardWrapperV1_Reward{Reward: &fePb.Reward{Id: "reward-1", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 10, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "eo-1", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 9, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_Reward{Reward: &fePb.Reward{Id: "reward-2", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 5, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "eo-2", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 1, 0, 0, datetime.IST))}}},
				},
				PageContext: &fePb.PageContextResponse{
					AfterToken: "after-token-1#after-token-2",
					HasAfter:   true,
				},
			},
		},
		{
			name: "non empty page token passed in request, rpc calls to rewards and exchanger-orders service should be made to fetch data using the passed page tokens",
			fields: fields{
				rewardsFrontendMeta:    &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				rewardsGeneratorClient: beMockRewardGeneratorClient,
				exchangerOfferClient:   beMockExchangerOfferClient,
				tieringClient:          beMockTieringClient,
				beUserGrpClient:        beMockUserGrpClient,
			},
			args: args{
				ctx: ctx1,
				req: &fePb.GetRewardsForActorV1Request{
					Req:         feRequestHeader,
					PageContext: &fePb.PageContextRequest{Token: &fePb.PageContextRequest_AfterToken{AfterToken: "after-token-1#after-token-2"}},
				},
			},
			setupMocks: func() {
				beMockTieringClient.EXPECT().GetTieringPitchV2(ctx1, gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.StatusOk(),
				}, nil)
				beMockUserGrpClient.EXPECT().GetGroupsMappedToEmail(ctx1, gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil)
				beMockRewardGeneratorClient.EXPECT().GetRewardsByActorId(ctx1, &beRewardsPb.RewardsByActorIdRequest{
					ActorId:     "actor-1",
					PageContext: &rpc.PageContextRequest{Token: &rpc.PageContextRequest_AfterToken{AfterToken: "after-token-1"}, PageSize: 10},
				}).Return(&beRewardsPb.RewardsResponse{
					Status:      rpc.StatusOk(),
					PageContext: &rpc.PageContextResponse{AfterToken: "after-token-3", HasAfter: true},
					Rewards: []*beRewardsPb.Reward{
						{Id: "reward-1", Status: beRewardsPb.RewardStatus_PROCESSING_FAILED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 10, 0, 0, datetime.IST))},
						{Id: "reward-2", Status: beRewardsPb.RewardStatus_PROCESSING_FAILED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 5, 0, 0, datetime.IST))},
					},
				}, nil)

				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(ctx1, &beExchangerPb.GetExchangerOfferOrdersForActorRequest{
					ActorId: "actor-1",
					Filters: &beExchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{
						States: []beExchangerPb.ExchangerOfferOrderState{beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTION_CHOSEN, beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
							beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED},
						RewardTypes: []beExchangerPb.RewardType{beExchangerPb.RewardType_REWARD_TYPE_CASH, beExchangerPb.RewardType_REWARD_TYPE_CREDIT_CARD_BILL_ERASER},
					},
					PageContext: &rpc.PageContextRequest{Token: &rpc.PageContextRequest_AfterToken{AfterToken: "after-token-2"}, PageSize: 10},
				}).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:      rpc.StatusOk(),
					PageContext: &rpc.PageContextResponse{HasAfter: false},
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{Id: "eo-1", State: beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 9, 0, 0, datetime.IST))},
						{Id: "eo-2", State: beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 1, 0, 0, datetime.IST))},
					},
				}, nil)
			},
			want: &fePb.GetRewardsForActorV1Response{
				Status: rpc.StatusOk(),
				// reward entries should be sorted by createdAt time
				Rewards: []*fePb.RewardWrapperV1{
					{Data: &fePb.RewardWrapperV1_Reward{Reward: &fePb.Reward{Id: "reward-1", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 10, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "eo-1", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 9, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_Reward{Reward: &fePb.Reward{Id: "reward-2", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 5, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "eo-2", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 1, 0, 0, datetime.IST))}}},
				},
				PageContext: &fePb.PageContextResponse{
					AfterToken: "after-token-3#",
					HasAfter:   true,
				},
			},
		},
		{
			name: "rewards page token is empty in request, rewards shouldn't be fetched",
			fields: fields{
				rewardsFrontendMeta:    &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				rewardsGeneratorClient: beMockRewardGeneratorClient,
				exchangerOfferClient:   beMockExchangerOfferClient,
				tieringClient:          beMockTieringClient,
				beUserGrpClient:        beMockUserGrpClient,
			},
			args: args{
				ctx: ctx1,
				req: &fePb.GetRewardsForActorV1Request{
					Req:         feRequestHeader,
					PageContext: &fePb.PageContextRequest{Token: &fePb.PageContextRequest_AfterToken{AfterToken: "#after-token-2"}},
				},
			},
			setupMocks: func() {
				beMockTieringClient.EXPECT().GetTieringPitchV2(ctx1, gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.StatusOk(),
				}, nil)
				beMockUserGrpClient.EXPECT().GetGroupsMappedToEmail(ctx1, gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(ctx1, &beExchangerPb.GetExchangerOfferOrdersForActorRequest{
					ActorId: "actor-1",
					Filters: &beExchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{
						States: []beExchangerPb.ExchangerOfferOrderState{beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTION_CHOSEN, beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
							beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED},
						RewardTypes: []beExchangerPb.RewardType{beExchangerPb.RewardType_REWARD_TYPE_CASH, beExchangerPb.RewardType_REWARD_TYPE_CREDIT_CARD_BILL_ERASER},
					},
					PageContext: &rpc.PageContextRequest{Token: &rpc.PageContextRequest_AfterToken{AfterToken: "after-token-2"}, PageSize: 10},
				}).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:      rpc.StatusOk(),
					PageContext: &rpc.PageContextResponse{AfterToken: "after-token-3", HasAfter: true},
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{Id: "eo-1", State: beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 9, 0, 0, datetime.IST))},
						{Id: "eo-2", State: beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 1, 0, 0, datetime.IST))},
					},
				}, nil)
			},
			want: &fePb.GetRewardsForActorV1Response{
				Status: rpc.StatusOk(),
				// reward entries should be sorted by createdAt time
				Rewards: []*fePb.RewardWrapperV1{
					{Data: &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "eo-1", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 9, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "eo-2", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 1, 0, 0, datetime.IST))}}},
				},
				PageContext: &fePb.PageContextResponse{
					AfterToken: "#after-token-3",
					HasAfter:   true,
				},
			},
		},
		{
			name: "exchanger-orders page token is empty in request, exchanger-orders shouldn't be fetched",
			fields: fields{
				rewardsFrontendMeta:    &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				rewardsGeneratorClient: beMockRewardGeneratorClient,
				exchangerOfferClient:   beMockExchangerOfferClient,
				tieringClient:          beMockTieringClient,
				beUserGrpClient:        beMockUserGrpClient,
			},
			args: args{
				ctx: ctx1,
				req: &fePb.GetRewardsForActorV1Request{
					Req:         feRequestHeader,
					PageContext: &fePb.PageContextRequest{Token: &fePb.PageContextRequest_AfterToken{AfterToken: "after-token-1#"}},
				},
			},
			setupMocks: func() {
				beMockTieringClient.EXPECT().GetTieringPitchV2(ctx1, gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.StatusOk(),
				}, nil)
				beMockUserGrpClient.EXPECT().GetGroupsMappedToEmail(ctx1, gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil)
				beMockRewardGeneratorClient.EXPECT().GetRewardsByActorId(ctx1, &beRewardsPb.RewardsByActorIdRequest{
					ActorId:     "actor-1",
					PageContext: &rpc.PageContextRequest{Token: &rpc.PageContextRequest_AfterToken{AfterToken: "after-token-1"}, PageSize: 10},
				}).Return(&beRewardsPb.RewardsResponse{
					Status:      rpc.StatusOk(),
					PageContext: &rpc.PageContextResponse{AfterToken: "after-token-3", HasAfter: true},
					Rewards: []*beRewardsPb.Reward{
						{Id: "reward-1", Status: beRewardsPb.RewardStatus_PROCESSING_FAILED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 10, 0, 0, datetime.IST))},
						{Id: "reward-2", Status: beRewardsPb.RewardStatus_PROCESSING_FAILED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 5, 0, 0, datetime.IST))},
					},
				}, nil)
			},
			want: &fePb.GetRewardsForActorV1Response{
				Status: rpc.StatusOk(),
				// reward entries should be sorted by createdAt time
				Rewards: []*fePb.RewardWrapperV1{
					{Data: &fePb.RewardWrapperV1_Reward{Reward: &fePb.Reward{Id: "reward-1", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 10, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_Reward{Reward: &fePb.Reward{Id: "reward-2", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 5, 0, 0, datetime.IST))}}},
				},
				PageContext: &fePb.PageContextResponse{
					AfterToken: "after-token-3#",
					HasAfter:   true,
				},
			},
		},
		{
			name: "no more rewards or exchanger-orders exist, hasAfter should be false in pageCtx response",
			fields: fields{
				rewardsFrontendMeta:    &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				rewardsGeneratorClient: beMockRewardGeneratorClient,
				exchangerOfferClient:   beMockExchangerOfferClient,
				tieringClient:          beMockTieringClient,
				beUserGrpClient:        beMockUserGrpClient,
			},
			args: args{
				ctx: ctx1,
				req: &fePb.GetRewardsForActorV1Request{
					Req:         feRequestHeader,
					PageContext: &fePb.PageContextRequest{Token: &fePb.PageContextRequest_AfterToken{AfterToken: "after-token-1#after-token-2"}},
				},
			},
			setupMocks: func() {
				beMockTieringClient.EXPECT().GetTieringPitchV2(ctx1, gomock.Any()).Return(&beTieringPb.GetTieringPitchV2Response{
					Status: rpc.StatusOk(),
				}, nil)
				beMockUserGrpClient.EXPECT().GetGroupsMappedToEmail(ctx1, gomock.Any()).Return(&userGroupPb.GetGroupsMappedToEmailResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{commontypes.UserGroup_INTERNAL},
				}, nil)
				beMockRewardGeneratorClient.EXPECT().GetRewardsByActorId(ctx1, &beRewardsPb.RewardsByActorIdRequest{
					ActorId:     "actor-1",
					PageContext: &rpc.PageContextRequest{Token: &rpc.PageContextRequest_AfterToken{AfterToken: "after-token-1"}, PageSize: 10},
				}).Return(&beRewardsPb.RewardsResponse{
					Status:      rpc.StatusOk(),
					PageContext: &rpc.PageContextResponse{HasAfter: false},
					Rewards: []*beRewardsPb.Reward{
						{Id: "reward-1", Status: beRewardsPb.RewardStatus_PROCESSING_FAILED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 10, 0, 0, datetime.IST))},
						{Id: "reward-2", Status: beRewardsPb.RewardStatus_PROCESSING_FAILED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 5, 0, 0, datetime.IST))},
					},
				}, nil)

				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(ctx1, &beExchangerPb.GetExchangerOfferOrdersForActorRequest{
					ActorId: "actor-1",
					Filters: &beExchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{
						States: []beExchangerPb.ExchangerOfferOrderState{beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTION_CHOSEN, beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
							beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED},
						RewardTypes: []beExchangerPb.RewardType{beExchangerPb.RewardType_REWARD_TYPE_CASH, beExchangerPb.RewardType_REWARD_TYPE_CREDIT_CARD_BILL_ERASER},
					},
					PageContext: &rpc.PageContextRequest{Token: &rpc.PageContextRequest_AfterToken{AfterToken: "after-token-2"}, PageSize: 10},
				}).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:      rpc.StatusOk(),
					PageContext: &rpc.PageContextResponse{HasAfter: false},
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{Id: "eo-1", State: beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 9, 0, 0, datetime.IST))},
						{Id: "eo-2", State: beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED, CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 1, 0, 0, datetime.IST))},
					},
				}, nil)
			},
			want: &fePb.GetRewardsForActorV1Response{
				Status: rpc.StatusOk(),
				// reward entries should be sorted by createdAt time
				Rewards: []*fePb.RewardWrapperV1{
					{Data: &fePb.RewardWrapperV1_Reward{Reward: &fePb.Reward{Id: "reward-1", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 10, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "eo-1", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 9, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_Reward{Reward: &fePb.Reward{Id: "reward-2", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 5, 0, 0, datetime.IST))}}},
					{Data: &fePb.RewardWrapperV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "eo-2", CreatedAt: timestampPb.New(time.Date(2022, 1, 1, 1, 1, 0, 0, datetime.IST))}}},
				},
				PageContext: &fePb.PageContextResponse{
					AfterToken: "",
					HasAfter:   false,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			r := NewRewardsService(tt.fields.rewardsFrontendMeta, tt.fields.rewardsGeneratorClient, nil, nil, tt.fields.tieringClient, nil, nil, nil, nil, nil, nil, nil, nil, tt.fields.exchangerOfferClient, nil, nil, dyconf, nil, tags.NewManager(dyconf), nil, nil, nil, nil, nil, nil, tt.fields.beUserGrpClient, nil, nil, nil, nil, nil, nil, nil, nil)

			got, err := r.GetRewardsForActorV1(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRewardsForActorV1() error = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			// validate page token
			if !proto.Equal(got.GetPageContext(), tt.want.GetPageContext()) {
				t.Errorf("GetRewardsForActorV1() got PageContext() = %v, want PageContext() = %v", got.GetPageContext(), tt.want.GetPageContext())
				return
			}
			if len(got.GetRewards()) != len(tt.want.GetRewards()) {
				t.Errorf("GetRewardsForActorV1() got reward count = %v, want reward count=  %v", len(got.GetRewards()), len(tt.want.GetRewards()))
				return
			}
			// validate ordering of rewards
			for i := 0; i < len(got.GetRewards()); i++ {
				if got.GetRewards()[i].GetReward() != nil && got.GetRewards()[i].GetReward().GetId() != tt.want.GetRewards()[i].GetReward().GetId() {
					t.Errorf("GetRewardsForActorV1() got reward list = %v, want reward list = %v", got.GetRewards(), tt.want.GetRewards())
					return
				}
				if got.GetRewards()[i].GetExchangerOrder() != nil && got.GetRewards()[i].GetExchangerOrder().GetId() != tt.want.GetRewards()[i].GetExchangerOrder().GetId() {
					t.Errorf("GetRewardsForActorV1() got reward list = %v, want reward list = %v", got.GetRewards(), tt.want.GetRewards())
					return
				}
			}
		})
	}
}

func TestRewardService_GetRedeemedOffersV1(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	beMockExchangerOfferClient := exchangerMocks.NewMockExchangerOfferServiceClient(ctr)
	beMockRedemptionClient := casperRedemptionMocks.NewMockOfferRedemptionServiceClient(ctr)
	beMockCatalogueService := casperMocks.NewMockOfferCatalogServiceClient(ctr)
	beMockOfferInventoryServiceClient := casperMocks.NewMockOfferInventoryServiceClient(ctr)
	beAccrualClient := accuralMocks.NewMockAccrualClient(ctr)
	beMockExternalVendorRedemptionClient := evrMocks.NewMockExternalVendorRedemptionServiceClient(ctr)
	beMockVendorMappingClient := vendorMappingMocks.NewMockVendorMappingServiceClient(ctr)
	beMockCardClient := provisioningMocks.NewMockCardProvisioningClient(ctr)

	feRequestHeader := &feHeaderPb.RequestHeader{
		Auth: &feHeaderPb.AuthHeader{ActorId: "actor-1"},
	}

	fiStoreRedemption1 := &evrPb.FiStoreRedemption{
		Id:                          "fi-store-redemption-1",
		ActorId:                     "actor-1",
		Vendor:                      evrPb.Vendor_DPANDA,
		VendorRefId:                 "ref-12892",
		ProductId:                   "product-1",
		PaymentInstrumentIdentifier: "09090",
		ProductPrice: &money.Money{
			CurrencyCode: pkgMoney.RupeeCurrencyCode,
			Units:        1000,
		},
		SpentCashUnits: &money.Money{
			CurrencyCode: pkgMoney.RupeeCurrencyCode,
			Units:        800,
		},
		SpentFiCoinUnits: 2000,
		OrderStatus:      evrPb.OrderStatus_ORDER_STATUS_CONFIRMED,
		Category:         evrPb.Category_CATEGORY_ECOM,
		RedemptionMetaData: &evrPb.RedemptionMetaData{
			ProductName: "product-name-1",
			BrandName:   "brand-name-1",
			SubCategory: "sub-category-1",
			Quantity:    2,
		},
		OrderTimestamp: &timestampPb.Timestamp{Seconds: 1643306013},
	}
	fiStoreRedemption2 := &evrPb.FiStoreRedemption{
		Id:                          "fi-store-redemption-2",
		ActorId:                     "actor-1",
		Vendor:                      evrPb.Vendor_DPANDA,
		VendorRefId:                 "ref-12892",
		ProductId:                   "product-2",
		PaymentInstrumentIdentifier: "09090",
		ProductPrice: &money.Money{
			CurrencyCode: pkgMoney.RupeeCurrencyCode,
			Units:        1000,
		},
		SpentCashUnits: &money.Money{
			CurrencyCode: pkgMoney.RupeeCurrencyCode,
			Units:        800,
		},
		SpentFiCoinUnits: 2000,
		OrderStatus:      evrPb.OrderStatus_ORDER_STATUS_CONFIRMED,
		Category:         evrPb.Category_CATEGORY_ECOM,
		RedemptionMetaData: &evrPb.RedemptionMetaData{
			ProductName: "product-name-2",
			BrandName:   "brand-name-2",
			SubCategory: "sub-category-2",
			Quantity:    2,
		},
		OrderTimestamp: &timestampPb.Timestamp{Seconds: 1643306013},
	}

	type fields struct {
		rewardsFrontendMeta            *config.RewardsFrontendMeta
		offerRedemptionClient          beRedemptionPb.OfferRedemptionServiceClient
		exchangerOfferClient           exchanger.ExchangerOfferServiceClient
		catalogueServiceClient         beCasperPb.OfferCatalogServiceClient
		offerInventoryServiceClient    beCasperPb.OfferInventoryServiceClient
		accrualClient                  accrual.AccrualClient
		externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient
		vendorMappingClient            vendormappingPb.VendorMappingServiceClient
		cardClient                     provisioning.CardProvisioningClient
	}
	type args struct {
		ctx context.Context
		req *fePb.GetRedeemedOffersV1Request
	}

	tests := []struct {
		name       string
		fields     fields
		args       args
		setupMocks func()
		want       *fePb.GetRedeemedOffersV1Response
		wantErr    bool
	}{
		{
			name: "should return empty list when no exchanger-order or redeemed offers or external-vendor-redemptions exist",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:               rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{},
					PageContext:          &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{Status: rpc.StatusOk()}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:          rpc.StatusOk(),
					Redemptions:     []*evrPb.FiStoreRedemption{},
					PageCtxResponse: &rpc.PageContextResponse{HasAfter: false},
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status:      rpc.StatusOk(),
				Orders:      []*fePb.OrderV1{},
				PageContext: &rpc.PageContextResponse{AfterToken: "", HasAfter: false},
			},
			wantErr: false,
		},
		{
			name: "should return list containing exchanger-orders when only exchanger orders, and no redeemed offers & external vendor redemptions exist",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status: rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{
							Id:               "exchangerOrder-1",
							ExchangerOfferId: "exchangerOffer-1",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
						},
						{
							Id:               "exchangerOrder-2",
							ExchangerOfferId: "exchangerOffer-1",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
						},
					},
					PageContext: nil,
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{"exchangerOffer-1"}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{
					Status: rpc.StatusOk(),
					ExchangerOffers: []*beExchangerPb.ExchangerOffer{
						{
							Id: "exchangerOffer-1",
						},
					},
				}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    nil,
				}, nil)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:          rpc.StatusOk(),
					Redemptions:     []*evrPb.FiStoreRedemption{},
					PageCtxResponse: nil,
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-1"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-2"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "", HasAfter: false},
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when error occurs in fetching exchanger orders",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf(""))
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    nil,
				}, nil)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:          rpc.StatusOk(),
					Redemptions:     []*evrPb.FiStoreRedemption{},
					PageCtxResponse: &rpc.PageContextResponse{HasAfter: false},
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers and exchanger orders"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if status from GetExchangerOfferOrdersForActor isn't Ok",
			fields: fields{
				rewardsFrontendMeta:   &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient: beMockRedemptionClient,
				exchangerOfferClient:  beMockExchangerOfferClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:               rpc.StatusInternalWithDebugMsg(""),
					ExchangerOfferOrders: nil,
					PageContext:          nil,
				}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    nil,
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers and exchanger orders"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if error occurs in fetching redeemed offers",
			fields: fields{
				rewardsFrontendMeta:   &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient: beMockRedemptionClient,
				exchangerOfferClient:  beMockExchangerOfferClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(nil, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf(""))
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers and exchanger orders"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if status from GetRedeemedOffersForActor isn't Ok",
			fields: fields{
				rewardsFrontendMeta:   &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient: beMockRedemptionClient,
				exchangerOfferClient:  beMockExchangerOfferClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(nil, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusInternalWithDebugMsg(""),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    nil,
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers and exchanger orders"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if DecryptRedeemedOffersDetails returns an error",
			fields: fields{
				rewardsFrontendMeta:   &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient: beMockRedemptionClient,
				exchangerOfferClient:  beMockExchangerOfferClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(nil, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
						},
						{
							Id: "redeemedOffer-2",
						},
					},
					PageContext: nil,
				}, nil)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(nil, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers and exchanger orders"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if DecryptRedeemedOffersDetails doesn't return StatusOk ",
			fields: fields{
				rewardsFrontendMeta:   &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient: beMockRedemptionClient,
				exchangerOfferClient:  beMockExchangerOfferClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(nil, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
						},
						{
							Id: "redeemedOffer-2",
						},
					},
					PageContext: nil,
				}, nil)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.DecryptRedeemedOffersDetailsResponse{
					Status:         rpc.StatusInternalWithDebugMsg(""),
					RedeemedOffers: nil,
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers and exchanger orders"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if GetBulkOfferDetailsByIds returns an error",
			fields: fields{
				rewardsFrontendMeta:    &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:  beMockRedemptionClient,
				exchangerOfferClient:   beMockExchangerOfferClient,
				catalogueServiceClient: beMockCatalogueService,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(nil, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
						},
						{
							Id: "redeemedOffer-2",
						},
					},
					PageContext: nil,
				}, nil)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.DecryptRedeemedOffersDetailsResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id:        "redeemedOffer-1",
							OfferId:   "offer-1",
							OfferType: beCasperPb.OfferType_PHYSICAL_MERCHANDISE,
						},
						{
							Id:        "redeemedOffer-2",
							OfferId:   "offer-2",
							OfferType: beCasperPb.OfferType_COUPON,
						},
					},
				}, nil)
				beMockCatalogueService.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf(""))
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers and exchanger orders"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if GetBulkOfferDetailsByIds doesn't return StatusOk ",
			fields: fields{
				rewardsFrontendMeta:    &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:  beMockRedemptionClient,
				exchangerOfferClient:   beMockExchangerOfferClient,
				catalogueServiceClient: beMockCatalogueService,
				accrualClient:          beAccrualClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(nil, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
						},
						{
							Id: "redeemedOffer-2",
						},
					},
					PageContext: nil,
				}, nil)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.DecryptRedeemedOffersDetailsResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id:        "redeemedOffer-1",
							OfferId:   "offer-1",
							OfferType: beCasperPb.OfferType_PHYSICAL_MERCHANDISE,
						},
						{
							Id:        "redeemedOffer-2",
							OfferId:   "offer-2",
							OfferType: beCasperPb.OfferType_COUPON,
						},
					},
				}, nil)
				beMockCatalogueService.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetBulkOfferDetailsByIdsResponse{
					Status: rpc.StatusInternalWithDebugMsg(""),
					Offers: nil,
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers and exchanger orders"),
			},
			wantErr: false,
		},
		{
			name: "should return list containing redeemed offers when no exchanger orders exist, only redeemed offers exist",
			fields: fields{
				rewardsFrontendMeta:         &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:       beMockRedemptionClient,
				exchangerOfferClient:        beMockExchangerOfferClient,
				catalogueServiceClient:      beMockCatalogueService,
				offerInventoryServiceClient: beMockOfferInventoryServiceClient,
				accrualClient:               beAccrualClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:               rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{},
					PageContext:          nil,
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{Status: rpc.StatusOk()}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
						},
						{
							Id: "redeemedOffer-2",
						},
					},
					PageContext: nil,
				}, nil)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.DecryptRedeemedOffersDetailsResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id:        "redeemedOffer-1",
							OfferId:   "offer-1",
							OfferType: beCasperPb.OfferType_PHYSICAL_MERCHANDISE,
						},
						{
							Id:        "redeemedOffer-2",
							OfferId:   "offer-2",
							OfferType: beCasperPb.OfferType_COUPON,
						},
					},
				}, nil)
				beMockCatalogueService.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetBulkOfferDetailsByIdsResponse{
					Status: rpc.StatusOk(),
					Offers: []*beCasperPb.Offer{
						{
							Id: "offer-1",
						},
						{
							Id: "offer-2",
						},
					},
				}, nil)
				beMockOfferInventoryServiceClient.EXPECT().GetOfferInventoryByOfferIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetOfferInventoryByOfferIdsResponse{
					Status: rpc.StatusOk(),
					OfferIdToOfferInventoryMap: map[string]*beCasperPb.OfferInventory{
						"offer-1": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
						"offer-2": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
					},
				}, nil).Times(2)
				beAccrualClient.EXPECT().GetAccountDetails(gomock.Any(), gomock.Any()).Return(&accrual.GetAccountDetailsResponse{
					Status:         rpc.StatusOk(),
					AccountBalance: 10000,
				}, nil).Times(2)

			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-1"}},
					},
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-2"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "", HasAfter: false},
			},
			wantErr: false,
		},
		{
			name: "should return list containing both exchanger orders and redeemed offers sorted by created_at time when both exist",
			fields: fields{
				rewardsFrontendMeta:         &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:       beMockRedemptionClient,
				exchangerOfferClient:        beMockExchangerOfferClient,
				catalogueServiceClient:      beMockCatalogueService,
				offerInventoryServiceClient: beMockOfferInventoryServiceClient,
				accrualClient:               beAccrualClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status: rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{
							Id:               "exchangerOrder-1",
							ExchangerOfferId: "exchangerOffer-1",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 3,
								Nanos:   0,
							},
						},
						{
							Id:               "exchangerOrder-2",
							ExchangerOfferId: "exchangerOffer-2",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 1,
								Nanos:   0,
							},
						},
					},
					PageContext: nil,
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{
					Status: rpc.StatusOk(),
					ExchangerOffers: []*beExchangerPb.ExchangerOffer{
						{
							Id:                 "exchangerOffer-1",
							RedemptionCurrency: beExchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
						},
						{
							Id:                 "exchangerOffer-2",
							RedemptionCurrency: beExchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
						},
					},
				}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 2,
								Nanos:   0,
							},
						},
						{
							Id: "redeemedOffer-2",
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 4,
								Nanos:   0,
							},
						},
					},
					PageContext: nil,
				}, nil).Times(1)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.DecryptRedeemedOffersDetailsResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id:        "redeemedOffer-1",
							OfferId:   "offer-1",
							OfferType: beCasperPb.OfferType_PHYSICAL_MERCHANDISE,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 2,
								Nanos:   0,
							},
						},
						{
							Id:        "redeemedOffer-2",
							OfferId:   "offer-2",
							OfferType: beCasperPb.OfferType_COUPON,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 4,
								Nanos:   0,
							},
						},
					},
				}, nil)
				beMockCatalogueService.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetBulkOfferDetailsByIdsResponse{
					Status: rpc.StatusOk(),
					Offers: []*beCasperPb.Offer{
						{
							Id: "offer-1",
						},
						{
							Id: "offer-2",
						},
					},
				}, nil)
				beMockOfferInventoryServiceClient.EXPECT().GetOfferInventoryByOfferIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetOfferInventoryByOfferIdsResponse{
					Status: rpc.StatusOk(),
					OfferIdToOfferInventoryMap: map[string]*beCasperPb.OfferInventory{
						"offer-1": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
						"offer-2": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
					},
				}, nil).Times(2)
				beAccrualClient.EXPECT().GetAccountDetails(gomock.Any(), gomock.Any()).Return(&accrual.GetAccountDetailsResponse{
					Status:         rpc.StatusOk(),
					AccountBalance: 10000,
				}, nil).Times(2)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-2"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-1"}},
					},
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-1"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-2"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "", HasAfter: false},
			},
			wantErr: false,
		},
		{
			name: "should return list containing both exchanger orders and redeemed offers using request's pageContext, when it exists",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				catalogueServiceClient:         beMockCatalogueService,
				offerInventoryServiceClient:    beMockOfferInventoryServiceClient,
				accrualClient:                  beAccrualClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: &rpc.PageContextRequest{
						Token:    &rpc.PageContextRequest_AfterToken{AfterToken: "after-token-1#after-token-2#"},
						PageSize: 10,
					},
					Req: feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status: rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{
							Id:               "exchangerOrder-1",
							ExchangerOfferId: "exchangerOffer-1",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 3,
								Nanos:   0,
							},
						},
						{
							Id:               "exchangerOrder-2",
							ExchangerOfferId: "exchangerOffer-2",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 1,
								Nanos:   0,
							},
						},
					},
					PageContext: &rpc.PageContextResponse{
						AfterToken: "after-token-4",
						HasAfter:   true,
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{
					Status: rpc.StatusOk(),
					ExchangerOffers: []*beExchangerPb.ExchangerOffer{
						{
							Id:                 "exchangerOffer-1",
							RedemptionCurrency: beExchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
						},
						{
							Id:                 "exchangerOffer-2",
							RedemptionCurrency: beExchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
						},
					},
				}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 2,
								Nanos:   0,
							},
						},
						{
							Id: "redeemedOffer-2",
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 4,
								Nanos:   0,
							},
						},
					},
					PageContext: &rpc.PageContextResponse{
						AfterToken: "after-token-3",
						HasAfter:   true,
					},
				}, nil).Times(1)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.DecryptRedeemedOffersDetailsResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id:        "redeemedOffer-1",
							OfferId:   "offer-1",
							OfferType: beCasperPb.OfferType_PHYSICAL_MERCHANDISE,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 2,
								Nanos:   0,
							},
						},
						{
							Id:        "redeemedOffer-2",
							OfferId:   "offer-2",
							OfferType: beCasperPb.OfferType_COUPON,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 4,
								Nanos:   0,
							},
						},
					},
				}, nil)
				beMockCatalogueService.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetBulkOfferDetailsByIdsResponse{
					Status: rpc.StatusOk(),
					Offers: []*beCasperPb.Offer{
						{
							Id: "offer-1",
						},
						{
							Id: "offer-2",
						},
					},
				}, nil)
				beMockOfferInventoryServiceClient.EXPECT().GetOfferInventoryByOfferIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetOfferInventoryByOfferIdsResponse{
					Status: rpc.StatusOk(),
					OfferIdToOfferInventoryMap: map[string]*beCasperPb.OfferInventory{
						"offer-1": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
						"offer-2": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
					},
				}, nil).Times(2)
				beAccrualClient.EXPECT().GetAccountDetails(gomock.Any(), gomock.Any()).Return(&accrual.GetAccountDetailsResponse{
					Status:         rpc.StatusOk(),
					AccountBalance: 10000,
				}, nil).Times(2)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-2"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-1"}},
					},
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-1"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-2"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "after-token-3#after-token-4#", HasAfter: true},
			},
			wantErr: false,
		},
		{
			name: "should return only exchanger orders when pageContextRequest contains empty after_token for redeemed offers",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				catalogueServiceClient:         beMockCatalogueService,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: &rpc.PageContextRequest{
						Token:    &rpc.PageContextRequest_AfterToken{AfterToken: "#after-token-2#"},
						PageSize: 10,
					},
					Req: feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status: rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{
							Id:               "exchangerOrder-1",
							ExchangerOfferId: "exchangerOffer-1",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 3,
								Nanos:   0,
							},
						},
						{
							Id:               "exchangerOrder-2",
							ExchangerOfferId: "exchangerOffer-2",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 1,
								Nanos:   0,
							},
						},
					},
					PageContext: &rpc.PageContextResponse{
						AfterToken: "after-token-4",
						HasAfter:   true,
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{
					Status: rpc.StatusOk(),
					ExchangerOffers: []*beExchangerPb.ExchangerOffer{
						{
							Id:                 "exchangerOffer-1",
							RedemptionCurrency: beExchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
						},
						{
							Id:                 "exchangerOffer-2",
							RedemptionCurrency: beExchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS,
						},
					},
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-1"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-2"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "#after-token-4#", HasAfter: true},
			},
			wantErr: false,
		},
		{
			name: "should return only redeemed offers when pageContextRequest contains empty after_token for exchanger orders",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				catalogueServiceClient:         beMockCatalogueService,
				offerInventoryServiceClient:    beMockOfferInventoryServiceClient,
				accrualClient:                  beAccrualClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: &rpc.PageContextRequest{
						Token:    &rpc.PageContextRequest_AfterToken{AfterToken: "after-token-1##"},
						PageSize: 10,
					},
					Req: feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 2,
								Nanos:   0,
							},
						},
						{
							Id: "redeemedOffer-2",
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 4,
								Nanos:   0,
							},
						},
					},
					PageContext: &rpc.PageContextResponse{
						AfterToken: "after-token-3",
						HasAfter:   true,
					},
				}, nil).Times(1)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.DecryptRedeemedOffersDetailsResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id:        "redeemedOffer-1",
							OfferId:   "offer-1",
							OfferType: beCasperPb.OfferType_PHYSICAL_MERCHANDISE,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 2,
								Nanos:   0,
							},
						},
						{
							Id:        "redeemedOffer-2",
							OfferId:   "offer-2",
							OfferType: beCasperPb.OfferType_COUPON,
							CreatedAt: &timestampPb.Timestamp{
								Seconds: 4,
								Nanos:   0,
							},
						},
					},
				}, nil)
				beMockCatalogueService.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetBulkOfferDetailsByIdsResponse{
					Status: rpc.StatusOk(),
					Offers: []*beCasperPb.Offer{
						{
							Id: "offer-1",
						},
						{
							Id: "offer-2",
						},
					},
				}, nil)
				beMockOfferInventoryServiceClient.EXPECT().GetOfferInventoryByOfferIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetOfferInventoryByOfferIdsResponse{
					Status: rpc.StatusOk(),
					OfferIdToOfferInventoryMap: map[string]*beCasperPb.OfferInventory{
						"offer-1": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
						"offer-2": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
					},
				}, nil).Times(2)
				beAccrualClient.EXPECT().GetAccountDetails(gomock.Any(), gomock.Any()).Return(&accrual.GetAccountDetailsResponse{
					Status:         rpc.StatusOk(),
					AccountBalance: 10000,
				}, nil).Times(2)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-2"}},
					},
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-1"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "after-token-3##", HasAfter: true},
			},
			wantErr: false,
		},
		{
			name: "should return empty list when pageContextRequest contains empty after_token for both",
			fields: fields{
				rewardsFrontendMeta:    &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:  beMockRedemptionClient,
				exchangerOfferClient:   beMockExchangerOfferClient,
				catalogueServiceClient: beMockCatalogueService,
			},
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: &rpc.PageContextRequest{
						Token:    &rpc.PageContextRequest_AfterToken{AfterToken: "##"},
						PageSize: 10,
					},
					Req: feRequestHeader,
				},
			},
			setupMocks: func() {},
			want: &fePb.GetRedeemedOffersV1Response{
				Status:      rpc.StatusOk(),
				Orders:      []*fePb.OrderV1{},
				PageContext: &rpc.PageContextResponse{AfterToken: "", HasAfter: false},
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if status from GetFiStoreRedemptions isn't OK",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:               rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{},
					PageContext:          &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{Status: rpc.StatusOk()}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status: rpc.StatusInternalWithDebugMsg("error while fetching fi store redemptions"),
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers, exchanger orders and external vendor redemptions"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if status from GetBEMappingById isn't OK",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:               rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{},
					PageContext:          &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{Status: rpc.StatusOk()}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:          rpc.StatusOk(),
					Redemptions:     []*evrPb.FiStoreRedemption{fiStoreRedemption1, fiStoreRedemption2},
					PageCtxResponse: &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockVendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), &vendormappingPb.GetBEMappingByIdRequest{Id: "actor-1"}).Return(&vendormappingPb.GetBEMappingByIdResponse{
					Status: rpc.StatusRecordNotFoundWithDebugMsg("record not found"),
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers, exchanger orders and external vendor redemptions"),
			},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg if status from getDebitCardIdForActor isn't OK",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
				cardClient:                     beMockCardClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:               rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{},
					PageContext:          &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{Status: rpc.StatusOk()}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:          rpc.StatusOk(),
					Redemptions:     []*evrPb.FiStoreRedemption{fiStoreRedemption1, fiStoreRedemption2},
					PageCtxResponse: &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockVendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), &vendormappingPb.GetBEMappingByIdRequest{Id: "actor-1"}).Return(&vendormappingPb.GetBEMappingByIdResponse{
					Status:   rpc.StatusOk(),
					DpandaId: "dpanda-id",
				}, nil)
				beMockCardClient.EXPECT().FetchCards(gomock.Any(), &provisioning.FetchCardsRequest{
					Actor:            &types.Actor{Id: "actor-1"},
					IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
					CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
					CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
					CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
					CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
					SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
				}).Return(&provisioning.FetchCardsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offers, exchanger orders and external vendor redemptions"),
			},
			wantErr: false,
		},
		{
			name: "should return only external vendor redemptions list, when exchanger and redeemed offers list are empty",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
				cardClient:                     beMockCardClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:               rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{},
					PageContext:          &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{Status: rpc.StatusOk()}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:          rpc.StatusOk(),
					Redemptions:     []*evrPb.FiStoreRedemption{fiStoreRedemption1, fiStoreRedemption2},
					PageCtxResponse: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token"},
				}, nil)
				beMockVendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), &vendormappingPb.GetBEMappingByIdRequest{Id: "actor-1"}).Return(&vendormappingPb.GetBEMappingByIdResponse{
					Status:   rpc.StatusOk(),
					DpandaId: "dpanda-1",
				}, nil)
				beMockCardClient.EXPECT().FetchCards(gomock.Any(), &provisioning.FetchCardsRequest{
					Actor:            &types.Actor{Id: "actor-1"},
					IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
					CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
					CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
					CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
					CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
					SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
				}).Return(&provisioning.FetchCardsResponse{
					Status: rpc.StatusOk(),
					Cards: []*cardPb.Card{
						{
							Id: "debitCard-id",
						},
					},
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_CollectedOffer{CollectedOffer: &fePb.CollectedOffer{Id: "fi-store-redemption-1"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "##after-token", HasAfter: true},
			},
			wantErr: false,
		},
		{
			name: "should return list containing both exchanger offers and external vendor redemptions, when no redeemed offers exists",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
				cardClient:                     beMockCardClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status: rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{
							Id:               "exchangerOrder-1",
							ExchangerOfferId: "exchangerOffer-1",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
						},
						{
							Id:               "exchangerOrder-2",
							ExchangerOfferId: "exchangerOffer-1",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
						},
					},
					PageContext: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token-1"},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{"exchangerOffer-1"}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{
					Status: rpc.StatusOk(),
					ExchangerOffers: []*beExchangerPb.ExchangerOffer{
						{
							Id: "exchangerOffer-1",
						},
					},
				}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status:         rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{},
					PageContext:    &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:          rpc.StatusOk(),
					Redemptions:     []*evrPb.FiStoreRedemption{fiStoreRedemption1, fiStoreRedemption2},
					PageCtxResponse: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token-3"},
				}, nil)
				beMockVendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), &vendormappingPb.GetBEMappingByIdRequest{Id: "actor-1"}).Return(&vendormappingPb.GetBEMappingByIdResponse{
					Status:   rpc.StatusOk(),
					DpandaId: "dpanda-1",
				}, nil)
				beMockCardClient.EXPECT().FetchCards(gomock.Any(), &provisioning.FetchCardsRequest{
					Actor:            &types.Actor{Id: "actor-1"},
					IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
					CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
					CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
					CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
					CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
					SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
				}).Return(&provisioning.FetchCardsResponse{
					Status: rpc.StatusOk(),
					Cards: []*cardPb.Card{
						{
							Id: "debitCard-id",
						},
					},
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_CollectedOffer{CollectedOffer: &fePb.CollectedOffer{Id: "fi-store-redemption-1"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-1"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-2"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "#after-token-1#after-token-3", HasAfter: true},
			},
			wantErr: false,
		},
		{
			name: "should return list containing both redeemed offers and external vendor redemptions, when no exchanger offers exists",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				catalogueServiceClient:         beMockCatalogueService,
				offerInventoryServiceClient:    beMockOfferInventoryServiceClient,
				accrualClient:                  beAccrualClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
				cardClient:                     beMockCardClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status:               rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{},
					PageContext:          &rpc.PageContextResponse{HasAfter: false},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{Status: rpc.StatusOk()}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
						},
						{
							Id: "redeemedOffer-2",
						},
					},
					PageContext: nil,
				}, nil)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.DecryptRedeemedOffersDetailsResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id:        "redeemedOffer-1",
							OfferId:   "offer-1",
							OfferType: beCasperPb.OfferType_PHYSICAL_MERCHANDISE,
						},
						{
							Id:        "redeemedOffer-2",
							OfferId:   "offer-2",
							OfferType: beCasperPb.OfferType_COUPON,
						},
					},
				}, nil)
				beMockCatalogueService.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetBulkOfferDetailsByIdsResponse{
					Status: rpc.StatusOk(),
					Offers: []*beCasperPb.Offer{
						{
							Id: "offer-1",
						},
						{
							Id: "offer-2",
						},
					},
				}, nil)
				beMockOfferInventoryServiceClient.EXPECT().GetOfferInventoryByOfferIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetOfferInventoryByOfferIdsResponse{
					Status: rpc.StatusOk(),
					OfferIdToOfferInventoryMap: map[string]*beCasperPb.OfferInventory{
						"offer-1": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
						"offer-2": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
					},
				}, nil).Times(2)
				beAccrualClient.EXPECT().GetAccountDetails(gomock.Any(), gomock.Any()).Return(&accrual.GetAccountDetailsResponse{
					Status:         rpc.StatusOk(),
					AccountBalance: 10000,
				}, nil).Times(2)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:          rpc.StatusOk(),
					Redemptions:     []*evrPb.FiStoreRedemption{fiStoreRedemption1, fiStoreRedemption2},
					PageCtxResponse: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token-3"},
				}, nil)
				beMockVendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), &vendormappingPb.GetBEMappingByIdRequest{Id: "actor-1"}).Return(&vendormappingPb.GetBEMappingByIdResponse{
					Status:   rpc.StatusOk(),
					DpandaId: "dpanda-1",
				}, nil)
				beMockCardClient.EXPECT().FetchCards(gomock.Any(), &provisioning.FetchCardsRequest{
					Actor:            &types.Actor{Id: "actor-1"},
					IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
					CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
					CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
					CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
					CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
					SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
				}).Return(&provisioning.FetchCardsResponse{
					Status: rpc.StatusOk(),
					Cards: []*cardPb.Card{
						{
							Id: "debitCard-id",
						},
					},
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_CollectedOffer{CollectedOffer: &fePb.CollectedOffer{Id: "fi-store-redemption-1"}},
					},
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-1"}},
					},
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-2"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "##after-token-3", HasAfter: true},
			},
			wantErr: false,
		},
		{
			name: "should return list containing exchanger offers, redeemed offers and external vendor redemptions",
			fields: fields{
				rewardsFrontendMeta:            &config.RewardsFrontendMeta{AppFetchRewardsPageSize: 10},
				offerRedemptionClient:          beMockRedemptionClient,
				exchangerOfferClient:           beMockExchangerOfferClient,
				catalogueServiceClient:         beMockCatalogueService,
				offerInventoryServiceClient:    beMockOfferInventoryServiceClient,
				accrualClient:                  beAccrualClient,
				externalVendorRedemptionClient: beMockExternalVendorRedemptionClient,
				vendorMappingClient:            beMockVendorMappingClient,
				cardClient:                     beMockCardClient,
			},
			args: args{
				ctx: epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID),
				req: &fePb.GetRedeemedOffersV1Request{
					PageContext: nil,
					Req:         feRequestHeader,
				},
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOfferOrdersForActor(gomock.Any(), gomock.Any()).Return(&beExchangerPb.GetExchangerOfferOrdersForActorResponse{
					Status: rpc.StatusOk(),
					ExchangerOfferOrders: []*beExchangerPb.ExchangerOfferOrder{
						{
							Id:               "exchangerOrder-1",
							ExchangerOfferId: "exchangerOffer-1",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
						},
						{
							Id:               "exchangerOrder-2",
							ExchangerOfferId: "exchangerOffer-1",
							State:            beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
						},
					},
					PageContext: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token-1"},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOffersByIds(gomock.Any(), &beExchangerPb.GetExchangerOffersByIdsRequest{Ids: []string{"exchangerOffer-1"}}).Return(&beExchangerPb.GetExchangerOffersByIdsResponse{
					Status: rpc.StatusOk(),
					ExchangerOffers: []*beExchangerPb.ExchangerOffer{
						{
							Id: "exchangerOffer-1",
						},
					},
				}, nil)
				beMockRedemptionClient.EXPECT().GetRedeemedOffersForActor(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.GetRedeemedOffersForActorResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id: "redeemedOffer-1",
						},
						{
							Id: "redeemedOffer-2",
						},
					},
					PageContext: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token-2"},
				}, nil)
				beMockRedemptionClient.EXPECT().DecryptRedeemedOffersDetails(gomock.Any(), gomock.Any()).Return(&beRedemptionPb.DecryptRedeemedOffersDetailsResponse{
					Status: rpc.StatusOk(),
					RedeemedOffers: []*beRedemptionPb.RedeemedOffer{
						{
							Id:        "redeemedOffer-1",
							OfferId:   "offer-1",
							OfferType: beCasperPb.OfferType_PHYSICAL_MERCHANDISE,
						},
						{
							Id:        "redeemedOffer-2",
							OfferId:   "offer-2",
							OfferType: beCasperPb.OfferType_COUPON,
						},
					},
				}, nil)
				beMockCatalogueService.EXPECT().GetBulkOfferDetailsByIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetBulkOfferDetailsByIdsResponse{
					Status: rpc.StatusOk(),
					Offers: []*beCasperPb.Offer{
						{
							Id: "offer-1",
						},
						{
							Id: "offer-2",
						},
					},
				}, nil)
				beMockOfferInventoryServiceClient.EXPECT().GetOfferInventoryByOfferIds(gomock.Any(), gomock.Any()).Return(&beCasperPb.GetOfferInventoryByOfferIdsResponse{
					Status: rpc.StatusOk(),
					OfferIdToOfferInventoryMap: map[string]*beCasperPb.OfferInventory{
						"offer-1": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
						"offer-2": &beCasperPb.OfferInventory{
							Id:              "redeemedOffer-1",
							OfferId:         "offer-1",
							TotalCount:      100,
							AvailableCount:  50,
							MaxPerUserLimit: 10,
							IsDeleted:       false,
						},
					},
				}, nil).Times(2)
				beAccrualClient.EXPECT().GetAccountDetails(gomock.Any(), gomock.Any()).Return(&accrual.GetAccountDetailsResponse{
					Status:         rpc.StatusOk(),
					AccountBalance: 10000,
				}, nil).Times(2)
				beMockExternalVendorRedemptionClient.EXPECT().GetFiStoreRedemptions(gomock.Any(), gomock.Any()).Return(&evrPb.GetFiStoreRedemptionsResponse{
					Status:          rpc.StatusOk(),
					Redemptions:     []*evrPb.FiStoreRedemption{fiStoreRedemption1, fiStoreRedemption2},
					PageCtxResponse: &rpc.PageContextResponse{HasAfter: true, AfterToken: "after-token-3"},
				}, nil)
				beMockVendorMappingClient.EXPECT().GetBEMappingById(gomock.Any(), &vendormappingPb.GetBEMappingByIdRequest{Id: "actor-1"}).Return(&vendormappingPb.GetBEMappingByIdResponse{
					Status:   rpc.StatusOk(),
					DpandaId: "dpanda-1",
				}, nil)
				beMockCardClient.EXPECT().FetchCards(gomock.Any(), &provisioning.FetchCardsRequest{
					Actor:            &types.Actor{Id: "actor-1"},
					IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
					CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
					CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
					CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
					CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
					SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
				}).Return(&provisioning.FetchCardsResponse{
					Status: rpc.StatusOk(),
					Cards: []*cardPb.Card{
						{
							Id: "debitCard-id",
						},
					},
				}, nil)
			},
			want: &fePb.GetRedeemedOffersV1Response{
				Status: rpc.StatusOk(),
				Orders: []*fePb.OrderV1{
					{
						OrderData: &fePb.OrderV1_CollectedOffer{CollectedOffer: &fePb.CollectedOffer{Id: "fi-store-redemption-1"}},
					},
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-1"}},
					},
					{
						OrderData: &fePb.OrderV1_RedeemedOffer{RedeemedOffer: &fePb.RedeemedOffer{Id: "redeemedOffer-2"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-1"}},
					},
					{
						OrderData: &fePb.OrderV1_ExchangerOrder{ExchangerOrder: &fePb.ExchangerOrder{Id: "exchangerOrder-2"}},
					},
				},
				PageContext: &rpc.PageContextResponse{AfterToken: "after-token-2#after-token-1#after-token-3", HasAfter: true},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			r := NewRewardsService(tt.fields.rewardsFrontendMeta, nil, nil, tt.fields.accrualClient, nil, nil, tt.fields.catalogueServiceClient, tt.fields.offerInventoryServiceClient, nil, tt.fields.offerRedemptionClient, nil, nil, nil, tt.fields.exchangerOfferClient, nil, nil, dyconf, nil, tags.NewManager(dyconf), nil, nil, nil, nil, tt.fields.vendorMappingClient, tt.fields.cardClient, nil, tt.fields.externalVendorRedemptionClient, nil, nil, nil, nil, nil, nil, nil)

			got, err := r.GetRedeemedOffersV1(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRedeemedOffersV1() error = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			// validate page token
			if !proto.Equal(got.GetPageContext(), tt.want.GetPageContext()) {
				t.Errorf("GetRedeemedOffersV1() got PageContext() = %v, want PageContext() = %v", got.GetPageContext(), tt.want.GetPageContext())
				return
			}
			if len(got.GetOrders()) != len(tt.want.GetOrders()) {
				t.Errorf("GetRedeemedOffersV1() got orders count = %v, want orders count=  %v", len(got.GetOrders()), len(tt.want.GetOrders()))
				return
			}
			// validate ordering of rewards
			for i := 0; i < len(got.GetOrders()); i++ {
				if got.GetOrders()[i].GetRedeemedOffer() != nil && got.GetOrders()[i].GetRedeemedOffer().GetId() != tt.want.GetOrders()[i].GetRedeemedOffer().GetId() {
					t.Errorf("GetRedeemedOffersV1() got orders list = %v, want orders list = %v", got.GetOrders(), tt.want.GetOrders())
					return
				}
				if got.GetOrders()[i].GetExchangerOrder() != nil && got.GetOrders()[i].GetExchangerOrder().GetId() != tt.want.GetOrders()[i].GetExchangerOrder().GetId() {
					t.Errorf("GetRedeemedOffersV1() got orders list = %v, want orders list = %v", got.GetOrders(), tt.want.GetOrders())
					return
				}
				if got.GetOrders()[i].GetCollectedOffer() != nil && got.GetOrders()[i].GetCollectedOffer().GetId() != tt.want.GetOrders()[i].GetCollectedOffer().GetId() {
					t.Errorf("GetRedeemedOffersV1() got orders list = %v, want orders list = %v", got.GetOrders(), tt.want.GetOrders())
					return
				}
			}
		})
	}
}

func TestRewardService_GetExchangerOrderInputScreen(t *testing.T) {
	var (
		feRequestHeader = &feHeaderPb.RequestHeader{
			Auth: &feHeaderPb.AuthHeader{ActorId: "actor-id-1"},
		}

		getExchangerOrderInputScreenRequest = &fePb.GetExchangerOrderInputScreenRequest{
			ExchangerOrderId: "exchanger-order-1",
			Req:              feRequestHeader,
		}
		happyCaseResponse = &fePb.GetExchangerOrderInputScreenResponse{
			InputScreen: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_REWARD_SHIPPING_ADDRESS_INPUT_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_RewardShippingAddressInputScreenOptions{
					RewardShippingAddressInputScreenOptions: &deeplinkPb.RewardShippingAddressInputScreenOptions{
						// existing shipping addresses present in user profile
						Addresses: []*types.PostalAddress{
							{
								AddressLines: []string{"shipping-address-line-1", "shipping-address-line-2"},
							},
						},
					},
				},
			},
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
		}
		exchangerOrderUserInputDefaultErrorView = feErrors.NewBottomSheetErrorView(
			"",
			"Something went wrong.", "",
			"Your reward is safe! But we need some additional information to process it. Re-directing you to my orders to confirm the same.",
			&errors2.CTA{
				Text: "OK",
				Type: errors2.CTA_CUSTOM,
				Action: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN,
				},
				DisplayTheme: errors2.CTA_PRIMARY,
			},
		)
	)

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	beMockExchangerOfferClient := exchangerMocks.NewMockExchangerOfferServiceClient(ctr)
	mockActorClient := actorMocks.NewMockActorClient(ctr)
	mockUserClient := userMocks.NewMockUsersClient(ctr)

	type fields struct {
		mockActorClient          actorPb.ActorClient
		mockExchangerOfferClient exchanger.ExchangerOfferServiceClient
		mockUserClient           beUserPb.UsersClient
	}
	type args struct {
		ctx context.Context
		req *fePb.GetExchangerOrderInputScreenRequest
	}

	tests := []struct {
		name       string
		fields     fields
		args       args
		setupMocks func()
		want       *fePb.GetExchangerOrderInputScreenResponse
		wantErr    bool
	}{
		{
			name: "should return correct deeplink when exchanger-order is in USER_INPUT_NEEDED_FOR_FULFILLMENT state, is of PHYSICAL_MERCHANDISE type and no error occurs",
			fields: fields{
				mockActorClient:          mockActorClient,
				mockExchangerOfferClient: beMockExchangerOfferClient,
				mockUserClient:           mockUserClient,
			},
			args: args{
				ctx: context.Background(),
				req: getExchangerOrderInputScreenRequest,
			},
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(context.Background(), &actorPb.GetActorByIdRequest{Id: "actor-id-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id-1",
						EntityId: "entity-id-1",
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: getExchangerOrderInputScreenRequest.GetExchangerOrderId()}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:      "exchanger-order-1",
						ActorId: "actor-id-1",
						State:   exchanger.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
						ChosenOption: &exchanger.ExchangerOfferOption{
							RewardType: exchanger.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
						},
					},
				}, nil)
				mockUserClient.EXPECT().GetAllAddresses(context.Background(), &beUserPb.GetAllAddressesRequest{
					UserId: "entity-id-1",
				}).Return(&beUserPb.GetAllAddressesResponse{
					Status: rpc.StatusOk(),
					Addresses: map[string]*types.Addresses{
						types.AddressType_SHIPPING.String(): {Addresses: []*postaladdress.PostalAddress{{
							AddressLines: []string{"shipping-address-line-1", "shipping-address-line-2"},
						}}},
					},
				}, nil)
			},
			want:    happyCaseResponse,
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when GetActorById returns err",
			fields: fields{
				mockActorClient:          mockActorClient,
				mockExchangerOfferClient: beMockExchangerOfferClient,
				mockUserClient:           mockUserClient,
			},
			args: args{
				ctx: context.Background(),
				req: getExchangerOrderInputScreenRequest,
			},
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(context.Background(), &actorPb.GetActorByIdRequest{Id: "actor-id-1"}).Return(nil, fmt.Errorf("error"))
			},
			want: &fePb.GetExchangerOrderInputScreenResponse{RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("failed to fetch actor by actorId"),
				ErrorView: nil,
			}},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when GetActorById does not return StatusOk",
			fields: fields{
				mockActorClient:          mockActorClient,
				mockExchangerOfferClient: beMockExchangerOfferClient,
				mockUserClient:           mockUserClient,
			},
			args: args{
				ctx: context.Background(),
				req: getExchangerOrderInputScreenRequest,
			},
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(context.Background(), &actorPb.GetActorByIdRequest{Id: "actor-id-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusInternal(),
					Actor:  nil,
				}, nil)
			},
			want: &fePb.GetExchangerOrderInputScreenResponse{RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("failed to fetch actor by actorId"),
				ErrorView: nil,
			}},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when GetExchangerOrderById does not return StatusOk",
			fields: fields{
				mockActorClient:          mockActorClient,
				mockExchangerOfferClient: beMockExchangerOfferClient,
				mockUserClient:           mockUserClient,
			},
			args: args{
				ctx: context.Background(),
				req: getExchangerOrderInputScreenRequest,
			},
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(context.Background(), &actorPb.GetActorByIdRequest{Id: "actor-id-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id-1",
						EntityId: "entity-id-1",
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: getExchangerOrderInputScreenRequest.GetExchangerOrderId()}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status:         rpc.StatusInternal(),
					ExchangerOrder: nil,
				}, nil)
			},
			want: &fePb.GetExchangerOrderInputScreenResponse{RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("error fetching exchanger order for the given exchangerOrderId for actor"),
				ErrorView: nil,
			}},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when GetExchangerOrderById returns error",
			fields: fields{
				mockActorClient:          mockActorClient,
				mockExchangerOfferClient: beMockExchangerOfferClient,
				mockUserClient:           mockUserClient,
			},
			args: args{
				ctx: context.Background(),
				req: getExchangerOrderInputScreenRequest,
			},
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(context.Background(), &actorPb.GetActorByIdRequest{Id: "actor-id-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id-1",
						EntityId: "entity-id-1",
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: getExchangerOrderInputScreenRequest.GetExchangerOrderId()}).Return(nil, fmt.Errorf("error"))
			},
			want: &fePb.GetExchangerOrderInputScreenResponse{RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("error fetching exchanger order for the given exchangerOrderId for actor"),
				ErrorView: nil,
			}},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when exchanger-order is not in USER_INPUT_NEEDED_FOR_FULFILLMENT state",
			fields: fields{
				mockActorClient:          mockActorClient,
				mockExchangerOfferClient: beMockExchangerOfferClient,
				mockUserClient:           mockUserClient,
			},
			args: args{
				ctx: context.Background(),
				req: getExchangerOrderInputScreenRequest,
			},
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(context.Background(), &actorPb.GetActorByIdRequest{Id: "actor-id-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id-1",
						EntityId: "entity-id-1",
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: getExchangerOrderInputScreenRequest.GetExchangerOrderId()}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:      "exchanger-order-1",
						ActorId: "actor-id-1",
						State:   exchanger.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED,
						ChosenOption: &exchanger.ExchangerOfferOption{
							RewardType: exchanger.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
						},
					},
				}, nil)
			},
			want: &fePb.GetExchangerOrderInputScreenResponse{RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("exchanger order not in USER_INPUT_NEEDED_FOR_FULFILLMENT state"),
				ErrorView: nil,
			}},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when exchanger-order is in USER_INPUT_NEEDED_FOR_FULFILLMENT state but isn't of supported rewardType",
			fields: fields{
				mockActorClient:          mockActorClient,
				mockExchangerOfferClient: beMockExchangerOfferClient,
				mockUserClient:           mockUserClient,
			},
			args: args{
				ctx: context.Background(),
				req: getExchangerOrderInputScreenRequest,
			},
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(context.Background(), &actorPb.GetActorByIdRequest{Id: "actor-id-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id-1",
						EntityId: "entity-id-1",
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: getExchangerOrderInputScreenRequest.GetExchangerOrderId()}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:      "exchanger-order-1",
						ActorId: "actor-id-1",
						State:   exchanger.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
						ChosenOption: &exchanger.ExchangerOfferOption{
							RewardType: exchanger.RewardType_REWARD_TYPE_CASH,
						},
					},
				}, nil)
			},
			want: &fePb.GetExchangerOrderInputScreenResponse{RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("error getting input screen deeplink"),
				ErrorView: exchangerOrderUserInputDefaultErrorView,
			}},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when GetAllAddresses RPC doesn't return StatusOk",
			fields: fields{
				mockActorClient:          mockActorClient,
				mockExchangerOfferClient: beMockExchangerOfferClient,
				mockUserClient:           mockUserClient,
			},
			args: args{
				ctx: context.Background(),
				req: getExchangerOrderInputScreenRequest,
			},
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(context.Background(), &actorPb.GetActorByIdRequest{Id: "actor-id-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id-1",
						EntityId: "entity-id-1",
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: getExchangerOrderInputScreenRequest.GetExchangerOrderId()}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:      "exchanger-order-1",
						ActorId: "actor-id-1",
						State:   exchanger.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
						ChosenOption: &exchanger.ExchangerOfferOption{
							RewardType: exchanger.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
						},
					},
				}, nil)
				mockUserClient.EXPECT().GetAllAddresses(context.Background(), &beUserPb.GetAllAddressesRequest{
					UserId: "entity-id-1",
				}).Return(&beUserPb.GetAllAddressesResponse{
					Status:    rpc.StatusInternal(),
					Addresses: nil,
				}, nil)
			},
			want: &fePb.GetExchangerOrderInputScreenResponse{RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("error getting input screen deeplink"),
				ErrorView: exchangerOrderUserInputDefaultErrorView,
			}},
			wantErr: false,
		},
		{
			name: "should return StatusInternalWithDebugMsg when GetAllAddresses RPC returns error",
			fields: fields{
				mockActorClient:          mockActorClient,
				mockExchangerOfferClient: beMockExchangerOfferClient,
				mockUserClient:           mockUserClient,
			},
			args: args{
				ctx: context.Background(),
				req: getExchangerOrderInputScreenRequest,
			},
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(context.Background(), &actorPb.GetActorByIdRequest{Id: "actor-id-1"}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id:       "actor-id-1",
						EntityId: "entity-id-1",
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: getExchangerOrderInputScreenRequest.GetExchangerOrderId()}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:      "exchanger-order-1",
						ActorId: "actor-id-1",
						State:   exchanger.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
						ChosenOption: &exchanger.ExchangerOfferOption{
							RewardType: exchanger.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
						},
					},
				}, nil)
				mockUserClient.EXPECT().GetAllAddresses(context.Background(), &beUserPb.GetAllAddressesRequest{
					UserId: "entity-id-1",
				}).Return(nil, fmt.Errorf("error"))
			},
			want: &fePb.GetExchangerOrderInputScreenResponse{RespHeader: &feHeaderPb.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("error getting input screen deeplink"),
				ErrorView: exchangerOrderUserInputDefaultErrorView,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			r := NewRewardsService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, tt.fields.mockUserClient, nil, tt.fields.mockExchangerOfferClient, tt.fields.mockActorClient, nil, dyconf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			got, err := r.GetExchangerOrderInputScreen(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExchangerOrderInputScreen() error = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			// validate input screen
			if !proto.Equal(got.GetInputScreen(), tt.want.GetInputScreen()) {
				t.Errorf("GetExchangerOrderInputScreen() got InputScreen = %v, want InputScreen = %v", got.GetInputScreen(), tt.want.GetInputScreen())
				return
			}

			// validate response header
			if !proto.Equal(got.GetRespHeader(), tt.want.GetRespHeader()) {
				t.Errorf("GetExchangerOrderInputScreen() got ResponseHeader = %v, want ResponseHeader = %v", got.GetRespHeader(), tt.want.GetRespHeader())
				return
			}
		})
	}
}

func TestRewardService_SubmitExchangerOrderUserInput(t *testing.T) {
	var (
		feRequestHeader = &feHeaderPb.RequestHeader{
			Auth: &feHeaderPb.AuthHeader{ActorId: "actor-id-1"},
		}

		submitExchangerOrderUserInputRequest = &fePb.SubmitExchangerOrderUserInputRequest{
			ExchangerOrderId: "exchanger-order-1",
			UserInput: &fePb.SubmitExchangerOrderUserInputRequest_UserInput{ShippingAddress: &types.PostalAddress{
				AddressLines: []string{"line-1", "line-2"},
			}},
			Req: feRequestHeader,
		}

		exchangerOrderUserInputDefaultErrorView = feErrors.NewBottomSheetErrorView(
			"",
			"Something went wrong.", "",
			"Your reward is safe! But we need some additional information to process it. Re-directing you to my orders to confirm the same.",
			&errors2.CTA{
				Text: "OK",
				Type: errors2.CTA_CUSTOM,
				Action: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN,
				},
				DisplayTheme: errors2.CTA_PRIMARY,
			},
		)
	)

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	// init mocks
	beMockExchangerOfferClient := exchangerMocks.NewMockExchangerOfferServiceClient(ctr)

	type fields struct {
		mockExchangerOfferClient exchanger.ExchangerOfferServiceClient
	}

	type args struct {
		ctx context.Context
		req *fePb.SubmitExchangerOrderUserInputRequest
	}

	tests := []struct {
		name       string
		fields     fields
		args       args
		setupMocks func()
		want       *fePb.SubmitExchangerOrderUserInputResponse
		wantErr    bool
	}{
		{
			name:   "should return StatusOk when exchangerOrder is USER_INPUT_NEEDED_FOR_FULFILLMENT state, is of PHYSICAL_MERCHANDISE type and no error occurs",
			fields: fields{mockExchangerOfferClient: beMockExchangerOfferClient},
			args: args{
				ctx: context.Background(),
				req: submitExchangerOrderUserInputRequest,
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-1"}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:         "exchanger-order-1",
						ActorId:    "actor-id-1",
						State:      beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
						RewardType: beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().SubmitUserInputForChosenOption(context.Background(), &beExchangerPb.SubmitUserInputForChosenOptionRequest{
					ActorId:          "actor-id-1",
					ExchangerOrderId: "exchanger-order-1",
					ShippingAddress: &postaladdress.PostalAddress{
						AddressLines: []string{"line-1", "line-2"},
					},
				}).Return(&beExchangerPb.SubmitUserInputForChosenOptionResponse{
					Status: rpc.StatusOk(),
					ExchangerOfferOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:         "exchanger-order-1",
						ActorId:    "actor-id-1",
						State:      beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
						RewardType: beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
					},
				}, nil)
			},
			want: &fePb.SubmitExchangerOrderUserInputResponse{
				ExchangerOrder: &fePb.ExchangerOrder{
					Id:     "exchanger-order-1",
					Status: fePb.ExchangerOrderStatus_IN_PROGRESS,
				},
				Cta: &fePb.SubmitExchangerOrderUserInputResponse_CTA{
					Text: "View in 'Collected Offers'",
					NextScreen: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN,
					},
				},
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
			},
			wantErr: false,
		},
		{
			name:   "should return StatusInternalWithDebugMsg when GetExchangerOrderById does not return StatusOk",
			fields: fields{mockExchangerOfferClient: beMockExchangerOfferClient},
			args: args{
				ctx: context.Background(),
				req: submitExchangerOrderUserInputRequest,
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-1"}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status:         rpc.StatusInternal(),
					ExchangerOrder: nil,
				}, nil)
			},
			want: &fePb.SubmitExchangerOrderUserInputResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("error fetching exchanger order for the given exchangerOrderId for actor"),
					ErrorView: exchangerOrderUserInputDefaultErrorView,
				},
			},
			wantErr: false,
		},
		{
			name:   "should return StatusInternalWithDebugMsg when GetExchangerOrderById returns error",
			fields: fields{mockExchangerOfferClient: beMockExchangerOfferClient},
			args: args{
				ctx: context.Background(),
				req: submitExchangerOrderUserInputRequest,
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-1"}).Return(nil, fmt.Errorf("error"))
			},
			want: &fePb.SubmitExchangerOrderUserInputResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("error fetching exchanger order for the given exchangerOrderId for actor"),
					ErrorView: exchangerOrderUserInputDefaultErrorView,
				},
			},
			wantErr: false,
		},
		{
			name:   "should return StatusInternalWithDebugMsg when exchangerOrder is not in USER_INPUT_NEEDED_FOR_FULFILLMENT state",
			fields: fields{mockExchangerOfferClient: beMockExchangerOfferClient},
			args: args{
				ctx: context.Background(),
				req: submitExchangerOrderUserInputRequest,
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-1"}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:         "exchanger-order-1",
						ActorId:    "actor-id-1",
						State:      beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
						RewardType: beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
					},
				}, nil)
			},
			want: &fePb.SubmitExchangerOrderUserInputResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("exchanger order isn't in USER_INPUT_NEEDED_FOR_FULFILLMENT state")},
			},
		},
		{
			name:   "should return StatusInternalWithDebugMsg when exchangerOrder is not of a supported type",
			fields: fields{mockExchangerOfferClient: beMockExchangerOfferClient},
			args: args{
				ctx: context.Background(),
				req: submitExchangerOrderUserInputRequest,
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-1"}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:         "exchanger-order-1",
						ActorId:    "actor-id-1",
						State:      beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
						RewardType: beExchangerPb.RewardType_REWARD_TYPE_CASH,
					},
				}, nil)
			},
			want: &fePb.SubmitExchangerOrderUserInputResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error in submitting user's input, unsupported reward type")},
			},
			wantErr: false,
		},
		{
			name:   "should return StatusInternalWithDebugMsg when SubmitUserInputForChosenOption does not return StatusOk",
			fields: fields{mockExchangerOfferClient: beMockExchangerOfferClient},
			args: args{
				ctx: context.Background(),
				req: submitExchangerOrderUserInputRequest,
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-1"}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:         "exchanger-order-1",
						ActorId:    "actor-id-1",
						State:      beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
						RewardType: beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().SubmitUserInputForChosenOption(context.Background(), &beExchangerPb.SubmitUserInputForChosenOptionRequest{
					ActorId:          "actor-id-1",
					ExchangerOrderId: "exchanger-order-1",
					ShippingAddress: &postaladdress.PostalAddress{
						AddressLines: []string{"line-1", "line-2"},
					},
				}).Return(&beExchangerPb.SubmitUserInputForChosenOptionResponse{
					Status:              rpc.StatusInternal(),
					ExchangerOfferOrder: nil,
				}, nil)
			},
			want: &fePb.SubmitExchangerOrderUserInputResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("error occurred while submitting user input for chosen option"),
					ErrorView: exchangerOrderUserInputDefaultErrorView,
				},
			},
			wantErr: false,
		},
		{
			name:   "should return StatusInternalWithDebugMsg when SubmitUserInputForChosenOption returns error",
			fields: fields{mockExchangerOfferClient: beMockExchangerOfferClient},
			args: args{
				ctx: context.Background(),
				req: submitExchangerOrderUserInputRequest,
			},
			setupMocks: func() {
				beMockExchangerOfferClient.EXPECT().GetExchangerOrderById(context.Background(), &beExchangerPb.GetExchangerOrderByIdRequest{ExchangerOrderId: "exchanger-order-1"}).Return(&beExchangerPb.GetExchangerOrderByIdResponse{
					Status: rpc.StatusOk(),
					ExchangerOrder: &beExchangerPb.ExchangerOfferOrder{
						Id:         "exchanger-order-1",
						ActorId:    "actor-id-1",
						State:      beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT,
						RewardType: beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE,
					},
				}, nil)
				beMockExchangerOfferClient.EXPECT().SubmitUserInputForChosenOption(context.Background(), &beExchangerPb.SubmitUserInputForChosenOptionRequest{
					ActorId:          "actor-id-1",
					ExchangerOrderId: "exchanger-order-1",
					ShippingAddress: &postaladdress.PostalAddress{
						AddressLines: []string{"line-1", "line-2"},
					},
				}).Return(nil, fmt.Errorf("error"))
			},
			want: &fePb.SubmitExchangerOrderUserInputResponse{
				RespHeader: &feHeaderPb.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg("error occurred while submitting user input for chosen option"),
					ErrorView: exchangerOrderUserInputDefaultErrorView,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			r := NewRewardsService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, tt.fields.mockExchangerOfferClient, nil, nil, dyconf, nil, tags.NewManager(dyconf), nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			got, err := r.SubmitExchangerOrderUserInput(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SubmitExchangerOrderUserInput() error = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			// validate ExchangerOrder
			if !proto.Equal(got.GetExchangerOrder(), tt.want.GetExchangerOrder()) {
				t.Errorf("SubmitExchangerOrderUserInput() got ExchangerOrder = %v, want ExchangerOrder = %v", got.GetExchangerOrder(), tt.want.GetExchangerOrder())
				return
			}

			// validate response header
			if !proto.Equal(got.GetRespHeader(), tt.want.GetRespHeader()) {
				t.Errorf("SubmitExchangerOrderUserInput() got ResponseHeader = %v, want ResponseHeader = %v", got.GetRespHeader(), tt.want.GetRespHeader())
				return
			}

			// validate CTA
			if !proto.Equal(got.GetCta(), tt.want.GetCta()) {
				t.Errorf("SubmitExchangerOrderUserInput() got CTA = %v, want CTA = %v", got.GetCta(), tt.want.GetCta())
				return
			}
		})
	}
}

func TestRewardService_GetRewardOfferDetails(t *testing.T) {
	type args struct {
		ctx context.Context
		req *fePb.GetRewardOfferDetailsRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockRewardOffersClient *mockRewardOffersPb.MockRewardOffersClient)
		want       *fePb.GetRewardOfferDetailsResponse
		wantErr    bool
	}{
		{
			name: "should return ISE rpc status code when rpc call to fetch rewardOffer fails",
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRewardOfferDetailsRequest{
					Req: &feHeaderPb.RequestHeader{
						Auth: &feHeaderPb.AuthHeader{
							ActorId: "actor-1",
							Device:  &commontypes.Device{Platform: commontypes.Platform_ANDROID},
						},
						AppVersionCode: 1001,
					},
					RewardOfferId: "reward-offer-id-1",
				},
			},
			setupMocks: func(mockRewardOffersClient *mockRewardOffersPb.MockRewardOffersClient) {
				mockRewardOffersClient.EXPECT().GetRewardOffersForActor(context.Background(), gomock.Any()).Return(&beRewardOffersPb.GetRewardOffersForActorResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want: &fePb.GetRewardOfferDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("rewardOffersClient.GetRewardOffersForActor call failed")},
			},
		},
		{
			name: "should return RecordNotFound rpc status code when no rewardOffer exists with given id",
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRewardOfferDetailsRequest{
					Req: &feHeaderPb.RequestHeader{
						Auth: &feHeaderPb.AuthHeader{
							ActorId: "actor-1",
							Device:  &commontypes.Device{Platform: commontypes.Platform_ANDROID},
						},
						AppVersionCode: 1001,
					},
					RewardOfferId: "reward-offer-id-1",
				},
			},
			setupMocks: func(mockRewardOffersClient *mockRewardOffersPb.MockRewardOffersClient) {
				mockRewardOffersClient.EXPECT().GetRewardOffersForActor(context.Background(), gomock.Any()).Return(&beRewardOffersPb.GetRewardOffersForActorResponse{
					Status:       rpc.StatusOk(),
					RewardOffers: []*beRewardOffersPb.RewardOffer{},
				}, nil)
			},
			want: &fePb.GetRewardOfferDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusRecordNotFound()},
			},
		},
		{
			name: "should return RecordNotFound rpc status code when action level inventory of rewardOffer already got exhausted",
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRewardOfferDetailsRequest{
					Req: &feHeaderPb.RequestHeader{
						Auth: &feHeaderPb.AuthHeader{
							ActorId: "actor-1",
							Device:  &commontypes.Device{Platform: commontypes.Platform_ANDROID},
						},
						AppVersionCode: 1001,
					},
					RewardOfferId: "reward-offer-id-1",
				},
			},
			setupMocks: func(mockRewardOffersClient *mockRewardOffersPb.MockRewardOffersClient) {
				mockRewardOffersClient.EXPECT().GetRewardOffersForActor(context.Background(), gomock.Any()).Return(&beRewardOffersPb.GetRewardOffersForActorResponse{
					Status: rpc.StatusOk(),
					RewardOffers: []*beRewardOffersPb.RewardOffer{
						{
							Id:          "reward-offer-id-1",
							DisplayMeta: &beRewardOffersPb.DisplayMeta{},
						},
					},
					ActionLevelRewardOfferInventory: []*beRewardOffersPb.RewardOfferInventory{
						{
							RewardOfferId: "reward-offer-id-1",
							// action level inventory of offer is exhausted
							RemainingCount: 0,
							TotalCount:     100,
						},
					},
				}, nil)
			},
			want: &fePb.GetRewardOfferDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusRecordNotFound()},
			},
		},
		{
			name: "should return OK status with reward offer details",
			args: args{
				ctx: context.Background(),
				req: &fePb.GetRewardOfferDetailsRequest{
					Req: &feHeaderPb.RequestHeader{
						Auth: &feHeaderPb.AuthHeader{
							ActorId: "actor-1",
							Device:  &commontypes.Device{Platform: commontypes.Platform_ANDROID},
						},
						AppVersionCode: 1001,
					},
					RewardOfferId: "reward-offer-id-1",
				},
			},
			setupMocks: func(mockRewardOffersClient *mockRewardOffersPb.MockRewardOffersClient) {
				mockRewardOffersClient.EXPECT().GetRewardOffersForActor(context.Background(), gomock.Any()).Return(&beRewardOffersPb.GetRewardOffersForActorResponse{
					Status: rpc.StatusOk(),
					RewardOffers: []*beRewardOffersPb.RewardOffer{
						{
							Id:          "reward-offer-id-1",
							DisplayMeta: &beRewardOffersPb.DisplayMeta{},
						},
					},
					ActionLevelRewardOfferInventory: []*beRewardOffersPb.RewardOfferInventory{
						{
							RewardOfferId:  "reward-offer-id-1",
							RemainingCount: 10,
							TotalCount:     100,
						},
					},
				}, nil)
			},
			want: &fePb.GetRewardOfferDetailsResponse{
				RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
				RewardOffer: &fePb.RewardOffer{
					Id:             "reward-offer-id-1",
					DisplayDetails: &fePb.DisplayDetails{},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			// init mocks
			mockRewardOffersClient := mockRewardOffersPb.NewMockRewardOffersClient(ctrl)

			tt.setupMocks(mockRewardOffersClient)

			r := &RewardService{
				dyconf:              dyconf,
				rewardsFrontendMeta: conf.RewardsFrontendMeta,
				rewardOffersClient:  mockRewardOffersClient,
			}
			got, err := r.GetRewardOfferDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRewardOfferDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetRewardOfferDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}
