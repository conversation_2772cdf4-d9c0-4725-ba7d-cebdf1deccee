package data

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	events2 "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feDePb "github.com/epifi/gamma/api/frontend/dynamic_elements"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/api/tiering/external"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pkg/tiering/amb"
	"github.com/epifi/gamma/frontend/tiering/amb/models"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
	tieringDeeplink "github.com/epifi/gamma/frontend/tiering/deeplink"
	"github.com/epifi/gamma/frontend/tiering/display_names"
	"github.com/epifi/gamma/frontend/tiering/events"
	"github.com/epifi/gamma/frontend/tiering/helper"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
)

// AMBDataProvider defines the interface for AMB data collection
type AMBDataProvider interface {
	// BuildAMBScreenData fetches and constructs all necessary data for the AMB screen
	BuildAMBScreenData(ctx context.Context, req *header.RequestHeader) (*models.AMBScreenData, error)
}

// AMBDataCollectorImpl implements AMBDataProvider
type AMBDataCollectorImpl struct {
	tieringClient         tiering.TieringClient
	dataCollector         tieringData.DataCollector
	tieringPinotClient    tieringPinotPb.EODBalanceClient
	config                *genconf.Config
	eventBroker           events2.Broker
	dynamicElementsClient feDePb.DynamicElementsClient
}

// NewAMBDataCollector creates a new instance of AMBDataCollectorImpl
func NewAMBDataCollector(
	tieringClient tiering.TieringClient,
	dataCollector tieringData.DataCollector,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	config *genconf.Config,
	eventBroker events2.Broker,
	dynamicElementsClient feDePb.DynamicElementsClient,
) AMBDataProvider {
	return &AMBDataCollectorImpl{
		tieringClient:         tieringClient,
		dataCollector:         dataCollector,
		tieringPinotClient:    tieringPinotClient,
		config:                config,
		eventBroker:           eventBroker,
		dynamicElementsClient: dynamicElementsClient,
	}
}

// BuildAMBScreenData fetches and calculates all the data needed for the AMB screen
func (c *AMBDataCollectorImpl) BuildAMBScreenData(ctx context.Context, req *header.RequestHeader) (*models.AMBScreenData, error) {
	actorID := req.GetAuth().GetActorId()
	appPlatform := epificontext.AppPlatformFromContext(ctx)
	// Get AMB data from the pkg function, which also includes tieringEssentials
	ambData, err := amb.GetAMBData(ctx, actorID, c.dataCollector, c.tieringPinotClient)
	if err != nil {
		return nil, fmt.Errorf("failed to get AMB data: %w", err)
	}

	// Get tiering essentials from ambData
	tieringEssentials := ambData.TieringEssentials
	if tieringEssentials == nil {
		return nil, fmt.Errorf("tiering essentials missing from AMB data")
	}

	// Get current tier information from tiering essentials
	currentTier := tieringEssentials.GetCurrentTier()

	// Get previous tier information from tiering essentials
	previousTier := tieringEssentials.GetPreviousTier()

	// Get tier change date
	tierChangeDate := c.formatTierChangeDate(tieringEssentials.GetLastMovementTimestamp())

	// Get AMB history
	historyEntries, err := c.getAMBHistory(ctx, actorID, tieringEssentials)
	if err != nil {
		logger.Error(ctx, "failed to get AMB history entries", zap.Error(err))
	}

	// Fetch dynamic elements for AMB_DETAILS_SCREEN
	dynamicBanner, err := c.fetchDynamicBanner(ctx, req)
	if err != nil {
		logger.Error(ctx, "failed to fetch dynamic banner", zap.Error(err))
	}

	// Get config values
	colors := c.config.Tiering().AMBScreen().Colors()
	textContent := c.config.Tiering().AMBScreen().TextContent()

	// Extract values from ambData
	currentAMB := money.ToDecimal(ambData.CurrentAMB).RoundCeil(0)
	targetAMB := money.ToDecimal(ambData.TargetAMB).RoundCeil(0)
	amountNeeded := money.ToDecimal(ambData.AmountNeeded).RoundCeil(0)
	progressPercentage := ambData.ProgressPercentage
	penaltyAmount := ambData.PenaltyAmount

	// Compute shouldAddBalance flag - using money.CompareV2 instead of float comparison
	shouldAddBalance, err := money.IsLessThan(ambData.CurrentAMB, ambData.TargetAMB)
	if err != nil {
		return nil, fmt.Errorf("failed to check if current amb is less than target: %w", err)
	}

	// Compute isShortfallAchievable flag
	maxShortfallAmount := money.ParseFloat(float64(c.config.Tiering().AMBScreen().MaxShortfallAmount()), money.RupeeCurrencyCode)
	if shortfallAmount, exists := c.config.Tiering().AMBScreen().TierShortfallAmountMap().Load(currentTier.String()); exists {
		maxShortfallAmount = money.ParseFloat(float64(shortfallAmount), money.RupeeCurrencyCode)
	}
	isShortfallAchievable, err := money.IsLessThan(ambData.AmountNeeded, maxShortfallAmount)
	if err != nil {
		return nil, fmt.Errorf("failed to check if amount is less than max shortfall: %w", err)
	}

	// Compute display names for tiers
	currentTierDisplayName, err := display_names.GetTitleCaseDisplayString(currentTier)
	if err != nil {
		return nil, fmt.Errorf("failed to get current tier display name: %w", err)
	}
	previousTierDisplayName, err := display_names.GetTitleCaseDisplayString(previousTier)
	if err != nil {
		// previous tier need not exist and is optional
		logger.Error(ctx, "failed to get previous tier display name", zap.Error(err))
	}

	// Compute color for current AMB and status banner
	var (
		currentAMBColor   string
		statusBannerColor string
		statusBannerText  string
		statusIconURL     string
	)
	switch {
	case !shouldAddBalance:
		currentAMBColor = colors.Green()
		statusBannerColor = colors.Green()
		statusBannerText = lo.Ternary(currentTier == external.Tier_TIER_FI_REGULAR, textContent.AMBMaintainedTextRegular(), textContent.AMBMaintainedText())
		statusIconURL = c.config.Tiering().AMBScreen().Images().WhiteCheck()
	case currentTier == external.Tier_TIER_FI_REGULAR:
		currentAMBColor = colors.Alert()
		statusBannerColor = colors.Alert()
		statusBannerText = lo.Ternary(isShortfallAchievable, textContent.PossibleChargeText(), textContent.AMBOutOfReachTextRegular())
		statusIconURL = c.config.Tiering().AMBScreen().Images().WarningError()
	default:
		currentAMBColor = colors.Warning()
		statusBannerColor = colors.Warning()
		statusBannerText = lo.Ternary(isShortfallAchievable, textContent.RewardsAtStakeText(), textContent.AMBOutOfReachText())
		statusIconURL = c.config.Tiering().AMBScreen().Images().WarningError()
	}

	// Compute color for amount needed
	amountNeededColor := lo.Ternary(currentTier == external.Tier_TIER_FI_REGULAR, colors.Alert(), colors.Warning())

	// Compute avoid text based on tier
	amountStatusText := lo.Ternary(currentTier == external.Tier_TIER_FI_REGULAR, textContent.AvoidChargesText(), textContent.AvoidLossOfRewardsText())

	// Get cashback percentage for tier from pkg function
	cashbackPercentage := ambData.RewardsAtStake

	// Compute disclaimer text
	disclaimerText := lo.Ternary(currentTier == external.Tier_TIER_FI_REGULAR,
		fmt.Sprintf(textContent.AMBPenaltyWarning(), formatCurrency(penaltyAmount), formatCurrency(ambData.TargetAMB)),
		fmt.Sprintf(textContent.AMBRewardsLossWarning(), cashbackPercentage))

	// Compute AMB on track section content
	ambOnTrackMainText := fmt.Sprintf(c.config.Tiering().AMBScreen().TextContent().AMBOnTrackText(), formatCurrency(ambData.CurrentAMB))
	ambOnTrackProTipText := c.config.Tiering().AMBScreen().TextContent().AMBOnTrackProTipText()
	ambOnTrackIconURL := c.config.Tiering().AMBScreen().Images().AMBOnTrackImagesMap().Get(currentTier.String())
	ambOnTrackLottieURL := c.config.Tiering().AMBScreen().Images().AMBOnTrackLottie()
	ambOnTrackTextColor := c.config.Tiering().AMBScreen().Colors().AMBOnTrackColorMap().Get(currentTier.String())
	if ambOnTrackTextColor == "" {
		ambOnTrackTextColor = colors.Text()
	}
	ambOnTrackProTipTextColor := colors.Content()

	// Compute tier badge image URL and gradient
	tierBadgeImageURL := tieringDeeplink.GetLaunchAnimationBadgeUrlV2(currentTier)
	tierBadgeGradient := tieringDeeplink.GetGradientTierColorV2(currentTier)
	progressBgGradient := tieringDeeplink.GetFadingGradientTierColor(currentTier, appPlatform)

	// Compute section visibility flags
	shouldShowAmountNeededSection := shouldAddBalance && isShortfallAchievable
	// we only show if there was a tier movement in last 7 days
	shouldShowAnnouncementSection := previousTier != external.Tier_TIER_UNSPECIFIED && time.Since(tieringEssentials.GetLastMovementTimestamp()).Hours() < 168

	ambRatio := calculateAMBRatio(ambData.CurrentAMB, ambData.TargetAMB)

	// even though these 2 flags are just the inverse of each other, their conditions can change over time so keeping both
	shouldShowAmbOnTrack := !shouldAddBalance && !lo.Contains([]external.Tier{external.Tier_TIER_FI_BASIC, external.Tier_TIER_FI_SALARY, external.Tier_TIER_FI_SALARY_BASIC, external.Tier_TIER_FI_REGULAR}, currentTier)
	shouldShowProgressBar := shouldAddBalance || currentTier == external.Tier_TIER_FI_REGULAR
	shouldShowNoAmbMessage := lo.Contains([]external.Tier{external.Tier_TIER_FI_BASIC, external.Tier_TIER_FI_SALARY, external.Tier_TIER_FI_SALARY_BASIC}, currentTier)
	shouldShowAmbOnTrackLottie := ambOnTrackLottieURL != "" && appPlatform != commontypes.Platform_IOS

	// Compute basic tier message
	noAmbMessage := "You don't have a minimum balance requirement on this plan"

	// Compute analytics properties
	baseAnalyticsProperties := map[string]string{
		"current_tier":     currentTier.String(),
		"target_AMB":       formatCurrency(ambData.TargetAMB),
		"amb_on_track":     strconv.FormatBool(!shouldAddBalance),
		"shortfall_amount": getShortfallBucket(amountNeeded.InexactFloat64()),
		"amb_status":       fmt.Sprintf("%.2f", ambRatio),
		"is_out_of_reach":  strconv.FormatBool(!isShortfallAchievable),
	}

	// Fire ProjectedAMBCalculationServer event which encapsulates AMB data and current status of user
	projectedAMBEvent := events.NewProjectedAMBCalculationServer(
		actorID,
		time.Now(),
		currentTier.String(),
		formatCurrency(ambData.TargetAMB),
		formatCurrency(ambData.CurrentAMB),
		formatCurrency(ambData.AmountNeeded),
		getShortfallBucket(amountNeeded.InexactFloat64()),
		strconv.FormatBool(!shouldAddBalance),
		fmt.Sprintf("%.2f", calculateAMBRatio(ambData.CurrentAMB, ambData.TargetAMB)),
	)
	goroutine.RunWithDefaultTimeout(epificontext.WithEventAttributesV2(ctx), func(gctx context.Context) {
		c.eventBroker.AddToBatch(gctx, projectedAMBEvent)
	})

	return &models.AMBScreenData{
		BannerCashbackPercent: cashbackPercentage,
		AmountNeeded:          money.ParseDecimal(amountNeeded, money.RupeeCurrencyCode),
		CurrentAMB:            money.ParseDecimal(currentAMB, money.RupeeCurrencyCode),
		TargetAMB:             money.ParseDecimal(targetAMB, money.RupeeCurrencyCode),
		PenaltyAmount:         formatCurrency(penaltyAmount),
		ProgressPercentage:    progressPercentage,
		AMBRatio:              ambRatio,
		CurrentTier:           currentTier,
		PreviousTier:          previousTier,
		TierChangeDate:        tierChangeDate,
		HistoryEntries:        historyEntries,
		ShouldAddBalance:      shouldAddBalance,
		IsShortfallAchievable: isShortfallAchievable,

		// Visual indicators
		CurrentAMBColor:   currentAMBColor,
		AmountNeededColor: amountNeededColor,
		ProgressBarColor:  currentAMBColor, // Same as CurrentAMBColor
		StatusBannerColor: statusBannerColor,

		// Display names
		CurrentTierDisplayName:  currentTierDisplayName,
		PreviousTierDisplayName: previousTierDisplayName,

		// Text content
		StatusBannerText: statusBannerText,
		AmountStatusText: amountStatusText,
		DisclaimerText:   disclaimerText,
		NoAmbMessage:     noAmbMessage,

		// AMB On Track section content
		AMBOnTrackMainText:        ambOnTrackMainText,
		AMBOnTrackProTipText:      ambOnTrackProTipText,
		AMBOnTrackIconURL:         ambOnTrackIconURL,
		AMBOnTrackLottieURL:       ambOnTrackLottieURL,
		AMBOnTrackTextColor:       ambOnTrackTextColor,
		AMBOnTrackProTipTextColor: ambOnTrackProTipTextColor,

		// Image URLs
		StatusIconURL:              statusIconURL,
		TierBadgeImageURL:          tierBadgeImageURL,
		TierBadgeGradient:          tierBadgeGradient,
		ProgressBackgroundGradient: progressBgGradient,
		DynamicBanner:              dynamicBanner,

		// Section visibility flags
		ShouldShowAmountNeededSection: shouldShowAmountNeededSection,
		ShouldShowAnnouncementSection: shouldShowAnnouncementSection,
		ShouldShowProgressBar:         shouldShowProgressBar,
		ShouldShowAMBOnTrackMessage:   shouldShowAmbOnTrack,
		ShouldShowNoAmbMessage:        shouldShowNoAmbMessage,
		ShouldShowAMBOnTrackLottie:    shouldShowAmbOnTrackLottie,

		// Analytics properties
		BaseAnalyticsProperties: baseAnalyticsProperties,
	}, nil
}

// formatTierChangeDate formats a time.Time as a user-friendly date string
func (c *AMBDataCollectorImpl) formatTierChangeDate(date time.Time) string {
	return date.Format("January 2")
}

// nolint:dupl
// getAMBHistory fetches AMB history for an actor
func (c *AMBDataCollectorImpl) getAMBHistory(ctx context.Context, actorID string, tieringEssentials *helper.TieringFeEssentials) ([]models.AMBHistoryEntry, error) {
	// Get current time in IST
	currentTime := time.Now().In(datetime.IST)

	// Calculate the start date - two months ago from current month start
	currentYear, currentMonth, _ := currentTime.Date()
	currentMonthStart := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentTime.Location())
	startDate := currentMonthStart.AddDate(0, -2, 0)

	// Get tier time ranges for the actor using the GetTierTimeRangesForActor API
	tierRangesResp, err := c.tieringClient.GetTierTimeRangesForActor(ctx, &tiering.GetTierTimeRangesForActorRequest{
		ActorId: actorID,
		Tiers:   []external.Tier{external.Tier_TIER_FI_INFINITE, external.Tier_TIER_FI_BASIC, external.Tier_TIER_FI_PLUS, external.Tier_TIER_FI_REGULAR, external.Tier_TIER_FI_AA_SALARY_BAND_3, external.Tier_TIER_FI_SALARY_BASIC, external.Tier_TIER_FI_SALARY},
		// Start date filter - only get periods that end after our start date
		FilterFrom: timestamppb.New(startDate),
	})

	if rpcErr := epifigrpc.RPCError(tierRangesResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to get tier time ranges", zap.Error(rpcErr))
		return nil, fmt.Errorf("failed to get tier time ranges: %w", rpcErr)
	}

	// Get the map of tier time ranges from the response
	tierTimeRangesMap := tierRangesResp.GetTierTimeRangesMap()

	// CLEANUP LATER: Start of mock data block for testing purposes
	if cfg.IsNonProdEnvBestEffort() && !cfg.IsTestEnv(c.config.Application().Environment) {
		logger.Warn("No tier time ranges found, using mock data for testing", zap.String(logger.ACTOR_ID, actorID))

		// Generate mock data for testing
		tierTimeRangesMap = make(map[string]*tiering.TimeRanges)

		// Create mock data for REGULAR tier
		regularTierRanges := &tiering.TimeRanges{
			TimeRanges: []*tiering.TimeRange{
				{
					FromTime: timestamppb.New(currentMonthStart.AddDate(0, -2, 0)),
					ToTime:   timestamppb.New(currentMonthStart.AddDate(0, -1, 0).Add(-time.Second)),
				},
			},
		}
		regularTierKey := external.Tier_TIER_FI_REGULAR.String()
		tierTimeRangesMap[regularTierKey] = regularTierRanges

		// Create mock data for PLUS tier
		plusTierRanges := &tiering.TimeRanges{
			TimeRanges: []*tiering.TimeRange{
				{
					FromTime: timestamppb.New(currentMonthStart.AddDate(0, -1, 0)),
					ToTime:   timestamppb.New(currentTime),
				},
			},
		}
		plusTierKey := external.Tier_TIER_FI_PLUS.String()
		tierTimeRangesMap[plusTierKey] = plusTierRanges
	}
	// CLEANUP LATER: End of mock data block

	if len(tierTimeRangesMap) == 0 {
		logger.Error(ctx, "no tier periods found for actor", zap.String("actorId", actorID))
		return nil, errors.New("no tier periods found for actor")
	}

	// Get target AMB values for different tiers
	tierToTargetAMB := make(map[external.Tier]float64)

	// For REGULAR tier
	regularTierMinAmb := tieringEssentials.GetGetConfigParamsResp().GetRegularTierConfigParams().GetMinBalanceForRegularTier()
	regularTierTargetAMB, _ := money.ToDecimal(regularTierMinAmb).Float64()
	tierToTargetAMB[external.Tier_TIER_FI_REGULAR] = regularTierTargetAMB

	// For other tiers, get the target AMB from tier criteria min values
	for tier, criteriaValues := range tieringEssentials.GetTierCriteriaMinValuesMap() {
		for _, criteriaValue := range criteriaValues {
			// Check only for balance criteria
			if criteriaValue.Criteria == tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC && criteriaValue.MinValue != nil {
				minValue, _ := money.ToDecimal(criteriaValue.MinValue).Float64()
				tierToTargetAMB[tier] = minValue
				break
			}
		}
	}

	// Process the tier time ranges to create monthly segments
	type monthSegment struct {
		startDate time.Time
		endDate   time.Time
		tier      external.Tier
	}

	var segments []monthSegment

	// Iterate through each tier in the map
	for tierStr, timeRanges := range tierTimeRangesMap {
		tier := external.Tier(external.Tier_value[tierStr])

		// Skip processing for tiers we don't care about
		if tier == external.Tier_TIER_UNSPECIFIED {
			continue
		}

		// Process each time range for this tier
		for _, timeRange := range timeRanges.GetTimeRanges() {
			periodStartTime := timeRange.GetFromTime().AsTime().In(datetime.IST)
			periodEndTime := timeRange.GetToTime().AsTime().In(datetime.IST)

			// Adjust start time to not go before our filter start date
			if periodStartTime.Before(startDate) {
				periodStartTime = startDate
			}

			// Adjust end time to not go beyond current time
			if periodEndTime.After(currentTime) {
				periodEndTime = currentTime
			}

			// Special handling for REGULAR tier - use whole calendar months
			if tier == external.Tier_TIER_FI_REGULAR {
				// For REGULAR tier, we want to use the whole calendar month
				// Get the start of the month containing periodStartTime
				regularStartMonth := time.Date(periodStartTime.Year(), periodStartTime.Month(), 1, 0, 0, 0, 0, periodStartTime.Location())

				// Get the end of the month containing periodEndTime
				// Move to the 1st of next month and subtract 1 second
				var regularEndMonth time.Time
				if periodEndTime.Month() == 12 {
					regularEndMonth = time.Date(periodEndTime.Year()+1, 1, 1, 0, 0, 0, 0, periodEndTime.Location()).Add(-time.Second)
				} else {
					regularEndMonth = time.Date(periodEndTime.Year(), periodEndTime.Month()+1, 1, 0, 0, 0, 0, periodEndTime.Location()).Add(-time.Second)
				}

				// Break down by month if period spans multiple months
				currentSegmentStart := regularStartMonth
				for currentSegmentStart.Before(regularEndMonth) || currentSegmentStart.Equal(regularEndMonth) {
					// Calculate the end of the current month
					var nextMonth time.Time
					if currentSegmentStart.Month() == 12 {
						nextMonth = time.Date(currentSegmentStart.Year()+1, 1, 1, 0, 0, 0, 0, currentSegmentStart.Location())
					} else {
						nextMonth = time.Date(currentSegmentStart.Year(), currentSegmentStart.Month()+1, 1, 0, 0, 0, 0, currentSegmentStart.Location())
					}
					segmentEnd := nextMonth.Add(-time.Second)

					// If segment end goes beyond regularEndMonth, cap it
					if segmentEnd.After(regularEndMonth) {
						segmentEnd = regularEndMonth
					}

					segments = append(segments, monthSegment{
						startDate: currentSegmentStart,
						endDate:   segmentEnd,
						tier:      tier,
					})

					// Move to next month
					currentSegmentStart = nextMonth

					// Break if we've reached or passed regularEndMonth
					if !currentSegmentStart.Before(regularEndMonth) {
						break
					}
				}
			} else {
				// For other tiers, break down by calendar month but use actual tier time range boundaries
				// Break down by month if period spans multiple months
				currentSegmentStart := periodStartTime
				for currentSegmentStart.Before(periodEndTime) {
					// Calculate the end of the current month
					var nextMonth time.Time
					if currentSegmentStart.Month() == 12 {
						nextMonth = time.Date(currentSegmentStart.Year()+1, 1, 1, 0, 0, 0, 0, currentSegmentStart.Location())
					} else {
						nextMonth = time.Date(currentSegmentStart.Year(), currentSegmentStart.Month()+1, 1, 0, 0, 0, 0, currentSegmentStart.Location())
					}
					segmentEnd := nextMonth.Add(-time.Second)

					// If segment end goes beyond period end, cap it
					if segmentEnd.After(periodEndTime) {
						segmentEnd = periodEndTime
					}

					segments = append(segments, monthSegment{
						startDate: currentSegmentStart,
						endDate:   segmentEnd,
						tier:      tier,
					})

					// Move to next month
					currentSegmentStart = nextMonth

					// Break if we've reached or passed periodEndTime
					if !currentSegmentStart.Before(periodEndTime) {
						break
					}
				}
			}
		}
	}

	// Sort segments by start date (most recent first)
	sort.Slice(segments, func(i, j int) bool {
		return segments[i].startDate.After(segments[j].startDate)
	})

	// remove first segment since this info is already captured in current AMB
	if len(segments) > 1 {
		segments = segments[1:]
	}

	if len(segments) == 0 {
		return nil, errors.New("no valid history segments found")
	}

	// Limit to 5 total segments to reduce total queries
	if len(segments) > 5 {
		segments = segments[:5]
	}

	// Create a wait group to synchronize goroutines
	var wg sync.WaitGroup
	// Create a mutex to protect concurrent writes to the history entries
	var mu sync.Mutex
	// Create a channel to collect errors
	errChan := make(chan error, len(segments))
	// Keep track of collected errors
	var collectedErrors []error

	// Prepare history entries
	historyEntries := make([]models.AMBHistoryEntry, len(segments))

	// Define colors
	const (
		colorRed   = "#D65779" // Red color for AMB below target
		colorBlack = "#313234" // Black color for AMB at or above target
	)

	// Process each segment in parallel
	for i, segment := range segments {
		wg.Add(1)

		// Create copies to avoid closure issues
		index := i
		timezoneStr := segment.startDate.Location().String()
		startDate := segment.startDate
		endDate := segment.endDate
		tier := segment.tier

		// Get target AMB for this tier, defaults to 0
		targetAMB := tierToTargetAMB[tier]

		// Format date range
		dateRange := fmt.Sprintf("%d %s – %d %s",
			startDate.Day(), startDate.Month().String()[:3],
			endDate.Day(), endDate.Month().String()[:3])

		tierName, tierErr := display_names.GetTitleCaseDisplayString(tier)
		if tierErr != nil {
			logger.Error(ctx, "error getting tier display name for history", zap.Error(tierErr))
		}
		// Pre-populate with date range and tier information
		historyEntries[index] = models.AMBHistoryEntry{
			DateRange: dateRange,
			Tier:      tierName,
			AMBColor:  colorBlack, // Default to black, will update in goroutine
		}

		// Launch a goroutine for each segment with a timeout
		goroutine.Run(ctx, 1*time.Second, func(ctx context.Context) {
			defer wg.Done()

			// Get average EOD balance for this segment
			eodBalanceResp, err := c.tieringPinotClient.GetAverageEODBalanceInDateRange(ctx, &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
				ActorId:       actorID,
				FromTimestamp: timestamppb.New(startDate),
				ToTimestamp:   timestamppb.New(endDate),
				Timezone:      timezoneStr,
			})

			// Default values
			avgBalance := 0.0

			// Process response
			if rpcErr := epifigrpc.RPCError(eodBalanceResp, err); rpcErr != nil {
				// Only use hardcoded values in staging environment
				if cfg.IsNonProdEnvBestEffort() {
					if tier == external.Tier_TIER_FI_REGULAR {
						avgBalance = 4859.4
					} else {
						avgBalance = 37034
					}
					logger.Warn("using hardcoded value for average EOD balance in non-prod", zap.String(logger.ACTOR_ID, actorID))
				} else {
					errChan <- fmt.Errorf("failed to get average EOD balance: %w", rpcErr)
					return
				}
			} else {
				avgBalance = eodBalanceResp.GetAvgBalance()
			}

			// Create money object for the average balance
			avgBalanceMoney := money.ParseFloat(avgBalance, money.RupeeCurrencyCode)
			// Create money object for the target AMB
			targetAMBMoney := money.ParseFloat(targetAMB, money.RupeeCurrencyCode)

			// Determine color based on comparison with target AMB
			color := colorBlack
			isLessThan, compErr := money.IsLessThan(avgBalanceMoney, targetAMBMoney)
			if compErr != nil {
				logger.Warn("failed to compare AMB values, defaulting to black color", zap.Error(err))
			} else if isLessThan {
				color = colorRed
			}

			// Update the history entry with mutex protection
			mu.Lock()
			historyEntries[index].AMBMaintained = money.ToDisplayStringInIndianFormat(avgBalanceMoney, 2, true)
			historyEntries[index].AMBColor = color
			mu.Unlock()
		})
	}

	// Wait for all goroutines to complete
	fetchedAllData := waitgroup.SafeWait(&wg, 2*time.Second)

	// Close the error channel now that all goroutines are done
	close(errChan)

	// Collect all errors from the channel
	for channelError := range errChan {
		if err != nil {
			collectedErrors = append(collectedErrors, channelError)
		}
	}

	if !fetchedAllData {
		// Log timeout error
		logger.Error(ctx, "Timeout occurred while fetching AMB history")
		return nil, errors.New("timeout while fetching AMB history")
	}

	// Check if there were any errors collected
	if len(collectedErrors) > 0 {
		// Log all collected errors
		for _, cerr := range collectedErrors {
			logger.Error(ctx, "Error occurred during AMB history fetch", zap.Error(cerr))
		}
		// Return a generic error indicating that some history fetches failed
		return nil, errors.New("failed to fetch some AMB history data")
	}

	return historyEntries, nil
}

// formatCurrency formats a Money object as a currency string
func formatCurrency(amount *moneyPb.Money) string {
	// Handle nil case
	if amount == nil {
		// Return zero amount formatted
		zeroAmount := money.ZeroINR().GetPb()
		return money.ToDisplayStringInIndianFormat(zeroAmount, 0, true)
	}

	// Use money package to format the currency
	return money.ToDisplayStringInIndianFormat(amount, 0, true)
}

// getShortfallBucket determines which bucket a shortfall amount falls into
func getShortfallBucket(amount float64) string {
	switch {
	case amount <= 0:
		return "0"
	case amount <= 1000:
		return "1 to 1000"
	case amount <= 3000:
		return "1000 to 3000"
	case amount <= 5000:
		return "3000 to 5000"
	case amount <= 10000:
		return "5001 to 10000"
	case amount <= 20000:
		return "10001 to 20000"
	case amount <= 40000:
		return "20001 to 40000"
	case amount <= 60000:
		return "40001 to 60000"
	case amount <= 80000:
		return "60001 to 80000"
	case amount <= 100000:
		return "80001 to 100000"
	case amount <= 200000:
		return "100001 to 200000"
	case amount <= 300000:
		return "200001 to 300000"
	case amount <= 500000:
		return "300001 to 500000"
	default:
		return "500001 or more"
	}
}

// calculateAMBRatio calculates the ratio of current AMB to target AMB
func calculateAMBRatio(currentAMB, targetAMB *moneyPb.Money) float64 {
	// Handle nil cases
	if currentAMB == nil || targetAMB == nil {
		return 0
	}

	// Get decimal values
	currentAMBDecimal := money.ToDecimal(currentAMB)
	targetAMBDecimal := money.ToDecimal(targetAMB)

	// Check if target is zero to avoid division by zero
	if targetAMBDecimal.IsZero() {
		return 0
	}

	// Calculate ratio
	return currentAMBDecimal.Div(targetAMBDecimal).InexactFloat64()
}

// fetchDynamicBanner fetches a dynamic banner for the AMB details screen
func (c *AMBDataCollectorImpl) fetchDynamicBanner(ctx context.Context, req *header.RequestHeader) (*models.DynamicBanner, error) {
	dynamicElementsRes, dynErr := c.dynamicElementsClient.FetchDynamicElements(ctx, &feDePb.FetchDynamicElementsRequest{
		Req:     req,
		ActorId: req.GetAuth().GetActorId(),
		ClientContext: &feDePb.ClientContext{
			ScreenName: deeplinkPb.Screen_AMB_DETAILS_SCREEN,
		},
	})

	if feErr := fepkg.FeRPCError(dynamicElementsRes, dynErr); feErr != nil {
		return nil, feErr
	}

	if len(dynamicElementsRes.GetElementsList()) > 0 {
		// Look for banner elements and use the first one
		for _, element := range dynamicElementsRes.GetElementsList() {
			if element.GetStructureType() == feDePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2 {
				bannerV2 := element.GetContent().GetBannerV2()
				if bannerV2 != nil && bannerV2.GetVisualElementFullBanner() != nil {
					return &models.DynamicBanner{
						Image:    bannerV2.GetVisualElementFullBanner(),
						Deeplink: bannerV2.GetDeeplink(),
					}, nil
				}
			}
		}
	}

	return nil, nil
}
