package user

import (
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	pb "github.com/epifi/gamma/api/frontend/user"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/alfred"
	authPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/auth"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

const (
	NameOnCard                       = "NAME ON CARD"
	PreferredName                    = "PREFERRED NAME"
	LegalName                        = "FULL LEGAL NAME"
	Email                            = "EMAIL ID"
	PhoneNumber                      = "PHONE NUMBER"
	PermanentAddress                 = "PERMANENT ADDRESS"
	CommunicationAddress             = "COMMUNICATION ADDRESS"
	DebitCardShippingAddress         = "DEBIT CARD DELIVERY ADDRESS"
	FiTnc                            = "Fi Terms & Conditions"
	FederalTnc                       = "Federal Bank Terms and Conditions"
	FiPrivacyPolicy                  = "Fi Privacy Policy"
	FiWealthPolicy                   = "epiFi Wealth Terms & Conditions"
	FederalLoansTnc                  = "Federal bank - Personal Loans Terms and conditions"
	LiquiloansLoansTnc               = "Liquiloans - Personal Loans Terms and Conditions"
	OpenSourceLicenses               = "Open Source licenses"
	ChromeWebView                    = "ChromeWebView"
	Cronet                           = "Cronet"
	Firebase                         = "Firebase"
	EmploymentType                   = "EMPLOYMENT TYPE"
	CompanyName                      = "EMPLOYER NAME"
	EmploymentDetailsTitle           = "EMPLOYMENT DETAILS"
	NomineeDetailsTitle              = "NOMINEE DETAILS"
	NomineeName                      = "NOMINEE NAME"
	NomineeRelationship              = "RELATIONSHIP"
	NomineeDob                       = "DATE OF BIRTH"
	NomineeAddress                   = "NOMINEE'S ADDRESS"
	ContactDetailsTitle              = "CONTACT DETAILS"
	PersonalDetailsTitle             = "PERSONAL DETAILS"
	AnnualIncome                     = "ANNUAL INCOME"
	AnnualIncomeAbsolute             = "ANNUAL INCOME"
	UpiPaymentEnabled                = "UPI Payment Enabled"
	Inactive                         = "Inactive"
	Active                           = "Active"
	PrimaryAccount                   = "Primary Account"
	SetUpiNumber                     = "Set UPI number"
	CreateCustomUpiNumber            = "Create a custom UPI Number"
	Occupation                       = "OCCUPATION"
	investmentAdvisoryAgreementTitle = "Epifi Wealth Investment Advisory Agreement"
	// this prefix needs to be added to any links with pdf docs for android to display it in the app
	embeddedUrlPrefixForPdfs                        = "https://docs.google.com/gview?embedded=true&"
	USStocksUseAndRisk                              = "US Stocks - Use and Risk"
	USStocksTnc                                     = "US Stocks - Terms and Conditions"
	USStocksPrivacyNotice                           = "US Stocks - Privacy Notice"
	USStocksPFOF                                    = "US Stocks - PFOF"
	USStocksMarginDiscStmt                          = "US Stocks - Margin Disclosure Statement"
	USStocksExtHrsRisk                              = "US Stocks - Extended Hours Trading Risk"
	USStocksBCPSummary                              = "US Stocks - Business Continuity Plan Summary"
	USStocksFormCRS                                 = "US Stocks - Form CRS"
	SalaryAccountTnc                                = "Salary Account Terms & Conditions"
	TrueString                                      = "TRUE"
	FalseString                                     = "FALSE"
	EnableString                                    = "ENABLE"
	DisableString                                   = "DISABLE"
	CTACall                                         = "Call us"
	CTAChat                                         = "Chat with us"
	CTAHeaderCX                                     = "Need further assistance?"
	AccValidatedTitle                               = "You’ll receive your funds in 14 business days"
	AccValidatedSubtitle                            = "Your account is closed & your remaining balance will be transferred to the account below <font color=#00B899>Know more</font>"
	KnowMoreAccClosedTitle                          = "Why did my account close?"
	KnowMoreAccClosedDetails                        = "As per bank guidelines, failure to complete your Video KYC call within %v days has resulted in your account being permanently closed.\n\nYour money should be credited to your account in 14 business days. "
	AccClosedContactCXTitle                         = "Your bank account is closed due to incomplete KYC"
	AccClosedContactCXSubtitle                      = "As per bank guidelines, your failure to complete your Video KYC call within %v days has resulted in your account being permanently closed.\n\nTo transfer your money out of your Federal savings account, you can contact our customer <NAME_EMAIL>"
	AccountNameDoesntMatch                          = "Account name doesn’t match"
	AccountNameDoesntMatchReasonSubtitle            = "Please share your own account details that has the same name as your current Federal savings account."
	AccountNumberSameTitle                          = "Oops! You shared the same account number"
	AccountNumberSameSubtitle                       = "Please share a different account number to transfer money out of your Federal savings account."
	TransactionFailed                               = "Transaction failed"
	TransactionFailedReasonSubtitle                 = "Uh-oh!  We couldn’t validate the account details you shared. Please re-check the details and retry."
	NameMismatchInlineError                         = "This name doesn’t match the name on your Federal savings account"
	GenericErrorTitile                              = "Oops! Something went wrong"
	GenericErrorSubtitle                            = "Please contact us to report the issue"
	MOTHER_FATHER_NAME_MATCH                        = "MOTHER_FATHER_NAME_MATCH"
	MOTHER_KYC_NAME_MATCH                           = "MOTHER_KYC_NAME_MATCH"
	FATHER_KYC_NAME_MATCH                           = "FATHER_KYC_NAME_MATCH"
	AccountValidationInProgressTitle                = "Account validation already in progress for this account"
	AccountValidationInProgressSubTitle             = "Please try again in 2 minutes"
	IntersetCertificateTitle                        = "Interest Certificate"
	InterestCertificateSubtitle                     = "Includes interest information for both deposits and savings account"
	IntersetCertificateCopyBoxTitle                 = "You’ll be required to enter your Federal Savings Account number in the next step. Get it from here."
	ReportsAndDownloadsPageHeading                  = "Reports and Downloads"
	ColorCodeNight                                  = "#333333"
	ColorCodeGraySteel                              = "#A4A4A4"
	ReportsAndDownloadsSavingsAccountSectionHeading = "Savings Account"
	ColorCodeIvory                                  = "#F7F9FA"
	IntersetCertificateWebLink                      = "https://accountopen.federalbank.co.in/CertificatePortal/Index"
	ProfileLandingCtaIconUrl                        = "https://epifi-icons.pointz.in/onboarding/profileLandingCta"
	ConnectedAccountDisconnectedText                = "This account is disconnected"
	ConnectedAccountDisconnectedBgColor             = "#FFFFFF"
	ConnectedAccountDisconnectedFontColor           = "#FA3B11"
	ConnectedAccountDataSyncOffText                 = "Data syncing is off"
	ConnectedAccountDataSyncOffBgColor              = "#FF7B31"
	ConnectedAccountDataSyncOffFontColor            = "#FFFFFF"
	EmptyString                                     = ""
	verifyIdentityPngUrl                            = "https://epifi-icons.pointz.in/profileUpdate/verifyIdentity"
	phoneEmailUpdateNotAllowedTitle                 = "Uh-oh, phone number or email change is not permitted"
	phoneEmailUpdateNotAllowedSubtitle              = "Some of the products and services your are using currently do not support a phone number or email change."
	PanSameTitle                                    = "Oops! You shared the same pan as the user"
	PanSameSubtitle                                 = "Please provide PAN for the nominee"
	DocumentNotProvidedTitle                        = "Oops! You haven't shared nominee document"
	DocumentNotProvidedSubtitle                     = "Please provide nominee document details like PAN or Aadhaar or Driving license"
	IncorrectDocumentTitle                          = "Oops! You shared invalid document"
	IncorrectPanSubTitle                            = "Please provide valid Pan number"
	IncorrectAadhaarSubTitle                        = "Please provide valid aadhaar number"
	IncorrectDrivingLicenseSubTitle                 = "Please provide valid driving license number"
)

var (
	AccountValidationFailedDl = &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &deeplinkPb.DetailedErrorViewScreenOptions{
				Title:    AccClosedContactCXTitle,
				Subtitle: fmt.Sprintf(AccClosedContactCXSubtitle, vkycPkg.AccountClosureDaysLimit),
				ImageUrl: "https://epifi-icons.pointz.in/onboarding/account_closure.png",
			},
		},
	}
	GenericFailureErrView = &errorsPb.ErrorView{
		Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
		Options: &errorsPb.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
				ErrorCode:   "",
				Title:       GenericErrorTitile,
				Subtitle:    GenericErrorSubtitle,
				Description: "",
				Ctas:        []*errorsPb.CTA{{Type: errorsPb.CTA_RETRY, Text: "Retry"}},
			},
		},
	}
	bottomSheetAddressUpdateErrorView = &errorsPb.ErrorView{
		Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
		Options: &errorsPb.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
				Title:    "We faced a server error",
				Subtitle: "Uh-oh! We faced a server error while updating your address. Please try again.",
				Ctas: []*errorsPb.CTA{
					{
						Type: errorsPb.CTA_RETRY,
						Text: "Retry",
					},
				},
			},
		},
	}
	AccountValidationInProgressErrView = &errorsPb.ErrorView{
		Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
		Options: &errorsPb.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
				ErrorCode:   "",
				Title:       AccountValidationInProgressTitle,
				Subtitle:    AccountValidationInProgressSubTitle,
				Description: "",
			},
		},
	}

	MyProfileSavingsAccountInfoSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Savings Account Info", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("View all account related information and bank statement", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/Profile/SavingAccountInfo/Icon.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PRIMARY_SAVINGS_SUMMARY_SCREEN,
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}

	MyProfileAboutMeSubSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("About me", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("View personal, contact and employment details", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/aboutMeIcon.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PROFILE_PERSONAL_DETAILS,
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}
	MyProfileSettingsSubSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Settings", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("Manage notifications and More ", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/settingsIcon.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PROFILE_SETTINGS,
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}
	MyProfileMcpCodeSubSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Fi MCP", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("Your FI MCP code ", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/networth/mcp/profile_icon.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_AUTH_TOTP,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&authPb.AuthTotpScreenOptions{
					TitleBar: &ui.HeaderBar{
						CenterItc: ui.NewITC().
							WithTexts(getTextByFontColorAndStyle("Fi MCP", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M)).
							WithLeftVisualElement(
								commontypes.GetVisualElementFromUrlHeightAndWidth(
									"https://epifi-icons.s3.ap-south-1.amazonaws.com/networth/mcp/fi_logo.png", 28, 28,
								),
							).
							WithLeftImagePadding(8),
						LeftItc: ui.NewITC().
							WithLeftVisualElement(
								commontypes.GetVisualElementFromUrlHeightAndWidth(
									"https://epifi-icons.pointz.in/savingsAccountClosure/back-icon.png", 28, 28,
								),
							),
					},
					MainLogo:      commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/networth/mcp/mcp_key.png", 80, 80),
					CodeTitleText: getTextByFontColorAndStyle("Your Fi MCP code", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_L),
					CodeText:      getTextByFontColorAndStyle("------", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_4XL),
					ExpiryText:    getTextByFontColorAndStyle("Generating code", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_S),
					BgColour:      widget.GetBlockBackgroundColour(colors.ColorDarkBase),
				}),
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}
	MyProfilePrivacyAndSecuritySubSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Privacy & Security", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("Control how your information is protected", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/securityIcon.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PROFILE_PRIVACY_SCREEN,
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}
	MyProfileReportsAndDownloadsSubsection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Reports and Downloads", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("Get your taxation related documents", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/reportsAndDownloads.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_REPORTS_AND_DOWNLOADS_SCREEN,
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}
	MyProfileChequeBookSubSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Chequebook Info", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("Request Tax Statement ELSS", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/serviceRequest.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_ALFRED_REQUEST_CHOICE,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
					&alfred.AlfredRequestChoicesScreenOptions{
						ChoicesFilter: []byte("{\"bundleTypes\":[\"RequestBundleType_REQUEST_BUNDLE_TYPE_CHEQUEBOOK\"]}"),
					},
				),
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}
	MyProfileAddressUpdateSubSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Address Update", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("Request for address update", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/serviceRequest.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_ALFRED_REQUEST_CHOICE,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
					&alfred.AlfredRequestChoicesScreenOptions{
						ChoicesFilter: []byte("{\"bundleTypes\":[\"REQUEST_BUNDLE_TYPE_PROFILE_UPDATE\"]}"),
					},
				),
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}
	MyProfileUSStocksSubSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("US Stocks Documents", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("Request US Stocks Documents", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/serviceRequest.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_ALFRED_REQUEST_CHOICE,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
					&alfred.AlfredRequestChoicesScreenOptions{
						ChoicesFilter: []byte("{\"bundleTypes\":[\"REQUEST_BUNDLE_TYPE_USS_DOCUMENTS\"]}"),
					},
				),
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}
	MyProfileRewardsAndOffersSubSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Rewards & Offers", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("Earn rewards and collect epic offers", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/rewardsAndOffersIcon.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
			},
			IconUrl: ProfileLandingCtaIconUrl,
		},
	}

	requestTypeToMyProfileContactInfoEditDLContent = map[string]*deeplinkPb.InfoAcknowledgementScreenOptions_ScreenContentTheme1{
		PhoneNumber: {
			TitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Update your mobile number", "#333333", commontypes.FontStyle_HEADLINE_L),
			ScreenImage: commontypes.GetVisualElementImageFromUrl(verifyIdentityPngUrl).WithProperties(&commontypes.VisualElementProperties{
				Width:  216,
				Height: 216,
			}).WithImageType(commontypes.ImageType_PNG),
			Body: &deeplinkPb.InfoAcknowledgementScreenOptions_Body{
				BodyInstructionType: deeplinkPb.InfoAcknowledgementScreenOptions_BODY_INSTRUCTION_TYPE_ORDERED,
				BodyInstructions: []string{
					"<b>Tap on the button below to log out & then log in with the new number</b>",
					"<b>Verify your details & wait for partner bank to update your number (takes 2-3 hrs)</b>",
					"<b>That’s it! You can continue using Fi</b>",
				},
			},
			CheckboxText: "My new mobile number is linked to Aadhaar & is present on this device",
			HeaderBar: &deeplinkPb.HeaderBar{
				BackAction: &deeplinkPb.BackAction{
					ShowButton: commontypes.BooleanEnum_TRUE,
				},
			},
		},
		Email: {
			TitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Update your email", "#333333", commontypes.FontStyle_HEADLINE_L),
			ScreenImage: commontypes.GetVisualElementImageFromUrl(verifyIdentityPngUrl).WithProperties(&commontypes.VisualElementProperties{
				Width:  216,
				Height: 216,
			}).WithImageType(commontypes.ImageType_PNG),
			Body: &deeplinkPb.InfoAcknowledgementScreenOptions_Body{
				BodyInstructionType: deeplinkPb.InfoAcknowledgementScreenOptions_BODY_INSTRUCTION_TYPE_ORDERED,
				BodyInstructions: []string{
					"<b>Tap on the button below to log out</b>",
					"<b>Log in to Fi again and add your new email address</b>",
					"<b>That’s it! You can continue using Fi</b>",
				},
			},
			HeaderBar: &deeplinkPb.HeaderBar{
				BackAction: &deeplinkPb.BackAction{
					ShowButton: commontypes.BooleanEnum_TRUE,
				},
			},
		},
	}

	requestTypeToMyProfilePersonalInfoEditDLCta = map[string][]*deeplinkPb.Cta{
		PhoneNumber: {
			{
				Type:         deeplinkPb.Cta_LOGOUT,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Text:         "Log out to update number",
			},
		},
		Email: {
			{
				Type:         deeplinkPb.Cta_CUSTOM,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_EMAIL_VERIFICATION,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&authPb.EmailVerificationScreenOptions{
						EntryPoint: authPb.EmailVerificationScreenOptions_ENTRY_POINT_PROFILE_EMAIL_UPDATE,
					}),
				},
				Text: "Log out to update email",
			},
		},
	}
)

var AccountValidationFailureReasonBeToFeMap = map[extacct.FailureReason]pb.AccountValidationFailureReason{
	extacct.FailureReason_FAILURE_REASON_UNSPECIFIED:               pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_UNSPECIFIED,
	extacct.FailureReason_FAILURE_REASON_API_TIMEOUT:               pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_API_TIMEOUT,
	extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH:  pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH,
	extacct.FailureReason_FAILURE_REASON_NAME_AT_BANK_MISMATCH:     pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_NAME_AT_BANK_MISMATCH,
	extacct.FailureReason_FAILURE_REASON_INVALID_IFSC:              pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_INVALID_IFSC,
	extacct.FailureReason_FAILURE_REASON_INVALID_ACCOUNT_NUMBER:    pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_INVALID_ACCOUNT_NUMBER,
	extacct.FailureReason_FAILURE_REASON_ACCOUNT_CLOSED:            pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_ACCOUNT_CLOSED,
	extacct.FailureReason_FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK: pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK,
	extacct.FailureReason_FAILURE_REASON_UNKNOWN:                   pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_REASON_UNKNOWN,
	extacct.FailureReason_FAILURE_REASON_SAME_ACCOUNT_NUMBER:       pb.AccountValidationFailureReason_ACCOUNT_VALIDATION_FAILURE_SAME_ACCOUNT_NUMBER,
}

var errorCodeToNameMatchResultMap = map[string]string{
	"613": MOTHER_FATHER_NAME_MATCH,
	"614": MOTHER_KYC_NAME_MATCH,
	"615": FATHER_KYC_NAME_MATCH,
}
