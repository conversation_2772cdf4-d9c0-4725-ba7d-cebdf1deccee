package user

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	consentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/frontend/smsscanner"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	gammanames "github.com/epifi/gamma/pkg/names"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	caEnums "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/connected_account/external"
	feAccounts "github.com/epifi/gamma/api/frontend/account"
	"github.com/epifi/gamma/api/frontend/analytics"
	pb "github.com/epifi/gamma/api/frontend/user"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upiPb "github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/onboarding"
	bePb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/pkg/feature/release"
	headerPkg "github.com/epifi/gamma/pkg/frontend/header"
)

// GetUserSessionDetails syncs the data between client and server on session start of the client
// will be called per session by the client
func (s *Service) GetUserSessionDetails(ctx context.Context, req *pb.GetUserSessionDetailsRequest) (*pb.GetUserSessionDetailsResponse, error) {
	res, err := s.clientCalls(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAppVersionCode(), req.GetReq().GetAuth().GetDevice().GetPlatform())
	if err != nil {
		logger.Error(ctx, "faced error while doing client calls", zap.Error(err))
		return &pb.GetUserSessionDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return res, nil
}

// nolint: funlen
func (s *Service) clientCalls(ctx context.Context, actorId string, appVersionCode uint32, appPlatform commontypes.Platform) (*pb.GetUserSessionDetailsResponse, error) {
	var (
		// responses
		actorRes          *actorPb.GetActorByIdResponse
		onboardingRes     *onbPb.GetDetailsResponse
		getFeatDetailsRes *onbPb.GetFeatureDetailsResponse
		groupResp         *userGroupPb.GetGroupsMappedToEmailResponse
		pinStatusRes      *upiPb.PinStatusResponse
		userRes           *bePb.GetUserResponse
		vmResp            *vmPb.GetBEMappingByIdResponse
		// final properties
		cardIds                []string
		displayName            *commontypes.Name
		firebaseUserProperties map[string]string
		featureMap             map[string]*pb.FeatureInfo
		mergedAccounts         []*feAccounts.Account
		// intermediate variables
		ccAccount         *feAccounts.Account
		tpapAccounts      []*onboarding.UpiAccount
		connectedAccounts []*external.AccountDetails
		msClarityConfig   *analytics.MsClarityConfig
		scienapticConfig  *smsscanner.ScienapticSmsScannerConfig
	)

	g, _ := errgroup.WithContext(ctx)

	// get user info
	userRes, err := s.client.GetUser(ctx, &bePb.GetUserRequest{
		Identifier: &bePb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching user details", zap.Error(rpcErr))
		return nil, rpcErr
	}
	g.Go(func() error {
		var gErr error
		onboardingRes, gErr = s.onboardingClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
			ActorId:    actorId,
			CachedData: true,
		})
		if rpcErr := epifigrpc.RPCError(onboardingRes, gErr); rpcErr != nil {
			logger.Error(ctx, "error while fetching onboarding details", zap.Error(rpcErr))
		}
		return nil
	})
	g.Go(func() error {
		var gErr error
		getFeatDetailsRes, gErr = s.onboardingClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(getFeatDetailsRes, gErr); rpcErr != nil {
			logger.Error(ctx, "error while fetching feature details", zap.Error(rpcErr))
		}

		return nil
	})
	// fetch display name from profile
	g.Go(func() error {
		displayName = gammanames.BestNameFromProfile(ctx, userRes.GetUser().GetProfile())
		return nil
	})
	// fetch firebase id
	g.Go(func() error {
		var gErr error
		vmResp, gErr = s.vendorMappingClient.GetBEMappingById(ctx, &vmPb.GetBEMappingByIdRequest{
			Id: actorId,
		})
		if rpcErr := epifigrpc.RPCError(vmResp, gErr); rpcErr != nil {
			logger.Error(ctx, "error while fetching firebase user id", zap.Error(rpcErr))
			// not mandatory, so don't return an error
		}
		return nil
	})
	// get user groups
	groupResp, err = s.userGroupClient.GetGroupsMappedToEmail(ctx, &userGroupPb.GetGroupsMappedToEmailRequest{
		Email: userRes.GetUser().GetProfile().GetEmail(),
	})
	if rpcErr := epifigrpc.RPCError(groupResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching group details", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, rpcErr
	}
	// get firebase user properties and credit card account
	g.Go(func() error {
		var gErr error
		firebaseUserProperties, gErr = s.getFirebaseUserProperties(ctx, actorId, appVersionCode, groupResp.GetGroups())
		if gErr != nil {
			logger.Error(ctx, "error while fetching firebase user properties", zap.Error(gErr))
			// not returning error here so rest of the flow should work as expected
			return nil
		}
		bcResp, errResp := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankcust.GetBankCustomerRequest_ActorId{
				ActorId: actorId,
			},
		})
		if gErr = epifigrpc.RPCError(bcResp, errResp); gErr != nil && !rpc.StatusFromError(gErr).IsRecordNotFound() {
			logger.Error(ctx, "error while fetching bank customer", zap.Error(gErr))
			return gErr
		}
		// defaulting the cc enabled to true so that all the users are able to see the cc tab .
		// In case the user is ineligible, they will see the waitlist intro screen
		firebaseUserProperties[pb.FirebaseProperty_IS_CREDIT_CARD_ENABLED.String()] = TrueString

		if getFeatDetailsRes.GetIsFiLiteUser() {
			firebaseUserProperties[pb.FirebaseProperty_IS_FI_LITE_USER.String()] = TrueString
		}

		if bcResp.GetBankCustomer().GetVendorCustomerId() == "" {
			logger.Info(ctx, "skipping cc account fetch since customer id does not exist")
			return nil
		}
		ccAccount, gErr = s.getCreditCardAccount(ctx, actorId, bcResp.GetBankCustomer().GetVendorCustomerId())
		if gErr != nil {
			return gErr
		}
		return nil
	})
	// get the feature map config
	g.Go(func() error {
		var gErr error
		featureMap, gErr = s.getFeatureMapConfig(ctx, actorId, groupResp.GetGroups())
		if gErr != nil {
			logger.Error(ctx, "error while fetching feature map", zap.Error(gErr))
			return gErr
		}
		return nil
	})

	g.Go(func() error {
		var gErr error
		tpapAccounts, gErr = s.getTPAPAccounts(ctx, actorId)
		if gErr != nil {
			logger.Error(ctx, "error fetching tpap accounts list", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(gErr))
			return gErr
		}
		return nil
	})

	g.Go(func() error {
		var gErr error
		connectedAccounts, gErr = s.getConnectedAccounts(ctx, actorId, defaultConnectedAccountFilters, []caEnums.AccInstrumentType{
			caEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
			caEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_TERM_DEPOSIT,
			caEnums.AccInstrumentType_ACC_INSTRUMENT_TYPE_RECURRING_DEPOSIT})
		if gErr != nil {
			logger.Error(ctx, "error fetching connected accounts list", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(gErr))
			return gErr
		}
		return nil
	})

	g.Go(func() error {
		evaluateRes, evaluateErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_SMS_PARSER_PARTNER_SDK).WithActorId(actorId))
		if evaluateErr != nil {
			// logging error and returning directly without setting scienaptic config so that client doesn't update the last used config
			logger.Error(ctx, "error in release evaluator for feature FEATURE_SMS_PARSER_PARTNER_SDK", zap.Error(evaluateErr))
			return nil
		}
		if !evaluateRes {
			// disabling sdk if release evaluator returns false
			scienapticConfig = &smsscanner.ScienapticSmsScannerConfig{
				EnableSdk:   false,
				PhoneNumber: userRes.GetUser().GetProfile().GetPhoneNumber().ToStringNationalNumber(),
			}
			return nil
		}

		fetchConsentRes, fetchConsentErr := s.consentClient.FetchConsent(ctx, &consentPb.FetchConsentRequest{
			ConsentType: consentPb.ConsentType_CONSENT_SMS_DATA_PROCESSING_CONSENT,
			ActorId:     actorId,
			Owner:       commontypes.Owner_OWNER_EPIFI_WEALTH,
		})
		if rpcErr := epifigrpc.RPCError(fetchConsentRes, fetchConsentErr); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
			// logging error and returning directly without setting scienaptic config so that client doesn't update the last used config
			logger.Error(ctx, "error while fetching consent for scienaptic sms scanner", zap.Error(rpcErr))
			return nil
		}

		// enable sdk if either the user belongs in SMS_PARSER_INTERNAL group or has given consent
		enableSdk := lo.Contains(groupResp.GetGroups(), commontypes.UserGroup_SMS_PARSER_INTERNAL) || !fetchConsentRes.GetStatus().IsRecordNotFound()

		scienapticConfig = &smsscanner.ScienapticSmsScannerConfig{
			EnableSdk:   enableSdk,
			PhoneNumber: userRes.GetUser().GetProfile().GetPhoneNumber().ToStringNationalNumber(), // todo: pass country code along with phone number
		}
		return nil
	})

	// Check if user is eligible for new home design feature
	var isFeatureHomeDesignEnhancementsEnabled = false
	g.Go(func() error {
		isFeatureHomeDesignEnhancementsEnabled = featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            s.releaseEvaluator,
				OnboardingClient:     s.onboardingClient,
				QuestSdkClient:       s.questSdkClient,
				UserAttributeFetcher: s.userAttrFetcher,
				NetWorthClient:       s.networthClient,
			},
		})
		return nil
	})

	// Fetch all the savings account for the user, i.e.
	// If resident, the user will have only 1 account.
	// If non-resident, the user can have multiple accounts (NRE+NRO).
	//
	// Though, this can extend tomorrow to multi-vendor accounts as well.
	accountsList, err := s.dataCollector.GetSavingsAccounts(ctx, actorId)
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error fetching savings accounts", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return nil, err
		}

		if err = g.Wait(); err != nil {
			logger.Error(ctx, "error in client calls for user session details", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, err
		}

		if mergedAccounts == nil {
			mergedAccounts = make([]*feAccounts.Account, 0)
		}
		if ccAccount != nil {
			mergedAccounts = append(mergedAccounts, ccAccount)
		}

		mrgdAccounts, mergeErr := s.getMergedAccounts(ctx, nil, connectedAccounts, tpapAccounts,
			pinStatusRes.GetAccountPinStateMap(), appVersionCode, pb.EntryPoint_ENTRY_POINT_USER_SESSION_DETAILS,
		)
		if mergeErr != nil {
			logger.Error(ctx, "error in merging all the accounts", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		}

		if len(mrgdAccounts) != 0 {
			mergedAccounts = append(mergedAccounts, mrgdAccounts...)
		}

		shouldOverrideAccountControlFlags, overrideAccControlErr := s.isFeatureEnabled(ctx, actorId, types.Feature_ALLOW_AND_CONTROL_AA_TXN_FOR_TPAP)
		if overrideAccControlErr != nil {
			logger.WarnWithCtx(ctx, "error in overriding Aa txn control flag to true. handling gracefully", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Error(overrideAccControlErr))
		}
		if shouldOverrideAccountControlFlags && overrideAccControlErr == nil {
			s.OverrideAccountControlFlags(mergedAccounts)
		}

		featureMap[types.Feature_FEATURE_DC_DASHBOARD_V2_SCREEN.String()] = &pb.FeatureInfo{
			Enable: s.isDcDashboardV2Enabled(ctx, featureMap[types.Feature_FEATURE_DC_DASHBOARD_V2_SCREEN.String()].GetEnable(), getFeatDetailsRes.GetIsFiLiteUser(), appPlatform),
		}

		// Update the state of Home Design feature flag
		featureMap[types.Feature_FEATURE_HOME_DESIGN_ENHANCEMENTS.String()] = &pb.FeatureInfo{
			Enable: isFeatureHomeDesignEnhancementsEnabled,
		}

		// fill properties in case savings account is not found
		return &pb.GetUserSessionDetailsResponse{
			Status:                     rpc.StatusOk(),
			RespHeader:                 headerPkg.SuccessRespHeader(),
			FeatureMap:                 featureMap,
			Accounts:                   mergedAccounts,
			FirebaseUserProperties:     firebaseUserProperties,
			FirebaseId:                 vmResp.GetFcmId(),
			MsClarityId:                vmResp.GetMsClarityId(),
			DisplayName:                displayName,
			IsFiLiteUser:               getFeatDetailsRes.GetIsFiLiteUser(),
			IsHomeAccessible:           onboardingRes.GetDetails().GetCompletedAt() != nil || getFeatDetailsRes.GetIsFiLiteUser(),
			CurrentFeature:             onboardingRes.GetDetails().GetStageMetadata().GetIntentSelectionMetadata().GetSelection().String(),
			ScienapticSmsScannerConfig: scienapticConfig,
			CardTabs:                   getCardTabs(),
		}, nil
	}

	// Check if VPA is created for the accounts or not. If not, initiate them concurrently.
	//
	// This will still remain a single iteration call for the majority of the user-base.
	// Only in case of Non-Residents who have two accounts (i.e. NRE+NRO), this will be a multi-iteration call.
	//
	// todo: figure out a neater way here
	for _, account := range accountsList {
		// reference to the account since we are using it in the goroutine
		closureAccount := account

		if closureAccount.GetState() != savingsPb.State_CREATED {
			continue
		}

		// create vpa if not created already in parallel
		// It will be a non-blocking step
		ctxForCreateVpa := epificontext.CloneCtx(ctx)
		goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
			// todo(yatin - 80941): evaluate if this call can be move somewhere else in upi flows
			s.createVpaIfNotExist(ctxForCreateVpa, actorId, closureAccount)
		})
	}

	// Pull UPI PIN SET status for internal accounts for whom the UPI accounts were created via the old way, i.e. non-TPAP.
	// Though, we do it only for the accounts which are in CREATED state.
	createdAccountIds := lo.FilterMap(accountsList, func(acc *savingsPb.SavingsAccountEssentials, _ int) (string, bool) {
		return acc.GetId(), acc.GetState() == savingsPb.State_CREATED
	})
	if len(createdAccountIds) != 0 {
		g.Go(func() error {
			// todo (yatin - 81056): this code can be removed, once we do the
			//		force-migration of all the users to the new vpa handle. This is
			// 		just used to fetch the pin status of internal non tpap onboarded
			//		account (accounts with old vpa handle).
			var gErr error

			pinStatusRes, gErr = s.upiClient.GetPinStatus(ctx, &upiPb.PinStatusRequest{
				CurrentActorId: actorId,
				AccountIds:     createdAccountIds,
			})
			switch {
			case gErr != nil:
				return fmt.Errorf("error while fetching pin set status for the account-ids = %s, err= %w", createdAccountIds, gErr)
			case pinStatusRes.GetStatus().IsRecordNotFound(): // for newly onboarded TPAP users, record not found is expected
				return nil
			case !pinStatusRes.GetStatus().IsSuccess():
				return fmt.Errorf("error while fetching pin set status for the account-ids = %s, err= %w", createdAccountIds, rpc.StatusAsError(pinStatusRes.GetStatus()))
			}

			return nil
		})
	}

	actorRes, err = s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
		Id: actorId,
	})
	if rpcErr := epifigrpc.RPCError(actorRes, err); rpcErr != nil {
		logger.Error(ctx, "Error in finding actor", zap.Error(rpcErr))
		return nil, rpcErr
	}

	g.Go(func() error {
		var gErr error
		cardIds, gErr = s.getCardIdsForActor(ctx, actorRes.GetActor())
		if gErr != nil {
			logger.Error(ctx, "error while fetching cards", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(gErr))
			return gErr
		}
		return nil
	})

	// Fetch config related to Ms Clarity Sdk, and override certain values based on User's eligibility/Group affinity.
	g.Go(func() error {
		releaseConstraint := release.NewCommonConstraintData(typesPb.Feature_FEATURE_MS_CLARITY_SDK_ENABLED).WithActorId(actorId)
		isEligible, releaseErr := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)

		if releaseErr != nil {
			logger.Error(ctx, "error in release evaluator for feature Feature_FEATURE_MS_CLARITY_SDK_ENABLED",
				zap.String(logger.ACTOR_ID_V2, actorId),
				zap.Error(releaseErr),
			)
			// Disable all config for Ms clarity if rollout evaluation fails
			msClarityConfig = &analytics.MsClarityConfig{
				EnableSdk:            false,
				AllowedScreenNames:   []string{},
				AllowedActivityNames: []string{},
			}
		} else {
			msClarityConfig = &analytics.MsClarityConfig{
				EnableSdk:            isEligible,
				AllowedScreenNames:   s.config.MsClarityConfig.AllowedScreenNames,
				AllowedActivityNames: s.config.MsClarityConfig.AllowedActivityNames,
			}
		}
		return nil
	})

	// Enable/Disable Home Single RPC as per the Feature rollout config for actor
	var isHomeSingleRPCEnabled = false
	g.Go(func() error {
		releaseConstraint := release.NewCommonConstraintData(typesPb.Feature_FEATURE_HOME_SINGLE_RPC).WithActorId(actorId)
		isEligible, releaseErr := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
		if releaseErr != nil {
			logger.Error(ctx, "error in release evaluator for feature Feature_FEATURE_HOME_SINGLE_RPC",
				zap.String(logger.ACTOR_ID_V2, actorId),
				zap.Error(releaseErr),
			)
			return nil
		}
		isHomeSingleRPCEnabled = isEligible
		return nil
	})

	if err = g.Wait(); err != nil {
		logger.Error(ctx, "error in client calls for user session details", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, err
	}

	mergedAccounts, err = s.getMergedAccounts(ctx, accountsList, connectedAccounts, tpapAccounts,
		pinStatusRes.GetAccountPinStateMap(), appVersionCode, pb.EntryPoint_ENTRY_POINT_USER_SESSION_DETAILS,
	)
	if err != nil {
		logger.Error(ctx, "error in merging all the accounts", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	// fill properties
	if ccAccount != nil {
		mergedAccounts = append(mergedAccounts, ccAccount)
	}

	shouldOverrideAccountControlFlags, overrideAccControlErr := s.isFeatureEnabled(ctx, actorId, types.Feature_ALLOW_AND_CONTROL_AA_TXN_FOR_TPAP)
	if overrideAccControlErr != nil {
		logger.WarnWithCtx(ctx, "error in overriding Aa txn control flag to true. handling gracefully", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Error(overrideAccControlErr))
	}
	if shouldOverrideAccountControlFlags && overrideAccControlErr == nil {
		s.OverrideAccountControlFlags(mergedAccounts)
	}
	featureMap[types.Feature_FEATURE_DC_DASHBOARD_V2_SCREEN.String()] = &pb.FeatureInfo{
		Enable: s.isDcDashboardV2Enabled(ctx, featureMap[types.Feature_FEATURE_DC_DASHBOARD_V2_SCREEN.String()].GetEnable(), getFeatDetailsRes.GetIsFiLiteUser(), appPlatform),
	}
	// Update the state of Home Single RPC enabled flag
	featureMap[types.Feature_FEATURE_HOME_SINGLE_RPC.String()] = &pb.FeatureInfo{
		Enable: isHomeSingleRPCEnabled,
	}
	// Update the state of Home Design feature flag
	featureMap[types.Feature_FEATURE_HOME_DESIGN_ENHANCEMENTS.String()] = &pb.FeatureInfo{
		Enable: isFeatureHomeDesignEnhancementsEnabled,
	}

	return &pb.GetUserSessionDetailsResponse{
		Status:                     rpc.StatusOk(),
		CardIds:                    cardIds,
		FirebaseId:                 vmResp.GetFcmId(),
		MsClarityId:                vmResp.GetMsClarityId(),
		FirebaseUserProperties:     firebaseUserProperties,
		DisplayName:                displayName,
		RespHeader:                 headerPkg.SuccessRespHeader(),
		Accounts:                   mergedAccounts,
		FeatureMap:                 featureMap,
		IsSaOnboarded:              onboardingRes.GetDetails().GetCompletedAt() != nil,
		IsFiLiteUser:               getFeatDetailsRes.GetIsFiLiteUser(),
		IsHomeAccessible:           onboardingRes.GetDetails().GetCompletedAt() != nil || getFeatDetailsRes.GetIsFiLiteUser(),
		CurrentFeature:             onboardingRes.GetDetails().GetStageMetadata().GetIntentSelectionMetadata().GetSelection().String(),
		MsClarityConfig:            msClarityConfig,
		ScienapticSmsScannerConfig: scienapticConfig,
		CardTabs:                   getCardTabs(),
	}, nil
}

func (s *Service) isDcDashboardV2Enabled(ctx context.Context, defaultVal, isFiLiteUser bool, appPlatform commontypes.Platform) bool {
	isFeatureEnabled := defaultVal && !(isFiLiteUser && appPlatform == commontypes.Platform_ANDROID)
	if !isFeatureEnabled || !s.genConfig.Card().DashboardV2Config().IsQuestCheckEnabledForDashboardV2() {
		return isFeatureEnabled
	}
	return isFeatureEnabled && s.genConfig.Card().DashboardV2Config().IsDashboardV2EnabledByQuest(ctx)
}
