// nolint: funlen
package user

import (
	"context"
	errs "errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/jinzhu/copier"
	errors2 "github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	colorPkg "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	savingsNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/savings"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/frontend/app"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	goUtils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/comms"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	connectedAccountEnums "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/connected_account/external"
	consentPb "github.com/epifi/gamma/api/consent"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/employment"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/frontend"
	feAccounts "github.com/epifi/gamma/api/frontend/account"
	feConnectedAccountEnums "github.com/epifi/gamma/api/frontend/account/connected_account/enums"
	"github.com/epifi/gamma/api/frontend/account/enums"
	feUpiPb "github.com/epifi/gamma/api/frontend/account/upi"
	feConnectedAccPb "github.com/epifi/gamma/api/frontend/connected_account"
	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	pb "github.com/epifi/gamma/api/frontend/user"
	onbPb "github.com/epifi/gamma/api/frontend/user/onboarding"
	"github.com/epifi/gamma/api/frontend/user/user_activity"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/kyc"
	kycDocsPb "github.com/epifi/gamma/api/kyc/docs"
	"github.com/epifi/gamma/api/kyc/vkyc"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	panPb "github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/product"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/savings/payload"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	types "github.com/epifi/gamma/api/typesv2"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
	onbScreenOptsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	frontend2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/profile"
	saClosurePb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/sa_closure"
	"github.com/epifi/gamma/api/typesv2/ui"
	upiPb "github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	bePb "github.com/epifi/gamma/api/user"
	userContactPb "github.com/epifi/gamma/api/user/contact"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	userLocationPb "github.com/epifi/gamma/api/user/location"
	beOnbPb "github.com/epifi/gamma/api/user/onboarding"
	savingsVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	woPb "github.com/epifi/gamma/api/wealthonboarding"

	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	events2 "github.com/epifi/gamma/frontend/events"
	"github.com/epifi/gamma/frontend/insights/secrets/colors"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	fePkgPay "github.com/epifi/gamma/frontend/pkg/pay"
	fePkgUpi "github.com/epifi/gamma/frontend/pkg/upi"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/helper"
	events3 "github.com/epifi/gamma/frontend/user/events"
	insightsPkg "github.com/epifi/gamma/insights/pkg"
	addressPkg "github.com/epifi/gamma/pkg/address"
	fePkgCA "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/countrystdinfo"
	deeplinkPkg "github.com/epifi/gamma/pkg/deeplink"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	config3 "github.com/epifi/gamma/pkg/feature/release/config"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
	headerPkg "github.com/epifi/gamma/pkg/frontend/header"
	gammanames "github.com/epifi/gamma/pkg/names"
	"github.com/epifi/gamma/pkg/obfuscator"
	onbPkg "github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/pkg/pay"
	pkgProfile "github.com/epifi/gamma/pkg/profile"
	"github.com/epifi/gamma/pkg/regex"
	pkgSavings "github.com/epifi/gamma/pkg/savings"
	"github.com/epifi/gamma/pkg/user"
	"github.com/epifi/gamma/pkg/vendormapping"
	federalPkg "github.com/epifi/gamma/pkg/vendors/federal"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

const (
	saClosureProfileEntryPointText       = `To close your account permanently, <a style="color: #00B899" href="%s">tap here</a>. This action cannot be undone`
	saClosureProfileEntryPointTextQa     = `<a style="color: #00B899" href="%s">To close your account permanently, tap here. This action cannot be undone</a>`
	saClosureProfileEntryPointPlainText  = `To delete your account permanently, tap here. This action cannot be undone`
	saClosureProfileEntryPointLinkText   = `tap here`
	saClosureProfileEntryPointLinkTextQa = `To delete your account permanently, tap here. This action cannot be undone`
	blackColor                           = "#000000"
	Default                              = "DEFAULT"
	updateAddressInfoBText               = `Add a new address`
	plusIconGreenUrl                     = "https://epifi-icons.pointz.in/card-images/plus-icon-green.png"
)

var (
	addressFlowToApplicableAddressTypeMap = map[pb.AddressFlow][]types.AddressType{
		pb.AddressFlow_ADDRESS_FLOW_ONBOARDING:  {types.AddressType_MAILING},
		pb.AddressFlow_ADDRESS_FLOW_DEBIT_CARD:  {types.AddressType_MAILING},
		pb.AddressFlow_ADDRESS_FLOW_CREDIT_CARD: {types.AddressType_MAILING},
	}

	// accountsToSortByAPOOrder : stores the order in which accounts can be sorted for priority / display purposes.
	accountsToSortByAPOOrder = []accountTypesPb.AccountProductOffering{
		accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
		accountTypesPb.AccountProductOffering_APO_REGULAR,
		accountTypesPb.AccountProductOffering_APO_NRE,
		accountTypesPb.AccountProductOffering_APO_NRO,
		accountTypesPb.AccountProductOffering_APO_FCNR,
		accountTypesPb.AccountProductOffering_APO_RFC,
	}
)

type void struct{}

// Service implements User GRPC service for the FE layer. All the client facing User RPCs
// must be implemented by this service.
type Service struct {
	pb.UnimplementedUserServer
	config                      *config.Config
	genConfig                   *genconf.Config
	onboardingClient            beOnbPb.OnboardingClient
	client                      bePb.UsersClient
	savingsClient               savingsPb.SavingsClient
	depositsClient              depositPb.DepositClient
	commsClient                 comms.CommsClient
	piClient                    paymentinstrument.PiClient
	accountPiClient             accountPiPb.AccountPIRelationClient
	actorClient                 actorPb.ActorClient
	upiClient                   upiPb.UPIClient
	userContactClient           userContactPb.ContactClient
	cardClient                  cardPb.CardProvisioningClient
	eventBroker                 events.Broker
	kycClient                   kyc.KycClient
	userGroupClient             userGroupPb.GroupClient
	salaryProgramClient         beSalaryPb.SalaryProgramClient
	vendorMappingClient         vmPb.VendorMappingServiceClient
	authClient                  auth.AuthClient
	connectedAccountClient      connectedAccountPb.ConnectedAccountClient
	releaseEvaluator            release.IEvaluator
	employmentClient            employment.EmploymentClient
	extAcctClient               extacct.ExternalAccountsClient
	upiOnboardingClient         onboarding.UpiOnboardingClient
	SafetyNetV2EnabledDeviceIds map[string]bool
	ffAccountingClient          ffAccPb.AccountingClient
	ffClient                    ffPb.FireflyClient
	beTieringClient             beTieringPb.TieringClient
	vKycFeClient                vkyc.VKYCFeClient
	feEmpClient                 employment.EmploymentFeClient
	bcClient                    bankCustPb.BankCustomerServiceClient
	wealthOnbClient             woPb.WealthOnboardingClient
	feConnectedAccClient        feConnectedAccPb.ConnectedAccountClient
	complianceClient            compliancePb.ComplianceClient
	panClient                   panPb.PanClient
	consentClient               consentPb.ConsentClient
	creditReportClient          creditReportV2Pb.CreditReportManagerClient
	userLocationClient          userLocationPb.LocationClient
	userAttrFetcher             user.UserAttributesFetcher
	questSdkClient              *questSdk.Client
	docExtractionClient         kycDocsPb.DocExtractionClient
	dataCollector               IDataCollector
	productClient               product.ProductClient
	savingsVgClient             savingsVgPb.SavingsClient
	celestialClient             celestialPb.CelestialClient
	tieringDataCollector        tieringData.DataCollector
	abEvaluatorGeneric          *release.ABEvaluator[string]
	panProcessor                insightsPkg.IPanProcessor
	networthClient              networthPb.NetWorthClient
}

// Factory method for creating an instance of user FE service. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewUserService(
	config *config.Config,
	onboardingClient beOnbPb.OnboardingClient,
	client bePb.UsersClient,
	savingsClient savingsPb.SavingsClient,
	depositsClient depositPb.DepositClient,
	commsClient comms.CommsClient,
	piClient paymentinstrument.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	actorClient actorPb.ActorClient,
	upiClient upiPb.UPIClient,
	userContactClient userContactPb.ContactClient,
	cardClient cardPb.CardProvisioningClient,
	broker events.Broker,
	kycClient kyc.KycClient,
	userGroupClient userGroupPb.GroupClient,
	salaryProgramClient beSalaryPb.SalaryProgramClient,
	vendorMappingClient vmPb.VendorMappingServiceClient,
	authClient auth.AuthClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	releaseEvaluator release.IEvaluator,
	employmentClient employment.EmploymentClient,
	extAcctClient extacct.ExternalAccountsClient,
	upiOnboardingClient onboarding.UpiOnboardingClient,
	safetyNetV2EnabledDeviceIds map[string]bool,
	genConfig *genconf.Config,
	ffAccountingClient ffAccPb.AccountingClient,
	ffClient ffPb.FireflyClient,
	beTieringClient beTieringPb.TieringClient,
	vKycFeClient vkyc.VKYCFeClient,
	feEmpClient employment.EmploymentFeClient,
	bcCLient bankCustPb.BankCustomerServiceClient,
	wealthOnbClient woPb.WealthOnboardingClient,
	feConnectedAccClient feConnectedAccPb.ConnectedAccountClient,
	complianceClient compliancePb.ComplianceClient,
	panClient panPb.PanClient,
	consentClient consentPb.ConsentClient,
	creditReportClient creditReportV2Pb.CreditReportManagerClient,
	userLocationClient userLocationPb.LocationClient,
	userAttrFetcher user.UserAttributesFetcher,
	questSdkClient *questSdk.Client,
	docExtractionClient kycDocsPb.DocExtractionClient,
	dataCollector IDataCollector,
	productClient product.ProductClient,
	savingsVgClient savingsVgPb.SavingsClient,
	celestialClient celestialPb.CelestialClient,
	tieringDataCollector tieringData.DataCollector,
	panProcessor insightsPkg.IPanProcessor,
	networthClient networthPb.NetWorthClient,
) *Service {
	return &Service{
		client:                      client,
		config:                      config,
		onboardingClient:            onboardingClient,
		savingsClient:               savingsClient,
		depositsClient:              depositsClient,
		commsClient:                 commsClient,
		piClient:                    piClient,
		accountPiClient:             accountPiClient,
		actorClient:                 actorClient,
		upiClient:                   upiClient,
		userContactClient:           userContactClient,
		cardClient:                  cardClient,
		eventBroker:                 broker,
		kycClient:                   kycClient,
		userGroupClient:             userGroupClient,
		salaryProgramClient:         salaryProgramClient,
		vendorMappingClient:         vendorMappingClient,
		authClient:                  authClient,
		connectedAccountClient:      connectedAccountClient,
		releaseEvaluator:            releaseEvaluator,
		employmentClient:            employmentClient,
		extAcctClient:               extAcctClient,
		upiOnboardingClient:         upiOnboardingClient,
		SafetyNetV2EnabledDeviceIds: safetyNetV2EnabledDeviceIds,
		genConfig:                   genConfig,
		ffAccountingClient:          ffAccountingClient,
		ffClient:                    ffClient,
		beTieringClient:             beTieringClient,
		vKycFeClient:                vKycFeClient,
		feEmpClient:                 feEmpClient,
		bcClient:                    bcCLient,
		wealthOnbClient:             wealthOnbClient,
		feConnectedAccClient:        feConnectedAccClient,
		complianceClient:            complianceClient,
		panClient:                   panClient,
		consentClient:               consentClient,
		creditReportClient:          creditReportClient,
		userLocationClient:          userLocationClient,
		userAttrFetcher:             userAttrFetcher,
		questSdkClient:              questSdkClient,
		docExtractionClient:         docExtractionClient,
		dataCollector:               dataCollector,
		productClient:               productClient,
		savingsVgClient:             savingsVgClient,
		celestialClient:             celestialClient,
		tieringDataCollector:        tieringDataCollector,
		abEvaluatorGeneric:          helper.GetABEvaluatorOfFeature[string](actorClient, client, userGroupClient, genConfig.ABFeatureReleaseConfig(), func(str string) string { return str }),
		panProcessor:                panProcessor,
		networthClient:              networthClient,
	}
}

const (
	// Deprecated: in favour of types.NomineeEntryPoint
	wealthOnboarding = "wealth_onboarding"
)

// RPC facilitates adding a new address.
// We identify the type of address using addressType attribute.
//
// This is a synchronous call i.e. if there is an RPC failure, the client is
// expected to call the RPC again for the retry.
// nolint:dupl
// nolint:funlen
func (s *Service) AddAddress(ctx context.Context, req *pb.AddAddressRequest) (*pb.AddAddressResponse, error) {
	logger.Info(ctx, "address type in AddAddress request", zap.String(logger.REQUEST_TYPE, req.GetType().String()))
	resp := &pb.AddAddressResponse{}
	addr := req.GetAddress()
	actorId := req.GetReq().GetAuth().GetActorId()
	actorResp, errResp := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
		logger.Error(ctx, "Error in finding actor", zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}
	if !isAddressValid(ctx, req.GetType(), addr) {
		logger.Info(ctx, "invalid address received from client")
		return &pb.AddAddressResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	postalAddr, err := convertToGooglePostalAddress(addr)
	if err != nil {
		logger.Error(ctx, "address format mismatch.", zap.Error(err))
		return &pb.AddAddressResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	// TODO(anand): BE user service assumes a single address per addrType. The address to be updated is identified using
	// addressType. Ideally, we should make use of this req.Id to uniquely identify the address.
	beReq := &bePb.UpdateAddressRequest{
		UserId:  actorResp.GetActor().GetEntityId(),
		Type:    req.GetType(),
		Address: postalAddr,
	}

	rpcResp, err := s.client.UpdateAddress(ctx, beReq)
	if er := epifigrpc.RPCError(rpcResp, err); er != nil {
		logger.Error(ctx, "error in updating address for user", zap.Error(er))
		s.sendAddressAddedEvent(ctx, actorId, events2.Failure, resp.GetStatus().GetShortMessage())
		return &pb.AddAddressResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: bottomSheetAddressUpdateErrorView,
			},
		}, nil
	}
	s.sendAddressAddedEvent(ctx, actorId, events2.Success, "")
	resp.Status = rpc.StatusOk()
	resp.Id = "1"
	return resp, nil
}

func isAddressValid(ctx context.Context, addressType types.AddressType, address *types.PostalAddress) bool {
	if address == nil {
		logger.Info(ctx, "address received from client is empty")
		return false
	}

	if addressType == types.AddressType_ADDRESS_TYPE_UNSPECIFIED {
		logger.Info(ctx, "address received has type unspecified")
		return false
	}

	if !isAddressComplete(address) {
		logger.Info(ctx, "address has missing fields")
		return false
	}

	return true
}

// RPC facilitates updating an address.
// There can be multiple addresses for a user. We identify the address to be updated using addressType attribute.
//
// This is a synchronous call i.e. if there is an RPC failure, the client is
// expected to call the RPC again for the retry.
// nolint:dupl
func (s *Service) UpdateAddress(ctx context.Context, req *pb.UpdateAddressRequest) (*pb.UpdateAddressResponse, error) {
	resp := &pb.UpdateAddressResponse{}

	if !isAddressComplete(req.Address) {
		logger.Error(ctx, "Address to be updated is invalid")
		resp.Status = rpc.StatusInvalidArgumentWithDebugMsg("invalid address")
		return resp, nil
	}

	actorId := req.GetReq().GetAuth().GetActorId()
	actorResp, errResp := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
		logger.Error(ctx, "Error in finding actor", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in finding actor"))
		return resp, nil
	}
	status := s.updateAddress(ctx, actorResp.GetActor().GetEntityId(), req.Type, req.Address)

	nextAction, err := s.getNextAction(ctx, req.GetReq().GetAuth())
	if err != nil {
		resp.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in next action: %v", err))
		return resp, nil
	}

	resp.NextAction = nextAction
	resp.Status = status
	return resp, nil
}

// RPC fetches the existing address for the given user.
// There can be multiple addresses for a user. We identify the address to be updated using addressType attribute.
// If address type is ADDRESS_TYPE_UNSPECIFIED, all the available addresses are fetched.
func (s *Service) GetAddress(ctx context.Context, req *pb.GetAddressRequest) (*pb.GetAddressResponse, error) {
	resp := &pb.GetAddressResponse{}

	actorId := req.GetReq().GetAuth().GetActorId()
	actorResp, errResp := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
		logger.Error(ctx, "Error in finding actor", zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}
	// TODO(anand): BE user service assumes a single address per addrType. The address to be updated is identified using
	// addressType. Ideally, we should make use of this req.Id to uniquely identify the address.

	// TODO(anand): fix this when BE service supports address type as unspecified.
	if req.Type == types.AddressType_ADDRESS_TYPE_UNSPECIFIED {
		req.Type = types.AddressType_MAILING
	}
	beReq := &bePb.GetAddressRequest{
		UserId: actorResp.GetActor().GetEntityId(),
		Type:   req.Type,
	}
	beResp, err := s.client.GetAddress(ctx, beReq)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in invoking user service to get addresses: %v", beReq),
			zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}
	// TODO(anand): typecast the BE service specific status codes into FE response status codes
	resp.Status = beResp.Status

	if beResp.Status.IsSuccess() {
		clientPostalAddr, err := convertToClientPostalAddressType(beResp.Address)
		if err != nil {
			logger.Error(ctx, "address format mismatch.", zap.Error(err))
			resp.Status = rpc.StatusInternal()
			return resp, nil
		}
		addressInfo := &pb.AddressInfo{
			// TODO(anand): short term hack, implement BE RPC to support address-id
			Id:      "1",
			Type:    req.Type,
			Address: clientPostalAddr,
		}
		resp.Addresses = append(resp.Addresses, addressInfo)
	}
	return resp, nil
}

// RPC to fetch a list of user addresses across different address types (Permanent, Mailing and Shipping).
// The list is ordered according to the relevance of the address most likely to be the delivery address (lower index corresponds to more relevance).
// The user can choose the address they want for delivering their card to. The address at 0th index is considered as Default address for card delivery.
func (s *Service) GetCardDeliveryAddresses(ctx context.Context, req *pb.GetCardDeliveryAddressesRequest) (*pb.GetCardDeliveryAddressesResponse, error) {
	var (
		res     = &pb.GetCardDeliveryAddressesResponse{}
		actorId string
	)

	actorId = req.GetReq().GetAuth().GetActorId()
	actorResp, errResp := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
		logger.Error(ctx, "Error in finding actor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	entityId := actorResp.GetActor().GetEntityId()
	beReq := &bePb.GetAllAddressesRequest{
		UserId: entityId,
		Format: types.AddressFormat_USER_READABLE,
	}

	beRes, err := s.client.GetAllAddresses(ctx, beReq)
	if rpcErr := epifigrpc.RPCError(beRes, err); rpcErr != nil {
		logger.Error(ctx, "error in invoking User service to get all addresses", zap.Error(rpcErr))
		res.Status = rpc.StatusInternal()
		failureReason := "error in invoking User service to get all addresses"
		s.sendAddressFetchedEvent(ctx, actorId, events2.Failure, failureReason)
		return res, nil
	}

	if res.Addresses, err = getCardDeliveryAddresses(ctx, beRes.Addresses); err != nil {
		failureReason := "address format mismatch" + err.Error()
		s.sendAddressFetchedEvent(ctx, actorId, events2.Failure, failureReason)
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	isNriRes, err := s.userAttrFetcher.IsNonResidentUser(ctx, &user.IsNonResidentUserRequest{
		ActorId: actorId,
	})
	if err != nil {
		return nil, err
	}

	kycResp, errResp := s.kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(kycResp, errResp); err != nil && !kycResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while fetching kyc status for actor", zap.Error(err))
		return &pb.GetCardDeliveryAddressesResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	isUserOnboarded, onbCheckErr := s.isUserOnboarded(ctx, req.GetReq().GetAuth().GetActorId())
	if onbCheckErr != nil {
		logger.Error(ctx, "error while check onb completion status for actor", zap.Error(onbCheckErr))
		res.Status = rpc.StatusInternalWithDebugMsg(onbCheckErr.Error())
		return res, nil
	}

	if res.AddressesWithType, err = s.getCardDeliveryAddressesWithType(ctx, beRes.GetAddresses(), actorId,
		req.GetAddressFlow(), isNriRes.GetIsNonResidentUser(), isUserOnboarded); err != nil {
		logger.Error(ctx, "error while fetching address with type", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		res.Status = rpc.StatusInternal()
		failureReason := "address format mismatch" + err.Error()
		s.sendAddressFetchedEvent(ctx, actorId, events2.Failure, failureReason)
		return res, nil
	}
	s.sendAddressFetchedEvent(ctx, actorId, events2.Success, "")

	if kycResp != nil {
		res.EnableAddressUpdate = kycResp.GetKycType() != kyc.KycType_CKYC && !isNriRes.GetIsNonResidentUser()
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

// TODO(rohan): Consider moving this to a different service.
// RPC fetches Pincode details.
// Deprecated: use GetPinCodeArea instead
func (s *Service) GetPinCodeDetails(context.Context, *pb.GetPinCodeDetailsRequest) (*pb.GetPinCodeDetailsResponse, error) {
	// TODO(anand): implement this
	resp := &pb.GetPinCodeDetailsResponse{
		Status: rpc.StatusOk(),
		City:   "Bangalore",
		State:  "Karnataka",
	}

	return resp, nil
}

// RPC fetches Area details corresponding to a PIN code. Here, Area is defined as City-State pair
// As one PIN code can span across multiple cities and states, a list of all such city-state pairs is responded back
func (s *Service) GetPinCodeAreas(ctx context.Context, req *onbPb.GetPinCodeAreasRequest) (*onbPb.GetPinCodeAreasResponse, error) {
	countryCode, err := getCountryCodeFromStr(req.GetCountryCode())
	if err != nil {
		logger.Debug(ctx, "invalid country code from client", zap.String(logger.COUNTRY, req.GetCountryCode()))
		return nil, err
	}
	// falling back to India as default country code for backward compatibility
	if countryCode == types.CountryCode_COUNTRY_CODE_UNSPECIFIED {
		countryCode = types.CountryCode_COUNTRY_CODE_IND
	}

	switch countryCode {
	case types.CountryCode_COUNTRY_CODE_IND:
		return s.getPinCodeAreasForIndia(ctx, req)

	case types.CountryCode_COUNTRY_CODE_ARE:
		return getPinCodeAreasForUAE(ctx)
	case types.CountryCode_COUNTRY_CODE_QAT:
		return getPinCodeAreasForQatar(ctx)

	default:
		logger.Debug(ctx, "unsupported country", zap.String(logger.COUNTRY, req.GetCountryCode()))
		return &onbPb.GetPinCodeAreasResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("unsupported country"),
			},
		}, nil
	}
}

func (s *Service) getPinCodeAreasForIndia(ctx context.Context, req *onbPb.GetPinCodeAreasRequest) (*onbPb.GetPinCodeAreasResponse, error) {
	resp := &onbPb.GetPinCodeAreasResponse{}

	beReq := &beOnbPb.GetPinCodeDetailsRequest{
		PinCode: req.GetPinCode(),
		FieldMask: []beOnbPb.PinCodeField{
			beOnbPb.PinCodeField_PIN_CODE_FIELD_DISTRICT,
			beOnbPb.PinCodeField_PIN_CODE_FIELD_STATE,
		},
	}
	beRes, err := s.onboardingClient.GetPinCodeDetails(ctx, beReq)

	if err != nil {
		logger.Error(ctx, "error invoking Onboarding Service to get PIN code Area", zap.String("pinCode", req.PinCode), zap.Error(err))

		resp.Status = rpc.StatusInternal()
		resp.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(err.Error())}
		return resp, err
	}

	resp.Status = beRes.GetStatus()
	resp.RespHeader = &header.ResponseHeader{Status: beRes.GetStatus()}

	if beRes.GetStatus().IsSuccess() {
		resp.Areas = extractAreas(beRes.Details)
		resp.Status = rpc.StatusOk()
		resp.RespHeader = headerPkg.SuccessRespHeader()
	}
	if beRes.GetStatus().IsRecordNotFound() {
		return &onbPb.GetPinCodeAreasResponse{
			Status: rpc.StatusRecordNotFound(),
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusRecordNotFound(),
				ErrorView: feErrors.NewInlineErrorView("", "Please enter a valid pin code"),
			},
		}, nil
	}

	return resp, nil
}

func getPinCodeAreasForUAE(ctx context.Context) (*onbPb.GetPinCodeAreasResponse, error) {
	cities := addressPkg.GetCitiesForCountry(addressPkg.CountryUAE)
	if len(cities) == 0 {
		logger.Error(ctx, "found empty cities list for UAE")
		return nil, fmt.Errorf("found empty cities list for UAE")
	}

	var areas []*onbPb.GetPinCodeAreasResponse_Area
	for _, city := range cities {
		areas = append(areas, &onbPb.GetPinCodeAreasResponse_Area{
			City: city,
		})
	}
	return &onbPb.GetPinCodeAreasResponse{
		RespHeader: headerPkg.SuccessRespHeader(),
		Areas:      areas,
	}, nil
}

func getPinCodeAreasForQatar(ctx context.Context) (*onbPb.GetPinCodeAreasResponse, error) {
	states := addressPkg.GetStatesForCountryCode(types.CountryCode_COUNTRY_CODE_QAT)
	if len(states) == 0 {
		logger.Error(ctx, "found empty states list for Qatar")
		return nil, fmt.Errorf("found empty states list for Qatar")
	}

	var areas []*onbPb.GetPinCodeAreasResponse_Area
	for _, state := range states {
		areas = append(areas, &onbPb.GetPinCodeAreasResponse_Area{
			State: state,
		})
	}
	return &onbPb.GetPinCodeAreasResponse{
		RespHeader: headerPkg.SuccessRespHeader(),
		Areas:      areas,
	}, nil
}

// nolint:protogetter
func (s *Service) CreateNominee(ctx context.Context, req *pb.CreateNomineeRequest) (*pb.CreateNomineeResponse, error) {
	resp := &pb.CreateNomineeResponse{}

	var beReq *bePb.CreateNomineeRequest

	if req.GetNomineeV2() != nil {
		feNominee := req.GetNomineeV2()
		documentType := goUtils.Enum(feNominee.GetDocumentType(), types.NomineeDocumentType_value, types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_UNSPECIFIED)
		documentNumber := feNominee.GetDocumentNumber()
		var nomineeDocument *types.NomineeDocument
		if documentType != types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_UNSPECIFIED && documentNumber != "" {
			nomineeDocument = &types.NomineeDocument{
				DocumentType:   documentType,
				DocumentNumber: documentNumber,
			}
		}
		beNominee := &types.Nominee{
			Name:            feNominee.Name,
			Relationship:    feNominee.Relationship,
			Dob:             feNominee.Dob,
			ContactInfo:     feNominee.ContactInfo,
			GuardianInfo:    feNominee.GuardianInfo,
			NomineeDocument: nomineeDocument,
		}
		beReq = &bePb.CreateNomineeRequest{Nominee: beNominee}
	} else {
		feNominee := req.GetNominee()
		var nomineeDocument *types.NomineeDocument
		if feNominee.GetPan() != "" {
			nomineeDocument = &types.NomineeDocument{
				DocumentType:   types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN,
				DocumentNumber: feNominee.GetPan(),
			}
		}
		beNominee := &types.Nominee{
			Name:            feNominee.Name,
			Relationship:    feNominee.Relationship,
			Dob:             feNominee.Dob,
			ContactInfo:     feNominee.ContactInfo,
			GuardianInfo:    feNominee.GuardianInfo,
			NomineeDocument: nomineeDocument,
		}
		beReq = &bePb.CreateNomineeRequest{Nominee: beNominee}
	}

	if beReq.GetNominee().GetContactInfo().GetAddress().GetPostalCode() == "" &&
		// UAE and Qatar doesn't have concept of postal code
		beReq.GetNominee().GetContactInfo().GetAddress().GetRegionCode() != addressPkg.CountryUAE &&
		!strings.EqualFold(beReq.GetNominee().GetContactInfo().GetAddress().GetRegionCode(), countrystdinfo.GetCLDRCountryName(types.CountryCode_COUNTRY_CODE_QAT)) {
		ce := feErrors.NewClientError(feErrors.EMPTY_NOMINEE_PIN)
		return &pb.CreateNomineeResponse{
			Status:     feErrors.ErrViewStatus,
			RespHeader: headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, ce.Subtitle, ""),
		}, nil
	}
	if !federalPkg.IsValidFederalNomineeAddress(ctx, beReq.GetNominee().GetContactInfo().GetAddress().GetAddressLines()) {
		logger.Info(ctx, "Address contains only special characters.")
		ce := feErrors.NewClientError(feErrors.ALL_SPECIAL_CHARACTER)
		return &pb.CreateNomineeResponse{
			Status:     feErrors.ErrViewStatus,
			RespHeader: headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, ce.Subtitle, ""),
		}, nil
	}

	validateResponse, _ := validateDocument(ctx, beReq.GetNominee())
	if validateResponse != nil {
		return validateResponse, nil
	}

	// Dropped phone number verification as client no longer sends this data
	nomineeAge, minorFlag := isMinor(beReq.GetNominee().GetDob())

	beReq.Nominee.ActorId = req.GetReq().GetAuth().GetActorId()
	entryPoint := goUtils.Enum(req.GetEntryPoint(), types.NomineeEntryPoint_value, types.NomineeEntryPoint_NOMINEE_ENTRY_POINT_UNSPECIFIED)
	if entryPoint == types.NomineeEntryPoint_NOMINEE_ENTRY_POINT_USER_PROFILE_DETAILS {
		return s.createNomineeForProfile(ctx, beReq, req.GetReq().GetAuth().GetActorId(), minorFlag, nomineeAge)
	} else if entryPoint == types.NomineeEntryPoint_NOMINEE_ENTRY_POINT_WEALTH_ONBOARDING || req.GetEntryPoint() == wealthOnboarding {
		return s.createNomineeForWealthOnb(ctx, beReq, req.GetReq().GetAuth().GetActorId(), minorFlag, nomineeAge)
	}

	beRes, err := s.client.CreateNominee(ctx, beReq)
	if er := epifigrpc.RPCError(beRes, err); er != nil {
		logger.Error(ctx, "error in invoking User service to create nominee", zap.Error(er))
		failureReason := events2.FailCreateNomineeDb
		if beRes.GetStatus() == nil {
			resp.Status = rpc.StatusInternal()
		} else {
			resp.Status = beRes.GetStatus()
			failureReason = beRes.GetStatus().GetShortMessage()
		}
		s.sendCreateNomineeEvent(ctx, beReq.Nominee.ActorId, minorFlag, beReq.Nominee.Relationship.String(), nomineeAge, events2.Failure, failureReason)
		return resp, nil
	}

	resp.Status = rpc.StatusOk()
	resp.RespHeader = headerPkg.SuccessRespHeader()
	resp.NomineeId = beRes.Nominee.Id
	s.sendCreateNomineeEvent(ctx, beReq.Nominee.ActorId, minorFlag, beReq.Nominee.Relationship.String(), nomineeAge, events2.Success, "")
	return resp, nil
}

// validateDocument validates document like pan, aadhaar, driving license if provided, if valid returns error response other wise nil
func validateDocument(ctx context.Context, nominee *types.Nominee) (*pb.CreateNomineeResponse, error) {
	documentType := nominee.GetNomineeDocument().GetDocumentType()
	documentNumber := nominee.GetNomineeDocument().GetDocumentNumber()
	if documentType == types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN &&
		!regex.IsValidPersonalPAN(documentNumber) {
		logger.Info(ctx, "Invalid pan provided for nominee")
		return ConstructErrorResponse(rpc.StatusInvalidArgument().Code,
			"Invalid request: Please provide valid pan", IncorrectDocumentTitle, IncorrectPanSubTitle)
	}
	if documentType == types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_AADHAAR &&
		!regex.IsValidAadhaar4Digit(documentNumber) {
		logger.Info(ctx, "Invalid AADHAAR provided for nominee")
		return ConstructErrorResponse(rpc.StatusInvalidArgument().Code,
			"Invalid request: Please provide valid aadhaar", IncorrectDocumentTitle, IncorrectAadhaarSubTitle)
	}
	if documentType == types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_DRIVING_LICENSE &&
		!regex.IsValidDrivingLicense(documentNumber) {
		logger.Info(ctx, "Invalid driving license provided for nominee")
		return ConstructErrorResponse(rpc.StatusInvalidArgument().Code,
			"Invalid request: Please provide valid driving license", IncorrectDocumentTitle, IncorrectDrivingLicenseSubTitle)
	}
	return nil, nil
}

// nolint:protogetter
func (s *Service) createNomineeForProfile(ctx context.Context, beReq *bePb.CreateNomineeRequest, actorId, minorFlag string, nomineeAge int) (*pb.CreateNomineeResponse, error) {
	resp := &pb.CreateNomineeResponse{}
	beRes, err := s.client.CreateNominee(ctx, beReq)
	if er := epifigrpc.RPCError(beRes, err); er != nil {
		logger.Error(ctx, "error in invoking User service to create nominee", zap.Error(er))
		failureReason := events2.FailCreateNomineeDb
		if beRes.GetStatus() == nil {
			resp.Status = rpc.StatusInternal()
		} else {
			resp.Status = beRes.GetStatus()
			failureReason = beRes.GetStatus().GetShortMessage()
		}
		s.sendCreateNomineeEvent(ctx, beReq.GetNominee().ActorId, minorFlag, beReq.GetNominee().GetRelationship().String(), nomineeAge, events2.Failure, failureReason)
		return resp, nil
	}

	accDetails, accDetailsErr := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{
			ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
				ActorId:                actorId,
				AccountProductOffering: accountTypesPb.AccountProductOffering_APO_REGULAR,
				PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})

	if err != nil {
		logger.Error(ctx, "error in fetching account details", zap.Error(accDetailsErr))
		return nil, accDetailsErr
	}

	getKYCVendorDataResp, getKYCVendorDataErr := s.kycClient.GetKYCVendorData(ctx, &kyc.GetKYCVendorDataRequest{
		Identifier: &kyc.GetKYCVendorDataRequest_ActorIdV2{
			ActorIdV2: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(getKYCVendorDataResp, getKYCVendorDataErr); rpcErr != nil {
		logger.Error(ctx, "error in fetching kyc status", zap.Error(rpcErr))
		return nil, rpcErr
	}

	nomineeDetails := accDetails.GetAccount().GetNomineeDetails()
	clientReqId := uuid.NewString()

	if nomineeDetails == nil || nomineeDetails.GetNomineeUpdateWorkflowDetails() == nil {
		nomineeDetails = s.getNomineeUpdateWorkflowDetails(clientReqId)
	} else {
		nomineeDetails.GetNomineeUpdateWorkflowDetails().ClientReqId = clientReqId
		nomineeDetails.GetNomineeUpdateWorkflowDetails().State = savingsPb.WorkflowState_WORKFLOW_STATE_IN_PROGRESS
		if accDetails.GetAccount().GetNomineeDetails().GetNominees() != nil || len(accDetails.GetAccount().GetNomineeDetails().GetNominees().GetNomineeWithShares()) != 0 {
			nomineeDetails.GetNominees().NomineeWithShares = []*savingsPb.NomineeWithShare{
				{
					NomineeId:       accDetails.GetAccount().GetNomineeDetails().GetNominees().GetNomineeWithShares()[0].GetNomineeId(),
					PercentageShare: 100,
				},
			}
		} else {
			nomineeDetails.GetNominees().NomineeWithShares = []*savingsPb.NomineeWithShare{
				{},
			}
		}
	}

	// Updating in Savings Account DB the nominee details
	nomineeUpdateResp, nomineeUpdateErr := s.savingsClient.UpdateSavingsAccountNominees(ctx, &savingsPb.UpdateSavingsAccountNomineesRequest{
		ActorId:        beReq.GetNominee().GetActorId(),
		NomineeDetails: nomineeDetails,
	})
	if rpcErr := epifigrpc.RPCError(nomineeUpdateResp, nomineeUpdateErr); rpcErr != nil {
		logger.Error(context.Background(), "error in updating nominee at vendor", zap.Error(rpcErr))
		return nil, rpcErr
	}

	nomineeMinorFLag := "N"
	guardianCode := ""
	if beRes.GetNominee().GetGuardianInfo() != nil {
		nomineeMinorFLag = "Y"
		guardianCode = "0"
	}

	workflowPayload, err := s.constructNomineeUpdateWorkflowPayload(beRes, nomineeMinorFLag, guardianCode, clientReqId, beReq, accDetails.GetAccount().GetAccountNo(), nomineeDetails, getKYCVendorDataResp.GetVendorData().GetPayload().GetEkycRecord().GetUidReferenceKey())
	if err != nil {
		logger.Error(ctx, "error in constructing payload", zap.Error(err))
		return nil, err
	}
	if beRes.GetNominee().GetGuardianInfo() != nil {
		workflowPayload.GuardianName = beRes.GetNominee().GetGuardianInfo().GetName()
	}
	workflowPayload.GetNomineeDetails().GetNominees().GetNomineeWithShares()[0].NomineeId = beRes.GetNominee().GetId()
	workflowPayload.GetNomineeDetails().GetNominees().GetNomineeWithShares()[0].PercentageShare = 100

	workflowPayloadMarshalled, marshalErr := protojson.Marshal(workflowPayload)
	if marshalErr != nil {
		logger.Error(ctx, "error in marshalling workflow payload", zap.Error(marshalErr))
		return nil, marshalErr
	}

	// Initiating workflow for nominee update at vendor
	initiateResp, initiateErr := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: beReq.GetNominee().GetActorId(),
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(savingsNs.UpdateNominee),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     clientReqId,
				Client: workflowPb.Client_SAVINGS,
			},
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			Payload:          workflowPayloadMarshalled,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil {
		logger.Error(ctx, fmt.Sprintf("failed to initiate workflow for nominee update, client req id %v", clientReqId), zap.Error(te))
		return nil, te
	}

	resp.Status = rpc.StatusOk()
	resp.RespHeader = headerPkg.SuccessRespHeader()
	resp.NomineeId = beRes.Nominee.Id
	resp.Deeplink = s.getNomineeUpdateSuccessScreenDeeplink(beReq.GetNominee().GetName())
	s.sendCreateNomineeEvent(ctx, beReq.Nominee.ActorId, minorFlag, beReq.Nominee.Relationship.String(), nomineeAge, events2.Success, "")
	return resp, nil
}

func (s *Service) createNomineeForWealthOnb(ctx context.Context, beReq *bePb.CreateNomineeRequest, actorId, minorFlag string, nomineeAge int) (*pb.CreateNomineeResponse, error) {
	resp, err := s.wealthOnbClient.GetInvestmentDataV2(ctx, &woPb.GetInvestmentDataV2Request{
		ActorIds: []string{actorId},
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error while calling GetInvestmentData", zap.Error(rpcErr))
		return &pb.CreateNomineeResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	userResp, userErr := s.client.GetUser(ctx, &bePb.GetUserRequest{
		Identifier: &bePb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, userErr); rpcErr != nil {
		logger.Error(ctx, "error while calling GetUser", zap.Error(rpcErr))
		return &pb.CreateNomineeResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	documentType := beReq.GetNominee().GetNomineeDocument().GetDocumentType()
	documentNumber := beReq.GetNominee().GetNomineeDocument().GetDocumentNumber()
	if documentType == types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_UNSPECIFIED ||
		documentNumber == "" {
		logger.Info(ctx, "Either document type or document number is empty for nominee")
		return ConstructErrorResponse(rpc.StatusInvalidArgument().Code,
			"Invalid request: Please provide nominee document", DocumentNotProvidedTitle, DocumentNotProvidedSubtitle)
	}
	if documentType == types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN &&
		(resp.GetInvestmentDetailInfo()[actorId].GetInvestmentDetails().GetPan().GetId() == documentNumber || userResp.GetUser().GetProfile().GetPAN() == documentNumber) {
		logger.Info(ctx, "nominee pan and user pan are the same")
		return ConstructErrorResponse(uint32(pb.CreateNomineeResponse_FAILED_PRECONDITION_NOMINEE_SAME_AS_ACCOUNT_HOLDER),
			"Invalid request: Nominee details cannot be the same as user details", PanSameTitle, PanSameSubtitle)
	}

	beRes, err := s.client.CreateNominee(ctx, beReq)
	if rpcErr := epifigrpc.RPCError(beRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling CreateNominee", zap.Error(rpcErr))
		s.sendCreateNomineeEvent(ctx, beReq.GetNominee().GetActorId(), minorFlag, beReq.GetNominee().GetRelationship().String(), nomineeAge, events2.Failure, events2.FailCreateNomineeDb)
		return &pb.CreateNomineeResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	s.sendCreateNomineeEvent(ctx, beReq.GetNominee().GetActorId(), minorFlag, beReq.GetNominee().GetRelationship().String(), nomineeAge, events2.Success, "")
	return &pb.CreateNomineeResponse{
		Status:     rpc.StatusOk(),
		RespHeader: headerPkg.SuccessRespHeader(),
		NomineeId:  beReq.GetNominee().GetId(),
	}, nil
}

func ConstructErrorResponse(errorCode uint32, shortErrorMessage, title, subtitle string) (*pb.CreateNomineeResponse, error) {
	return &pb.CreateNomineeResponse{
		Status: rpc.NewStatusWithoutDebug(errorCode, shortErrorMessage),
		RespHeader: &header.ResponseHeader{
			Status: rpc.NewStatus(errorCode, shortErrorMessage, ""),
			ErrorView: &errorsPb.ErrorView{
				Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
				Options: &errorsPb.ErrorView_BottomSheetErrorView{
					BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
						Title:        title,
						Subtitle:     subtitle,
						TitleText:    commontypes.GetTextFromStringFontColourFontStyle(title, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_1),
						SubtitleText: commontypes.GetTextFromStringFontColourFontStyle(subtitle, "#A4A4A4", commontypes.FontStyle_BODY_3),
					},
				},
			},
		},
	}, nil
}

func (s *Service) constructNomineeUpdateWorkflowPayload(beRes *bePb.CreateNomineeResponse, nomineeMinorFLag, guardianCode, clientReqId string, beReq *bePb.CreateNomineeRequest, accountNo string, nomineeDetails *savingsPb.NomineeDetails, ekycCrrn string) (*payload.UpdateNomineePayload, error) {

	relationship := beRes.GetNominee().GetRelationship()
	nomineeRelationshipCode, err := federalPkg.GetNomineeRelationCode(relationship)
	if err != nil {
		return nil, fmt.Errorf("failed to map nominee relationship: %s to a matching Federal defined nominee relationship Code", relationship)
	}
	splittedAddressLines := splitAddressLine(beRes.GetNominee().GetContactInfo().GetAddress().GetAddressLines()[0])
	return &payload.UpdateNomineePayload{
		AccountId:            accountNo,
		ServiceRequestId:     "SBAcctMod",
		EkycCrrn:             ekycCrrn,
		NomineeName:          beRes.GetNominee().GetName(),
		NomineeRegNo:         beRes.GetNominee().GetId(),
		NomineeRelationType:  nomineeRelationshipCode,
		NomineeMinorFlag:     nomineeMinorFLag,
		NomineeDob:           beRes.GetNominee().GetDob().AsTime().Format("02-01-2006"),
		NomineeAddressLine_1: splittedAddressLines[0],
		NomineeAddressLine_2: splittedAddressLines[1],
		NomineeAddressLine_3: splittedAddressLines[2],
		Nominee_City:         beRes.GetNominee().GetContactInfo().GetAddress().GetLocality(),
		NomineeState:         beRes.GetNominee().GetContactInfo().GetAddress().GetAdministrativeArea(),
		NomineeCountry:       beRes.GetNominee().GetContactInfo().GetAddress().GetRegionCode(),
		NomineePostalCode:    beRes.GetNominee().GetContactInfo().GetAddress().GetPostalCode(),
		GuardianCode:         guardianCode,
		ActorId:              beReq.GetNominee().GetActorId(),
		NomineeDetails:       nomineeDetails,
		ClientReqId:          clientReqId,
	}, nil
}

func (s *Service) getNomineeUpdateWorkflowDetails(clientReqId string) *savingsPb.NomineeDetails {
	return &savingsPb.NomineeDetails{
		NomineeUpdateWorkflowDetails: &savingsPb.NomineeUpdateWorkflowDetails{
			ClientReqId: clientReqId,
			State:       savingsPb.WorkflowState_WORKFLOW_STATE_IN_PROGRESS,
		},
		Nominees: &savingsPb.Nominees{
			NomineeWithShares: []*savingsPb.NomineeWithShare{
				{},
			},
		},
	}
}

// nolint:gocritic
func (s *Service) getNomineeUpdateSuccessScreenDeeplink(nomineeName string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_SA_NOMINEE_DETAILS_UPDATE_SUCCESS_SCREEN,
		ScreenOptionsV2: getAnyWithoutError(&frontend2.NomineeDetailsUpdateSuccessScreenOptions{
			TopIcon:  commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/savings/nominee_update_success.png", 160, 160),
			Title:    getTextByFontColorAndStyle("Nominee added successfully", "#313234", commontypes.FontStyle_HEADLINE_XL),
			Subtitle: commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("<b>%s</b> is the nominee for this account. Please check after some time for update status.", nomineeName), "#333333", commontypes.FontStyle_SUBTITLE_M),
			BottomCta: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Done",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
			NomineeInitial: getTextByFontColorAndStyle(string([]rune(nomineeName)[0]), "#FFFFFF", commontypes.FontStyle_SUBTITLE_M),
		}),
	}
}

// Fetches all nominees details associated with an actor
func (s *Service) GetNominees(ctx context.Context, req *pb.GetNomineesRequest) (*pb.GetNomineesResponse, error) {
	resp := &pb.GetNomineesResponse{}

	actorId := req.GetReq().GetAuth().GetActorId()
	beReq := &bePb.GetNomineesRequest{ActorId: actorId}

	beRes, err := s.client.GetNominees(ctx, beReq)
	if er := epifigrpc.RPCError(beRes, err); er != nil {
		if err == nil && beRes != nil && beRes.GetStatus() != nil && beRes.GetStatus().IsRecordNotFound() {
			resp.Status = rpc.StatusRecordNotFound()
			s.sendGetNomineesEvent(ctx, actorId, events2.NilCount, events2.Failure, events2.FailRecordNotFound)
			return resp, nil
		}
		logger.Error(ctx, "error in fetching nominees for actor from User service", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))

		failureReason := "error in fetching nominees for actor from User service"
		s.sendGetNomineesEvent(ctx, actorId, events2.NilCount, events2.Failure, failureReason)
		resp.Status = rpc.StatusInternal()
		return resp, err
	}
	s.sendGetNomineesEvent(ctx, actorId, len(resp.Nominees), events2.Success, "")
	entryPoint := goUtils.Enum(req.GetFlowType(), types.NomineeEntryPoint_value, types.NomineeEntryPoint_NOMINEE_ENTRY_POINT_UNSPECIFIED)
	var beNominees []*types.Nominee
	// For wealth onb, we require nominees who have pan, email and phone number for order feed
	if entryPoint == types.NomineeEntryPoint_NOMINEE_ENTRY_POINT_WEALTH_ONBOARDING || req.GetFlowType() == wealthOnboarding {
		beNominees = filterWealthOnbNominees(beRes.Nominees)
	} else {
		beNominees = beRes.Nominees
	}

	feNominees := make([]*types.Nominee, 0)
	feNomineesV2 := make([]*pb.Nominee, 0)

	for _, nominee := range beNominees {
		feNominees = append(feNominees, ConvertBeNomineeToFeNominee(nominee))
		feNomineesV2 = append(feNomineesV2, ConvertBeNomineeToFeNomineeV2(nominee))
	}

	resp.Nominees = feNominees
	resp.NomineesV2 = feNomineesV2
	resp.Status = rpc.StatusOk()
	return resp, nil
}

func filterWealthOnbNominees(beNominees []*types.Nominee) []*types.Nominee {
	var nominees []*types.Nominee
	for _, nominee := range beNominees {
		if nominee.GetNomineeDocument().GetDocumentType() != types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_UNSPECIFIED &&
			nominee.GetNomineeDocument().GetDocumentNumber() != "" &&
			nominee.GetContactInfo().GetPhoneNumber().ToString() != "" &&
			nominee.GetContactInfo().GetEmailId() != "" {
			nominees = append(nominees, nominee)
		}
	}
	return nominees
}

// Fetches nominee details for a requested Nominee ID
func (s *Service) GetNominee(ctx context.Context, req *pb.GetNomineeRequest) (*pb.GetNomineeResponse, error) {
	resp := &pb.GetNomineeResponse{}

	actorId := req.GetReq().GetAuth().GetActorId()
	nomineeId := req.NomineeId
	beReq := &bePb.GetNomineeRequest{NomineeId: nomineeId, ActorId: actorId}

	beRes, err := s.client.GetNominee(ctx, beReq)
	if er := epifigrpc.RPCError(beRes, err); er != nil {
		if err == nil && beRes != nil && beRes.GetStatus() != nil && beRes.GetStatus().IsRecordNotFound() {
			resp.Status = rpc.StatusRecordNotFound()
			return resp, nil
		}
		logger.Error(ctx, "error in fetching nominee from User service", zap.String(logger.NOMINEE_ID, nomineeId), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, err
	}

	resp.Status = rpc.StatusOk()
	beNominee := beRes.GetNominee()
	resp.Nominee = ConvertBeNomineeToFeNominee(beNominee)
	resp.NomineeV2 = ConvertBeNomineeToFeNomineeV2(beNominee)
	return resp, nil
}

func (s *Service) ConfirmCardShippingPreference(ctx context.Context, req *pb.ConfirmCardShippingPreferenceRequest) (*pb.ConfirmCardShippingPreferenceResponse, error) {
	resp := &pb.ConfirmCardShippingPreferenceResponse{}
	actorId := req.GetReq().GetAuth().GetActorId()

	addrType := req.AddressType
	kycResp, err := s.kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{
		ActorId:        actorId,
		IgnoreLiveness: false,
	})

	// marking address type as mailing in case of ekyc, so we can skip the address update API call.
	// mailing and permanent addresses are same in case of ekyc
	if er := epifigrpc.RPCError(kycResp, err); er != nil {
		logger.Error(ctx, "error in ckyc kyc status", zap.Error(er))
	} else if kycResp.GetKycType() == kyc.KycType_EKYC && addrType == types.AddressType_PERMANENT {
		addrType = types.AddressType_MAILING
	}

	beReq := &bePb.CreateShippingPreferenceRequest{Preference: &bePb.ShippingPreference{
		ActorId:      actorId,
		ShippingItem: types.ShippingItem_DEBIT_CARD,
		AddressType:  addrType,
	}}

	beRes, err := s.client.CreateShippingPreference(ctx, beReq)
	if er := epifigrpc.RPCError(beRes, err); er != nil {
		logger.Error(ctx, "error in invoking User service to create shipping preference", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		failureReason := "error in invoking User service to create shipping preference"
		s.sendConfirmedAddressEvent(ctx, actorId, req.AddressType.String(), events2.Failure, failureReason)
		return resp, er
	}

	nextAction, err := s.getNextAction(ctx, req.GetReq().GetAuth())
	if err != nil {
		resp.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in next action: %v", err))
		s.sendConfirmedAddressEvent(ctx, actorId, req.AddressType.String(), events2.Failure, events2.FailNextAction)
		return resp, nil
	}
	s.sendConfirmedAddressEvent(ctx, actorId, req.AddressType.String(), events2.Success, "")
	resp.NextAction = nextAction
	resp.Status = rpc.StatusOk()
	return resp, nil
}

// SyncContactDetails returns the information regarding the user's contacts which are on fi, this information includes
// hashed phone number, verified name and other details
func (s *Service) SyncContactDetails(ctx context.Context, req *pb.SyncContactDetailsRequest) (*pb.SyncContactDetailsResponse, error) {
	resp := &pb.SyncContactDetailsResponse{}

	beReq := &userContactPb.SyncContactDetailsRequest{}
	beRes, err := s.userContactClient.SyncContactDetails(ctx, beReq)
	switch {
	case err != nil:
		logger.Error(ctx, "error while syncing contacts", zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	case beRes.GetStatus().IsRecordNotFound():
		return &pb.SyncContactDetailsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	case !beRes.GetStatus().IsSuccess():
		logger.Error(ctx, fmt.Sprintf("SyncContactDetails() rpc failed with status: %s", beRes.GetStatus()))
		resp.Status = beRes.GetStatus()
		return resp, nil
	}
	resp.ContactDetails = convertToFeContactDetails(beRes.GetContactDetails())
	resp.Status = rpc.StatusOk()
	return resp, nil
}

// convertToFeContactDetails converts SyncContactDetailsResponse_ContactDetails of backend type into frontend type
func convertToFeContactDetails(contactDetails []*userContactPb.SyncContactDetailsResponse_ContactDetails) []*pb.SyncContactDetailsResponse_ContactDetails {
	var resp []*pb.SyncContactDetailsResponse_ContactDetails

	for _, detail := range contactDetails {
		contactDetail := &pb.SyncContactDetailsResponse_ContactDetails{
			PhoneNumberHash: detail.GetPhoneNumberHash(),
			IconUrl:         detail.GetIconUrl(),
			VerifiedName:    detail.GetVerifiedName().ToString(),
			IsNewOnFi:       detail.GetIsNewOnFi(),
			ColourCode:      detail.GetColourCode(),
		}
		resp = append(resp, contactDetail)
	}
	return resp
}

func (s *Service) updateAddress(ctx context.Context, actorID string, addressType types.AddressType,
	address *types.PostalAddress) *rpc.Status {
	postalAddr, err := convertToGooglePostalAddress(address)
	if err != nil {
		logger.Error(ctx, "address format mismatch.", zap.Error(err))
		return rpc.StatusInternal()
	}
	// TODO(anand): BE user service assumes a single address per addrType. The address to be updated is identified using
	// addressType. Ideally, we should make use of this req.Id to uniquely identify the address.
	beReq := &bePb.UpdateAddressRequest{
		UserId:  actorID,
		Type:    addressType,
		Address: postalAddr,
	}
	beResp, err := s.client.UpdateAddress(ctx, beReq)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error in invoking user service to update address for user id: %v", beReq.UserId),
			zap.Error(err))
		return rpc.StatusInternal()
	}
	return beResp.Status
}

// RecordHashedContacts takes in a list of hashed contacts and for each contact either inserts it or deletes it from the
// contacts table based on if IsDeleted flag is set or not
func (s *Service) RecordHashedContacts(ctx context.Context, req *pb.RecordHashedContactsRequest) (*pb.RecordHashedContactsResponse, error) {
	resp := &pb.RecordHashedContactsResponse{}
	beReq := &userContactPb.RecordHashedContactsRequest{
		Contact: convertToBeHashedContacts(req.GetContact()),
	}
	beRes, err := s.userContactClient.RecordHashedContacts(ctx, beReq)
	switch {
	case err != nil:
		logger.Error(ctx, "error while recording hashed contacts into the database", zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	case beRes.GetStatus().IsFailedPrecondition():
		logger.Info(ctx, "rpc call is locked for the actor")
		return &pb.RecordHashedContactsResponse{
			Status: rpc.StatusOk(),
		}, nil
	case !beRes.GetStatus().IsSuccess():
		logger.Error(ctx, fmt.Sprintf("RecordHashedContacts() rpc failed with status: %s", beRes.GetStatus()))
		resp.Status = beRes.GetStatus()
		return resp, nil
	}
	resp.Status = rpc.StatusOk()
	return resp, nil
}

// convertToBeHashedContacts() converts RecordHashedContactsRequest_Contact of frontend type to backend type
func convertToBeHashedContacts(hashedContactDetails []*pb.RecordHashedContactsRequest_Contact) []*userContactPb.RecordHashedContactsRequest_Contact {
	var resp []*userContactPb.RecordHashedContactsRequest_Contact
	for _, hashedContact := range hashedContactDetails {
		hashedContactDetail := &userContactPb.RecordHashedContactsRequest_Contact{
			PhoneNumberHash: hashedContact.GetPhoneNumberHash(),
			IsDeleted:       hashedContact.GetIsDeleted(),
		}
		resp = append(resp, hashedContactDetail)
	}
	return resp
}

func extractAreas(pinCodeDetails []*beOnbPb.PinCodeDetails) []*onbPb.GetPinCodeAreasResponse_Area {
	type Area struct {
		city  string
		state string
	}

	set := make(map[Area]void) // new empty set of areas
	resp := make([]*onbPb.GetPinCodeAreasResponse_Area, 0)

	for _, detail := range pinCodeDetails {
		district := detail.District
		state := detail.State
		area := Area{
			city:  district, // we consider District name to be the closest entity to a city name in our database
			state: state,
		}

		if _, ok := set[area]; !ok {
			set[area] = void{}
			resp = append(resp, &onbPb.GetPinCodeAreasResponse_Area{City: district, State: state})
		}
	}
	return resp
}

// Client consumes address in types.postalAddress type, which is copy of google's postal address.
// It is so because there was some issue with Google's proto, explained in types.PostalAddress doc.
func convertToGooglePostalAddress(address *types.PostalAddress) (*postaladdress.PostalAddress, error) {
	postalAddr := &postaladdress.PostalAddress{}
	if err := copier.Copy(postalAddr, address); err != nil {
		return nil, fmt.Errorf("failed to copy address: %w", err)
	}
	return postalAddr, nil
}

func convertToClientPostalAddressType(postalAddr *postaladdress.PostalAddress) (*types.PostalAddress, error) {
	addr := &types.PostalAddress{}
	if err := copier.Copy(addr, postalAddr); err != nil {
		return nil, fmt.Errorf("failed to copy address: %w", err)
	}
	return addr, nil
}

func (s *Service) getCreditCardAccount(ctx context.Context, actorId, referenceId string) (*feAccounts.Account, error) {
	var (
		getAccountRes = &ffAccPb.GetAccountResponse{}
		rpcErr        error
		getCardRes    = &ffPb.GetCreditCardResponse{}
		getCardErr    error
	)
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		getAccountRes, rpcErr = s.ffAccountingClient.GetAccount(grpCtx, &ffAccPb.GetAccountRequest{GetBy: &ffAccPb.GetAccountRequest_ByActorIdAndRefId{
			ByActorIdAndRefId: &ffAccPb.GetAccountRequest_ActorIdAndRefId{
				ActorId:     actorId,
				ReferenceId: referenceId,
			},
		}})
		switch {
		case rpcErr != nil:
			return fmt.Errorf("error in fetching account %w", rpcErr)
		case getAccountRes.GetStatus().IsRecordNotFound():
			return nil
		case !getAccountRes.GetStatus().IsSuccess():
			return fmt.Errorf("non success status while fetching account details %s", getAccountRes.GetStatus().String())
		default:
			return nil
		}
	})

	grp.Go(func() error {
		getCardRes, getCardErr = s.ffClient.GetCreditCard(grpCtx, &ffPb.GetCreditCardRequest{
			GetBy: &ffPb.GetCreditCardRequest_ActorId{
				ActorId: actorId,
			}},
		)
		switch {
		case getCardErr != nil:
			return fmt.Errorf("error in fetching card %w", getCardErr)
		case getCardRes.GetStatus().IsRecordNotFound():
			return nil
		case !getCardRes.GetStatus().IsSuccess():
			return fmt.Errorf("non success status while fetching card details %s", getCardRes.GetStatus().String())
		default:
			return nil
		}
	})

	if err := grp.Wait(); err != nil {
		return nil, err
	}
	if getAccountRes.GetStatus().IsRecordNotFound() || getCardRes.GetStatus().IsRecordNotFound() {
		logger.Info(ctx, "credit account or card not found", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String(logger.ENTITY_ID, referenceId))
		return nil, nil
	}

	derivedAccountIdProto := &accounts.DerivedAccountId{
		CreditCardAccountId: getAccountRes.GetAccount().GetId(),
	}

	derivedAccountIdString, err := idgen.EncodeProtoToStdBase64(derivedAccountIdProto)
	if err != nil {
		return nil, fmt.Errorf("failed to encode derived account id %w", err)
	}

	// masked card number stored is of the format : 1234XXXXXXXX5678
	maskedCardNumber := getCardRes.GetCreditCard().GetBasicInfo().GetMaskedCardNumber()
	// we will take the last 4 digits of masked card number
	lastFourDigitsOfMaskedCardNumber := maskedCardNumber[len(maskedCardNumber)-4:]
	// as per design we need to show card number of the format : "••3874"
	visibleMaskedCardNumber := "••" + lastFourDigitsOfMaskedCardNumber

	return &feAccounts.Account{
		AccountId: getAccountRes.GetAccount().GetId(),
		// we will show card number for credit card transactions
		MaskedAccountNumber: visibleMaskedCardNumber,
		PartnerBank:         (*s.config.VendorToAccountTypeToNameMap)[commonvgpb.Vendor_FEDERAL_BANK.String()][accounts.Type_CREDIT_CARD_ACCOUNT.String()],
		BankLogoUrl:         s.config.BankIcon.FederalFiIconUrl,
		AccountType:         accounts.Type_CREDIT_CARD_ACCOUNT,
		Capabilities: []feAccounts.AccountCapability{
			feAccounts.AccountCapability_CREDIT,
			feAccounts.AccountCapability_DEBIT,
		},
		DerivedAccountId: derivedAccountIdString,
		AccountProvenances: []enums.AccountProvenance{
			enums.AccountProvenance_ACCOUNT_PROVENANCE_CREDIT_CARD_ACCOUNT,
		},
	}, nil
}

func (s *Service) getFeatureMapConfig(ctx context.Context, actorId string, groups []commontypes.UserGroup) (
	map[string]*pb.FeatureInfo, error) {
	featureMap := make(map[string]*pb.FeatureInfo)

	for _, featureVal := range types.Feature_value {
		// Ignoring older features for backward compatibility
		if types.Feature(featureVal) == types.Feature_FEATURE_UNSPECIFIED ||
			types.Feature(featureVal) == types.Feature_INVESTMENT_MF_UI ||
			types.Feature(featureVal) == types.Feature_PAY_VIA_PHONE_NUMBER {
			continue
		}
		// Default feature to disable
		info := &pb.FeatureInfo{Enable: false}
		featureMap[types.Feature(featureVal).String()] = info
		enableFeature, err := s.releaseEvaluator.Evaluate(ctx,
			release.NewCommonConstraintData(types.Feature(featureVal)).WithActorId(actorId).WithUserGroup(groups))
		if err != nil {
			return nil, err
		}
		info.Enable = enableFeature
	}
	// setting feature flag for tpap and mapper
	isTpapEnabled := fePkgUpi.IsUpiTpapEnabledForActor(ctx, actorId, s.upiOnboardingClient)
	featureMap[types.Feature_UPI_TPAP.String()] = &pb.FeatureInfo{Enable: isTpapEnabled}

	isMapperEnabled := fePkgUpi.IsUpiMapperEnabledForActor(ctx, actorId, s.upiOnboardingClient)
	featureMap[types.Feature_UPI_MAPPER.String()] = &pb.FeatureInfo{Enable: isMapperEnabled}

	// setting feature flag for upi pin set using aadhaar number
	isUpiPinSetUsingAadhaarEnabled := fePkgUpi.IsUpiPinSetUsingAadhaarEnabledForActor(ctx, actorId, s.upiOnboardingClient)
	featureMap[types.Feature_UPI_PIN_SET_USING_AADHAAR.String()] = &pb.FeatureInfo{Enable: isUpiPinSetUsingAadhaarEnabled}

	isUpiInternationalPaymentEnabledForActor := fePkgUpi.IsUpiInternationalPaymentEnabledForActor(ctx, actorId, s.upiOnboardingClient)
	featureMap[types.Feature_UPI_INTERNATIONAL_PAYMENT.String()] = &pb.FeatureInfo{Enable: isUpiInternationalPaymentEnabledForActor}

	isCcLinkingEnabledForActor := fePkgUpi.IsCcLinkingEnabledForActor(ctx, actorId, s.upiOnboardingClient)
	featureMap[types.Feature_CC_UPI_LINKING.String()] = &pb.FeatureInfo{Enable: isCcLinkingEnabledForActor}

	isAutoPayEnabled, _ := fePkgPay.IsAutoPayEnabledForActor(ctx, actorId, s.savingsClient)
	featureMap[types.Feature_AUTOPAY_HUB.String()] = &pb.FeatureInfo{Enable: isAutoPayEnabled}

	isFeatureRewardsCatalogMergedPageEnabled := featureflags.IsFeatureRewardsCatalogMergedPageEnabled(ctx, &featureflags.IsFeatureRewardsCatalogMergedPageEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator:            s.releaseEvaluator,
			OnboardingClient:     s.onboardingClient,
			QuestSdkClient:       s.questSdkClient,
			UserAttributeFetcher: s.userAttrFetcher,
			NetWorthClient:       s.networthClient,
		},
	})
	featureMap[types.Feature_FEATURE_REWARDS_CATALOG_MERGED_PAGE.String()] = &pb.FeatureInfo{Enable: isFeatureRewardsCatalogMergedPageEnabled}
	return featureMap, nil
}

func (s *Service) getCardIdsForActor(ctx context.Context, actor *types.Actor) ([]string, error) {
	cardRes, err := s.cardClient.GetCardGroups(ctx, &cardPb.GetCardGroupsRequest{
		Actor:               actor,
		AscOrderCreatedTime: false,
		NumGroups:           1,
		GetAll:              false,
	})
	if err = epifigrpc.RPCError(cardRes, err); err != nil {
		logger.Error(ctx, "error encountered while fetching card details", zap.Error(err))
		return nil, fmt.Errorf("error fetching card details : %v", err)
	}
	cards := cardRes.GetCards()
	if len(cards) == 0 {
		logger.Error(ctx, "no card details found")
		return nil, fmt.Errorf("error fetching card details : no card found for actor")
	}
	cardIds := make([]string, 0)
	for _, card := range cards {
		cardIds = append(cardIds, card.Id)
	}
	return cardIds, nil
}

func (s *Service) getNextAction(ctx context.Context, authHeader *header.AuthHeader) (*deeplink.Deeplink, error) {
	nextActionRes, err := s.onboardingClient.GetNextAction(ctx, &beOnbPb.GetNextActionRequest{
		ActorId: authHeader.GetActorId(),
	})
	if err = epifigrpc.RPCError(nextActionRes, err); err != nil {
		logger.Error(ctx, "failed to get next onboarding action", zap.Error(err))
		return nil, err
	}
	return nextActionRes.GetNextAction(), nil
}

func getCardDeliveryAddresses(ctx context.Context, addresses map[string]*types.Addresses) ([]*types.PostalAddress, error) {
	var deliveryAddresses []*types.PostalAddress
	sortedAddresses := getSortedAddressesForCardDelivery(addresses)
	uniqueAddresses := addressPkg.DedupeAddresses(sortedAddresses)

	for _, address := range uniqueAddresses {
		replaceInvalidEntities(ctx, address)
		clientPostalAddr, err := convertToClientPostalAddressType(address)
		if err != nil {
			logger.Error(ctx, "address format mismatch.", zap.Error(err))
			return nil, err
		}
		deliveryAddresses = append(deliveryAddresses, clientPostalAddr)
	}

	return deliveryAddresses, nil
}

func (s *Service) getCardDeliveryAddressesWithType(ctx context.Context,
	addresses map[string]*types.Addresses, actorId string, addressFlow pb.AddressFlow, isNri bool, isUserOnboarded bool) ([]*pb.AddressWithType, error) {
	var deliveryAddresses []*pb.AddressWithType

	sortedAddresses := s.getSortedAddressesWithTypeForCardDelivery(ctx, actorId, addresses, isNri)

	uniqueAddresses := addressPkg.DedupeAddressesWithType(sortedAddresses)

	for _, addressWithType := range uniqueAddresses {
		addressType := addressWithType.Type
		if addressFlow != pb.AddressFlow_ADDRESS_FLOW_UNSPECIFIED {
			addressTypes, ok := addressFlowToApplicableAddressTypeMap[addressFlow]
			switch {
			case !ok:
				logger.Error(ctx, "invalid address flow, adding address to list", zap.String(logger.ACTOR_ID_V2, actorId),
					zap.String("address_flow", addressFlow.String()))
			case !lo.Contains(addressTypes, addressType):
				logger.Debug(ctx, "address type not valid for address flow", zap.String(logger.ACTOR_ID_V2, actorId),
					zap.String(logger.ADDRESS_TYPE, addressType.String()), zap.String("address_flow", addressFlow.String()))
				continue
			default:
			}
		}
		address := addressWithType.Address

		replaceInvalidEntities(ctx, address)
		clientPostalAddr, err := convertToClientPostalAddressType(address)
		if err != nil {
			logger.Error(ctx, "address format mismatch.", zap.Error(err))
			return nil, err
		}
		deliveryAddresses = append(deliveryAddresses, &pb.AddressWithType{
			Type:     addressType,
			Address:  clientPostalAddr,
			IsActive: true,
		})
	}
	if len(deliveryAddresses) > 0 && addressFlow == pb.AddressFlow_ADDRESS_FLOW_DEBIT_CARD {
		deliveryAddresses = []*pb.AddressWithType{deliveryAddresses[0]}
		logger.Debug(ctx, "fetched physical debit card delivery address", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ADDRESS_TYPE, deliveryAddresses[0].GetType().String()))
		isMailingAddressUpdateEnabled, err := s.isFeatureEnabled(ctx, actorId, types.Feature_FEATURE_DC_ORDER_SCREEN_WA_ADDRESS_UPDATE_FLOW)
		if err != nil {
			logger.Error(ctx, "error evaluating feature constraints", zap.Error(err), zap.String(logger.FEATURE, types.Feature_FEATURE_DC_ORDER_SCREEN_WA_ADDRESS_UPDATE_FLOW.String()))
		}

		if deliveryAddresses[0].GetType() == types.AddressType_MAILING &&
			isMailingAddressUpdateEnabled && isUserOnboarded {
			deliveryAddresses[0].InfoText = ui.NewITC().
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(updateAddressInfoBText, colorPkg.ColorLightPrimaryAction, commontypes.FontStyle_BUTTON_S)).
				WithDeeplink(pkgProfile.GetCommunicationAddressesUpdateViaWhatsappBottomSheetCta()).
				WithLeftVisualElementUrlHeightAndWidth(plusIconGreenUrl, 16, 16).
				WithLeftImagePadding(8)
		}
	}

	return deliveryAddresses, nil
}

// Replaced value for entities of a address that can be empty
func replaceInvalidEntities(ctx context.Context, address *postaladdress.PostalAddress) {
	if !addressPkg.IsValidAddressEntity(ctx, "Locality", address.Locality) {
		address.Locality = ""
	}
	if !addressPkg.IsValidAddressEntity(ctx, "Sublocality", address.Sublocality) {
		address.Sublocality = ""
	}
}

// Returned a ordered list of addresses where relevance of addresses decrease with increase in index in the list
// Sorting order:
//  1. Mailing addresses
//
// TODO(team): update sorting order and sorting items as more types of addresses keep getting added
func getSortedAddressesForCardDelivery(allAddresses map[string]*types.Addresses) []*postaladdress.PostalAddress {
	sortedAddresses := make([]*postaladdress.PostalAddress, 0)

	sortingOrder := []string{
		types.AddressType_MAILING.String(),
	}

	for _, addressType := range sortingOrder {
		if addresses, ok := allAddresses[addressType]; ok {
			sortedAddresses = append(sortedAddresses, addresses.Addresses...)
		}
	}
	return sortedAddresses
}

// Returned a ordered list of addresses where relevance of addresses decrease with increase in index in the list
// Sorting order:
// 1. Mailing address (a.k.a communication address)
func (s *Service) getSortedAddressesWithTypeForCardDelivery(ctx context.Context, actorId string,
	allAddresses map[string]*types.Addresses, isNri bool) []*types.AddressWithType {
	sortedAddresses := make([]*types.AddressWithType, 0)

	sortingOrder := []types.AddressType{
		types.AddressType_MAILING,
	}
	// Do not show permanent address for shipping card to NRI user
	if isNri {
		sortingOrder = []types.AddressType{
			types.AddressType_MAILING,
		}
	}

	for _, addressType := range sortingOrder {
		addresses, ok := allAddresses[addressType.String()]
		if !ok {
			continue
		}
		if len(addresses.Addresses) > 1 {
			logger.Error(ctx, "multiple addresses belonging to one type during customer onboarding",
				zap.String(logger.ADDRESS_TYPE, addressType.String()))
		}

		address := addresses.Addresses[0]
		sortedAddresses = append(sortedAddresses, &types.AddressWithType{
			Type:    addressType,
			Address: address,
		})
	}
	return sortedAddresses
}

func (s *Service) getCardShippingPreference(ctx context.Context, actorId string, shippingItem types.ShippingItem) ([]types.AddressType, error) {
	shippingPreference, err := s.client.GetShippingPreference(ctx, &bePb.GetShippingPreferenceRequest{
		ActorId:      actorId,
		ShippingItem: shippingItem,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while fetching shipping preference for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	case !shippingPreference.Status.IsSuccess():
		if !shippingPreference.Status.IsRecordNotFound() {
			logger.Error(ctx, "non success state while fetching "+
				"shipping preference for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return nil, fmt.Errorf("non success state while fetching shipping preference entry")
		} else {
			return nil, nil
		}
	}
	sortingOrder := make([]types.AddressType, 0)
	switch shippingPreference.GetPreference().GetAddressType() {
	case types.AddressType_MAILING:
		sortingOrder = append(sortingOrder, types.AddressType_MAILING, types.AddressType_SHIPPING, types.AddressType_PERMANENT)
	case types.AddressType_PERMANENT:
		sortingOrder = append(sortingOrder, types.AddressType_PERMANENT, types.AddressType_SHIPPING, types.AddressType_MAILING)
	case types.AddressType_SHIPPING, types.AddressType_CREDIT_CARD_SHIPPING:
		sortingOrder = append(sortingOrder, types.AddressType_SHIPPING, types.AddressType_MAILING, types.AddressType_PERMANENT)
	default:
		return nil, fmt.Errorf("invalid shipping address type %s", shippingPreference.GetPreference().GetAddressType().String())
	}
	return sortingOrder, nil
}

func isMinor(ts *timestamppb.Timestamp) (int, string) {
	nomineeAge := datetime.Age(ts.AsTime())
	if nomineeAge < events2.MinAge {
		return nomineeAge, "TRUE"
	}
	return nomineeAge, "FALSE"
}

func (s *Service) GetByPhoneNumber(ctx context.Context, req *pb.GetByPhoneNumberRequest) (*pb.GetByPhoneNumberResponse, error) {
	var (
		res       = &pb.GetByPhoneNumberResponse{}
		chatHeads []*frontend.ChatHead
		err       error
	)
	logger.Debug(ctx, "Checking if the user if Fi user")
	onboardedUserResp, err := s.onboardingClient.GetOnboardedUser(ctx, &beOnbPb.GetOnboardedUserRequest{
		Identifier: &beOnbPb.GetOnboardedUserRequest_PhoneNumber{
			PhoneNumber: req.GetPhoneNumber(),
		},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "GetOnboardedUser RPC failed", zap.String(logger.PHONE_NUMBER, req.GetPhoneNumber().ToString()),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil

	case onboardedUserResp.GetStatus().IsRecordNotFound() || onboardedUserResp.GetStatus().GetCode() == uint32(beOnbPb.GetOnboardedUserResponse_USER_NOT_ONBOARDED):
		// check if pay via phone number enabled for the user
		isEnable := s.isPayViaPhoneNumberEnabled(ctx, req.GetReq().GetAuth().GetActorId())
		if !isEnable {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		logger.Info(ctx, " finding valid VPAs for other PSPs", zap.String(logger.PHONE_NUMBER, req.GetPhoneNumber().ToString()))
		chatHeads, err = s.getChatHeadsForPhoneNumberVPAs(ctx, req.GetReq().GetAuth().GetActorId(), req.GetReq().GetAuth().GetDevice(), req.GetPhoneNumber())
		switch {
		case err != nil:
			res.Status = rpc.StatusInternal()
			return res, nil
		case len(chatHeads) == 0:
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		default:
			res.ChatHeads = chatHeads
			res.Status = rpc.StatusOk()
			return res, nil
		}
	case !onboardedUserResp.GetStatus().IsSuccess():
		logger.Error(ctx, "GetUser RPC returned non-ok status")
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	logger.Debug(ctx, "This is Fi user , so no other psp")
	// Fi user
	fiChatHead := &frontend.ChatHead{
		FullTitle:   gammanames.BestNameFromProfile(ctx, onboardedUserResp.GetUser().GetProfile()).ToString(),
		TitleLine_1: gammanames.BestNameFromProfile(ctx, onboardedUserResp.GetUser().GetProfile()).ToString(),
		IconImage:   &commontypes.Image{ImageUrl: onboardedUserResp.GetUser().GetProfile().GetProfileImageUrl()},
		ColourCode:  actorPb.GetColourCodeForActor(onboardedUserResp.GetActorId()),
		UserParams: &frontend.UserParams{
			PhoneNumber: req.GetPhoneNumber(),
		},
	}
	res.ChatHead = fiChatHead
	// appending the fi user to the other psp chat heads
	res.ChatHeads = append(res.ChatHeads, fiChatHead)
	res.Status = rpc.StatusOk()
	return res, nil
}

// getChatHeadsForPhoneNumberVPAs will return chat heads for the VPAs of type phoneNumber@Psp (eg. 8888888888@upi) , currently only for the number not onboarded with Fi
func (s *Service) getChatHeadsForPhoneNumberVPAs(ctx context.Context, actorId string, device *commontypes.Device, phoneNumber *commontypes.PhoneNumber) ([]*frontend.ChatHead, error) {
	var chatHeads []*frontend.ChatHead
	ph, err := s.beGetActorPhoneNumber(ctx, actorId)
	if err != nil {
		return nil, err
	}
	getVpasByPhoneNumberResults, err := s.upiClient.GetVerifiedVpasByPhoneNumber(ctx, &upiPb.GetVerifiedVpasByPhoneNumberRequest{
		Device:       upiPb.FromFeDevice(ctx, device, ph),
		PhoneNumber:  phoneNumber,
		PayerActorId: actorId,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "GetVerifiedVpasByPhoneNumber RPC failed", zap.String(logger.PHONE_NUMBER, phoneNumber.ToString()),
			zap.Error(err))
		return nil, err
	case getVpasByPhoneNumberResults.GetStatus().IsRecordNotFound():
		logger.Info(ctx, "No valid VPA returned by get_vpas_for_phone_number")
		return nil, nil
	case !getVpasByPhoneNumberResults.GetStatus().IsSuccess():
		logger.Error(ctx, "GetVerifiedVpasByPhoneNumber returned non-ok status: ", zap.Uint32(logger.RPC_STATUS, getVpasByPhoneNumberResults.GetStatus().GetCode()))
		return nil, rpc.StatusAsError(getVpasByPhoneNumberResults.GetStatus())
	}
	for _, pspInfo := range getVpasByPhoneNumberResults.GetVpaInfo() {
		chatHead := &frontend.ChatHead{
			FullTitle:   pspInfo.PayeeName,
			TitleLine_1: pspInfo.PayeeName,
			ColourCode:  actorPb.GetColourCodeForActor(pspInfo.GetPayeeName()),
			UserParams: &frontend.UserParams{
				Vpa: pspInfo.Vpa,
			},
			BadgeImage: pspInfo.PspBadgeIcon,
		}
		chatHeads = append(chatHeads, chatHead)
	}
	return chatHeads, nil
}

// isPayViaPhoneNumber will check if user falls under the %age roll out or in the user group
func (s *Service) isPayViaPhoneNumberEnabled(ctx context.Context, actorId string) bool {
	enablePayViaPhoneNumber, err := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_PAY_VIA_PHONE_NUMBER).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to check if Pay Via Phone Number is enabled", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return false
	}
	return enablePayViaPhoneNumber
}
func (s *Service) getFirebaseUserProperties(ctx context.Context, actorId string, versionCode uint32, groups []commontypes.UserGroup) (map[string]string, error) {
	userProperties := make(map[string]string)
	// set KYC level properties
	bankCustomerInfo, errResp := s.bcClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if err := epifigrpc.RPCError(bankCustomerInfo, errResp); err != nil && !rpc.StatusFromError(err).IsRecordNotFound() {
		logger.Error(ctx, fmt.Sprintf("failed to fetch bank customer details of the user, userId: %v ", bankCustomerInfo.GetBankCustomer().GetUserId()), zap.Error(err))
		return nil, err
	}
	userProperties[pb.FirebaseProperty_KYC_LEVEL.String()] = bankCustomerInfo.GetBankCustomer().GetKycInfo().GetKycLevel().String()

	if err := s.SetGroupFlagsInUserProperties(ctx, actorId, groups, userProperties); err != nil {
		return nil, err
	}

	// set version code property
	userProperties[pb.FirebaseProperty_VERSION_CODE.String()] = strconv.FormatInt(int64(versionCode), 10)
	return userProperties, nil
}

func (s *Service) SetGroupFlagsInUserProperties(ctx context.Context, actorId string, groups []commontypes.UserGroup,
	userProperties map[string]string) error {
	// set flags to false initially
	userProperties[pb.FirebaseProperty_IS_INTERNAL_USER.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_FNF_USER.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_CONNECTED_ACC_USER.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_INVESTMENT_USER.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_P2P_INVESTMENT_USER.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_SALARY_PROGRAM_USER.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_PREAPPROVED_LOAN_USER.String()] = TrueString
	userProperties[pb.FirebaseProperty_IS_CX_INTERNAL_USER.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_INVEST_LANDING_INT.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_CREDIT_CARD_ENABLED.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_USSTOCKS_ENABLED.String()] = FalseString
	userProperties[pb.FirebaseProperty_IS_FI_LITE_USER.String()] = FalseString

	// set firebase property flags to true if group is present
	setFireBaseUserProps(groups, userProperties)

	enableMFInvestmentUI, err := s.releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(types.Feature_INVESTMENT_MF_UI).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to check if MF investment UI is enabled", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return err
	}
	if enableMFInvestmentUI {
		userProperties[pb.FirebaseProperty_IS_INVESTMENT_USER.String()] = TrueString
	}
	return nil
}

func setFireBaseUserProps(groups []commontypes.UserGroup, userProperties map[string]string) {
	for _, group := range groups {
		switch group {
		case commontypes.UserGroup_INTERNAL:
			userProperties[pb.FirebaseProperty_IS_INTERNAL_USER.String()] = TrueString
		case commontypes.UserGroup_FNF:
			userProperties[pb.FirebaseProperty_IS_FNF_USER.String()] = TrueString
		case commontypes.UserGroup_CONNECTED_ACCOUNT:
			userProperties[pb.FirebaseProperty_IS_CONNECTED_ACC_USER.String()] = TrueString
		case commontypes.UserGroup_P2P_INVESTMENT_INTERNAL:
			userProperties[pb.FirebaseProperty_IS_P2P_INVESTMENT_USER.String()] = TrueString
		case commontypes.UserGroup_SALARY_PROGRAM_WHITELIST:
			userProperties[pb.FirebaseProperty_IS_SALARY_PROGRAM_USER.String()] = TrueString
		case commontypes.UserGroup_CX_INTERNAL:
			userProperties[pb.FirebaseProperty_IS_CX_INTERNAL_USER.String()] = TrueString
		case commontypes.UserGroup_INVEST_LANDING_INT:
			userProperties[pb.FirebaseProperty_IS_INVEST_LANDING_INT.String()] = TrueString
		case commontypes.UserGroup_CREDIT_CARD_INTERNAL:
			userProperties[pb.FirebaseProperty_IS_CREDIT_CARD_ENABLED.String()] = TrueString
		case commontypes.UserGroup_USSTOCKS_INTERNAL:
			userProperties[pb.FirebaseProperty_IS_USSTOCKS_ENABLED.String()] = TrueString
		}
	}
}

// GetLegalName returns user's kyc name to show pre-filled name on confirm card mailing address screen.
func (s *Service) GetLegalName(ctx context.Context, req *pb.GetLegalNameRequest) (*pb.GetLegalNameResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	actorResp, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		logger.Error(ctx, "error while fetching actor details", zap.Error(te))
		return &pb.GetLegalNameResponse{
			Status: rpc.StatusInternal(),
		}, te
	}
	// get user info
	userResp, err := s.client.GetUser(ctx, &bePb.GetUserRequest{
		Identifier: &bePb.GetUserRequest_Id{
			Id: actorResp.GetActor().GetEntityId(),
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		logger.Error(ctx, "error while fetching user details", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		return &pb.GetLegalNameResponse{
			Status: rpc.StatusInternal(),
		}, te
	}
	if len(userResp.GetUser().GetProfile().GetKycName().ToString()) > 20 {
		logger.Info(ctx, "kyc name greater than 20 characters")
		return &pb.GetLegalNameResponse{Status: rpc.StatusOk()}, nil
	}
	return &pb.GetLegalNameResponse{
		Status:    rpc.StatusOk(),
		LegalName: userResp.GetUser().GetProfile().GetKycName(),
	}, nil
}

// ConfirmCardPreferences checks if the user entered debit card name matches with KYC and PAN name and if we have user's shipping preference
// for card delivery address and returns the next action to UI accordingly.
func (s *Service) ConfirmCardPreferences(ctx context.Context, req *pb.ConfirmCardPreferencesRequest) (*pb.ConfirmCardPreferencesResponse, error) {
	if err := validateNameLength(ctx, req.GetDebitCardName()); err != nil {
		return &pb.ConfirmCardPreferencesResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg(err.Error()),
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusFailedPreconditionWithDebugMsg(err.Error()),
				ErrorView: feErrors.NewInlineErrorView("INVALID_NAME_LENGTH", "Name cannot be greater than 20 "+
					"characters or less than 2 characters"),
			},
		}, err
	}
	actorId := req.GetReq().GetAuth().GetActorId()
	debitCardNameCheckResp, err1 := s.onboardingClient.DebitCardNameCheck(ctx, &beOnbPb.DebitCardNameCheckRequest{
		ActorId:       actorId,
		DebitCardName: req.GetDebitCardName(),
		SkipNameCheck: req.GetSkipNameCheck(),
	})
	switch {
	case err1 != nil:
		logger.Error(ctx, "error in debit card name check", zap.Error(err1))
		return &pb.ConfirmCardPreferencesResponse{Status: rpc.StatusInternal()}, err1
	case debitCardNameCheckResp.GetStatus().Code == uint32(beOnbPb.DebitCardNameCheckResponse_NAME_CHECK_FAILED):
		logger.Info(ctx, "name match failed for the user")
		nextAction, err2 := s.getNextAction(ctx, req.GetReq().GetAuth())
		if err2 != nil {
			return &pb.ConfirmCardPreferencesResponse{Status: rpc.StatusInternal()}, err2
		}
		if nextAction.GetScreen() == deeplink.Screen_CONFIRM_CARD_MAILING_ADDRESS {
			return &pb.ConfirmCardPreferencesResponse{
				Status:     rpc.NewStatus(101, "NameMatchFailed", "debit card name match failed"),
				NextAction: nextAction,
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(101, "NameMatchFailed", "debit card name match failed"),
					ErrorView: feErrors.NewInlineErrorView("NAME_CHECK_FAILED", "Only your full PAN or KYC name or its initials are allowed. "+
						"For example John Doe can have J Doe or John D as Debit Card Name"),
				},
			}, nil
		}
		return &pb.ConfirmCardPreferencesResponse{
			Status:     rpc.StatusOk(),
			NextAction: nextAction,
		}, nil
	case !debitCardNameCheckResp.GetStatus().IsSuccess():
		logger.Error(ctx, "non success response for debit card name check", zap.String(logger.STATUS_CODE,
			debitCardNameCheckResp.GetStatus().String()))
		return &pb.ConfirmCardPreferencesResponse{Status: rpc.StatusInternal()}, nil
	default:
		nextAction, err4 := s.getNextAction(ctx, req.GetReq().GetAuth())
		if err4 != nil {
			return &pb.ConfirmCardPreferencesResponse{Status: rpc.StatusInternal()}, err4
		}
		logger.Info(ctx, "confirm card details successful")
		return &pb.ConfirmCardPreferencesResponse{
			Status:     rpc.StatusOk(),
			NextAction: nextAction,
		}, nil
	}
}

// Adds app instance IDs for different third party vendors. Eg. client_appsFlyer_id for AppsFlyer, app_instance_id for Firebase, etc.
func (s *Service) AddAppInstanceIdentifiers(ctx context.Context, req *pb.AddAppInstanceIdentifiersRequest) (*pb.AddAppInstanceIdentifiersResponse, error) {
	resp := &pb.AddAppInstanceIdentifiersResponse{RespHeader: &header.ResponseHeader{}}

	// TODO(bhrigu) : Add check for uniqueness of each key name for app instance IDs

	prospectId := req.Req.ProspectId
	actorId := req.GetReq().GetAuth().GetActorId()

	// append Prospect ID to the list of app identifiers
	// Prospect ID is sent as part of the request headers and not list of identifiers from the client
	req.Identifiers = append(req.Identifiers, &types.AppInstanceId{
		Name:  types.AppInstanceIdName_PROSPECT_ID,
		Value: prospectId,
	})

	beRes, err := s.client.AddAppInstanceIdentifiers(ctx, &bePb.AddAppInstanceIdentifiersRequest{
		ActorId:     actorId,
		Identifiers: req.Identifiers,
	})
	if er := epifigrpc.RPCError(beRes, err); er != nil {
		logger.Error(ctx, "error received while adding app instance identifiers", zap.Any(logger.APP_INSTANCE_IDENTIFIERS, req.Identifiers), zap.Error(err))
		resp.RespHeader.Status = rpc.StatusInternal()
		return resp, er
	}

	newCtx := epificontext.WithEventAttributes(ctx)
	newCtx = context.WithValue(newCtx, epificontext.CtxActorKey, actorId)

	// Upsert Client AppsFlyer ID to vendormapping table async
	goroutine.Run(ctx, 20*time.Second, func(ctx context.Context) {
		isClientAppsFlyerIdSent := false
		for _, identifier := range req.Identifiers {
			if identifier.Name == types.AppInstanceIdName_CLIENT_APPSFLYER_ID {
				isClientAppsFlyerIdSent = true
				if _, err = vendormapping.GetOrCreateVendorMapping(newCtx, prospectId, s.vendorMappingClient); err == nil {
					_ = vendormapping.SyncClientAppsflyerIdInVendorMapping(newCtx, identifier.Value, prospectId, s.vendorMappingClient)
				}
			}
		}
		if !isClientAppsFlyerIdSent {
			logger.Info(newCtx, "client appsFlyer ID not sent by the client in AddAppInstanceIdentifiers call", zap.Any(logger.APP_INSTANCE_IDENTIFIERS, req.Identifiers))
		}
	})

	resp.RespHeader.Status = rpc.StatusOk()
	return resp, nil
}

// As of this comment, this rpc is only being used to sync the Appsflyer id.
func (s *Service) SyncVendorIDs(ctx context.Context, req *pb.SyncVendorIDsRequest) (*pb.SyncVendorIDsResponse, error) {
	res := &pb.SyncVendorIDsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}
	prospectId := req.GetReq().GetProspectId()
	allUpdatesDone := true
	// TODO(Shivam): Remove this post debugging
	if prospectId == "" {
		logger.Info(ctx, "sending prospect id: <empty>", zap.String(logger.DEVICE_ID, obfuscator.Hashed(req.GetReq().GetAuth().GetDevice().GetDeviceId())))
	} else {
		logger.Info(ctx, fmt.Sprintf("sending prospect id: %s", prospectId), zap.String(logger.DEVICE_ID, obfuscator.Hashed(req.GetReq().GetAuth().GetDevice().GetDeviceId())))
	}
	for _, identifier := range req.GetIdentifiers() {
		switch identifier.GetName() {
		case types.AppInstanceIdName_CLIENT_APPSFLYER_ID:
			if err := vendormapping.SyncClientAppsflyerIdInVendorMapping(ctx, identifier.Value, prospectId, s.vendorMappingClient); err != nil {
				logger.Error(ctx, "failed to sync clients appsFlyerId", zap.String(logger.PROSPECT_ID, prospectId), zap.Error(err),
					zap.String(logger.DEVICE_ID, obfuscator.Hashed(req.GetReq().GetAuth().GetDevice().GetDeviceId())))
				allUpdatesDone = false
			}
		default:
			logger.Error(ctx, "not handling this identifier", zap.String(logger.APP_INSTANCE_NAME, identifier.GetName().String()))
			res.RespHeader.Status = rpc.StatusInvalidArgument()
		}
	}
	if !allUpdatesDone {
		logger.Error(ctx, "failed to update for one or more identifiers")
		res.RespHeader.Status = rpc.StatusInternalWithDebugMsg("failed to update for one or more identifiers")
	}
	return res, nil
}

// Fetch MoEngageId for the given actor Id
func (s *Service) GetMoEngageId(ctx context.Context, req *pb.GetMoEngageIdRequest) (*pb.GetMoEngageIdResponse, error) {
	resp := &pb.GetMoEngageIdResponse{RespHeader: &header.ResponseHeader{}}
	actorId := req.GetReq().GetAuth().GetActorId()

	vmRes, err := s.vendorMappingClient.GetDPMappingById(ctx, &vmPb.GetDPMappingByIdRequest{Identifier: &vmPb.GetDPMappingByIdRequest_ActorId{ActorId: actorId}})
	if er := epifigrpc.RPCError(vmRes, err); er != nil {
		if vmRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "Error record not found for the actor in vendor mapping")
			resp.RespHeader.Status = rpc.StatusRecordNotFound()
			return resp, nil
		}
		logger.Error(ctx, "error received while fetching DP Vendor Mapping response", zap.Error(err))
		resp.RespHeader.Status = rpc.StatusInternal()
		return resp, er
	}

	if vmRes.MoengageId == "" {
		logger.Error(ctx, "MoengageID not found for the Actor")
		resp.RespHeader.Status = rpc.StatusRecordNotFound()
		return resp, nil
	}

	resp.MoengageId = vmRes.MoengageId
	resp.RespHeader.Status = rpc.StatusOk()
	return resp, nil
}

func validateNameLength(ctx context.Context, name *commontypes.Name) error {
	nameLen := len(name.ToString())
	switch {
	case nameLen < 2:
		logger.Info(ctx, "debit card name validation failed: specified name too short", zap.Int(logger.LENGTH, nameLen))
		return fmt.Errorf("short name validation failed")
	case nameLen > 20:
		logger.Info(ctx, "debit card name validation failed: specified name too long", zap.Int(logger.LENGTH, nameLen))
		return fmt.Errorf("long name validation failed")
	default:
		return nil
	}
}

func (s *Service) GetDebitCardName(ctx context.Context, req *pb.GetDebitCardNameRequest) (*pb.GetDebitCardNameResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	actorResp, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorResp, err); te != nil {
		logger.Error(ctx, "error while fetching actor details", zap.Error(te))
		return &pb.GetDebitCardNameResponse{
			Status: rpc.StatusInternal(),
		}, te
	}
	// get user info
	userResp, err := s.client.GetUser(ctx, &bePb.GetUserRequest{
		Identifier: &bePb.GetUserRequest_Id{
			Id: actorResp.GetActor().GetEntityId(),
		},
	})
	if te := epifigrpc.RPCError(userResp, err); te != nil {
		logger.Error(ctx, "error while fetching user details", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		return &pb.GetDebitCardNameResponse{
			Status: rpc.StatusInternal(),
		}, te
	}
	var debitCardName *commontypes.Name
	if len(userResp.GetUser().GetProfile().GetDebitCardName().ToString()) == 0 {
		debitCardName = userResp.GetUser().GetProfile().GetKycName()
	} else {
		debitCardName = userResp.GetUser().GetProfile().GetDebitCardName()
	}
	return &pb.GetDebitCardNameResponse{
		Status:        rpc.StatusOk(),
		DebitCardName: debitCardName,
	}, nil
}

// getDeeplinkActionFromPinState gets the deeplink action from the fe pin set state
// nolint: dupl, unparam
func (s *Service) getDeeplinkActionFromPinState(ctx context.Context, upiPinSetState feUpiPb.PinSetState, accountId, actorId, derivedAccountId string) (*deeplinkPb.Deeplink, error) {

	tpapAccountId, err := fePkgUpi.GetTpapAccountIdFromDerivedId(derivedAccountId)
	if err != nil {
		return nil, fmt.Errorf("error getting tpapAccountId from derived account idL err = %w", err)
	}

	// 1. If account is Tpap, user should move to new screen (UPI_PIN_SETUP_V2)
	//
	// 2. If user is currently onboarding then show new screen (UPI_PIN_SETUP_V2) [TEMPORARY Backend Fix till iOS client implements UI changes for PIN screen]
	// ISSUE:
	// - Due to Race condition in GetUserSessionDetails response where TPAP account ID
	//   may not be available when deciding which PIN setup screen to show we end up sending old screen (non-compliant).
	// SOLUTION (TEMPORARY BACKEND FIX):
	// - Quick fix from backend until iOS client implements UI changes for PIN screen
	// - Show new screen if user is currently onboarding (i.e. user is onboarded >= year 2025)
	if tpapAccountId != "" || s.shouldShowPinScreenV2ForRecentlyOnbUsers(ctx, actorId, accountId) {
		return s.getDeeplinkActionFromPinStateForTpapAccount(upiPinSetState, accountId, derivedAccountId)
	}

	// it will be used to store deeplink associated with upi pin set state
	var deeplinkAction *deeplinkPb.Deeplink

	switch upiPinSetState {
	case feUpiPb.PinSetState_PIN_NOT_SET, feUpiPb.PinSetState_REOOBE_PIN_NOT_SET:
		deeplinkAction = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_UPI_PIN_SETUP,
			ScreenOptions: &deeplinkPb.Deeplink_UpiPinSetupOptions{
				UpiPinSetupOptions: &deeplinkPb.UpiPinSetupOptions{
					NpciFlowType:     types.NpciFlowType_SET_PIN,
					AccountId:        accountId,
					DerivedAccountId: derivedAccountId,
				},
			},
		}
		// deeplink is set for this case to inform user that the pin has already been set on another psp
		// TODO(Ankit): change the message here, since now we have other bank accounts
	case feUpiPb.PinSetState_ETB_PIN_SET:
		deeplinkAction = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_ETB_UPI_PIN_SET_ACTIVITY,
			ScreenOptions: &deeplinkPb.Deeplink_UpiEtbPinSetActivityOptions{
				UpiEtbPinSetActivityOptions: &deeplinkPb.UpiETBPinSetActivityOptions{
					Title:            s.config.UpiPinSetMessage.EtbPinSetTitle,
					Body:             s.config.UpiPinSetMessage.EtbPinSetBody,
					AccountId:        accountId,
					DerivedAccountId: derivedAccountId,
					UserActivity:     user_activity.UserActivity_UPI_ETB_MESSAGE_SEEN,
				},
			},
		}
	default:
		// no action required for UPI_PIN_SET  state
		deeplinkAction = nil
	}

	return deeplinkAction, nil
}

// getDeeplinkActionFromPinStateForTpapAccount - get the deeplink action from pin state for tpap account
// user should go to new upi pin set screen (UPI_PIN_SETUP_V2)
// nolint: dupl
func (s *Service) getDeeplinkActionFromPinStateForTpapAccount(upiPinSetState feUpiPb.PinSetState, accountId, derivedAccountId string) (*deeplinkPb.Deeplink, error) {
	vendor := commonvgpb.Vendor_FEDERAL_BANK

	// client req id to be passed to the backend
	// from client to store consent corresponding to
	// it, and pass the same to vendor
	// so that we can have a common id throughout
	txnId, err := pay.GenerateVendorRequestId(vendor, paymentPb.PaymentProtocol_UPI)
	if err != nil {
		return nil, fmt.Errorf("error generate vendor req id, err = %w", err)
	}

	// it will be used to store deeplink associated with upi pin set state
	var deeplinkAction *deeplinkPb.Deeplink

	switch upiPinSetState {
	case feUpiPb.PinSetState_PIN_NOT_SET, feUpiPb.PinSetState_REOOBE_PIN_NOT_SET:
		deeplinkAction = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_UPI_PIN_SETUP_V2,
			ScreenOptions: &deeplinkPb.Deeplink_UpiPinSetupOptionsV2{
				UpiPinSetupOptionsV2: &deeplinkPb.UpiPinSetupOptionsV2{
					NpciFlowType:     types.NpciFlowType_SET_PIN,
					DerivedAccountId: derivedAccountId,
					ClientReqId:      txnId,
				},
			},
		}
		// deeplink is set for this case to inform user that the pin has already been set on another psp
		// TODO(Ankit): change the message here, since now we have other bank accounts
	case feUpiPb.PinSetState_ETB_PIN_SET:
		deeplinkAction = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_ETB_UPI_PIN_SET_ACTIVITY,
			ScreenOptions: &deeplinkPb.Deeplink_UpiEtbPinSetActivityOptions{
				UpiEtbPinSetActivityOptions: &deeplinkPb.UpiETBPinSetActivityOptions{
					Title:            s.config.UpiPinSetMessage.EtbPinSetTitle,
					Body:             s.config.UpiPinSetMessage.EtbPinSetBody,
					AccountId:        accountId,
					DerivedAccountId: derivedAccountId,
					UserActivity:     user_activity.UserActivity_UPI_ETB_MESSAGE_SEEN,
				},
			},
		}
	default:
		// no action required for UPI_PIN_SET  state
		deeplinkAction = nil
	}

	return deeplinkAction, nil
}

// convertInternalAccountToFeAccount converts the internal account into fe account
func (s *Service) convertInternalAccountToFeAccount(
	ctx context.Context,
	account *savingsPb.SavingsAccountEssentials,
	isPrimary bool,
	accountPinStateMap map[string]upiPb.PinSetState,
	appVersionCode uint32) (*feAccounts.Account, error) {
	derivedAccountIdProto := &accounts.DerivedAccountId{
		InternalAccountId: account.GetId(),
	}

	derivedAccountIdString, err := idgen.EncodeProtoToStdBase64(derivedAccountIdProto)
	if err != nil {
		return nil, fmt.Errorf("failed to encode derived account id %w", err)
	}
	upiPinSetState := feUpiPb.FromBeToFePinSetState(ctx, s.config.Flags.UpiMaxRetriesPinSet, accountPinStateMap[account.GetId()], appVersionCode)
	deeplinkAction, err := s.getDeeplinkActionFromPinState(ctx, upiPinSetState, account.GetId(), account.GetActorId(), derivedAccountIdString)
	if err != nil {
		return nil, fmt.Errorf("error getting deeplink action: err = %w", err)
	}

	feAccountDetails := &feAccounts.Account{
		AccountId:              account.GetId(),
		MaskedAccountNumber:    mask.GetMaskedAccountNumber(account.GetAccountNo(), ""),
		PartnerBank:            (*s.config.VendorToBankNameMap)[account.GetPartnerBank().String()],
		BankLogoUrl:            s.config.BankIcon.FederalFiIconUrl,
		AccountType:            accounts.Type_SAVINGS,
		AccountProductOffering: account.GetSkuInfo().GetAccountProductOffering(),
		Capabilities: []feAccounts.AccountCapability{
			feAccounts.AccountCapability_CREDIT,
			feAccounts.AccountCapability_DEBIT,
		},
		Identifier: &feAccounts.Account_SavingsAccount{
			SavingsAccount: &feAccounts.SavingsAccount{
				PinSetState: upiPinSetState,
				Action:      deeplinkAction,
			},
		},
		IsPrimaryAccount: isPrimary,
		DerivedAccountId: derivedAccountIdString,
		AccountProvenances: []enums.AccountProvenance{
			enums.AccountProvenance_ACCOUNT_PROVENANCE_INTERNAL,
		},
		PinSetInfo: &feAccounts.PinSetInfo{
			PinSetState: upiPinSetState,
			Action:      deeplinkAction,
		},
		IfscCode: account.GetIfscCode(),
	}
	return feAccountDetails, nil
}

// checkIfTpapOneOfInternalAccounts checks if the TPAP account is one of the internal accounts or not.
// if yes, it returns the corresponding internal account.
func checkIfTpapOneOfInternalAccounts(internalAccounts []*savingsPb.SavingsAccountEssentials, tpapAccount *onboarding.UpiAccount) *savingsPb.SavingsAccountEssentials {
	for _, internalAccount := range internalAccounts {
		if internalAccount.GetId() == tpapAccount.GetAccountRefId() {
			return internalAccount
		}
	}

	return nil
}

// checkIfTpapAccountConnectedWithAA checks if the tpap account is duplicate of any connected account
// if yes, it returns true along with connected account
// else if returns false with empty string
func (s *Service) checkIfTpapAccountConnectedWithAA(
	ctx context.Context,
	connectedAccounts []*external.AccountDetails,
	tpapAccount *onboarding.UpiAccount) (*external.AccountDetails, bool, error) {

	if len(connectedAccounts) == 0 {
		return nil, false, nil
	}

	for _, connectedAccount := range connectedAccounts {
		accType, err := fePkgCA.GetAccountTypeForAaAccount(connectedAccount)
		if err != nil {
			return nil, false, fmt.Errorf("error while fetching account type for connected account : %w", err)
		}

		if tpapAccount.GetIfscCode() == connectedAccount.GetIfscCode() &&
			(pay.IsAccountNumberMatch(tpapAccount.GetMaskedAccountNumber(), connectedAccount.GetMaskedAccountNumber())) &&
			tpapAccount.GetAccountType() == accType {
			return connectedAccount, true, nil
		}
	}
	return nil, false, nil
}

func getRequestPayloadToFetchConnectedAccountDetails(connectedAccounts []*external.AccountDetails) *connectedAccountPb.GetAccountDetailsBulkRequest {
	req := &connectedAccountPb.GetAccountDetailsBulkRequest{
		AccountIdList: make([]string, 0),
		AccountDetailsMaskList: []external.AccountDetailsMask{
			external.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY,
		},
	}

	for _, connectedAccount := range connectedAccounts {
		req.AccountIdList = append(req.AccountIdList, connectedAccount.GetAccountId())
	}
	return req
}

// getTPAPAccounts gets all the active tpap accounts for the user
func (s *Service) getTPAPAccounts(ctx context.Context, actorId string) ([]*onboarding.UpiAccount, error) {
	res, err := s.upiOnboardingClient.GetAccounts(ctx, &onboarding.GetAccountsRequest{
		ActorId: actorId,
		AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
			upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE}})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			logger.Debug(ctx, "no tpap accounts found for actor", zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, nil
		}
		return nil, fmt.Errorf("error fetching tpap accounts for actor: %s %w", actorId, err)
	}
	return res.GetAccounts(), nil
}

// getConnectedAccounts gets all the connected accounts for the user
func (s *Service) getConnectedAccounts(ctx context.Context, actorId string, accountFilter []external.AccountFilter, accountInstType []connectedAccountEnums.AccInstrumentType) ([]*external.AccountDetails, error) {
	if !s.isConnectedAccountEnabledForUser(ctx, actorId) {
		return nil, nil
	}

	res, err := s.connectedAccountClient.GetAccounts(ctx, &connectedAccountPb.GetAccountsRequest{
		ActorId:               actorId,
		AccountFilterList:     accountFilter,
		AccInstrumentTypeList: accountInstType,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsRecordNotFound() {
			logger.Debug(ctx, "no connected account for actor", zap.String(logger.ACTOR_ID_V2, actorId))
			return nil, nil
		}
		return nil, fmt.Errorf("error fetching connected accounts for actor: %s %w", actorId, err)
	}
	return res.GetAccountDetailsList(), nil
}

// getMergedAccounts gets the all the accounts internal, connected account, tpap account, and makes sure that we do not show duplicates
// Also in case of duplicate, it adds the id of the connected account and internal account to the tpap account along with the provenance
// while creating the fe account
// Eg- an account can be both tpap and internal, we should show only one account in this case
// Also if an account can be both internal and connected account, we will be showing two accounts in this case as per existing
// behaviour
func (s *Service) getMergedAccounts(
	ctx context.Context,
	internalAccounts []*savingsPb.SavingsAccountEssentials, connectedAccounts []*external.AccountDetails, tpapAccounts []*onboarding.UpiAccount,
	accountPinStateMap map[string]upiPb.PinSetState,
	appVersionCode uint32,
	entrypoint pb.EntryPoint,
) ([]*feAccounts.Account, error) {
	var (
		feAccts                  []*feAccounts.Account
		isPrimaryAccountAssigned bool
		accountToPinSetStatusMap map[string]upiOnboardingEnumsPb.UpiPinSetStatus
		err                      error
	)

	//  accIdsConnectedWithTpapMap contains the ids of the connected accounts and internal accounts that are tpap accounts also
	accIdsConnectedWithTpapMap := make(map[string]bool)

	// we only fetch pin set status of all the tpap accounts in case of user session details
	if entrypoint == pb.EntryPoint_ENTRY_POINT_USER_SESSION_DETAILS {
		accountToPinSetStatusMap, err = s.fetchUpiPinStatusForTpapAccounts(ctx, tpapAccounts)
		if err != nil {
			return nil, fmt.Errorf("error in fetching tpap accounts pin status %w", err)
		}
	}

	// remove this code once AA team starts sending ifsc in GetAccounts rpc
	if connectedAccounts, err = s.populateIfscDetailsForConnectedAccounts(ctx, connectedAccounts); err != nil {
		return nil, fmt.Errorf("error while populating ifsc code for connected accounts")
	}

	// Convert all TPAP accounts to FE Accounts for display purpose.
	// Also figure out the internal and connected accounts that are TPAP accounts as well.
	for _, tpapAccount := range tpapAccounts {
		internalAcc, connectedAccount, dupErr := s.getDuplicateInternalAndConnectedAccountForTPAP(ctx, internalAccounts, connectedAccounts, tpapAccount, accIdsConnectedWithTpapMap)
		if dupErr != nil {
			return nil, dupErr
		}

		isPrimary, feAcct, convertErr := s.convertTpapAccountToFeAccount(ctx, tpapAccount, internalAcc, connectedAccount, accountToPinSetStatusMap)
		if convertErr != nil {
			return nil, fmt.Errorf("error in converting to fe account %w", convertErr)
		}

		// Capture that we have assigned primary account, i.e. one of the TPAP accounts is primary.
		//
		// If we end up not finding any primary TPAP account (can happen in case of no TPAP accounts), we will proceed
		// further to consider one of non-TPAP internal accounts as primary (refer logic in convertNonTPAPInternalAccountsToFe).
		//
		// History behind this:
		// 1. Concept of Primary account came with TPAP accounts since you can select which account to by-default receive money into by changing the primary account.
		// 2. Though, for users who are yet to migrate to TPAP, they technically don't have any primary account. Thus, for them, this field isPrimaryAccountAssigned will always be false.
		// 3. This is used to pass on to the later logic where a non-TPAP-internal account is considered as primary.
		// 4. The field IsPrimaryAccount (https://github.com/epiFi/gamma/blob/53528fbb0afef109c386beefcbdb7fb0865b3eae/api/frontend/account/account.pb.go#L206-L207)
		//    is set based on an account is primary or not. It is used at client side for surfacing few more things (ordering, prioritising this account over others) in some flows.
		if isPrimary && !isPrimaryAccountAssigned {
			isPrimaryAccountAssigned = true
		}

		// Ordering is important as we want to show internal account first and then all other accounts.
		if internalAcc != nil {
			feAccts = append([]*feAccounts.Account{feAcct}, feAccts...)
			continue
		}

		feAccts = append(feAccts, feAcct)
	}

	// Converting any internal account that is not a TPAP account to FE Account for display purpose.
	feInternalAccts, feInternalAccErr := s.convertNonTPAPInternalAccountsToFe(ctx, accIdsConnectedWithTpapMap, internalAccounts, !isPrimaryAccountAssigned, accountPinStateMap, appVersionCode)
	if feInternalAccErr != nil {
		return nil, feInternalAccErr
	}
	// Ordering is important as we show internal accounts first and then all other accounts.
	if feInternalAccts != nil {
		feAccts = append(feInternalAccts, feAccts...)
	}

	// Converting any connected account that is not a TPAP account to FE Account for display purpose.
	feConnectedAccs, feConnectedAccsErr := s.convertNonTPAPConnectedAccountsToFe(ctx, accIdsConnectedWithTpapMap, connectedAccounts)
	if feConnectedAccsErr != nil {
		return nil, feConnectedAccsErr
	}
	feAccts = append(feAccts, feConnectedAccs...)

	return feAccts, nil
}

// populateIfscDetailsForConnectedAccounts: populates the ifsc for the connected accounts by fetching connected accounts details
func (s *Service) populateIfscDetailsForConnectedAccounts(ctx context.Context, connectedAccounts []*external.AccountDetails) ([]*external.AccountDetails, error) {
	if len(connectedAccounts) == 0 {
		return connectedAccounts, nil
	}

	getAccountDetailsBulkReq := getRequestPayloadToFetchConnectedAccountDetails(connectedAccounts)
	getAccountsDetailsBulkRes, err := s.connectedAccountClient.GetAccountDetailsBulk(ctx, getAccountDetailsBulkReq)
	if err = epifigrpc.RPCError(getAccountsDetailsBulkRes, err); err != nil {
		return nil, fmt.Errorf("error while fetching get account details in bulk : %w", err)
	}

	connectedAccountDetails := make([]*external.AccountDetails, 0)
	for _, connectedAccount := range connectedAccounts {
		// fetching connected account again because we don't have ifsc
		connectedAccountDetail, ok := getAccountsDetailsBulkRes.GetAccountDetailsMap()[connectedAccount.GetAccountId()]
		if !ok {
			// even if we fail to get the ifsc, still we will need to pass it remaining flow
			connectedAccountDetails = append(connectedAccountDetails, connectedAccountDetail.GetAccountDetails())

			logger.Debug(ctx, "connected account detail not found", zap.String(logger.ACCOUNT_ID, connectedAccount.GetAccountId()),
				zap.String(logger.ACTOR_ID_V2, connectedAccount.GetActorId()))

			continue
		}

		connectedAccountDetails = append(connectedAccountDetails, connectedAccountDetail.GetAccountDetails())
	}
	return connectedAccountDetails, nil
}

// getDuplicateInternalAndConnectedAccountForTPAP checks if the TPAP account is duplicate of internal and connected account (AA).
// If yes, it returns the corresponding internal account and connected account.
// It also maintains a map of account ids (Internal, CA both) that are connected with TPAP account.
//
// Note: It is not necessary that TPAP account is duplicate of both connected and internal account.
func (s *Service) getDuplicateInternalAndConnectedAccountForTPAP(
	ctx context.Context,
	internalAccounts []*savingsPb.SavingsAccountEssentials, connectedAccounts []*external.AccountDetails,
	tpapAccount *onboarding.UpiAccount,
	accIdsConnectedWithTpapMap map[string]bool,
) (*savingsPb.SavingsAccountEssentials, *external.AccountDetails, error) {

	internalAcc := checkIfTpapOneOfInternalAccounts(internalAccounts, tpapAccount)
	if internalAcc != nil {
		accIdsConnectedWithTpapMap[internalAcc.GetId()] = true
	}

	connectedAccount, isDuplicateOfConnectedAccount, connectedAccountErr := s.checkIfTpapAccountConnectedWithAA(ctx, connectedAccounts, tpapAccount)
	if connectedAccountErr != nil {
		return nil, nil, fmt.Errorf("failed to check if tpap is duplicated of any connected account %w", connectedAccountErr)
	}
	if isDuplicateOfConnectedAccount {
		accIdsConnectedWithTpapMap[connectedAccount.GetAccountId()] = true
	}

	return internalAcc, connectedAccount, nil
}

// convertNonTPAPConnectedAccountsToFe converts the connected accounts that are not tpap account to fe accounts
func (s *Service) convertNonTPAPConnectedAccountsToFe(
	ctx context.Context,
	accIdsConnectedWithTpapMap map[string]bool,
	connectedAccounts []*external.AccountDetails) ([]*feAccounts.Account, error) {

	var feAccts []*feAccounts.Account
	for _, connectedAccount := range connectedAccounts {
		_, ok := accIdsConnectedWithTpapMap[connectedAccount.GetAccountId()]
		if !ok {
			feAcct, err := s.convertConnectedAccountToFeAccount(ctx, connectedAccount)
			if err != nil {
				return nil, fmt.Errorf("error in converting connected account to fe account %w", err)
			}
			feAccts = append(feAccts, feAcct)
		}
	}
	return feAccts, nil
}

// convertNonTPAPInternalAccountsToFe converts internal accounts if not exising as a TPAP account to FE account.
// It also figures out a fallback primary account if no primary account is set, i.e. if assignPrimaryAccount is true.
func (s *Service) convertNonTPAPInternalAccountsToFe(
	ctx context.Context,
	accIdsConnectedWithTpapMap map[string]bool,
	internalAccounts []*savingsPb.SavingsAccountEssentials,
	assignPrimaryAccount bool,
	accountPinStateMap map[string]upiPb.PinSetState,
	appVersionCode uint32,
) ([]*feAccounts.Account, error) {

	var (
		nonTPAPInternalAccounts []*savingsPb.SavingsAccountEssentials
		// can be used in figuring out an account which can be used as primary if no primary account is set
		fallbackPrimaryAccount *savingsPb.SavingsAccountEssentials
		feAccts                []*feAccounts.Account
	)

	// filter out non-TPAP internal accounts
	nonTPAPInternalAccounts = lo.Filter(internalAccounts, func(account *savingsPb.SavingsAccountEssentials, _ int) bool {
		_, ok := accIdsConnectedWithTpapMap[account.GetId()]
		return !ok
	})

	// assign fallback primary account if no primary account is set.
	// currently, this fallback is decided based on the APO priority order.
	if assignPrimaryAccount {
		for _, apo := range accountsToSortByAPOOrder {
			for _, internalAccount := range nonTPAPInternalAccounts {
				if internalAccount.GetSkuInfo().GetAccountProductOffering() == apo {
					fallbackPrimaryAccount = internalAccount
					break
				}
			}
		}
	}

	// convert non-TPAP internal accounts to FE accounts
	for _, internalAccount := range nonTPAPInternalAccounts {
		feAcct, err := s.convertInternalAccountToFeAccount(ctx, internalAccount, internalAccount.GetId() == fallbackPrimaryAccount.GetId(), accountPinStateMap, appVersionCode)
		if err != nil {
			return nil, fmt.Errorf("error in converting connected account to fe account %w", err)
		}

		feAccts = append(feAccts, feAcct)
	}

	return feAccts, nil
}

// getIdentifierForSavingsAccount gets the deeplink and pin set info for savings account
func (s *Service) getBankInfoForSavingsAccount(internalAccount *savingsPb.SavingsAccountEssentials) (string, string) {
	partnerBank := (*s.config.VendorToBankNameMap)[internalAccount.GetPartnerBank().String()]
	bankLogoUrl := s.config.BankIcon.FederalBankUrl
	return partnerBank, bankLogoUrl
}

// getPartnerBankLogoAndFeatureInfoForConnectedAccount gets the required information such as partner bank, logo
// and feature info for connected account
func (s *Service) getPartnerBankLogoAndFeatureInfoForConnectedAccount(
	ctx context.Context,
	connectedAccount *external.AccountDetails) (string, string, *feAccounts.ConnectedAccount) {
	var (
		partnerBank string
		bankLogoUrl string
	)

	fipMeta := connectedAccount.GetFipMeta()
	if fipMeta == nil {
		// just logging the error
		// This api is used on session start on the client. Should not fail
		// in meta data fetching failure
		logger.Error(ctx, "error fetching fip meta data", zap.Any(logger.FIP_ID, connectedAccount.GetFipId()), zap.Any(logger.ACTOR_ID_V2, connectedAccount.GetActorId()))
	}
	connectedAccountFeatureInfo := &feAccounts.ConnectedAccount{
		Features:         getFeConnectedAccountControls(connectedAccount.GetAccountControlsMap(), s.config.ConnectedAccount),
		IsConsentExpired: connectedAccount.GetAccountSubStatus() == connectedAccountEnums.AccountSubStatus_ACCOUNT_SUB_STATUS_CONSENT_EXPIRED,
	}
	if fipMeta != nil {
		partnerBank = fipMeta.Name
		bankLogoUrl = fipMeta.LogoUrl
	}
	// for backward compatibility
	if connectedAccount.GetFipMeta() != nil {
		partnerBank = connectedAccount.GetFipMeta().GetDisplayName()
		bankLogoUrl = connectedAccount.GetFipMeta().GetLogoUrl()
	}

	return partnerBank, bankLogoUrl, connectedAccountFeatureInfo
}

// convertTpapAccountToFeAccount converts the tpap account into feAccount
// nolint: funlen
func (s *Service) convertTpapAccountToFeAccount(ctx context.Context, tpapAccount *onboarding.UpiAccount, internalAccount *savingsPb.SavingsAccountEssentials,
	connectedAccount *external.AccountDetails, accountToPinSetStatusMap map[string]upiOnboardingEnumsPb.UpiPinSetStatus) (bool, *feAccounts.Account, error) {
	var (
		accountId                   string
		isConnectedAccount          bool
		connectedAccountFeatureInfo = &feAccounts.ConnectedAccount{}
		partnerBank                 string
		bankLogoUrl                 string
		ok                          bool
		isPrimaryAccountSet         bool
		derivedAccountIdProto       = &accounts.DerivedAccountId{}
	)
	derivedAccountIdProto.TpapAccountId = tpapAccount.GetId()
	partnerBank = tpapAccount.GetBankName()
	accountProvenances := []enums.AccountProvenance{enums.AccountProvenance_ACCOUNT_PROVENANCE_TPAP}

	// update bankLogoUrl
	if bankLogoUrl, ok = s.config.BankNameToLogoUrlMap[tpapAccount.GetBankName()]; !ok {
		bankLogoUrl = s.config.BankIcon.DefaultBankLogoUrl
	}

	// get the required information to make it backward compatible
	switch {
	case internalAccount != nil:
		derivedAccountIdProto.InternalAccountId = internalAccount.GetId()
		accountId = internalAccount.GetId()
		partnerBank, bankLogoUrl = s.getBankInfoForSavingsAccount(internalAccount)
		accountProvenances = append(accountProvenances, enums.AccountProvenance_ACCOUNT_PROVENANCE_INTERNAL)
	case connectedAccount != nil:
		derivedAccountIdProto.ConnectedAccountId = connectedAccount.GetAccountId()
		accountProvenances = append(accountProvenances, enums.AccountProvenance_ACCOUNT_PROVENANCE_CONNECTED_ACCOUNT)
		accountId = connectedAccount.GetAccountId()
		isConnectedAccount = true
		partnerBank, bankLogoUrl, connectedAccountFeatureInfo = s.getPartnerBankLogoAndFeatureInfoForConnectedAccount(ctx,
			connectedAccount)
	}

	// checks if the account is primary, sets the primary account
	if tpapAccount.GetAccountPreference() == upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY {
		isPrimaryAccountSet = true
	}

	derivedAccountIdString, err := idgen.EncodeProtoToStdBase64(derivedAccountIdProto)
	if err != nil {
		return false, nil, fmt.Errorf("failed to encode derived account id %w", err)
	}

	fePinSetState := feUpiPb.UpiAccountBePinStateToFePinState(ctx, s.config.Flags.UpiMaxRetriesPinSet, accountToPinSetStatusMap[tpapAccount.GetId()])
	deeplinkAction, err := s.getDeeplinkActionFromPinState(ctx, fePinSetState, "", tpapAccount.GetActorId(), derivedAccountIdString)
	if err != nil {
		return false, nil, fmt.Errorf("error getting deeplink action: err = %w", err)
	}

	// TODO(Ankit): fill logo url from list account provider/map, partner bank
	feAccount := &feAccounts.Account{
		AccountId:              accountId,
		DerivedAccountId:       derivedAccountIdString,
		MaskedAccountNumber:    mask.GetMaskedAccountNumber(tpapAccount.GetMaskedAccountNumber(), ""),
		AccountType:            tpapAccount.GetAccountType(),
		AccountProductOffering: tpapAccount.GetApo(),
		Capabilities:           getTpapAccountCapabilities(tpapAccount.GetAccountType()),
		AccountProvenances:     accountProvenances,
		IfscCode:               tpapAccount.GetIfscCode(),
		PinSetInfo: &feAccounts.PinSetInfo{
			PinSetState: fePinSetState,
			Action:      deeplinkAction,
		},
		IsConnectedAccount: isConnectedAccount,
		ConnectedAccount:   connectedAccountFeatureInfo,
		PartnerBank:        partnerBank,
		BankLogoUrl:        bankLogoUrl,
		IsPrimaryAccount:   isPrimaryAccountSet,
	}

	if internalAccount != nil {
		feAccount.Identifier = &feAccounts.Account_SavingsAccount{
			SavingsAccount: &feAccounts.SavingsAccount{
				PinSetState: fePinSetState,
				Action:      deeplinkAction,
			},
		}
	}

	return isPrimaryAccountSet, feAccount, nil
}

// getTpapAccountCapabilities - decides the account capabilities based on the account type
func getTpapAccountCapabilities(accountType accounts.Type) []feAccounts.AccountCapability {
	if accountType == accounts.Type_UPI_LITE {
		return []feAccounts.AccountCapability{
			feAccounts.AccountCapability_DEBIT,
		}
	}
	return []feAccounts.AccountCapability{
		feAccounts.AccountCapability_CREDIT,
		feAccounts.AccountCapability_DEBIT,
	}
}

// convertConnectedAccountToFeAccount converts the connected account to fe account
func (s *Service) convertConnectedAccountToFeAccount(ctx context.Context, connectedAccount *external.AccountDetails) (*feAccounts.Account, error) {
	partnerBank, bankLogo, connectedAccountFeatureInfo := s.getPartnerBankLogoAndFeatureInfoForConnectedAccount(ctx, connectedAccount)
	accType, err := fePkgCA.GetAccountTypeForAaAccount(connectedAccount)
	if err != nil {
		return nil, fmt.Errorf("error while fetching account type for connected account : %w", err)
	}

	derivedAccountIdProto := &accounts.DerivedAccountId{
		ConnectedAccountId: connectedAccount.GetAccountId(),
	}
	derivedAccountIdString, err := idgen.EncodeProtoToStdBase64(derivedAccountIdProto)
	if err != nil {
		return nil, fmt.Errorf("failed to encode derived account id %w", err)
	}

	feAccount := &feAccounts.Account{
		AccountId:           connectedAccount.GetAccountId(),
		MaskedAccountNumber: connectedAccount.GetMaskedAccountNumber(),
		AccountType:         accType,
		IsConnectedAccount:  true,
		ConnectedAccount:    connectedAccountFeatureInfo,
		AccountProvenances: []enums.AccountProvenance{
			enums.AccountProvenance_ACCOUNT_PROVENANCE_CONNECTED_ACCOUNT,
		},
		IfscCode:         connectedAccount.GetIfscCode(),
		DerivedAccountId: derivedAccountIdString,
		PartnerBank:      partnerBank,
		BankLogoUrl:      bankLogo,
	}
	return feAccount, nil
}

func (s *Service) isConnectedAccountEnabledForUser(ctx context.Context, actorId string) bool {
	if !s.config.ConnectedAccountUserGroupParams.IsConnectedAccountRestricted {
		return true
	}
	userGroupConstraintData := release.NewUserGroupConstraintData(&config3.UserGroupConstraintConfig{
		AllowedGroups: s.config.ConnectedAccountUserGroupParams.AllowedUserGrps,
	})
	userGroupConstraint := release.NewUserGroupConstraint(s.actorClient, s.client, s.userGroupClient)
	isEnabled, err := userGroupConstraint.Evaluate(ctx, userGroupConstraintData,
		release.NewCommonConstraintData(types.Feature_FEATURE_UNSPECIFIED).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error fetching user groups for user", zap.Error(err))
		return false
	}
	return isEnabled
}

// Validates if the address has all the required properties
// The following properties are mandatory for an address to be valid: Pincode, State, address lines, city (Locality/Sublocality)
func isAddressComplete(address *types.PostalAddress) bool {
	if address.GetPostalCode() == "" || address.GetAdministrativeArea() == "" || address.GetAddressLines() == nil {
		return false
	}
	if address.GetLocality() == "" && address.GetSublocality() == "" {
		return false
	}
	return true
}

// BlockActor RPC blocks an actor for the current logged in user and optionally mark report the user,
// based on the `is_spam` flag in the request.
// Once blocked, current actor wont be able to send/request any payment to the blocked actor. Further,
// all the payment requests from the blocked actor will not be received by the current actor.
// The blocked actor is never notified of being blocked by the current actor.
//
// In addition once reported, the blocked actor is marked as a spam to the internal system. Upon crossing
// a defined tolerance along with a bunch of other parameters defined by spam engine,
// the actor might get blocked across the platform permanently.
//
// Note: An actor can blocked only once at given point of time i.e. once blocked, the logged in actor can't block
// the other actor again without unblocking first. Hence, client must call ReportSpam
// if an already blocked actor is to be reported.
func (s *Service) BlockActor(ctx context.Context, req *pb.BlockActorRequest) (*pb.BlockActorResponse, error) {
	var (
		res = &pb.BlockActorResponse{}
	)

	blockRes, err := s.actorClient.BlockActor(ctx, &actorPb.BlockActorRequest{
		CurrentActorId: req.GetReq().GetAuth().GetActorId(),
		OtherActorId:   req.GetActorId(),
		IsSpam:         req.GetIsSpam(),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "BlockActor RPC failed", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusInternal()
		return res, nil

	case blockRes.Status.IsAlreadyProcessed():
		logger.Info(ctx, "actor already blocked by the current actor")
		res.GetRespHeader().Status = rpc.ExtendedStatusAlreadyProcessed()
		return res, nil

	case !blockRes.GetStatus().IsSuccess():
		logger.Error(ctx, "BlockActor returned non-ok response", zap.Any("status", blockRes.GetStatus()))
		res.GetRespHeader().Status = rpc.StatusInternal()
		return res, nil
	}

	res.GetRespHeader().Status = rpc.StatusOk()
	return res, nil
}

// UnblockActor RPC unblocks an actor for the current actor and optionally revokes the spam reported.
// Unblocking revokes the temporary restriction that was imposed for a blocked.
func (s *Service) UnblockActor(ctx context.Context, req *pb.UnblockActorRequest) (*pb.UnblockActorResponse, error) {
	var (
		res = &pb.UnblockActorResponse{}
	)

	unblockRes, err := s.actorClient.UnblockActor(ctx, &actorPb.UnblockActorRequest{
		CurrentActorId: req.GetActorId(),
		OtherActorId:   req.GetActorId(),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "UnblockActor RPC failed", zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusInternal()
		return res, nil

	case res.GetRespHeader().GetStatus().IsAlreadyProcessed():
		logger.Info(ctx, "actor already unblocked")
		res.GetRespHeader().Status = rpc.ExtendedStatusAlreadyProcessed()
		return res, nil

	case !unblockRes.GetStatus().IsSuccess():
		logger.Error(ctx, "UnblockActor returned non-ok response", zap.Any("status", unblockRes.GetStatus()))
		res.GetRespHeader().Status = rpc.StatusInternal()
		return res, nil
	}

	res.GetRespHeader().Status = rpc.StatusOk()
	return res, nil
}

// ReportSpam reports spam for a given blocked actor. Once reported, the blocked actor is marked as a spam to the
// internal system. Upon crossing a defined tolerance along with a bunch of other parameters defined by spam engine,
// the actor might get blocked across the platform permanently.
//
// Note: The blocked actor can be reported only once by a logged in actor at a given point of time i.e. once reported
// the logged in actor can't report the other actor again without unblocking first.
func (s *Service) ReportSpam(ctx context.Context, req *pb.ReportSpamRequest) (*pb.ReportSpamResponse, error) {
	var (
		statusActorNotBlocked = rpc.NewStatus(uint32(pb.ReportSpamResponse_ACTOR_NOT_BLOCKED),
			"actor must be blocked to be reported as spam", "")

		res = &pb.ReportSpamResponse{}
	)

	spamRes, err := s.actorClient.ReportSpamForBlockedActor(ctx, &actorPb.ReportSpamForBlockedActorRequest{
		CurrentActorId: req.GetActorId(),
		BlockedActorId: req.GetActorId(),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "failed to report spam for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.GetRespHeader().Status = rpc.StatusInternal()
		return res, nil

	case spamRes.GetStatus().IsAlreadyExists():
		res.GetRespHeader().Status = rpc.ExtendedStatusAlreadyProcessedWithDebug("actor can be report only once")
		return res, nil
	case spamRes.GetStatus().GetCode() == uint32(actorPb.ReportSpamForBlockedActorResponse_ACTOR_NOT_BLOCKED):
		logger.Error(ctx, "actor not blocked already")
		res.GetRespHeader().Status = statusActorNotBlocked
		return res, nil
	case !spamRes.GetStatus().IsSuccess():
		res.GetRespHeader().Status = rpc.StatusInternal()
		return res, nil
	}

	res.GetRespHeader().Status = rpc.StatusOk()
	return res, nil
}

// ValidateExternalAccount validates external account details entered by the user
func (s *Service) ValidateExternalAccount(ctx context.Context, req *pb.ValidateExternalAccountRequest) (*pb.ValidateExternalAccountResponse, error) {
	// fetching existing external accounts for user if they exist
	getBankAccResp, err := s.extAcctClient.GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})
	if grpcErr := epifigrpc.RPCError(getBankAccResp, err); grpcErr != nil {
		logger.Error(ctx, "error fetching existing external accounts for user", zap.Error(grpcErr))
		return &pb.ValidateExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(grpcErr.Error()),
				ErrorView: GenericFailureErrView,
			},
		}, nil
	}
	switch {
	case len(getBankAccResp.GetBankAccounts()) > 0:
		// already done validations
		return &pb.ValidateExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			NextAction: pkgSavings.AcctClosureTransferInitScreen(ctx, bePb.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED, getBankAccResp.GetBankAccounts()[0]),
		}, nil
	// checking if user has reached his retry limit
	case getBankAccResp.GetAccVerificationRetriesLeft() <= 0 || getBankAccResp.GetNameMatchRetriesLeft() <= 0:
		// terminal failure
		return &pb.ValidateExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			NextAction: AccountValidationFailedDl,
		}, nil
	case len(getBankAccResp.GetBankAccountVerifications()) > 0:
		for _, bav := range getBankAccResp.GetBankAccountVerifications() {
			if isSameAccount(req.GetAccountNumber(), req.GetIfscCode(), bav.GetAccountNumber(), bav.GetIfsc()) &&
				bav.GetOverallStatus() == extacct.OverallStatus_OVERALL_STATUS_FAILURE &&
				bav.GetFailureReason() != extacct.FailureReason_FAILURE_REASON_UNSPECIFIED {
				return &pb.ValidateExternalAccountResponse{
					RespHeader: &header.ResponseHeader{
						Status:    rpc.NewStatus(999, "show error view", ""),
						ErrorView: getErrorViewForAccValidationFailure(bav.GetFailureReason()),
					},
				}, nil
			}
		}
	}
	// validating user entered bank details
	addAccRes, err := s.extAcctClient.AddBankAccount(ctx, &extacct.AddBankAccountRequest{
		ActorId:       req.GetReq().GetAuth().GetActorId(),
		Ifsc:          req.GetIfscCode(),
		AccountNumber: req.GetAccountNumber(),
		UserGivenName: req.GetUserGivenName(),
		Caller: &extacct.Caller{
			Source: extacct.Source_SOURCE_USER,
		},
	})
	if grpcErr := epifigrpc.RPCError(addAccRes, err); grpcErr != nil {
		if addAccRes.GetStatus().IsAlreadyExists() {
			return &pb.ValidateExternalAccountResponse{
				RespHeader: &header.ResponseHeader{
					Status:    addAccRes.GetStatus(),
					ErrorView: AccountValidationInProgressErrView,
				},
			}, nil
		}
		logger.Error(ctx, "error validating given bank account details", zap.Error(grpcErr))
		rpcStatus := rpc.StatusInternalWithDebugMsg(grpcErr.Error())
		return &pb.ValidateExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpcStatus,
				ErrorView: GenericFailureErrView,
			},
		}, nil
	}
	return getValidateExternalAccountResponse(ctx, addAccRes.GetFailureReason(), addAccRes.GetOverallStatus(), &extacct.BankAccount{
		Ifsc:          req.GetIfscCode(),
		AccountNumber: req.GetAccountNumber(),
		Name:          req.GetUserGivenName(),
	})
}

func isSameAccount(accNum1, ifsc1, accNum2, ifsc2 string) bool {
	return strings.EqualFold(accNum1, accNum2) && strings.EqualFold(ifsc1, ifsc2)
}

func (s *Service) GetExternalAccount(ctx context.Context, req *pb.GetExternalAccountRequest) (*pb.GetExternalAccountResponse, error) {
	getBankAccResp, err := s.extAcctClient.GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})
	if grpcErr := epifigrpc.RPCError(getBankAccResp, err); grpcErr != nil {
		logger.Error(ctx, "error fetching existing external accounts for user", zap.Error(grpcErr))
		return &pb.GetExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(grpcErr.Error()),
			},
		}, nil
	}
	if len(getBankAccResp.GetBankAccounts()) == 0 {
		return &pb.GetExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusRecordNotFound(),
			},
		}, nil
	}
	account := getBankAccResp.GetBankAccounts()[0]
	return &pb.GetExternalAccountResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		AccountNumber:     account.GetAccountNumber(),
		IfscCode:          account.GetIfsc(),
		AccountHolderName: account.GetName(),
		BankLogoUrl:       "", // TODO
		Title:             account.GetName(),
		Line1:             fmt.Sprintf("IFSC: %s", account.GetIfsc()),
		Line2:             fmt.Sprintf("A/c no: %s", account.GetAccountNumber()),
	}, nil
}

// beGetActorPhoneNumber returns phone number of the actor
func (s *Service) beGetActorPhoneNumber(ctx context.Context, actorId string) (*commontypes.PhoneNumber, error) {
	res, err := s.beGetActorEntityDetails(ctx, actorId)
	if err != nil {
		return nil, err
	}

	return res.GetMobileNumber(), nil
}

// beGetActorEntityDetails returns actors entity information like profile image url, name, phone number, etc.
func (s *Service) beGetActorEntityDetails(ctx context.Context, actorId string) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
	logger.Debug(ctx, "Fetching the entity details by actor id")
	res, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})

	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "failed to fetch actors entity information ", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}
	return res, nil
}

// getFeConnectedAccountControls create a new frontend map from BE connected account control map
func getFeConnectedAccountControls(beConnectedAccountControlMap map[string]bool, connectedAccountConf *config.ConnectedAccount) []*feAccounts.ConnectedAccount_Feature {
	var (
		feConnectedAccountFeatureControl []*feAccounts.ConnectedAccount_Feature
	)

	// iterate over beConnectedAccountControlMap got from BE and check for each key and transform into FE controls
	for k, v := range beConnectedAccountControlMap {
		beConnectedAccountControl, ok := connectedAccountEnums.AccountControls_value[k]
		if ok {
			feControl := fromBeToFeAccountControls(connectedAccountEnums.AccountControls(beConnectedAccountControl), v, connectedAccountConf)
			if feControl != nil {
				feConnectedAccountFeatureControl = append(feConnectedAccountFeatureControl, feControl)
			}
		}
	}
	return feConnectedAccountFeatureControl
}

// fromBeToFeAccountControls converts BE connected account controls enum to FE account control feature
func fromBeToFeAccountControls(controls connectedAccountEnums.AccountControls, controlEnable bool,
	connectedAccountConf *config.ConnectedAccount) *feAccounts.ConnectedAccount_Feature {
	switch controls {
	case connectedAccountEnums.AccountControls_ACCOUNT_CONTROLS_TRANSACTIONS_ENABLED:
		if controlEnable {
			return &feAccounts.ConnectedAccount_Feature{
				Feature:     feConnectedAccountEnums.ConnectedAccountFeature_CONNECTED_ACCOUNT_FEATURE_TRANSACTIONS,
				ControlFlag: types.ControlFlag_CONTROL_FLAG_ENABLE,
			}
		}
		return &feAccounts.ConnectedAccount_Feature{
			Feature:     feConnectedAccountEnums.ConnectedAccountFeature_CONNECTED_ACCOUNT_FEATURE_TRANSACTIONS,
			ControlFlag: types.ControlFlag_CONTROL_FLAG_DISABLE,
			Title:       connectedAccountConf.AccountTxnNotVisibleTitle,
			Body:        connectedAccountConf.AccountTxnNotVisibleBody,
		}
	case connectedAccountEnums.AccountControls_ACCOUNT_CONTROLS_BALANCE_ENABLED:
		if controlEnable {
			return &feAccounts.ConnectedAccount_Feature{
				Feature:     feConnectedAccountEnums.ConnectedAccountFeature_CONNECTED_ACCOUNT_FEATURE_BALANCE,
				ControlFlag: types.ControlFlag_CONTROL_FLAG_ENABLE,
			}
		}
		return &feAccounts.ConnectedAccount_Feature{
			Feature:     feConnectedAccountEnums.ConnectedAccountFeature_CONNECTED_ACCOUNT_FEATURE_BALANCE,
			ControlFlag: types.ControlFlag_CONTROL_FLAG_DISABLE,
		}
	default:
		return nil
	}
}

func getValidateExternalAccountResponse(ctx context.Context, failureReason extacct.FailureReason, status extacct.OverallStatus, account *extacct.BankAccount) (*pb.ValidateExternalAccountResponse, error) {
	switch status {
	case extacct.OverallStatus_OVERALL_STATUS_SUCCESS:
		// successful verification
		return &pb.ValidateExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusOk(),
				ErrorView: nil,
			},
			NextAction: pkgSavings.AcctClosureTransferInitScreen(ctx, bePb.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED, account),
		}, nil
	case extacct.OverallStatus_OVERALL_STATUS_FAILURE:
		// unsuccessful verification, setting error views depending on reason
		return &pb.ValidateExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.NewStatus(999, "show error view", ""),
				ErrorView: getErrorViewForAccValidationFailure(failureReason),
			},
			FailureReason: AccountValidationFailureReasonBeToFeMap[failureReason],
		}, nil
	case extacct.OverallStatus_OVERALL_STATUS_IN_PROGRESS:
		return &pb.ValidateExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusAlreadyExistsWithDebugMsg("bank account verification in progress"),
				ErrorView: GenericFailureErrView,
			},
			FailureReason: AccountValidationFailureReasonBeToFeMap[failureReason],
		}, nil
	default:
		logger.Error(ctx, "unspecified overall status while performing account validation")
		return &pb.ValidateExternalAccountResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("failure doing account validation"),
				ErrorView: GenericFailureErrView,
			},
			FailureReason: AccountValidationFailureReasonBeToFeMap[failureReason],
		}, nil
	}
}

func getErrorViewForAccValidationFailure(failureReason extacct.FailureReason) *errorsPb.ErrorView {
	var errView *errorsPb.ErrorView
	switch failureReason {
	case extacct.FailureReason_FAILURE_REASON_SAME_ACCOUNT_NUMBER:
		errView = &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       AccountNumberSameTitle,
					Subtitle:    AccountNumberSameSubtitle,
					Description: "",
				},
			},
		}
	case extacct.FailureReason_FAILURE_REASON_NAME_AT_BANK_MISMATCH:
		errView = &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       AccountNameDoesntMatch,
					Subtitle:    AccountNameDoesntMatchReasonSubtitle,
					Description: "",
				},
			},
		}
	case extacct.FailureReason_FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK, extacct.FailureReason_FAILURE_REASON_INVALID_ACCOUNT_NUMBER,
		extacct.FailureReason_FAILURE_REASON_INVALID_IFSC, extacct.FailureReason_FAILURE_REASON_ACCOUNT_CLOSED,
		extacct.FailureReason_FAILURE_REASON_UNKNOWN:
		errView = &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
			Options: &errorsPb.ErrorView_BottomSheetErrorView{
				BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
					ErrorCode:   "",
					Title:       TransactionFailed,
					Subtitle:    TransactionFailedReasonSubtitle,
					Description: "",
				},
			},
		}
	case extacct.FailureReason_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH:
		errView = &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_INLINE,
			Options: &errorsPb.ErrorView_InlineErrorView{
				InlineErrorView: &errorsPb.InlineErrorView{
					ErrorCode: "",
					Title:     NameMismatchInlineError,
				},
			},
		}
	case extacct.FailureReason_FAILURE_REASON_UNSPECIFIED, extacct.FailureReason_FAILURE_REASON_API_TIMEOUT:
		errView = GenericFailureErrView
	}
	return errView
}

func (s *Service) SetUserPreferences(ctx context.Context, req *pb.SetUserPreferencesRequest) (*pb.SetUserPreferencesResponse, error) {
	bePrefPairList, err := convertToBePreferencePairList(req.GetPreferences())
	if err != nil {
		logger.Error(ctx, "error converting to BE pref pairs list", zap.Error(err))
		return &pb.SetUserPreferencesResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}
	beResp, err := s.client.SetUserPreferences(ctx, &bePb.SetUserPreferencesRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		Preferences: bePrefPairList,
	})
	if grpcErr := epifigrpc.RPCError(beResp, err); grpcErr != nil {
		logger.Error(ctx, "error setting user preferences to BE service", zap.Error(grpcErr))
		return &pb.SetUserPreferencesResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusFromErrorWithDefaultInternal(grpcErr),
			},
		}, nil
	}

	return &pb.SetUserPreferencesResponse{
		RespHeader: &header.ResponseHeader{
			Status: beResp.GetStatus(),
		},
	}, nil
}

func (s *Service) GetUserPreferences(ctx context.Context, req *pb.GetUserPreferencesRequest) (*pb.GetUserPreferencesResponse, error) {
	bePrefTypes, err := convertToBePrefTypesList(req.GetPreferenceTypes())
	if err != nil {
		if errs.Is(err, epifierrors.ErrInvalidArgument) {
			return &pb.GetUserPreferencesResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInvalidArgument(),
				},
			}, nil
		}
		logger.Error(ctx, "error converting to BE pref types list", zap.Error(err))
		return &pb.GetUserPreferencesResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}
	beResp, err := s.client.GetUserPreferences(ctx, &bePb.GetUserPreferencesRequest{
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		PreferenceTypes: bePrefTypes,
	})
	if grpcErr := epifigrpc.RPCError(beResp, err); grpcErr != nil {
		logger.Error(ctx, "error fetching user preferences from BE service", zap.Error(grpcErr))
		if beResp.GetStatus().IsRecordNotFound() {
			return &pb.GetUserPreferencesResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				}}, nil
		}
		return &pb.GetUserPreferencesResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	userPreferences, err := convertToFeUserPreferencesList(beResp.GetUserPreferences())
	if err != nil {
		logger.Error(ctx, "error converting to FE user preferences list", zap.Error(err))
		return &pb.GetUserPreferencesResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}
	return &pb.GetUserPreferencesResponse{
		RespHeader: &header.ResponseHeader{
			Status: beResp.GetStatus(),
		},
		UserPreferences: userPreferences,
	}, nil
}

func (s *Service) UpdateDOBForDedupeRetry(_ context.Context, _ *pb.UpdateDOBForDedupeRetryRequest) (*pb.UpdateDOBForDedupeRetryResponse, error) {
	return &pb.UpdateDOBForDedupeRetryResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusUnimplemented(),
		},
	}, nil
}

func convertToFeUserPreferencesList(beUserPreferences []*bePb.UserPreference) ([]*pb.UserPreference, error) {
	var feUserPreferences []*pb.UserPreference

	for _, beUserPref := range beUserPreferences {
		userPref, err := convertToFeUserPreference(beUserPref)
		if err != nil {
			return nil, err
		}
		feUserPreferences = append(feUserPreferences, userPref)
	}
	return feUserPreferences, nil
}

func convertToFeUserPreference(beUserPref *bePb.UserPreference) (*pb.UserPreference, error) {
	prefType, err := convertToFePreferenceType(beUserPref.GetPreferenceType())
	if err != nil {
		return nil, err
	}
	prefVal := convertToFePreferenceValue(beUserPref.GetPreferenceValue())
	if prefVal == nil {
		return nil, errs.New("failed to convert to FE pref value")
	}
	return &pb.UserPreference{
		Id:              beUserPref.GetId(),
		PreferenceType:  prefType,
		PreferenceValue: prefVal,
	}, nil
}

func convertToFePreferenceType(bePreferenceType bePb.PreferenceType) (pb.PreferenceType, error) {
	fePreferenceType, ok := pb.PreferenceType_value[bePreferenceType.String()]
	if !ok {
		return pb.PreferenceType_PREFERENCE_TYPE_UNSPECIFIED, errs.New("failed to convert to FE pref type")
	}
	return pb.PreferenceType(fePreferenceType), nil
}

func convertToFePreferenceArea(bePreferenceArea comms.Area) pb.Area {
	fePreferenceArea, ok := pb.Area_value[bePreferenceArea.String()]
	if !ok {
		return pb.Area_AREA_UNSPECIFIED
	}
	return pb.Area(fePreferenceArea)
}

func convertToFePreferenceMedium(bePreferenceMedium comms.Medium) pb.Medium {
	fePreferenceMedium, ok := pb.Medium_value[bePreferenceMedium.String()]
	if !ok {
		return pb.Medium_MEDIUM_UNSPECIFIED
	}
	return pb.Medium(fePreferenceMedium)
}

func convertToFePreferenceSignal(bePreferenceSignal bePb.CommsSignal) pb.CommsSignal {
	fePreferenceSignal, ok := pb.CommsSignal_value[bePreferenceSignal.String()]
	if !ok {
		return pb.CommsSignal_COMMS_SIGNAL_UNSPECIFIED
	}
	return pb.CommsSignal(fePreferenceSignal)
}

func convertToFeCommsPreference(bePrefValue *bePb.CommsPreference) []*pb.CommsPreferenceInfo {
	commsPrefInfos := make([]*pb.CommsPreferenceInfo, 0)
	for _, val := range bePrefValue.GetCommsPreferenceInfos() {
		commsPrefInfos = append(commsPrefInfos, &pb.CommsPreferenceInfo{
			Area:   convertToFePreferenceArea(val.GetArea()),
			Medium: convertToFePreferenceMedium(val.GetMedium()),
			Signal: convertToFePreferenceSignal(val.GetSignal()),
		})
	}
	return commsPrefInfos
}

// nolint: dupl
func convertToFePreferenceValue(bePrefValue *bePb.PreferenceValue) *pb.PreferenceValue {
	fePrefValue := &pb.PreferenceValue{}
	switch bePrefValue.GetPrefVal().(type) {
	case *bePb.PreferenceValue_PreferredCallLanguage:
		fePrefValue.PrefVal = &pb.PreferenceValue_PreferredCallLanguage{
			PreferredCallLanguage: &pb.LanguagePreferenceOrder{
				PreferredLanguages: bePrefValue.GetPreferredCallLanguage().GetPreferredLanguages(),
			},
		}
	case *bePb.PreferenceValue_SuggestedCallLanguage:
		fePrefValue.PrefVal = &pb.PreferenceValue_SuggestedCallLanguage{
			SuggestedCallLanguage: &pb.LanguagePreferenceOrder{
				PreferredLanguages: bePrefValue.GetSuggestedCallLanguage().GetPreferredLanguages(),
			},
		}
	case *bePb.PreferenceValue_CommsPreference:
		fePrefValue.PrefVal = &pb.PreferenceValue_CommsPreference{
			CommsPreference: &pb.CommsPreference{
				CommsPreferenceInfos: convertToFeCommsPreference(bePrefValue.GetCommsPreference()),
			},
		}
	default:
		return nil
	}
	return fePrefValue
}

func convertToBePrefTypesList(fePrefTypes []pb.PreferenceType) ([]bePb.PreferenceType, error) {
	var bePrefTypes []bePb.PreferenceType
	for _, fePrefType := range fePrefTypes {
		prefType, err := convertToBePreferenceType(fePrefType)
		if err != nil {
			return nil, err
		}
		bePrefTypes = append(bePrefTypes, prefType)
	}
	return bePrefTypes, nil
}

func convertToBePreferenceType(fePreferenceType pb.PreferenceType) (bePb.PreferenceType, error) {
	bePreferenceType, ok := bePb.PreferenceType_value[fePreferenceType.String()]
	if !ok {
		logger.ErrorNoCtx("unsupported fe pref type", zap.String(logger.ARTICLE_ID, fePreferenceType.String()))
		return bePb.PreferenceType_PREFERENCE_TYPE_UNSPECIFIED, fmt.Errorf("failed to convert to BE pref type: %w", epifierrors.ErrInvalidArgument)
	}
	return bePb.PreferenceType(bePreferenceType), nil
}

func convertToBePreferencePairList(fePreferences []*pb.PreferenceTypeValuePair) ([]*bePb.PreferenceTypeValuePair, error) {
	var beList []*bePb.PreferenceTypeValuePair
	for _, fePref := range fePreferences {
		bePref, err := convertToBePreferencePair(fePref)
		if err != nil {
			return nil, err
		}
		beList = append(beList, bePref)
	}
	return beList, nil
}

func convertToBePreferencePair(fePref *pb.PreferenceTypeValuePair) (*bePb.PreferenceTypeValuePair, error) {
	prefType, err := convertToBePreferenceType(fePref.GetPreferenceType())
	if err != nil {
		return nil, err
	}
	prefVal := convertToBePrefValue(fePref.GetPreferenceValue())
	if prefVal == nil {
		return nil, errs.New("failed to convert to BE pref value")
	}
	return &bePb.PreferenceTypeValuePair{
		PreferenceType:  prefType,
		PreferenceValue: prefVal,
	}, nil
}

func convertToBePreferenceArea(fePreferenceArea pb.Area) comms.Area {
	bePreferenceArea, ok := comms.Area_value[fePreferenceArea.String()]
	if !ok {
		return comms.Area_AREA_UNSPECIFIED
	}
	return comms.Area(bePreferenceArea)
}

func convertToBePreferenceMedium(fePreferenceMedium pb.Medium) comms.Medium {
	bePreferenceMedium, ok := comms.Medium_value[fePreferenceMedium.String()]
	if !ok {
		return comms.Medium_MEDIUM_UNSPECIFIED
	}
	return comms.Medium(bePreferenceMedium)
}

func convertToBePreferenceSignal(fePreferenceSignal pb.CommsSignal) bePb.CommsSignal {
	bePreferenceSignal, ok := bePb.CommsSignal_value[fePreferenceSignal.String()]
	if !ok {
		return bePb.CommsSignal_COMMS_SIGNAL_UNSPECIFIED
	}
	return bePb.CommsSignal(bePreferenceSignal)
}

func convertToBeCommsPreference(fePrefValue *pb.CommsPreference) []*bePb.CommsPreferenceInfo {
	commsPrefInfos := make([]*bePb.CommsPreferenceInfo, 0)
	for _, val := range fePrefValue.GetCommsPreferenceInfos() {
		commsPrefInfos = append(commsPrefInfos, &bePb.CommsPreferenceInfo{
			Area:   convertToBePreferenceArea(val.GetArea()),
			Medium: convertToBePreferenceMedium(val.GetMedium()),
			Signal: convertToBePreferenceSignal(val.GetSignal()),
		})
	}
	return commsPrefInfos
}

// nolint: dupl
func convertToBePrefValue(fePrefValue *pb.PreferenceValue) *bePb.PreferenceValue {
	bePrefValue := &bePb.PreferenceValue{}
	switch fePrefValue.GetPrefVal().(type) {
	case *pb.PreferenceValue_PreferredCallLanguage:
		bePrefValue.PrefVal = &bePb.PreferenceValue_PreferredCallLanguage{
			PreferredCallLanguage: &bePb.LanguagePreferenceOrder{
				PreferredLanguages: fePrefValue.GetPreferredCallLanguage().GetPreferredLanguages(),
			},
		}
	case *pb.PreferenceValue_SuggestedCallLanguage:
		bePrefValue.PrefVal = &bePb.PreferenceValue_SuggestedCallLanguage{
			SuggestedCallLanguage: &bePb.LanguagePreferenceOrder{
				PreferredLanguages: fePrefValue.GetSuggestedCallLanguage().GetPreferredLanguages(),
			},
		}
	case *pb.PreferenceValue_CommsPreference:
		bePrefValue.PrefVal = &bePb.PreferenceValue_CommsPreference{
			CommsPreference: &bePb.CommsPreference{
				CommsPreferenceInfos: convertToBeCommsPreference(fePrefValue.GetCommsPreference()),
			},
		}
	default:
		return nil
	}
	return bePrefValue
}

func (s *Service) isV2DeviceIntegrityVerifierEnabled(ctx context.Context, deviceId string) bool {
	devIntegrityCfg := s.genConfig.DeviceIntegrity()
	return (s.SafetyNetV2EnabledDeviceIds[deviceId] || app.IsFeatureRolledOut(deviceId, devIntegrityCfg.AsyncDeviceIntegrityRolloutPercentage())) &&
		apputils.IsFeatureEnabledFromCtxDynamic(ctx, devIntegrityCfg.AsyncDeviceIntegrityCheck())
}

// fetchUpiPinStatusForTpapAccounts fetches upi pin status for given tpap account ids
func (s *Service) fetchUpiPinStatusForTpapAccounts(ctx context.Context, tpapAccounts []*onboarding.UpiAccount) (map[string]upiOnboardingEnumsPb.UpiPinSetStatus, error) {
	if len(tpapAccounts) == 0 {
		return nil, nil
	}
	var accountIds []string
	for _, account := range tpapAccounts {
		accountIds = append(accountIds, account.GetId())
	}

	resp, err := s.upiOnboardingClient.GetPinStatus(ctx, &onboarding.GetPinStatusRequest{
		AccountIds: accountIds,
	})

	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		return nil, fmt.Errorf("error fetching pin set status for upi accounts %w", grpcErr)
	}

	return resp.GetUpiAccountPinStatusMap(), nil
}

// getTpapAccountIdFromDerivedId - decodes tpap account id from derived account id
func getTpapAccountIdFromDerivedId(derivedAccountId string) (string, error) {
	derivedAccountIdProto := &accounts.DerivedAccountId{}

	err := idgen.DecodeProtoFromStdBase64(derivedAccountId, derivedAccountIdProto)
	if err != nil {
		return "", fmt.Errorf("error decoding derived accountId %s %w", derivedAccountId, err)
	}
	return derivedAccountIdProto.GetTpapAccountId(), nil
}

func (s *Service) GetProfilePageSections(ctx context.Context, req *pb.GetProfilePageSectionsRequest) (*pb.GetProfilePageSectionsResponse, error) {
	var (
		sections    []*pb.ProfileSection
		pageHeading *commontypes.Text
		err         error
	)
	pageEnum := pb.ProfilePageType(pb.ProfilePageType_value[req.GetPageType()])

	switch pageEnum {
	case pb.ProfilePageType_PROFILE_PAGE_TYPE_REPORTS_AND_DOWNLOADS:
		sections = getReportsAndDownloadsSections()
		pageHeading = getTextByFontColorAndStyle(ReportsAndDownloadsPageHeading, ColorCodeNight, commontypes.FontStyle_HEADLINE_3)
	case pb.ProfilePageType_PROFILE_PAGE_TYPE_MY_PROFILE:
		sections, err = s.getMyProfileSections(ctx, req.GetReq().GetAuth().GetActorId())

	default:
		logger.Error(ctx, "unexpected page enum recieved", zap.String(logger.PAGE_TYPE, req.GetPageType()))
		return &pb.GetProfilePageSectionsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgument(),
			},
		}, nil
	}

	if err != nil {
		logger.Error(ctx, "error in get reports and downloads page", zap.Error(err))
		return &pb.GetProfilePageSectionsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("error in get reports and downloads page"),
			},
		}, nil
	}

	return &pb.GetProfilePageSectionsResponse{
		Sections:    sections,
		PageHeading: pageHeading,
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

func (s *Service) GetProfileSettingPageSection(ctx context.Context, req *pb.GetProfileSettingPageSectionRequest) (*pb.GetProfileSettingPageSectionResponse, error) {
	accountClosureText, accountClosureHyperlink, txtErr := s.getAccountClosureTextForProfileSettings(ctx, req)
	if txtErr != nil {
		logger.Error(ctx, "failed to get entry point text for savings account closure", zap.Error(txtErr))
		return &pb.GetProfileSettingPageSectionResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}
	resp := &pb.GetProfileSettingPageSectionResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},

		AccountClosureText:       accountClosureText,
		AccountClosureHyperlinks: accountClosureHyperlink,
	}
	if s.genConfig.FeedbackEngineConfig().ResponseHeaderPopulationConfig().IsPopulationInGetProfileSettingPageSectionEnabled() {
		resp.RespHeader.FeedbackEngineInfo = &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_APP_FLOW,
				FlowIdentifier:     types.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_LANGUAGE_PREFERENCE_SURVEY.String(),
			}}
	}
	return resp, nil
}

func (s *Service) getAccountClosureTextForProfileSettings(ctx context.Context, req *pb.GetProfileSettingPageSectionRequest) (*commontypes.Text, *ui.TextWithHyperlinks, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	getFeatureResp, getFeatureErr := s.onboardingClient.GetFeatureDetails(ctx, &beOnbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: beOnbPb.Feature_FEATURE_FI_LITE,
	})
	if rpcErr := epifigrpc.RPCError(getFeatureResp, getFeatureErr); rpcErr != nil {
		return nil, nil, errors2.Wrap(rpcErr, "get feature details rpc failed")
	}

	if getFeatureResp.GetIsFiLiteUser() {
		return nil, nil, nil
	}

	constraints := release.NewCommonConstraintData(types.Feature_SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT).WithActorId(actorId)
	isAllowed, evalErr := s.releaseEvaluator.Evaluate(ctx, constraints)
	if evalErr != nil {
		return nil, nil, errors2.Wrap(evalErr, "failed to evaluate on Feature_SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT")
	}

	if !isAllowed {
		logger.Debug(ctx, "feature SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT in not allowed for the user")
		return nil, nil, nil
	}

	screenOptionsV2, screenOptErr := deeplinkV3.GetScreenOptionV2(&saClosurePb.SaClosureBenefitScreenOptions{
		EntryPoint: enums.SAClosureRequestEntryPoint_SA_CLOSURE_REQUEST_ENTRY_POINT_PROFILE,
	})
	if screenOptErr != nil {
		return nil, nil, errors2.Wrap(screenOptErr, "failed to get html link for deeplink")
	}

	saClosureDeeplink := &deeplink.Deeplink{
		Screen:          deeplink.Screen_SA_CLOSURE_BENEFITS_SCREEN,
		ScreenOptionsV2: screenOptionsV2,
	}

	link, htmlLinkErr := deeplinkPkg.GetHtmlLinkForDeeplink(saClosureDeeplink)
	if htmlLinkErr != nil {
		return nil, nil, errors2.Wrap(htmlLinkErr, "failed to get html link for deeplink")
	}

	htmlTextString := saClosureProfileEntryPointText
	linkTextString := saClosureProfileEntryPointLinkText

	if cfg.IsQaEnv(s.config.Application.Environment) {
		htmlTextString = saClosureProfileEntryPointTextQa
		linkTextString = saClosureProfileEntryPointLinkTextQa
	}

	htmlText := fmt.Sprintf(htmlTextString, link)
	text := commontypes.GetTextFromHtmlStringFontColourFontStyle(htmlText, blackColor, commontypes.FontStyle_BODY_XS)

	plainText := commontypes.GetTextFromStringFontColourFontStyle(saClosureProfileEntryPointPlainText, blackColor, commontypes.FontStyle_BODY_XS)

	hyperlinkText := &ui.TextWithHyperlinks{
		Text: plainText,
		HyperlinkMap: map[string]*ui.HyperLink{
			linkTextString: {
				Link: &ui.HyperLink_NextActionLink{NextActionLink: saClosureDeeplink},
			},
		},
	}

	return text, hyperlinkText, nil
}

func (s *Service) SetCallLanguagePreferences(ctx context.Context, req *pb.SetCallLanguagePreferencesRequest) (*pb.SetCallLanguagePreferencesResponse, error) {
	var (
		title        *commontypes.Text
		desc         *commontypes.Text
		editPrefInfo *commontypes.Text
		infoIcon     *ui.IconTextComponent
	)

	switch req.GetPreferences().GetPreferenceType() {
	case pb.PreferenceType_PREFERENCE_TYPE_PREFERRED_CALL_LANGUAGE:
		callLangPrefConfig, ok := s.genConfig.CallLanguagePreferenceConfig().PreferredCallLangFlowSuccessScreenConfigMap().Load(req.GetEntryPoint().String())
		if !ok {
			// if config not found for given screen name, return default config
			callLangPrefConfig, ok = s.genConfig.CallLanguagePreferenceConfig().PreferredCallLangFlowSuccessScreenConfigMap().Load(Default)
			if !ok {
				logger.Error(ctx, "cannot get default screen mapping from config", zap.String("screen", req.GetEntryPoint().String()))
				return &pb.SetCallLanguagePreferencesResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternal(),
					},
				}, nil
			}
		}
		title, desc, editPrefInfo, infoIcon = getPrefLangSuccessScreenDetails(callLangPrefConfig, false)
	case pb.PreferenceType_PREFERENCE_TYPE_SUGGESTED_CALL_LANGUAGE:
		callLangPrefConfig, ok := s.genConfig.CallLanguagePreferenceConfig().SuggestedCallLangFlowSuccessScreenConfigMap().Load(req.GetEntryPoint().String())
		if !ok {
			// if config not found for given screen name, return default config
			callLangPrefConfig, ok = s.genConfig.CallLanguagePreferenceConfig().SuggestedCallLangFlowSuccessScreenConfigMap().Load(Default)
			if !ok {
				logger.Error(ctx, "cannot get default screen mapping from config", zap.String("screen", req.GetEntryPoint().String()))
				return &pb.SetCallLanguagePreferencesResponse{
					RespHeader: &header.ResponseHeader{
						Status: rpc.StatusInternal(),
					},
				}, nil
			}
		}
		title, desc, editPrefInfo, infoIcon = getPrefLangSuccessScreenDetails(callLangPrefConfig, true)
	default:
		logger.Error(ctx, "preference type not supported for set call lang preferences", zap.String("preferenceType", req.GetPreferences().GetPreferenceType().String()))
		return &pb.SetCallLanguagePreferencesResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	bePrefPairList, err := convertToBePreferencePairList([]*pb.PreferenceTypeValuePair{req.GetPreferences()})
	if err != nil {
		logger.Error(ctx, "error converting to BE pref pairs list", zap.Error(err))
		return &pb.SetCallLanguagePreferencesResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}
	beResp, err := s.client.SetUserPreferences(ctx, &bePb.SetUserPreferencesRequest{
		ActorId:     req.GetReq().GetAuth().GetActorId(),
		Preferences: bePrefPairList,
	})
	if grpcErr := epifigrpc.RPCError(beResp, err); grpcErr != nil {
		logger.Error(ctx, "error setting user preferences to BE service", zap.Error(grpcErr))
		return &pb.SetCallLanguagePreferencesResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusFromErrorWithDefaultInternal(grpcErr),
			},
		}, nil
	}

	return &pb.SetCallLanguagePreferencesResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Title:        title,
		Desc:         desc,
		EditPrefInfo: editPrefInfo,
		InfoIcon:     infoIcon,
	}, nil
}

func getPrefLangSuccessScreenDetails(callLangPrefConfig *genconf.CallLangPrefSuccessScreenConfig, showSuggestedCallLang bool) (*commontypes.Text, *commontypes.Text, *commontypes.Text, *ui.IconTextComponent) {
	title := commontypes.GetTextFromStringWithCustomFontStyle(callLangPrefConfig.Title().Content(), callLangPrefConfig.Title().FontColor(), &commontypes.FontStyleInfo{
		FontFamily: callLangPrefConfig.Title().FontFamily(),
		FontSize:   callLangPrefConfig.Title().FontSize(),
	})
	desc := commontypes.GetTextFromStringFontColourFontStyle(callLangPrefConfig.Desc().Content(), callLangPrefConfig.Desc().FontColor(), (commontypes.FontStyle)(commontypes.FontStyle_value[callLangPrefConfig.Desc().FontStyle()]))
	editPrefInfo := commontypes.GetTextFromStringFontColourFontStyle(callLangPrefConfig.EditPrefInfo().Content(), callLangPrefConfig.EditPrefInfo().FontColor(), (commontypes.FontStyle)(commontypes.FontStyle_value[callLangPrefConfig.EditPrefInfo().FontStyle()]))
	infoIcon := &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(callLangPrefConfig.InfoIcon().Texts()[0].Content, callLangPrefConfig.InfoIcon().Texts()[0].FontColor, (commontypes.FontStyle)(commontypes.FontStyle_value[callLangPrefConfig.InfoIcon().Texts()[0].FontStyle])),
		},
		LeftImgTxtPadding: 12,
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BorderColor:   "#E7E7E7",
			CornerRadius:  8,
			BorderWidth:   1,
			TopPadding:    12,
			BottomPadding: 12,
			LeftPadding:   20,
			RightPadding:  20,
		},
		LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: callLangPrefConfig.InfoIcon().LeftVisualElementImage().Url()},
				Properties: &commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				},
				ImageType: commontypes.ImageType_PNG,
			},
		}},
	}
	// for success screens when preference type is of SUGGESTED_CALL_LANGUAGE, then don't show edit pref info.
	if showSuggestedCallLang {
		editPrefInfo = nil
	} else {
		desc = nil
	}
	return title, desc, editPrefInfo, infoIcon
}

func (s *Service) GetFeatureBenefitsScreenOptions(ctx context.Context, req *pb.GetFeatureBenefitsScreenOptionsRequest) (*pb.GetFeatureBenefitsScreenOptionsResponse, error) {
	var (
		actorId        = req.GetReq().GetAuth().GetActorId()
		appPlatform    = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersionCode = req.GetReq().GetAppVersionCode()
	)

	getDetailsResp, getDetailsErr := s.onboardingClient.GetDetails(ctx, &beOnbPb.GetDetailsRequest{
		ActorId:    actorId,
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		CachedData: true,
	})
	if grpcErr := epifigrpc.RPCError(getDetailsResp, getDetailsErr); grpcErr != nil {
		logger.Error(ctx, "error fetching details from BE service", zap.Error(grpcErr))
		return &pb.GetFeatureBenefitsScreenOptionsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusFromErrorWithDefaultInternal(grpcErr),
			},
		}, nil
	}
	softIntents := getDetailsResp.GetDetails().GetStageMetadata().GetSoftIntentSelectionMetadata().GetSelection()
	var categories []beOnbPb.OnboardingSoftIntentCategory
	for _, softIntent := range softIntents {
		category, _ := beOnbPb.SoftIntentToCategoryMap[softIntent]
		categories = append(categories, category)
	}
	var (
		dl       *deeplink.Deeplink
		getDlErr error
	)
	switch {
	case lo.Contains(categories, beOnbPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_EVERYDAY_NEEDS):
		dl, getDlErr = onbPkg.GetSABenefitsScreenV3(appPlatform, appVersionCode)
		if getDlErr != nil {
			logger.Error(ctx, "error fetching savings account benefits screen", zap.Error(getDlErr))
			return &pb.GetFeatureBenefitsScreenOptionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error fetching savings account benefits screen"),
				},
			}, nil
		}
	case lo.Contains(categories, beOnbPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_GLOBAL_SPENDING):
		dl, getDlErr = onbPkg.GetDCBenefitsScreenV2()
		if getDlErr != nil {
			logger.Error(ctx, "error fetching DC benefits screen", zap.Error(getDlErr))
			return &pb.GetFeatureBenefitsScreenOptionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error fetching DC benefits screen"),
				},
			}, nil
		}
	case lo.Contains(categories, beOnbPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_INVEST_MONEY):
		dl, getDlErr = onbPkg.GetUsStocksBenefitsScreenV2()
		if getDlErr != nil {
			logger.Error(ctx, "error fetching US stocks benefits screen", zap.Error(getDlErr))
			return &pb.GetFeatureBenefitsScreenOptionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error fetching US stocks benefits screen"),
				},
			}, nil
		}
	default:
		dl, getDlErr = onbPkg.GetSABenefitsScreenV3(appPlatform, appVersionCode)
		if getDlErr != nil {
			logger.Error(ctx, "error fetching savings account benefits screen", zap.Error(getDlErr))
			return &pb.GetFeatureBenefitsScreenOptionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg("error fetching savings account benefits screen"),
				},
			}, nil
		}
	}
	featureBenefitsScreenOpts := &onbScreenOptsPb.FeatureBenefitsScreenOptions{}
	unmarshalErr := dl.GetScreenOptionsV2().UnmarshalTo(featureBenefitsScreenOpts)
	if unmarshalErr != nil {
		logger.Error(ctx, "error unmarshalling any to feature benefits screen options", zap.Error(unmarshalErr))
		return &pb.GetFeatureBenefitsScreenOptionsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("error unmarshalling any to feature benefits screen options"),
			},
		}, nil
	}
	return &pb.GetFeatureBenefitsScreenOptionsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		FeatureBenefitsScreenOptions: featureBenefitsScreenOpts,
	}, nil
}

func getCountryCodeFromStr(codeStr string) (types.CountryCode, error) {
	// for backward compatibility
	if codeStr == "" {
		return types.CountryCode_COUNTRY_CODE_UNSPECIFIED, nil
	}

	if val, ok := types.CountryCode_value[codeStr]; ok {
		return types.CountryCode(val), nil
	}

	return 0, fmt.Errorf("%w: invalid country code", epifierrors.ErrInvalidArgument)
}

func getAnyWithoutError(msg proto.Message) *anyPb.Any {
	res, _ := anyPb.New(msg)
	return res
}

func (s *Service) SendSmsData(ctx context.Context, req *pb.SendSmsDataRequest) (*pb.SendSmsDataResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	errRespWithDebugMsg := &pb.SendSmsDataResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg("error when updating onboarding stage"),
		},
	}
	for _, smsData := range req.GetSmsData() {
		if smsData.GetSmsDataError() != pb.SmsDataError_SMS_DATA_ERRORS_UNSPECIFIED {
			logger.Info(ctx, "failed to fetch pan from sms", zap.String("error", smsData.GetSmsDataError().String()))
			s.publishSmsDataFetchServerEvent(ctx, actorId, "FAILED", smsData.GetSmsDataError().String())
			if err := s.skipWaitForAutoPanOnbStage(ctx, actorId); err != nil {
				return errRespWithDebugMsg, nil
			}
			continue
		}
		switch smsData.GetSmsDataType().(type) {
		case *pb.SmsData_Pan:
			err := s.panProcessor.StoreUnverifiedPan(ctx, actorId, smsData.GetPan(), bePb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_SMS_DATA)
			if err != nil {
				logger.Error(ctx, "failed to store unverified pan", zap.Error(err))
				s.publishSmsDataFetchServerEvent(ctx, actorId, "FAILED", "STORE_PAN_FAILURE")
				if err1 := s.skipWaitForAutoPanOnbStage(ctx, actorId); err1 != nil {
					return errRespWithDebugMsg, nil
				}
			} else {
				s.publishSmsDataFetchServerEvent(ctx, actorId, "SUCCESS", "")
				logger.Info(ctx, "fetch pan from sms successful")
			}
		default:
			continue
		}
	}
	return &pb.SendSmsDataResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}

func (s *Service) skipWaitForAutoPanOnbStage(ctx context.Context, actorId string) error {
	res, err := s.onboardingClient.UpdateStage(ctx, &beOnbPb.UpdateStageRequest{
		ActorId:  actorId,
		Stage:    beOnbPb.OnboardingStage_WAIT_FOR_AUTO_PAN,
		NewState: beOnbPb.OnboardingState_SKIPPED,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		logger.Error(ctx, "failed to skip WAITFOR_AUTO_PAN stage of user", zap.Error(err))
		return fmt.Errorf("failed to skip WAITFOR_AUTO_PAN stage %v", zap.Error(err))
	}
	return nil
}

func (s *Service) publishSmsDataFetchServerEvent(ctx context.Context, actorId, status, reason string) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events3.NewSmsDataFetchServer(actorId, status, reason))
	})
}

// nolint: ineffassign
func splitAddressLine(addressLine string) [3]string {
	// Split the address by commas
	parts := strings.Split(addressLine, ",")

	// Trim spaces from each part
	for i := range parts {
		parts[i] = strings.TrimSpace(parts[i])
	}

	// Initialize our three result components
	var result [3]string

	// If we have 3 or fewer parts, each goes into its own component
	if len(parts) <= 3 {
		for i := 0; i < len(parts); i++ {
			result[i] = parts[i]
		}
		return result
	}

	// Otherwise, distribute the parts relatively evenly
	partsPerComponent := len(parts) / 3
	remainder := len(parts) % 3

	// Calculate indices for each segment
	start := 0
	end := partsPerComponent
	if remainder > 0 {
		end++
		remainder--
	}

	// First component
	result[0] = strings.Join(parts[start:end], ", ")

	// Second component
	start = end
	end = start + partsPerComponent
	if remainder > 0 {
		end++
		remainder--
	}
	result[1] = strings.Join(parts[start:end], ", ")

	// Third component
	result[2] = strings.Join(parts[end:], ", ")

	return result
}

// 1. Checks if FEATURE_SHOULD_SHOW_PIN_SCREEN_V2 flag is enabled for user
// 2. For enabled users, checks if account was created in/after 2025
// 3. Returns true only if both conditions are met
func (s *Service) shouldShowPinScreenV2ForRecentlyOnbUsers(ctx context.Context, actorId, accountId string) bool {

	isFeatureEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_SHOULD_SHOW_PIN_SCREEN_V2).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "error evaluating should show pin screen v2", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	if !isFeatureEnabled {
		return false
	}
	logger.Info(ctx, "FEATURE_SHOULD_SHOW_PIN_SCREEN_V2 - TRUE", zap.String(logger.ACTOR_ID_V2, actorId))

	account, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_Id{Id: accountId}})
	if err != nil {
		logger.Error(ctx, "error getting account details", zap.Error(err))
		return false
	}

	if account.GetAccount().GetCreatedAt() != nil && account.GetAccount().GetCreatedAt().AsTime().Year() >= 2025 {
		logger.Info(ctx, "shouldShowPinScreenV2ForRecentlyOnbUsers - TRUE", zap.String(logger.ACTOR_ID_V2, actorId))
		return true
	}
	return false
}

// ConvertBeNomineeToFeNominee : Converts nominee object we got from BE to FE Types Nominee for backward compatible clients
func ConvertBeNomineeToFeNominee(beNominee *types.Nominee) *types.Nominee {
	pan := ""
	if beNominee.GetNomineeDocument().GetDocumentType() == types.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN {
		pan = beNominee.GetNomineeDocument().GetDocumentNumber()
	}
	feNominee := &types.Nominee{
		Id:           beNominee.GetId(),
		ActorId:      beNominee.GetActorId(),
		Relationship: beNominee.GetRelationship(),
		Name:         beNominee.GetName(),
		Dob:          beNominee.GetDob(),
		ContactInfo:  beNominee.GetContactInfo(),
		GuardianInfo: beNominee.GetGuardianInfo(),
		Pan:          pan,
	}
	return feNominee
}

// ConvertBeNomineeToFeNomineeV2 : Converts nominee object we got from BE to FE Nominee
func ConvertBeNomineeToFeNomineeV2(beNominee *types.Nominee) *pb.Nominee {
	var documentType, documentNumber string

	nomineeDocument := beNominee.GetNomineeDocument()

	if nomineeDocument != nil {
		documentType = nomineeDocument.GetDocumentType().String()
		documentNumber = nomineeDocument.GetDocumentNumber()
	}

	feNominee := &pb.Nominee{
		Id:             beNominee.GetId(),
		Relationship:   beNominee.GetRelationship(),
		Name:           beNominee.GetName(),
		Dob:            beNominee.GetDob(),
		ContactInfo:    beNominee.GetContactInfo(),
		GuardianInfo:   beNominee.GetGuardianInfo(),
		DocumentType:   documentType,
		DocumentNumber: documentNumber,
	}
	return feNominee
}
