package user

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mock"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	pb "github.com/epifi/gamma/api/frontend/user"
	onbPb "github.com/epifi/gamma/api/frontend/user/onboarding"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	onboarding2 "github.com/epifi/gamma/api/upi/onboarding"
	bePb "github.com/epifi/gamma/api/user"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	beOnbPb "github.com/epifi/gamma/api/user/onboarding"
	mocks3 "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/api/vendormapping"
	mockVm "github.com/epifi/gamma/api/vendormapping/mocks"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/address"
	mockRelease "github.com/epifi/gamma/pkg/feature/release/mocks"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
	headerPkg "github.com/epifi/gamma/pkg/frontend/header"
	testMocks "github.com/epifi/gamma/testing/mocks"
)

var (
	actorId        = "actorId"
	helpSubSection = &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Help", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("For any assistance you need", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/helpIcon.png",
		Cta: &deeplink.Cta{
			Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_CONTACT_US_LANDING_SCREEN_V2},
			IconUrl:  ProfileLandingCtaIconUrl,
		},
	}
)

type LinkInternalAccountRequestMatcher struct {
	want *onboarding2.LinkInternalAccountRequest
}

func newLinkInternalAccountRequestMatcher(want *onboarding2.LinkInternalAccountRequest) *LinkInternalAccountRequestMatcher {
	return &LinkInternalAccountRequestMatcher{want: want}
}

func (c *LinkInternalAccountRequestMatcher) Matches(x interface{}) bool {
	got, ok := x.(*onboarding2.LinkInternalAccountRequest)
	if !ok {
		return false
	}

	// ignoring below fields
	c.want.ClientReqId = got.ClientReqId

	return reflect.DeepEqual(c.want, got)
}

func (c *LinkInternalAccountRequestMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

func TestService_SyncVendorIDs(t *testing.T) {
	logger.Init("test")
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()

	conf, _ = config.Load()
	gconf, _ := genconf.Load()

	mockVendorMappingClient := mockVm.NewMockVendorMappingServiceClient(ctr)

	feUserService := NewUserService(conf, nil, nil,
		nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, mockVendorMappingClient,
		nil, nil, nil, nil, nil,
		nil, nil, gconf, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil)

	type MockUpsertClientAfId struct {
		enable bool
		req    *vendormapping.UpsertClientAfIdRequest
		res    *vendormapping.UpsertClientAfIdResponse
		err    error
	}

	type args struct {
		ctx context.Context
		req *pb.SyncVendorIDsRequest
	}

	tests := []struct {
		name                 string
		args                 args
		MockUpsertClientAfId MockUpsertClientAfId
		want                 *pb.SyncVendorIDsResponse
		wantErr              bool
	}{
		{
			name: "Events published successfully",
			args: args{
				ctx: context.Background(),
				req: &pb.SyncVendorIDsRequest{
					Req: &header.RequestHeader{
						ProspectId: "pid-1",
					},
					Identifiers: []*types.AppInstanceId{
						{Name: types.AppInstanceIdName_CLIENT_APPSFLYER_ID, Value: "cai-1"},
					},
				},
			},
			MockUpsertClientAfId: MockUpsertClientAfId{
				enable: true,
				req: &vendormapping.UpsertClientAfIdRequest{
					ClientAfId: "cai-1",
					UpsertIdentifier: &vendormapping.UpsertClientAfIdRequest_ProspectId{
						ProspectId: "pid-1",
					},
				},
				res: &vendormapping.UpsertClientAfIdResponse{
					Status: rpc.StatusOk(),
				},
			},
			want: &pb.SyncVendorIDsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			if tt.MockUpsertClientAfId.enable {
				mockVendorMappingClient.EXPECT().UpsertClientAfId(tt.args.ctx, tt.MockUpsertClientAfId.req).Return(
					tt.MockUpsertClientAfId.res, tt.MockUpsertClientAfId.err)
			}

			got, err := feUserService.SyncVendorIDs(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("SyncVendorIDs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("SyncVendorIDs() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetMoEngageId(t *testing.T) {
	logger.Init("test")
	ctr := gomock.NewController(t)
	defer func() {
		ctr.Finish()
	}()

	conf, _ = config.Load()
	gconf, _ := genconf.Load()

	mockVendorMappingClient := mockVm.NewMockVendorMappingServiceClient(ctr)

	feUserService := NewUserService(conf, nil, nil,
		nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, mockVendorMappingClient,
		nil, nil, nil, nil, nil,
		nil, nil, gconf, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil)

	type args struct {
		ctx context.Context
		req *pb.GetMoEngageIdRequest
	}

	actorId = "AC001"
	moengId := "Moengage-Id"
	ctxb := context.Background()

	tests := []struct {
		name    string
		args    args
		vmres   *vendormapping.GetDPMappingByIdResponse
		err     error
		want    *pb.GetMoEngageIdResponse
		wantErr bool
	}{
		{
			name: "MoEngageId fetched Successfully",
			args: args{
				ctx: context.WithValue(ctxb, epificontext.CtxActorKey, actorId),
				req: &pb.GetMoEngageIdRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
				},
			},
			vmres: &vendormapping.GetDPMappingByIdResponse{
				Status:     rpc.StatusOk(),
				MoengageId: moengId,
			},
			err: nil,
			want: &pb.GetMoEngageIdResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				MoengageId: moengId,
			},
			wantErr: false,
		},
		{
			name: "no vendor mapping for the actor",
			args: args{
				ctx: context.WithValue(ctxb, epificontext.CtxActorKey, actorId),
				req: &pb.GetMoEngageIdRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
				},
			},
			vmres: &vendormapping.GetDPMappingByIdResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			err: nil,
			want: &pb.GetMoEngageIdResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			wantErr: false,
		},
		{
			name: "Vendormapping call failed",
			args: args{
				ctx: context.WithValue(ctxb, epificontext.CtxActorKey, actorId),
				req: &pb.GetMoEngageIdRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: actorId,
						},
					},
				},
			},
			vmres: &vendormapping.GetDPMappingByIdResponse{
				Status: rpc.StatusInternal(),
			},
			err: nil,
			want: &pb.GetMoEngageIdResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternal(),
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			mockVendorMappingClient.EXPECT().GetDPMappingById(tt.args.ctx, &vendormapping.GetDPMappingByIdRequest{
				Identifier: &vendormapping.GetDPMappingByIdRequest_ActorId{ActorId: actorId},
			}).Return(tt.vmres, tt.err)

			got, err := feUserService.GetMoEngageId(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("GetMoEngageId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetMoEngageId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetProfilePageSections(t *testing.T) {
	logger.Init(cfg.TestEnv)
	var mockOnbClient *mocks3.MockOnboardingClient
	type mockStruct struct {
		mockOnbClient   *mocks3.MockOnboardingClient
		mockReleaseEval *mockRelease.MockIEvaluator
	}
	tests := []struct {
		name     string
		req      *pb.GetProfilePageSectionsRequest
		want     *pb.GetProfilePageSectionsResponse
		wantErr  error
		mockFunc func(mocks mockStruct)
	}{
		{
			name: "unsupported page type",
			req: &pb.GetProfilePageSectionsRequest{
				Req: &header.RequestHeader{
					Auth: &header.AuthHeader{
						ActorId: actorId,
					},
				},
				PageType: "abc",
			},
			want: &pb.GetProfilePageSectionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInvalidArgument(),
				},
			},
			wantErr: nil,
		},
		{
			name: "get reports and downloads page successfully",
			req: &pb.GetProfilePageSectionsRequest{
				Req: &header.RequestHeader{
					Auth: &header.AuthHeader{
						ActorId: actorId,
					},
				},
				PageType: pb.ProfilePageType_PROFILE_PAGE_TYPE_REPORTS_AND_DOWNLOADS.String(),
			},
			want: &pb.GetProfilePageSectionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				PageHeading: getTextByFontColorAndStyle(ReportsAndDownloadsPageHeading, ColorCodeNight, commontypes.FontStyle_HEADLINE_3),
				Sections: []*pb.ProfileSection{
					{
						SubSections: []*pb.ProfileSubSection{
							getInterestCertificateProfileSubSection(),
						},
						Heading: getTextByFontColorAndStyle(ReportsAndDownloadsSavingsAccountSectionHeading, ColorCodeNight, commontypes.FontStyle_HEADLINE_4),
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "get my profile sections for fi lite users successfully",
			req: &pb.GetProfilePageSectionsRequest{
				Req: &header.RequestHeader{
					Auth: &header.AuthHeader{
						ActorId: actorId,
					},
				},
				PageType: pb.ProfilePageType_PROFILE_PAGE_TYPE_MY_PROFILE.String(),
			},
			mockFunc: func(mocks mockStruct) {
				mocks.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), &beOnbPb.GetFeatureDetailsRequest{
					ActorId: actorId,
				}).Return(&beOnbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: true,
				}, nil)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
			},
			want: &pb.GetProfilePageSectionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				Sections: []*pb.ProfileSection{
					{
						SubSections: []*pb.ProfileSubSection{
							MyProfileAboutMeSubSection,
							helpSubSection,
							MyProfilePrivacyAndSecuritySubSection,
							MyProfileSettingsSubSection,
							MyProfileMcpCodeSubSection,
						},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "get my profile sections for fi lite users successfully when fi mcp is disabled",
			req: &pb.GetProfilePageSectionsRequest{
				Req: &header.RequestHeader{
					Auth: &header.AuthHeader{
						ActorId: actorId,
					},
				},
				PageType: pb.ProfilePageType_PROFILE_PAGE_TYPE_MY_PROFILE.String(),
			},
			mockFunc: func(mocks mockStruct) {
				mocks.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), &beOnbPb.GetFeatureDetailsRequest{
					ActorId: actorId,
				}).Return(&beOnbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: true,
				}, nil)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil)
			},
			want: &pb.GetProfilePageSectionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				Sections: []*pb.ProfileSection{
					{
						SubSections: []*pb.ProfileSubSection{
							MyProfileAboutMeSubSection,
							helpSubSection,
							MyProfilePrivacyAndSecuritySubSection,
							MyProfileSettingsSubSection,
						},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "get my profile sections for savings account users successfully",
			req: &pb.GetProfilePageSectionsRequest{
				Req: &header.RequestHeader{
					Auth: &header.AuthHeader{
						ActorId: actorId,
					},
				},
				PageType: pb.ProfilePageType_PROFILE_PAGE_TYPE_MY_PROFILE.String(),
			},
			mockFunc: func(mocks mockStruct) {
				mocks.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), &beOnbPb.GetFeatureDetailsRequest{
					ActorId: actorId,
				}).Return(&beOnbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
			},
			want: &pb.GetProfilePageSectionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				Sections: []*pb.ProfileSection{
					{
						SubSections: []*pb.ProfileSubSection{
							MyProfileSavingsAccountInfoSection,
							MyProfileAboutMeSubSection,
							helpSubSection,
							MyProfilePrivacyAndSecuritySubSection,
							MyProfileSettingsSubSection,
							MyProfileReportsAndDownloadsSubsection,
							MyProfileChequeBookSubSection,
							MyProfileAddressUpdateSubSection,
							MyProfileRewardsAndOffersSubSection,
							MyProfileMcpCodeSubSection,
						},
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "get my profile sections for savings account users successfully when fi mcp is disabled",
			req: &pb.GetProfilePageSectionsRequest{
				Req: &header.RequestHeader{
					Auth: &header.AuthHeader{
						ActorId: actorId,
					},
				},
				PageType: pb.ProfilePageType_PROFILE_PAGE_TYPE_MY_PROFILE.String(),
			},
			mockFunc: func(mocks mockStruct) {
				mocks.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), &beOnbPb.GetFeatureDetailsRequest{
					ActorId: actorId,
				}).Return(&beOnbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil)
				mocks.mockReleaseEval.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil)
			},
			want: &pb.GetProfilePageSectionsResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				Sections: []*pb.ProfileSection{
					{
						SubSections: []*pb.ProfileSubSection{
							MyProfileSavingsAccountInfoSection,
							MyProfileAboutMeSubSection,
							helpSubSection,
							MyProfilePrivacyAndSecuritySubSection,
							MyProfileSettingsSubSection,
							MyProfileReportsAndDownloadsSubsection,
							MyProfileChequeBookSubSection,
							MyProfileAddressUpdateSubSection,
							MyProfileRewardsAndOffersSubSection,
						},
					},
				},
			},
			wantErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockOnbClient = mocks3.NewMockOnboardingClient(ctr)
			mockReleaseEval := mockRelease.NewMockIEvaluator(ctr)
			mocks := mockStruct{
				mockOnbClient:   mockOnbClient,
				mockReleaseEval: mockReleaseEval,
			}
			s := &Service{
				onboardingClient: mockOnbClient,
				releaseEvaluator: mockReleaseEval,
				genConfig:        genconfig,
			}

			if tt.mockFunc != nil {
				tt.mockFunc(mocks)
			}
			got, err := s.GetProfilePageSections(context.Background(), tt.req)
			assert.Equal(t, tt.want, got)
			assert.Equal(t, tt.wantErr, err)
		})
	}
}

func TestService_CreateNominee(t *testing.T) {
	ctx := context.Background()
	const actorId0 = "actor-000"
	const actorId1 = "actor-001"
	var (
		uaeAddrWithEmptyPincode = &typesPb.PostalAddress{
			AddressLines: []string{"Dila du tujhe Burj khalifa"},
			RegionCode:   "United Arab Emirates",
			Locality:     "Dubai",
			PostalCode:   "",
		}
		indAddrWithEmptyPincode = &typesPb.PostalAddress{
			AddressLines: []string{"lol"},
			RegionCode:   "India",
			Locality:     "Ghaziabad",
			PostalCode:   "",
		}
		specialCharAddress = &typesPb.ContactInfo{
			PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999900000},
			EmailId:     "<EMAIL>",
			Address:     &typesPb.PostalAddress{AddressLines: []string{"", "%", "."}, RegionCode: "Region", Locality: "Locality", PostalCode: "560103"},
			AddressType: typesPb.AddressType_PERMANENT,
		}
		stdAddress = &typesPb.ContactInfo{
			PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999900000},
			EmailId:     "gangadhar@shaktimaan",
			Address:     &typesPb.PostalAddress{AddressLines: []string{"actual"}, RegionCode: "India", Locality: "Locality", PostalCode: "560103"},
			AddressType: typesPb.AddressType_PERMANENT,
		}
		nominee = &typesPb.Nominee{
			ActorId:      actorId0,
			Relationship: typesPb.RelationType_BROTHER,
			Name:         "Severus Snape",
			Dob:          &timestamp.Timestamp{Seconds: 654727808},
			ContactInfo:  specialCharAddress,
		}
		nomineeV2 = &pb.Nominee{
			Relationship: typesPb.RelationType_BROTHER,
			Name:         "Severus Snape",
			Dob:          &timestamp.Timestamp{Seconds: 654727808},
			ContactInfo:  specialCharAddress,
		}
		mockUserClient   *mockUser.MockUsersClient
		ce               = feErrors.NewClientError(feErrors.ALL_SPECIAL_CHARACTER)
		panErr           = feErrors.NewClientError(feErrors.EMPTY_NOMINEE_PIN)
		invalidPanErr, _ = ConstructErrorResponse(rpc.StatusInvalidArgument().Code,
			"Invalid request: Please provide valid pan", IncorrectDocumentTitle, IncorrectPanSubTitle)
		invalidAadhaarErr, _ = ConstructErrorResponse(rpc.StatusInvalidArgument().Code,
			"Invalid request: Please provide valid aadhaar", IncorrectDocumentTitle, IncorrectAadhaarSubTitle)
		invalidDrivingLicenseErr, _ = ConstructErrorResponse(rpc.StatusInvalidArgument().Code,
			"Invalid request: Please provide valid driving license", IncorrectDocumentTitle, IncorrectDrivingLicenseSubTitle)

		stdNominee = &typesPb.Nominee{
			ActorId:      actorId1,
			Relationship: typesPb.RelationType_BROTHER,
			Name:         "Gangadhar shaktimaan",
			Dob:          &timestamp.Timestamp{Seconds: 654727808},
			ContactInfo:  stdAddress,
		}

		stdNomineeV2 = &pb.Nominee{
			Relationship: typesPb.RelationType_BROTHER,
			Name:         "Gangadhar shaktimaan",
			Dob:          &timestamp.Timestamp{Seconds: 654727808},
			ContactInfo:  stdAddress,
		}

		nomineeWithInvalidPan = &typesPb.Nominee{
			ActorId:      actorId1,
			Relationship: typesPb.RelationType_BROTHER,
			Name:         "Gangadhar shaktimaan",
			Dob:          &timestamp.Timestamp{Seconds: 654727808},
			ContactInfo:  stdAddress,
			Pan:          "123213251",
		}

		nomineeV2WithInvalidPan = &pb.Nominee{
			Relationship:   typesPb.RelationType_BROTHER,
			Name:           "Gangadhar shaktimaan",
			Dob:            &timestamp.Timestamp{Seconds: 654727808},
			ContactInfo:    stdAddress,
			DocumentNumber: "123213251",
			DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN.String(),
		}

		nomineeV2WithInvalidAadhaar = &pb.Nominee{
			Relationship:   typesPb.RelationType_BROTHER,
			Name:           "Gangadhar shaktimaan",
			Dob:            &timestamp.Timestamp{Seconds: 654727808},
			ContactInfo:    stdAddress,
			DocumentNumber: "123213251",
			DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_AADHAAR.String(),
		}

		nomineeV2WithInvalidDL = &pb.Nominee{
			Relationship:   typesPb.RelationType_BROTHER,
			Name:           "Gangadhar shaktimaan",
			Dob:            &timestamp.Timestamp{Seconds: 654727808},
			ContactInfo:    stdAddress,
			DocumentNumber: "123213251",
			DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_DRIVING_LICENSE.String(),
		}

		nomineeWithAddrFn = func(addr *typesPb.PostalAddress) *typesPb.Nominee {
			n := proto.Clone(stdNominee).(*typesPb.Nominee)
			n.ContactInfo.Address = addr
			return n
		}
		nomineeWithV2AddrFn = func(addr *typesPb.PostalAddress) *pb.Nominee {
			n := proto.Clone(stdNomineeV2).(*pb.Nominee)
			n.ContactInfo.Address = addr
			return n
		}
		nomineeWithUAEAddrEmptyPincode     = nomineeWithAddrFn(uaeAddrWithEmptyPincode)
		nomineeWithIndiaAddrEmptyPincode   = nomineeWithAddrFn(indAddrWithEmptyPincode)
		nomineeV2WithUAEAddrEmptyPincode   = nomineeWithV2AddrFn(uaeAddrWithEmptyPincode)
		nomineeV2WithIndiaAddrEmptyPincode = nomineeWithV2AddrFn(indAddrWithEmptyPincode)
	)

	type mockStruct struct {
		mockUserClient *mockUser.MockUsersClient
	}
	type args struct {
		ctx context.Context
		req *pb.CreateNomineeRequest
	}
	tests := []struct {
		name     string
		args     args
		req      *pb.CreateNomineeRequest
		want     *pb.CreateNomineeResponse
		wantErr  bool
		mockFunc func(mock *mockStruct)
	}{
		{
			name: "Nominee with special character in address",
			req:  &pb.CreateNomineeRequest{Nominee: nominee},
			want: &pb.CreateNomineeResponse{
				Status:     feErrors.ErrViewStatus,
				NomineeId:  "",
				RespHeader: headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, ce.Subtitle, ""),
			},
		},
		{
			name: "Nominee v2 with special character in address",
			req:  &pb.CreateNomineeRequest{NomineeV2: nomineeV2},
			want: &pb.CreateNomineeResponse{
				Status:     feErrors.ErrViewStatus,
				NomineeId:  "",
				RespHeader: headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, ce.CodeStr, ce.Title, ce.Subtitle, ""),
			},
		},
		{
			name: "Nominee with empty pincode for india",
			req: &pb.CreateNomineeRequest{
				Nominee: nomineeWithIndiaAddrEmptyPincode,
			},
			want: &pb.CreateNomineeResponse{
				Status:     feErrors.ErrViewStatus,
				RespHeader: headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, panErr.CodeStr, panErr.Title, panErr.Subtitle, ""),
			},
		},
		{
			name: "Nominee v2 with empty pincode for india",
			req: &pb.CreateNomineeRequest{
				NomineeV2: nomineeV2WithIndiaAddrEmptyPincode,
			},
			want: &pb.CreateNomineeResponse{
				Status:     feErrors.ErrViewStatus,
				RespHeader: headerPkg.BottomSheetErrResp(feErrors.ErrViewStatus, panErr.CodeStr, panErr.Title, panErr.Subtitle, ""),
			},
		},
		{
			name: "Nominee with empty pincode for UAE",
			req: &pb.CreateNomineeRequest{
				Req: &header.RequestHeader{
					Auth: &header.AuthHeader{
						ActorId: nomineeWithUAEAddrEmptyPincode.GetActorId(),
					},
				},
				Nominee: nomineeWithUAEAddrEmptyPincode,
			},
			want: &pb.CreateNomineeResponse{
				Status:     rpc.StatusOk(),
				RespHeader: headerPkg.SuccessRespHeader(),
				NomineeId:  "nominee-id",
			},
			mockFunc: func(ms *mockStruct) {
				n := proto.Clone(nomineeWithUAEAddrEmptyPincode).(*typesPb.Nominee)
				n.ActorId = nomineeWithUAEAddrEmptyPincode.GetActorId()
				ms.mockUserClient.EXPECT().CreateNominee(gomock.Any(), mock.NewProtoMatcher(&bePb.CreateNomineeRequest{
					Nominee: nomineeWithAddrFn(uaeAddrWithEmptyPincode),
				})).Return(&bePb.CreateNomineeResponse{
					Status: rpc.StatusOk(),
					Nominee: &types.Nominee{
						Id: "nominee-id",
					},
				}, nil)
			},
		},
		{
			name: "Nominee V2 with empty pincode for UAE",
			req: &pb.CreateNomineeRequest{
				Req: &header.RequestHeader{
					Auth: &header.AuthHeader{
						ActorId: actorId1,
					},
				},
				NomineeV2: nomineeV2WithUAEAddrEmptyPincode,
			},
			want: &pb.CreateNomineeResponse{
				Status:     rpc.StatusOk(),
				RespHeader: headerPkg.SuccessRespHeader(),
				NomineeId:  "nominee-id",
			},
			mockFunc: func(ms *mockStruct) {
				n := proto.Clone(nomineeWithUAEAddrEmptyPincode).(*typesPb.Nominee)
				n.ActorId = actorId1
				ms.mockUserClient.EXPECT().CreateNominee(gomock.Any(), mock.NewProtoMatcher(&bePb.CreateNomineeRequest{
					Nominee: nomineeWithAddrFn(uaeAddrWithEmptyPincode),
				})).Return(&bePb.CreateNomineeResponse{
					Status: rpc.StatusOk(),
					Nominee: &types.Nominee{
						Id: "nominee-id",
					},
				}, nil)
			},
		},
		{
			name: "Nominee with invalid PAN",
			req:  &pb.CreateNomineeRequest{Nominee: nomineeWithInvalidPan},
			want: invalidPanErr,
		},
		{
			name: "Nominee v2 with invalid PAN",
			req:  &pb.CreateNomineeRequest{NomineeV2: nomineeV2WithInvalidPan},
			want: invalidPanErr,
		},
		{
			name: "Nominee v2 with invalid aadhaar",
			req:  &pb.CreateNomineeRequest{NomineeV2: nomineeV2WithInvalidAadhaar},
			want: invalidAadhaarErr,
		},
		{
			name: "Nominee v2 with invalid driving license",
			req:  &pb.CreateNomineeRequest{NomineeV2: nomineeV2WithInvalidDL},
			want: invalidDrivingLicenseErr,
		},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockUserClient = mockUser.NewMockUsersClient(ctr)
			mockStructs := &mockStruct{
				mockUserClient: mockUserClient,
			}
			s := &Service{
				client:      mockUserClient,
				eventBroker: &testMocks.NoTestMockBroker{},
			}
			if test.mockFunc != nil {
				test.mockFunc(mockStructs)
			}
			got, err := s.CreateNominee(ctx, test.req)
			if (err != nil) != test.wantErr {
				t.Errorf("CreateNominee() error = %v, wantErr %v", err, test.wantErr)
				return
			}

			if !proto.Equal(got, test.want) {
				t.Errorf("CreateNominee() got = %v, want %v", got, test.want)
			}
		})
	}
}

func TestService_GetPinCodeAreas(t *testing.T) {
	const (
		IndiaPincodeGzb = "201012"
	)

	type args struct {
		req *onbPb.GetPinCodeAreasRequest
	}
	type mockClients struct {
		onbClient *mocks3.MockOnboardingClient
	}
	tests := []struct {
		name      string
		args      args
		want      *onbPb.GetPinCodeAreasResponse
		wantMocks func(store *mockClients)
		wantErr   string
	}{
		{
			name: "request without country",
			args: args{
				req: &onbPb.GetPinCodeAreasRequest{
					PinCode: IndiaPincodeGzb,
				},
			},
			want: &onbPb.GetPinCodeAreasResponse{
				Status:     rpc.StatusOk(),
				RespHeader: headerPkg.SuccessRespHeader(),
				Areas: []*onbPb.GetPinCodeAreasResponse_Area{
					{
						City:  "Ghaziabad",
						State: "Uttar Pradesh",
					},
				},
			},
			wantMocks: func(c *mockClients) {
				c.onbClient.EXPECT().GetPinCodeDetails(gomock.Any(), gomock.Eq(&beOnbPb.GetPinCodeDetailsRequest{
					PinCode: IndiaPincodeGzb,
					FieldMask: []beOnbPb.PinCodeField{
						beOnbPb.PinCodeField_PIN_CODE_FIELD_DISTRICT,
						beOnbPb.PinCodeField_PIN_CODE_FIELD_STATE,
					},
				})).Return(&beOnbPb.GetPinCodeDetailsResponse{
					Status: rpc.StatusOk(),
					Details: []*beOnbPb.PinCodeDetails{
						{
							PinCode:  "201012",
							District: "Ghaziabad",
							State:    "Uttar Pradesh",
						},
					},
				}, nil)
			},
			wantErr: "",
		},
		{
			name: "request with India country code",
			args: args{
				req: &onbPb.GetPinCodeAreasRequest{
					PinCode:     IndiaPincodeGzb,
					CountryCode: types.CountryCode_COUNTRY_CODE_IND.String(),
				},
			},
			want: &onbPb.GetPinCodeAreasResponse{
				Status:     rpc.StatusOk(),
				RespHeader: headerPkg.SuccessRespHeader(),
				Areas: []*onbPb.GetPinCodeAreasResponse_Area{
					{
						City:  "Ghaziabad",
						State: "Uttar Pradesh",
					},
				},
			},
			wantMocks: func(c *mockClients) {
				c.onbClient.EXPECT().GetPinCodeDetails(gomock.Any(), gomock.Eq(&beOnbPb.GetPinCodeDetailsRequest{
					PinCode: IndiaPincodeGzb,
					FieldMask: []beOnbPb.PinCodeField{
						beOnbPb.PinCodeField_PIN_CODE_FIELD_DISTRICT,
						beOnbPb.PinCodeField_PIN_CODE_FIELD_STATE,
					},
				})).Return(&beOnbPb.GetPinCodeDetailsResponse{
					Status: rpc.StatusOk(),
					Details: []*beOnbPb.PinCodeDetails{
						{
							PinCode:  "201012",
							District: "Ghaziabad",
							State:    "Uttar Pradesh",
						},
					},
				}, nil)
			},
			wantErr: "",
		},
		{
			name: "request with UAE country code",
			args: args{
				req: &onbPb.GetPinCodeAreasRequest{
					CountryCode: types.CountryCode_COUNTRY_CODE_ARE.String(),
				},
			},
			want: &onbPb.GetPinCodeAreasResponse{
				RespHeader: headerPkg.SuccessRespHeader(),
				Areas: []*onbPb.GetPinCodeAreasResponse_Area{
					{
						City: address.CityAEAbuDhabi,
					},
					{
						City: address.CityAEDubai,
					},
					{
						City: address.CityAESharjah,
					},
					{
						City: address.CityAEAjman,
					},
					{
						City: address.CityAEUmmAlQuwain,
					},
					{
						City: address.CityAERasAlKhaimah,
					},
					{
						City: address.CityAEFujairah,
					},
				},
			},
		},
		{
			name: "request with unsupported country code",
			args: args{
				req: &onbPb.GetPinCodeAreasRequest{
					CountryCode: types.CountryCode_COUNTRY_CODE_AFG.String(),
				},
			},
			want: &onbPb.GetPinCodeAreasResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInvalidArgumentWithDebugMsg("unsupported country"),
				},
			},
		},
		{
			name: "request with invalid country code",
			args: args{
				req: &onbPb.GetPinCodeAreasRequest{
					CountryCode: "IN",
				},
			},
			wantErr: "invalid argument passed by the client: invalid country code",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			clients := &mockClients{
				onbClient: mocks3.NewMockOnboardingClient(ctrl),
			}
			s := &Service{
				onboardingClient: clients.onbClient,
			}
			if tt.wantMocks != nil {
				tt.wantMocks(clients)
			}
			got, err := s.GetPinCodeAreas(context.Background(), tt.args.req)
			if (err != nil) != (tt.wantErr != "") {
				t.Errorf("got error : %v\nwant error: %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr {
					t.Errorf("got error : %v\nwant error: %v", err, tt.wantErr)
				}
				return
			}
			assert.Equalf(t, tt.want, got, "GetPinCodeAreas(%v)", tt.args.req)
		})
	}
}

func Test_ConvertBeNomineeToFeNominee(t *testing.T) {
	var (
		stdAddress = &typesPb.ContactInfo{
			PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999900000},
			EmailId:     "gangadhar@shaktimaan",
			Address:     &typesPb.PostalAddress{AddressLines: []string{"actual"}, RegionCode: "India", Locality: "Locality", PostalCode: "560103"},
			AddressType: typesPb.AddressType_PERMANENT,
		}
		guardianInfo = &types.GuardianInfo{
			Relationship: types.RelationType_DE_FACTO_GUARDIAN,
			Name:         "Voldemort",
			ContactInfo:  stdAddress,
		}
	)
	tests := []struct {
		name      string
		beNominee *types.Nominee
		feNominee *types.Nominee
		want      bool
	}{
		{
			name: "Document empty from BE PAN empty",
			beNominee: &types.Nominee{
				Id:           "NOM000",
				Name:         "Nominee 0",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
			},
			feNominee: &types.Nominee{
				Id:           "NOM000",
				Name:         "Nominee 0",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
			},
			want: true,
		},
		{
			name: "Document empty from BE PAN empty 2",
			beNominee: &types.Nominee{
				Id:           "NOM001",
				Name:         "Nominee 1",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
			},
			feNominee: &types.Nominee{
				Id:           "NOM001",
				Name:         "Nominee 1",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
			},
			want: true,
		},
		{
			name: "Document have PAN from BE PAN is there in FE",
			beNominee: &types.Nominee{
				Id:           "NOM002",
				Name:         "Nominee 2",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
				NomineeDocument: &types.NomineeDocument{
					DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN,
					DocumentNumber: "**********",
				},
			},
			feNominee: &types.Nominee{
				Id:           "NOM002",
				Name:         "Nominee 2",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
				Pan:          "**********",
			},
			want: true,
		},
		{
			name: "Document have Aadhaar from BE PAN empty",
			beNominee: &types.Nominee{
				Id:           "NOM003",
				Name:         "Nominee 3",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
				NomineeDocument: &types.NomineeDocument{
					DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_AADHAAR,
					DocumentNumber: "1234",
				},
			},
			feNominee: &types.Nominee{
				Id:           "NOM003",
				Name:         "Nominee 3",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
			},
			want: true,
		},
		{
			name: "Document have Driving License from BE PAN empty",
			beNominee: &types.Nominee{
				Id:           "NOM004",
				Name:         "Nominee 4",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
				NomineeDocument: &types.NomineeDocument{
					DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_DRIVING_LICENSE,
					DocumentNumber: "MH1220040012345",
				},
			},
			feNominee: &types.Nominee{
				Id:           "NOM004",
				Name:         "Nominee 4",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
			},
			want: true,
		},
		{
			name: "Document have unspecified from BE PAN empty",
			beNominee: &types.Nominee{
				Id:           "NOM004",
				Name:         "Nominee 4",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
				NomineeDocument: &types.NomineeDocument{
					DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_UNSPECIFIED,
					DocumentNumber: "**********",
				},
			},
			feNominee: &types.Nominee{
				Id:           "NOM004",
				Name:         "Nominee 4",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nominee := ConvertBeNomineeToFeNominee(tt.beNominee)
			if proto.Equal(nominee, tt.feNominee) != tt.want {
				t.Errorf("got %v, want %v", nominee, tt.feNominee)
			}
		})
	}
}

func Test_ConvertBeNomineeToFeNomineeV2(t *testing.T) {
	var (
		stdAddress = &typesPb.ContactInfo{
			PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9999900000},
			EmailId:     "gangadhar@shaktimaan",
			Address:     &typesPb.PostalAddress{AddressLines: []string{"actual"}, RegionCode: "India", Locality: "Locality", PostalCode: "560103"},
			AddressType: typesPb.AddressType_PERMANENT,
		}
		guardianInfo = &types.GuardianInfo{
			Relationship: types.RelationType_DE_FACTO_GUARDIAN,
			Name:         "Voldemort",
			ContactInfo:  stdAddress,
		}
	)
	tests := []struct {
		name      string
		beNominee *types.Nominee
		feNominee *pb.Nominee
		want      bool
	}{
		{
			name: "Document empty fe document empty",
			beNominee: &types.Nominee{
				Id:           "NOM000",
				Name:         "Nominee 0",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
			},
			feNominee: &pb.Nominee{
				Id:           "NOM000",
				Name:         "Nominee 0",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
			},
			want: true,
		},
		{
			name: "Document empty fe document empty 2",
			beNominee: &types.Nominee{
				Id:           "NOM001",
				Name:         "Nominee 1",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
			},
			feNominee: &pb.Nominee{
				Id:           "NOM001",
				Name:         "Nominee 1",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
			},
			want: true,
		},
		{
			name: "Document have PAN from BE PAN is there in FE",
			beNominee: &types.Nominee{
				Id:           "NOM002",
				Name:         "Nominee 2",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
				NomineeDocument: &types.NomineeDocument{
					DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN,
					DocumentNumber: "**********",
				},
			},
			feNominee: &pb.Nominee{
				Id:             "NOM002",
				Name:           "Nominee 2",
				Relationship:   typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:            &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:    stdAddress,
				GuardianInfo:   guardianInfo,
				DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_PAN.String(),
				DocumentNumber: "**********",
			},
			want: true,
		},
		{
			name: "Document have aadhaar from BE aadhaar is there in FE",
			beNominee: &types.Nominee{
				Id:           "NOM003",
				Name:         "Nominee 3",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
				NomineeDocument: &types.NomineeDocument{
					DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_AADHAAR,
					DocumentNumber: "1234",
				},
			},
			feNominee: &pb.Nominee{
				Id:             "NOM003",
				Name:           "Nominee 3",
				Relationship:   typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:            &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:    stdAddress,
				GuardianInfo:   guardianInfo,
				DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_AADHAAR.String(),
				DocumentNumber: "1234",
			},
			want: true,
		},
		{
			name: "Document have driving license from BE driving license is there in FE",
			beNominee: &types.Nominee{
				Id:           "NOM004",
				Name:         "Nominee 4",
				Relationship: typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:          &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:  stdAddress,
				GuardianInfo: guardianInfo,
				NomineeDocument: &types.NomineeDocument{
					DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_DRIVING_LICENSE,
					DocumentNumber: "MH1220040012345",
				},
			},
			feNominee: &pb.Nominee{
				Id:             "NOM004",
				Name:           "Nominee 4",
				Relationship:   typesPb.RelationType_BENEFICIAL_OWNER,
				Dob:            &timestamp.Timestamp{Seconds: 654727808},
				ContactInfo:    stdAddress,
				GuardianInfo:   guardianInfo,
				DocumentType:   typesPb.NomineeDocumentType_NOMINEE_DOCUMENT_TYPE_DRIVING_LICENSE.String(),
				DocumentNumber: "MH1220040012345",
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nominee := ConvertBeNomineeToFeNomineeV2(tt.beNominee)
			if proto.Equal(nominee, tt.feNominee) != tt.want {
				t.Errorf("got %v, want %v", nominee, tt.feNominee)
			}
		})
	}
}
