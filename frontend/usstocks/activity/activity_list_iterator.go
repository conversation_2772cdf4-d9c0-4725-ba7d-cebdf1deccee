//go:generate mockgen -source=activity_list_iterator.go -destination=./mocks/activity_list_iterator.go -package=mocks
package activity

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/google/wire"
	"github.com/samber/lo"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	usstocksNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/usstocks"
	"github.com/epifi/gamma/api/typesv2/ui"
	usstocksPb "github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/catalog"
	orderManagerPb "github.com/epifi/gamma/api/usstocks/order"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/frontend/usstocks/utils"
	usstocksPkg "github.com/epifi/gamma/pkg/usstocks"
)

// nolint:dupl

var (
	ActivityListWireSet = wire.NewSet(NewPendingBeforeCompletedActivitiesIterator, wire.Bind(new(ActivityListIterator), new(*PendingBeforeCompletedActivitiesIterator)))
)

const (
	inProgressActivityGroupId         = "IN_PROGRESS_GROUP_ID"
	buyActivityDisplayNameFormat      = "Buy: %s"
	sellActivityDisplayNameFormat     = "Sell: %s"
	dividendActivityDisplayNameFormat = "Dividend: %s"
	sipBuyOrderDisplayNameFormat      = "SIP in %s"
	activityPageSize                  = 10
)

var (
	runningStateWalletStages = func() []usstocksPb.OrderState {
		return []usstocksPb.OrderState{
			usstocksPb.OrderState_ORDER_INITIATED,
			usstocksPb.OrderState_ORDER_CREATED,
			usstocksPb.OrderState_ORDER_MANUAL_INTERVENTION,
		}
	}

	terminalStageWalletStages = func() []usstocksPb.OrderState {
		return []usstocksPb.OrderState{
			usstocksPb.OrderState_ORDER_SUCCESS,
			usstocksPb.OrderState_ORDER_FAILED,
			usstocksPb.OrderState_ORDER_CANCELED,
			usstocksPb.OrderState_ORDER_EXPIRED,
		}
	}
)

// ActivityListIterator provides a method for callers to iterate over past account activities of a user.
// The iterator groups the output activities based on some logic (ex. grouping activities that happened on same day)
// before returning it to the caller.
type ActivityListIterator interface {
	GetAccountActivities(ctx context.Context, actorId string, filters *usstocks.AccountActivityFilters,
		pageCtx *rpc.PageContextRequest) ([]*usstocks.AccountActivityList, *rpc.PageContextResponse, error)
}

type PendingBeforeCompletedActivitiesIterator struct {
	orderManagerClient orderManagerPb.OrderManagerClient
}

func NewPendingBeforeCompletedActivitiesIterator(orderClient orderManagerPb.OrderManagerClient) *PendingBeforeCompletedActivitiesIterator {
	return &PendingBeforeCompletedActivitiesIterator{
		orderManagerClient: orderClient,
	}
}

// GetAccountActivities allows callers to filter account activity for an actor and iterate through it in a paginated manner
// Order of items in filtered result:
//  1. Order currently in processing stage ordered by (created_at desc)
//  2. Order that have been executed ordered by (created_at desc)
func (s *PendingBeforeCompletedActivitiesIterator) GetAccountActivities(ctx context.Context, actorId string, filters *usstocks.AccountActivityFilters,
	pageCtx *rpc.PageContextRequest) ([]*usstocks.AccountActivityList, *rpc.PageContextResponse, error) {
	bePageCtx, runningActivitiesRequested, err := s.convertToBeTokenReq(pageCtx.GetAfterToken(), activityPageSize)
	if err != nil {
		return nil, nil, fmt.Errorf("error while creating be page token : %w", err)
	}
	// pending activity will have market order and wallet order
	pendingActivityFilter := getPendingActivityFilterWithoutDividend(filters)
	orderStates := getOrderStateFromRunningStatus(runningActivitiesRequested)
	activityRes, err := s.fetchActivitiesFromBE(ctx, actorId, pendingActivityFilter, orderStates, bePageCtx)
	if err != nil {
		return nil, nil, err
	}
	nextPageToken, err := s.convertToFeTokenRes(activityRes.GetPageContext(), runningActivitiesRequested)
	if err != nil {
		return nil, nil, fmt.Errorf("error while creating fe page context response : %w", err)
	}
	activityListArr, err := s.createAccountActivityLists(ctx, activityRes)
	if err != nil {
		return nil, nil, err
	}
	// if items fetched from BE are less than our page size and there are more pages present, then fetch one more page with page size as the
	// number of items that are required to complete the existing page.
	// We will encounter this case when all the processing entries are fetches but the page is not complete yet, so we can fetch some completed entries.
	if len(activityRes.GetAccountActivities()) < activityPageSize && nextPageToken.GetHasAfter() {
		itemsToFetch := activityPageSize - len(activityRes.GetAccountActivities())
		bePageCtx, runningActivitiesRequested, err = s.convertToBeTokenReq(nextPageToken.GetAfterToken(), uint32(itemsToFetch))
		if err != nil {
			return nil, nil, fmt.Errorf("error while creating be page token : %w", err)
		}
		orderStates = getOrderStateFromRunningStatus(runningActivitiesRequested)
		activityRes, err = s.fetchActivitiesFromBE(ctx, actorId, filters, orderStates, bePageCtx)
		if err != nil {
			return nil, nil, err
		}
		nextPageToken, err = s.convertToFeTokenRes(activityRes.GetPageContext(), runningActivitiesRequested)
		if err != nil {
			return nil, nil, fmt.Errorf("error while creating fe page context response : %w", err)
		}
		var remainingListArr []*usstocks.AccountActivityList
		remainingListArr, err = s.createAccountActivityLists(ctx, activityRes)
		if err != nil {
			return nil, nil, err
		}
		activityListArr = append(activityListArr, remainingListArr...)
	}
	return activityListArr, nextPageToken, nil
}

// Ignore dividend activity during pending activity receipt, as dividends do not have an order state
func getPendingActivityFilterWithoutDividend(filters *usstocks.AccountActivityFilters) *usstocks.AccountActivityFilters {
	// pending activity will have market order and wallet order
	pendingActivityFilter := &usstocks.AccountActivityFilters{
		ActivityTypes: make([]usstocks.AccountActivityType, 0),
	}
	for _, activityType := range filters.GetActivityTypes() {
		if activityType != usstocks.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND {
			pendingActivityFilter.ActivityTypes = append(pendingActivityFilter.GetActivityTypes(), activityType)
		}
	}
	return pendingActivityFilter
}

func (s *PendingBeforeCompletedActivitiesIterator) fetchActivitiesFromBE(ctx context.Context, actorId string, filters *usstocks.AccountActivityFilters,
	orderStates []usstocksPb.OrderState, pageCtx *rpc.PageContextRequest) (*orderManagerPb.GetAccountActivitiesResponse, error) {
	activitiesRes, err := s.orderManagerClient.GetAccountActivities(ctx, &orderManagerPb.GetAccountActivitiesRequest{
		ActorId:      actorId,
		ActivityType: utils.ConvertToBeAccountActivityTypes(filters.GetActivityTypes()),
		OrderStates:  orderStates,
		PageContext:  pageCtx,
		FieldMask: []orderManagerPb.GetAccountActivitiesRequest_FieldMask{
			orderManagerPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_STOCKS_MAP,
			orderManagerPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_WALLET_ORDER_MAP,
			orderManagerPb.GetAccountActivitiesRequest_FIELD_MASK_POPULATE_ORDER_MAP,
		},
	})
	if rpcErr := epifigrpc.RPCError(activitiesRes, err); rpcErr != nil {
		return nil, fmt.Errorf("error while fetching activities from BE : %w", rpcErr)
	}
	return activitiesRes, nil
}

type pageToken struct {
	// specifies if we have completed iterating through "processing" activities.
	RunningActivitiesIterated bool
	NextPageToken             string
}

// convertToBeTokenReq converts token received from client to BE token, so that required entries can
// be fetched from BE.
func (s *PendingBeforeCompletedActivitiesIterator) convertToBeTokenReq(afterToken string, pageSize uint32) (*rpc.PageContextRequest, bool, error) {
	if afterToken == "" {
		return &rpc.PageContextRequest{
			PageSize: pageSize,
		}, true, nil
	}
	var fePageDetails pageToken
	err := json.Unmarshal([]byte(afterToken), &fePageDetails)
	if err != nil {
		return nil, false, fmt.Errorf("error while unmarshaling fe token : %w", err)
	}
	if fePageDetails.NextPageToken == "" {
		return &rpc.PageContextRequest{
			PageSize: pageSize,
		}, !fePageDetails.RunningActivitiesIterated, nil
	}
	return &rpc.PageContextRequest{
		Token: &rpc.PageContextRequest_AfterToken{
			AfterToken: fePageDetails.NextPageToken,
		},
		PageSize: pageSize,
	}, !fePageDetails.RunningActivitiesIterated, nil
}

// convertToFeTokenRes converts BE page token to FE token to be sent to user.
// It takes the token received from BE and a boolean flag representing whether we requested for processing entries or not.
// the boolean flag is required as, we're iterating through 2 different types of queries ( first querying all processing entries then all completed entries)
// When all processing entries are completed, BE will just send nil as next page token and at that point we need this flag to determine what FE next page token will be.
func (s *PendingBeforeCompletedActivitiesIterator) convertToFeTokenRes(beTokenRes *rpc.PageContextResponse,
	processingEntriesRequested bool) (*rpc.PageContextResponse, error) {
	if beTokenRes == nil {
		return nil, nil
	}
	feTokenRes := &rpc.PageContextResponse{
		BeforeToken: beTokenRes.GetBeforeToken(),
		HasBefore:   beTokenRes.GetHasBefore(),
	}
	var (
		afterToken *pageToken
		hasAfter   bool
		err        error
	)

	switch {
	case processingEntriesRequested && beTokenRes.GetHasAfter():
		afterToken = newPageToken(false, beTokenRes.GetAfterToken())
		hasAfter = true

	case processingEntriesRequested && !beTokenRes.GetHasAfter():
		afterToken = newPageToken(true, "")
		hasAfter = true

	case !processingEntriesRequested && beTokenRes.GetHasAfter():
		afterToken = newPageToken(true, beTokenRes.GetAfterToken())
		hasAfter = true

	case !processingEntriesRequested && !beTokenRes.GetHasAfter():
		hasAfter = false
	}
	var afterTokenStr []byte
	if hasAfter {
		afterTokenStr, err = json.Marshal(afterToken)
		if err != nil {
			return nil, fmt.Errorf("error while marshaling fe page token : %w", err)
		}
	}
	feTokenRes.AfterToken = string(afterTokenStr)
	feTokenRes.HasAfter = hasAfter
	return feTokenRes, nil
}

func (s *PendingBeforeCompletedActivitiesIterator) createAccountActivityLists(ctx context.Context,
	activityRes *orderManagerPb.GetAccountActivitiesResponse) ([]*usstocks.AccountActivityList, error) {
	activities := activityRes.GetAccountActivities()
	activityListMap := make(map[string][]*orderManagerPb.AccountActivity)
	activityListArr := make([]*usstocks.AccountActivityList, 0)
	for _, activity := range activities {
		groupId := s.getGroupIdForActivity(activity)
		activityListInMap, present := activityListMap[groupId]
		if present {
			activityListInMap = append(activityListInMap, activity)
			activityListMap[groupId] = activityListInMap
			continue
		}
		activityListMap[groupId] = []*orderManagerPb.AccountActivity{activity}
		activityListArr = append(activityListArr, &usstocks.AccountActivityList{
			GroupId: groupId,
		})
	}
	for _, list := range activityListArr {
		err1 := s.fillActivityList(ctx, list, activityListMap[list.GetGroupId()], activityRes)
		if err1 != nil {
			return nil, fmt.Errorf("error while generating account activity list : %w", err1)
		}
	}
	return activityListArr, nil
}

func (s *PendingBeforeCompletedActivitiesIterator) fillActivityList(ctx context.Context, activityList *usstocks.AccountActivityList,
	activities []*orderManagerPb.AccountActivity, activityRes *orderManagerPb.GetAccountActivitiesResponse) error {
	title, err := s.getGroupTitle(activityList.GetGroupId())
	if err != nil {
		return fmt.Errorf("error while generating title for activity list : %w", err)
	}
	activityList.Title = title
	tiles := make([]*usstocks.AccountActivity, 0)
	for _, activity := range activities {
		tile, err1 := s.createActivityTile(ctx, activity, activityRes)
		if err1 != nil {
			return err1
		}
		tiles = append(tiles, tile)
	}
	activityList.Activities = tiles
	return nil
}

func (s *PendingBeforeCompletedActivitiesIterator) createActivityTile(ctx context.Context, activity *orderManagerPb.AccountActivity,
	activityRes *orderManagerPb.GetAccountActivitiesResponse) (*usstocks.AccountActivity, error) {
	switch activity.GetType() {
	case orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_ADD_FUNDS,
		orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_ADD_FUNDS_FOR_SIP:
		return s.createActivityTileForAddFunds(ctx, activity, activityRes.GetWalletOrderMappedToActivity()[activity.GetOrderId()])
	case orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_WITHDRAW_FUNDS:
		return s.createActivityTileForWithdrawFunds(ctx, activity, activityRes.GetWalletOrderMappedToActivity()[activity.GetOrderId()])
	case orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY, orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_REWARD,
		orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_FOR_SIP:
		order := activityRes.GetOrderMappedToActivity()[activity.GetOrderId()]
		stock, present := activityRes.GetSymbolToStock()[order.GetSymbol()]
		if !present {
			return nil, fmt.Errorf("stock details not found for buy order : %s and symbol : %s", order.GetId(), order.GetSymbol())
		}
		return s.createActivityTileForBuyOrder(activity, order, stock)
	case orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_SELL:
		order := activityRes.GetOrderMappedToActivity()[activity.GetOrderId()]
		stock, present := activityRes.GetSymbolToStock()[order.GetSymbol()]
		if !present {
			return nil, fmt.Errorf("stock details not found for buy order")
		}
		return s.createActivityTileForSellOrder(activity, order, stock)
	case orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND:
		stock, present := activityRes.GetSymbolToStock()[activity.GetSymbol()]
		if !present {
			return nil, fmt.Errorf("stock details not found for buy order")
		}
		return s.createActivityTileForDividends(activity, stock)
	default:
		return nil, fmt.Errorf("unable to create activity tile : invalid account activity type %s", activity.GetType().String())
	}
}

// nolint:dupl
func (s *PendingBeforeCompletedActivitiesIterator) createActivityTileForAddFunds(ctx context.Context, activity *orderManagerPb.AccountActivity,
	order *orderManagerPb.WalletOrder) (*usstocks.AccountActivity, error) {
	if isActivityInRunningState(activity) {
		tile, err := s.createActivityTileForProcessingAddFunds(ctx, activity, order)
		if err != nil {
			return nil, err
		}
		return tile, nil
	}
	dollars, cents := moneyPkg.GetDisplayStringWithValueAndPrecision(order.GetInvoiceDetails().GetAmountIn_USD(), 2, false,
		true, moneyPkg.InternationalNumberSystem)
	activityTitle := "Funds added"
	if order.GetOrderSubType() == usstocksPb.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP {
		activityTitle = "SIP fund transfer"
	}
	if activity.GetOrderState() != usstocksPb.OrderState_ORDER_SUCCESS {
		if order.GetOrderSubType() == usstocksPb.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP {
			activityTitle = "SIP fund transfer failed"
		} else {
			activityTitle = "Add funds failed"
		}
	}

	return &usstocks.AccountActivity{
		Icon: commontypes.GetImageFromUrl(usstocksUi.WalletAddFundsArrow),
		Name: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(activityTitle).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(usstocksUi.Night),
		),
		Value: &ui.VerticalKeyValuePair{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText(dollars+cents).WithFontStyle(commontypes.FontStyle_SUBTITLE_3).WithFontColor(usstocksUi.Night),
			).WithRightImagePadding(
				2,
			).WithRightImageUrlHeightAndWidth(getActivityTxnRightIcon(activity), 16, 16),
		},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_ORDER_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksOrderReceiptScreenOptions{
				UsstocksOrderReceiptScreenOptions: &deeplink.USStocksOrderReceiptScreenOptions{
					Identifier: &deeplink.USStocksOrderReceiptScreenOptions_WalletOrderId{
						WalletOrderId: order.GetId(),
					},
				},
			},
		},
	}, nil
}

func (s *PendingBeforeCompletedActivitiesIterator) createActivityTileForProcessingAddFunds(ctx context.Context, _ *orderManagerPb.AccountActivity,
	order *orderManagerPb.WalletOrder) (*usstocks.AccountActivity, error) {
	processingDetails, err := s.orderManagerClient.GetWalletOrderProcessingDetails(ctx, &orderManagerPb.GetWalletOrderProcessingDetailsRequest{
		Id: &orderManagerPb.GetWalletOrderProcessingDetailsRequest_OrderId{
			OrderId: order.GetId(),
		},
		ActorId: order.GetActorId(),
	})
	if rpcErr := epifigrpc.RPCError(processingDetails, err); rpcErr != nil {
		return nil, fmt.Errorf("error while fetching processing details for wallet order : %w", rpcErr)
	}
	var completionEta time.Time
	for _, stageDetail := range processingDetails.GetStageDetails() {
		if stageDetail.GetStage() == string(usstocksNs.ForeignFundTransferStage) && stageDetail.GetEta() != nil {
			completionEta = stageDetail.GetEta().AsTime().In(datetime.IST)
			break
		}
	}
	tags := []*ui.IconTextComponent{
		getAccountActivityTag("Processing", usstocksUi.TertiaryT1, usstocksUi.PastelLemon),
	}
	if !completionEta.IsZero() {
		tags = append(tags, getAccountActivityTag(fmt.Sprintf("ETA %s", completionEta.Format("02 Jan")), usstocksUi.Lead, usstocksUi.Ivory))
	}
	dollars, cents := moneyPkg.GetDisplayStringWithValueAndPrecision(order.GetInvoiceDetails().GetAmountIn_USD(), 2, false,
		true, moneyPkg.InternationalNumberSystem)

	addFundsText := "Add funds"
	if order.GetOrderSubType() == usstocksPb.WalletOrderSubType_WALLET_ORDER_SUB_TYPE_ADD_FUNDS_SIP {
		addFundsText = "SIP fund transfer"
	}
	return &usstocks.AccountActivity{
		Icon: commontypes.GetImageFromUrl(usstocksUi.WalletAddFundsArrow),
		Name: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(addFundsText).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(usstocksUi.Night),
		),
		Tags: tags,
		Value: &ui.VerticalKeyValuePair{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText(dollars+cents).WithFontStyle(commontypes.FontStyle_SUBTITLE_3).WithFontColor(usstocksUi.Night),
			).WithRightImagePadding(
				2,
			).WithRightImageUrlHeightAndWidth(usstocksUi.ActivityTxnDownArrow, 16, 16),
		},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_ORDER_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksOrderReceiptScreenOptions{
				UsstocksOrderReceiptScreenOptions: &deeplink.USStocksOrderReceiptScreenOptions{
					Identifier: &deeplink.USStocksOrderReceiptScreenOptions_WalletOrderId{
						WalletOrderId: order.GetId(),
					},
				},
			},
		},
	}, nil
}

// nolint:dupl
func (s *PendingBeforeCompletedActivitiesIterator) createActivityTileForWithdrawFunds(ctx context.Context, activity *orderManagerPb.AccountActivity,
	order *orderManagerPb.WalletOrder) (*usstocks.AccountActivity, error) {
	if isActivityInRunningState(activity) {
		tile, err := s.createActivityTileForProcessingWithdrawFunds(ctx, activity, order)
		if err != nil {
			return nil, err
		}
		return tile, nil
	}

	activityTitle := "Withdrawn"
	if activity.GetOrderState() != usstocksPb.OrderState_ORDER_SUCCESS {
		activityTitle = "Withdrawal failed"
	}
	dollars, cents := moneyPkg.GetDisplayStringWithValueAndPrecision(order.GetInvoiceDetails().GetAmountIn_USD(), 2, false,
		true, moneyPkg.InternationalNumberSystem)
	return &usstocks.AccountActivity{
		Icon: commontypes.GetImageFromUrl(usstocksUi.WalletWithdrawArrow),
		Name: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(activityTitle).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(usstocksUi.Night),
		),
		Value: &ui.VerticalKeyValuePair{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText(dollars+cents).WithFontStyle(commontypes.FontStyle_SUBTITLE_3).WithFontColor(usstocksUi.Night),
			).WithRightImagePadding(
				2,
			).WithRightImageUrlHeightAndWidth(getActivityTxnRightIcon(activity), 16, 16),
		},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_ORDER_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksOrderReceiptScreenOptions{
				UsstocksOrderReceiptScreenOptions: &deeplink.USStocksOrderReceiptScreenOptions{
					Identifier: &deeplink.USStocksOrderReceiptScreenOptions_WalletOrderId{
						WalletOrderId: order.GetId(),
					},
				},
			},
		},
	}, nil
}

func (s *PendingBeforeCompletedActivitiesIterator) createActivityTileForProcessingWithdrawFunds(ctx context.Context, _ *orderManagerPb.AccountActivity,
	order *orderManagerPb.WalletOrder) (*usstocks.AccountActivity, error) {
	processingDetails, err := s.orderManagerClient.GetWalletOrderProcessingDetails(ctx, &orderManagerPb.GetWalletOrderProcessingDetailsRequest{
		Id: &orderManagerPb.GetWalletOrderProcessingDetailsRequest_OrderId{
			OrderId: order.GetId(),
		},
		ActorId: order.GetActorId(),
	})
	if rpcErr := epifigrpc.RPCError(processingDetails, err); rpcErr != nil {
		return nil, fmt.Errorf("error while fetching processing details for wallet order : %w", rpcErr)
	}
	var inwardRemittanceCompletionEta time.Time
	for _, stageDetail := range processingDetails.GetStageDetails() {
		if stageDetail.GetStage() == string(usstocksNs.TrackInwardsRemittanceStatusStage) && stageDetail.GetEta() != nil {
			inwardRemittanceCompletionEta = stageDetail.GetEta().AsTime().In(datetime.IST)
			break
		}
	}
	tags := []*ui.IconTextComponent{
		getAccountActivityTag("Processing", usstocksUi.TertiaryT1, usstocksUi.PastelLemon),
	}
	if !inwardRemittanceCompletionEta.IsZero() {
		tags = append(tags, getAccountActivityTag(fmt.Sprintf("ETA %s", inwardRemittanceCompletionEta.Format("02 Jan")), usstocksUi.Lead, usstocksUi.Ivory))
	}
	dollars, cents := moneyPkg.GetDisplayStringWithValueAndPrecision(order.GetInvoiceDetails().GetAmountIn_USD(), 2, false,
		true, moneyPkg.InternationalNumberSystem)

	return &usstocks.AccountActivity{
		Icon: commontypes.GetImageFromUrl(usstocksUi.WalletWithdrawArrow),
		Name: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText("Withdraw funds").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(usstocksUi.Night),
		),
		Tags: tags,
		Value: &ui.VerticalKeyValuePair{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText(dollars+cents).WithFontStyle(commontypes.FontStyle_SUBTITLE_3).WithFontColor(usstocksUi.Night),
			).WithRightImagePadding(
				2,
			).WithRightImageUrlHeightAndWidth(usstocksUi.ActivityTxnUpArrow, 16, 16),
		},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_ORDER_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksOrderReceiptScreenOptions{
				UsstocksOrderReceiptScreenOptions: &deeplink.USStocksOrderReceiptScreenOptions{
					Identifier: &deeplink.USStocksOrderReceiptScreenOptions_WalletOrderId{
						WalletOrderId: order.GetId(),
					},
				},
			},
		},
	}, nil
}

// nolint:dupl
func (s *PendingBeforeCompletedActivitiesIterator) createActivityTileForBuyOrder(activity *orderManagerPb.AccountActivity,
	order *orderManagerPb.Order, stock *catalog.Stock) (*usstocks.AccountActivity, error) {
	var orderAmount *moneyPb.Money
	if order.GetAmountConfirmed() != nil {
		orderAmount = order.GetAmountConfirmed()
	} else if order.GetAmountRequested() != nil {
		orderAmount = order.GetAmountRequested()
	}
	dollars, cents := moneyPkg.GetDisplayStringWithValueAndPrecision(orderAmount, 2, false,
		true, moneyPkg.InternationalNumberSystem)

	activityText := fmt.Sprintf(buyActivityDisplayNameFormat, stock.GetStockBasicDetails().GetName().GetShortName())
	if activity.GetType() == orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_REWARD {
		activityText = fmt.Sprintf("Reward stock: %s", stock.GetStockBasicDetails().GetName().GetShortName())
	}
	if activity.GetType() == orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_FOR_SIP {
		activityText = fmt.Sprintf(sipBuyOrderDisplayNameFormat, stock.GetStockBasicDetails().GetName().GetShortName())
	}
	tags := make([]*ui.IconTextComponent, 0)
	if isActivityInRunningState(activity) {
		tags = append(tags, getAccountActivityTag("Processing", usstocksUi.TertiaryT1, usstocksUi.PastelLemon))
	}

	return &usstocks.AccountActivity{
		Icon: commontypes.GetImageFromUrl(stock.GetStockBasicDetails().GetLogoUrl()),
		Name: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(activityText).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(usstocksUi.Night),
		),
		Tags: tags,
		Value: &ui.VerticalKeyValuePair{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText(dollars+cents).WithFontStyle(commontypes.FontStyle_SUBTITLE_3).WithFontColor(usstocksUi.Night),
			).WithRightImagePadding(
				2,
			).WithRightImageUrlHeightAndWidth(getActivityTxnRightIcon(activity), 16, 16),
		},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_ORDER_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksOrderReceiptScreenOptions{
				UsstocksOrderReceiptScreenOptions: &deeplink.USStocksOrderReceiptScreenOptions{
					Identifier: &deeplink.USStocksOrderReceiptScreenOptions_StockOrderId{
						StockOrderId: order.GetId(),
					},
				},
			},
		},
	}, nil
}

// nolint:dupl
func (s *PendingBeforeCompletedActivitiesIterator) createActivityTileForSellOrder(activity *orderManagerPb.AccountActivity,
	order *orderManagerPb.Order, stock *catalog.Stock) (*usstocks.AccountActivity, error) {
	var orderAmount *moneyPb.Money
	if order.GetAmountConfirmed() != nil {
		orderAmount = order.GetAmountConfirmed()
	} else if order.GetAmountRequested() != nil {
		orderAmount = order.GetAmountRequested()
	}
	dollars, cents := moneyPkg.GetDisplayStringWithValueAndPrecision(orderAmount, 2, false,
		true, moneyPkg.InternationalNumberSystem)
	activityText := fmt.Sprintf(sellActivityDisplayNameFormat, stock.GetStockBasicDetails().GetName().GetShortName())
	tags := make([]*ui.IconTextComponent, 0)
	if isActivityInRunningState(activity) {
		tags = append(tags, getAccountActivityTag("Processing", usstocksUi.TertiaryT1, usstocksUi.PastelLemon))
	}

	return &usstocks.AccountActivity{
		Icon: commontypes.GetImageFromUrl(stock.GetStockBasicDetails().GetLogoUrl()),
		Name: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(activityText).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(usstocksUi.Night),
		),
		Tags: tags,
		Value: &ui.VerticalKeyValuePair{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText(dollars+cents).WithFontStyle(commontypes.FontStyle_SUBTITLE_3).WithFontColor(usstocksUi.Night),
			).WithRightImagePadding(
				2,
			).WithRightImageUrlHeightAndWidth(getActivityTxnRightIcon(activity), 16, 16),
		},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_ORDER_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksOrderReceiptScreenOptions{
				UsstocksOrderReceiptScreenOptions: &deeplink.USStocksOrderReceiptScreenOptions{
					Identifier: &deeplink.USStocksOrderReceiptScreenOptions_StockOrderId{
						StockOrderId: order.GetId(),
					},
				},
			},
		},
	}, nil
}

func (s *PendingBeforeCompletedActivitiesIterator) createActivityTileForDividends(activity *orderManagerPb.AccountActivity, stock *catalog.Stock) (*usstocks.AccountActivity, error) {
	dollars, cents := moneyPkg.GetDisplayStringWithValueAndPrecision(activity.GetNetAmount(), 2, false,
		true, moneyPkg.InternationalNumberSystem)
	activityText := fmt.Sprintf(dividendActivityDisplayNameFormat, stock.GetStockBasicDetails().GetName().GetShortName())

	return &usstocks.AccountActivity{
		Icon: commontypes.GetImageFromUrl(stock.GetStockBasicDetails().GetLogoUrl()),
		Name: ui.NewITC().WithTexts(
			commontypes.GetPlainStringText(activityText).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(usstocksUi.Night),
		),
		Value: &ui.VerticalKeyValuePair{
			Title: ui.NewITC().WithTexts(
				commontypes.GetPlainStringText(dollars+cents).WithFontStyle(commontypes.FontStyle_SUBTITLE_3).WithFontColor(usstocksUi.Night),
			).WithRightImagePadding(
				2,
			).WithRightImageUrlHeightAndWidth(getActivityTxnRightIcon(activity), 16, 16),
		},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_ORDER_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksOrderReceiptScreenOptions{
				UsstocksOrderReceiptScreenOptions: &deeplink.USStocksOrderReceiptScreenOptions{
					Identifier: &deeplink.USStocksOrderReceiptScreenOptions_AccountActivityId{
						AccountActivityId: activity.GetId(),
					},
				},
			},
		},
	}, nil
}

func getActivityTxnRightIcon(activity *orderManagerPb.AccountActivity) string {
	rightIcon := usstocksUi.ActivityTxnUpArrow
	if activity.GetType() == orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_ADD_FUNDS ||
		activity.GetType() == orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_SELL ||
		activity.GetType() == orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_DIVIDEND ||
		activity.GetType() == orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_ADD_FUNDS_FOR_SIP {
		rightIcon = usstocksUi.ActivityTxnDownArrow
	}
	if activity.GetType() == orderManagerPb.AccountActivityType_ACCOUNT_ACTIVITY_TYPE_BUY_STOCK_REWARD {
		rightIcon = usstocksUi.GreyGiftSmallIcon
	}

	if usstocksPkg.IsOrderStateTerminal(activity.GetOrderState()) && activity.GetOrderState() != usstocksPb.OrderState_ORDER_SUCCESS {
		rightIcon = usstocksUi.ActivityTxnCrossIcon
	}
	return rightIcon
}

func getAccountActivityTag(text, fontColor, containerBgColor string) *ui.IconTextComponent {
	return ui.NewITC().WithTexts(
		commontypes.GetPlainStringText(text).WithFontStyle(commontypes.FontStyle_BODY_4).WithFontColor(fontColor),
	).WithContainer(
		0, 0, 11, containerBgColor,
	).WithContainerPadding(4, 8, 4, 8)
}

func (s *PendingBeforeCompletedActivitiesIterator) getGroupIdForActivity(activity *orderManagerPb.AccountActivity) string {
	if isActivityInRunningState(activity) {
		return inProgressActivityGroupId
	}
	return activity.GetCreatedAt().AsTime().In(datetime.IST).Format("2006-01-02")
}

func (s *PendingBeforeCompletedActivitiesIterator) getGroupTitle(groupId string) (*ui.IconTextComponent, error) {
	if groupId == inProgressActivityGroupId {
		return ui.NewITC().WithTexts(
			commontypes.GetPlainStringText("IN-PROGRESS").WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS).WithFontColor(usstocksUi.Lead),
		), nil
	}
	date, err := time.Parse("2006-01-02", groupId)
	if err != nil {
		return nil, fmt.Errorf("error while parsing date from activity list group id. groupId=%s - %w", groupId, err)
	}
	titleString := date.Format("02 January")
	return ui.NewITC().WithTexts(
		commontypes.GetPlainStringText(strings.ToUpper(titleString)).WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS).WithFontColor(usstocksUi.Lead),
	), nil
}

func newPageToken(runningActivitiesIterated bool, nextPageToken string) *pageToken {
	return &pageToken{
		NextPageToken:             nextPageToken,
		RunningActivitiesIterated: runningActivitiesIterated,
	}
}

func getOrderStateFromRunningStatus(runningProcessStates bool) []usstocksPb.OrderState {
	if runningProcessStates {
		return runningStateWalletStages()
	}
	return terminalStageWalletStages()
}

func isActivityInRunningState(activity *orderManagerPb.AccountActivity) bool {
	return lo.Contains(runningStateWalletStages(), activity.GetOrderState())
}
