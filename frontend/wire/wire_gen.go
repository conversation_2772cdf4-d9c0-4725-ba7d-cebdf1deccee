// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	genconf2 "github.com/epifi/be-common/pkg/cfg/genconf"
	types3 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/retry"
	genconf3 "github.com/epifi/be-common/quest/sdk/config/genconf"
	"github.com/epifi/gamma/analyser/creditscore/params_fetcher"
	dataprovider2 "github.com/epifi/gamma/analyser/dataprovider"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/accounts/statement"
	"github.com/epifi/gamma/api/accrual"
	"github.com/epifi/gamma/api/acquisition/crossattach"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/actor_activity"
	"github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/analyser/investment"
	"github.com/epifi/gamma/api/analyser/txnaggregates"
	"github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/biometrics"
	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/auth/location"
	orchestrator2 "github.com/epifi/gamma/api/auth/orchestrator"
	"github.com/epifi/gamma/api/auth/partnersdk"
	"github.com/epifi/gamma/api/auth/totp"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bankcust/compliance"
	"github.com/epifi/gamma/api/budgeting/reminder"
	"github.com/epifi/gamma/api/card/control"
	"github.com/epifi/gamma/api/card/currencyinsights"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/casper/exchanger"
	"github.com/epifi/gamma/api/casper/external_vendor_redemption"
	"github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/comms/inapptargetedcomms"
	"github.com/epifi/gamma/api/comms/user_preference"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/securities"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/credit_limit_estimator"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/cx/app_log"
	"github.com/epifi/gamma/api/cx/call_routing"
	"github.com/epifi/gamma/api/cx/chat"
	"github.com/epifi/gamma/api/cx/customer_auth"
	"github.com/epifi/gamma/api/cx/dispute"
	"github.com/epifi/gamma/api/cx/issue_config"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/datasharing"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/firefly/card_recommendation"
	"github.com/epifi/gamma/api/firefly/lms"
	pinot3 "github.com/epifi/gamma/api/firefly/pinot"
	"github.com/epifi/gamma/api/firefly/v2"
	"github.com/epifi/gamma/api/fittt"
	"github.com/epifi/gamma/api/fittt/sports"
	actoractivity2 "github.com/epifi/gamma/api/frontend/actoractivity"
	"github.com/epifi/gamma/api/frontend/analyser"
	card3 "github.com/epifi/gamma/api/frontend/card"
	categorizer3 "github.com/epifi/gamma/api/frontend/categorizer"
	connected_account2 "github.com/epifi/gamma/api/frontend/connected_account"
	home3 "github.com/epifi/gamma/api/frontend/cx/home"
	ticket2 "github.com/epifi/gamma/api/frontend/cx/ticket"
	"github.com/epifi/gamma/api/frontend/dynamic_elements"
	fcm2 "github.com/epifi/gamma/api/frontend/fcm"
	firefly2 "github.com/epifi/gamma/api/frontend/firefly"
	home2 "github.com/epifi/gamma/api/frontend/home"
	faq2 "github.com/epifi/gamma/api/frontend/inapphelp/faq"
	networth2 "github.com/epifi/gamma/api/frontend/insights/networth"
	"github.com/epifi/gamma/api/frontend/insights/secrets"
	"github.com/epifi/gamma/api/frontend/investment/aggregator"
	nudge2 "github.com/epifi/gamma/api/frontend/nudge"
	transaction2 "github.com/epifi/gamma/api/frontend/pay/transaction"
	preapprovedloan2 "github.com/epifi/gamma/api/frontend/preapprovedloan"
	recurringpayment3 "github.com/epifi/gamma/api/frontend/recurringpayment"
	"github.com/epifi/gamma/api/frontend/referral"
	rewards2 "github.com/epifi/gamma/api/frontend/rewards"
	search3 "github.com/epifi/gamma/api/frontend/search"
	timeline3 "github.com/epifi/gamma/api/frontend/timeline"
	wealthonboarding2 "github.com/epifi/gamma/api/frontend/wealthonboarding"
	"github.com/epifi/gamma/api/goals"
	"github.com/epifi/gamma/api/health_engine"
	"github.com/epifi/gamma/api/inapphelp/app_feedback"
	"github.com/epifi/gamma/api/inapphelp/faq/serving"
	"github.com/epifi/gamma/api/inapphelp/feedback_engine"
	"github.com/epifi/gamma/api/inapphelp/issue_reporting"
	"github.com/epifi/gamma/api/inapphelp/media"
	"github.com/epifi/gamma/api/inapphelp/recent_activity"
	"github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/api/inappreferral/season"
	"github.com/epifi/gamma/api/insights"
	"github.com/epifi/gamma/api/insights/accessinfo"
	"github.com/epifi/gamma/api/insights/emailparser"
	"github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/api/insights/kubair"
	"github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/insights/story"
	"github.com/epifi/gamma/api/insights/user_declaration"
	aggregator2 "github.com/epifi/gamma/api/investment/aggregator"
	auth3 "github.com/epifi/gamma/api/investment/auth"
	"github.com/epifi/gamma/api/investment/dynamic_ui_element"
	"github.com/epifi/gamma/api/investment/event_processor"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/investment/mutualfund/notifications"
	order2 "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	profile2 "github.com/epifi/gamma/api/investment/profile"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/agent"
	"github.com/epifi/gamma/api/kyc/docs"
	"github.com/epifi/gamma/api/kyc/uqudo"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/api/nudge/journey"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/aa"
	"github.com/epifi/gamma/api/order/actoractivity"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/p2pinvestment"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/beneficiarymanagement"
	internationalfundtransfer2 "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/lendability"
	"github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	"github.com/epifi/gamma/api/product"
	manager2 "github.com/epifi/gamma/api/quest/manager"
	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	"github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/luckydraw"
	pinot2 "github.com/epifi/gamma/api/rewards/pinot"
	"github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/api/rewards/rewardoffers"
	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/profile"
	manager3 "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/rms/ui"
	"github.com/epifi/gamma/api/salaryestimation"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	"github.com/epifi/gamma/api/salaryprogram/referrals"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/pinot"
	"github.com/epifi/gamma/api/timeline"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/upcomingtransactions"
	"github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/mandate"
	onboarding2 "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/upi/simulation"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/contact"
	"github.com/epifi/gamma/api/user/group"
	location2 "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/useractions"
	"github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/usstocks/account"
	catalog2 "github.com/epifi/gamma/api/usstocks/catalog"
	order3 "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/portfolio"
	rewards4 "github.com/epifi/gamma/api/usstocks/rewards"
	"github.com/epifi/gamma/api/vendordata/ip"
	employment2 "github.com/epifi/gamma/api/vendorgateway/employment"
	"github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	"github.com/epifi/gamma/api/vendorgateway/namecheck/employernamematch"
	"github.com/epifi/gamma/api/vendorgateway/ocr"
	payment2 "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	savings3 "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/comms/wire/types"
	datafetcher2 "github.com/epifi/gamma/connectedaccount/datafetcher"
	types4 "github.com/epifi/gamma/dynamicelements/wire/types"
	"github.com/epifi/gamma/frontend/account/saclosure"
	group2 "github.com/epifi/gamma/frontend/account/saclosure/criteria/group"
	"github.com/epifi/gamma/frontend/account/saclosure/criteria/item"
	orchestrator4 "github.com/epifi/gamma/frontend/account/saclosure/orchestrator"
	savings4 "github.com/epifi/gamma/frontend/account/saclosure/savings"
	"github.com/epifi/gamma/frontend/account/saclosure/screen"
	"github.com/epifi/gamma/frontend/account/screening"
	"github.com/epifi/gamma/frontend/account/signup"
	statement2 "github.com/epifi/gamma/frontend/account/statement"
	upi2 "github.com/epifi/gamma/frontend/account/upi"
	"github.com/epifi/gamma/frontend/acquisition"
	actoractivity3 "github.com/epifi/gamma/frontend/actoractivity"
	alfred2 "github.com/epifi/gamma/frontend/alfred"
	analyser2 "github.com/epifi/gamma/frontend/analyser"
	config2 "github.com/epifi/gamma/frontend/analyser/config"
	"github.com/epifi/gamma/frontend/analyser/dataprovider"
	"github.com/epifi/gamma/frontend/analyser/debt/credit_score"
	deeplink2 "github.com/epifi/gamma/frontend/analyser/deeplink"
	helper4 "github.com/epifi/gamma/frontend/analyser/helper"
	"github.com/epifi/gamma/frontend/analyser/helper/category"
	mutualfunds2 "github.com/epifi/gamma/frontend/analyser/helper/mutualfunds"
	"github.com/epifi/gamma/frontend/analyser/helper/time"
	"github.com/epifi/gamma/frontend/analyser/insights/loans"
	"github.com/epifi/gamma/frontend/analyser/investments/mutualfunds"
	"github.com/epifi/gamma/frontend/analyser/processor/analyser/executor"
	factory5 "github.com/epifi/gamma/frontend/analyser/processor/analyser/factory"
	"github.com/epifi/gamma/frontend/analyser/processor/analyser/handler"
	factory10 "github.com/epifi/gamma/frontend/analyser/processor/banner/factory"
	factory8 "github.com/epifi/gamma/frontend/analyser/processor/card/factory"
	"github.com/epifi/gamma/frontend/analyser/processor/feedback"
	"github.com/epifi/gamma/frontend/analyser/processor/feedback/reference_id_mapping"
	factory6 "github.com/epifi/gamma/frontend/analyser/processor/filter/factory"
	"github.com/epifi/gamma/frontend/analyser/processor/filter/generator"
	factory9 "github.com/epifi/gamma/frontend/analyser/processor/filterwidget/factory"
	factory4 "github.com/epifi/gamma/frontend/analyser/processor/hub/banner/factory"
	"github.com/epifi/gamma/frontend/analyser/processor/hub/section"
	"github.com/epifi/gamma/frontend/analyser/processor/hub/widget"
	factory3 "github.com/epifi/gamma/frontend/analyser/processor/hub/widget/factory"
	"github.com/epifi/gamma/frontend/analyser/processor/landingpage"
	factory7 "github.com/epifi/gamma/frontend/analyser/processor/landingpage/factory"
	"github.com/epifi/gamma/frontend/analyser/processor/status"
	release3 "github.com/epifi/gamma/frontend/analyser/release"
	"github.com/epifi/gamma/frontend/analyser/spends/top_categories"
	"github.com/epifi/gamma/frontend/analyser/util"
	"github.com/epifi/gamma/frontend/analyser/visualcomponents"
	"github.com/epifi/gamma/frontend/apikeys"
	auth2 "github.com/epifi/gamma/frontend/auth"
	liveness2 "github.com/epifi/gamma/frontend/auth/liveness"
	orchestrator3 "github.com/epifi/gamma/frontend/auth/orchestrator"
	partnersdk2 "github.com/epifi/gamma/frontend/auth/partnersdk"
	totp2 "github.com/epifi/gamma/frontend/auth/totp"
	"github.com/epifi/gamma/frontend/bank_customer"
	"github.com/epifi/gamma/frontend/budgeting/reminders"
	card2 "github.com/epifi/gamma/frontend/card"
	"github.com/epifi/gamma/frontend/card/dashboard_sections"
	categorizer2 "github.com/epifi/gamma/frontend/categorizer"
	"github.com/epifi/gamma/frontend/clientlogger"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/configsvc"
	connected_account3 "github.com/epifi/gamma/frontend/connected_account"
	factory2 "github.com/epifi/gamma/frontend/connected_account/factory"
	processor2 "github.com/epifi/gamma/frontend/connected_account/factory/processor"
	"github.com/epifi/gamma/frontend/connected_account/fi_to_fi"
	consent2 "github.com/epifi/gamma/frontend/consent"
	"github.com/epifi/gamma/frontend/consent/ack"
	"github.com/epifi/gamma/frontend/consent/ack/factory"
	"github.com/epifi/gamma/frontend/consent/ack/factory/ack_manager"
	"github.com/epifi/gamma/frontend/contacts"
	"github.com/epifi/gamma/frontend/credit_report"
	"github.com/epifi/gamma/frontend/cx/app_logs"
	"github.com/epifi/gamma/frontend/cx/call"
	chat2 "github.com/epifi/gamma/frontend/cx/chat"
	customer_auth2 "github.com/epifi/gamma/frontend/cx/customer_auth"
	dispute2 "github.com/epifi/gamma/frontend/cx/dispute"
	home4 "github.com/epifi/gamma/frontend/cx/home"
	ticket3 "github.com/epifi/gamma/frontend/cx/ticket"
	datasharing2 "github.com/epifi/gamma/frontend/datasharing"
	deposit2 "github.com/epifi/gamma/frontend/deposit"
	"github.com/epifi/gamma/frontend/deposit/accessor"
	"github.com/epifi/gamma/frontend/deposit/auto_save_suggestions"
	"github.com/epifi/gamma/frontend/deposit/creation"
	"github.com/epifi/gamma/frontend/deposit/creation/data_fetcher"
	"github.com/epifi/gamma/frontend/deposit/creation/navigation/navigators"
	"github.com/epifi/gamma/frontend/deposit/creation/primary"
	"github.com/epifi/gamma/frontend/deposit/creation/secondary/component_handlers"
	"github.com/epifi/gamma/frontend/digilocker"
	docs2 "github.com/epifi/gamma/frontend/docs"
	factory11 "github.com/epifi/gamma/frontend/docs/factory"
	"github.com/epifi/gamma/frontend/docs/factory/file_upload_manager"
	"github.com/epifi/gamma/frontend/document_upload"
	salaryprogram3 "github.com/epifi/gamma/frontend/document_upload/document_exchange/salaryprogram"
	usstocks3 "github.com/epifi/gamma/frontend/document_upload/document_exchange/usstocks"
	dynamic_elements2 "github.com/epifi/gamma/frontend/dynamic_elements"
	"github.com/epifi/gamma/frontend/fcm"
	firefly3 "github.com/epifi/gamma/frontend/firefly"
	"github.com/epifi/gamma/frontend/firefly/billinfo_v2"
	"github.com/epifi/gamma/frontend/firefly/fees_and_benefits"
	"github.com/epifi/gamma/frontend/firefly/homedashboard"
	generator2 "github.com/epifi/gamma/frontend/firefly/homedashboard/generator"
	"github.com/epifi/gamma/frontend/firefly/homedashboard/warning"
	rewards5 "github.com/epifi/gamma/frontend/firefly/rewards"
	fittt2 "github.com/epifi/gamma/frontend/fittt"
	"github.com/epifi/gamma/frontend/fittt/client_metrics"
	"github.com/epifi/gamma/frontend/genie"
	goals2 "github.com/epifi/gamma/frontend/goals"
	"github.com/epifi/gamma/frontend/home"
	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>/attributes"
	"github.com/epifi/gamma/frontend/home/<USER>/sort"
	"github.com/epifi/gamma/frontend/home/<USER>"
	"github.com/epifi/gamma/frontend/home/<USER>/components/scrollable"
	"github.com/epifi/gamma/frontend/home/<USER>/components/scrollable/dashboard"
	"github.com/epifi/gamma/frontend/home/<USER>/components/stickybottom"
	"github.com/epifi/gamma/frontend/home/<USER>/components/stickytop"
	actor_activity2 "github.com/epifi/gamma/frontend/inapphelp/actor_activity"
	app_feedback2 "github.com/epifi/gamma/frontend/inapphelp/app_feedback"
	"github.com/epifi/gamma/frontend/inapphelp/contact_us"
	"github.com/epifi/gamma/frontend/inapphelp/faq"
	feedback_engine2 "github.com/epifi/gamma/frontend/inapphelp/feedback_engine"
	media2 "github.com/epifi/gamma/frontend/inapphelp/media"
	insights2 "github.com/epifi/gamma/frontend/insights"
	accessinfo2 "github.com/epifi/gamma/frontend/insights/accessinfo"
	"github.com/epifi/gamma/frontend/insights/calculator"
	emailparser2 "github.com/epifi/gamma/frontend/insights/emailparser"
	epf2 "github.com/epifi/gamma/frontend/insights/epf"
	networth3 "github.com/epifi/gamma/frontend/insights/networth"
	config3 "github.com/epifi/gamma/frontend/insights/networth/config"
	data_fetcher2 "github.com/epifi/gamma/frontend/insights/networth/data_fetcher"
	deeplink3 "github.com/epifi/gamma/frontend/insights/networth/deeplink"
	section2 "github.com/epifi/gamma/frontend/insights/networth/generator/section"
	factory13 "github.com/epifi/gamma/frontend/insights/networth/generator/visualisation/factory"
	factory12 "github.com/epifi/gamma/frontend/insights/networth/generator/widget/factory"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/asset_dashboard"
	factory14 "github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/factory"
	networth4 "github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/networth"
	profile4 "github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/profile"
	user_declaration2 "github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/user_declaration"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputvalidator"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputvalidator/input_type_validator/data_type_validator"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputvalidator/input_type_validator/multi_edit_option_validator"
	dashboard3 "github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/dashboard"
	scrollable2 "github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/scrollable"
	dashboard2 "github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/scrollable/dashboard"
	secrets2 "github.com/epifi/gamma/frontend/insights/secrets"
	"github.com/epifi/gamma/frontend/insights/secrets/assets"
	ui7 "github.com/epifi/gamma/frontend/insights/secrets/assets/ui"
	valuegenerator4 "github.com/epifi/gamma/frontend/insights/secrets/assets/valuegenerator"
	processor3 "github.com/epifi/gamma/frontend/insights/secrets/collections/processor"
	"github.com/epifi/gamma/frontend/insights/secrets/componentbuilder"
	config4 "github.com/epifi/gamma/frontend/insights/secrets/config"
	credit_report2 "github.com/epifi/gamma/frontend/insights/secrets/credit_report"
	ui3 "github.com/epifi/gamma/frontend/insights/secrets/credit_report/ui"
	"github.com/epifi/gamma/frontend/insights/secrets/credit_report_secret"
	ui5 "github.com/epifi/gamma/frontend/insights/secrets/credit_report_secret/ui"
	valuegenerator2 "github.com/epifi/gamma/frontend/insights/secrets/credit_report_secret/valuegenerator"
	"github.com/epifi/gamma/frontend/insights/secrets/dataproviders"
	epf3 "github.com/epifi/gamma/frontend/insights/secrets/epf"
	ui2 "github.com/epifi/gamma/frontend/insights/secrets/epf/ui"
	"github.com/epifi/gamma/frontend/insights/secrets/epf/valuegenerator"
	"github.com/epifi/gamma/frontend/insights/secrets/epf/zero_state"
	"github.com/epifi/gamma/frontend/insights/secrets/epf_sms"
	ui8 "github.com/epifi/gamma/frontend/insights/secrets/epf_sms/ui"
	filter2 "github.com/epifi/gamma/frontend/insights/secrets/filter"
	"github.com/epifi/gamma/frontend/insights/secrets/mfhistory"
	ui9 "github.com/epifi/gamma/frontend/insights/secrets/mfhistory/ui"
	"github.com/epifi/gamma/frontend/insights/secrets/mfhistory/variablegenerator"
	"github.com/epifi/gamma/frontend/insights/secrets/mfperformance"
	ui10 "github.com/epifi/gamma/frontend/insights/secrets/mfperformance/ui"
	variablegenerator2 "github.com/epifi/gamma/frontend/insights/secrets/mfperformance/variablegenerator"
	"github.com/epifi/gamma/frontend/insights/secrets/mfportfolio"
	dataprovider3 "github.com/epifi/gamma/frontend/insights/secrets/mfportfolio/dataprovider"
	ui6 "github.com/epifi/gamma/frontend/insights/secrets/mfportfolio/ui"
	valuegenerator3 "github.com/epifi/gamma/frontend/insights/secrets/mfportfolio/valuegenerator"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/portfolio_tracker_ui"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/portfolio_tracker_ui/asset_wise_distribution_builder"
	"github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"
	"github.com/epifi/gamma/frontend/insights/secrets/salary_report"
	ui11 "github.com/epifi/gamma/frontend/insights/secrets/salary_report/ui"
	factory15 "github.com/epifi/gamma/frontend/insights/secrets/secret_builder/factory"
	"github.com/epifi/gamma/frontend/insights/secrets/secret_builder/footer"
	"github.com/epifi/gamma/frontend/insights/secrets/secret_builder/lock"
	"github.com/epifi/gamma/frontend/insights/secrets/secret_builder/secret_provider"
	category2 "github.com/epifi/gamma/frontend/insights/secrets/spends/category"
	ui4 "github.com/epifi/gamma/frontend/insights/secrets/spends/category/ui"
	"github.com/epifi/gamma/frontend/insights/secrets/wealth_analyser_report"
	"github.com/epifi/gamma/frontend/insights/secrets/zero_state_helper"
	story2 "github.com/epifi/gamma/frontend/insights/story"
	aggregator3 "github.com/epifi/gamma/frontend/investment/aggregator"
	"github.com/epifi/gamma/frontend/investment/aggregator/recommendation_plugins"
	"github.com/epifi/gamma/frontend/investment/aggregator/retention"
	"github.com/epifi/gamma/frontend/investment/aggregator/retention/Instruments/fixed_deposit/components"
	components2 "github.com/epifi/gamma/frontend/investment/aggregator/retention/Instruments/smart_deposit/components"
	navigators2 "github.com/epifi/gamma/frontend/investment/aggregator/retention/navigation/navigators"
	"github.com/epifi/gamma/frontend/investment/broker_details"
	"github.com/epifi/gamma/frontend/investment/indianstocks"
	dashboard4 "github.com/epifi/gamma/frontend/investment/indianstocks/dashboard"
	"github.com/epifi/gamma/frontend/investment/indianstocks/dashboard/filter"
	data_fetcher3 "github.com/epifi/gamma/frontend/investment/indianstocks/data_fetcher"
	"github.com/epifi/gamma/frontend/investment/mutualfund"
	profile3 "github.com/epifi/gamma/frontend/investment/profile"
	kubair2 "github.com/epifi/gamma/frontend/kubair"
	uqudo2 "github.com/epifi/gamma/frontend/kyc/uqudo"
	vkyc2 "github.com/epifi/gamma/frontend/kyc/vkyc"
	media3 "github.com/epifi/gamma/frontend/media"
	nudge3 "github.com/epifi/gamma/frontend/nudge"
	journey2 "github.com/epifi/gamma/frontend/nudge/journey"
	widget2 "github.com/epifi/gamma/frontend/nudge/widget"
	"github.com/epifi/gamma/frontend/otp"
	"github.com/epifi/gamma/frontend/otp/investment/mutual_fund"
	p2pinvestment2 "github.com/epifi/gamma/frontend/p2pinvestment"
	benefits3 "github.com/epifi/gamma/frontend/p2pinvestment/benefits"
	"github.com/epifi/gamma/frontend/p2pinvestment/deeplinks"
	helper3 "github.com/epifi/gamma/frontend/p2pinvestment/helper"
	pan2 "github.com/epifi/gamma/frontend/pan"
	pay2 "github.com/epifi/gamma/frontend/pay"
	"github.com/epifi/gamma/frontend/pay/search/impl"
	provider2 "github.com/epifi/gamma/frontend/pay/search/provider"
	"github.com/epifi/gamma/frontend/pay/simulator"
	"github.com/epifi/gamma/frontend/pay/transaction"
	data_collector2 "github.com/epifi/gamma/frontend/pay/transaction/payment_options/data_collector"
	"github.com/epifi/gamma/frontend/pay/transaction/validator/fund_transfer_validator"
	preapprovedloan3 "github.com/epifi/gamma/frontend/preapprovedloan"
	consent3 "github.com/epifi/gamma/frontend/preapprovedloan/datacollectors/consent"
	deeplinks2 "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/aggregated_deeplinks"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/abfl"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/epifitech"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/federal"
	fiftyfin2 "github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/fiftyfin"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/idfc"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/lenden"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/liquiloans"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/moneyview"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/realtimeetb"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/stockguardian"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	dashboard5 "github.com/epifi/gamma/frontend/preapprovedloan/home/<USER>"
	"github.com/epifi/gamma/frontend/preapprovedloan/home/<USER>"
	"github.com/epifi/gamma/frontend/prompt"
	"github.com/epifi/gamma/frontend/prompt/processors"
	qr2 "github.com/epifi/gamma/frontend/qr"
	recurringpayment2 "github.com/epifi/gamma/frontend/recurringpayment"
	referral2 "github.com/epifi/gamma/frontend/referral"
	rewards3 "github.com/epifi/gamma/frontend/rewards"
	navigation2 "github.com/epifi/gamma/frontend/rewards/home/<USER>"
	"github.com/epifi/gamma/frontend/rewards/offerdisplay"
	"github.com/epifi/gamma/frontend/rewards/offerwidget"
	"github.com/epifi/gamma/frontend/rewards/tags"
	salaryestimation2 "github.com/epifi/gamma/frontend/salaryestimation"
	salaryprogram2 "github.com/epifi/gamma/frontend/salaryprogram"
	"github.com/epifi/gamma/frontend/salaryprogram/aa_salary"
	benefits2 "github.com/epifi/gamma/frontend/salaryprogram/benefits"
	savings2 "github.com/epifi/gamma/frontend/savings"
	search2 "github.com/epifi/gamma/frontend/search"
	"github.com/epifi/gamma/frontend/smsfetcher"
	epf4 "github.com/epifi/gamma/frontend/smsfetcher/epf"
	"github.com/epifi/gamma/frontend/stockguardian/matrix"
	tiering2 "github.com/epifi/gamma/frontend/tiering"
	"github.com/epifi/gamma/frontend/tiering/add_funds"
	"github.com/epifi/gamma/frontend/tiering/benefits"
	cta2 "github.com/epifi/gamma/frontend/tiering/cta"
	"github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/deeplink"
	"github.com/epifi/gamma/frontend/tiering/deeplink/plan_options"
	"github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/bottom_info"
	"github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/card"
	"github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/cta"
	"github.com/epifi/gamma/frontend/tiering/earned_benefits"
	"github.com/epifi/gamma/frontend/tiering/feedback_flow_integration"
	release2 "github.com/epifi/gamma/frontend/tiering/release"
	timeline2 "github.com/epifi/gamma/frontend/timeline"
	upi3 "github.com/epifi/gamma/frontend/upi"
	onboarding4 "github.com/epifi/gamma/frontend/upi/onboarding"
	user3 "github.com/epifi/gamma/frontend/user"
	"github.com/epifi/gamma/frontend/user/update_form_details_processor"
	usstocks2 "github.com/epifi/gamma/frontend/usstocks"
	"github.com/epifi/gamma/frontend/usstocks/activity"
	"github.com/epifi/gamma/frontend/usstocks/dropoff"
	"github.com/epifi/gamma/frontend/usstocks/orderdetails"
	"github.com/epifi/gamma/frontend/vkyccall"
	"github.com/epifi/gamma/frontend/waitlist"
	onboarding3 "github.com/epifi/gamma/frontend/wealth/onboarding"
	"github.com/epifi/gamma/frontend/wire/provider"
	types2 "github.com/epifi/gamma/frontend/wire/types"
	"github.com/epifi/gamma/insights/pkg"
	"github.com/epifi/gamma/pkg/acquisition/sourceandintent"
	"github.com/epifi/gamma/pkg/deeplinkv2"
	"github.com/epifi/gamma/pkg/dmf/datafetcher"
	txnaggregates2 "github.com/epifi/gamma/pkg/dmf/txnaggregates"
	"github.com/epifi/gamma/pkg/feature/release"
	fittt3 "github.com/epifi/gamma/pkg/fittt"
	internationalfundtransfer3 "github.com/epifi/gamma/pkg/internationalfundtransfer"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/a2form"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/gst"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/invoice"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/tcs"
	"github.com/epifi/gamma/pkg/qr"
	user2 "github.com/epifi/gamma/pkg/user"
	"github.com/epifi/gamma/pkg/usstocks"
	genconf4 "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/quest/sdk"
	"github.com/epifi/gamma/quest/sdk/init"
	helper2 "github.com/epifi/gamma/upcomingtransactions/helper"
	pkg2 "github.com/epifi/gamma/verifi/pkg"
	"go.uber.org/zap"
)

// Injectors from wire.go:

func InitializeSignupService(authClient auth.AuthClient, usersClient user.UsersClient, kycClient kyc.KycClient, livenessClient liveness.LivenessClient, actorClient actor.ActorClient, consentClient consent.ConsentClient, onboardingClient onboarding.OnboardingClient, cardClient provisioning.CardProvisioningClient, broker events.Broker, deviceTokenClient types.FCMDeviceTokenClientWithInterceptors, vendorMappingClient vendormapping.VendorMappingServiceClient, vkycClient vkyc.VKYCClient, location2 location.LocationClient, extacctClient extacct.ExternalAccountsClient, genConfig *genconf.Config, uaClient useractions.UserActionsClient, empClient employment.EmploymentClient, obfuscatorClient obfuscator.ObfuscatorClient, savClient savings.SavingsClient, bcClient bankcust.BankCustomerServiceClient, opStatusClient operstatus.OperationalStatusServiceClient, panClient pan.PanClient, caClient connected_account.ConnectedAccountClient, userGroupClient group.GroupClient, productClient product.ProductClient, creditReportV2Client creditreportv2.CreditReportManagerClient, riskClient risk.RiskClient, ipClient ip.IpServiceClient, userCommsPrefClient user_preference.UserPreferenceClient) (*signup.Service, error) {
	fcmDeviceTokenClient := types.FCMDeviceTokenClientProvider(deviceTokenClient)
	qrCode := BKYCQRConfigProvider(genConfig)
	qrCodeProcImpl := qr.NewQRCodeProcImpl(qrCode)
	defaultTime := datetime.NewDefaultTime()
	decryptionKeys := types2.NewAcqSourceIntentDecryptionKeysProvider(genConfig)
	identifier := sourceandintent.NewIdentifier(decryptionKeys)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	retryParams := provider.GNOARetryStrategyProvider(genConfig)
	retryStrategy, err := retry.NewStrategyFromConfig(retryParams)
	if err != nil {
		return nil, err
	}
	nsdlPanValidator := processor.NewNsdlPanValidator(usersClient, genConfig, consentClient, broker)
	federalOnboardingPan := processor.NewFederalOnboardingPan(usersClient, genConfig, consentClient, creditReportV2Client, broker)
	v := signup.NewPANFormProcessors(nsdlPanValidator, federalOnboardingPan)
	service := signup.NewService(authClient, usersClient, kycClient, actorClient, livenessClient, consentClient, onboardingClient, cardClient, broker, fcmDeviceTokenClient, vendorMappingClient, vkycClient, location2, extacctClient, genConfig, uaClient, empClient, obfuscatorClient, savClient, bcClient, opStatusClient, panClient, caClient, qrCodeProcImpl, defaultTime, identifier, userGroupClient, productClient, domainIdGenerator, creditReportV2Client, riskClient, retryStrategy, ipClient, userCommsPrefClient, v)
	return service, nil
}

func InitialiseAcquisitionService(broker events.Broker, genConfig *genconf.Config, authClient auth.AuthClient) *acquisition.Service {
	decryptionKeys := types2.NewAcqSourceIntentDecryptionKeysProvider(genConfig)
	identifier := sourceandintent.NewIdentifier(decryptionKeys)
	service := acquisition.NewService(broker, identifier, authClient)
	return service
}

func InitializeUPIService(actorClient actor.ActorClient, savingsClient savings.SavingsClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, upiClient upi.UPIClient, feConfig *config.Config, bcClient bankcust.BankCustomerServiceClient, usersClient user.UsersClient, upiOnboardingClient onboarding2.UpiOnboardingClient, onboardingClient onboarding.OnboardingClient) *upi2.Service {
	service := upi2.NewService(savingsClient, actorClient, piClient, accountPiClient, upiClient, usersClient, upiOnboardingClient, onboardingClient, feConfig)
	return service
}

func InitializeSavingsService(usersClient user.UsersClient, savingsClient savings.SavingsClient, authClient auth.AuthClient, cardClient provisioning.CardProvisioningClient, accountPiClient account_pi.AccountPIRelationClient, piClient paymentinstrument.PiClient, actorClient actor.ActorClient, conf *config.Config, onboardingClient onboarding.OnboardingClient, accountBalanceClient balance.BalanceClient, kycClient kyc.KycClient, bcClient bankcust.BankCustomerServiceClient) *savings2.Service {
	defaultTime := datetime.NewDefaultTime()
	service := savings2.NewSavingsService(savingsClient, usersClient, authClient, cardClient, accountPiClient, piClient, actorClient, conf, onboardingClient, defaultTime, accountBalanceClient, kycClient, bcClient)
	return service
}

func InitializeStatementService(savingsClient savings.SavingsClient, statementClient statement.AccountStatementClient, conf *config.Config, depositClient deposit.DepositClient, actorClient actor.ActorClient) *statement2.Service {
	service := statement2.NewStatementService(savingsClient, statementClient, conf, depositClient, actorClient)
	return service
}

func InitializeConsentService(authClient auth.AuthClient, beConsentClient consent.ConsentClient, userCommsPrefClient types.UserPreferenceClientWithInterceptors, client onboarding.OnboardingClient, actorClient actor.ActorClient, upiOnboardingClient onboarding2.UpiOnboardingClient, userClient user.UsersClient) *consent2.Service {
	userPreferenceClient := types.UserPreferenceClientProvider(userCommsPrefClient)
	service := consent2.NewConsentService(beConsentClient, userPreferenceClient, client, upiOnboardingClient, actorClient, userClient)
	return service
}

func InitialiseAckService(vkycFeClient vkyc.VKYCFeClient, onbClient onboarding.OnboardingClient, compClient compliance.ComplianceClient) *ack.AckService {
	vkycAckManager := manager.NewVKYCAckManager(vkycFeClient)
	onbAckManager := manager.NewOnbAckManager(onbClient)
	bankCustomerAckManager := manager.NewBankCustomerAckManager(compClient)
	ackFactoryService := factory.NewAckFactoryService(vkycAckManager, onbAckManager, bankCustomerAckManager)
	ackService := ack.NewAckService(ackFactoryService)
	return ackService
}

func InitializePayTransactionService(conf *genconf.Config, orderClient order.OrderServiceClient, paymentClient payment.PaymentClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, categorizerClient categorizer.TxnCategorizerClient, accountPIRelationClient account_pi.AccountPIRelationClient, piClient paymentinstrument.PiClient, decisionEngineClient payment.DecisionEngineClient, authClient auth.AuthClient, upiClient upi.UPIClient, timeline2 timeline.TimelineServiceClient, userClient user.UsersClient, onboardingClient onboarding.OnboardingClient, broker events.Broker, rewardOfferClient rewardoffers.RewardOffersClient, userGroupClient group.GroupClient, payClient pay.PayClient, upiOnboardingClient onboarding2.UpiOnboardingClient, bankCustClient bankcust.BankCustomerServiceClient, tieringClient tiering.TieringClient, tieringPinotClient pinot.EODBalanceClient, beSalaryClient salaryprogram.SalaryProgramClient, healthEngineClient health_engine.HealthEngineServiceClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, serverConf *config.Config, inappreferralClient inappreferral.InAppReferralClient, userIntelClient userintel.UserIntelServiceClient, operationStatusSvcClient operstatus.OperationalStatusServiceClient, accountBalanceClient balance.BalanceClient, cardClient provisioning.CardProvisioningClient, alfredClient alfred.AlfredClient, rewardsClient rewards.RewardsGeneratorClient, projectionClient projector.ProjectorServiceClient, txnAggregateClient txnaggregates.TxnAggregatesClient, segmentationClient segment.SegmentationServiceClient, rewardsAggregatesClient pinot2.RewardsAggregatesClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, pgServiceClient paymentgateway.PaymentGatewayServiceClient, accountManagerClient account.AccountManagerClient, employmentClient employment.EmploymentClient, healthInsuranceClient healthinsurance.HealthInsuranceClient) (*transaction.Service, error) {
	fundTransferValidator := fund_transfer_validator.NewFundTransferValidator(accountPIRelationClient)
	dataCollectorService := data_collector.NewDataCollectorService(conf, savingsClient, actorClient, bankCustClient, beSalaryClient, accountBalanceClient, tieringClient, cardClient, alfredClient, rewardsClient, rewardsAggregatesClient, projectionClient, txnAggregateClient, orderClient, accountManagerClient, employmentClient, healthInsuranceClient, rewardOfferClient, userClient, userGroupClient)
	service := benefits.NewBenefitsManagerService(conf)
	bottom_infoService := bottom_info.NewBottomInfoManagerService(tieringClient)
	ctaService := cta.NewCtaManagerService(conf)
	cardService := card.NewCardManagerService()
	plan_optionsService := plan_options.NewPlanOptionsManagerService(conf, service, bottom_infoService, ctaService, cardService, broker)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	benefitsService := benefits2.NewService(rewardOfferClient, evaluator, conf)
	tieringBenefitsService := benefits3.NewTieringBenefitsService(p2pInvestmentClient)
	deeplinkService := deeplink.NewDeeplinkManagerService(conf, dataCollectorService, plan_optionsService, service, tieringClient, benefitsService, tieringBenefitsService, cardClient, segmentationClient, beSalaryClient, broker, userClient)
	feManagerService := release2.NewFeReleaseManagerService(conf)
	tieringAddFundsManagerService := add_funds.NewTieringAddFundsManagerService(conf, tieringClient, dataCollectorService, feManagerService)
	data_collectorDataCollectorService := data_collector2.NewPaymentOptionsDataCollector(serverConf, conf, savingsClient, accountPIRelationClient, upiOnboardingClient, authClient)
	tierMovementBasedFeedbackFlowIntegrationSvc := feedback_flow_integration.NewTierMovementBasedFeedbackFlowIntegrationSvc(conf, tieringClient)
	v := provider.PaymentOptionsProvider(conf, serverConf)
	transactionService, err := transaction.NewTransactionService(conf, orderClient, paymentClient, savingsClient, categorizerClient, actorClient, accountPIRelationClient, piClient, decisionEngineClient, authClient, upiClient, timeline2, userClient, onboardingClient, broker, rewardOfferClient, userGroupClient, fundTransferValidator, payClient, upiOnboardingClient, bankCustClient, tieringClient, tieringPinotClient, deeplinkService, tieringAddFundsManagerService, healthEngineClient, evaluator, serverConf, inappreferralClient, userIntelClient, operationStatusSvcClient, accountBalanceClient, data_collectorDataCollectorService, tierMovementBasedFeedbackFlowIntegrationSvc, v, rewardsClient, beSalaryClient, cardClient, pgServiceClient, recurringPaymentClient, dataCollectorService)
	if err != nil {
		return nil, err
	}
	return transactionService, nil
}

func InitializeContactsService() *contacts.Service {
	service := contacts.SyncContactsService()
	return service
}

func InitializeDeviceTokenService(deviceTokenClient types.FCMDeviceTokenClientWithInterceptors, commsClient types.CommsClientWithInterceptors, conf *config.Config) *fcm.Service {
	fcmDeviceTokenClient := types.FCMDeviceTokenClientProvider(deviceTokenClient)
	commsCommsClient := types.CommsClientProvider(commsClient)
	comms := CommsConfigProvider(conf)
	service := fcm.NewFCMDeviceTokenService(fcmDeviceTokenClient, commsCommsClient, comms)
	return service
}

func InitializeReminderService(beReminderClient reminder.ReminderServiceClient) *reminders.Service {
	service := reminders.NewRemindersService(beReminderClient)
	return service
}

func InitializeSearchService(searchClient search.ActionBarClient, client timeline.TimelineServiceClient, actorClient actor.ActorClient, cpClient provisioning.CardProvisioningClient, insightsAccessInfoClient accessinfo.AccessInfoClient, accountPiClient account_pi.AccountPIRelationClient, accountClient connected_account2.ConnectedAccountClient, broker events.Broker, genConfig *genconf.Config, usersClient user.UsersClient, groupClient group.GroupClient, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, beConnectedAccClient connected_account.ConnectedAccountClient, savingsClient savings.SavingsClient, onboardingClient onboarding.OnboardingClient) *search2.Service {
	deepLinkBuilder := deeplinkv2.NewDeepLinkBuilder()
	abFeatureReleaseConfig := provider.ABFeatureReleaseConfigProvider(genConfig)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConfig)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, groupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	connectFiToFiSvc := fi_to_fi.NewConnectFiToFiSvc(beConnectedAccClient, savingsClient, genConfig, evaluator, onboardingClient)
	service := search2.NewSearchService(searchClient, genConfig, deepLinkBuilder, client, actorClient, cpClient, insightsAccessInfoClient, accountPiClient, accountClient, broker, abFeatureReleaseConfig, usersClient, groupClient, preApprovedLoanClient, connectFiToFiSvc, onboardingClient)
	return service
}

func InitializeCardService(cardClient provisioning.CardProvisioningClient, cardCtrlClient control.CardControlClient, upiClient upi.UPIClient, conf *config.Config, userClient user.UsersClient, actorClient actor.ActorClient, userGroupClient group.GroupClient, onbClient onboarding.OnboardingClient, livenessclient liveness.LivenessClient, savingsClient savings.SavingsClient, kycClient kyc.KycClient, vkycClient vkyc.VKYCClient, dynamicConf *genconf.Config, beTieringClient tiering.TieringClient, salaryProgramClient salaryprogram.SalaryProgramClient, bcClient bankcust.BankCustomerServiceClient, offersListingClient casper.OfferListingServiceClient, orderActorActivityClient actoractivity.ActorActivityClient, rewardsGeneratorClient rewards.RewardsGeneratorClient, rewardsAggregatesClient pinot2.RewardsAggregatesClient, segmentationClient segment.SegmentationServiceClient, authClient auth.AuthClient, actorActivityFeClient actoractivity2.ActorActivityClient, accountBalanceClient balance.BalanceClient, inAppTargetedCommsClient inapptargetedcomms.InAppTargetedCommsClient, eventBroker events.Broker, panClient pan.PanClient, cardCiClient currencyinsights.CurrencyInsightsClient, payClient pay.PayClient, questCacheStorage types3.QuestCacheStorage, questManagerClient manager2.ManagerClient, networthClient networth.NetWorthClient) (*card2.Service, error) {
	flags := provider.FeatureFlagProvider(dynamicConf)
	genconfCard := provider.CardProvider(dynamicConf)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynamicConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	cardSectionBuilder := dashboard_sections.NewCardSection(cardClient, genconfCard, upiClient, orderActorActivityClient, evaluator)
	physicalCardOrderSection := dashboard_sections.NewPhysicalCardOrderSection(genconfCard, cardClient, segmentationClient)
	offersAndPromosSectionBuilder := dashboard_sections.NewOffersAndPromosSectionBuilder(genconfCard, offersListingClient, segmentationClient, inAppTargetedCommsClient, cardClient, evaluator)
	rewardsAndTxnsBuilder := dashboard_sections.NewRewardsAndTxnsBuilder(rewardsGeneratorClient, actorActivityFeClient, rewardsAggregatesClient)
	bottomSectionBuilder := dashboard_sections.NewBottomSectionBuilder(genconfCard)
	internationalSectionBuilder := dashboard_sections.NewInternationalSectionBuilder(genconfCard, cardClient, evaluator, beTieringClient)
	digitalCardActivationSection := dashboard_sections.NewDigitalCardActivationSection(upiClient)
	promotionalBannerLargeSectionBuilder := dashboard_sections.NewPromotionalBannerLargeSectionBuilder(inAppTargetedCommsClient, cardClient, evaluator)
	shortcutsSectionBuilder := dashboard_sections.NewShortcutsSectionBuilder(inAppTargetedCommsClient, cardClient, evaluator, genconfCard, actorClient, userClient, userGroupClient)
	uiSectionBuilderFactory := dashboard_sections.NewUiSectionBuilderFactory(cardSectionBuilder, physicalCardOrderSection, offersAndPromosSectionBuilder, rewardsAndTxnsBuilder, bottomSectionBuilder, internationalSectionBuilder, digitalCardActivationSection, promotionalBannerLargeSectionBuilder, shortcutsSectionBuilder)
	client := InitializeQuestSdkClient(questCacheStorage, dynamicConf, segmentationClient, actorClient, userClient, userGroupClient, questManagerClient, eventBroker)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(userClient)
	service, err := card2.NewCardService(cardClient, cardCtrlClient, upiClient, conf, userGroupClient, actorClient, userClient, onbClient, livenessclient, savingsClient, kycClient, vkycClient, flags, beTieringClient, salaryProgramClient, genconfCard, bcClient, uiSectionBuilderFactory, evaluator, offersListingClient, orderActorActivityClient, rewardsGeneratorClient, segmentationClient, authClient, accountBalanceClient, eventBroker, panClient, cardCiClient, payClient, dynamicConf, client, userAttributesFetcherImpl, networthClient)
	if err != nil {
		return nil, err
	}
	return service, nil
}

func InitializeTimelineService(conf *config.Config, piClient paymentinstrument.PiClient, actorClient actor.ActorClient, timelineClient timeline.TimelineServiceClient, orderClient order.OrderServiceClient, upiClient upi.UPIClient, userClient user.UsersClient, savingsClient savings.SavingsClient, merchantClient merchant.MerchantServiceClient, userGroupClient group.GroupClient, dynConf *genconf.Config, ffAccClient accounting.AccountingClient, upiOnboardingClient onboarding2.UpiOnboardingClient, searchClient search.ActionBarClient, ovgPaymentClient payment2.PaymentClient, userOnboardingClient onboarding.OnboardingClient, eventsBroker events.Broker) *timeline2.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := timeline2.NewService(conf, piClient, actorClient, timelineClient, orderClient, upiClient, userClient, savingsClient, merchantClient, userGroupClient, dynConf, ffAccClient, upiOnboardingClient, searchClient, evaluator, userOnboardingClient, ovgPaymentClient, eventsBroker)
	return service
}

func InitializeUserService(config2 *config.Config, onboardingClient onboarding.OnboardingClient, userClient user.UsersClient, savingsClient savings.SavingsClient, depositsClient deposit.DepositClient, commsClient types.CommsClientWithInterceptors, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, actorClient actor.ActorClient, upiClient upi.UPIClient, userContactClient contact.ContactClient, cardClient provisioning.CardProvisioningClient, broker events.Broker, kycClient kyc.KycClient, userGroupClient group.GroupClient, salaryProgramClient salaryprogram.SalaryProgramClient, vmClient vendormapping.VendorMappingServiceClient, client auth.AuthClient, connectedAccountClient connected_account.ConnectedAccountClient, employmentClient employment.EmploymentClient, extAcctClient extacct.ExternalAccountsClient, upiOnboardingClient onboarding2.UpiOnboardingClient, genConfig *genconf.Config, ffAccountingClient accounting.AccountingClient, ffClient firefly.FireflyClient, beTieringClient tiering.TieringClient, vKycFeClient vkyc.VKYCFeClient, feEmpClient employment.EmploymentFeClient, bcClient bankcust.BankCustomerServiceClient, wealthOnbClient wealthonboarding.WealthOnboardingClient, feConnectedAccClient connected_account2.ConnectedAccountClient, complianceClient compliance.ComplianceClient, panClient pan.PanClient, consentClient consent.ConsentClient, managerClient creditreportv2.CreditReportManagerClient, userLocationClient location2.LocationClient, storage types3.QuestCacheStorage, questManagerClient manager2.ManagerClient, segmentationClient segment.SegmentationServiceClient, docExtractionClient docs.DocExtractionClient, productClient product.ProductClient, savingsVgClient savings3.SavingsClient, celestialClient celestial.CelestialClient, accountBalanceClient balance.BalanceClient, alfredClient alfred.AlfredClient, rewardsClient rewards.RewardsGeneratorClient, projectionClient projector.ProjectorServiceClient, txnAggregateClient txnaggregates.TxnAggregatesClient, rewardsAggregatesClient pinot2.RewardsAggregatesClient, orderClient order.OrderServiceClient, accountManagerClient account.AccountManagerClient, healthInsuranceClient healthinsurance.HealthInsuranceClient, rewardOffersClient rewardoffers.RewardOffersClient, networthClient networth.NetWorthClient) (*user3.Service, error) {
	commsCommsClient := types.CommsClientProvider(commsClient)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConfig)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	v, err := genconf.GetDeviceIdsForSafetynetV2Flow(genConfig)
	if err != nil {
		return nil, err
	}
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(userClient)
	questsdkClient := InitializeQuestSdkClient(storage, genConfig, segmentationClient, actorClient, userClient, userGroupClient, questManagerClient, broker)
	feProfileDataCollector := user3.NewFeProfileDataCollector(savingsClient)
	dataCollectorService := data_collector.NewDataCollectorService(genConfig, savingsClient, actorClient, bcClient, salaryProgramClient, accountBalanceClient, beTieringClient, cardClient, alfredClient, rewardsClient, rewardsAggregatesClient, projectionClient, txnAggregateClient, orderClient, accountManagerClient, employmentClient, healthInsuranceClient, rewardOffersClient, userClient, userGroupClient)
	panProcessor := pkg.NewUnverifiedPanProcessor(userClient, managerClient, connectedAccountClient)
	service := user3.NewUserService(config2, onboardingClient, userClient, savingsClient, depositsClient, commsCommsClient, piClient, accountPiClient, actorClient, upiClient, userContactClient, cardClient, broker, kycClient, userGroupClient, salaryProgramClient, vmClient, client, connectedAccountClient, evaluator, employmentClient, extAcctClient, upiOnboardingClient, v, genConfig, ffAccountingClient, ffClient, beTieringClient, vKycFeClient, feEmpClient, bcClient, wealthOnbClient, feConnectedAccClient, complianceClient, panClient, consentClient, managerClient, userLocationClient, userAttributesFetcherImpl, questsdkClient, docExtractionClient, feProfileDataCollector, productClient, savingsVgClient, celestialClient, dataCollectorService, panProcessor, networthClient)
	return service, nil
}

func InitializeSimulationService(orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, piClient paymentinstrument.PiClient, upiConsumer upi.ConsumerClient, upiSimulationClient simulation.SimulationClient) *simulator.Service {
	service := simulator.NewService(orderClient, savingsClient, piClient, upiConsumer, upiSimulationClient)
	return service
}

func InitializeCustomerAuthCallBackService(cxClient customer_auth.CustomerAuthCallbackClient) *customer_auth2.Service {
	service := customer_auth2.NewService(cxClient)
	return service
}

func InitializeFaqService(Faqclient serving.ServeFAQClient, beRaClient recent_activity.RecentActivityClient, genconf2 *genconf.Config, eventBroker events.Broker) *faq.Service {
	service := faq.NewService(Faqclient, beRaClient, genconf2, eventBroker)
	return service
}

func InitializeContactUsService(issueReportingClient issue_reporting.ServiceClient, genconf2 *genconf.Config, ticketClient ticket.TicketClient, eventBroker events.Broker, userClient user.UsersClient, issueConfigManagementClient issue_config.IssueConfigManagementClient, userGroupClient group.GroupClient, actorClient actor.ActorClient, customerAuthClient customer_auth.CustomerAuthenticationClient, faqClient faq2.FAQClient, feTicketClient ticket2.TicketClient, riskProfileClient profile.ProfileClient) *contact_us.Service {
	cx := cxConfigProvider(genconf2)
	inAppContactUsFlowConfig := contactUsConfigProvider(genconf2)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genconf2)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := contact_us.NewService(issueReportingClient, ticketClient, cx, eventBroker, userClient, inAppContactUsFlowConfig, issueConfigManagementClient, evaluator, customerAuthClient, faqClient, feTicketClient, riskProfileClient, actorClient, userGroupClient)
	return service
}

func InitializeHomeService(conf *config.Config, genconf2 *genconf.Config, savClient savings.SavingsClient, searchClient search.ActionBarClient, orderClient order.OrderServiceClient, actorClient actor.ActorClient, timelineClient timeline.TimelineServiceClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, depositClient deposit.DepositClient, userGroupClient group.GroupClient, userClient user.UsersClient, txnAggClient txnaggregates.TxnAggregatesClient, rmsRuleManagerClient manager3.RuleManagerClient, mfCatalogManagerClient catalog.CatalogManagerClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, onboardingClient onboarding.OnboardingClient, nudgesClient nudge.NudgeServiceClient, kycClient kyc.KycClient, salaryProgramClient salaryprogram.SalaryProgramClient, ffAccountingClient accounting.AccountingClient, vkycFeClient vkyc.VKYCFeClient, beTieringCient tiering.TieringClient, bcClient bankcust.BankCustomerServiceClient, storyClient story.StoryClient, employmentClient employment.EmploymentClient, commsClient types.CommsClientWithInterceptors, beConnectedAccountClient connected_account.ConnectedAccountClient, segmentationClient segment.SegmentationServiceClient, ffBeClient firefly.FireflyClient, accountBalanceClient balance.BalanceClient, homeRedisStore types2.HomeRedisStore, upcomingTxnsClient upcomingtransactions.UpcomingTransactionsClient, merchantClient merchant.MerchantServiceClient, questManagerClient manager2.ManagerClient, authClient auth.AuthClient, vendorMappingClient vendormapping.VendorMappingServiceClient, storage types3.QuestCacheStorage, eventsBroker events.Broker, palBeClient preapprovedloan.PreApprovedLoanClient, consentClient consent.ConsentClient, cardProvisioningClient provisioning.CardProvisioningClient, offerCatalogServiceClient casper.OfferCatalogServiceClient, upiOnboardingClient onboarding2.UpiOnboardingClient, productClient product.ProductClient, alfredClient alfred.AlfredClient, rewardAggregateClient pinot2.RewardsAggregatesClient, rewardProjectionClient projector.ProjectorServiceClient, rewardsGeneratorClient rewards.RewardsGeneratorClient, accountManagerClient account.AccountManagerClient, crossAttachClient crossattach.CrossAttachClient, healthInsuranceClient healthinsurance.HealthInsuranceClient, rewardOffersClient rewardoffers.RewardOffersClient, networthClient networth.NetWorthClient, lendabilityClient lendability.LendabilityClient) *home.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genconf2)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	recentActivitiesCollector := home.NewRecentActivitiesDataCollector(orderClient, actorClient, timelineClient, piClient, depositClient, conf, evaluator, userClient, userGroupClient, ffAccountingClient, genconf2)
	feManagerService := release2.NewFeReleaseManagerService(genconf2)
	client := types2.HomeRedisClientProvider(homeRedisStore)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	commsCommsClient := types.CommsClientProvider(commsClient)
	ccDashboardWarningSvc := CcDashboardWarningSvcProvider(ffAccountingClient, ffBeClient)
	defaultTime := datetime.NewDefaultTime()
	rpcHelperImpl := helper.NewRpcHelperImpl(actorClient, savClient, userClient, palBeClient, mfCatalogManagerClient, defaultTime, consentClient, accountBalanceClient, userGroupClient)
	plDashboardWarningSvc := PlDashboardWarningSvcProvider(palBeClient, genconf2, rpcHelperImpl)
	plNavigationBarHighlightSvc := PlNavigationBarHighlightSvcProvider(palBeClient, genconf2, rpcHelperImpl, segmentationClient, onboardingClient, savClient)
	ussNavigationBarHighlightSvc := USSNavigationBarHighlightSvcProvider(genconf2)
	rewardsNavigationBarHighlightSvc := RewardsNavigationBarHighlightSvcProvider(genconf2, rewardsGeneratorClient)
	upcomingTransactionsHelperImpl := helper2.NewUpcomingTransactionsHelperImpl(actorClient, merchantClient, mfCatalogManagerClient, depositClient)
	questsdkClient := InitializeQuestSdkClient(storage, genconf2, segmentationClient, actorClient, userClient, userGroupClient, questManagerClient, eventsBroker)
	segmentExprAttributeEvaluator := attributes.NewSegmentExprAttributeEvaluator(segmentationClient)
	featureLifecycleAttributeEvaluator := attributes.NewFeatureLifecycleAttributeEvaluator(onboardingClient)
	userGroupAttributeEvaluator := attributes.NewUserGroupAttributeEvaluator(userClient, userGroupClient)
	crossAttachAttributeEvaluator := attributes.NewCrossAttachAttributeEvaluator(crossAttachClient)
	attributeEvaluatorFactory := attributes.NewAttributeEvaluatorFactory(segmentExprAttributeEvaluator, featureLifecycleAttributeEvaluator, userGroupAttributeEvaluator, crossAttachAttributeEvaluator)
	priorityOrderSortingStrategy := sort.NewPrioritySortingStrategy()
	sortingStrategyFactory := sort.NewSortingStrategyFactory(priorityOrderSortingStrategy)
	engine := layoutconfiguration.NewEngine(attributeEvaluatorFactory, sortingStrategyFactory, eventsBroker)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(userClient)
	dataCollectorService := data_collector.NewDataCollectorService(genconf2, savClient, actorClient, bcClient, salaryProgramClient, accountBalanceClient, beTieringCient, cardProvisioningClient, alfredClient, rewardsGeneratorClient, rewardAggregateClient, rewardProjectionClient, txnAggClient, orderClient, accountManagerClient, employmentClient, healthInsuranceClient, rewardOffersClient, userClient, userGroupClient)
	service := home.NewService(conf, genconf2, savClient, searchClient, orderClient, actorClient, timelineClient, piClient, accountPiClient, depositClient, userGroupClient, userClient, txnAggClient, rmsRuleManagerClient, mfCatalogManagerClient, recurringPaymentClient, onboardingClient, nudgesClient, recentActivitiesCollector, evaluator, kycClient, salaryProgramClient, vkycFeClient, beTieringCient, evaluator, bcClient, feManagerService, storyClient, employmentClient, redisCacheStorage, commsCommsClient, beConnectedAccountClient, segmentationClient, ffBeClient, ccDashboardWarningSvc, plDashboardWarningSvc, plNavigationBarHighlightSvc, ussNavigationBarHighlightSvc, rewardsNavigationBarHighlightSvc, accountBalanceClient, upcomingTxnsClient, upcomingTransactionsHelperImpl, defaultTime, authClient, questsdkClient, vendorMappingClient, cardProvisioningClient, offerCatalogServiceClient, upiOnboardingClient, engine, ffAccountingClient, userAttributesFetcherImpl, productClient, dataCollectorService, crossAttachClient, networthClient, rewardProjectionClient, rewardsGeneratorClient, lendabilityClient)
	return service
}

func InitializeHomeOrchestratorService(homeFeClient home2.HomeClient, fcmFeClient fcm2.FCMClient, investmentFeClient aggregator.InvestmentAggregatorClient, fireflyFeClient firefly2.FireflyClient, palFeClient preapprovedloan2.PreApprovedLoanClient, networthFeClient networth2.NetWorthClient, caFeClient connected_account2.ConnectedAccountClient, analyserFeClient analyser.AnalyserServiceClient, searchFeClient search3.SearchClient, referralFeClient referral.ReferralClient, deFeClient dynamic_elements.DynamicElementsClient, nudgeFeClient nudge2.NudgeServiceClient, rewardsFeClient rewards2.RewardsClient, cxHomeFeClient home3.HomeClient, secretsFeClient secrets.SecretsClient, journeyFeClient nudge2.JourneyServiceClient, cardFePb card3.CardClient) *orchestrator.Service {
	topNavigationBarComponent := stickytop.NewTopNavigationBarComponent(homeFeClient)
	maintenanceComponent := stickytop.NewMaintenanceComponent(deFeClient)
	componentGeneratorFactory := stickytop.NewComponentGeneratorFactory(topNavigationBarComponent, maintenanceComponent)
	bottomNavigationBarComponent := stickybottom.NewBottomNavigationBarComponent()
	stickybottomComponentGeneratorFactory := stickybottom.NewComponentGeneratorFactory(bottomNavigationBarComponent)
	criticalNotificationComponent := scrollable.NewCriticalNotificationComponent(fcmFeClient)
	fiNroAccountDashboardComponentViewGetter := dashboard.NewFiNroAccountDashboardComponentViewGetter(homeFeClient)
	fiNreAccountDashboardComponentViewGetter := dashboard.NewFiNreAccountDashboardComponentViewGetter(homeFeClient)
	fiAccountDashboardComponentViewGetter := dashboard.NewFiAccountDashboardComponentViewGetter(homeFeClient)
	investmentSummaryDashboardComponentViewGetter := dashboard.NewInvestmentSummaryDashboardComponentViewGetter(investmentFeClient)
	loansDashboardComponentViewGetter := dashboard.NewLoansDashboardComponentViewGetter(palFeClient)
	creditCardDashboardComponentViewGetter := dashboard.NewCreditCardDashboardComponentViewGetter(fireflyFeClient)
	networthDashboardComponentViewGetter := dashboard.NewNetworthDashboardComponentViewGetter(networthFeClient)
	connectedAccountsDashboardComponentViewGetter := dashboard.NewConnectedAccountsDashboardComponentViewGetter(caFeClient)
	introDashboardComponentViewGetter := dashboard.NewIntroDashboardComponentViewGetter()
	creditScoreDashboardComponentViewGetter := dashboard.NewCreditScoreDashboardComponentViewGetter(networthFeClient)
	epfDashboardComponentViewGetter := dashboard.NewEpfDashboardComponentViewGetter(networthFeClient)
	mfDashboardComponentViewGetter := dashboard.NewMfDashboardComponentViewGetter(networthFeClient)
	dashboardComponentViewGetterFactory := dashboard.NewDashboardComponentViewGetterFactory(fiNroAccountDashboardComponentViewGetter, fiNreAccountDashboardComponentViewGetter, fiAccountDashboardComponentViewGetter, investmentSummaryDashboardComponentViewGetter, loansDashboardComponentViewGetter, creditCardDashboardComponentViewGetter, networthDashboardComponentViewGetter, connectedAccountsDashboardComponentViewGetter, introDashboardComponentViewGetter, creditScoreDashboardComponentViewGetter, epfDashboardComponentViewGetter, mfDashboardComponentViewGetter)
	dashboardComponent := scrollable.NewDashboardComponent(dashboardComponentViewGetterFactory)
	analyserComponent := scrollable.NewAnalyserComponent(analyserFeClient)
	cardOffersComponent := scrollable.NewCardOffersComponent(rewardsFeClient)
	catalogOffersComponent := scrollable.NewCatalogOffersComponent(rewardsFeClient)
	helpComponent := scrollable.NewHelpComponent(cxHomeFeClient)
	investmentComponent := scrollable.NewInvestmentComponent(investmentFeClient)
	nudgesComponent := scrollable.NewNudgesComponent(nudgeFeClient)
	primaryFeatureComponent := scrollable.NewPrimaryFeatureComponent(deFeClient)
	secondaryFeatureComponent := scrollable.NewSecondaryFeatureComponent(deFeClient)
	promotionalBannerComponent := scrollable.NewPromotionalBannerComponent(nudgeFeClient)
	promotionalBanner2Component := scrollable.NewPromotionalBanner2Component(deFeClient)
	recentAndUpcomingComponent := scrollable.NewRecentAndUpcomingComponent(homeFeClient)
	referralComponent := scrollable.NewReferralComponent(referralFeClient)
	searchComponent := scrollable.NewSearchComponent(searchFeClient)
	shortcutsComponent := scrollable.NewShortcutsComponent(homeFeClient)
	tabbedCardComponent := scrollable.NewTabbedCardComponent(deFeClient)
	trustMarkerComponent := scrollable.NewTrustMarkerComponent(homeFeClient)
	moneySecretsComponent := scrollable.NewMoneySecretsComponent(secretsFeClient)
	upiWidgetComponent := scrollable.NewUpiWidgetComponent(homeFeClient)
	journeysComponent := scrollable.NewJourneysComponent(journeyFeClient)
	wealthAnalyserComponent := scrollable.NewWealthAnalyserComponent(secretsFeClient)
	dcInternationWidgetComponent := scrollable.NewDcInternationWidgetComponent(cardFePb)
	activationWidgetComponent := scrollable.NewActivationWidgetComponent(journeyFeClient)
	wealthBuilderLandingComponent := scrollable.NewWealthBuilderLandingComponent(networthFeClient)
	scrollableComponentGeneratorFactory := scrollable.NewComponentGeneratorFactory(criticalNotificationComponent, dashboardComponent, analyserComponent, cardOffersComponent, catalogOffersComponent, helpComponent, investmentComponent, nudgesComponent, primaryFeatureComponent, secondaryFeatureComponent, promotionalBannerComponent, promotionalBanner2Component, recentAndUpcomingComponent, referralComponent, searchComponent, shortcutsComponent, tabbedCardComponent, trustMarkerComponent, moneySecretsComponent, upiWidgetComponent, journeysComponent, wealthAnalyserComponent, dcInternationWidgetComponent, activationWidgetComponent, wealthBuilderLandingComponent)
	service := orchestrator.NewService(homeFeClient, componentGeneratorFactory, stickybottomComponentGeneratorFactory, scrollableComponentGeneratorFactory)
	return service
}

func InitializeQuestSdkClient(questCacheStorage types3.QuestCacheStorage, genconf2 *genconf.Config, segmentClient segment.SegmentationServiceClient, actorClient actor.ActorClient, usersClient user.UsersClient, userGroupClient group.GroupClient, questManagerCl manager2.ManagerClient, eventsBroker events.Broker) *questsdk.Client {
	genconfConfig := QuestSDKClientConfProvider(genconf2)
	cacheStorage := QuestCacheStorageProvider(questCacheStorage)
	client := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGroupClient, usersClient, actorClient, segmentClient, cacheStorage, eventsBroker)
	return client
}

func InitializeDepositService(depositClient deposit.DepositClient, orderClient order.OrderServiceClient, piClient paymentinstrument.PiClient, savingsClient savings.SavingsClient, usersClient user.UsersClient, upiClient upi.UPIClient, accountPiClient account_pi.AccountPIRelationClient, userGroupClient group.GroupClient, actorClient actor.ActorClient, goalsClient goals.GoalsClient, ruleManagerClient manager3.RuleManagerClient, accountStatementClient statement.AccountStatementClient, heClient health_engine.HealthEngineServiceClient, conf *genconf.Config, fireflyAccountingClient accounting.AccountingClient, accountBalanceClient balance.BalanceClient) *deposit2.Service {
	abFeatureReleaseConfig := provider.ABFeatureReleaseConfigProvider(conf)
	defaultTime := datetime.NewDefaultTime()
	sdNavigator := navigators.NewSdNavigator()
	fdNavigator := navigators.NewFdNavigator()
	taxSaverFdNavigator := navigators.NewTaxSaverFdNavigator()
	sdFitCustomisationNavigator := navigators.NewSdFitCustomisationNavigator()
	navigationImpl := creation.NewNavigationImplWithNavigators(sdNavigator, fdNavigator, taxSaverFdNavigator, sdFitCustomisationNavigator)
	primaryImpl := primary.NewPrimaryImpl(defaultTime)
	autoSaveSuggestionsImpl := auto_save_suggestions.NewAutoSaveSuggestionsImpl(depositClient, defaultTime, conf)
	autoSaveSuggestionOptionsHandler := component_handlers.NewAutoSaveSuggestionOptionsHandler(autoSaveSuggestionsImpl)
	createDepositCtaHandler := component_handlers.NewCreateDepositCtaHandler()
	creationConsentHandler := component_handlers.NewCreationConsentHandler()
	maturityActionSelectorHandler := component_handlers.NewMaturityActionSelectorHandler()
	nextNavigatorHandler := component_handlers.NewNextNavigationHandler()
	textDisplayerHandler := component_handlers.NewTextDisplayerHandler()
	amountSliderSelectorHandler := component_handlers.NewAmountSliderSelectorHandler()
	depositNameSelectorHandler := component_handlers.NewDepositNameSelectorHandler()
	durationSliderSelectorHandler := component_handlers.NewDurationSliderSelectorHandler(defaultTime)
	goalDiscoveryHandler := component_handlers.NewGoalDiscoveryHandler()
	interestPayoutSelectorHandler := component_handlers.NewInterestPayoutSelectorHandler()
	nomineeSelectorHandler := component_handlers.NewNomineeSelectorHandler()
	secondaryImpl := creation.NewSecondaryImplWithHandlers(navigationImpl, autoSaveSuggestionOptionsHandler, createDepositCtaHandler, creationConsentHandler, maturityActionSelectorHandler, nextNavigatorHandler, textDisplayerHandler, amountSliderSelectorHandler, depositNameSelectorHandler, durationSliderSelectorHandler, goalDiscoveryHandler, interestPayoutSelectorHandler, nomineeSelectorHandler)
	creationDataFetcherFactoryImpl := data_fetcher.NewCreationDataFetcherFactory(depositClient, usersClient, goalsClient, savingsClient, actorClient, defaultTime, conf, accountBalanceClient, autoSaveSuggestionsImpl)
	creationImpl := creation.NewCreationImpl(navigationImpl, primaryImpl, secondaryImpl, creationDataFetcherFactoryImpl)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	fireFlyAccessor := accessor.NewFireFlyAccessor(fireflyAccountingClient)
	userAccessor := accessor.NewUserAccessor(usersClient, userGroupClient)
	service := deposit2.NewService(heClient, depositClient, orderClient, piClient, savingsClient, usersClient, upiClient, accountPiClient, userGroupClient, goalsClient, ruleManagerClient, conf, actorClient, accountStatementClient, abFeatureReleaseConfig, defaultTime, fireflyAccountingClient, creationImpl, evaluator, accountBalanceClient, fireFlyAccessor, userAccessor)
	return service
}

func InitializeChatService(cxChatClient chat.ChatsClient, conf *genconf.Config) *chat2.Service {
	service := chat2.NewService(cxChatClient, conf)
	return service
}

func InitializeCallService(conf *genconf.Config, cxCallRoutingClient call_routing.CallRoutingClient, customerAuthClient customer_auth.CustomerAuthenticationClient) *call.Service {
	service := call.NewService(cxCallRoutingClient, customerAuthClient, conf)
	return service
}

func InitializeActorActivitiesService(actorActivityClient actoractivity.ActorActivityClient, savingsClient savings.SavingsClient, cardProvisioningClient provisioning.CardProvisioningClient, piClient paymentinstrument.PiClient, conf *config.Config, depositClient deposit.DepositClient, connectedAccountClient connected_account.ConnectedAccountClient, categorizerClient categorizer.TxnCategorizerClient, actorClient actor.ActorClient, userGroupClient group.GroupClient, userClient user.UsersClient, accountPiRelationClient account_pi.AccountPIRelationClient, dynconf *genconf.Config, ffAccountingClient accounting.AccountingClient, timelineClient timeline.TimelineServiceClient, upiOnboardingClient onboarding2.UpiOnboardingClient, onboardingClient onboarding.OnboardingClient, orderClient order.OrderServiceClient, rewardsGeneratorClient rewards.RewardsGeneratorClient, rewardsAggregatesClient pinot2.RewardsAggregatesClient, feSearchClient search3.SearchClient) *actoractivity3.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynconf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	connectFiToFiSvc := fi_to_fi.NewConnectFiToFiSvc(connectedAccountClient, savingsClient, dynconf, evaluator, onboardingClient)
	service := actoractivity3.NewService(actorActivityClient, savingsClient, piClient, cardProvisioningClient, conf, depositClient, connectedAccountClient, categorizerClient, userGroupClient, userClient, actorClient, accountPiRelationClient, feSearchClient, dynconf, ffAccountingClient, timelineClient, upiOnboardingClient, orderClient, rewardsGeneratorClient, connectFiToFiSvc, rewardsAggregatesClient, evaluator)
	return service
}

func InitializeRecurrentPaymentsService(recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, actorClient actor.ActorClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, orderClient order.OrderServiceClient, savingClient savings.SavingsClient, payClient payment.PaymentClient, dynconf *genconf.Config, upiClient upi.UPIClient, mandateClient mandate.MandateServiceClient, upiOnbClient onboarding2.UpiOnboardingClient, ticketClient ticket.TicketClient, usersClient user.UsersClient, userGroupClient group.GroupClient, bankCustClient bankcust.BankCustomerServiceClient, palClient preapprovedloan.PreApprovedLoanClient) *recurringpayment2.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynconf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := recurringpayment2.NewService(recurringPaymentClient, actorClient, piClient, accountPiClient, orderClient, savingClient, payClient, dynconf, upiClient, mandateClient, upiOnbClient, ticketClient, usersClient, evaluator, bankCustClient, palClient)
	return service
}

func InitializeAuthService(authClient auth.AuthClient, biometricClient biometrics.BiometricsServiceClient, conf *genconf.Config, authOrchClient orchestrator2.OrchestratorClient, usersClient user.UsersClient, livClient liveness.LivenessClient, actorClient actor.ActorClient) *auth2.Service {
	service := auth2.NewAuthService(authClient, biometricClient, conf, authOrchClient, usersClient, livClient, actorClient)
	return service
}

func InitializeRewardsService(rewardsGeneratorClient rewards.RewardsGeneratorClient, rewardOffersClient rewardoffers.RewardOffersClient, offerRedemptionClient redemption.OfferRedemptionServiceClient, accrualClient accrual.AccrualClient, tieringClient tiering.TieringClient, offerListingClient casper.OfferListingServiceClient, offerCatalogClient casper.OfferCatalogServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, savingsClient savings.SavingsClient, luckyDrawClient luckydraw.LuckyDrawServiceClient, usersClient user.UsersClient, depositClient deposit.DepositClient, exchangerOfferClient exchanger.ExchangerOfferServiceClient, actorServiceClient actor.ActorClient, conf *config.Config, dyconf *genconf.Config, offerInventoryServiceClient casper.OfferInventoryServiceClient, bcClient bankcust.BankCustomerServiceClient, fireflyClient firefly.FireflyClient, vendorMappingClient vendormapping.VendorMappingServiceClient, cardClient provisioning.CardProvisioningClient, userGroupClient group.GroupClient, externalVendorRedemptionClient external_vendor_redemption.ExternalVendorRedemptionServiceClient, segmentationServiceClient segment.SegmentationServiceClient, onboardingClient onboarding.OnboardingClient, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, questCacheStorage types3.QuestCacheStorage, questManagerClient manager2.ManagerClient, eventBroker events.Broker, networthClient networth.NetWorthClient) *rewards3.RewardService {
	rewardsFrontendMeta := RewardsFrontendMetaProvider(conf)
	creditCardCriteriaEvaluator := offerdisplay.NewCreditCardCriteriaEvaluator(fireflyClient)
	salaryCriteriaEvaluator := offerdisplay.NewSalaryCriteriaEvaluator(salaryProgramClient)
	catalogNewUserCriteriaEvaluator := offerdisplay.NewCatalogNewUserCriteriaEvaluator(exchangerOfferClient, offerRedemptionClient)
	savingsAccountCriteriaEvaluator := offerdisplay.NewSavingsAccountCriteriaEvaluator(savingsClient)
	criteriaEvaluatorFactory := offerdisplay.NewCriteriaEvaluatorFactory(creditCardCriteriaEvaluator, salaryCriteriaEvaluator, catalogNewUserCriteriaEvaluator, savingsAccountCriteriaEvaluator)
	engine := offerdisplay.NewEngine(criteriaEvaluatorFactory)
	iManager := tags.NewManager(dyconf)
	mappingsManager := offerwidget.NewMappingsManager(iManager, dyconf)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dyconf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorServiceClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	client := InitializeQuestSdkClient(questCacheStorage, dyconf, segmentationServiceClient, actorServiceClient, usersClient, userGroupClient, questManagerClient, eventBroker)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(usersClient)
	homeOfferWidgetGenerator := offerwidget.NewHomeOfferWidgetGenerator(mappingsManager, evaluator, onboardingClient, client, userAttributesFetcherImpl, networthClient)
	homeCatalogOffersWidgetGenerator := offerwidget.NewHomeCatalogOffersWidgetGenerator(mappingsManager, evaluator, onboardingClient, client, userAttributesFetcherImpl, networthClient)
	homeCardOffersWidgetGenerator := offerwidget.NewHomeCardOffersWidgetGenerator(mappingsManager, fireflyClient, cardClient, evaluator, segmentationServiceClient, onboardingClient, client, userAttributesFetcherImpl, networthClient)
	rewardDetailsOfferWidgetGenerator := offerwidget.NewRewardDetailsOfferWidgetGenerator(mappingsManager, evaluator, onboardingClient, client, userAttributesFetcherImpl, networthClient)
	ccDashboardOfferWidgetGenerator := offerwidget.NewCCDashboardOfferWidgetGenerator(mappingsManager)
	ccMerchantRewardsOfferWidgetGenerator := offerwidget.NewCCMerchantRewardsOfferWidgetGenerator(mappingsManager)
	myRewardsOffersWidgetGenerator := offerwidget.NewMyRewardsOffersWidgetGenerator(mappingsManager, evaluator, onboardingClient, client, userAttributesFetcherImpl, networthClient)
	salaryBenefitsOfferWidgetGenerator := offerwidget.NewSalaryBenefitsOfferWidgetGenerator(mappingsManager)
	offerWidgetGeneratorFactory := offerwidget.NewOfferWidgetGeneratorFactory(homeOfferWidgetGenerator, homeCatalogOffersWidgetGenerator, homeCardOffersWidgetGenerator, rewardDetailsOfferWidgetGenerator, ccDashboardOfferWidgetGenerator, ccMerchantRewardsOfferWidgetGenerator, myRewardsOffersWidgetGenerator, salaryBenefitsOfferWidgetGenerator)
	rewardsCacheStorage := NewInMemoryCacheStorage()
	rewardService := rewards3.NewRewardsService(rewardsFrontendMeta, rewardsGeneratorClient, rewardOffersClient, accrualClient, tieringClient, offerListingClient, offerCatalogClient, offerInventoryServiceClient, salaryProgramClient, offerRedemptionClient, luckyDrawClient, usersClient, depositClient, exchangerOfferClient, actorServiceClient, engine, dyconf, bcClient, iManager, offerWidgetGeneratorFactory, rewardsCacheStorage, fireflyClient, savingsClient, vendorMappingClient, cardClient, userGroupClient, externalVendorRedemptionClient, onboardingClient, segmentationServiceClient, preApprovedLoanClient, evaluator, client, userAttributesFetcherImpl, networthClient)
	return rewardService
}

func InitializeSalaryProgramService(salaryProgramClient salaryprogram.SalaryProgramClient, salaryReferralsClient referrals.ReferralsClient, rewardGeneratorClient rewards.RewardsGeneratorClient, healthInsuranceClient healthinsurance.HealthInsuranceClient, rewardOffersClient rewardoffers.RewardOffersClient, employmentClient employment.EmploymentClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, usersClient user.UsersClient, userGroupClient group.GroupClient, accountBalanceClient balance.BalanceClient, alfredClient alfred.AlfredClient, provisioningClient provisioning.CardProvisioningClient, projectionClient projector.ProjectorServiceClient, txnAggregateClient txnaggregates.TxnAggregatesClient, conf *config.Config, epifiIconsBucketS3Client types2.EpifiIconsS3Client, salaryProgramBucketS3Client types2.SalaryProgramS3Client, dconf *genconf.Config, actorActivityClient actoractivity.ActorActivityClient, inAppReferralClient inappreferral.InAppReferralClient, bankCustClient bankcust.BankCustomerServiceClient, vgEmploymentClient employment2.EmploymentClient, vgEmployerNameMatchClient employernamematch.EmployerNameMatchClient, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, enachClient enach.EnachServiceClient, orderServiceClient order.OrderServiceClient, txnCategorizerClient categorizer.TxnCategorizerClient, segmentClient segment.SegmentationServiceClient, upiOnbClient onboarding2.UpiOnboardingClient, accountPiRelationClient account_pi.AccountPIRelationClient, caClient connected_account.ConnectedAccountClient, tieringClient tiering.TieringClient, rewardAggregateClient pinot2.RewardsAggregatesClient, consentClient consent.ConsentClient, accountManagerClient account.AccountManagerClient) *salaryprogram2.Service {
	s3Client := types2.EpifiIconsS3ClientProvider(epifiIconsBucketS3Client)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dconf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := benefits2.NewService(rewardOffersClient, evaluator, dconf)
	nextActionManager := aa_salary.NewNextActionManager(dconf, salaryProgramClient)
	screenBuilder := aa_salary.NewScreenBuilder(dconf, salaryProgramClient, savingsClient, accountPiRelationClient, upiOnbClient, caClient, conf, evaluator)
	dataCollectorService := data_collector.NewDataCollectorService(dconf, savingsClient, actorClient, bankCustClient, salaryProgramClient, accountBalanceClient, tieringClient, provisioningClient, alfredClient, rewardGeneratorClient, rewardAggregateClient, projectionClient, txnAggregateClient, orderServiceClient, accountManagerClient, employmentClient, healthInsuranceClient, rewardOffersClient, usersClient, userGroupClient)
	salaryprogramService := salaryprogram2.NewService(salaryProgramClient, healthInsuranceClient, salaryReferralsClient, employmentClient, rewardGeneratorClient, rewardOffersClient, actorClient, savingsClient, usersClient, conf, s3Client, salaryProgramBucketS3Client, dconf, actorActivityClient, inAppReferralClient, evaluator, bankCustClient, service, vgEmploymentClient, vgEmployerNameMatchClient, preApprovedLoanClient, recurringPaymentClient, enachClient, orderServiceClient, txnCategorizerClient, segmentClient, userGroupClient, nextActionManager, screenBuilder, tieringClient, dataCollectorService)
	return salaryprogramService
}

func InitializePartnerSDKService(partnerSDKClient partnersdk.PartnerSDKClient) *partnersdk2.Service {
	service := partnersdk2.NewService(partnerSDKClient)
	return service
}

func InitializeLivenessService(livenessClient liveness.LivenessClient) *liveness2.Service {
	service := liveness2.NewService(livenessClient)
	return service
}

func InitializeAccessInfoService(accessInfoClient accessinfo.AccessInfoClient, authClient auth.AuthClient, actorClient actor.ActorClient, userClient user.UsersClient) *accessinfo2.Service {
	service := accessinfo2.NewService(accessInfoClient, authClient, actorClient, userClient)
	return service
}

func InitializeEmailParserService(emailParserClient emailparser.EmailParserClient) *emailparser2.Service {
	service := emailparser2.NewService(emailParserClient)
	return service
}

func InitializeDisputeService(disputeClient dispute.DisputeClient, paymentClient payment.PaymentClient, disputeConf *genconf.Config, orderClient order.OrderServiceClient) *dispute2.Service {
	genconfDispute := getDisputeConf(disputeConf)
	service := dispute2.NewService(disputeClient, paymentClient, genconfDispute, orderClient)
	return service
}

func InitializeAppLogService(appLogClient app_log.AppLogClient, authClient auth.AuthClient, genConf *genconf.Config) *app_logs.Service {
	service := app_logs.NewService(appLogClient, authClient, genConf)
	return service
}

func InitializeVkycService(vkycClient vkyc.VKYCClient, onbClient onboarding.OnboardingClient, userClient user.UsersClient, actorClient actor.ActorClient, userGroupClient group.GroupClient, genConf *genconf.Config, kycClient kyc.KycClient, savingsClient savings.SavingsClient, locationClient location2.LocationClient, conf *config.Config, bcClient bankcust.BankCustomerServiceClient, panClient pan.PanClient, accountBalanceClient balance.BalanceClient) *vkyc2.Service {
	flags := FlagsProvider(conf)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	defaultTime := datetime.NewDefaultTime()
	service := vkyc2.NewService(vkycClient, onbClient, flags, genConf, kycClient, savingsClient, locationClient, conf, bcClient, evaluator, panClient, accountBalanceClient, defaultTime, actorClient, userClient, userGroupClient)
	return service
}

func InitializeFitttService(conf *genconf.Config, rmsClient manager3.RuleManagerClient, depositClient deposit.DepositClient, fitttClient fittt.FitttClient, userClient user.UsersClient, actorClient actor.ActorClient, userGroupClient group.GroupClient, searchClient search.ActionBarClient, fundCatalogClient catalog.CatalogManagerClient, wealthOnboardingClient wealthonboarding2.WealthOnboardingClient, investPayClient payment_handler.PaymentHandlerClient, investOrderClient order2.OrderManagerClient, broker events.Broker, fitttSportsManagerClient sports.SportsManagerClient, rmUIClient ui.RuleUIManagerClient, rewardsClient rewards.RewardsGeneratorClient, luckyDrawClient luckydraw.LuckyDrawServiceClient, savingsClient savings.SavingsClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, timelineClient timeline3.TimelineServiceClient, userOnboardingClient onboarding.OnboardingClient, accountBalanceClient balance.BalanceClient, usStocksCatalogManagerClient catalog2.CatalogManagerClient, bankCustomerServiceClient bankcust.BankCustomerServiceClient, vgIftClient internationalfundtransfer.InternationalFundTransferClient, iftClient internationalfundtransfer2.InternationalFundTransferClient, payClient pay.PayClient) (*fittt2.Service, error) {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	standingInstructionFitttAggregator := fittt3.NewStandingInstructionFitttAggregator(rmsClient, investPayClient)
	slabRateBasedGSTCalculator := gst.NewSlabRateBasedGSTCalculator()
	apiBasedTCSCalculator := tcs.NewAPIBasedTCSCalculator(bankCustomerServiceClient, vgIftClient)
	slabRateBasedTCSCalculator := tcs.NewSlabRateBasedTCSCalculator()
	sipInvoiceCalculator := invoice.NewSIPInvoiceCalculator(slabRateBasedGSTCalculator, apiBasedTCSCalculator, slabRateBasedTCSCalculator)
	recurringOutwardRemittance := internationalfundtransfer3.NewRecurringOutwardRemittance(recurringPaymentClient)
	usStocks := fittt3.NewUSStocks(usStocksCatalogManagerClient)
	federalA2Form := a2form.NewFederalA2Form(userClient, bankCustomerServiceClient, savingsClient, payClient)
	sipParamsValidation := usstocks.NewSIPParamsValidation(usStocksCatalogManagerClient, recurringOutwardRemittance, rmsClient)
	service, err := fittt2.NewFitttService(rmsClient, depositClient, fitttClient, userClient, actorClient, userGroupClient, searchClient, fundCatalogClient, wealthOnboardingClient, investPayClient, investOrderClient, conf, broker, fitttSportsManagerClient, rmUIClient, featureReleaseConfig, evaluator, rewardsClient, luckyDrawClient, savingsClient, recurringPaymentClient, timelineClient, userOnboardingClient, accountBalanceClient, standingInstructionFitttAggregator, sipInvoiceCalculator, iftClient, recurringOutwardRemittance, usStocks, federalA2Form, sipParamsValidation)
	if err != nil {
		return nil, err
	}
	return service, nil
}

func InitializeClientLoggerService(authClient auth.AuthClient) *clientlogger.Service {
	service := clientlogger.NewService(authClient)
	return service
}

func InitializeReferralService(actorClient actor.ActorClient, onbClient onboarding.OnboardingClient, inAppReferralClient inappreferral.InAppReferralClient, seasonsClient season.SeasonServiceClient, rewardsClient rewards.RewardsGeneratorClient, userClient user.UsersClient, userGrpClient group.GroupClient, userContactClient contact.ContactClient, nudgeClient nudge.NudgeServiceClient, conf *config.Config, dyconf *genconf.Config, broker events.Broker) *referral2.Service {
	configReferrals := ReferralConfigProvider(conf)
	referralsColourMap := ReferralsColourMapProvider(conf)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dyconf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := referral2.NewService(actorClient, onbClient, inAppReferralClient, seasonsClient, rewardsClient, userClient, userContactClient, userGrpClient, configReferrals, dyconf, referralsColourMap, broker, evaluator, nudgeClient)
	return service
}

func InitializeInsightsService(conf *config.Config, beInsightsClient insights.InsightsClient) *insights2.Service {
	service := insights2.NewService(conf, beInsightsClient)
	return service
}

func InitializeScreeningService(employmnetClient employment.EmploymentClient, onbClient onboarding.OnboardingClient, actorClient actor.ActorClient, consentClient consent.ConsentClient, usersClient user.UsersClient, scrClient screener.ScreenerClient, broker events.Broker, genConf *genconf.Config, config2 *config.Config, creditReportV2Client creditreportv2.CreditReportManagerClient) *screening.Service {
	configScreening := ScreeningConfigProvider(config2)
	service := screening.NewService(employmnetClient, configScreening, onbClient, actorClient, consentClient, usersClient, scrClient, broker, genConf, config2, creditReportV2Client)
	return service
}

func InitializeConnectedAccService(beConnectedAccClient connected_account.ConnectedAccountClient, beUserClient user.UsersClient, beActorClient actor.ActorClient, beSavingsClient savings.SavingsClient, beAuthClient auth.AuthClient, beConsentClient consent.ConsentClient, userGroupClient group.GroupClient, onbClient onboarding.OnboardingClient, scrClient screener.ScreenerClient, conf *config.Config, dynconf *genconf.Config, celestialClient celestial.CelestialClient, accountManagerClient account.AccountManagerClient, segmentationClient segment.SegmentationServiceClient, depositClient deposit.DepositClient, netWorthClient networth.NetWorthClient, accountBalanceClient balance.BalanceClient, palClient preapprovedloan.PreApprovedLoanClient, eventBroker events.Broker, epfClient epf.EpfClient, investmentAnalyticsClient investment.InvestmentAnalyticsClient, creditReportClient creditreportv2.CreditReportManagerClient) *connected_account3.Service {
	connectedAccount := ConnectedAccountConfigProvider(conf)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynconf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(beActorClient, beUserClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	caFlowDefaultConnectedAccountProcessor := processor2.NewCaFlowDefaultConnectedAccountProcessor(beConnectedAccClient)
	caFlowScreenerProcessor := processor2.NewCaFlowScreenerProcessor(beConnectedAccClient, onbClient, scrClient)
	caFlowUsStocksBuyProcessor := processor2.NewCaFlowUsStocksBuyProcessor()
	caFlowUsStocksOnboardingProcessor := processor2.NewCaFlowUsStocksOnboardingProcessor(celestialClient, accountManagerClient)
	caFlowConnectedAccountRenewalProcessor := processor2.NewCaFlowConnectedAccountRenewalProcessor()
	caFlowConnectFiToFiProcessor := processor2.NewCaFlowConnectFiToFiProcessor()
	caFlowNetWorthProcessor := processor2.NewCAFlowNetWorthProcessor(beUserClient)
	deeplinkBuilder := deeplink_builder.NewDeeplinkBuilder(dynconf, evaluator)
	caFlowNetWorthIndStocksProcessor := processor2.NewCAFlowNetWorthIndStocksProcessor(beUserClient, deeplinkBuilder)
	caFlowLoanEligibilityProcessor := processor2.NewCAFlowLoanEligibilityProcessor(palClient, celestialClient)
	caFlowAaSalaryProcessor := processor2.NewCaFlowAaSalaryProcessor()
	caFlowNetWorthNpsProcessor := processor2.NewCAFlowNetWorthNpsProcessor(beUserClient, deeplinkBuilder)
	caUnifiedFlowProcessor := processor2.NewCAUnifiedFlowProcessor(beUserClient, netWorthClient, epfClient, investmentAnalyticsClient)
	caFlowSalaryEstimationProcessor := processor2.NewCaFlowSalaryEstimationProcessor(beConnectedAccClient)
	caFlowWealthBuilderOnboardingProcessor := processor2.NewCaFlowWealthBuilderOnboardingProcessor(beConnectedAccClient)
	caFlowDLFactorySvc := factory2.NewCaFlowDLFactorySvc(caFlowDefaultConnectedAccountProcessor, caFlowScreenerProcessor, caFlowUsStocksBuyProcessor, caFlowUsStocksOnboardingProcessor, caFlowConnectedAccountRenewalProcessor, caFlowConnectFiToFiProcessor, caFlowNetWorthProcessor, caFlowNetWorthIndStocksProcessor, caFlowLoanEligibilityProcessor, caFlowAaSalaryProcessor, caFlowNetWorthNpsProcessor, caUnifiedFlowProcessor, caFlowSalaryEstimationProcessor, caFlowWealthBuilderOnboardingProcessor)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(beUserClient)
	panProcessor := pkg.NewUnverifiedPanProcessor(beUserClient, creditReportClient, beConnectedAccClient)
	service := connected_account3.NewService(beConnectedAccClient, connectedAccount, beUserClient, beActorClient, beSavingsClient, beAuthClient, dynconf, beConsentClient, userGroupClient, evaluator, caFlowDLFactorySvc, segmentationClient, onbClient, depositClient, netWorthClient, accountBalanceClient, userAttributesFetcherImpl, eventBroker, panProcessor)
	return service
}

func InitializeClientMetricsService(rmsClient manager3.RuleManagerClient, fitttClient fittt.FitttClient, rmsUIClient ui.RuleUIManagerClient, conf *config.Config) *client_metrics.ClientMetricsService {
	clientMetricsService := client_metrics.NewClientMetricsService(rmsClient, fitttClient, conf, rmsUIClient)
	return clientMetricsService
}

func InitializeQRService(cardProvisioningClient provisioning.CardProvisioningClient, feCardClient card3.CardClient, feTimelineClient timeline3.TimelineServiceClient, conf *config.Config, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, upiClient upi.UPIClient, piClient paymentinstrument.PiClient, actorClient actor.ActorClient, merchantServiceClient merchant.MerchantServiceClient, orderServiceClient order.OrderServiceClient, mandateServiceClient mandate.MandateServiceClient, timelineClient timeline.TimelineServiceClient, accountPiRelationClient account_pi.AccountPIRelationClient, fireflyClient firefly.FireflyClient, dynConf *genconf.Config, upiOnbClient onboarding2.UpiOnboardingClient, userOnboardingClient onboarding.OnboardingClient, savingsClient savings.SavingsClient, eventBroker events.Broker) *qr2.Service {
	service := qr2.NewService(cardProvisioningClient, feCardClient, feTimelineClient, conf, recurringPaymentClient, upiClient, piClient, actorClient, merchantServiceClient, orderServiceClient, mandateServiceClient, timelineClient, accountPiRelationClient, fireflyClient, dynConf, upiOnbClient, userOnboardingClient, savingsClient, eventBroker)
	return service
}

func InitializeWealthOnboardingService(beWealthOnboardingClient wealthonboarding.WealthOnboardingClient, consentClient consent.ConsentClient, actorClient actor.ActorClient, userClient user.UsersClient, config2 *genconf.Config, authClient auth.AuthClient, eventBroker events.Broker, investmentProflileClient profile2.InvestmentProfileServiceClient) *onboarding3.WealthOnboardingService {
	wealthOnboardingService := onboarding3.NewWealthOnboardingService(beWealthOnboardingClient, consentClient, userClient, actorClient, config2, authClient, eventBroker, investmentProflileClient)
	return wealthOnboardingService
}

func InitializeAppFeedbackService(beClient app_feedback.AppFeedbackClient) *app_feedback2.Service {
	service := app_feedback2.NewAppFeedbackService(beClient)
	return service
}

func InitializeApiKeysService(conf *config.Config, genConfig *genconf.Config) (*apikeys.Service, error) {
	v, err := genconf.GetDeviceIdsForSafetynetV2Flow(genConfig)
	if err != nil {
		return nil, err
	}
	service := apikeys.NewApiKeysService(conf, v, genConfig)
	return service, nil
}

func InitializeInAppHelpMediaService(beClient media.InAppHelpMediaClient) *media2.Service {
	service := media2.NewInAppHelpMediaService(beClient)
	return service
}

func InitializeInvestmentService(catalogClient catalog.CatalogManagerClient, rmsClient manager3.RuleManagerClient, phClient payment_handler.PaymentHandlerClient, orderManagerClient order2.OrderManagerClient, wealthOnBoardingClient wealthonboarding.WealthOnboardingClient, feWealthOnBoardingClient wealthonboarding2.WealthOnboardingClient, vkycClient vkyc.VKYCClient, transactionFeClient transaction2.TransactionClient, payClient pay.PayClient, savingsClient savings.SavingsClient, omsClient order.OrderServiceClient, timeline4 timeline.TimelineServiceClient, actorClient actor.ActorClient, authClient auth.AuthClient, investmentAuth auth3.AuthClient, config2 *genconf.Config, userClient user.UsersClient, userGroupClient group.GroupClient, eventBroker events.Broker, mfNotificationClient notifications.NotificationsClient, fitttClient fittt.FitttClient, consentClient consent.ConsentClient, mfExternalClient external.MFExternalOrdersClient, onboardingClient onboarding.OnboardingClient, paySavingsBalanceClient balance.BalanceClient, creditReportClient creditreportv2.CreditReportManagerClient, connectedAccountClient connected_account.ConnectedAccountClient, networthClient networth.NetWorthClient) *mutualfund.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(config2)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	defaultTime := datetime.NewDefaultTime()
	panProcessor := pkg.NewUnverifiedPanProcessor(userClient, creditReportClient, connectedAccountClient)
	service := mutualfund.NewInvestmentService(catalogClient, rmsClient, phClient, orderManagerClient, wealthOnBoardingClient, feWealthOnBoardingClient, vkycClient, transactionFeClient, payClient, savingsClient, omsClient, timeline4, actorClient, authClient, investmentAuth, config2, userClient, userGroupClient, evaluator, eventBroker, mfNotificationClient, fitttClient, consentClient, mfExternalClient, onboardingClient, defaultTime, paySavingsBalanceClient, panProcessor, networthClient)
	return service
}

func InitializeDynamicElementsService(beDynamicElementsClient types4.DynamicElementsClientWithInterceptors, config2 *genconf.Config) *dynamic_elements2.Service {
	dynamicElementsClient := types4.DynamicElementsClientProvider(beDynamicElementsClient)
	service := dynamic_elements2.NewDynamicElementsService(dynamicElementsClient, config2)
	return service
}

func InitializeP2PInvestmentService(p2PInvestmentClient p2pinvestment.P2PInvestmentClient, userClient user.UsersClient, actorClient actor.ActorClient, omsClient order.OrderServiceClient, riskProfileClient profile.ProfileClient, consentClient consent.ConsentClient, userGroupClient group.GroupClient, conf *genconf.Config, client onboarding.OnboardingClient, tieringClient tiering.TieringClient) *p2pinvestment2.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	abFeatureReleaseConfig := provider.ABFeatureReleaseConfigProvider(conf)
	abEvaluator := provider.GetZeroStateDashboardABEvaluatorProvider(actorClient, userClient, userGroupClient, abFeatureReleaseConfig)
	rpcHelper := helper3.NewRpcHelper(p2PInvestmentClient, userClient, actorClient, evaluator, consentClient, userGroupClient, abEvaluator, client)
	knowMoreGenerator := deeplinks.NewKnowMoreGenerator()
	unlockAccessGenerator := deeplinks.NewUnlockAccessGenerator(conf)
	withdrawGenerator := deeplinks.NewWithdrawGenerator(rpcHelper)
	availablePlansInfoGenerator := deeplinks.NewAvailablePlansInfoGenerator(rpcHelper, conf)
	enterWithdrawAmountGenerator := deeplinks.NewEnterWithdrawAmountGenerator(rpcHelper)
	deeplinkGenerator := deeplinks.NewDeeplinkGenerator(knowMoreGenerator, unlockAccessGenerator, withdrawGenerator, availablePlansInfoGenerator, enterWithdrawAmountGenerator)
	service := p2pinvestment2.NewService(p2PInvestmentClient, omsClient, riskProfileClient, rpcHelper, deeplinkGenerator, conf, tieringClient)
	return service
}

func InitializePreApprovedLoanService(preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, consentClient consent.ConsentClient, vkycClient vkyc.VKYCClient, savingsClient savings.SavingsClient, actorClient actor.ActorClient, eventsBroker events.Broker, conf *config.Config, usersClient user.UsersClient, employmentClient employment.EmploymentClient, genconf2 *genconf.Config, onbClient onboarding.OnboardingClient, segmentationServiceClient segment.SegmentationServiceClient, userGroupClient group.GroupClient, accountBalanceClient balance.BalanceClient, mfCatalogClient catalog.CatalogManagerClient, authClient auth.AuthClient, ffVgClient fiftyfin.FiftyFinClient, client secured_loans.SecuredLoansClient, searchClient search.ActionBarClient, userLocationClient location2.LocationClient, frontendClient recurringpayment3.RecurringPaymentServiceClient, payClient pay.PayClient) *preapprovedloan3.Service {
	preApprovedLoan := getPreApprovedLoanConfig(genconf2)
	lending := getLendingConfig(genconf2)
	defaultTime := datetime.NewDefaultTime()
	rpcHelperImpl := helper.NewRpcHelperImpl(actorClient, savingsClient, usersClient, preApprovedLoanClient, mfCatalogClient, defaultTime, consentClient, accountBalanceClient, userGroupClient)
	baseDeeplinkProvider := baseprovider.NewBaseDeeplinkProvider(preApprovedLoan, lending, rpcHelperImpl, onbClient, savingsClient, usersClient)
	liquiloansProvider := liquiloans.NewLiquiloansProvider(baseDeeplinkProvider)
	esProvider := liquiloans.NewLiquiloansEarlySalaryProvider(baseDeeplinkProvider)
	idfcProvider := idfc.NewIdfcProvider(baseDeeplinkProvider)
	fldgProvider := liquiloans.NewFldgProvider(liquiloansProvider)
	fiLiteProvider := liquiloans.NewFiLiteProvider(liquiloansProvider)
	acqToLendProvider := liquiloans.NewAcqToLendProvider(fiLiteProvider, onbClient)
	lamfProvider := fiftyfin2.NewLamfProvider(baseDeeplinkProvider, mfCatalogClient, rpcHelperImpl, defaultTime, ffVgClient, preApprovedLoanClient)
	abflProvider := abfl.NewAbflProvider(baseDeeplinkProvider)
	moneyviewProvider := moneyview.NewMvProvider(baseDeeplinkProvider, rpcHelperImpl, preApprovedLoanClient)
	realtimeetbProvider := realtimeetb.NewRealTimeEtbProvider(baseDeeplinkProvider)
	realTimeDistProvider := liquiloans.NewRealTimeDistProvider(liquiloansProvider)
	loansProvider := federal.NewFederalLoansProvider(baseDeeplinkProvider)
	stplProvider := liquiloans.NewStplProvider(fldgProvider)
	realTimeProvider := federal.NewFederalRealTimeProvider(loansProvider, rpcHelperImpl)
	realTimeSubventionProvider := liquiloans.NewRealTimeSubventionProvider(liquiloansProvider)
	realTimeStplProvider := liquiloans.NewRealTimeStplProvider(realTimeSubventionProvider)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genconf2)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	eligibilityProvider := epifitech.NewEligibilityProvider(baseDeeplinkProvider, evaluator)
	nonFiCoreStplProvider := liquiloans.NewNonFiCoreStplProvider(liquiloansProvider)
	nonFiCoreSubventionProvider := liquiloans.NewNonFiCoreSubventionProvider(liquiloansProvider)
	stockguardianProvider := stockguardian.NewStockGuardianProvider(baseDeeplinkProvider)
	lendenProvider := lenden.NewLendenProvider(baseDeeplinkProvider)
	pwaJourneyProvider := abfl.NewPwaJourneyProvider(abflProvider, rpcHelperImpl)
	realTimeNtbFedProvider := federal.NewRealTimeNtbFedProvider(loansProvider)
	nonFiCoreMvProvider := moneyview.NewNonFiCoreMvProvider(moneyviewProvider)
	stockGuardianProviderEarlySalary := stockguardian.NewStockGuardianProviderEarlySalary(stockguardianProvider)
	deeplinkProviderFactory := deeplinks2.NewDeeplinkProviderFactory(baseDeeplinkProvider, liquiloansProvider, esProvider, idfcProvider, fldgProvider, fiLiteProvider, acqToLendProvider, lamfProvider, abflProvider, moneyviewProvider, realtimeetbProvider, realTimeDistProvider, loansProvider, stplProvider, realTimeProvider, realTimeSubventionProvider, realTimeStplProvider, eligibilityProvider, nonFiCoreStplProvider, nonFiCoreSubventionProvider, stockguardianProvider, lendenProvider, pwaJourneyProvider, realTimeNtbFedProvider, nonFiCoreMvProvider, stockGuardianProviderEarlySalary)
	aggregatedDeeplinkProvider := aggregated_deeplinks.NewAggregatedDeeplinkProvider(deeplinkProviderFactory, baseDeeplinkProvider, usersClient, onbClient, preApprovedLoan, evaluator, eventsBroker)
	recordP2PPreBreConsent := consent3.NewRecordP2PPreBreConsent(consentClient)
	service := preapprovedloan3.NewService(preApprovedLoanClient, consentClient, vkycClient, savingsClient, actorClient, eventsBroker, conf, deeplinkProviderFactory, usersClient, employmentClient, genconf2, onbClient, segmentationServiceClient, evaluator, accountBalanceClient, userGroupClient, authClient, client, rpcHelperImpl, aggregatedDeeplinkProvider, searchClient, userLocationClient, payClient, recordP2PPreBreConsent)
	return service
}

func InitializeCategorizerService(txnCategorizerClient categorizer.TxnCategorizerClient, orderClient order.OrderServiceClient, aaOrderClient aa.AccountAggregatorClient, conf *config.Config) *categorizer2.Service {
	txnCatParams := TxnCatParamsProvider(conf)
	similarActivityParams := SimilarActivityParamsProvider(conf)
	service := categorizer2.NewTxnCategorizerService(txnCategorizerClient, orderClient, aaOrderClient, txnCatParams, similarActivityParams)
	return service
}

func InitializeAnalyserService(config3 *config.Config, dynConf *genconf.Config, actorClient actor.ActorClient, userGroupClient group.GroupClient, caClient connected_account.ConnectedAccountClient, savingsClient savings.SavingsClient, txnAggregatesClient txnaggregates.TxnAggregatesClient, feCategoriserClient categorizer3.TxnCategorizerClient, eventsBroker events.Broker, merchantClient merchant.MerchantServiceClient, categorizerBeClient categorizer.TxnCategorizerClient, userClient user.UsersClient, creditReportClient creditreportv2.CreditReportManagerClient, appFeedbackClient app_feedback.AppFeedbackClient, beStoryClient story.StoryClient, investAnalyticsClient investment.InvestmentAnalyticsClient, mfCatalogManagerClient catalog.CatalogManagerClient, client pinot3.TxnAggregatesClient, fireflyClient firefly.FireflyClient, accountingClient accounting.AccountingClient, mfExternalClient external.MFExternalOrdersClient, upcomingTxnsClient upcomingtransactions.UpcomingTransactionsClient, depositClient deposit.DepositClient, piClient paymentinstrument.PiClient, onboardingClient onboarding.OnboardingClient, accountBalanceClient balance.BalanceClient, palClient preapprovedloan.PreApprovedLoanClient, tieringClient tiering.TieringClient, reminderClient reminder.ReminderServiceClient, segmentClient segment.SegmentationServiceClient, portfolioManagerClient portfolio.PortfolioManagerClient) (*analyser2.Service, error) {
	featureReleaseConfig := AnalyserFeatureFlagProvider(config3)
	genconfFeatureReleaseConfig := provider.FeatureReleaseConfigProvider(dynConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(genconfFeatureReleaseConfig, constraintFactoryImpl)
	experimentEvaluatorImpl := util.NewExperimentEvalutor(dynConf)
	userGroupProviderImpl := dataprovider.NewUserGroupHelperProviderImpl(actorClient, userGroupClient)
	analyserReleaseEvaluatorImpl := release3.NewAnalyserReleaseEvaluatorImpl(userGroupProviderImpl, dynConf)
	widgetGeneratorFactoryImpl := factory3.NewWidgetGeneratorFactoryImpl(evaluator, experimentEvaluatorImpl, analyserReleaseEvaluatorImpl, mfExternalClient, onboardingClient)
	connectFiToFiSvc := fi_to_fi.NewConnectFiToFiSvc(caClient, savingsClient, dynConf, evaluator, onboardingClient)
	genericWidgetEnricherImpl := widget.NewGenericWidgetEnricherImpl(dynConf, evaluator, connectFiToFiSvc)
	sectionGenerator := section.NewSectionGenerator(widgetGeneratorFactoryImpl, genericWidgetEnricherImpl)
	uuidGenerator := idgen.NewUuidGenerator()
	defaultTime := datetime.NewDefaultTime()
	referenceIdMapImpl := reference_id_mapping.NewReferenceIdMapImpl()
	analyserFeedback := feedback.NewAnalyserFeedback(appFeedbackClient, dynConf, uuidGenerator, defaultTime, referenceIdMapImpl)
	bannerFactoryImpl := factory4.NewBannerFactoryImpl(beStoryClient, dynConf, evaluator)
	processorImpl := status.NewProcessor(investAnalyticsClient)
	configConfig := config2.LoadAnalyserConfigV2(dynConf)
	creditScoreParamsFetcherImpl := params_fetcher.NewCreditScoreParamsFetcherImpl(userClient)
	creditCardHelperImpl := helper4.NewCreditCardHelperImpl(fireflyClient, accountingClient)
	mutualFundCatalogProvider := dataprovider2.NewMutualFundCatalogProvider(mfCatalogManagerClient)
	upcomingTxnsHelperImpl := helper4.NewUpcomingTxnsHelperImpl(actorClient, merchantClient, mfCatalogManagerClient, depositClient, upcomingTxnsClient)
	analyticsCollectorImpl := mutualfunds.NewAnalyticsCollectorImpl(investAnalyticsClient, mfCatalogManagerClient, mutualFundCatalogProvider)
	creditScoreAnalyserConfig := creditScoreAnalyserConfigProvider(dynConf)
	creditReportFetcher := credit_score.NewCreditReportFetcher(creditScoreAnalyserConfig, defaultTime, evaluator, creditReportClient)
	mutualFundsHelperImpl := mutualfunds2.NewMutualFundsHelperImpl(investAnalyticsClient, mfExternalClient)
	deeplinkProvider := deeplink2.NewDeeplinkProvider(evaluator)
	actorAccountsImpl := datafetcher.NewActorAccountsImpl(caClient, savingsClient, fireflyClient)
	merchantsImpl := datafetcher.NewMerchantsImpl(merchantClient)
	txnAggregatesImpl := txnaggregates2.NewTxnAggregatesImpl(txnAggregatesClient, actorAccountsImpl, merchantsImpl, piClient)
	lendingInsightsImpl := loans.NewLendingInsightsImpl(segmentClient, dynConf)
	categoryAggregateFetcher := top_categories.NewCategoryAggregateFetcher(txnAggregatesClient, client, creditCardHelperImpl, categorizerBeClient)
	analyserProcessorFactoryImpl := factory5.NewAnalyserProcessorFactoryImpl(dynConf, evaluator, analyserReleaseEvaluatorImpl, txnAggregatesClient, merchantClient, savingsClient, creditReportClient, creditScoreParamsFetcherImpl, investAnalyticsClient, mfCatalogManagerClient, client, creditCardHelperImpl, mfExternalClient, mutualFundCatalogProvider, actorClient, userGroupProviderImpl, upcomingTxnsHelperImpl, analyticsCollectorImpl, creditReportFetcher, mutualFundsHelperImpl, categorizerBeClient, accountBalanceClient, deeplinkProvider, defaultTime, actorAccountsImpl, txnAggregatesImpl, palClient, tieringClient, reminderClient, lendingInsightsImpl, categoryAggregateFetcher)
	accountsHelperImpl := helper4.NewAccountHelperImpl(caClient, savingsClient, actorClient)
	filterProcessorFactoryImpl := factory6.NewFilterProcessorFactoryImpl(feCategoriserClient, categorizerBeClient, evaluator, accountsHelperImpl, creditCardHelperImpl)
	filterValueGeneratorImpl := generator.NewFilterValueGeneratorImpl(filterProcessorFactoryImpl)
	parallelGeneratorEvaluator := visualcomponents.NewParallelGeneratorEvaluator()
	previewGenerator := landingpage.NewPreviewGenerator(configConfig, analyserProcessorFactoryImpl, analyserReleaseEvaluatorImpl, filterValueGeneratorImpl, parallelGeneratorEvaluator, evaluator)
	landingPageProcessorFactory := factory7.NewLandingPageProcessorFactory(previewGenerator)
	timeAnalyserHelperImpl := time.NewTimeAnalyserHelperImpl(actorAccountsImpl, txnAggregatesImpl)
	categoryHelperImpl := category.NewCategoryHelperImpl(actorAccountsImpl, txnAggregatesImpl, client, categorizerBeClient)
	cardFactory := factory8.NewCardFactory(creditReportClient, creditScoreParamsFetcherImpl, timeAnalyserHelperImpl, defaultTime, mutualFundsHelperImpl, categoryHelperImpl, onboardingClient, actorAccountsImpl, portfolioManagerClient)
	filterWidgetGeneratorFactoryImpl := factory9.NewFilterWidgetGeneratorFactoryImpl()
	bannerProcessorFactoryImpl := factory10.NewBannerProcessorFactory()
	analyserRequestHandlerImpl := handler.NewAnalyserRequestHandlerImpl(config3, dynConf, featureReleaseConfig, analyserProcessorFactoryImpl, filterWidgetGeneratorFactoryImpl, eventsBroker, bannerProcessorFactoryImpl, evaluator, sectionGenerator, experimentEvaluatorImpl, creditReportClient, analyserFeedback, userGroupProviderImpl, analyserReleaseEvaluatorImpl, bannerFactoryImpl, processorImpl, filterValueGeneratorImpl, cardFactory, onboardingClient)
	analyserRequestExecutorImpl := executor.NewAnalyserRequestExecutorImpl(analyserRequestHandlerImpl, txnAggregatesClient, actorAccountsImpl, defaultTime)
	service, err := analyser2.NewService(config3, dynConf, featureReleaseConfig, eventsBroker, evaluator, sectionGenerator, experimentEvaluatorImpl, creditReportClient, analyserFeedback, userGroupProviderImpl, analyserReleaseEvaluatorImpl, bannerFactoryImpl, processorImpl, landingPageProcessorFactory, cardFactory, onboardingClient, analyserRequestExecutorImpl)
	if err != nil {
		return nil, err
	}
	return service, nil
}

func InitializeConfigService(cfg *genconf.Config) (*configsvc.ConfigSvc, error) {
	v, err := genconf.GetDeviceIdsForSafetynetV2Flow(cfg)
	if err != nil {
		return nil, err
	}
	configSvc := configsvc.NewConfigSvc(cfg, v)
	return configSvc, nil
}

func InitializeGoalsService(goalsClient goals.GoalsClient, usersClient user.UsersClient, depositClient deposit.DepositClient, actorClient actor.ActorClient, userGroupClient group.GroupClient, conf *genconf.Config, bcClient bankcust.BankCustomerServiceClient) *goals2.Service {
	service := goals2.NewService(goalsClient, usersClient, depositClient, actorClient, userGroupClient, conf, bcClient)
	return service
}

func InitializeTicketService(ticketClient ticket.TicketClient, config3 *genconf.Config) *ticket3.Service {
	service := ticket3.NewService(ticketClient, config3)
	return service
}

func InitializeInvestmentAggregatorService(invAggrClient aggregator2.InvestmentAggregatorClient, vkycClient vkyc.VKYCClient, savingsClient savings.SavingsClient, actorClient actor.ActorClient, investPayClient payment_handler.PaymentHandlerClient, config2_2 *genconf.Config, userClient user.UsersClient, p2PInvestmentClient p2pinvestment.P2PInvestmentClient, dynamicElementsFeClient dynamic_elements.DynamicElementsClient, userGroupClient group.GroupClient, segmentationServiceClient segment.SegmentationServiceClient, mfCatalogManagerClient catalog.CatalogManagerClient, usStocksCatalogManagerClient catalog2.CatalogManagerClient, eventBroker events.Broker, rmsRuleManagerClient manager3.RuleManagerClient, invEventProcessorClient event_processor.EventProcessorClient, depositClient deposit.DepositClient, usStocksOrderManagerClient order3.OrderManagerClient, onbClient onboarding.OnboardingClient, usStocksAccountManager account.AccountManagerClient, dynamicUIElementSvc dynamic_ui_element.DynamicUIElementServiceClient, accountBalanceClient balance.BalanceClient, connectedAccountClient connected_account.ConnectedAccountClient) *aggregator3.Service {
	mutualFundsPlugin := recommendation_plugins.NewMutualFundsPlugin(config2_2, mfCatalogManagerClient)
	usStocksPlugin := recommendation_plugins.NewUSStocksPlugin(config2_2, usStocksCatalogManagerClient, usStocksOrderManagerClient)
	p2PPlugin := recommendation_plugins.NewP2PPlugin(config2_2)
	sdPlugin := recommendation_plugins.NewSDPlugin(config2_2, depositClient)
	fdPlugin := recommendation_plugins.NewFDPlugin(config2_2, depositClient)
	collectionPlugin := recommendation_plugins.NewCollectionPlugin()
	hybridInstrumentsPlugin := recommendation_plugins.NewHybridInstrumentsPlugin(config2_2, mutualFundsPlugin, usStocksPlugin, p2PPlugin, sdPlugin, fdPlugin, collectionPlugin)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(config2_2)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	defaultTime := datetime.NewDefaultTime()
	abFeatureReleaseConfig := provider.ABFeatureReleaseConfigProvider(config2_2)
	abEvaluator := provider.GetInvestmentLandingMutualFundDeeplinkABEvaluatorProvider(actorClient, userClient, userGroupClient, abFeatureReleaseConfig)
	releaseABEvaluator := provider.GetInvestmentHomeComponentABEvaluatorProvider(actorClient, userClient, userGroupClient, abFeatureReleaseConfig)
	fdNavigator := navigators2.NewFdNavigator(depositClient)
	sdNavigator := navigators2.NewSDNavigator(depositClient)
	navigationImpl := retention.NewNavigator(fdNavigator, sdNavigator)
	fdRetentionExitComponent := components.NewFDRetentionExitComponent(depositClient, config2_2)
	fdClosureChoiceComponent := components.NewFDClosureChoiceComponent(depositClient, config2_2)
	fdClosureSummaryComponent := components.NewFDClosureSummaryComponent(depositClient, config2_2)
	fdRetentionBenefitComponent := components.NewFDRetentionBenefitComponent(depositClient, config2_2)
	fdRetentionRewardsComponent := components.NewFDRetentionRewardsComponent(depositClient, config2_2)
	fdInstrumentHandler := retention.NewFDInstrument(fdRetentionExitComponent, fdClosureChoiceComponent, fdClosureSummaryComponent, fdRetentionBenefitComponent, fdRetentionRewardsComponent)
	sdRetentionExitComponent := components2.NewSDRetentionExitComponent(depositClient, config2_2)
	sdClosureChoiceComponent := components2.NewSDClosureChoiceComponent(depositClient, config2_2)
	sdClosureSummaryComponent := components2.NewSDClosureSummaryComponent(depositClient, config2_2)
	sdRetentionBenefitComponent := components2.NewSDRetentionBenefitComponent(depositClient, config2_2)
	sdRetentionRewardsComponent := components2.NewSDRetentionRewardsComponent(depositClient, config2_2)
	sdInstrumentHandler := retention.NewSDInstrument(sdRetentionExitComponent, sdClosureChoiceComponent, sdClosureSummaryComponent, sdRetentionBenefitComponent, sdRetentionRewardsComponent)
	instrumentHandlerFactoryImpl := retention.NewInstrumentHandler(fdInstrumentHandler, sdInstrumentHandler)
	retentionImpl := retention.NewRetentionImpl(navigationImpl, instrumentHandlerFactoryImpl)
	standingInstructionFitttAggregator := fittt3.NewStandingInstructionFitttAggregator(rmsRuleManagerClient, investPayClient)
	service := aggregator3.NewService(invAggrClient, vkycClient, savingsClient, actorClient, dynamicElementsFeClient, p2PInvestmentClient, config2_2, mutualFundsPlugin, usStocksPlugin, hybridInstrumentsPlugin, sdPlugin, fdPlugin, p2PPlugin, dynamicUIElementSvc, mfCatalogManagerClient, usStocksCatalogManagerClient, userClient, userGroupClient, segmentationServiceClient, evaluator, eventBroker, rmsRuleManagerClient, defaultTime, invEventProcessorClient, abFeatureReleaseConfig, abEvaluator, releaseABEvaluator, onbClient, retentionImpl, usStocksAccountManager, accountBalanceClient, connectedAccountClient, standingInstructionFitttAggregator)
	return service
}

func InitializeInvestmentProfileService(investmentProfileServiceClient profile2.InvestmentProfileServiceClient) *profile3.Service {
	service := profile3.NewService(investmentProfileServiceClient)
	return service
}

func InitializeUSStocksService(accountManager account.AccountManagerClient, conf *genconf.Config, iftClient internationalfundtransfer2.InternationalFundTransferClient, actorClient actor.ActorClient, catalogManagerClient catalog2.CatalogManagerClient, catalogManagerStreamClient types2.USSCatalogMgrStreamClient, orderManagerClient order3.OrderManagerClient, portfolioManagerClient portfolio.PortfolioManagerClient, savingsClient savings.SavingsClient, authClient auth.AuthClient, usersClient user.UsersClient, userGroupClient group.GroupClient, payClient pay.PayClient, bcClient bankcust.BankCustomerServiceClient, broker events.Broker, dynamicUiSvcClient dynamic_ui_element.DynamicUIElementServiceClient, onboardingClient onboarding.OnboardingClient, accountBalanceClient balance.BalanceClient, preApprovedLoanClient preapprovedloan.PreApprovedLoanClient, ussRewardsClient rewards4.UssRewardManagerClient, upiOnboardingClient onboarding2.UpiOnboardingClient, rmsClient manager3.RuleManagerClient, fitttClient fittt.FitttClient, salaryClient salaryprogram.SalaryProgramClient, beTieringClient tiering.TieringClient, provisioningClient provisioning.CardProvisioningClient, alfredClient alfred.AlfredClient, rewardsGeneratorClient rewards.RewardsGeneratorClient, rewardsAggregatesClient pinot2.RewardsAggregatesClient, rewardProjectionClient projector.ProjectorServiceClient, txnAggregateClient txnaggregates.TxnAggregatesClient, orderServiceClient order.OrderServiceClient, employmentClient employment.EmploymentClient, healthInsuranceClient healthinsurance.HealthInsuranceClient, rewardOffersClient rewardoffers.RewardOffersClient) (*usstocks2.Service, error) {
	retryParams := provider.PriceUpdatesRetryStrategyProvider(conf)
	retryStrategy, err := retry.NewStrategyFromConfig(retryParams)
	if err != nil {
		return nil, err
	}
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	pendingBeforeCompletedActivitiesIterator := activity.NewPendingBeforeCompletedActivitiesIterator(orderManagerClient)
	dataCollectorService := data_collector.NewDataCollectorService(conf, savingsClient, actorClient, bcClient, salaryClient, accountBalanceClient, beTieringClient, provisioningClient, alfredClient, rewardsGeneratorClient, rewardsAggregatesClient, rewardProjectionClient, txnAggregateClient, orderServiceClient, accountManager, employmentClient, healthInsuranceClient, rewardOffersClient, usersClient, userGroupClient)
	bottomSheetGenerator := dropoff.NewBottomSheetGenerator(conf, catalogManagerClient, dataCollectorService)
	federalA2Form := a2form.NewFederalA2Form(usersClient, bcClient, savingsClient, payClient)
	buyDetailsGenerator := orderdetails.NewBuyDetailsGenerator(accountManager, catalogManagerClient, orderManagerClient)
	sellDetailsGenerator := orderdetails.NewSellDetailsGenerator(accountManager, catalogManagerClient, portfolioManagerClient, orderManagerClient)
	service := usstocks2.NewUSStocksService(conf, accountManager, iftClient, catalogManagerClient, catalogManagerStreamClient, orderManagerClient, portfolioManagerClient, savingsClient, authClient, retryStrategy, usersClient, evaluator, userGroupClient, payClient, bcClient, broker, dynamicUiSvcClient, onboardingClient, pendingBeforeCompletedActivitiesIterator, accountBalanceClient, preApprovedLoanClient, ussRewardsClient, bottomSheetGenerator, upiOnboardingClient, rmsClient, fitttClient, federalA2Form, buyDetailsGenerator, sellDetailsGenerator, dataCollectorService)
	return service, nil
}

func InitializeUpiOnboardingService(upiOnboardingClient onboarding2.UpiOnboardingClient, actorClient actor.ActorClient, consentClient consent.ConsentClient, conf *config.Config, dynConf *genconf.Config, savingsClient savings.SavingsClient, authClient auth.AuthClient, payClient payment.PaymentClient, userOnboardingClient onboarding.OnboardingClient, connectedAccountClient connected_account.ConnectedAccountClient, userGroupClient group.GroupClient, userClient user.UsersClient, eventBroker events.Broker, accountPiClient account_pi.AccountPIRelationClient) *onboarding4.Service {
	service := onboarding4.NewService(upiOnboardingClient, actorClient, conf, consentClient, dynConf, savingsClient, authClient, payClient, userOnboardingClient, connectedAccountClient, userGroupClient, userClient, eventBroker, accountPiClient)
	return service
}

func InitialiseUpiService(actorClient actor.ActorClient, savingsClient savings.SavingsClient, upiClient upi.UPIClient) *upi3.Service {
	service := upi3.NewService(actorClient, savingsClient, upiClient)
	return service
}

func InitialiseFireflyService(fireflyClient firefly.FireflyClient, fireflyAccountingClient accounting.AccountingClient, txnCategorizerFeClient categorizer3.TxnCategorizerClient, ffBillingClient billing.BillingClient, actorClient actor.ActorClient, rewardsClient rewards.RewardsGeneratorClient, savingsClient savings.SavingsClient, txnCategorizerBeClient categorizer.TxnCategorizerClient, userClient user.UsersClient, merchantClient merchant.MerchantServiceClient, fireflyLmsClient lms.LoanManagementSystemClient, consentClient consent.ConsentClient, conf *config.Config, dynConf *genconf.Config, pinotClient pinot3.TxnAggregatesClient, limitEstimator credit_limit_estimator.CreditLimitEstimatorClient, onbClient onboarding.OnboardingClient, depositClient deposit.DepositClient, accountBalanceClient balance.BalanceClient, rewardsProjectionClient projector.ProjectorServiceClient, cardRecommendationClient card_recommendation.CardRecommendationServiceClient, payFeTxnClient transaction2.TransactionClient, payBeClient pay.PayClient, client group.GroupClient, creditReportClient creditreportv2.CreditReportManagerClient, segmentationServiceClient segment.SegmentationServiceClient, rewardsListingClient casper.OfferListingServiceClient, redemptionClient redemption.OfferRedemptionServiceClient, fireflyV2Client v2.FireflyV2Client, questCacheStorage types3.QuestCacheStorage, questManagerClient manager2.ManagerClient, eventsBroker events.Broker, rewardAggrClient pinot2.RewardsAggregatesClient, networthClient networth.NetWorthClient) *firefly3.Service {
	applicationInProgress := generator2.NewApplicationInProgress()
	applicationNotApproved := generator2.NewApplicationNotApproved()
	billDue := generator2.NewBillDue(dynConf)
	billDuePastDueDate := generator2.NewBillDuePastDueDate(dynConf)
	cardBlockedAndBillDue := generator2.NewCardBlockedAndBillDue(dynConf)
	cardBlockedAndNoBillDue := generator2.NewCardBlockedAndNoBillDue(dynConf)
	completeApplication := generator2.NewCompleteApplication()
	getYourCard := generator2.NewGetYourCard()
	hopOnWaitlist := generator2.NewHopOnWaitlist()
	noBillDue := generator2.NewNoBillDue(dynConf)
	notEligibleForCard := generator2.NewNotEligibleForCard()
	creditCardClosed := generator2.NewCreditCardClosed()
	homedashboardFactory := homedashboard.NewFactory(applicationInProgress, applicationNotApproved, billDue, billDuePastDueDate, cardBlockedAndBillDue, cardBlockedAndNoBillDue, completeApplication, getYourCard, hopOnWaitlist, noBillDue, notEligibleForCard, creditCardClosed)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, client)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	billInfoDashboardCommonHelper := billinfo_v2.NewBillInfoDashboardCommonHelper(dynConf, evaluator)
	billDueInXDaysValidator := billinfo_v2.NewBillDueInXDaysValidator(billInfoDashboardCommonHelper)
	billDueInXDaysCloseToDueDateValidator := billinfo_v2.NewBillDueInXDaysCloseToDueDateValidator(billInfoDashboardCommonHelper)
	billPastDueDateValidator := billinfo_v2.NewBillPastDueDateValidator(billInfoDashboardCommonHelper)
	paymentFailedValidator := billinfo_v2.NewPaymentFailedValidator(billInfoDashboardCommonHelper)
	paymentInProgressValidator := billinfo_v2.NewPaymentInProgressValidator(billInfoDashboardCommonHelper)
	limitUtilisedLatestBillNotGeneratedValidator := billinfo_v2.NewLimitUtilisedLatestBillNotGeneratedValidator(billInfoDashboardCommonHelper)
	noPendingDuesValidator := billinfo_v2.NewNoPendingDuesValidator(billInfoDashboardCommonHelper)
	creditCardClosedValidator := billinfo_v2.NewCreditCardClosedValidator(billInfoDashboardCommonHelper)
	billInfoValidationProcessor := billinfo_v2.NewBillInfoValidationProcessor(billDueInXDaysValidator, billDueInXDaysCloseToDueDateValidator, billPastDueDateValidator, paymentFailedValidator, paymentInProgressValidator, limitUtilisedLatestBillNotGeneratedValidator, noPendingDuesValidator, creditCardClosedValidator)
	unsecuredRewardsProvider := rewards5.NewUnsecuredRewards(dynConf, rewardsProjectionClient, pinotClient, evaluator)
	securedRewardsProvider := rewards5.NewSecuredRewards(dynConf, rewardsProjectionClient, fireflyAccountingClient, evaluator)
	massUnsecuredRewardsProvider := rewards5.NewMassUnsecuredRewardsProvider(dynConf, rewardsProjectionClient, evaluator, fireflyAccountingClient)
	rewardProvider := rewards5.NewRewardProvider(unsecuredRewardsProvider, securedRewardsProvider, massUnsecuredRewardsProvider)
	unsecuredFeesAndBenefitsProvider := fees_and_benefits.NewUnsecuredFeesAndBenefitsProvider(dynConf, fireflyClient, userClient, rewardsClient, fireflyAccountingClient, ffBillingClient, evaluator, redemptionClient)
	securedFeesAndBenefitsProvider := fees_and_benefits.NewSecuredFeesAndBenefitsProvider(dynConf)
	massUnsecuredFeesAndBenefitsProvider := fees_and_benefits.NewMassUnsecuredFeesAndBenefitsProvider(dynConf)
	feesAndBenefitsProvider := fees_and_benefits.NewFeesAndBenefitsProvider(unsecuredFeesAndBenefitsProvider, securedFeesAndBenefitsProvider, massUnsecuredFeesAndBenefitsProvider)
	questsdkClient := InitializeQuestSdkClient(questCacheStorage, dynConf, segmentationServiceClient, actorClient, userClient, client, questManagerClient, eventsBroker)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(userClient)
	ccIntroScreenBuilder := firefly3.NewCcIntroScreenBuilder()
	service := firefly3.NewService(fireflyClient, fireflyAccountingClient, txnCategorizerFeClient, ffBillingClient, actorClient, rewardsClient, savingsClient, txnCategorizerBeClient, userClient, merchantClient, fireflyLmsClient, consentClient, conf, dynConf, pinotClient, homedashboardFactory, limitEstimator, onbClient, depositClient, billInfoValidationProcessor, rewardProvider, accountBalanceClient, rewardsProjectionClient, feesAndBenefitsProvider, cardRecommendationClient, payFeTxnClient, payBeClient, evaluator, creditReportClient, segmentationServiceClient, rewardsListingClient, fireflyV2Client, questsdkClient, userAttributesFetcherImpl, ccIntroScreenBuilder, rewardAggrClient, networthClient)
	return service
}

func InitializeAuthOrchestratorService(authV2Client orchestrator2.OrchestratorClient, dynConf *genconf.Config) *orchestrator3.Service {
	service := orchestrator3.NewService(authV2Client, dynConf)
	return service
}

func InitializeNudgeService(nudgeClient nudge.NudgeServiceClient, userClient user.UsersClient, conf *config.Config, dynConf *genconf.Config, actorClient actor.ActorClient, userGroupClient group.GroupClient, onbClient onboarding.OnboardingClient, segmentationClient segment.SegmentationServiceClient, questCacheStorage types3.QuestCacheStorage, questManagerClient manager2.ManagerClient, eventBroker events.Broker, networthClient networth.NetWorthClient) *nudge3.Service {
	homeNudgeParams := HomeNudgeParamsProvider(conf)
	homeNudgeWidgetGenerator := widget2.NewHomeNudgeWidgetGenerator(homeNudgeParams, userClient)
	investmentLandingInvestedNudgeWidgetGenerator := widget2.NewInvestmentLandingInvestedNudgeWidgetGenerator(conf)
	nudgeWidgetGeneratorFactory := widget2.NewNudgeWidgetGeneratorFactory(homeNudgeWidgetGenerator, investmentLandingInvestedNudgeWidgetGenerator)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	client := InitializeQuestSdkClient(questCacheStorage, dynConf, segmentationClient, actorClient, userClient, userGroupClient, questManagerClient, eventBroker)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(userClient)
	service := nudge3.NewService(nudgeClient, nudgeWidgetGeneratorFactory, dynConf, evaluator, onbClient, client, userAttributesFetcherImpl, networthClient)
	return service
}

func InitializePromptService(conf *config.Config, genConf *genconf.Config, consentClient consent.ConsentClient, employmentClient employment.EmploymentClient, actorClient actor.ActorClient, userClient user.UsersClient, vkycClient vkyc.VKYCClient, userGroupClient group.GroupClient, kycClient kyc.KycClient, savingsClient savings.SavingsClient, bcClient bankcust.BankCustomerServiceClient, feEmpClient employment.EmploymentFeClient) *prompt.Service {
	incEmpDiscrepancyProc := processors.NewIncEmpDiscrepancyProc(conf, consentClient, employmentClient, feEmpClient)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := prompt.NewService(conf, genConf, incEmpDiscrepancyProc, evaluator)
	return service
}

func InitializeCreditReportService(creditReportV2Client creditreportv2.CreditReportManagerClient, dynConf *genconf.Config) *credit_report.Service {
	creditReportConfig := CreditReportCommonConfigProvider(dynConf)
	service := credit_report.NewCreditReportService(creditReportV2Client, creditReportConfig)
	return service
}

func InitializeDocumentUploadService(conf *genconf.Config, beWealthOnboardingClient wealthonboarding.WealthOnboardingClient, accountsManagerClient account.AccountManagerClient, actorClient actor.ActorClient, savingsClient savings.SavingsClient, accountStatementClient statement.AccountStatementClient, connectedAccountClient connected_account.ConnectedAccountClient, iftClient internationalfundtransfer2.InternationalFundTransferClient, celestialClient celestial.CelestialClient, salaryClient salaryprogram.SalaryProgramClient) *document_upload.Service {
	usStocks := usStocksConfigProvider(conf)
	usStocksDocumentExchange := usstocks3.NewUSStocksDocumentExchange(usStocks, beWealthOnboardingClient, accountsManagerClient, actorClient, savingsClient, accountStatementClient, connectedAccountClient, iftClient, celestialClient)
	documentExchange := salaryprogram3.NewSalaryProgramDocumentExchange(connectedAccountClient, salaryClient)
	documentExchangeFactory := document_upload.NewDocumentExchangeFactory(usStocksDocumentExchange, documentExchange)
	service := document_upload.NewDocumentUploadService(documentExchangeFactory)
	return service
}

func InitializeTieringService(genConf *genconf.Config, beTieringClient tiering.TieringClient, beSavingsClient savings.SavingsClient, beActorClient actor.ActorClient, beBankCustClient bankcust.BankCustomerServiceClient, beSalaryClient salaryprogram.SalaryProgramClient, client rewardoffers.RewardOffersClient, p2PInvestmentClient p2pinvestment.P2PInvestmentClient, accountBalanceClient balance.BalanceClient, usersClient user.UsersClient, userGroupClient group.GroupClient, segmentationClient segment.SegmentationServiceClient, provisioningClient provisioning.CardProvisioningClient, alfredClient alfred.AlfredClient, rewardsClient rewards.RewardsGeneratorClient, projectionClient projector.ProjectorServiceClient, txnAggregateClient txnaggregates.TxnAggregatesClient, orderClient order.OrderServiceClient, rewardsAggregatesClient pinot2.RewardsAggregatesClient, eventBroker events.Broker, ussAccountManagerClient account.AccountManagerClient, rewardsFeClient rewards2.RewardsClient, employmentClient employment.EmploymentClient, healthInsuranceClient healthinsurance.HealthInsuranceClient, tieringPinotClient pinot.EODBalanceClient, feDynamicElementsClient dynamic_elements.DynamicElementsClient) *tiering2.Service {
	feManagerService := release2.NewFeReleaseManagerService(genConf)
	dataCollectorService := data_collector.NewDataCollectorService(genConf, beSavingsClient, beActorClient, beBankCustClient, beSalaryClient, accountBalanceClient, beTieringClient, provisioningClient, alfredClient, rewardsClient, rewardsAggregatesClient, projectionClient, txnAggregateClient, orderClient, ussAccountManagerClient, employmentClient, healthInsuranceClient, client, usersClient, userGroupClient)
	service := benefits.NewBenefitsManagerService(genConf)
	bottom_infoService := bottom_info.NewBottomInfoManagerService(beTieringClient)
	ctaService := cta.NewCtaManagerService(genConf)
	cardService := card.NewCardManagerService()
	plan_optionsService := plan_options.NewPlanOptionsManagerService(genConf, service, bottom_infoService, ctaService, cardService, eventBroker)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(beActorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	benefitsService := benefits2.NewService(client, evaluator, genConf)
	tieringBenefitsService := benefits3.NewTieringBenefitsService(p2PInvestmentClient)
	deeplinkService := deeplink.NewDeeplinkManagerService(genConf, dataCollectorService, plan_optionsService, service, beTieringClient, benefitsService, tieringBenefitsService, provisioningClient, segmentationClient, beSalaryClient, eventBroker, usersClient)
	tierMovementBasedFeedbackFlowIntegrationSvc := feedback_flow_integration.NewTierMovementBasedFeedbackFlowIntegrationSvc(genConf, beTieringClient)
	earnedBenefitsDataCollectorService := earned_benefits.NewEarnedBenefitsDataCollectorService(genConf, dataCollectorService, segmentationClient, beTieringClient, rewardsFeClient, evaluator)
	service2 := cta2.NewService(genConf)
	componentBuilder := earned_benefits.NewComponentBuilder(genConf, service2, eventBroker)
	ambDataProvider := provider.AMBDataProviderProvider(beTieringClient, dataCollectorService, tieringPinotClient, genConf, eventBroker, feDynamicElementsClient)
	ambScreenBuilder := provider.AMBScreenBuilderProvider(genConf)
	tieringService := tiering2.NewService(feManagerService, genConf, beTieringClient, beSavingsClient, beActorClient, deeplinkService, segmentationClient, tierMovementBasedFeedbackFlowIntegrationSvc, earnedBenefitsDataCollectorService, componentBuilder, ussAccountManagerClient, beBankCustClient, dataCollectorService, usersClient, userGroupClient, ambDataProvider, ambScreenBuilder)
	return tieringService
}

func InitializeStoryService(genConf *genconf.Config, storyClient story.StoryClient) *story2.Service {
	service := story2.NewService(genConf, storyClient)
	return service
}

func InitializeBankCustomerService(genConf *genconf.Config, bankCustomerClient bankcust.BankCustomerServiceClient, alfredClient alfred.AlfredClient, consentClient consent.ConsentClient, compClient compliance.ComplianceClient) *bank_customer.Service {
	defaultTime := datetime.NewDefaultTime()
	service := bank_customer.NewService(genConf, bankCustomerClient, alfredClient, consentClient, defaultTime, compClient)
	return service
}

func InitializeAlfredService(genConf *genconf.Config, alfredClient alfred.AlfredClient, actorClient actor.ActorClient, usersClient user.UsersClient, userGroupClient group.GroupClient) *alfred2.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := alfred2.NewService(genConf, alfredClient, evaluator)
	return service
}

func InitializePanService(conf *config.Config, panClient pan.PanClient) *pan2.Service {
	service := pan2.NewService(conf, panClient)
	return service
}

func InitializeOTPService(catalogClient catalog.CatalogManagerClient, wealthOnboardingClient wealthonboarding.WealthOnboardingClient, rmsClient manager3.RuleManagerClient, authClient auth.AuthClient, investmentAuth auth3.AuthClient) *otp.Service {
	oneTimePurchaseHandler := mutual_fund.NewOneTimePurchaseHandler(wealthOnboardingClient, catalogClient, authClient, investmentAuth)
	registerSIPHandler := mutual_fund.NewRegisterSIPHandler(wealthOnboardingClient, catalogClient, authClient, investmentAuth, rmsClient)
	withdrawalHandler := mutual_fund.NewWithdrawalHandler(wealthOnboardingClient, catalogClient, authClient, investmentAuth)
	service := otp.NewService(oneTimePurchaseHandler, registerSIPHandler, withdrawalHandler)
	return service
}

func InitializeWaitlistService() *waitlist.Service {
	service := waitlist.NewService()
	return service
}

func InitializeDocsService(client alfred.AlfredClient, panClient pan.PanClient, userIntelClient userintel.UserIntelServiceClient, onboardingClient onboarding.OnboardingClient, palClient preapprovedloan.PreApprovedLoanClient, celestialClient celestial.CelestialClient, broker events.Broker) *docs2.Service {
	epanFileUploadManager := file_upload_manager.NewEPANFileUploadManager(panClient)
	itrIntimationFileUploadManager := file_upload_manager.NewITRIntimationFileUploadManager(userIntelClient, onboardingClient)
	lendingItrFileUploadManager := file_upload_manager.NewLendingItrFileUploadManager(userIntelClient, palClient, celestialClient, broker)
	fileUploadFactoryService := factory11.NewFileUploadFactoryService(epanFileUploadManager, itrIntimationFileUploadManager, lendingItrFileUploadManager)
	service := docs2.NewService(client, panClient, fileUploadFactoryService, palClient)
	return service
}

func InitializeFeedbackEngineService(genConf *genconf.Config, beFeedbackEngineClient feedback_engine.FeedbackEngineClient, actorClient actor.ActorClient, usersClient user.UsersClient, userGroupClient group.GroupClient) *feedback_engine2.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := feedback_engine2.NewService(genConf, beFeedbackEngineClient, evaluator)
	return service
}

func InitialiseGenieService(genConf *genconf.Config, authClient auth.AuthClient, userClient user.UsersClient, bcClient bankcust.BankCustomerServiceClient, kycClient kyc.KycClient, actorClient actor.ActorClient, agentClient agent.KycAgentServiceClient, userGroupMappingClient group.GroupClient, consentClient consent.ConsentClient, panClient pan.PanClient, empClient employment.EmploymentClient) *genie.Service {
	defaultTime := datetime.NewDefaultTime()
	service := genie.NewService(genConf, authClient, userClient, bcClient, kycClient, actorClient, agentClient, userGroupMappingClient, defaultTime, consentClient, panClient, empClient)
	return service
}

func InitialiseEpfService(genConf *genconf.Config, epfClient epf.EpfClient, consentClient consent.ConsentClient, userClient user.UsersClient, actorClient actor.ActorClient, client group.GroupClient) *epf2.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, client)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	deeplinkBuilder := deeplink_builder.NewDeeplinkBuilder(genConf, evaluator)
	service := epf2.NewEpfService(genConf, epfClient, userClient, consentClient, deeplinkBuilder, actorClient, evaluator)
	return service
}

func InitialiseNetWorthService(genConf *genconf.Config, netWorthClient networth.NetWorthClient, userClient user.UsersClient, consentClient consent.ConsentClient, epfClient epf.EpfClient, creditReportManagerClient creditreportv2.CreditReportManagerClient, userGroupClient group.GroupClient, actorClient actor.ActorClient, connectedAccountClient connected_account.ConnectedAccountClient, ordersClient external.MFExternalOrdersClient, onbClient onboarding.OnboardingClient, secretsFeClient secrets.SecretsClient, userDeclarationClient user_declaration.ServiceClient, empClient employment.EmploymentClient, investAnalyticsClient investment.InvestmentAnalyticsClient, mfCatalogManagerClient catalog.CatalogManagerClient, segmentSrvClient segment.SegmentationServiceClient, variableGeneratorClient variables.VariableGeneratorClient) *networth3.Service {
	configConfig := config3.LoadNetWorthConfig(genConf)
	networthConfig := provider.NetworthConfigProvider(genConf)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	deeplinkBuilder := deeplink_builder.NewDeeplinkBuilder(genConf, evaluator)
	widgetGeneratorFactory := factory12.NewWidgetGeneratorFactory(genConf, networthConfig, userClient, epfClient, connectedAccountClient, ordersClient, evaluator, userGroupClient, deeplinkBuilder)
	sectionGenerator := section2.NewGenerator(widgetGeneratorFactory, evaluator)
	netWorthDashboardConfig := NetWorthDashboardConfigProvider(configConfig)
	netWorthDataFetcherImpl := data_fetcher2.NewNetWorthDataFetcherImpl(netWorthClient, netWorthDashboardConfig, creditReportManagerClient, evaluator)
	visualisationGeneratorFactory := factory13.NewVisualisationGeneratorFactory()
	formBuilderFactoryImpl := networth4.NewFormBuilderFactoryImpl(networthConfig, netWorthClient, evaluator)
	dataTypeHandlerFactoryImpl := data_type_validator.NewDataTypeHandlerFactoryImpl()
	multiEditOptionHandlerFactoryImpl := multi_edit_option_validator.NewMultiEditOptionHandlerFactoryImpl()
	inputTypeHandlerFactoryImpl := inputvalidator.NewInputTypeHandlerFactoryImpl(dataTypeHandlerFactoryImpl, multiEditOptionHandlerFactoryImpl, netWorthClient)
	formInputValidatorImpl := inputvalidator.NewFormInputValidatorImpl(inputTypeHandlerFactoryImpl)
	assetDashboardGeneratorFactoryImpl := asset_dashboard.NewAssetDashboardGeneratorFactoryImpl()
	defaultTime := datetime.NewDefaultTime()
	networthFormProcessor := networth4.NewNetworthFormBuilder(formBuilderFactoryImpl, netWorthClient, consentClient, formInputValidatorImpl)
	userDeclarationFormBuilderFactoryImpl := user_declaration2.NewUserDeclarationFormBuilderFactoryImpl(empClient, genConf, userDeclarationClient)
	userDeclarationFromProcessor := user_declaration2.NewUserDeclarationFromProcessor(userDeclarationClient, userDeclarationFormBuilderFactoryImpl)
	panProcessor := pkg.NewUnverifiedPanProcessor(userClient, creditReportManagerClient, connectedAccountClient)
	panFormProcessor := profile4.NewPanFormProcessor(panProcessor)
	dobProcessor := pkg.NewDobProcessor(userClient)
	dobFormProcessor := profile4.NewDobFormProcessor(dobProcessor)
	profileDataCollectionFormProcessor := profile4.NewProfileDataCollectionFormProcessor(panFormProcessor, dobFormProcessor)
	formProcessorFactory := factory14.NewFormProcessorFactory(networthFormProcessor, userDeclarationFromProcessor, profileDataCollectionFormProcessor)
	epfDashboardComponentViewGetter := dashboard2.NewEpfDashboardComponentViewGetter(netWorthClient)
	mfDashboardComponentViewGetter := dashboard2.NewMfDashboardComponentViewGetter(netWorthClient)
	creditScoreDashboardComponentViewGetter := dashboard2.NewCreditScoreDashboardComponentViewGetter(creditReportManagerClient, netWorthClient)
	netWorthDashboardComponentViewGetter := dashboard2.NewNetWorthDashboardComponentViewGetter(connectedAccountClient, genConf, deeplinkBuilder, onbClient, netWorthClient)
	dashboardComponentViewGetterFactory := dashboard2.NewDashboardComponentViewGetterFactory(epfDashboardComponentViewGetter, mfDashboardComponentViewGetter, creditScoreDashboardComponentViewGetter, netWorthDashboardComponentViewGetter)
	dashboardComponent := scrollable2.NewDashboardComponent(dashboardComponentViewGetterFactory)
	wealthAnalyserComponent := scrollable2.NewWealthAnalyserComponent(secretsFeClient)
	dailyTracker := strategy.NewDailyTracker(evaluator, genConf)
	weeklyTracker := strategy.NewWeeklyTracker()
	wealthBuilderLanding := dashboard3.NewWealthBuilderLanding(configConfig, netWorthClient, netWorthDataFetcherImpl, sectionGenerator, deeplinkBuilder, evaluator, genConf, segmentSrvClient, variableGeneratorClient, dailyTracker, weeklyTracker)
	wealthBuilderLandingComponent := scrollable2.NewWealthBuilderLandingComponent(wealthBuilderLanding)
	wealthComponentGeneratorFactory := scrollable2.NewWealthComponentGeneratorFactory(dashboardComponent, wealthAnalyserComponent, wealthBuilderLandingComponent)
	mutualFundCatalogProvider := dataprovider2.NewMutualFundCatalogProvider(mfCatalogManagerClient)
	analyticsCollectorImpl := mutualfunds.NewAnalyticsCollectorImpl(investAnalyticsClient, mfCatalogManagerClient, mutualFundCatalogProvider)
	service := networth3.NewService(genConf, configConfig, netWorthClient, consentClient, sectionGenerator, netWorthDataFetcherImpl, visualisationGeneratorFactory, formBuilderFactoryImpl, formInputValidatorImpl, assetDashboardGeneratorFactoryImpl, defaultTime, onbClient, connectedAccountClient, deeplinkBuilder, epfClient, ordersClient, creditReportManagerClient, evaluator, secretsFeClient, formProcessorFactory, wealthComponentGeneratorFactory, analyticsCollectorImpl, dashboardComponentViewGetterFactory, wealthBuilderLanding, widgetGeneratorFactory)
	return service
}

func InitialisePayService(bmsClient beneficiarymanagement.BeneficiaryManagementClient, upiOnboardingClient onboarding2.UpiOnboardingClient, conf *config.Config, actorClient actor.ActorClient, upiClient upi.UPIClient, onboardingClient onboarding.OnboardingClient, accountPiClient account_pi.AccountPIRelationClient, savingsClient savings.SavingsClient, genconf2 *genconf.Config, connectedAccountClient connected_account.ConnectedAccountClient, payClient pay.PayClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, userClient user.UsersClient, userGroupClient group.GroupClient, segmentationClient segment.SegmentationServiceClient, questCacheStorage types3.QuestCacheStorage, questManagerClient manager2.ManagerClient, eventBroker events.Broker, networthClient networth.NetWorthClient) *pay2.Service {
	numberSearchProcessor := impl.NewNumberSearchProcessor(actorClient, upiClient, onboardingClient)
	vpaSearchProcessor := impl.NewVPASearchProcessor(actorClient, upiClient)
	searchProcessorProvider := provider2.NewSearchProcessorProvider(numberSearchProcessor, vpaSearchProcessor)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genconf2)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	client := InitializeQuestSdkClient(questCacheStorage, genconf2, segmentationClient, actorClient, userClient, userGroupClient, questManagerClient, eventBroker)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(userClient)
	service := pay2.NewService(bmsClient, upiOnboardingClient, conf, accountPiClient, savingsClient, genconf2, payClient, connectedAccountClient, searchProcessorProvider, evaluator, actorClient, onboardingClient, client, userAttributesFetcherImpl, networthClient)
	return service
}

func InitialiseSavingsAccountClosureService(conf *genconf.Config, savingsClient savings.SavingsClient, client accrual.AccrualClient, aggregatorClient aggregator2.InvestmentAggregatorClient, loanClient preapprovedloan.PreApprovedLoanClient, rmsClient manager3.RuleManagerClient, accountingClient accounting.AccountingClient, fireflyClient firefly.FireflyClient, balanceClient balance.BalanceClient, ticketClient ticket.TicketClient, usersClient user.UsersClient, operationalStatusClient operstatus.OperationalStatusServiceClient, bcClient bankcust.BankCustomerServiceClient) *saclosure.SavingsAccountClosureService {
	saAccountEvaluator := savings4.NewSaAccountEvaluator(savingsClient, operationalStatusClient)
	benefitsScreenBuilder := screen.NewBenefitsScreenBuilder()
	feedbackScreenBuilder := screen.NewFeedbackScreenBuilder(conf)
	accountFreezeScreenBuilder := screen.NewAccountFreezeScreenBuilder()
	criteriaScreenBuilder := screen.NewCriteriaScreenBuilder()
	panDobScreenBuilder := screen.NewPanDobScreenBuilder()
	submitRequestScreenBuilder := screen.NewSubmitRequestScreenBuilder()
	submitSuccessScreenBuilder := screen.NewSubmitSuccessScreenBuilder(conf)
	feedbackTicketSubmittedScreenBuilder := screen.NewFeedbackTicketSubmittedScreenBuilder(conf)
	pendingChargesScreenBuilder := screen.NewPendingChargesScreenBuilder(conf)
	screenFactory := screen.NewScreenFactory(benefitsScreenBuilder, feedbackScreenBuilder, accountFreezeScreenBuilder, criteriaScreenBuilder, panDobScreenBuilder, submitRequestScreenBuilder, submitSuccessScreenBuilder, feedbackTicketSubmittedScreenBuilder, pendingChargesScreenBuilder)
	deeplinkOrchestrator := orchestrator4.NewDeeplinkOrchestrator(saAccountEvaluator, savingsClient, balanceClient, screenFactory)
	investmentsItemMaker := item.NewInvestmentsItemMaker(conf, aggregatorClient)
	fiCoinsItemMaker := item.NewFiCoinsItemMaker(conf, client)
	preApprovedLoansItemMaker := item.NewPreApprovedLoansItemMaker(conf, loanClient)
	creditCardsItemMaker := item.NewCreditCardsItemMaker(conf, accountingClient, fireflyClient)
	saBalanceItemMaker := item.NewSaBalanceItemMaker(conf, balanceClient, savingsClient)
	autoPayItemMaker := item.NewAutoPayItemMaker(conf, rmsClient)
	operationalStatusItemMaker := item.NewOperationalStatusItemMaker(conf, operationalStatusClient, savingsClient)
	criteriaDataCollectorFactory := item.NewCriteriaDataCollectorFactory(investmentsItemMaker, fiCoinsItemMaker, preApprovedLoansItemMaker, creditCardsItemMaker, saBalanceItemMaker, autoPayItemMaker, operationalStatusItemMaker)
	parallelExecutor := item.NewParallelExecutor(criteriaDataCollectorFactory)
	builder := group2.NewBuilder(parallelExecutor)
	savingsAccountClosureService := saclosure.NewSavingsAccountClosureService(conf, deeplinkOrchestrator, savingsClient, screenFactory, builder, usersClient, saAccountEvaluator, ticketClient, parallelExecutor, bcClient)
	return savingsAccountClosureService
}

func InitialiseIndianStocksService(dynConf *genconf.Config, connectedAccountBeClient connected_account.ConnectedAccountClient, securitiesClient securities.SecuritiesClient, actorClient actor.ActorClient, userClient user.UsersClient, client group.GroupClient) *indianstocks.Service {
	brokerDetailsGetter := broker_details.NewBrokerDetailsGetter(dynConf)
	accountFilterProcessorImpl := filter.NewAccountFilterProcessorImpl(dynConf, brokerDetailsGetter)
	sortByFilterProcessorImpl := filter.NewSortByFilterProcessorImpl()
	defaultTime := datetime.NewDefaultTime()
	dashboardBuilderImpl := dashboard4.NewDashboardBuilderImpl(defaultTime)
	connectedAccountHelper := datafetcher2.NewConnectedAccountHelper(connectedAccountBeClient)
	equityDataFetcher := data_fetcher3.NewEquityDataFetcher(dynConf, securitiesClient, connectedAccountBeClient, connectedAccountHelper)
	etfDataFetcher := data_fetcher3.NewEtfDataFetcher(dynConf, securitiesClient, connectedAccountBeClient, connectedAccountHelper)
	invitDataFetcher := data_fetcher3.NewInvitDataFetcher(dynConf, securitiesClient, connectedAccountBeClient, connectedAccountHelper)
	reitDataFetcher := data_fetcher3.NewReitDataFetcher(dynConf, securitiesClient, connectedAccountBeClient, connectedAccountHelper)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, client)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	dataFetcherFactory := data_fetcher3.NewDataFetcherFactory(equityDataFetcher, etfDataFetcher, invitDataFetcher, reitDataFetcher, evaluator)
	service := indianstocks.NewService(dynConf, connectedAccountBeClient, accountFilterProcessorImpl, sortByFilterProcessorImpl, dashboardBuilderImpl, securitiesClient, dataFetcherFactory, brokerDetailsGetter, actorClient, userClient, evaluator)
	return service
}

func InitializeMediaService(ocrClient ocr.OCRClient, panClient pan.PanClient, conf *genconf.Config, onbClient onboarding.OnboardingClient) *media3.Service {
	service := media3.NewMediaService(ocrClient, panClient, conf, onbClient)
	return service
}

func InitializeCXHomeService(ticketClient ticket.TicketClient, dynConf *genconf.Config, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient group.GroupClient) *home4.Service {
	cx := cxConfigProvider(dynConf)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	inAppContactUsFlowConfig := contactUsConfigProvider(dynConf)
	service := home4.NewCXHomeService(ticketClient, cx, evaluator, userClient, inAppContactUsFlowConfig)
	return service
}

func InitializeKubairService(dynConf *genconf.Config, insightKubairClient kubair.InsightKubairClient) *kubair2.Service {
	service := kubair2.NewService(dynConf, insightKubairClient)
	return service
}

func InitializerInsightSecretsService(dynConf *genconf.Config, userClient user.UsersClient, actorClient actor.ActorClient, userGroupClient group.GroupClient, epfClient epf.EpfClient, creditReportV2Client creditreportv2.CreditReportManagerClient, segmentationClient segment.SegmentationServiceClient, feCategoriserClient categorizer3.TxnCategorizerClient, categorizerBeClient categorizer.TxnCategorizerClient, savingsClient savings.SavingsClient, txnAggregatesClient txnaggregates.TxnAggregatesClient, fireflyClient firefly.FireflyClient, accountingClient accounting.AccountingClient, connectedAccountClient connected_account.ConnectedAccountClient, ccTxnAggClient pinot3.TxnAggregatesClient, investAnalyticsClient investment.InvestmentAnalyticsClient, mfCatalogManagerClient catalog.CatalogManagerClient, networthClient networth.NetWorthClient, mfExternalClient external.MFExternalOrdersClient, redisClient types2.AnalyserRedisStore, consentService consent.ConsentClient, userDeclarationClient user_declaration.ServiceClient, nudgeClient nudge.NudgeServiceClient, onboardingClient onboarding.OnboardingClient, palClient preapprovedloan.PreApprovedLoanClient, variableGeneratorClient variables.VariableGeneratorClient) *secrets2.Service {
	moneySecretsConfig := moneySecretsConfigProvider(dynConf)
	configConfig := config4.NewConfig(moneySecretsConfig)
	footerGeneratorImpl := footer.NewFooterGeneratorImpl(configConfig)
	lockHelperImpl := lock.NewLockHelperImpl(dynConf, nudgeClient, onboardingClient, segmentationClient)
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(dynConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	deeplinkBuilder := deeplink_builder.NewDeeplinkBuilder(dynConf, evaluator)
	epfZeroStateHelper := zero_state.NewEpfZeroStateHelper(userClient, deeplinkBuilder)
	refreshSecretBuilderFactoryImpl := deeplink3.NewRefreshSecretBuilderFactoryImpl(epfClient, mfExternalClient, creditReportV2Client)
	secretRefreshDeeplinkProvider := deeplink3.NewSecretRefreshDeeplinkProvider(networthClient, refreshSecretBuilderFactoryImpl)
	epfSecretUiBuilder := ui2.NewEpfSecretUiBuilder(epfZeroStateHelper, secretRefreshDeeplinkProvider)
	defaultTime := datetime.NewDefaultTime()
	monthlyCompoundedInvestmentCalculatorImpl := calculator.NewMonthlyCompoundedInvestmentCalculatorImpl()
	epfDimensionDataProviderFactoryImpl := valuegenerator.NewEpfDimensionDataProviderFactoryImpl(defaultTime, monthlyCompoundedInvestmentCalculatorImpl)
	epfSecretsBuilder := epf3.NewEpfSecretsBuilder(epfClient, epfZeroStateHelper, epfSecretUiBuilder, defaultTime, epfDimensionDataProviderFactoryImpl)
	userDataProviderImpl := dataproviders.NewUserDataProvider(userClient)
	creditReportUiBuilder := ui3.NewCreditReportUiBuilder(secretRefreshDeeplinkProvider, palClient, userDataProviderImpl)
	creditScoreAnalyserConfig := creditScoreAnalyserConfigProvider(dynConf)
	creditReportFetcher := credit_score.NewCreditReportFetcher(creditScoreAnalyserConfig, defaultTime, evaluator, creditReportV2Client)
	creditScoreParamsFetcherImpl := params_fetcher.NewCreditScoreParamsFetcherImpl(userClient)
	lendingInsightsImpl := loans.NewLendingInsightsImpl(segmentationClient, dynConf)
	creditReportSecretBuilder := credit_report2.NewCreditReportSecretBuilder(dynConf, creditReportUiBuilder, creditReportFetcher, creditScoreParamsFetcherImpl, evaluator, lendingInsightsImpl, creditReportV2Client)
	filterWidgetGeneratorFactoryImpl := factory9.NewFilterWidgetGeneratorFactoryImpl()
	filterProcessor := filter2.NewFilterProcessor(filterWidgetGeneratorFactoryImpl)
	zeroStateHelper := zero_state_helper.NewZeroStateHelper(connectedAccountClient)
	categorySecretUiBuilder := ui4.NewCategorySecretUiBuilder(filterProcessor, zeroStateHelper)
	creditCardHelperImpl := helper4.NewCreditCardHelperImpl(fireflyClient, accountingClient)
	categoryAggregateFetcher := top_categories.NewCategoryAggregateFetcher(txnAggregatesClient, ccTxnAggClient, creditCardHelperImpl, categorizerBeClient)
	categorySecretsBuilder := category2.NewCategorySpendsSecretsBuilder(categorySecretUiBuilder, categoryAggregateFetcher, connectedAccountClient, zeroStateHelper)
	creditReportSecretDataProviderImpl := valuegenerator2.NewCreditReportSecretDataProviderImpl(defaultTime, creditScoreParamsFetcherImpl)
	creditReportSecretUiBuilder := ui5.NewCreditReportSecretUiBuilder(secretRefreshDeeplinkProvider, creditReportSecretDataProviderImpl)
	creditReportSecretsBuilder := credit_report_secret.NewCreditReportSecretsBuilder(creditReportFetcher, creditReportSecretUiBuilder, creditScoreParamsFetcherImpl, creditReportV2Client)
	mfSecretValueGenerator := valuegenerator3.NewMfSecretValueGenerator()
	mfPortfolioSecretUiBuilder := ui6.NewMfPortfolioSecretUiBuilder(mfSecretValueGenerator, secretRefreshDeeplinkProvider)
	mfDataProviderFactoryImpl := dataprovider3.NewMfDataProviderFactory(mfCatalogManagerClient)
	mutualFundCatalogProvider := dataprovider2.NewMutualFundCatalogProvider(mfCatalogManagerClient)
	analyticsCollectorImpl := mutualfunds.NewAnalyticsCollectorImpl(investAnalyticsClient, mfCatalogManagerClient, mutualFundCatalogProvider)
	mfSecretsBuilder := mfportfolio.NewMfSecretsBuilder(mfPortfolioSecretUiBuilder, mfDataProviderFactoryImpl, analyticsCollectorImpl)
	networthCalculatorImpl := valuegenerator4.NewNetworthCalculatorImpl(defaultTime)
	assetsSecretDataProviderImpl := valuegenerator4.NewAssetsSecretDataProvider(defaultTime, networthCalculatorImpl)
	updateMonthlyIncomeBannerBuilder := componentbuilder.NewUpdateMonthlyIncomeBannerBuilder()
	commonUIBuilderFactory := componentbuilder.NewCommonUIBuilderFactory(updateMonthlyIncomeBannerBuilder)
	assetsSecretUiBuilder := ui7.NewAssetsSecretUiBuilder(assetsSecretDataProviderImpl, commonUIBuilderFactory, deeplinkBuilder)
	config5 := config3.LoadNetWorthConfig(dynConf)
	assetsSecretsBuilder := assets.NewAssetsSecretsBuilder(assetsSecretUiBuilder, connectedAccountClient, networthClient, userClient, config5, zeroStateHelper, variableGeneratorClient)
	insightsParams := insightsConfigProvider(dynConf)
	client := types2.AnalyserRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	epfSMSAttributesStore := epf4.NewEpfSMSAttributesStore(redisCacheStorage)
	epfSmsSecretUiBuilder := ui8.NewEpfSmsSecretUiBuilder()
	epfSmsSecretBuilder := epf_sms.NewEpfSmsSecretBuilder(insightsParams, epfSMSAttributesStore, epfSmsSecretUiBuilder)
	commonVariableGeneratorAdapter := variablegenerator.NewCommonVariableGeneratorAdapter(variableGeneratorClient)
	variableGeneratorImpl := variablegenerator.NewVariableGeneratorImpl(commonVariableGeneratorAdapter)
	componentBuilderFactory := ui9.NewComponentBuilderFactory(commonUIBuilderFactory, monthlyCompoundedInvestmentCalculatorImpl, defaultTime)
	mfHistorySecretBuilder := mfhistory.NewMfHistorySecretBuilder(investAnalyticsClient, defaultTime, variableGeneratorImpl, componentBuilderFactory)
	variablegeneratorVariableGeneratorImpl := variablegenerator2.NewVariableGeneratorImpl()
	uiComponentBuilderFactory := ui10.NewComponentBuilderFactory(dynConf)
	mfPerformanceSecretBuilder := mfperformance.NewMfPerformanceSecretBuilder(analyticsCollectorImpl, variablegeneratorVariableGeneratorImpl, uiComponentBuilderFactory)
	componentBuilderFactory2 := ui11.NewComponentBuilderFactory()
	salaryReportSecretBuilder := salary_report.NewSalaryReportSecretBuilder(componentBuilderFactory2, zeroStateHelper, variableGeneratorClient)
	secretBuilderFactory := factory15.NewSecretBuilderFactory(configConfig, footerGeneratorImpl, lockHelperImpl, epfSecretsBuilder, creditReportSecretBuilder, categorySecretsBuilder, creditReportSecretsBuilder, mfSecretsBuilder, assetsSecretsBuilder, epfSmsSecretBuilder, mfHistorySecretBuilder, mfPerformanceSecretBuilder, salaryReportSecretBuilder)
	accountsHelperImpl := helper4.NewAccountHelperImpl(connectedAccountClient, savingsClient, actorClient)
	filterProcessorFactoryImpl := factory6.NewFilterProcessorFactoryImpl(feCategoriserClient, categorizerBeClient, evaluator, accountsHelperImpl, creditCardHelperImpl)
	filterValueGeneratorImpl := generator.NewFilterValueGeneratorImpl(filterProcessorFactoryImpl)
	mfDataProvider := wealth_analyser_report.NewMfDataProvider(investAnalyticsClient, secretRefreshDeeplinkProvider, networthClient)
	epfDataProvider := wealth_analyser_report.NewEpfDataProvider(epfClient, secretRefreshDeeplinkProvider)
	assetDataProvider := wealth_analyser_report.NewAssetDataProvider(networthClient, deeplinkBuilder)
	wealthAnalyserDataProviderImpl := wealth_analyser_report.NewWealthAnalyserReport(mfDataProvider, epfDataProvider, assetDataProvider)
	secretProvider := secret_provider.NewSecretSummariesImpl(configConfig, defaultTime, filterValueGeneratorImpl, secretBuilderFactory, investAnalyticsClient, evaluator)
	collectionProcessorFactoryImpl := processor3.NewCollectionProcessorFactoryImpl(configConfig, secretBuilderFactory, secretProvider)
	assetWiseDistributionFactory := assetwisedistributionbuilder.NewAssetWiseDistributionFactory(dynConf, evaluator)
	portfolioTrackerFactory := portfoliotrackerui.NewPortfolioTrackerFactory(assetWiseDistributionFactory, dynConf, evaluator)
	dailyTracker := strategy.NewDailyTracker(evaluator, dynConf)
	weeklyTracker := strategy.NewWeeklyTracker()
	portfolioTrackerBuilder := portfoliotrackerbuilder.NewPortfolioTrackerBuilder(variableGeneratorClient, portfolioTrackerFactory, evaluator, dailyTracker, weeklyTracker)
	service := secrets2.NewService(configConfig, secretBuilderFactory, filterValueGeneratorImpl, wealthAnalyserDataProviderImpl, epfClient, networthClient, creditReportV2Client, connectedAccountClient, evaluator, investAnalyticsClient, nudgeClient, defaultTime, collectionProcessorFactoryImpl, secretProvider, onboardingClient, userGroupClient, actorClient, deeplinkBuilder, variableGeneratorClient, portfolioTrackerBuilder, assetWiseDistributionFactory, dailyTracker, weeklyTracker)
	return service
}

func InitializeVKYCCallService(dynConf *genconf.Config, vkycCallClientEpifiTech pkg2.VkycCallClientToOnboardingServer, vkycCallClientStockguardian pkg2.VkycCallClientToSGApiGatewayServer, obfuscatorClient obfuscator.ObfuscatorClient, locationClient location.LocationClient) (*vkyccall.Service, error) {
	vkycCall := VKYCCallConfigProvider(dynConf)
	vKycCallClientWrapper := pkg2.NewVkycCallClientWrapper(vkycCallClientEpifiTech, vkycCallClientStockguardian)
	service := vkyccall.NewService(dynConf, vkycCall, vKycCallClientWrapper, obfuscatorClient, locationClient)
	return service, nil
}

func InitializeUqudoService(dynConf *genconf.Config, kycUqudoClient uqudo.UqudoClient, docExtClient docs.DocExtractionClient) *uqudo2.Service {
	service := uqudo2.NewService(kycUqudoClient, docExtClient)
	return service
}

func InitializeJourneyService(dynConf *genconf.Config, journeyClient journey.JourneyServiceClient) *journey2.Service {
	service := journey2.NewService(dynConf, journeyClient)
	return service
}

func InitializeInAppHelpActorActivityService(genConf *genconf.Config, cxActorActivityClient actor_activity.ActorActivityClient, actorClient actor.ActorClient, usersClient user.UsersClient, userGroupClient group.GroupClient) *actor_activity2.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := actor_activity2.NewService(genConf, evaluator, cxActorActivityClient)
	return service
}

func InitialiseSmsFetcherService(redisClient types2.AnalyserRedisStore, consentService consent.ConsentClient, epfClient epf.EpfClient) *smsfetcher.Service {
	client := types2.AnalyserRedisStoreRedisClientProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	epfSMSAttributesStore := epf4.NewEpfSMSAttributesStore(redisCacheStorage)
	service := smsfetcher.NewService(epfSMSAttributesStore, consentService, epfClient)
	return service
}

func InitialiseDataSharingService(dataSharingClient datasharing.DataSharingClient) *datasharing2.Service {
	service := datasharing2.NewService(dataSharingClient)
	return service
}

func InitialiseSalaryEstimationService(salaryEstimationClient salaryestimation.SalaryEstimationClient, consentClient consent.ConsentClient, eventBroker events.Broker) *salaryestimation2.Service {
	service := salaryestimation2.NewService(salaryEstimationClient, consentClient, eventBroker)
	return service
}

func InitializeDigilockerService(conf *config.Config) *digilocker.Service {
	service := digilocker.NewService(conf)
	return service
}

func InitializeStockguardianMatrixService(conf *config.Config) *matrix.Service {
	service := matrix.NewService(conf)
	return service
}

func InitializeTotpService(client totp.TotpClient) *totp2.Service {
	service := totp2.NewService(client)
	return service
}

// wire.go:

func BKYCQRConfigProvider(gconf *genconf.Config) *genconf2.QRCode {
	return gconf.BKYC().QRConfig()
}

func QuestSDKClientConfProvider(conf *genconf.Config) *genconf3.Config {
	return conf.QuestSdk()
}

func QuestCacheStorageProvider(cacheStorage types3.QuestCacheStorage) cache.CacheStorage {
	return cacheStorage
}

func CcDashboardWarningSvcProvider(ffAccountingClient accounting.AccountingClient, ffBeClient firefly.FireflyClient) types2.CcDashboardWarningSvc {
	return warning.NewService(ffAccountingClient, ffBeClient)
}

func PlDashboardWarningSvcProvider(
	palClient preapprovedloan.PreApprovedLoanClient,
	conf *genconf.Config,
	rpcHelper helper.IRpcHelper,
) types2.PlDashboardWarningSvc {
	return dashboard5.NewDashboardWarningService(
		palClient,
		conf,
		rpcHelper,
	)
}

func PlNavigationBarHighlightSvcProvider(
	palClient preapprovedloan.PreApprovedLoanClient,
	conf *genconf.Config,
	rpcHelper helper.IRpcHelper,
	segmentationClient segment.SegmentationServiceClient,
	onboardingClient onboarding.OnboardingClient,
	savingsClient savings.SavingsClient,
) types2.PlNavigationBarHighlightSvc {
	return navigation.NewNavigationBarHighlightService(palClient, conf, rpcHelper, segmentationClient)
}

func USSNavigationBarHighlightSvcProvider(conf *genconf.Config) types2.USSNavigationBarHighlightSvc {
	return usstocks2.NewNavigationBarHighlightService(conf)
}

func RewardsNavigationBarHighlightSvcProvider(conf *genconf.Config, rewardsGeneratorClient rewards.RewardsGeneratorClient) types2.RewardsNavigationBarHighlightSvc {
	return navigation2.NewNavigationBarHighlightService(conf, rewardsGeneratorClient)
}

func CommsConfigProvider(conf *config.Config) *config.Comms { return conf.Comms }

func contactUsConfigProvider(dynConf *genconf.Config) *genconf.InAppContactUsFlowConfig {
	return dynConf.InAppContactUsFlowConfig()
}

func NewInMemoryCacheStorage() types2.RewardsCacheStorage {
	return cache.NewInMemoryCacheService()
}

func getDisputeConf(genConf *genconf.Config) *genconf.Dispute {
	return genConf.Dispute()
}

func FlagsProvider(conf *config.Config) *config.Flags { return conf.Flags }

func ReferralConfigProvider(conf *config.Config) *config.Referrals { return conf.Referrals }

func ReferralsColourMapProvider(conf *config.Config) *config.ReferralsColourMap {
	return conf.ReferralsColourMap
}

func getPreApprovedLoanConfig(conf *genconf.Config) *genconf.PreApprovedLoan {
	return conf.Lending().PreApprovedLoan()
}

func getLendingConfig(conf *genconf.Config) *genconf.Lending {
	return conf.Lending()
}

func creditScoreAnalyserConfigProvider(cfg *genconf.Config) *genconf.CreditScoreAnalyserConfig {
	return cfg.AnalyserParams().CreditScoreAnalyserConfig()
}

func AnalyserFeatureFlagProvider(conf *config.Config) *cfg.FeatureReleaseConfig {
	return conf.Flags.AnalyserFeatureFlag
}

func CreditReportCommonConfigProvider(conf *genconf.Config) *genconf4.CreditReportConfig {
	return conf.CreditReportConfig()
}

func RewardsFrontendMetaProvider(conf *config.Config) *config.RewardsFrontendMeta {
	return conf.RewardsFrontendMeta
}

func ScreeningConfigProvider(conf *config.Config) *config.Screening { return conf.Screening }

func ConnectedAccountConfigProvider(conf *config.Config) *config.ConnectedAccount {
	return conf.ConnectedAccount
}

func TxnCatParamsProvider(conf *config.Config) *config.TxnCatParams { return conf.TxnCatParams }

func SimilarActivityParamsProvider(conf *config.Config) *config.SimilarActivityParams {
	return conf.SimilarActivityParams
}

func CxProvider(conf *config.Config) *config.Cx { return conf.Cx }

func HomeNudgeParamsProvider(conf *config.Config) *config.HomeNudgeParams {
	return conf.HomeRevampParams.HomeNudgeParams
}

func NetWorthDashboardConfigProvider(config5 *config3.Config) *networth2.NetWorthDashboardConfig {
	networthDashboardConfig, err := config5.GetNetworthDashboardConfig()
	if err != nil {
		logger.Panic("failed to get networth dashboard config", zap.Error(err))
	}
	return networthDashboardConfig
}

func usStocksConfigProvider(dynConf *genconf.Config) *genconf.USStocks {
	return dynConf.USStocks()
}

func cxConfigProvider(dynConf *genconf.Config) *genconf.Cx {
	return dynConf.Cx()
}

func moneySecretsConfigProvider(conf *genconf.Config) *genconf.MoneySecretsConfig {
	return conf.MoneySecretsConfig()
}

func insightsConfigProvider(conf *genconf.Config) *genconf.InsightsParams {
	return conf.InsightsParams()
}

func VKYCCallConfigProvider(conf *genconf.Config) *genconf.VKYCCall {
	return conf.VKYCCall()
}
