package servergenhook

import (
	"context"
	"fmt"
	"net/http"

	"golang.org/x/sync/errgroup" // nolint:depguard
	"google.golang.org/grpc"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/gamma/httpgw/config"

	"github.com/epifi/be-common/pkg/epifiserver"
	"github.com/epifi/be-common/pkg/logger"

	// MCP imports
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

// createMCPServer creates a basic MCP server for POC
func createMCPServer() *server.MCPServer {
	mcpServer := server.NewMCPServer(
		"Epifi HTTP MCP Server",
		"1.0.0",
		server.WithToolCapabilities(true),
	)

	// Add a server info tool
	serverInfoTool := mcp.NewTool("server_info",
		mcp.WithDescription("Get information about the Http server"),
	)
	mcpServer.AddTool(serverInfoTool, func(ctx context.Context, req mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		info := map[string]interface{}{
			"service":      "httpgw",
			"version":      "1.0.0",
			"status":       "running",
			"capabilities": []string{""},
		}
		return mcp.NewToolResultText("Server Info: " + fmt.Sprintf("%+v", info)), nil
	})

	return mcpServer
}

// nolint:funlen,ineffassign,staticcheck
func StartHttpGWServer(s *grpc.Server, ctx context.Context, initNotifier chan<- cfg.ServerName) (func(), error) {
	ctx, cancel := context.WithCancel(ctx)
	cleanupFn := func() { cancel() }

	conf, err := config.Load()
	if err != nil {
		return cleanupFn, err
	}

	g, grpCtx := errgroup.WithContext(context.Background())

	httpMux := http.NewServeMux()
	epifiserver.RegisterHealthCheckEndpoint(httpMux, conf.Application.Name)

	// Create and mount MCP server using streamable HTTP transport
	mcpServer := createMCPServer()

	// Configure streamable HTTP server with proper endpoints
	streamableServer := server.NewStreamableHTTPServer(mcpServer,
		server.WithEndpointPath("/stream"),
	)

	// Mount MCP server using StripPrefix to handle routing correctly
	logger.InfoNoCtx("Mounting MCP server with streamable HTTP at /mcp/")

	httpMux.Handle("/mcp/", streamableServer)

	// Add a test handler to verify the path works
	httpMux.HandleFunc("/mcp/test", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		_, _ = w.Write([]byte(`{"status": "MCP streamable HTTP server is working", "path": "/mcp/test", "transport": "streamable-http"}`))
	})

	logger.InfoNoCtx("MCP streamable HTTP server mounted successfully")

	httpServer := epifiserver.NewHttpServer(
		conf.Server.Port, httpMux,
	)
	httpServer.ReadTimeout = conf.HttpServer.ReadTimeout
	httpServer.ReadHeaderTimeout = conf.HttpServer.ReadHeaderTimeout
	httpClosure := epifiserver.StartHttpServer(grpCtx, g, httpServer)

	healthCheckClosure := func() {}
	// skip http servers since all the use-cases with this is not required for TEST_TENANT setup where
	// all servers are hosted in the same instance for short time for testing purpose.
	if !(cfg.IsTestTenantEnabled() || cfg.IsRemoteDebugEnabled()) {
		// instantiate health check server
		healthCheckServer := epifiserver.NewHttpServer(conf.Server.HealthCheckPort, http.DefaultServeMux)
		epifiserver.RegisterHealthCheckEndpoint(http.DefaultServeMux, conf.Application.Name)
		epifiserver.RegisterMonitoringEndpoint(http.DefaultServeMux)
		healthCheckClosure = epifiserver.StartHttpServer(grpCtx, g, healthCheckServer)
		epifiserver.RegisterLogLevelEndpoint(http.DefaultServeMux)
	}
	initNotifier <- cfg.HTTP_GATEWAY_SERVER
	// block till we get sig term or one of server crashes
	epifiserver.HandleGracefulShutdown(grpCtx, g, httpClosure, healthCheckClosure)
	return cleanupFn, nil
}
