//nolint:gocritic
package consumer

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	caEnumsPb "github.com/epifi/gamma/api/connected_account/enums"
	caxPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/api/insights/consumer"
	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	aaHelper "github.com/epifi/gamma/insights/networth/assets/helper/aa"
)

// ProcessCaNewDataFetchEvent RPC will receive events from NPS, Indian Stocks and Deposits(if synced in last 36 HRs) CAs, handle accordingly
func (s *Service) ProcessCaNewDataFetchEvent(ctx context.Context, event *caxPb.AccountDataSyncEvent) (*consumer.ProcessCaNewDataFetchEventResponse, error) {
	actorId := event.GetActorId()
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	logger.Debug(ctx, "Collected ca account data sync event", zap.String(logger.ACC_INSTRUMENT_TYPE, event.GetAccInstrumentType().String()))
	// first level filtering since we only need indian securities/nps data for now
	// event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF &&
	// event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT &&
	// event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_NPS &&
	// event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT
	if event.GetAccInstrumentType() != caEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES {
		return &consumer.ProcessCaNewDataFetchEventResponse{
			ResponseHeader: success,
		}, nil
	}
	// get asset type from instrument type
	assetType := aaHelper.GetAssetTypeForInstrumentType(event.GetAccInstrumentType())
	if assetType == enumsPb.AssetType_ASSET_TYPE_UNSPECIFIED {
		logger.Error(ctx, "mapping not found for account instrument type", zap.String(logger.ACC_INSTRUMENT_TYPE, event.GetAccInstrumentType().String()))
		return &consumer.ProcessCaNewDataFetchEventResponse{
			ResponseHeader: permanentFailure,
		}, nil
	}
	// get account holdings for given asset type for an actor
	assetIdsUnitsPairs, getPairsErr := s.aaBalanceFetcher.GetAaAccountHoldings(ctx, event.GetActorId(), assetType)
	if getPairsErr != nil {
		if errors.Is(getPairsErr, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "no holdings found for actor for given asset type", zap.String(logger.ASSET_TYPE, assetType.String()))
			return &consumer.ProcessCaNewDataFetchEventResponse{
				ResponseHeader: success,
			}, nil
		}
		logger.Error(ctx, "error getting holdings", zap.Error(getPairsErr), zap.String(logger.ASSET_TYPE, assetType.String()))
		return &consumer.ProcessCaNewDataFetchEventResponse{
			ResponseHeader: transientFailure,
		}, nil
	}
	if len(assetIdsUnitsPairs) == 0 {
		logger.Debug(ctx, "no holdings found for actor for given asset type", zap.String(logger.ASSET_TYPE, assetType.String()))
		return &consumer.ProcessCaNewDataFetchEventResponse{
			ResponseHeader: success,
		}, nil
	}
	// store snapshot
	assetData, getAssetDataErr := s.getAssetData(ctx, assetType, assetIdsUnitsPairs)
	if getAssetDataErr != nil {
		logger.Error(ctx, "error getting asset data", zap.Error(getAssetDataErr), zap.String(logger.ASSET_TYPE, assetType.String()))
		if errors.Is(getAssetDataErr, epifierrors.ErrRecordNotFound) {
			return &consumer.ProcessCaNewDataFetchEventResponse{
				ResponseHeader: permanentFailure,
			}, nil
		}
		return &consumer.ProcessCaNewDataFetchEventResponse{
			ResponseHeader: transientFailure,
		}, nil
	}
	assetHistory := &modelPb.AssetHistory{
		AssetType:   assetType,
		Data:        assetData,
		HistoryDate: timestampPb.Now(),
		ActorId:     actorId,
	}
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_WEALTH)
	_, createOrUpdateErr := s.assetHistoryDao.CreateOrUpdateMultipleHistories(ctx, []*modelPb.AssetHistory{assetHistory})
	if createOrUpdateErr != nil {
		logger.Error(ctx, "error creating or updating asset history", zap.Error(createOrUpdateErr))
		return &consumer.ProcessCaNewDataFetchEventResponse{
			ResponseHeader: transientFailure,
		}, nil
	}
	return &consumer.ProcessCaNewDataFetchEventResponse{
		ResponseHeader: success,
	}, nil
}

func (s *Service) getAssetData(ctx context.Context, assetType enumsPb.AssetType, unitPairs []*aaHelper.AssetIdUnitsPair) (*modelPb.AssetData, error) {
	switch assetType {
	case enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES:
		indianStocks, getIndianStocksErr := s.getIndianStocksFromUnitPairs(ctx, unitPairs)
		if getIndianStocksErr != nil {
			return nil, errors.Wrap(getIndianStocksErr, "error getting indian stocks")
		}
		return &modelPb.AssetData{
			AssetData: &modelPb.AssetData_IndianStocksData{
				IndianStocksData: &modelPb.IndianStocksData{
					IndianStocks: indianStocks,
				},
			},
		}, nil
	default:
		return nil, fmt.Errorf("unhandled asset type: %s", assetType.String())
	}
}

// getIndianStocksFromUnitPairs returns indian stocks from given assetId unit pairs
// fetches security listing ids from catalog from both NSE and BSE and in response includes BSE if exists, others includes NSE, or none, if it doesn't exist in both
func (s *Service) getIndianStocksFromUnitPairs(ctx context.Context, unitPairs []*aaHelper.AssetIdUnitsPair) ([]*modelPb.IndianStock, error) {
	isinToStockMap := make(map[string]*modelPb.IndianStock)
	isinExchangePairs := make([]*catalogPb.ISINExchangePair, 0)
	for _, pair := range unitPairs {
		isinToStockMap[pair.AssetId] = &modelPb.IndianStock{
			Units: float64(pair.Units),
		}
		isinExchangePairs = append(isinExchangePairs, &catalogPb.ISINExchangePair{
			Isin:     pair.AssetId,
			Exchange: catalogPb.Exchange_EXCHANGE_INDIA_NSE,
		})
		isinExchangePairs = append(isinExchangePairs, &catalogPb.ISINExchangePair{
			Isin:     pair.AssetId,
			Exchange: catalogPb.Exchange_EXCHANGE_INDIA_BSE,
		})
	}
	secListingByIsinResp, getSecListingErr := s.catalogClient.GetSecListingIdsByISINs(ctx, &catalogPb.GetSecListingIdsByISINsRequest{
		IsinExchangePairs: isinExchangePairs,
	})
	if rpcErr := epifigrpc.RPCError(secListingByIsinResp, getSecListingErr); rpcErr != nil {
		if secListingByIsinResp.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("no security listings found for given isinExchangePairs %s: %w", isinExchangePairs, epifierrors.ErrRecordNotFound)
		}
		return nil, errors.Wrap(rpcErr, "error getting security listing ids from catalog")
	}
	// first filling NSE
	for _, secListing := range secListingByIsinResp.GetSecListingIdsWithIsins() {
		if secListing.GetIsinExchangePair().GetExchange() == catalogPb.Exchange_EXCHANGE_INDIA_NSE {
			isinToStockMap[secListing.GetIsinExchangePair().GetIsin()].SecurityListingId = secListing.GetSecurityListingExternalId()
		}
	}
	// then overwrite with BSE
	for _, secListing := range secListingByIsinResp.GetSecListingIdsWithIsins() {
		if secListing.GetIsinExchangePair().GetExchange() == catalogPb.Exchange_EXCHANGE_INDIA_BSE {
			isinToStockMap[secListing.GetIsinExchangePair().GetIsin()].SecurityListingId = secListing.GetSecurityListingExternalId()
		}

	}
	finalStocks := make([]*modelPb.IndianStock, 0)
	for _, stock := range isinToStockMap {
		// this can be empty if catalog returns empty for partial data
		if stock.GetSecurityListingId() != "" {
			finalStocks = append(finalStocks, stock)
		}
	}
	return finalStocks, nil
}
