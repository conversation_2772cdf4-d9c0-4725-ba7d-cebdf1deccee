package processor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/insights/developer"
	"github.com/epifi/gamma/api/insights/networth/enums"
	"github.com/epifi/gamma/insights/networth/dao"
)

const (
	ActorId        = "actor_id"
	AssetType      = "asset_type"
	HistoryDate    = "history_date"
	AssetHistoryId = "asset_history_id"
)

var (
	ownerships = []commontypes.Ownership{
		commontypes.Ownership_EPIFI_TECH,
		commontypes.Ownership_EPIFI_WEALTH,
		commontypes.Ownership_FEDERAL_BANK,
	}
)

type AssetHistory struct {
	assetHistoryDao dao.AssetHistoryDao
}

func NewAssetHistory(assetHistoryDao dao.AssetHistoryDao) *AssetHistory {
	return &AssetHistory{
		assetHistoryDao: assetHistoryDao,
	}
}

func (a *AssetHistory) FetchParamList(ctx context.Context, entity developer.InsightsEntity) ([]*db_state.ParameterMeta, error) {
	// Create asset type options for multi-select dropdown
	var assetTypeOptions []string
	for name := range enums.AssetType_value {
		// Skip UNSPECIFIED type
		if name == "ASSET_TYPE_UNSPECIFIED" {
			continue
		}
		assetTypeOptions = append(assetTypeOptions, name)
	}

	return []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           "Actor Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            AssetType,
			Label:           "Asset Type",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
			Options:         assetTypeOptions,
		},
		{
			Name:            HistoryDate,
			Label:           "History Date",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            AssetHistoryId,
			Label:           "Asset History ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}, nil
}

func (a *AssetHistory) FetchData(ctx context.Context, entity developer.InsightsEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", fmt.Errorf("filter cannot be empty to fetch asset history")
	}

	var (
		actorId        string
		assetTypes     []string
		historyDate    *timestampPb.Timestamp
		assetHistoryId string
	)

	// Parse filters
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			actorId = filter.GetStringValue()
		case AssetType:
			if filter.GetMultiSelectDropdownFilter() != nil {
				assetTypes = filter.GetMultiSelectDropdownFilter().GetDropdownValues()
			}
		case HistoryDate:
			if filter.GetTimestamp() != nil {
				historyDate = filter.GetTimestamp()
			}
		case AssetHistoryId:
			assetHistoryId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("unknown filter applied to get db state")
		}
	}

	if assetHistoryId != "" {
		return a.getEntityByID(ctx, assetHistoryId)
	}

	if actorId == "" {
		return "", fmt.Errorf("either asset history ID or actor ID is required")
	}

	return a.getEntityByActorId(ctx, actorId, assetTypes, historyDate)
}

func (a *AssetHistory) maskAssetHistoryData(history *modelPb.AssetHistory) {
	if history.Data == nil {
		return
	}

	switch data := history.Data.GetAssetData().(type) {
	case *modelPb.AssetData_MfData:
		for _, scheme := range data.MfData.GetMfSchemes() {
			scheme.Units = float64(mask.GetMaskedFloat(scheme.Units))
		}
	case *modelPb.AssetData_IndianStocksData:
		for _, stock := range data.IndianStocksData.GetIndianStocks() {
			stock.Units = float64(mask.GetMaskedFloat(stock.Units))
		}
	case *modelPb.AssetData_NpsData:
		for _, scheme := range data.NpsData.GetNpsSchemes() {
			scheme.Units = float64(mask.GetMaskedFloat(scheme.Units))
		}
	}
}

func (a *AssetHistory) getEntityByID(ctx context.Context, id string) (string, error) {
	// Map to store results by ownership
	resultsByOwnership := make(map[string]*modelPb.AssetHistory)

	// Fetch data for each ownership
	for _, ownership := range ownerships {
		ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)
		history, err := a.assetHistoryDao.Get(ctxWithOwnership, id)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				continue
			}
			logger.Error(ctx, "error fetching asset history by ID", zap.String("assetHistoryId", id), zap.String("ownership", ownership.String()), zap.Error(err))
			return "", fmt.Errorf("error fetching asset history by ID for ownership %s: %w", ownership.String(), err)
		}

		if history != nil {
			a.maskAssetHistoryData(history)
			resultsByOwnership[ownership.String()] = history
		}
	}

	if len(resultsByOwnership) == 0 {
		response := fmt.Sprintf("no asset history found for ID: %s", id)
		logger.Info(ctx, response, zap.String("assetHistoryId", id))
		return response, nil
	}

	jsonData, err := json.Marshal(resultsByOwnership)
	if err != nil {
		logger.Error(ctx, "error marshaling asset history to JSON", zap.String("assetHistoryId", id), zap.Error(err))
		return "", fmt.Errorf("error marshaling asset history to JSON: %w", err)
	}

	return string(jsonData), nil
}

func (a *AssetHistory) getEntityByActorId(ctx context.Context, actorId string, assetTypeStrings []string, historyDate *timestampPb.Timestamp) (string, error) {
	// Prepare asset types
	var assetTypes []enums.AssetType
	if len(assetTypeStrings) > 0 {
		assetTypes = make([]enums.AssetType, 0, len(assetTypeStrings))
		for _, assetTypeStr := range assetTypeStrings {
			assetTypeEnum := enums.AssetType(enums.AssetType_value[assetTypeStr])
			if assetTypeEnum == enums.AssetType_ASSET_TYPE_UNSPECIFIED {
				logger.Error(ctx, "invalid asset type", zap.String("actorId", actorId), zap.String("assetType", assetTypeStr))
				return "", fmt.Errorf("invalid asset type: %s", assetTypeStr)
			}
			assetTypes = append(assetTypes, assetTypeEnum)
		}
	} else {
		// Get all available asset types from the enum
		assetTypes = make([]enums.AssetType, 0, len(enums.AssetType_value))
		for name, value := range enums.AssetType_value {
			if name == "ASSET_TYPE_UNSPECIFIED" {
				continue
			}
			assetTypes = append(assetTypes, enums.AssetType(value))
		}
	}

	// Use current date if history date not specified
	if historyDate == nil {
		historyDate = timestampPb.New(time.Now())
	}

	// Map to store results by ownership
	resultsByOwnership := make(map[string]map[enums.AssetType]*modelPb.AssetHistory)

	// Fetch data for each ownership
	for _, ownership := range ownerships {
		ctxWithOwnership := epificontext.WithOwnership(ctx, ownership)
		assetHistories, err := a.assetHistoryDao.GetMultipleHistoriesByDate(ctxWithOwnership, actorId, assetTypes, historyDate)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				response := fmt.Sprintf("no asset histories found for actor: %s, ownership: %s, assetTypes: %v, historyDate: %v", actorId, ownership.String(), assetTypeStrings, historyDate)
				logger.Info(ctx, response, zap.String("actorId", actorId), zap.String("ownership", ownership.String()), zap.Strings("assetTypes", assetTypeStrings), zap.String("historyDate", historyDate.String()))
				continue
			}
			logger.Error(ctx, "error fetching asset histories", zap.String("actorId", actorId), zap.String("ownership", ownership.String()), zap.Strings("assetTypes", assetTypeStrings), zap.String("historyDate", historyDate.String()), zap.Error(err))
			return "", fmt.Errorf("error fetching asset histories for ownership %s: %w", ownership.String(), err)
		}

		if len(assetHistories) > 0 {
			// Mask data in each history
			for _, history := range assetHistories {
				a.maskAssetHistoryData(history)
			}
			resultsByOwnership[ownership.String()] = assetHistories
		}
	}

	if len(resultsByOwnership) == 0 {
		response := fmt.Sprintf("no asset histories found for actorId: %s, assetTypes: %v, historyDate: %v", actorId, assetTypeStrings, historyDate)
		logger.Info(ctx, response, zap.String("actorId", actorId), zap.Strings("assetTypes", assetTypeStrings), zap.String("historyDate", historyDate.String()))
		return response, nil
	}

	jsonData, err := json.Marshal(resultsByOwnership)
	if err != nil {
		logger.Error(ctx, "error marshaling asset histories to JSON", zap.String("actorId", actorId), zap.Error(err))
		return "", fmt.Errorf("error marshaling asset histories to JSON: %w", err)
	}

	return string(jsonData), nil
}
