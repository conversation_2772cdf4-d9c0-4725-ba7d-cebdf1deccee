package assets

//go:generate mockgen -source=factory.go -destination=./mocks/mock_factory.go package=mocks

import (
	"context"
	"fmt"

	epfHelper "github.com/epifi/gamma/insights/networth/assets/helper/epf"
	"github.com/epifi/gamma/insights/networth/assets/helper/mutualfund"
	"github.com/epifi/gamma/insights/networth/assets/indian_securities"
	"github.com/epifi/gamma/insights/networth/assets/manual_asset"
	"github.com/epifi/gamma/insights/networth/assets/nps"
	"github.com/epifi/gamma/insights/networth/assets/usstocks"

	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/datetime"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	investAnalyserPb "github.com/epifi/gamma/api/analyser/investment"
	epfPb "github.com/epifi/gamma/api/insights/epf"
	netWorthPb "github.com/epifi/gamma/api/insights/networth"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/insights/networth/aggregator"
	"github.com/epifi/gamma/insights/networth/assets/epf"
	"github.com/epifi/gamma/insights/networth/assets/fixed_deposit"
	aaHelper "github.com/epifi/gamma/insights/networth/assets/helper/aa"
	investmentHelper "github.com/epifi/gamma/insights/networth/assets/helper/investment"
	"github.com/epifi/gamma/insights/networth/assets/mutual_fund"
	"github.com/epifi/gamma/insights/networth/assets/p2p_lending"
	"github.com/epifi/gamma/insights/networth/assets/savings_account"
	investmentDeclaration "github.com/epifi/gamma/insights/networth/investment_declaration"
)

var WireAssertValueAggregatorFactorySet = wire.NewSet(NewAssertValueAggregatorFactory, wire.Bind(new(aggregator.IValueAggregatorFactory[netWorthPb.AssetType, *netWorthPb.AssetValue]), new(*AssertValueAggregatorFactory)))

type AssertValueAggregatorFactory struct {
	datetime                       datetime.Time
	savingsClient                  savingsPb.SavingsClient
	aaBalanceFetcher               aaHelper.IAaBalanceFetcher
	investAnalyticsClient          investAnalyserPb.InvestmentAnalyticsClient
	investmentAggregatorHelper     investmentHelper.IInvestmentAggregatorHelper
	epfAggregatorHelper            epfHelper.IEpfAggregatorHelper
	mfAggregatorHelper             mutualfund.IMfAggregatorHelper
	epfClient                      epfPb.EpfClient
	investmentDeclarationProcessor investmentDeclaration.IProcessor
	paySavingsBalanceClient        accountBalancePb.BalanceClient
}

func NewAssertValueAggregatorFactory(
	datetime datetime.Time,
	savingsClient savingsPb.SavingsClient,
	aaBalanceFetcher aaHelper.IAaBalanceFetcher,
	investAnalyticsClient investAnalyserPb.InvestmentAnalyticsClient,
	investmentAggregatorHelper investmentHelper.IInvestmentAggregatorHelper,
	epfClient epfPb.EpfClient,
	epfAggregatorHelper epfHelper.IEpfAggregatorHelper,
	mfAggregatorHelper mutualfund.IMfAggregatorHelper,
	investmentDeclarationProcessor investmentDeclaration.IProcessor,
	paySavingsBalanceClient accountBalancePb.BalanceClient,
) *AssertValueAggregatorFactory {
	return &AssertValueAggregatorFactory{
		datetime:                       datetime,
		savingsClient:                  savingsClient,
		aaBalanceFetcher:               aaBalanceFetcher,
		investAnalyticsClient:          investAnalyticsClient,
		investmentAggregatorHelper:     investmentAggregatorHelper,
		epfClient:                      epfClient,
		epfAggregatorHelper:            epfAggregatorHelper,
		mfAggregatorHelper:             mfAggregatorHelper,
		investmentDeclarationProcessor: investmentDeclarationProcessor,
		paySavingsBalanceClient:        paySavingsBalanceClient,
	}
}

func (f *AssertValueAggregatorFactory) GetValueAggregator(ctx context.Context, assetType netWorthPb.AssetType) (aggregator.IValueAggregator[*netWorthPb.AssetValue], error) {
	switch assetType {
	case netWorthPb.AssetType_ASSET_TYPE_SAVINGS_ACCOUNTS:
		return savings_account.NewSavingsAccountAggregator(f.savingsClient, f.aaBalanceFetcher, f.paySavingsBalanceClient), nil
	case netWorthPb.AssetType_ASSET_TYPE_MUTUAL_FUND:
		return mutual_fund.NewMutualFundAggregator(f.mfAggregatorHelper), nil
	case netWorthPb.AssetType_ASSET_TYPE_P2P_LENDING:
		return p2p_lending.NewP2PLendingAggregator(f.investmentAggregatorHelper), nil
	case netWorthPb.AssetType_ASSET_TYPE_US_SECURITIES:
		return usstocks.NewUSStocksAggregator(f.investmentAggregatorHelper), nil
	case netWorthPb.AssetType_ASSET_TYPE_FIXED_DEPOSITS:
		return fixed_deposit.NewFixedDepositAggregator(f.aaBalanceFetcher, f.investmentAggregatorHelper, f.investmentDeclarationProcessor, f.datetime), nil
	case netWorthPb.AssetType_ASSET_TYPE_EPF:
		return epf.NewEPFAggregator(f.epfAggregatorHelper), nil
	case netWorthPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES:
		return indian_securities.NewIndianSecuritiesAggregator(f.aaBalanceFetcher), nil
	case netWorthPb.AssetType_ASSET_TYPE_BONDS, netWorthPb.AssetType_ASSET_TYPE_CASH, netWorthPb.AssetType_ASSET_TYPE_DIGITAL_SILVER, netWorthPb.AssetType_ASSET_TYPE_DIGITAL_GOLD,
		netWorthPb.AssetType_ASSET_TYPE_ART_ARTEFACTS, netWorthPb.AssetType_ASSET_TYPE_AIF, netWorthPb.AssetType_ASSET_TYPE_REAL_ESTATE, netWorthPb.AssetType_ASSET_TYPE_PRIVATE_EQUITY,
		netWorthPb.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE, netWorthPb.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION, netWorthPb.AssetType_ASSET_TYPE_GADGETS:
		return manual_asset.NewManualAssetAggregator(f.datetime, assetType, f.investmentDeclarationProcessor), nil
	case netWorthPb.AssetType_ASSET_TYPE_NPS:
		return nps.NewNPSAggregator(f.aaBalanceFetcher), nil
	default:
		return nil, fmt.Errorf("unhandled asset class %v", assetType)
	}
}
