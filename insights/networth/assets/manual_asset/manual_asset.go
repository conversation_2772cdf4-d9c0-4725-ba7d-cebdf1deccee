package manual_asset

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	typesPb "github.com/epifi/gamma/api/typesv2"
	investmentDeclaration "github.com/epifi/gamma/insights/networth/investment_declaration"
)

var assetTypeToInvestmentInstrumentMap = map[networthPb.AssetType]typesPb.InvestmentInstrumentType{
	networthPb.AssetType_ASSET_TYPE_PRIVATE_EQUITY:               typesPb.InvestmentInstrumentType_PRIVATE_EQUITY,
	networthPb.AssetType_ASSET_TYPE_REAL_ESTATE:                  typesPb.InvestmentInstrumentType_REAL_ESTATE,
	networthPb.AssetType_ASSET_TYPE_BONDS:                        typesPb.InvestmentInstrumentType_BOND,
	networthPb.AssetType_ASSET_TYPE_AIF:                          typesPb.InvestmentInstrumentType_AIF,
	networthPb.AssetType_ASSET_TYPE_ART_ARTEFACTS:                typesPb.InvestmentInstrumentType_ART_AND_ARTEFACTS,
	networthPb.AssetType_ASSET_TYPE_CASH:                         typesPb.InvestmentInstrumentType_CASH,
	networthPb.AssetType_ASSET_TYPE_DIGITAL_GOLD:                 typesPb.InvestmentInstrumentType_DIGITAL_GOLD,
	networthPb.AssetType_ASSET_TYPE_DIGITAL_SILVER:               typesPb.InvestmentInstrumentType_DIGITAL_SILVER,
	networthPb.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE: typesPb.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE,
	networthPb.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION:        typesPb.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION,
	networthPb.AssetType_ASSET_TYPE_GADGETS:                      typesPb.InvestmentInstrumentType_GADGETS,
}

type ManualAssetAggregator struct {
	assetType                      networthPb.AssetType
	investmentDeclarationProcessor investmentDeclaration.IProcessor
	time                           datetime.Time
}

func NewManualAssetAggregator(time datetime.Time, assetType networthPb.AssetType, investmentDeclarationProcessor investmentDeclaration.IProcessor) *ManualAssetAggregator {
	return &ManualAssetAggregator{
		time:                           time,
		assetType:                      assetType,
		investmentDeclarationProcessor: investmentDeclarationProcessor,
	}
}

// GetAggregatedValue returns current mutual fund portfolio values
func (m *ManualAssetAggregator) GetAggregatedValue(ctx context.Context, actorId string) (*networthPb.AssetValue, error) {
	investmentInstrument, ok := assetTypeToInvestmentInstrumentMap[m.assetType]
	if !ok {
		return nil, fmt.Errorf("missing investment instrument mapping for asset type %v", m.assetType)
	}
	details, err := m.investmentDeclarationProcessor.GetAllInvestmentDetails(ctx, actorId, []typesPb.InvestmentInstrumentType{investmentInstrument})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &networthPb.AssetValue{
			NetWorthAttribute: m.assetType,
			ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_FAILED,
			ErrorMessage:      fmt.Sprintf("failed to get all investments declarations for instrument %v: %s", investmentInstrument, err),
		}, nil
	}
	todayEOD := datetime.EndOfDay(m.time.Now())
	details = lo.Filter(details, func(detail *networthPb.InvestmentDetails, _ int) bool {
		decl := detail.GetInvestmentDeclaration()
		return decl.GetMaturityDate() == nil || decl.GetMaturityDate().AsTime().After(todayEOD)
	})

	if len(details) == 0 || errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &networthPb.AssetValue{
			NetWorthAttribute: m.assetType,
			ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND,
		}, nil
	}

	currentValue := money.ZeroINR().GetPb()
	for _, detail := range details {
		currentValue, err = money.Sum(currentValue, detail.GetCurrentValue())
		if err != nil {
			return nil, fmt.Errorf("failed to sum current value: %w", err)
		}
	}

	return &networthPb.AssetValue{
		NetWorthAttribute: m.assetType,
		Value:             currentValue,
		ComputationStatus: networthPb.ComputationStatus_COMPUTATION_STATUS_SUCCESS,
	}, nil
}
