package utils

import "time"

const (
	// Worker Stage Constants
	WorkerStageBinary     = "BINARY"
	WorkerStageDeployment = "DEPLOYMENT"
	WorkerStagePromotion  = "PROMOTION"

	// Status constants
	StatusSuccess    = "SUCCESS"
	StatusFailed     = "FAILED"
	StatusAborted    = "ABORTED"
	StatusInProgress = "IN_PROGRESS"
	StatusMerged     = "MERGED"
	StatusClosed     = "CLOSED"
	StatusCompleted  = "COMPLETED"
	StatusPending    = "PENDING"
	StatusTimeout    = "TIMEOUT"

	// Timeout constants
	PrMergeTimeout      = 48 * time.Hour
	CherryPickTimeout   = 48 * time.Hour
	JenkinsBuildTimeout = 2 * time.Hour
	PollingInterval     = 1 * time.Minute

	JenkinsNonProdBaseURL = "https://jenkins-deploy.pointz.in"
	// TODO(shafi): refactor the code and move it to config file
	CherrypickChannelId = "C02B47U6X9V"
	StatusEmojiSuccess  = "✅"
	StatusEmojiPending  = "⏳"
	StatusEmojiFailed   = "❌"
	StatusEmojiAborted  = "⚠️"
	StatusEmojiAlert    = "🚨"

	// Base URLs
	BaseGithubURL      = "https://github.com/epifi"
	BaseTemporalURL    = "https://temporal.pointz.in/namespaces/deploy-jarvis/workflows"
	BaseProdJenkinsURL = "https://jenkins-prod.pointz.in/"
)
