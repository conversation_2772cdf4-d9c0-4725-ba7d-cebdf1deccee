// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/leads/config"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DeleteUserEventSqsSubscriber *gencfg.SqsSubscriber
	_DbConfigMap                  cfg.DbConfigMap
	_Application                  *config.Application
	_AWS                          *cfg.AWS
	_Secrets                      *cfg.Secrets
}

func (obj *Config) DeleteUserEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._DeleteUserEventSqsSubscriber
}
func (obj *Config) DbConfigMap() cfg.DbConfigMap {
	return obj._DbConfigMap
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_DeleteUserEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeleteUserEventSqsSubscriber = _DeleteUserEventSqsSubscriber
	helper.AddFieldSetters("deleteusereventsqssubscriber", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_DeleteUserEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DeleteUserEventSqsSubscriber = _DeleteUserEventSqsSubscriber
	helper.AddFieldSetters("deleteusereventsqssubscriber", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "deleteusereventsqssubscriber":
		return obj._DeleteUserEventSqsSubscriber.Set(v.DeleteUserEventSqsSubscriber, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj._DeleteUserEventSqsSubscriber.Set(v.DeleteUserEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._DbConfigMap = v.DbConfigMap
	obj._Application = v.Application
	obj._AWS = v.AWS
	obj._Secrets = v.Secrets
	return nil
}
