package consumer

import (
	"context"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/logger"
	consumerPb "github.com/epifi/gamma/api/leads/consumer"
	"github.com/epifi/gamma/leads/dao"

	"go.uber.org/zap"
)

// ConsumerService processes SQS messages for the leads domain.
// Currently it handles delete-user events and removes the actor_id
// reference from the user_leads table.
type ConsumerService struct {
	consumerPb.UnimplementedConsumerServer
	leadDao dao.UserLeadDao
}

func NewConsumerService(leadDao dao.UserLeadDao) *ConsumerService {
	return &ConsumerService{leadDao: leadDao}
}

// ProcessDeleteUserEvent marks the actor_id column as NULL for
// all leads that belong to the deleted actor.
func (s *ConsumerService) ProcessDeleteUserEvent(ctx context.Context, req *consumerPb.ProcessDeleteUserEventRequest) (*consumerPb.ProcessDeleteUserEventResponse, error) {
	res := &consumerPb.ProcessDeleteUserEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{},
	}
	logger.Info(ctx, "ProcessDeleteUserEvent called with request", zap.Any("request", req))
	// If actor id is missing, we treat the message as successfully processed because
	// there is nothing we can do to recover such a message.
	if req == nil || req.GetActorId() == "" {
		logger.Error(ctx, "ProcessDeleteUserEvent called with empty actor_id")
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
		return res, nil
	}

	if err := s.leadDao.RemoveActorId(ctx, req.GetActorId()); err != nil {
		logger.Error(ctx, "error updating user_leads actor_id to NULL", zap.String("actor_id", req.GetActorId()), zap.Error(err))
		res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE
		return res, nil
	}

	logger.Info(ctx, "cleared actor_id for leads", zap.String("actor_id", req.GetActorId()))
	res.ResponseHeader.Status = queuePb.MessageConsumptionStatus_SUCCESS
	return res, nil
}
