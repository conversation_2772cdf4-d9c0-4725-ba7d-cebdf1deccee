package middlewares

import (
	"context"

	"github.com/mark3labs/mcp-go/server"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	sessionPb "github.com/epifi/gamma/api/auth/session"
)

type Auth struct {
	sessionManagerClient sessionPb.SessionManagerClient
}

func NewAuthMiddleware(
	sessionManagerClient sessionPb.SessionManagerClient,
) *Auth {
	return &Auth{
		sessionManagerClient: sessionManagerClient,
	}
}

// Validate validates session id and returns actorId if session is logged in
func (m *Auth) Validate(ctx context.Context) (string, string, error) {
	// fetch sessionId from context
	// this gets populated for every tool call
	session := server.ClientSessionFromContext(ctx)
	sessionId := session.SessionID()
	validateSessResp, validateSessErr := m.sessionManagerClient.ValidateSession(ctx, &sessionPb.ValidateSessionRequest{
		SessionId: sessionId,
	})
	if rpcErr := epifigrpc.RPCError(validateSessResp, validateSessErr); rpcErr != nil {
		return "", "", errors.Wrap(rpcErr, "error in validate session call")
	}
	// is session is expired or not created yet, then redirect to creating login url
	if validateSessResp.GetStatus().GetCode() == uint32(sessionPb.CreateSessionResponse_SESSION_EXPIRED) {
		return "", "", epifierrors.ErrUnauthorized
	}
	return validateSessResp.GetSessionDetails().GetActorId(), sessionId, nil
}

// GetLoginUrl fetches dynamic login url for given sessionId
func (m *Auth) GetLoginUrl(ctx context.Context, sessionId string) (string, error) {
	sessionLoginUrlResp, sessionLoginUrlErr := m.sessionManagerClient.GetSessionLoginUrl(ctx, &sessionPb.GetSessionLoginUrlRequest{
		SessionId: sessionId,
	})
	if rpcErr := epifigrpc.RPCError(sessionLoginUrlResp, sessionLoginUrlErr); rpcErr != nil {
		return "", errors.Wrap(rpcErr, "error in getting session login url")
	}
	return sessionLoginUrlResp.GetSessionLoginUrl(), nil
}
