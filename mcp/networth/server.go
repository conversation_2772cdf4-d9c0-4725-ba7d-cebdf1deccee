package networth

import (
	"context"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	sessionPb "github.com/epifi/gamma/api/auth/session"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/mcp/networth/middlewares"
	"github.com/epifi/gamma/mcp/networth/tools"
)

func CreateMcpServer(
	netWorthClient networthPb.NetWorthClient,
	sessionManagerClient sessionPb.SessionManagerClient) *server.MCPServer {
	// Create a new MCP server
	s := server.NewMCPServer(
		"Net Worth",
		"0.1.0",
		// Notifies clients when new tools gets added or any changes in tools
		server.WithToolCapabilities(true),
		server.WithResourceCapabilities(true, true),
		server.WithLogging(),
	)

	authMiddleware := middlewares.NewAuthMiddleware(sessionManagerClient)
	fetchNetWorthHandler := tools.NewFetchNetWorthHandler(netWorthClient, authMiddleware)

	// Register fetch net worth tool
	s.AddTool(fetchNetWorthHandler.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		result, fetchNetWorthErr := fetchNetWorthHandler.Handle(ctx, request)
		if fetchNetWorthErr != nil {
			logger.Error(ctx, "error fetching net worth", zap.Error(fetchNetWorthErr))
		} else {
			logger.Info(ctx, "net worth fetched successfully")
		}
		return result, fetchNetWorthErr
	})

	return s
}
