package tools

import (
	"context"
	"fmt"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/mcp/networth/middlewares"
)

var (
	// We will return the same for any kind of error to reduce attack surface
	fetchNetWorthErrMsg = mcp.NewToolResultText("something went wrong in fetching net worth")
)

type FetchNetWorthHandler struct {
	netWorthClient networthPb.NetWorthClient
	authMiddleware *middlewares.Auth
}

func NewFetchNetWorthHandler(
	netWorthClient networthPb.NetWorthClient,
	authMiddleware *middlewares.Auth,
) *FetchNetWorthHandler {
	return &FetchNetWorthHandler{
		netWorthClient: netWorthClient,
		authMiddleware: authMiddleware,
	}
}

// GetTool returns tool with name, description and other metadata which gets used by mcp host
func (h *FetchNetWorthHandler) GetTool() mcp.Tool {
	return mcp.NewTool("fetch_net_worth",
		mcp.WithDescription("Fetch user's net worth information including assets, liabilities, and breakdown"),
	)
}

// Handle is the main handler call that gets called on each tool call
func (h *FetchNetWorthHandler) Handle(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	actorId, sessionId, authMiddlewareErr := h.authMiddleware.Validate(ctx)
	if authMiddlewareErr != nil {
		// TODO(sainath): add something like decorator pattern to avoid duplication
		if errors.Is(authMiddlewareErr, epifierrors.ErrUnauthorized) {
			loginUrl, getLoginUrlErr := h.authMiddleware.GetLoginUrl(ctx, sessionId)
			if getLoginUrlErr != nil {
				logger.Error(ctx, "error fetching login url", zap.Error(getLoginUrlErr))
				return fetchNetWorthErrMsg, nil
			}
			// TODO(sainath): Craft markdown text so that link can be clickable
			res := "You are not logged in, log in using this link: " + loginUrl
			return mcp.NewToolResultText(res), nil
		}
		logger.Error(ctx, "error in auth middleware", zap.Error(authMiddlewareErr))
		return fetchNetWorthErrMsg, nil
	}
	logger.Debug(ctx, "Received fetch net worth call", zap.String(logger.SESSION_ID, sessionId))
	res, fetchNetWorthErr := h.fetchNetWorthApiCall(ctx, actorId)
	if fetchNetWorthErr != nil {
		logger.Error(ctx, "error in fetching net worth", zap.Error(fetchNetWorthErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return fetchNetWorthErrMsg, nil
	}
	return mcp.NewToolResultText(res), nil
}

func (h *FetchNetWorthHandler) fetchNetWorthApiCall(ctx context.Context, actorId string) (string, error) {
	netWorthResp, netWorthErr := h.netWorthClient.GetNetWorthValue(ctx, &networthPb.GetNetWorthValueRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(netWorthResp, netWorthErr); rpcErr != nil {
		return "", fmt.Errorf("error from networth rpc: %v", rpcErr)
	}
	byteResp, marshalErr := protojson.Marshal(netWorthResp)
	if marshalErr != nil {
		return "", errors.Wrap(marshalErr, "error marshalling net worth response")
	}
	return string(byteResp), nil
}
