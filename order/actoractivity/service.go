package actoractivity

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	accountsPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	caPb "github.com/epifi/gamma/api/connected_account"
	caExternalPb "github.com/epifi/gamma/api/connected_account/external"
	depositPb "github.com/epifi/gamma/api/deposit"
	orderPb "github.com/epifi/gamma/api/order"
	aaPb "github.com/epifi/gamma/api/order/aa"
	actorActivityPb "github.com/epifi/gamma/api/order/actoractivity"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	timelinePb "github.com/epifi/gamma/api/timeline"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	config "github.com/epifi/gamma/order/config/genconf"
	userProcessor "github.com/epifi/gamma/order/internal/user"
	"github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/feature"
)

var (
	fdCreationPrefix = "Opened "
	sdCreationPrefix = "Created "
	sdAddMoneyPrefix = "Money added into "
	preClosurePrefix = "Closed "
	maturedSuffix    = " Matured"
	// TODO(harish): finalise with product
	depositInterestCreditTitle = "Interest credit"

	activityTypeToTransactionTypeMap = map[paymentPb.AccountingEntryType]orderPb.GetSuccessOrdersWithTransactionsForActorRequest_TransactionType{
		paymentPb.AccountingEntryType_DEBIT:  orderPb.GetSuccessOrdersWithTransactionsForActorRequest_DEBIT,
		paymentPb.AccountingEntryType_CREDIT: orderPb.GetSuccessOrdersWithTransactionsForActorRequest_CREDIT,
	}

	activityTypeToSelectedTransactionTypeMap = map[paymentPb.AccountingEntryType]orderPb.GetSelectedOrdersWithTransactionsRequest_TransactionType{
		paymentPb.AccountingEntryType_DEBIT:  orderPb.GetSelectedOrdersWithTransactionsRequest_DEBIT,
		paymentPb.AccountingEntryType_CREDIT: orderPb.GetSelectedOrdersWithTransactionsRequest_CREDIT,
	}
)

type Service struct {
	// UnimplementedPaymentServer is embedded to have forward compatible implementations
	actorActivityPb.UnimplementedActorActivityServer
	orderClient                     orderPb.OrderServiceClient
	actorClient                     actorPb.ActorClient
	accountPiClient                 accountPiPb.AccountPIRelationClient
	piClient                        piPb.PiClient
	depositClient                   depositPb.DepositClient
	iconUrls                        *config.IconUrls
	timelineClient                  timelinePb.TimelineServiceClient
	aaClient                        aaPb.AccountAggregatorClient
	caClient                        caPb.ConnectedAccountClient
	userProcessor                   userProcessor.UserProcessor
	userGroupClient                 userGroupPb.GroupClient
	userClient                      userPb.UsersClient
	connectedAccountUserGroupParams *config.ConnectedAccountUserGroupParams
	conf                            *config.Config
}

// Factory method for creating an instance of actor activity service. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewService(
	orderClient orderPb.OrderServiceClient,
	actorClient actorPb.ActorClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	piClient piPb.PiClient,
	depositClient depositPb.DepositClient,
	timelineClient timelinePb.TimelineServiceClient,
	aaClient aaPb.AccountAggregatorClient,
	caClient caPb.ConnectedAccountClient,
	userProcessor userProcessor.UserProcessor,
	userGroupClient userGroupPb.GroupClient,
	userClient userPb.UsersClient,
	conf *config.Config,
) *Service {
	return &Service{
		orderClient:                     orderClient,
		actorClient:                     actorClient,
		accountPiClient:                 accountPiClient,
		piClient:                        piClient,
		depositClient:                   depositClient,
		iconUrls:                        conf.IconUrls(),
		timelineClient:                  timelineClient,
		aaClient:                        aaClient,
		caClient:                        caClient,
		userProcessor:                   userProcessor,
		userGroupClient:                 userGroupClient,
		userClient:                      userClient,
		connectedAccountUserGroupParams: conf.ConnectedAccountUserGroupParams(),
		conf:                            conf,
	}
}

// GetActivities fetches a list of activities to a combination of actor and accounts passed in the request.
// The activities list can grow very big with time.
// Thus, rpc method returns the list in pages. It returns all the activities from specified start_timestamp.
// The sequence of the result returned be based on the boolean flag descending.
// i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
// If marked false ASCENDING ordered results are returned from the given start timestamp.
// The max number of activities returned is bounded by the page_size specified in the request, while offset helps in
// eliminating initial entries which are already returned in the last call.
//
// Sorting logic - The orders are sorted based on last updated timestamp of the order.
// nolint: funlen
func (s *Service) GetActivities(ctx context.Context, req *actorActivityPb.GetActivitiesRequest) (*actorActivityPb.GetActivitiesResponse, error) {
	var (
		res           = &actorActivityPb.GetActivitiesResponse{}
		orderWithTxns []*orderPb.OrderWithTransactions
		aaTxns        []*aaPb.Transaction
	)

	internalPiIds, aaTxnPiIds, err := s.getPiIds(ctx, req.GetAccountFilter(), req.GetPiFilter())
	switch {
	case err != nil:
		logger.Error(ctx, "error fetching Pis for account", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	case len(internalPiIds) == 0 && len(aaTxnPiIds) == 0:
		logger.Error(ctx, "No Pis found for accounts")
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}

	orderWithTxns, err = s.getOrderWithTxnForInternalPis(ctx, req.GetCurrentActorId(),
		req.GetPageSize(), req.GetOrderOffset(), req.GetDescending(), req.GetPaymentFilter().GetPaymentProtocol(),
		req.GetActivitiesStartTimestamp(), req.GetActivitiesEndTimestamp(), req.GetPaymentFilter().GetTransactionType(),
		req.GetPaymentFilter().GetFromAmount(), req.GetPaymentFilter().GetToAmount(), internalPiIds, nil, req.GetPageLandingTimestamp(), req.GetEntryPointType())
	if err != nil {
		logger.Error(ctx, "error fetching order with txns for internal pi",
			zap.String(logger.ACTOR_ID, req.GetCurrentActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	isAaAllowed := s.isAaEnabledForCurrentActor(ctx, req.GetCurrentActorId())

	if isAaAllowed {
		aaTxns, err = s.getTransactionsForAaPi(ctx, req.GetActivitiesStartTimestamp(), nil,
			req.GetPageSize(), req.GetAaTxnOffset(), req.GetDescending(), req.GetCurrentActorId(),
			req.GetPaymentFilter().GetTransactionType(), aaTxnPiIds, nil, nil)
		if err != nil {
			logger.Error(ctx, "error fetching txns for aa pis",
				zap.String(logger.ACTOR_ID, req.GetCurrentActorId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	filteredOrderWithTxns, filteredAaTxns := getFilteredTransactions(
		orderWithTxns,
		aaTxns,
		req.GetPageSize(),
		req.GetDescending(),
	)

	if len(filteredOrderWithTxns) == 0 && len(filteredAaTxns) == 0 {
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}

	internalActivities, err := s.getInternalPiActivities(ctx, filteredOrderWithTxns, req.GetCurrentActorId(), isAaAllowed, req.GetFetchSoftDeletedUsers())
	if err != nil {
		logger.Error(ctx, "error fetching activities for internal pi", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	aaActivities, err := s.getAaActivities(ctx, filteredAaTxns, req.GetCurrentActorId(), req.GetFetchSoftDeletedUsers())
	if err != nil {
		logger.Error(ctx, "error fetching activities for aa pi", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOk()
	res.Activities = getMergedActivities(internalActivities, aaActivities, req.GetDescending())
	return res, nil
}

// getEntityDetailsForActors returns map of entity details which includes name, profile image url for actors mapped with
// actor ids by calling the GetEntityDetails service. It take list of actor Ids as request.
func (s *Service) getEntityDetailsForActors(ctx context.Context, actorIds []string, fetchForSoftDeletedUsers bool) (map[string]*actorPb.GetEntityDetailsResponse_EntityDetail, error) {
	actorIds = lo.Uniq(actorIds)
	entityDetailsRes, err := s.actorClient.GetEntityDetails(ctx, &actorPb.GetEntityDetailsRequest{ActorIds: actorIds, FetchSoftDeletedUsers: fetchForSoftDeletedUsers})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error fetching entity details for actors, err %w", err)
	case !entityDetailsRes.GetStatus().IsSuccess():
		return nil, fmt.Errorf("GetEntityDetailsByActorId() rpc failed with status %s", entityDetailsRes.GetStatus())
	}
	actorToEntityDetails := make(map[string]*actorPb.GetEntityDetailsResponse_EntityDetail)

	for _, entity := range entityDetailsRes.GetEntityDetails() {
		actorToEntityDetails[entity.GetActorId()] = entity
	}

	return actorToEntityDetails, nil
}

// getActivity returns activity given orders with txns, title, iconurl, current and second actor id
func getActivity(
	shortDescription,
	title,
	iconUrl,
	secondActorId,
	timelineId string,
	amountBadge actorActivityPb.GetActivitiesResponse_Activity_AmountBadge,
	accountId, activityId string,
	accountType accountsPb.Type,
	activityType actorActivityPb.GetActivitiesResponse_Activity_Type,
	amount *moneyPb.Money,
	activityTimestamp *timestamppb.Timestamp,
	shortDescIconUrl string,
	orderTags []orderPb.OrderTag,
	activityEntryPoint actorActivityPb.ActivityEntryPoint,
) *actorActivityPb.GetActivitiesResponse_Activity {

	activity := &actorActivityPb.GetActivitiesResponse_Activity{
		IconUrl:            iconUrl,
		Title:              title,
		ShortDesc:          shortDescription,
		ShortDescIconUrl:   shortDescIconUrl,
		Amount:             amount,
		AmountBadge:        amountBadge,
		ActivityId:         activityId,
		SecondActorId:      secondActorId,
		ActivityType:       activityType,
		ActivityTimestamp:  activityTimestamp,
		PartnerTag:         getPartnerTagForTransaction(orderTags, accountType),
		ActivityEntryPoint: activityEntryPoint,
	}

	switch {
	case timelineId != "":
		activity.DeeplinkIdentifier = &actorActivityPb.GetActivitiesResponse_Activity_TimelineId{
			TimelineId: timelineId,
		}
	case accountId != "":
		activity.DeeplinkIdentifier = &actorActivityPb.GetActivitiesResponse_Activity_DepositAccountIdentifier_{
			DepositAccountIdentifier: &actorActivityPb.GetActivitiesResponse_Activity_DepositAccountIdentifier{
				AccountId:   accountId,
				AccountType: accountType,
			},
		}
	// TODO(harish): handle FD interest credit txns
	//  Monorail: https://monorail.pointz.in/p/fi-app/issues/detail?id=19555
	case activityType == actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_INTEREST_CREDIT:
		activity.DeeplinkIdentifier = &actorActivityPb.GetActivitiesResponse_Activity_DepositAccountIdentifier_{
			DepositAccountIdentifier: &actorActivityPb.GetActivitiesResponse_Activity_DepositAccountIdentifier{
				AccountId:   "",
				AccountType: accountsPb.Type_FIXED_DEPOSIT,
			},
		}
	}
	return activity
}

// getActivityType returns activity type based on payment protocol and transaction status
// nolint
func getActivityType(ordersWithTxns *orderPb.OrderWithTransactions) actorActivityPb.GetActivitiesResponse_Activity_Type {
	var (
		activityType actorActivityPb.GetActivitiesResponse_Activity_Type
	)
	order := ordersWithTxns.GetOrder()
	txn := ordersWithTxns.Transactions[0]

	switch txn.GetPaymentProtocol() {
	case paymentPb.PaymentProtocol_NEFT:
		switch txn.GetStatus() {
		case paymentPb.TransactionStatus_SUCCESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_SUCCESS
		case paymentPb.TransactionStatus_FAILED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_FAILED
		case paymentPb.TransactionStatus_REVERSED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_REVERSED
		case paymentPb.TransactionStatus_IN_PROGRESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_NEFT_TRANSACTION_PENDING
		}

	case paymentPb.PaymentProtocol_RTGS:
		switch txn.GetStatus() {
		case paymentPb.TransactionStatus_SUCCESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_SUCCESS
		case paymentPb.TransactionStatus_FAILED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_FAILED
		case paymentPb.TransactionStatus_REVERSED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_REVERSED
		case paymentPb.TransactionStatus_IN_PROGRESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_RTGS_TRANSACTION_PENDING
		}

	case paymentPb.PaymentProtocol_UPI:
		switch txn.GetStatus() {
		case paymentPb.TransactionStatus_SUCCESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_SUCCESS
		case paymentPb.TransactionStatus_FAILED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_FAILED
		case paymentPb.TransactionStatus_REVERSED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_REVERSED
		case paymentPb.TransactionStatus_IN_PROGRESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_UPI_TRANSACTION_PENDING
		}

	case paymentPb.PaymentProtocol_INTRA_BANK:
		switch txn.GetStatus() {
		case paymentPb.TransactionStatus_SUCCESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_SUCCESS
		case paymentPb.TransactionStatus_FAILED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_FAILED
		case paymentPb.TransactionStatus_REVERSED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_REVERSED
		case paymentPb.TransactionStatus_IN_PROGRESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_INTRABANK_TRANSACTION_PENDING
		}

	case paymentPb.PaymentProtocol_IMPS:
		switch txn.GetStatus() {
		case paymentPb.TransactionStatus_SUCCESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_SUCCESS
		case paymentPb.TransactionStatus_FAILED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_FAILED
		case paymentPb.TransactionStatus_REVERSED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_REVERSED
		case paymentPb.TransactionStatus_IN_PROGRESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_IMPS_TRANSACTION_PENDING
		}

	case paymentPb.PaymentProtocol_CARD:
		if order.GetProvenance() == orderPb.OrderProvenance_ATM {
			switch txn.GetStatus() {
			case paymentPb.TransactionStatus_SUCCESS:
				activityType = actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_SUCCESS
			case paymentPb.TransactionStatus_FAILED:
				activityType = actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_FAILED
			case paymentPb.TransactionStatus_REVERSED:
				activityType = actorActivityPb.GetActivitiesResponse_Activity_ATM_TRANSACTION_REVERSED
			}
		} else {
			switch txn.GetStatus() {
			case paymentPb.TransactionStatus_SUCCESS:
				activityType = actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_SUCCESS
			case paymentPb.TransactionStatus_FAILED:
				activityType = actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_FAILED
			case paymentPb.TransactionStatus_REVERSED:
				activityType = actorActivityPb.GetActivitiesResponse_Activity_DEBIT_CARD_TRANSACTION_REVERSED
			}
		}
	case paymentPb.PaymentProtocol_ENACH:
		switch txn.GetStatus() {
		case paymentPb.TransactionStatus_SUCCESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_SUCCESS
		case paymentPb.TransactionStatus_FAILED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_FAILED
		case paymentPb.TransactionStatus_REVERSED:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_REVERSED
		case paymentPb.TransactionStatus_IN_PROGRESS:
			activityType = actorActivityPb.GetActivitiesResponse_Activity_ENACH_TRANSACTION_PENDING
		}

	default:
		activityType = actorActivityPb.GetActivitiesResponse_Activity_ACTIVITY_TYPE_UNSPECIFIED
	}

	return activityType
}

// getPiIds returns a list of PiIds, nil given a list of accountIds
// returns nil, relevant error in case of failure/error
// TODO(raunak) optimize the rpc call with a batch api, get account type in the request
func (s *Service) getPiIds(ctx context.Context,
	accountFilters []*actorActivityPb.GetActivitiesRequest_AccountFilter,
	piFilter []string,
) ([]string, []string, error) {
	var (
		internalPiIds []string
		aaTxnPiIds    []string
	)

	for _, accountFilter := range accountFilters {
		internalAccountPis, err := s.getInternalAccountPis(ctx, accountFilter.GetAccountId(), accountFilter.GetAccountType())
		if err != nil {
			return nil, nil, err
		}
		internalPiIds = append(internalPiIds, internalAccountPis...)
		if len(internalAccountPis) == 0 {
			// check for connected account pis
			connectedAccountPis, err := s.getConnectedAccountPis(ctx, accountFilter.GetAccountId(), accountFilter.GetAccountType())
			if err != nil {
				return nil, nil, err
			}
			aaTxnPiIds = append(aaTxnPiIds, connectedAccountPis...)
		}
	}
	internalPiIds = append(internalPiIds, piFilter...)
	return internalPiIds, aaTxnPiIds, nil
}

// isSavings returns a boolean denoting if the order is savings or not
// loops in the order tag to find savings related tag like DEPOSIT, FD, RD, SD
func isSavings(order *orderPb.Order) bool {
	for _, tag := range order.GetTags() {
		if tag == orderPb.OrderTag_DEPOSIT || tag == orderPb.OrderTag_FD ||
			tag == orderPb.OrderTag_RD || tag == orderPb.OrderTag_SD {
			return true
		}
	}
	return false
}

// getDepositActivityDetails returns nil, activity title, icon url for a savings Payment instrument
func (s *Service) getDepositActivityDetails(ctx context.Context, piTo, piFrom string, order *orderPb.Order) (error,
	string, string, actorActivityPb.GetActivitiesResponse_Activity_AmountBadge, string, accountsPb.Type,
	actorActivityPb.GetActivitiesResponse_Activity_Type) {
	var (
		account       *piPb.Account
		accountPiTo   *piPb.Account
		accountPiFrom *piPb.Account
		depositTitle  string
		amtBadge      actorActivityPb.GetActivitiesResponse_Activity_AmountBadge
		accountId     string
		fromAccType   accountsPb.Type
		err           error
		actorActivity actorActivityPb.GetActivitiesResponse_Activity_Type
	)
	accountPiTo, err = s.getAccountDetailsFromPi(ctx, piTo)
	if err != nil {
		return fmt.Errorf("failed to get account details for pi : %s: %w", piTo, err), "", "", 0, "", 0, 0
	}
	switch {
	case accountsPb.IsDepositAccount(accountPiTo.GetAccountType()):
		account = accountPiTo
	default:
		accountPiFrom, err = s.getAccountDetailsFromPi(ctx, piFrom)
		if err != nil {
			return fmt.Errorf("failed to get account details for pi : %s: %w", piFrom, err), "", "", 0, "", 0, 0
		}
		if accountsPb.IsDepositAccount(accountPiFrom.GetAccountType()) {
			account = accountPiFrom
		} else if accountPiFrom.GetAccountType() == accountsPb.Type_SAVINGS {
			// TODO(harish): handle FD interest credit txns in actor activity
			//  Monorail:https://monorail.pointz.in/p/fi-app/issues/detail?id=19555
			title := "Interest credited for FD"
			return nil, title, s.iconUrls.FDIconUrl(), actorActivityPb.GetActivitiesResponse_Activity_SAVINGS, "",
				accountsPb.Type_SAVINGS, actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_INTEREST_CREDIT
		} else {
			return fmt.Errorf("failed to get deposit account details for pi's : %s %s : %w", piTo, piFrom, err), "", "", 0, "", 0, 0
		}
	}

	req := &depositPb.GetByAccountNumberAndIfscRequest{
		AccountNumber: account.GetActualAccountNumber(),
		IfscCode:      account.GetIfscCode(),
	}
	res, err := s.depositClient.GetByAccountNumberAndIfsc(ctx, req)
	switch {
	case err != nil:
		return fmt.Errorf("deposit account details can't be fetched due to rpc failure: req : %v: %w", req, err), "", "", 0, "", 0, 0
	case res.Status.IsRecordNotFound():
		return fmt.Errorf("deposit record not found for account number: %s: ", req.GetAccountNumber()[len(req.GetAccountNumber())-4:]), "", "", 0, "", 0, 0
	case !res.Status.IsSuccess():
		return fmt.Errorf("got unexpected response from deposit service: %v: ", res.Status), "", "", 0, "", 0, 0
	}

	accountId = res.GetAccount().GetId()
	fromAccType = res.GetAccount().GetType()
	amtBadge = actorActivityPb.GetActivitiesResponse_Activity_SAVINGS

	if accountsPb.IsDepositAccount(accountPiFrom.GetAccountType()) {
		amtBadge = actorActivityPb.GetActivitiesResponse_Activity_CREDIT
	}
	depositTitle, actorActivity = getDepositTitle(accountPiFrom.GetAccountType(), res.GetAccount().GetState(), order, res.GetAccount().GetName())
	iconUrl := s.getIconUrlForDeposit(order)

	return nil, depositTitle, iconUrl, amtBadge, accountId, fromAccType, actorActivity
}

func getDepositTitle(fromAccType accountsPb.Type, depositState depositPb.DepositState, order *orderPb.Order, depositTitle string) (string, actorActivityPb.GetActivitiesResponse_Activity_Type) {
	var actorActivity actorActivityPb.GetActivitiesResponse_Activity_Type
	if accountsPb.IsDepositAccount(fromAccType) {
		switch {
		case depositState == depositPb.DepositState_PRECLOSED:
			depositTitle = preClosurePrefix + depositTitle
			switch fromAccType { // nolint:exhaustive
			case accountsPb.Type_FIXED_DEPOSIT:
				actorActivity = actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_PRECLOSED
			case accountsPb.Type_SMART_DEPOSIT:
				actorActivity = actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_PRECLOSED
			}
		case depositState == depositPb.DepositState_CLOSED:
			depositTitle = depositTitle + maturedSuffix
			switch fromAccType { // nolint:exhaustive
			case accountsPb.Type_FIXED_DEPOSIT:
				actorActivity = actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_MATURED
			case accountsPb.Type_SMART_DEPOSIT:
				actorActivity = actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_MATURED
			}
		}

	} else {
		switch {
		case orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_INTEREST):
			depositTitle = depositInterestCreditTitle
			if orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_SD) {
				actorActivity = actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_INTEREST_CREDIT
			} else {
				actorActivity = actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_INTEREST_CREDIT
			}
		case orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_SD):
			if order.GetWorkflow() == orderPb.OrderWorkflow_NO_OP {
				depositTitle = sdCreationPrefix + depositTitle
				actorActivity = actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_CREATED
			}
			// order workflow P2P_FUND_TRANSFER denotes add money to existing SD
			if order.GetWorkflow() == orderPb.OrderWorkflow_P2P_FUND_TRANSFER {
				depositTitle = sdAddMoneyPrefix + depositTitle
				actorActivity = actorActivityPb.GetActivitiesResponse_Activity_SMART_DEPOSIT_AMT_ADDED
			}
		case orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_FD):
			depositTitle = fdCreationPrefix + depositTitle
			actorActivity = actorActivityPb.GetActivitiesResponse_Activity_FIXED_DEPOSIT_CREATED
		}
	}
	return depositTitle, actorActivity
}

// getAccountDetailsFromPi fetches account number, ifsc code and type of account given a PI.
// returns error in case PI is not of account type of rpc call to PI service fails
func (s *Service) getAccountDetailsFromPi(ctx context.Context, piId string) (*piPb.Account, error) {

	pi, err := s.getPIDetails(ctx, piId)
	if err != nil {
		return nil, err
	}

	if pi.Type != piPb.PaymentInstrumentType_BANK_ACCOUNT && pi.Type != piPb.PaymentInstrumentType_GENERIC {
		return nil, fmt.Errorf("payment instrument is of type: %s expected %s : ", pi.Type.String(),
			piPb.PaymentInstrumentType_BANK_ACCOUNT.String())
	}

	return pi.GetAccount(), nil
}

// getPIDetails calls pi service's `GetPiByID` RPC and returns payment-instrument
func (s *Service) getPIDetails(ctx context.Context, piId string) (*piPb.PaymentInstrument, error) {
	piReq := &piPb.GetPiByIdRequest{
		Id: piId,
	}
	piRes, err := s.piClient.GetPiById(ctx, piReq)
	switch {
	case err != nil:
		return nil, fmt.Errorf("payment instrument details can't be fetched due to rpc failure: PI: %s : %w",
			piId, err)
	case piRes.Status.IsRecordNotFound():
		return nil, fmt.Errorf("PI record not found for PI: %s: ", piId)
	case !piRes.Status.IsSuccess():
		return nil, fmt.Errorf("got unexpected response from payment instrument service: %v", piRes.Status)
	}

	return piRes.PaymentInstrument, nil
}

// getIconUrlForDeposit returns icon url for a deposit order
func (s *Service) getIconUrlForDeposit(order *orderPb.Order) string {
	switch {
	case orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_FD):
		return s.iconUrls.FDIconUrl()
	case orderPb.IsTagExist(order.GetTags(), orderPb.OrderTag_SD):
		return s.iconUrls.SDIconUrl()
	}
	return ""
}

// getConnectedAccountPis fetches the pi id for connected accounts
// nolint: dupl
func (s *Service) getConnectedAccountPis(ctx context.Context, accountId string, accountType accountsPb.Type) ([]string, error) {
	var piIds []string
	aaAccountPiRes, err := s.accountPiClient.GetAaAccountPiByAccountId(ctx, &accountPiPb.GetAaAccountPiByAccountIdRequest{
		AccountId:   accountId,
		AccountType: accountType,
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error fetching accountPis for connected account Id %s, err %w", accountId, err)
	case aaAccountPiRes.GetStatus().IsRecordNotFound():
		logger.Info(ctx, "no aa account pi relation present", zap.String(logger.ACCOUNT_ID, accountId))
		return piIds, nil
	case !aaAccountPiRes.GetStatus().IsSuccess():
		return nil, fmt.Errorf("GetAaAccountPiByAccountId() failed with status %s", aaAccountPiRes.GetStatus())
	}
	for _, accountPi := range aaAccountPiRes.GetAccountPis() {
		piIds = append(piIds, accountPi.GetPiId())
	}
	return piIds, nil
}

// getInternalAccountPis fetches the pi id for internal accounts
// nolint: dupl
func (s *Service) getInternalAccountPis(ctx context.Context, accountId string, accountType accountsPb.Type) ([]string, error) {
	var piIds []string
	accountPiRes, err := s.accountPiClient.GetByAccountId(ctx, &accountPiPb.GetByAccountIdRequest{
		AccountId:   accountId,
		AccountType: accountType,
	})
	switch {
	case err != nil:
		return nil, fmt.Errorf("error fetching accountPis for internal account Id %s, err %w", accountId, err)
	case accountPiRes.GetStatus().IsRecordNotFound():
		logger.Info(ctx, "no account pi relation present", zap.String(logger.ACCOUNT_ID, accountId))
		return piIds, nil
	case !accountPiRes.GetStatus().IsSuccess():
		return nil, fmt.Errorf("GetByAccountId() failed with status %s", accountPiRes.GetStatus())
	}
	for _, accountPi := range accountPiRes.GetAccountPis() {
		piIds = append(piIds, accountPi.GetPiId())
	}
	return piIds, nil
}

func getSecondaryActorIdWithBadge(currentActorId, fromActorId, toActorId string) (string, actorActivityPb.GetActivitiesResponse_Activity_AmountBadge, error) {
	switch currentActorId {
	case fromActorId:
		return toActorId, actorActivityPb.GetActivitiesResponse_Activity_DEBIT, nil
	case toActorId:
		return fromActorId, actorActivityPb.GetActivitiesResponse_Activity_CREDIT, nil
	}
	return "", actorActivityPb.GetActivitiesResponse_Activity_AMOUNT_BADGE_UNSPECIFIED, fmt.Errorf("error fetching second actor id")
}

// nolint: funlen
// getOrderWithTxnForInternalPis fetches order with transactions for internal pis
func (s *Service) getOrderWithTxnForInternalPis(ctx context.Context, currentActorId string, pageSize, offset int32, isDescending bool, protocol []paymentPb.PaymentProtocol, startTimeStamp, endTimeStamp *timestamppb.Timestamp, transactionType paymentPb.AccountingEntryType, fromAmount, toAmount *moneyPb.Money, piIds, otherPiIds []string, pageLandingTimestamp *timestamppb.Timestamp, entryPointType actorActivityPb.GetActivitiesRequest_EntryPointType) ([]*orderPb.OrderWithTransactions, error) {
	if len(piIds) == 0 {
		return nil, nil
	}

	if feature.IsFeatureEnabledForUser(ctx, currentActorId, s.conf.FeatureFlags().EnableAllTransactionForSelectedOrderStatesParams(), s.userGroupClient, s.userClient, s.actorClient) &&
		entryPointType == actorActivityPb.GetActivitiesRequest_ENTRY_POINT_TYPE_UI {
		var (
			paymentFailedWorkflows = []orderPb.OrderWorkflow{orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
				orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER}
		)
		if feature.IsFeatureEnabledForUserV2(ctx, currentActorId, s.conf.FeatureFlags().EnableAllTransactionsForNoOpFailures(), s.userGroupClient, s.userClient, s.actorClient) {
			paymentFailedWorkflows = append(paymentFailedWorkflows, orderPb.OrderWorkflow_NO_OP)
		}
		ordersWithTxnsRes, err := s.orderClient.GetSelectedOrdersWithTransactions(ctx, &orderPb.GetSelectedOrdersWithTransactionsRequest{
			ActorId:         currentActorId,
			StartTimestamp:  startTimeStamp,
			PageSize:        pageSize,
			Offset:          offset,
			IsDescending:    isDescending,
			PiFilter:        piIds,
			PaymentProtocol: protocol,
			TransactionType: activityTypeToSelectedTransactionTypeMap[transactionType],
			FromAmount:      fromAmount,
			ToAmount:        toAmount,
			EndTime:         endTimeStamp,
			OtherPiFilter:   otherPiIds,
			TxnFieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_REMARKS,
				paymentPb.TransactionFieldMask_PAYMENT_PROTOCOL, paymentPb.TransactionFieldMask_PARTNER_BANK,
				paymentPb.TransactionFieldMask_PI_TO, paymentPb.TransactionFieldMask_PI_FROM,
				paymentPb.TransactionFieldMask_COMPUTED_EXECUTED_AT, paymentPb.TransactionFieldMask_ORDER_REF_ID, paymentPb.TransactionFieldMask_STATUS},
			TimeTravelQueryTimestamp: pageLandingTimestamp,
			OrderStatusWorkflowTypeFilters: []*orderPb.OrderStatusAndWorkflowTypeFilter{
				{
					OrderStatus: orderPb.OrderStatus_IN_PAYMENT,
					Workflows: []orderPb.OrderWorkflow{
						orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER,
					},
				}, {
					OrderStatus: orderPb.OrderStatus_PAYMENT_FAILED,
					Workflows:   paymentFailedWorkflows,
				}, {
					OrderStatus: orderPb.OrderStatus_PAYMENT_REVERSED,
					Workflows: []orderPb.OrderWorkflow{
						orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER,
					},
				},
			},
		})
		if err = epifigrpc.RPCError(ordersWithTxnsRes, err); err != nil {
			if !ordersWithTxnsRes.GetStatus().IsRecordNotFound() {
				return nil, fmt.Errorf("error fetching order with txns for actor: %s %w with new GetSelectedOrdersWithTransactions RPC", currentActorId, err)
			}
		}
		return ordersWithTxnsRes.GetOrdersWithTxns(), nil
	}

	ordersWithTxnsRes, err := s.orderClient.GetSuccessOrdersWithTransactionsForActor(ctx, &orderPb.GetSuccessOrdersWithTransactionsForActorRequest{
		ActorId:         currentActorId,
		StartTimestamp:  startTimeStamp,
		PageSize:        pageSize,
		Offset:          offset,
		IsDescending:    isDescending,
		PiFilter:        piIds,
		PaymentProtocol: protocol,
		TransactionType: activityTypeToTransactionTypeMap[transactionType],
		FromAmount:      fromAmount,
		ToAmount:        toAmount,
		EndTime:         endTimeStamp,
		OtherPiFilter:   otherPiIds,
		TxnFieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_REMARKS,
			paymentPb.TransactionFieldMask_PAYMENT_PROTOCOL, paymentPb.TransactionFieldMask_PARTNER_BANK,
			paymentPb.TransactionFieldMask_PI_TO, paymentPb.TransactionFieldMask_PI_FROM,
			paymentPb.TransactionFieldMask_UPDATED_AT, paymentPb.TransactionFieldMask_DEBITED_AT, paymentPb.TransactionFieldMask_CREDITED_AT, paymentPb.TransactionFieldMask_ORDER_REF_ID},
	})

	if err = epifigrpc.RPCError(ordersWithTxnsRes, err); err != nil {
		if !ordersWithTxnsRes.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("error fetching order with txns for actor: %s %w", currentActorId, err)
		}
	}

	filteredOrderWithTxns := s.filterOrderWithTxns(ordersWithTxnsRes.GetOrdersWithTxns())

	return filteredOrderWithTxns, nil
}

func (s *Service) filterOrderWithTxns(OrdersWithTxns []*orderPb.OrderWithTransactions) []*orderPb.OrderWithTransactions {
	// There is a special handling required in case of US Stocks even if the order is IN_PAYMENT, we want to show it as success
	// This is because the first leg transaction is complete and user should not be concerned with second leg transactions
	// Any granularity in tracking needed here should be provided by US Stocks on their  stock purchase page
	// Hence, after fetching the orders, we update any IFT IN_PAYMENT orders to PAID state just for better UX.
	for _, orderWithTxn := range OrdersWithTxns {
		if orderWithTxn.GetOrder().GetWorkflow() == orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER && orderWithTxn.GetOrder().GetStatus() == orderPb.OrderStatus_IN_PAYMENT {
			orderWithTxn.Order.Status = orderPb.OrderStatus_PAID
		}
	}
	return OrdersWithTxns
}

// getTransactionsForAaPi fetches transactions for aa pis
func (s *Service) getTransactionsForAaPi(ctx context.Context,
	startTimestamp, endTimestamp *timestamppb.Timestamp,
	pageSize, offset int32,
	isDescending bool,
	currentActorId string,
	transactionType paymentPb.AccountingEntryType,
	piIds, otherPiIds []string,
	paymentProtocol []paymentPb.PaymentProtocol) ([]*aaPb.Transaction, error) {
	if len(piIds) == 0 {
		return nil, nil
	}
	res, err := s.aaClient.GetTransactions(ctx, &aaPb.GetTransactionsRequest{
		PiFilter:        piIds,
		StartTimestamp:  startTimestamp,
		EndTimestamp:    endTimestamp,
		OtherPiFilter:   otherPiIds,
		PageSize:        pageSize,
		Offset:          offset,
		IsDescending:    isDescending,
		TransactionType: transactionType,
		TxnFieldMasks: []aaPb.AATransactionFieldMask{
			aaPb.AATransactionFieldMask_PAYLOAD, aaPb.AATransactionFieldMask_PROTOCOL,
			aaPb.AATransactionFieldMask_REMARKS, aaPb.AATransactionFieldMask_EXECUTED_AT,
			aaPb.AATransactionFieldMask_AMOUNT, aaPb.AATransactionFieldMask_ID,
			aaPb.AATransactionFieldMask_BANK,
		},
		PaymentProtocol: paymentProtocol,
	})

	if err = epifigrpc.RPCError(res, err); err != nil {
		if !res.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("error fetching aa transactions for actor %s %w", currentActorId, err)
		}
	}
	return res.GetTransactions(), nil
}

func getFilteredTransactions(internalPiTxns []*orderPb.OrderWithTransactions, aaTxns []*aaPb.Transaction, size int32, isDescending bool) ([]*orderPb.OrderWithTransactions, []*aaPb.Transaction) {
	var (
		count                  = int32(0)
		internalPiTxnIndex     = 0
		aaTxnIndex             = 0
		filteredInternalPiTxns []*orderPb.OrderWithTransactions
		filteredAaPiTxns       []*aaPb.Transaction
	)

	for count < size {
		switch {
		case internalPiTxnIndex >= len(internalPiTxns) && aaTxnIndex >= len(aaTxns):
			return filteredInternalPiTxns, filteredAaPiTxns
		case internalPiTxnIndex >= len(internalPiTxns):
			filteredAaPiTxns = append(filteredAaPiTxns, aaTxns[aaTxnIndex])
			aaTxnIndex++
		case aaTxnIndex >= len(aaTxns):
			filteredInternalPiTxns = append(filteredInternalPiTxns, internalPiTxns[internalPiTxnIndex])
			internalPiTxnIndex++
		default:
			internalTxnTimestamp := internalPiTxns[internalPiTxnIndex].GetTransactions()[0].GetComputedExecutedAt()
			aaTxnTimestamp := aaTxns[aaTxnIndex].GetExecutedAt()
			if internalTxnTimestamp.AsTime().After(aaTxnTimestamp.AsTime()) {
				if isDescending {
					filteredInternalPiTxns = append(filteredInternalPiTxns, internalPiTxns[internalPiTxnIndex])
					internalPiTxnIndex++
				} else {
					filteredAaPiTxns = append(filteredAaPiTxns, aaTxns[aaTxnIndex])
					aaTxnIndex++
				}
			} else {
				if isDescending {
					filteredAaPiTxns = append(filteredAaPiTxns, aaTxns[aaTxnIndex])
					aaTxnIndex++
				} else {
					filteredInternalPiTxns = append(filteredInternalPiTxns, internalPiTxns[internalPiTxnIndex])
					internalPiTxnIndex++
				}
			}
		}
		count++
	}
	return filteredInternalPiTxns, filteredAaPiTxns
}

func getInternalTxnTimestamp(txn *paymentPb.Transaction) *timestamppb.Timestamp {
	if txn.GetCreditedAt().IsValid() {
		return txn.GetCreditedAt()
	}
	if txn.GetDebitedAt().IsValid() {
		return txn.DebitedAt
	}
	return txn.GetUpdatedAt()
}

// nolint: funlen
func (s *Service) getInternalPiActivities(ctx context.Context, orderWithTxns []*orderPb.OrderWithTransactions, currentActorId string, isAaAllowed bool, fetchForSoftDeletedUsers bool) ([]*actorActivityPb.GetActivitiesResponse_Activity, error) {
	var (
		secondActorIds          []string
		actorToEntityDetailsMap map[string]*actorPb.GetEntityDetailsResponse_EntityDetail
		actorActivityType       actorActivityPb.GetActivitiesResponse_Activity_Type
		activities              []*actorActivityPb.GetActivitiesResponse_Activity
		secondActorId           string
		shortIconURL            string
		err                     error
	)
	if len(orderWithTxns) == 0 {
		return nil, nil
	}
	for _, orderWithTxn := range orderWithTxns {
		order := orderWithTxn.GetOrder()
		if !isSavings(order) {
			secondActorId, _, err = getSecondaryActorIdWithBadge(currentActorId, order.GetFromActorId(), order.GetToActorId())
			if err != nil {
				return nil, fmt.Errorf("error fetching second actor id for order: %s", order.GetId())
			}
			secondActorIds = append(secondActorIds, secondActorId)
		}
	}

	if len(secondActorIds) != 0 {
		actorToEntityDetailsMap, err = s.getEntityDetailsForActors(ctx, secondActorIds, fetchForSoftDeletedUsers)
		if err != nil {
			return nil, err
		}
	}

	if len(orderWithTxns) != 0 && isAaAllowed {
		shortIconURL = s.iconUrls.FiBankIconUrl()
	}

	for _, orderWithTxn := range orderWithTxns {
		var activityTitle, iconUrl, timelineId, accountId string
		var accountType accountsPb.Type
		var amtBadge actorActivityPb.GetActivitiesResponse_Activity_AmountBadge
		order := orderWithTxn.GetOrder()
		secondActorId = ""
		switch {
		case order.Workflow == orderPb.OrderWorkflow_REWARDS_CREATE_SD:
			amtBadge = actorActivityPb.GetActivitiesResponse_Activity_CREDIT
			activityTitle = "Transfer for Smart deposit Reward"
			timelineId, err = s.getTimelineId(ctx, order.GetFromActorId(), order.GetToActorId())
			if err != nil {
				return nil, err
			}
		case isSavings(order):
			// deposit/sd creation etc.
			// TODO (priyansh) : optimize the rpc call with a batch api for savings txns
			piTo := orderWithTxn.Transactions[0].PiTo
			piFrom := orderWithTxn.Transactions[0].PiFrom
			err, activityTitle, iconUrl, amtBadge, accountId, accountType, actorActivityType = s.getDepositActivityDetails(ctx, piTo, piFrom, order)
			if err != nil {
				return nil, fmt.Errorf("error fetching name for activity :%s %w", order.GetUpdatedAt(), err)
			}
		case !isSavings(order):
			secondActorId, amtBadge, err = getSecondaryActorIdWithBadge(currentActorId, order.GetFromActorId(), order.GetToActorId())
			if err != nil {
				return nil, fmt.Errorf("error fetching second actor id for order: %s", order.GetId())
			}
			entityDetails, ok := actorToEntityDetailsMap[secondActorId]
			if !ok {
				return nil, fmt.Errorf("error fetching details for actor %s %w", secondActorId, err)
			}
			activityTitle = entityDetails.GetName().ToString()
			if order.GetProvenance() == orderPb.OrderProvenance_ATM {
				iconUrl = s.iconUrls.ATMWithdrawalIconUrl()
			} else {
				iconUrl = entityDetails.GetProfileImageUrl()
			}
			// TODO(raunak)<5954>: optimise this with a batch call
			timelineId, err = s.getTimelineId(ctx, order.GetFromActorId(), order.GetToActorId())
			if err != nil {
				return nil, fmt.Errorf("error fetching timeline for order: %s %w", order.GetId(), err)
			}
			accountType = s.getAccountTypeFromPiId(ctx, orderWithTxn.GetTransactions()[0].GetPiFrom())
			actorActivityType = getActivityType(orderWithTxn)
		}

		activity := getActivity(
			orderWithTxn.GetTransactions()[0].GetRemarks(),
			activityTitle,
			iconUrl,
			secondActorId,
			timelineId,
			amtBadge,
			accountId,
			order.GetId(),
			accountType,
			actorActivityType,
			order.GetAmount(),
			orderWithTxn.GetTransactions()[0].GetComputedExecutedAt(),
			shortIconURL,
			order.GetTags(),
			actorActivityPb.ActivityEntryPoint_ORDER,
		)
		activities = append(activities, activity)
	}
	return activities, nil
}

// getPartnerTagForTransaction : decides the partner tag based on the type of txn
func getPartnerTagForTransaction(orderTags []orderPb.OrderTag, accountType accountsPb.Type) *commontypes.VisualElement {
	var (
		partnerTag *commontypes.VisualElement
	)

	switch {
	case isUpiMandateTransaction(orderTags):
		partnerTag = commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/upi/icons/autopay_icon.png", 10, 64)
	case isRuPayCcTransaction(accountType):
		partnerTag = commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rupay_logo_white", 25, 38)
	default:
		// no default logo required for now
	}
	return partnerTag
}

func isRuPayCcTransaction(accountType accountsPb.Type) bool {
	return accountType == accountsPb.Type_CREDIT
}

func isUpiMandateTransaction(orderTags []orderPb.OrderTag) bool {
	return lo.Contains(orderTags, orderPb.OrderTag_UPI_MANDATES)
}

func (s *Service) getTimelineId(ctx context.Context, primaryActorId, secondaryActorId string) (string, error) {
	timelineRes, err := s.timelineClient.GetByActorIds(ctx, &timelinePb.GetByActorIdsRequest{
		PrimaryActorId:   primaryActorId,
		SecondaryActorId: secondaryActorId,
	})
	if err = epifigrpc.RPCError(timelineRes, err); err != nil {
		return "", err
	}
	return timelineRes.GetTimeline().GetId(), nil
}

// getAaActivities returns account aggregator related activities
func (s *Service) getAaActivities(ctx context.Context, aaTxns []*aaPb.Transaction, currentActorId string, fetchForSoftDeletedUsers bool) ([]*actorActivityPb.GetActivitiesResponse_Activity, error) {
	var (
		activities              []*actorActivityPb.GetActivitiesResponse_Activity
		secondActorIds          []string
		actorToEntityDetailsMap map[string]*actorPb.GetEntityDetailsResponse_EntityDetail
		err                     error
		secondActorId           string
		amtBadge                actorActivityPb.GetActivitiesResponse_Activity_AmountBadge
		bankIconUrl             string
	)
	if len(aaTxns) == 0 {
		return nil, nil
	}
	// contains all account ids for which bulk fetch is required
	var accountIdList []string
	// To avoid repeated account ids
	accountIdMap := make(map[string]bool)
	for _, aaTxn := range aaTxns {
		if _, found := accountIdMap[aaTxn.GetPayload().GetAccountId()]; !found {
			accountIdList = append(accountIdList, aaTxn.GetPayload().GetAccountId())
			accountIdMap[aaTxn.GetPayload().GetAccountId()] = true
		}
		secondActorId, _, err = getSecondaryActorIdWithBadge(currentActorId, aaTxn.GetPayload().GetFromActorId(), aaTxn.GetPayload().GetToActorId())
		if err != nil {
			return nil, fmt.Errorf("error fetching second actor for aa txn: %s %w", aaTxn.GetId(), err)
		}
		secondActorIds = append(secondActorIds, secondActorId)
	}

	// make batch call to get account details
	bulkAccountDetails, bulkAccDetailsErr := s.caClient.GetAccountDetailsBulk(ctx, &caPb.GetAccountDetailsBulkRequest{AccountIdList: accountIdList, AccountDetailsMaskList: []caExternalPb.AccountDetailsMask{caExternalPb.AccountDetailsMask_ACCOUNT_DETAILS_MASK_SUMMARY}})
	if bulkAccDetailsErr != nil {
		logger.Error(ctx, "error getting bulk account details", zap.Error(bulkAccDetailsErr))
		// suppressing and logging the error, as we don't want to return failure in case we are unable to fetch meta data for a bank
	}
	actorToEntityDetailsMap, err = s.getEntityDetailsForActors(ctx, secondActorIds, fetchForSoftDeletedUsers)
	if err != nil {
		return nil, err
	}

	for _, aaTxn := range aaTxns {
		secondActorId, amtBadge, err = getSecondaryActorIdWithBadge(currentActorId, aaTxn.GetPayload().GetFromActorId(), aaTxn.GetPayload().GetToActorId())
		if err != nil {
			return nil, fmt.Errorf("error fetching second actor for aa txn: %s %w", aaTxn.GetId(), err)
		}
		entityDetails, ok := actorToEntityDetailsMap[secondActorId]
		if !ok {
			return nil, fmt.Errorf("entity details not present for :%s", secondActorId)
		}
		// suppressing the error, as we don't want to return failure in case meta data for a bank is not present
		// TODO (sayan) : remove call to pkg method
		fipMeta, _ := connectedaccount.GetFipMetaByBank(aaTxn.GetBank())
		if fipMeta != nil {
			bankIconUrl = fipMeta.LogoUrl
		}
		if bulkAccountDetails != nil {
			if apsDetails, isPresent := bulkAccountDetails.GetAccountDetailsMap()[aaTxn.GetPayload().GetAccountId()]; isPresent {
				bankIconUrl = apsDetails.GetAccountDetails().GetFipMeta().GetLogoUrl()
			} else {
				logger.Error(ctx, "account details not present", zap.String(logger.ACCOUNT_ID, aaTxn.GetPayload().GetAccountId()))
			}
		}

		activity := getActivity(
			aaTxn.GetRemarks(),
			entityDetails.GetName().ToString(),
			entityDetails.GetProfileImageUrl(),
			secondActorId,
			"",
			amtBadge,
			"",
			aaTxn.GetId(),
			accountsPb.Type_TYPE_UNSPECIFIED,
			actorActivityPb.GetActivitiesResponse_Activity_ACTIVITY_TYPE_UNSPECIFIED,
			aaTxn.GetAmount(),
			aaTxn.GetExecutedAt(),
			bankIconUrl,
			nil,
			actorActivityPb.ActivityEntryPoint_AA,
		)
		activities = append(activities, activity)
	}
	return activities, nil
}

func getMergedActivities(internalActivities, aaActivities []*actorActivityPb.GetActivitiesResponse_Activity, isDescending bool) []*actorActivityPb.GetActivitiesResponse_Activity {
	var (
		activities            []*actorActivityPb.GetActivitiesResponse_Activity
		internalActivityIndex = 0
		aaActivityIndex       = 0
	)

	for {
		switch {
		case internalActivityIndex >= len(internalActivities) && aaActivityIndex >= len(aaActivities):
			return activities
		case internalActivityIndex >= len(internalActivities):
			activities = append(activities, aaActivities[aaActivityIndex])
			aaActivityIndex++
		case aaActivityIndex >= len(aaActivities):
			activities = append(activities, internalActivities[internalActivityIndex])
			internalActivityIndex++
		default:
			internalActivityTimestamp := internalActivities[internalActivityIndex].GetActivityTimestamp()
			aaActivityTimestamp := aaActivities[aaActivityIndex].GetActivityTimestamp()
			if internalActivityTimestamp.AsTime().After(aaActivityTimestamp.AsTime()) {
				if isDescending {
					activities = append(activities, internalActivities[internalActivityIndex])
					internalActivityIndex++
				} else {
					activities = append(activities, aaActivities[aaActivityIndex])
					aaActivityIndex++
				}
			} else {
				if isDescending {
					activities = append(activities, aaActivities[aaActivityIndex])
					aaActivityIndex++
				} else {
					activities = append(activities, internalActivities[internalActivityIndex])
					internalActivityIndex++
				}
			}
		}
	}
}

// isAaEnabledForCurrentActor returns if the user is enabled for connected account
func (s *Service) isAaEnabledForCurrentActor(ctx context.Context, currentActorId string) bool {
	if !s.connectedAccountUserGroupParams.IsAARestricted() {
		return true
	}

	userGrp, userGrpErr := s.userProcessor.GetUserGroupsByActorID(ctx, currentActorId)
	if userGrpErr != nil {
		logger.Error(ctx, "error fetching user group for actorId",
			zap.String(logger.ACTOR_ID, currentActorId), zap.Error(userGrpErr))
		return false
	}

	for _, grp := range s.connectedAccountUserGroupParams.AllowedUserGroupForAA() {
		if lo.Contains(userGrp, grp) {
			return true
		}
	}

	logger.Info(ctx, "actor not enabled for aa", zap.String(logger.ACTOR_ID, currentActorId))
	return false
}

func (s *Service) getAccountTypeFromPiId(ctx context.Context, piFrom string) accountsPb.Type {
	accPiResp, rpcErr := s.accountPiClient.GetByPiId(ctx, &accountPiPb.GetByPiIdRequest{PiId: piFrom})
	if err := epifigrpc.RPCError(accPiResp, rpcErr); err != nil {
		logger.Error(ctx, "error fetching account pi details for piId", zap.String(logger.PI_ID, piFrom), zap.Error(err))
		return accountsPb.Type_TYPE_UNSPECIFIED
	}
	return accPiResp.GetAccountType()
}
