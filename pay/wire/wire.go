//go:build wireinject
// +build wireinject

//go:generate wire
package wire

import (
	"crypto/tls"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"

	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/rulerover/ruleengine/baseengine"

	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"
	projectorPb "github.com/epifi/gamma/api/rewards/projector"
	tieringPb "github.com/epifi/gamma/api/tiering"

	"github.com/epifi/gamma/pay/internal/signalling"
	"github.com/epifi/gamma/pay/paymentrecommendationsystem"

	"github.com/google/wire"
	"github.com/slack-go/slack"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	oncev2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	forexPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/forex"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	payworkergenconfig "github.com/epifi/gamma/pay/config/worker/genconf"
	enrichOrderAndTxnProcessor "github.com/epifi/gamma/pay/internal/enrichorder"
	"github.com/epifi/gamma/pay/internationalfundtransfer/forex"
	iftValidation "github.com/epifi/gamma/pay/internationalfundtransfer/validation"
	iftPkg "github.com/epifi/gamma/pkg/internationalfundtransfer"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/datafetcher"
	iftInvoicePkg "github.com/epifi/gamma/pkg/internationalfundtransfer/invoice"
	iftTCSPkg "github.com/epifi/gamma/pkg/internationalfundtransfer/tcs"
	iftValidationPkg "github.com/epifi/gamma/pkg/internationalfundtransfer/validation"

	celestialPb "github.com/epifi/be-common/api/celestial"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountsStatmentPb "github.com/epifi/gamma/api/accounts/statement"
	actorPb "github.com/epifi/gamma/api/actor"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	authPb "github.com/epifi/gamma/api/auth"
	authOrchestratorPb "github.com/epifi/gamma/api/auth/orchestrator"
	"github.com/epifi/gamma/api/bankcust"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	docsPb "github.com/epifi/gamma/api/docs"
	ffPb "github.com/epifi/gamma/api/firefly"
	merchantPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	aaPb "github.com/epifi/gamma/api/order/aa"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	parserPb "github.com/epifi/gamma/api/parser"
	payPb "github.com/epifi/gamma/api/pay"
	internationalFundTransferPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	fileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	payIncidentManagerPb "github.com/epifi/gamma/api/pay/payincidentmanager"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/timeline"
	upiPb "github.com/epifi/gamma/api/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	usersPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	usStocksAccountPb "github.com/epifi/gamma/api/usstocks/account"
	usstocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/vendorgateway/location"
	vgAccountsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	vgIftPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	vgUpiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	vgPgPb "github.com/epifi/gamma/api/vendorgateway/pg"
	types4 "github.com/epifi/gamma/order/wire/types"
	payService "github.com/epifi/gamma/pay"
	payActivity "github.com/epifi/gamma/pay/activity"
	bms "github.com/epifi/gamma/pay/beneficiarymanagement"
	bmsDao "github.com/epifi/gamma/pay/beneficiarymanagement/dao"
	payServerConfig "github.com/epifi/gamma/pay/config/server"
	payServerGenConfig "github.com/epifi/gamma/pay/config/server/genconf"
	payWorkerConfig "github.com/epifi/gamma/pay/config/worker"
	"github.com/epifi/gamma/pay/cx"
	"github.com/epifi/gamma/pay/dao"
	payDao "github.com/epifi/gamma/pay/dao"
	"github.com/epifi/gamma/pay/developer"
	"github.com/epifi/gamma/pay/developer/processor"
	"github.com/epifi/gamma/pay/internal"
	"github.com/epifi/gamma/pay/internal/fundtransfernotification"
	"github.com/epifi/gamma/pay/internal/paymentauth"
	"github.com/epifi/gamma/pay/internal/pgprocessor"
	internationalFundTransfer "github.com/epifi/gamma/pay/internationalfundtransfer"
	"github.com/epifi/gamma/pay/internationalfundtransfer/aggregate"
	iftConsumer "github.com/epifi/gamma/pay/internationalfundtransfer/consumer"
	fileGenerator "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator"
	fileConsumer "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/consumer"
	fgDao "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/dao/impl"
	fileProcessorFactory "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/file_processor/factory"
	fgVfg "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator"
	vfgFederal "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/federal"
	federalA2FormFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/a2_form_file/federal"
	federalAggrTaxReportContents "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/aggr_tax_report/federal"
	federalBulkTaxPaymentFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/bulk_tax_payment/federal"
	federalGSTReportingFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/gst_reporting_file/federal"
	federalInwardFundTransferFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/inward_fund_transfer_file/federal"
	federalInwardRemGSTReportingFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/inward_gst_report/federal"
	federalLrsCheckFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/lrs_check_file/federal"
	federalLrsReportingFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/lrs_reporting_file/federal"
	federalMT199FileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/mt199_file/federal"
	federalMT199AttachmentContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/mt199_message_attachment/federal"
	federalRefundTransferFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/refund_transfer_file/federal"
	federalSofCheckFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/sof_check_file/federal"
	federalSwiftTransferFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/swift_transfer_file/federal"
	federalTCSReportingFileContent "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/tcs_reporting_file/federal"
	fng "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_name_generator"
	fsg "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_name_generator/sequence_number_generator"
	"github.com/epifi/gamma/pay/internationalfundtransfer/sof"
	sofDataFetcher "github.com/epifi/gamma/pay/internationalfundtransfer/sof/datafetcher"
	"github.com/epifi/gamma/pay/internationalfundtransfer/sof/migrator"
	"github.com/epifi/gamma/pay/payincidentmanager"
	payIncidentManagerConsumer "github.com/epifi/gamma/pay/payincidentmanager/consumer"
	"github.com/epifi/gamma/pay/payincidentmanager/incidentmanager/incidentprocessor"
	incidentTypeProcessor "github.com/epifi/gamma/pay/payincidentmanager/incidentmanager/incidenttype"
	pgconsumer "github.com/epifi/gamma/pay/paymentgateway/consumer"
	savingsAccountConsumer "github.com/epifi/gamma/pay/savings_account/consumer"
	types3 "github.com/epifi/gamma/pay/types"
	"github.com/epifi/gamma/pay/velocityengine"
	"github.com/epifi/gamma/pay/wire/provider"
	types2 "github.com/epifi/gamma/pay/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	iftGSTPkg "github.com/epifi/gamma/pkg/internationalfundtransfer/gst"
	"github.com/epifi/gamma/pkg/vendorstore"
	vgtypes "github.com/epifi/gamma/vendorgateway/wire/types"

	"github.com/epifi/gamma/pay/internal/decisionengine/factengine"
	"github.com/epifi/gamma/pay/internal/decisionengine/ruleengine/paymentinitiationengine"
	"github.com/epifi/gamma/pay/internal/decisionengine/rulesprovider"
)

func provideGormDb(db types.EpifiCRDB) *gorm.DB {
	return db
}

func FundTransferCelestialParamsProvider(genConf *payServerGenConfig.Config) *payServerGenConfig.FundTransferCelestialParams {
	return genConf.FundTransferCelestialParams()
}

func FundTransferParamsProvider(genConf *payServerGenConfig.Config) *payServerGenConfig.FundTransferParams {
	return genConf.FundTransferParams()
}

func PayOrderCacheConfigProvider(config *payServerConfig.Config) *payServerConfig.PayOrderCacheConfig {
	return config.PayOrderCacheConfig
}

func PayOrderCacheStorageProvider(payRedisStore types2.PayRedisStore) types2.PayOrderCacheStorage {
	return cache.NewRedisCacheStorage(payRedisStore)
}

func PayTransactionCacheConfigProvider(config *payServerConfig.Config) *payServerConfig.PayTransactionCacheConfig {
	return config.PayTransactionCacheConfig
}

func PayTransactionCacheStorageProvider(payRedisStore types2.PayRedisStore) types2.PayTransactionCacheStorage {
	return cache.NewRedisCacheStorage(payRedisStore)

}

func InitialisePayService(
	db types.EpifiCRDB,
	celestialClient celestialPb.CelestialClient,
	client orderPb.OrderServiceClient,
	actorClient actorPb.ActorClient,
	conf *payServerConfig.Config,
	savingsClient savingsPb.SavingsClient,
	engineClient paymentPb.DecisionEngineClient,
	userClient usersPb.UsersClient,
	authClient authPb.AuthClient,
	piClient piPb.PiClient,
	vgPayClient vgtypes.VgPaymentClientWithInterceptors,
	paymentClient paymentPb.PaymentClient,
	broker events.Broker,
	acPiClient account_pi.AccountPIRelationClient,
	genConf *payServerGenConfig.Config,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	txnAggregatesClient txnAggregatesPb.TxnAggregatesClient,
	timlineClient timeline.TimelineServiceClient,
	userGrpClient userGroupPb.GroupClient,
	inPaymentOrderPublisher types2.InPaymentOrderUpdatePublisher,
	bcClient bankcust.BankCustomerServiceClient,
	onboardingClient onboardingPb.OnboardingClient,
	s3Client payService.TxnBackfillS3Client,
	upiClient upiPb.UPIClient,
	balanceClient accountBalancePb.BalanceClient,
	pgClient vgPgPb.PaymentGatewayClient,
	fireflyClient ffPb.FireflyClient,
	nameCheckClient vgtypes.UNNameCheckClientWithInterceptors,
	temporalClient types2.PayClient,
	payRedisStore types2.PayRedisStore,
	dbTxnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	cpClient cpPb.CardProvisioningClient,
	orderUpdatePublisher types3.OrderUpdateEventPublisher,
	tieringClient tieringPb.TieringClient,
	aggregatesClient rewardsPinotPb.RewardsAggregatesClient,
	rewardsProjectorClient projectorPb.ProjectorServiceClient,
) *payService.Service {
	wire.Build(
		PayServerEntitySegregationFlagProvider,
		internal.WireSet,
		pgprocessor.WireSet,
		provider.ServicePgConfigProvider,
		enrichOrderAndTxnProcessor.WireSet,
		signalling.NewSignallingProcessorFactory,
		provider.OrderWorkflowToDomainDataMethodProvider,
		vgtypes.VgPaymentClientProvider,
		paymentauth.AuthFactoryWireSet,
		idgen.NewClock,
		idgen.WireSet,
		idgen.UuidGeneratorWireSet,
		dao.WireSet,
		types2.PayClientProvider,
		payService.NewService,
		FundTransferParamsProvider,
		FundTransferCelestialParamsProvider,
		ReportStalenessDurationProvider,
		release.EvaluatorWireSet,
		FeatureReleaseConfigProvider,
		NonSaUserTpapReleaseConfigProvider,
		SaUserTpapReleaseConfigProvider,
		NonSaUserTpapReleaseEvaluatorProvider,
		SaUserTpapReleaseEvaluatorProvider,
		vendorstore.NewVendorStore,
		vendorstore.VendorStoreDAOWireSet,
		PayOrderCacheConfigProvider,
		PayOrderCacheStorageProvider,
		PayTransactionCacheConfigProvider,
		PayTransactionCacheStorageProvider,
	)
	return &payService.Service{}
}

func GormProvider(db types.EpifiCRDB) *gorm.DB {
	return db
}

func NonSaUserTpapReleaseEvaluatorProvider(conf types2.TpapForNonSaUserFeatureConfig, factory release.ConstraintFactory) types2.TpapForNonSaUserEvaluator {
	return release.NewEvaluator(conf, factory)
}

func SaUserTpapReleaseEvaluatorProvider(conf types2.TpapForSaUserFeatureConfig, factory release.ConstraintFactory) types2.TpapForSaUserEvaluator {
	return release.NewEvaluator(conf, factory)
}

func NonSaUserTpapReleaseConfigProvider(conf *payServerGenConfig.Config) types2.TpapForNonSaUserFeatureConfig {
	return conf.TpapEntryPointSwitchConfigForNonSaUser()
}

func SaUserTpapReleaseConfigProvider(conf *payServerGenConfig.Config) types2.TpapForSaUserFeatureConfig {
	return conf.TpapEntryPointSwitchConfigForSaUser()
}

func ReportStalenessDurationProvider(conf *payServerGenConfig.Config) time.Duration {
	return conf.ExecutionReportGenerationParams().ReportStalenessDuration()
}

func FeatureReleaseConfigProvider(conf *payServerGenConfig.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func InitializeActivityProcessor(
	db types.EpifiCRDB,
	payBusinessClient paymentPb.BusinessClient,
	savingsClient savingsPb.SavingsClient,
	reportStalenessDuration time.Duration,
	payClient payPb.PayClient,
	parserClient parserPb.ParserClient,
	fileGenClient fileGenPb.FileGeneratorClient,
	s3Client s3.S3Client,
	actorClient actorPb.ActorClient,
	celestialClient celestialPb.CelestialClient,
	accountStatementClient accountsStatmentPb.AccountStatementClient,
	iftClient internationalFundTransferPb.InternationalFundTransferClient,
	timelineClient timeline.TimelineServiceClient,
	accountPiClient account_pi.AccountPIRelationClient,
	piClient piPb.PiClient,
	userGroupClient userGroupPb.GroupClient,
	userClient usersPb.UsersClient,
	fundTransferParams *payServerConfig.FundTransferParams,
	bcClient bankcust.BankCustomerServiceClient,
	payOrderCacheConfig *payServerConfig.PayOrderCacheConfig,
	payTransactionCacheConfig *payServerConfig.PayTransactionCacheConfig,
	conf *payWorkerConfig.Config,
	iftVgClient vgIftPb.InternationalFundTransferClient,
	balanceClient accountBalancePb.BalanceClient,
	vgUpiClient vgUpiPb.UPIClient,
	orderClient orderPb.OrderServiceClient,
	pgClient vgPgPb.PaymentGatewayClient,
	merchantClient merchantPb.MerchantServiceClient,
	orderUpdatePublisher types3.OrderUpdateEventPublisher,
	payRedisStore types2.PayRedisStore,
	vgAccountsClient vgAccountsPb.AccountsClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	forexClient forexPb.ForexServiceClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	lockRedisStore types2.LockRedisStore,
	dynConf *payworkergenconfig.Config,
	ussOrderManagerClient usstocksOrderPb.OrderManagerClient,
	txnDetailedStatusUpdateSnsPublisher types4.TxnDetailedStatusUpdateSnsPublisher,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	upiClient upiPb.UPIClient,
) *payActivity.Processor {
	wire.Build(
		WorkerEntitySegregationFlagProvider,
		pgprocessor.WireSet,
		idgen.NewClock,
		provider.WorkerPgConfigProvider,
		fundtransfernotification.FundTransferNotificationFactoryWireSet,
		idgen.WireSet,
		dao.WireSet,
		internal.WireSet,
		slackClientProvider,
		iftConfProvider,
		payActivity.NewProcessor,
		lock.RedisStatelessLockManagerWireSet,
		aggregate.IftAggregateWireSet,
		types2.LockRedisStoreClientProvider,
		PayOrderCacheStorageProvider,
		PayTransactionCacheStorageProvider,
		iftValidation.LRSLimitValidationWireSet,
		sof.SofLimitOverrideParamsGeneratorWireSet,
		iftPkg.RecurringOutwardRemittanceWireSet,
		sofDataFetcher.DataFetcherWireSet,
		migrator.SofMigratorWireSet,
	)
	return &payActivity.Processor{}
}

func iftConfProvider(conf *payWorkerConfig.Config) *payWorkerConfig.InternationalFundTransfer {
	return conf.InternationalFundTransfer
}

func slackClientProvider(conf *payWorkerConfig.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[payWorkerConfig.IFTReportsSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[payWorkerConfig.IFTReportsSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}

func InitializeFileConsumerService(
	db types.EpifiCRDB,
	awsConfig aws.Config,
	iftc internationalFundTransferPb.InternationalFundTransferClient,
	genConf *payServerGenConfig.Config,
	iftRemittanceFileProcessingEventPublisher types2.IFTRemittanceFileProcessingEventPublisher,
	celestialClient celestialPb.CelestialClient,
) *fileConsumer.Service {
	wire.Build(
		MaxPageSizeProvider,
		fgDao.FileGenerationAttemptWireSet,
		fileProcessorFactory.NewFileProcessorFactory,
		fileConsumer.NewService,
		InternationalFundTransferS3ClientProvider,
	)
	return &fileConsumer.Service{}
}

func HttpClientWithTransportProvider() *http.Client {
	return &http.Client{
		Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
	}
}

func InitializeForexService(
	txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	db types.EpifiCRDB,
	celestialClient celestialPb.CelestialClient,
	genConf *payServerGenConfig.Config,
) *forex.Service {
	wire.Build(
		forex.NewService,
		oncev2.NewDoOnce,
		payDao.WireSet,
		idgen.WireSet,
		idgen.NewClock,
	)
	return &forex.Service{}
}

func InitializeInternationalFundTransferService(
	db types.EpifiCRDB,
	genConf *payServerGenConfig.Config,
	celestialClient celestialPb.CelestialClient,
	orderClient orderPb.OrderServiceClient,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
	piClient piPb.PiClient,
	userClient usersPb.UsersClient,
	savingsClient savingsPb.SavingsClient,
	actorClient actorPb.ActorClient,
	fileGeneratorClient fileGenPb.FileGeneratorClient,
	awsConfig aws.Config,
	timelineClient timeline.TimelineServiceClient,
	temporalClient types2.PayClient,
	vgIft vgIftPb.InternationalFundTransferClient,
	userGrpClient userGroupPb.GroupClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	txnAggClient txnAggregatesPb.TxnAggregatesClient,
	onboardingClient onboarding.OnboardingClient,
	caClient connectedAccountPb.ConnectedAccountClient,
	usstocksOrderClient usstocksOrderPb.OrderManagerClient,
	payClient payPb.PayClient,
	redisClient types2.PayRedisStore,
	balanceClient accountBalancePb.BalanceClient,
	aaClient aaPb.AccountAggregatorClient,
	locationClient location.LocationClient,
	generateSofLimitStrategiesValuesPublisher types2.GenerateSofLimitStrategiesValuesPublisher,
	authClient authPb.AuthClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	config *payServerConfig.Config,
	forexClient forexPb.ForexServiceClient,
) *internationalFundTransfer.Service {
	wire.Build(
		PayServerEntitySegregationFlagProvider,
		provideGenerateSofLimitStrategiesValuesPublisher,
		HttpClientWithTransportProvider,
		idgen.NewClock,
		idgen.WireSet,
		payDao.WireSet,
		internal.WireSet,
		internationalFundTransfer.NewService,
		ReportStalenessDurationProvider,
		InternationalFundTransferS3ClientProvider,
		types2.PayClientProvider,
		oncev2.NewDoOnce,
		idgen.UuidGeneratorWireSet,
		datetime.WireDefaultTimeSet,
		types2.PayRedisStoreClientProvider,
		fgDao.FileGenerationAttemptWireSet,
		sof.SofLimitStrategiesFactoryWireSet,
		sof.SofLimitOverrideParamsGeneratorWireSet,
		migrator.SofMigratorWireSet,
		sofDataFetcher.DataFetcherWireSet,
		MaxPageSizeProvider,
		PayOrderCacheConfigProvider,
		PayOrderCacheStorageProvider,
		PayTransactionCacheConfigProvider,
		PayTransactionCacheStorageProvider,
		iftInvoicePkg.SIPInvoiceCalculatorWireSet,
		iftGSTPkg.SlabRateBasedGSTCalculatorWireSet,
		iftTCSPkg.APIBasedTCSCalculatorWireSet,
		iftTCSPkg.SlabRateBasedTCSCalculatorWireSet,
		datafetcher.TransactionAggregatesFetcherWireSet,
		iftValidationPkg.FYRemittanceAmountValidationWireSet,
		iftValidation.DailyRemittanceAmountValidationWireSet,
		iftValidation.LRSLimitValidationWireSet,
		iftValidation.OutwardRemittanceBulkValidationWireSet,
	)
	return &internationalFundTransfer.Service{}
}

func InitialiseIFTConsumer(
	db types.EpifiCRDB,
	client connectedAccountPb.ConnectedAccountClient,
	balanceClient accountBalancePb.BalanceClient,
	payClient payPb.PayClient,
	aggregatorClient aaPb.AccountAggregatorClient,
	savingsClient savingsPb.SavingsClient,
) *iftConsumer.ConsumerService {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		payDao.WireSet,
		sof.SofLimitStrategiesFactoryWireSet,
		sofDataFetcher.DataFetcherWireSet,
		iftConsumer.NewConsumerService,
		datetime.WireDefaultTimeSet,
	)
	return &iftConsumer.ConsumerService{}
}

func InternationalFundTransferS3ClientProvider(awsConfig aws.Config, genConf *payServerGenConfig.Config) s3.S3Client {
	client := s3.NewClient(awsConfig, genConf.InternationalFundTransfer().S3Bucket())
	return client
}

func MaxPageSizeProvider(conf *payServerGenConfig.Config) uint32 {
	return conf.InternationalFundTransfer().MaxPageSize()
}

func InitializeInternationalFundTransferFileGeneratorService(
	db types.EpifiCRDB,
	conf *payServerConfig.Config,
	genConf *payServerGenConfig.Config,
	awsConfig aws.Config,
	docsClient docsPb.DocsClient,
	iftc internationalFundTransferPb.InternationalFundTransferClient,
	celestialClient celestialPb.CelestialClient,
	iftRemittanceFileProcessingEventPublisher types2.IFTRemittanceFileProcessingEventPublisher,
	savingsClient savingsPb.SavingsClient,
	actorClient actorPb.ActorClient,
	usStocksOrderClient usstocksOrderPb.OrderManagerClient,
	usStocksAccountClient usStocksAccountPb.AccountManagerClient,
	balanceClient accountBalancePb.BalanceClient,
	payRedisStore types2.PayRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
) *fileGenerator.Service {
	wire.Build(
		fileProcessorFactory.NewFileProcessorFactory,
		PayServerEntitySegregationFlagProvider,
		MaxPageSizeProvider,
		fgDao.FileGenerationAttemptWireSet,
		fgDao.FileSequenceGeneratorWireSet,
		fgDao.FileEntityMappingWireSet,
		vfgFederal.NewFederalVendorFileGenerator,
		fileGenerator.NewVendorFileGeneratorFactory,
		federalLrsCheckFileContent.NewLrsCheckFileContent,
		federalSofCheckFileContent.NewSofCheckFileContent,
		federalSwiftTransferFileContent.NewSwiftTransferFileContent,
		federalRefundTransferFileContent.NewRefundTransferFileContent,
		federalInwardFundTransferFileContent.NewInwardFundTransferFileContent,
		federalA2FormFileContent.NewA2FormFileContent,
		federalLrsReportingFileContent.NewLrsReportingFileContent,
		federalBulkTaxPaymentFileContent.NewBulkTaxPaymentFileContent,
		federalGSTReportingFileContent.NewGSTReconFileContent,
		federalTCSReportingFileContent.NewTCSReconFileContent,
		federalInwardRemGSTReportingFileContent.NewFederalInwardRemGSTReportContent,
		federalMT199FileContent.NewMT199FileContent,
		federalMT199AttachmentContent.NewMt199MessageAttachmentContentGenerator,
		federalAggrTaxReportContents.NewAggrTaxReportContentsFederal,
		fng.NewFederalFileNameGenerator,
		fsg.IntradDaySequenceGeneratorWireSet,
		fileGenerator.NewService,
		HttpClientProvider,
		InternationalFundTransferS3ClientProvider,
		PayInternationalFundTransferS3ClientProvider,
		payDao.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		ReportStalenessDurationProvider,
		internal.WireSet,
		PayOrderCacheConfigProvider,
		PayOrderCacheStorageProvider,
		PayTransactionCacheConfigProvider,
		PayTransactionCacheStorageProvider,
	)
	return &fileGenerator.Service{}
}

func PayInternationalFundTransferS3ClientProvider(client s3.S3Client) fgVfg.PayInternationalFundTransferS3Client {
	return client
}

func HttpClientProvider() *http.Client {
	return &http.Client{}
}

func InitializeDevService(
	db types.EpifiCRDB,
	db2 types.PayPGDB,
	iftClient internationalFundTransferPb.InternationalFundTransferClient,
	conf *payServerConfig.Config,
	genConf *payServerGenConfig.Config,
	payRedisStore types2.PayRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
) *developer.PayDevService {
	wire.Build(
		PayServerEntitySegregationFlagProvider,
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
		developer.NewPayDevService,
		developer.NewDevFactory,
		processor.NewTransactionProcessor,
		processor.NewForexRateProcessor,
		processor.NewIFTChecksProcessor,
		processor.NewLrsChecks,
		MaxPageSizeProvider,
		fgDao.FileGenerationAttemptWireSet,
		fgDao.FileEntityMappingWireSet,
		processor.NewFileGenAttemptsWithEntities,
		processor.NewSofDetailsProcessor,
		PayTransactionCacheConfigProvider,
		PayTransactionCacheStorageProvider,
	)
	return &developer.PayDevService{}
}

func InitializeVelocityEngineService(payclient payPb.PayClient, conf *payServerConfig.Config) *velocityengine.Service {
	return velocityengine.NewService(payclient, conf)
}

func InitializePayIncidentManagerService(
	config *payServerConfig.Config,
	watsonClient watsonPb.WatsonClient,
	payClient payPb.PayClient,
	orderClient orderPb.OrderServiceClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	actorClient actorPb.ActorClient,
	userClient usersPb.UsersClient,
	userGrpClient userGroupPb.GroupClient,
	client upiPb.UPIClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	merchantClient merchantPb.MerchantServiceClient,
) *payincidentmanager.Service {
	wire.Build(
		provider.FeatureReleaseConfigProvider,
		release.StaticConfEvaluatorWireSet,
		payincidentmanager.NewService,
		incidentprocessor.IncidentProcessingWireSet,
		incidentTypeProcessor.WireSet,
	)
	return &payincidentmanager.Service{}
}

func InitializePayIncidentManagerConsumerService(
	config *payServerConfig.Config,
	payIncidentManagerClient payIncidentManagerPb.PayIncidentManagerClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	actorClient actorPb.ActorClient,
	merchantClient merchantPb.MerchantServiceClient,
	dynConfig *payServerGenConfig.Config,
	piClient piPb.PiClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	orderClient orderPb.OrderServiceClient,
	watsonClient watsonPb.WatsonClient,
) *payIncidentManagerConsumer.Service {
	wire.Build(
		internal.WireSet,
		payIncidentManagerConsumer.NewService,
		incidentTypeProcessor.WireSet,
	)
	return &payIncidentManagerConsumer.Service{}
}

func InitializeCxService(
	db types.EpifiCRDB,
	genConf *payServerGenConfig.Config,
	payServerConf *payServerConfig.Config,
	payRedisStore types2.PayRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
) *cx.Service {
	wire.Build(
		PayServerEntitySegregationFlagProvider,
		idgen.WireSet,
		idgen.NewClock,
		dao.WireSet,
		UniqueATMActorProvider,
		PageSizeToFetchTxnForActorProvider,
		ReportStalenessDurationProvider,
		pageSizeForChargeRelatedOrderAndTxnProvider,
		cx.NewService,
		PayOrderCacheConfigProvider,
		PayOrderCacheStorageProvider,
		PayTransactionCacheConfigProvider,
		PayTransactionCacheStorageProvider,
	)
	return &cx.Service{}
}

func InitializeBeneficiaryManagementService(db types.PayPGDB, authOrchestratorClient authOrchestratorPb.OrchestratorClient) *bms.Service {
	wire.Build(
		types.PayPGDBGormProvider,
		storagev2.DefaultTxnExecutorWireSet,
		idgen.WireSet,
		idgen.NewClock,
		bmsDao.BeneficiaryDaoWireSet,
		bms.NewService,
	)
	return &bms.Service{}
}

func InitializePaymentRecommendationSystemService(actorClient actorPb.ActorClient, genConf *payServerGenConfig.Config, paymentClient paymentPb.PaymentClient) *paymentrecommendationsystem.Service {
	wire.Build(
		paymentrecommendationsystem.NewService,
		rulesprovider.Provider,
		factengine.Provider,
		paymentinitiationengine.Provider,
		baseengine.Provider,
		provider.BaseRuleEngineConfigProvider,
	)
	return &paymentrecommendationsystem.Service{}
}

func UniqueATMActorProvider(genConf *payServerGenConfig.Config) string {
	return genConf.UniqueATMActorId()
}

func PageSizeToFetchTxnForActorProvider(genConf *payServerGenConfig.Config) int32 {
	return genConf.PageSizeToFetchTxnForATMActor()
}

func pageSizeForChargeRelatedOrderAndTxnProvider(genConf *payServerGenConfig.Config) uint32 {
	return genConf.PageSizeForChargeRelatedOrderAndTxn()
}

func InitializeSavingsAccountConsumer(savingsClient savingsPb.SavingsClient, accountBalanceClient accountBalancePb.BalanceClient) *savingsAccountConsumer.Consumer {
	wire.Build(
		savingsAccountConsumer.NewConsumer,
	)
	return &savingsAccountConsumer.Consumer{}
}

func provideGenerateSofLimitStrategiesValuesPublisher(publisher types2.GenerateSofLimitStrategiesValuesPublisher) queue.Publisher {
	return publisher
}

func PayServerEntitySegregationFlagProvider(conf *payServerGenConfig.Config) types4.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in pay config : %v", conf.EnableEntitySegregation()))
	return types4.EnableResourceProvider(conf.EnableEntitySegregation())
}

func WorkerEntitySegregationFlagProvider(conf *payworkergenconfig.Config) types4.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in pay worker config : %v", conf.EnableEntitySegregation()))
	return types4.EnableResourceProvider(conf.EnableEntitySegregation())
}

func InitialisePaymentGatewayConsumer(conf *payServerGenConfig.Config, db types.EpifiCRDB, temporalClient types2.PayClient, celestialClient celestialPb.CelestialClient, recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient, enachClient enachPb.EnachServiceClient) *pgconsumer.Consumer {
	wire.Build(pgconsumer.NewConsumer, payDao.WireSet)
	return &pgconsumer.Consumer{}
}
