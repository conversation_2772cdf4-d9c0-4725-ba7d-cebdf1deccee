// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"crypto/tls"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/rulerover/ruleengine/baseengine"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/statement"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/analyser/txnaggregates"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/orchestrator"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/aa"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/parser"
	pay2 "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer/forex"
	payincidentmanager2 "github.com/epifi/gamma/api/pay/payincidentmanager"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/gamma/api/rewards/pinot"
	"github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/timeline"
	"github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	onboarding2 "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/usstocks/account"
	order2 "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/vendorgateway/location"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	internationalfundtransfer2 "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	upi3 "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/api/vendorgateway/pg"
	types5 "github.com/epifi/gamma/order/wire/types"
	"github.com/epifi/gamma/pay"
	"github.com/epifi/gamma/pay/activity"
	"github.com/epifi/gamma/pay/beneficiarymanagement"
	dao2 "github.com/epifi/gamma/pay/beneficiarymanagement/dao"
	"github.com/epifi/gamma/pay/config/server"
	"github.com/epifi/gamma/pay/config/server/genconf"
	"github.com/epifi/gamma/pay/config/worker"
	genconf2 "github.com/epifi/gamma/pay/config/worker/genconf"
	"github.com/epifi/gamma/pay/cx"
	"github.com/epifi/gamma/pay/dao"
	internationalfundtransfer3 "github.com/epifi/gamma/pay/dao/internationalfundtransfer"
	"github.com/epifi/gamma/pay/developer"
	"github.com/epifi/gamma/pay/developer/processor"
	actor2 "github.com/epifi/gamma/pay/internal/actor"
	auth2 "github.com/epifi/gamma/pay/internal/auth"
	bankcust2 "github.com/epifi/gamma/pay/internal/bankcust"
	celestial2 "github.com/epifi/gamma/pay/internal/celestial"
	"github.com/epifi/gamma/pay/internal/decisionengine/factengine"
	"github.com/epifi/gamma/pay/internal/decisionengine/ruleengine/paymentinitiationengine"
	"github.com/epifi/gamma/pay/internal/decisionengine/rulesprovider"
	"github.com/epifi/gamma/pay/internal/decisionengine/rulesprovider/rules/imps"
	"github.com/epifi/gamma/pay/internal/decisionengine/rulesprovider/rules/intrabank"
	"github.com/epifi/gamma/pay/internal/decisionengine/rulesprovider/rules/neft"
	"github.com/epifi/gamma/pay/internal/decisionengine/rulesprovider/rules/rtgs"
	upi4 "github.com/epifi/gamma/pay/internal/decisionengine/rulesprovider/rules/upi"
	"github.com/epifi/gamma/pay/internal/enrichorder"
	"github.com/epifi/gamma/pay/internal/enrichorder/debitcardswitch"
	"github.com/epifi/gamma/pay/internal/fundtransfernotification"
	"github.com/epifi/gamma/pay/internal/fundtransfernotification/basenotificationprocessor"
	"github.com/epifi/gamma/pay/internal/notification"
	order3 "github.com/epifi/gamma/pay/internal/order"
	"github.com/epifi/gamma/pay/internal/orderWithTransactions"
	"github.com/epifi/gamma/pay/internal/paymentauth"
	paymentinstrument2 "github.com/epifi/gamma/pay/internal/paymentinstrument"
	"github.com/epifi/gamma/pay/internal/pgprocessor"
	"github.com/epifi/gamma/pay/internal/pgprocessor/razorpay"
	savings2 "github.com/epifi/gamma/pay/internal/savings"
	"github.com/epifi/gamma/pay/internal/signalling"
	timeline2 "github.com/epifi/gamma/pay/internal/timeline"
	"github.com/epifi/gamma/pay/internal/transactionaggregates"
	upi2 "github.com/epifi/gamma/pay/internal/upi"
	user2 "github.com/epifi/gamma/pay/internal/user"
	internationalfundtransfer5 "github.com/epifi/gamma/pay/internationalfundtransfer"
	"github.com/epifi/gamma/pay/internationalfundtransfer/aggregate"
	consumer2 "github.com/epifi/gamma/pay/internationalfundtransfer/consumer"
	file_generator2 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/consumer"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/dao/impl"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/file_processor/factory"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/federal"
	federal6 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/a2_form_file/federal"
	federal13 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/aggr_tax_report/federal"
	federal11 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/bulk_tax_payment/federal"
	federal8 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/gst_reporting_file/federal"
	federal5 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/inward_fund_transfer_file/federal"
	federal12 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/inward_gst_report/federal"
	federal2 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/lrs_check_file/federal"
	federal7 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/lrs_reporting_file/federal"
	federal10 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/mt199_file/federal"
	federal14 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/mt199_message_attachment/federal"
	federal4 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/refund_transfer_file/federal"
	federal3 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/sof_check_file/federal"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/swift_transfer_file/federal"
	federal9 "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_content_generator/tcs_reporting_file/federal"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_name_generator"
	"github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/vendor_file_generator/file_name_generator/sequence_number_generator"
	forex2 "github.com/epifi/gamma/pay/internationalfundtransfer/forex"
	"github.com/epifi/gamma/pay/internationalfundtransfer/sof"
	"github.com/epifi/gamma/pay/internationalfundtransfer/sof/datafetcher"
	"github.com/epifi/gamma/pay/internationalfundtransfer/sof/migrator"
	"github.com/epifi/gamma/pay/internationalfundtransfer/validation"
	"github.com/epifi/gamma/pay/payincidentmanager"
	consumer3 "github.com/epifi/gamma/pay/payincidentmanager/consumer"
	"github.com/epifi/gamma/pay/payincidentmanager/incidentmanager/incidentprocessor"
	"github.com/epifi/gamma/pay/payincidentmanager/incidentmanager/incidenttype"
	consumer5 "github.com/epifi/gamma/pay/paymentgateway/consumer"
	"github.com/epifi/gamma/pay/paymentrecommendationsystem"
	consumer4 "github.com/epifi/gamma/pay/savings_account/consumer"
	types4 "github.com/epifi/gamma/pay/types"
	"github.com/epifi/gamma/pay/velocityengine"
	"github.com/epifi/gamma/pay/wire/provider"
	types3 "github.com/epifi/gamma/pay/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf3 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	internationalfundtransfer4 "github.com/epifi/gamma/pkg/internationalfundtransfer"
	datafetcher2 "github.com/epifi/gamma/pkg/internationalfundtransfer/datafetcher"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/gst"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/invoice"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/tcs"
	validation2 "github.com/epifi/gamma/pkg/internationalfundtransfer/validation"
	"github.com/epifi/gamma/pkg/vendorstore"
	types2 "github.com/epifi/gamma/vendorgateway/wire/types"
	"github.com/slack-go/slack"
	"gorm.io/gorm"
	"net/http"
	"time"
)

// Injectors from wire.go:

func InitialisePayService(db types.EpifiCRDB, celestialClient celestial.CelestialClient, client order.OrderServiceClient, actorClient actor.ActorClient, conf *server.Config, savingsClient savings.SavingsClient, engineClient payment.DecisionEngineClient, userClient user.UsersClient, authClient auth.AuthClient, piClient paymentinstrument.PiClient, vgPayClient types2.VgPaymentClientWithInterceptors, paymentClient payment.PaymentClient, broker events.Broker, acPiClient account_pi.AccountPIRelationClient, genConf *genconf.Config, upiOnboardingClient onboarding.UpiOnboardingClient, txnAggregatesClient txnaggregates.TxnAggregatesClient, timlineClient timeline.TimelineServiceClient, userGrpClient group.GroupClient, inPaymentOrderPublisher types3.InPaymentOrderUpdatePublisher, bcClient bankcust.BankCustomerServiceClient, onboardingClient onboarding2.OnboardingClient, s3Client pay.TxnBackfillS3Client, upiClient upi.UPIClient, balanceClient balance.BalanceClient, pgClient pg.PaymentGatewayClient, fireflyClient firefly.FireflyClient, nameCheckClient types2.UNNameCheckClientWithInterceptors, temporalClient types3.PayClient, payRedisStore types3.PayRedisStore, dbTxnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], cpClient provisioning.CardProvisioningClient, orderUpdatePublisher types4.OrderUpdateEventPublisher, tieringClient tiering.TieringClient, aggregatesClient pinot.RewardsAggregatesClient, rewardsProjectorClient projector.ProjectorServiceClient) *pay.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	duration := ReportStalenessDurationProvider(genConf)
	enableResourceProvider := PayServerEntitySegregationFlagProvider(genConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(domainIdGenerator, duration, dbResourceProvider, db, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	payOrderCacheStorage := PayOrderCacheStorageProvider(payRedisStore)
	payOrderCacheConfig := PayOrderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, payOrderCacheStorage, payOrderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, payOrderCacheConfig)
	savingsProcessor := savings2.NewSavingsProcessor(actorClient, savingsClient, balanceClient)
	paymentPaymentClient := types2.VgPaymentClientProvider(vgPayClient)
	authProcessor := auth2.NewAuthProcessor(authClient)
	userProcessor := user2.NewUserProcessor(actorClient, userClient, userGrpClient, bcClient)
	piProcessor := paymentinstrument2.NewPIProcessor(piClient, acPiClient)
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	transactionDaoCRDB := dao.NewTransactionDao(domainIdGenerator, dbResourceProvider, dbTxnExecutorProvider, db, crdbIdempotentTxnExecutor, enableResourceProvider, attributedIdGen)
	payTransactionCacheStorage := PayTransactionCacheStorageProvider(payRedisStore)
	payTransactionCacheConfig := PayTransactionCacheConfigProvider(conf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, payTransactionCacheStorage, payTransactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, payTransactionCacheConfig)
	processor := celestial2.NewProcessor(celestialClient)
	fundTransferParams := FundTransferParamsProvider(genConf)
	fundTransferCelestialParams := FundTransferCelestialParamsProvider(genConf)
	paymentValidation := paymentauth.NewPaymentValidation(client, processor, fundTransferParams)
	baseVendorAuthorisation := paymentauth.NewVendorAuthorisation(authProcessor, piProcessor, userProcessor, savingsProcessor, paymentClient, paymentPaymentClient, fundTransferParams)
	fundTransferCelestialProcessor := paymentauth.NewFundTransferCelestialProcessor(paymentValidation, processor, baseVendorAuthorisation)
	fundTransferOmsProcessor := paymentauth.NewFundTransferOmsProcessor(client, paymentValidation, baseVendorAuthorisation)
	internationalFundTransferProcessor := paymentauth.NewInternationalFundTransferProcessor(paymentValidation, processor, baseVendorAuthorisation)
	authFactoryImpl := paymentauth.NewAuthFactoryImpl(fundTransferCelestialProcessor, fundTransferOmsProcessor, internationalFundTransferProcessor)
	timelineProcessor := timeline2.NewTimelineProcessor(timlineClient)
	actorProcessor := actor2.NewActorProcessor(actorClient, userClient, userGrpClient)
	featureReleaseConfig := FeatureReleaseConfigProvider(genConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	orderWithTransactionsProcessor := orderWithTransactions.NewProcessor(orderDao, transactionDao)
	upiProcessor := upi2.NewUpiProcessor(upiOnboardingClient, upiClient, piClient, acPiClient)
	tpapForNonSaUserFeatureConfig := NonSaUserTpapReleaseConfigProvider(genConf)
	tpapForNonSaUserEvaluator := NonSaUserTpapReleaseEvaluatorProvider(tpapForNonSaUserFeatureConfig, constraintFactoryImpl)
	tpapForSaUserFeatureConfig := SaUserTpapReleaseConfigProvider(genConf)
	tpapForSaUserEvaluator := SaUserTpapReleaseEvaluatorProvider(tpapForSaUserFeatureConfig, constraintFactoryImpl)
	vendorResponseCRDB := vendorstore.NewVendorResponseDAO(gormDB)
	vendorStore := vendorstore.NewVendorStore(vendorResponseCRDB)
	pgParams := provider.ServicePgConfigProvider(conf)
	uuidGenerator := idgen.NewUuidGenerator()
	pgProcessor := razorpay.NewPgProcessor(pgParams, orderDao, transactionDao, orderVendorOrderMapDaoCRDB, pgClient, piClient, userClient, actorClient, recurringPaymentClient, clock, uuidGenerator, dbTxnExecutorProvider)
	factoryImpl := pgprocessor.NewFactory(pgProcessor)
	v := provider.OrderWorkflowToDomainDataMethodProvider(fireflyClient)
	clientClient := types3.PayClientProvider(temporalClient)
	enrichOrderFromSwitchProcessor := debitcardswitch.NewEnrichOrderFromSwitchProcessor(orderDao, cpClient, orderUpdatePublisher, piClient, transactionDao, acPiClient, evaluator, genConf)
	enrichOrderAndTxnFactoryImpl := enrichorder.NewEnrichOrderAndTxnFactory(enrichOrderFromSwitchProcessor)
	signallingProcessorFactory := signalling.NewSignallingProcessorFactory(orderVendorOrderMapDaoCRDB)
	service := pay.NewService(orderDao, celestialClient, client, conf, genConf, engineClient, savingsProcessor, paymentClient, paymentPaymentClient, authProcessor, userProcessor, piProcessor, broker, acPiClient, transactionDao, upiOnboardingClient, txnAggregatesClient, processor, fundTransferParams, fundTransferCelestialParams, authFactoryImpl, timelineProcessor, actorProcessor, inPaymentOrderPublisher, piClient, evaluator, onboardingClient, orderWithTransactionsProcessor, s3Client, upiProcessor, savingsClient, tpapForNonSaUserEvaluator, tpapForSaUserEvaluator, pgClient, vendorStore, orderVendorOrderMapDaoCRDB, nameCheckClient, factoryImpl, v, clientClient, recurringPaymentClient, dbTxnExecutorProvider, enrichOrderAndTxnFactoryImpl, signallingProcessorFactory, tieringClient, aggregatesClient, upiClient, rewardsProjectorClient, actorClient)
	return service
}

func InitializeActivityProcessor(db types.EpifiCRDB, payBusinessClient payment.BusinessClient, savingsClient savings.SavingsClient, reportStalenessDuration time.Duration, payClient pay2.PayClient, parserClient parser.ParserClient, fileGenClient file_generator.FileGeneratorClient, s3Client s3.S3Client, actorClient actor.ActorClient, celestialClient celestial.CelestialClient, accountStatementClient statement.AccountStatementClient, iftClient internationalfundtransfer.InternationalFundTransferClient, timelineClient timeline.TimelineServiceClient, accountPiClient account_pi.AccountPIRelationClient, piClient paymentinstrument.PiClient, userGroupClient group.GroupClient, userClient user.UsersClient, fundTransferParams *server.FundTransferParams, bcClient bankcust.BankCustomerServiceClient, payOrderCacheConfig *server.PayOrderCacheConfig, payTransactionCacheConfig *server.PayTransactionCacheConfig, conf *worker.Config, iftVgClient internationalfundtransfer2.InternationalFundTransferClient, balanceClient balance.BalanceClient, vgUpiClient upi3.UPIClient, orderClient order.OrderServiceClient, pgClient pg.PaymentGatewayClient, merchantClient merchant.MerchantServiceClient, orderUpdatePublisher types4.OrderUpdateEventPublisher, payRedisStore types3.PayRedisStore, vgAccountsClient accounts.AccountsClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], forexClient forex.ForexServiceClient, connectedAccountClient connected_account.ConnectedAccountClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, lockRedisStore types3.LockRedisStore, dynConf *genconf2.Config, ussOrderManagerClient order2.OrderManagerClient, txnDetailedStatusUpdateSnsPublisher types5.TxnDetailedStatusUpdateSnsPublisher, upiOnboardingClient onboarding.UpiOnboardingClient, upiClient upi.UPIClient) *activity.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	enableResourceProvider := WorkerEntitySegregationFlagProvider(dynConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	transactionDaoCRDB := dao.NewTransactionDao(domainIdGenerator, dbResourceProvider, txnExecutorProvider, db, crdbIdempotentTxnExecutor, enableResourceProvider, attributedIdGen)
	payTransactionCacheStorage := PayTransactionCacheStorageProvider(payRedisStore)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, payTransactionCacheStorage, payTransactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, payTransactionCacheConfig)
	orderVendorOrderMapDaoCRDB := dao.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(domainIdGenerator, reportStalenessDuration, dbResourceProvider, db, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	payOrderCacheStorage := PayOrderCacheStorageProvider(payRedisStore)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, payOrderCacheStorage, payOrderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, payOrderCacheConfig)
	processor := order3.NewProcessor(orderDao)
	savingsProcessor := savings2.NewSavingsProcessor(actorClient, savingsClient, balanceClient)
	userProcessor := user2.NewUserProcessor(actorClient, userClient, userGroupClient, bcClient)
	actorProcessor := actor2.NewActorProcessor(actorClient, userClient, userGroupClient)
	piProcessor := paymentinstrument2.NewPIProcessor(piClient, accountPiClient)
	timelineProcessor := timeline2.NewTimelineProcessor(timelineClient)
	notificationProcessor := notification.NewProcessor()
	baseNotificationProcessor := basenotificationprocessor.NewBaseNotificationProcessor(fundTransferParams, userProcessor, savingsProcessor, actorProcessor, piProcessor, timelineProcessor, notificationProcessor)
	fundTransferNotificationFactoryImpl := fundtransfernotification.NewFundTransferNotificationFactoryImpl(baseNotificationProcessor)
	forexDaoCrdb := internationalfundtransfer3.NewForexDaoCrdb(db, domainIdGenerator)
	client := slackClientProvider(conf)
	internationalFundTransfer := iftConfProvider(conf)
	lrsChecksDaoCRDB := internationalfundtransfer3.NewLRSChecksDao(db)
	sofDetailDaoCRDB := internationalfundtransfer3.NewSofDetailDaoCRDB(db, domainIdGenerator)
	iftAggregateImpl := aggregate.NewIftAggregateImpl(payClient)
	pgParams := provider.WorkerPgConfigProvider(conf)
	uuidGenerator := idgen.NewUuidGenerator()
	pgProcessor := razorpay.NewPgProcessor(pgParams, orderDao, transactionDao, orderVendorOrderMapDaoCRDB, pgClient, piClient, userClient, actorClient, recurringPaymentClient, clock, uuidGenerator, txnExecutorProvider)
	factoryImpl := pgprocessor.NewFactory(pgProcessor)
	lrsLimitValidation := validation.NewLRSLimitValidation(lrsChecksDaoCRDB)
	checksDaoCRDB := internationalfundtransfer3.NewInternationalFundTransferChecksDao(db)
	sofMigrator := migrator.NewSofMigrator(checksDaoCRDB, sofDetailDaoCRDB)
	connectedAccountDataFetcher := datafetcher.NewConnectedAccountDataFetcher(connectedAccountClient)
	savingsAccountDataFetcher := datafetcher.NewSavingsAccountDataFetcher(balanceClient, savingsClient)
	sofLimitOverrideParamsGenerator := sof.NewSofLimitOverrideParamsGenerator(sofMigrator, connectedAccountDataFetcher, savingsAccountDataFetcher)
	recurringOutwardRemittance := internationalfundtransfer4.NewRecurringOutwardRemittance(recurringPaymentClient)
	redisClient := types3.LockRedisStoreClientProvider(lockRedisStore)
	redisStatelessLockManager := lock.NewRedisStatelessLockManager(redisClient, uuidGenerator)
	upiProcessor := upi2.NewUpiProcessor(upiOnboardingClient, upiClient, piClient, accountPiClient)
	activityProcessor := activity.NewProcessor(transactionDao, orderDao, payBusinessClient, savingsClient, processor, payClient, piClient, parserClient, fileGenClient, celestialClient, savingsProcessor, accountStatementClient, s3Client, iftClient, fundTransferNotificationFactoryImpl, forexDaoCrdb, client, internationalFundTransfer, lrsChecksDaoCRDB, crdbIdempotentTxnExecutor, iftVgClient, vgUpiClient, bcClient, orderClient, sofDetailDaoCRDB, iftAggregateImpl, factoryImpl, actorClient, timelineClient, merchantClient, orderUpdatePublisher, vgAccountsClient, ussOrderManagerClient, conf, dynConf, forexClient, lrsLimitValidation, sofLimitOverrideParamsGenerator, recurringOutwardRemittance, recurringPaymentClient, piProcessor, redisStatelessLockManager, txnDetailedStatusUpdateSnsPublisher, pgClient, accountPiClient, upiProcessor)
	return activityProcessor
}

func InitializeFileConsumerService(db types.EpifiCRDB, awsConfig aws.Config, iftc internationalfundtransfer.InternationalFundTransferClient, genConf *genconf.Config, iftRemittanceFileProcessingEventPublisher types3.IFTRemittanceFileProcessingEventPublisher, celestialClient celestial.CelestialClient) *consumer.Service {
	s3Client := InternationalFundTransferS3ClientProvider(awsConfig, genConf)
	uint32_2 := MaxPageSizeProvider(genConf)
	fileGenerationAttemptCrdb := impl.NewFileGenerationAttemptCrdb(db, uint32_2)
	fileProcessorFactory := factory.NewFileProcessorFactory(s3Client, iftc, genConf, fileGenerationAttemptCrdb, celestialClient)
	service := consumer.NewService(fileProcessorFactory, iftRemittanceFileProcessingEventPublisher)
	return service
}

func InitializeForexService(txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], db types.EpifiCRDB, celestialClient celestial.CelestialClient, genConf *genconf.Config) *forex2.Service {
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	doOnce := once.NewDoOnce(gormDB)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	forexDaoCrdb := internationalfundtransfer3.NewForexDaoCrdb(db, domainIdGenerator)
	service := forex2.NewService(crdbIdempotentTxnExecutor, doOnce, celestialClient, genConf, forexDaoCrdb)
	return service
}

func InitializeInternationalFundTransferService(db types.EpifiCRDB, genConf *genconf.Config, celestialClient celestial.CelestialClient, orderClient order.OrderServiceClient, accountPiRelationClient account_pi.AccountPIRelationClient, piClient paymentinstrument.PiClient, userClient user.UsersClient, savingsClient savings.SavingsClient, actorClient actor.ActorClient, fileGeneratorClient file_generator.FileGeneratorClient, awsConfig aws.Config, timelineClient timeline.TimelineServiceClient, temporalClient types3.PayClient, vgIft internationalfundtransfer2.InternationalFundTransferClient, userGrpClient group.GroupClient, bankCustomerServiceClient bankcust.BankCustomerServiceClient, txnAggClient txnaggregates.TxnAggregatesClient, onboardingClient onboarding2.OnboardingClient, caClient connected_account.ConnectedAccountClient, usstocksOrderClient order2.OrderManagerClient, payClient pay2.PayClient, redisClient types3.PayRedisStore, balanceClient balance.BalanceClient, aaClient aa.AccountAggregatorClient, locationClient location.LocationClient, generateSofLimitStrategiesValuesPublisher types3.GenerateSofLimitStrategiesValuesPublisher, authClient auth.AuthClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], config *server.Config, forexClient forex.ForexServiceClient) *internationalfundtransfer5.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	enableResourceProvider := PayServerEntitySegregationFlagProvider(genConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	transactionDaoCRDB := dao.NewTransactionDao(domainIdGenerator, dbResourceProvider, txnExecutorProvider, db, crdbIdempotentTxnExecutor, enableResourceProvider, attributedIdGen)
	payTransactionCacheStorage := PayTransactionCacheStorageProvider(redisClient)
	payTransactionCacheConfig := PayTransactionCacheConfigProvider(config)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, payTransactionCacheStorage, payTransactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, payTransactionCacheConfig)
	duration := ReportStalenessDurationProvider(genConf)
	orderVendorOrderMapDaoCRDB := dao.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(domainIdGenerator, duration, dbResourceProvider, db, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	payOrderCacheStorage := PayOrderCacheStorageProvider(redisClient)
	payOrderCacheConfig := PayOrderCacheConfigProvider(config)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, payOrderCacheStorage, payOrderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, payOrderCacheConfig)
	piProcessor := paymentinstrument2.NewPIProcessor(piClient, accountPiRelationClient)
	userProcessor := user2.NewUserProcessor(actorClient, userClient, userGrpClient, bankCustomerServiceClient)
	savingsProcessor := savings2.NewSavingsProcessor(actorClient, savingsClient, balanceClient)
	actorProcessor := actor2.NewActorProcessor(actorClient, userClient, userGrpClient)
	timelineProcessor := timeline2.NewTimelineProcessor(timelineClient)
	s3Client := InternationalFundTransferS3ClientProvider(awsConfig, genConf)
	checksDaoCRDB := internationalfundtransfer3.NewInternationalFundTransferChecksDao(db)
	forexDaoCrdb := internationalfundtransfer3.NewForexDaoCrdb(db, domainIdGenerator)
	client := types3.PayClientProvider(temporalClient)
	httpClient := HttpClientWithTransportProvider()
	bankCustomerProcessor := bankcust2.NewBankCustomerProcessor(bankCustomerServiceClient)
	txnAggregatesProcessor := transactionaggregates.NewTxnAggregatesProcessor(txnAggClient)
	doOnce := once.NewDoOnce(gormDB)
	usersBlacklistDaoCRDB := internationalfundtransfer3.NewInternationalFundTransferUsersBlacklistDao(db)
	uuidGenerator := idgen.NewUuidGenerator()
	lrsChecksDaoCRDB := internationalfundtransfer3.NewLRSChecksDao(db)
	defaultTime := datetime.NewDefaultTime()
	client2 := types3.PayRedisStoreClientProvider(redisClient)
	uint32_2 := MaxPageSizeProvider(genConf)
	fileGenerationAttemptCrdb := impl.NewFileGenerationAttemptCrdb(db, uint32_2)
	sofDetailDaoCRDB := internationalfundtransfer3.NewSofDetailDaoCRDB(db, domainIdGenerator)
	connectedAccountDataFetcher := datafetcher.NewConnectedAccountDataFetcher(caClient)
	savingsAccountDataFetcher := datafetcher.NewSavingsAccountDataFetcher(balanceClient, savingsClient)
	sofLimitStrategiesFactory := sof.NewSofLimitStrategiesFactory(sofDetailDaoCRDB, payClient, aaClient, connectedAccountDataFetcher, savingsAccountDataFetcher, defaultTime)
	sofMigrator := migrator.NewSofMigrator(checksDaoCRDB, sofDetailDaoCRDB)
	sofLimitOverrideParamsGenerator := sof.NewSofLimitOverrideParamsGenerator(sofMigrator, connectedAccountDataFetcher, savingsAccountDataFetcher)
	publisher := provideGenerateSofLimitStrategiesValuesPublisher(generateSofLimitStrategiesValuesPublisher)
	authProcessor := auth2.NewAuthProcessor(authClient)
	slabRateBasedGSTCalculator := gst.NewSlabRateBasedGSTCalculator()
	apiBasedTCSCalculator := tcs.NewAPIBasedTCSCalculator(bankCustomerServiceClient, vgIft)
	slabRateBasedTCSCalculator := tcs.NewSlabRateBasedTCSCalculator()
	sipInvoiceCalculator := invoice.NewSIPInvoiceCalculator(slabRateBasedGSTCalculator, apiBasedTCSCalculator, slabRateBasedTCSCalculator)
	outwardRemittanceBulkValidation := validation.NewOutwardRemittanceBulkValidation(genConf, authProcessor, bankCustomerProcessor, savingsProcessor, vgIft, txnAggregatesProcessor, usersBlacklistDaoCRDB, orderClient, actorClient, savingsClient, balanceClient)
	transactionAggregatesFetcher := datafetcher2.NewTransactionAggregatesFetcher(payClient)
	dailyRemittanceAmountValidation := validation.NewDailyRemittanceAmountValidation(genConf, transactionAggregatesFetcher)
	lrsLimitValidation := validation.NewLRSLimitValidation(lrsChecksDaoCRDB)
	fyRemittanceAmountValidation := validation2.NewFYRemittanceAmountValidation(transactionAggregatesFetcher)
	service := internationalfundtransfer5.NewService(genConf, celestialClient, orderClient, transactionDao, orderDao, accountPiRelationClient, piProcessor, actorClient, userProcessor, savingsProcessor, actorProcessor, timelineProcessor, fileGeneratorClient, s3Client, checksDaoCRDB, forexDaoCrdb, vgIft, client, httpClient, onboardingClient, bankCustomerProcessor, txnAggregatesProcessor, crdbIdempotentTxnExecutor, savingsClient, caClient, doOnce, usersBlacklistDaoCRDB, usstocksOrderClient, payClient, uuidGenerator, lrsChecksDaoCRDB, defaultTime, client2, fileGenerationAttemptCrdb, balanceClient, sofLimitStrategiesFactory, sofDetailDaoCRDB, locationClient, sofMigrator, sofLimitOverrideParamsGenerator, publisher, authProcessor, slabRateBasedGSTCalculator, sipInvoiceCalculator, outwardRemittanceBulkValidation, dailyRemittanceAmountValidation, lrsLimitValidation, fyRemittanceAmountValidation, forexClient)
	return service
}

func InitialiseIFTConsumer(db types.EpifiCRDB, client connected_account.ConnectedAccountClient, balanceClient balance.BalanceClient, payClient pay2.PayClient, aggregatorClient aa.AccountAggregatorClient, savingsClient savings.SavingsClient) *consumer2.ConsumerService {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	sofDetailDaoCRDB := internationalfundtransfer3.NewSofDetailDaoCRDB(db, domainIdGenerator)
	connectedAccountDataFetcher := datafetcher.NewConnectedAccountDataFetcher(client)
	savingsAccountDataFetcher := datafetcher.NewSavingsAccountDataFetcher(balanceClient, savingsClient)
	defaultTime := datetime.NewDefaultTime()
	sofLimitStrategiesFactory := sof.NewSofLimitStrategiesFactory(sofDetailDaoCRDB, payClient, aggregatorClient, connectedAccountDataFetcher, savingsAccountDataFetcher, defaultTime)
	consumerService := consumer2.NewConsumerService(sofDetailDaoCRDB, sofLimitStrategiesFactory)
	return consumerService
}

func InitializeInternationalFundTransferFileGeneratorService(db types.EpifiCRDB, conf *server.Config, genConf *genconf.Config, awsConfig aws.Config, docsClient docs.DocsClient, iftc internationalfundtransfer.InternationalFundTransferClient, celestialClient celestial.CelestialClient, iftRemittanceFileProcessingEventPublisher types3.IFTRemittanceFileProcessingEventPublisher, savingsClient savings.SavingsClient, actorClient actor.ActorClient, usStocksOrderClient order2.OrderManagerClient, usStocksAccountClient account.AccountManagerClient, balanceClient balance.BalanceClient, payRedisStore types3.PayRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *file_generator2.Service {
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	fileSequenceGeneratorCrdb := impl.NewFileSequenceGeneratorCrdb(db)
	intraDaySequenceNumberGenerator := sequence_number_generator.NewIntraDaySequenceNumberGenerator(fileSequenceGeneratorCrdb, crdbIdempotentTxnExecutor)
	federalFileNameGenerator := file_name_generator.NewFederalFileNameGenerator(intraDaySequenceNumberGenerator)
	s3Client := InternationalFundTransferS3ClientProvider(awsConfig, genConf)
	payInternationalFundTransferS3Client := PayInternationalFundTransferS3ClientProvider(s3Client)
	federalVendorFileGenerator := federal_vendor_file_generator.NewFederalVendorFileGenerator(federalFileNameGenerator, payInternationalFundTransferS3Client)
	swiftTransferFileContent := federal.NewSwiftTransferFileContent(genConf, iftc)
	lrsCheckFileContent := federal2.NewLrsCheckFileContent(genConf, iftc)
	sofCheckFileContent := federal3.NewSofCheckFileContent(genConf)
	refundTransferFileContent := federal4.NewRefundTransferFileContent(genConf, iftc)
	inwardFundTransferFileContent := federal5.NewInwardFundTransferFileContent(genConf, iftc)
	client := HttpClientProvider()
	a2FormFileContent := federal6.NewA2FormFileContent(genConf, client, docsClient, iftc)
	lrsReportingFileContent := federal7.NewLrsReportingFileContent(genConf, iftc)
	gstReconFileContent := federal8.NewGSTReconFileContent(genConf, iftc)
	tcsReconFileContent := federal9.NewTCSReconFileContent(genConf, iftc)
	mt199FileContent := federal10.NewMT199FileContent(genConf, iftc, s3Client)
	bulkTaxPaymentFileContent := federal11.NewBulkTaxPaymentFileContent(genConf, iftc)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	duration := ReportStalenessDurationProvider(genConf)
	enableResourceProvider := PayServerEntitySegregationFlagProvider(genConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(domainIdGenerator, duration, dbResourceProvider, db, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	payOrderCacheStorage := PayOrderCacheStorageProvider(payRedisStore)
	payOrderCacheConfig := PayOrderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, payOrderCacheStorage, payOrderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, payOrderCacheConfig)
	fileEntityMappingCrdb := impl.NewFileEntityMappingCrdb(db)
	savingsProcessor := savings2.NewSavingsProcessor(actorClient, savingsClient, balanceClient)
	inwardRemGSTReportContent := federal12.NewFederalInwardRemGSTReportContent(iftc, fileEntityMappingCrdb, savingsProcessor, usStocksOrderClient)
	aggrTaxReportFileContentsFederal := federal13.NewAggrTaxReportContentsFederal(genConf, iftc, celestialClient, orderDao, gstReconFileContent, tcsReconFileContent, inwardRemGSTReportContent)
	mt199MessageAttachmentContentGenerator := federal14.NewMt199MessageAttachmentContentGenerator(genConf, orderDao, usStocksAccountClient, celestialClient)
	vendorFileGeneratorFactory := file_generator2.NewVendorFileGeneratorFactory(federalVendorFileGenerator, swiftTransferFileContent, lrsCheckFileContent, sofCheckFileContent, refundTransferFileContent, inwardFundTransferFileContent, a2FormFileContent, lrsReportingFileContent, gstReconFileContent, tcsReconFileContent, mt199FileContent, bulkTaxPaymentFileContent, aggrTaxReportFileContentsFederal, mt199MessageAttachmentContentGenerator)
	uint32_2 := MaxPageSizeProvider(genConf)
	fileGenerationAttemptCrdb := impl.NewFileGenerationAttemptCrdb(db, uint32_2)
	transactionDaoCRDB := dao.NewTransactionDao(domainIdGenerator, dbResourceProvider, txnExecutorProvider, db, crdbIdempotentTxnExecutor, enableResourceProvider, attributedIdGen)
	payTransactionCacheStorage := PayTransactionCacheStorageProvider(payRedisStore)
	payTransactionCacheConfig := PayTransactionCacheConfigProvider(conf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, payTransactionCacheStorage, payTransactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, payTransactionCacheConfig)
	fileProcessorFactory := factory.NewFileProcessorFactory(s3Client, iftc, genConf, fileGenerationAttemptCrdb, celestialClient)
	service := file_generator2.NewService(genConf, crdbIdempotentTxnExecutor, vendorFileGeneratorFactory, fileGenerationAttemptCrdb, iftc, celestialClient, fileEntityMappingCrdb, orderDao, s3Client, transactionDao, iftRemittanceFileProcessingEventPublisher, awsConfig, fileProcessorFactory)
	return service
}

func InitializeDevService(db types.EpifiCRDB, db2 types.PayPGDB, iftClient internationalfundtransfer.InternationalFundTransferClient, conf *server.Config, genConf *genconf.Config, payRedisStore types3.PayRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *developer.PayDevService {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	enableResourceProvider := PayServerEntitySegregationFlagProvider(genConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	transactionDaoCRDB := dao.NewTransactionDao(domainIdGenerator, dbResourceProvider, txnExecutorProvider, db, crdbIdempotentTxnExecutor, enableResourceProvider, attributedIdGen)
	payTransactionCacheStorage := PayTransactionCacheStorageProvider(payRedisStore)
	payTransactionCacheConfig := PayTransactionCacheConfigProvider(conf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, payTransactionCacheStorage, payTransactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, payTransactionCacheConfig)
	transactionProcessor := processor.NewTransactionProcessor(transactionDao)
	forexDaoCrdb := internationalfundtransfer3.NewForexDaoCrdb(db, domainIdGenerator)
	forexRateProcessor := processor.NewForexRateProcessor(forexDaoCrdb)
	checksDaoCRDB := internationalfundtransfer3.NewInternationalFundTransferChecksDao(db)
	iftChecksProcessor := processor.NewIFTChecksProcessor(checksDaoCRDB, iftClient)
	lrsChecksDaoCRDB := internationalfundtransfer3.NewLRSChecksDao(db)
	lrsChecks := processor.NewLrsChecks(lrsChecksDaoCRDB)
	uint32_2 := MaxPageSizeProvider(genConf)
	fileGenerationAttemptCrdb := impl.NewFileGenerationAttemptCrdb(db, uint32_2)
	fileEntityMappingCrdb := impl.NewFileEntityMappingCrdb(db)
	fileGenAttemptsWithEntities := processor.NewFileGenAttemptsWithEntities(fileGenerationAttemptCrdb, fileEntityMappingCrdb)
	sofDetailDaoCRDB := internationalfundtransfer3.NewSofDetailDaoCRDB(db, domainIdGenerator)
	sofDetailsProcessor := processor.NewSofDetailsProcessor(sofDetailDaoCRDB)
	devFactory := developer.NewDevFactory(transactionProcessor, forexRateProcessor, iftChecksProcessor, lrsChecks, fileGenAttemptsWithEntities, sofDetailsProcessor)
	payDevService := developer.NewPayDevService(devFactory)
	return payDevService
}

func InitializePayIncidentManagerService(config *server.Config, watsonClient watson.WatsonClient, payClient pay2.PayClient, orderClient order.OrderServiceClient, accountPiClient account_pi.AccountPIRelationClient, actorClient actor.ActorClient, userClient user.UsersClient, userGrpClient group.GroupClient, client upi.UPIClient, upiOnboardingClient onboarding.UpiOnboardingClient, merchantClient merchant.MerchantServiceClient) *payincidentmanager.Service {
	featureReleaseConfig := provider.FeatureReleaseConfigProvider(config)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewStaticConfEvaluator(featureReleaseConfig, constraintFactoryImpl)
	baseIncidentProcessor := incidentprocessor.NewBaseIncidentProcessor(config, watsonClient, actorClient, userClient, userGrpClient, evaluator)
	upiPinFlowErrorProcessor := incidentprocessor.NewUpiPinFlowErrorProcessor(config, orderClient, payClient, watsonClient, accountPiClient, client, upiOnboardingClient, baseIncidentProcessor)
	incidenttypeProcessor := incidenttype.NewProcessor(config, orderClient, merchantClient)
	defaultTransactionHandlerProcessor := incidentprocessor.NewDefaultTransactionHandlerProcessor(config, orderClient, payClient, watsonClient, baseIncidentProcessor, incidenttypeProcessor)
	incidentProcessingFactoryImpl := incidentprocessor.NewIncidentProcessorFactoryImpl(upiPinFlowErrorProcessor, defaultTransactionHandlerProcessor)
	service := payincidentmanager.NewService(config, watsonClient, payClient, orderClient, incidentProcessingFactoryImpl)
	return service
}

func InitializePayIncidentManagerConsumerService(config *server.Config, payIncidentManagerClient payincidentmanager2.PayIncidentManagerClient, accountPiClient account_pi.AccountPIRelationClient, actorClient actor.ActorClient, merchantClient merchant.MerchantServiceClient, dynConfig *genconf.Config, piClient paymentinstrument.PiClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], orderClient order.OrderServiceClient, watsonClient watson.WatsonClient) *consumer3.Service {
	piProcessor := paymentinstrument2.NewPIProcessor(piClient, accountPiClient)
	incidenttypeProcessor := incidenttype.NewProcessor(config, orderClient, merchantClient)
	service := consumer3.NewService(config, payIncidentManagerClient, accountPiClient, actorClient, merchantClient, dynConfig, piProcessor, orderClient, watsonClient, incidenttypeProcessor)
	return service
}

func InitializeCxService(db types.EpifiCRDB, genConf *genconf.Config, payServerConf *server.Config, payRedisStore types3.PayRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *cx.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	duration := ReportStalenessDurationProvider(genConf)
	enableResourceProvider := PayServerEntitySegregationFlagProvider(genConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(domainIdGenerator, duration, dbResourceProvider, db, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	payOrderCacheStorage := PayOrderCacheStorageProvider(payRedisStore)
	payOrderCacheConfig := PayOrderCacheConfigProvider(payServerConf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, payOrderCacheStorage, payOrderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, payOrderCacheConfig)
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	transactionDaoCRDB := dao.NewTransactionDao(domainIdGenerator, dbResourceProvider, txnExecutorProvider, db, crdbIdempotentTxnExecutor, enableResourceProvider, attributedIdGen)
	payTransactionCacheStorage := PayTransactionCacheStorageProvider(payRedisStore)
	payTransactionCacheConfig := PayTransactionCacheConfigProvider(payServerConf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, payTransactionCacheStorage, payTransactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, payTransactionCacheConfig)
	string2 := UniqueATMActorProvider(genConf)
	int32_2 := PageSizeToFetchTxnForActorProvider(genConf)
	uint32_2 := pageSizeForChargeRelatedOrderAndTxnProvider(genConf)
	service := cx.NewService(orderDao, transactionDao, string2, int32_2, uint32_2)
	return service
}

func InitializeBeneficiaryManagementService(db types.PayPGDB, authOrchestratorClient orchestrator.OrchestratorClient) *beneficiarymanagement.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	beneficiaryDaoPgdb := dao2.NewBeneficiaryDaoPgdb(db, domainIdGenerator)
	beneficiaryActivationLogDaoPgdb := dao2.NewBeneficiaryActivationLogDaoPgdb(db)
	gormDB := types.PayPGDBGormProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	service := beneficiarymanagement.NewService(beneficiaryDaoPgdb, beneficiaryActivationLogDaoPgdb, authOrchestratorClient, gormTxnExecutor)
	return service
}

func InitializePaymentRecommendationSystemService(actorClient actor.ActorClient, genConf *genconf.Config, paymentClient payment.PaymentClient) *paymentrecommendationsystem.Service {
	engine := factengine.NewEngine(actorClient, paymentClient)
	ruleEngineConfig := provider.BaseRuleEngineConfigProvider(genConf)
	baseEngine := baseengine.NewBaseEngine(ruleEngineConfig)
	paymentInitiationEngine := paymentinitiationengine.NewPaymentInitiationEngine(baseEngine)
	preferredUPI := upi4.NewPreferredUPI()
	preferredIMPS := imps.NewPreferredIMPS()
	preferredRTGS := rtgs.NewPreferredRTGS()
	preferredNEFT := neft.NewPreferredNEFT()
	preferredINTRABANK := intrabank.NewPreferredINTRABANK()
	rulesProvider := rulesprovider.NewRulesProvider(preferredUPI, preferredIMPS, preferredRTGS, preferredNEFT, preferredINTRABANK)
	service := paymentrecommendationsystem.NewService(engine, paymentInitiationEngine, rulesProvider)
	return service
}

func InitializeSavingsAccountConsumer(savingsClient savings.SavingsClient, accountBalanceClient balance.BalanceClient) *consumer4.Consumer {
	consumerConsumer := consumer4.NewConsumer(savingsClient, accountBalanceClient)
	return consumerConsumer
}

func InitialisePaymentGatewayConsumer(conf *genconf.Config, db types.EpifiCRDB, temporalClient types3.PayClient, celestialClient celestial.CelestialClient, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, enachClient enach.EnachServiceClient) *consumer5.Consumer {
	orderVendorOrderMapDaoCRDB := dao.NewOrderVendorOrderMapDao(db)
	consumerConsumer := consumer5.NewConsumer(conf, temporalClient, orderVendorOrderMapDaoCRDB, celestialClient, recurringPaymentClient, enachClient)
	return consumerConsumer
}

// wire.go:

func provideGormDb(db types.EpifiCRDB) *gorm.DB {
	return db
}

func FundTransferCelestialParamsProvider(genConf *genconf.Config) *genconf.FundTransferCelestialParams {
	return genConf.FundTransferCelestialParams()
}

func FundTransferParamsProvider(genConf *genconf.Config) *genconf.FundTransferParams {
	return genConf.FundTransferParams()
}

func PayOrderCacheConfigProvider(config *server.Config) *server.PayOrderCacheConfig {
	return config.PayOrderCacheConfig
}

func PayOrderCacheStorageProvider(payRedisStore types3.PayRedisStore) types3.PayOrderCacheStorage {
	return cache.NewRedisCacheStorage(payRedisStore)
}

func PayTransactionCacheConfigProvider(config *server.Config) *server.PayTransactionCacheConfig {
	return config.PayTransactionCacheConfig
}

func PayTransactionCacheStorageProvider(payRedisStore types3.PayRedisStore) types3.PayTransactionCacheStorage {
	return cache.NewRedisCacheStorage(payRedisStore)

}

func GormProvider(db types.EpifiCRDB) *gorm.DB {
	return db
}

func NonSaUserTpapReleaseEvaluatorProvider(conf types3.TpapForNonSaUserFeatureConfig, factory2 release.ConstraintFactory) types3.TpapForNonSaUserEvaluator {
	return release.NewEvaluator(conf, factory2)
}

func SaUserTpapReleaseEvaluatorProvider(conf types3.TpapForSaUserFeatureConfig, factory2 release.ConstraintFactory) types3.TpapForSaUserEvaluator {
	return release.NewEvaluator(conf, factory2)
}

func NonSaUserTpapReleaseConfigProvider(conf *genconf.Config) types3.TpapForNonSaUserFeatureConfig {
	return conf.TpapEntryPointSwitchConfigForNonSaUser()
}

func SaUserTpapReleaseConfigProvider(conf *genconf.Config) types3.TpapForSaUserFeatureConfig {
	return conf.TpapEntryPointSwitchConfigForSaUser()
}

func ReportStalenessDurationProvider(conf *genconf.Config) time.Duration {
	return conf.ExecutionReportGenerationParams().ReportStalenessDuration()
}

func FeatureReleaseConfigProvider(conf *genconf.Config) *genconf3.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func iftConfProvider(conf *worker.Config) *worker.InternationalFundTransfer {
	return conf.InternationalFundTransfer
}

func slackClientProvider(conf *worker.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[worker.IFTReportsSlackBotOauthToken], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[worker.IFTReportsSlackBotOauthToken], slack.OptionDebug(true))
	}
	return slackClient
}

func HttpClientWithTransportProvider() *http.Client {
	return &http.Client{
		Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
	}
}

func InternationalFundTransferS3ClientProvider(awsConfig aws.Config, genConf *genconf.Config) s3.S3Client {
	client := s3.NewClient(awsConfig, genConf.InternationalFundTransfer().S3Bucket())
	return client
}

func MaxPageSizeProvider(conf *genconf.Config) uint32 {
	return conf.InternationalFundTransfer().MaxPageSize()
}

func PayInternationalFundTransferS3ClientProvider(client s3.S3Client) vendor_file_generator.PayInternationalFundTransferS3Client {
	return client
}

func HttpClientProvider() *http.Client {
	return &http.Client{}
}

func InitializeVelocityEngineService(payclient pay2.PayClient, conf *server.Config) *velocityengine.Service {
	return velocityengine.NewService(payclient, conf)
}

func UniqueATMActorProvider(genConf *genconf.Config) string {
	return genConf.UniqueATMActorId()
}

func PageSizeToFetchTxnForActorProvider(genConf *genconf.Config) int32 {
	return genConf.PageSizeToFetchTxnForATMActor()
}

func pageSizeForChargeRelatedOrderAndTxnProvider(genConf *genconf.Config) uint32 {
	return genConf.PageSizeForChargeRelatedOrderAndTxn()
}

func provideGenerateSofLimitStrategiesValuesPublisher(publisher types3.GenerateSofLimitStrategiesValuesPublisher) queue.Publisher {
	return publisher
}

func PayServerEntitySegregationFlagProvider(conf *genconf.Config) types5.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in pay config : %v", conf.EnableEntitySegregation()))
	return types5.EnableResourceProvider(conf.EnableEntitySegregation())
}

func WorkerEntitySegregationFlagProvider(conf *genconf2.Config) types5.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in pay worker config : %v", conf.EnableEntitySegregation()))
	return types5.EnableResourceProvider(conf.EnableEntitySegregation())
}
