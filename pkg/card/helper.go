package card

import (
	"context"

	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/pkg/frontend/app/apputils"

	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

// DesignEnhancementEnabled checks if the card design enhancement feature is enabled for the user.
func DesignEnhancementEnabled(ctx context.Context, actorId string, releaseEvaluator release.IEvaluator, onboardingClient onboarding.OnboardingClient, questSdkClient *questSdk.Client, userAttributeFetcher pkgUser.UserAttributesFetcher, networthClient networthPb.NetWorthClient, dynamicConf *genconf.Config) bool {
	// Check if :
	// 1. Card design enhancement is enabled in config
	// 2. Home design enhancement is enabled for the user
	// note : checking in reverse because home design enhancement has multiple rpc calls
	return apputils.IsFeatureEnabledFromCtxDynamic(ctx, dynamicConf.Flags().EnableCardDesignEnhancement()) &&
		featureflags.IsFeatureHomeDesignEnhancementsEnabled(ctx, &featureflags.IsFeatureHomeDesignEnhancementsEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator:            releaseEvaluator,
				OnboardingClient:     onboardingClient,
				QuestSdkClient:       questSdkClient,
				UserAttributeFetcher: userAttributeFetcher,
				NetWorthClient:       networthClient,
			},
		})
}

func GetAnyWithoutError(msg proto.Message) *anyPb.Any {
	res, _ := anyPb.New(msg)
	return res
}
