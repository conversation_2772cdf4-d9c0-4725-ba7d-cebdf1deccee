package loans

import (
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
)

var typesToPalVendorMap = map[typesPb.Vendor]palBePb.Vendor{
	typesPb.Vendor_VENDOR_FEDERAL:            palBePb.Vendor_FEDERAL,
	typesPb.Vendor_VENDOR_IDFC:               palBePb.Vendor_IDFC,
	typesPb.Vendor_VENDOR_LIQUILOANS:         palBePb.Vendor_LIQUILOANS,
	typesPb.Vendor_VENDOR_FIFTYFIN:           palBePb.Vendor_FIFTYFIN,
	typesPb.Vendor_VENDOR_MONEYVIEW:          palBePb.Vendor_MONEYVIEW,
	typesPb.Vendor_VENDOR_ABFL:               palBePb.Vendor_ABFL,
	typesPb.Vendor_VENDOR_STOCK_GUARDIAN_LSP: palBePb.Vendor_STOCK_GUARDIAN_LSP,
	typesPb.Vendor_VENDOR_LENDEN:             palBePb.Vendor_LENDEN,
}

var typesToPalProgramMap = map[typesPb.LoanProgram]palBePb.LoanProgram{
	typesPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN: palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
	typesPb.LoanProgram_LOAN_PROGRAM_FLDG:              palBePb.LoanProgram_LOAN_PROGRAM_FLDG,
	typesPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:      palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY,
	typesPb.LoanProgram_LOAN_PROGRAM_LAMF:              palBePb.LoanProgram_LOAN_PROGRAM_LAMF,
	typesPb.LoanProgram_LOAN_PROGRAM_STPL:              palBePb.LoanProgram_LOAN_PROGRAM_STPL,
}

func ConvertToPalLoanHeader(lh *typesPb.LoanHeader) *palBePb.LoanHeader {
	vendor, isVendorFound := typesToPalVendorMap[lh.GetVendor()]
	if !isVendorFound {
		vendor = palBePb.Vendor_VENDOR_UNSPECIFIED
	}
	lp, isLpFound := typesToPalProgramMap[lh.GetLoanProgram()]
	if !isLpFound {
		lp = palBePb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
	}
	return &palBePb.LoanHeader{
		LoanProgram: lp,
		Vendor:      vendor,
	}
}

var (
	LoanProgramEligibleForLLAltAccFlow = map[palFeEnumsPb.LoanProgram]bool{
		palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:   true,
		palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG:                true,
		palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL:                true,
		palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION: true,
		palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:       true,
	}
)
var LoanProgramVersionToLLSchemeVersion = map[enums.LoanProgramVersion]liquiloans.SchemeVersion{
	enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1: liquiloans.SchemeVersion_SCHEME_VERSION_V1,
	enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V2: liquiloans.SchemeVersion_SCHEME_VERSION_V2,
}

func CheckPrePayEnabled(lh *palFeEnumsPb.LoanHeader) bool {
	switch lh.GetVendor() {
	case palFeEnumsPb.Vendor_LIQUILOANS:
		switch lh.GetLoanProgram() {
		case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
			return true
		default:
			return false
		}
	case palFeEnumsPb.Vendor_IDFC:
		return true
	case palFeEnumsPb.Vendor_FEDERAL_BANK:
		return true
	case palFeEnumsPb.Vendor_FIFTYFIN:
		return true
	case palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP:
		return true
	case palFeEnumsPb.Vendor_LENDEN:
		return true
	default:
		return false
	}
}

func CheckPreCloseEnabled(lh *palFeEnumsPb.LoanHeader) bool {
	switch lh.GetVendor() {
	case palFeEnumsPb.Vendor_LIQUILOANS:
		switch lh.GetLoanProgram() {
		case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL,
			palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
			return true
		default:
			return false
		}
	case palFeEnumsPb.Vendor_IDFC:
		return false
	case palFeEnumsPb.Vendor_FEDERAL_BANK:
		return true
	case palFeEnumsPb.Vendor_FIFTYFIN:
		return true
	case palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP:
		return true
	default:
		return false
	}
}

func GetPalFeVendorFromBe(beVendor palBePb.Vendor) palFeEnumsPb.Vendor {
	switch beVendor {
	case palBePb.Vendor_FEDERAL:
		return palFeEnumsPb.Vendor_FEDERAL_BANK
	case palBePb.Vendor_LIQUILOANS:
		return palFeEnumsPb.Vendor_LIQUILOANS
	case palBePb.Vendor_IDFC:
		return palFeEnumsPb.Vendor_IDFC
	case palBePb.Vendor_FIFTYFIN:
		return palFeEnumsPb.Vendor_FIFTYFIN
	case palBePb.Vendor_EPIFI_TECH:
		return palFeEnumsPb.Vendor_EPIFI_TECH
	case palBePb.Vendor_ABFL:
		return palFeEnumsPb.Vendor_ABFL
	case palBePb.Vendor_MONEYVIEW:
		return palFeEnumsPb.Vendor_MONEYVIEW
	case palBePb.Vendor_STOCK_GUARDIAN_LSP:
		return palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP
	case palBePb.Vendor_LENDEN:
		return palFeEnumsPb.Vendor_LENDEN
	default:
		return palFeEnumsPb.Vendor_VENDOR_UNSPECIFIED
	}
}

func GetFeLoanProgramFromBe(beLp palBePb.LoanProgram) palFeEnumsPb.LoanProgram {
	switch beLp {
	case palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN
	case palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY
	case palBePb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL
	case palBePb.LoanProgram_LOAN_PROGRAM_FLDG:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG
	case palBePb.LoanProgram_LOAN_PROGRAM_STPL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL
	case palBePb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND
	case palBePb.LoanProgram_LOAN_PROGRAM_LAMF:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF
	case palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB
	case palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
	case palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2
	case palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION
	case palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL
	case palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL
	case palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION
	case palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY
	case palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB
	default:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
	}
}
