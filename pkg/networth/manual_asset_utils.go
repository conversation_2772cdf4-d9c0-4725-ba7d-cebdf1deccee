package networth

import (
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	types "github.com/epifi/gamma/api/typesv2"
)

var (
	ManualAssetsNetworthCategoryToAssetTypeMap = map[networthFePb.NetworthCategory]networthBePb.AssetType{
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_AIF:                          networthBePb.AssetType_ASSET_TYPE_AIF,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_ART_ARTEFACTS:                networthBePb.AssetType_ASSET_TYPE_ART_ARTEFACTS,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_BONDS:                        networthBePb.AssetType_ASSET_TYPE_BONDS,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_CASH:                         networthBePb.AssetType_ASSET_TYPE_CASH,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_GOLD:                 networthBePb.AssetType_ASSET_TYPE_DIGITAL_GOLD,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_SILVER:               networthBePb.AssetType_ASSET_TYPE_DIGITAL_SILVER,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_PRIVATE_EQUITY:               networthBePb.AssetType_ASSET_TYPE_PRIVATE_EQUITY,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_REAL_ESTATE:                  networthBePb.AssetType_ASSET_TYPE_REAL_ESTATE,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE: networthBePb.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_EMPLOYEE_STOCK_OPTION:        networthBePb.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_GADGETS:                      networthBePb.AssetType_ASSET_TYPE_GADGETS,
	}

	ManualAssetsNetworthCategoryToInvestmentInstrumentTypeMap = map[networthFePb.NetworthCategory]types.InvestmentInstrumentType{
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_AIF:                          types.InvestmentInstrumentType_AIF,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_ART_ARTEFACTS:                types.InvestmentInstrumentType_ART_AND_ARTEFACTS,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_BONDS:                        types.InvestmentInstrumentType_BOND,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_CASH:                         types.InvestmentInstrumentType_CASH,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_GOLD:                 types.InvestmentInstrumentType_DIGITAL_GOLD,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_DIGITAL_SILVER:               types.InvestmentInstrumentType_DIGITAL_SILVER,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_PRIVATE_EQUITY:               types.InvestmentInstrumentType_PRIVATE_EQUITY,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_REAL_ESTATE:                  types.InvestmentInstrumentType_REAL_ESTATE,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_PORTFOLIO_MANAGEMENT_SERVICE: types.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE,
		networthFePb.NetworthCategory_NETWORTH_CATEGORY_EMPLOYEE_STOCK_OPTION:        types.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION,
	}
	ManualAssetsInstrumentTypeToAssetTypeMap = map[types.InvestmentInstrumentType]networthBePb.AssetType{
		types.InvestmentInstrumentType_AIF:                          networthBePb.AssetType_ASSET_TYPE_AIF,
		types.InvestmentInstrumentType_ART_AND_ARTEFACTS:            networthBePb.AssetType_ASSET_TYPE_ART_ARTEFACTS,
		types.InvestmentInstrumentType_BOND:                         networthBePb.AssetType_ASSET_TYPE_BONDS,
		types.InvestmentInstrumentType_CASH:                         networthBePb.AssetType_ASSET_TYPE_CASH,
		types.InvestmentInstrumentType_DIGITAL_GOLD:                 networthBePb.AssetType_ASSET_TYPE_DIGITAL_GOLD,
		types.InvestmentInstrumentType_DIGITAL_SILVER:               networthBePb.AssetType_ASSET_TYPE_DIGITAL_SILVER,
		types.InvestmentInstrumentType_PRIVATE_EQUITY:               networthBePb.AssetType_ASSET_TYPE_PRIVATE_EQUITY,
		types.InvestmentInstrumentType_REAL_ESTATE:                  networthBePb.AssetType_ASSET_TYPE_REAL_ESTATE,
		types.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE: networthBePb.AssetType_ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE,
		types.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION:        networthBePb.AssetType_ASSET_TYPE_EMPLOYEE_STOCK_OPTION,
		types.InvestmentInstrumentType_GADGETS:                      networthBePb.AssetType_ASSET_TYPE_GADGETS,
	}

	ManualInstrumentTypeToAssetDisplayName = map[types.InvestmentInstrumentType]string{
		types.InvestmentInstrumentType_AIF:                          "AIF",
		types.InvestmentInstrumentType_CASH:                         "Cash",
		types.InvestmentInstrumentType_BOND:                         "Bond",
		types.InvestmentInstrumentType_REAL_ESTATE:                  "Real Estate",
		types.InvestmentInstrumentType_DIGITAL_GOLD:                 "Digital Gold",
		types.InvestmentInstrumentType_DIGITAL_SILVER:               "Digital Silver",
		types.InvestmentInstrumentType_PRIVATE_EQUITY:               "Private Equity",
		types.InvestmentInstrumentType_ART_AND_ARTEFACTS:            "Art & Artefacts",
		types.InvestmentInstrumentType_EMPLOYEE_STOCK_OPTION:        "ESOP",
		types.InvestmentInstrumentType_PORTFOLIO_MANAGEMENT_SERVICE: "PMS",
	}
)
