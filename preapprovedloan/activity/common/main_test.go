package common_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	epifiTemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"
	storageV2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/federal"

	balanceMocksPb "github.com/epifi/gamma/api/accounts/balance/mocks"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/bankcust/mocks"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	recurringPaymentMocksPb "github.com/epifi/gamma/api/recurringpayment/mocks"
	profileMocks "github.com/epifi/gamma/api/risk/profile/mocks"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	uiMocksPb "github.com/epifi/gamma/api/userintel/mocks"
	vgCustomerMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/customer/mocks"
	"github.com/epifi/gamma/preapprovedloan/activity/common"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/config/worker"
	workerGenConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	daoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	deeplinkMocks "github.com/epifi/gamma/preapprovedloan/deeplink/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	llDl "github.com/epifi/gamma/preapprovedloan/deeplink/provider/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	wts             epifitemporalTest.WorkflowTestSuite
	conf            *worker.Config
	deeplinkConf    *commonGenConf.DeeplinkConfig
	workerGenConfig *workerGenConf.Config
)

func TestMain(m *testing.M) {
	confTest, genConfTest, teardown := test.InitTestWorker()
	wts.SetLogger(epifiTemporalLogging.NewZapAdapter(logger.Log))
	conf = confTest
	workerGenConfig = genConfTest
	deeplinkConf = genConfTest.DeeplinkConfig()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockedDependencies struct {
	loanStepExecutionDao     *daoMocks.MockLoanStepExecutionsDao
	loanApplicantDao         *daoMocks.MockLoanApplicantDao
	loanOffersDao            *daoMocks.MockLoanOffersDao
	loanRequestDao           *daoMocks.MockLoanRequestsDao
	loanAccountDao           *daoMocks.MockLoanAccountsDao
	loanActivityDao          *daoMocks.MockLoanActivityDao
	loanInstallmentInfoDao   *daoMocks.MockLoanInstallmentInfoDao
	loanInstallmentPayoutDao *daoMocks.MockLoanInstallmentPayoutDao
	loanPAymentRequestDao    *daoMocks.MockLoanPaymentRequestsDao
	loecDao                  *daoMocks.MockLoanOfferEligibilityCriteriaDao
	rpcHelper                *helper.RpcHelper
	orderClient              orderMocks.MockOrderServiceClient
	recurringPaymentClient   *recurringPaymentMocksPb.MockRecurringPaymentServiceClient
	config                   *worker.Config
	deeplinkFactory          *deeplinkMocks.MockIDeeplinkProviderFactory
	accountPiRelationClient  *accountPiMocks.MockAccountPIRelationClient
	piClient                 *piMocks.MockPiClient
	accountBalanceClient     *balanceMocksPb.MockBalanceClient
	userIntelClient          *uiMocksPb.MockUserIntelServiceClient
	txnExecutor              *storageV2Mocks.PassThroughMockIdempotentTxnExecutor
	actorClient              *actorMocks.MockActorClient
	userClient               *userMocks.MockUsersClient
	txnExecutorProvider      *storageV2Mocks.MockPassthroughDbResourceProvider
}

func newActProcessorWithMocks(t *testing.T) (*common.Processor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)

	// dao mocks
	mockLoanRequestsDao := daoMocks.NewMockLoanRequestsDao(ctr)
	mockLoanOffersDao := daoMocks.NewMockLoanOffersDao(ctr)
	mockLoanApplicantDao := daoMocks.NewMockLoanApplicantDao(ctr)
	mockLoanAccountDao := daoMocks.NewMockLoanAccountsDao(ctr)
	mockLoanStepExecutionDao := daoMocks.NewMockLoanStepExecutionsDao(ctr)
	mockLoanActivityDao := daoMocks.NewMockLoanActivityDao(ctr)
	mockLoanInstallmentInfoDao := daoMocks.NewMockLoanInstallmentInfoDao(ctr)
	mockLoanInstallmentPayoutDao := daoMocks.NewMockLoanInstallmentPayoutDao(ctr)
	moclLoanPaymentRequestDao := daoMocks.NewMockLoanPaymentRequestsDao(ctr)
	mockLoecDao := daoMocks.NewMockLoanOfferEligibilityCriteriaDao(ctr)
	txnExecProvider := storageV2Mocks.NewPassThroughMockIdempotentTxnExecutorProvider(conf.DbConfigMap.GetOwnershipToDbConfigMap())

	celestialClient := celestialMocks.NewMockCelestialClient(ctr)
	txnExecutor := storageV2Mocks.NewPassThroughMockIdempotentTxnExecutor()
	commsClient := commsMocks.NewMockCommsClient(ctr)
	authClient := authMocks.NewMockAuthClient(ctr)
	vgCustomerClient := vgCustomerMocks.NewMockCustomerClient(ctr)
	orderClient := orderMocks.NewMockOrderServiceClient(ctr)
	userClient := userMocks.NewMockUsersClient(ctr)
	actorClient := actorMocks.NewMockActorClient(ctr)
	piClient := piMocks.NewMockPiClient(ctr)
	savingsClient := savingsMocks.NewMockSavingsClient(ctr)
	accountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	profileClient := profileMocks.NewMockProfileClient(ctr)
	bcClient := mocks.NewMockBankCustomerServiceClient(ctr)
	onbClient := onbMocks.NewMockOnboardingClient(ctr)
	rpcHelper := helper.NewRpcHelper(nil, actorClient, userClient, savingsClient, celestialClient,
		nil, nil, vgCustomerClient, authClient, nil, nil, commsClient,
		mockLoanStepExecutionDao, conf.Notification, orderClient, nil, piClient, accountPiClient,
		nil, nil, mockLoanActivityDao, nil, nil, profileClient,
		bcClient, nil, nil, nil, nil, nil, nil, onbClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	baseDlProvider := baseprovider.NewProvider(nil, deeplinkConf, nil, nil, nil, nil)
	llPlDlProvider := llDl.NewProvider(baseDlProvider)
	llFldgDlProvider := llDl.NewFldgProvider(baseDlProvider)
	llEsDlProvider := llDl.NewLiquiloansEarlySalaryProvider(baseDlProvider)
	llFlDlProvider := llDl.NewLiquiloansFiLiteProvider(llPlDlProvider)
	llAtlDlProvider := llDl.NewAcqToLendProvider(llFlDlProvider)
	llRealTimeDlProvider := llDl.NewRealtimeDistProvider(llPlDlProvider)
	fdRtdDlProvider := federal.NewRealTimeFedProvider(baseDlProvider)
	fdRtdNtbDlProvider := federal.NewRealTimeNtbFedProvider(fdRtdDlProvider)
	dlFactory := deeplink.NewDeeplinkProviderFactory(baseDlProvider, llPlDlProvider, llEsDlProvider, nil, llFldgDlProvider, llFlDlProvider, llAtlDlProvider, nil, nil, nil, llRealTimeDlProvider, nil, nil, nil, fdRtdDlProvider, nil, nil, nil, nil, nil, nil, nil, nil, nil, fdRtdNtbDlProvider, nil, nil)
	mockRecurringPaymentClient := recurringPaymentMocksPb.NewMockRecurringPaymentServiceClient(ctr)
	userIntelClient := uiMocksPb.NewMockUserIntelServiceClient(ctr)

	act := common.NewProcessor(mockLoanStepExecutionDao, mockLoanApplicantDao, mockLoanRequestsDao, mockLoanAccountDao, mockLoanActivityDao, mockLoanInstallmentInfoDao, mockLoanInstallmentPayoutDao, moclLoanPaymentRequestDao, mockLoanOffersDao, mockLoecDao, rpcHelper, orderClient, nil, nil, dlFactory, nil, nil, nil, userIntelClient, txnExecProvider, nil, nil, nil, nil, onbClient, savingsClient, nil, nil, nil, nil)

	md := &mockedDependencies{
		loanStepExecutionDao:   mockLoanStepExecutionDao,
		loanApplicantDao:       mockLoanApplicantDao,
		loanOffersDao:          mockLoanOffersDao,
		loanRequestDao:         mockLoanRequestsDao,
		loanAccountDao:         mockLoanAccountDao,
		loanActivityDao:        mockLoanActivityDao,
		loanInstallmentInfoDao: mockLoanInstallmentInfoDao,
		rpcHelper:              rpcHelper,
		txnExecutor:            txnExecutor,
		config:                 conf,
		recurringPaymentClient: mockRecurringPaymentClient,
		piClient:               piClient,
		userIntelClient:        userIntelClient,
		actorClient:            actorClient,
		userClient:             userClient,
	}

	return act, md, func() {
		ctr.Finish()
	}
}
