package federal_test

import (
	"context"
	"os"
	"testing"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	mockTime "github.com/epifi/be-common/pkg/datetime/mocks"
	epifiTemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	mockEvents "github.com/epifi/be-common/pkg/events/mocks"
	"github.com/epifi/be-common/pkg/logger"
	storageV2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	"github.com/epifi/gamma/preapprovedloan/config/worker/genconf"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	livenessPb "github.com/epifi/gamma/api/auth/liveness/mocks"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	authOrchMocks "github.com/epifi/gamma/api/auth/orchestrator/mocks"
	"github.com/epifi/gamma/api/bankcust/mocks"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	limitestimatorpb "github.com/epifi/gamma/api/credit_limit_estimator/mocks"
	esignMocksPb "github.com/epifi/gamma/api/docs/esign/mocks"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	vkycMocks "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	profileMocks "github.com/epifi/gamma/api/risk/profile/mocks"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	obfuscatorMocks "github.com/epifi/gamma/api/user/obfuscator/mocks"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	llVgMocks "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans/mocks"
	palVgMocks "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/mocks"
	setuVgPb "github.com/epifi/gamma/api/vendorgateway/lending/setu/mocks"
	vgCustomerMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/customer/mocks"
	profileValidationMocks "github.com/epifi/gamma/api/vendorgateway/profilevalidation/mocks"
	persistentQueueMocks "github.com/epifi/gamma/pkg/persistentqueue/mocks"
	federalActivity "github.com/epifi/gamma/preapprovedloan/activity/federal"
	"github.com/epifi/gamma/preapprovedloan/config/worker"
	daoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/federal"
	deeplinkMocks "github.com/epifi/gamma/preapprovedloan/deeplink/provider/mocks"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/test"

	"github.com/golang/mock/gomock"
)

var (
	wts       epifitemporalTest.WorkflowTestSuite
	conf      *worker.Config
	dynConf   *genconf.Config
	dummyLrId = "dummy-LR-id"
	dummyDl   = &deepLinkPb.Deeplink{}
	ctx       = context.Background()
)

func TestMain(m *testing.M) {
	confTest, dynConfTest, teardown := test.InitTestWorker()
	wts.SetLogger(epifiTemporalLogging.NewZapAdapter(logger.Log))
	conf = confTest
	dynConf = dynConfTest
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockedDependencies struct {
	loanStepExecutionDao      *daoMocks.MockLoanStepExecutionsDao
	loanApplicantDao          *daoMocks.MockLoanApplicantDao
	loanOffersDao             *daoMocks.MockLoanOffersDao
	loanRequestDao            *daoMocks.MockLoanRequestsDao
	loanAccountDao            *daoMocks.MockLoanAccountsDao
	loanActivityDao           *daoMocks.MockLoanActivityDao
	loanInstallmentInfoDao    *daoMocks.MockLoanInstallmentInfoDao
	llPalVgClient             *llVgMocks.MockLiquiloansClient
	rpcHelper                 *helper.RpcHelper
	txnExecutor               *storageV2Mocks.MockTxnExecutor
	authClient                *authOrchMocks.MockOrchestratorClient
	deeplinkProvider          *deeplinkMocks.MockIDeeplinkProvider
	userClient                *userMocks.MockUsersClient
	actorClient               *actorMocks.MockActorClient
	savingsClient             *savingsMocks.MockSavingsClient
	config                    *worker.Config
	onbClient                 *onbMocks.MockOnboardingClient
	eventsBroker              *mockEvents.MockBroker
	vkycClient                *vkycMocks.MockVKYCClient
	bcClient                  *mocks.MockBankCustomerServiceClient
	esignClient               *esignMocksPb.MockESignClient
	timePkg                   *mockTime.MockTime
	limitEstimatorClient      *limitestimatorpb.MockCreditLimitEstimatorClient
	livenessClient            *livenessPb.MockLivenessClient
	persistentQueue           *persistentQueueMocks.MockPersistentQueue
	profileClient             *profileMocks.MockProfileClient
	authActualClient          *authMocks.MockAuthClient
	vgCustomerClient          *vgCustomerMocks.MockCustomerClient
	profileValidationVgClient *profileValidationMocks.MockProfileValidationClient
	palVgClient               *palVgMocks.MockPreApprovedLoanClient
	piClient                  *piMocks.MockPiClient
	accountPiClient           *accountPiMocks.MockAccountPIRelationClient
	orderClient               *orderMocks.MockOrderServiceClient
	setuVgClient              *setuVgPb.MockSetuClient
	obfuscatorClient          *obfuscatorMocks.MockObfuscatorClient
	loecDao                   *daoMocks.MockLoanOfferEligibilityCriteriaDao
}

func newFederalActProcessorWithMocks(t *testing.T) (*federalActivity.Processor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)

	// dao mocks
	mockLoanRequestsDao := daoMocks.NewMockLoanRequestsDao(ctr)
	mockLoanOffersDao := daoMocks.NewMockLoanOffersDao(ctr)
	mockLoanApplicantDao := daoMocks.NewMockLoanApplicantDao(ctr)
	mockLoanAccountDao := daoMocks.NewMockLoanAccountsDao(ctr)
	mockLoanStepExecutionDao := daoMocks.NewMockLoanStepExecutionsDao(ctr)
	mockLoanActivityDao := daoMocks.NewMockLoanActivityDao(ctr)
	mockLoanInstallmentInfoDao := daoMocks.NewMockLoanInstallmentInfoDao(ctr)
	mockLoanEligibilityDao := daoMocks.NewMockLoanOfferEligibilityCriteriaDao(ctr)

	palVgClient := llVgMocks.NewMockLiquiloansClient(ctr)
	celestialClient := celestialMocks.NewMockCelestialClient(ctr)
	vkycClient := vkycMocks.NewMockVKYCClient(ctr)
	txnExecutor := storageV2Mocks.NewMockTxnExecutor(ctr)
	commsClient := commsMocks.NewMockCommsClient(ctr)
	authOrchClient := authOrchMocks.NewMockOrchestratorClient(ctr)
	authClient := authMocks.NewMockAuthClient(ctr)
	vgCustomerClient := vgCustomerMocks.NewMockCustomerClient(ctr)
	orderClient := orderMocks.NewMockOrderServiceClient(ctr)
	userClient := userMocks.NewMockUsersClient(ctr)
	actorClient := actorMocks.NewMockActorClient(ctr)
	piClient := piMocks.NewMockPiClient(ctr)
	savingsClient := savingsMocks.NewMockSavingsClient(ctr)
	accountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	profileClient := profileMocks.NewMockProfileClient(ctr)
	bcClient := mocks.NewMockBankCustomerServiceClient(ctr)
	onbClient := onbMocks.NewMockOnboardingClient(ctr)
	mockEventBroker := mockEvents.NewMockBroker(ctr)
	deeplinkProvider := deeplinkMocks.NewMockIDeeplinkProvider(ctr)
	esignClient := esignMocksPb.NewMockESignClient(ctr)
	timePkg := mockTime.NewMockTime(ctr)
	limitEstimatorClient := limitestimatorpb.NewMockCreditLimitEstimatorClient(ctr)
	livenessClient := livenessPb.NewMockLivenessClient(ctr)
	persistentQueue := persistentQueueMocks.NewMockPersistentQueue(ctr)
	profileValidationVgClient := profileValidationMocks.NewMockProfileValidationClient(ctr)
	palFedVgClient := palVgMocks.NewMockPreApprovedLoanClient(ctr)
	setuvgClient := setuVgPb.NewMockSetuClient(ctr)
	obfuscatorClient := obfuscatorMocks.NewMockObfuscatorClient(ctr)

	rpcHelper := helper.NewRpcHelper(nil, actorClient, userClient, savingsClient, celestialClient, nil, nil, vgCustomerClient, authClient, esignClient, timePkg, commsClient, mockLoanStepExecutionDao, conf.Notification, orderClient, nil, piClient, accountPiClient, nil, nil, mockLoanActivityDao, nil, nil, profileClient, bcClient, nil, nil, nil, nil, nil, nil, onbClient, obfuscatorClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	provider := baseprovider.NewProvider(nil, dynConf.DeeplinkConfig(), nil, onbClient, savingsClient, rpcHelper)
	fedAaProvider := federal.NewRealTimeFedProvider(provider)

	deeplinkFactory := deeplink.NewDeeplinkProviderFactory(provider, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, fedAaProvider, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	act := federalActivity.NewProcessor(mockLoanRequestsDao, nil, mockLoanOffersDao, mockLoanStepExecutionDao, mockLoanAccountDao,
		nil, nil, nil, mockLoanEligibilityDao, nil, nil, rpcHelper,
		livenessClient, txnExecutor, palFedVgClient, persistentQueue, esignClient, profileValidationVgClient, nil,
		deeplinkProvider, limitEstimatorClient, authOrchClient, nil, nil, bcClient, nil, nil, nil,
		nil, nil, vkycClient, piClient, accountPiClient, orderClient, deeplinkFactory, setuvgClient, userClient,
		nil, fedAaProvider, nil, nil, nil, onbClient, savingsClient)

	md := &mockedDependencies{
		loanStepExecutionDao:      mockLoanStepExecutionDao,
		loanApplicantDao:          mockLoanApplicantDao,
		loanOffersDao:             mockLoanOffersDao,
		loanRequestDao:            mockLoanRequestsDao,
		loanAccountDao:            mockLoanAccountDao,
		loanActivityDao:           mockLoanActivityDao,
		loanInstallmentInfoDao:    mockLoanInstallmentInfoDao,
		llPalVgClient:             palVgClient,
		rpcHelper:                 rpcHelper,
		txnExecutor:               txnExecutor,
		authClient:                authOrchClient,
		userClient:                userClient,
		actorClient:               actorClient,
		savingsClient:             savingsClient,
		deeplinkProvider:          deeplinkProvider,
		config:                    nil,
		onbClient:                 onbClient,
		eventsBroker:              mockEventBroker,
		vkycClient:                vkycClient,
		bcClient:                  bcClient,
		esignClient:               esignClient,
		timePkg:                   timePkg,
		limitEstimatorClient:      limitEstimatorClient,
		livenessClient:            livenessClient,
		persistentQueue:           persistentQueue,
		profileClient:             profileClient,
		authActualClient:          authClient,
		vgCustomerClient:          vgCustomerClient,
		profileValidationVgClient: profileValidationVgClient,
		palVgClient:               palFedVgClient,
		piClient:                  piClient,
		accountPiClient:           accountPiClient,
		orderClient:               orderClient,
		setuVgClient:              setuvgClient,
		obfuscatorClient:          obfuscatorClient,
		loecDao:                   mockLoanEligibilityDao,
	}

	return act, md, func() {
		ctr.Finish()
	}
}
