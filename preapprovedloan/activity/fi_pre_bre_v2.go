package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/typesv2"

	mvVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"

	brePb "github.com/epifi/gamma/api/bre"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/userdata"
)

// Dedupe rejections cases can be retried every day as per Moneyview
// the previous active lead could get cancelled or open for attribution for other partner anytime
const mvDedupeExpiryTime = 24 * time.Hour

func (p *Processor) FiPreBreV2(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		lg := activity.GetLogger(ctx)

		userFeatProp, err := helper.GetUserFeatureProperty(ctx, lse.GetActorId(), p.onbClient, p.savingsClient)
		if err != nil {
			lg.Error("error getting user feature property", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching user feature property: %v", err))
		}

		loecs, loecsErr := helper.GetCDCEligibleLoecs(ctx, p.loanOfferEligibilityCriteriaDao, lse.GetActorId(), userFeatProp.IsFiSAHolder)
		if loecsErr != nil && !errors.Is(loecsErr, epifierrors.ErrRecordNotFound) {
			lg.Error("failed to fetch loecs", zap.Error(loecsErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loecs, err: %v", loecsErr))
		}

		if len(loecs) == 0 {
			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
			res.LseFieldMasks = append(res.LseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			return res, nil
		}
		preScreenErr := p.callPreBreAndUpdateLoecsV2(ctx, lse, loecs)
		if preScreenErr != nil {
			lg.Error("failed call pre-bre and update loecs", zap.Error(preScreenErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to call bre and updated loecs, err: %v", preScreenErr))
		}
		return res, nil
	})
	return actRes, actErr
}

// nolint: funlen
func (p *Processor) callPreBreAndUpdateLoecsV2(ctx context.Context, lse *palPb.LoanStepExecution, loecs []*palPb.LoanOfferEligibilityCriteria) error {
	userData, err := p.userDataPrpvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: lse.GetActorId()})
	if err != nil {
		return errors.Wrap(err, "error in getting user data")
	}

	address := userData.GetAddress()
	// only basic address details are collected from user in eligibility flow, use them if present
	if userData.GetResidenceDetails().GetResidentialAddress().GetAddress().GetPostalCode() != "" {
		address = typesv2.GetFromBeAddress(userData.GetResidenceDetails().GetResidentialAddress().GetAddress())
	}

	userFeatProp, err := helper.GetUserFeatureProperty(ctx, lse.GetActorId(), p.onbClient, p.savingsClient)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in rpcHelper.IsNonFiCoreUser err: %v", err))
	}

	req := &brePb.GetPreBreEligibilityDetailsRequest{
		ActorId:   lse.GetActorId(),
		RequestId: uuid.NewString(),
		CustomerDetails: &brePb.CustomerDetails{
			PersonalDetails: &brePb.PersonalDetails{
				Dob:    userData.GetGivenDateOfBirth(),
				Name:   userData.GetBestName(),
				Gender: userData.GetGivenGender(),
				Pan:    userData.GetPan(),
			},
			ResidentialAddress: address,
		},
		IsEtbUser: userFeatProp.IsFiSAHolder,
	}

	// todo(Sharath/Anupam): Placing this condition because monthly income not
	// available for the user in self employed case, so till the time that checks
	// goes in we will be using annual revenue.
	monthlyIncome := userData.GetEmploymentDetails().GetMonthlyIncome()
	if monthlyIncome == nil && userData.GetEmploymentDetails().GetAnnualRevenue() != nil {
		monthlyIncome = userData.GetEmploymentDetails().GetAnnualRevenue()
		monthlyIncome.Units = monthlyIncome.GetUnits() / 12
	}

	if userData.EmploymentDetails != nil {
		req.GetCustomerDetails().EmploymentDetails = &brePb.EmploymentDetails{
			EmploymentType: userData.GetEmploymentDetails().GetEmploymentType(),
			MonthlyIncome:  monthlyIncome,
			EmployerName:   userData.GetEmploymentDetails().GetOrganizationName(),
			WorkEmail:      userData.GetEmploymentDetails().GetWorkEmail(),
			WorkAddress:    userData.GetEmploymentDetails().GetWorkAddress(),
		}
	}
	res, err := p.breClient.GetPreBreEligibilityDetails(ctx, req)
	if err = epifigrpc.RPCError(res, err); err != nil {
		return errors.Wrap(err, "failed to call GetPreBreEligibilityDetails")
	}
	if res.GetRawBreResponse() != nil {
		csvRow := &helper.CsvRow{
			ActorId:       req.GetActorId(),
			LoecId:        "",
			ReqId:         req.GetRequestId(),
			BreRes:        string(res.GetRawBreResponse()),
			BreIdentifier: "PRE",
		}
		writeErr := helper.WriteRawResponseToS3(ctx, csvRow, p.config, p.dataDevS3Client, true)
		if writeErr != nil {
			return errors.Wrap(writeErr, fmt.Sprintf("failed to write pre-bre res, err: %v", writeErr))
		}
	}

	var finalValidLenders []palPb.Vendor
	var finalInvalidLenders []palPb.Vendor

	for _, loec := range loecs {
		var loecFieldMasks []palPb.LoanOfferEligibilityCriteriaFieldMask

		// Updating Request ID in LOEC
		if loec.GetDataRequirementDetails() == nil {
			loec.DataRequirementDetails = &palPb.DataRequirementDetails{}
		}
		loec.GetDataRequirementDetails().PreBreRequestId = req.GetRequestId()
		// We should not update data info which is coming from BRE as we have the existing data info which was the correct source of truth
		existingDataInfo := loec.GetPolicyParams().GetDataInfo()
		loec.PolicyParams = res.GetDecision().GetPolicyParams()
		if loec.GetPolicyParams() == nil {
			loec.PolicyParams = &palPb.PolicyParams{}
		}
		loec.PolicyParams.DataInfo = existingDataInfo
		loec.ExpiredAt = res.GetDecision().GetValidTill()
		loecFieldMasks = append(loecFieldMasks,
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT,
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS,
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_POLICY_PARAMS,
		)

		// if the corresponding
		if !lo.Contains(res.GetDecision().GetValidLenders(), loec.GetVendor()) {
			loec.Status = palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
			loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_FI_PRE_BRE
			loecFieldMasks = append(loecFieldMasks,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
			)
			finalInvalidLenders = append(finalInvalidLenders, loec.GetVendor())
		} else {
			fm, valid, err2 := p.performLenderSpecificValidationAndUpdateLoec(ctx, loec, userData)
			if err2 != nil {
				return errors.Wrap(err2, "failed to run lender validations")
			}
			loecFieldMasks = append(loecFieldMasks, fm...)
			if valid {
				finalValidLenders = append(finalValidLenders, loec.GetVendor())
			} else {
				finalInvalidLenders = append(finalInvalidLenders, loec.GetVendor())
			}
		}
		ctx = RunWithOwnershipAndReset(ctx, helper.GetPalOwnership(loec.GetVendor()), func(ctx context.Context) {
			err = p.loanOfferEligibilityCriteriaDao.Update(ctx, loec, loecFieldMasks)
		})
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("failed to update loec, err: %v", err))
		}
	}

	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		p.eventBroker.AddToBatch(ctx, events.NewFiPreBre(lse.GetActorId(), lse.GetRefId(), req.GetRequestId(), finalValidLenders, finalInvalidLenders))
	})

	return nil
}

func (p *Processor) performLenderSpecificValidationAndUpdateLoec(ctx context.Context, loec *palPb.LoanOfferEligibilityCriteria, userData *userdata.GetDefaultUserDataResponse) ([]palPb.LoanOfferEligibilityCriteriaFieldMask, bool, error) {
	switch loec.GetVendor() {
	case palPb.Vendor_FEDERAL:
		dedupeRes, err := p.userClient.DedupeCheck(ctx, &userPb.DedupeCheckRequest{
			ActorId:     loec.GetActorId(),
			Vendor:      vgPb.Vendor_FEDERAL_BANK,
			PanNumber:   userData.GetPan(),
			PhoneNumber: userData.GetMobileNumber(),
			DateOfBirth: userData.GetGivenDateOfBirth(),
			EmailId:     userData.GetEmail(),
		})
		if err = epifigrpc.RPCError(dedupeRes, err); err != nil {
			return nil, false, errors.Wrap(err, "failed to call DedupeCheck")
		}
		dedupeSuccess := dedupeRes.GetDedupeStatus() == customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS
		var loecFieldMasks []palPb.LoanOfferEligibilityCriteriaFieldMask
		if !dedupeSuccess {
			loec.Status = palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
			loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_CHECK
			// If user is already a federal bank customer, they cannot take a loan through this flow
			// User is being blocked from checking their federal bank eligibility for 1 year by marking the expiry as 1 year from now
			// After 1 year if the user is not a federal bank customer, they can check their eligibility again
			loec.ExpiredAt = timestampPb.New(time.Now().Add(365 * 24 * time.Hour))
			loecFieldMasks = append(loecFieldMasks,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT,
			)
		}
		return loecFieldMasks, dedupeSuccess, nil
	case palPb.Vendor_MONEYVIEW:
		dedupeSuccess := false
		singleDeDupeRes, errSingleDeDupeRes := p.mvVgClient.GetSingleDeDupeStatus(ctx, &mvVgPb.GetSingleDeDupeStatusRequest{
			Header:       &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MONEYVIEW},
			PanNumber:    userData.GetPan(),
			Email:        userData.GetEmail(),
			MobileNumber: userData.GetMobileNumber().ToStringNationalNumber(),
			UserType:     mvLoanProgramToUserType[loec.GetLoanProgram()],
		})
		if te := epifigrpc.RPCError(singleDeDupeRes, errSingleDeDupeRes); te != nil {
			logger.Error(ctx, "error in GetSingleDeDupeStatus vg RPC", zap.Error(te))
			// todo(Govind/Anupam): after verifying in prod
			dedupeRes, errDedupeRes := p.mvVgClient.GetDeDupeStatus(ctx, &mvVgPb.GetDeDupeStatusRequest{
				Header:   &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MONEYVIEW},
				Pans:     []string{userData.GetPan()},
				UserType: mvLoanProgramToUserType[loec.GetLoanProgram()],
			})
			if teDedupeCall := epifigrpc.RPCError(dedupeRes, errDedupeRes); teDedupeCall != nil {
				logger.Error(ctx, "error in GetDeDupeStatus vg RPC", zap.Error(teDedupeCall))
				return nil, false, errors.Wrap(teDedupeCall, "failed to call GetDeDupeStatus")
			}
			for _, panDedupeStatus := range dedupeRes.GetPanDedupeStatuses() {
				dedupeSuccess = panDedupeStatus.GetPan() == userData.GetPan() && panDedupeStatus.GetStatus() == mvVgPb.GetDeDupeStatusResponse_PanDeDupeStatus_DEDUPE_STATUS_DEDUPE_PASSED
			}
		} else {
			dedupeSuccess = singleDeDupeRes.GetDedupeStatus() == mvVgPb.DeDupeStatus_DEDUPE_STATUS_DEDUPE_PASSED
		}

		var loecFieldMasks []palPb.LoanOfferEligibilityCriteriaFieldMask
		if !dedupeSuccess {
			loec.Status = palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
			loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_DEDUPE_CHECK
			// todo(Anupam): check dedupe status expiry
			loec.ExpiredAt = timestampPb.New(time.Now().Add(mvDedupeExpiryTime))
			loecFieldMasks = append(loecFieldMasks,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_EXPIRED_AT,
			)
		}
		return loecFieldMasks, dedupeSuccess, nil
	default:
		return nil, true, nil
	}
}

var mvLoanProgramToUserType = map[palPb.LoanProgram]mvVgPb.MvUserType{
	palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:      mvVgPb.MvUserType_MV_USER_TYPE_WHITELISTED_OFFER,
	palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION: mvVgPb.MvUserType_MV_USER_TYPE_OPEN_MARKET,
}
