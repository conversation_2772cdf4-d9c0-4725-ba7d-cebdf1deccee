package idfc_test

import (
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	onceV2Mocks "github.com/epifi/be-common/pkg/counter/once/v2/mocks"
	datetimePkgMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	epifiTemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"
	storageV2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/bankcust/mocks"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	kycMocks "github.com/epifi/gamma/api/kyc/mocks"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	profileMocks "github.com/epifi/gamma/api/risk/profile/mocks"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	userLocationMocks "github.com/epifi/gamma/api/user/location/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	idfcVgMocks "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc/mocks"
	vgCustomerMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/customer/mocks"
	releaseEvaluatorMocks "github.com/epifi/gamma/pkg/feature/release/mocks"
	"github.com/epifi/gamma/preapprovedloan/activity/idfc"
	"github.com/epifi/gamma/preapprovedloan/config/worker"
	"github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	daoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	idfc2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/idfc"
	vkycFailureHandlers "github.com/epifi/gamma/preapprovedloan/deeplink/provider/idfc/vkyc/failure_handlers"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	wts     epifitemporalTest.WorkflowTestSuite
	conf    *worker.Config
	dynConf *genconf.Config
)

func TestMain(m *testing.M) {
	confTest, dynConfTest, teardown := test.InitTestWorker()
	wts.SetLogger(epifiTemporalLogging.NewZapAdapter(logger.Log))
	conf = confTest
	dynConf = dynConfTest
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockedDependencies struct {
	loanStepExecutionDao   *daoMocks.MockLoanStepExecutionsDao
	loanRequestDao         *daoMocks.MockLoanRequestsDao
	loanApplicantDao       *daoMocks.MockLoanApplicantDao
	idfcPalVgClient        *idfcVgMocks.MockIdfcClient
	userClient             *userMocks.MockUsersClient
	actorClient            *actorMocks.MockActorClient
	kycClient              *kycMocks.MockKycClient
	authClient             *authMocks.MockAuthClient
	commsClient            *commsMocks.MockCommsClient
	userLocationClient     *userLocationMocks.MockLocationClient
	multiDbDOnceMgr        *onceV2Mocks.MockMultiDbDoOnce
	doOnce                 *onceV2Mocks.MockDoOnce
	loanAccountDao         *daoMocks.MockLoanAccountsDao
	loanInstallmentInfoDao *daoMocks.MockLoanInstallmentInfoDao
	loanOfferDao           *daoMocks.MockLoanOffersDao
	loanActivityDao        *daoMocks.MockLoanActivityDao
	txnExecutor            *storageV2Mocks.MockTxnExecutor
	time                   *datetimePkgMocks.MockTime
	releaseEvaluatorMocks  *releaseEvaluatorMocks.MockIEvaluator
	savingsClient          *savingsMocks.MockSavingsClient
	onbClient              *onbMocks.MockOnboardingClient
}

func newIdfcActProcessorWithMocks(t *testing.T) (*idfc.Processor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)

	// dao mocks
	mockLoanRequestsDao := daoMocks.NewMockLoanRequestsDao(ctr)
	mockLoanApplicantDao := daoMocks.NewMockLoanApplicantDao(ctr)
	mockLoanStepExecutionDao := daoMocks.NewMockLoanStepExecutionsDao(ctr)
	mockLoanActivityDao := daoMocks.NewMockLoanActivityDao(ctr)
	authClient := authMocks.NewMockAuthClient(ctr)
	userLocationClient := userLocationMocks.NewMockLocationClient(ctr)
	palVgClient := idfcVgMocks.NewMockIdfcClient(ctr)
	celestialClient := celestialMocks.NewMockCelestialClient(ctr)
	commsClient := commsMocks.NewMockCommsClient(ctr)
	vgCustomerClient := vgCustomerMocks.NewMockCustomerClient(ctr)
	orderClient := orderMocks.NewMockOrderServiceClient(ctr)
	userClient := userMocks.NewMockUsersClient(ctr)
	actorClient := actorMocks.NewMockActorClient(ctr)
	piClient := piMocks.NewMockPiClient(ctr)
	savingsClient := savingsMocks.NewMockSavingsClient(ctr)
	accountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	profileClient := profileMocks.NewMockProfileClient(ctr)
	bcClient := mocks.NewMockBankCustomerServiceClient(ctr)
	onbClient := onbMocks.NewMockOnboardingClient(ctr)
	rpcHelper := helper.NewRpcHelper(nil, actorClient, userClient, savingsClient, celestialClient, nil, nil, vgCustomerClient, authClient, nil, nil, commsClient, mockLoanStepExecutionDao, conf.Notification, orderClient, nil, piClient, accountPiClient, nil, nil, mockLoanActivityDao, nil, nil, profileClient, bcClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, userLocationClient, nil, nil, nil, nil)
	provider := baseprovider.NewProvider(nil, dynConf.DeeplinkConfig(), nil, onbClient, savingsClient, rpcHelper)
	idfcProvider := idfc2.NewProvider(provider, vkycFailureHandlers.NewFactoryImpl())
	kycClient := kycMocks.NewMockKycClient(ctr)
	multiDbDOnceMgr := onceV2Mocks.NewMockMultiDbDoOnce(ctr)
	doOnce := onceV2Mocks.NewMockDoOnce(ctr)
	loanAccountDao := daoMocks.NewMockLoanAccountsDao(ctr)
	loanInstallmentInfoDao := daoMocks.NewMockLoanInstallmentInfoDao(ctr)
	loanOfferDao := daoMocks.NewMockLoanOffersDao(ctr)
	loanActivityDao := daoMocks.NewMockLoanActivityDao(ctr)
	txnExecutor := storageV2Mocks.NewMockTxnExecutor(ctr)
	time := datetimePkgMocks.NewMockTime(ctr)
	commsHelper := helper.NewCommsHelper(commsClient, rpcHelper)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)

	deeplinkFactory := deeplink.NewDeeplinkProviderFactory(provider, nil, nil, idfcProvider, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	act := idfc.NewProcessor(mockLoanStepExecutionDao, mockLoanApplicantDao, mockLoanRequestsDao, loanAccountDao,
		loanActivityDao, loanOfferDao, loanInstallmentInfoDao, rpcHelper,
		palVgClient, idfcProvider, nil, nil, nil, nil, nil, txnExecutor, deeplinkFactory, nil, kycClient, dynConf, commsHelper,
		multiDbDOnceMgr, time, mockReleaseEvaluator)

	md := &mockedDependencies{
		loanStepExecutionDao:   mockLoanStepExecutionDao,
		loanRequestDao:         mockLoanRequestsDao,
		loanApplicantDao:       mockLoanApplicantDao,
		idfcPalVgClient:        palVgClient,
		userClient:             userClient,
		actorClient:            actorClient,
		kycClient:              kycClient,
		authClient:             authClient,
		commsClient:            commsClient,
		userLocationClient:     userLocationClient,
		multiDbDOnceMgr:        multiDbDOnceMgr,
		doOnce:                 doOnce,
		loanAccountDao:         loanAccountDao,
		loanInstallmentInfoDao: loanInstallmentInfoDao,
		loanOfferDao:           loanOfferDao,
		loanActivityDao:        loanActivityDao,
		txnExecutor:            txnExecutor,
		time:                   time,
		releaseEvaluatorMocks:  mockReleaseEvaluator,
		savingsClient:          savingsClient,
		onbClient:              onbClient,
	}

	return act, md, func() {
		ctr.Finish()
	}
}
