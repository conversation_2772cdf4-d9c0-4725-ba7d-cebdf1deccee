package lenden

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	userPb "github.com/epifi/gamma/api/user"
	lendenVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	mandateHelper "github.com/epifi/gamma/preapprovedloan/mandate_manager/helper"
	"github.com/epifi/gamma/preapprovedloan/utils"
)

type getMandateInitScreenRequest struct {
	vendor                 palPb.Vendor
	loanProgram            palPb.LoanProgram
	lse                    *palPb.LoanStepExecution
	aaBankDetails          *palPb.AaAnalysisBankDetails
	needSavingsBankAccount bool
}

// Helper function to get the initiate mandate screen v2 deeplink
func (p *Processor) getMandateInitScreen(ctx context.Context, req *getMandateInitScreenRequest) (*deeplinkPb.Deeplink, error) {
	lg := activity.GetLogger(ctx)
	dlProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.vendor, LoanProgram: req.loanProgram})
	if req.aaBankDetails != nil {
		lg.Info("AA bank details found, returning screen to add complete bank details")
		userBankDetails := &palPb.MandateData_BankingDetails_AccountDetails{
			AccountNumber:     req.aaBankDetails.GetAccountNumber(),
			AccountHolderName: req.aaBankDetails.GetAccountHolderName(),
			IfscCode:          req.aaBankDetails.GetIfsc(),
			BankName:          req.aaBankDetails.GetBankName(),
		}
		if len(req.aaBankDetails.GetAccountNumber()) < 4 {
			return nil, errors.New("account number string is less than 4 digits")
		}
		accountNumberSuffix := req.aaBankDetails.GetAccountNumber()[len(req.aaBankDetails.GetAccountNumber())-4:]
		bankDl, err := dlProvider.GetBankingDetailsScreenDeeplink(dlProvider.GetLoanHeader(), req.lse.GetRefId(), req.lse.GetId(), 0, 0, accountNumberSuffix)
		if err != nil {
			return nil, errors.Wrap(err, "error getting screen for adding AA bank account details")
		}
		dl, err := deeplink.ConstructPlMandateInitScreenV2FromLse(ctx, dlProvider, req.lse, userBankDetails, false, bankDl)
		if err != nil {
			return nil, errors.Wrap(err, "error creating mandate init screen for AA bank details")
		}
		return dl, nil
	}
	var savingsBankAccountDetails *palPb.MandateData_BankingDetails_AccountDetails
	for _, accDetails := range req.lse.GetDetails().GetMandateData().GetBankingDetails().GetAlternateAccDetails() {
		// TODO(Brijesh): Add logic to show a preferable bank account when we have multiple accounts
		savingsBankAccountDetails = accDetails
	}
	var addBankDl *deeplinkPb.Deeplink
	if req.needSavingsBankAccount {
		var err error
		addBankDl, err = dlProvider.GetBankingDetailsScreenDeeplink(dlProvider.GetLoanHeader(), req.lse.GetRefId(), req.lse.GetId(), 0, 0, "")
		if err != nil {
			return nil, errors.Wrap(err, "error getting screen for adding savings bank account details")
		}
	}
	dl, err := deeplink.ConstructPlMandateInitScreenV2FromLse(ctx, dlProvider, req.lse, savingsBankAccountDetails, true, addBankDl)
	if err != nil {
		return nil, errors.Wrap(err, "error creating mandate init screen")
	}
	return dl, nil
}

func (p *Processor) LdcCheckMandateStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	lg := activity.GetLogger(ctx)
	execWorkRes, err := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		var err error
		if lse.GetDetails().GetMandateData().GetMerchantTxnId() == "" {
			lg.Info("no tracking id found in mandate data")
			var alreadyCompleted bool
			alreadyCompleted, lse, err = p.checkAndUpdateLseIfMandateAlreadySetup(ctx, lse)
			if err != nil {
				lg.Error("error checking and updating lse if mandate already setup", zap.Error(err))
				return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error checking and updating lse if mandate already setup"))
			}
			if alreadyCompleted {
				lg.Info("mandate setup already completed")
				return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
			}
			var needSavingsBankAccount bool
			offerBankAccountDetails, err := p.getOfferBankAccountDetails(ctx, lse.GetRefId())
			if err != nil {
				if !errors.Is(err, epifierrors.ErrRecordNotFound) {
					lg.Error("error getting offer bank account details", zap.Error(err))
					return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error getting estimated salary bank account details"))
				}
				lse, needSavingsBankAccount, err = p.getAndStoreSavingsBankAccountDetails(ctx, lse)
				if err != nil {
					lg.Error("error storing savings bank account details", zap.Error(err))
					return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error storing savings bank account details"))
				}
			}
			var initMandateScreenDl *deeplinkPb.Deeplink
			initMandateScreenDl, err = p.getMandateInitScreen(ctx, &getMandateInitScreenRequest{
				vendor:                 req.GetVendor(),
				loanProgram:            req.GetLoanProgram(),
				lse:                    lse,
				aaBankDetails:          offerBankAccountDetails,
				needSavingsBankAccount: needSavingsBankAccount,
			})
			if err != nil {
				lg.Error("error getting initiate mandate screen", zap.Error(err))
				return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error getting initiate mandate screen"))
			}
			return &palActivityPb.PalActivityResponse{LoanStep: lse, NextAction: initMandateScreenDl},
				epifitemporal.NewTransientError(errors.Errorf("mandate not initiated yet, keep sending mandate initiation screen"))
		}
		var completed bool
		completed, lse, err = p.checkMandateStatus(ctx, lse)
		if err != nil {
			lg.Error("error checking mandate status", zap.Error(err))
			return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error checking mandate status"))
		}
		if completed {
			lg.Info("mandate setup completed")
			return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
		}
		if lse.GetDetails().GetMandateData().GetUrl() == "" {
			lg.Error("no mandate setup URL found in mandate data")
			// TODO(Brijesh): Check and mark permanent error with special sub statuses if needed for better debugging
			return nil, epifitemporal.NewTransientError(errors.New("no mandate setup URL found in mandate data"))
		}
		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})
		mandateWebViewScreenDl, err := deeplinkProvider.GetLoansMandateSetupScreen(ctx, deeplinkProvider.GetLoanHeader(), lse.GetActorId(), lse.GetRefId(), lse.GetId(), &provider.LoansMandateSetupScreenParams{
			WebViewParams: &provider.WebViewParams{EntryUrl: lse.GetDetails().GetMandateData().GetUrl()},
		})
		if err != nil {
			lg.Error("error generating mandate webview screen deeplink", zap.Error(err))
			return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error generating mandate webview screen deeplink"))
		}
		return &palActivityPb.PalActivityResponse{LoanStep: lse, NextAction: mandateWebViewScreenDl},
			epifitemporal.NewTransientError(errors.Errorf("mandate setup not completed yet, keep asking for mandate status"))
	})
	return execWorkRes, err
}

func (p *Processor) checkAndUpdateLseIfMandateAlreadySetup(ctx context.Context, lse *palPb.LoanStepExecution) (bool, *palPb.LoanStepExecution, error) {
	applicant, err := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return false, nil, errors.Wrap(err, "error getting applicant")
	}
	initMandateRes, err := p.initiateMandate(ctx, lse, applicant)
	if err != nil {
		return false, nil, errors.Wrap(err, "error getting initiation info")
	}
	switch initMandateRes.status {
	case mandateStatusAlreadyCompleted:
		if initMandateRes.data.trackingId == "" {
			return false, nil, errors.New("expected non-empty tracking id when e-NACH already completed")
		}
		lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, initMandateRes.data)
		if err != nil {
			return false, nil, errors.Wrap(err, "error updating mandate")
		}
		return true, lse, nil
	case mandateStatusPreRequisitesPending, mandateStatusInProgress:
		return false, lse, nil
	default:
		return false, nil, errors.Errorf("unhandled mandate status: %s", initMandateRes.status)
	}
}

func (p *Processor) updateLseWithMandateCompletionInfo(ctx context.Context, lse *palPb.LoanStepExecution, mandateData *mandateData) (*palPb.LoanStepExecution, error) {
	if lse.GetDetails().GetMandateData() == nil {
		lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
	}
	if mandateData.trackingId != "" {
		lse.GetDetails().GetMandateData().MerchantTxnId = mandateData.trackingId
	}
	if mandateData.url != "" {
		lse.GetDetails().GetMandateData().Url = mandateData.url
	}
	if mandateData.urlValidity.AsTime() != time.Unix(0, 0) {
		lse.GetDetails().GetMandateData().MandateLinkExpiry = mandateData.urlValidity
	}
	lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_SETUP_SUCCESSFUL
	err := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error updating mandate status")
	}
	return lse, nil
}

func (p *Processor) getOfferBankAccountDetails(ctx context.Context, loanReqId string) (*palPb.AaAnalysisBankDetails, error) {
	loanRequest, err := p.loanRequestDao.GetById(ctx, loanReqId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request")
	}
	loanOffer, err := p.loanOfferDao.GetById(ctx, loanRequest.GetOfferId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan offer")
	}
	offerBankDetails := loanOffer.GetOfferConstraints().GetLendenConstraintInfo().GetBankDetails()
	if offerBankDetails == nil {
		return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "no bank account details found in offer")
	}
	return offerBankDetails, nil
}

func (p *Processor) getAndStoreSavingsBankAccountDetails(ctx context.Context, lse *palPb.LoanStepExecution) (*palPb.LoanStepExecution, bool, error) {
	lg := activity.GetLogger(ctx)
	if len(lse.GetDetails().GetMandateData().GetBankingDetails().GetAlternateAccDetails()) != 0 {
		lg.Info("bank details already present in mandate data, skipping re-population")
		return lse, false, nil
	}
	acc, err := p.rpcHelper.GetSavingsAccountDetails(ctx, lse.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return lse, true, nil
		}
		return nil, false, errors.Wrap(err, "error getting savings account details")
	}
	user, err := p.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
	if err != nil {
		return nil, false, errors.Wrap(err, "error getting user")
	}
	userBankDetails := &palPb.MandateData_BankingDetails_AccountDetails{
		AccountNumber:     acc.GetAccountNo(),
		AccountHolderName: user.GetProfile().GetKycName().ToString(),
		IfscCode:          acc.GetIfscCode(),
		BankName:          utils.FiFederalBank,
	}
	mandateStepBanks := make(map[string]*palPb.MandateData_BankingDetails_AccountDetails)
	mandateStepBanks[mandateHelper.GetAltAccMapKey(userBankDetails)] = userBankDetails
	if _, exists := lse.GetDetails().GetMandateData().GetBankingDetails().GetAlternateAccDetails()[mandateHelper.GetAltAccMapKey(userBankDetails)]; !exists {
		if lse.GetDetails().GetMandateData() == nil {
			lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_MandateData{MandateData: &palPb.MandateData{}}
		}
		lse.GetDetails().GetMandateData().BankingDetails = &palPb.MandateData_BankingDetails{AlternateAccDetails: mandateStepBanks}
	}
	err = p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if err != nil {
		return nil, false, errors.Wrap(err, "error updating alternate account details in loan step execution")
	}
	return lse, false, nil
}

func (p *Processor) checkMandateStatus(ctx context.Context, lse *palPb.LoanStepExecution) (bool, *palPb.LoanStepExecution, error) {
	applicant, err := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return false, nil, errors.Wrap(err, "error getting applicant")
	}
	mandateStatusRes, err := p.lendenVgClient.CheckMandateStatus(ctx, &lendenVgPb.CheckMandateStatusRequest{
		UserId:      applicant.GetVendorApplicantId(),
		TrackingId:  lse.GetDetails().GetMandateData().GetMerchantTxnId(),
		MandateType: lendenVgPb.MandateType_MANDATE_TYPE_NACH_MANDATE,
	})
	if err = epifigrpc.RPCError(mandateStatusRes, err); err != nil {
		return false, nil, errors.Wrap(err, "error checking mandate status")
	}
	var isMandateCompleted bool
	switch mandateStatusRes.GetMandateStatus() {
	case lendenVgPb.MandateStatus_MANDATE_STATUS_COMPLETED:
		data := &mandateData{trackingId: mandateStatusRes.TrackingId}
		lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, data)
		if err != nil {
			return false, nil, errors.Wrap(err, "error updating mandate status")
		}
		return true, lse, nil
	case lendenVgPb.MandateStatus_MANDATE_STATUS_FAILED,
		lendenVgPb.MandateStatus_MANDATE_STATUS_EXPIRED:
		isMandateCompleted, lse, err = p.reInitiateMandateSetup(ctx, lse, applicant)
		if err != nil {
			return false, nil, errors.Wrap(err, "error re-initiating mandate setup after status failure")
		}
		if isMandateCompleted {
			return true, lse, nil
		}
		if lse.GetDetails().GetMandateData().GetUrl() == "" {
			return false, nil, errors.New("expected non-empty mandate URL after re-initiating mandate setup")
		}
		return false, lse, nil
	case lendenVgPb.MandateStatus_MANDATE_STATUS_IN_PROGRESS:
		// Mandate setup URL can change even with the same tracking ID, and mandate setup is in progress.
		// Clients like Epifi must fetch the latest URLs via initiate mandate API.
		// URL may be same or new - clients must handle both cases.
		// Android/iOS clients currently reload the webpage on receiving a new URL during status poll,
		// causing users to lose their setup progress and resulting in poor experience.
		// To reduce unnecessary page reloads on clients, we only re-initiate
		// if the URL was generated more than 10 minutes ago, rather than on every status poll.
		mandateLinkExpired := time.Now().After(lse.GetDetails().GetMandateData().GetMandateLinkExpiry().AsTime().Add(-5 * time.Minute))
		urlGeneratedMoreThanTenMinutesAgo := time.Now().After(lse.GetDetails().GetMandateData().GetUrlGeneratedAt().AsTime().Add(10 * time.Minute))
		if mandateLinkExpired || urlGeneratedMoreThanTenMinutesAgo {
			isMandateCompleted, lse, err = p.reInitiateMandateSetup(ctx, lse, applicant)
			if err != nil {
				return false, nil, errors.Wrap(err, "error re-initiating mandate setup after link expiry or URL age")
			}
			if isMandateCompleted {
				return true, lse, nil
			}
			if lse.GetDetails().GetMandateData().GetUrl() == "" {
				return false, nil, errors.New("expected non-empty mandate URL after re-initiating mandate setup")
			}
			return false, lse, nil
		}
		return false, lse, nil
	default:
		return false, nil, errors.Errorf("unexpected mandate status: %s", mandateStatusRes.GetMandateStatus())
	}
}

func (p *Processor) reInitiateMandateSetup(ctx context.Context, lse *palPb.LoanStepExecution, applicant *palPb.LoanApplicant) (bool, *palPb.LoanStepExecution, error) {
	initiateMandateRes, err := p.initiateMandate(ctx, lse, applicant)
	if err != nil {
		return false, nil, errors.Wrap(err, "error getting initiation info")
	}
	switch initiateMandateRes.status {
	case mandateStatusInProgress:
		if initiateMandateRes.data.trackingId == "" {
			return false, nil, errors.Errorf("expected non-empty mandate setup tracking id with in progress status")
		}
		if initiateMandateRes.data.url == "" {
			return false, nil, errors.Errorf("expected non-empty mandate setup url with in progress status")
		}
		lse.GetDetails().GetMandateData().MerchantTxnId = initiateMandateRes.data.trackingId
		lse.GetDetails().GetMandateData().Url = initiateMandateRes.data.url
		lse.GetDetails().GetMandateData().UrlGeneratedAt = initiateMandateRes.data.urlGeneratedAt
		if initiateMandateRes.data.urlValidity.AsTime() != time.Unix(0, 0) {
			lse.GetDetails().GetMandateData().MandateLinkExpiry = initiateMandateRes.data.urlValidity
		}
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_LINK_FETCHED
		err = p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
		})
		if err != nil {
			return false, nil, errors.Wrap(err, "error updating mandate status")
		}
		return false, lse, nil
	case mandateStatusAlreadyCompleted:
		if initiateMandateRes.data.trackingId == "" {
			return false, nil, errors.New("expected non-empty tracking id when e-NACH already completed")
		}
		lse, err = p.updateLseWithMandateCompletionInfo(ctx, lse, initiateMandateRes.data)
		if err != nil {
			return false, nil, errors.Wrap(err, "error updating mandate")
		}
		return true, lse, nil
	default:
		return false, nil, errors.Errorf("unexpected mandate status: %s", initiateMandateRes.status)
	}
}

type mandateStatus int

const (
	mandateStatusUnknown mandateStatus = iota

	// When an action needs to be done before setting up a payment mandate
	// like validating bank account details with lender
	mandateStatusPreRequisitesPending

	// When pre-requisites for starting mandate setup are complete and the setup has been started
	mandateStatusInProgress

	// When the mandate has already been set up (most probably during a previous loan account opening orchestration)
	mandateStatusAlreadyCompleted
)

func (s mandateStatus) String() string {
	return []string{"Unknown", "PreRequisitesPending", "InProgress", "AlreadyCompleted"}[s]
}

type initiateMandateResponse struct {
	status mandateStatus
	data   *mandateData
}

type mandateData struct {
	trackingId     string
	url            string
	urlValidity    *timestamppb.Timestamp
	urlGeneratedAt *timestamppb.Timestamp
}

func (p *Processor) initiateMandate(ctx context.Context, lse *palPb.LoanStepExecution, applicant *palPb.LoanApplicant) (*initiateMandateResponse, error) {
	lr, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request")
	}
	userIpAddress, err := p.rpcHelper.FetchIpAddress(ctx, lse.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "error getting user ip address")
	}
	userDP, udpErr := p.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId: lse.GetActorId(),
		PropertyTypes: []typesv2.DeviceProperty{
			typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID,
		},
	})
	if te := epifigrpc.RPCError(userDP, udpErr); te != nil {
		return nil, errors.Wrapf(te, "error getting device id")
	}
	deviceId := userDP.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID).GetDeviceId()
	res, err := p.lendenVgClient.InitMandate(ctx, &lendenVgPb.InitMandateRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LENDEN},
		LoanId: lr.GetVendorRequestId(),
		UserId: applicant.GetVendorApplicantId(),
		ConsentCodeList: []lendenVgPb.ConsentType{
			lendenVgPb.ConsentType_CONSENT_TYPE_MANDATE,
		},
		MandateType: lendenVgPb.MandateType_MANDATE_TYPE_NACH_MANDATE,
		UserIp:      userIpAddress,
		DeviceId:    deviceId,
		ConsentTime: timestamppb.Now(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		switch lendenVgPb.InitMandateResponse_Status(res.GetStatus().GetCode()) {
		case lendenVgPb.InitMandateResponse_ENACH_ALREADY_COMPLETED:
			if res.GetTrackingId() == "" {
				return nil, errors.Errorf("expected non-empty tracking id when e-NACH setup is already completed")
			}
			return &initiateMandateResponse{
				status: mandateStatusAlreadyCompleted,
				data:   &mandateData{trackingId: res.GetTrackingId()},
			}, nil
		case lendenVgPb.InitMandateResponse_OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND,
			lendenVgPb.InitMandateResponse_BANK_ACCOUNT_NOT_VERIFIED:
			return &initiateMandateResponse{
				status: mandateStatusPreRequisitesPending,
			}, nil
		default:
			return nil, errors.Wrapf(err, "error initiating mandate with lender")
		}
	}
	return &initiateMandateResponse{
		status: mandateStatusInProgress,
		data: &mandateData{
			trackingId:     res.GetTrackingId(),
			url:            res.GetRedirectionUrl(),
			urlValidity:    res.GetMandateUrlValidity(),
			urlGeneratedAt: timestamppb.Now(),
		},
	}, nil
}
