package liquiloans_test

import (
	"context"
	"os"
	"testing"

	"github.com/golang/mock/gomock"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	mockS3 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	epifiTemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	mockEvents "github.com/epifi/be-common/pkg/events/mocks"
	"github.com/epifi/be-common/pkg/logger"
	storageV2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	mocks5 "github.com/epifi/gamma/api/accounts/operstatus/mocks"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	authOrchPb "github.com/epifi/gamma/api/auth/orchestrator"
	authOrchMocks "github.com/epifi/gamma/api/auth/orchestrator/mocks"
	mocks2 "github.com/epifi/gamma/api/bankcust/compliance/mocks"
	"github.com/epifi/gamma/api/bankcust/mocks"
	breMocks "github.com/epifi/gamma/api/bre/mocks"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	recurringPaymentMocksPb "github.com/epifi/gamma/api/recurringpayment/mocks"
	mocks3 "github.com/epifi/gamma/api/risk/mocks"
	profileMocks "github.com/epifi/gamma/api/risk/profile/mocks"
	salaryprogramMocks "github.com/epifi/gamma/api/salaryprogram/mocks"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	uiMocksPb "github.com/epifi/gamma/api/userintel/mocks"
	finfluxVgMocks "github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux/mocks"
	llVgMocks "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans/mocks"
	vgCustomerMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/customer/mocks"
	vgMockPan "github.com/epifi/gamma/api/vendorgateway/pan/mocks"
	mock_release "github.com/epifi/gamma/pkg/feature/release/mocks"
	"github.com/epifi/gamma/preapprovedloan/activity/liquiloans"
	defaultValueCalculator "github.com/epifi/gamma/preapprovedloan/calculator/defaultvalue"
	calculatorProvidersWrapper "github.com/epifi/gamma/preapprovedloan/calculator/providers/wrapper"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/config/worker"
	workerGenConf "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	daoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	llDl "github.com/epifi/gamma/preapprovedloan/deeplink/provider/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	wts             epifitemporalTest.WorkflowTestSuite
	conf            *worker.Config
	deeplinkConf    *commonGenConf.DeeplinkConfig
	workerGenConfig *workerGenConf.Config
)

func TestMain(m *testing.M) {
	confTest, genConfTest, teardown := test.InitTestWorker()
	wts.SetLogger(epifiTemporalLogging.NewZapAdapter(logger.Log))
	conf = confTest
	workerGenConfig = genConfTest
	deeplinkConf = genConfTest.DeeplinkConfig()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockedDependencies struct {
	loanStepExecutionDao   *daoMocks.MockLoanStepExecutionsDao
	loanApplicantDao       *daoMocks.MockLoanApplicantDao
	loanOffersDao          *daoMocks.MockLoanOffersDao
	loanRequestDao         *daoMocks.MockLoanRequestsDao
	loanAccountDao         *daoMocks.MockLoanAccountsDao
	loanActivityDao        *daoMocks.MockLoanActivityDao
	loanInstallmentInfoDao *daoMocks.MockLoanInstallmentInfoDao
	loecDao                *daoMocks.MockLoanOfferEligibilityCriteriaDao
	llPalVgClient          *llVgMocks.MockLiquiloansClient
	rpcHelper              *helper.RpcHelper
	txnExecutor            *storageV2Mocks.PassThroughMockIdempotentTxnExecutor
	txnExecutorProvider    *storageV2Mocks.MockPassthroughDbResourceProvider
	authOrchestratorClient authOrchPb.OrchestratorClient
	deeplinkProvider       *provider.IDeeplinkProvider
	userClient             *userMocks.MockUsersClient
	actorClient            *actorMocks.MockActorClient
	savingsClient          *savingsMocks.MockSavingsClient
	config                 *worker.Config
	onbClient              *onbMocks.MockOnboardingClient
	authClient             *authMocks.MockAuthClient
	eventsBroker           *mockEvents.MockBroker
	s3Client               *mockS3.MockS3Client
	recurringPaymentClient *recurringPaymentMocksPb.MockRecurringPaymentServiceClient
	piClient               *piMocks.MockPiClient
	salaryClient           *salaryprogramMocks.MockSalaryProgramClient
	panVgClient            *vgMockPan.MockPANClient
	finfluxClient          *finfluxVgMocks.MockFinfluxClient
	userIntelClient        *uiMocksPb.MockUserIntelServiceClient
	bankCustClient         *mocks.MockBankCustomerServiceClient
	compClient             *mocks2.MockComplianceClient
	breClient              *breMocks.MockBreClient
	dataDevS3Client        *mockS3.MockS3Client
	profileClient          *profileMocks.MockProfileClient
	releaseEvaluator       *mock_release.MockIEvaluator
	riskClient             *mocks3.MockRiskClient
	workerGenConfig        *workerGenConf.Config
	oprStatusClient        *mocks5.MockOperationalStatusServiceClient
	mandateRequestDao      *daoMocks.MockMandateRequestDao
}

// TODO: use mockgen to generate mocks for these and remove the manual implementation
type mockCalculatorProvider struct {
}

func (p *mockCalculatorProvider) GetDefaultValueCalculator(
	ctx context.Context,
	loanOffer *palPb.LoanOffer,
) calculatorTypes.DefaultValueCalculator {
	return defaultValueCalculator.NewCalculator(ctx, loanOffer)
}

func (p *mockCalculatorProvider) GetCalculator(
	ctx context.Context,
	req *calculatorTypes.Request,
) (calculatorTypes.Calculator, error) {
	return calculatorProvidersWrapper.NewProvider().GetCalculator(ctx, req)
}

func newLLActProcessorWithMocks(t *testing.T) (*liquiloans.Processor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)

	// dao mocks
	mockLoanRequestsDao := daoMocks.NewMockLoanRequestsDao(ctr)
	mockLoanOffersDao := daoMocks.NewMockLoanOffersDao(ctr)
	mockLoanApplicantDao := daoMocks.NewMockLoanApplicantDao(ctr)
	mockLoanAccountDao := daoMocks.NewMockLoanAccountsDao(ctr)
	mockLoanStepExecutionDao := daoMocks.NewMockLoanStepExecutionsDao(ctr)
	mockLoanActivityDao := daoMocks.NewMockLoanActivityDao(ctr)
	mockLoanInstallmentInfoDao := daoMocks.NewMockLoanInstallmentInfoDao(ctr)
	mockLoanEligibilityDao := daoMocks.NewMockLoanOfferEligibilityCriteriaDao(ctr)

	panVgClient := vgMockPan.NewMockPANClient(ctr)
	salaryClient := salaryprogramMocks.NewMockSalaryProgramClient(ctr)
	palVgClient := llVgMocks.NewMockLiquiloansClient(ctr)
	celestialClient := celestialMocks.NewMockCelestialClient(ctr)
	txnExecutorProvider := storageV2Mocks.NewPassThroughMockIdempotentTxnExecutorProvider(conf.DbConfigMap.GetOwnershipToDbConfigMap())
	txnExecutor := storageV2Mocks.NewPassThroughMockIdempotentTxnExecutor()
	commsClient := commsMocks.NewMockCommsClient(ctr)
	authOrchClient := authOrchMocks.NewMockOrchestratorClient(ctr)
	authClient := authMocks.NewMockAuthClient(ctr)
	vgCustomerClient := vgCustomerMocks.NewMockCustomerClient(ctr)
	orderClient := orderMocks.NewMockOrderServiceClient(ctr)
	userClient := userMocks.NewMockUsersClient(ctr)
	actorClient := actorMocks.NewMockActorClient(ctr)
	piClient := piMocks.NewMockPiClient(ctr)
	savingsClient := savingsMocks.NewMockSavingsClient(ctr)
	accountPiClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	profileClient := profileMocks.NewMockProfileClient(ctr)
	bcClient := mocks.NewMockBankCustomerServiceClient(ctr)
	onbClient := onbMocks.NewMockOnboardingClient(ctr)
	mockEventBroker := mockEvents.NewMockBroker(ctr)
	breClient := breMocks.NewMockBreClient(ctr)
	rpcHelper := helper.NewRpcHelper(nil, actorClient, userClient, savingsClient, celestialClient, nil, nil, vgCustomerClient, authClient, nil, nil, commsClient, mockLoanStepExecutionDao, conf.Notification, orderClient, nil, piClient, accountPiClient, nil, nil, mockLoanActivityDao, nil, nil, profileClient, bcClient, nil, nil, nil, nil, nil, nil, onbClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	baseDlProvider := baseprovider.NewProvider(nil, deeplinkConf, nil, onbClient, savingsClient, rpcHelper)
	llPlDlProvider := llDl.NewProvider(baseDlProvider)
	llFldgDlProvider := llDl.NewFldgProvider(baseDlProvider)
	llEsDlProvider := llDl.NewLiquiloansEarlySalaryProvider(baseDlProvider)
	llFlDlProvider := llDl.NewLiquiloansFiLiteProvider(llPlDlProvider)
	llAtlDlProvider := llDl.NewAcqToLendProvider(llFlDlProvider)
	llRealTimeDlProvider := llDl.NewRealtimeDistProvider(llPlDlProvider)
	dlFactory := deeplink.NewDeeplinkProviderFactory(baseDlProvider, llPlDlProvider, llEsDlProvider, nil, llFldgDlProvider, llFlDlProvider, llAtlDlProvider, nil, nil, nil, llRealTimeDlProvider, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	mockS3Client := mockS3.NewMockS3Client(ctr)
	mockDataDevS3Client := mockS3.NewMockS3Client(ctr)
	mockRecurringPaymentClient := recurringPaymentMocksPb.NewMockRecurringPaymentServiceClient(ctr)
	finfluxVgClient := finfluxVgMocks.NewMockFinfluxClient(ctr)
	userIntelClient := uiMocksPb.NewMockUserIntelServiceClient(ctr)
	complianceClient := mocks2.NewMockComplianceClient(ctr)
	mockEvaluator := mock_release.NewMockIEvaluator(ctr)
	riskClient := mocks3.NewMockRiskClient(ctr)
	oprStatusClient := mocks5.NewMockOperationalStatusServiceClient(ctr)
	mandateRequestDao := daoMocks.NewMockMandateRequestDao(ctr)
	mockCalculatorFactory := &mockCalculatorProvider{}
	act := liquiloans.NewProcessor(mockLoanStepExecutionDao, mockLoanApplicantDao, mockLoanOffersDao, mockLoanRequestsDao,
		mockLoanAccountDao, mockLoanActivityDao, mockLoanEligibilityDao, mockLoanInstallmentInfoDao, palVgClient, rpcHelper, txnExecutorProvider,
		authOrchClient, llPlDlProvider, mockS3Client, conf, llEsDlProvider, llFlDlProvider, dlFactory,
		mockRecurringPaymentClient, nil, nil, nil, nil, nil, nil, nil, mockEventBroker,
		authClient, breClient, mockDataDevS3Client, nil, nil, llAtlDlProvider, nil, nil, workerGenConfig, nil, piClient, userClient, salaryClient, onbClient, panVgClient, mockEvaluator, finfluxVgClient, userIntelClient, bcClient,
		complianceClient, riskClient, oprStatusClient, savingsClient, mandateRequestDao, workerGenConfig.MandateConfig(), mockCalculatorFactory, nil)

	md := &mockedDependencies{
		loanStepExecutionDao:   mockLoanStepExecutionDao,
		loanApplicantDao:       mockLoanApplicantDao,
		loanOffersDao:          mockLoanOffersDao,
		loanRequestDao:         mockLoanRequestsDao,
		loanAccountDao:         mockLoanAccountDao,
		loanActivityDao:        mockLoanActivityDao,
		loanInstallmentInfoDao: mockLoanInstallmentInfoDao,
		loecDao:                mockLoanEligibilityDao,
		llPalVgClient:          palVgClient,
		rpcHelper:              rpcHelper,
		txnExecutor:            txnExecutor,
		authOrchestratorClient: authOrchClient,
		userClient:             userClient,
		actorClient:            actorClient,
		savingsClient:          savingsClient,
		deeplinkProvider:       nil,
		config:                 conf,
		onbClient:              onbClient,
		authClient:             authClient,
		eventsBroker:           mockEventBroker,
		s3Client:               mockS3Client,
		recurringPaymentClient: mockRecurringPaymentClient,
		piClient:               piClient,
		salaryClient:           salaryClient,
		panVgClient:            panVgClient,
		finfluxClient:          finfluxVgClient,
		userIntelClient:        userIntelClient,
		bankCustClient:         bcClient,
		compClient:             complianceClient,
		breClient:              breClient,
		dataDevS3Client:        mockDataDevS3Client,
		profileClient:          profileClient,
		releaseEvaluator:       mockEvaluator,
		riskClient:             riskClient,
		workerGenConfig:        workerGenConfig,
		oprStatusClient:        oprStatusClient,
		mandateRequestDao:      mandateRequestDao,
	}

	return act, md, func() {
		ctr.Finish()
	}
}
