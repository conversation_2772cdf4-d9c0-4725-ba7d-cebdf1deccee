// nolint:goimports
package stock_guardian

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	accountPb "github.com/epifi/be-common/api/typesv2/common/account"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/names"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	sgApplicantPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/applicant"
	sgApplicationPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	accTypes "github.com/epifi/gamma/api/typesv2/account"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/userdata"
)

// nolint:funlen,gocritic
func (p *Processor) SgOnboardingAtVendor(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		loanApplicant, loanApplicantErr := p.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, lr.GetActorId(), lr.GetVendor(), lr.GetLoanProgram(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
		if loanApplicantErr != nil {
			lg.Error("failed to fetch loan applicant", zap.Error(loanApplicantErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan applicant, err: %v", loanApplicantErr))
		}

		userFeatProp, err := helper.GetUserFeatureProperty(ctx, lse.GetActorId(), p.onbClient, p.savingsClient)
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in rpcHelper.IsNonFiCoreUser err: %v", err))
		}

		// fetch user details
		userDetails, err := p.getUserDetails(ctx, lse.GetActorId(), !userFeatProp.IsFiSAHolder)
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting user details err: %v", err))
		}

		// creating applicant at vendor
		createApplicantResp, createApplicantErr := p.sgApplicantApiGateway.CreateApplicant(ctx, &sgApplicantPb.CreateApplicantRequest{
			ClientRequestId: loanApplicant.GetVendorRequestId(),
			PAN:             userDetails.GetPan(),
			PhoneNumber:     userDetails.GetMobileNumber(),
			Email:           userDetails.GetEmail(),
			Name:            userDetails.GetBestName(),
		})
		if te := epifigrpc.RPCError(createApplicantResp, createApplicantErr); te != nil {
			lg.Error("failed to create applicant at stock guardian", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to create applicant at stock guardian, err: %v", te))
		}

		// updating loan applicant id
		loanApplicant.VendorApplicantId = createApplicantResp.GetApplicant().GetId()
		updateErr := p.loanApplicantDao.Update(ctx, loanApplicant, []palPb.LoanApplicantFieldMask{palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_VENDOR_APPLICANT_ID})
		if updateErr != nil {
			lg.Error("failed to update loan applicant id in loan applicant", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loan applicant id in loan applicant, err: %v", updateErr))
		}
		productIdToPass := productId
		if lr.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2 {
			productIdToPass = productIdEarlySalary
		}
		// starting application at vendor side
		startApplicationResp, startApplicationErr := p.sgApplicationApiGateway.StartApplication(ctx, &sgApplicationPb.StartApplicationRequest{
			LoanHeader: &sgApplicationPb.LoanHeader{
				ClientId: clientId,
			},
			ApplicantId: loanApplicant.GetVendorApplicantId(),
			ProductId:   productIdToPass,
		})
		if te := epifigrpc.RPCError(startApplicationResp, startApplicationErr); te != nil && !isApplicationAlreadyExistsAtVendor(startApplicationResp.GetStatus()) {
			lg.Error("failed to start application at vendor side", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to start application at vendor side, err: %v", te))
		}
		applicationId := startApplicationResp.GetApplicationId()

		if isApplicationAlreadyExistsAtVendor(startApplicationResp.GetStatus()) {
			lg.Error("application already exists at vendor end")
			cancelApplicationResp, cancelAplicationErr := p.sgApplicationApiGateway.CancelApplication(ctx, &sgApplicationPb.CancelApplicationRequest{
				LoanHeader: &sgApplicationPb.LoanHeader{
					ClientId: clientId,
				},
				ApplicationId: applicationId,
			})
			if te := epifigrpc.RPCError(cancelApplicationResp, cancelAplicationErr); te != nil {
				lg.Error("failed to cancel application at vendor side", zap.Error(te))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to cancel application at vendor side, err: %v", te))
			}

			// starting application at vendor side after cancelling the application
			startApplicationResp, startApplicationErr = p.sgApplicationApiGateway.StartApplication(ctx, &sgApplicationPb.StartApplicationRequest{
				LoanHeader: &sgApplicationPb.LoanHeader{
					ClientId: clientId,
				},
				ApplicantId: loanApplicant.GetVendorApplicantId(),
				ProductId:   productIdToPass,
			})
			if te := epifigrpc.RPCError(startApplicationResp, startApplicationErr); te != nil {
				lg.Error("failed to start application at vendor side", zap.Error(te))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to start application at vendor side, err: %v", te))
			}
			applicationId = startApplicationResp.GetApplicationId()
		}

		// storing vendor application id in loan request
		lr.VendorRequestId = applicationId
		updateErr = p.loanRequestDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_VENDOR_REQUEST_ID})
		if updateErr != nil {
			lg.Error("failed to update loan request", zap.Error(updateErr), zap.String("applicant_id", ""))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loan request, err: %v", updateErr))
		}

		return res, nil
	})
	return actRes, actErr
}

// nolint:funlen,gocritic
func (p *Processor) SgUpdateUserDetailsAtVendor(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		updateApplicationResp, updateApplicationErr := p.sgApplicationApiGateway.UpdateApplicationDetails(ctx, &sgApplicationPb.UpdateApplicationDetailsRequest{
			LoanHeader: &sgApplicationPb.LoanHeader{
				ClientId: clientId,
			},
			ApplicationId: lr.GetVendorRequestId(),
			Details: &sgApplicationPb.UserSelectedDetails{
				LoanAmount:     lr.GetDetails().GetLoanInfo().GetAmount(),
				TenureInMonths: lr.GetDetails().GetLoanInfo().GetTenureInMonths(),
				InterestRate:   lr.GetDetails().GetLoanInfo().GetInterestRate(),
			},
		})
		if te := epifigrpc.RPCError(updateApplicationResp, updateApplicationErr); te != nil {
			lg.Error("failed to update application details at stock guardian", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to updation applicantion details at stock guardian, err: %v", te))
		}

		// check if user is non fi core user
		userFeatProp, err := helper.GetUserFeatureProperty(ctx, lse.GetActorId(), p.onbClient, p.savingsClient)
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in rpcHelper.IsNonFiCoreUser err: %v", err))
		}

		userDetails, err := p.getUserDetails(ctx, lse.GetActorId(), !userFeatProp.IsFiSAHolder)
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting user details err: %v", err))
		}

		// todo: suggest about making UpdateUserDetails as an array and not single req
		// update address
		updateUserResp, updateUserErr := p.sgApplicationApiGateway.UpdateUserDetails(ctx, &sgApplicationPb.UpdateUserDetailsRequest{
			LoanHeader: &sgApplicationPb.LoanHeader{
				ClientId: clientId,
			},
			ApplicationId:   lr.GetVendorRequestId(),
			UpdateFieldMask: sgApplicationPb.UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_ADDRESS,
			Details: &sgApplicationPb.UpdateUserDetailsRequest_AddressDetails{
				AddressDetails: &sgApplicationPb.AddressDetails{
					AddressDetails: ConvertPostalAddressToCommon(userDetails.GetAddress()),
					AddressType:    common.AddressType_LOAN_COMMUNICATION,
				},
			},
		})
		if te := epifigrpc.RPCError(updateUserResp, updateUserErr); te != nil {
			lg.Error("failed to update personal address details at stock guardian", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update address user details at stock guardian, err: %v", te))
		}

		// update employment details
		updateUserResp, updateUserErr = p.sgApplicationApiGateway.UpdateUserDetails(ctx, &sgApplicationPb.UpdateUserDetailsRequest{
			LoanHeader: &sgApplicationPb.LoanHeader{
				ClientId: clientId,
			},
			ApplicationId:   lr.GetVendorRequestId(),
			UpdateFieldMask: sgApplicationPb.UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_EMPLOYMENT_DETAILS,
			Details: &sgApplicationPb.UpdateUserDetailsRequest_EmploymentDetails{
				EmploymentDetails: &sgApplicationPb.EmploymentDetails{
					Occupation:       ConvertEmploymentTypeToCommon(userDetails.GetEmploymentDetails().GetEmploymentType()),
					OrganizationName: userDetails.GetEmploymentDetails().GetOrganizationName(),
					MonthlyIncome:    userDetails.GetEmploymentDetails().GetMonthlyIncome(),
					WorkEmail:        userDetails.GetEmploymentDetails().GetWorkEmail(),
					OfficeAddress:    ConvertPostalAddressToCommon(userDetails.GetEmploymentDetails().GetWorkAddress()),
				},
			},
		})
		if te := epifigrpc.RPCError(updateUserResp, updateUserErr); te != nil {
			lg.Error("failed to update employment user details at stock guardian", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update employment user details at stock guardian, err: %v", te))
		}

		if userFeatProp.IsFiSAHolder {
			savingsAccount, saErr := p.rpcHelper.GetSavingsAccountDetails(ctx, lse.GetActorId(), vgPb.Vendor_FEDERAL_BANK, accTypes.AccountProductOffering_APO_REGULAR)
			if saErr != nil {
				lg.Error("failed to savings account by actor id", zap.Error(saErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to savings account by actor id, err: %v", saErr))
			}
			// update bank account details
			updateUserResp, updateUserErr = p.sgApplicationApiGateway.UpdateUserDetails(ctx, &sgApplicationPb.UpdateUserDetailsRequest{
				LoanHeader: &sgApplicationPb.LoanHeader{
					ClientId: clientId,
				},
				ApplicationId:   lr.GetVendorRequestId(),
				UpdateFieldMask: sgApplicationPb.UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_BANK_ACCOUNT_DETAILS,
				Details: &sgApplicationPb.UpdateUserDetailsRequest_BankAccountDetails{
					BankAccountDetails: &sgApplicationPb.BankAccountDetails{
						BankAccountDetails: &common.BankAccountDetails{
							AccountName:   names.ToString(userDetails.GetBestName()),
							AccountNumber: savingsAccount.GetAccountNo(),
							AccountType:   accountPb.AccountType_SAVINGS,
							Ifsc:          savingsAccount.GetIfscCode(),
							BankName:      "Federal",
						},
					},
				},
			})
			if te := epifigrpc.RPCError(updateUserResp, updateUserErr); te != nil {
				lg.Error("failed to update account user details at stock guardian", zap.Error(te))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update account user details at stock guardian, err: %v", te))
			}
		}

		// update personal details
		updateUserResp, updateUserErr = p.sgApplicationApiGateway.UpdateUserDetails(ctx, &sgApplicationPb.UpdateUserDetailsRequest{
			LoanHeader: &sgApplicationPb.LoanHeader{
				ClientId: clientId,
			},
			ApplicationId:   lr.GetVendorRequestId(),
			UpdateFieldMask: sgApplicationPb.UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_PERSONAL_DETAILS,
			Details: &sgApplicationPb.UpdateUserDetailsRequest_PersonalDetails{
				PersonalDetails: &sgApplicationPb.PersonalDetails{
					Dob:     userDetails.GetGivenDateOfBirth(),
					Email:   userDetails.GetEmail(),
					PanName: userDetails.GetBestName(),
				},
			},
		})
		if te := epifigrpc.RPCError(updateUserResp, updateUserErr); te != nil {
			lg.Error("failed to update personal user details at stock guardian", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update personal user details at stock guardian, err: %v", te))
		}

		// fetch ip address
		ipAddress, ipAddressErr := p.rpcHelper.FetchIpAddress(ctx, lse.GetActorId())
		if ipAddressErr != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to fetch the ip address, err: %v", ipAddressErr))
		}

		// recording consent at vendor
		recordConsentResp, recordconsentErr := p.sgApplicationApiGateway.RecordConsent(ctx, &sgApplicationPb.RecordConsentRequest{
			LoanHeader: &sgApplicationPb.LoanHeader{
				ClientId: clientId,
			},
			ApplicationId: lr.GetVendorRequestId(),
			ConsentTypes: []sgApplicationPb.ConsentType{
				sgApplicationPb.ConsentType_CONSENT_TYPE_CKYC_DOWNLOAD,
				sgApplicationPb.ConsentType_CONSENT_TYPE_CREDIT_REPORT_HARD_PULL,
				sgApplicationPb.ConsentType_CONSENT_TYPE_DATA_USAGE_FOR_PROMOTIONS,
			},
			IpAddress: ipAddress,
		})
		if te := epifigrpc.RPCError(recordConsentResp, recordconsentErr); te != nil {
			lg.Error("failed to record consents at vendor", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to record consents at vendor, err: %v", te))
		}

		// mark LSE success
		palActivity.MarkLoanStepSuccess(lse)
		return res, nil
	})
	return actRes, actErr
}

func isApplicationAlreadyExistsAtVendor(req *rpc.Status) bool {
	if req.GetCode() == uint32(sgApplicationPb.StartApplicationResponse_Status_value[sgApplicationPb.StartApplicationResponse_ALREADY_EXISTS.String()]) {
		return true
	}
	return false
}

func (p *Processor) getUserDetails(ctx context.Context, actorId string, isNonFiCoreUser bool) (*userdata.GetDefaultUserDataResponse, error) {
	if isNonFiCoreUser || (p.config.SgEtbNewEligibilityFlow() != nil && p.config.SgEtbNewEligibilityFlow().IsAllowed()) {
		return p.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: actorId})
	}

	loanApplicant, loanApplicantErr := p.loanApplicantDao.GetByActorId(ctx, actorId)
	if loanApplicantErr != nil {
		return nil, errors.Wrap(loanApplicantErr, "failed to fetch loan applicant")
	}

	// fetch user profile
	user, userErr := p.rpcHelper.GetUserByActorId(ctx, actorId)
	if userErr != nil {
		return nil, errors.Wrap(userErr, "rpcHelper.GetUserByActorId failed")
	}
	return &userdata.GetDefaultUserDataResponse{
		GivenName:        user.GetProfile().GetPanName(),
		GivenDateOfBirth: user.GetProfile().GetDateOfBirth(),
		PAN:              user.GetProfile().GetPAN(),
		GivenGender:      user.GetProfile().GetGivenGender(),
		MobileNumber:     user.GetProfile().GetPhoneNumber(),
		Email:            user.GetProfile().GetEmail(),
		EmploymentDetails: &userdata.EmploymentDetails{
			EmploymentType:   loanApplicant.GetEmploymentDetails().GetOccupation(),
			OrganizationName: loanApplicant.GetEmploymentDetails().GetOrganizationName(),
			MonthlyIncome:    loanApplicant.GetEmploymentDetails().GetStatedIncome(),
			WorkEmail:        loanApplicant.GetEmploymentDetails().GetWorkEmail(),
			WorkAddress:      loanApplicant.GetEmploymentDetails().GetOfficeAddress(),
		},
		Address: loanApplicant.GetPersonalDetails().GetAddress(),
	}, nil
}
