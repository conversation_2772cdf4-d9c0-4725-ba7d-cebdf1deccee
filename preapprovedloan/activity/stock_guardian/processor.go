package stock_guardian

import (
	"context"
	"fmt"

	palActivity "github.com/epifi/gamma/preapprovedloan/activity"

	"github.com/pkg/errors"

	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/savings"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/preapprovedloan/userdata"

	riskPb "github.com/epifi/gamma/api/risk"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	empPb "github.com/epifi/gamma/api/employment"

	authPb "github.com/epifi/gamma/api/auth/orchestrator"
	brePb "github.com/epifi/gamma/api/bre"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	omegleVkycClient "github.com/epifi/gamma/api/omegle"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	recurringpaymentpb "github.com/epifi/gamma/api/recurringpayment"
	sgApplicantApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/applicant"
	sgApplicantionApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	sgCustomerApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/customer"
	sgEsignApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/esign"
	sgKycApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	sgLmsApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	sgMatrixApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/matrix"
	usersPb "github.com/epifi/gamma/api/user"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/stock_guardian"
	"github.com/epifi/gamma/preapprovedloan/helper"
	loandataprovider "github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
)

const (
	clientId             = "FI_MONEY"
	productId            = "PERSONAL_LOAN_FI_1"
	productIdEarlySalary = "PERSONAL_LOAN_FI_2"
	emiGracePeriod       = 3
)

type Processor struct {
	loanRequestDao          dao.LoanRequestsDao
	loanStepExecutionDao    dao.LoanStepExecutionsDao
	loanOfferDao            dao.LoanOffersDao
	loanApplicantDao        dao.LoanApplicantDao
	deeplinkFactory         deeplink.IDeeplinkProviderFactory
	rpcHelper               *helper.RpcHelper
	loanAccountDao          dao.LoanAccountsDao
	loanActivityDao         dao.LoanActivityDao
	loanInstallmentInfoDao  dao.LoanInstallmentInfoDao
	s3Client                s3.S3Client
	txnExecutorProvider     *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	userClient              usersPb.UsersClient
	sgDlProvider            *stock_guardian.Provider
	txnExecutor             storageV2.TxnExecutor
	calculatorFactory       calculatorTypes.FactoryProvider
	authClient              authPb.OrchestratorClient
	lmsProviders            loandataprovider.IFactory
	creditReportConfig      *commonGenConf.CreditReportConfig
	creditReportV2Client    creditReportV2Pb.CreditReportManagerClient
	loecDao                 dao.LoanOfferEligibilityCriteriaDao
	breClient               brePb.BreClient
	config                  *genconf.Config
	dataDevS3Client         types2.DataDevS3Client
	sgApplicantApiGateway   sgApplicantApiGatewayPb.ApplicantServiceClient
	sgApplicationApiGateway sgApplicantionApiGatewayPb.ApplicationClient
	sgCustomerApiGateway    sgCustomerApiGatewayPb.CustomerServiceClient
	sgEsignApiGateway       sgEsignApiGatewayPb.EsignClient
	sgMatrixApiGateway      sgMatrixApiGatewayPb.MatrixClient
	sgKycApiGateway         sgKycApiGatewayPb.KYCClient
	sgLmsApiGateway         sgLmsApiGatewayPb.LmsClient
	recurringPaymentClient  recurringpaymentpb.RecurringPaymentServiceClient
	riskClient              riskPb.RiskClient
	omegleClient            omegleVkycClient.OmegleClient
	empClient               empPb.EmploymentClient
	palClient               palPb.PreApprovedLoanClient
	userDataProvider        userdata.IUserDataProvider
	onbClient               onbPb.OnboardingClient
	savingsClient           savings.SavingsClient
	creditReportHelper      *palActivity.CreditReportHelper
}

// nolint:funlen
func NewProcessor(
	loanRequestDao dao.LoanRequestsDao,
	loanOfferDao dao.LoanOffersDao,
	deeplinkFactory deeplink.IDeeplinkProviderFactory,
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	rpcHelper *helper.RpcHelper,
	loanAccountDao dao.LoanAccountsDao,
	loanActivityDao dao.LoanActivityDao,
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao,
	s3Client types2.PreApprovedLoanS3Client,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	userClient usersPb.UsersClient,
	sgDlProvider *stock_guardian.Provider,
	txnExecutor storageV2.TxnExecutor,
	calculatorFactory calculatorTypes.FactoryProvider,
	authClient authPb.OrchestratorClient,
	lmsProviders loandataprovider.IFactory,
	creditReportConfig *commonGenConf.CreditReportConfig,
	creditReportV2Client creditReportV2Pb.CreditReportManagerClient,
	loecDao dao.LoanOfferEligibilityCriteriaDao,
	breClient brePb.BreClient,
	config *genconf.Config,
	dataDevS3Client types2.DataDevS3Client,
	sgApplicantApiGateway sgApplicantApiGatewayPb.ApplicantServiceClient,
	sgApplicationApiGateway sgApplicantionApiGatewayPb.ApplicationClient,
	sgCustomerApiGateway sgCustomerApiGatewayPb.CustomerServiceClient,
	sgEsignApiGateway sgEsignApiGatewayPb.EsignClient,
	sgMatrixApiGateway sgMatrixApiGatewayPb.MatrixClient,
	loanApplicantDao dao.LoanApplicantDao,
	recurringPaymentClient recurringpaymentpb.RecurringPaymentServiceClient,
	sgKycApiGateway sgKycApiGatewayPb.KYCClient,
	riskClient riskPb.RiskClient,
	omegleClient omegleVkycClient.OmegleClient,
	empClient empPb.EmploymentClient,
	sgLmsApiGateway sgLmsApiGatewayPb.LmsClient,
	palClient palPb.PreApprovedLoanClient,
	userDataProvider userdata.IUserDataProvider,
	onbClient onbPb.OnboardingClient,
	savingsClient savings.SavingsClient,
	creditReportHelper *palActivity.CreditReportHelper,
) *Processor {
	return &Processor{
		loanRequestDao:          loanRequestDao,
		loanOfferDao:            loanOfferDao,
		deeplinkFactory:         deeplinkFactory,
		loanStepExecutionDao:    loanStepExecutionDao,
		rpcHelper:               rpcHelper,
		loanAccountDao:          loanAccountDao,
		loanActivityDao:         loanActivityDao,
		loanInstallmentInfoDao:  loanInstallmentInfoDao,
		s3Client:                s3Client,
		txnExecutorProvider:     txnExecutorProvider,
		userClient:              userClient,
		sgDlProvider:            sgDlProvider,
		txnExecutor:             txnExecutor,
		calculatorFactory:       calculatorFactory,
		authClient:              authClient,
		lmsProviders:            lmsProviders,
		creditReportConfig:      creditReportConfig,
		creditReportV2Client:    creditReportV2Client,
		loecDao:                 loecDao,
		breClient:               breClient,
		config:                  config,
		dataDevS3Client:         dataDevS3Client,
		sgApplicantApiGateway:   sgApplicantApiGateway,
		sgApplicationApiGateway: sgApplicationApiGateway,
		sgCustomerApiGateway:    sgCustomerApiGateway,
		sgEsignApiGateway:       sgEsignApiGateway,
		sgMatrixApiGateway:      sgMatrixApiGateway,
		loanApplicantDao:        loanApplicantDao,
		recurringPaymentClient:  recurringPaymentClient,
		sgKycApiGateway:         sgKycApiGateway,
		riskClient:              riskClient,
		omegleClient:            omegleClient,
		empClient:               empClient,
		sgLmsApiGateway:         sgLmsApiGateway,
		palClient:               palClient,
		userDataProvider:        userDataProvider,
		onbClient:               onbClient,
		savingsClient:           savingsClient,
		creditReportHelper:      creditReportHelper,
	}
}

func (p *Processor) updateNextActionInLoanRequest(ctx context.Context, lrId string, dl *deeplinkPb.Deeplink) error {
	loanRequest, err := p.loanRequestDao.GetById(ctx, lrId)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting lr by id, err: %v", err))
	}
	loanRequest.NextAction = dl
	updateErr := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
	})
	if updateErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lr next action, err: %v", updateErr))
	}
	return nil
}

func (p *Processor) getStageStatusFromVendor(ctx context.Context, applicationId string, stageName sgApplicantionApiGatewayPb.LoanApplicationStageName) (sgApplicantionApiGatewayPb.LoanApplicationStageStatus, error) {
	applicationStatusResp, applicationStatusErr := p.sgApplicationApiGateway.GetApplicationStatus(ctx, &sgApplicantionApiGatewayPb.GetApplicationStatusRequest{
		LoanHeader: &sgApplicantionApiGatewayPb.LoanHeader{
			ClientId: clientId,
		},
		ApplicationId: applicationId,
	})
	if te := epifigrpc.RPCError(applicationStatusResp, applicationStatusErr); te != nil {
		return sgApplicantionApiGatewayPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get applicant status, err: %v", te))
	}

	var stageStatus sgApplicantionApiGatewayPb.LoanApplicationStageStatus
	for _, stage := range applicationStatusResp.GetStageDetails() {
		if stageName == stage.GetStageName() {
			stageStatus = stage.GetStageStatus()
		}
		if isVendorFailedTerminalStatus(stage.GetStageStatus()) {
			return sgApplicantionApiGatewayPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("received a failed terminal status at stageName: %s and status: %s", stage.GetStageName().String(), stage.GetStageStatus().String()))
		}
	}
	return stageStatus, nil
}

func (p *Processor) getKycStageStatusFromVendor(ctx context.Context, applicationId string, stageName sgMatrixApiGatewayPb.Stage) (sgMatrixApiGatewayPb.StageStatus, error) {
	kycStatusResp, kycStatusErr := p.sgMatrixApiGateway.GetCustomerApplicationDetails(ctx, &sgMatrixApiGatewayPb.GetCustomerApplicationDetailsRequest{
		ApplicationId: applicationId,
		Stage:         stageName,
	})
	if te := epifigrpc.RPCError(kycStatusResp, kycStatusErr); te != nil {
		return sgMatrixApiGatewayPb.StageStatus_STAGE_STATUS_UNSPECIFIED, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get kyc status, err: %v", te))
	}

	stageStatus := kycStatusResp.GetApplicationDetails().GetStatus()
	if isKycVendorFailedTerminalStatus(stageStatus) {
		return sgMatrixApiGatewayPb.StageStatus_STAGE_STATUS_UNSPECIFIED, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("received a failed kyc terminal status at stageName: %s and status: %s", stageName.String(), stageStatus.String()))
	}
	return stageStatus, nil
}

func isVendorFailedTerminalStatus(status sgApplicantionApiGatewayPb.LoanApplicationStageStatus) bool {
	if status == sgApplicantionApiGatewayPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_EXPIRED ||
		status == sgApplicantionApiGatewayPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_CANCELLED ||
		status == sgApplicantionApiGatewayPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_FAILED {
		return true
	}
	return false
}

func isKycVendorFailedTerminalStatus(status sgMatrixApiGatewayPb.StageStatus) bool {
	if status == sgMatrixApiGatewayPb.StageStatus_STAGE_STATUS_FAILED {
		return true
	}
	return false
}
