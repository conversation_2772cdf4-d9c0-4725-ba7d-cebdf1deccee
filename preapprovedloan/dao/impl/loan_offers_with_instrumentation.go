//nolint:dupl
package impl

import (
	"context"
	"time"

	"github.com/google/wire"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"
	preapprovedloanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/metrics"
	wireTypes "github.com/epifi/gamma/preapprovedloan/wire/types"
)

type CrdbLoanOffersDaoWithInstrumentation struct {
	*CrdbLoanOffersDaoMultiDB
	eventBroker events.Broker
}

var LoanOffersDaoWithInstrumentationWireSet = wire.NewSet(NewCrdbLoanOffersDaoWithInstrumentation, wire.Bind(new(dao.LoanOffersDao), new(*CrdbLoanOffersDaoWithInstrumentation)))

func NewCrdbLoanOffersDaoWithInstrumentation(dbResourceProvider *storage.DBResourceProvider[*gorm.DB], idGen idgen.IdGenerator, cache wireTypes.LendingCacheStorage, eventBroker events.Broker) *CrdbLoanOffersDaoWithInstrumentation {
	return &CrdbLoanOffersDaoWithInstrumentation{
		CrdbLoanOffersDaoMultiDB: NewCrdbLoanOffersDaoMultiDB(dbResourceProvider, idGen, cache),
		eventBroker:              eventBroker,
	}
}

func (c *CrdbLoanOffersDaoWithInstrumentation) Create(ctx context.Context, lo *preapprovedloanPb.LoanOffer) (*preapprovedloanPb.LoanOffer, error) {
	defer metric_util.TrackDuration("preapprovedloan/dao/impl", "CrdbLoanOffersDaoWithInstrumentation", "Create", time.Now())
	newLo, err := c.CrdbLoanOffersDaoMultiDB.Create(ctx, lo)
	if err != nil {
		return nil, err
	}
	logLoanOfferCreate(ctx, newLo)
	metrics.RecordLoanOfferCreation(newLo.GetVendor(), newLo.GetLoanProgram(), newLo.GetLoanOfferType())
	return newLo, nil
}

func logLoanOfferCreate(ctx context.Context, lo *preapprovedloanPb.LoanOffer) {
	logger.Info(ctx, "Loan Offer created",
		zap.String(logger.VENDOR, lo.GetVendor().String()),
		zap.String(logger.LOAN_PROGRAM, lo.GetLoanProgram().String()),
		zap.String(logger.OFFER_ID, lo.GetId()),
		zap.String("offerType", lo.GetLoanOfferType().String()),
		zap.String("loecId", lo.GetLoanOfferEligibilityCriteriaId()))
}
