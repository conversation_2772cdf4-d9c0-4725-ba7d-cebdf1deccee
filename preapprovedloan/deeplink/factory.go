package deeplink

import (
	"context"

	"github.com/google/wire"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/abfl"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/epifitech"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/federal"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/fiftyfin"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/idfc"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/lenden"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/moneyview"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/realtimeetb"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/stock_guardian"
)

var WireDlProviders = wire.NewSet(federal.NewRealTimeFedProvider, liquiloans.NewRealtimeSubventionProvider, liquiloans.NewRealTimeStplProvider, liquiloans.NewNonFiCoreSubventionProvider, liquiloans.NewNonFiCoreStplProvider, moneyview.NewMvProvider, liquiloans.NewRealtimeDistProvider, liquiloans.NewAcqToLendProvider,
	abfl.NewAbflProvider, liquiloans.NewLiquiloansFiLiteProvider, liquiloans.NewStplProvider, liquiloans.NewFldgProvider, liquiloans.NewLiquiloansEarlySalaryProvider, fiftyfin.NewProvider,
	idfc.NewProvider, realtimeetb.NewProvider, liquiloans.NewProvider, epifitech.NewEligibilityProvider, stock_guardian.NewStockGuardianProvider, lenden.NewLendenProvider, abfl.NewAbflPwaProvider, federal.NewEligibilityProvider, federal.NewRealTimeNtbFedProvider, moneyview.NewNonFiCoreMvProvider, stock_guardian.NewStockGuardianProviderEarlySalary)
var WireDeeplinkProviderFactorySet = wire.NewSet(WireDlProviders, NewDeeplinkProviderFactory, wire.Bind(new(IDeeplinkProviderFactory), new(*ProviderFactory)))

//go:generate mockgen -source=factory.go -destination=./mocks/factory.go -package=mocks
type IDeeplinkProviderFactory interface {
	GetDeeplinkGenerator(ctx context.Context, req *GetDeeplinkProviderRequest) provider.IDeeplinkProvider
}

type GetDeeplinkProviderRequest struct {
	Vendor      palPb.Vendor
	LoanProgram palPb.LoanProgram
}

type ProviderFactory struct {
	baseDeeplinkProvider          *baseprovider.Provider
	liquiloansProvider            *liquiloans.PlProvider
	llEarlySalaryProvider         *liquiloans.EsProvider
	idfcProvider                  *idfc.Provider
	llFldgProvider                *liquiloans.FldgProvider
	llFiLiteProvider              *liquiloans.FiLiteProvider
	llAcqToLendProvider           *liquiloans.AcqToLendProvider
	fiftyfinProvider              *fiftyfin.Provider
	abflProvider                  *abfl.Provider
	realtimeEtbProvider           *realtimeetb.Provider
	llRealtimeDistProvider        *liquiloans.RealtimeDistProvider
	mvProvider                    *moneyview.MvProvider
	llStplProvider                *liquiloans.StplProvider
	llRealtimeSubventionProvider  *liquiloans.RealtimeSubventionProvider
	fedAaProvider                 *federal.RealTimeFedProvider
	llRealtimeStplProvider        *liquiloans.RealTimeStplProvider
	epifiTechEligibilityProvider  *epifitech.EligibilityProvider
	llNonFiCoreStplProvider       *liquiloans.NonFiCoreStplProvider
	llNonFiCoreSubventionProvider *liquiloans.NonFiCoreSubventionProvider
	stockGuardianProvider         *stock_guardian.Provider
	lendenProvider                *lenden.Provider
	lendenEligibilityProvider     *lenden.EligibilityProvider
	abflPwaJourneyProvider        *abfl.AbflPwaProvider
	fedEligibilityProvider        *federal.EligibilityProvider
	fedRtdNtbProvider             *federal.RealTimeNtbFedProvider
	nonFiCoreMvProvider           *moneyview.NonFiCoreMvProvider
	earlySalarySgProvider         *stock_guardian.StockGuardianProviderEarlySalary
}

func NewDeeplinkProviderFactory(
	baseDeeplinkProvider *baseprovider.Provider,
	liquiloansProvider *liquiloans.PlProvider,
	llEarlySalaryProvider *liquiloans.EsProvider,
	idfcProvider *idfc.Provider,
	llFldgProvider *liquiloans.FldgProvider,
	llFiLiteProvider *liquiloans.FiLiteProvider,
	llAcqToLendProvider *liquiloans.AcqToLendProvider,
	fiftyfinProvider *fiftyfin.Provider,
	abflProvider *abfl.Provider,
	realtimeEtbProvider *realtimeetb.Provider,
	llRealtimeDistProvider *liquiloans.RealtimeDistProvider,
	mvProvider *moneyview.MvProvider,
	llStplProvider *liquiloans.StplProvider,
	llRealtimeSubventionProvider *liquiloans.RealtimeSubventionProvider,
	fedAaProvider *federal.RealTimeFedProvider,
	llRealtimeStplProvider *liquiloans.RealTimeStplProvider,
	epifiTechEligibilityProvider *epifitech.EligibilityProvider,
	llNonFiCoreStplProvider *liquiloans.NonFiCoreStplProvider,
	llNonFiCoreSubventionProvider *liquiloans.NonFiCoreSubventionProvider,
	stockGuardianProvider *stock_guardian.Provider,
	lendenProvider *lenden.Provider,
	lendenEligibilityProvider *lenden.EligibilityProvider,
	abflPwaJourneyProvider *abfl.AbflPwaProvider,
	fedEligibilityProvider *federal.EligibilityProvider,
	fedRtdNtbProvider *federal.RealTimeNtbFedProvider,
	nonFiCoreMvProvider *moneyview.NonFiCoreMvProvider,
	earlySalarySgProvider *stock_guardian.StockGuardianProviderEarlySalary,
) *ProviderFactory {
	return &ProviderFactory{
		baseDeeplinkProvider:          baseDeeplinkProvider,
		liquiloansProvider:            liquiloansProvider,
		llEarlySalaryProvider:         llEarlySalaryProvider,
		idfcProvider:                  idfcProvider,
		llFldgProvider:                llFldgProvider,
		llFiLiteProvider:              llFiLiteProvider,
		llAcqToLendProvider:           llAcqToLendProvider,
		fiftyfinProvider:              fiftyfinProvider,
		abflProvider:                  abflProvider,
		realtimeEtbProvider:           realtimeEtbProvider,
		llRealtimeDistProvider:        llRealtimeDistProvider,
		mvProvider:                    mvProvider,
		llStplProvider:                llStplProvider,
		llRealtimeSubventionProvider:  llRealtimeSubventionProvider,
		fedAaProvider:                 fedAaProvider,
		llRealtimeStplProvider:        llRealtimeStplProvider,
		epifiTechEligibilityProvider:  epifiTechEligibilityProvider,
		llNonFiCoreStplProvider:       llNonFiCoreStplProvider,
		llNonFiCoreSubventionProvider: llNonFiCoreSubventionProvider,
		stockGuardianProvider:         stockGuardianProvider,
		lendenProvider:                lendenProvider,
		lendenEligibilityProvider:     lendenEligibilityProvider,
		abflPwaJourneyProvider:        abflPwaJourneyProvider,
		fedEligibilityProvider:        fedEligibilityProvider,
		fedRtdNtbProvider:             fedRtdNtbProvider,
		nonFiCoreMvProvider:           nonFiCoreMvProvider,
		earlySalarySgProvider:         earlySalarySgProvider,
	}
}

// nolint:gocritic
func (d *ProviderFactory) GetDeeplinkGenerator(_ context.Context, req *GetDeeplinkProviderRequest) provider.IDeeplinkProvider {
	switch req.Vendor {
	case palPb.Vendor_LIQUILOANS:
		switch req.LoanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_FLDG:
			return d.llFldgProvider
		case palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
			return d.llFiLiteProvider
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			return d.liquiloansProvider
		case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
			return d.llEarlySalaryProvider
		case palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
			return d.llAcqToLendProvider
		case palPb.LoanProgram_LOAN_PROGRAM_LAMF:
			return d.fiftyfinProvider
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return d.llRealtimeDistProvider
		case palPb.LoanProgram_LOAN_PROGRAM_STPL:
			return d.llStplProvider
		case palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
			return d.llRealtimeSubventionProvider
		case palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
			return d.llRealtimeStplProvider
		case palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
			return d.llNonFiCoreSubventionProvider
		case palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
			return d.llNonFiCoreStplProvider
		default:
			return d.liquiloansProvider
		}
	case palPb.Vendor_IDFC:
		return d.idfcProvider
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		switch req.LoanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
			return d.earlySalarySgProvider
		default:
			return d.stockGuardianProvider
		}
	case palPb.Vendor_ABFL:
		switch req.LoanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			return d.abflProvider
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return d.abflPwaJourneyProvider
		default:
			return d.abflProvider
		}
	case palPb.Vendor_FIFTYFIN:
		if req.LoanProgram == palPb.LoanProgram_LOAN_PROGRAM_LAMF {
			return d.fiftyfinProvider
		}
		return d.baseDeeplinkProvider
	case palPb.Vendor_MONEYVIEW:
		switch req.LoanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			return d.mvProvider
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return d.nonFiCoreMvProvider
		default:
			return d.mvProvider
		}
	case palPb.Vendor_EPIFI_TECH:
		switch req.LoanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
			return d.realtimeEtbProvider
		case palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
			return d.epifiTechEligibilityProvider
		}
	case palPb.Vendor_FEDERAL:
		switch req.LoanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return d.fedAaProvider
		case palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
			return d.fedEligibilityProvider
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
			return d.fedRtdNtbProvider
		default:
			return d.baseDeeplinkProvider
		}
	case palPb.Vendor_LENDEN:
		switch req.LoanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return d.lendenProvider
		case palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
			return d.lendenEligibilityProvider
		}
	}
	return d.baseDeeplinkProvider
}
