// nolint:gocritic
package deeplink

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/nulltypes"

	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/logger"

	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"

	fePb "github.com/epifi/gamma/api/frontend"
	"github.com/epifi/gamma/api/frontend/analyser"
	"github.com/epifi/gamma/api/frontend/analytics"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/frontend"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
	"github.com/epifi/gamma/preapprovedloan/utils"
)

const (
	// TODO(Diparth): Store this value in config or accepts this as method param
	maxBankAccAllowedInMandate = 3
)

func GetBeLoanProgramFromFe(feLp palFeEnumsPb.LoanProgram) palBePb.LoanProgram {
	switch feLp {
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		return palBePb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG:
		return palBePb.LoanProgram_LOAN_PROGRAM_FLDG
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL:
		return palBePb.LoanProgram_LOAN_PROGRAM_STPL
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
		return palBePb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF:
		return palBePb.LoanProgram_LOAN_PROGRAM_LAMF
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
		return palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		return palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
		return palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		return palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		return palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		return palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
		return palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION
	default:
		return palBePb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
	}
}

func GetFeLoanProgramFromBe(beLp palBePb.LoanProgram) palFeEnumsPb.LoanProgram {
	switch beLp {
	case palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN
	case palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY
	case palBePb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL
	case palBePb.LoanProgram_LOAN_PROGRAM_FLDG:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FLDG
	case palBePb.LoanProgram_LOAN_PROGRAM_STPL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_STPL
	case palBePb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND
	case palBePb.LoanProgram_LOAN_PROGRAM_LAMF:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF
	case palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB
	case palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION
	case palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2
	case palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION
	case palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL
	case palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY
	case palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL
	case palBePb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION
	default:
		return palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
	}
}

// TODO(mohitswain): move this file to loans_vendor_helper.go
func GetBeVendorFromFe(feVendor palFeEnumsPb.Vendor) palBePb.Vendor {
	switch feVendor {
	case palFeEnumsPb.Vendor_FEDERAL_BANK:
		return palBePb.Vendor_FEDERAL
	case palFeEnumsPb.Vendor_LIQUILOANS:
		return palBePb.Vendor_LIQUILOANS
	case palFeEnumsPb.Vendor_IDFC:
		return palBePb.Vendor_IDFC
	case palFeEnumsPb.Vendor_FIFTYFIN:
		return palBePb.Vendor_FIFTYFIN
	case palFeEnumsPb.Vendor_EPIFI_TECH:
		return palBePb.Vendor_EPIFI_TECH
	case palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP:
		return palBePb.Vendor_STOCK_GUARDIAN_LSP
	case palFeEnumsPb.Vendor_LENDEN:
		return palBePb.Vendor_LENDEN
	default:
		return palBePb.Vendor_VENDOR_UNSPECIFIED
	}
}

func GetFeVendorFromBe(beVendor palBePb.Vendor) fePb.Vendor {
	switch beVendor {
	case palBePb.Vendor_FEDERAL:
		return fePb.Vendor_FEDERAL_BANK
	case palBePb.Vendor_LIQUILOANS:
		return fePb.Vendor_LIQUILOANS
	case palBePb.Vendor_IDFC:
		return fePb.Vendor_IDFC
	case palBePb.Vendor_FIFTYFIN:
		return fePb.Vendor_FIFTYFIN
	default:
		return fePb.Vendor_VENDOR_UNSPECIFIED
	}
}

// TODO(mohitswain): move this file to loans_vendor_helper.go
func GetPalFeVendorFromBe(beVendor palBePb.Vendor) palFeEnumsPb.Vendor {
	switch beVendor {
	case palBePb.Vendor_FEDERAL:
		return palFeEnumsPb.Vendor_FEDERAL_BANK
	case palBePb.Vendor_LIQUILOANS:
		return palFeEnumsPb.Vendor_LIQUILOANS
	case palBePb.Vendor_IDFC:
		return palFeEnumsPb.Vendor_IDFC
	case palBePb.Vendor_FIFTYFIN:
		return palFeEnumsPb.Vendor_FIFTYFIN
	case palBePb.Vendor_EPIFI_TECH:
		return palFeEnumsPb.Vendor_EPIFI_TECH
	case palBePb.Vendor_STOCK_GUARDIAN_LSP:
		return palFeEnumsPb.Vendor_STOCK_GUARDIAN_LSP
	case palBePb.Vendor_LENDEN:
		return palFeEnumsPb.Vendor_LENDEN
	default:
		return palFeEnumsPb.Vendor_VENDOR_UNSPECIFIED
	}
}

func GetBeLoanHeaderByFeLoanHeader(lh *palFeEnumsPb.LoanHeader) *palBePb.LoanHeader {
	return &palBePb.LoanHeader{
		LoanProgram: GetBeLoanProgramFromFe(lh.GetLoanProgram()),
		Vendor:      GetBeVendorFromFe(lh.GetVendor()),
	}
}

func GetFeLoanHeaderByBeLoanHeader(lh *palBePb.LoanHeader) *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: GetFeLoanProgramFromBe(lh.GetLoanProgram()),
		Vendor:      GetPalFeVendorFromBe(lh.GetVendor()),
	}
}

// nolint:funlen
func GetApplicationFailureDeeplink(lh *palFeEnumsPb.LoanHeader, lse *palBePb.LoanStepExecution, wfStatus stagePb.Status) (*deeplinkPb.Deeplink, error) {
	dl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_DASHBOARD_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanDashboardScreenOptions{
			PreApprovedLoanDashboardScreenOptions: &deeplinkPb.PreApprovedLoanDashboardScreenOptions{
				LoanHeader: lh,
			},
		},
	}
	switch lh.GetLoanProgram() {
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
		dl = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
				PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
					LoanHeader: lh,
				},
			},
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		dl.Screen = deeplinkPb.Screen_EARLY_SALARY_DASHBOARD_SCREEN
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		if lse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CALL_VENDOR ||
			lse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_BRE ||
			lse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER ||
			lse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CHECK_FI_PRE_BRE {
			dl = GetEligibilityCheckFailedScreenDeeplink(lh)
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_LAMF:
		if wfStatus == stagePb.Status_FAILED {
			var err error
			dl, err = deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_FAILURE_SCREEN, &palTypesPb.LoansFailureScreen{
				LoanHeader: lh,
				PageHeader: &widget.VisualElementTitleSubtitleElement{
					VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/filled_triangle_warning.png").WithProperties(&commontypes.VisualElementProperties{Width: 72, Height: 72}),
					TitleText:     commontypes.GetPlainStringText("Looks like there was an issue").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#000000"),
					SubtitleText:  commontypes.GetPlainStringText("Don't worry! You can retry in some time.").WithFontStyle(commontypes.FontStyle_BODY_3_PARA).WithFontColor("#929599"),
				},
				BackCta: &deeplinkPb.Button{
					Text: helper.GetText("Back to home", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_M),
					Cta: &deeplinkPb.Cta{
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
							ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
								PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
									LoanHeader: lh,
								},
							},
						},
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
						Type:         deeplinkPb.Cta_DONE,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
						Text:         "Back to home",
					},
					Padding: helper.GetButtonPadding(12, 24, 12, 24),
					Margin:  helper.GetButtonMargin(16, 24, 24, 24),
				},
				AnalyticsScreenName: analytics.AnalyticsScreenName_LAMF_PERMANENT_FAILURE_FULL_SCREEN,
				Components: []*palTypesPb.LoansFailureScreenComponent{
					{
						Component: &palTypesPb.LoansFailureScreenComponent_TitleWithImage{
							TitleWithImage: &palTypesPb.VisualElementTitleSubtitleComponent{
								Component: &widget.VisualElementTitleSubtitleElement{
									VisualElement:   commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/filled_triangle_warning.png").WithProperties(&commontypes.VisualElementProperties{Width: 72, Height: 72}),
									TitleText:       commontypes.GetPlainStringText("Looks like there was an issue").WithFontStyle(commontypes.FontStyle_HEADLINE_L).WithFontColor("#000000"),
									SubtitleText:    commontypes.GetPlainStringText("Don't worry! You can retry in some time.").WithFontStyle(commontypes.FontStyle_BODY_3_PARA).WithFontColor("#929599"),
									BackgroundColor: "#FFFFFF",
								},
								TopMargin: 72,
							},
						},
					},
				},
			})
			if err != nil {
				return nil, fmt.Errorf("error while generating lamf permanant failure screen : %w", err)
			}
		} else {
			dl = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationStatusPollScreenOptions{
					PreApprovedLoanApplicationStatusPollScreenOptions: &deeplinkPb.PreApprovedLoanApplicationStatusPollScreenOptions{
						RetryAttemptNumber: 30,
						RetryDelay:         10_000,
						RetryDuration:      300_000,
						LoanRequestId:      lse.GetRefId(),
						PollingText: &deeplinkPb.InfoItemV2{
							Title: &commontypes.Text{
								FontColor:    "#000000",
								DisplayValue: &commontypes.Text_PlainString{PlainString: "Uh-oh! this is taking longer than expected"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
							},
							SubTitle: &commontypes.Text{
								FontColor: "#000000",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "We are working on resolving it",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
							},
							Icon: "https://epifi-icons.pointz.in/lamf/loader_doc.png",
						},
						LoanHeader:       lh,
						BackgroundColour: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
					},
				},
			}
		}
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		dl = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
				PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
					LoanHeader: lh,
				},
			},
		}
	}
	return dl, nil
}

func GetApplicationStatusPollScreenDeepLink(requestId string, lh *palFeEnumsPb.LoanHeader) *deeplinkPb.Deeplink {
	dl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationStatusPollScreenOptions{
			PreApprovedLoanApplicationStatusPollScreenOptions: &deeplinkPb.PreApprovedLoanApplicationStatusPollScreenOptions{
				RetryAttemptNumber: 30,
				RetryDelay:         1000,
				LoanRequestId:      requestId,
				PollingText: &deeplinkPb.InfoItemV2{
					Title: &commontypes.Text{
						FontColor:    "#333333",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Please wait"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
					},
				},
				LoanHeader:       lh,
				BackgroundColour: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
			},
		},
	}
	switch lh.GetLoanProgram() {
	case palFeEnumsPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		dl.Screen = deeplinkPb.Screen_EARLY_SALARY_STATUS_POLL_SCREEN
	}
	return dl
}

func GetApplicationReviewDetailsScreenDeeplink(lh *palFeEnumsPb.LoanHeader, lrId string) *deeplinkPb.Deeplink {
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_PL_APPLICATION_REVIEW_DETAILS_SCREEN, &palTypesPb.LoanApplicationReviewDetailsScreenOptions{
		LoanHeader:    lh,
		LoanRequestId: lrId,
	})
	if dlErr != nil {
		logger.ErrorNoCtx("error in generating deeplink for application review details screen", zap.Error(dlErr))
		dl = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PL_APPLICATION_REVIEW_DETAILS_SCREEN}
	}
	return dl
}

func GetApplicationStatusPollScreenWithCustomMsgDeepLinkV2(requestId string, pollingText string, fontColor string, iconUrl string, lh *palBePb.LoanHeader) (*deeplinkPb.Deeplink, error) {
	var visualElem *commontypes.VisualElement
	if iconUrl != "" {
		visualElem = commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 120, 120)
	}
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_STATUS_POLL_SCREEN, &palTypesPb.LoansStatusPollScreenOptions{
		Id:            uuid.New().String(),
		RetryDelay:    200,
		RetryDuration: 300_000,
		LoanRequestId: requestId,
		LoanHeader:    GetFeLoanHeaderByBeLoanHeader(lh),
		PollingView: &palTypesPb.PollingView{
			View: &palTypesPb.PollingView_PollingTextWithVisualView{
				PollingTextWithVisualView: &palTypesPb.PollingView_TextWithVisualView{
					TextWithVisual: &widget.VisualElementTitleSubtitleElement{
						TitleText:     commontypes.GetTextFromStringFontColourFontStyle(pollingText, fontColor, commontypes.FontStyle_SUBTITLE_1),
						VisualElement: visualElem,
					},
				},
			},
		},
	})
}

func GetApplicationStatusPollScreenWithCustomMsgDeepLink(requestId string, pollingText string, fontColor string, iconUrl string, lh *palBePb.LoanHeader) (*deeplinkPb.Deeplink, error) {
	dl := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationStatusPollScreenOptions{
			PreApprovedLoanApplicationStatusPollScreenOptions: &deeplinkPb.PreApprovedLoanApplicationStatusPollScreenOptions{
				RetryAttemptNumber: 30,
				RetryDelay:         1000,
				LoanRequestId:      requestId,
				PollingText: &deeplinkPb.InfoItemV2{
					Title: &commontypes.Text{
						FontColor:    fontColor,
						DisplayValue: &commontypes.Text_PlainString{PlainString: pollingText},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
					},
				},
				LoanHeader:       GetFeLoanHeaderByBeLoanHeader(lh),
				BackgroundColour: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
			},
		},
	}
	if iconUrl != "" {
		dl.GetPreApprovedLoanApplicationStatusPollScreenOptions().GetPollingText().Icon = iconUrl
	}
	return dl, nil
}

// nolint: funlen
func GetEligibilityCheckFailedScreenDeeplink(lh *palFeEnumsPb.LoanHeader) *deeplinkPb.Deeplink {
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_PL_ELIGIBILITY_NO_OFFER_AVAILABLE_SCREEN, &palTypesPb.LoanEligibilityNoOfferAvailableScreenOptions{
		LoanHeader: lh,
		VisualElementTitleSubtitleElement: &widget.VisualElementTitleSubtitleElement{
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-no-offer.png",
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  80,
							Height: 80,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
			TitleText:    helper.GetText("You don't have an Instant Loan offer now", "#313234", "", commontypes.FontStyle_HEADLINE_XL),
			SubtitleText: helper.GetText("We'll let you know once we have an offer for you", "#6A6D70", "", commontypes.FontStyle_BODY_S),
		},
		HomeButton: &deeplinkPb.Button{
			Text: helper.GetText("Back to Home", "#00B899", "#F6F9FD", commontypes.FontStyle_BUTTON_M),
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   24,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   24,
				RightMargin:  24,
				BottomMargin: 12,
				TopMargin:    12,
			},
			Cta: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         "Back to Home",
				Deeplink:     &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_HOME},
				DisplayTheme: deeplinkPb.Cta_TEXT,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
		},
		LearnWhyButton: &deeplinkPb.Button{
			Text: helper.GetText("Learn why", "#00B899", "", commontypes.FontStyle_BUTTON_S),
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   56,
				RightPadding:  56,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   24,
				RightMargin:  24,
				BottomMargin: 12,
				TopMargin:    12,
			},
			Cta: nil,
		},
		ReasonsForNoOffer: &palTypesPb.NoLoanOfferReasons{
			Title: helper.GetText("Here’s how you can improve your chances of getting one", "##313234", "", commontypes.FontStyle_HEADLINE_M),
			Reasons: []*palTypesPb.NoLoanOfferReasons_ReasonBlock{
				{
					BgColor: "#FFFFFF",
					Title:   helper.GetText("A low credit score", "#313234", "", commontypes.FontStyle_HEADLINE_M),
					Icon: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-no-offer-credit.png",
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  40,
									Height: 40,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					Description: helper.GetText("This indicates that you have had difficulty repaying your loan in the past, which makes you a riskier borrower.", "#929599", "", commontypes.FontStyle_BODY_S),
					BlockActionCta: &deeplinkPb.Cta{
						Type: deeplinkPb.Cta_CUSTOM,
						Text: "Check your credit score",
						Deeplink: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_ANALYSER_SCREEN,
							ScreenOptions: &deeplinkPb.Deeplink_AnalyserScreenOptions{
								AnalyserScreenOptions: &deeplinkPb.AnalyserScreenOptions{
									AnalyserName: analyser.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
								},
							},
						},
						DisplayTheme: deeplinkPb.Cta_TEXT,
						Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
						IconUrl:      "https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-no-offer-credit-arrow.png",
					},
				},
				{
					BgColor: "#FFFFFF",
					Title:   helper.GetText("Your location is not serviceable", "#313234", "", commontypes.FontStyle_HEADLINE_M),
					Icon: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-no-offer-location.png",
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  40,
									Height: 40,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					Description:    helper.GetText("We may not be serviceable in some locations. But we'll be coming soon!", "#929599", "", commontypes.FontStyle_BODY_S),
					BlockActionCta: nil,
				},
				{
					BgColor: "#FFFFFF",
					Title:   helper.GetText("You’re under the age of 21", "#313234", "", commontypes.FontStyle_HEADLINE_M),
					Icon: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-no-offer-age.png",
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  40,
									Height: 40,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					Description:    helper.GetText("To apply for a loan, you must be at least 21 years old as per legal requirements.", "#929599", "", commontypes.FontStyle_BODY_S),
					BlockActionCta: nil,
				},
			},
		},
		BgColor: "#EFF2F6",
	})
	if dlErr != nil {
		logger.ErrorNoCtx("error in generating deeplink for eligibility no offer available screen", zap.Error(dlErr))
		dl = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PL_ELIGIBILITY_NO_OFFER_AVAILABLE_SCREEN}
	}
	return dl
}

// ConstructPlMandateInitScreenV2FromLse encapsulates all the steps to generate mandate init v2 deeplink
// uses deeplink provider, default acc details and lse to generate
// banking details dl, alt acc details dl and feeds them to generate mandate init v2 dl
// TODO: make allowUserToChangeDefaultAccount inside a struct param
func ConstructPlMandateInitScreenV2FromLse(ctx context.Context,
	deeplinkProvider provider.IDeeplinkProvider,
	lse *palBePb.LoanStepExecution,
	defaultAccDetails *palBePb.MandateData_BankingDetails_AccountDetails,
	allowUserToChangeDefaultAccount bool,
	nextDeeplink *deeplinkPb.Deeplink) (*deeplinkPb.Deeplink, error) {
	if allowUserToChangeDefaultAccount {
		// generate deeplink for add banking details CTA
		// deeplink can be banking details deeplink or max bank added error bottom sheet
		// depending on the number of banks added by the user
		altBankDl, altBankDlErr := getAddBankDlForAltAcc(ctx, deeplinkProvider, lse)
		if altBankDlErr != nil {
			return nil, errors.Wrap(altBankDlErr, "error while generating dl for add bank cta")
		}

		// build alternate account list from lse
		var altAccList []*palBePb.MandateData_BankingDetails_AccountDetails
		for _, accDetail := range lse.GetDetails().GetMandateData().GetBankingDetails().GetAlternateAccDetails() {
			altAccList = append(altAccList, accDetail)
		}
		if len(altAccList) == 0 {
			altAccList = nil
		}

		// generate alternate account deeplink
		altAcc, altAccErr := deeplinkProvider.GetLoansAlternateAccountsScreen(ctx, deeplinkProvider.GetLoanHeader(), lse.GetRefId(), lse.GetId(), &provider.LoansAlternateAccountsScreenParams{
			AlternateMandateBankAccDetails: altAccList,
			BankingDetailsScreenDeeplink:   altBankDl,
		})
		if altAccErr != nil {
			return nil, errors.Wrap(altAccErr, "error while generating loans alternate account screen deeplink")
		}

		// generate mandate v2 deeplink
		mandateInitDl, mdErr := deeplinkProvider.GetLoansMandateInitiateScreenV2(ctx, deeplinkProvider.GetLoanHeader(), lse.GetRefId(), lse.GetId(), &provider.LoansMandateInitiateScreenV2Params{
			DefaultMandateBankAccDetails:    defaultAccDetails,
			AlternateAccountsScreenDeeplink: altAcc,
			AllowUserToChangeDefaultAccount: allowUserToChangeDefaultAccount,
			NextDeeplink:                    nextDeeplink,
		})
		if mdErr != nil {
			return nil, errors.Wrap(mdErr, "error while generating mandate init v2 deeplink when user is allowed to change default account")
		}
		return mandateInitDl, nil
	} else { // don't get alternative accounts if user is not allowed to change default account
		mandateInitDl, mdErr := deeplinkProvider.GetLoansMandateInitiateScreenV2(ctx, deeplinkProvider.GetLoanHeader(), lse.GetRefId(), lse.GetId(), &provider.LoansMandateInitiateScreenV2Params{
			DefaultMandateBankAccDetails:    defaultAccDetails,
			AlternateAccountsScreenDeeplink: nil,
			AllowUserToChangeDefaultAccount: allowUserToChangeDefaultAccount,
			NextDeeplink:                    nextDeeplink,
		})
		if mdErr != nil {
			return nil, errors.Wrap(mdErr, "error while generating mandate init v2 deeplink when user is not allowed to change default account")
		}
		return mandateInitDl, nil
	}
}

func getAddBankDlForAltAcc(_ context.Context, deeplinkProvider provider.IDeeplinkProvider, lse *palBePb.LoanStepExecution) (*deeplinkPb.Deeplink, error) {
	// if user has added greater than n accounts then do not allow to add more accounts
	// TODO(Diparth): find better way to make this decision outside deeplink logic
	if len(lse.GetDetails().GetMandateData().GetBankingDetails().GetAlternateAccDetails()) >= maxBankAccAllowedInMandate {
		return getMaxBankAddedErrBottomSheet()
	}
	// check if user is allowed to set up the mandate on fi-federal bank account, if not set the appropriate search type
	searchType := palFeEnumsPb.SearchIfscType_SEARCH_IFSC_TYPE_UNSPECIFIED
	faqScreenType := palTypesPb.BankingDetailsBottomSheetType_BANKING_DETAILS_BOTTOM_SHEET_TYPE_UNSPECIFIED
	inEligibleReason := lse.GetDetails().GetMandateData().GetFiAccountIneligibleForMandateReason()
	if lse.GetDetails().GetMandateData().GetMandateWithFiAccountNotAllowed() {
		searchType = utils.IneligibilityReasonToSearchIfscType[inEligibleReason]
		faqScreenType = utils.MandateIneligibleWithFiAccountToFaqScreenMap[inEligibleReason]
	}
	bankDl, bankDlErr := deeplinkProvider.GetBankingDetailsScreenDeeplink(deeplinkProvider.GetLoanHeader(), lse.GetRefId(), lse.GetId(), searchType, faqScreenType, "")
	if bankDlErr != nil {
		return nil, errors.Wrap(bankDlErr, "error while generating banking details screen deeplink")
	}
	return bankDl, nil
}

func getMaxBankAddedErrBottomSheet() (*deeplinkPb.Deeplink, error) {
	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_BOTTOM_SHEET_INFO_VIEW, &frontend.BottomSheetInfoViewOptions{
		Icon: &commontypes.Image{
			ImageUrl: "https://epifi-icons.pointz.in/lending/warn-icon.png",
			Width:    80,
			Height:   80,
		},
		Title:    commontypes.GetTextFromStringFontColourFontStyle("Maximum limit hit", colors.ColorDarkLayer2, commontypes.FontStyle_HEADLINE_L),
		SubTitle: commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("You can add a maximum of %d bank accounts when applying for a loan.", maxBankAccAllowedInMandate), colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
		Ctas: []*deeplinkPb.Cta{
			{
				Type:         deeplinkPb.Cta_CONTINUE,
				Text:         "Ok",
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
			},
		},
	})
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "error while creating max bank added bottom sheet")
	}
	return dl, nil
}

func GetApplicationStatusPollDeeplinkWithParams(lh *palFeEnumsPb.LoanHeader, loanReqId string, params *provider.ApplicationStatusPollDeeplinkParams) *deeplinkPb.Deeplink {
	newParams := &provider.ApplicationStatusPollDeeplinkParams{
		Title:             nulltypes.NewNullString("Please wait"),
		RetryDurationInMs: 30_000,
		RetryBackOffInMs:  200,
	}
	if params != nil {
		if params.Icon.Valid {
			newParams.Icon = params.Icon
		}
		if params.RetryBackOffInMs != 0 {
			newParams.RetryBackOffInMs = params.RetryBackOffInMs
		}
		if params.Title.Valid {
			newParams.Title = params.Title
		}
		if params.SubTitle.Valid {
			newParams.SubTitle = params.SubTitle
		}
		if params.RetryDurationInMs != 0 {
			newParams.RetryDurationInMs = params.RetryDurationInMs
		}
		newParams.GetNextActionInSync = params.GetNextActionInSync
	}
	var title, subtitle *commontypes.Text
	var visElem *commontypes.VisualElement
	if newParams.Title.GetValue() != "" {
		title = &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: newParams.Title.GetValue()},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
		}
	}
	if newParams.SubTitle.GetValue() != "" {
		subtitle = &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: newParams.SubTitle.GetValue()},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
		}
	}
	if newParams.Icon.GetValue() != "" {
		visElem = commontypes.GetVisualElementImageFromUrl(newParams.Icon.GetValue()).WithProperties(&commontypes.VisualElementProperties{
			Width:  120,
			Height: 120,
		})
	}
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanApplicationStatusPollScreenOptions{
			PreApprovedLoanApplicationStatusPollScreenOptions: &deeplinkPb.PreApprovedLoanApplicationStatusPollScreenOptions{
				RetryAttemptNumber: 30,
				RetryDelay:         newParams.RetryBackOffInMs,
				RetryDuration:      newParams.RetryDurationInMs,
				LoanRequestId:      loanReqId,
				PollingText: &deeplinkPb.InfoItemV2{
					Title:    title,
					SubTitle: subtitle,
				},
				LoanHeader:          lh,
				CentreIcon:          visElem,
				GetNextActionInSync: newParams.GetNextActionInSync,
				BackgroundColour:    &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"}},
			},
		},
	}
}
