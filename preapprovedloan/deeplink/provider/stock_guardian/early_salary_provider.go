// nolint:funlen,gocritic
package stock_guardian

import (
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
)

type StockGuardianProviderEarlySalary struct {
	*Provider
}

var _ provider.IDeeplinkProvider = &StockGuardianProviderEarlySalary{}

func NewStockGuardianProviderEarlySalary(SgProvider *Provider) *StockGuardianProviderEarlySalary {
	return &StockGuardianProviderEarlySalary{
		Provider: SgProvider,
	}
}

func (sg *StockGuardianProviderEarlySalary) GetLoanHeader() *palPbFeEnums.LoanHeader {
	return &palPbFeEnums.LoanHeader{
		LoanProgram: palPbFeEnums.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2,
		Vendor:      palPbFeEnums.Vendor_STOCK_GUARDIAN_LSP,
	}
}
