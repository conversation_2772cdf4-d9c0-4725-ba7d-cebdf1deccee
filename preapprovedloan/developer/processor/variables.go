package processor

import (
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
)

// contains the list of vendors for Preapprovedloan
var (
	vendors = []string{
		preApprovedLoanPb.Vendor_FEDERAL.String(),
		preApprovedLoanPb.Vendor_LIQUILOANS.String(),
		preApprovedLoanPb.Vendor_IDFC.String(),
		preApprovedLoanPb.Vendor_FIFTYFIN.String(),
		preApprovedLoanPb.Vendor_MONEYVIEW.String(),
		preApprovedLoanPb.Vendor_EPIFI_TECH.String(),
		preApprovedLoanPb.Vendor_ABFL.String(),
		preApprovedLoanPb.Vendor_STOCK_GUARDIAN_LSP.String(),
		preApprovedLoanPb.Vendor_LENDEN.String(),
	}

	loanPrograms = []string{
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_FLDG.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_FED_REAL_TIME.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_LAMF.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_STPL.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION.String(),
		preApprovedLoanPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB.String(),
	}

	lmsPartners = []string{
		enums.LmsPartner_LMS_PARTNER_FINFLUX.String(),
	}
	loanRequestTypes = []string{
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY.String(),
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION.String(),
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE.String(),
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY.String(),
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_NEW_PORTFOLIO_FETCH.String(),
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_REFRESH_PORTFOLIO.String(),
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_UPDATE_USER.String(),
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_SETUP_SI.String(),
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_MUTUAL_FUND_NFT.String(),
		preApprovedLoanPb.LoanRequestType_LOAN_REQUEST_TYPE_UNSPECIFIED.String(),
	}
	programVersions = []string{
		enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1.String(),
		enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V2.String(),
	}
)
