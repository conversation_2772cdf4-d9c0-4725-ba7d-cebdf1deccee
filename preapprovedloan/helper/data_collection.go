package helper

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao"
)

var CDCEligibleLenderToPrograms = map[palPb.Vendor][]palPb.LoanProgram{
	palPb.Vendor_LENDEN: {
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
	},
	palPb.Vendor_STOCK_GUARDIAN_LSP: {
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
	},
	palPb.Vendor_ABFL: {
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
	},
	palPb.Vendor_FEDERAL: {
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB,
	},
	palPb.Vendor_MONEYVIEW: {
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
	},
}

var CDCEligibleLenderToProgramsForETB = map[palPb.Vendor][]palPb.LoanProgram{
	palPb.Vendor_LENDEN: {
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
	},
	palPb.Vendor_STOCK_GUARDIAN_LSP: {
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2,
	},
}

const (
	LoecExpiryDuration    = 30 * 24 * time.Hour
	LoecExpiryDurationETB = 90 * 24 * time.Hour
)

func CheckCDCEligibility(vendor palPb.Vendor, program palPb.LoanProgram, isFiSAHolder bool) bool {
	if isFiSAHolder {
		programs, ok := CDCEligibleLenderToProgramsForETB[vendor]
		if !ok {
			return false
		}
		for _, p := range programs {
			if p == program {
				return true
			}
		}
	}
	programs, ok := CDCEligibleLenderToPrograms[vendor]
	if !ok {
		return false
	}
	for _, p := range programs {
		if p == program {
			return true
		}
	}
	return false
}

func getLOECS(ctx context.Context, loecDao dao.LoanOfferEligibilityCriteriaDao, actorId string, eligibleVendorsAndLoanPrograms map[palPb.Vendor][]palPb.LoanProgram, isFiSAHolder bool) ([]*palPb.LoanOfferEligibilityCriteria, error) {
	var activeLoecs []*palPb.LoanOfferEligibilityCriteria
	for vendor, programs := range eligibleVendorsAndLoanPrograms {
		// reset ownership to fetch loec from all vendor DBs
		newCtx := epificontext.WithOwnership(ctx, GetPalOwnership(vendor))
		expiryDuration := LoecExpiryDuration
		if isFiSAHolder {
			expiryDuration = LoecExpiryDurationETB
		}
		loecs, err := loecDao.GetActiveLoecsByActorIdLoanProgramsAndStatuses(newCtx, actorId, programs, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, expiryDuration, true)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				continue
			}
			return nil, errors.Wrap(err, fmt.Sprintf("error in fetching loecs for actor from %v DB", vendor.String()))
		}
		if isFiSAHolder {
			for _, loec := range loecs {
				switch loec.GetVendor() {
				case palPb.Vendor_STOCK_GUARDIAN_LSP:
					// loec filtering logic for ETB refer: preapprovedloan/recommendation_engine/eligibility_evaluator/providers/stock_guardian/real_time_subv.go:72
					if loec.GetStatus() != palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED ||
						loec.GetSubStatus() == palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED {
						logger.Info(ctx, "skipping LOEC", zap.String(logger.VENDOR, loec.GetVendor().String()), zap.String("loec_id", loec.GetId()))
						continue
					}
				case palPb.Vendor_LENDEN:
					if loec.GetStatus() != palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED {
						logger.Info(ctx, "skipping LOEC", zap.String(logger.VENDOR, loec.GetVendor().String()), zap.String("loec_id", loec.GetId()))
						continue
					}
				default:
					logger.Info(ctx, "no LOEC going to be skipped", zap.String(logger.VENDOR, loec.GetVendor().String()), zap.String("loec_id", loec.GetId()))
				}
				activeLoecs = append(activeLoecs, loec)
			}
		} else {
			activeLoecs = append(activeLoecs, loecs...)
		}
	}
	if len(activeLoecs) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return activeLoecs, nil
}

func GetCDCEligibleLoecs(ctx context.Context, loecDao dao.LoanOfferEligibilityCriteriaDao, actorId string, isFiSAHolder bool) ([]*palPb.LoanOfferEligibilityCriteria, error) {
	if isFiSAHolder {
		return getLOECS(ctx, loecDao, actorId, CDCEligibleLenderToProgramsForETB, isFiSAHolder)
	}
	return getLOECS(ctx, loecDao, actorId, CDCEligibleLenderToPrograms, isFiSAHolder)
}
