package helper

import (
	"errors"
	"fmt"

	"github.com/samber/lo"

	palBePb "github.com/epifi/gamma/api/preapprovedloan"
)

var (
	nonTerminalLseStatusArr = []palBePb.LoanStepExecutionStatus{
		palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION,
		palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
		palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
		palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED,
	}
)

func GetLseNonTerminalStatuses() []palBePb.LoanStepExecutionStatus {
	// clone nonTerminalLseStatusArr array and return
	return lo.Map(nonTerminalLseStatusArr, func(item palBePb.LoanStepExecutionStatus, _ int) palBePb.LoanStepExecutionStatus {
		return item
	})
}

func GetAppropriateAddDetailsStepName(lh *palBePb.LoanHeader, setAddressStep bool) (palBePb.LoanStepExecutionStepName, error) {
	stepName := palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS
	if !setAddressStep {
		stepName = palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT
	}
	switch lh.GetVendor() {
	case palBePb.Vendor_FEDERAL:
		if lh.GetLoanProgram() == palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB {
			return palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS, nil
		}
	case palBePb.Vendor_ABFL:
		return stepName, nil
	case palBePb.Vendor_LIQUILOANS:
		switch lh.GetLoanProgram() {
		case palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION, palBePb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
			return stepName, nil
		default:
			return palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS, nil
		}
	case palBePb.Vendor_EPIFI_TECH:
		return stepName, nil
	case palBePb.Vendor_STOCK_GUARDIAN_LSP:
		switch lh.GetLoanProgram() {
		case palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION, palBePb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
			return stepName, nil
		default:
			return palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED, errors.New(fmt.Sprintf("unimplemented step name for this loan program: %s", lh.GetLoanProgram().String()))
		}
	case palBePb.Vendor_MONEYVIEW:
		return stepName, nil
	default:
		// nothing to do here, move forward and return from function
	}
	return palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS, nil
}
