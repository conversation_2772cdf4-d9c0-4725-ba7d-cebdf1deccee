package helper

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
)

var PalVendorToVgVendor = map[palPb.Vendor]commonvgpb.Vendor{
	palPb.Vendor_VENDOR_UNSPECIFIED: commonvgpb.Vendor_VENDOR_UNSPECIFIED,
	palPb.Vendor_FEDERAL:            commonvgpb.Vendor_FEDERAL_BANK,
	palPb.Vendor_LIQUILOANS:         commonvgpb.Vendor_LIQUILOANS,
	palPb.Vendor_IDFC:               commonvgpb.Vendor_IDFC,
	palPb.Vendor_MONEYVIEW:          commonvgpb.Vendor_MONEYVIEW,
	palPb.Vendor_ABFL:               commonvgpb.Vendor_ABFL,
	palPb.Vendor_STOCK_GUARDIAN_LSP: commonvgpb.Vendor_STOCK_GUARDIAN_LSP,
	palPb.Vendor_LENDEN:             commonvgpb.Vendor_LENDEN,
}

func GetLaLoanTypeByLoanProgram(lp palPb.LoanProgram) palPb.LoanType {
	switch lp {
	case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		palPb.LoanProgram_LOAN_PROGRAM_FLDG,
		palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL,
		palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND,
		palPb.LoanProgram_LOAN_PROGRAM_LAMF,
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB,
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		palPb.LoanProgram_LOAN_PROGRAM_STPL,
		palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION,
		palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL,
		palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION,
		palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2,
		palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL,
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
		return palPb.LoanType_LOAN_TYPE_PERSONAL
	case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return palPb.LoanType_LOAN_TYPE_EARLY_SALARY
	default:
		return palPb.LoanType_LOAN_TYPE_UNSPECIFIED
	}
}

// this is only for LL for now, will need to fix this or create new if multiple programs introduced for other vendors
func (r *RpcHelper) ConvertToVGLoanProgram(lp palPb.LoanProgram) llVgPb.LoanProgram {
	switch lp {
	case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN, palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
		return llVgPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN
	case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return llVgPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY
	case palPb.LoanProgram_LOAN_PROGRAM_FLDG:
		return llVgPb.LoanProgram_LOAN_PROGRAM_FLDG
	case palPb.LoanProgram_LOAN_PROGRAM_STPL:
		return llVgPb.LoanProgram_LOAN_PROGRAM_STPL
	case palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND, palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		return llVgPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND
	case palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
		return llVgPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION
	case palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		return llVgPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL
	case palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
		return llVgPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION
	case palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		return llVgPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL
	default:
		return llVgPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED
	}
}

var DetailsTypeToLoanStepName = map[preapprovedloans.DetailsType]palPb.LoanStepExecutionStepName{
	preapprovedloans.DetailsType_DETAILS_TYPE_PAN:                    palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION,
	preapprovedloans.DetailsType_DETAILS_TYPE_DOB:                    palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION,
	preapprovedloans.DetailsType_DETAILS_TYPE_OCCUPATION:             palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC,
	preapprovedloans.DetailsType_DETAILS_TYPE_AADHAAR:                palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AADHAAR,
	preapprovedloans.DetailsType_DETAILS_TYPE_PHONE_NUMBER_AND_EMAIL: palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_APPLICANT_CREATION,
	preapprovedloans.DetailsType_DETAILS_TYPE_ADDITIONAL_KYC:         palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS,
	preapprovedloans.DetailsType_DETAILS_TYPE_SELFIE:                 palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_SELFIE_CAPTURE,
	preapprovedloans.DetailsType_DETAILS_TYPE_REFERENCES:             palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REFERENCES,
}

func GetLoanStepNameFromDetailsType(lh *palPb.LoanHeader, detailsType preapprovedloans.DetailsType) palPb.LoanStepExecutionStepName {
	switch detailsType {
	case preapprovedloans.DetailsType_DETAILS_TYPE_PAN:
		return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION
	case preapprovedloans.DetailsType_DETAILS_TYPE_DOB:
		return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION
	case preapprovedloans.DetailsType_DETAILS_TYPE_OCCUPATION:
		if lh.GetVendor() == palPb.Vendor_ABFL {
			return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT
		}
		return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC
	case preapprovedloans.DetailsType_DETAILS_TYPE_AADHAAR:
		return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AADHAAR
	case preapprovedloans.DetailsType_DETAILS_TYPE_PHONE_NUMBER_AND_EMAIL:
		return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_APPLICANT_CREATION
	case preapprovedloans.DetailsType_DETAILS_TYPE_ADDITIONAL_KYC:
		return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS
	case preapprovedloans.DetailsType_DETAILS_TYPE_SELFIE:
		return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_SELFIE_CAPTURE
	case preapprovedloans.DetailsType_DETAILS_TYPE_REFERENCES:
		return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REFERENCES
	}
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UNSPECIFIED
}

var lmsPartnerToVGVendor = map[enums.LmsPartner]commonvgpb.Vendor{
	enums.LmsPartner_LMS_PARTNER_FINFLUX: commonvgpb.Vendor_FINFLUX,
}

func ConvertLmsPartnerToVgVendor(lmsPartner enums.LmsPartner) commonvgpb.Vendor {
	return lmsPartnerToVGVendor[lmsPartner]
}
