package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"strings"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/epificontext"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
)

func GetPalVendor(ownership commontypes.Ownership) palPb.Vendor {
	switch ownership {
	case commontypes.Ownership_FEDERAL_BANK:
		return palPb.Vendor_FEDERAL
	case commontypes.Ownership_LIQUILOANS_PL:
		return palPb.Vendor_LIQUILOANS
	case commontypes.Ownership_IDFC_PL:
		return palPb.Vendor_IDFC
	case commontypes.Ownership_FIFTYFIN_LAMF:
		return palPb.Vendor_FIFTYFIN
	case commontypes.Ownership_LOANS_ABFL:
		return palPb.Vendor_ABFL
	case commontypes.Ownership_EPIFI_TECH_V2:
		return palPb.Vendor_EPIFI_TECH
	case commontypes.Ownership_MONEYVIEW_PL:
		return palPb.Vendor_MONEYVIEW
	case commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP:
		return palPb.Vendor_STOCK_GUARDIAN_LSP
	case commontypes.Ownership_LOANS_LENDEN:
		return palPb.Vendor_LENDEN

	default:
		return palPb.Vendor_VENDOR_UNSPECIFIED
	}
}

// GetLoanPrograms only used by multi dao
func GetLoanPrograms(vendor palPb.Vendor, loanProgram palPb.LoanProgram) []palPb.LoanProgram {
	if loanProgram == palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED {
		loanProgram = palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN
	}
	lps := []palPb.LoanProgram{loanProgram}
	if vendor == palPb.Vendor_LIQUILOANS && loanProgram == palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN {
		lps = append(lps, palPb.LoanProgram_LOAN_PROGRAM_FLDG)
		lps = append(lps, palPb.LoanProgram_LOAN_PROGRAM_STPL)
	}
	return lps
}

func GetLoanProgramsForLoanType(loanType palPb.LoanType) []palPb.LoanProgram {
	switch loanType {
	case palPb.LoanType_LOAN_TYPE_PERSONAL:
		return []palPb.LoanProgram{
			palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			palPb.LoanProgram_LOAN_PROGRAM_FLDG,
			palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL,
			palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND,
			palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
			palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2,
			palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB,
			palPb.LoanProgram_LOAN_PROGRAM_STPL,
			palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION,
			palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL,
			palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL,
			palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION,
			palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
			palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB,
		}
	case palPb.LoanType_LOAN_TYPE_EARLY_SALARY:
		return []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY}
	case palPb.LoanType_LOAN_TYPE_SECURED_LOAN:
		return []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_LAMF}
	default:
		return []palPb.LoanProgram{palPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED}
	}
}

func GetLoanTypeFromLoanProgram(program palPb.LoanProgram) palPb.LoanType {
	switch program {
	case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		palPb.LoanProgram_LOAN_PROGRAM_FLDG,
		palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL,
		palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND,
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2,
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB,
		palPb.LoanProgram_LOAN_PROGRAM_STPL,
		palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION,
		palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL,
		palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
		palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
		return palPb.LoanType_LOAN_TYPE_PERSONAL
	case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
		return palPb.LoanType_LOAN_TYPE_EARLY_SALARY
	case palPb.LoanProgram_LOAN_PROGRAM_LAMF:
		return palPb.LoanType_LOAN_TYPE_SECURED_LOAN
	default:
		return palPb.LoanType_LOAN_TYPE_UNSPECIFIED
	}
}

func GetPalOwnerships() []commontypes.Ownership {
	var ownerships []commontypes.Ownership

	ownerships = append(ownerships, commontypes.Ownership_FEDERAL_BANK, commontypes.Ownership_LIQUILOANS_PL, commontypes.Ownership_IDFC_PL, commontypes.Ownership_FIFTYFIN_LAMF, commontypes.Ownership_LOANS_ABFL, commontypes.Ownership_MONEYVIEW_PL, commontypes.Ownership_EPIFI_TECH_V2, commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP, commontypes.Ownership_LOANS_LENDEN)
	return ownerships
}

func GetPalOwnership(vendor palPb.Vendor) commontypes.Ownership {
	switch vendor {
	case palPb.Vendor_FEDERAL:
		return commontypes.Ownership_FEDERAL_BANK
	case palPb.Vendor_LIQUILOANS:
		return commontypes.Ownership_LIQUILOANS_PL
	case palPb.Vendor_IDFC:
		return commontypes.Ownership_IDFC_PL
	case palPb.Vendor_FIFTYFIN:
		return commontypes.Ownership_FIFTYFIN_LAMF
	case palPb.Vendor_MONEYVIEW:
		return commontypes.Ownership_MONEYVIEW_PL
	case palPb.Vendor_ABFL:
		return commontypes.Ownership_LOANS_ABFL
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		return commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP
	case palPb.Vendor_EPIFI_TECH:
		return commontypes.Ownership_EPIFI_TECH_V2
	case palPb.Vendor_LENDEN:
		return commontypes.Ownership_LOANS_LENDEN
	default:
		return commontypes.Ownership_EPIFI_TECH
	}
}

func GetPalOwnershipsForLoanPrograms(loanPrograms []palPb.LoanProgram) []commontypes.Ownership {
	var ownerships []commontypes.Ownership
	for _, loanProgram := range loanPrograms {
		switch loanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
			ownerships = append(ownerships, commontypes.Ownership_LIQUILOANS_PL)
		case palPb.LoanProgram_LOAN_PROGRAM_FLDG:
			ownerships = append(ownerships, commontypes.Ownership_LIQUILOANS_PL)
		case palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
			ownerships = append(ownerships, commontypes.Ownership_LIQUILOANS_PL)
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			ownerships = append(ownerships, commontypes.Ownership_LIQUILOANS_PL, commontypes.Ownership_FEDERAL_BANK, commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP, commontypes.Ownership_LOANS_LENDEN, commontypes.Ownership_LOANS_ABFL, commontypes.Ownership_MONEYVIEW_PL)
		case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
			ownerships = append(ownerships, commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP)
		case palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
			ownerships = append(ownerships, commontypes.Ownership_LIQUILOANS_PL)
		case palPb.LoanProgram_LOAN_PROGRAM_FED_REAL_TIME:
			ownerships = append(ownerships, commontypes.Ownership_FEDERAL_BANK)
		case palPb.LoanProgram_LOAN_PROGRAM_LAMF:
			ownerships = append(ownerships, commontypes.Ownership_FIFTYFIN_LAMF)
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			ownerships = append(ownerships, GetPalOwnerships()...)
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB:
			ownerships = append(ownerships, commontypes.Ownership_EPIFI_TECH_V2)
		case palPb.LoanProgram_LOAN_PROGRAM_STPL:
			ownerships = append(ownerships, commontypes.Ownership_LIQUILOANS_PL)
		case palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
			ownerships = append(ownerships, commontypes.Ownership_LIQUILOANS_PL)
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
			ownerships = append(ownerships, commontypes.Ownership_FEDERAL_BANK)
		default:
			ownerships = append(ownerships, GetPalOwnerships()...)
		}
	}
	return lo.Uniq(ownerships)
}

func GetTxnExecutorByOwnership(ctx context.Context, providers *storage.DBResourceProvider[storage.IdempotentTxnExecutor]) (storage.IdempotentTxnExecutor, error) {
	ow := epificontext.OwnershipFromContext[context.Context](ctx)
	// default to federal bank
	if ow == commontypes.Ownership_EPIFI_TECH {
		ow = commontypes.Ownership_FEDERAL_BANK
	}
	txnExec, err := providers.GetResourceForOwnership(ow)
	if err != nil {
		return nil, err
	}
	return txnExec, nil
}

func GetLseFlowByLrType(lrType palPb.LoanRequestType) palPb.LoanStepExecutionFlow {
	switch lrType {
	case palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY:
		return palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY
	case palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION:
		return palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION
	default:
		return palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION
	}
}

func GetRecommendationEngineOffersKey(vendor palPb.Vendor, loanProgram palPb.LoanProgram) string {
	return strings.Join([]string{
		vendor.String(),
		loanProgram.String(),
	}, ":")
}
