// nolint:goimports
package preapprovedloan_test

import (
	"context"
	"flag"
	"os"
	"testing"

	"github.com/golang/mock/gomock"
	datePb "google.golang.org/genproto/googleapis/type/date"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	mock_cache "github.com/epifi/be-common/pkg/cache/mocks"
	mockTime "github.com/epifi/be-common/pkg/datetime/mocks"
	eventMock "github.com/epifi/be-common/pkg/events/mocks"
	mockIdGen "github.com/epifi/be-common/pkg/idgen/mocks"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"
	storageV2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	livenessMocks "github.com/epifi/gamma/api/auth/liveness/mocks"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	mockCreditLimitEstimator "github.com/epifi/gamma/api/credit_limit_estimator/mocks"
	mockCreditReportV2Pb "github.com/epifi/gamma/api/creditreportv2/mocks"
	esignMocks "github.com/epifi/gamma/api/docs/esign/mocks"
	mockMfExternal "github.com/epifi/gamma/api/investment/mutualfund/external/mocks"
	vkycMocks "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentMocksPb "github.com/epifi/gamma/api/order/payment/mocks"
	payMocksPb "github.com/epifi/gamma/api/pay/mocks"
	piMock "github.com/epifi/gamma/api/paymentinstrument/mocks"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/secured_loans/mocks"
	profileMocks "github.com/epifi/gamma/api/risk/profile/mocks"
	salaryProgramMocks "github.com/epifi/gamma/api/salaryprogram/mocks"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	mockCreditReportPb "github.com/epifi/gamma/api/user/credit_report/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	llVgMocks "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans/mocks"
	palVgMocks "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/mocks"
	vgPalMocks "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/mocks"
	releaseEvaluatorMocks "github.com/epifi/gamma/pkg/feature/release/mocks"
	pQueueMocks "github.com/epifi/gamma/pkg/persistentqueue/mocks"
	"github.com/epifi/gamma/preapprovedloan"
	defaultValueCalculator "github.com/epifi/gamma/preapprovedloan/calculator/defaultvalue"
	calculatorProvidersWrapper "github.com/epifi/gamma/preapprovedloan/calculator/providers/wrapper"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/config"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	daoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	dataExistenceManagerMocks "github.com/epifi/gamma/preapprovedloan/data_existence_manager/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	fiftyfinDeeplink "github.com/epifi/gamma/preapprovedloan/deeplink/provider/fiftyfin"
	llDeeplink "github.com/epifi/gamma/preapprovedloan/deeplink/provider/liquiloans"
	downtimeMocks "github.com/epifi/gamma/preapprovedloan/downtime/mocks"
	"github.com/epifi/gamma/preapprovedloan/helper"
	landingProviderMocks "github.com/epifi/gamma/preapprovedloan/landing_provider/mocks"
	loanDataProviderMock "github.com/epifi/gamma/preapprovedloan/loan_data_provider/mocks"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/loanplans"
	mockMultiDBProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider/mocks"
	prepayMocks "github.com/epifi/gamma/preapprovedloan/prepay/mocks"
	dcProviderMocks "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/data_collector/mocks"
	palReconciliationTypesMocks "github.com/epifi/gamma/preapprovedloan/reconciliation/types/mocks"
	"github.com/epifi/gamma/preapprovedloan/test"
)

var (
	conf    *config.Config
	dynConf *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	conf, dynConf, teardown = test.InitTestServer()
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type mockedDependencies struct {
	loanRequestDao                 *daoMocks.MockLoanRequestsDao
	loanOfferDao                   *daoMocks.MockLoanOffersDao
	loanStepExecutionDao           *daoMocks.MockLoanStepExecutionsDao
	loanPaymentRequestsDao         *daoMocks.MockLoanPaymentRequestsDao
	loanAccountDao                 *daoMocks.MockLoanAccountsDao
	loanInstallmentInfo            *daoMocks.MockLoanInstallmentInfoDao
	loanActivityDao                *daoMocks.MockLoanActivityDao
	loanInstallmentPayoutDao       *daoMocks.MockLoanInstallmentPayoutDao
	loecDao                        *daoMocks.MockLoanOfferEligibilityCriteriaDao
	livenessClient                 *livenessMocks.MockLivenessClient
	txnExecutor                    *storageV2Mocks.MockTxnExecutor
	palVgClient                    *vgPalMocks.MockPreApprovedLoanClient
	rpcHelper                      *helper.RpcHelper
	actorClient                    *actorMocks.MockActorClient
	usersClient                    *userMocks.MockUsersClient
	eSignClient                    *esignMocks.MockESignClient
	timePkg                        *mockTime.MockTime
	vkycClient                     *vkycMocks.MockVKYCClient
	pQueue                         *pQueueMocks.MockPersistentQueue
	commsClient                    *commsMocks.MockCommsClient
	authClient                     *authMocks.MockAuthClient
	savingsClient                  *savingsMocks.MockSavingsClient
	celestialClient                *celestialMocks.MockCelestialClient
	paymentClient                  *paymentMocksPb.MockPaymentClient
	orderClient                    *orderMocks.MockOrderServiceClient
	payClient                      *payMocksPb.MockPayClient
	piClient                       *piMock.MockPiClient
	publisherClient                *mock_queue.MockPublisher
	profileClient                  *profileMocks.MockProfileClient
	multiDBProvider                *mockMultiDBProvider.MockIMultiDbProvider
	onbClient                      *onbMocks.MockOnboardingClient
	downtime                       *downtimeMocks.MockProcessor
	llVgMockClient                 *llVgMocks.MockLiquiloansClient
	mockLoanDataProvider           *loanDataProviderMock.MockIFactory
	loanDataProvider               *liquiloans.LLLoanDataProvider
	mockCache                      *mock_cache.MockCacheStorage
	loanApplicationDao             *daoMocks.MockLoanApplicantDao
	salaryClient                   *salaryProgramMocks.MockSalaryProgramClient
	creditReportClient             *mockCreditReportPb.MockCreditReportManagerClient
	creditReportV2Client           *mockCreditReportV2Pb.MockCreditReportManagerClient
	limitEstimatorClient           *mockCreditLimitEstimator.MockCreditLimitEstimatorClient
	mockPartnerLmsUserDAo          *daoMocks.MockPartnerLmsUserDao
	releaseEvaluator               *releaseEvaluatorMocks.MockIEvaluator
	prepay                         *prepayMocks.MockIFactory
	lmsDataDifferenceFinderFactory *palReconciliationTypesMocks.MockLmsDataDifferenceFinderFactory
	lmsDataDifferenceFinder        *palReconciliationTypesMocks.MockLmsDataDifferenceFinder
	uuidGenerator                  *mockIdGen.MockIUuidGenerator
	time                           *mockTime.MockTime
	mfExternalOrdersClient         *mockMfExternal.MockMFExternalOrdersClient
	dataExistenceManager           *dataExistenceManagerMocks.MockManager
	landingProvider                *landingProviderMocks.MockILandingProvider
}

// TODO: use mockgen to generate mocks for these and remove the manual implementation
type mockCalculatorProvider struct {
}

func (p *mockCalculatorProvider) GetDefaultValueCalculator(
	ctx context.Context,
	loanOffer *palPb.LoanOffer,
) calculatorTypes.DefaultValueCalculator {
	return defaultValueCalculator.NewCalculator(ctx, loanOffer)
}

func (p *mockCalculatorProvider) GetCalculator(
	ctx context.Context,
	req *calculatorTypes.Request,
) (calculatorTypes.Calculator, error) {
	return calculatorProvidersWrapper.NewProvider().GetCalculator(ctx, req)
}

func newPalServiceWithMocks(t *testing.T) (*preapprovedloan.Service, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)
	// dao mocks
	mockLoanRequestsDao := daoMocks.NewMockLoanRequestsDao(ctr)
	mockLoanOffersDao := daoMocks.NewMockLoanOffersDao(ctr)
	mockLoanAccountDao := daoMocks.NewMockLoanAccountsDao(ctr)
	mockLoanStepExecutionDao := daoMocks.NewMockLoanStepExecutionsDao(ctr)
	mockLoanPaymentRequestsDao := daoMocks.NewMockLoanPaymentRequestsDao(ctr)
	mockLoanInstallmentInfoDao := daoMocks.NewMockLoanInstallmentInfoDao(ctr)
	mockLoanInstallmentPayoutDao := daoMocks.NewMockLoanInstallmentPayoutDao(ctr)
	mockLoanActivityDao := daoMocks.NewMockLoanActivityDao(ctr)
	mockLoecDao := daoMocks.NewMockLoanOfferEligibilityCriteriaDao(ctr)
	mockLoanApplicantDao := daoMocks.NewMockLoanApplicantDao(ctr)
	mockMultiDBDataProvider := mockMultiDBProvider.NewMockIMultiDbProvider(ctr)
	mockLandingProvider := landingProviderMocks.NewMockILandingProvider(ctr)
	mockDcPriorityProviderFactoryMocks := dcProviderMocks.NewMockIDataCollectorFactory(ctr)

	limitEstimatorClient := mockCreditLimitEstimator.NewMockCreditLimitEstimatorClient(ctr)
	salaryClient := salaryProgramMocks.NewMockSalaryProgramClient(ctr)
	palVgClient := palVgMocks.NewMockPreApprovedLoanClient(ctr)
	actorClient := actorMocks.NewMockActorClient(ctr)
	userClient := userMocks.NewMockUsersClient(ctr)
	eSignClient := esignMocks.NewMockESignClient(ctr)
	timePkg := mockTime.NewMockTime(ctr)
	savingsClient := savingsMocks.NewMockSavingsClient(ctr)
	celestialClient := celestialMocks.NewMockCelestialClient(ctr)
	livenessClient := livenessMocks.NewMockLivenessClient(ctr)
	vkycClient := vkycMocks.NewMockVKYCClient(ctr)
	txnExecutor := storageV2Mocks.NewMockTxnExecutor(ctr)
	pQueue := pQueueMocks.NewMockPersistentQueue(ctr)
	commsClient := commsMocks.NewMockCommsClient(ctr)
	authClient := authMocks.NewMockAuthClient(ctr)
	payClient := payMocksPb.NewMockPayClient(ctr)
	piClient := piMock.NewMockPiClient(ctr)
	mockPublisher := mock_queue.NewMockPublisher(ctr)
	paymentClient := paymentMocksPb.NewMockPaymentClient(ctr)
	orderClient := orderMocks.NewMockOrderServiceClient(ctr)
	eventMock := eventMock.NewMockBroker(ctr)
	profileClient := profileMocks.NewMockProfileClient(ctr)
	onbClient := onbMocks.NewMockOnboardingClient(ctr)
	eventMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	downtimeClient := downtimeMocks.NewMockProcessor(ctr)
	llVgClient := llVgMocks.NewMockLiquiloansClient(ctr)
	mockLoanDataProvider := loanDataProviderMock.NewMockIFactory(ctr)
	mockCache := mock_cache.NewMockCacheStorage(ctr)
	mockSecuredLoansClient := mocks.NewMockSecuredLoansClient(ctr)
	creditReportClient := mockCreditReportPb.NewMockCreditReportManagerClient(ctr)
	creditReportV2Client := mockCreditReportV2Pb.NewMockCreditReportManagerClient(ctr)
	mockPartnerLmsUserDao := daoMocks.NewMockPartnerLmsUserDao(ctr)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctr)
	// TODO: use mockgen to generate mocks for these and remove the manual implementation
	calculatorFactory := &mockCalculatorProvider{}
	mockPrepayFactory := prepayMocks.NewMockIFactory(ctr)
	lmsDataDifferenceFinderFactory := palReconciliationTypesMocks.NewMockLmsDataDifferenceFinderFactory(ctr)
	lmsDataDifferenceFinder := palReconciliationTypesMocks.NewMockLmsDataDifferenceFinder(ctr)
	dataExistenceManager := dataExistenceManagerMocks.NewMockManager(ctr)
	mockMandateRequestDao := daoMocks.NewMockMandateRequestDao(ctr)

	rpcHelper := helper.NewRpcHelper(palVgClient, actorClient, userClient, savingsClient, celestialClient, livenessClient, nil, nil, authClient, eSignClient, timePkg, commsClient, mockLoanStepExecutionDao, conf.Notification, orderClient, payClient, piClient, nil, paymentClient, nil, mockLoanActivityDao, llVgClient, nil, profileClient, nil, nil, nil, nil, nil, nil, nil, onbClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, mockLoanRequestsDao, nil, nil)
	uuidGenerator := mockIdGen.NewMockIUuidGenerator(ctr)
	time := mockTime.NewMockTime(ctr)
	mfExternalOrdersClient := mockMfExternal.NewMockMFExternalOrdersClient(ctr)
	baseproviderProvider := baseprovider.NewProvider(nil, nil, nil, nil, nil, nil)
	fiftyfinDlProvider := fiftyfinDeeplink.NewProvider(baseproviderProvider, nil, uuidGenerator)
	llDlProvider := llDeeplink.NewProvider(baseproviderProvider)
	deeplinkProviderFactory := deeplink.NewDeeplinkProviderFactory(baseproviderProvider, llDlProvider, nil, nil, nil, nil, nil, fiftyfinDlProvider, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)

	s := preapprovedloan.NewService(mockLoanOffersDao, mockLoanAccountDao, mockLoanRequestsDao,
		mockLoanStepExecutionDao, mockLoanInstallmentInfoDao, mockLoanActivityDao, mockLoanPaymentRequestsDao,
		mockLoanInstallmentPayoutDao, mockLoecDao, mockLoanApplicantDao, mockMultiDBDataProvider, eventMock,
		rpcHelper, mockPublisher, conf, txnExecutor, nil, nil, deeplinkProviderFactory,
		nil, mockPrepayFactory, nil, nil, mockLoanDataProvider, downtimeClient, celestialClient,
		nil, dynConf, nil, nil, nil, nil,
		mockSecuredLoansClient, nil, mockReleaseEvaluator, userClient, piClient, authClient, palVgClient, salaryClient,
		nil, creditReportClient, creditReportV2Client, onbClient, nil, limitEstimatorClient, nil,
		mockPartnerLmsUserDao, nil, calculatorFactory, lmsDataDifferenceFinderFactory, uuidGenerator, time, mfExternalOrdersClient,
		dataExistenceManager, nil, mockLandingProvider, mockMandateRequestDao, nil,
		nil, mockDcPriorityProviderFactoryMocks, nil, nil,
		nil, nil, nil, nil,
		nil, nil, savingsClient, loanplans.NewProviderFactory(nil), nil, nil, nil, nil)

	llLoansProvider := liquiloans.NewLiquiloansProvider(llVgClient, nil, nil, mockCache)

	md := &mockedDependencies{
		loanRequestDao:                 mockLoanRequestsDao,
		loanOfferDao:                   mockLoanOffersDao,
		loanStepExecutionDao:           mockLoanStepExecutionDao,
		loanPaymentRequestsDao:         mockLoanPaymentRequestsDao,
		loanAccountDao:                 mockLoanAccountDao,
		loanInstallmentInfo:            mockLoanInstallmentInfoDao,
		loanActivityDao:                mockLoanActivityDao,
		loanInstallmentPayoutDao:       mockLoanInstallmentPayoutDao,
		loecDao:                        mockLoecDao,
		livenessClient:                 livenessClient,
		txnExecutor:                    txnExecutor,
		palVgClient:                    palVgClient,
		rpcHelper:                      rpcHelper,
		actorClient:                    actorClient,
		usersClient:                    userClient,
		eSignClient:                    eSignClient,
		timePkg:                        timePkg,
		savingsClient:                  savingsClient,
		vkycClient:                     vkycClient,
		pQueue:                         pQueue,
		commsClient:                    commsClient,
		authClient:                     authClient,
		celestialClient:                celestialClient,
		payClient:                      payClient,
		piClient:                       piClient,
		publisherClient:                mockPublisher,
		paymentClient:                  paymentClient,
		orderClient:                    orderClient,
		profileClient:                  profileClient,
		multiDBProvider:                mockMultiDBDataProvider,
		onbClient:                      onbClient,
		downtime:                       downtimeClient,
		llVgMockClient:                 llVgClient,
		mockLoanDataProvider:           mockLoanDataProvider,
		loanDataProvider:               llLoansProvider,
		mockCache:                      mockCache,
		loanApplicationDao:             mockLoanApplicantDao,
		salaryClient:                   salaryClient,
		creditReportClient:             creditReportClient,
		creditReportV2Client:           creditReportV2Client,
		limitEstimatorClient:           limitEstimatorClient,
		releaseEvaluator:               mockReleaseEvaluator,
		prepay:                         mockPrepayFactory,
		lmsDataDifferenceFinderFactory: lmsDataDifferenceFinderFactory,
		lmsDataDifferenceFinder:        lmsDataDifferenceFinder,
		uuidGenerator:                  uuidGenerator,
		time:                           time,
		mfExternalOrdersClient:         mfExternalOrdersClient,
		dataExistenceManager:           dataExistenceManager,
		landingProvider:                mockLandingProvider,
	}

	return s, md, func() {
		ctr.Finish()
	}
}

func getLoanOffer(offerId string, actorId string, vendorOfferId string, vendor palPb.Vendor, loecId string, loanProgram palPb.LoanProgram) *palPb.LoanOffer {
	return &palPb.LoanOffer{
		Id:               offerId,
		ActorId:          actorId,
		VendorOfferId:    vendorOfferId,
		Vendor:           vendor,
		OfferConstraints: nil,
		ProcessingInfo: &palPb.OfferProcessingInfo{
			ApplicationId: "dummy-application-id",
		},
		ValidSince:                     nil,
		ValidTill:                      nil,
		LoanOfferEligibilityCriteriaId: loecId,
		LoanProgram:                    loanProgram,
	}
}

func getUserRes() *userPb.GetUserResponse {
	return &userPb.GetUserResponse{
		User: &userPb.User{
			Profile: &userPb.Profile{
				GivenName: &commontypes.Name{
					FirstName: "first",
				},
				GivenGender: types.Gender_MALE,
				PAN:         "**********",
				PhoneNumber: &commontypes.PhoneNumber{
					CountryCode:    91,
					NationalNumber: 9999999999,
				},
				DateOfBirth: &datePb.Date{
					Year:  2000,
					Month: 1,
					Day:   1,
				},
				Email: "<EMAIL>",
				KycName: &commontypes.Name{
					FirstName: "first",
				},
			},
		},
		Status: rpc.StatusOk(),
	}
}
