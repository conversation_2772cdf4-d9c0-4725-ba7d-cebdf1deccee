package lenden

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	typesPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/account"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	rpEnachEnumsPb "github.com/epifi/gamma/api/recurringpayment/enach/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	lendenVgClient "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

type LendenMandateViewProvider struct {
	rpcHelper            *helper.RpcHelper
	deeplinkFactory      deeplink.IDeeplinkProviderFactory
	loanStepExecutionDao dao.LoanStepExecutionsDao
	loanApplicantDao     dao.LoanApplicantDao
	loanRequestsDao      dao.LoanRequestsDao
	savingsClient        savingsPb.SavingsClient
	lendenClient         lendenVgClient.LendenClient
	userClient           userPb.UsersClient
	loanOfferDao         dao.LoanOffersDao
}

func NewLendenMandateViewProvider(
	rpcHelper *helper.RpcHelper,
	deeplinkFactory deeplink.IDeeplinkProviderFactory,
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	loanApplicantDao dao.LoanApplicantDao,
	loanRequestsDao dao.LoanRequestsDao,
	savingsClient savingsPb.SavingsClient,
	lendenClient lendenVgClient.LendenClient,
	userClient userPb.UsersClient,
	loanOfferDao dao.LoanOffersDao,
) *LendenMandateViewProvider {
	return &LendenMandateViewProvider{
		rpcHelper:            rpcHelper,
		deeplinkFactory:      deeplinkFactory,
		loanStepExecutionDao: loanStepExecutionDao,
		loanApplicantDao:     loanApplicantDao,
		loanRequestsDao:      loanRequestsDao,
		savingsClient:        savingsClient,
		lendenClient:         lendenClient,
		userClient:           userClient,
		loanOfferDao:         loanOfferDao,
	}
}

func (l *LendenMandateViewProvider) ValidateLoanStep(ctx context.Context, _ *palPb.LoanHeader, lse *palPb.LoanStepExecution, accDetails *palPb.MandateData_BankingDetails_AccountDetails) (error, string) {
	// validate mandate link expiry to check if some mandate is already in progress
	const userFacingError string = "Something went wrong"
	applicant, err := l.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
	if err != nil {
		return errors.Wrap(err, "error getting actor id"), userFacingError
	}

	lr, lrErr := l.loanRequestsDao.GetById(ctx, lse.GetRefId())
	if lrErr != nil {
		return errors.Wrap(lrErr, "error getting loan_request"), userFacingError
	}

	userIpAddress, ipAddressErr := l.rpcHelper.FetchIpAddress(ctx, lse.GetActorId())
	if ipAddressErr != nil {
		return errors.Wrap(ipAddressErr, "error getting ip adress of the user"), userFacingError
	}

	userDP, udpErr := l.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId: lse.GetActorId(),
		PropertyTypes: []typesv2.DeviceProperty{
			typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID,
		},
	})
	if te := epifigrpc.RPCError(userDP, udpErr); te != nil {
		return errors.Wrapf(te, "error getting device id"), userFacingError
	}
	deviceId := userDP.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID).GetDeviceId()

	loanOffer, err := l.loanOfferDao.GetById(ctx, lr.GetOfferId())
	if err != nil {
		return errors.Wrap(err, "error getting loan offer"), userFacingError
	}
	offerBankDetails := loanOffer.GetOfferConstraints().GetLendenConstraintInfo().GetBankDetails()
	accountNumber := accDetails.GetAccountNumber()
	// TODO(Brijesh): Find out how to get account type and where to get it from
	accountType := account.AccountType_SAVINGS
	bankAccountDetailsForLDC := &typesPb.BankAccountDetails{
		AccountName:   accDetails.GetAccountHolderName(),
		AccountNumber: accountNumber,
		BankName:      accDetails.GetBankName(),
		Ifsc:          accDetails.GetIfscCode(),
		AccountType:   accountType,
	}
	if offerBankDetails != nil {
		logger.Info(ctx, "using details from Lenden offer constraints for sending bank details to LDC", zap.String(logger.OFFER_ID, loanOffer.GetId()))
		bankAccountDetailsForLDC = &typesPb.BankAccountDetails{
			AccountName:   offerBankDetails.GetAccountHolderName(),
			AccountNumber: accountNumber,
			BankName:      offerBankDetails.GetBankName(),
			Ifsc:          offerBankDetails.GetIfsc(),
			AccountType:   accountType,
		}
	}
	addBankDetailsRequest := lendenVgClient.AddBankDetailsRequest{
		Header:             &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LENDEN},
		LoanId:             lr.GetVendorRequestId(),
		UserId:             applicant.GetVendorApplicantId(),
		BankAccountDetails: bankAccountDetailsForLDC,
	}
	addBankDetailsResponse, addBankDetailsErr := l.lendenClient.AddBankDetails(ctx, &addBankDetailsRequest)
	if rpcErr := epifigrpc.RPCError(addBankDetailsResponse, addBankDetailsErr); rpcErr != nil {
		logger.Error(ctx, "error in lenden add bank details api", zap.Error(rpcErr))
		if addBankDetailsResponse.GetStatus().GetCode() == uint32(lendenVgClient.AddBankDetailsResponse_BANK_ACCOUNT_NOT_ACTIVE) {
			return errors.Wrap(epifierrors.ErrInvalidArgument, "error adding bank details"), "Bank account is not active"
		}
		if addBankDetailsResponse.GetStatus().GetCode() == uint32(lendenVgClient.AddBankDetailsResponse_NAME_MISMATCH) {
			return errors.Wrap(epifierrors.ErrInvalidArgument, "error adding bank details"), "Name mismatch when adding bank details"
		}
		if addBankDetailsResponse.GetStatus().GetCode() == uint32(lendenVgClient.AddBankDetailsResponse_BANK_CONNECTION_ERROR) {
			return errors.Wrap(epifierrors.ErrInvalidArgument, "error adding bank details"), "Error connecting to bank"
		}
		if addBankDetailsResponse.GetStatus().GetCode() == uint32(lendenVgClient.AddBankDetailsResponse_INVALID_BANK_DETAILS) {
			return errors.Wrap(epifierrors.ErrInvalidArgument, "error adding bank details"), "Invalid bank details"
		}
		return errors.Wrap(rpcErr, "error in lenden add bank details api"), "Failed to add bank details"
	}

	// call LDC mandate api to get mandate link
	initMandateRequest := lendenVgClient.InitMandateRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LENDEN},
		LoanId: lr.GetVendorRequestId(),
		UserId: applicant.GetVendorApplicantId(),
		ConsentCodeList: []lendenVgClient.ConsentType{
			lendenVgClient.ConsentType_CONSENT_TYPE_MANDATE,
		},
		MandateType: lendenVgClient.MandateType_MANDATE_TYPE_NACH_MANDATE,
		UserIp:      userIpAddress,
		DeviceId:    deviceId,
		// TODO(Brijesh): Check if we should use the current time or the creation time of the consent from consent service?
		ConsentTime: timestamppb.Now(),
	}

	initMandateResponse, initMandateErr := l.lendenClient.InitMandate(ctx, &initMandateRequest)
	if rpcErr := epifigrpc.RPCError(initMandateResponse, initMandateErr); rpcErr != nil {
		logger.Error(ctx, "error initiating mandate", zap.Error(rpcErr))
		if initMandateResponse.GetStatus().GetCode() == uint32(lendenVgClient.InitMandateResponse_MANDATE_VERIFICATION_FAILED) {
			return errors.Wrap(epifierrors.ErrInvalidArgument, "error initiating mandate"), "Mandate verification failed"
		}
		if initMandateResponse.GetStatus().GetCode() == uint32(lendenVgClient.InitMandateResponse_OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND) {
			return errors.Wrap(epifierrors.ErrInvalidArgument, "error initiating mandate"), "Account details not found"
		}
		if initMandateResponse.GetStatus().GetCode() == uint32(lendenVgClient.InitMandateResponse_BANK_ACCOUNT_NOT_VERIFIED) {
			return errors.Wrap(epifierrors.ErrInvalidArgument, "error initiating mandate"), "Bank account not verified"
		}
		// TODO(Brijesh): Revisit handling of ENACH already completed case, user should be redirected to next step of application.
		if initMandateResponse.GetStatus().GetCode() == uint32(lendenVgClient.InitMandateResponse_ENACH_ALREADY_COMPLETED) {
			return errors.Wrap(epifierrors.ErrInvalidArgument, "error initiating mandate"), "ENACH already completed"
		}
		return errors.Wrap(rpcErr, "error initiating mandate"), "Failed to initiate mandate"
	}

	// Update the mandate link in LSE
	lse.Details = &palPb.LoanStepExecutionDetails{
		Details: &palPb.LoanStepExecutionDetails_MandateData{
			MandateData: &palPb.MandateData{
				Url:               initMandateResponse.GetRedirectionUrl(),
				MerchantTxnId:     initMandateResponse.GetTrackingId(),
				MandateLinkExpiry: initMandateResponse.GetMandateUrlValidity(),
				BankingDetails:    lse.GetDetails().GetMandateData().GetBankingDetails(),
			},
		},
	}
	lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_LINK_FETCHED
	if lseUpdateErr := l.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	}); lseUpdateErr != nil {
		return lseUpdateErr, "Something went wrong"
	}

	return nil, ""
}

func (l *LendenMandateViewProvider) GetLoansMandateSetupScreen(ctx context.Context, loanHeader *palPb.LoanHeader, lse *palPb.LoanStepExecution,
	_ *palPb.MandateData_BankingDetails_AccountDetails, _ rpEnachEnumsPb.EnachRegistrationAuthMode) (*deeplinkPb.Deeplink, error) {
	mandateUrl := lse.GetDetails().GetMandateData().GetUrl()
	if mandateUrl == "" {
		return nil, errors.New("mandate URL is empty")
	}
	deeplinkProvider := l.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{
		Vendor:      loanHeader.GetVendor(),
		LoanProgram: loanHeader.GetLoanProgram(),
	})
	mandateWebViewDl, dlErr := deeplinkProvider.GetLoansMandateSetupScreen(ctx, deeplinkProvider.GetLoanHeader(), lse.GetActorId(), lse.GetRefId(), lse.GetId(), &provider.LoansMandateSetupScreenParams{
		WebViewParams: &provider.WebViewParams{EntryUrl: mandateUrl},
	})
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "error while generating mandate setup deeplink")
	}
	return mandateWebViewDl, nil
}

func (l *LendenMandateViewProvider) PostDlGenerationProcessing(ctx context.Context, _ *palPb.LoanHeader, lseId string, accDetails *palPb.MandateData_BankingDetails_AccountDetails) error {
	// fetch lse object from db to update final acc used
	// fetching this record from db to avoid any dirty updates
	lse, err := l.loanStepExecutionDao.GetById(ctx, lseId)
	if err != nil {
		return errors.Wrap(err, "error while fetching lse object by id from db")
	}
	lse.GetDetails().GetMandateData().GetBankingDetails().FinalAccDetailsUsed = accDetails
	updateErr := l.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if updateErr != nil {
		return errors.Wrap(updateErr, "error while updating final acc details used field in mandate banking details")
	}
	return nil
}
