package decision_engine

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/epifierrors"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
)

var SimpleOffersDecisionEngineWireSet = wire.NewSet(NewSimpleOffersDecisionEngine, wire.Bind(new(IOffersDecisionEngine), new(*SimpleOffersDecisionEngine)))

// SimpleOffersDecisionEngine is a decision engine that compares the available offers and returns the
// Federal Bank offer if it exists, else it returns any of the remaining offers.
type SimpleOffersDecisionEngine struct {
	commonFlagsConfig *commonGenConf.Flags
}

var _ IOffersDecisionEngine = &SimpleOffersDecisionEngine{}

// NewSimpleOffersDecisionEngine creates a new instance of SimpleOffersDecisionEngine
func NewSimpleOffersDecisionEngine(commonFlagsConfig *commonGenConf.Flags) *SimpleOffersDecisionEngine {
	return &SimpleOffersDecisionEngine{
		commonFlagsConfig: commonFlagsConfig,
	}
}

func getMapKeyFromOwnerAndLoanProgram(ownerStr string, lp palPb.LoanProgram) string {
	return ownerStr + "_" + lp.String()
}

func (d *SimpleOffersDecisionEngine) FilterLoanOffers(ctx context.Context, req *OffersDecisionEngineRequest) (*OffersDecisionEngineResponse, error) {
	if req.GetDataMap() == nil {
		return nil, epifierrors.ErrRecordNotFound
	}

	dataMp := make(map[string]*palPb.LoanOffer, 0)

	for owner, data := range req.GetDataMap().Items() {
		if data != nil {
			loArr := data.([]*palPb.LoanOffer)
			for _, lo := range loArr {
				dataMp[getMapKeyFromOwnerAndLoanProgram(owner, lo.GetLoanProgram())] = lo
			}
		}
	}
	if fedData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_FEDERAL_BANK.String(), palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: fedData,
		}, nil
	}
	if fedData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_FEDERAL_BANK.String(), palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: fedData,
		}, nil
	}
	if fedData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_FEDERAL_BANK.String(), palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: fedData,
		}, nil
	}
	// check if user has offer from IDFC. If yes, show user IDFC loan offer before showing Liquiloans
	if !d.commonFlagsConfig.HideIdfcOffer() {
		if idfcData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_IDFC_PL.String(), palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN)]; ok {
			return &OffersDecisionEngineResponse{
				LoanOffer: idfcData,
			}, nil
		}
	}

	if llFldgData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_LIQUILOANS_PL.String(), palPb.LoanProgram_LOAN_PROGRAM_FLDG)]; ok {
		return &OffersDecisionEngineResponse{LoanOffer: llFldgData}, nil
	}
	if llPalData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_LIQUILOANS_PL.String(), palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN)]; ok {
		return &OffersDecisionEngineResponse{LoanOffer: llPalData}, nil
	}
	if llStplData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_LIQUILOANS_PL.String(), palPb.LoanProgram_LOAN_PROGRAM_STPL)]; ok {
		return &OffersDecisionEngineResponse{LoanOffer: llStplData}, nil
	}

	if mvData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_MONEYVIEW_PL.String(), palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: mvData,
		}, nil
	}
	if mvData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_MONEYVIEW_PL.String(), palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: mvData,
		}, nil
	}
	if sgData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP.String(), palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: sgData,
		}, nil
	}
	if sgData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP.String(), palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: sgData,
		}, nil
	}

	if abflData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_LOANS_ABFL.String(), palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: abflData,
		}, nil
	}
	if abflData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_LOANS_ABFL.String(), palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: abflData,
		}, nil
	}

	for key, data := range dataMp {
		if d.commonFlagsConfig.HideIdfcOffer() {
			// Skip showing IDFC offer if exists
			if key == getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_IDFC_PL.String(), palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN) {
				continue
			}
		}
		if data != nil {
			return &OffersDecisionEngineResponse{
				LoanOffer: data,
			}, nil
		}
	}

	if ldcData, ok := dataMp[getMapKeyFromOwnerAndLoanProgram(commontypes.Ownership_LOANS_LENDEN.String(), palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION)]; ok {
		return &OffersDecisionEngineResponse{
			LoanOffer: ldcData,
		}, nil
	}

	return nil, epifierrors.ErrRecordNotFound
}
