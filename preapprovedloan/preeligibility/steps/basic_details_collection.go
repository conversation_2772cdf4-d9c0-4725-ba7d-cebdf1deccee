package steps

import (
	"context"
	"strings"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/pkg/errors"

	preEPb "github.com/epifi/gamma/api/preapprovedloan/pre_eligibility"
	userPb "github.com/epifi/gamma/api/user"
)

func (s *AddUserDetailsStep) collectUserBasicDetails(ctx context.Context, req *StepExecutionReq) error {
	userData, err := s.rpcHelper.GetUserByActorId(ctx, req.GetActorId())
	if err != nil {
		return errors.Wrap(err, "failed to fetch user by actor ID")
	}

	// Update verification details
	if userData.GetDataVerificationDetails() == nil {
		userData.DataVerificationDetails = &userPb.DataVerificationDetails{}
	}

	// Assigning the new list, since the user client rpc internally handles the appending(ref: user/dao/user.go:574)
	userData.DataVerificationDetails = &userPb.DataVerificationDetails{
		DataVerificationDetails: s.createBasicUserVerificationDetails(req.GetBasicUserDetails(), userData),
	}

	// Update user details
	return s.updateUserDetails(ctx, userData, []userPb.UserFieldMask{userPb.UserFieldMask_DATA_VERIFICATION_DETAILS})
}

// Create verification details for basic user info
func (s *AddUserDetailsStep) createBasicUserVerificationDetails(basicDetails *preEPb.BasicUserDetails, userData *userPb.User) []*userPb.DataVerificationDetail {
	var newList []*userPb.DataVerificationDetail

	// Add PAN Name, if not present in the user profile
	if userData.GetProfile().GetPanName() == nil {
		newList = append(newList, &userPb.DataVerificationDetail{
			DataType:           userPb.DataType_DATA_TYPE_PAN_NAME,
			VerificationMethod: userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			DataValue:          &userPb.DataVerificationDetail_PanName{PanName: basicDetails.GetName()},
		})
	}

	// Add PAN Number, if not present in the user profile
	if userData.GetProfile().GetPAN() == "" {
		newList = append(newList, &userPb.DataVerificationDetail{
			DataType:           userPb.DataType_DATA_TYPE_PAN,
			VerificationMethod: userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			DataValue:          &userPb.DataVerificationDetail_PanNumber{PanNumber: strings.ToUpper(basicDetails.GetPan())},
		})
	}

	// Add DOB, if not present in the user profile
	if userData.GetProfile().GetDateOfBirth() == nil {
		newList = append(newList, &userPb.DataVerificationDetail{
			DataType:           userPb.DataType_DATA_TYPE_DOB,
			VerificationMethod: userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			DataValue:          &userPb.DataVerificationDetail_DOB{DOB: basicDetails.GetDob()},
		})
	}

	return newList
}

// Update user details
func (s *AddUserDetailsStep) updateUserDetails(ctx context.Context, user *userPb.User, fieldMasks []userPb.UserFieldMask) error {
	updateRes, err := s.userClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User:       user,
		UpdateMask: fieldMasks,
	})
	if te := epifigrpc.RPCError(updateRes, err); te != nil {
		return errors.Wrap(te, "failed to update the user info")
	}
	return nil
}
