package stock_guardian

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/collection"
	collectionTypesPb "github.com/epifi/gamma/api/collection/types"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palCommsPb "github.com/epifi/gamma/api/preapprovedloan/comms"
	sgLmsPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	sgLmsEnumsPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/lms/enums"
	"github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	"github.com/epifi/gamma/preapprovedloan/dao"
	dlProvider "github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/helper"
	loanDataProvider "github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	"github.com/epifi/gamma/preapprovedloan/prepay/providers"
	"github.com/epifi/gamma/preapprovedloan/utils"
)

type StockGuardianBaseProvider struct {
	sgLmsClient           sgLmsPb.LmsClient
	loanAccountDao        dao.LoanAccountsDao
	loanDataProvider      loanDataProvider.IFactory
	rpcHelper             *helper.RpcHelper
	loanPaymentRequestDao dao.LoanPaymentRequestsDao
	piClient              piPb.PiClient
	installmentInfoDao    dao.LoanInstallmentInfoDao
	prePayConf            *common.Prepay
	dlProvider            dlProvider.IDeeplinkProvider
	collectionClient      collection.CollectionClient
}

func NewStockGuardianBaseProvider(sgLmsClient sgLmsPb.LmsClient, loanAccountDao dao.LoanAccountsDao, loanDataProvider loanDataProvider.IFactory, rpcHelper *helper.RpcHelper, loanPaymentRequestDao dao.LoanPaymentRequestsDao, piClient piPb.PiClient, installmentInfoDao dao.LoanInstallmentInfoDao, prePayConf *common.Prepay, dlProvider dlProvider.IDeeplinkProvider, collectionClient collection.CollectionClient) *StockGuardianBaseProvider {
	return &StockGuardianBaseProvider{
		sgLmsClient:           sgLmsClient,
		loanAccountDao:        loanAccountDao,
		loanDataProvider:      loanDataProvider,
		rpcHelper:             rpcHelper,
		loanPaymentRequestDao: loanPaymentRequestDao,
		piClient:              piClient,
		installmentInfoDao:    installmentInfoDao,
		prePayConf:            prePayConf,
		dlProvider:            dlProvider,
		collectionClient:      collectionClient,
	}
}

// compile time check to make sure StockGuardianBaseProvider implements providers.ILoanPreClosureDecider.
var _ providers.ILoanPreClosureDecider = &StockGuardianBaseProvider{}

func (s *StockGuardianBaseProvider) ShouldPreCloseTheLoanOnPayment(ctx context.Context, loanAccountId string, paymentAmount *money.Money) (bool, error) {
	loanAccount, err := s.loanAccountDao.GetById(ctx, loanAccountId)
	if err != nil {
		logger.Error(ctx, "failed to fetch loan account from db", zap.String(logger.LOAN_ACCOUNT_ID, loanAccountId), zap.Error(err))
		return false, errors.Wrap(err, "failed to fetch loan account from db")
	}
	loanForeClosureProvider, err := s.loanDataProvider.FetchLoanForeClosureDetailsProvider(ctx, &palPb.LoanHeader{
		Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
		LoanProgram: loanAccount.GetLoanProgram(),
	}, loanAccount.GetLmsPartner())
	if err != nil {
		return false, fmt.Errorf("failed to fetch loan fore closure provider: %w", err)
	}

	if loanForeClosureProvider == nil {
		return false, fmt.Errorf("loan fore closure provider not found")
	}

	foreclosureDetails, err := loanForeClosureProvider.FetchLoanPreClosureDetailsFromVendor(ctx, loanAccount)
	if err != nil {
		return false, fmt.Errorf("failed to fetch loan pre closure details from vendor: %w", err)
	}

	if foreclosureDetails != nil && foreclosureDetails.LoanPreCloseAmount != nil {
		compare, err := pkgMoney.CompareV2(foreclosureDetails.LoanPreCloseAmount, paymentAmount)
		if err != nil {
			logger.Error(ctx, "failed to compare payment amount and net foreclose amount", zap.Error(err), zap.String(logger.LOAN_ACCOUNT_ID, loanAccountId))
			return false, errors.Wrap(err, "failed to compare payment amount and foreclose amount")
		}
		switch compare {
		// if difference is negative or zero means payment amount>=foreclose amount, so we can foreclose.
		case 0, -1:
			return true, nil
		default:
			// since difference is negative means payment amount< foreclose amount so we cant foreclose.
			return false, nil
		}
	}
	return false, errors.New("loan pre-closure amount is not present")
}

func (s *StockGuardianBaseProvider) GetOrderRequestForPrePayment(ctx context.Context, req *providers.GetOrderRequestForPrePaymentRequest) (*providers.GetOrderRequestForPrePaymentResponse, error) {
	var userIdentifier *payPb.UserIdentifier

	paymentProtocol := payment.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED
	// PayerUserIdentifier will not be nil for Tpap flow
	if req.PayerUserIdentifier != nil {
		logger.Info(ctx, "Explicitly setting payment protocol to UPI for TPAP", zap.String(logger.LOAN_PROGRAM, req.LoanHeader.GetLoanProgram().String()))
		// explicitly send upi as preferred payment mode
		paymentProtocol = payment.PaymentProtocol_UPI
	}

	// if payer user identifier is empty, get savings account identifier
	if req.PayerUserIdentifier == nil {
		savingsAccount, err := s.rpcHelper.GetSavingsAccountDetails(ctx, req.ActorId, commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
		if err != nil {
			return nil, fmt.Errorf("error getting savings account by actor id: %w", err)
		}

		savingsPi, err := s.getPi(ctx, savingsAccount.GetAccountNo(), savingsAccount.GetIfscCode())
		if err != nil {
			return nil, fmt.Errorf("error getting pi for savings account: %w", err)
		}

		userIdentifier = &payPb.UserIdentifier{
			ActorId: req.ActorId,
			PiId:    savingsPi.GetId(),
		}
	} else {
		userIdentifier = &payPb.UserIdentifier{
			ActorId:     req.PayerUserIdentifier.GetActorId(),
			AccountId:   req.PayerUserIdentifier.GetAccountId(),
			AccountType: req.PayerUserIdentifier.GetAccountType(),
		}
	}

	orderReq := &payPb.CreateFundTransferOrderRequest{
		Amount:          req.Amount,
		Provenance:      orderPb.OrderProvenance_INTERNAL,
		Workflow:        orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		Tags:            []orderPb.OrderTag{orderPb.OrderTag_LOAN},
		ClientRequestId: req.OrchId,
		Remarks:         "PrePay Loan",
		PayerIdentifier: userIdentifier,
		PayeeIdentifier: &payPb.UserIdentifier{
			ActorId: s.prePayConf.PoolAccounts.StockGuardianPoolAccountActorId,
			PiId:    s.prePayConf.PoolAccounts.StockGuardianPoolAccountPi,
		},
		// The api always prefers IMPS, for using any other protocol, pass hard preferred protocol
		HardPreferredPaymentProtocol: paymentProtocol,
		PostAuthorisationAction:      s.dlProvider.GetLoanDashboardScreenDeepLink(s.dlProvider.GetLoanHeader()),
	}
	return &providers.GetOrderRequestForPrePaymentResponse{OrderRequest: orderReq}, nil
}

func (s *StockGuardianBaseProvider) getPi(ctx context.Context, accountNumber, ifscCode string) (*piPb.PaymentInstrument, error) {
	piRes, err := s.piClient.GetPi(ctx, &piPb.GetPiRequest{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.GetPiRequest_AccountRequestParams_{AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
			ActualAccountNumber: accountNumber,
			IfscCode:            ifscCode,
		}},
	})
	if te := epifigrpc.RPCError(piRes, err); te != nil {
		return nil, fmt.Errorf("failed to fetch pi by acount number, %w", te)
	}
	return piRes.GetPaymentInstrument(), nil
}

func (s *StockGuardianBaseProvider) ReconPrePaymentAtLender(ctx context.Context, reconReq *providers.ReconPrepaymentAtLenderRequest) (*providers.ReconPrepaymentAtLenderResponse, error) {
	loanPaymentReq, err := s.loanPaymentRequestDao.GetById(ctx, reconReq.PaymentRequestId)
	if err != nil {
		return nil, fmt.Errorf("unable to get loan payment request by id %s: %w", reconReq.PaymentRequestId, err)
	}

	loanAccount, err := s.loanAccountDao.GetById(ctx, loanPaymentReq.GetAccountId())
	if err != nil {
		return nil, fmt.Errorf("unable to get loan account by id %s: %w", loanPaymentReq.GetAccountId(), err)
	}

	isSgLoanAllowedToCancel, err := s.isSgLoanAllowedToCancel(ctx, loanAccount)
	if err != nil {
		return nil, fmt.Errorf("not able to check is sg allowed to cancel or not,err: %w", err)
	}

	isLoanCancellation := loanPaymentReq.GetType() ==
		palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE &&
		isSgLoanAllowedToCancel

	txnDetails := reconReq.PrepaymentTxnDetails

	repaymentType := sgLmsPb.RecordLoanRepaymentRequest_REPAYMENT_TYPE_UNSPECIFIED
	if isLoanCancellation {
		repaymentType = sgLmsPb.RecordLoanRepaymentRequest_REPAYMENT_TYPE_CANCELLATION
	}

	// Posting a successful payment at the vendor
	paymentPostingRes, err := s.sgLmsClient.RecordLoanRepayment(ctx, &sgLmsPb.RecordLoanRepaymentRequest{
		LoanAccountId:   loanAccount.GetAccountNumber(),
		TransactionDate: datetimePkg.TimeToDateInLoc(txnDetails.TxnInitiationTime, datetimePkg.IST),
		Amount:          txnDetails.TxnAmount,
		TransactionUtr:  txnDetails.Utr,
		PaymentProtocol: convertPaymentProtocolToVendorPaymentProtocol(txnDetails.PaymentProtocol),
		ClientRequestId: loanPaymentReq.GetOrchId(),
		RepaymentType:   repaymentType,
	})
	if te := epifigrpc.RPCError(paymentPostingRes, err); te != nil {
		return nil, te
	}

	// sending pre-pay push notification to user
	notifReq := helper.SendNotificationRequest{
		Amount:            txnDetails.TxnAmount,
		ActorId:           loanAccount.GetActorId(),
		NotificationType:  palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_PRE_PAY,
		LoanAccountNumber: loanAccount.GetAccountNumber(),
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_DETAILS_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanDetailsScreenOptions{
				PreApprovedLoanDetailsScreenOptions: &deeplinkPb.PreApprovedLoanDetailsScreenOptions{
					LoanId: loanAccount.GetId(),
				},
			},
		},
	}
	s.rpcHelper.SendNotificationWithGoRoutine(ctx, notifReq)

	if isLoanProgramEligibleForCollection(loanAccount.GetVendor(), loanAccount.GetLoanProgram()) {
		logger.Info(ctx, fmt.Sprintf("initiate payment collection for loan program: %v", loanAccount.GetLoanProgram()))
		s.InitiateUpdatePaymentWithCollection(ctx, loanAccount, txnDetails)
	}

	return &providers.ReconPrepaymentAtLenderResponse{}, nil
}

func (s *StockGuardianBaseProvider) InitiateUpdatePaymentWithCollection(ctx context.Context, loanAccount *palPb.LoanAccount, txnDetails *providers.PrepaymentTxnDetails) {
	req := &collection.InitiateUpdatePaymentRequest{
		CollectionHeader: &collectionTypesPb.Header{
			Product:          collectionTypesPb.Product_PRODUCT_LOAN,
			ProductVendor:    commonvgpb.Vendor_STOCK_GUARDIAN_LSP,
			CollectionVendor: commonvgpb.Vendor_CREDGENICS,
		},
		PaymentDetails: &collection.PaymentDetails{
			AccountId:     loanAccount.GetId(),
			MoneyReceived: txnDetails.TxnAmount,
			RecoveryDate:  datetimePkg.TimeToDateInLoc(txnDetails.TxnInitiationTime, datetimePkg.IST),
			ReferenceNo:   txnDetails.Utr,
		},
	}
	collRes, err := s.collectionClient.InitiateUpdatePayment(ctx, req)
	if err != nil || !collRes.GetStatus().IsSuccess() && !collRes.GetStatus().IsAlreadyExists() {
		logger.Error(ctx, "collectionClient.InitiateUpdatePayment rpc call failed", zap.Any(logger.RPC_STATUS, collRes.GetStatus()), zap.Error(err))
	}
}

func (s *StockGuardianBaseProvider) GetPayeeInfo() (*providers.PayeeInfo, error) {
	return &providers.PayeeInfo{
		PiId:    s.prePayConf.PoolAccounts.StockGuardianPoolAccountActorId,
		ActorId: s.prePayConf.PoolAccounts.StockGuardianPoolAccountPi,
	}, nil
}

func (s *StockGuardianBaseProvider) isSgLoanAllowedToCancel(
	ctx context.Context,
	loanAccount *palPb.LoanAccount,
) (bool, error) {
	vgRes, vgErr := s.sgLmsClient.GetLoanCancellationDetails(ctx, &sgLmsPb.GetLoanCancellationDetailsRequest{
		LoanAccountId:    loanAccount.GetAccountNumber(),
		CancellationDate: datetimePkg.TimeToDateInLoc(time.Now(), datetimePkg.IST),
	})
	if te := epifigrpc.RPCError(vgRes, vgErr); te != nil {
		if vgRes.GetStatus().GetCode() == rpc.StatusFailedPrecondition().GetCode() {
			return false, nil
		}
		return false, errors.Wrap(te, "error in getting loan cancellation details")
	}
	return true, nil
}

func convertPaymentProtocolToVendorPaymentProtocol(paymentProtocol payment.PaymentProtocol) sgLmsEnumsPb.PaymentProtocol {
	switch paymentProtocol {
	case payment.PaymentProtocol_IMPS:
		// vendor required IMPS payment to be accepted as a online_transfer mode
		return sgLmsEnumsPb.PaymentProtocol_PAYMENT_PROTOCOL_ONLINE_TRANSFER
	case payment.PaymentProtocol_ENACH:
		return sgLmsEnumsPb.PaymentProtocol_PAYMENT_PROTOCOL_NACH
	case payment.PaymentProtocol_UPI:
		return sgLmsEnumsPb.PaymentProtocol_PAYMENT_PROTOCOL_UPI
	case payment.PaymentProtocol_NEFT:
		return sgLmsEnumsPb.PaymentProtocol_PAYMENT_PROTOCOL_NEFT
	case payment.PaymentProtocol_RTGS:
		return sgLmsEnumsPb.PaymentProtocol_PAYMENT_PROTOCOL_RTGS
	default:
		return sgLmsEnumsPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED
	}
}

func isLoanProgramEligibleForCollection(lender palPb.Vendor, loanProgram palPb.LoanProgram) bool {
	return lo.Contains(utils.LenderToCollectionsEligibleLoanProgrammes[lender], loanProgram)
}
