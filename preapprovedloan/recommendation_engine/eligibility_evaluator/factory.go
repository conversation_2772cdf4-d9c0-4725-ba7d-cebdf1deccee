package eligibility_evaluator

import (
	"errors"
	"fmt"

	"github.com/google/wire"

	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	"github.com/epifi/gamma/api/preapprovedloan"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/federal"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/lenden"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/liquiloans"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers/stock_guardian"
)

var EligibilityEvaluatorFactoryWireSet = wire.NewSet(NewEligibilityEvaluatorFactoryImpl, wire.Bind(new(EligibilityEvaluatorFactory), new(*EligibilityEvaluatorFactoryImpl)))

//go:generate mockgen -source=factory.go -destination=mocks/mock_eligibility_evaluator.go
type EligibilityEvaluatorFactory interface {
	GetEligibilityEvaluatorProvider(loanHeader *preapprovedloan.LoanHeader, isNonFiCore bool) (providers.ILoansEligibilityProvider, error)
}

type EligibilityEvaluatorFactoryImpl struct {
	commonProvider                        *providers.CommonEligibilityProvider
	llRealtimeDistProvider                *liquiloans.RealtimeDistEligibilityProvider
	limitEstimatorClient                  limitEstimatorPb.CreditLimitEstimatorClient
	calculatorFactory                     calculatorTypes.FactoryProvider
	llFiliteProvider                      *liquiloans.LLFiliteProvider
	fedRealtimeProvider                   *federal.FedRealtimeDistEligibilityProvider
	fedRealtimeNtbProvider                *federal.FedRealtimeDistNtbEligibilityProvider
	nonFiCoreProvider                     *providers.NonFiCoreProvider
	realtimeCommonProvider                *providers.RealtimeCommonProvider
	sgRealtimeDistEligibilityProvider     *stock_guardian.SgRealtimeDistEligibilityProvider
	lendenRealtimeDistEligibilityProvider *lenden.RealtimeDistEligibilityProvider
}

func NewEligibilityEvaluatorFactoryImpl(
	commonProvider *providers.CommonEligibilityProvider,
	llRealtimeDistProvider *liquiloans.RealtimeDistEligibilityProvider,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	calculatorFactory calculatorTypes.FactoryProvider,
	llFiliteProvider *liquiloans.LLFiliteProvider,
	fedRealtimeProvider *federal.FedRealtimeDistEligibilityProvider,
	fedRealtimeNtbProvider *federal.FedRealtimeDistNtbEligibilityProvider,
	nonFiCoreProvider *providers.NonFiCoreProvider,
	realtimeCommonProvider *providers.RealtimeCommonProvider,
	sgRealtimeDistEligibilityProvider *stock_guardian.SgRealtimeDistEligibilityProvider,
	lendenRealtimeDistEligibilityProvider *lenden.RealtimeDistEligibilityProvider,
) *EligibilityEvaluatorFactoryImpl {
	return &EligibilityEvaluatorFactoryImpl{
		commonProvider:                        commonProvider,
		llRealtimeDistProvider:                llRealtimeDistProvider,
		limitEstimatorClient:                  limitEstimatorClient,
		calculatorFactory:                     calculatorFactory,
		llFiliteProvider:                      llFiliteProvider,
		realtimeCommonProvider:                realtimeCommonProvider,
		fedRealtimeProvider:                   fedRealtimeProvider,
		fedRealtimeNtbProvider:                fedRealtimeNtbProvider,
		nonFiCoreProvider:                     nonFiCoreProvider,
		sgRealtimeDistEligibilityProvider:     sgRealtimeDistEligibilityProvider,
		lendenRealtimeDistEligibilityProvider: lendenRealtimeDistEligibilityProvider,
	}
}

//nolint:gocritic
func (m *EligibilityEvaluatorFactoryImpl) GetEligibilityEvaluatorProvider(lh *preapprovedloan.LoanHeader, isNonFiCore bool) (providers.ILoansEligibilityProvider, error) {
	if isNonFiCore {
		return m.nonFiCoreProvider, nil
	}

	switch lh.GetVendor() {
	case preapprovedloan.Vendor_MONEYVIEW, preapprovedloan.Vendor_IDFC, preapprovedloan.Vendor_ABFL:
		return m.commonProvider, nil
	case preapprovedloan.Vendor_LIQUILOANS:
		switch lh.GetLoanProgram() {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return m.llRealtimeDistProvider, nil
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
			return m.realtimeCommonProvider, nil
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
			return m.realtimeCommonProvider, nil
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
			return m.llFiliteProvider, nil
		default:
			return m.commonProvider, nil
		}
	case preapprovedloan.Vendor_STOCK_GUARDIAN_LSP:
		switch lh.GetLoanProgram() {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION, preapprovedloan.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
			return m.sgRealtimeDistEligibilityProvider, nil
		default:
			return nil, errors.New(fmt.Sprintf("eligibility evaluator implementation for vendor %s and program %s not found", lh.GetVendor().String(), lh.GetLoanProgram().String()))
		}
	case preapprovedloan.Vendor_FEDERAL:
		switch lh.GetLoanProgram() {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return m.fedRealtimeProvider, nil
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
			return m.realtimeCommonProvider, nil
		// case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
		//	return m.fedRealtimeNtbProvider, nil
		default:
			return m.commonProvider, nil
		}
	case preapprovedloan.Vendor_LENDEN:
		switch lh.GetLoanProgram() {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return m.lendenRealtimeDistEligibilityProvider, nil
		default:
			return nil, errors.New(fmt.Sprintf("eligibility evaluator implementation for vendor %s and program %s not found", lh.GetVendor().String(), lh.GetLoanProgram().String()))
		}
	default:
		return nil, errors.New(fmt.Sprintf("eligibility evaluator implementation for vendor %s and program %s not found", lh.GetVendor().String(), lh.GetLoanProgram().String()))
	}
}
