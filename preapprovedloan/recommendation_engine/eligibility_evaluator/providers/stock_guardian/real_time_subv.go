package stock_guardian

import (
	"context"
	"errors"
	"time"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	commonGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
)

type SgRealtimeDistEligibilityProvider struct {
	*providers.CommonEligibilityProvider
	loecDao                dao.LoanOfferEligibilityCriteriaDao
	newEligibilityFlowConf *commonGenConf.FeatureConstraint
}

func NewSgRealtimeDistEligibilityProvider(loecDao dao.LoanOfferEligibilityCriteriaDao, commonProvider *providers.CommonEligibilityProvider, newEligibilityFlowConf *commonGenConf.FeatureConstraint,
) *SgRealtimeDistEligibilityProvider {
	return &SgRealtimeDistEligibilityProvider{
		loecDao:                   loecDao,
		CommonEligibilityProvider: commonProvider,
		newEligibilityFlowConf:    newEligibilityFlowConf,
	}

}

func (c *SgRealtimeDistEligibilityProvider) EvaluateLoanEligibility(ctx context.Context, req *providers.EvaluateLoanEligibilityRequest, lh *palPb.LoanHeader) (*providers.EvaluateLoanEligibilityResponse, error) {
	res, err := c.CommonEligibilityProvider.EvaluateLoanEligibility(ctx, req, lh)
	if err != nil {
		return nil, err
	}
	if res.IsOfferAvailable {
		return res, nil
	}

	ctx2 := epificontext.WithOwnership(ctx, helper.GetPalOwnership(lh.GetVendor()))
	loecs, loecErr := c.loecDao.GetActiveLoecsByActorIdLoanProgramsAndStatuses(ctx2, req.ActorId, []palPb.LoanProgram{lh.GetLoanProgram()}, nil, time.Hour*24*90, true)
	if loecErr != nil && !errors.Is(loecErr, epifierrors.ErrRecordNotFound) {
		return nil, loecErr
	}

	if len(loecs) > 0 {
		// Migrate from LoanEligibility to LoanDataCollection
		// LoanDataCollection workflow will be initiated for all the lender-program combinations listed  preapprovedloan/helper/data_collection.go->CDCEligibleLenderToPrograms
		// If the `newEligibilityFlowConf` feature is enabled, the loan program transitions to
		// `LOAN_PROGRAM_REAL_TIME_DISTRIBUTION`, triggering the LoanDataCollection workflow.
		// Otherwise, it defaults to `LOAN_PROGRAM_ELIGIBILITY`.
		if c.newEligibilityFlowConf != nil && c.newEligibilityFlowConf.IsAllowed() {
			res.CheckEligibility = &providers.CheckEligibility{
				ShouldCheckEligibility: true,
				LoanHeader: &palPb.LoanHeader{
					LoanProgram: loecs[0].GetLoanProgram(),
					Vendor:      loecs[0].GetVendor(),
				},
			}
		} else {
			res.CheckEligibility = &providers.CheckEligibility{
				ShouldCheckEligibility: true,
				LoanHeader: &palPb.LoanHeader{
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
					Vendor:      palPb.Vendor_EPIFI_TECH,
				},
			}

		}
		// Offline LOEC creation process:
		// - LOEC is created with sub status unspecified and
		// - if pre approved, status and sub status are changed to APPROVED
		// - if pre qualified, status will be in CREATED and sub status is changed to PRE_QUALIFIED_BY_VENDOR
		// - if rejected, the LOEC entry is left as is
		// to avoid opening up eligibility for non pre-qualified users we are returning false if sub status is unspecified
		if loecs[0].GetStatus() != palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED ||
			loecs[0].GetSubStatus() == palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_UNSPECIFIED {
			res.CheckEligibility.ShouldCheckEligibility = false
		}
	}
	return res, nil
}
