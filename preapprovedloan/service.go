// nolint : funlen, dupl, unparam, depguard
package preapprovedloan

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/google/uuid"
	"github.com/mohae/deepcopy"
	"github.com/pkg/errors"
	temporalClient "go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/encoding/protojson"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/celestial"
	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	onceV2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	workflowPkg "github.com/epifi/be-common/pkg/epifitemporal/workflow"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/names"
	namesPkg "github.com/epifi/be-common/pkg/names"
	"github.com/epifi/be-common/pkg/nulltypes"
	"github.com/epifi/be-common/pkg/queue"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	authPb "github.com/epifi/gamma/api/auth"
	authOrchestratorPb "github.com/epifi/gamma/api/auth/orchestrator"
	commsPb "github.com/epifi/gamma/api/comms"
	connectedAccPb "github.com/epifi/gamma/api/connected_account"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	mfExternal "github.com/epifi/gamma/api/investment/mutualfund/external"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palEnumsPb "github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/gamma/api/preapprovedloan/secured_loans"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	sgKycApiGatewayPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/kyc"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	userPb "github.com/epifi/gamma/api/user"
	userpb "github.com/epifi/gamma/api/user"
	creditReportPb "github.com/epifi/gamma/api/user/credit_report"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	mvPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	"github.com/epifi/gamma/pkg/feature/release"
	stringPkg "github.com/epifi/gamma/pkg/string"
	vendorPkg "github.com/epifi/gamma/pkg/vendors/federal"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/config"
	palCommonGenconf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/config/genconf"
	"github.com/epifi/gamma/preapprovedloan/consent"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/dao/filters"
	dataExistenceManager "github.com/epifi/gamma/preapprovedloan/data_existence_manager"
	"github.com/epifi/gamma/preapprovedloan/datacollectors"
	"github.com/epifi/gamma/preapprovedloan/dcpp"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	vkycFailureHandlers "github.com/epifi/gamma/preapprovedloan/deeplink/provider/idfc/vkyc/failure_handlers"
	"github.com/epifi/gamma/preapprovedloan/downtime"
	"github.com/epifi/gamma/preapprovedloan/dynamic_elements"
	deProvider "github.com/epifi/gamma/preapprovedloan/dynamic_elements/provider"
	loanErrors "github.com/epifi/gamma/preapprovedloan/errors"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/helper/calculators"
	"github.com/epifi/gamma/preapprovedloan/helper/calculators/provider/basecalculator"
	"github.com/epifi/gamma/preapprovedloan/landing_provider"
	"github.com/epifi/gamma/preapprovedloan/lms"
	loanDataProvider "github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	loanTxnFactory "github.com/epifi/gamma/preapprovedloan/loan_txn_provider"
	loanTxnProvider "github.com/epifi/gamma/preapprovedloan/loan_txn_provider/providers"
	loanPlans "github.com/epifi/gamma/preapprovedloan/loanplans"
	mandateHelper "github.com/epifi/gamma/preapprovedloan/mandate_manager/helper"
	mandateView "github.com/epifi/gamma/preapprovedloan/mandate_manager/mandate_view"
	multiDbProvider "github.com/epifi/gamma/preapprovedloan/multidb_provider"
	"github.com/epifi/gamma/preapprovedloan/preclose"
	precloseProviders "github.com/epifi/gamma/preapprovedloan/preclose/providers"
	"github.com/epifi/gamma/preapprovedloan/prepay"
	"github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator"
	pro "github.com/epifi/gamma/preapprovedloan/recommendation_engine/eligibility_evaluator/providers"
	priorityProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider"
	dcPriorityProviderFactory "github.com/epifi/gamma/preapprovedloan/recommendation_engine/priority_provider/data_collector"
	recommendationProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
	reconciliationTypes "github.com/epifi/gamma/preapprovedloan/reconciliation/types"
	"github.com/epifi/gamma/preapprovedloan/userdata"
	loanUtils "github.com/epifi/gamma/preapprovedloan/utils"
	"github.com/epifi/gamma/preapprovedloan/wire/types"
)

var NonTerminalLrStatuses = []palPb.LoanRequestStatus{
	palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
	palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
	palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED,
	palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_DISBURSED,
	palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION,
	palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
	palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFICATION_FAILED,
	palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_UNSPECIFIED,
	palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_UNKNOWN,
}

var EmiTimelineMap = map[helper.EmiTimelineType]palPb.GetLoanSummaryForHomeResponse_EmiTimelineState{
	helper.EmiTimelineType_EARLY_PRE_PAYMENT: palPb.GetLoanSummaryForHomeResponse_EMI_TIMELINE_STATE_EARLY_PRE_PAYMENT,
	helper.EmiTimelineType_PRE_DUE:           palPb.GetLoanSummaryForHomeResponse_EMI_TIMELINE_STATE_PRE_DUE,
}

var loanProgramToUserType = map[palPb.LoanProgram]mvPb.MvUserType{
	palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:      mvPb.MvUserType_MV_USER_TYPE_WHITELISTED_OFFER,
	palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION: mvPb.MvUserType_MV_USER_TYPE_OPEN_MARKET,
}

type Service struct {
	palPb.UnimplementedPreApprovedLoanServer
	loanOffersDao                  dao.LoanOffersDao
	loanAccountsDao                dao.LoanAccountsDao
	loanRequestsDao                dao.LoanRequestsDao
	loanStepExecutionsDao          dao.LoanStepExecutionsDao
	loanInstallmentInfoDao         dao.LoanInstallmentInfoDao
	loanActivityDao                dao.LoanActivityDao
	loanPaymentRequestDao          dao.LoanPaymentRequestsDao
	loanInstallmentPayoutDao       dao.LoanInstallmentPayoutDao
	loanApplicantDao               dao.LoanApplicantDao
	mandateRequestsDao             dao.MandateRequestDao
	multiDbProvider                multiDbProvider.IMultiDbProvider
	loecDao                        dao.LoanOfferEligibilityCriteriaDao
	eventBroker                    events.Broker
	rpcHelper                      *helper.RpcHelper
	signalWorkflowPublisher        queue.Publisher
	conf                           *config.Config
	txnExecutor                    storage.TxnExecutor
	txnExecutorProvider            *storage.DBResourceProvider[storage.IdempotentTxnExecutor]
	palEventPublisher              queue.Publisher
	deeplinkFactory                *deeplink.ProviderFactory
	lms                            lms.ILms
	prepayFactory                  prepay.IFactory
	segmentClient                  segmentPb.SegmentationServiceClient
	loanTxnFactory                 loanTxnFactory.IFactory
	loanDataProvider               loanDataProvider.IFactory
	downtime                       downtime.Processor
	celestialClient                celestialPb.CelestialClient
	dynamicElementsProviderFactory dynamic_elements.IFactory
	DynConf                        *genconf.Config
	connAccClient                  connectedAccPb.ConnectedAccountClient
	mvVgClient                     moneyview.MoneyviewClient
	mandateViewFactory             mandateView.MandateViewFactory
	multiDbDOnceMgr                onceV2.MultiDbDoOnce
	securedLoansClient             secured_loans.SecuredLoansClient
	payClient                      payPb.PayClient
	releaseEvaluator               release.IEvaluator
	userClient                     userPb.UsersClient
	piClient                       piPb.PiClient
	authClient                     authPb.AuthClient
	palVgClient                    palVgPb.PreApprovedLoanClient
	salaryClient                   salaryPb.SalaryProgramClient
	creditReportConf               *palCommonGenconf.CreditReportConfig
	creditReportClient             creditReportPb.CreditReportManagerClient
	creditReportV2Client           creditReportV2Pb.CreditReportManagerClient
	onbClient                      onbPb.OnboardingClient
	s3Client                       types.PreApprovedLoanS3Client
	limitEstimatorClient           limitEstimatorPb.CreditLimitEstimatorClient
	temporalClient                 temporalClient.Client
	partnerLmsUserDao              dao.PartnerLmsUserDao
	userIntelClient                userintel.UserIntelServiceClient
	calculatorFactory              calculatorTypes.FactoryProvider
	lmsDataDifferenceFinderFactory reconciliationTypes.LmsDataDifferenceFinderFactory
	uuidGenerator                  idgen.IUuidGenerator
	time                           datetime.Time
	mfExternalClient               mfExternal.MFExternalOrdersClient
	dataExistenceManager           dataExistenceManager.Manager
	preClosureFactory              preclose.IFactory
	landingProvider                landing_provider.ILandingProvider
	consentManagerFactory          consent.IConsentManagerFactory
	consentManagerHelper           *consent.ConsentManagerHelper
	dcPriorityProviderFactory      dcPriorityProviderFactory.IDataCollectorFactory
	recommendationEngine           recommendationProvider.ILoanRecommendationEngine
	loanPreferencesSaver           datacollectors.LoanPreferencesSaver
	consentDataSaver               datacollectors.ConsentDataSaver
	modifiedRoiConsentSaver        datacollectors.ModifiedRoiDataSaver
	authOrchestratorClient         authOrchestratorPb.OrchestratorClient
	dataCollectionPriorityProvider dcpp.IDataCollectionPriorityProvider
	priorityProviderFactory        priorityProvider.IPriorityProviderFactory
	userDataProvider               userdata.IUserDataProvider
	connectedSalaryAccountProvider *helper.ConnectedSalaryAccountProvider
	savingsClient                  savings.SavingsClient
	loanPlansProviderFactory       loanPlans.IProviderFactory
	acqEventPublisher              palEvents.AcqEventPublisher
	eligibilityEvaluatorFactory    eligibility_evaluator.EligibilityEvaluatorFactory
	sgKYCClient                    sgKycApiGatewayPb.KYCClient
}

func NewService(
	loanOffersDao dao.LoanOffersDao,
	loanAccountsDao dao.LoanAccountsDao,
	loanRequestsDao dao.LoanRequestsDao,
	loanStepExecutionsDao dao.LoanStepExecutionsDao,
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao,
	loanActivityDao dao.LoanActivityDao,
	loanPaymentRequestDao dao.LoanPaymentRequestsDao,
	loanInstallmentPayoutDao dao.LoanInstallmentPayoutDao,
	loecDao dao.LoanOfferEligibilityCriteriaDao,
	loanApplicantDao dao.LoanApplicantDao,
	multiDBDataProvider multiDbProvider.IMultiDbProvider,
	eventBroker events.Broker,
	rpcHelper *helper.RpcHelper,
	signalWorkflowPublisher types.SignalWorkflowPublisher,
	conf *config.Config,
	txnExecutor storage.TxnExecutor,
	txnExecutorProvider *storage.DBResourceProvider[storage.IdempotentTxnExecutor],
	palEventPublisher types.NudgeExitEventPublisher,
	deeplinkFactory *deeplink.ProviderFactory, lms lms.ILms,
	prepayFactory prepay.IFactory,
	segmentClient segmentPb.SegmentationServiceClient,
	loanTxnFactory loanTxnFactory.IFactory,
	loanDataProvider loanDataProvider.IFactory,
	downtime downtime.Processor,
	celestialClient celestialPb.CelestialClient,
	dynamicElementsProviderFactory dynamic_elements.IFactory,
	dynConf *genconf.Config,
	connAccClient connectedAccPb.ConnectedAccountClient,
	mvVgClient moneyview.MoneyviewClient,
	mandateViewFactory mandateView.MandateViewFactory,
	multiDbDOnceMgr onceV2.MultiDbDoOnce,
	securedLoansClient secured_loans.SecuredLoansClient,
	payClient payPb.PayClient,
	releaseEvaluator release.IEvaluator,
	userClient userPb.UsersClient,
	piClient piPb.PiClient,
	authClient authPb.AuthClient,
	palVgClient palVgPb.PreApprovedLoanClient,
	salaryClient salaryPb.SalaryProgramClient,
	creditReportConf *palCommonGenconf.CreditReportConfig,
	creditReportClient creditReportPb.CreditReportManagerClient,
	creditReportV2Client creditReportV2Pb.CreditReportManagerClient,
	onbClient onbPb.OnboardingClient,
	s3Client types.PreApprovedLoanS3Client,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	temporalClient temporalClient.Client,
	partnerLmsUserDao dao.PartnerLmsUserDao,
	userIntelClient userintel.UserIntelServiceClient,
	calculatorFactory calculatorTypes.FactoryProvider,
	lmsDataDifferenceFinderFactory reconciliationTypes.LmsDataDifferenceFinderFactory,
	uuidGenerator idgen.IUuidGenerator,
	time datetime.Time,
	mfExternalClient mfExternal.MFExternalOrdersClient,
	dataExistenceManager dataExistenceManager.Manager,
	preClosureFactory preclose.IFactory,
	landingProvider landing_provider.ILandingProvider,
	mandateRequestsDao dao.MandateRequestDao,
	consentManagerFactory consent.IConsentManagerFactory,
	consentManagerHelper *consent.ConsentManagerHelper,
	dcPriorityProviderFactory dcPriorityProviderFactory.IDataCollectorFactory,
	recommendationEngine recommendationProvider.ILoanRecommendationEngine,
	loanPreferencesSaver datacollectors.LoanPreferencesSaver,
	consentDataSaver datacollectors.ConsentDataSaver,
	authOrchestratorClient authOrchestratorPb.OrchestratorClient,
	dataCollectionPriorityProvider dcpp.IDataCollectionPriorityProvider,
	priorityProviderFactory priorityProvider.IPriorityProviderFactory,
	userDataProvider userdata.IUserDataProvider,
	connectedSalaryAccountProvider *helper.ConnectedSalaryAccountProvider,
	savingsClient savings.SavingsClient,
	loanPlansProviderFactory loanPlans.IProviderFactory,
	acqEventPublisher palEvents.AcqEventPublisher,
	modifiedRoiConsentSaver datacollectors.ModifiedRoiDataSaver,
	eligibilityEvaluatorFactory eligibility_evaluator.EligibilityEvaluatorFactory,
	sgKYCClient sgKycApiGatewayPb.KYCClient,
) *Service {
	return &Service{
		eligibilityEvaluatorFactory:    eligibilityEvaluatorFactory,
		sgKYCClient:                    sgKYCClient,
		loanOffersDao:                  loanOffersDao,
		loanAccountsDao:                loanAccountsDao,
		loanRequestsDao:                loanRequestsDao,
		loanStepExecutionsDao:          loanStepExecutionsDao,
		loanInstallmentInfoDao:         loanInstallmentInfoDao,
		loanActivityDao:                loanActivityDao,
		loanPaymentRequestDao:          loanPaymentRequestDao,
		loanInstallmentPayoutDao:       loanInstallmentPayoutDao,
		loanApplicantDao:               loanApplicantDao,
		loecDao:                        loecDao,
		multiDbProvider:                multiDBDataProvider,
		eventBroker:                    eventBroker,
		rpcHelper:                      rpcHelper,
		signalWorkflowPublisher:        signalWorkflowPublisher,
		conf:                           conf,
		txnExecutor:                    txnExecutor,
		txnExecutorProvider:            txnExecutorProvider,
		palEventPublisher:              palEventPublisher,
		deeplinkFactory:                deeplinkFactory,
		lms:                            lms,
		prepayFactory:                  prepayFactory,
		segmentClient:                  segmentClient,
		loanTxnFactory:                 loanTxnFactory,
		loanDataProvider:               loanDataProvider,
		downtime:                       downtime,
		celestialClient:                celestialClient,
		dynamicElementsProviderFactory: dynamicElementsProviderFactory,
		DynConf:                        dynConf,
		connAccClient:                  connAccClient,
		mvVgClient:                     mvVgClient,
		mandateViewFactory:             mandateViewFactory,
		multiDbDOnceMgr:                multiDbDOnceMgr,
		securedLoansClient:             securedLoansClient,
		payClient:                      payClient,
		releaseEvaluator:               releaseEvaluator,
		userClient:                     userClient,
		piClient:                       piClient,
		authClient:                     authClient,
		palVgClient:                    palVgClient,
		salaryClient:                   salaryClient,
		creditReportConf:               creditReportConf,
		creditReportClient:             creditReportClient,
		creditReportV2Client:           creditReportV2Client,
		onbClient:                      onbClient,
		s3Client:                       s3Client,
		limitEstimatorClient:           limitEstimatorClient,
		temporalClient:                 temporalClient,
		partnerLmsUserDao:              partnerLmsUserDao,
		userIntelClient:                userIntelClient,
		calculatorFactory:              calculatorFactory,
		lmsDataDifferenceFinderFactory: lmsDataDifferenceFinderFactory,
		uuidGenerator:                  uuidGenerator,
		time:                           time,
		mfExternalClient:               mfExternalClient,
		dataExistenceManager:           dataExistenceManager,
		preClosureFactory:              preClosureFactory,
		landingProvider:                landingProvider,
		mandateRequestsDao:             mandateRequestsDao,
		consentManagerFactory:          consentManagerFactory,
		consentManagerHelper:           consentManagerHelper,
		dcPriorityProviderFactory:      dcPriorityProviderFactory,
		recommendationEngine:           recommendationEngine,
		loanPreferencesSaver:           loanPreferencesSaver,
		consentDataSaver:               consentDataSaver,
		authOrchestratorClient:         authOrchestratorClient,
		dataCollectionPriorityProvider: dataCollectionPriorityProvider,
		priorityProviderFactory:        priorityProviderFactory,
		userDataProvider:               userDataProvider,
		connectedSalaryAccountProvider: connectedSalaryAccountProvider,
		savingsClient:                  savingsClient,
		loanPlansProviderFactory:       loanPlansProviderFactory,
		acqEventPublisher:              acqEventPublisher,
		modifiedRoiConsentSaver:        modifiedRoiConsentSaver,
	}
}

const esignDocumentExpiryInSeconds = 10 * 60 * 60

type checkForActiveLoanRequestsAndAccountsResponse struct {
	// denotes the details of existing active loanRequest/loanAccount
	// if alreadyExists is true, it denotes that an existing active loanRequest/loanAccount exists for the actor.
	alreadyExists       bool
	loanProgram         palPb.LoanProgram
	vendor              palPb.Vendor
	loanAccountId       string
	loanRequest         *palPb.LoanRequest
	lse                 *palPb.LoanStepExecution
	continueApplication bool
}

func getFederalLoanOffer(loRes *palVgPb.GetInstantLoanOfferResponse, lp palPb.LoanProgram) *palPb.LoanOffer {
	getRangeData := func(rs []*palVgPb.GetInstantLoanOfferResponse_RangeToFrom) []*palPb.RangeData {
		var rds []*palPb.RangeData
		for _, r := range rs {
			rd := &palPb.RangeData{
				Start: r.GetRangeFrom().GetUnits(),
				End:   r.GetRangeTo().GetUnits(),
			}
			switch r.GetType() {
			case palVgPb.GetInstantLoanOfferResponse_TYPE_PERCENTAGE:
				rd.Value = &palPb.RangeData_Percentage{
					Percentage: r.GetValue(),
				}
			case palVgPb.GetInstantLoanOfferResponse_TYPE_VALUE:
				rd.Value = &palPb.RangeData_AbsoluteValue{
					AbsoluteValue: moneyPkg.ParseFloat(r.GetValue(), moneyPkg.RupeeCurrencyCode),
				}
			default:
				// skip populating rd.Value
			}
			rds = append(rds, rd)
		}
		return rds
	}

	return &palPb.LoanOffer{
		VendorOfferId: loRes.GetOfferId(),
		Vendor:        palPb.Vendor_FEDERAL,
		OfferConstraints: &palPb.OfferConstraints{
			MaxLoanAmount:   loRes.GetMaxAmount(),
			MaxEmiAmount:    loRes.GetMaxAllowedEmi(),
			MaxTenureMonths: loRes.GetMaxTenureMonths(),
		},
		ProcessingInfo: &palPb.OfferProcessingInfo{
			Gst:           loRes.GetServiceTaxGst(),
			InterestRate:  getRangeData(loRes.GetInterestRates()),
			ProcessingFee: getRangeData(loRes.GetProcessingFeePercentages()),
			ApplicationId: loRes.GetApplicationId(),
		},
		ValidSince:  timestampPb.Now(),
		ValidTill:   timestampPb.New(time.Unix(loRes.GetExpiryTimestamp(), 0)),
		LoanProgram: lp,
	}
}

func (s *Service) getFederalInstantLoanOffer(ctx context.Context, pan string, phoneNumber *commontypes.PhoneNumber, lp palPb.LoanProgram) (*palPb.LoanOffer, error) {
	res, err := s.palVgClient.GetInstantLoanOffer(ctx, &palVgPb.GetInstantLoanOfferRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		Pan:           pan,
		PhoneNumber:   phoneNumber,
		ApplicationId: idgen.RandAlphaNumericString(25),
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		if res.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "record not found error in GetInstantLoanOffer from VG")
		}
		return nil, errors.Wrap(te, "error in GetInstantLoanOffer from VG")
	}
	return getFederalLoanOffer(res, lp), nil
}

// GetOfferDetails to fetch loan offer details for an actor
// If offer ID is present in the request, the RPC fetch details for that specific offer ID. If not passed, the RPC checks
// if a loan offer exists at vendor for the actor or not and accordingly returns the response.
// Note: If a new loan offer exists at the vendor's end, the RPC first marks the older loan offer as deactivated and then
// creates a new one. This is done to ensure at a single moment at max only one loan offer is present in active state.
func (s *Service) GetOfferDetails(ctx context.Context, req *palPb.GetOfferDetailsRequest) (*palPb.GetOfferDetailsResponse, error) {
	vendor := req.GetLoanHeader().GetVendor()
	res := &palPb.GetOfferDetailsResponse{}
	errRes := &palPb.GetOfferDetailsResponse{
		Status: rpcPb.StatusInternal(),
	}

	var offer *palPb.LoanOffer
	var err error

	if req.GetOfferId() != "" {
		offer, err = s.loanOffersDao.GetById(ctx, req.GetOfferId())
		if err != nil {
			logger.Error(ctx, "error in GetById", zap.Error(err), zap.String(logger.LOAN_OFFER_ID, req.GetOfferId()), zap.String(logger.LOAN_PROGRAM, req.GetLoanHeader().GetLoanProgram().String()), zap.String(logger.VENDOR, vendor.String()))
			return errRes, nil
		}
	}

	if offer == nil || (offer.GetVendor() == palPb.Vendor_FEDERAL && offer.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN) {
		oldLoanOffer, getErr := s.loanOffersDao.GetActiveOfferByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), vendor, req.GetLoanHeader().GetLoanProgram())
		if getErr != nil && !errors.Is(getErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch old loan offer for actor", zap.Error(getErr))
			return errRes, nil
		}
		if oldLoanOffer.GetVendor() == palPb.Vendor_FEDERAL && oldLoanOffer.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN {
			usr, usrErr := s.rpcHelper.GetUserByActorId(ctx, req.GetActorId())
			if usrErr != nil {
				logger.Error(ctx, "error in GetUserByActorId", zap.Error(usrErr))
				return errRes, nil
			}

			offerFromVendor, offerFromVendorErr := s.getFederalInstantLoanOffer(ctx, usr.GetProfile().GetPAN(), usr.GetProfile().GetPhoneNumber(), req.GetLoanHeader().GetLoanProgram())
			if offerFromVendorErr != nil && !errors.Is(offerFromVendorErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error in GetInstantLoanOffer", zap.Error(offerFromVendorErr))
				return errRes, nil
			}
			if errors.Is(offerFromVendorErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error in GetInstantLoanOffer record not found", zap.Error(offerFromVendorErr))
				if oldLoanOffer != nil {
					oldLoanOffer.DeactivatedAt = timestampPb.Now()
					updateErr := s.loanOffersDao.Update(ctx, oldLoanOffer, []palPb.LoanOfferFieldMask{
						palPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT,
					})
					if updateErr != nil {
						logger.Error(ctx, "error in deactivating loan offer in DB after no offer from vendor", zap.Error(updateErr))
						// purposefully not returning internal from here. We would want this to be handled by record not found
					}
				}
				errRes.Status = rpcPb.StatusRecordNotFound()
				return errRes, nil
			}

			var newOfferErr error
			offer, newOfferErr = s.updateOldOfferWithNewOffer(ctx, oldLoanOffer, offerFromVendor)
			if newOfferErr != nil {
				logger.Error(ctx, "failed to update old offer with new offer", zap.Error(newOfferErr))
				return errRes, nil
			}

		} else {
			if oldLoanOffer == nil {
				logger.Info(ctx, "user doesn't have any loan offer")
				res.Status = rpcPb.StatusRecordNotFound()
				return res, nil
			}
			offer = oldLoanOffer
		}
	}

	// getting defaultValueCalculator
	defaultValueCalculator := s.calculatorFactory.GetDefaultValueCalculator(ctx, offer)
	// if the max loan amount constraint not able to honour the max emi constraint`then correct the max amount
	maxLoanAmount := defaultValueCalculator.GetMaxAmountBasisOnMaxTenureAndMaxEmi()
	moneyComp, moneyCompErr := moneyPkg.CompareV2(maxLoanAmount, offer.GetOfferConstraints().GetMaxLoanAmount())
	if moneyCompErr != nil {
		logger.Error(ctx, "could not compare actual max loan amount and offers max loan amount: ", zap.Error(moneyCompErr))
		return errRes, nil
	}
	if moneyComp == -1 {
		offer.GetOfferConstraints().MaxLoanAmount = maxLoanAmount
		updateOfferErr := s.loanOffersDao.Update(ctx, offer, []palPb.LoanOfferFieldMask{palPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_OFFER_CONSTRAINTS})
		if updateOfferErr != nil {
			return errRes, nil
		}
	}

	// if minAmount is greater than max amount
	maxAmount, err := moneyPkg.ToPaise(offer.GetOfferConstraints().GetMaxLoanAmount())
	if err != nil {
		logger.Error(ctx, "error getting max loan amount in paise", zap.Error(err))
		return errRes, nil
	}
	minAmount, err := helper.GetMinLoanAmountForSliderInPaisa(offer)
	if err != nil {
		logger.Error(ctx, "error getting min loan amount for slider", zap.Error(err))
		return errRes, nil
	}
	if maxAmount < minAmount {
		logger.Info(ctx, "Max Loan Amount in Offer From VG is less than minimum loan amount")
		res.Status = rpcPb.StatusPermissionDenied()
		return res, nil
	}

	// getting tenure and loanAmount for first time and also if loanAmount is selected
	tenureInMonths, loanAmount, err := defaultValueCalculator.GetDefaultOfferTenureAndLoanAmount(req.GetTenureInMonths(), req.GetLoanAmount(), s.conf.LoanCalculator.LoanSelectionConstraint[req.GetLoanHeader().GetVendor().String()])
	if err != nil {
		logger.Error(ctx, "error getting default tenure and loan amount", zap.Error(err),
			zap.Int32("tenure", req.GetTenureInMonths()), zap.String(logger.AMOUNT, req.GetLoanAmount().String()),
			zap.String(logger.VENDOR, req.GetLoanHeader().GetVendor().String()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	pledgeDetails := req.GetPledgeDetails()
	if len(req.GetPledgeDetails().GetMutualFunds().GetSchemes()) == 0 {
		pledgeDetails = defaultValueCalculator.GetDefaultPledgeDetails()
	}

	currentDate := datetime.TimeToDateInLoc(time.Now(), datetime.IST)
	calculator, err := s.calculatorFactory.GetCalculator(
		ctx,
		calculatorTypes.NewRequest(
			req.GetCalculatorAccuracy(),
			loanAmount,
			tenureInMonths,
			currentDate,
			offer,
			req.GetPledgeDetails(),
		),
	)
	if err != nil {
		logger.Error(ctx, "error in getting calculator", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// Currently, we have hard coded the loan plans strategy to Min Mid Max. As more strategies are added, we can revisit
	// this logic, and put this behind an actual strategy design pattern.
	plansProvider, err := s.loanPlansProviderFactory.GetLoanPlansProvider(offer.GetVendor())
	if err != nil {
		logger.Error(ctx, "error getting loan plans provider", zap.Error(err), zap.String(logger.VENDOR, offer.GetVendor().String()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	plans, err := plansProvider.GetLoanPlans(ctx, &loanPlans.Request{
		Calculator: calculators.GetCalculator(
			ctx,
			calculators.GetCalculatorProviderRequest{Vendor: offer.GetVendor(), LoanProgram: offer.GetLoanProgram(), Offer: offer},
			&basecalculator.Calculator{LoanOffer: offer}),
		LoanOffer:          offer,
		LoanAmount:         loanAmount,
		CalculatorAccuracy: req.GetCalculatorAccuracy(),
		PledgeDetails:      req.GetPledgeDetails(),
	})
	if err != nil {
		logger.Error(ctx, "error getting loan plans", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	year, month, day := offer.GetValidTill().AsTime().Date()

	preciseMaxLoanAmount := deepcopy.Copy(offer.GetOfferConstraints().GetMaxLoanAmount()).(*money.Money)

	res = &palPb.GetOfferDetailsResponse{
		Status:  rpcPb.StatusOk(),
		OfferId: offer.GetId(),
		OfferInfo: &palPb.GetOfferDetailsResponse_OfferInfo{
			MinLoanAmount:           defaultValueCalculator.GetMinLoanAmount(),
			MaxLoanAmount:           maxLoanAmount,
			InterestRate:            calculator.GetInterestRate(),
			MinTenureInMonths:       calculator.GetMinOfferTenureInMonths(),
			MaxTenureInMonths:       calculator.GetMaxOfferTenureInMonths(),
			MaxEmiAmount:            offer.GetOfferConstraints().GetMaxEmiAmount(),
			GstPercentage:           offer.GetProcessingInfo().GetGst(),
			AprRate:                 calculator.GetAprRate(),
			ValidTill:               &date.Date{Year: int32(year), Month: int32(month), Day: int32(day)},
			ProcessingFeePercentage: calculator.GetProcessingFeePercentage(),
			AdditionalConstraints:   getOfferDetailsAdditionalConstraints(req, offer),
			MinProcessingFee:        calculator.GetMinProcessingFee(),
			MaxProcessingFee:        calculator.GetMaxProcessingFee(),
			ExactMaxLoanAmount:      preciseMaxLoanAmount,
		},
		LoanInfo: &palPb.GetOfferDetailsResponse_LoanInfo{
			Amount:          loanAmount,
			TenureInMonths:  tenureInMonths,
			DisbursalAmount: calculator.GetDisbursalAmount(),
			EmiAmount:       calculator.GetEmiAmount(),
			Deductions: &palPb.GetOfferDetailsResponse_LoanInfo_Deductions{
				TotalDeductions: calculator.GetTotalDeductions(),
				Gst:             calculator.GetProcessingFeeGst(),
				ProcessingFee:   calculator.GetProcessingFee(),
				AdvanceInterest: calculator.GetAdvanceInterest(),
			},
			Constrains: &palPb.GetOfferDetailsResponse_LoanInfo_Constrains{
				MinLoanAmount:         defaultValueCalculator.GetMinLoanAmount(),
				MaxLoanAmount:         calculator.GetMaxLoanAmount(),
				MinTenureInMonths:     calculator.GetSliderMinTenureInMonths(),
				MaxTenureInMonths:     calculator.GetSliderMaxTenureInMonths(),
				AdditionalConstraints: getLoanInfoAdditionalConstraints(req, offer),
			},
			TotalPayable:    calculator.GetTotalPayable(),
			PledgeDetails:   pledgeDetails,
			MinMonthlyDue:   calculator.GetMinMonthlyDueAmount(),
			FirstEmiDueDate: calculator.GetFirstEmiDueDate(),
			GracePeriod:     calculator.GetGracePeriod(),
		},
		Vendor:    offer.GetVendor(),
		LoanPlans: plans,
	}
	return res, nil
}

func (s *Service) updateOldOfferWithNewOffer(ctx context.Context, oldLoanOffer, offerFromVendor *palPb.LoanOffer) (*palPb.LoanOffer, error) {
	if oldLoanOffer.GetVendorOfferId() == offerFromVendor.GetVendorOfferId() {
		// since the offer id returned from vendor is same, no need to generate a new offer in DB
		return oldLoanOffer, nil
	}
	offerFromVendor.ActorId = oldLoanOffer.GetActorId()
	loecDaoRes, loecDaoResErr := s.loecDao.GetByActorIdAndVendorAndStatus(ctx, oldLoanOffer.GetActorId(), oldLoanOffer.GetVendor(), []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED}, true)
	if loecDaoResErr != nil && !storage.IsRecordNotFoundError(loecDaoResErr) {
		logger.Error(ctx, "failed to get loan_offer_eligibility by actor ID.", zap.Error(loecDaoResErr))
		return nil, loecDaoResErr
	}
	if len(loecDaoRes) > 0 {
		offerFromVendor.LoanOfferEligibilityCriteriaId = loecDaoRes[0].GetId()
	}
	var lo *palPb.LoanOffer
	var err error
	// txnExecutor belongs to federal Db
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		if oldLoanOffer != nil {
			oldLoanOffer.DeactivatedAt = timestampPb.Now()
			updateErr := s.loanOffersDao.Update(txnCtx, oldLoanOffer, []palPb.LoanOfferFieldMask{
				palPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_DEACTIVATED_AT,
			})
			if updateErr != nil {
				return errors.Wrap(updateErr, "failed to update old loan offer")
			}
		}
		lo, err = s.loanOffersDao.Create(txnCtx, offerFromVendor)
		if err != nil {
			if !errors.Is(err, epifierrors.ErrAlreadyExists) {
				return errors.Wrap(err, "failed to create new loan offer from vendor offer")
			}
			lo, err = s.loanOffersDao.GetActiveOfferByActorIdAndVendor(ctx, offerFromVendor.GetActorId(), offerFromVendor.GetVendor())
			if err != nil {
				return errors.Wrap(err, "failed to get active offer by actor ID and vendor")
			}
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "failed to create a new loan offer from vendor offer", zap.Error(txnErr))
		return nil, txnErr
	}
	offerFromVendor.Id = lo.GetId()
	return offerFromVendor, nil
}

func getOfferDetailsAdditionalConstraints(req *palPb.GetOfferDetailsRequest, loanOffer *palPb.LoanOffer) *palPb.GetOfferDetailsResponse_OfferInfo_FiftyfinLamfConstraints {
	if req.GetLoanHeader().GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_LAMF || req.GetLoanHeader().GetVendor() != palPb.Vendor_FIFTYFIN {
		return nil
	}
	return &palPb.GetOfferDetailsResponse_OfferInfo_FiftyfinLamfConstraints{
		FiftyfinLamfConstraints: &palPb.FiftyFinLamfConstraints{
			MfPortfolioConstraint: loanOffer.GetOfferConstraints().GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint(),
		},
	}
}

func getLoanInfoAdditionalConstraints(req *palPb.GetOfferDetailsRequest, loanOffer *palPb.LoanOffer) *palPb.GetOfferDetailsResponse_LoanInfo_Constrains_FiftyfinLamfConstraints {
	if req.GetLoanHeader().GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_LAMF || req.GetLoanHeader().GetVendor() != palPb.Vendor_FIFTYFIN {
		return nil
	}
	return &palPb.GetOfferDetailsResponse_LoanInfo_Constrains_FiftyfinLamfConstraints{
		FiftyfinLamfConstraints: &palPb.FiftyFinLamfConstraints{
			MfPortfolioConstraint: loanOffer.GetOfferConstraints().GetFiftyfinLamfConstraintInfo().GetMfPortfolioConstraint(),
		},
	}
}

// GetApplicationStatus returns info for the loan application request status
func (s *Service) GetApplicationStatus(ctx context.Context, req *palPb.GetApplicationStatusRequest) (*palPb.GetApplicationStatusResponse, error) {
	var loanRequest *palPb.LoanRequest
	var err error
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	if req.GetLoanRequestId() != "" {
		loanRequest, err = s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "no lr records found for GetApplicationStatus", zap.Error(err), zap.String("lrId", req.GetLoanRequestId()),
					zap.String("vendor", req.GetLoanHeader().GetVendor().String()), zap.String("program", req.GetLoanHeader().GetLoanProgram().String()))
				return &palPb.GetApplicationStatusResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			logger.Error(ctx, "error while fetching loan request from db", zap.Error(err))
			return &palPb.GetApplicationStatusResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
	} else {
		// try with fallback
		loanRequests, lrsErr := s.loanRequestsDao.GetByActorIdVendorStatusAndLoanProgram(ctx, req.GetActorId(), req.GetLoanHeader().GetVendor(), []palPb.LoanRequestStatus{
			palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
			palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
			palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_DISBURSED,
			palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED,
			palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED,
			palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFICATION_FAILED,
		}, req.GetLoanHeader().GetLoanProgram())
		if lrsErr != nil || len(loanRequests) < 1 {
			if errors.Is(lrsErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "no lr records found for GetApplicationStatus (fallback)", zap.Error(lrsErr), zap.String("lrId", req.GetLoanRequestId()),
					zap.String("vendor", req.GetLoanHeader().GetVendor().String()), zap.String("program", req.GetLoanHeader().GetLoanProgram().String()))
				return &palPb.GetApplicationStatusResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil
			}
			logger.Error(ctx, "error while fetching loan request from db by ActorIdVendorStatusAndLoanProgram", zap.Error(lrsErr))
			return &palPb.GetApplicationStatusResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		} else {
			logger.Info(ctx, "fetched loan request via actor id, vendor, statuses and loan program with fallback")
			loanRequest = loanRequests[0]
		}
	}

	if loanRequest.GetActorId() != req.GetActorId() {
		logger.Error(ctx, "actor id mismatch", zap.String("expected", loanRequest.GetActorId()), zap.String("actual", req.GetActorId()))
		return &palPb.GetApplicationStatusResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}

	flow := palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION
	if loanRequest.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY {
		flow = palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY
	}

	lse, err := s.loanStepExecutionsDao.GetLatestByRefIdAndFlow(ctx, loanRequest.GetId(), flow)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching loan request from db", zap.Error(err))
		return &palPb.GetApplicationStatusResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	loanRequest, err = helper.RoundLoanRequest(loanRequest)
	if err != nil {
		logger.Error(ctx, "Error in Rounding Loan Request", zap.Error(err))
		return &palPb.GetApplicationStatusResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	loanRequest, lse, err = s.checkCurrentLrAndInitiateDataCollectionWf(ctx, loanRequest, lse)
	if err != nil {
		logger.Error(ctx, "error in checking and initiating new data collection workflow", zap.Error(err))
		return &palPb.GetApplicationStatusResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &palPb.GetApplicationStatusResponse{
		Status:           rpcPb.StatusOk(),
		LoanRequest:      loanRequest,
		CurrentExecution: lse,
	}, nil
}

func (s *Service) checkCurrentLrAndInitiateDataCollectionWf(ctx context.Context, currentLr *palPb.LoanRequest, currentLse *palPb.LoanStepExecution) (newLr *palPb.LoanRequest, newLse *palPb.LoanStepExecution, err error) {
	// we want to consider initiation of a new data collection flow only if the current lr has ended and has no next action for the user
	if currentLr.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY ||
		currentLr.GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS ||
		currentLr.GetNextAction() != nil {
		if shouldGetNextActionInSync(currentLr, currentLse) {
			nextAction, syncErr := s.getNextActionInSync(ctx, currentLr)
			if nextAction != nil {
				currentLr.NextAction = nextAction
			} else {
				logger.Error(ctx, "failed to get next action in sync", zap.Error(syncErr))
			}
		}
		return currentLr, currentLse, nil
	}
	// do not initiate data collection flow if the current lr is already in terminal non success state to avoid endless loop of loan request creation
	// instead redirect the user back to landing screen so that appropriate screen can be shown
	if currentLr.GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS && currentLr.GetCompletedAt() != nil {
		currentLr.NextAction = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN}
		return currentLr, nil, nil
	}
	recomLoanOptionRes, err := s.recommendationEngine.GetRecommendedLoanOption(ctx, &recommendationProvider.GetRecommendedLoanOptionRequest{
		ActorId: currentLr.GetActorId(),
	})
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to get recommended loan option")
	}

	// If the loan Offer is ABFL RTB and we have SG eligibility then go in eligibility flow
	if recomLoanOptionRes.ActiveOffer != nil {
		allowedOffer := recomLoanOptionRes.ActiveOffer

		isSgEligPrio, err := landing_provider.CheckIfPrioritizeSgEligOverLoanOffer(ctx, s.DynConf, allowedOffer,
			s.eligibilityEvaluatorFactory, s.onbClient, s.savingsClient)
		if err != nil {
			return nil, nil, errors.Wrap(err, fmt.Sprintf("failed to check if sg elig is prioritised over loan offer or not, err: %v", err))
		}

		if isSgEligPrio {
			recomLoanOptionRes.Eligibility.CheckEligibility = true
			recomLoanOptionRes.Eligibility.LoanHeader = &palPb.LoanHeader{
				LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
				Vendor:      palPb.Vendor_EPIFI_TECH,
			}
			recomLoanOptionRes.ActiveOffer = nil
		} else {
			logger.Info(ctx,
				"user had an offer but experimentation was false or offer was not for ABFL/MV vendor",
				zap.Any("vendor", allowedOffer.GetVendor()),
				zap.Any("loanProgram", allowedOffer.GetLoanProgram()),
				zap.String(logger.ACTOR_ID_V2, currentLr.GetActorId()),
			)
		}
	}
	logger.Info(ctx, "Recommendation engine does not sent active offer so continuing with normal flow in getApplicationStatus", zap.String(logger.ACTOR_ID_V2, currentLr.GetActorId()))

	// we want to initiate data collection flow only if recommended loan option returns CheckEligibility as true
	if recomLoanOptionRes == nil || recomLoanOptionRes.Eligibility == nil || !recomLoanOptionRes.Eligibility.CheckEligibility {
		return currentLr, currentLse, nil
	}
	userFeatProp, err := helper.GetUserFeatureProperty(ctx, currentLr.GetActorId(), s.onbClient, s.savingsClient)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to check if user is non fi core user or not")
	}
	newLr, err = s.checkAndInitiateNonFiCoreDataCollectionWf(ctx, currentLr.GetActorId(), userFeatProp)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to initiate data collection workflow")
	}
	return newLr, nil, nil
}
func (s *Service) CheckSGEligibility(ctx context.Context, actorId string) (bool, error) {
	lh := &palPb.LoanHeader{
		LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palPb.Vendor_STOCK_GUARDIAN_LSP,
	}
	userFeatRes, err := helper.GetUserFeatureProperty(ctx, actorId, s.onbClient, s.savingsClient)
	if err != nil {
		return false, fmt.Errorf("failed to get user feature property, err: %w", err)
	}
	isNonFiCoreUser := !userFeatRes.IsFiSAHolder
	provider, err := s.eligibilityEvaluatorFactory.GetEligibilityEvaluatorProvider(lh, isNonFiCoreUser)
	if err != nil {
		return false, fmt.Errorf("failed to get eligibilityEvaluator, err: %w", err)
	}
	res, err := provider.EvaluateLoanEligibility(ctx, &pro.EvaluateLoanEligibilityRequest{
		ActorId:         actorId,
		IsNonFiCoreUser: isNonFiCoreUser,
	}, lh)
	if err != nil {
		return false, err
	}

	if res.ShouldCheckLoanEligibility() {
		return true, nil
	}
	return false, nil
}
func shouldGetNextActionInSync(lr *palPb.LoanRequest, lse *palPb.LoanStepExecution) bool {
	if lr.GetDetails().GetIsInSyncMode() {
		return true
	}
	switch {
	case lr.GetVendor() == palPb.Vendor_STOCK_GUARDIAN_LSP && lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION:
		switch {
		case lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE && lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
			lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK,
			lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VKYC,
			lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AML:
			return true
		default:
			return false
		}
	case lr.GetVendor() == palPb.Vendor_ABFL && lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION:
		switch {
		case lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:
			return true
		default:
			return false
		}
	case lr.GetVendor() == palPb.Vendor_FEDERAL:
		switch {
		case lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE && lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
			lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP && lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
			lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK,
			lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VKYC:
			return true
		default:
			return false
		}
	case lr.GetVendor() == palPb.Vendor_LENDEN && lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION:
		switch {
		case lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC,
			lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE:
			return true
		default:
			return false
		}
	case lr.GetVendor() == palPb.Vendor_EPIFI_TECH:
		switch lse.GetStepName() {
		case palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_VERIFICATION:
			return true
		default:
			return false
		}
	}
	return false
}

func (s *Service) initiateWorkflow(ctx context.Context, clientReqId *celestialPb.ClientReqId, actorId string,
	payload []byte, workflowType workflowPb.Type, version workflowPb.Version) error {
	ow := epificontext.OwnershipFromContext[context.Context](ctx)

	// Using best-effort QOS to bypass SQS queues during remote debugging, allowing direct workflow initiation
	var qos celestialPb.QoS
	if cfg.IsNonProdEnv(s.DynConf.Application().Environment) && cfg.IsRemoteDebugEnabled() {
		qos = celestialPb.QoS_BEST_EFFORT
	}
	initiateWorkflowRes, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: actorId,
			Version: version,
			Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowType),
			Payload: payload,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     clientReqId.GetId(),
				Client: clientReqId.GetClient(),
			},
			Ownership:        ow,
			QualityOfService: qos,
		},
	})

	if te := epifigrpc.RPCError(initiateWorkflowRes, err); te != nil {
		return fmt.Errorf("error while initiating workflow for client req id %s, %w", clientReqId.GetId(), te)
	}
	return nil
}

func (s *Service) isPlCCLimitAlreadyExhausted(ctx context.Context, lo *palPb.LoanOffer, callVendor bool) (bool, error) {
	if lo.GetVendor() == palPb.Vendor_FEDERAL {
		limitResp, rpcErr := s.limitEstimatorClient.GetLoanConservativeLimit(ctx, &limitEstimatorPb.GetLoanConservativeLimitRequest{
			ActorId:          lo.GetActorId(),
			Vendor:           lo.GetVendor(),
			OfferId:          lo.GetId(),
			ShouldCallVendor: callVendor,
		})
		if err := epifigrpc.RPCError(limitResp, rpcErr); err != nil {
			if limitResp.GetStatus().IsResourceExhausted() {
				return true, nil
			}
			return false, err
		}
	}
	return false, nil
}

// ApplyForLoan is called after Submitting the loan required details and confirming to take loan
// ApplyForLoan get live offer details, and create a new entry in loan_requests for Application, returns ApplyForLoanResponse
// nolint:gocritic, dupl
func (s *Service) ApplyForLoan(ctx context.Context, req *palPb.ApplyForLoanRequest) (*palPb.ApplyForLoanResponse, error) {
	vendor := req.GetLoanHeader().GetVendor()
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendor))
	res := &palPb.ApplyForLoanResponse{}
	loanOffer, err := s.loanOffersDao.GetById(ctx, req.GetOfferId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in GetById", zap.Error(err), zap.String(logger.LOAN_OFFER_ID, req.GetOfferId()))
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "error while fetching loan offer from db", zap.Error(err), zap.String(logger.LOAN_OFFER_ID, req.GetOfferId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// check if loan program passed in req params and loan program in offer are same or not. If not same, log an error and return from here
	if req.GetLoanHeader().GetLoanProgram() != loanOffer.GetLoanProgram() {
		logger.Error(ctx, "loan program mismatch",
			zap.String("expected", loanOffer.GetLoanProgram().String()),
			zap.String("actual", req.GetLoanHeader().GetLoanProgram().String()))
		res.Status = rpcPb.StatusInvalidArgument()
		return res, nil
	}

	// in case of realtime distribution, setu will be doing limit check, so we need not to do this check here.
	if loanOffer.GetVendor() == palPb.Vendor_FEDERAL && loanOffer.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION &&
		loanOffer.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB {
		limitExhausted, limitExhaustedErr := s.isPlCCLimitAlreadyExhausted(ctx, loanOffer, true)
		if limitExhaustedErr != nil {
			logger.Error(ctx, "error while checking if offer limit is already exhausted", zap.String(logger.OFFER_ID, loanOffer.GetId()), zap.Error(limitExhaustedErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		if limitExhausted {
			res.Status = rpcPb.StatusInternal()
			deactivateErr := s.loanOffersDao.DeactivateLoanOffer(ctx, loanOffer.GetId())
			if deactivateErr != nil {
				logger.Error(ctx, "error while deactivating offer due fed limit change", zap.String(logger.OFFER_ID, loanOffer.GetId()), zap.Error(deactivateErr))
				return res, nil
			}
			logger.Info(ctx, fmt.Sprintf("fed offer deactivated due to limit changed for actor: %v", loanOffer.GetActorId()))
			return res, nil
		}
	}

	var activeLr *palPb.LoanRequest
	var activeLse *palPb.LoanStepExecution
	checkRes, checkErr := s.checkForActiveLoanRequestsAndAccounts(ctx, req.GetLoanHeader(), req.GetActorId())
	if checkErr != nil {
		logger.Error(ctx, "error in checkForAlreadyExistingEntities", zap.Error(checkErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if checkRes.alreadyExists {
		activeLr = checkRes.loanRequest
		activeLse = checkRes.lse
		if activeLr != nil && req.GetCancelCurrentLoanRequest() {
			cancelRes, cancelErr := s.CancelApplication(ctx, &palPb.CancelApplicationRequest{
				LoanRequestId: activeLr.GetId(),
				ActorId:       req.GetActorId(),
				LoanHeader: &palPb.LoanHeader{
					LoanProgram: checkRes.loanProgram,
					Vendor:      checkRes.vendor,
				},
			})
			if cancelErr = epifigrpc.RPCError(cancelRes, cancelErr); cancelErr != nil {
				logger.Error(ctx, "error while cancelling loan request", zap.Error(cancelErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
		} else {
			logger.Info(ctx, "loan request already exists in ApplyForLoan", zap.String("active_vendor", activeLr.GetVendor().String()), zap.String("active_program", activeLr.GetLoanProgram().String()),
				zap.String("new_vendor", loanOffer.GetVendor().String()), zap.String("new_program", loanOffer.GetLoanProgram().String()))
			return &palPb.ApplyForLoanResponse{
				Status:            rpcPb.StatusAlreadyExistsWithDebugMsg("active loan request already exists"),
				ActiveLoanRequest: activeLr,
				ActiveLse:         activeLse,
				RespHeader: &palPb.LoanRespHeader{
					LoanProgram: loanOffer.GetLoanProgram(),
					Vendor:      loanOffer.GetVendor(),
				},
				ActiveLoanAccountId: checkRes.loanAccountId,
			}, nil
		}
	}

	loanRequest, err := s.fillLoanRequest(ctx, req, loanOffer)
	if err != nil {
		logger.Error(ctx, "error in fillLoanRequest", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if checkRes.continueApplication {
		loanRequest.Id = checkRes.loanRequest.Id
		if loanRequest.GetDetails() != nil {
			loanRequest.GetDetails().IsInSyncMode = true
		}
		err = s.loanRequestsDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS,
		})
		if err != nil {
			logger.Error(ctx, "error in updating LR with new loan request details", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		return &palPb.ApplyForLoanResponse{
			Status:        rpcPb.StatusOk(),
			LoanRequestId: loanRequest.GetId(),
			RespHeader: &palPb.LoanRespHeader{
				LoanProgram: loanRequest.GetLoanProgram(),
				Vendor:      loanRequest.GetVendor(),
			},
		}, nil
	}

	loanRequest, err = s.loanRequestsDao.Create(ctx, loanRequest)
	if err != nil {
		if errors.Is(err, epifierrors.ErrDuplicateEntry) {
			logger.Error(ctx, "loan request already created", zap.Error(err))
			res.Status = rpcPb.StatusAlreadyExists()
			return res, nil
		}
		logger.Error(ctx, "error in Create", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// publish pal-loan-request-event
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		if publishErr := s.PublishLoanRequestEventForNudgeExit(ctx, &palPb.PALEvent{
			ActorId:         req.GetActorId(),
			ActionTimestamp: timestampPb.New(time.Now()),
			EventInfo: &palPb.PALEvent_LoanRequestEvent{
				LoanRequestEvent: &palPb.LoanRequestEvent{
					LoanRequestEventDetails: &palPb.LoanRequestEventDetails{
						LoanRequestStatus:    loanRequest.GetStatus(),
						LoanRequestSubStatus: loanRequest.GetSubStatus(),
					},
				},
			},
		}); publishErr != nil {
			logger.Error(ctx, "failed to publish pal event for nudge exit", zap.Error(publishErr))
		}
	})

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendor))
	payload := &palWorkflowPb.LoanApplicationPayload{
		Vendor:      req.GetLoanHeader().GetVendor(),
		LoanProgram: req.GetLoanHeader().GetLoanProgram(),
		LmsPartner:  loanOffer.GetLmsPartner(),
	}
	marPayload, marPayloadErr := protojson.Marshal(payload)
	if marPayloadErr != nil {
		logger.Error(ctx, "failed to marshal workflow payload", zap.Error(marPayloadErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	err = s.initiateWorkflow(ctx, &celestialPb.ClientReqId{
		Id:     loanRequest.GetOrchId(),
		Client: workflow.Client_PRE_APPROVED_LOAN,
	}, loanRequest.GetActorId(), marPayload, workflow.Type_LOAN_APPLICATION, workflow.Version_V0)
	if err != nil {
		logger.Error(ctx, "failed to initiate pre approved loan application workflow", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	return &palPb.ApplyForLoanResponse{
		Status:        rpcPb.StatusOk(),
		LoanRequestId: loanRequest.GetId(),
		RespHeader: &palPb.LoanRespHeader{
			LoanProgram: loanRequest.GetLoanProgram(),
			Vendor:      loanRequest.GetVendor(),
		},
		ActiveLoanRequest: activeLr,
		ActiveLse:         activeLse,
	}, nil
}

func (s *Service) fillLoanRequest(ctx context.Context, req *palPb.ApplyForLoanRequest, loanOffer *palPb.LoanOffer) (*palPb.LoanRequest, error) {
	if loanOffer.GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED {
		if len(loanOffer.GetProcessingInfo().GetInterestRate()) == 0 {
			return nil, errors.New("interest rate not found in processing info")
		}
		return &palPb.LoanRequest{
			ActorId: loanOffer.GetActorId(),
			OfferId: loanOffer.GetId(),
			OrchId:  uuid.New().String(),
			Vendor:  loanOffer.GetVendor(),
			Details: &palPb.LoanRequestDetails{
				LoanInfo: &palPb.LoanRequestDetails_LoanInfo{
					Amount:         loanOffer.GetOfferConstraints().GetMaxLoanAmount(),
					TenureInMonths: loanOffer.GetOfferConstraints().GetMaxTenureMonths(),
					InterestRate:   loanOffer.GetProcessingInfo().GetInterestRate()[0].GetPercentage(),
				},
				LoanOfferExpiry: loanOffer.GetValidTill(),
				ProgramVersion:  loanOffer.GetProcessingInfo().GetLoanProgramVersion(),
			},
			Type:        palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
			Status:      palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
			SubStatus:   palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
			LoanProgram: req.GetLoanHeader().GetLoanProgram(),
		}, nil
	}

	user, err := s.rpcHelper.GetUserByActorId(ctx, req.GetActorId())
	if err != nil {
		return nil, errors.Wrap(err, "GetUserByActorId failed")
	}
	userFeatProp, liteUserErr := helper.GetUserFeatureProperty(ctx, req.GetActorId(), s.onbClient, s.savingsClient)
	if liteUserErr != nil {
		return nil, errors.Wrap(liteUserErr, "failed to check filite user")
	}
	var maskedAccountNumber string
	if userFeatProp.IsFiSAHolder {
		savingsAccount, savErr := s.rpcHelper.GetSavingsAccountDetails(ctx, req.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
		if savErr != nil {
			return nil, errors.Wrap(savErr, "GetSavingsAccountDetails failed")
		}
		accountNumber := savingsAccount.GetAccountNo()

		// Mask account number, slice to only last 4 chars
		length := len(accountNumber)
		if length > 3 {
			maskedAccountNumber = accountNumber[length-4:]
		} else {
			maskedAccountNumber = accountNumber
		}
	}

	var tenure int32
	loanAmount := req.GetLoanAmount()
	// todo(divas) need to move this logic behind vendor provider
	if req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		tenure = 1
	} else if req.GetLoanHeader().GetVendor() == palPb.Vendor_MONEYVIEW || (req.GetLoanHeader().GetVendor() == palPb.Vendor_ABFL && req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION) {
		// for money-view/ABFL PWA, there will not be any offer selection screen , so tenure and loan amount in req
		// will be nil, hence setting them explicitly here so that calculator will not break
		tenure = 12
		loanAmount = moneyPkg.GetZeroInrIfNil(req.GetLoanAmount())
	} else {
		tenure = req.GetTenureInMonths()
	}

	vendorReqId := loanOffer.GetProcessingInfo().GetApplicationId()
	// todo(Anupam): https://monorail.pointz.in/p/fi-app/issues/detail?id=93469
	if req.GetLoanHeader().GetVendor() == palPb.Vendor_ABFL && req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION {
		vendorReqId = loanOffer.GetVendorOfferId()
	}

	currentDate := datetime.TimeToDateInLoc(time.Now(), datetime.IST)
	calculator, err := s.calculatorFactory.GetCalculator(
		ctx,
		calculatorTypes.NewRequest(
			palEnumsPb.CalculatorAccuracy_CALCULATOR_ACCURACY_ACCURATE,
			loanAmount,
			tenure,
			currentDate,
			loanOffer,
			req.GetPledgeDetails(),
		),
	)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting calculator")
	}

	return &palPb.LoanRequest{
		ActorId:         req.GetActorId(),
		OfferId:         req.GetOfferId(),
		OrchId:          uuid.New().String(),
		Vendor:          req.GetLoanHeader().GetVendor(),
		VendorRequestId: vendorReqId,
		// OTP info to be filled by celestial
		// CustomerDeviceId to be filled while Confirm Application
		Details: &palPb.LoanRequestDetails{
			LocationToken: req.GetLocationToken(),
			LoanInfo: &palPb.LoanRequestDetails_LoanInfo{
				Amount:          loanAmount,
				TenureInMonths:  tenure,
				DisbursalAmount: calculator.GetDisbursalAmount(),
				InterestRate:    calculator.GetInterestRate(),
				EmiAmount:       calculator.GetEmiAmount(),
				Deductions: &palPb.LoanRequestDetails_LoanInfo_Deductions{
					TotalDeductions: calculator.GetTotalDeductions(),
					Gst:             calculator.GetProcessingFeeGst(),
					ProcessingFee:   calculator.GetProcessingFee(),
					AdvanceInterest: calculator.GetAdvanceInterest(),
				},
				TotalPayable:      calculator.GetTotalPayable(),
				AprRate:           calculator.GetAprRate(),
				PledgeDetails:     req.GetPledgeDetails(),
				IsDiscountedOffer: loanOffer.GetIsDiscountApplied(),
			},
			PhoneNumber:         user.GetProfile().GetPhoneNumber(),
			MaskedAccountNumber: maskedAccountNumber,
			LoanOfferExpiry:     loanOffer.GetValidTill(),
			ProgramVersion:      loanOffer.GetProcessingInfo().GetLoanProgramVersion(),
		},
		Type:        palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION,
		Status:      palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		SubStatus:   palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
		LoanProgram: req.GetLoanHeader().GetLoanProgram(),
	}, nil
}

func (s *Service) checkForActiveLoanRequestsAndAccounts(ctx context.Context, lh *palPb.LoanHeader, actorId string) (*checkForActiveLoanRequestsAndAccountsResponse, error) {
	res := &checkForActiveLoanRequestsAndAccountsResponse{
		loanProgram: lh.GetLoanProgram(),
		vendor:      lh.GetVendor(),
	}

	// setting this to as epifi_tech as we need to fetch loan requests/accounts from every vendor
	newCtxWithoutOwnership := epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
	loanAccountRes, laErr := s.multiDbProvider.CheckAndGetLoanAccountsForActor(newCtxWithoutOwnership, &multiDbProvider.CheckAndGetLoanAccountsForActorRequest{
		ActorId:         actorId,
		AccountStatuses: []palPb.LoanAccountStatus{palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE},
		LoanPrograms:    []palPb.LoanProgram{},
	})
	if laErr != nil && !errors.Is(laErr, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("%w. could not fetch loan accounts from multiDB provider", laErr)
	}
	if laErr == nil && loanAccountRes != nil {
		for _, la := range loanAccountRes.Accounts {
			logger.Debug(ctx, "active loan account already exists", zap.String("loan_account_id", la.GetId()))
			res.alreadyExists = true
			res.loanAccountId = la.GetId()
			res.vendor = la.GetVendor()
			res.loanProgram = la.GetLoanProgram()
			return res, nil
		}
	}
	lrTypes := []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION}
	appVersionConstraintData := release.NewAppVersionConstraintData(s.DynConf.AutoCancelCurrentLrConfig())
	isAppVersionCompatible, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		return nil, errors.Wrap(appVerErr, "error in checking app version compatibility")
	}
	// added exception for LAMF for backward compatibility. For LAMF we only need to check for active creation requests.
	// also check for eligibility requests only if the auto cancel feature is supported, otherwise there is no way for the user to cancel the eligibility request
	if lh.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_LAMF && isAppVersionCompatible {
		lrTypes = append(lrTypes, palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY)
	}
	loanRequestsRes, lrErr := s.multiDbProvider.CheckAndGetNonTerminalLoanRequests(newCtxWithoutOwnership, &multiDbProvider.CheckAndGetNonTerminalLoanRequestsRequest{
		ActorId: actorId,
		Types:   lrTypes,
	})
	if lrErr != nil && !errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("%w. could not fetch loan requests from multiDB provider", lrErr)
	}
	if lrErr == nil && loanRequestsRes != nil {
		for _, lr := range loanRequestsRes.Requests {
			ctxWithOwner := epificontext.WithOwnership(ctx, helper.GetPalOwnership(lr.GetVendor()))
			latestLse, err := s.loanStepExecutionsDao.GetLatestByRefIdAndFlow(ctxWithOwner, lr.GetId(), helper.GetLseFlowByLrType(lr.GetType()))
			if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error while fetching loan step execution from db", zap.Error(err))
				return nil, fmt.Errorf("%w. could not fetch loan step execution from db", err)
			}
			if lr.GetVendor() == lh.GetVendor() && lr.GetLoanProgram() == lh.GetLoanProgram() {
				if latestLse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_OFFER_SELECTION &&
					latestLse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED || latestLse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS {
					logger.Debug(ctx, "active loan request already exists", zap.String("loan_request_id", lr.GetId()))
					res.loanRequest = lr
					res.continueApplication = true
					res.alreadyExists = false
					return res, nil
				}
			}
			// Explaining from an example -
			// If a user is going through our ES program and LL vendor, and we found same lr for that user
			// then we just allow it normally for the flow. otherwise, we will send the user to the another
			// active loan request landing screen
			logger.Debug(ctx, "active loan request already exists", zap.String("loan_request_id", lr.GetId()))
			res.alreadyExists = true
			res.loanRequest = lr
			res.lse = latestLse
			res.vendor = lr.GetVendor()
			res.loanProgram = lr.GetLoanProgram()
			return res, nil
		}
	}
	res.alreadyExists = false
	return res, nil
}

func (s *Service) verifyOtp(ctx context.Context, req *palPb.ConfirmApplicationRequest) (*palPb.ConfirmApplicationResponse, error) {
	res := &palPb.ConfirmApplicationResponse{}

	loanRequest, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to fetch loan request by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.LoanRequest = loanRequest
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(loanRequest.GetVendor()))
	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})

	if req.GetLoanStepExecutionId() != "" {
		lse, err := s.loanStepExecutionsDao.GetById(ctx, req.GetLoanStepExecutionId())
		if err != nil {
			logger.Error(ctx, "error getting LSE by ID", zap.Error(err))
			return &palPb.ConfirmApplicationResponse{Status: rpcPb.StatusInternal()}, nil
		}
		if loanRequest.GetVendor() == palPb.Vendor_STOCK_GUARDIAN_LSP &&
			lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC {
			logger.Info(ctx, "setting OTP flow to CKYC for backward compatibility during OTP verification for Stock Guardian LSP")
			req.OtpFlowIdentifier = pal_enums.OtpFlow_OTP_FLOW_CKYC_OTP_VERIFCATION
		}
	}

	switch req.GetOtpFlowIdentifier() {
	case pal_enums.OtpFlow_OTP_FLOW_ADD_ALTERNATE_PHONE_NUMBER:
		loanApplicant, err := s.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, req.GetActorId(), req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram(), loanRequest.GetDetails().GetProgramVersion())
		if err != nil {
			logger.Error(ctx, "failed to fetch loan applicant", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		lseFms := []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}
		loanStep, err := s.loanStepExecutionsDao.GetById(ctx, req.GetLoanStepExecutionId())
		if err != nil {
			logger.Error(ctx, "failed to fetch loan step execution by id", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		phNum := loanStep.GetDetails().GetContactabilityDetailsData().GetPhoneNumber()
		verifyRes, err := s.authClient.VerifyOtp(ctx, &authPb.VerifyOtpRequest{
			Device:      req.GetDevice(),
			PhoneNumber: phNum,
			Token:       loanStep.GetDetails().GetContactabilityDetailsData().GetToken(),
			Otp:         req.GetOtp(),
			Mediums:     []commsPb.Medium{commsPb.Medium_SMS},
		})
		if te := epifigrpc.RPCError(verifyRes, err); te != nil {
			// status code above 99 represents invalid OTP, return nil error
			if verifyRes.GetStatus().GetCode() >= 100 {
				loanStep.GetDetails().GetContactabilityDetailsData().LastAttemptTime = timestampPb.New(time.Now().In(datetime.IST))
				loanStep.GetDetails().GetContactabilityDetailsData().OtpAttemptCount += 1
				// the maximum number of attempts has exhausted
				if loanStep.GetDetails().GetContactabilityDetailsData().GetMaxOtpAttempts() <= loanStep.GetDetails().GetContactabilityDetailsData().GetOtpAttemptCount() {
					logger.Info(ctx, "maximum number of OTP tries exhausted per phone number")
					if loanStep.GetDetails().GetContactabilityDetailsData().GetMaxPhoneAttemptAllowed() <= loanStep.GetDetails().GetContactabilityDetailsData().GetPhoneNumberAttemptCount()+1 {
						logger.Info(ctx, "maximum number of OTP tries exhausted for all phone number attempts")
						loanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
						loanStep.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_AUTH_OTP_VERIFICATION_FAILED
						lseFms = append(lseFms, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
						res.Status = rpcPb.StatusPermissionDenied()

						err = s.sendSignalForAlternateContactAddition(ctx, loanStep, loanRequest.GetOrchId())
						if err != nil {
							logger.Error(ctx, "Unable to send signal to workflow", zap.Error(err))
							res.Status = rpcPb.StatusInternal()
							return res, nil
						}
					} else {
						loanStep.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CONTACTABILITY_COOL_OFF_PERIOD
						loanStep.GetDetails().GetContactabilityDetailsData().OtpAttemptCount = 0
						dl, dlErr := deeplinkProvider.GetAlternateContactCoolOffScreen(ctx, deeplinkProvider.GetLoanHeader())
						if dlErr != nil {
							res.Status = rpcPb.StatusInternal()
							return nil, err
						}
						loanRequest.NextAction = dl
						res.Status = rpcPb.StatusTransientFailure()
					}

					lseFms = append(lseFms, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
					if updateErr := s.loanRequestsDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
						palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
					}); updateErr != nil {
						logger.Error(ctx, "failed to update lr next action", zap.Error(updateErr))
					}
				} else {
					res.Status = rpcPb.StatusTransientFailure()
				}
				if updateLseErr := s.loanStepExecutionsDao.Update(ctx, loanStep, lseFms); updateLseErr != nil {
					logger.Error(ctx, "failed to update lse", zap.Error(updateLseErr))
				}
			} else {
				res.Status = rpcPb.StatusTransientFailure()
			}
			res.LoanStep = loanStep
			return res, nil
		}

		// update LSE for succcess otp verification
		loanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		lseFms = append(lseFms, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS)
		successDl, err := deeplinkProvider.GetAlternateContactSuccessScreen(ctx, deeplinkProvider.GetLoanHeader())
		if err != nil {
			res.Status = rpcPb.StatusInternal()
			return nil, err
		}
		loanRequest.NextAction = successDl

		// store alternate phone number in loan applicant
		if loanApplicant.GetPersonalDetails() == nil {
			loanApplicant.PersonalDetails = &palPb.PersonalDetails{AlternatePhoneNumber: phNum}
		}
		loanApplicant.GetPersonalDetails().AlternatePhoneNumber = phNum

		txnExec, err := helper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
		if err != nil {
			logger.Error(ctx, "error in fetching txn executor from ctx", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
			if updateLseErr := s.loanStepExecutionsDao.Update(txnCtx, loanStep, lseFms); updateLseErr != nil {
				return errors.Wrap(updateLseErr, "failed to update lse")
			}
			if updateLrErr := s.loanRequestsDao.Update(txnCtx, loanRequest, []palPb.LoanRequestFieldMask{
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
			}); updateLrErr != nil {
				return errors.Wrap(updateLrErr, "failed to update loan request for next action")
			}
			if updateLaErr := s.loanApplicantDao.Update(txnCtx, loanApplicant, []palPb.LoanApplicantFieldMask{
				palPb.LoanApplicantFieldMask_LOAN_APPLICANT_FIELD_MASK_PERSONAL_DETAILS,
			}); updateLaErr != nil {
				return errors.Wrap(updateLaErr, "failed to update loan applicant")
			}
			return nil
		})
		if txnErr != nil {
			logger.Error(ctx, "failed to update details for alternate contact", zap.Error(txnErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		err = s.sendSignalForAlternateContactAddition(ctx, loanStep, loanRequest.GetOrchId())
		if err != nil {
			logger.Error(ctx, "Unable to send signal to workflow", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.Status = rpcPb.StatusOk()
		return res, nil
	case pal_enums.OtpFlow_OTP_FLOW_CKYC_OTP_VERIFCATION:
		loanStep, err := s.loanStepExecutionsDao.GetById(ctx, req.GetLoanStepExecutionId())
		if err != nil {
			logger.Error(ctx, "failed to get lse", zap.Error(err))
			return &palPb.ConfirmApplicationResponse{
				Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
		verifyOTPRes, err := s.sgKYCClient.VerifyOtp(ctx, &sgKycApiGatewayPb.VerifyOtpRequest{
			ApplicationId: loanStep.GetDetails().GetCkycStepData().GetVendorKycId(),
			Otp:           req.GetOtp(),
		})
		if err = epifigrpc.RPCError(verifyOTPRes, err); err != nil {
			logger.Error(ctx, "error verifying OTP", zap.Error(err))
			status := convertSGKYCVerifyOTPStatus(sgKycApiGatewayPb.VerifyOtpResponse_Status(verifyOTPRes.GetStatus().GetCode()))
			if status == 0 {
				logger.Error(ctx, "status code not handled", zap.String(logger.STATUS_CODE, verifyOTPRes.GetStatus().String()))
				return &palPb.ConfirmApplicationResponse{
					Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error verifying otp: %s", verifyOTPRes.GetStatus().String())),
				}, nil
			}
			return &palPb.ConfirmApplicationResponse{
				Status: &rpcPb.Status{
					Code: uint32(status),
				},
				LoanStep:    loanStep,
				LoanRequest: loanRequest,
				DisplayMsg:  verifyOTPRes.GetDisplayMsg(),
			}, nil
		}
		return &palPb.ConfirmApplicationResponse{
			Status:      rpc.StatusOk(),
			LoanStep:    loanStep,
			LoanRequest: loanRequest,
			DisplayMsg:  verifyOTPRes.GetDisplayMsg(),
		}, nil
	default:
		logger.Info(ctx, "defaulting to original otp verification flow", zap.String(logger.FLOW_ID, req.GetOtpFlow().String()))
		// Do nothing and move to the original flow
	}

	switch req.GetOtpFlow() {
	case palPb.ConfirmApplicationRequest_OTP_FLOW_E_SIGN:
		lse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, loanRequest.GetId(),
			palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS)
		if err != nil {
			logger.Error(ctx, "failed to get loan step execution", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		if lse.GetDetails().GetESignStepData().GetOtpInfo() != nil {
			loanOffer, err := s.loanOffersDao.GetById(ctx, loanRequest.GetOfferId())
			if err != nil {
				logger.Error(ctx, "failed to get loan offer by id", zap.Error(err), zap.String(logger.OFFER_ID, loanRequest.GetOfferId()))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			isVerified, isVerifiedErr := s.rpcHelper.VerifyOtp(ctx, lse, loanRequest, req, loanOffer)
			if isVerifiedErr != nil {
				if errors.Is(isVerifiedErr, epifierrors.ErrFailedPrecondition) {
					logger.Error(ctx, "verify OTP is not allowed", zap.Error(isVerifiedErr))
					res.Status = rpcPb.StatusFailedPrecondition()
					return res, nil
				}
				logger.Error(ctx, "failed to verify Otp", zap.Error(isVerifiedErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			lse.GetDetails().GetESignStepData().GetOtpInfo().Otp = req.GetOtp()
			lse.GetDetails().GetESignStepData().GetOtpInfo().AttemptsCount += 1

			lseFms := []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}
			if !isVerified {
				if lse.GetDetails().GetESignStepData().GetOtpInfo().GetAttemptsCount() >= lse.GetDetails().GetESignStepData().GetOtpInfo().GetMaxAttempts() {
					logger.Info(ctx, "maximum number of OTP tries exhausted")
					lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
					lseFms = append(lseFms, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
					res.Status = rpcPb.StatusPermissionDenied()
					loanRequest.NextAction = deeplinkProvider.GetLoanApplicationConfirmationViaOtpAttemptsExhaustedScreenDeepLink(deeplinkProvider.GetLoanHeader())
					if updateErr := s.loanRequestsDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
						palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
					}); updateErr != nil {
						logger.Error(ctx, "failed to update lr next action", zap.Error(updateErr))
					}

				} else {
					lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_OTP_FAILED
					lseFms = append(lseFms, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
					if updateErr := s.loanStepExecutionsDao.Update(ctx, lse, lseFms); updateErr != nil {
						logger.Error(ctx, "failed to update lse", zap.Error(updateErr))
						res.Status = rpcPb.StatusInternal()
						return res, nil
					}
					res.LoanStep = lse
					res.Status = rpc.NewStatus(uint32(palPb.ConfirmApplicationResponse_INCORRECT_OTP), "Incorrect OTP", "")
					return res, nil
				}
			} else {
				lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
				lseFms = append(lseFms, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
				loanRequest.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), loanRequest.GetId())
				res.Status = rpcPb.StatusOk()
			}

			txnExec, err := helper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
			if err != nil {
				logger.Error(ctx, "error in fetching txn executor from ctx", zap.Error(err))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
				if updateLseErr := s.loanStepExecutionsDao.Update(txnCtx, lse, lseFms); updateLseErr != nil {
					return errors.Wrap(updateLseErr, "failed to update lse")
				}
				if updateLrErr := s.loanRequestsDao.Update(txnCtx, loanRequest, []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				}); updateLrErr != nil {
					return errors.Wrap(updateLrErr, "failed to update loan request for next action")
				}

				return nil
			})
			if txnErr != nil {
				logger.Error(ctx, "failed to update details for E-sign success", zap.Error(txnErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}

			marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&palActivityPb.LoanApplicationESignVerificationSignalPayload{
				Lse: lse,
			})
			if err != nil {
				logger.Error(ctx, "failed to marshal signal with lse", zap.Error(err))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}

			// signal workflow to verify OTP
			if signalErr := s.rpcHelper.SendSignalSync(ctx, loanRequest.GetOrchId(), string(palNs.LoanApplicationESignVerificationSignal), marshalledPayload); signalErr != nil {
				logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
				// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
			}

			return res, nil
		}
		res.Status = rpcPb.StatusInternal()
		return res, nil
	default:
		logger.Error(ctx, "unknown otp flow")
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
}

func convertSGKYCVerifyOTPStatus(status sgKycApiGatewayPb.VerifyOtpResponse_Status) palPb.ConfirmApplicationResponse_Status {
	switch status {
	case sgKycApiGatewayPb.VerifyOtpResponse_STATUS_INVALID_OTP:
		return palPb.ConfirmApplicationResponse_INCORRECT_OTP
	case sgKycApiGatewayPb.VerifyOtpResponse_STATUS_OTP_EXPIRED:
		return palPb.ConfirmApplicationResponse_OTP_EXPIRED
	case sgKycApiGatewayPb.VerifyOtpResponse_STATUS_VERIFICATION_TEMP_BLOCKED:
		return palPb.ConfirmApplicationResponse_TEMP_BLOCKED
	default:
		return palPb.ConfirmApplicationResponse_UNSPECIFIED
	}
}

// ConfirmApplication aka VerifyOTP is called after collecting OTP from customer. It can be used to verify OTP in any flow
// of a loan application.
// Check for the OTP received in request with the OTP stored in loan request details from DB
// If OTP mismatches, return failed validation response
// If OTP is verified, signal celestial to resume workflow for submitting the application and return success response
func (s *Service) ConfirmApplication(ctx context.Context, req *palPb.ConfirmApplicationRequest) (*palPb.ConfirmApplicationResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	if req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_LAMF {
		return s.verifyOtpForSecuredLoans(ctx, req)
	}
	return s.verifyOtp(ctx, req)

}

// CheckForInvalidStatusAndExceededAttempts Checks if Status not in OTP state or Number of retries exceeded
func checkForInvalidStatusAndExceededAttempts(otpInfo *palPb.LoanRequestDetails_OtpInfo, loanRequest *palPb.LoanRequest) error {
	if (loanRequest.GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING) &&
		(loanRequest.GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFICATION_FAILED) {
		return errors.New("status not in OTP state")
	}
	if otpInfo.GetAttemptsCount() >= otpInfo.GetMaxAttempts() {
		return errors.New("number of retries exceeded")
	}
	return nil
}

func (s *Service) updateInvalidOtpAttempt(lockCtx context.Context, loanRequest *palPb.LoanRequest) error {
	attemptCount := loanRequest.GetDetails().GetOtpInfo().GetAttemptsCount()
	maxCount := loanRequest.GetDetails().GetOtpInfo().GetMaxAttempts()
	// If in case of last attempt
	if attemptCount >= maxCount-1 {
		// If number of attempts exceeded
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_OTP_MAX_ATTEMPTS_EXHAUSTED
		loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
		loanRequest.Details.OtpInfo.AttemptsCount = maxCount
	} else {
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFICATION_FAILED
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP
		loanRequest.Details.OtpInfo.AttemptsCount += 1
	}
	// In case of invalid OTP last OTP entered becomes null
	loanRequest.Details.OtpInfo.LastEnteredOtp = ""
	// Update otp entered attempt count
	err := s.loanRequestsDao.Update(lockCtx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
	})
	if err != nil {
		return errors.Wrap(err, "Unable to update Incorrect OTP Attempt Count, Status and Substatus")
	}
	return nil
}

func (s *Service) getSalaryDetailsForPastNMonths(ctx context.Context, actorId string, n int) ([]*salaryPb.SalaryTxnVerificationRequest, error) {
	salaryTxnVerReqsRes, salaryTxnVerReqsErr := s.salaryClient.GetSalaryTxnVerificationRequests(ctx, &salaryPb.GetSalaryTxnVerificationRequestsRequest{
		Filters: &salaryPb.GetSalaryTxnVerificationRequestsRequest_Filters{
			ActorId:  actorId,
			Status:   salaryPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
			FromDate: timestampPb.New(time.Now().In(datetime.IST).AddDate(0, -(n + 1), 0)),
			UptoDate: timestampPb.New(time.Now().In(datetime.IST)),
		},
		SortOrder: salaryPb.SortOrder_DESC,
		PageContext: &rpc.PageContextRequest{
			PageSize: uint32(n),
		},
	})
	if grpcErr := epifigrpc.RPCError(salaryTxnVerReqsRes, salaryTxnVerReqsErr); grpcErr != nil {
		return nil, errors.Wrap(grpcErr, "error getting salary txn verification requests for actor")
	}
	return salaryTxnVerReqsRes.GetSalaryTxnVerificationRequests(), nil
}

// GetLandingInfo API to show the landing info to user
// If user already applied for loan, or has any active loans, returns loan management screen
// If no requests are present, but an offer is found for user, returns loan offer screen
// If no requests and no live offers are present, means user is not eligible for loan, returns status for not eligible
func (s *Service) GetLandingInfo(ctx context.Context, req *palPb.GetLandingInfoRequest) (*palPb.GetLandingInfoResponse, error) {
	res := &palPb.GetLandingInfoResponse{
		EarlySalaryDetails: &palPb.GetLandingInfoResponse_EarlySalaryDetails{
			MinSalaryCredits:        int32(s.conf.EarlySalary.MinSalaryCredits),
			MinDaysPostSalaryCredit: int32(s.conf.EarlySalary.MinDaysPostSalaryCredit),
		},
	}
	pastLoansCount := 0
	if req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		// Get loanAccounts for given actor ID, vendor, and all statuses from db (sent empty status list to get all status)
		loanAccounts, laErr := s.loanAccountsDao.GetByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), palPb.Vendor_VENDOR_UNSPECIFIED, req.GetLoanHeader().GetLoanProgram())
		if laErr != nil && !errors.Is(laErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch loan request from db", zap.Error(laErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		if len(loanAccounts) > 0 {
			ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(loanAccounts[0].GetVendor()))
			for _, la := range loanAccounts {
				if la.GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
					res.ActiveLoanAccount = la
					res.Status = rpcPb.NewStatusWithoutDebug(uint32(palPb.GetLandingInfoResponse_LOAN_DETAILS), "Loan details")
					return res, nil
				} else {
					pastLoansCount += 1
				}
			}
		}
	}
	res.GetEarlySalaryDetails().PastLoansCount = int32(pastLoansCount)
	// Get loanRequests for given actor ID, vendor, and all statuses from db (sent empty status list to get all status)
	duration1d, _ := time.ParseDuration("24h")
	loanRequests, rErr := s.loanRequestsDao.GetByActorIdTypesStatusAndLoanProgram(ctx, req.GetActorId(), []palPb.LoanRequestType{}, []palPb.LoanRequestStatus{}, []palPb.LoanProgram{req.GetLoanHeader().GetLoanProgram()}, &duration1d, nil)
	if rErr != nil && !errors.Is(rErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch loan request from db", zap.Error(rErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if len(loanRequests) > 0 {
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(loanRequests[0].GetVendor()))
	}
	// Get active loanOffer for given actorID, vendor from db
	loanOffer, oErr := s.loanOffersDao.GetActiveOfferByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), palPb.Vendor_VENDOR_UNSPECIFIED, req.GetLoanHeader().GetLoanProgram())
	if oErr != nil && !errors.Is(oErr, epifierrors.ErrRecordNotFound) {
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if loanOffer != nil {
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(loanOffer.GetVendor()))
	}
	// If already taken any loan or applied for any loan, returns Loan Applications status for loan management with new loan offer
	if len(loanRequests) > 0 {
		res.Status = rpcPb.NewStatusWithoutDebug(uint32(palPb.GetLandingInfoResponse_LOAN_APPLICATIONS), "Loan Management")
		res.LoanOffer = loanOffer
		for _, lr := range loanRequests {
			if lr.GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED && lr.GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
				res.ActiveLoanRequest = lr
				break
			}
		}
		return res, nil
	}

	isRiskyUser, riskErr := s.rpcHelper.IsRiskyUser(ctx, req.GetActorId())
	if riskErr != nil {
		logger.Error(ctx, "failed to check risk profile for PL", zap.Error(riskErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY {
		isActive, isActiveErr := s.rpcHelper.IsFullSalaryProgramActive(ctx, req.GetActorId())
		if isActiveErr != nil {
			logger.Error(ctx, "failed to isActive salary user", zap.Error(isActiveErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		if !isActive {
			res.Status = rpcPb.NewStatusWithoutDebug(uint32(palPb.GetLandingInfoResponse_NON_SALARY_USER), "Non salaried user")
			return res, nil
		}
		salaryDetails, salaryDetailsErr := s.getSalaryDetailsForPastNMonths(ctx, req.GetActorId(), s.conf.EarlySalary.MinSalaryCredits)
		if salaryDetailsErr != nil {
			logger.Error(ctx, "failed to isActive salary user", zap.Error(salaryDetailsErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		var salaryTimestamps []*timestampPb.Timestamp
		for _, salary := range salaryDetails {
			salaryTimestamps = append(salaryTimestamps, salary.GetTxnTimestamp())
		}
		res.GetEarlySalaryDetails().PastSalaryTimestamps = salaryTimestamps
		if len(salaryTimestamps) < s.conf.EarlySalary.MinSalaryCredits ||
			(len(salaryTimestamps) > 0 && datetime.IsBefore(timestampPb.New(time.Now().In(datetime.IST)),
				timestampPb.New(datetime.TimestampToTime(salaryTimestamps[0]).In(datetime.IST).AddDate(0, 0, s.conf.EarlySalary.MinDaysPostSalaryCredit)))) {
			res.Status = rpcPb.StatusPermissionDenied()
			return res, nil
		}
	}
	// If no record for loan_request is present, but loan offer is present, return offer status with new offer
	if loanOffer != nil && loanOffer.IsActiveNow() && !isRiskyUser {
		maxAmount, err := moneyPkg.ToPaise(loanOffer.GetOfferConstraints().GetMaxLoanAmount())
		if err != nil {
			logger.Error(ctx, "failed to get max loan amount", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		minAmount, err := helper.GetMinLoanAmountForSliderInPaisa(loanOffer)
		if err != nil {
			logger.Error(ctx, "failed to get min loan amount for slider", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		if maxAmount >= minAmount {
			res.Status = rpcPb.NewStatusWithoutDebug(uint32(palPb.GetLandingInfoResponse_LOAN_OFFER), "Loan Offer")
			res.LoanOffer = loanOffer
			return res, nil
		}
	}

	// If no record for loan_request and no active loans are present. Return Permission denied for not eligible users
	res.Status = rpcPb.StatusPermissionDenied()
	return res, nil
}

// GetLandingInfoV2 API to show the landing info to user
// If user already applied for loan, or has any active loans, returns loan management screen
// If no requests are present, but an offer is found for user, returns loan offer screen
// If no requests and no live offers are present, means user is not eligible for loan, returns status for not eligible
func (s *Service) GetLandingInfoV2(ctx context.Context, req *palPb.GetLandingInfoV2Request) (*palPb.GetLandingInfoV2Response, error) {
	res := &palPb.GetLandingInfoV2Response{
		Status: rpcPb.StatusOk(),
	}
	var err error

	if req.GetLoanHeader().GetVendor() != palPb.Vendor_VENDOR_UNSPECIFIED {
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	}
	skipFailedLR := false
	for _, filter := range req.GetLandingInfoFilters() {
		if filter == palPb.LandingInfoFilter_LANDING_INFO_SKIP_FAILED_LR_FETCH {
			skipFailedLR = true
			break
		}
	}
	dataRes, dataErr := s.landingProvider.GetLandingPageDataForActor(ctx, &landing_provider.GetLandingPageDataForActorRequest{
		ActorId:             req.GetActorId(),
		LoanRequestStatuses: []palPb.LoanRequestStatus{},
		LoanProgram:         req.GetLoanHeader().GetLoanProgram(),
		LoanType:            req.GetLoanType(),
		SkipFailedLR:        skipFailedLR,
	})
	if dataErr != nil {
		logger.Error(ctx, "failed to fetch landing page data from multiDB provider", zap.Error(dataErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res, err = s.getLandingRes(ctx, dataRes, req)
	if err != nil {
		logger.Error(ctx, "error in getLandingRes", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	isA2LData := (dataRes.ActiveLoanAccount != nil && dataRes.ActiveLoanAccount.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND) ||
		(len(dataRes.LoanOptions) != 0 && dataRes.LoanOptions[0].GetLoanOffer().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND) ||
		(len(dataRes.LoanRequests) > 0 && dataRes.LoanRequests[0].GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND)

	// If the loan program in req A2L (since it is hardcoded in onboarding) and user has another offer, GetLandingPageDataForActor will not the other offer
	// that is why we have to check GetLandingPageDataForActor again with empty loan programs, this is being done to ensure backward compatability for A2L
	if !isA2LData && req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND {
		ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
		dataRes, err = s.landingProvider.GetLandingPageDataForActor(ctx, &landing_provider.GetLandingPageDataForActorRequest{
			ActorId:  req.GetActorId(),
			LoanType: req.GetLoanType(),
		})
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch dashboard data from multiDB provider", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res, err = s.getLandingRes(ctx, dataRes, req)
		if err != nil {
			logger.Error(ctx, "error in getDashboardRes", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}
	return res, nil
}

func (s *Service) getLandingRes(ctx context.Context, dataRes *landing_provider.GetLandingPageDataForActorResponse, req *palPb.GetLandingInfoV2Request) (*palPb.GetLandingInfoV2Response, error) {
	res := &palPb.GetLandingInfoV2Response{
		Status: rpcPb.StatusOk(),
	}
	res.LoanHeader = dataRes.LoanHeader
	if dataRes.ActiveLoanAccount != nil {
		res.ActiveLoanAccount = dataRes.ActiveLoanAccount
		res.LandingStatus = rpcPb.NewStatusWithoutDebug(uint32(palPb.GetLandingInfoV2Response_LANDING_STATUS_LOAN_APPLICATIONS), "Loan Management")
		return res, nil
	}

	if len(dataRes.LoanRequests) > 0 {
		res.ActiveLoanRequest = dataRes.LoanRequests[0]
	}

	var activeLoanOffers []*palPb.LoanOffer
	// update the loan offers to update the constraints from loan calculator
	for _, option := range dataRes.LoanOptions {
		if option.GetLoanOffer() == nil {
			continue
		}
		offer := option.GetLoanOffer()
		bc := &basecalculator.Calculator{LoanOffer: offer}
		lc := calculators.GetCalculator(ctx, calculators.GetCalculatorProviderRequest{
			Vendor:      offer.GetVendor(),
			LoanProgram: offer.GetLoanProgram(),
			Offer:       offer,
		}, bc)

		offer.GetOfferConstraints().MinLoanAmount = lc.GetMinLoanAmount()
		activeLoanOffers = append(activeLoanOffers, offer)
	}
	res.LoanOptions = dataRes.LoanOptions
	if len(activeLoanOffers) > 0 {
		res.LoanOffer = activeLoanOffers[0] // Added for backward compatibility
		res.LoanOffers = activeLoanOffers   // Added for backward compatibility
	}

	switch {
	case res.GetActiveLoanRequest() != nil:
		res.LandingStatus = rpcPb.NewStatusWithoutDebug(uint32(palPb.GetLandingInfoResponse_LOAN_APPLICATIONS), "Loan Management")
		return res, nil
	case len(activeLoanOffers) > 0:
		res.LandingStatus = rpcPb.NewStatusWithoutDebug(uint32(palPb.GetLandingInfoV2Response_LANDING_STATUS_LOAN_OFFER), "Loan Offer")
		return res, nil
	}

	if len(res.GetLoanOptions()) > 0 && res.GetLoanOptions()[0].GetEligibilityHeader() != nil {
		// evaluating if user is eligible for non fi core eligibility flow
		nonFiCoreEligEval, evalErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(typesPb.Feature_FEATURE_LOANS_NON_FI_CORE_ELIGIBILITY_V2).WithActorId(req.GetActorId()))
		if evalErr != nil {
			logger.Error(ctx, "error in checking if eligibility v2 is enabled", zap.Error(evalErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		// if feature not enabled and user is non fi core, we need not allow user to enter eligibility flow
		if !nonFiCoreEligEval {
			userFeatProp, err := helper.GetUserFeatureProperty(ctx, req.GetActorId(), s.onbClient, s.savingsClient)
			if err != nil {
				logger.Error(ctx, "error in checking if user is fi lite", zap.Error(err))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}

			if !userFeatProp.IsFiSAHolder {
				res.Status = rpcPb.StatusPermissionDenied()
				return res, nil
			}
		}
		res.LandingStatus = rpcPb.NewStatusWithoutDebug(uint32(palPb.GetLandingInfoV2Response_LANDING_STATUS_CHECK_LOAN_ELIGIBILITY), "Check Loan Eligibility")
		return res, nil
	}
	// If no record for loan_request and no active loans are present. Return Permission denied for not eligible users
	res.Status = rpcPb.StatusPermissionDenied()
	return res, nil
}

func (s *Service) generateKfsSigningOtp(ctx context.Context, device *commontypes.Device, lr *palPb.LoanRequest) (string, error) {
	user, err := s.rpcHelper.GetUserByActorId(ctx, lr.GetActorId())
	if err != nil {
		return "", fmt.Errorf("error while fetching user from actor id, %w", err)
	}

	res, err := s.authClient.GenerateOtp(ctx, &authPb.GenerateOtpRequest{
		Device:          device,
		PhoneNumber:     user.GetProfile().GetPhoneNumber(),
		GenerateOtpFlow: authPb.GenerateOTPFlow_GENERATE_OTP_FLOW_PL_KFS_SIGNING,
		Email:           user.GetProfile().GetEmail(),
		Mediums:         []commsPb.Medium{commsPb.Medium_SMS},
		OtpOptions:      nil,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return "", fmt.Errorf("failed to generate otp, %w", err)
	}

	lse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lr.GetId(),
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS)
	if err != nil {
		return "", fmt.Errorf("failed to fetch kfs loan step, %w", err)
	}

	if lse.GetDetails().GetESignStepData().GetOtpInfo() == nil {
		lse.GetDetails().GetESignStepData().OtpInfo = &palPb.OtpInfo{
			MaxAttempts: 3,
		}
	}
	lse.GetDetails().GetESignStepData().GetOtpInfo().Token = res.GetToken()
	err = s.loanStepExecutionsDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if err != nil {
		return "", fmt.Errorf("failed to update otp token in lse, %w", err)
	}

	return res.GetToken(), nil
}

// GenerateConfirmationCode is called when generate OTP process is initiated or when resend OTP is called
// Collects responses from DB tables loan_request and loan_offer, with respect to loanRequestId received
// Send request to VG to generate OTP and collect and update loan_request table, details with new OTP received from vendor
func (s *Service) GenerateConfirmationCode(ctx context.Context, req *palPb.GenerateConfirmationCodeRequest) (*palPb.GenerateConfirmationCodeResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	if req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_LAMF {
		return s.generateOtpForSecuredLoans(ctx, req)
	}
	loanRequest, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "error while fetching loan request from db", zap.Error(err))
		return &palPb.GenerateConfirmationCodeResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	loanOffer, err := s.loanOffersDao.GetById(ctx, loanRequest.GetOfferId())
	if err != nil {
		logger.Error(ctx, "error while fetching loan offer from db", zap.Error(err))
		return &palPb.GenerateConfirmationCodeResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// The enum OTP_FLOW_CKYC_OTP_VERIFCATION was added in epifi/m204-rc1-May-26 release, hence old clients
	// are not sending this enum.
	// TODO(Brijesh): Remove this logic after a few weeks of rollout of epifi/m204-rc1-May-26
	if req.GetLoanStepExecutionId() != "" {
		lse, err := s.loanStepExecutionsDao.GetById(ctx, req.GetLoanStepExecutionId())
		if err != nil {
			logger.Error(ctx, "error getting LSE by ID", zap.Error(err))
			return &palPb.GenerateConfirmationCodeResponse{Status: rpcPb.StatusInternal()}, nil
		}
		if loanRequest.GetVendor() == palPb.Vendor_STOCK_GUARDIAN_LSP &&
			lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC {
			logger.Info(ctx, "setting OTP flow to CKYC for backward compatibility during OTP generation")
			req.OtpFlow = pal_enums.OtpFlow_OTP_FLOW_CKYC_OTP_VERIFCATION
		}
	}

	switch req.GetOtpFlow() {
	case pal_enums.OtpFlow_OTP_FLOW_ADD_ALTERNATE_PHONE_NUMBER:
		token, tokenErr := s.generateOtpForAlternateContact(ctx, req.GetDevice(), req.GetLoanStepExecutionId())
		if tokenErr != nil {
			logger.Error(ctx, "failed to generate OTP internally using auth service", zap.Error(tokenErr))
			return &palPb.GenerateConfirmationCodeResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		loanSteps, err := s.loanStepExecutionsDao.GetById(ctx, req.GetLoanStepExecutionId())
		if err != nil {
			logger.Error(ctx, "failed to generate OTP internally using auth service", zap.Error(tokenErr))
			return &palPb.GenerateConfirmationCodeResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		return &palPb.GenerateConfirmationCodeResponse{
			Status:            rpcPb.StatusOk(),
			LoanRequest:       loanRequest,
			Token:             token,
			LoanStepExecution: loanSteps,
		}, nil
	case pal_enums.OtpFlow_OTP_FLOW_CKYC_OTP_VERIFCATION:
		var lse *palPb.LoanStepExecution
		lse, err = s.loanStepExecutionsDao.GetById(ctx, req.GetLoanStepExecutionId())
		if err != nil {
			logger.Error(ctx, "failed to get lse")
			return &palPb.GenerateConfirmationCodeResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		var res *sgKycApiGatewayPb.GenerateOtpResponse
		res, err = s.sgKYCClient.GenerateOtp(ctx, &sgKycApiGatewayPb.GenerateOtpRequest{
			ApplicationId: lse.GetDetails().GetCkycStepData().GetVendorKycId(),
		})
		if err = epifigrpc.RPCError(res, err); err != nil {
			logger.Error(ctx, "error generating otp", zap.Error(err), zap.String(logger.STATUS_CODE, res.GetStatus().String()))
			status := convertSGKYCGenerateOTPStatus(sgKycApiGatewayPb.GenerateOtpResponse_Status(res.GetStatus().GetCode()))
			if status == 0 {
				logger.Error(ctx, "status code not handled", zap.String(logger.STATUS_CODE, res.GetStatus().String()))
				return &palPb.GenerateConfirmationCodeResponse{
					Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error generating otp: %s", res.GetStatus().String())),
				}, nil
			}
			return &palPb.GenerateConfirmationCodeResponse{
				Status:            &rpcPb.Status{Code: uint32(status)},
				LoanStepExecution: lse,
				LoanRequest:       loanRequest,
				DisplayMsg:        res.GetDisplayMsg(),
			}, nil
		}
		return &palPb.GenerateConfirmationCodeResponse{
			Status:            rpc.StatusOk(),
			LoanStepExecution: lse,
			LoanRequest:       loanRequest,
			DisplayMsg:        res.GetDisplayMsg(),
		}, nil
	default:
		logger.Info(ctx, "defaulting to original otp generation flow", zap.String(logger.FLOW_ID, req.GetOtpFlow().String()))
		// do nothing and move to original flow
	}

	// For IDFC we need to generate OTP internally
	if shouldGenerateOtpInternally(req, loanOffer) {
		token, tokenErr := s.generateKfsSigningOtp(ctx, req.GetDevice(), loanRequest)
		if tokenErr != nil {
			logger.Error(ctx, "failed to generate OTP internally using auth service", zap.Error(tokenErr))
			return &palPb.GenerateConfirmationCodeResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		// return success response
		return &palPb.GenerateConfirmationCodeResponse{
			Status:      rpcPb.StatusOk(),
			LoanRequest: loanRequest,
			Token:       token,
		}, nil
	}

	_, err = s.rpcHelper.GetConfirmationCodeFromVG(ctx, loanRequest, loanOffer)
	if err != nil {
		logger.Error(ctx, "Unable to get OTP from Vendor", zap.Error(err))
		return &palPb.GenerateConfirmationCodeResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &palPb.GenerateConfirmationCodeResponse{
		Status:      rpcPb.StatusOk(),
		LoanRequest: loanRequest,
	}, nil
}

func convertSGKYCGenerateOTPStatus(status sgKycApiGatewayPb.GenerateOtpResponse_Status) palPb.GenerateConfirmationCodeResponse_Status {
	switch status {
	case sgKycApiGatewayPb.GenerateOtpResponse_STATUS_UNSPECIFIED:
		return palPb.GenerateConfirmationCodeResponse_STATUS_UNSPECIFIED
	case sgKycApiGatewayPb.GenerateOtpResponse_STATUS_MOBILE_NOT_REGISTERED,
		sgKycApiGatewayPb.GenerateOtpResponse_STATUS_AUTH_FACTOR_MISMATCH:
		return palPb.GenerateConfirmationCodeResponse_STATUS_FAILED
	case sgKycApiGatewayPb.GenerateOtpResponse_STATUS_OTP_RESEND_BLOCKED:
		return palPb.GenerateConfirmationCodeResponse_STATUS_OTP_RESEND_BLOCKED
	case sgKycApiGatewayPb.GenerateOtpResponse_STATUS_GENERATION_TEMP_BLOCKED:
		return palPb.GenerateConfirmationCodeResponse_STATUS_TEMP_BLOCKED
	default:
		return palPb.GenerateConfirmationCodeResponse_STATUS_UNSPECIFIED
	}
}

func shouldGenerateOtpInternally(req *palPb.GenerateConfirmationCodeRequest, loanOffer *palPb.LoanOffer) bool {
	return req.GetLoanHeader().GetVendor() == palPb.Vendor_IDFC ||
		(req.GetLoanHeader().GetVendor() == palPb.Vendor_LIQUILOANS && req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY && loanOffer.GetLmsPartner() == palEnumsPb.LmsPartner_LMS_PARTNER_FINFLUX)
}

func (s *Service) generateOtpForAlternateContact(ctx context.Context, device *commontypes.Device, lse string) (string, error) {
	loanSteps, err := s.loanStepExecutionsDao.GetById(ctx, lse)
	if err != nil {
		return "", errors.Wrap(err, fmt.Sprintf("error in fetching loan step execution"))
	}
	phNum := loanSteps.GetDetails().GetContactabilityDetailsData().GetPhoneNumber()

	res, err := s.authClient.GenerateOtp(ctx, &authPb.GenerateOtpRequest{
		Device:          device,
		PhoneNumber:     phNum,
		GenerateOtpFlow: authPb.GenerateOTPFlow_GENERATE_OTP_FLOW_LOAN_ALTERNATE_CONTACT_NUMBER,
		Mediums:         []commsPb.Medium{commsPb.Medium_SMS},
		OtpOptions:      nil,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return "", fmt.Errorf("failed to generate otp, %w", err)
	}

	if loanSteps.GetDetails().GetContactabilityDetailsData().GetMaxOtpAttempts() == 0 {
		loanSteps.GetDetails().GetContactabilityDetailsData().MaxOtpAttempts = 3
	}

	if loanSteps.GetDetails().GetContactabilityDetailsData().GetMaxPhoneAttemptAllowed() == 0 {
		loanSteps.GetDetails().GetContactabilityDetailsData().MaxPhoneAttemptAllowed = 3
	}

	loanSteps.GetDetails().GetContactabilityDetailsData().Token = res.GetToken()
	err = s.loanStepExecutionsDao.Update(ctx, loanSteps, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
	})
	if err != nil {
		return "", errors.Wrap(err, "error in updating loan step execution in generateOtpForAlternateContact")
	}

	return res.GetToken(), nil
}

func (s *Service) SaveContactDetails(ctx context.Context, req *palPb.SaveContactDetailsRequest) (*palPb.SaveContactDetailsResponse, error) {
	res := &palPb.SaveContactDetailsResponse{Status: rpcPb.StatusOk()}
	loanStep, err := s.loanStepExecutionsDao.GetById(ctx, req.GetLseId())
	if err != nil {
		logger.Error(ctx, "error in loanStepExecutionsDao.GetById", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanRequest, err := s.loanRequestsDao.GetById(ctx, loanStep.GetRefId())
	if err != nil {
		logger.Error(ctx, "error in getting loan request by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	userRes, err := s.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: loanStep.GetActorId(),
		},
	})

	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting user details", zap.Error(rpcErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// If the primary phone number is same as alternate phone return failed precondition so that frontend
	//  service can handle appropriate error view
	if userRes.GetUser().GetProfile().GetPhoneNumber().ToString() == req.GetPhoneNumber().ToString() {
		res.Status = rpcPb.StatusFailedPrecondition()
		return res, nil
	}

	if loanStep.GetDetails() == nil {
		loanStep.Details = &palPb.LoanStepExecutionDetails{}
	}

	if loanStep.GetDetails().GetContactabilityDetailsData() == nil {
		loanStep.GetDetails().Details = &palPb.LoanStepExecutionDetails_ContactabilityDetailsData{ContactabilityDetailsData: &palPb.ContactabilityDetailsData{
			PhoneNumber:            req.GetPhoneNumber(),
			MaxPhoneAttemptAllowed: 3,
		}}
		loanStep.GetDetails().GetContactabilityDetailsData().PhoneNumberAttemptCount += 1
	} else {
		if loanStep.GetDetails().GetContactabilityDetailsData().GetPhoneNumber().ToString() != req.GetPhoneNumber().ToString() {
			loanStep.GetDetails().GetContactabilityDetailsData().PhoneNumberAttemptCount += 1
			loanStep.GetDetails().GetContactabilityDetailsData().OtpAttemptCount = 0
		}
		loanStep.GetDetails().GetContactabilityDetailsData().PhoneNumber = req.GetPhoneNumber()
	}

	if loanStep.GetDetails().GetContactabilityDetailsData().GetMaxPhoneAttemptAllowed() != 0 &&
		loanStep.GetDetails().GetContactabilityDetailsData().GetMaxPhoneAttemptAllowed() < loanStep.GetDetails().GetContactabilityDetailsData().GetPhoneNumberAttemptCount() {
		loanStep.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		if err := s.loanStepExecutionsDao.Update(ctx, loanStep, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		}); err != nil {
			logger.Error(ctx, "Unable to update loanStepExecution in DB", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		err = s.sendSignalForAlternateContactAddition(ctx, loanStep, loanRequest.GetOrchId())
		if err != nil {
			logger.Error(ctx, "Unable to send signal to workflow", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		res.Status = rpcPb.StatusPermissionDenied()
		return res, nil
	}

	err = s.loanStepExecutionsDao.Update(ctx, loanStep, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
	})
	if err != nil {
		logger.Error(ctx, "Unable to update loanStepExecution in DB", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})

	dl, err := deeplinkProvider.GetAlternateContactVerificationViaOtpScreen(ctx, deeplinkProvider.GetLoanHeader(), loanStep)
	if err != nil {
		logger.Error(ctx, "Unable to fetch GetAlternateContactVerificationViaOtpScreen", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.Deeplink = dl

	return res, nil
}

func (s *Service) GetDashboard(ctx context.Context, req *palPb.GetDashboardRequest) (*palPb.GetDashboardResponse, error) {
	res := &palPb.GetDashboardResponse{}
	checkUserEligibility := true
	loanRequests, err := s.loanRequestsDao.GetByActorIdVendorStatusAndLoanProgram(ctx, req.GetActorId(), palPb.Vendor_VENDOR_UNSPECIFIED, []palPb.LoanRequestStatus{}, req.GetLoanHeader().GetLoanProgram())
	var loanSteps []*palPb.LoanStepExecution
	var recentLoanRequest *palPb.LoanRequest
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in loanRequestsDao.GetByActorIdVendorStatusAndLoanProgram", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if len(loanRequests) > 0 {
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(loanRequests[0].GetVendor()))

		loanSteps, err = s.loanStepExecutionsDao.GetByRefIdAndStatuses(ctx, loanRequests[0].GetId(), nil)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in loanStepExecutionsDao.GetByRefIdAndStatuses", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		if loanRequests[0].GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
			recentLoanRequest = loanRequests[0]
		}

		if err = s.handleStatusForFrontend(ctx, recentLoanRequest); err != nil {
			logger.Error(ctx, "Error in Handle FrontEnd Status", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	loanAccounts, err := s.loanAccountsDao.GetByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), palPb.Vendor_VENDOR_UNSPECIFIED, req.GetLoanHeader().GetLoanProgram())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in loanAccountsDao.GetByActorIdVendorAndLoanProgram", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if len(loanAccounts) > 0 {
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(loanAccounts[0].GetVendor()))
		// Checks if user can take another loan, if one loan has already been taken
		// if one loan already active, not eligible for another loan
		if loanAccounts[0].GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			checkUserEligibility = false
			recentLoanRequest = nil
		}
	}
	if checkUserEligibility {
		latestOffer, latestOfferErr := s.loanOffersDao.GetActiveOfferByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), palPb.Vendor_VENDOR_UNSPECIFIED, req.GetLoanHeader().GetLoanProgram())
		if latestOfferErr != nil {
			if errors.Is(latestOfferErr, epifierrors.ErrRecordNotFound) {
				checkUserEligibility = false
			} else {
				logger.Error(ctx, "error in checking if Active Offer Valid for actor id and loan program", zap.Error(latestOfferErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
		}
		if latestOffer != nil {
			ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(latestOffer.GetVendor()))
		}
		if latestOffer != nil && latestOffer.GetOfferConstraints().GetMaxLoanAmount() != nil && latestOffer.GetOfferConstraints().GetMinLoanAmount() != nil {
			maxAmount, maxConvErr := moneyPkg.ToPaise(latestOffer.GetOfferConstraints().GetMaxLoanAmount())
			if maxConvErr != nil {
				logger.Error(ctx, "error in converting max loan amount to paisa", zap.Error(maxConvErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			minAmount, minConvErr := helper.GetMinLoanAmountForSliderInPaisa(latestOffer)
			if minConvErr != nil {
				logger.Error(ctx, "error in converting min loan amount to paisa", zap.Error(minConvErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			if maxAmount < minAmount {
				checkUserEligibility = false
			}
		}

		if latestOffer == nil || !latestOffer.IsActiveNow() {
			checkUserEligibility = false
		}
	}

	res.Status = rpcPb.StatusOk()
	res.RecentLoanRequest = recentLoanRequest
	loanInfoList := make([]*palPb.LoanInfo, 0)
	for _, la := range loanAccounts {
		loanInstallmentInfos, liiErr := s.loanInstallmentInfoDao.GetByAccountIdAndStatuses(ctx, la.GetId(), []palPb.LoanInstallmentInfoStatus{})
		if liiErr != nil && !errors.Is(liiErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in loanInstallmentInfoDao.GetByAccountIdAndStatuses", zap.Error(liiErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		var installmentInfos []*palPb.InstallmentInfo
		var loanInstallmentInfoIds []string
		installmentInfo := &palPb.InstallmentInfo{}
		for _, lii := range loanInstallmentInfos {
			loanInstallmentInfoIds = append(loanInstallmentInfoIds, lii.GetId())
			installmentInfo.LoanInstallmentInfo = helper.RoundLoanInstallmentInfo(lii)
		}
		if len(loanInstallmentInfoIds) > 0 {
			loanInstallmentPayouts, lipErr := s.loanInstallmentPayoutDao.GetByLoanInstallmentInfoIds(ctx, loanInstallmentInfoIds)
			if lipErr != nil && !errors.Is(lipErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error in loanInstallmentPayoutDao.GetByLoanInstallmentInfoIds", zap.Error(lipErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			for _, lip := range loanInstallmentPayouts {
				helper.RoundLoanInstallmentPayout(lip)
			}
			installmentInfo.LoanInstallmentPayouts = loanInstallmentPayouts
		}
		installmentInfos = append(installmentInfos, installmentInfo)
		loanInfoList = append(loanInfoList, &palPb.LoanInfo{
			LoanAccount:         helper.RoundLoanAccount(la),
			InstallmentInfoList: installmentInfos,
		})
	}

	res.LoanSteps = loanSteps
	res.RecentLoanRequest, err = helper.RoundLoanRequest(res.GetRecentLoanRequest())
	if err != nil {
		logger.Error(ctx, "Error in Rounding Loan Request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.CheckUserEligibility = checkUserEligibility
	res.LoanInfoList = loanInfoList
	return res, nil
}

//nolint:govet
func (s *Service) GetDashboardV2(ctx context.Context, req *palPb.GetDashboardRequest) (*palPb.GetDashboardResponse, error) {
	res := &palPb.GetDashboardResponse{
		Status: rpcPb.StatusOk(),
	}

	dataRes, err := s.landingProvider.GetLandingPageDataForActor(ctx, &landing_provider.GetLandingPageDataForActorRequest{
		ActorId:                 req.GetActorId(),
		LoanProgram:             req.GetLoanHeader().GetLoanProgram(),
		FetchEligibilityRequest: req.GetFetchEligibilityRequest(),
	})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch dashboard data from multiDB provider", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res, err = s.getDashboardRes(ctx, dataRes, req)
	if err != nil {
		logger.Error(ctx, "error in getDashboardRes", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	isA2LData := (dataRes.ActiveLoanAccount != nil && dataRes.ActiveLoanAccount.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND) ||
		(len(dataRes.LoanOptions) != 0 && dataRes.LoanOptions[0].GetLoanOffer().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND) ||
		(len(dataRes.LoanRequests) > 0 && dataRes.LoanRequests[0].GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND)

	// If the loan program is A2L and user has a NON_FI_CORE_STPL offer and we are getting loan program as A2L (since it is hardcoded in onboarding), GetLandingPageDataForActor will not return NON_FI_CORE_STPL offer
	// that is why we have to check GetLandingPageDataForActor again with empty loan programs
	if !isA2LData && req.GetLoanHeader().GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND {
		ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
		dataRes, err = s.landingProvider.GetLandingPageDataForActor(ctx, &landing_provider.GetLandingPageDataForActorRequest{
			ActorId: req.GetActorId(),
		})
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch dashboard data from multiDB provider", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res, err = s.getDashboardRes(ctx, dataRes, req)
		if err != nil {
			logger.Error(ctx, "error in getDashboardRes", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	return res, nil
}

func (s *Service) getDashboardRes(ctx context.Context, dataRes *landing_provider.GetLandingPageDataForActorResponse, req *palPb.GetDashboardRequest) (*palPb.GetDashboardResponse, error) {
	res := &palPb.GetDashboardResponse{
		Status: rpcPb.StatusOk(),
	}

	// active loan account is nil and active offer is nil and loan requests is nil then
	// if user is non fi core, call GetLandingPageDataForActor again without any program

	if dataRes.ActiveLoanAccount != nil {
		la := dataRes.ActiveLoanAccount

		// Populated detail about EMI paid for the found loan accounts.
		loanInfoList := make([]*palPb.LoanInfo, 0)
		loanInstallmentInfos, liiErr := s.loanInstallmentInfoDao.GetByAccountIdAndStatuses(ctx, la.GetId(), []palPb.LoanInstallmentInfoStatus{})
		if liiErr != nil && !errors.Is(liiErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in loanInstallmentInfoDao.GetByAccountIdAndStatuses", zap.Error(liiErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		var installmentInfos []*palPb.InstallmentInfo
		var loanInstallmentInfoIds []string
		installmentInfo := &palPb.InstallmentInfo{}
		for _, lii := range loanInstallmentInfos {
			loanInstallmentInfoIds = append(loanInstallmentInfoIds, lii.GetId())
			installmentInfo.LoanInstallmentInfo = helper.RoundLoanInstallmentInfo(lii)
		}
		if len(loanInstallmentInfoIds) > 0 {
			loanInstallmentPayouts, lipErr := s.loanInstallmentPayoutDao.GetByLoanInstallmentInfoIds(ctx, loanInstallmentInfoIds)
			if lipErr != nil && !errors.Is(lipErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error in loanInstallmentPayoutDao.GetByLoanInstallmentInfoIds", zap.Error(lipErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			for _, lip := range loanInstallmentPayouts {
				helper.RoundLoanInstallmentPayout(lip)
			}
			installmentInfo.LoanInstallmentPayouts = loanInstallmentPayouts
		}
		installmentInfos = append(installmentInfos, installmentInfo)
		loanInfoList = append(loanInfoList, &palPb.LoanInfo{
			LoanAccount:         helper.RoundLoanAccount(la),
			InstallmentInfoList: installmentInfos,
		})
		res.LoanInfoList = loanInfoList
		return res, nil
	}

	// The multiDbProvider.GetLandingPageDataForActor takes care of returning the appropriate loan offer based on the
	// state of loan request. So we just need to set the loan offer & loan request if they exist.
	res.LoanOptions = dataRes.LoanOptions
	for _, lo := range res.LoanOptions {
		if lo.GetLoanOffer() == nil {
			continue
		}
		res.ActiveLoanOffer = lo.GetLoanOffer()
		res.CheckUserEligibility = true
		break
	}
	if len(dataRes.LoanRequests) > 0 {
		res.RecentLoanRequest = dataRes.LoanRequests[0]
		roundedLr, err := helper.RoundLoanRequest(res.GetRecentLoanRequest())
		if err != nil {
			logger.Error(ctx, "Error in Rounding Loan Request", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.RecentLoanRequest = roundedLr

		loanSteps, err := s.loanStepExecutionsDao.GetByRefIdAndStatuses(ctx, res.RecentLoanRequest.GetId(), nil)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error in loanStepExecutionsDao.GetByRefIdAndStatuses", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		if err = s.handleStatusForFrontend(ctx, res.RecentLoanRequest); err != nil {
			logger.Error(ctx, "Error in Handle FrontEnd Status", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.LoanSteps = loanSteps
		// Don't return here because loan offer is set in the following section.
	}
	loec, err := s.loecDao.GetByActorIdLoanProgramsAndStatuses(ctx, req.GetActorId(), []palPb.LoanProgram{req.GetLoanHeader().GetLoanProgram()}, []palPb.LoanOfferEligibilityCriteriaStatus{}, 0, true)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in loanStepExecutionsDao.GetByActorIdLoanProgramsAndStatuses", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if len(loec) > 0 {
		res.Loec = loec[0]
	}
	return res, nil
}

func (s *Service) handleStatusForFrontend(ctx context.Context, recentLoanRequest *palPb.LoanRequest) error {
	switch {
	// Check if Status and Substatus is Pending on Liveness
	// Check if VKYC stage skipped or not.
	// If VKYC not skipped, update the status to liveness after VKYC
	case recentLoanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING && recentLoanRequest.GetSubStatus() == palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS:
		isVkycDone, err := s.isVkycFromPreApprovedLoan(ctx, recentLoanRequest)
		if err != nil {
			return errors.Wrap(err, "Error in if is Vkyc From PreApprovedLoan")
		}
		if isVkycDone {
			recentLoanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS_AFTER_VKYC
		}
	// Check if Status and Substatus is Verified VKYC
	// Check if VKYC stage skipped or not.
	// If VKYC skipped, update the status and substatus to liveness pending
	case recentLoanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED && recentLoanRequest.GetSubStatus() == palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_VKYC:
		isVkycDone, err := s.isVkycFromPreApprovedLoan(ctx, recentLoanRequest)
		if err != nil {
			return errors.Wrap(err, "Error in if is Vkyc From PreApprovedLoan")
		}
		if !isVkycDone {
			recentLoanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING
			recentLoanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_LIVENESS
		}
	default:
		return nil
	}
	return nil
}

func (s *Service) GetLivenessStatus(ctx context.Context, req *palPb.GetLivenessStatusRequest) (*palPb.GetLivenessStatusResponse, error) {
	res := &palPb.GetLivenessStatusResponse{}
	loanRequest, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &palPb.GetLivenessStatusResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching loan request from db", zap.Error(err))
		return &palPb.GetLivenessStatusResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if loanRequest.GetActorId() != req.GetActorId() {
		logger.Error(ctx, "actor id mismatch", zap.String("expected", loanRequest.GetActorId()), zap.String("actual", req.GetActorId()))
		return &palPb.GetLivenessStatusResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}
	livenessAttempt, err := s.rpcHelper.GetLivenessAttempt(ctx, loanRequest.GetActorId())
	if err != nil {
		logger.Error(ctx, "GetLivenessAttempt failed", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.LoanRequest = loanRequest
	res.LivenessAttempt = livenessAttempt
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) CancelApplication(ctx context.Context, req *palPb.CancelApplicationRequest) (*palPb.CancelApplicationResponse, error) {
	res := &palPb.CancelApplicationResponse{}

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	loanRequest, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "Unable to get loanRequest by Id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if loanRequest.GetActorId() != req.GetActorId() {
		logger.Error(ctx, "actor id mismatch", zap.String("expected", loanRequest.GetActorId()), zap.String("actual", req.GetActorId()))
		return &palPb.CancelApplicationResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}

	// If loan request is already raised with the vendor, i.e. is in INITIATED state or loan request has already
	// reached a terminal state, the user cannot cancel the request.
	// In this case, the CTA to cancel will also not be visible to the user.
	if (loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED) ||
		(loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS) ||
		(loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_DISBURSED) ||
		(loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED) ||
		(loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED) {
		res.Status = rpcPb.StatusFailedPrecondition()
		return res, nil
	}

	loanStepExecs, err := s.loanStepExecutionsDao.GetByRefIdAndStatuses(ctx, loanRequest.GetId(), []palPb.LoanStepExecutionStatus{
		palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
		palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
	})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "Unable to get loanStepExecution by Ref Id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED
	loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CANCELLED
	loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))

	err = s.loanRequestsDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
	})
	if err != nil {
		logger.Error(ctx, "failed to update loan request with cancelled status", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// Mark the latest loan step cancelled on a best effort basis
	// LSEs fetched are returned in descending order of created at
	if len(loanStepExecs) > 0 {
		latestLse := loanStepExecs[0]
		latestLse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED
		if err = s.loanStepExecutionsDao.Update(ctx, latestLse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		}); err != nil {
			logger.Error(ctx, fmt.Sprintf("failed to cancel lse: step name: %v", latestLse.GetStepName()), zap.Error(err))
		}
	}

	if loanRequest.GetOfferId() != "" {
		loanOffer, loErr := s.loanOffersDao.GetById(ctx, loanRequest.GetOfferId())
		if loErr != nil {
			logger.Error(ctx, "failed to get offer by ID in cancel application", zap.Error(loErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.OfferId = loanOffer.GetId()
		res.LoanOffer = loanOffer
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) InitiateESign(ctx context.Context, req *palPb.InitiateESignRequest) (*palPb.InitiateESignResponse, error) {
	res := &palPb.InitiateESignResponse{}

	loanRequest, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &palPb.InitiateESignResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching loan request from db", zap.Error(err))
		return &palPb.InitiateESignResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if loanRequest.GetActorId() != req.GetActorId() {
		logger.Error(ctx, "actor id mismatch", zap.String("expected", loanRequest.GetActorId()), zap.String("actual", req.GetActorId()))
		return &palPb.InitiateESignResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}

	loanOffer, err := s.loanOffersDao.GetById(ctx, loanRequest.GetOfferId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &palPb.InitiateESignResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching loan offer from db", zap.Error(err))
		return &palPb.InitiateESignResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// we are fetching pre_kfs first as per the federal use case,
	// if that is not present in DB then we will fetch the KFS LSE
	// by the below code which will work for federal V1 flow
	var loanStepExecution *palPb.LoanStepExecution
	var preKfsErr error
	if req.GetLoanHeader().GetVendor() == palPb.Vendor_FEDERAL {
		loanStepExecution, preKfsErr = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS)
		if preKfsErr != nil && !errors.Is(preKfsErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch loan step execution by ref ID, flow and step name", zap.Error(preKfsErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}
	if req.GetLoanHeader().GetVendor() != palPb.Vendor_FEDERAL || errors.Is(preKfsErr, epifierrors.ErrRecordNotFound) {
		loanStepExecution, err = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS)
		if err != nil {
			logger.Error(ctx, "failed to fetch loan step execution by ref ID, flow and step name", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	// TODO(prasoon): only do the below if sign url not expired
	if loanStepExecution != nil &&
		loanStepExecution.GetDetails() != nil &&
		loanStepExecution.GetDetails().GetESignStepData() != nil &&
		loanStepExecution.GetDetails().GetESignStepData().GetSignUrl() != "" {
		res.Status = rpcPb.StatusOk()
		res.SignUrl = loanStepExecution.GetDetails().GetESignStepData().GetSignUrl()
		res.ExitUrl = s.conf.KfsExitUrl
		return res, nil
	}

	eSignUrl, expiredAt, err := s.rpcHelper.InitiateESign(ctx, req.GetActorId(), loanStepExecution.GetOrchId(), loanRequest, loanOffer)
	if err != nil {
		logger.Error(ctx, "Enable to Initiate Esign from client(pal/rpc helper)", zap.Error(err))
		return &palPb.InitiateESignResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	loanStepExecution.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
	loanStepExecution.Details = &palPb.LoanStepExecutionDetails{
		Details: &palPb.LoanStepExecutionDetails_ESignStepData{
			ESignStepData: &palPb.ESignStepData{
				SignUrl:  eSignUrl,
				ExpiryAt: expiredAt,
			},
		},
	}
	err = s.loanStepExecutionsDao.Update(ctx, loanStepExecution, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
	})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "Unable to update loanStepExecution in DB", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// TODO(@prasoon/harish): Signal Workflow to initiate KFS

	res.Status = rpcPb.StatusOk()
	res.SignUrl = eSignUrl
	res.ExitUrl = s.conf.KfsExitUrl
	return res, nil
}

func (s *Service) GetLoanDetails(ctx context.Context, req *palPb.GetLoanDetailsRequest) (*palPb.GetLoanDetailsResponse, error) {
	res := &palPb.GetLoanDetailsResponse{}

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	loanAccount, err := s.loanAccountsDao.GetById(ctx, req.GetLoanId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error record not found while fetching loan account from db", zap.Error(err))
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "error while fetching loan account from db", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if loanAccount.GetActorId() != req.GetActorId() {
		logger.Error(ctx, "actor id mismatch", zap.String("expected", loanAccount.GetActorId()), zap.String("actual", req.GetActorId()))
		return &palPb.GetLoanDetailsResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}
	loanInstallmentInfo, err := s.loanInstallmentInfoDao.GetByActiveAccountId(ctx, loanAccount.GetId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error record not found while fetching loan installment info from db", zap.Error(err))
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "error while fetching loan installment info from db", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanRequests, err := s.loanRequestsDao.GetByLoanAccountIdAndType(ctx, loanAccount.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
	if err != nil || len(loanRequests) == 0 {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error record not found while fetching loan request from db", zap.Error(err))
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "error while fetching loan request from db", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanOffer, err := s.loanOffersDao.GetById(ctx, loanRequests[0].GetOfferId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error record not found while fetching loan offer from db", zap.Error(err))
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "error while fetching loan offer from db", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// only need to fetch outstanding balances for vendors for which we do not update LMS daily
	if req.GetLoanHeader().GetVendor() == palPb.Vendor_FEDERAL || req.GetLoanHeader().GetVendor() == palPb.Vendor_IDFC ||
		req.GetLoanHeader().GetVendor() == palPb.Vendor_ABFL || req.GetLoanHeader().GetVendor() == palPb.Vendor_LENDEN {
		outstandingLoanAmountProvider := s.loanDataProvider.FetchOutstandingLoanAmountProvider(ctx, &palPb.LoanHeader{
			LoanProgram: loanAccount.GetLoanProgram(),
			Vendor:      loanAccount.GetVendor(),
		}, loanAccount.GetLmsPartner())
		if outstandingLoanAmountProvider != nil {
			outstandingAmountResponse, outStandingErr := outstandingLoanAmountProvider.GetOutstandingLoanAmount(ctx, &providers.GetOutstandingLoanAmountRequest{
				LoanAccountId: loanAccount.GetId(),
			})
			if outStandingErr != nil {
				logger.Error(ctx, "could not calculate outstanding amount for the user", zap.Error(outStandingErr),
					zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()), zap.Any(logger.VENDOR, loanAccount.GetVendor()))
				// intentionally not returning from here. Can show stale data to user
			}
			if outstandingAmountResponse != nil {
				loanAccount.GetLoanAmountInfo().OutstandingAmount = outstandingAmountResponse.GetOutstandingLoanAmount()
			}
		} else {
			logger.WarnWithCtx(ctx, "error in getting the outstanding amount provider from loan data provider",
				zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()), zap.Any(logger.VENDOR, loanAccount.GetVendor()))
			// intentionally not returning from here. Can show stale data to user
		}
	}

	loanForeClosureProvider, err := s.loanDataProvider.FetchLoanForeClosureDetailsProvider(ctx, req.GetLoanHeader(), loanAccount.GetLmsPartner())
	if err != nil {
		logger.Error(ctx, "failed to fetch loan data provider", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	var preclosureDetails *palPb.GetLoanDetailsResponse_PreClosureDetails
	if loanForeClosureProvider != nil {
		foreclosureDetails, foreclosureErr := loanForeClosureProvider.FetchLoanPreClosureDetailsFromVendor(ctx, loanAccount)
		if foreclosureErr != nil {
			// not sending the error from here intentionally if we not receive the response from the vendor, as we need
			// to show the EMI amount at the prepay screen instead of not showing him the preapay option
			logger.Error(ctx, "failed to fetch loan pre close amount and charges from vendor", zap.Error(foreclosureErr), zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()), zap.Any(logger.VENDOR, loanAccount.GetVendor()))
		} else {
			preclosureDetails = &palPb.GetLoanDetailsResponse_PreClosureDetails{
				LoanPreCloseAmount:  foreclosureDetails.LoanPreCloseAmount,
				LoanPreCloseCharges: foreclosureDetails.LoanPreCloseCharges,
			}
		}
	}

	loanCancellationDetailsProvider, err := s.loanDataProvider.FetchLoanCancellationDetailsProvider(ctx, req.GetLoanHeader())
	if err != nil {
		logger.Error(ctx, "failed to fetch loanCancellationDetailsProvider", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	var cancellationDetails *palPb.LoanCancellationDetails
	if loanCancellationDetailsProvider != nil {
		cancellationDetailsRes, cancelDetailsErr := loanCancellationDetailsProvider.FetchLoanCancellationDetailsFromVendor(ctx, loanAccount)
		if cancelDetailsErr != nil {
			// not sending the error from here intentionally if we don't receive the response from the vendor, as we need
			// to show the EMI amount at the prepay screen instead of not showing them the preapay option
			logger.Error(ctx, "failed to fetch loan cancellation amount from vendor", zap.Error(cancelDetailsErr), zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()), zap.Any(logger.VENDOR, loanAccount.GetVendor()))
		} else {
			cancellationDetails = &palPb.LoanCancellationDetails{
				IsCancellationAllowed: cancellationDetailsRes.IsCancellationAllowed,
				CancellationAmount:    cancellationDetailsRes.LoanCancellationAmount,
			}
		}
	}

	preClosureProvider, err := s.preClosureFactory.GetPreClosureProvider(ctx, &preclose.GetPreClosureProviderRequest{
		LoanHeader: req.GetLoanHeader(),
	})
	if err != nil && !errors.Is(err, preclose.ErrUnhandledVendor) {
		logger.Error(ctx, "failed to get pre closure provider", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if preClosureProvider != nil {
		isClosureAllowedRes := preClosureProvider.IsClosureAllowed(ctx, &precloseProviders.IsClosureAllowedRequest{
			LoanAccountId: loanAccount.GetId(),
		})
		if preclosureDetails == nil {
			preclosureDetails = &palPb.GetLoanDetailsResponse_PreClosureDetails{}
		}
		preclosureDetails.IsPreCloseBlocked = isClosureAllowedRes.IsClosureBlocked
		preclosureDetails.PreCloseBlockedReason = isClosureAllowedRes.Reason
	}

	loanActivities, err := s.loanActivityDao.GetByAccountIdAndTypesAndCount(ctx, req.GetLoanId(), []palPb.LoanActivityType{
		palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_EMI,
		palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LUMPSUM,
		palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LATE_FEE,
		palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT,
	}, 1)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching loan offer from db", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if len(loanActivities) > 0 {
		res.Transaction, err = s.getTransactionFromLoanActivity(ctx, loanActivities[0], &palPb.LoanHeader{
			LoanProgram: loanAccount.GetLoanProgram(),
			Vendor:      loanAccount.GetVendor(),
		})
		if err != nil {
			logger.Error(ctx, "error while fetching transaction from loan activity", zap.Error(err))
		}
	}

	res.LoanInstallmentPayouts, err = s.loanInstallmentPayoutDao.GetByLoanInstallmentInfoId(ctx, loanInstallmentInfo.GetId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to get loan installment payouts by installment info id", zap.Error(err), zap.String(logger.ID, loanInstallmentInfo.GetId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// fetch mandate lse details only if given vendor program is enabled for alt account flow
	if loanUtils.IsVendorProgramEnabledForAltAccFlow(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()) {
		// fetch lse for mandate step to fetch account details used during mandate stage
		lse, lseErr := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, loanRequests[0].GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE)
		if lseErr != nil {
			logger.Error(ctx, "error while fetching mandate lse from db", zap.Error(lseErr), zap.Any(logger.LOAN_REQUEST_ID, loanRequests[0].GetId()))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		// if this field is populated in lse that means alternate account flow was on for mandate stage for this actor
		// send these details in response
		// TODO(Diparth): Check if we need to populate banking details shown on loan details screen from mandate request table
		// This is purely used for showing purpose on app and it is common logic used across LL and IDFC alt account flow
		// so not changing for now, since we are still going to write details in LSE and not completely move away immediately
		if lse.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed() != nil {
			accDetails := lse.GetDetails().GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed()
			res.DisbursalAndEmiAccountDetails = &palPb.GetLoanDetailsResponse_DisbursalAndEmiAccountDetails{
				LastFourDigitAccountNumber: accDetails.GetAccountNumber()[len(accDetails.GetAccountNumber())-4:],
				BankName:                   accDetails.GetBankName(),
			}
		}
	}

	// update next emi amount and date if vendor is Federal
	if loanAccount.GetVendor() == palPb.Vendor_FEDERAL {
		goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
			loanDetails, vgErr := s.rpcHelper.GetVgLoanDetails(ctx, loanAccount.GetActorId(), loanAccount.GetAccountNumber())
			if vgErr != nil {
				logger.Error(ctx, "failed to fetch loan details from vendor", zap.Error(vgErr))
				return
			}

			if loanInstallmentInfo.GetNextInstallmentDate().GetYear() != loanDetails.GetNextPayDate().GetYear() ||
				loanInstallmentInfo.GetNextInstallmentDate().GetMonth() != loanDetails.GetNextPayDate().GetMonth() ||
				loanInstallmentInfo.GetNextInstallmentDate().GetDay() != loanDetails.GetNextPayDate().GetDay() {
				loanInstallmentInfo.NextInstallmentDate = loanDetails.GetNextPayDate()
				loanInstallmentInfo.GetDetails().NextEmiAmount = loanDetails.GetNextPayAmount()

				if upErr := s.loanInstallmentInfoDao.Update(ctx, loanInstallmentInfo, []palPb.LoanInstallmentInfoFieldMask{
					palPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_DETAILS,
					palPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_NEXT_INSTALLMENT_DATE,
				}); upErr != nil {
					logger.Error(ctx, "failed to update loan next installment date and amount", zap.Error(upErr))
				}
			}

			fedVgAccountStatusMap := map[palVgPb.FetchLoanDetailsResponse_LoanStatus]palPb.LoanAccountStatus{
				palVgPb.FetchLoanDetailsResponse_LOAN_STATUS_CLOSED: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED,
				palVgPb.FetchLoanDetailsResponse_LOAN_STATUS_ACTIVE: palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
			}

			laStatus, ok := fedVgAccountStatusMap[loanDetails.GetLoanStatus()]
			if ok {
				loanAccount.Status = laStatus
				loanAccount.LoanEndDate = loanDetails.GetEndDate()
				loanAccount.GetDetails().InterestRate = loanDetails.GetInterestRate()
				laUpdateErr := s.loanAccountsDao.Update(ctx, loanAccount, []palPb.LoanAccountFieldMask{
					palPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_STATUS,
					palPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_LOAN_END_DATE,
					palPb.LoanAccountFieldMask_LOAN_ACCOUNT_FIELD_MASK_DETAILS,
				})
				if laUpdateErr != nil {
					logger.Error(ctx, "failed to update loan account status", zap.Error(laUpdateErr))
				}
			}
		})
	}

	res.LoanAccount = helper.RoundLoanAccount(loanAccount)
	res.LoanInstallmentInfo = helper.RoundLoanInstallmentInfo(loanInstallmentInfo)
	res.LoanRequest = loanRequests[0]
	res.LoanOffer = loanOffer
	res.LoanRequest, err = helper.RoundLoanRequest(res.GetLoanRequest())
	res.PreClosureDetails = preclosureDetails
	res.LoanCancellationDetails = cancellationDetails
	if err != nil {
		logger.Error(ctx, "Error in Rounding Loan Request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

// InitialiseKfsStage method to initiate an e-sign request with the bank.
// The first step in KFS E-signing is e-sign by vendor and only after that will the user be allowed to e-sign the document.
// So idea is to begin the vendor e-sign before KYC so that by the time the user reaches e-sign state, the vendor has
// already e-signed the document.
// This method creates a loan step execution for e-sign and initiates e-sign with the vendor. The activity will be called
// in an async fashion and not block the loan application workflow.
// Note that we won't be updating loan request as part of this activity.
func (s *Service) initialiseKfsStage(ctx context.Context, loanRequest *palPb.LoanRequest, loanOffer *palPb.LoanOffer) error {
	// Create a loan step execution for KFS
	loanStepExec := &palPb.LoanStepExecution{
		ActorId:   loanRequest.GetActorId(),
		RefId:     loanRequest.GetId(),
		Flow:      palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
		OrchId:    uuid.New().String(),
		StepName:  palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
		Status:    palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
		SubStatus: palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED,
	}

	loanStepExec, createErr := s.loanStepExecutionsDao.Create(ctx, loanStepExec)
	if createErr != nil {
		return createErr
	}

	eSignUrl, expiry, err := s.rpcHelper.InitiateESign(ctx, loanRequest.GetActorId(), loanStepExec.GetOrchId(), loanRequest, loanOffer)
	if err != nil {
		logger.Error(ctx, "initiate kfs eSign failed", zap.Error(err))
		// return fmt.Errorf("failed to initate e-sign request for KFS, %w", err)
	}

	loanStepExec.Details = &palPb.LoanStepExecutionDetails{
		Details: &palPb.LoanStepExecutionDetails_ESignStepData{
			ESignStepData: &palPb.ESignStepData{
				SignUrl:  eSignUrl,
				ExpiryAt: expiry,
			},
		}}
	updateErr := s.loanStepExecutionsDao.Update(ctx, loanStepExec, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
	})
	if updateErr != nil {
		logger.Error(ctx, "failed to update e-sign URL in laon step execution", zap.Error(updateErr))
		return updateErr
	}
	return nil
}

// Method to check if there is any entry in loanStepExecution for VKYC for the referenceId.
// Used to know if a user has gone through KYC flow through PreApproved or not
func (s *Service) isVkycFromPreApprovedLoan(ctx context.Context, loanRequest *palPb.LoanRequest) (bool, error) {
	_, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, loanRequest.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return false, nil
		}
		return false, errors.Wrap(err, "Error in Getting loanStepExecution by refId, Flow and Name")
	}
	return true, nil
}

func (s *Service) GetLoanActivityStatus(ctx context.Context, req *palPb.GetLoanActivityStatusRequest) (*palPb.GetLoanActivityStatusResponse, error) {
	res := &palPb.GetLoanActivityStatusResponse{}

	loanPaymentReq, err := s.loanPaymentRequestDao.GetByOrchId(ctx, req.GetRefId())
	if err != nil {
		logger.Error(ctx, "error while fetching loan payment request from db", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	var activityStatus palPb.GetLoanActivityStatusResponse_ActivityStatus
	// Check if already in terminal state, then avoid calling order again
	switch loanPaymentReq.GetStatus() {
	case palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_SUCCESS:
		activityStatus = palPb.GetLoanActivityStatusResponse_COMPLETED
	case palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_FAILED:
		activityStatus = palPb.GetLoanActivityStatusResponse_FAILED
	default:
		activityStatus, _, err = s.rpcHelper.GetOrderAndTxn(ctx, req.GetRefId())
		if err != nil && activityStatus != palPb.GetLoanActivityStatusResponse_UNSPECIFIED {
			logger.Error(ctx, "failed to get order", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	res.Status = rpcPb.StatusOk()
	res.ActivityStatus = activityStatus
	res.LoanAccountId = loanPaymentReq.GetAccountId()
	return res, nil
}

func (s *Service) initiateWorkflowV2(ctx context.Context, clientReqId *workflowPb.ClientReqId, actorId string,
	payload []byte, workflowType *workflowPb.TypeEnum, version workflowPb.Version) error {
	ow := epificontext.OwnershipFromContext[context.Context](ctx)

	// Using best-effort QOS to bypass SQS queues during remote debugging, allowing direct workflow initiation
	var qos celestialPb.QoS
	if cfg.IsNonProdEnv(s.DynConf.Application().Environment) && cfg.IsRemoteDebugEnabled() {
		qos = celestialPb.QoS_BEST_EFFORT
	}
	initiateWorkflowRes, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId:          actorId,
			Version:          version,
			Type:             workflowType,
			Payload:          payload,
			ClientReqId:      clientReqId,
			Ownership:        ow,
			QualityOfService: qos,
		},
	})
	if te := epifigrpc.RPCError(initiateWorkflowRes, err); te != nil {
		if initiateWorkflowRes.GetStatus().IsAlreadyExists() {
			return epifierrors.ErrAlreadyExists
		}
		return fmt.Errorf("error while initiating workflow for client req id %s, %w", clientReqId.GetId(), te)
	}
	return nil
}

// PrePayLoan is used to make loan pre-payment/pre-closure payment
// 1. Loan Payment Request Creation
// 2. Create Fund Transfer Order
// 3. Initiate Workflow
func (s *Service) PrePayLoan(ctx context.Context, req *palPb.PrePayLoanRequest) (*palPb.PrePayLoanResponse, error) {
	if req.GetLoanHeader().GetVendor() != palPb.Vendor_FEDERAL {
		return s.PrePayLoanV2(ctx, req)
	}
	// for federal
	res := &palPb.PrePayLoanResponse{}
	requestType := palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM
	loanPaymentRequest, err := s.loanPaymentRequestDao.Create(ctx, &palPb.LoanPaymentRequest{
		ActorId:   req.GetActorId(),
		AccountId: req.GetLoanId(),
		OrchId:    uuid.New().String(),
		Amount:    req.GetAmount(),
		Details:   &palPb.LoanPaymentRequestDetails{},
		Type:      requestType,
		Status:    palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
		SubStatus: palPb.LoanPaymentRequestSubStatus_LOAN_PAYMENT_REQUEST_SUB_STATUS_UNSPECIFIED,
	})
	if err != nil {
		logger.Error(ctx, "error in Creating loan payment request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanAccount, err := s.loanAccountsDao.GetById(ctx, req.GetLoanId())
	if err != nil {
		logger.Error(ctx, "error in fetching loan account", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	savingsAccount, err := s.rpcHelper.GetSavingsAccountDetails(ctx, req.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
	if err != nil {
		logger.Error(ctx, "error while fetching savings account by actor", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanPi, err := s.getPi(ctx, loanAccount.GetAccountNumber(), loanAccount.GetIfscCode())
	if err != nil {
		logger.Error(ctx, "error while fetching loan PI", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// since this flow is for federal only, checking if user has not selected federal savings account for pre-pay
	// if not, then only payment protocol will be UPI which is handled below
	paymentProtocol := paymentPb.PaymentProtocol_INTRA_BANK
	var payerUserIdentifier *payPb.UserIdentifier
	var payeeUserIdentifier *payPb.UserIdentifier
	// PayerUserIdentifier will not be nil for Tpap flow
	if req.GetPayerUserIdentifier() != nil {
		// here assumption is that user can only select  bank accounts for making payment through Tpap
		// for loans, hence req.GetPayerUserIdentifier().GetAccountId() will always has some account Id
		if req.GetPayerUserIdentifier().GetAccountId() != savingsAccount.GetId() {
			paymentProtocol = paymentPb.PaymentProtocol_UPI
		}
		payerUserIdentifier = &payPb.UserIdentifier{
			ActorId:     req.GetPayerUserIdentifier().GetActorId(),
			AccountId:   req.GetPayerUserIdentifier().GetAccountId(),
			AccountType: req.GetPayerUserIdentifier().GetAccountType(),
		}
		payeeUserIdentifier = &payPb.UserIdentifier{
			ActorId: req.GetPayerUserIdentifier().GetActorId(),
			PiId:    loanPi.GetId(),
		}
	}
	orderRes, orderErr := s.payClient.CreateFundTransferOrder(ctx, &payPb.CreateFundTransferOrderRequest{
		PayerActorId:                 req.GetActorId(),
		PayeeActorId:                 req.GetActorId(),
		Amount:                       req.GetAmount(),
		Provenance:                   orderPb.OrderProvenance_INTERNAL,
		Workflow:                     orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		Tags:                         []orderPb.OrderTag{orderPb.OrderTag_LOAN},
		ClientRequestId:              loanPaymentRequest.GetOrchId(),
		Remarks:                      "PrePay Loan",
		HardPreferredPaymentProtocol: paymentProtocol,
		PayerIdentifier:              payerUserIdentifier,
		PayeeIdentifier:              payeeUserIdentifier,
	})
	if te := epifigrpc.RPCError(orderRes, orderErr); te != nil {
		logger.Error(ctx, "CreateFundTransferOrder failed", zap.Error(te), zap.String(logger.REQUEST_ID, loanPaymentRequest.GetId()))
		if orderRes.GetPayErrorCodeForPayer() != "" {
			res.Status = rpc.NewStatus(uint32(palPb.PrePayLoanResponse_KNOWN_ERROR_FROM_PAY), "KNOWN_ERROR_FROM_PAY", "")
			res.PayErrorCodeForPayer = orderRes.GetPayErrorCodeForPayer()
			return res, nil
		}
		res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("pay CreateFundTransferOrder failed: %v", te.Error()))
		return res, nil
	}
	if len(orderRes.GetTxnAttributes()) == 0 {
		logger.Error(ctx, "txn attributes not present", zap.String(logger.REQUEST_ID, loanPaymentRequest.GetId()))
		res.Status = rpc.StatusInternalWithDebugMsg("txn attributes not present in pay response")
		return res, nil
	}
	res.TransactionAttribute = orderRes.GetTxnAttributes()[0]

	savingsPi, err := s.getPi(ctx, savingsAccount.GetAccountNo(), savingsAccount.GetIfscCode())
	if err != nil {
		logger.Error(ctx, "error while fetching savings PI", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// these two will be used for Tpap flow for building FE txn attributes in PrePayLoan rpc, for eg : can be Upi-id
	res.TransactionAttribute.PayeePaymentInstrument = orderRes.GetTxnAttributes()[0].GetPayeePaymentInstrument()
	res.TransactionAttribute.PayerPaymentInstrument = orderRes.GetTxnAttributes()[0].GetPayerPaymentInstrument()
	// these two will be used for intra bank transfer only for building FE txn attributes in PrePayLoan rpc,
	res.TransactionAttribute.PayeePaymentInstrumentId = loanPi.GetId()
	res.TransactionAttribute.PayerPaymentInstrumentId = savingsPi.GetId()
	res.TransactionAttribute.CredRequiredType = orderRes.GetTxnAttributes()[0].GetCredRequiredType()

	switch req.GetLoanHeader().GetVendor() {
	case palPb.Vendor_LIQUILOANS, palPb.Vendor_IDFC:
		payload := &palWorkflowPb.LoanPrePayPayload{
			Vendor:      req.GetLoanHeader().GetVendor(),
			LoanProgram: req.GetLoanHeader().GetLoanProgram(),
			RequestType: requestType,
		}
		marPayload, marPayloadErr := protojson.Marshal(payload)
		if marPayloadErr != nil {
			logger.Error(ctx, "failed to marshal workflow payload", zap.Error(marPayloadErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		wfErr := s.initiateWorkflowV2(ctx, &workflow.ClientReqId{Id: loanPaymentRequest.GetOrchId(), Client: workflow.Client_PRE_APPROVED_LOAN}, loanAccount.GetActorId(), marPayload, celestialPkg.GetTypeEnumFromWorkflowType(palNs.LoanPrePay), workflow.Version_V0)
		if wfErr != nil {
			logger.Error(ctx, "failed to initiate loan pre-pay", zap.Error(wfErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	default:
		err = s.initiateWorkflow(ctx, &celestialPb.ClientReqId{
			Id:     loanPaymentRequest.GetOrchId(),
			Client: workflow.Client_PRE_APPROVED_LOAN,
		}, loanPaymentRequest.GetActorId(), nil, workflow.Type_PRE_APPROVED_LOAN_PRE_PAY, workflow.Version_V0)
		if err != nil {
			logger.Error(ctx, "error while Initiating Pre-Pay Workflow", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

	}

	res.ReferenceId = loanPaymentRequest.GetOrchId()
	res.OrderId = orderRes.GetOrder().GetId()
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) getPi(ctx context.Context, accountNumber, ifscCode string) (*piPb.PaymentInstrument, error) {
	piRes, err := s.piClient.GetPi(ctx, &piPb.GetPiRequest{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.GetPiRequest_AccountRequestParams_{AccountRequestParams: &piPb.GetPiRequest_AccountRequestParams{
			ActualAccountNumber: accountNumber,
			IfscCode:            ifscCode,
		}},
	})
	if te := epifigrpc.RPCError(piRes, err); te != nil {
		return nil, fmt.Errorf("failed to fetch pi by acount number, %w", te)
	}
	return piRes.GetPaymentInstrument(), nil
}

func (s *Service) GetAllTransactions(ctx context.Context, req *palPb.GetAllTransactionsRequest) (*palPb.GetAllTransactionsResponse, error) {
	res := &palPb.GetAllTransactionsResponse{}

	loanActivities, err := s.loanActivityDao.GetByAccountIdAndTypesAndCount(ctx, req.GetLoanAccountId(), []palPb.LoanActivityType{
		palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_EMI,
		palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LUMPSUM,
		palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LATE_FEE,
		palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT,
	}, 0)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching loan offer from db", zap.Error(err), zap.String(logger.VENDOR, req.GetLoanHeader().GetVendor().String()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanAccount, err := s.loanAccountsDao.GetById(ctx, req.GetLoanAccountId())
	if err != nil {
		logger.Error(ctx, "error in fetching loan account", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if loanAccount.GetActorId() != req.GetActorId() {
		logger.Error(ctx, "actor id mismatch", zap.String("expected", loanAccount.GetActorId()), zap.String("actual", req.GetActorId()))
		return &palPb.GetAllTransactionsResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}

	lh := &palPb.LoanHeader{
		LoanProgram: loanAccount.GetLoanProgram(),
		Vendor:      loanAccount.GetVendor(),
	}
	transactions := make([]*palPb.TransactionActivity, 0)
	for _, loanActivity := range loanActivities {
		transaction, err := s.getTransactionFromLoanActivity(ctx, loanActivity, lh)
		if err != nil {
			logger.Error(ctx, "error while fetching transaction from loan activity", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		transactions = append(transactions, transaction)
	}
	res.Transactions = transactions
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) getTransactionFromLoanActivity(ctx context.Context, loanActivity *palPb.LoanActivity, loanHeader *palPb.LoanHeader) (*palPb.TransactionActivity, error) {
	transaction := &palPb.TransactionActivity{}
	transaction.LoanActivityId = loanActivity.GetId()
	transaction.TxnParticulars = loanActivity.GetDetails().GetTxnParticulars()
	transaction.PaymentTimestamp = loanActivity.GetDetails().GetTxnTime()
	transaction.LoanHeader = loanHeader
	// NOTE: utr and timestamp can be fetched from ReferenceId only for transactions made through Fi app
	// we also have off app transactions maintained in loan activities so failure from the following method is expected for those activities.
	utr, paymentTimestamp, utrErr := s.rpcHelper.GetUtrAndPaymentTimestampFromTransactionId(ctx, loanActivity.GetReferenceId())
	if utrErr != nil {
		logger.Error(ctx, "failed to get utr and payment timestamp from transactionId", zap.Error(utrErr))
		transaction.Utr = loanActivity.GetDetails().GetUtr()
	} else {
		transaction.Utr = utr
		transaction.PaymentTimestamp = paymentTimestamp
	}
	switch loanActivity.GetType() {
	case palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_EMI:
		loanInstallmentInfos, err := s.loanInstallmentInfoDao.GetByAccountIdAndStatuses(ctx, loanActivity.GetLoanAccountId(), []palPb.LoanInstallmentInfoStatus{
			palPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
			palPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_COMPLETED,
			palPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_CLOSED,
		})
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, fmt.Errorf("error while fetching loan installment infos from db")
		}
		var installmentIds []string
		for index := range loanInstallmentInfos {
			installmentIds = append(installmentIds, loanInstallmentInfos[index].GetId())
		}
		emiCount, err := s.loanInstallmentPayoutDao.GetCountByInstallmentInfoIds(ctx, installmentIds)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			return nil, fmt.Errorf("error while getting count of loan installment payouts by loan installment info ids from db")
		}

		if loanActivity.GetDetails().GetEmiActivityDetails().GetEmiDueDate() != nil {
			transaction.PaymentTimestamp = datetime.DateToTimestamp(loanActivity.GetDetails().GetEmiActivityDetails().GetEmiDueDate(), datetime.IST)
		}
		transaction.Status = palPb.TransactionActivity_SUCCESS
		transaction.Details = &palPb.TransactionActivity_Details{
			Type:      palPb.TransactionActivity_Details_EMI,
			Amount:    loanActivity.GetDetails().GetAmount(),
			EmiNumber: int32(emiCount),
		}
	case palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LUMPSUM:
		transaction.Status = palPb.TransactionActivity_SUCCESS
		transaction.Details = &palPb.TransactionActivity_Details{
			Type:   palPb.TransactionActivity_Details_LUMPSUM,
			Amount: loanActivity.GetDetails().GetAmount(),
		}
	case palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT:
		transaction.Status = palPb.TransactionActivity_SUCCESS
		transaction.Details = &palPb.TransactionActivity_Details{
			Type:   palPb.TransactionActivity_Details_DISBURSEMENT,
			Amount: loanActivity.GetDetails().GetAmount(),
		}
	default:
		return transaction, nil
	}
	return transaction, nil
}

func (s *Service) GetTransactionReceipt(ctx context.Context, request *palPb.GetTransactionReceiptRequest) (*palPb.GetTransactionReceiptResponse, error) {
	res := &palPb.GetTransactionReceiptResponse{}

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(request.GetLoanHeader().GetVendor()))

	loanActivity, err := s.loanActivityDao.GetById(ctx, request.GetLoanActivityId())
	if err != nil {
		logger.Error(ctx, "Unable to get LoanActivity by ID", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanAccount, err := s.loanAccountsDao.GetById(ctx, loanActivity.GetLoanAccountId())
	if err != nil {
		logger.Error(ctx, "Unable to get Loan Account by ID", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	transaction, err := s.getTransactionFromLoanActivity(ctx, loanActivity, &palPb.LoanHeader{
		LoanProgram: loanAccount.GetLoanProgram(),
		Vendor:      loanAccount.GetVendor(),
	})
	if err != nil {
		logger.Error(ctx, "error while fetching transaction from loan activity", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.Transaction = transaction
	res.LoanName = loanAccount.GetDetails().GetLoanName()
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetLoanSummaryForHome(ctx context.Context, req *palPb.GetLoanSummaryForHomeRequest) (*palPb.GetLoanSummaryForHomeResponse, error) {
	res := &palPb.GetLoanSummaryForHomeResponse{}
	dataExistenceRes := s.dataExistenceManager.GetOrRefreshLoanDataExistenceCache(ctx, req.GetActorId())
	dataRes, dataErr := s.landingProvider.GetLandingPageDataForActor(ctx,
		&landing_provider.GetLandingPageDataForActorRequest{
			ActorId:                    req.GetActorId(),
			LoanRequestStatuses:        []palPb.LoanRequestStatus{},
			OwnershipFilterMap:         dataExistenceRes.GetDataExistenceMap(),
			OwnershipFilterMapForLoecs: dataExistenceRes.GetLoecDataExistenceMap(),
		})
	if dataErr != nil {
		logger.Error(ctx, "failed to fetch landing page data from multiDB provider", zap.Error(dataErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	switch {
	case dataRes.ActiveLoanAccount != nil && dataRes.ActiveLoanAccount.GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE:
		var loanInfos []*palPb.GetLoanSummaryForHomeResponse_LoanAccountInfo
		loanAccount := dataRes.ActiveLoanAccount
		loanInstallmentInfo, loanInstallmentInfoErr := s.loanInstallmentInfoDao.GetByActiveAccountId(
			epificontext.WithOwnership(ctx, helper.GetPalOwnership(loanAccount.GetVendor())), loanAccount.GetId())
		if loanInstallmentInfoErr != nil && !storage.IsRecordNotFoundError(loanInstallmentInfoErr) {
			logger.Error(ctx, "failed to fetch loan installment from GetByActiveAccountId", zap.Error(loanInstallmentInfoErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		loanInfos = append(loanInfos, &palPb.GetLoanSummaryForHomeResponse_LoanAccountInfo{
			LoanAccount:         loanAccount,
			LoanInstallmentInfo: helper.RoundLoanInstallmentInfo(loanInstallmentInfo),
		})

		bankDetails, err := s.rpcHelper.GetMandateBankAccountDetails(ctx, loanAccount)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to get mandate acc details", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		isIfscBelongToEpifi, err := vendorPkg.CheckIfIfscBelongsToEpifi(commonvgpb.Vendor_FEDERAL_BANK, bankDetails.GetIfsc())
		if err != nil {
			logger.Error(ctx, "failed to check if ifsc belongs to epifi", zap.Error(err))
		}

		emiTimelineDetails, _ := helper.GetEmiTimelineDetails(s.DynConf, loanInstallmentInfo.GetDetails().GetGracePeriod(), loanInstallmentInfo.GetNextInstallmentDate(), loanAccount.GetVendor())
		emiTimelineState, ok := EmiTimelineMap[emiTimelineDetails]
		if !ok {
			emiTimelineState = palPb.GetLoanSummaryForHomeResponse_EMI_TIMELINE_STATE_UNSPECIFIED
		}
		res.HomeCardType = palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_LOAN_ACCOUNT_ACTIVE
		res.HomeCardData = &palPb.GetLoanSummaryForHomeResponse_CardActiveLoanAccount_{
			CardActiveLoanAccount: &palPb.GetLoanSummaryForHomeResponse_CardActiveLoanAccount{
				LoanAccountInfos:     loanInfos,
				EmiTimelineState:     emiTimelineState,
				IsMandateOnFiAccount: isIfscBelongToEpifi,
			},
		}
	case len(dataRes.LoanRequests) > 0:
		loanRequest := dataRes.LoanRequests[0]
		if loanRequest.IsNonTerminal() {
			// for ongoing loan application (in non-terminal state)
			res.HomeCardType = palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_LOAN_APPLICATION
			res.HomeCardData = &palPb.GetLoanSummaryForHomeResponse_CardLoanApplication_{CardLoanApplication: &palPb.GetLoanSummaryForHomeResponse_CardLoanApplication{LoanRequest: loanRequest}}
		} else {
			var loanOffer *palPb.LoanOffer
			if len(dataRes.LoanOptions) != 0 && dataRes.LoanOptions[0].GetLoanOffer() != nil {
				loanOffer = dataRes.LoanOptions[0].GetLoanOffer()
			}
			res = s.getHomeCardloanOffer(loanOffer, nil, false)
		}
	case len(dataRes.LoanOptions) > 0 && dataRes.LoanOptions[0].GetEligibilityHeader() != nil:
		res.HomeCardType = palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_CHECK_ELIGIBILITY
	default:
		var loanOffer *palPb.LoanOffer
		if len(dataRes.LoanOptions) != 0 && dataRes.LoanOptions[0].GetLoanOffer() != nil {
			loanOffer = dataRes.LoanOptions[0].GetLoanOffer()
		}
		res = s.getHomeCardloanOffer(loanOffer, nil, false)
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) getHomeCardloanOffer(loanOffer *palPb.LoanOffer, loanRequest *palPb.LoanRequest, pastLoansApplicationRecord bool) *palPb.GetLoanSummaryForHomeResponse {
	res := &palPb.GetLoanSummaryForHomeResponse{}

	if loanOffer != nil {
		if loanRequest == nil || (loanRequest != nil && !loanRequest.IsNonTerminal()) {
			res.HomeCardType = palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_LOAN_OFFER
		} else {
			res.HomeCardType = palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_NO_OFFER
		}
	} else {
		res.HomeCardType = palPb.GetLoanSummaryForHomeResponse_HOME_CARD_TYPE_NO_OFFER
	}
	res.HomeCardData = &palPb.GetLoanSummaryForHomeResponse_CardLoanOffer_{CardLoanOffer: &palPb.GetLoanSummaryForHomeResponse_CardLoanOffer{
		LoanOffer:                  loanOffer,
		PastLoansApplicationRecord: pastLoansApplicationRecord,
	}}
	return res
}

func (s *Service) getEmiTimelineState(startTime, endTime time.Time, nextInstallmentDate *date.Date) palPb.GetLoanSummaryForHomeResponse_EmiTimelineState {
	// this will check if current time is between pre emi due period when pre-pay is not allowed
	// for eg: For LL if EMI is 5th of every month, we will check for 3rd and 4th date of that month if current date is between that
	if datetime.IsBetweenTimestamp(timestampPb.Now().AsTime().In(datetime.IST), startTime, datetime.DateToTimeV2(nextInstallmentDate, datetime.IST)) {
		return palPb.GetLoanSummaryForHomeResponse_EMI_TIMELINE_STATE_PRE_DUE
	}
	// checking if current time is before next installment date, in this case pre-pay is allowed
	if time.Now().In(datetime.IST).Before(datetime.DateToTimeV2(nextInstallmentDate, datetime.IST)) {
		return palPb.GetLoanSummaryForHomeResponse_EMI_TIMELINE_STATE_EARLY_PRE_PAYMENT
	}

	return palPb.GetLoanSummaryForHomeResponse_EMI_TIMELINE_STATE_UNSPECIFIED
}

func (s *Service) AddAddressDetails(ctx context.Context, req *palPb.AddAddressDetailsRequest) (*palPb.AddAddressDetailsResponse, error) {
	res := &palPb.AddAddressDetailsResponse{}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	if req.GetLocationToken() == "" {
		logger.Error(ctx, "location token empty", zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
	}
	lr, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to get loan request by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(lr.GetVendor()))

	if !s.rpcHelper.IsPinCodeServiceable(lr.GetVendor(), req.GetAddress().GetPostalCode(), lr.GetLoanProgram()) {
		logger.Error(ctx, "postal code not serviceable", zap.String("loan_program", lr.GetLoanProgram().String()))
		res.Status = rpcPb.StatusOutOfRange()
		return res, nil
	}

	loanStep, err := s.getLoanStepForAddDetails(ctx, req.GetLoanRequestId(), req.GetLoanHeader(), lr.GetType(), true)
	if err != nil {
		logger.Error(ctx, "error in fetching loan step for add details", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if loanStep.GetDetails().GetOnboardingData().GetAddressDetails().GetAddressDetails() != nil {
		loanStep.GetDetails().GetOnboardingData().GetAddressDetails().AddressDetails = req.GetAddress()
	} else {
		loanStep.Details = &palPb.LoanStepExecutionDetails{
			Details: &palPb.LoanStepExecutionDetails_OnboardingData{
				OnboardingData: &palPb.OnboardingData{
					AddressDetails: &palPb.OnboardingData_AddressDetails{
						AddressDetails: req.GetAddress(),
						LocationToken:  req.GetLocationToken(),
					},
				},
			},
		}
	}
	err = s.rpcHelper.UpdateLoanCommunicationAddressInUser(ctx, req.GetActorId(), req.GetAddress())
	if err != nil {
		logger.Error(ctx, "error updating loan communication address in user", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	err = s.loanStepExecutionsDao.Update(ctx, loanStep, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if err != nil {
		logger.Error(ctx, "error updating loan step execution details", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	payload := palActivityPb.LoanApplicationESignVerificationSignalPayload{}
	marPayload, _ := protojson.Marshal(&payload)

	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})
	if lr.GetNextAction().GetScreen() != deeplinkPb.Screen_PL_APPLICATION_REVIEW_DETAILS_SCREEN {
		lr.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
		if err = s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
		}); err != nil {
			logger.Error(ctx, "failed to update loan request for next action", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		err = s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.AddressConfirmationSignal), marPayload)
		if err != nil {
			logger.Error(ctx, "failed to signal workflow for address details confirmation", zap.Error(err))
		}
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) AddEmploymentDetails(ctx context.Context, req *palPb.AddEmploymentDetailsRequest) (*palPb.AddEmploymentDetailsResponse, error) {
	// Stage 1 : Declare variables to be used
	res := &palPb.AddEmploymentDetailsResponse{}
	payload := palActivityPb.LoanApplicationESignVerificationSignalPayload{}
	marPayload, _ := protojson.Marshal(&payload)

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	loanRequest, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to get loan request by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// Stage 2 : Get Flow details for loan step
	loanStep, err := s.getLoanStepForAddDetails(ctx, req.GetLoanRequestId(), req.GetLoanHeader(), loanRequest.GetType(), false)
	if err != nil {
		logger.Error(ctx, "error in fetching loan step for add details", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// Stage 3 : Update the occupation data in the loan step
	if loanStep.GetDetails() == nil {
		loanStep.Details = &palPb.LoanStepExecutionDetails{}
	}
	if loanStep.GetDetails().GetOnboardingData() == nil {
		loanStep.GetDetails().Details = &palPb.LoanStepExecutionDetails_OnboardingData{
			OnboardingData: &palPb.OnboardingData{},
		}
	}
	loanStep.GetDetails().GetOnboardingData().EmploymentDetails = &palPb.OnboardingData_EmploymentDetails{
		Occupation:       req.GetOccupationType(),
		OrganizationName: stringPkg.SanitizeFreeText(stringPkg.AlphaNumericWithSpecialChar, req.GetOrganizationName()),
		MonthlyIncome:    req.GetMonthlyIncome(),
		WorkEmail:        req.GetWorkEmail(),
		OfficeAddress:    req.GetOfficeAddress(),
	}

	// Get Loecs and update the desired loan amount
	updateFieldValueLoecMap := make(map[palEnumsPb.FieldId]interface{})
	updateFieldValueLoecMap[palEnumsPb.FieldId_DESIRED_LOAN_AMOUNT] = req.GetDesiredLoanAmount()
	err = s.updateLoecsDataRequirementFields(ctx, req.GetActorId(), updateFieldValueLoecMap)
	if err != nil {
		logger.Error(ctx, "failed to update loecs with desired loan amount", zap.Error(err))
		return &palPb.AddEmploymentDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	txnExec, err := helper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
	if err != nil {
		logger.Error(ctx, "error in fetching txn executor from ctx", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	// Stage 4 : In a transaction update the loan step details then update the next action
	// Done in a transaction so if one of them fails we can roll back the prev ones executed
	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})
	txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
		// Stage 4.1 Update the Loan step details
		err = s.loanStepExecutionsDao.Update(txnCtx, loanStep, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
		if err != nil {
			return errors.Wrap(err, "can't update loan step details")
		}
		loanReq, idErr := s.loanRequestsDao.GetById(txnCtx, req.GetLoanRequestId())
		if idErr != nil {
			return errors.Wrap(idErr, "failed to get loan request by id")
		}
		err = s.checkAndUpdateUserEmploymentDetails(txnCtx, req)
		if err != nil {
			return errors.Wrap(err, "can't update employment details in user")
		}

		// Stage 4.2 Update the next action
		if loanReq.GetNextAction().GetScreen() != deeplinkPb.Screen_PL_APPLICATION_REVIEW_DETAILS_SCREEN {
			loanReq.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), loanReq.GetId())
			if err = s.loanRequestsDao.Update(txnCtx, loanReq, []palPb.LoanRequestFieldMask{
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
			}); err != nil {
				return errors.Wrap(err, "failed to update loan request for next action")
			}
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "failed to add employment details", zap.Error(txnErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	err = s.rpcHelper.SendSignalSync(ctx, loanRequest.GetOrchId(), string(palNs.EmploymentConfirmationSignal), marPayload)
	if err != nil {
		logger.Error(ctx, "failed to signal workflow for employment details confirmation", zap.Error(err))
	}
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) checkAndUpdateUserEmploymentDetails(ctx context.Context, req *palPb.AddEmploymentDetailsRequest) error {
	switch req.GetLoanHeader().GetLoanProgram() {
	case palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		user, err := s.rpcHelper.GetUserByActorId(ctx, req.GetActorId())
		if err != nil {
			return err
		}

		// only send the additional employment details, userClient.UpdateUser rpc will append it to existing data
		if user.GetDataVerificationDetails() == nil {
			user.DataVerificationDetails = &userPb.DataVerificationDetails{}
		}
		user.GetDataVerificationDetails().DataVerificationDetails = []*userPb.DataVerificationDetail{{
			DataType: userPb.DataType_DATA_TYPE_EMPLOYMENT_DETAIL,
			DataValue: &userPb.DataVerificationDetail_EmploymentDetail_{
				EmploymentDetail: &userPb.DataVerificationDetail_EmploymentDetail{
					EmploymentType:   req.GetOccupationType(),
					OrganizationName: req.GetOrganizationName(),
					MonthlyIncome:    req.GetMonthlyIncome(),
					WorkEmail:        req.GetWorkEmail(),
				},
			},
			VerificationMethod: userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
		}}

		err = s.updateUserDetails(ctx, user, []userPb.UserFieldMask{userPb.UserFieldMask_DATA_VERIFICATION_DETAILS})
		if err != nil {
			return err
		}
	default:
		return nil
	}
	return nil
}

func (s *Service) FetchDynamicElements(ctx context.Context, req *dePb.FetchDynamicElementsRequest) (*dePb.FetchDynamicElementsResponse, error) {
	// fetch appropriate provider for fetching dynamic elements.
	dynamicElementsProvider := s.dynamicElementsProviderFactory.GetDynamicElementsProvider(ctx, req.GetClientContext())
	if dynamicElementsProvider == nil {
		logger.Info(ctx, "no dynamic elements provider found", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.SCREEN, req.GetClientContext().GetScreenName().String()), zap.String("home_info_section", req.GetClientContext().GetHomeInfo().GetSection().String()))
		return &dePb.FetchDynamicElementsResponse{Status: rpc.StatusRecordNotFound()}, nil
	}

	// fetch dynamic elements from the provider.
	elements, err := dynamicElementsProvider.GetDynamicElements(ctx, &deProvider.GetDynamicElementsRequest{
		ActorId:                  req.GetActorId(),
		DynElementsClientContext: req.GetClientContext(),
	})
	if err != nil {
		logger.Error(ctx, "error fetching dynamic elements from provider", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any("clientContext", req.GetClientContext()), zap.Error(err))
		return &dePb.FetchDynamicElementsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	if len(elements) == 0 {
		return &dePb.FetchDynamicElementsResponse{Status: rpc.StatusRecordNotFound()}, nil
	}

	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: elements,
	}, nil
}

func (s *Service) DynamicElementCallback(ctx context.Context, req *dePb.DynamicElementCallbackRequest) (*dePb.DynamicElementCallbackResponse, error) {
	// explicitly setting ownership as EPIFI_TECH_V2 since we are just using do once to record status of the dynamic element
	doOnceMgr, err := s.multiDbDOnceMgr.GetDoOnceManagerForOwnership(commontypes.Ownership_EPIFI_TECH_V2, onceV2.NewDoOnce)
	if err != nil {
		logger.Error(ctx, "Error getting doOnceMgr for ownership", zap.Error(err))
		return &dePb.DynamicElementCallbackResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	err = doOnceMgr.Do(ctx, req.GetElementId())
	if err != nil && !errors.Is(err, epifierrors.ErrDuplicateEntry) {
		logger.Error(ctx, "Error in marking task as done", zap.Error(err))
		return &dePb.DynamicElementCallbackResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &dePb.DynamicElementCallbackResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) ConfirmRevisedLoanDetails(ctx context.Context, req *palPb.ConfirmRevisedLoanDetailsRequest) (*palPb.ConfirmRevisedLoanDetailsResponse, error) {
	res := &palPb.ConfirmRevisedLoanDetailsResponse{}

	loanRequest, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "error while fetching loan request from db", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&emptyPb.Empty{})
	if err != nil {
		logger.Error(ctx, "failed to marshal signal with empty", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// signal workflow to verify OTP
	if signalErr := s.rpcHelper.SendSignalSync(ctx, loanRequest.GetOrchId(), string(palNs.RevisedLoanApplicationConfirmationSignal), marshalledPayload); signalErr != nil {
		logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
		// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
	}

	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})
	loanRequest.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), loanRequest.GetId())
	if err = s.loanRequestsDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
	}); err != nil {
		logger.Error(ctx, "failed to update loan request next action", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	res.LoanRequest = loanRequest
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) PublishLoanRequestEventForNudgeExit(ctx context.Context, event *palPb.PALEvent) error {
	if event.GetActorId() == "" {
		return fmt.Errorf("insufficient arguments to publish event %w", epifierrors.ErrInvalidArgument)
	}
	// publish the event to preapprovedloan-nudge-exit-queue
	if _, err := s.palEventPublisher.Publish(ctx, event); err != nil {
		return fmt.Errorf("failed to publish to the preapprovedloan-nudge-exit-queue %w", err)
	}
	return nil
}

func (s *Service) ClientCallback(ctx context.Context, req *palPb.ClientCallbackRequest) (*palPb.ClientCallbackResponse, error) {
	res := &palPb.ClientCallbackResponse{}
	var lse *palPb.LoanStepExecution
	var lr *palPb.LoanRequest
	var lseErr, lrErr error
	if req.GetStepOrchId() != "" {
		lse, lseErr = s.loanStepExecutionsDao.GetByOrchId(ctx, req.GetStepOrchId())
		if lseErr != nil {
			logger.Error(ctx, "error while fetching loan step from db", zap.Error(lseErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}

	// for backward compatible
	lrId := lse.GetRefId()
	if lrId == "" {
		lrId = req.GetLoanReqId()
	}
	if lrId == "" {
		lrId = req.GetLoanRequestId()
	}

	// loan request id will not be populated for offer viewed callback
	if req.GetType() != palPb.ClientCallbackRequest_OFFER_VIEWED {
		lr, lrErr = s.loanRequestsDao.GetById(ctx, lrId)
		if lrErr != nil {
			logger.Error(ctx, "error while fetching loan request from db", zap.Error(lrErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
	}
	if lr != nil {
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(lr.GetVendor()))
	} else {
		ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	}

	txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
	if txnExecErr != nil {
		logger.Error(ctx, "error in fetching txn executor from ctx", zap.Error(txnExecErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
	switch req.GetType() {
	case palPb.ClientCallbackRequest_SI:
		if lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED ||
			lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS {

			lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
			lr.NextAction = deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
			txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
				if updateErr := s.loanStepExecutionsDao.Update(txnCtx, lse, []palPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				}); updateErr != nil {
					return errors.Wrap(updateErr, "error while updating lse")
				}

				if updateErr := s.loanRequestsDao.Update(txnCtx, lr, []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				}); updateErr != nil {
					return errors.Wrap(updateErr, "error while updating lr")
				}

				return nil
			})
			if txnErr != nil {
				logger.Error(ctx, "failed to update si callback details", zap.Error(txnErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
		}
	case palPb.ClientCallbackRequest_MANDATE:
		marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&emptyPb.Empty{})
		if err != nil {
			logger.Error(ctx, "failed to marshal signal with empty", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		lse, lseErr = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE)
		if lseErr != nil {
			logger.Error(ctx, "error while fetching loan step from db", zap.Error(lseErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		switch req.GetResult() {
		case palPb.ClientCallbackRequest_SUCCESS:
			// signal workflow
			if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationMandateCompletionSignal), marshalledPayload); signalErr != nil {
				logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
				// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
			}

			// In case client callback is delayed and the workflow has moved forward by polling the mandate status with lender
			// we should not override the next action to polling screen
			if lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
				res.Status = rpcPb.StatusOk()
				return res, nil
			}

			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_INITIATED_AT_VENDOR
			if lseUpdateErr := s.loanStepExecutionsDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
			}); lseUpdateErr != nil {
				logger.Error(ctx, "failed to update loan step sub status", zap.Error(lseUpdateErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}

			// TODO(@prasoon): Move update logic from BE to workflow
			// we don't want to update either lse or lr from BE. We want them to get updated via workflow only, not via multiple places.
			// reason being: there could be a race condition where workflow moves to next stage and updates the next action, but in this RPC we override it.
			lr.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
			if lrUpdateErr := s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
			}); lrUpdateErr != nil {
				logger.Error(ctx, "failed to update loan request next action", zap.Error(lrUpdateErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
		case palPb.ClientCallbackRequest_INITIATED:
			// signal workflow
			if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationMandateCompletionSignal), marshalledPayload); signalErr != nil {
				logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
				// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
			}

			// In case client callback is delayed and the workflow has moved forward by polling the mandate status with lender
			// we should not override the next action to polling screen
			if lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
				res.Status = rpcPb.StatusOk()
				return res, nil
			}

			lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_INITIATED_AT_VENDOR
			if lseUpdateErr := s.loanStepExecutionsDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
			}); lseUpdateErr != nil {
				logger.Error(ctx, "failed to update loan step sub status", zap.Error(lseUpdateErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
		default:
			// Do Nothing
		}
		switch data := req.GetCallbackPayload().GetPaylaod().(type) {
		case *palPb.CallbackPayload_MandatePayload:
			logger.Info(ctx, "Client callback mandate payload", zap.String(logger.PAYLOAD, req.GetCallbackPayload().GetMandatePayload().GetMandateSdkEvent().GetEventPayload()))
			err := s.updateMandateStatusFromCallbackPayload(ctx, data, lse, req.GetDevicePlatform())
			// handle error at best effort basis only since we are updating data in mandate requests table and
			// data might not be available in that table for mandates which were performed before related changes went live
			if err != nil {
				logger.Error(ctx, "failed update mandate callback payload data in lse and mandate request table", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, lrId))
			}
		default:
			// do nothing
		}

	case palPb.ClientCallbackRequest_E_SIGN:
		// TODO: pass success/failure payload in signal
		marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&emptyPb.Empty{})
		if err != nil {
			logger.Error(ctx, "failed to marshal signal with empty", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		if req.GetResult() == palPb.ClientCallbackRequest_SUCCESS {
			lse, err = s.getCurrentKfsLseForEsigning(ctx, req.GetLoanRequestId())
			if err != nil {
				logger.Error(ctx, "error while fetching kfs loan step from db", zap.Error(err))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			// In case client callback is delayed and the workflow has moved forward by polling the mandate status with lender
			// we should not override the next action to polling screen
			if lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
				res.Status = rpcPb.StatusOk()
				return res, nil
			}

			var updateFieldMask []palPb.LoanStepExecutionFieldMask
			if req.GetLoanHeader().GetVendor() == palPb.Vendor_ABFL ||
				req.GetLoanHeader().GetVendor() == palPb.Vendor_LENDEN {
				lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_SIGNED
				updateFieldMask = append(updateFieldMask, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
			} else {
				lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
				updateFieldMask = append(updateFieldMask, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
			}
			if lseUpdateErr := s.loanStepExecutionsDao.Update(ctx, lse, updateFieldMask); lseUpdateErr != nil {
				logger.Error(ctx, "failed to update loan step status", zap.Error(lseUpdateErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			// Although we are currently updating LSE and LR from within this RPC, this is not the ideal approach.
			// we don't want to update either lse or lr from BE. We want them to get updated via workflow only, not via multiple places.
			// reason being: there could be a race condition where workflow moves to next stage and updates the next action, but in this RPC we override it.
			lr.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
			if lrUpdateErr := s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
			}); lrUpdateErr != nil {
				logger.Error(ctx, "failed to update loan request next action", zap.Error(lrUpdateErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}

			// signal workflow
			if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationESignVerificationSignal), marshalledPayload); signalErr != nil {
				logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
				// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
			}
		}
	case palPb.ClientCallbackRequest_OFFER_VIEWED:
		if req.GetLoanOfferId() == "" {
			logger.Error(ctx, "offer id cannot be empty for OFFER_VIEWED callback request type")
			return &palPb.ClientCallbackResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("offer id cannot be empty")}, nil
		}
		lo, err := s.loanOffersDao.GetById(ctx, req.GetLoanOfferId())
		if err != nil {
			logger.Error(ctx, "error in fetching loan offer by id", zap.Error(err), zap.String(logger.OFFER_ID, req.GetLoanOfferId()))
			return &palPb.ClientCallbackResponse{Status: rpcPb.StatusInternal()}, nil
		}
		// we need to track last viewed time only for offers where discount is not applied yet
		// this check can be modified/removed if there are other use cases for the last viewed time
		if len(lo.GetProcessingInfo().GetOfferDiscounts()) == 0 {
			lo.LastViewedAt = timestampPb.Now()
			err = s.loanOffersDao.Update(ctx, lo, []palPb.LoanOfferFieldMask{palPb.LoanOfferFieldMask_LOAN_OFFER_FIELD_MASK_LAST_VIEWED_AT})
			if err != nil {
				logger.Error(ctx, "failed to update loan offer viewed time", zap.Error(err), zap.String(logger.OFFER_ID, req.GetLoanOfferId()))
				return &palPb.ClientCallbackResponse{Status: rpcPb.StatusInternal()}, nil
			}
		}
	default:
		// do nothing
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) uploadImageToS3(ctx context.Context, path string, imageBase64 string) (string, error) {

	s3Err := s.s3Client.Write(ctx, path, []byte(imageBase64), string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if s3Err != nil {
		return "", errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in uploading image to s3, err: %v", s3Err))
	}
	return path, nil
}

func (s *Service) VerifyDetails(ctx context.Context, req *palPb.VerifyDetailsRequest) (*palPb.VerifyDetailsResponse, error) {
	res := &palPb.VerifyDetailsResponse{}

	user, err := s.rpcHelper.GetUserByActorId(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to fetch user by actor ID", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	lr, err := s.loanRequestsDao.GetById(ctx, req.GetReqId())
	if err != nil {
		logger.Error(ctx, "failed to get loan request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	var lse *palPb.LoanStepExecution
	if req.GetLseId() != "" {
		lse, err = s.loanStepExecutionsDao.GetById(ctx, req.GetLseId())
		if err != nil {
			logger.Error(ctx, "error while fetching loan step")
			return res, err
		}
	} else {
		lse, err = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetReqId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, helper.GetLoanStepNameFromDetailsType(req.GetLoanHeader(), req.GetDetailsType()))
		if err != nil {
			logger.Error(ctx, "error while fetching loan step")
			return res, err
		}
	}

	marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&emptyPb.Empty{})
	if err != nil {
		logger.Error(ctx, "failed to marshal signal with empty", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	lseFieldMasks := []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS}
	var lrFieldMasks []palPb.LoanRequestFieldMask
	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
	switch req.GetDetailsType() {
	case preapprovedloans.DetailsType_DETAILS_TYPE_PAN:
		if strings.ToUpper(req.GetValue()) != user.GetProfile().GetPAN() {
			res.Status = rpc.StatusInvalidArgument()
			return res, nil
		}

		dl, dlErr := deeplinkProvider.GetApplicationStatusPollScreenWithCustomMsgDeepLink(
			deeplinkProvider.GetLoanHeader(),
			lr.GetId(),
			"",
			"#333333",
			"",
		)
		if dlErr != nil {
			logger.Error(ctx, "error while generating application status poll deeplink %v", zap.Error(dlErr))
			return nil, fmt.Errorf("error while generating application status poll deeplink %w", dlErr)
		}
		lr.NextAction = dl
		lrFieldMasks = []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}

		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAN_ADDED
		lseFieldMasks = append(lseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)

	case preapprovedloans.DetailsType_DETAILS_TYPE_DOB, preapprovedloans.DetailsType_DETAILS_TYPE_OCCUPATION:
		if req.GetLoanHeader().GetVendor() == palPb.Vendor_ABFL {
			// signal workflow that user has entered the details
			if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.EmploymentConfirmationSignal), marshalledPayload); signalErr != nil {
				logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
				// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
			}
			// in case of occupation details, persist the employment type of the user in loan step details
			if req.DetailsType == preapprovedloans.DetailsType_DETAILS_TYPE_OCCUPATION {
				if lse.GetDetails() == nil {
					lse.Details = &palPb.LoanStepExecutionDetails{}
				}
				if lse.GetDetails().GetOnboardingData() == nil {
					lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_OnboardingData{
						OnboardingData: &palPb.OnboardingData{},
					}
				}

				lse.GetDetails().GetOnboardingData().EmploymentDetails = &palPb.OnboardingData_EmploymentDetails{
					Occupation: req.GetEmploymentType(),
				}
				lseFieldMasks = append(lseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS)
				lr.NextAction = deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
				lrFieldMasks = []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}
			}
		} else {
			// check if the DOB entered by the user matches with the one in our records
			if req.GetDetailsType() == preapprovedloans.DetailsType_DETAILS_TYPE_DOB && datetime.DateFromString(req.GetValue()).GetYear() != user.GetProfile().GetDateOfBirth().GetYear() {
				res.Status = rpc.StatusInvalidArgument()
				return res, nil
			}

			lr.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(
				deeplinkProvider.GetLoanHeader(),
				lr.GetId(),
			)
			lrFieldMasks = []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}

			if req.GetLoanHeader().GetVendor() != palPb.Vendor_IDFC {
				lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
			}
			// in case of occupation details, persist the employment type of the user in loan step details
			if req.DetailsType == preapprovedloans.DetailsType_DETAILS_TYPE_OCCUPATION {
				lse.GetDetails().GetCkycStepData().EmploymentType = req.GetEmploymentType()
				lseFieldMasks = append(lseFieldMasks, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS)
			}
		}

	case preapprovedloans.DetailsType_DETAILS_TYPE_AADHAAR:
		if lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
			return &palPb.VerifyDetailsResponse{Status: rpc.StatusOk()}, nil
		}

		lr.NextAction = deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())

		if updateErr := s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}); updateErr != nil {
			logger.Error(ctx, "error while updating lr", zap.Error(updateErr), zap.String(logger.LOAN_REQUEST_ID, lr.GetId()))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		if lse.GetDetails().GetAadhaarData() == nil {
			lse.Details = &palPb.LoanStepExecutionDetails{
				Details: &palPb.LoanStepExecutionDetails_AadhaarData{
					AadhaarData: &palPb.AadhaarData{
						LastFourDigit: req.GetValue()},
				},
			}
		}
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_AADHAAR_DETAILS_ADDED
		err = s.loanStepExecutionsDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
		if err != nil {
			logger.Error(ctx, "can't update loan step details", zap.Error(err), zap.String(logger.LOAN_STEP_EXECUTION_ID, lse.GetId()))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanAadhaarVerificationSignal), marshalledPayload); signalErr != nil {
			logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
			// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
		}
		res.Status = rpc.StatusOk()
		return res, nil

	case preapprovedloans.DetailsType_DETAILS_TYPE_PHONE_NUMBER_AND_EMAIL:
		// persist the data in loan step details and
		if lse.GetDetails() == nil {
			lse.Details = &palPb.LoanStepExecutionDetails{}
		}
		if lse.GetDetails().GetApplicantData() == nil {
			lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_ApplicantData{
				ApplicantData: &palPb.ApplicantData{},
			}
		}
		lse.GetDetails().GetApplicantData().PhoneNumber = req.GetUserInput().GetPhoneAndEmailDetails().GetPhoneNumber()
		lse.GetDetails().GetApplicantData().Email = req.GetUserInput().GetPhoneAndEmailDetails().GetEmail()
		lseFieldMasks = []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}

		// update the sub status of loan request to "USER_DETAILS_ADDED" as workflow is polling for this sub status and set the next action to polling screen
		lr.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_USER_DETAILS_ADDED
		lr.NextAction, err = deeplinkProvider.GetLoansApplicationStatusPollDeeplink(ctx, deeplinkProvider.GetLoanHeader(), req.GetActorId(), lr.GetId(), &provider.ApplicationStatusPollDeeplinkParams{
			Icon:     nulltypes.NewNullString("https://epifi-icons.pointz.in/preapprovedloan/poll_loader_pf_fetch.png"),
			Title:    nulltypes.NewNullString("Searching your Mutual Funds portfolio"),
			SubTitle: nulltypes.NewNullString("This may take up to a minute. Hang on tight!"),
		})
		if err != nil {
			return nil, fmt.Errorf("error while generating application poll status deeplink : %w", err)
		}
		lrFieldMasks = []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
		}

	case preapprovedloans.DetailsType_DETAILS_TYPE_ADDITIONAL_KYC:
		if lse.GetDetails() == nil {
			lse.Details = &palPb.LoanStepExecutionDetails{}
		}
		if lse.GetDetails().GetApplicantData() == nil {
			lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_ApplicantData{
				ApplicantData: &palPb.ApplicantData{},
			}
		}
		// persist data in loan step details
		lse.GetDetails().GetApplicantData().EmploymentType = req.GetUserInput().GetAdditionalKycDetails().GetEmploymentType()
		lse.GetDetails().GetApplicantData().MaritalStatus = req.GetUserInput().GetAdditionalKycDetails().GetMaritalStatus()
		lse.GetDetails().GetApplicantData().ResidenceType = req.GetUserInput().GetAdditionalKycDetails().GetResidenceType()
		lse.GetDetails().GetApplicantData().FatherName = req.GetUserInput().GetAdditionalKycDetails().GetFatherName()
		lse.GetDetails().GetApplicantData().MotherName = req.GetUserInput().GetAdditionalKycDetails().GetMotherName()
		lseFieldMasks = []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}

		// update sub status of LSE
		lr.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_USER_DETAILS_ADDED
		lr.NextAction, err = deeplinkProvider.GetLoansApplicationStatusPollDeeplink(ctx, deeplinkProvider.GetLoanHeader(), req.GetActorId(), lr.GetId(), &provider.ApplicationStatusPollDeeplinkParams{
			Icon:     nulltypes.EmptyNullString(),
			Title:    nulltypes.NewNullString("Please wait"),
			SubTitle: nulltypes.EmptyNullString(),
		})
		if err != nil {
			return nil, fmt.Errorf("error while generating application poll status deeplink : %w", err)
		}
		lrFieldMasks = []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
		}

	case preapprovedloans.DetailsType_DETAILS_TYPE_SELFIE:
		if lse.GetDetails() == nil {
			lse.Details = &palPb.LoanStepExecutionDetails{}
		}
		if lse.GetDetails().GetSelfieData() == nil {
			lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_SelfieData{
				SelfieData: &palPb.SelfieData{},
			}
		}

		if req.GetUserInput().GetSelfieDetails().GetSelfieImage().GetImageDataBase64() == "" {
			logger.Info(ctx, "empty image data")
			res.Status = rpcPb.StatusFailedPrecondition()
			return res, nil
		}

		imageUrl, uploadImageErr := s.uploadImageToS3(ctx,
			fmt.Sprintf("SelfieImages/%v/%v.%v", req.GetActorId(), lr.GetId(), helper.ImageFileExt),
			req.GetUserInput().GetSelfieDetails().GetSelfieImage().GetImageDataBase64())
		if uploadImageErr != nil {
			logger.Error(ctx, "failed to upload image to s3", zap.Error(uploadImageErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		selfieImageBE := &commontypes.Image{ImageUrl: imageUrl}
		// Fetch lse and update
		lse.GetDetails().GetSelfieData().SelfieImage = selfieImageBE
		lr.NextAction = deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
		lrFieldMasks = []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}
		lseFieldMasks = []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}

	case preapprovedloans.DetailsType_DETAILS_TYPE_REFERENCES:
		if lse.GetDetails() == nil {
			lse.Details = &palPb.LoanStepExecutionDetails{}
		}
		if lse.GetDetails().GetApplicantData() == nil {
			lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_ApplicantData{
				ApplicantData: &palPb.ApplicantData{},
			}
		}

		if len(req.GetUserInput().GetFormDetails().GetFormFields()) == 0 {
			logger.Error(ctx, "form fields nil")
			res.Status = rpcPb.StatusFailedPrecondition()
			return res, nil
		}

		var references []*palPb.Reference
		useOfLoan := ""
		for _, ff := range req.GetUserInput().GetFormDetails().GetFormFields() {
			if len(ff.GetIdentifier()) < 2 {
				logger.Error(ctx, fmt.Sprintf("form fields, invalid identifier, %s", ff.GetIdentifier()))
				res.Status = rpcPb.StatusFailedPrecondition()
				return res, nil
			}
			i, atoiErr := strconv.Atoi(ff.GetIdentifier()[1:2])
			if atoiErr != nil {
				logger.Error(ctx, fmt.Sprintf("unable to convert Atoi, %s", ff.GetIdentifier()), zap.Error(atoiErr))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
			for len(references) < i+1 {
				references = append(references, &palPb.Reference{})
			}
			if ff.GetIdentifier()[:1] == "n" {
				references[i].Name = namesPkg.ParseStringV2(ff.GetFieldOptions().GetTextFieldOptions().GetValue().GetPlainString())
			}
			if ff.GetIdentifier()[:1] == "p" {
				references[i].PhoneNumber, err = commontypes.ParsePhoneNumber(ff.GetFieldOptions().GetTextFieldOptions().GetValue().GetPlainString())
				if err != nil {
					logger.Error(ctx, fmt.Sprintf("unable to parse phone number, %s", ff.GetFieldOptions().GetTextFieldOptions().GetValue().GetPlainString()), zap.Error(err))
					res.Status = rpcPb.StatusInternal()
					return res, nil
				}
				if references[i].GetPhoneNumber().GetCountryCode() == 0 {
					references[i].GetPhoneNumber().CountryCode = 91
				}
			}
			if ff.GetIdentifier()[:1] == "u" {
				useOfLoan = ff.GetFieldOptions().GetDropdownFieldOptions().GetValue()
			}
		}
		lse.GetDetails().GetApplicantData().References = references
		lse.GetDetails().GetApplicantData().PurposeOfLoan = useOfLoan
		lr.NextAction = deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
		lrFieldMasks = []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}
		lseFieldMasks = []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}
	default:
		// do nothing
	}

	txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
	if txnExecErr != nil {
		logger.Error(ctx, "error in fetching txn executor from ctx", zap.Error(txnExecErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
		if len(lseFieldMasks) > 0 {
			if updateErr := s.loanStepExecutionsDao.Update(txnCtx, lse, lseFieldMasks); updateErr != nil {
				return errors.Wrap(updateErr, "error while updating lse")
			}
		}

		if len(lrFieldMasks) > 0 {
			if updateErr := s.loanRequestsDao.Update(txnCtx, lr, lrFieldMasks); updateErr != nil {
				return errors.Wrap(updateErr, "error while updating lr")
			}
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "failed to update verify details", zap.Error(txnErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	switch req.GetDetailsType() {
	case preapprovedloans.DetailsType_DETAILS_TYPE_PAN:
		// if user entered the PAN saved in our records, signal the workflow to proceed with VG call to validate PAN
		if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationPanVerificationSignal), marshalledPayload); signalErr != nil {
			logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
			// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
		}
	case preapprovedloans.DetailsType_DETAILS_TYPE_DOB, preapprovedloans.DetailsType_DETAILS_TYPE_OCCUPATION:
		if req.GetLoanHeader().GetVendor() != palPb.Vendor_ABFL {
			// signal workflow that user has entered the details
			if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationDetailsVerificationSignal), marshalledPayload); signalErr != nil {
				logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
				// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
			}
		}
	case preapprovedloans.DetailsType_DETAILS_TYPE_PHONE_NUMBER_AND_EMAIL:
		// signal the workflow that details are added by the user
		if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanEligibilityDetailsAddedSignal), marshalledPayload); signalErr != nil {
			logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
			// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
		}

	case preapprovedloans.DetailsType_DETAILS_TYPE_ADDITIONAL_KYC:
		// signal workflow that details are added by user
		if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationDetailsVerificationSignal), marshalledPayload); signalErr != nil {
			logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
			// Since we only have signal(push) based implementation, return internal status from here
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

	case preapprovedloans.DetailsType_DETAILS_TYPE_SELFIE:
		// signal workflow that details are added by user
		if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationUploadSelfieSignal), marshalledPayload); signalErr != nil {
			logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
			// Since we only have signal(push) based implementation, return internal status from here
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

	case preapprovedloans.DetailsType_DETAILS_TYPE_REFERENCES:
		// signal workflow that details are added by user
		if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationAddReferencesSignal), marshalledPayload); signalErr != nil {
			logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
			// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
		}
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) VerifyCkycDetails(ctx context.Context, req *palPb.VerifyCkycDetailsRequest) (*palPb.VerifyCkycDetailsResponse, error) {
	res := &palPb.VerifyCkycDetailsResponse{}

	lr, err := s.loanRequestsDao.GetById(ctx, req.GetLoanReqId())
	if err != nil {
		logger.Error(ctx, "failed to get loan request", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&emptyPb.Empty{})
	if err != nil {
		logger.Error(ctx, "failed to marshal signal with empty", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	var lse *palPb.LoanStepExecution
	var lseFms []palPb.LoanStepExecutionFieldMask
	if req.GetLoanHeader().GetVendor() == palPb.Vendor_ABFL {
		lse, err = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanReqId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching loan request from db", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED
		lseFms = []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS}
	} else {
		lse, err = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanReqId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while fetching loan request from db", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		lseFms = []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS}
	}

	if updateErr := s.loanStepExecutionsDao.Update(ctx, lse, lseFms); updateErr != nil {
		logger.Error(ctx, "failed to update lse", zap.Error(updateErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// updating next action to polling screen so that user won't land on "verify details" screen again
	err = s.updateLoanRequestNextActionToPollScreen(ctx, lr)
	if err != nil {
		logger.Error(ctx, "failed to update loan request next action to poll screen", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if signalErr := s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationCkycVerificationSignal), marshalledPayload); signalErr != nil {
		logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
		// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) updateLoanRequestNextActionToPollScreen(
	ctx context.Context,
	lr *palPb.LoanRequest,
) error {
	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
	lr.NextAction = deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
	if err := s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{palPb.
		LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}); err != nil {
		return errors.Wrap(err, "failed to update lr")
	}
	return nil
}

func (s *Service) RefreshLMSSchedule(ctx context.Context, req *palPb.RefreshLmsScheduleRequest) (*palPb.RefreshLmsScheduleResponse, error) {
	logAndReturnErr := func(msg string, err error) (*palPb.RefreshLmsScheduleResponse, error) {
		logger.Error(ctx, msg, zap.Error(err))
		return &palPb.RefreshLmsScheduleResponse{Status: rpcPb.StatusFromError(err)}, nil
	}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	loanAccounts, err := s.loanAccountsDao.GetByActorIdVendorAndLoanProgram(ctx, req.GetActorId(), req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	if err != nil {
		if storage.IsRecordNotFoundError(err) {
			return logAndReturnErr("failed to get loan account by id", rpc.StatusAsError(rpc.StatusRecordNotFound()))
		}
		return logAndReturnErr("failed to get loan account by id", err)
	}
	if len(loanAccounts) == 0 {
		return logAndReturnErr("failed to get loan account by id", rpc.StatusAsError(rpc.StatusRecordNotFound()))
	}
	var refreshLmsErrors []error
	for _, la := range loanAccounts {
		if la.GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			lmsReq := &lms.RefreshMirroredLmsFromVendorRequest{Header: req.GetLoanHeader(), LoanAccountId: la.GetId()}
			err = s.lms.RefreshMirroredLmsFromVendor(ctx, lmsReq)
			if err != nil {
				logger.Error(ctx, "failed to refresh lms for loan account", zap.String(logger.LOAN_ACCOUNT_ID, la.GetId()), zap.Error(err))
				refreshLmsErrors = append(refreshLmsErrors, err)
			}
		}
	}
	if len(refreshLmsErrors) > 0 {
		for _, err = range refreshLmsErrors {
			if !errors.Is(err, loanErrors.ErrExpectedDelayInLmsUpdateAtLender) {
				return &palPb.RefreshLmsScheduleResponse{Status: rpc.StatusInternalWithDebugMsg("lms refresh failed for some loan accounts")}, nil
			}
		}
		return &palPb.RefreshLmsScheduleResponse{Status: rpcPb.NewStatusWithoutDebug(uint32(palPb.RefreshLmsScheduleResponse_EXPECTED_DELAY_IN_LMS_UPDATE_AT_VENDOR), "lms data not updated at vendor yet")}, nil
	}
	return &palPb.RefreshLmsScheduleResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) GetMandateViewData(ctx context.Context, req *palPb.GetMandateViewDataRequest) (*palPb.GetMandateViewDataResponse, error) {
	res := &palPb.GetMandateViewDataResponse{}

	lse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(),
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE)
	if err != nil {
		logger.Error(ctx, "failed to fetch lse for mandate stage", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if lse.GetDetails().GetMandateData().GetMandateLinkExpiry() != nil &&
		datetime.IsBefore(timestampPb.Now(), lse.GetDetails().GetMandateData().GetMandateLinkExpiry()) {
		logger.Error(ctx, "cannot process another mandate until the current one expires")
		res.Status = rpc.StatusAlreadyExists()
		return res, nil
	}
	lr, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to fetch loan request by ID")
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	base64Html, err := s.rpcHelper.GetWebPageHtml(ctx, req.GetActorId(), req.GetLoanRequestId(), req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram(), nil, lr.GetDetails().GetProgramVersion())
	if err != nil {
		logger.Error(ctx, "failed to fetch mandate web page from vendor", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{
		Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})
	lr.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
	// Update next action in LR to polling API
	if updateErr := s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}); updateErr != nil {
		logger.Error(ctx, "failed to update next action in loan request")
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Base64EncodedHtml = base64Html
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) ReconLoanActivity(ctx context.Context, req *palPb.ReconLoanActivityRequest) (*palPb.ReconLoanActivityResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	res := &palPb.ReconLoanActivityResponse{
		Status: rpcPb.StatusOk(),
	}

	provider, err := s.loanTxnFactory.FetchLoanTxnProvider(ctx, req.GetLoanHeader())
	if err != nil {
		logger.Error(ctx, "failed to fetch loan txn provider", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanAccTxns, err := provider.FetchAccountTxnFromVendor(ctx, &loanTxnProvider.FetchAccountTxnRequest{ActorId: req.GetActorId()})
	if err != nil {
		logger.Error(ctx, "failed to fetch loan acc txn from vendor", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanActivities, err := provider.FilterLoanTxn(ctx, &loanTxnProvider.FilterLoanTxnRequest{
		LoanTransactions: loanAccTxns.LoanTransactions,
		LoanAccountId:    loanAccTxns.LoanAccountId,
		TransactionType:  loanTxnProvider.ExternalTxns,
	})
	if err != nil {
		logger.Error(ctx, "failed to parse loan txns", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	for _, loanActivity := range loanActivities.LoanActivities {
		_, err = s.loanActivityDao.GetByReferenceIdAndType(ctx, loanActivity.GetReferenceId(), palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LUMPSUM)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "failed to fetch loan activity", zap.Error(err))
			continue
		}

		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			_, createErr := s.loanActivityDao.Create(ctx, loanActivity)
			if createErr != nil {
				logger.Error(ctx, "failed to create loan activity", zap.Error(err))
			}
		}
	}

	return res, nil
}

func (s *Service) GetLoanInstallmentPayoutDetails(ctx context.Context, req *palPb.GetLoanInstallmentPayoutDetailsRequest) (*palPb.GetLoanInstallmentPayoutDetailsResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	res := &palPb.GetLoanInstallmentPayoutDetailsResponse{}
	loanInstallmentPayout, err := s.loanInstallmentPayoutDao.GetById(ctx, req.GetLoanInstallmentPayoutId())
	if err != nil {
		if storage.IsRecordNotFoundError(err) {
			logger.Error(ctx, "failed to get loan installment payout details: record not found", zap.Error(err))
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, "failed to get loan installment payout details", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.LoanInstallmentPayout = loanInstallmentPayout

	loanInstallmentInfo, err := s.loanInstallmentInfoDao.GetById(ctx, loanInstallmentPayout.GetLoanInstallmentInfoId())
	if err != nil {
		logger.Error(ctx, "failed to get loan installment payout details", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.LoanInstallmentInfo = loanInstallmentInfo

	loanAccount, err := s.loanAccountsDao.GetById(ctx, loanInstallmentInfo.GetAccountId())
	if err != nil {
		logger.Error(ctx, "failed to get loan installment payout details", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.LoanAccount = loanAccount

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) GetLoanSchedule(ctx context.Context, req *palPb.GetLoanScheduleRequest) (*palPb.GetLoanScheduleResponse, error) {
	// TODO: To move this logic behind LMS interface. That way if we move to external LMS, interface can abstract out that logic
	var (
		res = &palPb.GetLoanScheduleResponse{}
	)

	logAndReturnErr := func(msg string, err error) (*palPb.GetLoanScheduleResponse, error) {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}
		logger.Error(ctx, fmt.Sprint(msg), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	loanAccount, err := s.loanAccountsDao.GetById(ctx, req.GetLoanId())
	if err != nil {
		return logAndReturnErr("failed to get loan account by id", err)
	}

	lii, err := s.loanInstallmentInfoDao.GetByActiveAccountId(ctx, loanAccount.GetId())
	if err != nil {
		return logAndReturnErr("failed to get lii by account id", err)
	}

	loanPayouts, err := s.loanInstallmentPayoutDao.GetByLoanInstallmentInfoId(ctx, lii.GetId())
	if err != nil {
		return logAndReturnErr("failed to get payout by id", err)
	}

	return &palPb.GetLoanScheduleResponse{
		Schedule: loanPayouts,
		Status:   rpc.StatusOk(),
	}, nil
}

func (s *Service) AddBankingDetails(ctx context.Context, req *palPb.AddBankingDetailsRequest) (*palPb.AddBankingDetailsResponse, error) {
	res := &palPb.AddBankingDetailsResponse{}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	err := validateAddBankingDetailsRequest(req)
	if err != nil {
		if errors.Is(err, loanErrors.ErrBankNotSupported) {
			logger.Info(ctx, "user tried to use unsupported bank", zap.Any(logger.BANK_IDENTIFIER, req.GetBankingDetails().GetBankName()),
				zap.Any(logger.LOAN_PROGRAM, req.GetLoanHeader().GetLoanProgram()), zap.Any(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
			return &palPb.AddBankingDetailsResponse{
				Status: rpcPb.NewStatusWithoutDebug(uint32(palPb.AddBankingDetailsResponse_BANK_NOT_SUPPORTED), "bank is not allowed"),
			}, nil
		}
		logger.Error(ctx, "invalid add banking details request", zap.Error(err),
			zap.Any(logger.LOAN_PROGRAM, req.GetLoanHeader().GetLoanProgram()), zap.Any(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palPb.AddBankingDetailsResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	lr, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to get loan request by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(lr.GetVendor()))

	// get loan step object
	loanStep, err := s.getLoanStepForAddBankingDetails(ctx, req)
	if err != nil {
		logger.Error(ctx, "error in fetching loan step", zap.Error(err))
	}

	accountNumber, err := s.getCompleteAccountNumber(ctx, req.GetBankingDetails(), loanStep)
	if err != nil {
		logger.Error(ctx, "error getting complete account number", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	// if step is mandate then execute steps for mandate
	// this handling is added to keep changes for A2L backward compatible
	// TODO (Diparth): Move AddBankingDetails processing logic behind an interface
	if loanStep.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE {
		return s.addBankingDetailsForMandate(ctx, req, lr, loanStep, accountNumber)
	}
	loanStep.Details = &palPb.LoanStepExecutionDetails{
		Details: &palPb.LoanStepExecutionDetails_OnboardingData{
			OnboardingData: &palPb.OnboardingData{
				BankingDetails: &palPb.OnboardingData_BankingDetails{
					AccountNumber:     accountNumber,
					AccountHolderName: req.GetBankingDetails().GetAccountHolderName(),
					IfscCode:          req.GetBankingDetails().GetIfscCode(),
					BankName:          req.GetBankingDetails().GetBankName(),
				},
			},
		},
	}

	err = s.loanStepExecutionsDao.Update(ctx, loanStep, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if err != nil {
		logger.Error(ctx, "can't update loan step details", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	// setting payload as LoanApplicationESignVerificationSignalPayload since we need only empty payload, no need to
	// create a new one for banking details signal, hence reusing old one
	payload := emptyPb.Empty{}
	marPayload, _ := protojson.Marshal(&payload)

	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})
	lr.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
	if err = s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
	}); err != nil {
		logger.Error(ctx, "failed to update loan request for next action", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	err = s.rpcHelper.SendSignalSync(ctx, lr.GetOrchId(), string(palNs.LoanApplicationBankingDetailsSignal), marPayload)
	if err != nil {
		logger.Error(ctx, "failed to signal workflow for banking details confirmation", zap.Error(err))
	}

	res.Status = rpcPb.StatusOk()
	return res, nil
}

func validateAddBankingDetailsRequest(req *palPb.AddBankingDetailsRequest) error {
	// we are validating bank ifsc code to check whether given ifsc is supported on our end
	if !loanUtils.IsBankIfscSupportedForLoanApplication(req.GetBankingDetails().GetIfscCode()) {
		return loanErrors.ErrBankNotSupported
	}
	return nil
}

func (s *Service) getLoanStepForAddBankingDetails(ctx context.Context, req *palPb.AddBankingDetailsRequest) (*palPb.LoanStepExecution, error) {
	var lse *palPb.LoanStepExecution
	var err error
	// if lse id is present in the request then fetch lse from db directly using it
	// lse id input in request is done to make AddBankingDetails RPC more generic for wider use-cases
	// if lse id is not present in the request than fetch lse from db using loan request id and banking details step to keep changes backward compatible
	if req.GetLseId() != "" {
		lse, err = s.loanStepExecutionsDao.GetById(ctx, req.GetLseId())
	} else {
		lse, err = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS)
	}
	if err != nil {
		return nil, errors.Wrap(err, "error while fetching lse from db")
	}
	return lse, nil
}

// TODO(Diparth): Move this processing logic behind mandate specific processor
func (s *Service) addBankingDetailsForMandate(ctx context.Context, req *palPb.AddBankingDetailsRequest,
	loanRequest *palPb.LoanRequest, loanStep *palPb.LoanStepExecution, accountNumber string) (*palPb.AddBankingDetailsResponse, error) {
	userBankDetails := &palPb.MandateData_BankingDetails_AccountDetails{
		AccountNumber:     accountNumber,
		AccountHolderName: req.GetBankingDetails().GetAccountHolderName(),
		IfscCode:          req.GetBankingDetails().GetIfscCode(),
		BankName:          req.GetBankingDetails().GetBankName(),
	}
	var offerBankAccountConstraintsPresent bool
	if loanRequest.GetVendor() == palPb.Vendor_LENDEN {
		var err error
		offerBankAccountConstraintsPresent, userBankDetails, err = s.validateAndGetBankDetailsForLenden(ctx, req, loanRequest.GetOfferId(), accountNumber)
		if err != nil {
			logger.Error(ctx, "error validating bank details for Lenden", zap.Error(err), zap.String(logger.LOAN_STEP_EXECUTION_ID, loanStep.GetId()))
			return &palPb.AddBankingDetailsResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}

	// fetch all mandate requests from db to identify all bank accounts used in the past and populate banking details list section in alt account flow
	// handle errors on best effort basis since identifying old bank details used should not be a blocker to add new bank acount
	mandateReqs, _, getErr := s.mandateRequestsDao.GetByActorIdAndVendorAndLoanProgramPaginated(ctx, req.GetActorId(), req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram(), nil, 10)
	if getErr != nil {
		logger.Error(ctx, "error while fetching mandate requests from db", zap.Error(getErr))
	}

	loanStep = mandateHelper.GetLoanStepForMandateAltAccUpdate(loanStep, userBankDetails, mandateReqs)
	// Update lse with banking details shared by the user
	err := s.loanStepExecutionsDao.Update(ctx, loanStep, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
	if err != nil {
		logger.Error(ctx, "can't update loan step details in add banking details for mandate", zap.Error(err), zap.String(logger.LOAN_STEP_EXECUTION_ID, loanStep.GetId()))
		return &palPb.AddBankingDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{
		Vendor:      req.GetLoanHeader().GetVendor(),
		LoanProgram: req.GetLoanHeader().GetLoanProgram(),
	})

	allowUserToChangeAccount := true
	if offerBankAccountConstraintsPresent {
		allowUserToChangeAccount = false
	}
	mandateInitV2Dl, dlErr := deeplink.ConstructPlMandateInitScreenV2FromLse(ctx, deeplinkProvider, loanStep, userBankDetails, allowUserToChangeAccount, nil)
	if dlErr != nil {
		logger.Error(ctx, "error while generating pl mandate init screen v2 deeplink", zap.Error(dlErr),
			zap.String(logger.LOAN_STEP_EXECUTION_ID, loanStep.GetId()))
		return &palPb.AddBankingDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// updating deeplink in loan request to handle a case where user goes back to loan dashboard and comes back again on mandate screen
	// then in this case user should also see newly added bank account in the available account list and also as a default account
	updateErr := s.loanRequestsDao.Update(ctx, &palPb.LoanRequest{Id: req.GetLoanRequestId(), NextAction: mandateInitV2Dl}, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION})
	if updateErr != nil {
		logger.Error(ctx, "error while updating loan request while setting mandate init v2 dl as next action", zap.Error(updateErr),
			zap.String(logger.LOAN_STEP_EXECUTION_ID, loanStep.GetId()))
		return &palPb.AddBankingDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &palPb.AddBankingDetailsResponse{
		Status:   rpcPb.StatusOk(),
		Deeplink: mandateInitV2Dl,
	}, nil
}

func (s *Service) validateAndGetBankDetailsForLenden(ctx context.Context, req *palPb.AddBankingDetailsRequest,
	offerId, accountNumber string) (bool, *palPb.MandateData_BankingDetails_AccountDetails, error) {
	loanOffer, err := s.loanOffersDao.GetById(ctx, offerId)
	if err != nil {
		return false, nil, errors.Wrapf(err, "error getting loan offer by offer id: %s", offerId)
	}
	offerBankDetails := loanOffer.GetOfferConstraints().GetLendenConstraintInfo().GetBankDetails()
	if offerBankDetails != nil {
		// Prioritize bank details from offer constraints over user-entered details
		logger.Info(ctx, "returning details from Lenden offer constraints wherever possible", zap.String(logger.OFFER_ID, loanOffer.GetId()))
		offerAccountNumberSuffix := regexp.MustCompile(`\d+$`).FindString(offerBankDetails.GetAccountNumber())
		if len(offerAccountNumberSuffix) == 0 {
			return false, nil, errors.New("no trailing digits in offer bank account number")
		}
		if !strings.HasSuffix(accountNumber, offerAccountNumberSuffix) {
			return false, nil, errors.Errorf("account number does not match offer bank account number suffix: %s", offerAccountNumberSuffix)
		}
		return true, &palPb.MandateData_BankingDetails_AccountDetails{
			AccountNumber:     accountNumber,
			AccountHolderName: offerBankDetails.GetAccountHolderName(),
			IfscCode:          offerBankDetails.GetIfsc(),
			BankName:          offerBankDetails.GetBankName(),
		}, nil
	}
	return false, &palPb.MandateData_BankingDetails_AccountDetails{
		AccountNumber:     accountNumber,
		AccountHolderName: req.GetBankingDetails().GetAccountHolderName(),
		IfscCode:          req.GetBankingDetails().GetIfscCode(),
		BankName:          req.GetBankingDetails().GetBankName(),
	}, nil
}

func (s *Service) getCompleteAccountNumber(ctx context.Context, bankingDetails *palPb.AddBankingDetailsRequest_BankingDetails, lse *palPb.LoanStepExecution) (string, error) {
	var accountNumber string
	switch bankingDetails.GetAccountNum().(type) {
	case *palPb.AddBankingDetailsRequest_BankingDetails_AccountNumberPrefix:
		accountNumberPrefix := bankingDetails.GetAccountNumberPrefix()
		connectedSalaryAccountRes, err := s.connectedSalaryAccountProvider.GetConnectedSalaryAccount(ctx, lse)
		if err != nil {
			return "", errors.Wrap(err, "error getting connected salary account")
		}
		// If the user could not understand our UI hint and entered the complete account number, do not add the suffix.
		if strings.HasSuffix(accountNumberPrefix, connectedSalaryAccountRes.AccountNumberSuffix) {
			logger.Info(ctx, "complete account number entered, not appending suffix")
			accountNumber = accountNumberPrefix
		} else {
			logger.Info(ctx, "appending account number prefix and suffix")
			accountNumber = strings.Join([]string{accountNumberPrefix, connectedSalaryAccountRes.AccountNumberSuffix}, "")
		}
		return accountNumber, nil
	case *palPb.AddBankingDetailsRequest_BankingDetails_CompleteAccountNumber:
		return bankingDetails.GetCompleteAccountNumber(), nil
	default:
		// TODO(Brijesh): Replace below handling with error, once support for old app versions are deprecated
		if bankingDetails.GetAccountNumber() != "" {
			return bankingDetails.GetAccountNumber(), nil
		}
		return bankingDetails.GetCompleteAccountNumber(), nil
	}
}

// nolint:funlen
func (s *Service) GetLoanReviewDetails(ctx context.Context, req *palPb.GetLoanReviewDetailsRequest) (*palPb.GetLoanReviewDetailsResponse, error) {
	res := &palPb.GetLoanReviewDetailsResponse{
		Status:          rpcPb.StatusOk(),
		PersonalDetails: &palPb.GetLoanReviewDetailsResponse_PersonalDetails{},
		ContactDetails:  &palPb.GetLoanReviewDetailsResponse_ContactDetails{},
		OnboardingData:  &palPb.OnboardingData{},
	}

	lr, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to get loan request by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(lr.GetVendor()))
	userFeatProp, err := helper.GetUserFeatureProperty(ctx, req.GetActorId(), s.onbClient, s.savingsClient)
	if err != nil {
		logger.Error(ctx, "error in fetching user feature property", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loecs, err := helper.GetCDCEligibleLoecs(ctx, s.loecDao, req.GetActorId(), userFeatProp.IsFiSAHolder)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in fetching active loecs for actor", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if len(loecs) > 0 {
		res.DesiredLoanAmount = loecs[0].GetDataRequirementDetails().GetDesiredLoanAmount()
	}

	user, err := s.rpcHelper.GetUserByActorId(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "GetUserByActorId failed", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	res.GetContactDetails().PhoneNumber = user.GetProfile().GetPhoneNumber()
	res.GetContactDetails().EmailId = user.GetProfile().GetEmail()

	switch req.GetLoanHeader().GetLoanProgram() {
	case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_ETB, palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION, palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
		res.GetPersonalDetails().Name = user.GetProfile().GetPanName()
		res.GetPersonalDetails().Gender = user.GetProfile().GetKycGender()
		res.GetPersonalDetails().Pan = user.GetProfile().GetPAN()
		res.GetPersonalDetails().Dob = user.GetProfile().GetDateOfBirth()
		addressLse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(),
			helper.GetLseFlowByLrType(lr.GetType()),
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS)
		if err != nil {
			logger.Error(ctx, "error in fetching loan step for address", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		employmentLse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(),
			helper.GetLseFlowByLrType(lr.GetType()),
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT)
		if err != nil {
			logger.Error(ctx, "error in fetching loan step for employment", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.OnboardingData = &palPb.OnboardingData{
			AddressDetails:    addressLse.GetDetails().GetOnboardingData().GetAddressDetails(),
			EmploymentDetails: employmentLse.GetDetails().GetOnboardingData().GetEmploymentDetails(),
		}
	case palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
		userData, err := s.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: req.GetActorId()})
		if err != nil {
			return nil, errors.Wrap(err, "error in getting user data")
		}
		res.GetPersonalDetails().Name = userData.GetBestName()
		res.GetPersonalDetails().Gender = userData.GetGivenGender()
		res.GetPersonalDetails().Pan = userData.GetPan()
		res.GetPersonalDetails().Dob = userData.GetGivenDateOfBirth()
		res.GetPersonalDetails().MaritalStatus = userData.GetMaritalStatus()
		res.GetOnboardingData().AddressDetails = &palPb.OnboardingData_AddressDetails{
			AddressDetails: userData.GetAddress(),
			AddressType:    typesPb.AddressType_LOAN_COMMUNICATION,
		}
		// only basic address details are collected from user in eligibility flow, use them if present
		if userData.GetResidenceDetails().GetResidentialAddress().GetAddress().GetPostalCode() != "" {
			res.GetOnboardingData().AddressDetails = &palPb.OnboardingData_AddressDetails{
				AddressDetails: typesPb.GetFromBeAddress(userData.GetResidenceDetails().GetResidentialAddress().GetAddress()),
				AddressType:    typesPb.AddressType_LOAN_COMMUNICATION,
				ResidenceType:  userData.GetResidenceDetails().GetResidentialAddress().GetResidenceType(),
				MonthlyRent:    userData.GetResidenceDetails().GetMonthlyRent(),
			}
		}
		res.GetOnboardingData().EmploymentDetails = &palPb.OnboardingData_EmploymentDetails{
			Occupation:       userData.GetEmploymentDetails().GetEmploymentType(),
			OrganizationName: userData.GetEmploymentDetails().GetOrganizationName(),
			MonthlyIncome:    userData.GetEmploymentDetails().GetMonthlyIncome(),
			WorkEmail:        userData.GetEmploymentDetails().GetWorkEmail(),
			AnnualRevenue:    userData.GetEmploymentDetails().GetAnnualRevenue(),
			GSTIN:            userData.GetEmploymentDetails().GetGSTIN(),
		}
		if len(loecs) > 0 {
			res.LoanPurpose = loecs[0].GetDataRequirementDetails().GetLoanPurpose()
		}

	default:
		applicantLse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(),
			helper.GetLseFlowByLrType(lr.GetType()),
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_AND_DOB)
		if err != nil {
			logger.Error(ctx, "error in fetching loan step for applicant", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.GetPersonalDetails().Name = user.GetProfile().GetGivenName()
		res.GetPersonalDetails().Gender = user.GetProfile().GetGivenGender()
		res.GetPersonalDetails().Pan = applicantLse.GetDetails().GetApplicantData().GetPan()
		res.GetPersonalDetails().Dob = applicantLse.GetDetails().GetApplicantData().GetDob()

		addDetailsLse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(),
			helper.GetLseFlowByLrType(lr.GetType()),
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS)
		if err != nil {
			logger.Error(ctx, "error in fetching loan step for add applicant", zap.Error(err))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.OnboardingData = addDetailsLse.GetDetails().GetOnboardingData()
	}

	return res, nil
}

func (s *Service) SubmitReviewLoanDetails(ctx context.Context, req *palPb.SubmitReviewLoanDetailsRequest) (*palPb.SubmitReviewLoanDetailsResponse, error) {
	res := &palPb.SubmitReviewLoanDetailsResponse{
		Status: rpcPb.StatusOk(),
	}

	lr, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to get loan request by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(lr.GetVendor()))

	reviewDetailsLse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(),
		helper.GetLseFlowByLrType(lr.GetType()),
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REVIEW_DETAILS)
	if err != nil {
		logger.Error(ctx, "error in fetching loan step for review details", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})
	lr.NextAction = deeplinkProvider.GetApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lr.GetId())
	reviewDetailsLse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS

	txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, s.txnExecutorProvider)
	if txnExecErr != nil {
		logger.Error(ctx, "error in fetching txn executor from ctx", zap.Error(txnExecErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
		if updateErr := s.loanStepExecutionsDao.Update(txnCtx, reviewDetailsLse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		}); updateErr != nil {
			return errors.Wrap(updateErr, "error while updating lse")
		}

		if updateErr := s.loanRequestsDao.Update(txnCtx, lr, []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
		}); updateErr != nil {
			return errors.Wrap(updateErr, "error while updating lr")
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "failed to update si callback details", zap.Error(txnErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	payload := &emptyPb.Empty{}
	marPayload, _ := protojson.Marshal(payload)

	err = s.rpcHelper.SendSignalSync(ctx, reviewDetailsLse.GetOrchId(), string(palNs.LoanEligibilityReviewDetailsCompletedSignal), marPayload)
	if err != nil {
		logger.Error(ctx, "failed to signal workflow for review details confirmation", zap.Error(err))
	}

	return res, nil
}

func (s *Service) GetLoanDefaultDetails(
	ctx context.Context,
	req *palPb.GetLoanDefaultDetailsRequest,
) (*palPb.GetLoanDefaultDetailsResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	res := &palPb.GetLoanDefaultDetailsResponse{}

	loanAccount, err := s.loanAccountsDao.GetById(ctx, req.GetLoanAccountId())
	if err != nil {
		logger.Error(ctx, "failed to get loan account by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanInstallmentInfo, err := s.loanInstallmentInfoDao.GetByActiveAccountId(ctx, req.GetLoanAccountId())
	if err != nil {
		logger.Error(ctx, "failed to get loan installment info by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanInstallmentPayouts, err := s.loanInstallmentPayoutDao.GetByLoanInstallmentInfoId(ctx, loanInstallmentInfo.GetId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to get loan installment payouts by loan installment info id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	defaultDetails, err := helper.GetLoanDefaultDetails(loanInstallmentPayouts)
	if err != nil {
		logger.Error(ctx, "failed to get loan default details", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	bankDetails, bankDetailsErr := s.getBankingDetailsFromAccountId(ctx, loanAccount.GetId())
	if bankDetailsErr != nil {
		logger.Error(ctx, "failed to get banking details", zap.Error(bankDetailsErr))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanForeClosureProvider, err := s.loanDataProvider.FetchLoanForeClosureDetailsProvider(ctx, req.GetLoanHeader(), loanAccount.GetLmsPartner())
	if err != nil {
		logger.Error(ctx, "failed to fetch loan data provider", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if loanForeClosureProvider != nil {
		foreClosureDetails, foreClosureDetailsErr := loanForeClosureProvider.FetchLoanPreClosureDetailsFromVendor(ctx, loanAccount)
		if foreClosureDetailsErr != nil {
			// not returning error from here as there are cases e.g. newly created LL loan where we don't have foreclosure details yet at vendor,
			// hence need to gracefully handle for those. In frontend, this field has no use case, but failing due to unavailability from vendor.
			logger.Error(ctx, "unable to get the foreclosure details from vendor", zap.Error(foreClosureDetailsErr))
		}

		if foreClosureDetails != nil {
			foreClosureDetail := &palPb.ForeclosureDetails{}

			if foreClosureDetails.LoanPrincipalOutstandingAmount != nil {
				foreClosureDetail.PrincipalOutstandingAmount = foreClosureDetails.LoanPrincipalOutstandingAmount
			}
			if foreClosureDetails.LoanPreCloseAmount != nil {
				foreClosureDetail.TotalOutstandingAmount = foreClosureDetails.LoanPreCloseAmount
			}
			if foreClosureDetails.LoanInterestOutstandingAmount != nil {
				foreClosureDetail.InterestOutstandingAmount = foreClosureDetails.LoanInterestOutstandingAmount
			}
			if foreClosureDetails.LoanPenaltyAmount != nil {
				foreClosureDetail.PenaltyAmt = foreClosureDetails.LoanPenaltyAmount
			}
			if foreClosureDetails.LoanFeesAmount != nil {
				foreClosureDetail.FeesAmt = foreClosureDetails.LoanFeesAmount
			}
			if foreClosureDetails.LoanOtherCharges != nil {
				foreClosureDetail.OtherCharges = foreClosureDetails.LoanOtherCharges
			}

			res.ForeclosureDetails = foreClosureDetail
		}
	}

	applicationPhotoLink, err := s.getUserImageFromLivenessData(ctx, loanAccount.GetId())
	if err != nil {
		// Todo(Anupam) - Add error handling after testing on prod
		// not returning any error from here to not stop pushing collection cases due to this
		logger.Error(ctx, "failed to get applicant image by loan account id id", zap.Error(err), zap.String(logger.ACTOR_ID_V2, loanAccount.GetActorId()))
	}

	loanApplicant, err := s.loanApplicantDao.GetByActorId(ctx, loanAccount.GetActorId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to get loan applicant by actor id", zap.Error(err), zap.String(logger.ACTOR_ID, loanAccount.GetActorId()))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}
	if loanApplicant != nil {
		res.ContactDetails = &palPb.ContactDetails{
			AlternateContactNumber: loanApplicant.GetPersonalDetails().GetAlternatePhoneNumber(),
		}
	}
	res.UserImageLink = applicationPhotoLink
	res.BankAccountDetails = bankDetails
	res.LoanAccount = loanAccount
	res.LoanInstallmentInfo = loanInstallmentInfo
	res.DefaultDetails = defaultDetails
	res.Schedule = loanInstallmentPayouts
	res.Status = rpcPb.StatusOk()
	return res, nil
}

func (s *Service) getBankingDetailsFromAccountId(ctx context.Context, loanAccountId string) (*typesPb.BankAccountDetails, error) {
	loanRequests, err := s.loanRequestsDao.GetByLoanAccountIdAndType(ctx, loanAccountId, palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
	if err != nil {
		logger.Error(ctx, "failed to get loan default details", zap.Error(err))
		return nil, errors.Wrap(err, "failed to get loan default details")
	}

	if len(loanRequests) == 0 {
		logger.Error(ctx, "GetLoanDefaultDetails: empty loan requests")
		return nil, errors.Wrap(err, "GetLoanDefaultDetails: empty loan requests")
	}

	// For acquire to lend users, return the banking details
	loanStep, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, loanRequests[0].GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in fetching loan step from loanReqId", zap.Error(err))
		return nil, errors.Wrap(err, "error in fetching loan step from loanReqId")
	}

	return &typesPb.BankAccountDetails{
		AccountNumber: loanStep.GetDetails().GetOnboardingData().GetBankingDetails().GetAccountNumber(),
		Ifsc:          loanStep.GetDetails().GetOnboardingData().GetBankingDetails().GetIfscCode(),
		BankName:      loanStep.GetDetails().GetOnboardingData().GetBankingDetails().GetBankName(),
	}, nil
}

func (s *Service) isSegmentMember(ctx context.Context, actorId string) bool {
	if s.conf.InstantCashSegmentId != "" {
		segRes, err := s.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: []string{s.conf.InstantCashSegmentId},
			LatestBy:   timestampPb.Now(),
		})
		if rpcErr := epifigrpc.RPCError(segRes, err); rpcErr != nil {
			// log error and move forward as default values will be shown
			logger.Error(ctx, "error while checking if user belongs to pre approved loan segment", zap.Error(rpcErr))
		}
		if segRes != nil &&
			segRes.GetSegmentMembershipMap()[s.conf.InstantCashSegmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
			segRes.GetSegmentMembershipMap()[s.conf.InstantCashSegmentId].GetIsActorMember() {
			return true
		}
	}
	return false
}

func (s *Service) GetLoanIdByHeaderAndActorId(ctx context.Context, req *palPb.GetLoanIdByHeaderAndActorIdRequest) (*palPb.GetLoanIdByHeaderAndActorIdResponse, error) {
	var (
		res     = &palPb.GetLoanIdByHeaderAndActorIdResponse{}
		actorId = req.GetActorId()
		vendor  = req.GetLoanHeader().GetVendor()
		lp      = req.GetLoanHeader().GetLoanProgram()
	)

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendor))
	loanAccount, err := s.loanAccountsDao.GetByActorIdVendorAndLoanProgram(ctx, actorId, vendor, lp)
	if err != nil || len(loanAccount) == 0 {
		logger.Error(ctx, "Unable to get loan account by actorId, vendor and loanProgram", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, err
	}
	if len(loanAccount) > 1 {
		logger.Error(ctx, "Getting more than 1 loan account for given actor id and header")
	}
	res.Status = rpcPb.StatusOk()
	res.LoanId = loanAccount[0].Id
	return res, nil
}

func (s *Service) InitiateSiSetup(ctx context.Context, req *palPb.InitiateSiSetupRequest) (*palPb.InitiateSiSetupResponse, error) {
	vendor := req.GetLoanHeader().GetVendor()
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendor))

	// check if some active SETUP_SI req is already present
	loanReqs, loanReqErr := s.loanRequestsDao.GetByActorIdTypesStatusAndLoanProgram(ctx, req.GetActorId(), []palPb.LoanRequestType{palPb.LoanRequestType_LOAN_REQUEST_TYPE_SETUP_SI}, []palPb.LoanRequestStatus{
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED,
	}, []palPb.LoanProgram{req.GetLoanHeader().GetLoanProgram()}, nil, nil)
	if loanReqErr != nil && !errors.Is(loanReqErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to fetch si lr", zap.Error(loanReqErr))
		return &palPb.InitiateSiSetupResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	var loanRequest *palPb.LoanRequest
	if len(loanReqs) > 0 {
		loanRequest = loanReqs[0]
		if loanRequest.GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED {
			// workflow already initiated
			return &palPb.InitiateSiSetupResponse{
				Status:        rpcPb.StatusOk(),
				LoanRequestId: loanRequest.GetId(),
			}, nil
		}
	}

	if loanRequest == nil {
		orchId := uuid.New().String()
		var err error
		loanRequest, err = s.loanRequestsDao.Create(ctx, &palPb.LoanRequest{
			ActorId:       req.GetActorId(),
			OrchId:        orchId,
			Vendor:        vendor,
			LoanAccountId: req.GetLoanAccountId(),
			Type:          palPb.LoanRequestType_LOAN_REQUEST_TYPE_SETUP_SI,
			Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
			SubStatus:     palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_CREATED,
			LoanProgram:   req.GetLoanHeader().GetLoanProgram(),
		})
		if err != nil {
			logger.Error(ctx, "error while creating loan request", zap.Error(err), zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanAccountId()))
			if errors.Is(err, epifierrors.ErrDuplicateEntry) {
				return &palPb.InitiateSiSetupResponse{
					Status: rpcPb.StatusAlreadyExistsWithDebugMsg("request already exists for given actor id and vendor"),
				}, nil
			}
			return &palPb.InitiateSiSetupResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
	}

	payload := &palWorkflowPb.SetupSiPayload{
		LoanHeader: &palPb.LoanHeader{
			LoanProgram: req.GetLoanHeader().GetLoanProgram(),
			Vendor:      req.GetLoanHeader().GetVendor(),
		},
		LoanRequestId: loanRequest.GetId(),
		ActorId:       req.GetActorId(),
	}

	wfPayload, marErr := protojson.Marshal(payload)
	if marErr != nil {
		logger.Error(ctx, "error while marshaling workflow setup si payload", zap.Error(marErr), zap.Any(logger.LOAN_REQUEST_ID, loanRequest.GetId()))
		return &palPb.InitiateSiSetupResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	resp, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: req.GetActorId(),
			Version: workflow.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(palNs.SetupSi),
			Payload: wfPayload,
			ClientReqId: &workflow.ClientReqId{
				Id:     loanRequest.GetOrchId(),
				Client: workflow.Client_PRE_APPROVED_LOAN,
			},
			Ownership:        helper.GetPalOwnership(req.GetLoanHeader().GetVendor()),
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})

	if te := epifigrpc.RPCError(resp, err); te != nil && !resp.GetStatus().IsAlreadyExists() && !resp.GetStatus().IsAlreadyProcessed() {
		logger.ErrorNoCtx("error while executing initiate workflow rpc", zap.Error(te),
			zap.Any(logger.LOAN_REQUEST_ID, loanRequest.GetId()))
		return &palPb.InitiateSiSetupResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &palPb.InitiateSiSetupResponse{
		Status:        rpcPb.StatusOk(),
		LoanRequestId: loanRequest.GetId(),
	}, nil
}

func (s *Service) GetVendorApplicantId(ctx context.Context, req *palPb.GetVendorApplicantIdRequest) (*palPb.GetVendorApplicantIdResponse, error) {
	vendor := req.GetLoanHeader().GetVendor()
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendor))

	applicant, err := s.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, req.GetActorId(), req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram(), palEnumsPb.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in fetching loan applicant from actor id", zap.Error(err))
		return &palPb.GetVendorApplicantIdResponse{Status: rpcPb.StatusInternal()}, nil
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &palPb.GetVendorApplicantIdResponse{Status: rpcPb.StatusRecordNotFound()}, nil
	}

	return &palPb.GetVendorApplicantIdResponse{
		Status:            rpcPb.StatusInternal(),
		VendorApplicantId: applicant.GetVendorApplicantId(),
	}, nil
}

func (s *Service) GetPWARedirectionDetails(ctx context.Context, req *palPb.GetPWARedirectionDetailsRequest) (*palPb.GetPWARedirectionDetailsResponse, error) {
	vendor := req.GetLoanHeader().GetVendor()
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(vendor))
	switch vendor {
	case palPb.Vendor_MONEYVIEW:
		pwaUrl, err := s.getMoneyViewPWARedirectionUrl(ctx, req)
		if err != nil {
			if errors.Is(err, epifierrors.ErrFailedPrecondition) {
				logger.Error(ctx, "error getting PWA redirection details", zap.Error(err))
				return &palPb.GetPWARedirectionDetailsResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg(err.Error())}, nil
			}
			logger.Error(ctx, "failed to get mv pwa url", zap.Error(err), zap.Any(logger.REQUEST, req))
			return &palPb.GetPWARedirectionDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
		return &palPb.GetPWARedirectionDetailsResponse{Status: rpc.StatusOk(), PwaUrl: pwaUrl}, nil
	case palPb.Vendor_ABFL:
		pwaRedirectionDetailsProvider, err := s.loanDataProvider.FetchPwaRedirectionDetailsProvider(ctx, req.GetLoanHeader())
		if err != nil {
			logger.Error(ctx, "failed to get data provider", zap.Error(err), zap.Any(logger.REQUEST, req))
			return &palPb.GetPWARedirectionDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
		pwaUrlResp, err := pwaRedirectionDetailsProvider.GetPwaJourneyUrl(ctx, &providers.GetPwaJourneyUrlRequest{
			LoanRequestId: req.GetLoanRequestId(),
		})
		if err != nil {
			logger.Error(ctx, "failed to get redirection link", zap.Error(err), zap.Any(logger.REQUEST, req))
			return &palPb.GetPWARedirectionDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
		return &palPb.GetPWARedirectionDetailsResponse{Status: rpc.StatusOk(), PwaUrl: pwaUrlResp.PwaUrl}, nil
	default:
		logger.Error(ctx, "unsupported vendor for pwa redirection details", zap.String(logger.VENDOR, vendor.String()), zap.Any(logger.REQUEST, req))
		return &palPb.GetPWARedirectionDetailsResponse{Status: rpc.StatusInternalWithDebugMsg("unsupported vendor for fetching pwa redirection details")}, nil
	}
}

func (s *Service) getMoneyViewPWARedirectionUrl(ctx context.Context, req *palPb.GetPWARedirectionDetailsRequest) (string, error) {
	var leadId string
	var userType mvPb.MvUserType
	switch req.GetLoanIdentifier().(type) {
	case *palPb.GetPWARedirectionDetailsRequest_LoanRequestId:
		lr, lrErr := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
		if lrErr != nil {
			return "", errors.Wrap(lrErr, "error fetching loan request by id")
		}
		leadId = lr.GetVendorRequestId()
		userType = loanProgramToUserType[lr.GetLoanProgram()]
	case *palPb.GetPWARedirectionDetailsRequest_LoanAccountId:
		la, laErr := s.loanAccountsDao.GetById(ctx, req.GetLoanAccountId())
		if laErr != nil {
			return "", errors.Wrap(laErr, "error fetching loan account by id")
		}
		leadId = la.GetAccountNumber()
		userType = loanProgramToUserType[la.GetLoanProgram()]
	default:
		return "", fmt.Errorf("unhandled request identifier for fetching pwa redirection details")
	}
	vgRes, vgErr := s.mvVgClient.GetPWAJourneyUrl(ctx, &moneyview.GetPWAJourneyUrlRequest{
		Header:   &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MONEYVIEW},
		LeadId:   leadId,
		UserType: userType,
	})
	if te := epifigrpc.RPCError(vgRes, vgErr); te != nil {
		if vgRes.GetStatus().IsFailedPrecondition() {
			logger.Error(ctx, "error getting PWA journey URL", zap.Error(te))
			return "", errors.Wrap(epifierrors.ErrFailedPrecondition, te.Error())
		}
		return "", errors.Wrap(te, "unable to get the correct response from the mv vg pwa url")
	}
	return vgRes.GetSsoUrl(), nil
}

func (s *Service) GetMandateViewDataV2(ctx context.Context, req *palPb.GetMandateViewDataV2Request) (*palPb.GetMandateViewDataV2Response, error) {
	validateErr := validateGetMandateViewDataV2Request(req)
	if validateErr != nil {
		logger.Error(ctx, "invalid params in GetMandateViewDataV2Request", zap.Error(validateErr))
		return &palPb.GetMandateViewDataV2Response{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	lse, err := s.loanStepExecutionsDao.GetById(ctx, req.GetLseId())
	if err != nil {
		logger.Error(ctx, "failed to fetch lse for mandate view rpc", zap.Error(err),
			zap.String(logger.LOAN_STEP_EXECUTION_ID, req.GetLseId()))
		return &palPb.GetMandateViewDataV2Response{
			Status: rpc.StatusInternal(),
		}, nil
	}
	mandateViewProvider, viewErr := s.mandateViewFactory.GetMandateViewProvider(ctx, req.GetLoanHeader())
	if viewErr != nil {
		logger.Error(ctx, "error while identifying provider in mandate view factory", zap.Error(viewErr),
			zap.String(logger.LOAN_STEP_EXECUTION_ID, req.GetLseId()), zap.Any(logger.LOAN_HEADER, req.GetLoanHeader()))
		return &palPb.GetMandateViewDataV2Response{
			Status: rpc.StatusInternal(),
		}, nil
	}
	accDetails := &palPb.MandateData_BankingDetails_AccountDetails{
		AccountNumber:     req.GetMandateDetails().GetBankAccountDetails().GetAccountNumber(),
		AccountHolderName: req.GetMandateDetails().GetBankAccountDetails().GetAccountHolderName(),
		IfscCode:          req.GetMandateDetails().GetBankAccountDetails().GetIfscCode(),
		BankName:          req.GetMandateDetails().GetBankAccountDetails().GetBankName(),
	}
	validateLsErr, userFacingErrMsg := mandateViewProvider.ValidateLoanStep(ctx, req.GetLoanHeader(), lse, accDetails)
	if validateLsErr != nil {
		if errors.Is(validateLsErr, epifierrors.ErrResourceExhausted) {
			logger.Error(ctx, "max limit exhausted err while generating mandate setup screen dl", zap.Error(validateLsErr),
				zap.String(logger.LOAN_STEP_EXECUTION_ID, req.GetLseId()))
			return &palPb.GetMandateViewDataV2Response{
				Status:           rpcPb.StatusResourceExhausted(),
				UserFacingErrMsg: userFacingErrMsg,
			}, nil
		}
		if errors.Is(validateLsErr, epifierrors.ErrAlreadyExists) {
			logger.Error(ctx, "customer in cool off period, one mandate already in progress for the customer",
				zap.String(logger.LOAN_PROGRAM, req.GetLoanHeader().GetLoanProgram().String()),
				zap.String(logger.VENDOR, req.GetLoanHeader().GetVendor().String()),
				zap.String(logger.LOAN_STEP_EXECUTION_ID, req.GetLseId()))
			return &palPb.GetMandateViewDataV2Response{
				Status:           rpc.StatusAlreadyExists(),
				UserFacingErrMsg: userFacingErrMsg,
			}, nil
		}
		if errors.Is(validateLsErr, epifierrors.ErrInvalidArgument) {
			logger.Error(ctx, "invalid argument err while validating loan step for mandate view", zap.Error(validateLsErr),
				zap.String(logger.LOAN_PROGRAM, req.GetLoanHeader().GetLoanProgram().String()),
				zap.String(logger.VENDOR, req.GetLoanHeader().GetVendor().String()),
				zap.String(logger.LOAN_STEP_EXECUTION_ID, req.GetLseId()))
			return &palPb.GetMandateViewDataV2Response{
				Status:           rpcPb.StatusInvalidArgument(),
				UserFacingErrMsg: userFacingErrMsg,
			}, nil
		}
		logger.Error(ctx, "error in mandate view validate loan step", zap.Error(validateLsErr),
			zap.String("userFacingErrMsg", userFacingErrMsg),
			zap.String(logger.LOAN_STEP_EXECUTION_ID, req.GetLseId()))
		return &palPb.GetMandateViewDataV2Response{
			Status:           rpc.StatusInternal(),
			UserFacingErrMsg: userFacingErrMsg,
		}, nil
	}

	mandateDl, mandateErr := mandateViewProvider.GetLoansMandateSetupScreen(ctx, req.GetLoanHeader(), lse, accDetails, req.GetMandateDetails().GetAuthType())
	if mandateErr != nil {
		logger.Error(ctx, "error in mandate view setup screen step", zap.Error(mandateErr),
			zap.String(logger.LOAN_STEP_EXECUTION_ID, req.GetLseId()))
		return &palPb.GetMandateViewDataV2Response{
			Status: rpc.StatusInternal(),
		}, nil
	}

	procErr := mandateViewProvider.PostDlGenerationProcessing(ctx, req.GetLoanHeader(), lse.GetId(), accDetails)
	if procErr != nil {
		logger.Error(ctx, "error while executing post dl generation processing step in mandate view provider", zap.Error(procErr),
			zap.String(logger.LOAN_STEP_EXECUTION_ID, req.GetLseId()))
		return &palPb.GetMandateViewDataV2Response{
			Status: rpc.StatusInternal(),
		}, nil
	}

	logger.Info(ctx, "Bank account used by user for mandate screen V2", zap.Any(logger.LOAN_STEP_EXECUTION_ID, lse.GetId()),
		zap.Any(logger.BANK_IDENTIFIER, accDetails.GetBankName()), zap.Any(logger.ACCOUNT_NUMBER, mask.GetMaskedAccountNumber(accDetails.GetAccountNumber(), "")))

	return &palPb.GetMandateViewDataV2Response{
		Status:   rpcPb.StatusOk(),
		Deeplink: mandateDl,
	}, nil
}

func validateGetMandateViewDataV2Request(req *palPb.GetMandateViewDataV2Request) error {
	switch {
	case req.GetLseId() == "":
		return errors.New("lse id cannot be empty")
	case req.GetLoanRequestId() == "":
		return errors.New("loan request id cannot be empty")
	case req.GetLoanHeader() == nil:
		return errors.New("loan header cannot be nil")
	default:
		return nil
	}
}

func (s *Service) ProcessOffAppLoanRepayment(ctx context.Context, request *palPb.ProcessOffAppLoanRepaymentRequest) (*palPb.ProcessOffAppLoanRepaymentResponse, error) {
	la, err := s.loanAccountsDao.GetById(ctx, request.GetLoanAccountId())
	if err != nil {
		logger.Error(ctx, "failed to fetch loan account from db", zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		return &palPb.ProcessOffAppLoanRepaymentResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch loan account from db"),
		}, nil
	}
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(la.GetVendor()))
	if s.DynConf.Flags().IsOffAppPaymentV2Enabled() {
		orchId := crypto.GetSHA256Hash("off-app-prepay", request.GetPaymentReferenceId())
		lprType := palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE
		loanPaymentReq, err := s.loanPaymentRequestDao.GetByOrchId(ctx, orchId)
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			precloseDecider, err := s.prepayFactory.GetLoanPreClosureDecider(ctx, &palPb.LoanHeader{
				LoanProgram: la.GetLoanProgram(),
				Vendor:      la.GetVendor(),
				LoanType:    la.GetLoanType(),
			}, la.GetLmsPartner())
			if err != nil {
				logger.Error(ctx, "failed to get precloseDecider", zap.Error(err), zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId))
				return &palPb.ProcessOffAppLoanRepaymentResponse{
					Status: rpc.StatusInternalWithDebugMsg("failed to get precloseDecider"),
				}, nil
			}
			preClose, err := precloseDecider.ShouldPreCloseTheLoanOnPayment(ctx, la.GetId(), request.GetRepaidAmount())
			if err != nil {
				logger.Error(ctx, "failed to check whether to preclose account or not", zap.Error(err), zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId))
				return &palPb.ProcessOffAppLoanRepaymentResponse{
					Status: rpc.StatusInternalWithDebugMsg("failed to check whether to preclose account or not"),
				}, nil
			}
			if !preClose {
				lprType = palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM
			}
			loanPaymentReq, err = s.loanPaymentRequestDao.Create(ctx, &palPb.LoanPaymentRequest{
				ActorId:   la.GetActorId(),
				AccountId: la.GetId(),
				OrchId:    orchId,
				Amount:    request.GetRepaidAmount(),
				Details: &palPb.LoanPaymentRequestDetails{
					Utr:               request.GetPaymentReferenceId(),
					TxnInitiationTime: request.GetPaymentTime(),
					PaymentProtocol:   request.GetPaymentProtocol(),
					TxnSettlementTime: request.GetTxnSettlementTime(),
					TxnProvenance:     request.GetPaymentProvenance(),
					VendorSpecificDetails: &palPb.LoanPaymentRequestDetails_LiquiloansPaymentDetails{
						LiquiloansPaymentDetails: &palPb.LiquiloansPaymentDetails{
							SkipChargeCollection: request.GetSkipChargeCollection(),
						},
					},
				},
				Type:   lprType,
				Status: palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
			})
			if err != nil {
				logger.Error(ctx, "failed to create payment request entry", zap.Error(err), zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId))
				return &palPb.ProcessOffAppLoanRepaymentResponse{
					Status: rpc.StatusInternalWithDebugMsg("failed to create payment request entry"),
				}, nil
			}
		case err != nil:
			logger.Error(ctx, "failed to fetch loan payment request", zap.Error(err), zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId))
			return &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to fetch loan payment request"),
			}, nil
		default:
			logger.Info(ctx, "loan payment entry already exists for given reference id", zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId))
		}
		payload := &palWorkflowPb.LoanOffAppPrepayPayload{
			Vendor:      la.GetVendor(),
			LoanProgram: la.GetLoanProgram(),
			RequestType: lprType,
		}
		marPayload, marPayloadErr := protojson.Marshal(payload)
		if marPayloadErr != nil {
			logger.Error(ctx, "failed to marshal workflow payload", zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId), zap.Error(marPayloadErr))
			return &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to marshal workflow payload"),
			}, nil
		}
		err = s.initiateWorkflowV2(ctx, &workflow.ClientReqId{Id: loanPaymentReq.GetOrchId(), Client: workflow.Client_PRE_APPROVED_LOAN}, la.GetActorId(), marPayload, celestialPkg.GetTypeEnumFromWorkflowType(palNs.LoanOffAppPrePay), workflow.Version_V0)
		if err != nil {
			if errors.Is(err, epifierrors.ErrAlreadyExists) {
				logger.Info(ctx, "workflow already exists for the given ayment request", zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId))
				return &palPb.ProcessOffAppLoanRepaymentResponse{
					Status: rpc.StatusAlreadyExists(),
				}, nil
			}
			logger.Error(ctx, "error initiating loan off app prepay workflow", zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId), zap.Error(err))
			return &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("error initiating loan off app prepay workflow"),
			}, nil
		}
	} else {
		payload := &palWorkflowPb.ProcessOffAppRepaymentPayload{
			Lender:             la.GetVendor(),
			PaymentReferenceId: request.GetPaymentReferenceId(),
			RepaidAmount:       request.GetRepaidAmount(),
			PaymentTime:        request.GetPaymentTime(),
			LoanAccountId:      la.GetId(),
			LoanAccountNumber:  la.GetAccountNumber(),
			PaymentMode:        request.GetPaymentProtocol().String(),
		}
		marPayload, marPayloadErr := protojson.Marshal(payload)
		if marPayloadErr != nil {
			logger.Error(ctx, "failed to marshal workflow payload", zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId), zap.Error(marPayloadErr))
			return &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("failed to marshal workflow payload"),
			}, nil
		}
		// using hash to avoid exposing payment reference number on temporal dashboard
		err = s.initiateWorkflowV2(ctx, &workflow.ClientReqId{Id: crypto.GetSHA256Hash(request.GetPaymentReferenceId()), Client: workflow.Client_PRE_APPROVED_LOAN}, la.GetActorId(), marPayload, celestialPkg.GetTypeEnumFromWorkflowType(palNs.ProcessOffAppRepayment), workflow.Version_V0)
		if err != nil {
			if errors.Is(err, epifierrors.ErrAlreadyExists) {
				return &palPb.ProcessOffAppLoanRepaymentResponse{
					Status: rpc.StatusAlreadyExists(),
				}, nil
			}
			logger.Error(ctx, "error initiating off app repayment workflow", zap.String(logger.ACCOUNT_ID, request.GetLoanAccountId()), zap.String(logger.REFERENCE_ID, request.PaymentReferenceId), zap.Error(err))
			return &palPb.ProcessOffAppLoanRepaymentResponse{
				Status: rpc.StatusInternalWithDebugMsg("error initiating off app repayment workflow"),
			}, nil
		}
	}
	return &palPb.ProcessOffAppLoanRepaymentResponse{
		Status: rpc.StatusOk(),
	}, nil
}
func (s *Service) GetForeclosureDetails(ctx context.Context, req *palPb.GetForeclosureDetailsRequest) (*palPb.GetForeclosureDetailsResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	loanAccount, err := s.loanAccountsDao.GetById(ctx, req.GetLoanAccountId())
	if err != nil {
		logger.Error(ctx, "error while fetching loan account", zap.Error(err), zap.Any(logger.LOAN_ACCOUNT_ID, req.GetLoanAccountId()))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &palPb.GetForeclosureDetailsResponse{
				Status: rpcPb.StatusFailedPreconditionWithDebugMsg("loan account does not exists in db"),
			}, nil
		}
		return &palPb.GetForeclosureDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching loan account from db"),
		}, nil
	}
	loanForeClosureProvider, err := s.loanDataProvider.FetchLoanForeClosureDetailsProvider(ctx, req.GetLoanHeader(), loanAccount.GetLmsPartner())
	if err != nil {
		logger.Error(ctx, "Error while fetching loan foreclosure provider", zap.Error(err), zap.Any(logger.LOAN_ACCOUNT_ID, req.GetLoanAccountId()))
		return &palPb.GetForeclosureDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("Error while fetching loan foreclosure provider"),
		}, nil
	}
	resp, err := loanForeClosureProvider.FetchLoanPreClosureDetailsFromVendor(ctx, loanAccount)
	if err != nil {
		logger.Error(ctx, "error while fetching foreclosure details", zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()), zap.Error(err))
		return &palPb.GetForeclosureDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching foreclosure details from vendor"),
		}, nil
	}
	return &palPb.GetForeclosureDetailsResponse{
		Status: rpcPb.StatusOk(),
		Details: &palPb.GetForeclosureDetailsResponse_Details{
			ForeclosureAmount: resp.LoanPreCloseAmount,
		},
	}, nil
}

func (s *Service) InitiateLoanClosure(ctx context.Context, req *palPb.InitiateLoanClosureRequest) (*palPb.InitiateLoanClosureResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	account, err := s.loanAccountsDao.GetById(ctx, req.GetLoanAccountId())
	if err != nil {
		logger.Error(ctx, "error while fetching loan account from db", zap.Error(err), zap.Any(logger.LOAN_ACCOUNT_ID, req.GetLoanAccountId()))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &palPb.InitiateLoanClosureResponse{
				Status: rpcPb.StatusFailedPreconditionWithDebugMsg("loan account not found in db"),
			}, nil
		}
		return &palPb.InitiateLoanClosureResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching loan account from db"),
		}, nil
	}

	lrId, err := s.initiateLoanClosureFlow(ctx, account, req.GetLoanPaymentRequestId())
	if err != nil {
		logger.Error(ctx, "error while initiating loan closure flow", zap.Error(err), zap.Any(logger.LOAN_ACCOUNT_ID, account.GetId()))
		if errors.Is(err, epifierrors.ErrAlreadyExists) {
			return &palPb.InitiateLoanClosureResponse{
				Status: rpcPb.StatusAlreadyExistsWithDebugMsg("one active loan closure request already exists"),
			}, nil
		}
		return &palPb.InitiateLoanClosureResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while initiating loan closure flow"),
		}, nil
	}

	return &palPb.InitiateLoanClosureResponse{
		Status:        rpcPb.StatusOk(),
		LoanRequestId: lrId,
	}, nil
}

func (s *Service) GetApplicationStatusSync(ctx context.Context, req *palPb.GetApplicationStatusSyncRequest) (*palPb.GetApplicationStatusSyncResponse, error) {
	var loanRequest *palPb.LoanRequest
	var err error
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	loanRequest, err = s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "GetApplicationStatusSync: error while fetching loan request", zap.String("lrId", req.GetLoanRequestId()),
			zap.String("vendor", req.GetLoanHeader().GetVendor().String()), zap.String("program", req.GetLoanHeader().GetLoanProgram().String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &palPb.GetApplicationStatusSyncResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if loanRequest.GetActorId() != req.GetActorId() {
		logger.Error(ctx, "actor id mismatch", zap.String("expected", loanRequest.GetActorId()), zap.String("actual", req.GetActorId()))
		return &palPb.GetApplicationStatusSyncResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}

	// fetch the workflow id from the loan request id passed.
	celestialRes, err := s.celestialClient.GetWorkflowStatus(ctx, &celestial.GetWorkflowStatusRequest{
		Identifier: &celestial.GetWorkflowStatusRequest_ClientRequestId{
			ClientRequestId: &celestial.ClientReqId{
				Id:     loanRequest.GetOrchId(),
				Client: workflow.Client_PRE_APPROVED_LOAN,
			},
		},
		Ownership: helper.GetPalOwnership(loanRequest.GetVendor()),
	})
	if te := epifigrpc.RPCError(celestialRes, err); te != nil {
		logger.Error(ctx, "failed to fetch workflow ids from the loan request id", zap.Error(err))
		return &palPb.GetApplicationStatusSyncResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	wfResp, err := s.communicateWithWorkflowInSync(ctx, &palWorkflowPb.SyncProxyRequest{TargetWorkflowId: celestialRes.GetWorkflowRequest().GetId()})
	// if error in executing sync proxy workflow, return the loan request with OK status to send the next action that is already there in LR next action
	// TODO(@Shivansh): Check if we need to return error screen from here
	if err != nil {
		logger.Error(ctx, "error communicating with workflow", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palPb.GetApplicationStatusSyncResponse{
			Status: rpcPb.StatusOk(),
		}, nil
	}
	loanRequest.NextAction = wfResp.GetNextAction()

	return &palPb.GetApplicationStatusSyncResponse{
		Status:     rpcPb.StatusOk(),
		NextAction: wfResp.GetNextAction(),
	}, nil
}

func (s *Service) getNextActionInSync(ctx context.Context, lr *palPb.LoanRequest) (*deeplinkPb.Deeplink, error) {
	celestialRes, err := s.celestialClient.GetWorkflowStatus(ctx, &celestial.GetWorkflowStatusRequest{
		Identifier: &celestial.GetWorkflowStatusRequest_ClientRequestId{
			ClientRequestId: &celestial.ClientReqId{
				Id:     lr.GetOrchId(),
				Client: workflow.Client_PRE_APPROVED_LOAN,
			},
		},
		Ownership: helper.GetPalOwnership(lr.GetVendor()),
	})
	if te := epifigrpc.RPCError(celestialRes, err); te != nil {
		return nil, te
	}

	wfRes, err := s.communicateWithWorkflowInSync(ctx, &palWorkflowPb.SyncProxyRequest{TargetWorkflowId: celestialRes.GetWorkflowRequest().GetId()})
	if err != nil {
		return nil, err
	}
	return wfRes.GetNextAction(), nil
}

func (s *Service) communicateWithWorkflowInSync(ctx context.Context, wfPayload *palWorkflowPb.SyncProxyRequest) (*palWorkflowPb.SyncProxyResponse, error) {
	wfResponse := &palWorkflowPb.SyncProxyResponse{}
	err := workflowPkg.Execute(ctx, s.temporalClient, palNs.PlSyncProxy, wfPayload, wfResponse, nil)
	if err != nil {
		return nil, errors.Wrap(err, "unable to execute proxy workflow")
	}
	if len(wfResponse.GetError()) > 0 {
		return nil, errors.Wrap(errors.New("error returned from sync proxy workflow execution"), wfResponse.GetError())
	}

	return wfResponse, nil
}

func (s *Service) updateLoecsDataRequirementFields(ctx context.Context, actorId string, fieldValuesMap map[palEnumsPb.FieldId]interface{}) error {
	userFeatProp, err := helper.GetUserFeatureProperty(ctx, actorId, s.onbClient, s.savingsClient)
	if err != nil {
		return errors.Wrap(err, "error in fetching user property")
	}

	loecs, err := helper.GetCDCEligibleLoecs(ctx, s.loecDao, actorId, userFeatProp.IsFiSAHolder)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return errors.Wrap(err, "error in fetching active loecs for actor")
	}

	for _, loec := range loecs {
		newCtx := epificontext.WithOwnership(ctx, helper.GetPalOwnership(loec.GetVendor()))
		if loec.GetDataRequirementDetails() == nil {
			loec.DataRequirementDetails = &palPb.DataRequirementDetails{}
		}
		if val, exists := fieldValuesMap[palEnumsPb.FieldId_LOAN_PURPOSE]; exists {
			loec.GetDataRequirementDetails().LoanPurpose = val.(palEnumsPb.LoanPurpose)
		}
		if val, exists := fieldValuesMap[palEnumsPb.FieldId_DESIRED_LOAN_AMOUNT]; exists {
			loec.GetDataRequirementDetails().DesiredLoanAmount = val.(*money.Money)
		}

		if errUpdateLoec := s.loecDao.Update(newCtx, loec, []palPb.LoanOfferEligibilityCriteriaFieldMask{
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS,
		}); errUpdateLoec != nil {
			return errors.Wrap(errUpdateLoec, "error in updating loec")
		}
	}

	return nil
}

// AddEmploymentDetailsSync adds the employment details given by the user in sync fashion. This will add the details in the loan step details
// and then send a signal to PL workflow, that will validate the data presence in db and will update the appropriate next action in the loan request.
func (s *Service) AddEmploymentDetailsSync(ctx context.Context, req *palPb.AddEmploymentDetailsRequest) (*palPb.AddEmploymentDetailsResponse, error) {
	var loanRequest *palPb.LoanRequest
	var err error

	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	// step 1: fetch the loan request from the db
	loanRequest, err = s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "AddEmploymentDetailsSync: error while fetching loan request", zap.String("lrId", req.GetLoanRequestId()),
			zap.String("vendor", req.GetLoanHeader().GetVendor().String()), zap.String("program", req.GetLoanHeader().GetLoanProgram().String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &palPb.AddEmploymentDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if loanRequest.GetActorId() != req.GetActorId() {
		logger.Error(ctx, "actor id mismatch", zap.String("expected", loanRequest.GetActorId()), zap.String("actual", req.GetActorId()))
		return &palPb.AddEmploymentDetailsResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}

	// step 2: fetch the workflow id from the loan request id passed.
	celestialRes, err := s.celestialClient.GetWorkflowStatus(ctx, &celestial.GetWorkflowStatusRequest{
		Identifier: &celestial.GetWorkflowStatusRequest_ClientRequestId{
			ClientRequestId: &celestial.ClientReqId{
				Id:     loanRequest.GetOrchId(),
				Client: workflow.Client_PRE_APPROVED_LOAN,
			},
		},
		Ownership: helper.GetPalOwnership(loanRequest.GetVendor()),
	})
	if te := epifigrpc.RPCError(celestialRes, err); te != nil {
		logger.Error(ctx, "failed to fetch workflow ids from the loan request id", zap.Error(te))
		return &palPb.AddEmploymentDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// Get Loecs and update the desired loan amount
	updateFieldValueLoecMap := make(map[palEnumsPb.FieldId]interface{})
	updateFieldValueLoecMap[palEnumsPb.FieldId_DESIRED_LOAN_AMOUNT] = req.GetDesiredLoanAmount()
	err = s.updateLoecsDataRequirementFields(ctx, req.GetActorId(), updateFieldValueLoecMap)
	if err != nil {
		logger.Error(ctx, "failed to update loecs with desired loan amount", zap.Error(err))
		return &palPb.AddEmploymentDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// step 3: signal the workflow and send the employment details entered by the user in signal payload
	// workflow will take necessary action and return the next screen to the client.
	_, err = s.communicateWithWorkflowInSync(ctx, &palWorkflowPb.SyncProxyRequest{
		TargetWorkflowId: celestialRes.GetWorkflowRequest().GetId(),
		Data: &palWorkflowPb.CommonData{
			Data: &palWorkflowPb.CommonData_EmploymentDetails{
				EmploymentDetails: &palWorkflowPb.EmploymentData{
					OccupationType:   req.GetOccupationType(),
					OrganizationName: req.GetOrganizationName(),
					MonthlyIncome:    req.GetMonthlyIncome(),
					WorkEmail:        req.GetWorkEmail(),
					Address:          req.GetOfficeAddress(),
				},
			},
		},
	})
	// in case sync proxy workflow returns an error, we will send internal status from here and error screen will be shown to user.
	if err != nil {
		logger.Error(ctx, "error communicating with workflow", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palPb.AddEmploymentDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &palPb.AddEmploymentDetailsResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// TODO(mohitswain): move to somewhere else
func getESignStepDataFromLSE(documentType palPb.LoanDocType, lse *palPb.LoanStepExecution) (signUrl string, aws_path string, expiry_time *timestampPb.Timestamp) {
	switch documentType {
	case palPb.LoanDocType_LOAN_DOC_TYPE_KFS:
		return lse.GetDetails().GetESignStepData().GetKfsDocument().GetSignUrl(),
			lse.GetDetails().GetESignStepData().GetKfsDocument().GetAwsDestinationPath(),
			lse.GetDetails().GetESignStepData().GetKfsDocument().GetExpiryAt()
	case palPb.LoanDocType_LOAN_DOC_TYPE_LOAN_AGREEMENT:
		return lse.GetDetails().GetESignStepData().GetLoanAgreementDocument().GetSignUrl(),
			lse.GetDetails().GetESignStepData().GetLoanAgreementDocument().GetAwsDestinationPath(),
			lse.GetDetails().GetESignStepData().GetLoanAgreementDocument().GetExpiryAt()
	default:
		return lse.GetDetails().GetESignStepData().GetSignUrl(),
			lse.GetDetails().GetESignStepData().GetAwsDestinationPath(),
			lse.GetDetails().GetESignStepData().GetExpiryAt()
	}
}

func setEsignStepDataInLSE(documentType palPb.LoanDocType, lse *palPb.LoanStepExecution, signUrl string, expiryTime *timestampPb.Timestamp) *palPb.LoanStepExecution {
	switch documentType {
	case palPb.LoanDocType_LOAN_DOC_TYPE_KFS:
		lse.GetDetails().GetESignStepData().GetKfsDocument().SignUrl = signUrl
		lse.GetDetails().GetESignStepData().GetKfsDocument().ExpiryAt = expiryTime
	case palPb.LoanDocType_LOAN_DOC_TYPE_LOAN_AGREEMENT:
		lse.GetDetails().GetESignStepData().GetLoanAgreementDocument().SignUrl = signUrl
		lse.GetDetails().GetESignStepData().GetLoanAgreementDocument().ExpiryAt = expiryTime
	default:
		lse.GetDetails().GetESignStepData().SignUrl = signUrl
		lse.GetDetails().GetESignStepData().ExpiryAt = expiryTime
	}
	return lse
}

func (s *Service) GenerateEsignAgreement(ctx context.Context, req *palPb.GenerateEsignAgreementRequest) (*palPb.GenerateEsignAgreementResponse, error) {
	var lse *palPb.LoanStepExecution
	var err error

	lse, err = s.getCurrentKfsLseForEsigning(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to fetch lse for e-agreement stage", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palPb.GenerateEsignAgreementResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch lse for e-agreement stage"),
		}, nil
	}

	signedUrl, awsPath, expiryTime := getESignStepDataFromLSE(req.GetLoanDocType(), lse)
	// Aws path in LSE details was added recently and is implemented only for some of the loan programs
	// To maintain backward compatibility, in cases where this is not populated, we should return the presigned url that is already stored in the DB
	if awsPath == "" {
		return &palPb.GenerateEsignAgreementResponse{
			Status:    rpc.StatusOk(),
			SignedUrl: signedUrl,
		}, nil
	}
	if expiryTime != nil &&
		datetime.IsBefore(timestampPb.Now(), expiryTime) {
		// in case previous url has not expired yet, return it instead of generating new one.
		logger.Info(ctx, "e-sign agreement url has not expired yet")
		return &palPb.GenerateEsignAgreementResponse{
			Status:    rpc.StatusOk(),
			SignedUrl: signedUrl,
		}, nil
	}
	preSignedUrl, err := s.s3Client.GetPreSignedUrl(ctx, awsPath, time.Duration(s.DynConf.EsignLinkExpirationInSecs())*time.Second)
	if err != nil {
		logger.Error(ctx, "failed to get pre signed url", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palPb.GenerateEsignAgreementResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to get preSignedUrl"),
		}, nil
	}
	expiryTime = timestampPb.New(time.Now().Add(time.Duration(s.DynConf.EsignLinkExpirationInSecs()) * time.Second))
	lse = setEsignStepDataInLSE(req.GetLoanDocType(), lse, preSignedUrl, expiryTime)
	if err := s.loanStepExecutionsDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
	}); err != nil {
		logger.Error(ctx, "failed to update lse", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palPb.GenerateEsignAgreementResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to update lse"),
		}, nil
	}

	res := &palPb.GenerateEsignAgreementResponse{
		Status:    rpc.StatusOk(),
		SignedUrl: preSignedUrl,
	}

	lr, lrErr := s.loanRequestsDao.GetById(ctx, lse.GetRefId())
	if lrErr != nil {
		logger.Error(ctx, "failed to fetch lr for e-agreement stage", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palPb.GenerateEsignAgreementResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to fetch lr for e-agreement stage"),
		}, nil
	}

	if lr.GetNextAction().GetScreen() != deeplinkPb.Screen_PRE_APPROVED_LOAN_E_SIGN_VIEW_DOCUMENT_SCREEN &&
		lr.GetNextAction().GetScreen() != deeplinkPb.Screen_PRE_APPROVED_LOAN_INITIATE_ESIGN_SCREEN {
		logger.WarnWithCtx(ctx, "next action screen is not e-sign agreement screen", zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return res, nil
	}

	// this is to check if the deeplink is parsed properly or not, if not, we retrun with a warn log from here
	if lr.GetNextAction().GetPreApprovedLoanInitiateEsignScreenOptions().GetContinue().GetDeeplink().GetPreApprovedLoanESignViewDocumentScreenOptions().GetDocumentUrl() == "" {
		logger.WarnWithCtx(ctx, "error in screen options parsing for e-sign", zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return res, nil
	}

	lr.GetNextAction().GetPreApprovedLoanInitiateEsignScreenOptions().GetContinue().GetDeeplink().GetPreApprovedLoanESignViewDocumentScreenOptions().DocumentUrl = res.GetSignedUrl()
	if err := s.loanRequestsDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
	}); err != nil {
		logger.Error(ctx, "failed to update lr next action", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palPb.GenerateEsignAgreementResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to update lr next action"),
		}, nil
	}

	return res, nil
}

func (s *Service) GetVkycDeeplink(ctx context.Context, req *palPb.GetVkycDeeplinkRequest) (*palPb.GetVkycDeeplinkResponse, error) {
	// Step 1: fetch Vkyc data provider
	vkycDataProvider, vkycDataProviderErr := s.loanDataProvider.GetVkycDataProvider(ctx, req.GetLoanHeader())
	if vkycDataProviderErr != nil {
		logger.Error(ctx, "error in getting vkyc loan data provider", zap.Error(vkycDataProviderErr), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palPb.GetVkycDeeplinkResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// Step 2: fetch Vkyc data from the provider
	vkycData, vkycDataErr := vkycDataProvider.GetVkycdata(ctx, &providers.GetVkycDataRequest{
		LoanRequestId: req.GetLoanRequestId(),
	})
	if vkycDataErr != nil {
		logger.Error(ctx, "error in getting vkyc loan data", zap.Error(vkycDataErr), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return &palPb.GetVkycDeeplinkResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// Step 3: Get deeplink provider
	deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetLoanHeader().GetVendor(), LoanProgram: req.GetLoanHeader().GetLoanProgram()})

	// Step 4: Fetch LSE step for VKYC
	lse, lseErr := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanRequestId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VKYC)
	if lseErr != nil {
		logger.Error(ctx, "error in getting lse", zap.Error(lseErr), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
		return nil, errors.Wrap(lseErr, "unable to get the lse")
	}

	switch {
	case vkycData.IsUserOutsideIndia:
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_OUT_OF_INDIA
		lseUpdateErr := s.loanStepExecutionsDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
		})
		if lseUpdateErr != nil {
			logger.Error(ctx, "error in updating lse", zap.Error(lseUpdateErr), zap.String(logger.ACTOR_ID_V2, lse.GetActorId()))
			return &palPb.GetVkycDeeplinkResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}

		outOfIndiaDeeplink, err := deeplinkProvider.GetVkycFailureScreen(
			vkycFailureHandlers.LocationNotInIndia,
			deeplinkProvider.GetLoanDashboardScreenDeepLink(
				deeplink.GetFeLoanHeaderByBeLoanHeader(&palPb.LoanHeader{
					LoanProgram: req.LoanHeader.GetLoanProgram(),
					Vendor:      req.LoanHeader.GetVendor(),
				})),
			"Back to dashboard",
		)
		if err != nil {
			logger.Error(ctx, "error in updating lse", zap.Error(err), zap.String(logger.ACTOR_ID_V2, lse.GetActorId()))
			return &palPb.GetVkycDeeplinkResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}

		return &palPb.GetVkycDeeplinkResponse{
			Status:   rpc.StatusOk(),
			Deeplink: outOfIndiaDeeplink,
		}, nil
	case vkycData.IsVkycOfUserAlreadyCompleted:
		lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		updateErr := s.loanStepExecutionsDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS})
		if updateErr != nil {
			logger.Error(ctx, "error in updating lse", zap.Error(updateErr), zap.String(logger.ACTOR_ID_V2, lse.GetActorId()))
			return &palPb.GetVkycDeeplinkResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
		return &palPb.GetVkycDeeplinkResponse{
			Status: rpc.StatusOk(),
		}, nil
	default:
		appVersionData, err := s.rpcHelper.FetchAppVersionInfo(ctx, lse.GetActorId())
		if err != nil {
			logger.Error(ctx, "error in getting the app version from user service", zap.Error(err), zap.String(logger.ACTOR_ID_V2, lse.GetActorId()))
			return &palPb.GetVkycDeeplinkResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}

		// getting the deeplink with the vkyc url and loader
		nextActionDeeplink, err := deeplinkProvider.GetVkycDeeplinkWithWebview(
			lse.GetRefId(),
			vkycData.VkycUrl,
			appVersionData.GetPlatform(),
		)
		if err != nil {
			logger.Error(ctx, "error while getting vkyc instruction deeplink with webview", zap.Error(err), zap.String(logger.ACTOR_ID_V2, lse.GetActorId()))
			return &palPb.GetVkycDeeplinkResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}

		return &palPb.GetVkycDeeplinkResponse{
			Status:   rpc.StatusOk(),
			Deeplink: nextActionDeeplink,
		}, nil
	}
}

// AddAddressDetailsSync adds the address details given by the user in sync fashion.
// send a signal to PL workflow, that will validate the data presence in db and will update the appropriate next action in the loan request.
func (s *Service) AddAddressDetailsSync(ctx context.Context, req *palPb.AddAddressDetailsRequest) (*palPb.AddAddressDetailsResponse, error) {
	var loanRequest *palPb.LoanRequest
	var err error
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	// step 1: fetch the loan request from the db
	loanRequest, err = s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "AddAddressDetailsSync: error while fetching loan request", zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()),
			zap.String(logger.VENDOR, req.GetLoanHeader().GetVendor().String()), zap.String(logger.LOAN_PROGRAM, req.GetLoanHeader().GetLoanProgram().String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &palPb.AddAddressDetailsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if !s.rpcHelper.IsPinCodeServiceable(loanRequest.GetVendor(), req.GetAddress().GetPostalCode(), loanRequest.GetLoanProgram()) {
		logger.Error(ctx, "postal code not serviceable", zap.String(logger.LOAN_PROGRAM, loanRequest.GetLoanProgram().String()))
		return &palPb.AddAddressDetailsResponse{
			Status: rpcPb.StatusOutOfRange(),
		}, nil
	}

	if loanRequest.GetActorId() != req.GetActorId() {
		logger.Error(ctx, "actor id mismatch", zap.String("expected", loanRequest.GetActorId()), zap.String("actual", req.GetActorId()))
		return &palPb.AddAddressDetailsResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}

	// step 2: fetch the workflow id from the loan request id passed.
	celestialRes, err := s.celestialClient.GetWorkflowStatus(ctx, &celestial.GetWorkflowStatusRequest{
		Identifier: &celestial.GetWorkflowStatusRequest_ClientRequestId{
			ClientRequestId: &celestial.ClientReqId{
				Id:     loanRequest.GetOrchId(),
				Client: workflow.Client_PRE_APPROVED_LOAN,
			},
		},
		Ownership: helper.GetPalOwnership(loanRequest.GetVendor()),
	})
	if te := epifigrpc.RPCError(celestialRes, err); te != nil {
		logger.Error(ctx, "failed to fetch workflow ids from the loan request id", zap.Error(te))
		return &palPb.AddAddressDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// step 3: signal the workflow and send the address details entered by the user in signal payload
	// workflow will take necessary action and return the next screen to the client.
	_, err = s.communicateWithWorkflowInSync(ctx, &palWorkflowPb.SyncProxyRequest{
		TargetWorkflowId: celestialRes.GetWorkflowRequest().GetId(),
		Data: &palWorkflowPb.CommonData{
			Data: &palWorkflowPb.CommonData_AddressData{
				AddressData: &palWorkflowPb.AddressData{
					Address:              req.GetAddress(),
					CurrentLocationToken: req.GetLocationToken(),
				},
			},
		},
	})
	// in case sync proxy workflow returns an error, we will send internal status from here and error screen will be shown to user.
	if err != nil {
		logger.Error(ctx, "error communicating with workflow", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palPb.AddAddressDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	return &palPb.AddAddressDetailsResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) updateMandateStatusFromCallbackPayload(ctx context.Context, payload *palPb.CallbackPayload_MandatePayload, lse *palPb.LoanStepExecution, platform commontypes.Platform) error {
	digioSdkGatewayEvents := make([]*palPb.DigioSdkGatewayEvent, 0)
	digioSdkFailureRespEvents := make([]*palPb.DigioSdkFailureResponseEvent, 0)
	digioSdkSuccessRespEvents := make([]*palPb.DigioSdkSuccessResponseEvent, 0)
	var id, eventName string

	// push current time along with event to know at what time particular event reached to server from client
	currTimeStamp := timestampPb.New(time.Now())
	eventType := payload.MandatePayload.GetMandateSdkEvent().GetEventType()
	rawPayload := payload.MandatePayload.GetMandateSdkEvent().GetEventPayload()
	switch eventType {
	case palPb.MandateSdkEvent_EVENT_TYPE_SUCCESS:
		var successRespEvent palPb.DigioSdkSuccessResponseEvent
		err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(rawPayload), &successRespEvent)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("failed to unmarshall event, payload: %s", rawPayload))
		}
		id = successRespEvent.GetId()
		// Android SDK sends mandate id in document id field
		if platform == commontypes.Platform_ANDROID {
			id = successRespEvent.GetDocumentId()
		}
		successRespEvent.EventReceivedAt = currTimeStamp
		digioSdkSuccessRespEvents = append(digioSdkSuccessRespEvents, &successRespEvent)

	case palPb.MandateSdkEvent_EVENT_TYPE_FAILURE:
		var failureRespEvent palPb.DigioSdkFailureResponseEvent
		err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(rawPayload), &failureRespEvent)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("failed to unmarshall event, payload: %s", rawPayload))
		}
		id = failureRespEvent.GetId()
		// Android SDK sends mandate id in document id field
		if platform == commontypes.Platform_ANDROID {
			id = failureRespEvent.GetDocumentId()
		}
		failureRespEvent.EventReceivedAt = currTimeStamp
		digioSdkFailureRespEvents = append(digioSdkFailureRespEvents, &failureRespEvent)

	case palPb.MandateSdkEvent_EVENT_TYPE_GATEWAY:
		var gatewayEvent palPb.DigioSdkGatewayEvent
		err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal([]byte(rawPayload), &gatewayEvent)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("failed to unmarshall event, payload: %s", rawPayload))
		}
		id = gatewayEvent.GetDocumentId()
		// store event name if it is a gateway event
		eventName = gatewayEvent.GetEvent()
		gatewayEvent.EventReceivedAt = currTimeStamp
		digioSdkGatewayEvents = append(digioSdkGatewayEvents, &gatewayEvent)

	default:
		// Do nothing
	}
	if id == "" {
		return errors.New("mandate id not found")
	}
	mandateRequest, err := s.mandateRequestsDao.GetByVendorMandateId(ctx, id)
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("failed to fetch mandate request for id:%s", id))
	}
	digioSdkGatewayEvents = append(digioSdkGatewayEvents, mandateRequest.GetDetails().GetClientEvent().GetSdkEvent().GetDigio().GetGatewayEvents()...)
	digioSdkFailureRespEvents = append(digioSdkFailureRespEvents, mandateRequest.GetDetails().GetClientEvent().GetSdkEvent().GetDigio().GetFailureResponseEvents()...)
	digioSdkSuccessRespEvents = append(digioSdkSuccessRespEvents, mandateRequest.GetDetails().GetClientEvent().GetSdkEvent().GetDigio().GetSuccessResponseEvents()...)
	clientEvent := &palPb.ClientEvent{
		Event: &palPb.ClientEvent_SdkEvent{
			SdkEvent: &palPb.SdkEvent{
				Event: &palPb.SdkEvent_Digio{
					Digio: &palPb.DigioSdkEvent{
						GatewayEvents:         digioSdkGatewayEvents,
						SuccessResponseEvents: digioSdkSuccessRespEvents,
						FailureResponseEvents: digioSdkFailureRespEvents,
					},
				},
			},
		},
	}
	if mandateRequest.GetDetails() != nil {
		mandateRequest.GetDetails().ClientEvent = clientEvent
	} else {
		mandateRequest.Details = &palPb.MandateRequestDetails{
			ClientEvent: clientEvent,
		}
	}

	fieldMask := []palPb.MandateRequestFieldMask{
		palPb.MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_DETAILS,
	}

	// append events in db for all kinds of events [GATEWAY, SUCCESS, FAILURE]
	// update mandate request status and lse sub status only if event type is gateway event
	if eventType == palPb.MandateSdkEvent_EVENT_TYPE_GATEWAY {
		mandateReqStatus := loanUtils.GatewayEventToMandateRequestStatus[strings.ToLower(eventName)]
		mandateRequest.Status = mandateReqStatus
		fieldMask = append(fieldMask, palPb.MandateRequestFieldMask_MANDATE_REQUEST_FIELD_MASK_STATUS)
	}
	if err := s.mandateRequestsDao.UpdateById(ctx, mandateRequest, fieldMask); err != nil {
		return errors.Wrap(err, fmt.Sprintf("failed to update mandate request for Id:%s", id))
	}

	if eventType == palPb.MandateSdkEvent_EVENT_TYPE_GATEWAY {
		subStatus := loanUtils.GatewayEventToLseSubStatus[strings.ToLower(eventName)]
		lse.SubStatus = subStatus
		err := s.loanStepExecutionsDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
		})
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("error while updating lse sub status from gateway event with payload :%s", rawPayload))
		}
	}
	return nil
}

func (s *Service) sendSignalForAlternateContactAddition(ctx context.Context, lse *palPb.LoanStepExecution, lrOrchId string) error {
	marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&palWorkflowPb.AlternatePhoneNumberStatusSignalPayload{
		LseStatus: lse.GetStatus(),
	})
	if err != nil {
		logger.Error(ctx, "failed to marshal signal with lse", zap.Error(err))
		return errors.New("failed to marshal signal with lse")
	}

	if signalErr := s.rpcHelper.SendSignalSync(ctx, lrOrchId, string(palNs.LoanApplicationAlternatePhoneAddedSignal), marshalledPayload); signalErr != nil {
		logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
		// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
	}

	return nil
}

func (s *Service) GetApplicantByVendorApplicantId(ctx context.Context, req *palPb.GetApplicantByVendorApplicantIdRequest) (*palPb.GetApplicantByVendorApplicantIdResponse, error) {
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP)
	loanApplicants, err := s.loanApplicantDao.GetBatchByVendorApplicantIds(ctx, []string{req.GetVendorApplicantId()})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in fetching loan applicant by vendor applicant id from db", zap.Error(err))
		return &palPb.GetApplicantByVendorApplicantIdResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) || len(loanApplicants) == 0 {
		return &palPb.GetApplicantByVendorApplicantIdResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	// this RPC returns only 1 loan request since the current constraints allow only 1 active loan request
	return &palPb.GetApplicantByVendorApplicantIdResponse{
		Status:        rpcPb.StatusOk(),
		LoanApplicant: loanApplicants[0],
	}, nil
}

func (s *Service) getLoanStepForAddDetails(ctx context.Context, loanRequestId string, lh *palPb.LoanHeader, lrType palPb.LoanRequestType, setAddressStep bool) (*palPb.LoanStepExecution, error) {
	// for few vendors we are using two different lse for add details step, this will return appropriate
	// step name according to vendor and loan program, setAddressStep flag wil be used to decide b/w address and employment step
	stepName, err := helper.GetAppropriateAddDetailsStepName(lh, setAddressStep)
	if err != nil {
		logger.Error(ctx, "error in fetching loan step name", zap.Any(logger.LOAN_HEADER, lh), zap.Error(err))
		return nil, err
	}

	loanStep, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, loanRequestId, helper.GetLseFlowByLrType(lrType), stepName)
	if err != nil {
		logger.Error(ctx, "error in fetching loan step from loanReqId", zap.Error(err))
		return nil, err
	}
	return loanStep, nil
}

func (s *Service) getUserImageFromLivenessData(ctx context.Context, accountId string) (string, error) {
	lrs, lrErr := s.loanRequestsDao.GetByLoanAccountIdAndType(ctx, accountId, palPb.LoanRequestType_LOAN_REQUEST_TYPE_CREATION)
	if lrErr != nil || len(lrs) == 0 {
		return "", errors.Wrap(lrErr, "error in fetching loan request")
	}
	lr := lrs[0]
	livenessLse, lseErr := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lr.GetId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK)
	if lseErr != nil {
		return "", errors.Wrap(lseErr, "error in fetching liveness loan step")
	}

	authStageResp, authErr := s.authOrchestratorClient.GetAuthStageRefId(ctx, &authOrchestratorPb.GetAuthStageRefIdRequest{
		ActorId:         livenessLse.GetActorId(),
		ClientRequestId: livenessLse.GetOrchId(),
		Stage:           authOrchestratorPb.AuthStage_AUTH_STAGE_LIVENESS_SUMMARY,
	})
	if te := epifigrpc.RPCError(authStageResp, authErr); te != nil {
		return "", errors.Wrap(te, "error in GetAuthStageRefId from auth")
	}

	imageLink, err := s.rpcHelper.GetLivenessImageFromLivenessSummary(ctx, livenessLse.GetActorId(), authStageResp.GetAuthStageRefId())
	if err != nil {
		return "", errors.Wrap(err, "error in getting liveness image")
	}

	return imageLink, nil
}

// fetching re-kfs loan step, and if its not present, fetching kfs loan step,
// so that the esign after the roi modification can be e-signed again.
// This is a usecase for the vendors having NBFC p2p
func (s *Service) getCurrentKfsLseForEsigning(ctx context.Context, lrId string) (*palPb.LoanStepExecution, error) {
	lse, err := s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lrId,
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RE_KFS)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lse, err = s.loanStepExecutionsDao.GetByRefIdAndFlowAndName(ctx, lrId,
				palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
				palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS)
			if err != nil {
				return nil, errors.Wrapf(err, "error in getting kfs lse for loan request id: %s", lrId)
			}
		} else {
			return nil, errors.Wrapf(err, "error in getting re-kfs lse for loan request id: %s", lrId)
		}
	}
	return lse, nil
}

func (s *Service) CollectFormData(ctx context.Context, req *palPb.CollectFormDataRequest) (*palPb.CollectFormDataResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))

	loanRequest, err := s.loanRequestsDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		logger.Error(ctx, "failed to get loan request by id", zap.Error(err))
		return &palPb.CollectFormDataResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	validateReq := &helper.ValidateCollectFormDataRequest{}

	formData := req.GetFormDataList()
	for _, fd := range formData {
		switch fd.GetFieldId() {
		case palEnumsPb.FieldId_PINCODE:
			validateReq.PinCode = fd.GetIntValue()
		case palEnumsPb.FieldId_NAME:
			validateReq.Name = fd.GetStringValue()
		case palEnumsPb.FieldId_DOB:
			validateReq.Dob = fd.GetDateValue()
		case palEnumsPb.FieldId_PAN:
			validateReq.Pan = strings.ToUpper(fd.GetStringValue())
		case palEnumsPb.FieldId_GENDER:
			validateReq.Gender = fd.GetSelectedOptionId()
		case palEnumsPb.FieldId_MARITAL_STATUS:
			validateReq.MaritalStatus = fd.GetSelectedOptionId()
		case palEnumsPb.FieldId_ADDRESS_TYPE:
			validateReq.AddressType = fd.GetSelectedOptionId()
		case palEnumsPb.FieldId_RENT:
			validateReq.Rent = fd.GetMoneyValue()
		case palEnumsPb.FieldId_LOAN_PURPOSE:
			validateReq.LoanPurpose = fd.GetSelectedOptionId()
		case palEnumsPb.FieldId_DESIRED_LOAN_AMOUNT:
			validateReq.DesiredLoanAmount = fd.GetMoneyValue()
		case palEnumsPb.FieldId_EMPLOYMENT_TYPE:
			validateReq.EmploymentType = fd.GetSelectedOptionId()
		case palEnumsPb.FieldId_EMPLOYER_NAME:
			validateReq.EmployerName = fd.GetSelectedOptionId()
		case palEnumsPb.FieldId_WORK_EMAIL:
			validateReq.WorkEmail = fd.GetStringValue()
		case palEnumsPb.FieldId_MONTHLY_INCOME:
			validateReq.MonthlyIncome = fd.GetMoneyValue()
		case palEnumsPb.FieldId_GSTIN:
			validateReq.Gstin = fd.GetStringValue()
		case palEnumsPb.FieldId_ANNUAL_REVENUE:
			validateReq.AnnualRevenue = fd.GetMoneyValue()
		default:
			logger.Error(ctx, "invalid field id ", zap.Any("field_id", fd.GetFieldId()))
			return &palPb.CollectFormDataResponse{
				Status:     rpcPb.StatusInternal(),
				NextAction: nil,
			}, nil
		}
	}

	err = helper.ValidateCollectFormData(validateReq)
	if err != nil {
		logger.Error(ctx, "validation failed in collect form data request", zap.Error(err))
		return &palPb.CollectFormDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("%v", err)),
		}, nil
	}

	lse, err := s.loanStepExecutionsDao.GetLatestByRefIdAndFlow(ctx, req.GetLoanRequestId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY)
	if err != nil {
		logger.Error(ctx, "failed to get lse by ref id and flow name", zap.Error(err))
		return &palPb.CollectFormDataResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if lse.GetStepName() != palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_DETAILS_CAPTURE &&
		lse.GetStepName() != palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_OTHER_DETAILS_CAPTURE &&
		lse.GetStepName() != palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BASIC_ADDRESS_DETAILS_CAPTURE &&
		lse.GetStepName() != palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_REQUIREMENT_CAPTURE &&
		lse.GetStepName() != palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT &&
		lse.GetStepName() != palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REVIEW_DETAILS {

		logger.Error(ctx, "invalid step name for collect form data",
			zap.String("step_name", lse.GetStepName().String()),
			zap.String("loan_request_id", req.GetLoanRequestId()))
		return &palPb.CollectFormDataResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("invalid step name for collect form data"),
		}, nil

	}
	user, err := s.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to fetch user by actor ID", zap.Error(err))
		return &palPb.CollectFormDataResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	userFieldMasks := make(map[userpb.UserFieldMask]bool)
	verificationDetails := make([]*userpb.DataVerificationDetail, 0)
	lseFieldMasks := make(map[palPb.LoanStepExecutionFieldMask]bool)
	if user.GetDataVerificationDetails() == nil {
		user.DataVerificationDetails = &userpb.DataVerificationDetails{}
	}
	if validateReq.Pan != "" || validateReq.Dob != nil || validateReq.MaritalStatus != "" {
		if lse.GetDetails().GetApplicantData() == nil {
			lse.Details = &palPb.LoanStepExecutionDetails{
				Details: &palPb.LoanStepExecutionDetails_ApplicantData{
					ApplicantData: &palPb.ApplicantData{},
				},
			}
		}

		if validateReq.Pan != "" {
			lse.GetDetails().GetApplicantData().Pan = validateReq.Pan
			verificationDetails = append(verificationDetails, &userpb.DataVerificationDetail{
				DataType: userpb.DataType_DATA_TYPE_PAN,
				DataValue: &userpb.DataVerificationDetail_PanNumber{
					PanNumber: validateReq.Pan,
				},
				VerificationMethod: userpb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			})
		}
		if validateReq.Dob != nil {
			lse.GetDetails().GetApplicantData().Dob = validateReq.Dob
			verificationDetails = append(verificationDetails, &userpb.DataVerificationDetail{
				DataType: userpb.DataType_DATA_TYPE_DOB,
				DataValue: &userpb.DataVerificationDetail_DOB{
					DOB: validateReq.Dob,
				},
				VerificationMethod: userpb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			})
		}
		if validateReq.MaritalStatus != "" {
			if _, ok := typesPb.MaritalStatus_value[validateReq.MaritalStatus]; !ok {
				logger.Error(ctx, "invalid marital status", zap.String("maritalStatus", validateReq.MaritalStatus))
				return &palPb.CollectFormDataResponse{
					Status: rpcPb.StatusInvalidArgument(),
				}, nil
			}
			lse.GetDetails().GetApplicantData().MaritalStatus = validateReq.MaritalStatus
			verificationDetails = append(verificationDetails, &userpb.DataVerificationDetail{
				DataType: userpb.DataType_DATA_TYPE_MARITAL_STATUS,
				DataValue: &userpb.DataVerificationDetail_MaritalStatus{
					MaritalStatus: typesPb.MaritalStatus(typesPb.MaritalStatus_value[validateReq.MaritalStatus]),
				},
				VerificationMethod: userpb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			})
		}

		userFieldMasks[userpb.UserFieldMask_DATA_VERIFICATION_DETAILS] = true
		lseFieldMasks[palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS] = true
	}

	if validateReq.Name != "" || validateReq.Gender != "" {
		if validateReq.Name != "" {
			user.GetProfile().GivenName = names.ParseStringV2(validateReq.Name)
			verificationDetails = append(verificationDetails, &userpb.DataVerificationDetail{
				DataType: userpb.DataType_DATA_TYPE_PAN_NAME,
				DataValue: &userpb.DataVerificationDetail_PanName{
					PanName: names.ParseStringV2(validateReq.Name),
				},
				VerificationMethod: userpb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			})
			userFieldMasks[userpb.UserFieldMask_DATA_VERIFICATION_DETAILS] = true
			userFieldMasks[userpb.UserFieldMask_GIVEN_NAME] = true
		}
		if validateReq.Gender != "" {
			if _, ok := typesPb.Gender_value[validateReq.Gender]; !ok {
				logger.Error(ctx, "invalid gender enum", zap.String("gender", validateReq.Gender))
				return &palPb.CollectFormDataResponse{
					Status: rpcPb.StatusInvalidArgument(),
				}, nil
			}
			user.GetProfile().GivenGender = typesPb.Gender(typesPb.Gender_value[validateReq.Gender])
			userFieldMasks[userpb.UserFieldMask_GIVEN_GENDER] = true
		}
	}

	if validateReq.LoanPurpose != "" || validateReq.DesiredLoanAmount != nil {
		updateFieldValueLoecMap := make(map[palEnumsPb.FieldId]interface{})
		if lse.GetDetails().GetApplicantData() == nil {
			lse.Details = &palPb.LoanStepExecutionDetails{
				Details: &palPb.LoanStepExecutionDetails_ApplicantData{
					ApplicantData: &palPb.ApplicantData{},
				},
			}
		}
		lseFieldMasks[palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS] = true
		if validateReq.LoanPurpose != "" {
			if _, ok := palEnumsPb.LoanPurpose_value[validateReq.LoanPurpose]; !ok {
				logger.Error(ctx, "invalid loanPurpose enum", zap.String("loanPurpose", validateReq.LoanPurpose))
				return &palPb.CollectFormDataResponse{
					Status: rpcPb.StatusInvalidArgument(),
				}, nil
			}
			updateFieldValueLoecMap[palEnumsPb.FieldId_LOAN_PURPOSE] = palEnumsPb.LoanPurpose(palEnumsPb.LoanPurpose_value[validateReq.LoanPurpose])
			lse.GetDetails().GetApplicantData().PurposeOfLoan = validateReq.LoanPurpose
		}

		if validateReq.DesiredLoanAmount != nil {
			lse.GetDetails().GetApplicantData().DesiredLoanAmount = validateReq.DesiredLoanAmount
			updateFieldValueLoecMap[palEnumsPb.FieldId_DESIRED_LOAN_AMOUNT] = validateReq.DesiredLoanAmount
		}

		err = s.updateLoecsDataRequirementFields(ctx, lse.GetActorId(), updateFieldValueLoecMap)
		if err != nil {
			logger.Error(ctx, "failed to update desired loan amount in loec",
				zap.Error(err),
				zap.String("loan_request_id", req.GetLoanRequestId()))
			return &palPb.CollectFormDataResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
	}

	if validateReq.WorkEmail != "" || validateReq.EmployerName != "" || validateReq.EmploymentType != "" || validateReq.MonthlyIncome != nil ||
		validateReq.AnnualRevenue != nil || validateReq.Gstin != "" {
		if lse.GetDetails().GetOnboardingData() == nil {
			if lse.GetDetails() == nil {
				lse.Details = &palPb.LoanStepExecutionDetails{}
			}
			lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_OnboardingData{
				OnboardingData: &palPb.OnboardingData{},
			}
		}
		if _, ok := typesPb.EmploymentType_value[validateReq.EmploymentType]; !ok {
			logger.Error(ctx, "invalid employment type enum", zap.String("employmentType", validateReq.EmploymentType))
			return &palPb.CollectFormDataResponse{
				Status: rpcPb.StatusInvalidArgument(),
			}, nil
		}

		employerNameSanitized := stringPkg.SanitizeFreeText(stringPkg.AlphaNumericWithSpecialChar, validateReq.EmployerName)
		employmentTypeEnum := typesPb.EmploymentType(typesPb.EmploymentType_value[validateReq.EmploymentType])
		lse.GetDetails().GetOnboardingData().EmploymentDetails = &palPb.OnboardingData_EmploymentDetails{
			Occupation:       employmentTypeEnum,
			OrganizationName: employerNameSanitized,
			MonthlyIncome:    validateReq.MonthlyIncome,
			WorkEmail:        validateReq.WorkEmail,
			AnnualRevenue:    validateReq.AnnualRevenue,
			GSTIN:            validateReq.Gstin,
		}
		verificationDetails = append(verificationDetails, &userpb.DataVerificationDetail{
			DataType: userpb.DataType_DATA_TYPE_EMPLOYMENT_DETAIL,
			DataValue: &userpb.DataVerificationDetail_EmploymentDetail_{
				EmploymentDetail: &userpb.DataVerificationDetail_EmploymentDetail{
					WorkEmail:        validateReq.WorkEmail,
					OrganizationName: employerNameSanitized,
					MonthlyIncome:    validateReq.MonthlyIncome,
					GSTIN:            validateReq.Gstin,
					AnnualRevenue:    validateReq.AnnualRevenue,
					EmploymentType:   employmentTypeEnum,
				},
			},
			VerificationMethod: userpb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
		})

		userFieldMasks[userpb.UserFieldMask_DATA_VERIFICATION_DETAILS] = true
		lseFieldMasks[palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS] = true
	}

	if validateReq.AddressType != "" || validateReq.PinCode != 0 || validateReq.Rent != nil {
		var residenceTypeEnum typesPb.ResidenceType
		if validateReq.AddressType != "" {
			residenceTypeEnumValue, isValid := typesPb.ResidenceType_value[validateReq.AddressType]
			if !isValid {
				logger.Error(ctx, "invalid residence type", zap.String("residenceType", validateReq.AddressType))
				return &palPb.CollectFormDataResponse{
					Status: rpcPb.StatusInvalidArgument(),
				}, nil
			}
			residenceTypeEnum = typesPb.ResidenceType(residenceTypeEnumValue)
		}
		if lse.GetDetails().GetOnboardingData() == nil {
			if lse.GetDetails() == nil {
				lse.Details = &palPb.LoanStepExecutionDetails{}
			}
			lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_OnboardingData{
				OnboardingData: &palPb.OnboardingData{},
			}
		}

		lse.GetDetails().GetOnboardingData().AddressDetails = &palPb.OnboardingData_AddressDetails{
			AddressType:    typesPb.AddressType_LOAN_COMMUNICATION,
			AddressDetails: &typesPb.PostalAddress{PostalCode: strconv.Itoa(int(validateReq.PinCode))},
			MonthlyRent:    validateReq.Rent,
			ResidenceType:  residenceTypeEnum,
		}
		verificationDetails = append(verificationDetails, &userpb.DataVerificationDetail{
			DataType: userpb.DataType_DATA_TYPE_BASIC_ADDRESS_DETAILS,
			DataValue: &userpb.DataVerificationDetail_AddressDetails{
				AddressDetails: &userpb.DataVerificationDetail_ResidenceDetails{
					ResidentialAddress: &typesPb.ResidentialAddress{
						Address:       &postaladdress.PostalAddress{PostalCode: strconv.Itoa(int(validateReq.PinCode))},
						ResidenceType: residenceTypeEnum,
					},
					MonthlyRent: validateReq.Rent,
				},
			},
			VerificationMethod: userpb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
		})
		userFieldMasks[userpb.UserFieldMask_DATA_VERIFICATION_DETAILS] = true
		lseFieldMasks[palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS] = true
	}

	if len(userFieldMasks) > 0 {
		user.GetDataVerificationDetails().DataVerificationDetails = verificationDetails
		var keys []userpb.UserFieldMask
		// Traverse the map and collect the keys
		for key := range userFieldMasks {
			keys = append(keys, key)
		}
		err = s.updateUserDetails(ctx, user, keys)
		if err != nil {
			logger.Error(ctx, "failed to update the user details", zap.Error(err))
			return &palPb.CollectFormDataResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
	}
	if len(lseFieldMasks) > 0 {
		var keys []palPb.LoanStepExecutionFieldMask
		// Traverse the map and collect the keys
		for key := range lseFieldMasks {
			keys = append(keys, key)
		}
		err = s.loanStepExecutionsDao.Update(ctx, lse, keys)
		if err != nil {
			logger.Error(ctx, "failed to update the lse", zap.Error(err))
			return &palPb.CollectFormDataResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
	}

	var nextAction *deeplinkPb.Deeplink
	if lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_DETAILS_CAPTURE ||
		lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_OTHER_DETAILS_CAPTURE ||
		lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BASIC_ADDRESS_DETAILS_CAPTURE ||
		lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_REQUIREMENT_CAPTURE ||
		lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT {

		celestialRes, err := s.celestialClient.GetWorkflowStatus(ctx, &celestial.GetWorkflowStatusRequest{
			Identifier: &celestial.GetWorkflowStatusRequest_ClientRequestId{
				ClientRequestId: &celestial.ClientReqId{
					Id:     loanRequest.GetOrchId(),
					Client: workflow.Client_PRE_APPROVED_LOAN,
				},
			},
			Ownership: helper.GetPalOwnership(loanRequest.GetVendor()),
		})
		if te := epifigrpc.RPCError(celestialRes, err); te != nil {
			logger.Error(ctx, "failed to fetch workflow ids from the loan request id", zap.Error(err))
			return &palPb.CollectFormDataResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}

		// Communicate with workflow
		wfResp, err := s.communicateWithWorkflowInSync(ctx, &palWorkflowPb.SyncProxyRequest{
			TargetWorkflowId: celestialRes.GetWorkflowRequest().GetId(),
		})
		if err != nil {
			logger.Error(ctx, "error communicating with workflow", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanRequestId()))
			return &palPb.CollectFormDataResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		nextAction = wfResp.GetNextAction()
	}
	if nextAction == nil {
		deeplinkProvider := s.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: loanRequest.GetVendor(), LoanProgram: loanRequest.GetLoanProgram()})
		nextAction = deeplinkProvider.GetLoanApplicationStatusPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), loanRequest.GetId())
	}
	return &palPb.CollectFormDataResponse{
		Status:     rpcPb.StatusOk(),
		NextAction: nextAction,
	}, nil

}

func (s *Service) GetCompletedLoanRequests(ctx context.Context, req *palPb.GetCompletedLoanRequestsRequest) (*palPb.GetCompletedLoanRequestsResponse, error) {
	loanReqs, err := s.loanRequestsDao.GetTerminalByActorIdAndVendor(ctx, req.GetActorId(), palPb.Vendor_VENDOR_UNSPECIFIED, filters.WithLoanRequestType(req.GetLoanRequestType()))
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error in fetching loan requests from db", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &palPb.GetCompletedLoanRequestsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &palPb.GetCompletedLoanRequestsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}
	return &palPb.GetCompletedLoanRequestsResponse{
		Status:                rpcPb.StatusOk(),
		CompletedLoanRequests: loanReqs,
	}, nil
}
