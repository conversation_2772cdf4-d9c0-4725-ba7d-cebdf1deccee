package utils

import palPb "github.com/epifi/gamma/api/preapprovedloan"

var (
	LenderToCollectionsEligibleLoanProgrammes = map[palPb.Vendor][]palPb.LoanProgram{
		palPb.Vendor_STOCK_GUARDIAN_LSP: {palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION, palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2},
		palPb.Vendor_LIQUILOANS: {palPb.LoanProgram_LOAN_PROGRAM_FLDG, palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY, palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND,
			palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL, palPb.LoanProgram_LOAN_PROGRAM_STPL, palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION, palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL},
	}
)
