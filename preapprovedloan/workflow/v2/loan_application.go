// nolint:dupl,funlen
package v2

import (
	"fmt"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	"github.com/epifi/be-common/pkg/logger"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	abflPl "github.com/epifi/gamma/preapprovedloan/workflow/providers/abfl"
	abflPwa "github.com/epifi/gamma/preapprovedloan/workflow/providers/abfl/pwa_journey"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/federal"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/federal/realtime"
	fedRTNtb "github.com/epifi/gamma/preapprovedloan/workflow/providers/federal/realtimentb"
	ffLamf "github.com/epifi/gamma/preapprovedloan/workflow/providers/fiftyfin/lamf"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/idfc"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/lenden"
	llAcqToLend "github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/acqtolend"
	llEarlySalary "github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/earlysalary"
	llEarlySalaryV2 "github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/earlysalaryv2"
	llFiLite "github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/filite"
	llFldg "github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/fldg"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/nonficorestpl"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/nonficoresubvention"
	llPl "github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/preapprovedloan"
	llRealTimeDist "github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/realtimedist"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/realtimestpl"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/realtimesubvention"
	llStpl "github.com/epifi/gamma/preapprovedloan/workflow/providers/liquiloans/stpl"
	moneyviewPl "github.com/epifi/gamma/preapprovedloan/workflow/providers/moneyview/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers/stockguardian"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

// nolint:funlen
// LoanApplication - workflow to orchestrate a loan application. This workflow is vendor-agnostic and can be used to
// process a loan application for all vendors.
// The payload to be used in initialising this workflow has both the vendor and the loan program to initiate this loan
// application with.
// The workflow creates a vendor provider, fetches all group stages to be executed for this vendor provider, and starts
// executing each stage belonging to a group stage.
// Execution of each stage is done in three steps - Pre-processing, Perform, and Post-Processing. The Pre step involves
// initialising a workflow stage, creating a loan stage execution (loan step execution), and similar tasks. The Perform
// step executes the activities performing the main stage processing, and the post step updates the workflow stage status,
// publishes the packet if workflow stage status is terminal.
func LoanApplication(ctx workflow.Context, _ *workflowPb.Request) error {
	lg := workflow.GetLogger(ctx)
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID

	var payload palWorkflowPb.LoanApplicationPayload
	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, &payload)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return err
	}
	ctx = epificontext.WorkflowContextWithOwnership(ctx, helper.GetPalOwnership(payload.GetVendor()))
	// update loan request to initiated state
	updateErr := updateLoanRequest(ctx, wfProcessingParams.GetClientReqId().GetId(), palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING)
	if updateErr != nil {
		lg.Error("failed to update LR", zap.Error(updateErr))
		return updateErr
	}

	// Step1: Get vendor provider by the vendor and loan program for which this loan application is being executed.
	vendorProvider, err := getVendorProvider(payload.GetVendor(), payload.GetLoanProgram(), payload.GetLmsPartner())
	if err != nil {
		lg.Error("failed to get vendor provider by payload", zap.Error(err))
		return err
	}
	// Step2: Get group stages to be performed
	groupStagesRes, err := vendorProvider.GetGroupStages(ctx, nil)
	if err != nil {
		lg.Error("failed to get group stages from vendor provider", zap.Error(err))
		return err
	}

	if payload.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_LAMF {
		return newLoanApplicationStageProcessingLogic(ctx, vendorProvider, groupStagesRes, wfReqID, wfProcessingParams, &payload)
	}

	// Step3: For each group stage, execute individual stages
	for _, gStage := range groupStagesRes.GroupStages {
		stagesRes, err := vendorProvider.GetStages(ctx, &providers.GetStagesRequest{GroupStage: gStage, ClientRefId: wfProcessingParams.GetClientReqId().GetId()})
		if err != nil {
			lg.Error("failed to fetch stages for a group stage", zap.Error(err))
			return err
		}
		for _, stage := range stagesRes.Stages {
			// Note that stage Id in perform will be generated in PreProcess
			preProcessRes, preProcessErr := stage.PreProcess(ctx, &stages.PreProcessRequest{
				WfReqId:            wfReqID,
				WfProcessingParams: wfProcessingParams,
				StageName:          stage.GetName(),
				GroupStage:         gStage,
				CelestialStage:     stage.GetCelestialStage(),
				Vendor:             payload.GetVendor(),
				LoanProgram:        payload.GetLoanProgram(),
				FlowName:           palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
				LoanHeader: &palPb.LoanHeader{
					LoanProgram: payload.GetLoanProgram(),
					Vendor:      payload.GetVendor(),
				},
			})
			if preProcessErr != nil {
				lg.Error("failed to perform stage pre-processing", zap.String("stage", stage.GetName().String()), zap.Error(preProcessErr))
				return preProcessErr
			}
			performRes, performErr := stage.Perform(ctx, &stages.PerformRequest{
				WfReqId:            wfReqID,
				WfProcessingParams: wfProcessingParams,
				Request:            preProcessRes,
				Vendor:             payload.GetVendor(),
				LoanProgram:        payload.GetLoanProgram(),
			})
			if performErr != nil {
				lg.Error("failed to perform stage execution", zap.String("stage", stage.GetName().String()), zap.Error(performErr))
				// not returning from here to perform post process
			}
			_, postProcessErr := stage.PostProcess(ctx, &stages.PostProcessRequest{
				Request:            performRes,
				WfReqId:            wfReqID,
				WfProcessingParams: wfProcessingParams,
				CelestialStage:     stage.GetCelestialStage(),
				Vendor:             payload.GetVendor(),
				LoanProgram:        payload.GetLoanProgram(),
			})
			if postProcessErr != nil {
				// TODO(harish): see if we need to update next action here
				lrStatus := palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
				if epifitemporal.IsRetryableError(postProcessErr) {
					lrStatus = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION
				}
				updateErr = updateLoanRequest(ctx, wfProcessingParams.GetClientReqId().GetId(), lrStatus)
				if updateErr != nil {
					lg.Error("failed to update LR", zap.Error(updateErr))
					return updateErr
				}
				lg.Error("failed to perform post processing or stage failed", zap.String("stage", stage.GetName().String()), zap.Error(postProcessErr))
				return postProcessErr
			}
		}
	}

	updateErr = updateLoanRequest(ctx, wfProcessingParams.GetClientReqId().GetId(), palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS)
	if updateErr != nil {
		lg.Error("failed to update LR", zap.Error(updateErr))
		return updateErr
	}

	return nil
}

func newLoanApplicationStageProcessingLogic(ctx workflow.Context, vendorProvider providers.IVendorProvider, groupStagesRes *providers.GetGroupStagesResponse, wfReqID string,
	wfProcessingParams *workflowPb.ProcessingParams, payload *palWorkflowPb.LoanApplicationPayload) error {
	lg := workflow.GetLogger(ctx)
	nextGrpStageProvider, ok := vendorProvider.(providers.INextGroupStageProvider)
	if !ok {
		nextGrpStageProvider = providers.NewDefaultNextGroupStageProviderImpl(groupStagesRes.GroupStages)
	}
	var (
		currentGroupStage, nextGroupStage palPb.GroupStage
		err                               error
	)
	for {
		// if next group stage to be executed is not specified by the stages executed in the last group stage, then use
		// vendor provider to get the next stage.
		if nextGroupStage == palPb.GroupStage_GROUP_STAGE_UNSPECIFIED {
			nextGroupStageRes, nxtGrpErr := nextGrpStageProvider.GetNextGroupStage(ctx, &providers.GetNextGroupStageRequest{
				CurrentGroupStage: currentGroupStage,
			})
			if nxtGrpErr != nil {
				lg.Error("failed to get next group stage from nextGrpStageProvider", zap.Error(nxtGrpErr))
				return fmt.Errorf("error while fetching next group stage from vendor provider : %w", nxtGrpErr)
			}
			if nextGroupStageRes.NextStage == palPb.GroupStage_GROUP_STAGE_UNSPECIFIED {
				break
			}
			currentGroupStage = nextGroupStageRes.NextStage
		} else {
			currentGroupStage = nextGroupStage
		}
		nextGroupStage, err = executeGroupStage(ctx, vendorProvider, currentGroupStage, wfReqID, wfProcessingParams, payload)
		if err != nil {
			lg.Error(fmt.Sprintf("error while executing %s group stage", currentGroupStage.String()), zap.Error(err))
			return err
		}
	}
	updateErr := updateLoanRequest(ctx, wfProcessingParams.GetClientReqId().GetId(), palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS)
	if updateErr != nil {
		lg.Error("failed to update LR", zap.Error(updateErr))
		return updateErr
	}

	return nil
}
func executeGroupStage(ctx workflow.Context, vendorProvider providers.IVendorProvider, groupStage palPb.GroupStage, wfReqID string,
	wfProcessingParams *workflowPb.ProcessingParams, payload *palWorkflowPb.LoanApplicationPayload) (palPb.GroupStage, error) {
	lg := workflow.GetLogger(ctx)
	stagesRes, getStageErr := vendorProvider.GetStages(ctx, &providers.GetStagesRequest{GroupStage: groupStage, ClientRefId: wfProcessingParams.GetClientReqId().GetId()})
	if getStageErr != nil {
		lg.Error("failed to fetch stages for a group stage", zap.Error(getStageErr))
		return palPb.GroupStage_GROUP_STAGE_UNSPECIFIED, getStageErr
	}
	for _, stage := range stagesRes.Stages {
		// Note that stage Id in perform will be generated in PreProcess
		preProcessRes, preProcessErr := stage.PreProcess(ctx, &stages.PreProcessRequest{
			WfReqId:            wfReqID,
			WfProcessingParams: wfProcessingParams,
			StageName:          stage.GetName(),
			GroupStage:         groupStage,
			CelestialStage:     stage.GetCelestialStage(),
			Vendor:             payload.GetVendor(),
			LoanProgram:        payload.GetLoanProgram(),
			FlowName:           palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
		})
		if preProcessErr != nil {
			lg.Error("failed to perform stage pre-processing", zap.String("stage", stage.GetName().String()), zap.Error(preProcessErr))
			return palPb.GroupStage_GROUP_STAGE_UNSPECIFIED, preProcessErr
		}
		performRes, performErr := stage.Perform(ctx, &stages.PerformRequest{
			WfReqId:            wfReqID,
			WfProcessingParams: wfProcessingParams,
			Request:            preProcessRes,
			Vendor:             payload.GetVendor(),
			LoanProgram:        payload.GetLoanProgram(),
		})
		if performErr != nil {
			lg.Error("failed to perform stage execution", zap.String("stage", stage.GetName().String()), zap.Error(performErr))
			// not returning from here to perform post process
		}
		_, postProcessErr := stage.PostProcess(ctx, &stages.PostProcessRequest{
			Request:            performRes,
			WfReqId:            wfReqID,
			WfProcessingParams: wfProcessingParams,
			CelestialStage:     stage.GetCelestialStage(),
			Vendor:             payload.GetVendor(),
			LoanProgram:        payload.GetLoanProgram(),
		})
		if postProcessErr != nil {
			// TODO(harish): see if we need to update next action here
			lrStatus := palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
			if epifitemporal.IsRetryableError(postProcessErr) {
				lrStatus = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION
			}
			updateErr := updateLoanRequest(ctx, wfProcessingParams.GetClientReqId().GetId(), lrStatus)
			if updateErr != nil {
				lg.Error("failed to update LR", zap.Error(updateErr))
				return palPb.GroupStage_GROUP_STAGE_UNSPECIFIED, updateErr
			}
			lg.Error("failed to perform post processing or stage failed", zap.String("stage", stage.GetName().String()), zap.Error(postProcessErr))
			return palPb.GroupStage_GROUP_STAGE_UNSPECIFIED, postProcessErr
		}
		if performRes != nil && performRes.NextGroupStage != palPb.GroupStage_GROUP_STAGE_UNSPECIFIED {
			lg.Info("skipping pending stages as next group stage present in stage response",
				zap.String(logger.STAGE, stage.GetName().String()), zap.String("group_stage", groupStage.String()),
				zap.String("next_group_stage", performRes.NextGroupStage.String()))
			return performRes.NextGroupStage, nil
		}
	}
	return palPb.GroupStage_GROUP_STAGE_UNSPECIFIED, nil
}

// nolint:funlen
// TODO(harish): move get group stages to decision engine
func getVendorProvider(vendor palPb.Vendor, loanProgram palPb.LoanProgram, lmsPartner enums.LmsPartner) (providers.IVendorProvider, error) {
	switch vendor {
	case palPb.Vendor_FEDERAL:
		switch loanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			return federal.NewProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return realtime.NewLoanApplicationProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
			return fedRTNtb.NewLoanApplicationProvider(), nil
		default:
			return nil, fmt.Errorf("unsupported vendor and loan program provider")
		}
	case palPb.Vendor_LIQUILOANS:
		switch loanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			return llPl.NewProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY:
			if lmsPartner != enums.LmsPartner_LMS_PARTNER_UNSPECIFIED {
				return llEarlySalaryV2.NewProvider(), nil
			}
			return llEarlySalary.NewProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_FLDG:
			return llFldg.NewProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_STPL:
			return llStpl.NewProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
			return llFiLite.NewLoanApplicationProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND:
			return llAcqToLend.NewLoanApplicationProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return llRealTimeDist.NewLoanApplicationProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION:
			return realtimesubvention.NewProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL:
			return realtimestpl.NewProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION:
			return nonficoresubvention.NewProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
			return nonficorestpl.NewProvider(), nil
		default:
			return nil, fmt.Errorf("unsupported vendor and loan program provider")
		}
	case palPb.Vendor_IDFC:
		return idfc.NewProvider(), nil
	case palPb.Vendor_FIFTYFIN:
		switch loanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_LAMF:
			return ffLamf.NewProvider(), nil
		default:
			return nil, fmt.Errorf("unsupported vendor and loan program provider")
		}
	case palPb.Vendor_ABFL:
		switch loanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			return abflPl.NewProvider(), nil
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return abflPwa.NewProvider(), nil
		default:
			return nil, fmt.Errorf("unsupported vendor and loan program provider")
		}
	case palPb.Vendor_MONEYVIEW:
		switch loanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN, palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return moneyviewPl.NewProvider(), nil
		default:
			return nil, fmt.Errorf("unsupported vendor and loan program provider")
		}
	case palPb.Vendor_STOCK_GUARDIAN_LSP:
		switch loanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION, palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
			return stockguardian.NewLoanApplicationProvider(), nil
		default:
			return nil, fmt.Errorf("unsupported vendor and loan program provider, %v", loanProgram)
		}
	case palPb.Vendor_LENDEN:
		switch loanProgram {
		case palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return lenden.NewLoanApplicationProvider(), nil
		default:
			return nil, fmt.Errorf("unsupported vendor and loan program provider")
		}
	default:
		return nil, fmt.Errorf("unsupported vendor and loan program provider")
	}
}
