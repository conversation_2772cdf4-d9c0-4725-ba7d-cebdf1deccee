Application:
  Environment: "prod"
  Name: "recurringpayment"

Secrets:
  Ids:
    # todo: add these secrets to secret manager once confirmed with federal
    EnachSecrets: "prod/recurringpayment/enach/secrets"
    EnachFederalPublicKey: "prod/recurringpayment/enach/federal/public-key"

# Recurring Payment service is actually initialized on the port defined in order-<env>.yml
# These properties are kept here from forward compatibility POV when we may want Recurring Payment service to be running on a
# different port in the order server
Server:
  Port: 8091
  HealthCheckPort: 9999

# Recurring Payment service uses DB connection initialized by order-<env>.yml
# These properties are kept from forward compatibility POV when we may want to have DB separate connection
# for Recurring Payment service
EpifiDb:
  AppName: "recurringpayment"
  DbType: "CRDB"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

RecurringPaymentDb:
  Name: "recurring_payment"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 10
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "recurring_payment"
  SecretName: "prod/rds/prod-plutus/recurring_payment_dev_user"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Tracing:
  Enable: true

FeatureFlags:
  EnableRecurringPaymentCreationViaCelestial: true
  EnableRecurringPaymentExecutionWithoutAuthViaCelestial: true
  EnableRecurringPaymentModificationViaCelestial: true
  EnableRecurringPaymentRevokeViaCelestial: true
  EnableRecurringPaymentPauseUnpauseViaCelestial: true

SIExecutionParams:
  PaymentProtocol: 2

SignalWorkflowPublisher:
  QueueName: "prod-celestial-signal-workflow-queue"

UPIEnquiryPublisher:
  QueueName: "prod-payment-upi-enquiry-queue"

InPaymentOrderUpdatePublisher:
  QueueName: "prod-order-in-payment-order-update-queue"

FetchAndCreateOffAppRecurringPaymentPublisher:
  QueueName: "prod-fetch-recurringpayemnt-from-vendor-queue"

FeatureReleaseConfig:
  FeatureConstraints:
    # Feature rollout configuration for failed enach transactions
    FEATURE_FAILED_ENACH_TRANSACTIONS:
      AppVersionConstraintConfig:
        MinAndroidVersion: 456
        MinIOSVersion: 619
      StickyPercentageConstraintConfig:
        RolloutPercentage: 15
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL

FetchAndCreateFailedEnachTransactionPublisher:
  QueueName: "prod-recurringpayment-failed-enach-transaction-queue"

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

NonTpapPspHandles: ["fbl"]

RecurringPaymentCreationAuthVendorCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-recurringpayment-creation-auth-vendor-callback-queue"
  # Exponential backoff is followed for first ~30 seconds post that regular interval is followed for next 25 mins
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 1
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 25
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 5

FetchAndCreateOffAppRecurringPaymentSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-fetch-recurringpayemnt-from-vendor-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 20
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 35
        Period: 6s
    Namespace: "recpayment-offapp"

OffAppRecurringPaymentExecutionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-recurringpayemnt-off-app-execution-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 20
      TimeUnit: "Minute"

FetchAndCreateFailedEnachTransactionSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-recurringpayment-failed-enach-transaction-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 20
      TimeUnit: "Minute"



EnachServiceConfig:
  VendorToActivationCooldownMap:
    "FEDERAL_BANK": 72h # For federal the activation takes 2 days. Hence taking 3 (2+1 buffer) days cooldown.
  VendorConfig:
    FederalConfig:
      MandateCreationConfig:
        RedirectionUrl: "https://fedmandate.federalbank.co.in:4012/e-mandate/merchant/"
        ResponseUrl: "https://vnotificationgw.epifi.in/openbanking/recurringpayment/enach/registration/federal"
        ExitUrl: "https://vnotificationgw.epifi.in/openbanking/recurringpayment/enach/registration/federal"
        RedirectionExpiry: "10m"
        EnableRsaEncryption: true
      MandateExecutionConfig:
        # as per compliance a returned transaction can be represented for execution only after 3 days since last return
        MinWaitDurationForRepresentingReturnedTxn: "72h"
EnachFundTransferOrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-enach-fund-transfer-order-update-consumer-queue"
  # Exponential backoff is followed for first ~5 minutes post that regular interval is followed for next ~4 hours
  RetryStrategy:
    Hybrid:
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 5
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 10
          MaxAttempts: 25
          TimeUnit: "Minute"
      MaxAttempts: 30
      CutOff: 5

PaymentEnquiryParams:
  NotFoundMaxRetryDurationVendorMap:
    "FEDERAL_BANK":
      IntraBank: "24h"
      # We are currently enquiring at 90s and then 2h90s and then 4h90s
      # So we can mark Txn as Failed if 2nd enquiry gave U48 [a.k.a NotFound], hence we are keeping this less than 2h.
      UPI: "110m"
      NEFT: "24h"
      RTGS: "24h"
      IMPS: "24h"
  InProgressToSuccessMap: # time duration and error codes for in progress response status will be move to successser
    "FEDERAL_BANK":
      FiErrorCodes:
        - "FI304"
      PaymentProtocolToDurationMap:
        "NEFT": "120m"
        "RTGS": "120m"
      PaymentProtocolToDeemedEnquiryDurationMap:
        UPI:
          P2P: 105h
          P2M: 105h
        IMPS:
          P2P: 105h
          P2M: 105h

EnachCreationValidationConfig:
  MinRequiredDelayForEnachStartTimeSinceCreation: 24h
  MaxAllowedAmountInRs: 1000000 # 10 lac rupees

PgProgramToAuthSecretMap:
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "prod/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-stockguardian_MbZRPgHhafW":
    AuthParam: "prod/vendorgateway/razorpay-stock-guardian-loans-api-key"

EnableEntitySegregation: true

EnableEnachMandateEntitySegregation: true

PgParams:
  EnableOffAppMandateCreation: true
