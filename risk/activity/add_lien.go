package activity

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	epifiTemporal "github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"

	activityPb "github.com/epifi/gamma/api/risk/activity"
	riskLienPb "github.com/epifi/gamma/api/risk/lien"
)

// AddLien adds a lien on a savings account
func (p *Processor) AddLien(ctx context.Context, req *activityPb.AddLienRequest) (*activityPb.AddLienResponse, error) {

	startTime := timestamppb.New(time.Now().Add(5 * time.Minute))
	endTime := timestamppb.New(startTime.AsTime().Add(time.Hour * time.Duration(req.GetLienDurationInHours())))

	// Convert activity request to lien manager request
	lienReq := &riskLienPb.AddLienRequest{
		AccountNumber:    req.GetAccountNumber(),
		Amount:           float64(req.GetAmount()),
		CurrencyCode:     req.GetCurrencyCode(),
		ReasonCode:       req.GetReasonCode(),
		Remarks:          req.GetRemarks(),
		StartDate:        startTime,
		EndDate:          endTime,
		ChannelRequestId: req.GetChannelRequestId(),
		BankActionId:     req.GetBankActionId(),
	}

	// Call the lien manager
	_, err := p.lienManager.AddLien(ctx, lienReq)
	if err != nil {
		logger.Error(ctx, "Failed to add lien", zap.Error(err), zap.String("account_number", req.GetAccountNumber()))
		return nil, epifiTemporal.NewTransientError(errors.Wrap(err, "lien manager AddLien failed"))
	}

	// Convert lien manager response to activity response
	return &activityPb.AddLienResponse{}, nil
}
