package activity

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/risk"
	activityPb "github.com/epifi/gamma/api/risk/activity"
	riskEnums "github.com/epifi/gamma/api/risk/enums"
	savingsPb "github.com/epifi/gamma/api/savings"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

/*
AddToBankAction adds new entry to risk_bank_actions table
*/

func (p *Processor) AddToBankAction(ctx context.Context, req *activityPb.AddToBankActionRequest) (*activityPb.AddToBankActionResponse, error) {
	// getting account details for the given actor
	savingsAccount, err := p.getSavingsAccountFromActorId(ctx, req.GetActorId())
	if err != nil {
		return nil, err
	}
	lg := activity.GetLogger(ctx)
	// create a new entry in database
	// explicitly passing vendor as Federal and account type as savings here
	riskBankAction, err := p.riskBankActionDao.Create(ctx, &risk.RiskBankActions{
		ClientReqId:   req.GetWorkflowId(),
		ActorId:       req.GetActorId(),
		AccountId:     savingsAccount.GetId(),
		AccountType:   accountsPb.Type_SAVINGS,
		Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
		Action:        req.GetAction(),
		State:         riskEnums.State_STATE_INITIATED,
		RequestReason: req.GetRequestReason(),
		Provenance:    riskEnums.Provenance_PROVENANCE_FI,
		CommsTemplate: req.GetCommsTemplate(),
		IsRecon:       commontypes.BooleanEnum_FALSE,
	})
	if err != nil {
		lg.Error("error while creating entry in risk bank actions table", zap.String(logger.ACTION_TYPE, req.GetAction().String()), zap.Error(err))
		if storage.IsDuplicateRowError(err) {
			return nil, errors.Wrap(epifierrors.ErrPermanent, "duplicate entry while creating risk bank action in database")
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, "error while creating an entry for risk bank action in database")
	}

	return &activityPb.AddToBankActionResponse{
		BankActionId: riskBankAction.GetId(),
	}, nil
}

func (p *Processor) getSavingsAccountFromActorId(ctx context.Context, actorId string) (*savingsPb.Account, error) {
	getAcctResp, getAcctErr := p.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorId{
			ActorId: actorId,
		},
	})
	switch {
	case getAcctErr != nil && status.Code(getAcctErr) == codes.NotFound:
		return nil, errors.Wrap(epifierrors.ErrPermanent, errors.Wrap(getAcctErr, "record not found fetching savings account using actor id").Error())
	case getAcctErr != nil || getAcctResp.GetAccount() == nil:
		return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(getAcctErr, "error fetching savings account using actor id").Error())
	}
	return getAcctResp.GetAccount(), nil
}
