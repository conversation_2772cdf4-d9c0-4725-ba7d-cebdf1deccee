package activity_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"testing"

	"github.com/epifi/gamma/api/accounts"
	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/gamma/api/risk"
	riskPb "github.com/epifi/gamma/api/risk"
	activityPb "github.com/epifi/gamma/api/risk/activity"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/be-common/pkg/epifitemporal"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"
	"github.com/epifi/be-common/pkg/mock"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	userId1    = "user-id-1"
	savingsId1 = "savings-id-1"

	savingsFixture1 = &savingsPb.Account{
		Id:                   savingsId1,
		AccountNo:            "************",
		IfscCode:             "FDRL01234",
		PrimaryAccountHolder: userId1,
		Constraints:          nil,
		PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
	}
)

func TestProcessor_AddToBankAction(t *testing.T) {
	type args struct {
		req *activityPb.AddToBankActionRequest
	}
	type mockRBACreate struct {
		isEnable bool
		req      *risk.RiskBankActions
		res      *risk.RiskBankActions
		err      error
	}
	type mockGetAccount struct {
		isEnable bool
		req      *savingsPb.GetAccountRequest
		res      *savingsPb.GetAccountResponse
		err      error
	}
	tests := []struct {
		name           string
		args           args
		want           *activityPb.AddToBankActionResponse
		mockRBACreate  mockRBACreate
		mockGetAccount mockGetAccount
		wantErr        bool
		assertErr      func(err error) bool
	}{
		{
			name: "get account returns not found",
			args: args{
				req: &activityPb.AddToBankActionRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WorkflowId: "client-req-id-1",
					ActorId:    "actor-id",
					Action:     riskEnumsPb.Action_ACTION_FULL_FREEZE,
				},
			},
			mockGetAccount: mockGetAccount{
				isEnable: true,
				req: &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ActorId{
						ActorId: actorId1,
					},
				},
				res: &savingsPb.GetAccountResponse{},
				err: status.Error(codes.NotFound, ""),
			},
			mockRBACreate: mockRBACreate{
				isEnable: false,
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "duplicate risk bank entry: err",
			args: args{
				req: &activityPb.AddToBankActionRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WorkflowId: "client-req-id-1",
					ActorId:    "actor-id",
					Action:     riskEnumsPb.Action_ACTION_FULL_FREEZE,
					RequestReason: &riskPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
				},
			},
			mockGetAccount: mockGetAccount{
				isEnable: true,
				req: &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ActorId{
						ActorId: actorId1,
					},
				},
				res: &savingsPb.GetAccountResponse{Account: savingsFixture1},
			},
			mockRBACreate: mockRBACreate{
				isEnable: true,
				req: &risk.RiskBankActions{
					ActorId:     "actor-id",
					ClientReqId: "client-req-id-1",
					State:       riskEnumsPb.State_STATE_INITIATED,
					RequestReason: &riskPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Action:      riskEnumsPb.Action_ACTION_FULL_FREEZE,
					AccountId:   savingsId1,
					AccountType: accounts.Type_SAVINGS,
					Provenance:  riskEnumsPb.Provenance_PROVENANCE_FI,
					IsRecon:     commontypes.BooleanEnum_FALSE,
				},
				res: nil,
				err: errors.New("duplicate key value"),
			},
			wantErr: true,
			assertErr: func(err error) bool {
				return !epifitemporal.IsRetryableError(err)
			},
		},
		{
			name: "successfully added risk bank entry",
			args: args{
				req: &activityPb.AddToBankActionRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WorkflowId: "client-req-id-1",
					ActorId:    "actor-id",
					Action:     riskEnumsPb.Action_ACTION_FULL_FREEZE,
					RequestReason: &riskPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
				},
			},
			mockGetAccount: mockGetAccount{
				isEnable: true,
				req: &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ActorId{
						ActorId: actorId1,
					},
				},
				res: &savingsPb.GetAccountResponse{Account: savingsFixture1},
			},
			mockRBACreate: mockRBACreate{
				isEnable: true,
				req: &risk.RiskBankActions{
					ActorId:     "actor-id",
					ClientReqId: "client-req-id-1",
					State:       riskEnumsPb.State_STATE_INITIATED,
					RequestReason: &riskPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Action:      riskEnumsPb.Action_ACTION_FULL_FREEZE,
					AccountId:   savingsId1,
					AccountType: accounts.Type_SAVINGS,
					Provenance:  riskEnumsPb.Provenance_PROVENANCE_FI,
					IsRecon:     commontypes.BooleanEnum_FALSE,
				},
				res: &risk.RiskBankActions{
					Id:          "generated-id",
					ActorId:     "actor-id",
					ClientReqId: "client-req-id-1",
					State:       riskEnumsPb.State_STATE_INITIATED,
					RequestReason: &riskPb.RequestReason{
						Reason:  riskEnumsPb.RequestReason_REQUEST_REASON_OTHER,
						Remarks: "Manually resolved",
					},
					Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
					Action:      riskEnumsPb.Action_ACTION_FULL_FREEZE,
					AccountId:   savingsId1,
					AccountType: accounts.Type_SAVINGS,
					Provenance:  riskEnumsPb.Provenance_PROVENANCE_FI,
					IsRecon:     commontypes.BooleanEnum_FALSE,
				},
				err: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)

			if tt.mockGetAccount.isEnable {
				md.savingsClient.EXPECT().GetAccount(gomock.Any(), tt.mockGetAccount.req).Return(tt.mockGetAccount.res, tt.mockGetAccount.err)
			}

			if tt.mockRBACreate.isEnable {
				md.riskBankActionDao.EXPECT().Create(gomock.Any(), mock.NewProtoMatcher(tt.mockRBACreate.req)).Return(tt.mockRBACreate.res, tt.mockRBACreate.err)
			}
			_, err := env.ExecuteActivity(riskNs.AddToBankAction, tt.args.req)
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("AddToBankAction() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("AddToBankAction() error = %v assertion failed", err)
				return
			}
		})
	}
}
