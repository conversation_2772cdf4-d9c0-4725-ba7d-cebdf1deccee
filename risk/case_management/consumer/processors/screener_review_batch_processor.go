package processors

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	"github.com/epifi/gamma/risk/case_management/metrics"
	"github.com/epifi/gamma/risk/config/genconf"
)

type ScreenerReviewBatchProcessor struct {
	caseManagementClient caseManagementPb.CaseManagementClient
	cfg                  *genconf.Config
}

func NewScreenerReviewBatchProcessor(caseManagementClient caseManagementPb.CaseManagementClient, cfg *genconf.Config) *ScreenerReviewBatchProcessor {
	return &ScreenerReviewBatchProcessor{
		caseManagementClient: caseManagementClient,
		cfg:                  cfg,
	}
}

func (p *ScreenerReviewBatchProcessor) ProcessPayload(ctx context.Context, payloadType caseManagementPb.PayloadType, inputs []*Input) []*Failure {
	var failures []*Failure
	if payloadType != caseManagementPb.PayloadType_PAYLOAD_TYPE_SCREENER {
		for _, input := range inputs {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				Err:     fmt.Errorf("invalid payload type: %s: %w", payloadType, epifierrors.ErrInvalidArgument),
				BatchId: input.GetBatchId(),
			})
		}
		return failures
	}

	var currBatch []*Input
	for _, input := range inputs {
		screenerReviewPayload, ok := input.GetPayload().(*caseManagementPb.RiskCase_ScreenerReview)
		if !ok || screenerReviewPayload == nil || screenerReviewPayload.ScreenerReview == nil {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				Err:     fmt.Errorf("invalid payload passed for payload type: %s: %w", payloadType, epifierrors.ErrInvalidArgument),
				BatchId: input.GetBatchId(),
			})
			continue
		}

		// Validate required fields
		if err := validateScreenerReviewPayload(screenerReviewPayload.ScreenerReview); err != nil {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				Err:     fmt.Errorf("validation failed: %w", err),
				BatchId: input.GetBatchId(),
			})
			continue
		}

		currBatch = append(currBatch, input)

		// Keep adding to currBatch till processor capacity is not utilized
		if len(currBatch) < int(p.cfg.CMConsumerConfig().CreateAlertsBatchSize()) {
			continue
		}
		failures = append(failures, p.createAlerts(ctx, payloadType, currBatch)...)
		currBatch = []*Input{}
	}
	failures = append(failures, p.createAlerts(ctx, payloadType, currBatch)...)
	return failures
}

// createAlerts processes screener review payloads and creates alerts for manual review cases.
// This method is specifically designed for screener review workflows where:
// - EntityType is always ENTITY_TYPE_SCREENER (different from transaction processor)
// - EntityId contains the ScreenerAttemptId (screener-specific field)
// - Payloads come from CSV uploads containing ActorId and ScreenerAttemptId
// - Used for re-onboarding (REOOBE) and other screener criteria workflows
func (p *ScreenerReviewBatchProcessor) createAlerts(ctx context.Context, payloadType caseManagementPb.PayloadType, batch []*Input) []*Failure {
	var failures []*Failure
	if len(batch) == 0 {
		return failures
	}

	// Convert screener review payloads to raw alerts with screener-specific entity mapping
	// This function handles payload validation internally to avoid duplicate validation
	rawAlerts, createRawAlertsFailures := screenerReviewPayloadsToRawAlerts(batch)
	failures = append(failures, createRawAlertsFailures...)

	// Skip alert creation if no valid alerts to process
	if len(rawAlerts) == 0 {
		return failures
	}

	// Create alerts via RPC call for screener review cases
	resp, err := p.caseManagementClient.CreateAlerts(ctx,
		&caseManagementPb.CreateAlertsRequest{
			Alerts: rawAlerts,
		})

	if err = epifigrpc.RPCError(resp, err); err != nil {
		// Mark all screener review inputs in this batch as failed
		for _, input := range batch {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				Err:     fmt.Errorf("failed to create screener alert: %w", err),
				BatchId: input.GetBatchId(),
			})
		}
		return failures
	}

	// Record success metrics for screener review ingestion
	for _, input := range batch {
		metrics.RecordIngestionSuccess(input.GetBatchId(), payloadType)
	}
	return failures
}

func screenerReviewPayloadsToRawAlerts(inputs []*Input) ([]*caseManagementPb.RawAlert, []*Failure) {
	var rawAlerts []*caseManagementPb.RawAlert
	var failures []*Failure
	for _, input := range inputs {
		payload, ok := input.GetPayload().(*caseManagementPb.RiskCase_ScreenerReview)
		if !ok {
			failures = append(failures, &Failure{
				Payload: input.GetPayload(),
				BatchId: input.GetBatchId(),
				Err:     fmt.Errorf("invalid payload passed: %w", epifierrors.ErrInvalidArgument),
			})
			continue
		}

		rawAlert := &caseManagementPb.RawAlert{
			Identifier:  input.GetRuleIdentifier(),
			ActorId:     payload.ScreenerReview.GetActorId(),
			EntityType:  cmEnumsPb.EntityType_ENTITY_TYPE_SCREENER,
			EntityId:    payload.ScreenerReview.GetScreenerAttemptId(),
			BatchName:   input.GetBatchId(),
			InitiatedAt: input.GetInitiatedAt(),
		}
		rawAlerts = append(rawAlerts, rawAlert)
	}
	return rawAlerts, failures
}

func validateScreenerReviewPayload(screenerReview *caseManagementPb.ScreenerReview) error {
	if screenerReview.GetActorId() == "" {
		return fmt.Errorf("actor_id cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}
	if screenerReview.GetScreenerAttemptId() == "" {
		return fmt.Errorf("screener_attempt_id cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}
	return nil
}
