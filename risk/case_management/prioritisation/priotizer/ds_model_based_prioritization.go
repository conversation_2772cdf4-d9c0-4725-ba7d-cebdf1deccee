package priotizer

// nolint: goimports
import (
	"context"
	"fmt"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/risk/case_management/prioritization"
	"github.com/epifi/gamma/api/vendorgateway/risk"
	"github.com/epifi/gamma/risk/case_management/prioritisation/integrator"
)

const modelVersion = "v0"

type DSModelBasedPrioritization struct {
	ModelParameterIntegrator integrator.ParameterIntegrator
	VGRiskClient             risk.RiskClient
}

var _ IDSModelBasedPrioritization = &DSModelBasedPrioritization{}

func NewDSModelBasedPrioritization(modelParameterIntegrator integrator.ParameterIntegrator, vgRiskClient risk.RiskClient) *DSModelBasedPrioritization {
	return &DSModelBasedPrioritization{
		ModelParameterIntegrator: modelParameterIntegrator,
		VGRiskClient:             vgRiskClient,
	}
}

func (d *DSModelBasedPrioritization) GetConfidenceScore(ctx context.Context, params *prioritization.InputParameter) (float32, error) {
	if params == nil {
		return 0, fmt.Errorf("invalid parameter %w", epifierrors.ErrInvalidArgument)
	}

	integratedParameter, integratedParameterErr := d.ModelParameterIntegrator.Get(ctx, params)
	if integratedParameterErr != nil {
		return 0, fmt.Errorf("could not fetch the prioritization parameters, %w", integratedParameterErr)
	}

	reqId := uuid.NewString()
	request := &risk.GetCasePrioritisationScoreRequest{
		Header: &vendorgateway.RequestHeader{
			Vendor: vendorgateway.Vendor_IN_HOUSE,
		},
		ActorId:              params.GetActorId(),
		RequestId:            reqId,
		AlertWithRuleDetails: params.GetAlerts(),
		ModelVersion:         modelVersion,
	}

	getCasePrioritisationScoreRes, err := d.VGRiskClient.GetCasePrioritisationScore(ctx, request)
	if rpcErr := epifigrpc.RPCError(getCasePrioritisationScoreRes, err); rpcErr != nil {
		logger.Error(ctx, "error in VGRiskClient.GetCasePrioritisationScore call", zap.Error(rpcErr))
		return 0, fmt.Errorf("error in VGRiskClient.GetCasePrioritisationScore call, err: %w", rpcErr)
	}

	logger.Info(ctx, fmt.Sprintf("prioritization parameters %s", integratedParameter))
	logger.Info(ctx, fmt.Sprintf("calculated score %f", getCasePrioritisationScoreRes.GetScore()))
	return getCasePrioritisationScoreRes.GetScore(), nil
}
