//go:generate dao_metrics_gen .
package dao

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"github.com/google/wire"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	riskPb "github.com/epifi/gamma/api/risk"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	leaPb "github.com/epifi/gamma/api/risk/lea"
	riskLienPb "github.com/epifi/gamma/api/risk/lien"
	screenerPb "github.com/epifi/gamma/api/risk/screener"
	taggingPb "github.com/epifi/gamma/api/risk/tagging"
	whitelistPb "github.com/epifi/gamma/api/risk/whitelist"
)

//go:generate mockgen -source=dao.go -destination=mocks/mock_dao.go -package=mocks
var WireSet = wire.NewSet(
	NewRiskDataDao, wire.Bind(new(RiskDataDao), new(*RiskDataDaoCRDB)),
	NewRedListDao, wire.Bind(new(RedListDao), new(*RedListCRDB)),
	NewRiskBankActionsDao, wire.Bind(new(RiskBankActionsDao), new(*RiskBankActionsCRDB)),
	NewTxnRiskScoreDao, wire.Bind(new(TxnRiskScoreDao), new(*TxnRiskScoreImpl)),
	NewTxnRiskScoreDetailDao, wire.Bind(new(TxnRiskScoreDetailDao), new(*TxnRiskScoreDetailImpl)),
	NewRiskEvaluatorEntityDao, wire.Bind(new(RiskEvaluatorEntityDao), new(*RiskEvaluatorEntityImpl)),
	NewLeaComplaintDao, wire.Bind(new(LeaComplaintDao), new(*LeaComplaintCRDB)),
	NewLEAComplaintNarrationDao, wire.Bind(new(LEAComplaintNarrationDao), new(*LEAComplaintNarrationPGDB)),
	NewScreenerAttemptPGDB, wire.Bind(new(ScreenerAttemptDao), new(*ScreenerAttemptPGDB)),
	NewLEAComplaintSourceDao, wire.Bind(new(LEAComplaintSourceDao), new(*LEAComplaintSourcePGDB)),
	NewScreenerAttemptRiskDataMappingPGDB, wire.Bind(new(ScreenerAttemptRiskDataMappingsDao), new(*ScreenerAttemptRiskDataMappingPGDB)),
	NewDisputeDao, wire.Bind(new(DisputeDao), new(*DisputePGDB)),
	NewWhiteListDao, wire.Bind(new(WhiteListDao), new(*WhiteListPGDB)),
	NewBureauIdDetailsDao, wire.Bind(new(BureauIdDetailsDao), new(*BureauIdDetailsPGDB)),
	NewTxnTagMappingDao, wire.Bind(new(TxnTagMappingDao), new(*TxnTagMappingPGDB)),
	NewUnifiedLeaComplaintPGDB, wire.Bind(new(UnifiedLEAComplaintDao), new(*UnifiedLeaComplaintPGDB)),
	NewLienRequestDao, wire.Bind(new(LienRequestDao), new(*LienRequestPGDB)),
)

type RiskDataDao interface {
	// Create -> Delete existing entry for given actor and Risk Source if exist then create new entry in table
	Create(ctx context.Context, riskData *riskPb.RiskData) (*riskPb.RiskData, error)
	// GetByActorIdAndParams -> return risk data for given risk params and if array is empty it will return for riskParams
	GetByActorIdAndParams(ctx context.Context, actorId string, riskParam []riskPb.RiskParam, options ...storagev2.FilterOption) ([]*riskPb.RiskData, error)
	// DeleteByActorIdAndParams -> delete entry to that actor id if array is empty it won't delete any
	DeleteByActorIdAndParams(ctx context.Context, actorId string, riskParam []riskPb.RiskParam) error
	// GetByActorId will return all the latest screener check results for the given actor id
	// Additional filters can be applied using options
	// returns epifierrors.ErrInvalidArgument if actor id is not passed
	// returns epifierrors.ErrRecordNotFound in case no db entries are found
	// return non nil error in case of other db errors
	GetByActorId(ctx context.Context, actorId string, options ...storagev2.FilterOption) ([]*riskPb.RiskData, error)
	// GetById will return the risk data row corresponding to given id
	// returns epifierrors.ErrInvalidArgument if id is not passed
	// returns epifierrors.ErrRecordNotFound in case no db entries are found
	// return non nil error in case of other db errors
	GetById(ctx context.Context, id string) (*riskPb.RiskData, error)
}

type RedListDao interface {
	// Insert dao method for red list inserts all the members passed to it as argument in the table.
	Insert(ctx context.Context, members []*riskPb.RedLister) ([]*riskPb.RedLister, error)
	// Upsert dao method for red list upserts all the members passed to it as argument in the table on conflict with category, value, and deleted_at_unix.
	Upsert(ctx context.Context, members []*riskPb.RedLister) ([]*riskPb.RedLister, error)
	// GetByCategoryAndVal dao method for red list fetches a red list member by its category and value. This can be used to fetch the risk scores.
	GetByCategoryAndVal(ctx context.Context, redlistPair *riskPb.RedListPair) (*riskPb.RedLister, error)
	// DeleteByCategoryAndValue soft deletes a red listed entry based on the category and value
	DeleteByCategoryAndValue(ctx context.Context, redlistPairs []*riskPb.RedListPair) error
	// GetValuesNotInDbByCategory fetches all the red listed records for a given category type not amongst the given values
	GetValuesNotInDbByCategory(ctx context.Context, category riskPb.RedListCategory, values []*riskPb.RedListPair) ([]*riskPb.RedLister, error)
}

type RiskBankActionsDao interface {
	// Create dao method creates a new entry for risk bank action in the table.
	Create(ctx context.Context, riskBankAction *riskPb.RiskBankActions) (*riskPb.RiskBankActions, error)
	// BatchCreate dao method creates a new batch entry for given risk bank actions in the table.
	BatchCreate(ctx context.Context, riskBankActions []*riskPb.RiskBankActions) ([]*riskPb.RiskBankActions, error)
	// GetByAction dao method for risk bank action fetches a RiskBankActions entity by its action along with filter options
	GetByAction(ctx context.Context, action riskEnumsPb.Action, selectMask []riskPb.RiskBankActionsFieldMask, options ...storagev2.FilterOption) ([]*riskPb.RiskBankActions, error)
	// GetByState dao method for risk bank action fetches RiskBankActions entity by its state along with filter options
	GetByState(ctx context.Context, state riskEnumsPb.State, selectMask []riskPb.RiskBankActionsFieldMask, options ...storagev2.FilterOption) ([]*riskPb.RiskBankActions, error)
	// GetByClientReqId dao method for risk bank action fetches RiskBankActions entity by its ClientReqId
	GetByClientReqId(ctx context.Context, clientReqId string, selectMask []riskPb.RiskBankActionsFieldMask) (*riskPb.RiskBankActions, error)
	// GetByClientReqIds dao method for risk bank action fetches bulk RiskBankActions members by its ClientReqId array
	GetByClientReqIds(ctx context.Context, clientReqId []string, selectMask []riskPb.RiskBankActionsFieldMask) ([]*riskPb.RiskBankActions, error)
	// Update updates RiskBankActions on primary key for requested field mask into table and returns
	// error if no row updated or error encountered
	Update(ctx context.Context, riskBankAction *riskPb.RiskBankActions, updateMask []riskPb.RiskBankActionsFieldMask) error
	// BatchUpdateState updates in bulk RiskBankActions state on client_req_id for state returns error
	BatchUpdateState(ctx context.Context, clientReqIds []string, status riskEnumsPb.State) error
	// GetByActor fetches all bank actions associated with the actor ID
	GetByActor(ctx context.Context, actorId string, limit int, options ...storagev2.FilterOption) ([]*riskPb.RiskBankActions, error)
}

type TxnRiskScoreDao interface {
	// Create -> Create the Txn Risk Score generated from the Risk Evaluators
	Create(ctx context.Context, txnRiskScore *riskPb.TxnRiskScore) (*riskPb.TxnRiskScore, error)
}

type TxnRiskScoreDetailDao interface {
	// Create -> Create the Txn Risk Score details generated from the Risk Evaluators
	Insert(ctx context.Context, details []*riskPb.TxnRiskScoreDetail) ([]*riskPb.TxnRiskScoreDetail, error)
}

type RiskEvaluatorEntityDao interface {
	// Create -> Create the Risk Evaluator Entity sent to the Risk Evaluators
	Create(ctx context.Context, riskEvaluatorEntity *riskPb.RiskEvaluatorEntity) (*riskPb.RiskEvaluatorEntity, error)
	// Update -> Update the Risk Evaluator Entity sent to the Risk Evaluators
	Update(ctx context.Context, riskEvaluatorEntity *riskPb.RiskEvaluatorEntity, selectMask []riskPb.RiskEvaluatorEntityFieldMask) (*riskPb.RiskEvaluatorEntity, error)
	// Delete -> Delete the risk evaluator entity by id
	Delete(ctx context.Context, id string) error
	// GetEntityForVendor -> Get the record for the given entity and vendor, here entity can be customer and vendor can be
	// dronapay/phonepe etc.
	// It can also give the row with update lock if withUpdateLock param is set to true.
	GetEntityForVendor(ctx context.Context, entity *riskPb.RiskEntity, vendor commonvgpb.Vendor,
		selectMask []riskPb.RiskEvaluatorEntityFieldMask, withUpdateLock bool) (*riskPb.RiskEvaluatorEntity, error)
}

type LeaComplaintDao interface {
	// Create -> Create the Lea Complaint got from the partner bank
	Create(ctx context.Context, leaComplaint *riskPb.LEAComplaint) (*riskPb.LEAComplaint, error)

	// Get --> Get the lea complaint data for the actor id
	// if limit is not specified, limit condition will be cancelled
	Get(ctx context.Context, actorId string, limit int, options ...storagev2.FilterOption) ([]*riskPb.LEAComplaint, error)

	// GetById --> gets the lea complaint data for id
	// Fails with epifierrors.ErrInvalidArgument if id is empty.
	// Fails with epifierrors.ErrRecordNotFound if there is no complaint with id.
	GetById(ctx context.Context, id string) (*riskPb.LEAComplaint, error)

	// Update -> Updates the lea complaint for given field masks.
	// Fails with epifierrors.ErrInvalidArgument if id is empty.
	// Fails with epifierrors.ErrRecordNotFound if no rows were updated
	Update(ctx context.Context, complaint *riskPb.LEAComplaint, updateMasks []riskPb.LEAComplaintFieldMask) error
}

type LEAComplaintNarrationDao interface {
	// Create -> Creates the LEA Complaint Narration received from the partner bank.
	// Fails with epifierrors.ErrInvalidArgument if any of mandatory params is missing.
	Create(ctx context.Context, leaComplaintNarration *riskPb.LEAComplaintNarration) (*riskPb.LEAComplaintNarration, error)

	// Get --> Get the lea complaint narrations for the actor id
	// If limit is not specified, limit condition will be cancelled.
	// Default order of narrations is in descending order of creation time.
	// Fails with epifierrors.ErrInvalidArgument if actor id is empty.
	// Fails with epifierrors.ErrRecordNotFound if there are no complaint narrations against the actor.
	Get(ctx context.Context, actorId string, limit int) ([]*riskPb.LEAComplaintNarration, error)
}

type ScreenerAttemptDao interface {
	// Create -> Creates the screener attempt.
	// Fails with epifierrors.ErrInvalidArgument if any of mandatory params is missing.
	// Fails with epifierrors.ErrAlreadyExists in case of unique constraint violation
	Create(ctx context.Context, attempt *screenerPb.ScreenerAttempt) (*screenerPb.ScreenerAttempt, error)
	// Update -> Update the screener attempt for given id and update mask fields
	// Fails with epifierrors.ErrInvalidArgument if id is empty.
	// Fails with epifierrors.ErrRowNotUpdated if no rows were updated
	Update(ctx context.Context, attempt *screenerPb.ScreenerAttempt, updateMask []screenerPb.ScreenerAttemptFieldMask) (*screenerPb.ScreenerAttempt, error)
	// GetByClientRequestIdAndCriteria --> Get the screenerAttempts for client request id and criteria
	// Since client_req_id + criteria is a unique field, will return a single entry
	// Fails with epifierrors.ErrInvalidArgument if request id is empty.
	// Fails with epifierrors.ErrRecordNotFound if there are no details for that request id
	GetByClientRequestIdAndCriteria(ctx context.Context, requestId string, criteria riskEnumsPb.ScreenerCriteria, options ...storagev2.FilterOption) (*screenerPb.ScreenerAttempt, error)
	// GetById --> Get the screenerAttempts for given id
	// Fails with epifierrors.ErrInvalidArgument if request id is empty.
	// Fails with epifierrors.ErrRecordNotFound if there are no details for that request id
	GetById(ctx context.Context, id string) (*screenerPb.ScreenerAttempt, error)

	// GetByActorIdAndCriteria -> Get the latest screener attempt for actor based on screener criteria.
	// Fails with epifierrors.ErrInvalidArgument if actor id is empty or criteria is unspecified.
	// Fails with epifierrors.ErrRecordNotFound if there is no record for that actor id and criteria.
	GetByActorIdAndCriteria(ctx context.Context, actorId string, criteria riskEnumsPb.ScreenerCriteria, options ...storagev2.FilterOption) (*screenerPb.ScreenerAttempt, error)

	// GetByActorId ---> Get all the screener attempts for given actorId
	// Fails with epifierrors.ErrInvalidArgument if actor id is empty or selectedMask is empty or limit passed is above then maximum limit.
	// Fails with epifierrors.ErrRecordNotFound if there are no records for given actor id.
	GetByActorId(ctx context.Context, actorId string, selectMask []screenerPb.ScreenerAttemptFieldMask, limit int, options ...storagev2.FilterOption) ([]*screenerPb.ScreenerAttempt, error)
}

type ScreenerAttemptRiskDataMappingsDao interface {
	// Create -> Creates the screener <> risk data mapping.
	// Fails with epifierrors.ErrInvalidArgument if any of mandatory params is missing.
	Create(ctx context.Context, mapping *screenerPb.ScreenerAttemptRiskDataMapping) (*screenerPb.ScreenerAttemptRiskDataMapping, error)
	// GetByScreenerAttemptId -> Get the mappings for screener attempt id
	// since table can have m (risk_data) -> n (screener_attempts) mappings, current query can return array of objects
	// sorted by created_at desc i.e. the latest first
	// Fails with epifierrors.ErrInvalidArgument if screener attempt id is empty.
	// Fails with epifierrors.ErrRecordNotFound if there are no details for that screener attempt id
	GetByScreenerAttemptId(ctx context.Context, screenerAttemptId string, options ...storagev2.FilterOption) ([]*screenerPb.ScreenerAttemptRiskDataMapping, error)
	// GetByRiskDataId -> Get the mappings for risk data id
	// since table can have m (risk_data) -> n (screener_attempts) mappings, current query can return array of objects
	// Fails with epifierrors.ErrInvalidArgument if risk data id is empty.
	// Fails with epifierrors.ErrRecordNotFound if there are no details for that risk data id
	GetByRiskDataId(ctx context.Context, riskDataId string, options ...storagev2.FilterOption) ([]*screenerPb.ScreenerAttemptRiskDataMapping, error)
}

type LEAComplaintSourceDao interface {
	// Create -> Creates the LEA source info received from the partner bank for a complaint.
	// Fails with epifierrors.ErrInvalidArgument if any of mandatory params is missing.
	Create(ctx context.Context, leaDetail *riskPb.LEAComplaintSource) (*riskPb.LEAComplaintSource, error)

	// GetByComplaintId --> Returns the lea complaint source info for complaint id
	// Fails with epifierrors.ErrInvalidArgument if complaint id is empty.
	// Fails with epifierrors.ErrRecordNotFound if there are no lea details for complaint.
	GetByComplaintId(ctx context.Context, complaintId string) (*riskPb.LEAComplaintSource, error)
}

type UnifiedLEAComplaintDao interface {
	// Create -> creates a unified LEA complaint entry in table.
	// Fails with epifierrors.ErrInvalidArgument if any of mandatory params is missing.
	Create(ctx context.Context, unifiedLeaComplaint *leaPb.UnifiedLeaComplaint) (*leaPb.UnifiedLeaComplaint, error)

	// GetByActorId gives the unified LEA complaint for the given actorId and filters, returns in created_at desc
	// If limit is not specified, limit condition will be cancelled.
	// Fails with epifierrors.ErrInvalidArgument if actorId is empty.
	// Returns epifierrors.ErrRecordNotFound no values exist
	GetByActorId(ctx context.Context, actorId string, limit int, options ...storagev2.FilterOption) ([]*leaPb.UnifiedLeaComplaint, error)

	// GetByAccountNumber gives the unified LEA complaint for the given actorId and filters
	// Fails with epifierrors.ErrInvalidArgument if account number is empty.

	GetByAccountNumber(ctx context.Context, accountNumber string, options ...storagev2.FilterOption) ([]*leaPb.UnifiedLeaComplaint, error)

	// Update -> Update the screener attempt for given id and update mask fields
	// Fails with epifierrors.ErrInvalidArgument if id is empty.
	// Fails with epifierrors.ErrRowNotUpdated if no rows were updated
	Update(ctx context.Context, unifiedLeaComplaint *leaPb.UnifiedLeaComplaint, updateMask []leaPb.UnifiedLeaComplaintFieldMask) error

	// GetByComplaintId retrieves a list of complaint records filtered by complaint_id.
	// Currently, Complaint_id column does not have indexing, use only for very low qps use-cases
	// Fails with epifierrors.ErrInvalidArgument if complaintId is empty.
	// Returns epifierrors.ErrRecordNotFound if no records exist.
	GetByComplaintId(ctx context.Context, complaintId string) ([]*leaPb.UnifiedLeaComplaint, error)
}

type DisputeDao interface {
	// Upsert -> Records dispute in table, updates DisputeUpdatableColumns columns if dispute already exists.
	// Fails with epifierrors.ErrInvalidArgument if any of mandatory params is missing.
	Upsert(ctx context.Context, dispute *riskPb.Dispute) error
}
type WhiteListDao interface {
	// BulkCreate adds a batch of max 1000 members in whitelist
	// fails with epifierrors.ErrInvalidArgument if length of members list is greater than 1000 or empty
	BulkCreate(ctx context.Context, members []*whitelistPb.Member) ([]*whitelistPb.Member, error)
	// BulkDelete removes a batch of max 1000 members from whitelist in single call
	// fails with epifierrors.ErrRecordNotFound if no rows removed from whitelist
	// fails with epifierrors.ErrInvalidArgument if size of member items list is greater than 1000 or empty
	BulkDelete(ctx context.Context, members []*whitelistPb.MemberItem, metadata *whitelistPb.DeletionMetadata) error
	// GetByIdentifier will fetch whitelist members by given identifier value
	// fails with epifierrors.RecordNotFound if record not found for given identifier value
	// fails with epifierrors.ErrInvalidArgument if identifier value passed is empty
	GetByIdentifier(ctx context.Context, identifierValue string, filterOptions ...storagev2.FilterOption) ([]*whitelistPb.Member, error)
}

type BureauIdDetailsDao interface {
	// BulkInsert -> inserts a batch of max upto maxLimit in single call
	// fails with epifierrors.ErrInvalid argument if length of bureau info list is empty or greater than maxLimit.
	BulkInsert(ctx context.Context, bureauInfo []*riskPb.BureauIdRiskDetail) error
	// GetBureauIdDetailsByActorId -> will fetch bureau information for the given actor id
	// fails with epifierrors.ErrInvalidArgument if empty actorId is passed
	// fails with epifierrors.ErrRecordNotFound if record not found against the given actorId
	GetBureauIdDetailsByActorId(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*riskPb.BureauIdRiskDetail, error)
}

type TxnTagMappingDao interface {
	// BulkInsert adds transaction tag mappings upto maxTxnTagMappingsLimit in single call
	// fails with epifierrors.ErrInvalidArgument if length of txnTagMappings list is greater than maxTxnTagMappingsLimit or empty
	BulkInsert(ctx context.Context, txnTagMappings []*taggingPb.TransactionTagMapping) error
	// GetAllTagsForTxnId fetch all tags associated with given transaction id
	// fails with epifierrors.ErrInvalidArgument if empty transaction id passed
	// fails with epifierrors.ErrRecordNotFound if tag mapping not found for given transaction id
	GetAllTagsForTxnId(ctx context.Context, txnId string) ([]taggingPb.TransactionTag, error)
}

type LienRequestDao interface {
	// Create creates a new lien request entry in the database
	// Fails with epifierrors.ErrInvalidArgument if any of mandatory params is missing
	Create(ctx context.Context, lienRequest *riskLienPb.LienRequest) (*riskLienPb.LienRequest, error)

	// GetById retrieves a lien request by its ID
	// Fails with epifierrors.ErrInvalidArgument if id is empty
	// Returns epifierrors.ErrRecordNotFound if no record exists
	GetById(ctx context.Context, id string, options ...storagev2.FilterOption) (*riskLienPb.LienRequest, error)

	// GetByLienId retrieves a lien request by the bank's lien ID
	// Fails with epifierrors.ErrInvalidArgument if lien ID is empty
	// Returns epifierrors.ErrRecordNotFound if no record exists
	GetByLienId(ctx context.Context, lienId string, options ...storagev2.FilterOption) ([]*riskLienPb.LienRequest, error)

	// GetBySavingsAccountNumber retrieves all lien requests for a given savings account number
	// Fails with epifierrors.ErrInvalidArgument if savings account number is empty
	// Returns epifierrors.ErrRecordNotFound if no records exist
	GetBySavingsAccountNumber(ctx context.Context, savingsAccountNumber string, options ...storagev2.FilterOption) ([]*riskLienPb.LienRequest, error)
}
