// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	risk "github.com/epifi/gamma/api/risk"
	enums "github.com/epifi/gamma/api/risk/enums"
	lea "github.com/epifi/gamma/api/risk/lea"
	lien "github.com/epifi/gamma/api/risk/lien"
	screener "github.com/epifi/gamma/api/risk/screener"
	tagging "github.com/epifi/gamma/api/risk/tagging"
	whitelist "github.com/epifi/gamma/api/risk/whitelist"
	gomock "github.com/golang/mock/gomock"
)

// MockRiskDataDao is a mock of RiskDataDao interface.
type MockRiskDataDao struct {
	ctrl     *gomock.Controller
	recorder *MockRiskDataDaoMockRecorder
}

// MockRiskDataDaoMockRecorder is the mock recorder for MockRiskDataDao.
type MockRiskDataDaoMockRecorder struct {
	mock *MockRiskDataDao
}

// NewMockRiskDataDao creates a new mock instance.
func NewMockRiskDataDao(ctrl *gomock.Controller) *MockRiskDataDao {
	mock := &MockRiskDataDao{ctrl: ctrl}
	mock.recorder = &MockRiskDataDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskDataDao) EXPECT() *MockRiskDataDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRiskDataDao) Create(ctx context.Context, riskData *risk.RiskData) (*risk.RiskData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, riskData)
	ret0, _ := ret[0].(*risk.RiskData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRiskDataDaoMockRecorder) Create(ctx, riskData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRiskDataDao)(nil).Create), ctx, riskData)
}

// DeleteByActorIdAndParams mocks base method.
func (m *MockRiskDataDao) DeleteByActorIdAndParams(ctx context.Context, actorId string, riskParam []risk.RiskParam) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByActorIdAndParams", ctx, actorId, riskParam)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByActorIdAndParams indicates an expected call of DeleteByActorIdAndParams.
func (mr *MockRiskDataDaoMockRecorder) DeleteByActorIdAndParams(ctx, actorId, riskParam interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByActorIdAndParams", reflect.TypeOf((*MockRiskDataDao)(nil).DeleteByActorIdAndParams), ctx, actorId, riskParam)
}

// GetByActorId mocks base method.
func (m *MockRiskDataDao) GetByActorId(ctx context.Context, actorId string, options ...storagev2.FilterOption) ([]*risk.RiskData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorId", varargs...)
	ret0, _ := ret[0].([]*risk.RiskData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockRiskDataDaoMockRecorder) GetByActorId(ctx, actorId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockRiskDataDao)(nil).GetByActorId), varargs...)
}

// GetByActorIdAndParams mocks base method.
func (m *MockRiskDataDao) GetByActorIdAndParams(ctx context.Context, actorId string, riskParam []risk.RiskParam, options ...storagev2.FilterOption) ([]*risk.RiskData, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, riskParam}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorIdAndParams", varargs...)
	ret0, _ := ret[0].([]*risk.RiskData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndParams indicates an expected call of GetByActorIdAndParams.
func (mr *MockRiskDataDaoMockRecorder) GetByActorIdAndParams(ctx, actorId, riskParam interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, riskParam}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndParams", reflect.TypeOf((*MockRiskDataDao)(nil).GetByActorIdAndParams), varargs...)
}

// GetById mocks base method.
func (m *MockRiskDataDao) GetById(ctx context.Context, id string) (*risk.RiskData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*risk.RiskData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockRiskDataDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockRiskDataDao)(nil).GetById), ctx, id)
}

// MockRedListDao is a mock of RedListDao interface.
type MockRedListDao struct {
	ctrl     *gomock.Controller
	recorder *MockRedListDaoMockRecorder
}

// MockRedListDaoMockRecorder is the mock recorder for MockRedListDao.
type MockRedListDaoMockRecorder struct {
	mock *MockRedListDao
}

// NewMockRedListDao creates a new mock instance.
func NewMockRedListDao(ctrl *gomock.Controller) *MockRedListDao {
	mock := &MockRedListDao{ctrl: ctrl}
	mock.recorder = &MockRedListDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRedListDao) EXPECT() *MockRedListDaoMockRecorder {
	return m.recorder
}

// DeleteByCategoryAndValue mocks base method.
func (m *MockRedListDao) DeleteByCategoryAndValue(ctx context.Context, redlistPairs []*risk.RedListPair) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByCategoryAndValue", ctx, redlistPairs)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByCategoryAndValue indicates an expected call of DeleteByCategoryAndValue.
func (mr *MockRedListDaoMockRecorder) DeleteByCategoryAndValue(ctx, redlistPairs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByCategoryAndValue", reflect.TypeOf((*MockRedListDao)(nil).DeleteByCategoryAndValue), ctx, redlistPairs)
}

// GetByCategoryAndVal mocks base method.
func (m *MockRedListDao) GetByCategoryAndVal(ctx context.Context, redlistPair *risk.RedListPair) (*risk.RedLister, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCategoryAndVal", ctx, redlistPair)
	ret0, _ := ret[0].(*risk.RedLister)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCategoryAndVal indicates an expected call of GetByCategoryAndVal.
func (mr *MockRedListDaoMockRecorder) GetByCategoryAndVal(ctx, redlistPair interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCategoryAndVal", reflect.TypeOf((*MockRedListDao)(nil).GetByCategoryAndVal), ctx, redlistPair)
}

// GetValuesNotInDbByCategory mocks base method.
func (m *MockRedListDao) GetValuesNotInDbByCategory(ctx context.Context, category risk.RedListCategory, values []*risk.RedListPair) ([]*risk.RedLister, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetValuesNotInDbByCategory", ctx, category, values)
	ret0, _ := ret[0].([]*risk.RedLister)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetValuesNotInDbByCategory indicates an expected call of GetValuesNotInDbByCategory.
func (mr *MockRedListDaoMockRecorder) GetValuesNotInDbByCategory(ctx, category, values interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetValuesNotInDbByCategory", reflect.TypeOf((*MockRedListDao)(nil).GetValuesNotInDbByCategory), ctx, category, values)
}

// Insert mocks base method.
func (m *MockRedListDao) Insert(ctx context.Context, members []*risk.RedLister) ([]*risk.RedLister, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, members)
	ret0, _ := ret[0].([]*risk.RedLister)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockRedListDaoMockRecorder) Insert(ctx, members interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockRedListDao)(nil).Insert), ctx, members)
}

// Upsert mocks base method.
func (m *MockRedListDao) Upsert(ctx context.Context, members []*risk.RedLister) ([]*risk.RedLister, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, members)
	ret0, _ := ret[0].([]*risk.RedLister)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upsert indicates an expected call of Upsert.
func (mr *MockRedListDaoMockRecorder) Upsert(ctx, members interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockRedListDao)(nil).Upsert), ctx, members)
}

// MockRiskBankActionsDao is a mock of RiskBankActionsDao interface.
type MockRiskBankActionsDao struct {
	ctrl     *gomock.Controller
	recorder *MockRiskBankActionsDaoMockRecorder
}

// MockRiskBankActionsDaoMockRecorder is the mock recorder for MockRiskBankActionsDao.
type MockRiskBankActionsDaoMockRecorder struct {
	mock *MockRiskBankActionsDao
}

// NewMockRiskBankActionsDao creates a new mock instance.
func NewMockRiskBankActionsDao(ctrl *gomock.Controller) *MockRiskBankActionsDao {
	mock := &MockRiskBankActionsDao{ctrl: ctrl}
	mock.recorder = &MockRiskBankActionsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskBankActionsDao) EXPECT() *MockRiskBankActionsDaoMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockRiskBankActionsDao) BatchCreate(ctx context.Context, riskBankActions []*risk.RiskBankActions) ([]*risk.RiskBankActions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", ctx, riskBankActions)
	ret0, _ := ret[0].([]*risk.RiskBankActions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockRiskBankActionsDaoMockRecorder) BatchCreate(ctx, riskBankActions interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockRiskBankActionsDao)(nil).BatchCreate), ctx, riskBankActions)
}

// BatchUpdateState mocks base method.
func (m *MockRiskBankActionsDao) BatchUpdateState(ctx context.Context, clientReqIds []string, status enums.State) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateState", ctx, clientReqIds, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateState indicates an expected call of BatchUpdateState.
func (mr *MockRiskBankActionsDaoMockRecorder) BatchUpdateState(ctx, clientReqIds, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateState", reflect.TypeOf((*MockRiskBankActionsDao)(nil).BatchUpdateState), ctx, clientReqIds, status)
}

// Create mocks base method.
func (m *MockRiskBankActionsDao) Create(ctx context.Context, riskBankAction *risk.RiskBankActions) (*risk.RiskBankActions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, riskBankAction)
	ret0, _ := ret[0].(*risk.RiskBankActions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRiskBankActionsDaoMockRecorder) Create(ctx, riskBankAction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRiskBankActionsDao)(nil).Create), ctx, riskBankAction)
}

// GetByAction mocks base method.
func (m *MockRiskBankActionsDao) GetByAction(ctx context.Context, action enums.Action, selectMask []risk.RiskBankActionsFieldMask, options ...storagev2.FilterOption) ([]*risk.RiskBankActions, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, action, selectMask}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByAction", varargs...)
	ret0, _ := ret[0].([]*risk.RiskBankActions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAction indicates an expected call of GetByAction.
func (mr *MockRiskBankActionsDaoMockRecorder) GetByAction(ctx, action, selectMask interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, action, selectMask}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAction", reflect.TypeOf((*MockRiskBankActionsDao)(nil).GetByAction), varargs...)
}

// GetByActor mocks base method.
func (m *MockRiskBankActionsDao) GetByActor(ctx context.Context, actorId string, limit int, options ...storagev2.FilterOption) ([]*risk.RiskBankActions, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActor", varargs...)
	ret0, _ := ret[0].([]*risk.RiskBankActions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActor indicates an expected call of GetByActor.
func (mr *MockRiskBankActionsDaoMockRecorder) GetByActor(ctx, actorId, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActor", reflect.TypeOf((*MockRiskBankActionsDao)(nil).GetByActor), varargs...)
}

// GetByClientReqId mocks base method.
func (m *MockRiskBankActionsDao) GetByClientReqId(ctx context.Context, clientReqId string, selectMask []risk.RiskBankActionsFieldMask) (*risk.RiskBankActions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientReqId", ctx, clientReqId, selectMask)
	ret0, _ := ret[0].(*risk.RiskBankActions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientReqId indicates an expected call of GetByClientReqId.
func (mr *MockRiskBankActionsDaoMockRecorder) GetByClientReqId(ctx, clientReqId, selectMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientReqId", reflect.TypeOf((*MockRiskBankActionsDao)(nil).GetByClientReqId), ctx, clientReqId, selectMask)
}

// GetByClientReqIds mocks base method.
func (m *MockRiskBankActionsDao) GetByClientReqIds(ctx context.Context, clientReqId []string, selectMask []risk.RiskBankActionsFieldMask) ([]*risk.RiskBankActions, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientReqIds", ctx, clientReqId, selectMask)
	ret0, _ := ret[0].([]*risk.RiskBankActions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientReqIds indicates an expected call of GetByClientReqIds.
func (mr *MockRiskBankActionsDaoMockRecorder) GetByClientReqIds(ctx, clientReqId, selectMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientReqIds", reflect.TypeOf((*MockRiskBankActionsDao)(nil).GetByClientReqIds), ctx, clientReqId, selectMask)
}

// GetByState mocks base method.
func (m *MockRiskBankActionsDao) GetByState(ctx context.Context, state enums.State, selectMask []risk.RiskBankActionsFieldMask, options ...storagev2.FilterOption) ([]*risk.RiskBankActions, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, state, selectMask}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByState", varargs...)
	ret0, _ := ret[0].([]*risk.RiskBankActions)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByState indicates an expected call of GetByState.
func (mr *MockRiskBankActionsDaoMockRecorder) GetByState(ctx, state, selectMask interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, state, selectMask}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByState", reflect.TypeOf((*MockRiskBankActionsDao)(nil).GetByState), varargs...)
}

// Update mocks base method.
func (m *MockRiskBankActionsDao) Update(ctx context.Context, riskBankAction *risk.RiskBankActions, updateMask []risk.RiskBankActionsFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, riskBankAction, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRiskBankActionsDaoMockRecorder) Update(ctx, riskBankAction, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRiskBankActionsDao)(nil).Update), ctx, riskBankAction, updateMask)
}

// MockTxnRiskScoreDao is a mock of TxnRiskScoreDao interface.
type MockTxnRiskScoreDao struct {
	ctrl     *gomock.Controller
	recorder *MockTxnRiskScoreDaoMockRecorder
}

// MockTxnRiskScoreDaoMockRecorder is the mock recorder for MockTxnRiskScoreDao.
type MockTxnRiskScoreDaoMockRecorder struct {
	mock *MockTxnRiskScoreDao
}

// NewMockTxnRiskScoreDao creates a new mock instance.
func NewMockTxnRiskScoreDao(ctrl *gomock.Controller) *MockTxnRiskScoreDao {
	mock := &MockTxnRiskScoreDao{ctrl: ctrl}
	mock.recorder = &MockTxnRiskScoreDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTxnRiskScoreDao) EXPECT() *MockTxnRiskScoreDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTxnRiskScoreDao) Create(ctx context.Context, txnRiskScore *risk.TxnRiskScore) (*risk.TxnRiskScore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, txnRiskScore)
	ret0, _ := ret[0].(*risk.TxnRiskScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockTxnRiskScoreDaoMockRecorder) Create(ctx, txnRiskScore interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTxnRiskScoreDao)(nil).Create), ctx, txnRiskScore)
}

// MockTxnRiskScoreDetailDao is a mock of TxnRiskScoreDetailDao interface.
type MockTxnRiskScoreDetailDao struct {
	ctrl     *gomock.Controller
	recorder *MockTxnRiskScoreDetailDaoMockRecorder
}

// MockTxnRiskScoreDetailDaoMockRecorder is the mock recorder for MockTxnRiskScoreDetailDao.
type MockTxnRiskScoreDetailDaoMockRecorder struct {
	mock *MockTxnRiskScoreDetailDao
}

// NewMockTxnRiskScoreDetailDao creates a new mock instance.
func NewMockTxnRiskScoreDetailDao(ctrl *gomock.Controller) *MockTxnRiskScoreDetailDao {
	mock := &MockTxnRiskScoreDetailDao{ctrl: ctrl}
	mock.recorder = &MockTxnRiskScoreDetailDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTxnRiskScoreDetailDao) EXPECT() *MockTxnRiskScoreDetailDaoMockRecorder {
	return m.recorder
}

// Insert mocks base method.
func (m *MockTxnRiskScoreDetailDao) Insert(ctx context.Context, details []*risk.TxnRiskScoreDetail) ([]*risk.TxnRiskScoreDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, details)
	ret0, _ := ret[0].([]*risk.TxnRiskScoreDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Insert indicates an expected call of Insert.
func (mr *MockTxnRiskScoreDetailDaoMockRecorder) Insert(ctx, details interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockTxnRiskScoreDetailDao)(nil).Insert), ctx, details)
}

// MockRiskEvaluatorEntityDao is a mock of RiskEvaluatorEntityDao interface.
type MockRiskEvaluatorEntityDao struct {
	ctrl     *gomock.Controller
	recorder *MockRiskEvaluatorEntityDaoMockRecorder
}

// MockRiskEvaluatorEntityDaoMockRecorder is the mock recorder for MockRiskEvaluatorEntityDao.
type MockRiskEvaluatorEntityDaoMockRecorder struct {
	mock *MockRiskEvaluatorEntityDao
}

// NewMockRiskEvaluatorEntityDao creates a new mock instance.
func NewMockRiskEvaluatorEntityDao(ctrl *gomock.Controller) *MockRiskEvaluatorEntityDao {
	mock := &MockRiskEvaluatorEntityDao{ctrl: ctrl}
	mock.recorder = &MockRiskEvaluatorEntityDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRiskEvaluatorEntityDao) EXPECT() *MockRiskEvaluatorEntityDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRiskEvaluatorEntityDao) Create(ctx context.Context, riskEvaluatorEntity *risk.RiskEvaluatorEntity) (*risk.RiskEvaluatorEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, riskEvaluatorEntity)
	ret0, _ := ret[0].(*risk.RiskEvaluatorEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRiskEvaluatorEntityDaoMockRecorder) Create(ctx, riskEvaluatorEntity interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRiskEvaluatorEntityDao)(nil).Create), ctx, riskEvaluatorEntity)
}

// Delete mocks base method.
func (m *MockRiskEvaluatorEntityDao) Delete(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockRiskEvaluatorEntityDaoMockRecorder) Delete(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockRiskEvaluatorEntityDao)(nil).Delete), ctx, id)
}

// GetEntityForVendor mocks base method.
func (m *MockRiskEvaluatorEntityDao) GetEntityForVendor(ctx context.Context, entity *risk.RiskEntity, vendor vendorgateway.Vendor, selectMask []risk.RiskEvaluatorEntityFieldMask, withUpdateLock bool) (*risk.RiskEvaluatorEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityForVendor", ctx, entity, vendor, selectMask, withUpdateLock)
	ret0, _ := ret[0].(*risk.RiskEvaluatorEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityForVendor indicates an expected call of GetEntityForVendor.
func (mr *MockRiskEvaluatorEntityDaoMockRecorder) GetEntityForVendor(ctx, entity, vendor, selectMask, withUpdateLock interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityForVendor", reflect.TypeOf((*MockRiskEvaluatorEntityDao)(nil).GetEntityForVendor), ctx, entity, vendor, selectMask, withUpdateLock)
}

// Update mocks base method.
func (m *MockRiskEvaluatorEntityDao) Update(ctx context.Context, riskEvaluatorEntity *risk.RiskEvaluatorEntity, selectMask []risk.RiskEvaluatorEntityFieldMask) (*risk.RiskEvaluatorEntity, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, riskEvaluatorEntity, selectMask)
	ret0, _ := ret[0].(*risk.RiskEvaluatorEntity)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockRiskEvaluatorEntityDaoMockRecorder) Update(ctx, riskEvaluatorEntity, selectMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRiskEvaluatorEntityDao)(nil).Update), ctx, riskEvaluatorEntity, selectMask)
}

// MockLeaComplaintDao is a mock of LeaComplaintDao interface.
type MockLeaComplaintDao struct {
	ctrl     *gomock.Controller
	recorder *MockLeaComplaintDaoMockRecorder
}

// MockLeaComplaintDaoMockRecorder is the mock recorder for MockLeaComplaintDao.
type MockLeaComplaintDaoMockRecorder struct {
	mock *MockLeaComplaintDao
}

// NewMockLeaComplaintDao creates a new mock instance.
func NewMockLeaComplaintDao(ctrl *gomock.Controller) *MockLeaComplaintDao {
	mock := &MockLeaComplaintDao{ctrl: ctrl}
	mock.recorder = &MockLeaComplaintDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLeaComplaintDao) EXPECT() *MockLeaComplaintDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLeaComplaintDao) Create(ctx context.Context, leaComplaint *risk.LEAComplaint) (*risk.LEAComplaint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, leaComplaint)
	ret0, _ := ret[0].(*risk.LEAComplaint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLeaComplaintDaoMockRecorder) Create(ctx, leaComplaint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLeaComplaintDao)(nil).Create), ctx, leaComplaint)
}

// Get mocks base method.
func (m *MockLeaComplaintDao) Get(ctx context.Context, actorId string, limit int, options ...storagev2.FilterOption) ([]*risk.LEAComplaint, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].([]*risk.LEAComplaint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockLeaComplaintDaoMockRecorder) Get(ctx, actorId, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockLeaComplaintDao)(nil).Get), varargs...)
}

// GetById mocks base method.
func (m *MockLeaComplaintDao) GetById(ctx context.Context, id string) (*risk.LEAComplaint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*risk.LEAComplaint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLeaComplaintDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLeaComplaintDao)(nil).GetById), ctx, id)
}

// Update mocks base method.
func (m *MockLeaComplaintDao) Update(ctx context.Context, complaint *risk.LEAComplaint, updateMasks []risk.LEAComplaintFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, complaint, updateMasks)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLeaComplaintDaoMockRecorder) Update(ctx, complaint, updateMasks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLeaComplaintDao)(nil).Update), ctx, complaint, updateMasks)
}

// MockLEAComplaintNarrationDao is a mock of LEAComplaintNarrationDao interface.
type MockLEAComplaintNarrationDao struct {
	ctrl     *gomock.Controller
	recorder *MockLEAComplaintNarrationDaoMockRecorder
}

// MockLEAComplaintNarrationDaoMockRecorder is the mock recorder for MockLEAComplaintNarrationDao.
type MockLEAComplaintNarrationDaoMockRecorder struct {
	mock *MockLEAComplaintNarrationDao
}

// NewMockLEAComplaintNarrationDao creates a new mock instance.
func NewMockLEAComplaintNarrationDao(ctrl *gomock.Controller) *MockLEAComplaintNarrationDao {
	mock := &MockLEAComplaintNarrationDao{ctrl: ctrl}
	mock.recorder = &MockLEAComplaintNarrationDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLEAComplaintNarrationDao) EXPECT() *MockLEAComplaintNarrationDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLEAComplaintNarrationDao) Create(ctx context.Context, leaComplaintNarration *risk.LEAComplaintNarration) (*risk.LEAComplaintNarration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, leaComplaintNarration)
	ret0, _ := ret[0].(*risk.LEAComplaintNarration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLEAComplaintNarrationDaoMockRecorder) Create(ctx, leaComplaintNarration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLEAComplaintNarrationDao)(nil).Create), ctx, leaComplaintNarration)
}

// Get mocks base method.
func (m *MockLEAComplaintNarrationDao) Get(ctx context.Context, actorId string, limit int) ([]*risk.LEAComplaintNarration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, actorId, limit)
	ret0, _ := ret[0].([]*risk.LEAComplaintNarration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockLEAComplaintNarrationDaoMockRecorder) Get(ctx, actorId, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockLEAComplaintNarrationDao)(nil).Get), ctx, actorId, limit)
}

// MockScreenerAttemptDao is a mock of ScreenerAttemptDao interface.
type MockScreenerAttemptDao struct {
	ctrl     *gomock.Controller
	recorder *MockScreenerAttemptDaoMockRecorder
}

// MockScreenerAttemptDaoMockRecorder is the mock recorder for MockScreenerAttemptDao.
type MockScreenerAttemptDaoMockRecorder struct {
	mock *MockScreenerAttemptDao
}

// NewMockScreenerAttemptDao creates a new mock instance.
func NewMockScreenerAttemptDao(ctrl *gomock.Controller) *MockScreenerAttemptDao {
	mock := &MockScreenerAttemptDao{ctrl: ctrl}
	mock.recorder = &MockScreenerAttemptDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScreenerAttemptDao) EXPECT() *MockScreenerAttemptDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockScreenerAttemptDao) Create(ctx context.Context, attempt *screener.ScreenerAttempt) (*screener.ScreenerAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, attempt)
	ret0, _ := ret[0].(*screener.ScreenerAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockScreenerAttemptDaoMockRecorder) Create(ctx, attempt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockScreenerAttemptDao)(nil).Create), ctx, attempt)
}

// GetByActorId mocks base method.
func (m *MockScreenerAttemptDao) GetByActorId(ctx context.Context, actorId string, selectMask []screener.ScreenerAttemptFieldMask, limit int, options ...storagev2.FilterOption) ([]*screener.ScreenerAttempt, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, selectMask, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorId", varargs...)
	ret0, _ := ret[0].([]*screener.ScreenerAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockScreenerAttemptDaoMockRecorder) GetByActorId(ctx, actorId, selectMask, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, selectMask, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockScreenerAttemptDao)(nil).GetByActorId), varargs...)
}

// GetByActorIdAndCriteria mocks base method.
func (m *MockScreenerAttemptDao) GetByActorIdAndCriteria(ctx context.Context, actorId string, criteria enums.ScreenerCriteria, options ...storagev2.FilterOption) (*screener.ScreenerAttempt, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, criteria}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorIdAndCriteria", varargs...)
	ret0, _ := ret[0].(*screener.ScreenerAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndCriteria indicates an expected call of GetByActorIdAndCriteria.
func (mr *MockScreenerAttemptDaoMockRecorder) GetByActorIdAndCriteria(ctx, actorId, criteria interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, criteria}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndCriteria", reflect.TypeOf((*MockScreenerAttemptDao)(nil).GetByActorIdAndCriteria), varargs...)
}

// GetByClientRequestIdAndCriteria mocks base method.
func (m *MockScreenerAttemptDao) GetByClientRequestIdAndCriteria(ctx context.Context, requestId string, criteria enums.ScreenerCriteria, options ...storagev2.FilterOption) (*screener.ScreenerAttempt, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, requestId, criteria}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByClientRequestIdAndCriteria", varargs...)
	ret0, _ := ret[0].(*screener.ScreenerAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientRequestIdAndCriteria indicates an expected call of GetByClientRequestIdAndCriteria.
func (mr *MockScreenerAttemptDaoMockRecorder) GetByClientRequestIdAndCriteria(ctx, requestId, criteria interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, requestId, criteria}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientRequestIdAndCriteria", reflect.TypeOf((*MockScreenerAttemptDao)(nil).GetByClientRequestIdAndCriteria), varargs...)
}

// GetById mocks base method.
func (m *MockScreenerAttemptDao) GetById(ctx context.Context, id string) (*screener.ScreenerAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*screener.ScreenerAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockScreenerAttemptDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockScreenerAttemptDao)(nil).GetById), ctx, id)
}

// Update mocks base method.
func (m *MockScreenerAttemptDao) Update(ctx context.Context, attempt *screener.ScreenerAttempt, updateMask []screener.ScreenerAttemptFieldMask) (*screener.ScreenerAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, attempt, updateMask)
	ret0, _ := ret[0].(*screener.ScreenerAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockScreenerAttemptDaoMockRecorder) Update(ctx, attempt, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockScreenerAttemptDao)(nil).Update), ctx, attempt, updateMask)
}

// MockScreenerAttemptRiskDataMappingsDao is a mock of ScreenerAttemptRiskDataMappingsDao interface.
type MockScreenerAttemptRiskDataMappingsDao struct {
	ctrl     *gomock.Controller
	recorder *MockScreenerAttemptRiskDataMappingsDaoMockRecorder
}

// MockScreenerAttemptRiskDataMappingsDaoMockRecorder is the mock recorder for MockScreenerAttemptRiskDataMappingsDao.
type MockScreenerAttemptRiskDataMappingsDaoMockRecorder struct {
	mock *MockScreenerAttemptRiskDataMappingsDao
}

// NewMockScreenerAttemptRiskDataMappingsDao creates a new mock instance.
func NewMockScreenerAttemptRiskDataMappingsDao(ctrl *gomock.Controller) *MockScreenerAttemptRiskDataMappingsDao {
	mock := &MockScreenerAttemptRiskDataMappingsDao{ctrl: ctrl}
	mock.recorder = &MockScreenerAttemptRiskDataMappingsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockScreenerAttemptRiskDataMappingsDao) EXPECT() *MockScreenerAttemptRiskDataMappingsDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockScreenerAttemptRiskDataMappingsDao) Create(ctx context.Context, mapping *screener.ScreenerAttemptRiskDataMapping) (*screener.ScreenerAttemptRiskDataMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, mapping)
	ret0, _ := ret[0].(*screener.ScreenerAttemptRiskDataMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockScreenerAttemptRiskDataMappingsDaoMockRecorder) Create(ctx, mapping interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockScreenerAttemptRiskDataMappingsDao)(nil).Create), ctx, mapping)
}

// GetByRiskDataId mocks base method.
func (m *MockScreenerAttemptRiskDataMappingsDao) GetByRiskDataId(ctx context.Context, riskDataId string, options ...storagev2.FilterOption) ([]*screener.ScreenerAttemptRiskDataMapping, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, riskDataId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByRiskDataId", varargs...)
	ret0, _ := ret[0].([]*screener.ScreenerAttemptRiskDataMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRiskDataId indicates an expected call of GetByRiskDataId.
func (mr *MockScreenerAttemptRiskDataMappingsDaoMockRecorder) GetByRiskDataId(ctx, riskDataId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, riskDataId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRiskDataId", reflect.TypeOf((*MockScreenerAttemptRiskDataMappingsDao)(nil).GetByRiskDataId), varargs...)
}

// GetByScreenerAttemptId mocks base method.
func (m *MockScreenerAttemptRiskDataMappingsDao) GetByScreenerAttemptId(ctx context.Context, screenerAttemptId string, options ...storagev2.FilterOption) ([]*screener.ScreenerAttemptRiskDataMapping, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, screenerAttemptId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByScreenerAttemptId", varargs...)
	ret0, _ := ret[0].([]*screener.ScreenerAttemptRiskDataMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByScreenerAttemptId indicates an expected call of GetByScreenerAttemptId.
func (mr *MockScreenerAttemptRiskDataMappingsDaoMockRecorder) GetByScreenerAttemptId(ctx, screenerAttemptId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, screenerAttemptId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByScreenerAttemptId", reflect.TypeOf((*MockScreenerAttemptRiskDataMappingsDao)(nil).GetByScreenerAttemptId), varargs...)
}

// MockLEAComplaintSourceDao is a mock of LEAComplaintSourceDao interface.
type MockLEAComplaintSourceDao struct {
	ctrl     *gomock.Controller
	recorder *MockLEAComplaintSourceDaoMockRecorder
}

// MockLEAComplaintSourceDaoMockRecorder is the mock recorder for MockLEAComplaintSourceDao.
type MockLEAComplaintSourceDaoMockRecorder struct {
	mock *MockLEAComplaintSourceDao
}

// NewMockLEAComplaintSourceDao creates a new mock instance.
func NewMockLEAComplaintSourceDao(ctrl *gomock.Controller) *MockLEAComplaintSourceDao {
	mock := &MockLEAComplaintSourceDao{ctrl: ctrl}
	mock.recorder = &MockLEAComplaintSourceDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLEAComplaintSourceDao) EXPECT() *MockLEAComplaintSourceDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLEAComplaintSourceDao) Create(ctx context.Context, leaDetail *risk.LEAComplaintSource) (*risk.LEAComplaintSource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, leaDetail)
	ret0, _ := ret[0].(*risk.LEAComplaintSource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLEAComplaintSourceDaoMockRecorder) Create(ctx, leaDetail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLEAComplaintSourceDao)(nil).Create), ctx, leaDetail)
}

// GetByComplaintId mocks base method.
func (m *MockLEAComplaintSourceDao) GetByComplaintId(ctx context.Context, complaintId string) (*risk.LEAComplaintSource, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByComplaintId", ctx, complaintId)
	ret0, _ := ret[0].(*risk.LEAComplaintSource)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByComplaintId indicates an expected call of GetByComplaintId.
func (mr *MockLEAComplaintSourceDaoMockRecorder) GetByComplaintId(ctx, complaintId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByComplaintId", reflect.TypeOf((*MockLEAComplaintSourceDao)(nil).GetByComplaintId), ctx, complaintId)
}

// MockUnifiedLEAComplaintDao is a mock of UnifiedLEAComplaintDao interface.
type MockUnifiedLEAComplaintDao struct {
	ctrl     *gomock.Controller
	recorder *MockUnifiedLEAComplaintDaoMockRecorder
}

// MockUnifiedLEAComplaintDaoMockRecorder is the mock recorder for MockUnifiedLEAComplaintDao.
type MockUnifiedLEAComplaintDaoMockRecorder struct {
	mock *MockUnifiedLEAComplaintDao
}

// NewMockUnifiedLEAComplaintDao creates a new mock instance.
func NewMockUnifiedLEAComplaintDao(ctrl *gomock.Controller) *MockUnifiedLEAComplaintDao {
	mock := &MockUnifiedLEAComplaintDao{ctrl: ctrl}
	mock.recorder = &MockUnifiedLEAComplaintDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnifiedLEAComplaintDao) EXPECT() *MockUnifiedLEAComplaintDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockUnifiedLEAComplaintDao) Create(ctx context.Context, unifiedLeaComplaint *lea.UnifiedLeaComplaint) (*lea.UnifiedLeaComplaint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, unifiedLeaComplaint)
	ret0, _ := ret[0].(*lea.UnifiedLeaComplaint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockUnifiedLEAComplaintDaoMockRecorder) Create(ctx, unifiedLeaComplaint interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockUnifiedLEAComplaintDao)(nil).Create), ctx, unifiedLeaComplaint)
}

// GetByAccountNumber mocks base method.
func (m *MockUnifiedLEAComplaintDao) GetByAccountNumber(ctx context.Context, accountNumber string, options ...storagev2.FilterOption) ([]*lea.UnifiedLeaComplaint, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, accountNumber}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByAccountNumber", varargs...)
	ret0, _ := ret[0].([]*lea.UnifiedLeaComplaint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByAccountNumber indicates an expected call of GetByAccountNumber.
func (mr *MockUnifiedLEAComplaintDaoMockRecorder) GetByAccountNumber(ctx, accountNumber interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, accountNumber}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByAccountNumber", reflect.TypeOf((*MockUnifiedLEAComplaintDao)(nil).GetByAccountNumber), varargs...)
}

// GetByActorId mocks base method.
func (m *MockUnifiedLEAComplaintDao) GetByActorId(ctx context.Context, actorId string, limit int, options ...storagev2.FilterOption) ([]*lea.UnifiedLeaComplaint, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorId", varargs...)
	ret0, _ := ret[0].([]*lea.UnifiedLeaComplaint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockUnifiedLEAComplaintDaoMockRecorder) GetByActorId(ctx, actorId, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockUnifiedLEAComplaintDao)(nil).GetByActorId), varargs...)
}

// GetByComplaintId mocks base method.
func (m *MockUnifiedLEAComplaintDao) GetByComplaintId(ctx context.Context, complaintId string) ([]*lea.UnifiedLeaComplaint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByComplaintId", ctx, complaintId)
	ret0, _ := ret[0].([]*lea.UnifiedLeaComplaint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByComplaintId indicates an expected call of GetByComplaintId.
func (mr *MockUnifiedLEAComplaintDaoMockRecorder) GetByComplaintId(ctx, complaintId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByComplaintId", reflect.TypeOf((*MockUnifiedLEAComplaintDao)(nil).GetByComplaintId), ctx, complaintId)
}

// Update mocks base method.
func (m *MockUnifiedLEAComplaintDao) Update(ctx context.Context, unifiedLeaComplaint *lea.UnifiedLeaComplaint, updateMask []lea.UnifiedLeaComplaintFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, unifiedLeaComplaint, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockUnifiedLEAComplaintDaoMockRecorder) Update(ctx, unifiedLeaComplaint, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockUnifiedLEAComplaintDao)(nil).Update), ctx, unifiedLeaComplaint, updateMask)
}

// MockDisputeDao is a mock of DisputeDao interface.
type MockDisputeDao struct {
	ctrl     *gomock.Controller
	recorder *MockDisputeDaoMockRecorder
}

// MockDisputeDaoMockRecorder is the mock recorder for MockDisputeDao.
type MockDisputeDaoMockRecorder struct {
	mock *MockDisputeDao
}

// NewMockDisputeDao creates a new mock instance.
func NewMockDisputeDao(ctrl *gomock.Controller) *MockDisputeDao {
	mock := &MockDisputeDao{ctrl: ctrl}
	mock.recorder = &MockDisputeDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDisputeDao) EXPECT() *MockDisputeDaoMockRecorder {
	return m.recorder
}

// Upsert mocks base method.
func (m *MockDisputeDao) Upsert(ctx context.Context, dispute *risk.Dispute) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, dispute)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockDisputeDaoMockRecorder) Upsert(ctx, dispute interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockDisputeDao)(nil).Upsert), ctx, dispute)
}

// MockWhiteListDao is a mock of WhiteListDao interface.
type MockWhiteListDao struct {
	ctrl     *gomock.Controller
	recorder *MockWhiteListDaoMockRecorder
}

// MockWhiteListDaoMockRecorder is the mock recorder for MockWhiteListDao.
type MockWhiteListDaoMockRecorder struct {
	mock *MockWhiteListDao
}

// NewMockWhiteListDao creates a new mock instance.
func NewMockWhiteListDao(ctrl *gomock.Controller) *MockWhiteListDao {
	mock := &MockWhiteListDao{ctrl: ctrl}
	mock.recorder = &MockWhiteListDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWhiteListDao) EXPECT() *MockWhiteListDaoMockRecorder {
	return m.recorder
}

// BulkCreate mocks base method.
func (m *MockWhiteListDao) BulkCreate(ctx context.Context, members []*whitelist.Member) ([]*whitelist.Member, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkCreate", ctx, members)
	ret0, _ := ret[0].([]*whitelist.Member)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkCreate indicates an expected call of BulkCreate.
func (mr *MockWhiteListDaoMockRecorder) BulkCreate(ctx, members interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkCreate", reflect.TypeOf((*MockWhiteListDao)(nil).BulkCreate), ctx, members)
}

// BulkDelete mocks base method.
func (m *MockWhiteListDao) BulkDelete(ctx context.Context, members []*whitelist.MemberItem, metadata *whitelist.DeletionMetadata) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkDelete", ctx, members, metadata)
	ret0, _ := ret[0].(error)
	return ret0
}

// BulkDelete indicates an expected call of BulkDelete.
func (mr *MockWhiteListDaoMockRecorder) BulkDelete(ctx, members, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkDelete", reflect.TypeOf((*MockWhiteListDao)(nil).BulkDelete), ctx, members, metadata)
}

// GetByIdentifier mocks base method.
func (m *MockWhiteListDao) GetByIdentifier(ctx context.Context, identifierValue string, filterOptions ...storagev2.FilterOption) ([]*whitelist.Member, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, identifierValue}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByIdentifier", varargs...)
	ret0, _ := ret[0].([]*whitelist.Member)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIdentifier indicates an expected call of GetByIdentifier.
func (mr *MockWhiteListDaoMockRecorder) GetByIdentifier(ctx, identifierValue interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, identifierValue}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIdentifier", reflect.TypeOf((*MockWhiteListDao)(nil).GetByIdentifier), varargs...)
}

// MockBureauIdDetailsDao is a mock of BureauIdDetailsDao interface.
type MockBureauIdDetailsDao struct {
	ctrl     *gomock.Controller
	recorder *MockBureauIdDetailsDaoMockRecorder
}

// MockBureauIdDetailsDaoMockRecorder is the mock recorder for MockBureauIdDetailsDao.
type MockBureauIdDetailsDaoMockRecorder struct {
	mock *MockBureauIdDetailsDao
}

// NewMockBureauIdDetailsDao creates a new mock instance.
func NewMockBureauIdDetailsDao(ctrl *gomock.Controller) *MockBureauIdDetailsDao {
	mock := &MockBureauIdDetailsDao{ctrl: ctrl}
	mock.recorder = &MockBureauIdDetailsDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBureauIdDetailsDao) EXPECT() *MockBureauIdDetailsDaoMockRecorder {
	return m.recorder
}

// BulkInsert mocks base method.
func (m *MockBureauIdDetailsDao) BulkInsert(ctx context.Context, bureauInfo []*risk.BureauIdRiskDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkInsert", ctx, bureauInfo)
	ret0, _ := ret[0].(error)
	return ret0
}

// BulkInsert indicates an expected call of BulkInsert.
func (mr *MockBureauIdDetailsDaoMockRecorder) BulkInsert(ctx, bureauInfo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkInsert", reflect.TypeOf((*MockBureauIdDetailsDao)(nil).BulkInsert), ctx, bureauInfo)
}

// GetBureauIdDetailsByActorId mocks base method.
func (m *MockBureauIdDetailsDao) GetBureauIdDetailsByActorId(ctx context.Context, actorId string, filterOptions ...storagev2.FilterOption) ([]*risk.BureauIdRiskDetail, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId}
	for _, a := range filterOptions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBureauIdDetailsByActorId", varargs...)
	ret0, _ := ret[0].([]*risk.BureauIdRiskDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBureauIdDetailsByActorId indicates an expected call of GetBureauIdDetailsByActorId.
func (mr *MockBureauIdDetailsDaoMockRecorder) GetBureauIdDetailsByActorId(ctx, actorId interface{}, filterOptions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId}, filterOptions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBureauIdDetailsByActorId", reflect.TypeOf((*MockBureauIdDetailsDao)(nil).GetBureauIdDetailsByActorId), varargs...)
}

// MockTxnTagMappingDao is a mock of TxnTagMappingDao interface.
type MockTxnTagMappingDao struct {
	ctrl     *gomock.Controller
	recorder *MockTxnTagMappingDaoMockRecorder
}

// MockTxnTagMappingDaoMockRecorder is the mock recorder for MockTxnTagMappingDao.
type MockTxnTagMappingDaoMockRecorder struct {
	mock *MockTxnTagMappingDao
}

// NewMockTxnTagMappingDao creates a new mock instance.
func NewMockTxnTagMappingDao(ctrl *gomock.Controller) *MockTxnTagMappingDao {
	mock := &MockTxnTagMappingDao{ctrl: ctrl}
	mock.recorder = &MockTxnTagMappingDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTxnTagMappingDao) EXPECT() *MockTxnTagMappingDaoMockRecorder {
	return m.recorder
}

// BulkInsert mocks base method.
func (m *MockTxnTagMappingDao) BulkInsert(ctx context.Context, txnTagMappings []*tagging.TransactionTagMapping) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkInsert", ctx, txnTagMappings)
	ret0, _ := ret[0].(error)
	return ret0
}

// BulkInsert indicates an expected call of BulkInsert.
func (mr *MockTxnTagMappingDaoMockRecorder) BulkInsert(ctx, txnTagMappings interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkInsert", reflect.TypeOf((*MockTxnTagMappingDao)(nil).BulkInsert), ctx, txnTagMappings)
}

// GetAllTagsForTxnId mocks base method.
func (m *MockTxnTagMappingDao) GetAllTagsForTxnId(ctx context.Context, txnId string) ([]tagging.TransactionTag, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTagsForTxnId", ctx, txnId)
	ret0, _ := ret[0].([]tagging.TransactionTag)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTagsForTxnId indicates an expected call of GetAllTagsForTxnId.
func (mr *MockTxnTagMappingDaoMockRecorder) GetAllTagsForTxnId(ctx, txnId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTagsForTxnId", reflect.TypeOf((*MockTxnTagMappingDao)(nil).GetAllTagsForTxnId), ctx, txnId)
}

// MockLienRequestDao is a mock of LienRequestDao interface.
type MockLienRequestDao struct {
	ctrl     *gomock.Controller
	recorder *MockLienRequestDaoMockRecorder
}

// MockLienRequestDaoMockRecorder is the mock recorder for MockLienRequestDao.
type MockLienRequestDaoMockRecorder struct {
	mock *MockLienRequestDao
}

// NewMockLienRequestDao creates a new mock instance.
func NewMockLienRequestDao(ctrl *gomock.Controller) *MockLienRequestDao {
	mock := &MockLienRequestDao{ctrl: ctrl}
	mock.recorder = &MockLienRequestDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLienRequestDao) EXPECT() *MockLienRequestDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockLienRequestDao) Create(ctx context.Context, lienRequest *lien.LienRequest) (*lien.LienRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, lienRequest)
	ret0, _ := ret[0].(*lien.LienRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockLienRequestDaoMockRecorder) Create(ctx, lienRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockLienRequestDao)(nil).Create), ctx, lienRequest)
}

// GetById mocks base method.
func (m *MockLienRequestDao) GetById(ctx context.Context, id string, options ...storagev2.FilterOption) (*lien.LienRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetById", varargs...)
	ret0, _ := ret[0].(*lien.LienRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockLienRequestDaoMockRecorder) GetById(ctx, id interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockLienRequestDao)(nil).GetById), varargs...)
}

// GetByLienId mocks base method.
func (m *MockLienRequestDao) GetByLienId(ctx context.Context, lienId string, options ...storagev2.FilterOption) ([]*lien.LienRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, lienId}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByLienId", varargs...)
	ret0, _ := ret[0].([]*lien.LienRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByLienId indicates an expected call of GetByLienId.
func (mr *MockLienRequestDaoMockRecorder) GetByLienId(ctx, lienId interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, lienId}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByLienId", reflect.TypeOf((*MockLienRequestDao)(nil).GetByLienId), varargs...)
}

// GetBySavingsAccountNumber mocks base method.
func (m *MockLienRequestDao) GetBySavingsAccountNumber(ctx context.Context, savingsAccountNumber string, options ...storagev2.FilterOption) ([]*lien.LienRequest, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, savingsAccountNumber}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBySavingsAccountNumber", varargs...)
	ret0, _ := ret[0].([]*lien.LienRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBySavingsAccountNumber indicates an expected call of GetBySavingsAccountNumber.
func (mr *MockLienRequestDaoMockRecorder) GetBySavingsAccountNumber(ctx, savingsAccountNumber interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, savingsAccountNumber}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBySavingsAccountNumber", reflect.TypeOf((*MockLienRequestDao)(nil).GetBySavingsAccountNumber), varargs...)
}
