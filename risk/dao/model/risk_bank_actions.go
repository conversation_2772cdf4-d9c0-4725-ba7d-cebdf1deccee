package model

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"time"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/enums"
	"github.com/epifi/be-common/pkg/nulltypes"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
)

type RiskBankActions struct {
	Id               string `gorm:"type:uuid;default:gen_random_uuid()"`
	ClientReqId      string
	ActorId          string
	AccountId        string
	AccountType      accounts.Type
	Vendor           commonvgpb.Vendor
	State            enums.State
	Action           enums.Action
	RequestReason    *risk.RequestReason
	FailureReason    enums.FailureReason
	BankActionReason *risk.BankActionReason
	Provenance       enums.Provenance
	CommsTemplate    risk.CommsTemplateArray `gorm:"type:text[]"`
	IsRecon          nulltypes.NullBoolEnum
	BankActionDate   nulltypes.NullTime
	LienRequestId    string

	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt
}

// NewRiskBankActionsModel convert RiskBankActions proto to RiskBankActions model
func NewRiskBankActionsModel(riskBankAction *risk.RiskBankActions) *RiskBankActions {
	mdl := &RiskBankActions{
		Id:               riskBankAction.GetId(),
		ClientReqId:      riskBankAction.GetClientReqId(),
		ActorId:          riskBankAction.GetActorId(),
		AccountId:        riskBankAction.GetAccountId(),
		AccountType:      riskBankAction.GetAccountType(),
		Vendor:           riskBankAction.GetVendor(),
		State:            riskBankAction.GetState(),
		Action:           riskBankAction.GetAction(),
		RequestReason:    riskBankAction.GetRequestReason(),
		FailureReason:    riskBankAction.GetFailureReason(),
		BankActionReason: riskBankAction.GetBankActionReason(),
		Provenance:       riskBankAction.GetProvenance(),
		CommsTemplate:    riskBankAction.GetCommsTemplate(),
		IsRecon:          nulltypes.NewNullBoolEnum(riskBankAction.GetIsRecon()),
		LienRequestId:    riskBankAction.GetLienRequestId(),
	}

	if riskBankAction.GetBankActionDate() != nil {
		mdl.BankActionDate = nulltypes.NewNullTime(riskBankAction.GetBankActionDate().AsTime())
	}
	return mdl
}

func (a *RiskBankActions) ToProto() *risk.RiskBankActions {
	if a == nil {
		return nil
	}
	proto := &risk.RiskBankActions{
		Id:               a.Id,
		ClientReqId:      a.ClientReqId,
		ActorId:          a.ActorId,
		AccountId:        a.AccountId,
		AccountType:      a.AccountType,
		Vendor:           a.Vendor,
		State:            a.State,
		Action:           a.Action,
		RequestReason:    a.RequestReason,
		FailureReason:    a.FailureReason,
		BankActionReason: a.BankActionReason,
		Provenance:       a.Provenance,
		CommsTemplate:    a.CommsTemplate,
		IsRecon:          a.IsRecon.GetValue(),
		CreatedAt:        timestamppb.New(a.CreatedAt),
		UpdatedAt:        timestamppb.New(a.UpdatedAt),
		LienRequestId:    a.LienRequestId,
	}

	if a.BankActionDate.Valid {
		proto.BankActionDate = timestamppb.New(a.BankActionDate.Time)
	}

	return proto
}

// NewRiskBankActionsModels - Converts the given RiskBankActions array proto to RiskBankActions array model
func NewRiskBankActionsModels(actionData []*risk.RiskBankActions) []*RiskBankActions {
	var riskBankActionsModels []*RiskBankActions
	for _, riskBankActionsProto := range actionData {
		riskBankActionsModels = append(riskBankActionsModels, NewRiskBankActionsModel(riskBankActionsProto))
	}
	return riskBankActionsModels
}

func (RiskBankActions) TableName() string {
	return "risk_bank_actions"
}
