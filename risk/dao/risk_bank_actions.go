package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/enums"
	"github.com/epifi/gamma/risk/dao/model"

	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type RiskBankActionsCRDB struct {
	db *gorm.DB
}

func NewRiskBankActionsDao(db types.EpifiCRDB) *RiskBankActionsCRDB {
	return &RiskBankActionsCRDB{
		db: db,
	}
}

var _ RiskBankActionsDao = &RiskBankActionsCRDB{}

var (
	// RiskBankActionSelectColumnNameMap maps RiskBankActions field mask to column name
	RiskBankActionSelectColumnNameMap = map[risk.RiskBankActionsFieldMask]string{
		risk.RiskBankActionsFieldMask_ID:                 "id",
		risk.RiskBankActionsFieldMask_CLIENT_REQ_ID:      "client_req_id",
		risk.RiskBankActionsFieldMask_ACTOR_ID:           "actor_id",
		risk.RiskBankActionsFieldMask_ACCOUNT_ID:         "account_id",
		risk.RiskBankActionsFieldMask_ACCOUNT_TYPE:       "account_type",
		risk.RiskBankActionsFieldMask_VENDOR:             "vendor",
		risk.RiskBankActionsFieldMask_ACTION:             "action",
		risk.RiskBankActionsFieldMask_STATE:              "state",
		risk.RiskBankActionsFieldMask_REQUEST_REASON:     "request_reason",
		risk.RiskBankActionsFieldMask_FAILURE_REASON:     "failure_reason",
		risk.RiskBankActionsFieldMask_BANK_ACTION_REASON: "bank_action_reason",
		risk.RiskBankActionsFieldMask_PROVENANCE:         "provenance",
		risk.RiskBankActionsFieldMask_COMMS_TEMPLATE:     "comms_template",
		risk.RiskBankActionsFieldMask_IS_RECON:           "is_recon",
		risk.RiskBankActionsFieldMask_BANK_ACTION_DATE:   "bank_action_date",
		risk.RiskBankActionsFieldMask_CREATED_AT:         "created_at",
		risk.RiskBankActionsFieldMask_UPDATED_AT:         "updated_at",
		risk.RiskBankActionsFieldMask_DELETED_AT:         "deleted_at",
	}

	// RiskBankActionColumnUpdateNameMap maps RiskBankActions field mask to column name for updates
	RiskBankActionColumnUpdateNameMap = map[risk.RiskBankActionsFieldMask]string{
		risk.RiskBankActionsFieldMask_STATE:              "state",
		risk.RiskBankActionsFieldMask_REQUEST_REASON:     "request_reason",
		risk.RiskBankActionsFieldMask_FAILURE_REASON:     "failure_reason",
		risk.RiskBankActionsFieldMask_BANK_ACTION_REASON: "bank_action_reason",
		risk.RiskBankActionsFieldMask_PROVENANCE:         "provenance",
		risk.RiskBankActionsFieldMask_COMMS_TEMPLATE:     "comms_template",
		risk.RiskBankActionsFieldMask_BANK_ACTION_DATE:   "bank_action_date",
		risk.RiskBankActionsFieldMask_CREATED_AT:         "created_at",
		risk.RiskBankActionsFieldMask_UPDATED_AT:         "updated_at",
		risk.RiskBankActionsFieldMask_DELETED_AT:         "deleted_at",
		risk.RiskBankActionsFieldMask_LIEN_REQUEST_ID:    "lien_request_id",
	}
)

func (r RiskBankActionsCRDB) Create(ctx context.Context, riskBankActions *risk.RiskBankActions) (*risk.RiskBankActions, error) {
	defer metric_util.TrackDuration("risk/dao", "RiskBankActionsCRDB", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	riskBankActionModel := model.NewRiskBankActionsModel(riskBankActions)
	if err := db.Create(riskBankActionModel).Error; err != nil {
		return nil, fmt.Errorf("error creating Risk Bank Actions entity: %w", err)
	}
	return riskBankActionModel.ToProto(), nil
}

func (r RiskBankActionsCRDB) BatchCreate(ctx context.Context, riskBankActions []*risk.RiskBankActions) ([]*risk.RiskBankActions, error) {
	defer metric_util.TrackDuration("risk/dao", "RiskBankActionsCRDB", "BatchCreate", time.Now())
	if len(riskBankActions) == 0 {
		return nil, fmt.Errorf("riskBankActions list can't be empty %w", epifierrors.ErrInvalidArgument)
	}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	riskBankActionModels := model.NewRiskBankActionsModels(riskBankActions)
	if err := db.Create(riskBankActionModels).Error; err != nil {
		return nil, fmt.Errorf("error creating batch Risk Bank Actions entity : %w", err)
	}
	return convertToRiskBankActionsProtoList(riskBankActionModels), nil
}

func (r RiskBankActionsCRDB) GetByClientReqId(ctx context.Context, clientReqId string, selectMask []risk.RiskBankActionsFieldMask) (*risk.RiskBankActions, error) {
	defer metric_util.TrackDuration("risk/dao", "RiskBankActionsCRDB", "GetByClientReqId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if clientReqId == "" {
		return nil, fmt.Errorf("clientReqId cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}
	query := db.Where("client_req_id = ?", clientReqId)

	if len(selectMask) != 0 {
		selectColumns := getSelectColumnsForRiskBankActions(selectMask)
		query = query.Select(selectColumns)
	}
	riskBankActionsModel := &model.RiskBankActions{}

	if err := query.First(&riskBankActionsModel).Error; err != nil {
		return nil, fmt.Errorf("failed to get risk bank action data by clientReqId %v: %w", clientReqId,
			err)
	}
	return riskBankActionsModel.ToProto(), nil
}

func (r RiskBankActionsCRDB) GetByClientReqIds(ctx context.Context, clientReqId []string, selectMask []risk.RiskBankActionsFieldMask) ([]*risk.RiskBankActions, error) {
	defer metric_util.TrackDuration("risk/dao", "RiskBankActionsCRDB", "GetByClientReqIds", time.Now())
	if len(clientReqId) == 0 {
		return nil, fmt.Errorf("clientReqId list cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	query := db.Where("client_req_id IN (?)", clientReqId)

	if len(selectMask) != 0 {
		selectColumns := getSelectColumnsForRiskBankActions(selectMask)
		query = query.Select(selectColumns)
	}
	var riskBankActionsModels []*model.RiskBankActions

	if err := query.Find(&riskBankActionsModels).Error; err != nil {
		return nil, fmt.Errorf("failed to get risk bank action data by clientReqId %v: %w", clientReqId,
			err)
	}
	return convertToRiskBankActionsProtoList(riskBankActionsModels), nil
}

func (r RiskBankActionsCRDB) Update(ctx context.Context, riskBankActions *risk.RiskBankActions, updateMask []risk.RiskBankActionsFieldMask) error {
	defer metric_util.TrackDuration("risk/dao", "RiskBankActionsCRDB", "Update", time.Now())

	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	if riskBankActions.ClientReqId == "" {
		return fmt.Errorf("ClientReqId cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}

	riskBankActionModels := model.NewRiskBankActionsModel(riskBankActions)
	updateColumns, err := getUpdateColumnsForRiskBankActions(updateMask)
	if err != nil {
		return err
	}
	res := db.Model(riskBankActionModels).Select(updateColumns).Updates(riskBankActionModels)
	if res.Error != nil {
		return fmt.Errorf("unable to update RiskBankActions request : %s : %w", riskBankActions.GetId(), res.Error)
	}

	if res.RowsAffected == 0 {
		return epifierrors.ErrRowNotUpdated
	}

	return nil
}

func (r RiskBankActionsCRDB) BatchUpdateState(ctx context.Context, clientReqIds []string, state enums.State) error {
	defer metric_util.TrackDuration("risk/dao", "RiskBankActionsCRDB", "BatchUpdateState", time.Now())
	if len(clientReqIds) == 0 {
		return fmt.Errorf("riskBankActions list can't be empty %w", epifierrors.ErrInvalidArgument)
	}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	entity := &model.RiskBankActions{}
	err := db.Model(entity).Where("client_req_id in (?)", clientReqIds).Update("state", state).Error
	if err != nil {
		return fmt.Errorf("unable to Batch Update State request : %w", err)
	}
	return nil
}

//nolint:dupl
func (r RiskBankActionsCRDB) GetByAction(ctx context.Context, action enums.Action, selectMask []risk.RiskBankActionsFieldMask, options ...storagev2.FilterOption) ([]*risk.RiskBankActions, error) {
	defer metric_util.TrackDuration("risk/dao", "RiskBankActionsCRDB", "GetByAction", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if action == enums.Action_ACTION_UNSPECIFIED {
		return nil, fmt.Errorf("action cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}
	for _, opt := range options {
		db = opt.ApplyInGorm(db)
	}
	if len(selectMask) != 0 {
		selectColumns := getSelectColumnsForRiskBankActions(selectMask)
		db = db.Select(selectColumns)
	}
	var riskBankActionsModelList []*model.RiskBankActions
	if err := db.Find(&riskBankActionsModelList, " action = ?", action).Error; err != nil {
		return nil, fmt.Errorf("error fetching users by action: %s %w", action, err)
	}
	if len(riskBankActionsModelList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return convertToRiskBankActionsProtoList(riskBankActionsModelList), nil
}

// nolint: dupl
func (r RiskBankActionsCRDB) GetByState(ctx context.Context, state enums.State, selectMask []risk.RiskBankActionsFieldMask, options ...storagev2.FilterOption) ([]*risk.RiskBankActions, error) {
	defer metric_util.TrackDuration("risk/dao", "RiskBankActionsCRDB", "GetByState", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if state == enums.State_STATE_UNSPECIFIED {
		return nil, fmt.Errorf("state cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}
	for _, opt := range options {
		db = opt.ApplyInGorm(db)
	}
	if len(selectMask) != 0 {
		selectColumns := getSelectColumnsForRiskBankActions(selectMask)
		db = db.Select(selectColumns)
	}
	var riskBankActionsModelList []*model.RiskBankActions
	if err := db.Find(&riskBankActionsModelList, " state = ?", state).Error; err != nil {
		return nil, fmt.Errorf("error fetching users by state: %s %w", state, err)
	}
	if len(riskBankActionsModelList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return convertToRiskBankActionsProtoList(riskBankActionsModelList), nil
}

func (r *RiskBankActionsCRDB) GetByActor(ctx context.Context, actorId string, limit int, options ...storagev2.FilterOption) ([]*risk.RiskBankActions, error) {
	defer metric_util.TrackDuration("risk/dao", "RiskBankActionsCRDB", "GetByActor", time.Now())
	if actorId == "" {
		return nil, epifierrors.ErrInvalidArgument
	}
	db := gormctxv2.FromContextOrDefault(ctx, r.db)

	// apply filter options
	for _, opt := range options {
		db = opt.ApplyInGorm(db)
	}

	if limit == 0 {
		limit = -1
	}

	var rbaList []*model.RiskBankActions
	if err := db.Model(&model.RiskBankActions{}).Where(&model.RiskBankActions{ActorId: actorId}).
		Where("deleted_at IS NULL").Order("updated_at desc").Limit(limit).Find(&rbaList).Error; err != nil {
		logger.Error(ctx, "error while fetching risk bank actions by actor id", zap.Error(err))
		return nil, err
	}
	if len(rbaList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return convertToRiskBankActionsProtoList(rbaList), nil
}

// convertToRiskBankActionsProtoList - Converts the list of RiskBankAction model to list of RiskBankAction proto
func convertToRiskBankActionsProtoList(riskBankActionsModelList []*model.RiskBankActions) []*risk.RiskBankActions {
	var riskBankActionsList []*risk.RiskBankActions
	for _, riskBankActionsModel := range riskBankActionsModelList {
		riskBankActionsList = append(riskBankActionsList, riskBankActionsModel.ToProto())
	}
	return riskBankActionsList
}

// getUpdateColumnsForRiskBankActions converts update mask to string slice with column name corresponding to field name enums
func getUpdateColumnsForRiskBankActions(updateMasks []risk.RiskBankActionsFieldMask) ([]string, error) {
	var updateColumns []string

	if len(updateMasks) == 0 {
		return nil, fmt.Errorf("updateMasks cannot be empty: %w", epifierrors.ErrInvalidArgument)
	}

	for _, field := range updateMasks {
		if column, ok := RiskBankActionColumnUpdateNameMap[field]; ok {
			updateColumns = append(updateColumns, column)
		}
	}
	return funk.UniqString(updateColumns), nil
}

// getSelectColumnsForRiskBankActions converts update mask to string slice with column name corresponding to field name enums
func getSelectColumnsForRiskBankActions(fieldMasks []risk.RiskBankActionsFieldMask) []string {
	var selectColumns []string

	if len(fieldMasks) == 0 || isAllFieldsRequired(fieldMasks) {
		selectColumns = getAllSelectColumnsForRiskBankActions()
	} else {
		for _, field := range fieldMasks {
			if column, ok := RiskBankActionSelectColumnNameMap[field]; ok {
				selectColumns = append(selectColumns, column)
			}
		}
	}
	return funk.UniqString(selectColumns)
}

// isAllFieldsRequired will check if all columns of risk bank action table is required
func isAllFieldsRequired(fieldMasks []risk.RiskBankActionsFieldMask) bool {
	for _, fieldMask := range fieldMasks {
		if fieldMask == risk.RiskBankActionsFieldMask_ALL {
			return true
		}
	}
	return false
}

// getAllSelectColumnsForRiskBankActions converts field mask to string slice with column name corresponding to field name enums for all columns
func getAllSelectColumnsForRiskBankActions() []string {
	var selectColumns []string
	for _, columnName := range RiskBankActionSelectColumnNameMap {
		selectColumns = append(selectColumns, columnName)
	}
	return selectColumns
}
