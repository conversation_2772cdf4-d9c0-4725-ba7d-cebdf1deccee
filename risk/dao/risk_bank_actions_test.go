//nolint:dupl
package dao

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"

	"github.com/epifi/gamma/api/accounts"
	riskPb "github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	"github.com/golang/protobuf/proto"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
)

var (
	rbats RiskBankActionsTestSuite
	rba1  = &riskPb.RiskBankActions{
		ClientReqId: "client-req-1-2-3",
		ActorId:     "actor-1-2-1",
		AccountId:   "ac-number",
		AccountType: accounts.Type_SAVINGS,
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
		Action:      enums.Action_ACTION_FULL_FREEZE,
		State:       enums.State_STATE_INITIATED,
		RequestReason: &riskPb.RequestReason{
			Reason:  enums.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
			Remarks: "",
		},
		FailureReason:    0,
		BankActionReason: nil,
		Provenance:       enums.Provenance_PROVENANCE_BANK,
		CommsTemplate:    []enums.CommsTemplate{enums.CommsTemplate_COMMS_TEMPLATE_EMAIL_CF},
		IsRecon:          commontypes.BooleanEnum_FALSE,
		BankActionDate:   &timestamppb.Timestamp{Seconds: int64(**********)},
		DeletedAt:        nil,
	}
	rba2 = &riskPb.RiskBankActions{
		ClientReqId: "client-req-1-2-2",
		ActorId:     "actor-1-2-1",
		AccountId:   "ac-number",
		AccountType: accounts.Type_SAVINGS,
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
		Action:      enums.Action_ACTION_FULL_FREEZE,
		State:       enums.State_STATE_INITIATED,
		RequestReason: &riskPb.RequestReason{
			Reason:  enums.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
			Remarks: "",
		},
		FailureReason:    0,
		BankActionReason: nil,
		Provenance:       enums.Provenance_PROVENANCE_BANK,
		CommsTemplate:    []enums.CommsTemplate{enums.CommsTemplate_COMMS_TEMPLATE_EMAIL_CF},
		IsRecon:          commontypes.BooleanEnum_FALSE,
		BankActionDate:   &timestamppb.Timestamp{Seconds: int64(**********)},
		DeletedAt:        nil,
	}
	rba3 = &riskPb.RiskBankActions{
		ClientReqId: "client-req-1-2-1",
		ActorId:     "actor-1-2-1",
		AccountId:   "ac-number",
		AccountType: accounts.Type_SAVINGS,
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
		Action:      enums.Action_ACTION_UNFREEZE,
		State:       enums.State_STATE_SENT_TO_BANK,
		RequestReason: &riskPb.RequestReason{
			Reason:  enums.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
			Remarks: "",
		},
		FailureReason:    0,
		BankActionReason: nil,
		Provenance:       enums.Provenance_PROVENANCE_BANK,
		CommsTemplate:    []enums.CommsTemplate{enums.CommsTemplate_COMMS_TEMPLATE_EMAIL_CF},
		IsRecon:          commontypes.BooleanEnum_FALSE,
		BankActionDate:   &timestamppb.Timestamp{Seconds: int64(**********)},
		DeletedAt:        nil,
	}
	rba4 = &riskPb.RiskBankActions{
		Id:          "a5004f18-5d52-4991-82a9-2a1e5010e991",
		ClientReqId: "a5004f18-5d52-4991-82a9-xa1e5010e993",
		ActorId:     "actor-id-1",
		AccountId:   "FI12345",
		AccountType: accounts.Type_SAVINGS,
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
		Action:      enums.Action_ACTION_FULL_FREEZE,
		State:       enums.State_STATE_SUCCESS,
		RequestReason: &riskPb.RequestReason{
			Reason:  enums.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
			Remarks: "TEST",
		},
		FailureReason: enums.FailureReason_FAILURE_REASON_UNSPECIFIED,
		BankActionReason: &riskPb.BankActionReason{
			Status: "STATUS_TEST",
			Reason: "REASON_TEST",
		},
		Provenance:     enums.Provenance_PROVENANCE_FI,
		CommsTemplate:  []enums.CommsTemplate{enums.CommsTemplate_COMMS_TEMPLATE_EMAIL_CF},
		IsRecon:        commontypes.BooleanEnum_FALSE,
		BankActionDate: nil,
	}

	rba5 = &riskPb.RiskBankActions{
		Id:          "a5004f28-5d52-4991-82a9-2a1e5010e993",
		ClientReqId: "a5004f18-5d52-4991-82a9-xa1e5010e995",
		ActorId:     "actor-id-2",
		AccountId:   "FI123456",
		AccountType: accounts.Type_SAVINGS,
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
		Action:      enums.Action_ACTION_FULL_FREEZE,
		State:       enums.State_STATE_SENT_TO_BANK,
		RequestReason: &riskPb.RequestReason{
			Reason:  enums.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
			Remarks: "",
		},
		FailureReason: enums.FailureReason_FAILURE_REASON_UNSPECIFIED,
		BankActionReason: &riskPb.BankActionReason{
			Status: "STATUS_TEST",
			Reason: "REASON_TEST",
		},
		Provenance:     enums.Provenance_PROVENANCE_FI,
		CommsTemplate:  []enums.CommsTemplate{enums.CommsTemplate_COMMS_TEMPLATE_EMAIL_CF},
		IsRecon:        commontypes.BooleanEnum_FALSE,
		BankActionDate: &timestamppb.Timestamp{Seconds: int64(**********)},
	}
	rba6 = &riskPb.RiskBankActions{
		Id:          "a5004f28-5d52-4991-82a9-2a1e5010e992",
		ClientReqId: "a5004f18-5d52-4991-82a9-xa1e5010e994",
		ActorId:     "actor-id-1",
		AccountId:   "FI12345",
		AccountType: accounts.Type_SAVINGS,
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
		Action:      enums.Action_ACTION_UNFREEZE,
		State:       enums.State_STATE_INITIATED,
		RequestReason: &riskPb.RequestReason{
			Reason:  enums.RequestReason_REQUEST_REASON_OTHER,
			Remarks: "unfreeze for same",
		},
		FailureReason: enums.FailureReason_FAILURE_REASON_DROP_DEDUPE_ALREADY_SENT,
		BankActionReason: &riskPb.BankActionReason{
			Status: "STATUS_TEST",
			Reason: "REASON_TEST",
		},
		Provenance:     enums.Provenance_PROVENANCE_FI,
		CommsTemplate:  []enums.CommsTemplate{enums.CommsTemplate_COMMS_TEMPLATE_EMAIL_CF},
		IsRecon:        commontypes.BooleanEnum_TRUE,
		BankActionDate: nil,
	}
	rba7 = &riskPb.RiskBankActions{
		Id:          "a5004f18-5d52-4991-82a9-2a1e5010e910",
		ClientReqId: "a5004f18-5d52-4991-82a9-xa1e5010e910",
		ActorId:     "actor-id-3",
		AccountId:   "FI12345",
		AccountType: accounts.Type_SAVINGS,
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
		Action:      enums.Action_ACTION_FULL_FREEZE,
		State:       enums.State_STATE_SUCCESS,
		RequestReason: &riskPb.RequestReason{
			Reason:  enums.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
			Remarks: "TEST",
		},
		BankActionReason: &riskPb.BankActionReason{
			Status: "STATUS_TEST",
			Reason: "REASON_TEST",
		},
		Provenance:     enums.Provenance_PROVENANCE_FI,
		CommsTemplate:  []enums.CommsTemplate{enums.CommsTemplate_COMMS_TEMPLATE_EMAIL_CF},
		IsRecon:        commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
		BankActionDate: &timestamppb.Timestamp{Seconds: int64(**********)},
	}
	rba8 = &riskPb.RiskBankActions{
		Id:          "a5004f18-5d52-4991-82a9-2a1e5010e913",
		ClientReqId: "a5004f18-5d52-4991-82a9-xa1e5010e914",
		ActorId:     "actor-id-3",
		AccountId:   "FI12345",
		AccountType: accounts.Type_SAVINGS,
		Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
		Action:      enums.Action_ACTION_FULL_FREEZE,
		State:       enums.State_STATE_SUCCESS,
		RequestReason: &riskPb.RequestReason{
			Reason:  enums.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
			Remarks: "TEST",
		},
		BankActionReason: &riskPb.BankActionReason{
			Status: "STATUS_TEST",
			Reason: "REASON_TEST",
		},
		Provenance:     enums.Provenance_PROVENANCE_FI,
		CommsTemplate:  []enums.CommsTemplate{enums.CommsTemplate_COMMS_TEMPLATE_EMAIL_CF},
		IsRecon:        commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
		BankActionDate: &timestamppb.Timestamp{Seconds: int64(**********)},
	}
)

func TestRiskBankActionsCRDB_Create(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, rbats.conf.EpifiDb.GetName(), rbats.db, AffectedTestTables)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx             context.Context
		riskBankActions *riskPb.RiskBankActions
		selectMask      []riskPb.RiskBankActionsFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "create risk bank action successfully",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:             context.Background(),
				riskBankActions: rba1,
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			wantErr: false,
		},
		{
			name: "create risk bank action successfully WITH null recon",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:             context.Background(),
				riskBankActions: rba8,
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create risk bank action",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:             context.Background(),
				riskBankActions: &riskPb.RiskBankActions{},
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := RiskBankActionsCRDB{
				db: tt.fields.db,
			}
			_, err := w.Create(tt.args.ctx, tt.args.riskBankActions)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// confirming if the creation was successful
			got, _ := w.GetByClientReqId(tt.args.ctx, tt.args.riskBankActions.GetClientReqId(), tt.args.selectMask)
			if !tt.wantErr && !assertRiskBankActionCreation(got, tt.args.riskBankActions) {
				t.Errorf("Create() got = %v, want %v", got, tt.args.riskBankActions)
			}
		})
	}
}

func TestRiskBankActionsCRDB_BatchCreate(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, rbats.conf.EpifiDb.GetName(), rbats.db, AffectedTestTables)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx             context.Context
		riskBankActions []*riskPb.RiskBankActions
		want            []*riskPb.RiskBankActions
		selectMask      []riskPb.RiskBankActionsFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "create risk bank action batch successfully",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx: context.Background(),
				riskBankActions: []*riskPb.RiskBankActions{
					rba2, rba3,
				},
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
				want: []*riskPb.RiskBankActions{
					rba2, rba3,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to create risk bank action batch",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:             context.Background(),
				riskBankActions: []*riskPb.RiskBankActions{},
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RiskBankActionsCRDB{
				db: tt.fields.db,
			}
			res, err := r.BatchCreate(tt.args.ctx, tt.args.riskBankActions)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchCreate() gotErr: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			for i := range tt.args.want {
				got, _ := r.GetByClientReqId(context.Background(), res[i].GetClientReqId(), tt.args.selectMask)
				tt.args.want[i].Id = got.GetId()
				if !assertRiskBankActionList(got, tt.args.want) {
					t.Errorf("BatchCreate() got = %v, want %v", got, tt.args.want)
				}
			}
		})
	}
}

//nolint:dupl
func TestRiskBankActionsCRDB_GetByAction(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, rbats.conf.EpifiDb.GetName(), rbats.db, AffectedTestTables)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx        context.Context
		selectMask []riskPb.RiskBankActionsFieldMask
		action     enums.Action
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*riskPb.RiskBankActions
		wantErr bool
	}{
		{
			name: "got action users successfully using action type",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:    context.Background(),
				action: enums.Action_ACTION_UNFREEZE,
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			want: []*riskPb.RiskBankActions{
				rba6,
			},
			wantErr: false,
		}, // pending for options field
		{
			name: "failed to get action users using action type",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:    context.Background(),
				action: enums.Action_ACTION_UNSPECIFIED,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RiskBankActionsCRDB{
				db: tt.fields.db,
			}
			got, err := r.GetByAction(tt.args.ctx, tt.args.action, tt.args.selectMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByAction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i := range tt.want {
				if !assertRiskBankAction(got[i], tt.want[i]) {
					t.Errorf("GetByAction() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

//nolint:dupl
func TestRiskBankActionsCRDB_GetByState(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, rbats.conf.EpifiDb.GetName(), rbats.db, AffectedTestTables)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx        context.Context
		selectMask []riskPb.RiskBankActionsFieldMask
		state      enums.State
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*riskPb.RiskBankActions
		wantErr bool
	}{
		{
			name: "got action users successfully using state type",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:   context.Background(),
				state: enums.State_STATE_INITIATED,
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			want: []*riskPb.RiskBankActions{
				rba6,
			},
			wantErr: false,
		},
		{
			name: "failed to get action users using state type",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:   context.Background(),
				state: enums.State_STATE_UNSPECIFIED,
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RiskBankActionsCRDB{
				db: tt.fields.db,
			}
			got, err := r.GetByState(tt.args.ctx, tt.args.state, tt.args.selectMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByState() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i := range tt.want {
				if !assertRiskBankAction(got[i], tt.want[i]) {
					t.Errorf("GetByState() got = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func TestRiskBankActionsCRDB_GetByActor(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, rbats.conf.EpifiDb.GetName(), rbats.db, AffectedTestTables)
	type args struct {
		ctx           context.Context
		actorId       string
		filterOptions []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		want    []*riskPb.RiskBankActions
		wantErr error
	}{
		{
			name: "successful fetch",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-2",
			},
			want:    []*riskPb.RiskBankActions{rba5},
			wantErr: nil,
		},
		{
			name: "successful multi-fetch",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
			},
			want:    []*riskPb.RiskBankActions{rba6, rba4},
			wantErr: nil,
		},
		{
			name: "successful state field mask",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
				filterOptions: []storagev2.FilterOption{
					WithStateFilter([]enums.State{
						enums.State_STATE_INITIATED,
					}),
				},
			},
			want:    []*riskPb.RiskBankActions{rba6},
			wantErr: nil,
		},
		{
			name: "invalid argument",
			args: args{
				ctx:     context.Background(),
				actorId: "",
			},
			want:    nil,
			wantErr: epifierrors.ErrInvalidArgument,
		},
		{
			name: "does not exist",
			args: args{
				ctx:     context.Background(),
				actorId: "asdfa",
			},
			want:    nil,
			wantErr: epifierrors.ErrRecordNotFound,
		},
		{
			name: "successful action filter",
			args: args{
				ctx:     context.Background(),
				actorId: "actor-id-1",
				filterOptions: []storagev2.FilterOption{
					WithActionFilter([]enums.Action{
						enums.Action_ACTION_FULL_FREEZE,
					}),
				},
			},
			want:    []*riskPb.RiskBankActions{rba4},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rbats.riskBankActionsDao.GetByActor(tt.args.ctx, tt.args.actorId, 0, tt.args.filterOptions...)
			assert.Equal(t, tt.wantErr, err)
			assert.Equal(t, len(tt.want), len(got))
			for i := range got {
				opts := []cmp.Option{
					protocmp.Transform(),
					protocmp.IgnoreFields(&riskPb.RiskBankActions{}, "created_at", "updated_at", "deleted_at"),
				}
				if diff := cmp.Diff(got[i], tt.want[i], opts...); diff != "" {
					t.Errorf("risk.RiskBankActions value has mismatch (-got +want):%s\n", diff)
				}
			}
		})
	}
}

func TestRiskBankActionsCRDB_Update(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, rbats.conf.EpifiDb.GetName(), rbats.db, AffectedTestTables)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx             context.Context
		riskBankActions *riskPb.RiskBankActions
		updateMask      []riskPb.RiskBankActionsFieldMask
		selectMask      []riskPb.RiskBankActionsFieldMask
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successfully updated risk bank action",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:             context.Background(),
				riskBankActions: editRiskObjectState(rba4, enums.State_STATE_CANCELED),
				updateMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_STATE,
				},
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to update risk bank action",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx: context.Background(),
				riskBankActions: &riskPb.RiskBankActions{
					ClientReqId: "random-client-req-id",
				},
				updateMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ACTOR_ID,
					riskPb.RiskBankActionsFieldMask_UPDATED_AT,
				},
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			wantErr: true,
		},
		{
			name: "update bank_action_date",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:             context.Background(),
				riskBankActions: editRiskObjectBankActionDate(rba7, &timestamppb.Timestamp{Seconds: int64(**********)}),
				updateMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_BANK_ACTION_DATE,
				},
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			wantErr: false,
		},
		{
			name: "fail : update field mask is empty",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:             context.Background(),
				riskBankActions: editRiskObjectState(rba4, enums.State_STATE_CANCELED),
				selectMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_ALL,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := RiskBankActionsCRDB{
				db: tt.fields.db,
			}
			if err := w.Update(tt.args.ctx, tt.args.riskBankActions, tt.args.updateMask); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}

			// confirming if the update was successful
			got, _ := w.GetByClientReqId(tt.args.ctx, tt.args.riskBankActions.GetClientReqId(), []riskPb.RiskBankActionsFieldMask{
				riskPb.RiskBankActionsFieldMask_ALL,
			})
			if !tt.wantErr && !assertRiskBankAction(got, tt.args.riskBankActions) {
				t.Errorf("GetByClientReqId() got = %v, want %v", got, tt.args.riskBankActions)
			}
		})
	}
}

func TestRiskBankActionsCRDB_GetByClientReqIds(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, rbats.conf.EpifiDb.GetName(), rbats.db, AffectedTestTables)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx    context.Context
		idList []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*riskPb.RiskBankActions
		wantErr bool
	}{
		{
			name: "success - multiple correct ids",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx: context.Background(),
				idList: []string{
					rba5.ClientReqId,
				},
			},
			want: []*riskPb.RiskBankActions{
				rba5,
			},
			wantErr: false,
		},
		{
			name: "success - multiple correct ids with duplicates",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx: context.Background(),
				idList: []string{
					rba6.ClientReqId,
					rba6.ClientReqId,
				},
			},
			want: []*riskPb.RiskBankActions{
				rba6,
			},
			wantErr: false,
		},
		{
			name: "success - 1 correct and 1 incorrect account_id",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx: context.Background(),
				// 2nd id is a random uuid
				idList: []string{
					rba6.ClientReqId,
					"random-gen",
				},
			},
			want: []*riskPb.RiskBankActions{
				rba6,
			},
			wantErr: false,
		},
		{
			name: "fail - missing id list",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			wfr := RiskBankActionsCRDB{
				db: tt.fields.db,
			}
			got, err := wfr.GetByClientReqIds(tt.args.ctx, tt.args.idList, []riskPb.RiskBankActionsFieldMask{
				riskPb.RiskBankActionsFieldMask_ALL,
			})
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientReqIds() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			for i := range tt.want {
				if !assertRiskBankActionCreation(tt.want[i], got[i]) {
					t.Errorf("BatchCreate() got: %v, want: %v", got, tt.want)
					return
				}
			}
		})
	}
}

func TestRiskBankActionsCRDB_GetByClientReqId(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, rbats.conf.EpifiDb.GetName(), rbats.db, AffectedTestTables)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx         context.Context
		ClientReqId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *riskPb.RiskBankActions
		wantErr bool
	}{
		{
			name: "success - getting by id",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:         context.Background(),
				ClientReqId: rba6.ClientReqId,
			},
			want:    rba6,
			wantErr: false,
		},
		{
			name: "failed - random id's",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx:         context.Background(),
				ClientReqId: "random-non-uuid",
			},
			wantErr: true,
		},
		{
			name: "failure - getting by id",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			wfr := RiskBankActionsCRDB{
				db: tt.fields.db,
			}
			got, err := wfr.GetByClientReqId(tt.args.ctx, tt.args.ClientReqId, []riskPb.RiskBankActionsFieldMask{
				riskPb.RiskBankActionsFieldMask_ALL,
			})
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientReqId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !assertRiskBankAction(tt.want, got) {
				t.Errorf("GetByClientReqId() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRiskBankActionsCRDB_BatchUpdateState(t *testing.T) {
	pkgTest.PrepareScopedDatabase(t, rbats.conf.EpifiDb.GetName(), rbats.db, AffectedTestTables)
	type fields struct {
		db *gorm.DB
	}
	type args struct {
		ctx          context.Context
		ClientReqIds []string
		state        enums.State
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "successfully updated risk bank action state",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx: context.Background(),
				ClientReqIds: []string{
					rba4.ClientReqId,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to update risk bank action",
			fields: fields{
				db: rbats.db,
			},
			args: args{
				ctx: context.Background(),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := RiskBankActionsCRDB{
				db: tt.fields.db,
			}
			err := r.BatchUpdateState(tt.args.ctx, tt.args.ClientReqIds, tt.args.state)
			if (err != nil) != tt.wantErr {
				t.Errorf("BatchUpdateState() gotErr: %v, wantErr: %v", err, tt.wantErr)
				return
			}
			for i := range tt.args.ClientReqIds {
				got, _ := r.GetByClientReqId(context.Background(), tt.args.ClientReqIds[i], []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_STATE,
				})
				if got.State != tt.args.state {
					t.Errorf("BatchUpdateState() got = %v, want %v", got, tt.args.state)
				}
			}
		})
	}
}

func assertRiskBankAction(actual, expected *riskPb.RiskBankActions) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.GetCreatedAt()
		expected.UpdatedAt = actual.GetUpdatedAt()
		expected.DeletedAt = actual.GetDeletedAt()
	}
	return proto.Equal(actual, expected)
}

func assertRiskBankActionList(actual *riskPb.RiskBankActions, expected []*riskPb.RiskBankActions) bool {
	for i := 0; i < len(expected); i++ {
		if actual != nil && expected[i] != nil {
			expected[i].CreatedAt = actual.GetCreatedAt()
			expected[i].UpdatedAt = actual.GetUpdatedAt()
			expected[i].DeletedAt = actual.GetDeletedAt()
		}
		if proto.Equal(expected[i], actual) {
			return true
		}
	}
	return false
}

func assertRiskBankActionCreation(expected, actual *riskPb.RiskBankActions) bool {
	if (expected != nil) != (actual != nil) {
		return false
	}
	if expected == nil {
		return true
	}
	expected.CreatedAt = actual.CreatedAt
	expected.UpdatedAt = actual.UpdatedAt
	expected.DeletedAt = actual.DeletedAt
	expected.Id = actual.Id
	return proto.Equal(expected, actual)
}

func editRiskObjectState(req *riskPb.RiskBankActions, state enums.State) *riskPb.RiskBankActions {
	req.State = state
	return req
}

func editRiskObjectBankActionDate(req *riskPb.RiskBankActions, newDate *timestamppb.Timestamp) *riskPb.RiskBankActions {
	req.BankActionDate = newDate
	return req
}
