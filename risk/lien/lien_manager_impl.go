package lien

// nolint: goimports
import (
	"context"
	"fmt"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/google/wire"
	"go.uber.org/zap"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/risk"

	riskLienPb "github.com/epifi/gamma/api/risk/lien"

	vglienPb "github.com/epifi/gamma/api/vendorgateway/openbanking/lien"

	"github.com/epifi/gamma/risk/dao"
)

var (
	LienManagerWireSet = wire.NewSet(NewLienManagerImpl, wire.Bind(new(LienManager), new(*LienManagerImpl)))
)

// Manager implements LienManager interface using the vendorgateway/openbanking/lien service
type LienManagerImpl struct {
	vgLienClient   vglienPb.LienClient
	lienRequestDao dao.LienRequestDao
	bankActionsDao dao.RiskBankActionsDao
}

// NewManager creates a new instance of the lien manager
func NewLienManagerImpl(vgLienClient vglienPb.LienClient, lienRequestDao dao.LienRequestDao, bankActionsDao dao.RiskBankActionsDao) *LienManagerImpl {
	return &LienManagerImpl{
		vgLienClient:   vgLienClient,
		lienRequestDao: lienRequestDao,
		bankActionsDao: bankActionsDao,
	}
}

// AddLien adds a lien on a savings account
func (m *LienManagerImpl) AddLien(ctx context.Context, req *riskLienPb.AddLienRequest) (*riskLienPb.AddLienResponse, error) {

	// Create the add lien request for vendor gateway
	vgReq := &vglienPb.AddLienRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		LienRequest: &vglienPb.LienRequest{
			RequestId:     req.GetChannelRequestId(),
			AccountNumber: req.GetAccountNumber(),
			Amount: &moneyPb.Money{
				Units:        int64(req.GetAmount()),
				CurrencyCode: req.GetCurrencyCode(),
			},
			Reason:         vglienPb.Reason_REASON_UAT,
			Remarks:        req.GetRemarks(),
			StartTimestamp: req.GetStartDate(),
			EndTimestamp:   req.GetEndDate(),
		},
	}

	// Call the vendorgateway service
	addLienRes, err := m.vgLienClient.AddLien(ctx, vgReq)
	if rpcErr := epifigrpc.RPCError(addLienRes, err); rpcErr != nil {
		logger.Error(ctx, "vgLienClient.AddLien call failed", zap.Error(rpcErr), zap.String(logger.ACCOUNT_NUMBER, req.GetAccountNumber()))
		return nil, fmt.Errorf("vgLienClient.AddLien call failed, err: %w", rpcErr)
	}

	// Store the vendorgateway response in lien_requests table
	lienRequestReq := &riskLienPb.LienRequest{
		ReqType:              riskLienPb.LienRequestType_LIEN_REQUEST_TYPE_ADD,
		LienId:               addLienRes.GetLienResponse().GetLienId(),
		Status:               addLienRes.GetLienResponse().GetStatus(),
		SavingsAccountNumber: req.GetAccountNumber(),
		Amount:               float32(req.GetAmount()),
		CurrencyCode:         req.GetCurrencyCode(),
		ReasonCode:           vgReq.GetLienRequest().GetReason().String(),
		Remarks:              req.GetRemarks(),
		StartDate:            req.GetStartDate(),
		EndDate:              req.GetEndDate(),
		ChannelRequestId:     req.GetChannelRequestId(),
		Channel:              "EPIFI",
		CbsStatus:            addLienRes.GetLienResponse().GetCbsResponse(),
		CbsResponse:          addLienRes.GetLienResponse().GetLienId(),
		ApiMessage:           addLienRes.GetStatus().GetDebugMessage(),
	}

	lienRequest, err := m.lienRequestDao.Create(ctx, lienRequestReq)
	if err != nil {
		logger.Error(ctx, "Failed to store lien request in the database", zap.Error(err), zap.String(logger.ACCOUNT_NUMBER, req.GetAccountNumber()))
	}

	// Update bank action record with lien request ID
	if err := m.bankActionsDao.Update(ctx, &risk.RiskBankActions{
		Id:            req.GetBankActionId(),
		LienRequestId: lienRequest.GetId(),
	}, []risk.RiskBankActionsFieldMask{risk.RiskBankActionsFieldMask_LIEN_REQUEST_ID}); err != nil {
		logger.Error(ctx, "Failed to update bank action with lien request ID",
			zap.Error(err),
			zap.String("bank_action_id", req.GetBankActionId()),
			zap.String("lien_request_id", lienRequest.GetId()))
		// Note: Not returning error here to maintain backward compatibility, but the error is logged
	}

	return &riskLienPb.AddLienResponse{
		Id:          addLienRes.GetLienResponse().GetLienId(),
		CbsStatus:   addLienRes.GetLienResponse().GetCbsStatus(),
		CbsResponse: addLienRes.GetLienResponse().GetLienId(),
		ApiMessage:  addLienRes.GetStatus().GetDebugMessage(),
	}, nil
}

// EnquireLien fetches lien information for a given account
func (m *LienManagerImpl) EnquireLien(ctx context.Context, req *riskLienPb.EnquireLienRequest) (*riskLienPb.EnquireLienResponse, error) {

	// Create the enquire lien request for vendor gateway
	vgReq := &vglienPb.EnquireLienRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		RequestId:     req.GetRequestId(),
		AccountNumber: req.GetAccountNumber(),
	}

	// Call the vendorgateway service
	enquireLienRes, err := m.vgLienClient.EnquireLien(ctx, vgReq)
	if rpcErr := epifigrpc.RPCError(enquireLienRes, err); rpcErr != nil {
		logger.Error(ctx, "vgLienClient.EnquireLien call failed", zap.Error(rpcErr), zap.String(logger.ACCOUNT_NUMBER, req.GetAccountNumber()))
		return nil, fmt.Errorf("vgLienClient.EnquireLien call failed, err: %w", rpcErr)
	}

	// Prepare the lien details
	lienDetails := &riskLienPb.LienDetails{
		Amount:       float64(enquireLienRes.GetLienResponse().GetNewAmount().GetUnits()),
		CurrencyCode: enquireLienRes.GetLienResponse().GetNewAmount().GetCurrencyCode(),
		ReasonCode:   enquireLienRes.GetLienResponse().GetReason().String(),
		Remarks:      enquireLienRes.GetLienResponse().GetRemarks(),
		StartDate:    enquireLienRes.GetLienResponse().GetStartTimestamp(),
		EndDate:      enquireLienRes.GetLienResponse().GetEndTimestamp(),
	}

	// Store the vendorgateway response in lien_requests table
	lienRequest := &riskLienPb.LienRequest{
		ReqType:              riskLienPb.LienRequestType_LIEN_REQUEST_TYPE_ENQUIRE,
		LienId:               enquireLienRes.GetLienResponse().GetLienId(),
		Status:               enquireLienRes.GetLienResponse().GetStatus(),
		SavingsAccountNumber: req.GetAccountNumber(),
		Amount:               float32(enquireLienRes.GetLienResponse().GetNewAmount().GetUnits()),
		CurrencyCode:         enquireLienRes.GetLienResponse().GetNewAmount().GetCurrencyCode(),
		ReasonCode:           enquireLienRes.GetLienResponse().GetReason().String(),
		Remarks:              enquireLienRes.GetLienResponse().GetRemarks(),
		StartDate:            enquireLienRes.GetLienResponse().GetStartTimestamp(),
		EndDate:              enquireLienRes.GetLienResponse().GetEndTimestamp(),
		Channel:              "EPIFI",
		CbsStatus:            enquireLienRes.GetLienResponse().GetCbsStatus(),
		CbsResponse:          enquireLienRes.GetLienResponse().GetLienId(),
		ApiMessage:           enquireLienRes.GetStatus().GetDebugMessage(),
	}

	_, err = m.lienRequestDao.Create(ctx, lienRequest)
	if err != nil {
		logger.Error(ctx, "Failed to store lien request in the database", zap.Error(err), zap.String(logger.ACCOUNT_NUMBER, req.GetAccountNumber()))
	}

	// Return the response
	return &riskLienPb.EnquireLienResponse{
		LienId:      enquireLienRes.GetLienResponse().GetLienId(),
		ApiMessage:  enquireLienRes.GetStatus().GetDebugMessage(),
		LienDetails: lienDetails,
	}, nil
}

func (m *LienManagerImpl) GetComms(ctx context.Context, req *riskLienPb.GetCommsRequest) (*riskLienPb.GetCommsResponse, error) {
	return nil, nil
}
