package lien

// nolint: goimports
import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	riskLienPb "github.com/epifi/gamma/api/risk/lien"
	vglienPb "github.com/epifi/gamma/api/vendorgateway/openbanking/lien"
	mockLien "github.com/epifi/gamma/api/vendorgateway/openbanking/lien/mocks"
	mockDao "github.com/epifi/gamma/risk/dao/mocks"
)

func TestManager_AddLien(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := mockLien.NewMockLienClient(ctrl)
	mockLienRequestDao := mockDao.NewMockLienRequestDao(ctrl)
	mockBankActionsDao := mockDao.NewMockRiskBankActionsDao(ctrl)
	manager := NewLienManagerImpl(mockClient, mockLienRequestDao, mockBankActionsDao)

	ctx := context.Background()
	now := time.Now()
	startTime := timestamppb.New(now)
	endTime := timestamppb.New(now.Add(24 * time.Hour))

	tests := []struct {
		name      string
		req       *riskLienPb.AddLienRequest
		mockSetup func()
		want      *riskLienPb.AddLienResponse
		WantErr   bool
	}{
		{
			name: "successful add lien",
			req: &riskLienPb.AddLienRequest{
				AccountNumber:    "********",
				Amount:           100.50,
				CurrencyCode:     "INR",
				ReasonCode:       "LOAN",
				Remarks:          "Test lien",
				StartDate:        startTime,
				EndDate:          endTime,
				ChannelRequestId: "req123",
				BankActionId:     "bank_action_123",
			},
			mockSetup: func() {
				// Mock response from vendorgateway
				vgResp := &vglienPb.AddLienResponse{
					Status: rpc.StatusOk(),
					LienResponse: &vglienPb.LienResponse{
						ChannelRequestId: "req123",
						AccountNumber:    "********",
						LienId:           "lien123",
						CbsStatus:        "SUCCESS",
					},
				}

				mockClient.EXPECT().
					AddLien(gomock.Any(), gomock.Any()).
					Return(vgResp, nil)

				// Mock lien request DAO
				mockLienRequestDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(&riskLienPb.LienRequest{Id: "lien_req_123"}, nil)

				// Mock bank actions DAO - should be called to update with lien request ID
				mockBankActionsDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)
			},
			want: &riskLienPb.AddLienResponse{
				Id:          "lien123",
				CbsStatus:   "SUCCESS",
				CbsResponse: "lien123",
				ApiMessage:  "",
			},
		},
		{
			name: "vg returns error status",
			req: &riskLienPb.AddLienRequest{
				AccountNumber:    "********",
				Amount:           100.50,
				CurrencyCode:     "INR",
				ReasonCode:       "LOAN",
				Remarks:          "Test lien",
				StartDate:        startTime,
				EndDate:          endTime,
				ChannelRequestId: "req123",
				BankActionId:     "bank_action_123",
			},
			mockSetup: func() {
				// Mock error response from vendorgateway
				failureVgResp := &vglienPb.AddLienResponse{
					Status: rpc.StatusInternal(),
				}

				mockClient.EXPECT().
					AddLien(gomock.Any(), gomock.Any()).
					Return(failureVgResp, nil)
			},
			WantErr: true,
		},
		{
			name: "dao error - still returns success",
			req: &riskLienPb.AddLienRequest{
				AccountNumber:    "********",
				Amount:           100.50,
				CurrencyCode:     "INR",
				ReasonCode:       "LOAN",
				Remarks:          "Test lien",
				StartDate:        startTime,
				EndDate:          endTime,
				ChannelRequestId: "req123",
				BankActionId:     "bank_action_123",
			},
			mockSetup: func() {
				// Mock successful response from vendorgateway
				vgResp := &vglienPb.AddLienResponse{
					Status: rpc.StatusOk(),
					LienResponse: &vglienPb.LienResponse{
						ChannelRequestId: "req123",
						AccountNumber:    "********",
						LienId:           "lien123",
						CbsStatus:        "SUCCESS",
					},
				}

				mockClient.EXPECT().
					AddLien(gomock.Any(), gomock.Any()).
					Return(vgResp, nil)

				// Mock DAO error - but still return a valid object so Bank Actions DAO can be called
				mockLienRequestDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(&riskLienPb.LienRequest{Id: "lien_req_123"}, errors.New("db error"))

				// Bank actions DAO should still be called even when lien request has an error (log only)
				mockBankActionsDao.EXPECT().
					Update(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil)
			},
			want: &riskLienPb.AddLienResponse{
				Id:          "lien123",
				CbsStatus:   "SUCCESS",
				CbsResponse: "lien123",
				ApiMessage:  "",
			},
		},
		{
			name: "rpc client error",
			req: &riskLienPb.AddLienRequest{
				AccountNumber:    "********",
				Amount:           100.50,
				CurrencyCode:     "INR",
				ReasonCode:       "LOAN",
				Remarks:          "Test lien",
				StartDate:        startTime,
				EndDate:          endTime,
				ChannelRequestId: "req123",
				BankActionId:     "bank_action_123",
			},
			mockSetup: func() {
				// Mock RPC error
				mockClient.EXPECT().
					AddLien(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("rpc connection error"))
			},
			WantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			tt.mockSetup()
			logger.Init("test")

			// Call the function
			got, err := manager.AddLien(ctx, tt.req)

			if (err != nil) != tt.WantErr {
				t.Errorf("Got error = %v, wantErr %v", err, tt.WantErr)
				return
			}

			// Compare the expected and actual result
			if !proto.Equal(got, tt.want) {
				t.Errorf("Got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}

func TestManager_EnquireLien(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockClient := mockLien.NewMockLienClient(ctrl)
	mockLienRequestDao := mockDao.NewMockLienRequestDao(ctrl)
	mockBankActionsDao := mockDao.NewMockRiskBankActionsDao(ctrl)
	manager := NewLienManagerImpl(mockClient, mockLienRequestDao, mockBankActionsDao)

	ctx := context.Background()
	now := time.Now()
	startTime := timestamppb.New(now)
	endTime := timestamppb.New(now.Add(24 * time.Hour))

	tests := []struct {
		name      string
		req       *riskLienPb.EnquireLienRequest
		mockSetup func()
		want      *riskLienPb.EnquireLienResponse
		WantErr   bool
	}{
		{
			name: "successful enquire lien",
			req: &riskLienPb.EnquireLienRequest{
				AccountNumber: "********",
			},
			mockSetup: func() {
				// Mock response from vendorgateway
				vgResp := &vglienPb.EnquireLienResponse{
					Status: rpc.StatusOk(),
					LienResponse: &vglienPb.LienResponse{
						ChannelRequestId: "req123",
						AccountNumber:    "********",
						LienId:           "lien123",
						Reason:           vglienPb.Reason_REASON_UAT,
						Remarks:          "Test lien",
						StartTimestamp:   startTime,
						EndTimestamp:     endTime,
						NewAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        *********,
						},
					},
				}

				mockClient.EXPECT().
					EnquireLien(gomock.Any(), gomock.Any()).
					Return(vgResp, nil)

				// Mock lien request DAO
				mockLienRequestDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(&riskLienPb.LienRequest{}, nil)
			},
			want: &riskLienPb.EnquireLienResponse{
				LienId:     "lien123",
				ApiMessage: "",
				LienDetails: &riskLienPb.LienDetails{
					Amount:       100,
					CurrencyCode: "INR",
					ReasonCode:   "REASON_UAT",
					Remarks:      "Test lien",
					StartDate:    startTime,
					EndDate:      endTime,
				},
			},
		},
		{
			name: "vg returns error status",
			req: &riskLienPb.EnquireLienRequest{
				AccountNumber: "********",
			},
			mockSetup: func() {
				// Mock error response from vendorgateway
				failureVgResp := &vglienPb.EnquireLienResponse{
					Status: rpc.StatusInternal(),
				}

				mockClient.EXPECT().
					EnquireLien(gomock.Any(), gomock.Any()).
					Return(failureVgResp, nil)
			},
			WantErr: true,
		},
		{
			name: "dao error - still returns success",
			req: &riskLienPb.EnquireLienRequest{
				AccountNumber: "********",
			},
			mockSetup: func() {
				// Mock successful response from vendorgateway
				vgResp := &vglienPb.EnquireLienResponse{
					Status: rpc.StatusOk(),
					LienResponse: &vglienPb.LienResponse{
						ChannelRequestId: "req123",
						AccountNumber:    "********",
						LienId:           "lien123",
						Reason:           vglienPb.Reason_REASON_UAT,
						Remarks:          "Test lien",
						StartTimestamp:   startTime,
						EndTimestamp:     endTime,
						NewAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        *********,
						},
					},
				}

				mockClient.EXPECT().
					EnquireLien(gomock.Any(), gomock.Any()).
					Return(vgResp, nil)

				// Mock DAO error
				mockLienRequestDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("db error"))
			},
			want: &riskLienPb.EnquireLienResponse{
				LienId:     "lien123",
				ApiMessage: "",
				LienDetails: &riskLienPb.LienDetails{
					Amount:       100,
					CurrencyCode: "INR",
					ReasonCode:   "REASON_UAT",
					Remarks:      "Test lien",
					StartDate:    startTime,
					EndDate:      endTime,
				},
			},
		},
		{
			name: "empty account number",
			req: &riskLienPb.EnquireLienRequest{
				AccountNumber: "",
			},
			mockSetup: func() {
				// Mock response for invalid input
				mockClient.EXPECT().
					EnquireLien(gomock.Any(), gomock.Any()).
					Return(&vglienPb.EnquireLienResponse{
						Status: rpc.StatusInvalidArgument(),
					}, nil)
			},
			WantErr: true,
		},
		{
			name: "rpc client error",
			req: &riskLienPb.EnquireLienRequest{
				AccountNumber: "********",
			},
			mockSetup: func() {
				// Mock RPC error
				mockClient.EXPECT().
					EnquireLien(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("rpc connection error"))
			},
			WantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			tt.mockSetup()
			logger.Init("test")

			// Call the function
			got, err := manager.EnquireLien(ctx, tt.req)

			if (err != nil) != tt.WantErr {
				t.Errorf("Got error = %v, wantErr %v", err, tt.WantErr)
				return
			}

			// Compare the expected and actual result
			if !proto.Equal(got, tt.want) {
				t.Errorf("Got: %v, want: %v", got, tt.want)
				return
			}
		})
	}
}
