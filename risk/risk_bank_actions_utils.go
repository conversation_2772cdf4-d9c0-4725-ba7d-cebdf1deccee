package risk

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	"github.com/epifi/be-common/pkg/logger"
	storage "github.com/epifi/be-common/pkg/storage/v2"
	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/risk"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/risk/dao"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
)

var (
	// OpenStateBankActions describes all the running + blocked states
	// STATE_INITIATED is a blocked state in which a workflow waits for signal
	OpenStateBankActions = []riskEnumsPb.State{
		riskEnumsPb.State_STATE_INITIATED,
		riskEnumsPb.State_STATE_SENT_TO_BANK,
		riskEnumsPb.State_STATE_MANUAL_INTERVENTION,
	}
	// InProgressBankActionWorkflowStates contains only the running states
	// This can filter out running workflows, use-case include filtering out
	// workflows which can be make as manual override.
	InProgressBankActionWorkflowStates = []riskEnumsPb.State{
		riskEnumsPb.State_STATE_SENT_TO_BANK,
		riskEnumsPb.State_STATE_MANUAL_INTERVENTION,
	}
)

// addBankAction creates a new bank action workflow if the pre validations are successful for given actor and action
// this is a deprecated flow and doesn't support debit freeze
func (s *Service) addBankAction(ctx context.Context, request *risk.AddSavingsAccountBankActionRequest) error {
	// getting account details for the given actor
	account, err := s.getSavingsAccountFromActorId(ctx, request.GetActorId())
	if err != nil {
		logger.Error(ctx, "adding bank action failed while getting savings account details from actor id",
			zap.String(logger.ACTION_TYPE, request.GetAction().String()), zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return err
	}

	// performing pre validations to see if this action has already been performed
	err = s.performBankActionPreValidations(ctx, request.GetActorId(), request.GetAction(), account.GetConstraints(), request.GetIsRecon())
	if err != nil {
		logger.Error(ctx, "pre validations failed while adding a new bank action", zap.String(logger.ACTION_TYPE, request.GetAction().String()),
			zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return err
	}

	// pre validations successful, hence start a workflow to perform the requested action
	if err = s.initiateBankActionFlow(ctx, request, account.GetId()); err != nil {
		logger.Error(ctx, "adding bank action failed while initiateBankActionFlow", zap.String(logger.ACTION_TYPE, request.GetAction().String()),
			zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) getSavingsAccountFromActorId(ctx context.Context, actorId string) (*savingsPb.Account, error) {
	// fetching actor to get entity/user id
	getActorResp, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
	if grpcErr := epifigrpc.RPCError(getActorResp, err); grpcErr != nil {
		if storage.IsRecordNotFoundError(err) || getActorResp.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, "could not find actor associated to entered id")
		}
		logger.Error(ctx, "failed to get actor by id", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, errors.Wrap(grpcErr, "error fetching actor by id from database")
	}
	userId := getActorResp.GetActor().GetEntityId()
	// fetching account using primary account user id
	getAcctResp, getAcctErr := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: userId}})
	if getAcctErr != nil || getAcctResp == nil {
		// db error or record not found
		logger.Error(ctx, "error fetching savings account using user id", zap.String(logger.USER_ID, userId))
		return nil, errors.Wrap(getAcctErr, "error fetching savings account using user id")
	}
	return getAcctResp.GetAccount(), nil
}

/*
performBankActionPreValidations performs pre-validations before adding to risk bank actions
This is a deprecated flow and doesn't support debit freeze
Checks include:
- Return error if any operation for an actor is in running progress
- Returns error if the latest risk bank action and savings constraints is same as current action
*/
func (s *Service) performBankActionPreValidations(ctx context.Context, actorId string, action riskEnumsPb.Action, acctConstraints *savingsPb.AccountConstraints, isRecon commontypes.BooleanEnum) error {
	// fetching the latest bank action against this action
	// fetch actions only in ActionWorkflowMaxRuntime duration
	bankActions, err := s.bankActionsDao.GetByActor(ctx,
		actorId,
		1,
		dao.WithCreatedAtAfter(time.Now().Add(-1*s.genConf.RiskBankAction().ActionWorkflowMaxRuntime)))
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return errors.Wrap(err, "could not get risk bank action entry from database using actor id")
	}

	if len(bankActions) != 0 {
		latestAction := bankActions[0]

		if lo.Contains(OpenStateBankActions, latestAction.GetState()) {
			return errors.Wrap(epifierrors.ErrInProgress, "A workflow is already in progress for given actor")
		}

		// checking if this is for recon, then ignoring pre-validations for current state
		// checking if current action is not equal to new intended action
		// intent here is to not return error if the latest bank action is different to current action as
		// bank sometimes update savings status before we add an risk bank action for day
		if isRecon != commontypes.BooleanEnum_TRUE && latestAction.GetAction() == action {
			// check if action already reflects in our savings account state
			switch action {
			case riskEnumsPb.Action_ACTION_FULL_FREEZE:
				if acctConstraints.GetAccessLevel() == savingsPb.AccessLevel_ACCESS_LEVEL_NO_ACCESS {
					return errors.Wrap(epifierrors.ErrAlreadyExists, "savings account has already been marked as full freeze")
				}
			case riskEnumsPb.Action_ACTION_CREDIT_FREEZE:
				if acctConstraints.GetAccessLevel() == savingsPb.AccessLevel_ACCESS_LEVEL_PARTIAL_ACCESS && lo.Contains(acctConstraints.GetRestrictions(), savingsPb.Restriction_RESTRICTION_CREDIT_FREEZE) {
					return errors.Wrap(epifierrors.ErrAlreadyExists, "savings account has already been marked as credit freeze")
				}
			case riskEnumsPb.Action_ACTION_UNFREEZE:
				if acctConstraints.GetAccessLevel() == savingsPb.AccessLevel_ACCESS_LEVEL_FULL_ACCESS {
					return errors.Wrap(epifierrors.ErrAlreadyExists, "savings account has already been marked as unfrozen")
				}
			default:
				logger.Error(ctx, "no flows setup for action", zap.String("bank_action", action.String()))
				return errors.Wrap(epifierrors.ErrInvalidArgument, fmt.Sprintf("no flows set up for action: %v", action))
			}
		}
	}

	return nil
}

// initiateBankActionFlow creates a new entry in database and initiates a workflow to perform the given action
func (s *Service) initiateBankActionFlow(ctx context.Context, req *risk.AddSavingsAccountBankActionRequest, accountId string) error {
	// create a new entry in database
	workFlowId := uuid.New().String()
	_, err := s.bankActionsDao.Create(ctx, &risk.RiskBankActions{
		ClientReqId:    workFlowId,
		ActorId:        req.GetActorId(),
		AccountId:      accountId,
		AccountType:    accountsPb.Type_SAVINGS,
		Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
		Action:         req.GetAction(),
		State:          riskEnumsPb.State_STATE_INITIATED,
		RequestReason:  req.GetRequestReason(),
		Provenance:     riskEnumsPb.Provenance_PROVENANCE_FI,
		CommsTemplate:  req.GetUserCommsTemplate(),
		IsRecon:        req.GetIsRecon(),
		BankActionDate: req.GetBankActionDate(),
	})
	if err != nil {
		logger.Error(ctx, "error while creating entry in table", zap.String(logger.ACTION_TYPE, req.GetAction().String()),
			zap.String(logger.ACCOUNT_ID, accountId), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return errors.Wrap(err, "error while creating an entry for risk bank action in database")
	}

	// creating a new workflow to perform the said action
	workFlowType := workflow.Type_TYPE_UNSPECIFIED
	switch req.GetAction() {
	case riskEnumsPb.Action_ACTION_FULL_FREEZE:
		workFlowType = workflow.Type_APPLY_TOTAL_FREEZE_ON_ACCOUNT
	case riskEnumsPb.Action_ACTION_CREDIT_FREEZE:
		workFlowType = workflow.Type_APPLY_CREDIT_FREEZE_ON_ACCOUNT
	case riskEnumsPb.Action_ACTION_UNFREEZE:
		workFlowType = workflow.Type_APPLY_TOTAL_UNFREEZE_ON_ACCOUNT
	default:
		logger.Error(ctx, "no celestial flow for action", zap.String(logger.ACTION_TYPE, req.GetAction().String()))
		return errors.Wrap(epifierrors.ErrInvalidArgument, fmt.Sprintf("no workflow defined for action: %v", workFlowType))
	}
	initiateWorkflowResp, err := s.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: req.GetActorId(),
			Version: workflow.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromProtoEnum(workFlowType),
			Payload: nil,
			ClientReqId: &workflow.ClientReqId{
				Id:     workFlowId,
				Client: workflow.Client_RISK,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
	})
	if grpcErr := epifigrpc.RPCError(initiateWorkflowResp, err); grpcErr != nil {
		logger.Error(ctx, "error while creating a new workflow", zap.String(logger.ACTION_TYPE, req.GetAction().String()),
			zap.String(logger.ACCOUNT_ID, accountId), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(grpcErr))
		return errors.Wrap(grpcErr, "error while creating a new workflow for action in celestial")
	}
	return nil
}

// Pre-validation step compares with the current state of actor with required state
// we fail if the current state is in MANUAL_INTERVENTION, and we are requesting for SUCCESS_MANUAL_OVERRIDE as workflow
// closes in MANUAL_INTERVENTION hence no processing will happen.
// REJECT_MANUAL_OVERRIDE can happen for both SENT_TO_BANK or MANUAL_INTERVENTION
func (s *Service) validateManualAction(ctx context.Context, request *risk.OverrideBankActionStateRequest) (*risk.RiskBankActions, error) {
	bankAction, err := s.fetchInProgressRiskBankActionsForActor(ctx, request.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting bank action detail for actor", zap.Error(err))
		return nil, err
	}

	switch request.GetRequiredState() {
	case riskEnumsPb.State_STATE_SUCCESS_MANUAL_OVERRIDE:
		if bankAction.GetState() == riskEnumsPb.State_STATE_MANUAL_INTERVENTION {
			return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "cannot mark force success as account in manual intervention")
		}
	case riskEnumsPb.State_STATE_REJECT_MANUAL_OVERRIDE:
	default:
		return nil, errors.Wrap(epifierrors.ErrInvalidArgument, "required state is invalid")
	}

	return bankAction, nil
}

func (s *Service) fetchInProgressRiskBankActionsForActor(ctx context.Context, actorId string) (*risk.RiskBankActions, error) {
	bankActions, err := s.bankActionsDao.GetByActor(ctx, actorId, 0, dao.WithStateFilter(InProgressBankActionWorkflowStates))
	switch {
	case storage.IsRecordNotFoundError(err):
		return nil, epifierrors.ErrRecordNotFound
	case err != nil:
		return nil, errors.Wrap(err, "error while fetching actor with stage")
	}
	if len(bankActions) > 1 {
		// currently moving this as a log statement only till we clear off all multiple entries
		logger.Error(ctx, "inconsistent state for action: reach-out to devs")
	}
	return bankActions[0], nil
}
