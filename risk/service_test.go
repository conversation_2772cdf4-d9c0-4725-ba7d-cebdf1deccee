package risk

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"log"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"

	celestialPb "github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifierrors"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	storagev2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	accountPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	dynamicElementsPb "github.com/epifi/gamma/api/dynamic_elements"
	"github.com/epifi/gamma/api/frontend/deeplink"
	riskPb "github.com/epifi/gamma/api/risk"
	cmPb "github.com/epifi/gamma/api/risk/case_management"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	riskEnums "github.com/epifi/gamma/api/risk/enums"
	leaPb "github.com/epifi/gamma/api/risk/lea"
	screenerPb "github.com/epifi/gamma/api/risk/screener"
	"github.com/epifi/gamma/api/risk/transaction_monitoring"
	savingsPb "github.com/epifi/gamma/api/savings"
	typePb "github.com/epifi/gamma/api/typesv2"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
	accountStatus "github.com/epifi/gamma/risk/accountstatus"
	"github.com/epifi/gamma/risk/config"
	"github.com/epifi/gamma/risk/config/genconf"
	"github.com/epifi/gamma/risk/dao"
	mockRiskDao "github.com/epifi/gamma/risk/dao/mocks"
	"github.com/epifi/gamma/risk/dynamicelements"
	riskDynamicElements "github.com/epifi/gamma/risk/dynamicelements"
	"github.com/epifi/gamma/risk/lea/complaint/mocks"
	mockLea "github.com/epifi/gamma/risk/lea/mocks"
	"github.com/epifi/gamma/risk/lea/narration"
	mocks_narration "github.com/epifi/gamma/risk/lea/narration/mocks"
	mockUnifiedLea "github.com/epifi/gamma/risk/lea/unified_lea/mocks"
	"github.com/epifi/gamma/risk/riskparam"
	riskProcMocks "github.com/epifi/gamma/risk/riskparam/mocks"
	mockFactory "github.com/epifi/gamma/risk/riskparam/mocks/factory"
	mockScreener "github.com/epifi/gamma/risk/screener/mocks"
	mock_checkmanager "github.com/epifi/gamma/risk/screener/mocks/checkmanager"
	"github.com/epifi/gamma/risk/screener/params"
	mock_accountstatus "github.com/epifi/gamma/risk/test/mocks/accountstatus"
	mock_dynamicelements "github.com/epifi/gamma/risk/test/mocks/dynamicelements"
)

var (
	testactorId    = "actor-id"
	userId1        = "user-id-1"
	actorId1       = "actor-id-1"
	savingsId1     = "savings-id-1"
	workflowId1    = "client-id-1"
	analystEmailId = "<EMAIL>"
	testLea        = &riskPb.LEAComplaint{
		Id: "leaId",
	}
	dummyTimestamp     = &timestamppb.Timestamp{Seconds: int64(**********)}
	testLeaWithDetails = &riskPb.LEAComplaint{
		Id:                       "leaId",
		ActorId:                  actorId1,
		AuditBranchOperatingDate: dummyTimestamp,
		AccountType:              accountPb.Type_SAVINGS,
		AuditDate:                dummyTimestamp,
		FreezeCode:               riskEnums.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE,
	}
	leaArray = []*riskPb.ExtendedLEAComplaint{
		{
			Complaint: &riskPb.LEAComplaint{
				Id: "leaId",
			},
		},
	}
	BulkAddSavingsAccountBankActionsReq1 = &riskPb.BulkAddSavingsAccountBankActionRequest{
		Requests: []*riskPb.AddSavingsAccountBankActionRequest{
			{
				Identifier:    &riskPb.AddSavingsAccountBankActionRequest_ActorId{ActorId: actorId1},
				Action:        riskEnums.Action_ACTION_FULL_FREEZE,
				RequestReason: requestReasonFixture1,
			},
		},
	}
	BulkAddSavingsAccountBankActionsReq2 = &riskPb.BulkAddSavingsAccountBankActionRequest{
		Requests: []*riskPb.AddSavingsAccountBankActionRequest{
			{
				Identifier:    &riskPb.AddSavingsAccountBankActionRequest_ActorId{ActorId: actorId1},
				Action:        riskEnums.Action_ACTION_FULL_FREEZE,
				RequestReason: requestReasonFixture1,
			},
			{
				Identifier:    &riskPb.AddSavingsAccountBankActionRequest_ActorId{ActorId: testactorId},
				Action:        riskEnums.Action_ACTION_FULL_FREEZE,
				RequestReason: requestReasonFixture1,
			},
		},
	}
	requestReasonFixture1 = &riskPb.RequestReason{
		Reason: riskEnums.RequestReason_REQUEST_REASON_FRAUDULENT_ACCOUNT,
	}
	reqReasonLEa = &riskPb.RequestReason{
		Reason: riskEnums.RequestReason_REQUEST_REASON_LEA_COMPLAINT,
	}
	actorFixture1 = &typePb.Actor{
		Id:       actorId1,
		EntityId: userId1,
	}
	savingsFixture1 = &savingsPb.Account{
		Id:                   savingsId1,
		AccountNo:            "************",
		IfscCode:             "FDRL01234",
		PrimaryAccountHolder: userId1,
		Constraints:          nil,
		PartnerBank:          commonvgpb.Vendor_FEDERAL_BANK,
	}
	savingsFixtureFullFreeze = &savingsPb.Account{
		Id:                   savingsId1,
		AccountNo:            "************",
		IfscCode:             "FDRL01234",
		PrimaryAccountHolder: userId1,
		Constraints: &savingsPb.AccountConstraints{
			AccessLevel: savingsPb.AccessLevel_ACCESS_LEVEL_NO_ACCESS,
		},
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
	}
	bankActionFixture1 = &riskPb.RiskBankActions{
		ClientReqId:   workflowId1,
		ActorId:       actorId1,
		AccountId:     savingsFixture1.GetId(),
		AccountType:   accountPb.Type_SAVINGS,
		Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
		Action:        riskEnums.Action_ACTION_FULL_FREEZE,
		State:         riskEnums.State_STATE_INITIATED,
		RequestReason: requestReasonFixture1,
		Provenance:    riskEnums.Provenance_PROVENANCE_FI,
	}
	bankActionUnfreeze = &riskPb.RiskBankActions{
		ClientReqId:   workflowId1,
		ActorId:       actorId1,
		AccountId:     savingsFixture1.GetId(),
		AccountType:   accountPb.Type_SAVINGS,
		Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
		Action:        riskEnums.Action_ACTION_UNFREEZE,
		State:         riskEnums.State_STATE_SUCCESS,
		RequestReason: requestReasonFixture1,
		Provenance:    riskEnums.Provenance_PROVENANCE_FI,
	}
	bankActionFixture1ClosedState = &riskPb.RiskBankActions{
		ClientReqId:   workflowId1,
		ActorId:       actorId1,
		AccountId:     savingsFixture1.GetId(),
		AccountType:   accountPb.Type_SAVINGS,
		Vendor:        commonvgpb.Vendor_FEDERAL_BANK,
		Action:        riskEnums.Action_ACTION_FULL_FREEZE,
		State:         riskEnums.State_STATE_SUCCESS,
		RequestReason: requestReasonFixture1,
		Provenance:    riskEnums.Provenance_PROVENANCE_FI,
	}
	bankActionFixtureWithIsRecon = &riskPb.RiskBankActions{
		ClientReqId:    workflowId1,
		ActorId:        actorId1,
		AccountId:      savingsFixture1.GetId(),
		AccountType:    accountPb.Type_SAVINGS,
		Vendor:         commonvgpb.Vendor_FEDERAL_BANK,
		Action:         riskEnums.Action_ACTION_FULL_FREEZE,
		State:          riskEnums.State_STATE_INITIATED,
		RequestReason:  reqReasonLEa,
		Provenance:     riskEnums.Provenance_PROVENANCE_FI,
		IsRecon:        commontypes.BooleanEnum_TRUE,
		BankActionDate: dummyTimestamp,
	}
	workflowFixture1 = &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: actorId1,
			Version: 0,
			Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflow.Type_APPLY_TOTAL_FREEZE_ON_ACCOUNT),
			ClientReqId: &workflow.ClientReqId{
				Id:     workflowId1,
				Client: workflow.Client_RISK,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
	}
	dynamicElementReqForHome = &dynamicElementsPb.FetchDynamicElementsRequest{
		ActorId: "actor-1",
		ClientContext: &dynamicElementsPb.ClientContext{
			ScreenName: deeplink.Screen_HOME,
		},
	}
	homeFreezeBanner = &dynamicElementsPb.DynamicElement{
		OwnerService: typePb.ServiceName_RISK_SERVICE,
		Id:           "test-id-1",
		UtilityType:  dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
	}

	attemptIdentifier1 = &screenerPb.AttemptIdentifier{
		Identifier: &screenerPb.AttemptIdentifier_AttemptId{
			AttemptId: "test-1",
		},
	}
	screenerAttempt1 = &screenerPb.ScreenerAttempt{
		Id:              "1",
		ClientRequestId: "req1",
		Criteria:        riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
		ActorId:         actorId1,
		Status:          screenerPb.ScreenerStatus_SCREENER_STATUS_DONE,
		Verdict:         screenerPb.Verdict_VERDICT_FAIL,
		Score:           0.0,
	}
	screenerAttempt2 = &screenerPb.ScreenerAttempt{
		Id:              "1",
		ClientRequestId: "req1",
		Criteria:        riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
		ActorId:         actorId1,
		Status:          screenerPb.ScreenerStatus_SCREENER_STATUS_DONE,
		Verdict:         screenerPb.Verdict_VERDICT_PASS,
		Score:           0.0,
		ManualVerdictMetadata: &screenerPb.ManualVerdictMetadata{
			VerdictDetails: []*screenerPb.VerdictDetails{
				{
					AnalystEmail:    analystEmailId,
					Reason:          "manual reason to pass this attempt",
					PreviousStatus:  screenerPb.ScreenerStatus_SCREENER_STATUS_DONE,
					PreviousVerdict: screenerPb.Verdict_VERDICT_FAIL,
				},
			},
		},
	}
	screenerAttempt3 = &screenerPb.ScreenerAttempt{
		Id:              "1",
		ClientRequestId: "req1",
		Criteria:        riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
		ActorId:         actorId1,
		Status:          screenerPb.ScreenerStatus_SCREENER_STATUS_DONE,
		Verdict:         screenerPb.Verdict_VERDICT_FAIL,
		Score:           0.0,
	}

	additionalDetails = &riskDynamicElements.AccountAdditionalDetails{
		LatestLeaForActor: nil,
		AccountStatus:     &accountStatus.OperationalDetails{},
	}
)

func TestService_UpsertRiskData(t *testing.T) {
	type args struct {
		actorId   string
		RiskParam riskPb.RiskParam
		PayLoad   *riskPb.Payload
		Result    riskPb.Result
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(mock *mockedDependencies, args args)
		want    *riskPb.UpsertRiskDataResponse
		wantErr error
	}{
		{
			name: "upsert successful",
			args: args{
				actorId:   testactorId,
				RiskParam: riskPb.RiskParam_RISK_PARAM_GEO_LOCATION_PIN_CODE,
				PayLoad:   nil,
				Result:    riskPb.Result_RESULT_PASS,
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockRiskDataDaoMock.EXPECT().Create(gomock.Any(), &riskPb.RiskData{
					ActorId:   args.actorId,
					RiskParam: args.RiskParam,
					Payload:   args.PayLoad,
					Result:    args.Result,
				}).Return(&riskPb.RiskData{
					ActorId:   args.actorId,
					RiskParam: args.RiskParam,
					Payload:   args.PayLoad,
					Result:    args.Result,
				}, nil)
			},
			want: &riskPb.UpsertRiskDataResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: nil,
		},
		{
			name: "actor Id empty",
			args: args{
				actorId:   "",
				RiskParam: riskPb.RiskParam_RISK_PARAM_GEO_LOCATION_PIN_CODE,
				PayLoad:   nil,
				Result:    riskPb.Result_RESULT_PASS,
			},
			want: &riskPb.UpsertRiskDataResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id can not be empty"),
			},
			wantErr: nil,
		},
		{
			name: "risk param unspecified",
			args: args{
				actorId:   testactorId,
				RiskParam: riskPb.RiskParam_RISK_PARAM_UNSPECIFIED,
				PayLoad:   nil,
				Result:    riskPb.Result_RESULT_PASS,
			},
			want: &riskPb.UpsertRiskDataResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("risk param can not be unspecified"),
			},
			wantErr: nil,
		},
		{
			name: "error in dao layer",
			args: args{
				actorId:   testactorId,
				RiskParam: riskPb.RiskParam_RISK_PARAM_KYC_ADDRESS_PIN_CODE,
				PayLoad:   nil,
				Result:    riskPb.Result_RESULT_PASS,
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockRiskDataDaoMock.EXPECT().Create(gomock.Any(), &riskPb.RiskData{
					ActorId:   args.actorId,
					RiskParam: args.RiskParam,
					Payload:   args.PayLoad,
					Result:    args.Result,
				}).Return(nil, dao.IdNotPresentError)
			},
			want:    nil,
			wantErr: dao.IdNotPresentError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDeps := newServerWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mockedDeps, tt.args)
			}
			got, err := s.UpsertRiskData(context.Background(), &riskPb.UpsertRiskDataRequest{
				ActorId:   tt.args.actorId,
				RiskParam: tt.args.RiskParam,
				Payload:   tt.args.PayLoad,
				Result:    tt.args.Result,
			})
			assert.Equal(t, tt.wantErr, err)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestService_GetRiskData(t *testing.T) {
	var (
		reqNoMD = &riskPb.GetRiskDataRequest{
			ActorId: "a1",
			RiskParams: []riskPb.RiskParam{
				riskPb.RiskParam_RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL,
			},
			IsDevicePremium:                commontypes.BooleanEnum_TRUE,
			IsCreditReportFound:            commontypes.BooleanEnum_FALSE,
			HasCreditReportDownloadConsent: 0,
			GmailPanNameMatchScore:         123,
		}

		reqWithMD = &riskPb.GetRiskDataRequest{
			ActorId: "a1",
			RiskParams: []riskPb.RiskParam{
				riskPb.RiskParam_RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL,
			},
			Metadata: map[string]string{
				"IsDevicePremium":     "TRUE",
				"IsCreditReportFound": "FALSE",
			},
		}

		onbRiskModelRiskData = &riskPb.RiskData{
			Id:        "6919dc83-123b-4f85-85d5-a32c000549b1",
			ActorId:   "a1",
			RiskParam: riskPb.RiskParam_RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL,
			Payload:   &riskPb.Payload{},
			Result:    riskPb.Result_RESULT_FAIL,
		}
		onbRiskDataMap = map[string]*riskPb.RiskParamResponse{
			riskPb.RiskParam_RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL.String(): {
				Status:   riskPb.RiskParamResponse_SUCCESS,
				RiskData: onbRiskModelRiskData,
			},
		}
	)
	type mockDeps struct {
		riskDataDao          *mockRiskDao.MockRiskDataDao
		riskParamFactoryMock *mockFactory.MockIRiskParamProcessorFactory
		riskParamProcMock    *riskProcMocks.MockRiskParamProcessor
	}
	type args struct {
		req *riskPb.GetRiskDataRequest
	}
	tests := []struct {
		name    string
		mocksFn func(mock *mockDeps)
		args    args
		want    *riskPb.GetRiskDataResponse
	}{
		{
			name: "no cache; no metadata; valid processor",
			args: args{
				req: reqNoMD,
			},
			mocksFn: func(mock *mockDeps) {
				mock.riskDataDao.EXPECT().Create(gomock.Any(), gomock.Eq(onbRiskModelRiskData)).Return(nil, nil)
				mock.riskParamFactoryMock.EXPECT().GetProcessorImplementation(gomock.Any(), riskPb.RiskParam_RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL).Return(mock.riskParamProcMock, nil)
				mock.riskParamProcMock.EXPECT().ProcessRisk(gomock.Any(), gomock.Eq(&riskparam.ProcessorRequest{
					ActorId: reqNoMD.ActorId,
					Metadata: map[string]string{
						"GmailPanNameScore": "123",
					},
				})).Return(&riskparam.ProcessorResponse{
					RiskData: onbRiskModelRiskData,
				}, nil)
			},
			want: &riskPb.GetRiskDataResponse{
				Status:               rpc.StatusOk(),
				RiskData:             []*riskPb.RiskData{onbRiskModelRiskData},
				RiskParamResponseMap: onbRiskDataMap,
			},
		},
		{
			name: "no cache; valid metadata; valid processor",
			args: args{
				req: reqWithMD,
			},
			mocksFn: func(mock *mockDeps) {
				mock.riskDataDao.EXPECT().Create(gomock.Any(), gomock.Eq(onbRiskModelRiskData)).Return(nil, nil)
				mock.riskParamFactoryMock.EXPECT().GetProcessorImplementation(gomock.Any(), gomock.Any()).Return(mock.riskParamProcMock, nil)
				mock.riskParamProcMock.EXPECT().ProcessRisk(gomock.Any(), gomock.Eq(&riskparam.ProcessorRequest{
					ActorId:  reqWithMD.ActorId,
					Metadata: reqWithMD.Metadata,
				})).Return(&riskparam.ProcessorResponse{
					RiskData: onbRiskModelRiskData,
				}, nil)
			},
			want: &riskPb.GetRiskDataResponse{
				Status:               rpc.StatusOk(),
				RiskData:             []*riskPb.RiskData{onbRiskModelRiskData},
				RiskParamResponseMap: onbRiskDataMap,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			riskDataDaoMock := mockRiskDao.NewMockRiskDataDao(ctr)
			riskParamFactoryMock := mockFactory.NewMockIRiskParamProcessorFactory(ctr)
			riskParamProcMock := riskProcMocks.NewMockRiskParamProcessor(ctr)
			mocks := &mockDeps{
				riskDataDao:          riskDataDaoMock,
				riskParamProcMock:    riskParamProcMock,
				riskParamFactoryMock: riskParamFactoryMock,
			}
			tt.mocksFn(mocks)

			s := &Service{
				riskDataDao:          mocks.riskDataDao,
				riskParamProcFactory: riskParamFactoryMock,
			}
			got, err := s.GetRiskData(context.Background(), tt.args.req)
			assert.Nil(t, err)
			assert.Truef(t, proto.Equal(tt.want, got), "\n got: %v\nwant: %v", got, tt.want)
		})
	}
}

func TestService_BulkAddSavingsAccountBankAction(t *testing.T) {
	type args struct {
		ctx context.Context
		req *riskPb.BulkAddSavingsAccountBankActionRequest
	}
	tests := []struct {
		name    string
		mocks   func(*mockedDependencies)
		args    args
		want    *riskPb.BulkAddSavingsAccountBankActionResponse
		wantErr error
	}{
		{
			name: "successfully created workflow",
			mocks: func(m *mockedDependencies) {
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId1,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  actorFixture1,
				}, nil)
				m.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: userId1,
					},
				}).Return(&savingsPb.GetAccountResponse{Account: savingsFixture1}, nil)
				m.mockBankActionsDao.EXPECT().GetByActor(gomock.Any(), actorId1, gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				m.mockBankActionsDao.EXPECT().Create(gomock.Any(), newRiskBankActionMatcher(bankActionFixture1)).Return(bankActionFixture1, nil)
				m.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), newInitiateWorkflowMatcher(workflowFixture1)).
					Return(&celestialPb.InitiateWorkflowResponse{
						Status:            rpc.StatusOk(),
						WorkflowRequestId: workflowFixture1.GetClientReqId().GetId(),
					}, nil)
			},
			args: args{
				ctx: context.Background(),
				req: BulkAddSavingsAccountBankActionsReq1,
			},
			want: &riskPb.BulkAddSavingsAccountBankActionResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: nil,
		},
		{
			name: "success when older workflow is different",
			mocks: func(m *mockedDependencies) {
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId1,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  actorFixture1,
				}, nil)
				m.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: userId1,
					},
				}).Return(&savingsPb.GetAccountResponse{Account: savingsFixture1}, nil)
				m.mockBankActionsDao.EXPECT().GetByActor(gomock.Any(), actorId1, gomock.Any(), gomock.Any()).Return([]*riskPb.RiskBankActions{bankActionUnfreeze}, nil)
				m.mockBankActionsDao.EXPECT().Create(gomock.Any(), newRiskBankActionMatcher(bankActionFixture1)).Return(bankActionFixture1, nil)
				m.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), newInitiateWorkflowMatcher(workflowFixture1)).
					Return(&celestialPb.InitiateWorkflowResponse{
						Status:            rpc.StatusOk(),
						WorkflowRequestId: workflowFixture1.GetClientReqId().GetId(),
					}, nil)
			},
			args: args{
				ctx: context.Background(),
				req: BulkAddSavingsAccountBankActionsReq1,
			},
			want: &riskPb.BulkAddSavingsAccountBankActionResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: nil,
		},
		{
			name: "partial success",
			mocks: func(m *mockedDependencies) {
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId1,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  actorFixture1,
				}, nil)
				m.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: userId1,
					},
				}).Return(&savingsPb.GetAccountResponse{Account: savingsFixture1}, nil)
				m.mockBankActionsDao.EXPECT().GetByActor(gomock.Any(), actorId1, gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				m.mockBankActionsDao.EXPECT().Create(gomock.Any(), newRiskBankActionMatcher(bankActionFixture1)).Return(bankActionFixture1, nil)
				m.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), newInitiateWorkflowMatcher(workflowFixture1)).
					Return(&celestialPb.InitiateWorkflowResponse{
						Status:            rpc.StatusOk(),
						WorkflowRequestId: workflowFixture1.GetClientReqId().GetId(),
					}, nil)
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: testactorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			args: args{
				ctx: context.Background(),
				req: BulkAddSavingsAccountBankActionsReq2,
			},
			want: &riskPb.BulkAddSavingsAccountBankActionResponse{
				Status: rpc.StatusOk(),
				Failures: []*riskPb.AddSavingsAccountBankActionFailure{
					{
						RequestIndex: 1,
						ActorId:      testactorId,
						Reason:       errors.Wrap(epifierrors.ErrRecordNotFound, "could not find actor associated to entered id").Error(),
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "could not find actor from id",
			mocks: func(m *mockedDependencies) {
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId1,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			args: args{
				ctx: context.Background(),
				req: BulkAddSavingsAccountBankActionsReq1,
			},
			want: &riskPb.BulkAddSavingsAccountBankActionResponse{
				Status: rpc.StatusOk(),
				Failures: []*riskPb.AddSavingsAccountBankActionFailure{
					{
						RequestIndex: 0,
						ActorId:      actorId1,
						Reason:       errors.Wrap(epifierrors.ErrRecordNotFound, "could not find actor associated to entered id").Error(),
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "pre validation failure - action already reflects in savings account state",
			mocks: func(m *mockedDependencies) {
				m.mockBankActionsDao.EXPECT().GetByActor(gomock.Any(), actorId1, gomock.Any(), gomock.Any()).Return([]*riskPb.RiskBankActions{bankActionFixture1ClosedState}, nil)
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId1,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  actorFixture1,
				}, nil)
				m.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: userId1,
					},
				}).Return(&savingsPb.GetAccountResponse{Account: savingsFixtureFullFreeze}, nil)
			},
			args: args{
				ctx: context.Background(),
				req: BulkAddSavingsAccountBankActionsReq1,
			},
			want: &riskPb.BulkAddSavingsAccountBankActionResponse{
				Status: rpc.StatusOk(),
				Failures: []*riskPb.AddSavingsAccountBankActionFailure{
					{
						RequestIndex: 0,
						ActorId:      actorId1,
						Reason:       errors.Wrap(epifierrors.ErrAlreadyExists, "savings account has already been marked as full freeze").Error(),
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "pre validation failure - workflow already exists",
			mocks: func(m *mockedDependencies) {
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId1,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  actorFixture1,
				}, nil)
				m.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: userId1,
					},
				}).Return(&savingsPb.GetAccountResponse{Account: savingsFixture1}, nil)
				m.mockBankActionsDao.EXPECT().GetByActor(gomock.Any(), actorId1, gomock.Any(), gomock.Any()).Return([]*riskPb.RiskBankActions{bankActionFixture1}, nil)
			},
			args: args{
				ctx: context.Background(),
				req: BulkAddSavingsAccountBankActionsReq1,
			},
			want: &riskPb.BulkAddSavingsAccountBankActionResponse{
				Status: rpc.StatusOk(),
				Failures: []*riskPb.AddSavingsAccountBankActionFailure{
					{
						RequestIndex: 0,
						ActorId:      actorId1,
						Reason:       "A workflow is already in progress for given actor: in progress error",
					},
				},
			},
			wantErr: nil,
		},
		{
			name: "database entry creation failed",
			mocks: func(m *mockedDependencies) {
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId1,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  actorFixture1,
				}, nil)
				m.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: userId1,
					},
				}).Return(&savingsPb.GetAccountResponse{Account: savingsFixture1}, nil)
				m.mockBankActionsDao.EXPECT().GetByActor(gomock.Any(), actorId1, gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				m.mockBankActionsDao.EXPECT().Create(gomock.Any(), newRiskBankActionMatcher(bankActionFixture1)).Return(nil, epifierrors.ErrInvalidSQL)
			},
			args: args{
				ctx: context.Background(),
				req: BulkAddSavingsAccountBankActionsReq1,
			},
			want: &riskPb.BulkAddSavingsAccountBankActionResponse{
				Status: rpc.StatusOk(),
				Failures: []*riskPb.AddSavingsAccountBankActionFailure{
					{
						RequestIndex: 0,
						ActorId:      actorId1,
						Reason:       errors.Wrap(epifierrors.ErrInvalidSQL, "error while creating an entry for risk bank action in database").Error(),
					},
				},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDeps := newServerWithMocks(t)
			tt.mocks(mockedDeps)
			got, err := s.BulkAddSavingsAccountBankAction(tt.args.ctx, tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			assert.Equal(t, tt.want, got)
		})
	}
}

type riskBankActionArgMatcher struct {
	want *riskPb.RiskBankActions
}

func newRiskBankActionMatcher(rba *riskPb.RiskBankActions) interface{} {
	if rba == nil {
		return gomock.Any()
	}
	return &riskBankActionArgMatcher{want: rba}
}

func (l *riskBankActionArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*riskPb.RiskBankActions)
	if !ok {
		return false
	}
	l.want.ClientReqId = got.ClientReqId
	return reflect.DeepEqual(got, l.want)
}

func (l *riskBankActionArgMatcher) String() string {
	return fmt.Sprintf("%v", l.want)
}

type initiateWorkflowMatcher struct {
	want *celestialPb.InitiateWorkflowRequest
}

func newInitiateWorkflowMatcher(rba *celestialPb.InitiateWorkflowRequest) interface{} {
	if rba == nil {
		return gomock.Any()
	}
	return &initiateWorkflowMatcher{want: rba}
}

func (l *initiateWorkflowMatcher) Matches(x interface{}) bool {
	got, ok := x.(*celestialPb.InitiateWorkflowRequest)
	if !ok {
		return false
	}
	l.want.Params.ClientReqId = &workflow.ClientReqId{
		Id:     got.GetParams().GetClientReqId().GetId(),
		Client: got.GetParams().GetClientReqId().GetClient(),
	}
	return reflect.DeepEqual(got, l.want)
}

func (l *initiateWorkflowMatcher) String() string {
	return fmt.Sprintf("%v", l.want)
}

func TestService_OverrideStatesForBankAction(t *testing.T) {
	s, mockedDeps := newServerWithMocks(t)
	type fields struct {
		riskDataDao    dao.RiskDataDao
		bankActionsDao dao.RiskBankActionsDao
	}
	type args struct {
		ctx context.Context
		req *riskPb.BulkOverrideBankActionStateRequest
	}
	type mockBankActionsGetByActorDao struct {
		enable        bool
		actor         string
		filterOptions []storagev2.FilterOption
		limit         int
		res           []*riskPb.RiskBankActions
		err           error
	}
	type mockBankActionsUpdate struct {
		enable     bool
		req        *riskPb.RiskBankActions
		updateMask []riskPb.RiskBankActionsFieldMask
		res        *riskPb.RiskBankActions
		err        error
	}
	var (
		testActor = "test-actor"
	)
	tests := []struct {
		name                         string
		fields                       fields
		args                         args
		want                         *riskPb.BulkOverrideBankActionStateResponse
		mockBankActionsGetByActorDao mockBankActionsGetByActorDao
		mockBankActionsUpdate        mockBankActionsUpdate
		wantErr                      bool
	}{
		{
			name: "success: manual override force success added",
			args: args{
				ctx: context.Background(),
				req: &riskPb.BulkOverrideBankActionStateRequest{
					Requests: []*riskPb.OverrideBankActionStateRequest{
						{
							ActorId:        testActor,
							RequiredState:  riskEnums.State_STATE_SUCCESS_MANUAL_OVERRIDE,
							OverrideReason: "manual error",
						},
					},
					RequesterEmail: "<EMAIL>",
				},
			},
			mockBankActionsGetByActorDao: mockBankActionsGetByActorDao{
				enable: true,
				actor:  testActor,
				limit:  0,
				filterOptions: []storagev2.FilterOption{
					dao.WithStateFilter([]riskEnums.State{
						riskEnums.State_STATE_SENT_TO_BANK,
						riskEnums.State_STATE_MANUAL_INTERVENTION,
					}),
				},
				res: []*riskPb.RiskBankActions{
					{
						ActorId:     testActor,
						ClientReqId: "client-req-id-1",
						State:       riskEnums.State_STATE_SENT_TO_BANK,
						Action:      riskEnums.Action_ACTION_FULL_FREEZE,
						AccountId:   "account-1",
						AccountType: accountPb.Type_SAVINGS,
						RequestReason: &riskPb.RequestReason{
							Reason:  riskEnums.RequestReason_REQUEST_REASON_NPCI_COMPLAINT,
							Remarks: "test",
						},
					},
				},
			},
			mockBankActionsUpdate: mockBankActionsUpdate{
				enable: true,
				req: &riskPb.RiskBankActions{
					ActorId:     testActor,
					ClientReqId: "client-req-id-1",
					Action:      riskEnums.Action_ACTION_FULL_FREEZE,
					State:       riskEnums.State_STATE_SUCCESS_MANUAL_OVERRIDE,
					RequestReason: &riskPb.RequestReason{
						Reason:  riskEnums.RequestReason_REQUEST_REASON_NPCI_COMPLAINT,
						Remarks: "test manual state <NAME_EMAIL>: reason: manual error",
					},
					AccountId:   "account-1",
					AccountType: accountPb.Type_SAVINGS,
				},
				updateMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_REQUEST_REASON,
					riskPb.RiskBankActionsFieldMask_STATE,
				},
				res: &riskPb.RiskBankActions{
					ActorId:     testActor,
					ClientReqId: "client-req-id-1",
					State:       riskEnums.State_STATE_SUCCESS_MANUAL_OVERRIDE,
					Action:      riskEnums.Action_ACTION_FULL_FREEZE,
					AccountId:   "account-1",
					AccountType: accountPb.Type_SAVINGS,
					RequestReason: &riskPb.RequestReason{
						Reason:  riskEnums.RequestReason_REQUEST_REASON_NPCI_COMPLAINT,
						Remarks: "test manual state <NAME_EMAIL>: reason: manual error",
					},
				},
				err: nil,
			},
			want:    &riskPb.BulkOverrideBankActionStateResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "success: manual override force failure",
			args: args{
				ctx: context.Background(),
				req: &riskPb.BulkOverrideBankActionStateRequest{
					Requests: []*riskPb.OverrideBankActionStateRequest{
						{
							ActorId:        testActor,
							RequiredState:  riskEnums.State_STATE_REJECT_MANUAL_OVERRIDE,
							OverrideReason: "manual error",
						},
					},
					RequesterEmail: "<EMAIL>",
				},
			},
			mockBankActionsGetByActorDao: mockBankActionsGetByActorDao{
				enable: true,
				actor:  testActor,
				filterOptions: []storagev2.FilterOption{
					dao.WithStateFilter([]riskEnums.State{
						riskEnums.State_STATE_SENT_TO_BANK,
						riskEnums.State_STATE_MANUAL_INTERVENTION,
					}),
				},
				res: []*riskPb.RiskBankActions{
					{
						ActorId:     testActor,
						ClientReqId: "client-req-id-1",
						State:       riskEnums.State_STATE_SENT_TO_BANK,
						Action:      riskEnums.Action_ACTION_FULL_FREEZE,
						AccountId:   "account-1",
						AccountType: accountPb.Type_SAVINGS,
						RequestReason: &riskPb.RequestReason{
							Reason:  riskEnums.RequestReason_REQUEST_REASON_NPCI_COMPLAINT,
							Remarks: "test",
						},
					},
				},
			},
			mockBankActionsUpdate: mockBankActionsUpdate{
				enable: true,
				req: &riskPb.RiskBankActions{
					ActorId:     testActor,
					ClientReqId: "client-req-id-1",
					Action:      riskEnums.Action_ACTION_FULL_FREEZE,
					State:       riskEnums.State_STATE_REJECT_MANUAL_OVERRIDE,
					RequestReason: &riskPb.RequestReason{
						Reason:  riskEnums.RequestReason_REQUEST_REASON_NPCI_COMPLAINT,
						Remarks: "test manual state <NAME_EMAIL>: reason: manual error",
					},
					AccountId:   "account-1",
					AccountType: accountPb.Type_SAVINGS,
				},
				updateMask: []riskPb.RiskBankActionsFieldMask{
					riskPb.RiskBankActionsFieldMask_REQUEST_REASON,
					riskPb.RiskBankActionsFieldMask_STATE,
				},
				res: &riskPb.RiskBankActions{
					ActorId:     testActor,
					ClientReqId: "client-req-id-1",
					State:       riskEnums.State_STATE_REJECT_MANUAL_OVERRIDE,
					Action:      riskEnums.Action_ACTION_FULL_FREEZE,
					AccountId:   "account-1",
					AccountType: accountPb.Type_SAVINGS,
					RequestReason: &riskPb.RequestReason{
						Reason:  riskEnums.RequestReason_REQUEST_REASON_NPCI_COMPLAINT,
						Remarks: "test manual state <NAME_EMAIL>: reason: manual error",
					},
				},
				err: nil,
			},
			want:    &riskPb.BulkOverrideBankActionStateResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "fail: manual override force success but in manual",
			args: args{
				ctx: context.Background(),
				req: &riskPb.BulkOverrideBankActionStateRequest{
					Requests: []*riskPb.OverrideBankActionStateRequest{
						{
							ActorId:        testActor,
							RequiredState:  riskEnums.State_STATE_SUCCESS_MANUAL_OVERRIDE,
							OverrideReason: "manual error",
						},
					},
					RequesterEmail: "<EMAIL>",
				},
			},
			mockBankActionsGetByActorDao: mockBankActionsGetByActorDao{
				enable: true,
				actor:  testActor,
				filterOptions: []storagev2.FilterOption{
					dao.WithStateFilter([]riskEnums.State{
						riskEnums.State_STATE_SENT_TO_BANK,
						riskEnums.State_STATE_MANUAL_INTERVENTION,
					}),
				},
				res: []*riskPb.RiskBankActions{
					{
						ActorId:     testActor,
						ClientReqId: "client-req-id-1",
						State:       riskEnums.State_STATE_MANUAL_INTERVENTION,
						Action:      riskEnums.Action_ACTION_FULL_FREEZE,
						AccountId:   "account-1",
						AccountType: accountPb.Type_SAVINGS,
						RequestReason: &riskPb.RequestReason{
							Reason:  riskEnums.RequestReason_REQUEST_REASON_NPCI_COMPLAINT,
							Remarks: "test",
						},
					},
				},
			},
			mockBankActionsUpdate: mockBankActionsUpdate{
				enable: false,
				err:    nil,
			},
			want: &riskPb.BulkOverrideBankActionStateResponse{Status: rpc.StatusOk(), Failures: []*riskPb.BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure{
				{
					ActorId:     testActor,
					Status:      rpc.StatusInvalidArgument(),
					ErrorString: "cannot mark force success as account in manual intervention: invalid argument passed by the client",
				},
			}},
			wantErr: false,
		},
		{
			name: "fail: manual override force success no open state",
			args: args{
				ctx: context.Background(),
				req: &riskPb.BulkOverrideBankActionStateRequest{
					Requests: []*riskPb.OverrideBankActionStateRequest{
						{
							ActorId:        testActor,
							RequiredState:  riskEnums.State_STATE_SUCCESS_MANUAL_OVERRIDE,
							OverrideReason: "manual error",
						},
					},
					RequesterEmail: "<EMAIL>",
				},
			},
			mockBankActionsGetByActorDao: mockBankActionsGetByActorDao{
				enable: true,
				actor:  testActor,
				filterOptions: []storagev2.FilterOption{
					dao.WithStateFilter([]riskEnums.State{
						riskEnums.State_STATE_SENT_TO_BANK,
						riskEnums.State_STATE_MANUAL_INTERVENTION,
					}),
				},
				res: []*riskPb.RiskBankActions{},
				err: epifierrors.ErrRecordNotFound,
			},
			mockBankActionsUpdate: mockBankActionsUpdate{
				enable: false,
				err:    nil,
			},
			want: &riskPb.BulkOverrideBankActionStateResponse{Status: rpc.StatusOk(), Failures: []*riskPb.BulkOverrideBankActionStateResponse_OverrideBankActionStateFailure{
				{
					ActorId:     testActor,
					Status:      rpc.StatusInvalidArgument(),
					ErrorString: "record not found",
				},
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockBankActionsGetByActorDao.enable {
				mockedDeps.mockBankActionsDao.EXPECT().GetByActor(context.Background(), tt.mockBankActionsGetByActorDao.actor, tt.mockBankActionsGetByActorDao.limit, gomock.Any()).
					Return(tt.mockBankActionsGetByActorDao.res, tt.mockBankActionsGetByActorDao.err)
			}
			if tt.mockBankActionsUpdate.enable {
				mockedDeps.mockBankActionsDao.EXPECT().Update(gomock.Any(), tt.mockBankActionsUpdate.req, tt.mockBankActionsUpdate.updateMask).Return(tt.mockBankActionsUpdate.err)
			}

			got, err := s.BulkOverrideBankActionState(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("OverrideStatesForBankAction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("OverrideStatesForBankAction() got = %v, \nwant %v", got, tt.want)
			}
		})
	}
}

func TestService_CreateLEAComplaints(t *testing.T) {
	tests := []struct {
		name     string
		req      *riskPb.CreateLEAComplaintRequest
		want     *riskPb.CreateLEAComplaintResponse
		mockFunc func(m *mockedDependencies)
		wantErr  bool
	}{
		{
			name: "internal response from dao",
			req: &riskPb.CreateLEAComplaintRequest{
				LeaComplaint: testLea,
			},
			want: &riskPb.CreateLEAComplaintResponse{
				Status: rpc.StatusInternal(),
			},
			mockFunc: func(m *mockedDependencies) {
				m.mockLeaComplaintsDao.EXPECT().Create(gomock.Any(), testLea).Return(nil, errors.New("some error"))
			},
		},
		{
			name: "case not created for out of cut off date",
			req: &riskPb.CreateLEAComplaintRequest{
				LeaComplaint: testLeaWithDetails,
			},
			want: &riskPb.CreateLEAComplaintResponse{
				Status: rpc.StatusOk(),
			},
			mockFunc: func(m *mockedDependencies) {
				m.mockLeaComplaintsDao.EXPECT().Create(gomock.Any(), testLeaWithDetails).Return(testLeaWithDetails, nil)
				m.mockPublisher.EXPECT().Publish(gomock.Any(), &transaction_monitoring.SyncLeaActorsRequest{ActorIds: []string{actorId1}})
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId1,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  actorFixture1,
				}, nil)
				m.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: userId1,
					},
				}).Return(&savingsPb.GetAccountResponse{Account: savingsFixture1}, nil)
				m.mockBankActionsDao.EXPECT().GetByActor(gomock.Any(), actorId1, gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				m.mockBankActionsDao.EXPECT().Create(gomock.Any(), newRiskBankActionMatcher(bankActionFixtureWithIsRecon)).Return(nil, nil)
				m.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), newInitiateWorkflowMatcher(workflowFixture1)).
					Return(&celestialPb.InitiateWorkflowResponse{
						Status:            rpc.StatusOk(),
						WorkflowRequestId: workflowFixture1.GetClientReqId().GetId(),
					}, nil)
			},
		},
		{
			name: "case created successfullt",
			req: &riskPb.CreateLEAComplaintRequest{
				LeaComplaint: &riskPb.LEAComplaint{
					Id:                       "leaId",
					ActorId:                  actorId1,
					AuditBranchOperatingDate: dummyTimestamp,
					AccountType:              accountPb.Type_SAVINGS,
					AuditDate:                timestamppb.Now(),
					FreezeCode:               riskEnums.AccountFreezeStatus_ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE,
				},
			},
			want: &riskPb.CreateLEAComplaintResponse{
				Status: rpc.StatusOk(),
			},
			mockFunc: func(m *mockedDependencies) {
				m.mockLeaComplaintsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(testLeaWithDetails, nil)
				m.mockPublisher.EXPECT().Publish(gomock.Any(), &transaction_monitoring.SyncLeaActorsRequest{ActorIds: []string{actorId1}})
				m.mockActorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId1,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor:  actorFixture1,
				}, nil)
				m.mockSavingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
						PrimaryUserId: userId1,
					},
				}).Return(&savingsPb.GetAccountResponse{Account: savingsFixture1}, nil)
				m.mockBankActionsDao.EXPECT().GetByActor(gomock.Any(), actorId1, gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
				m.mockBankActionsDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, nil)
				m.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), newInitiateWorkflowMatcher(workflowFixture1)).
					Return(&celestialPb.InitiateWorkflowResponse{
						Status:            rpc.StatusOk(),
						WorkflowRequestId: workflowFixture1.GetClientReqId().GetId(),
					}, nil)
				m.mockCMClient.EXPECT().CreateAlerts(gomock.Any(), &cmPb.CreateAlertsRequest{
					Alerts: []*cmPb.RawAlert{
						{
							ActorId:    testLeaWithDetails.GetActorId(),
							AccountId:  testLeaWithDetails.GetAccountId(),
							EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_USER,
							EntityId:   testLeaWithDetails.GetActorId(),
							Identifier: &cmPb.RuleIdentifier{
								Identifier: &cmPb.RuleIdentifier_ExternalId{
									ExternalId: genConf.LEAComplaint().ReviewExternalRuleId,
								},
							},
						},
					},
				}).Return(&cmPb.CreateAlertsResponse{Status: rpc.StatusOk()}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDeps := newServerWithMocks(t)
			tt.mockFunc(mockedDeps)

			got, err := s.CreateLEAComplaint(context.Background(), tt.req)
			assert.Equal(t, err != nil, tt.wantErr)
			assert.Equal(t, got, tt.want)
		})
	}
}

func TestService_ScreenUser(t *testing.T) {
	ctr := gomock.NewController(t)
	screenerMock := mockScreener.NewMockIUserRiskScreener(ctr)
	defer func() {
		ctr.Finish()
	}()
	type args struct {
		ctx context.Context
		req *riskPb.ScreenUserRequest
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(screenerMock *mockScreener.MockIUserRiskScreener)
		want     *riskPb.ScreenUserResponse
		wantErr  bool
	}{
		{
			name: "failed due to screener error",
			args: args{
				ctx: context.Background(),
				req: &riskPb.ScreenUserRequest{ActorId: "test-actor", ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING},
			},
			mockFunc: func(mockScreener *mockScreener.MockIUserRiskScreener) {
				mockScreener.EXPECT().ScreenUser(context.Background(), "test-actor", riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING).
					Return(riskEnums.ScreenerAction_SCREENER_ACTION_UNSPECIFIED, errors.New("failed"))
			},
			want:    &riskPb.ScreenUserResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "screener is passed",
			args: args{
				ctx: context.Background(),
				req: &riskPb.ScreenUserRequest{ActorId: "test-actor", ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING},
			},
			mockFunc: func(mockScreener *mockScreener.MockIUserRiskScreener) {
				mockScreener.EXPECT().ScreenUser(context.Background(), "test-actor", riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING).
					Return(riskEnums.ScreenerAction_SCREENER_ACTION_PASS, nil)
			},
			want:    &riskPb.ScreenUserResponse{Status: rpc.StatusOk(), ScreenerAction: riskEnums.ScreenerAction_SCREENER_ACTION_PASS},
			wantErr: false,
		},
		{
			name: "screener is failed",
			args: args{
				ctx: context.Background(),
				req: &riskPb.ScreenUserRequest{ActorId: "test-actor", ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING},
			},
			mockFunc: func(mockScreener *mockScreener.MockIUserRiskScreener) {
				mockScreener.EXPECT().ScreenUser(context.Background(), "test-actor", riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING).
					Return(riskEnums.ScreenerAction_SCREENER_ACTION_FAIL, nil)
			},
			want:    &riskPb.ScreenUserResponse{Status: rpc.StatusOk(), ScreenerAction: riskEnums.ScreenerAction_SCREENER_ACTION_FAIL},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				userRiskScreener: screenerMock,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(screenerMock)
			}
			got, err := s.ScreenUser(tt.args.ctx, tt.args.req)
			assert.Equal(t, err != nil, tt.wantErr)
			assert.Equalf(t, tt.want, got, "ScreenUser(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestService_FetchLEAComplaints(t *testing.T) {
	tests := []struct {
		name     string
		req      *riskPb.FetchLEAComplaintsRequest
		want     *riskPb.FetchLEAComplaintsResponse
		mockFunc func(mockComplaintManager *mockLea.MockIComplaintManager)
		wantErr  bool
	}{
		{
			name: "fails with internal response from dao",
			req: &riskPb.FetchLEAComplaintsRequest{
				Identifier: &riskPb.FetchLEAComplaintsRequest_ActorId{
					ActorId: actorId1,
				},
				Limit: 0,
			},
			want: &riskPb.FetchLEAComplaintsResponse{
				Status: rpc.StatusInternal(),
			},
			mockFunc: func(mockComplaintManager *mockLea.MockIComplaintManager) {
				mockComplaintManager.EXPECT().GetByActorId(gomock.Any(), actorId1,
					[]riskPb.ExtendedLEAComplaintFieldMask{riskPb.ExtendedLEAComplaintFieldMask_EXTENDED_LEA_COMPLAINT_FIELD_MASK_SOURCE}).
					Return(nil, errors.New("some error"))
			},
		},
		{
			name: "fails with record not found",
			req: &riskPb.FetchLEAComplaintsRequest{
				Identifier: &riskPb.FetchLEAComplaintsRequest_ActorId{
					ActorId: actorId1,
				},
				Limit: 0,
			},
			want: &riskPb.FetchLEAComplaintsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			mockFunc: func(mockComplaintManager *mockLea.MockIComplaintManager) {
				mockComplaintManager.EXPECT().GetByActorId(gomock.Any(), actorId1,
					[]riskPb.ExtendedLEAComplaintFieldMask{riskPb.ExtendedLEAComplaintFieldMask_EXTENDED_LEA_COMPLAINT_FIELD_MASK_SOURCE}).
					Return(nil, gorm.ErrRecordNotFound)
			},
		},
		{
			name: "fails when no argument passed",
			req: &riskPb.FetchLEAComplaintsRequest{
				Limit: 0,
			},
			want: &riskPb.FetchLEAComplaintsResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "success",
			req: &riskPb.FetchLEAComplaintsRequest{
				Identifier: &riskPb.FetchLEAComplaintsRequest_ActorId{
					ActorId: actorId1,
				},
				Limit: 0,
			},
			want: &riskPb.FetchLEAComplaintsResponse{
				Status:                rpc.StatusOk(),
				ExtendedLeaComplaints: leaArray,
			},
			mockFunc: func(mockComplaintManager *mockLea.MockIComplaintManager) {
				mockComplaintManager.EXPECT().GetByActorId(gomock.Any(), actorId1,
					[]riskPb.ExtendedLEAComplaintFieldMask{riskPb.ExtendedLEAComplaintFieldMask_EXTENDED_LEA_COMPLAINT_FIELD_MASK_SOURCE}).
					Return(leaArray, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockComplaintManager := mockLea.NewMockIComplaintManager(ctr)
			s := &Service{
				leaComplaintManager: mockComplaintManager,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(mockComplaintManager)
			}

			got, err := s.FetchLEAComplaints(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchLEAComplaints() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FetchLEAComplaints() got = %v, \nwant %v", got, tt.want)
			}
		})
	}
}

func TestService_AddLEAComplaintSource(t *testing.T) {
	var (
		leaDetail = &riskPb.LEAComplaintSource{
			ComplaintId: "complaintId",
		}
	)
	tests := []struct {
		name     string
		req      *riskPb.AddLEAComplaintSourceRequest
		want     *riskPb.AddLEAComplaintSourceResponse
		mockFunc func(leaComplaintSourceProcessor *mocks.MockISourceProcessor)
		wantErr  bool
	}{
		{
			name: "fails to process complaint source",
			req: &riskPb.AddLEAComplaintSourceRequest{
				Source: leaDetail,
			},
			want: &riskPb.AddLEAComplaintSourceResponse{
				Status: rpc.StatusInternal(),
			},
			mockFunc: func(leaComplaintSourceProcessor *mocks.MockISourceProcessor) {
				leaComplaintSourceProcessor.EXPECT().Process(gomock.Any(), leaDetail).
					Return(epifierrors.ErrRecordNotFound)
			},
		},
		{
			name: "success",
			req: &riskPb.AddLEAComplaintSourceRequest{
				Source: leaDetail,
			},
			want: &riskPb.AddLEAComplaintSourceResponse{
				Status: rpc.StatusOk(),
			},
			mockFunc: func(leaComplaintSourceProcessor *mocks.MockISourceProcessor) {
				leaComplaintSourceProcessor.EXPECT().Process(gomock.Any(), leaDetail).
					Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockLEAComplaintSourceProcessor := mocks.NewMockISourceProcessor(ctr)
			s := &Service{
				leaComplaintSourceProcessor: mockLEAComplaintSourceProcessor,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(mockLEAComplaintSourceProcessor)
			}

			got, err := s.AddLEAComplaintSource(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddLEAComplaintSource() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddLEAComplaintSource() got = %v, \nwant %v", got, tt.want)
			}
		})
	}
}

func TestService_GetScreenerCheckResults(t *testing.T) {
	ctr := gomock.NewController(t)
	riskDataDaoMock := mockRiskDao.NewMockRiskDataDao(ctr)
	riskCheckManagerMock := mock_checkmanager.NewMockCheckManager(ctr)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		ctx context.Context
		req *riskPb.GetScreenerCheckResultsRequest
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(riskDataDaoMock *mockRiskDao.MockRiskDataDao, manager *mock_checkmanager.MockCheckManager)
		want     *riskPb.GetScreenerCheckResultsResponse
		wantErr  bool
	}{
		{
			name: "fails due to error in risk data dao",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerCheckResultsRequest{
					Identifier: &riskPb.GetScreenerCheckResultsRequest_ActorId{
						ActorId: "test-actor",
					},
				},
			},
			mockFunc: func(riskDataDaoMock *mockRiskDao.MockRiskDataDao, manager *mock_checkmanager.MockCheckManager) {
				riskDataDaoMock.EXPECT().GetByActorId(context.Background(), "test-actor", gomock.Any()).
					Return(nil, errors.New("failed"))
			},
			want: &riskPb.GetScreenerCheckResultsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "fails with record not found in risk data dao",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerCheckResultsRequest{
					Identifier: &riskPb.GetScreenerCheckResultsRequest_ActorId{
						ActorId: "test-actor",
					},
				},
			},
			mockFunc: func(riskDataDaoMock *mockRiskDao.MockRiskDataDao, manager *mock_checkmanager.MockCheckManager) {
				riskDataDaoMock.EXPECT().GetByActorId(context.Background(), "test-actor", gomock.Any()).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &riskPb.GetScreenerCheckResultsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerCheckResultsRequest{
					Identifier: &riskPb.GetScreenerCheckResultsRequest_ActorId{
						ActorId: "test-actor",
					},
				},
			},
			mockFunc: func(riskDataDaoMock *mockRiskDao.MockRiskDataDao, manager *mock_checkmanager.MockCheckManager) {
				riskDataDaoMock.EXPECT().GetByActorId(context.Background(), "test-actor", gomock.Any()).
					Return([]*riskPb.RiskData{
						{Id: "1", RiskParam: riskPb.RiskParam_RISK_PARAM_UPI_VPA_NAME_MATCH},
						{Id: "2", RiskParam: riskPb.RiskParam_RISK_PARAM_EMAIL_ID}}, nil)
				manager.EXPECT().AreAdditionalDetailsAvailable(context.Background(), riskPb.RiskParam_RISK_PARAM_UPI_VPA_NAME_MATCH).Return(true)
				manager.EXPECT().AreAdditionalDetailsAvailable(context.Background(), riskPb.RiskParam_RISK_PARAM_EMAIL_ID).Return(false)
			},
			want: &riskPb.GetScreenerCheckResultsResponse{
				Status: rpc.StatusOk(),
				RiskCheckResults: []*riskPb.GetScreenerCheckResultsResponse_CheckResult{
					{
						Data:                          &riskPb.RiskData{Id: "1", RiskParam: riskPb.RiskParam_RISK_PARAM_UPI_VPA_NAME_MATCH},
						AreAdditionalDetailsAvailable: true,
					},
					{
						Data:                          &riskPb.RiskData{Id: "2", RiskParam: riskPb.RiskParam_RISK_PARAM_EMAIL_ID},
						AreAdditionalDetailsAvailable: false,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				riskDataDao:  riskDataDaoMock,
				checkManager: riskCheckManagerMock,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(riskDataDaoMock, riskCheckManagerMock)
			}
			got, err := s.GetScreenerCheckResults(tt.args.ctx, tt.args.req)
			assert.Equal(t, err != nil, tt.wantErr)
			assert.Equalf(t, tt.want, got, "GetScreenerCheckResults(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestService_FetchDynamicElements(t *testing.T) {
	ctr := gomock.NewController(t)
	dynamicElementProviderFactoryMock := mock_dynamicelements.NewMockProviderFactory(ctr)
	dynamicElementProviderMock := mock_dynamicelements.NewMockProvider(ctr)
	leaComplaintManagerMock := mockLea.NewMockIComplaintManager(ctr)
	unifiedLEAMock := mockUnifiedLea.NewMockIUnifiedLEAComplaintManager(ctr)
	mockAccountStatusFetcher := mock_accountstatus.NewMockFetcher(ctr)
	defer ctr.Finish()

	type args struct {
		ctx context.Context
		req *dynamicElementsPb.FetchDynamicElementsRequest
	}
	tests := []struct {
		name    string
		args    args
		mocks   []interface{}
		want    *dynamicElementsPb.FetchDynamicElementsResponse
		wantErr bool
	}{
		{
			name: "failed due to unexpected error while fetching providers",
			args: args{
				ctx: context.Background(),
				req: dynamicElementReqForHome,
			},
			mocks: []interface{}{
				unifiedLEAMock.EXPECT().GetUnifiedLeaByActorId(context.Background(), dynamicElementReqForHome.GetActorId(), 1, gomock.Any()).
					Return([]*leaPb.UnifiedLeaComplaint{}, nil),
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(context.Background(), dynamicElementReqForHome.GetActorId(), riskEnums.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(&accountStatus.OperationalDetails{}, nil),
				dynamicElementProviderFactoryMock.EXPECT().GetProvidersForScreen(context.Background(), dynamicElementReqForHome.GetClientContext().GetScreenName(), additionalDetails).
					Return(nil, errors.New("failed")),
			},
			want:    &dynamicElementsPb.FetchDynamicElementsResponse{Status: rpc.StatusInternal()},
			wantErr: false,
		},
		{
			name: "success with method not implemented error while fetching providers",
			args: args{
				ctx: context.Background(),
				req: dynamicElementReqForHome,
			},
			mocks: []interface{}{
				unifiedLEAMock.EXPECT().GetUnifiedLeaByActorId(context.Background(), dynamicElementReqForHome.GetActorId(), 1, gomock.Any()).
					Return([]*leaPb.UnifiedLeaComplaint{}, nil),
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(context.Background(), dynamicElementReqForHome.GetActorId(), riskEnums.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(&accountStatus.OperationalDetails{}, nil),
				dynamicElementProviderFactoryMock.EXPECT().GetProvidersForScreen(context.Background(), dynamicElementReqForHome.GetClientContext().GetScreenName(), additionalDetails).
					Return(nil, epifierrors.ErrMethodUnimplemented),
			},
			want:    &dynamicElementsPb.FetchDynamicElementsResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "success with empty response due to error in provider method",
			args: args{
				ctx: context.Background(),
				req: dynamicElementReqForHome,
			},
			mocks: []interface{}{
				unifiedLEAMock.EXPECT().GetUnifiedLeaByActorId(context.Background(), dynamicElementReqForHome.GetActorId(), 1, gomock.Any()).
					Return([]*leaPb.UnifiedLeaComplaint{}, nil),
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(context.Background(), dynamicElementReqForHome.GetActorId(), riskEnums.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(&accountStatus.OperationalDetails{}, nil),
				dynamicElementProviderFactoryMock.EXPECT().GetProvidersForScreen(context.Background(), dynamicElementReqForHome.GetClientContext().GetScreenName(), additionalDetails).
					Return([]dynamicelements.Provider{dynamicElementProviderMock}, nil),
				dynamicElementProviderMock.EXPECT().GetDynamicElements(context.Background(),
					dynamicElementReqForHome.GetActorId(), dynamicElementReqForHome.GetClientContext(), additionalDetails).
					Return(nil, errors.New("failed")),
			},
			want:    &dynamicElementsPb.FetchDynamicElementsResponse{Status: rpc.StatusOk()},
			wantErr: false,
		},
		{
			name: "success with dynamic elements in resp",
			args: args{
				ctx: context.Background(),
				req: dynamicElementReqForHome,
			},
			mocks: []interface{}{
				unifiedLEAMock.EXPECT().GetUnifiedLeaByActorId(context.Background(), dynamicElementReqForHome.GetActorId(), 1, gomock.Any()).
					Return([]*leaPb.UnifiedLeaComplaint{}, nil),
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(context.Background(), dynamicElementReqForHome.GetActorId(), riskEnums.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(&accountStatus.OperationalDetails{}, nil),
				dynamicElementProviderFactoryMock.EXPECT().GetProvidersForScreen(context.Background(), dynamicElementReqForHome.GetClientContext().GetScreenName(), additionalDetails).
					Return([]dynamicelements.Provider{dynamicElementProviderMock}, nil),
				dynamicElementProviderMock.EXPECT().GetDynamicElements(context.Background(),
					dynamicElementReqForHome.GetActorId(), dynamicElementReqForHome.GetClientContext(), additionalDetails).
					Return([]*dynamicElementsPb.DynamicElement{homeFreezeBanner}, nil),
			},
			want: &dynamicElementsPb.FetchDynamicElementsResponse{
				Status:       rpc.StatusOk(),
				ElementsList: []*dynamicElementsPb.DynamicElement{homeFreezeBanner},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			staticConf, _ := config.Load()
			// Init Dynamic Config
			confGen, err := genconf.Load()
			if err != nil {
				log.Fatal("failed to load dynamic config", err)
			}
			s := &Service{
				dynamicElementProviderFactory: dynamicElementProviderFactoryMock,
				leaComplaintManager:           leaComplaintManagerMock,
				genConf:                       confGen,
				conf:                          staticConf,
				accountStatusFetcher:          mockAccountStatusFetcher,
				unifiedLEAComplaintManager:    unifiedLEAMock,
			}
			got, err := s.FetchDynamicElements(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchDynamicElements() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "FetchDynamicElements(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestService_ProcessLEAComplaintNarration(t *testing.T) {
	var (
		ctx = context.Background()
		req = &riskPb.ProcessLEAComplaintNarrationRequest{
			LeaComplaintNarrationDetails: &riskPb.LEAComplaintNarrationDetails{
				Narration: &riskPb.LEAComplaintNarration{
					Narration: "narration",
				},
				MOs: []*reviewPb.AllowedAnnotation{
					{
						Id: "id",
					},
				},
			},
			AnalystEmail: "analystEmail",
		}
	)

	type args struct {
		req *riskPb.ProcessLEAComplaintNarrationRequest
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(narrationProcessor *mocks_narration.MockIProcessor)
		want     *riskPb.ProcessLEAComplaintNarrationResponse
		wantErr  bool
	}{
		{
			name: "fails to process narration",
			args: args{
				req: req,
			},
			mockFunc: func(narrationProcessor *mocks_narration.MockIProcessor) {
				narrationProcessor.EXPECT().Process(ctx, &narration.ProcessNarrationRequest{
					NarrationDetails: req.GetLeaComplaintNarrationDetails(),
					AnalystEmail:     req.GetAnalystEmail(),
				}).Return(epifierrors.ErrInvalidArgument)
			},
			want: &riskPb.ProcessLEAComplaintNarrationResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				req: req,
			},
			mockFunc: func(narrationProcessor *mocks_narration.MockIProcessor) {
				narrationProcessor.EXPECT().Process(ctx, &narration.ProcessNarrationRequest{
					NarrationDetails: req.GetLeaComplaintNarrationDetails(),
					AnalystEmail:     req.GetAnalystEmail(),
				}).Return(nil)
			},
			want: &riskPb.ProcessLEAComplaintNarrationResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer func() {
				ctr.Finish()
			}()
			narrationProcessor := mocks_narration.NewMockIProcessor(ctr)
			s := &Service{
				leaComplaintNarrationProcessor: narrationProcessor,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(narrationProcessor)
			}
			got, err := s.ProcessLEAComplaintNarration(ctx, tt.args.req)
			assert.Equal(t, err != nil, tt.wantErr)
			assert.Equalf(t, tt.want, got, "ProcessLEAComplaintNarration(%v, %v)", ctx, tt.args.req)
		})
	}
}

func TestService_ScreenActor(t *testing.T) {
	type args struct {
		ctx context.Context
		req *riskPb.ScreenActorRequest
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(mockActorScreener *mockScreener.MockActorScreener)
		want     *riskPb.ScreenActorResponse
		wantErr  assert.ErrorAssertionFunc
	}{
		{
			name: "failed due to error in actor screener",
			args: args{
				ctx: context.Background(),
				req: &riskPb.ScreenActorRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING,
					ClientRequestId:  "test-1",
				},
			},
			mockFunc: func(mockActorScreener *mockScreener.MockActorScreener) {
				mockActorScreener.EXPECT().Screen(context.Background(), &params.ScreenActorParams{
					ActorID:     actorId1,
					Criteria:    riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING,
					ClientReqID: "test-1",
				}).Return(nil, fmt.Errorf("failed"))
			},
			want:    &riskPb.ScreenActorResponse{Status: rpc.StatusInternal()},
			wantErr: assert.NoError,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &riskPb.ScreenActorRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING,
					ClientRequestId:  "test-1",
				},
			},
			mockFunc: func(mockActorScreener *mockScreener.MockActorScreener) {
				mockActorScreener.EXPECT().Screen(context.Background(), &params.ScreenActorParams{
					ActorID:     actorId1,
					Criteria:    riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING,
					ClientReqID: "test-1",
				}).Return(&screenerPb.ScreenerAttempt{
					Id:     "1",
					Status: screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
				}, nil)
			},
			want: &riskPb.ScreenActorResponse{
				Status:         rpc.StatusOk(),
				AttemptId:      "1",
				ScreenerStatus: screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
				Verdict:        screenerPb.Verdict_VERDICT_UNSPECIFIED,
			},
			wantErr: assert.NoError,
		},
		{
			name: "success with risk flags",
			args: args{
				ctx: context.Background(),
				req: &riskPb.ScreenActorRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING,
					ClientRequestId:  "test-1",
				},
			},
			mockFunc: func(mockActorScreener *mockScreener.MockActorScreener) {
				mockActorScreener.EXPECT().Screen(context.Background(), &params.ScreenActorParams{
					ActorID:     actorId1,
					Criteria:    riskEnums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING,
					ClientReqID: "test-1",
				}).Return(&screenerPb.ScreenerAttempt{
					Id:                 "1",
					Status:             screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
					PotentialRiskFlags: []screenerPb.PotentialRiskFlag{screenerPb.PotentialRiskFlag_POTENTIAL_RISK_FLAG_ASSOCIATION_WITH_BAD_ACTOR},
				}, nil)
			},
			want: &riskPb.ScreenActorResponse{
				Status:             rpc.StatusOk(),
				AttemptId:          "1",
				ScreenerStatus:     screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
				Verdict:            screenerPb.Verdict_VERDICT_UNSPECIFIED,
				PotentialRiskFlags: []screenerPb.PotentialRiskFlag{screenerPb.PotentialRiskFlag_POTENTIAL_RISK_FLAG_ASSOCIATION_WITH_BAD_ACTOR},
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockActorScreener := mockScreener.NewMockActorScreener(ctr)
			s := &Service{
				actorRiskScreener: mockActorScreener,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(mockActorScreener)
			}
			got, err := s.ScreenActor(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("ScreenActor(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "ScreenActor(%v, %v)", tt.args.ctx, tt.args.req)
			ctr.Finish()
		})
	}
}

func TestService_GetScreenerAttemptStatus(t *testing.T) {
	type args struct {
		ctx context.Context
		req *riskPb.GetScreenerAttemptStatusRequest
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(mockActorScreener *mockScreener.MockActorScreener)
		want     *riskPb.GetScreenerAttemptStatusResponse
		wantErr  assert.ErrorAssertionFunc
	}{
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerAttemptStatusRequest{AttemptIdentifier: attemptIdentifier1},
			},
			mockFunc: func(mockActorScreener *mockScreener.MockActorScreener) {
				mockActorScreener.EXPECT().GetValidAttempt(context.Background(), attemptIdentifier1).Return(&screenerPb.ScreenerAttempt{
					Id:     "1",
					Status: screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
				}, nil)
			},
			want: &riskPb.GetScreenerAttemptStatusResponse{
				Status:         rpc.StatusOk(),
				ScreenerStatus: screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
			},
			wantErr: assert.NoError,
		},
		{
			name: "success with flags",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerAttemptStatusRequest{AttemptIdentifier: attemptIdentifier1},
			},
			mockFunc: func(mockActorScreener *mockScreener.MockActorScreener) {
				mockActorScreener.EXPECT().GetValidAttempt(context.Background(), attemptIdentifier1).Return(&screenerPb.ScreenerAttempt{
					Id:                 "1",
					Status:             screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
					PotentialRiskFlags: []screenerPb.PotentialRiskFlag{screenerPb.PotentialRiskFlag_POTENTIAL_RISK_FLAG_ASSOCIATION_WITH_BAD_ACTOR},
				}, nil)
			},
			want: &riskPb.GetScreenerAttemptStatusResponse{
				Status:             rpc.StatusOk(),
				ScreenerStatus:     screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
				PotentialRiskFlags: []screenerPb.PotentialRiskFlag{screenerPb.PotentialRiskFlag_POTENTIAL_RISK_FLAG_ASSOCIATION_WITH_BAD_ACTOR},
			},
			wantErr: assert.NoError,
		},
		{
			name: "failed with record not found",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerAttemptStatusRequest{AttemptIdentifier: attemptIdentifier1},
			},
			mockFunc: func(mockActorScreener *mockScreener.MockActorScreener) {
				mockActorScreener.EXPECT().GetValidAttempt(context.Background(), attemptIdentifier1).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &riskPb.GetScreenerAttemptStatusResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "failed with internal",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerAttemptStatusRequest{AttemptIdentifier: attemptIdentifier1},
			},
			mockFunc: func(mockActorScreener *mockScreener.MockActorScreener) {
				mockActorScreener.EXPECT().GetValidAttempt(context.Background(), attemptIdentifier1).Return(nil, fmt.Errorf("failed"))
			},
			want: &riskPb.GetScreenerAttemptStatusResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockActorScreener := mockScreener.NewMockActorScreener(ctr)
			s := &Service{
				actorRiskScreener: mockActorScreener,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(mockActorScreener)
			}
			got, err := s.GetScreenerAttemptStatus(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("GetScreenerAttemptStatus(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetScreenerAttemptStatus(%v, %v)", tt.args.ctx, tt.args.req)
			ctr.Finish()
		})
	}
}

func TestService_GetScreenerCheckDetails(t *testing.T) {
	var (
		result1 = &riskPb.RiskData{
			Id: "1",
		}
		additionaCheckDetails1 = &screenerPb.AdditionalCheckDetails{
			Details: nil,
		}
	)
	type args struct {
		ctx context.Context
		req *riskPb.GetScreenerCheckDetailsRequest
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(mockCheckManager *mock_checkmanager.MockCheckManager)
		want     *riskPb.GetScreenerCheckDetailsResponse
		wantErr  assert.ErrorAssertionFunc
	}{
		{
			name: "record not found error from check manager",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerCheckDetailsRequest{
					ResultId: "test-1",
				},
			},
			mockFunc: func(mockCheckManager *mock_checkmanager.MockCheckManager) {
				mockCheckManager.EXPECT().GetResultDetails(context.Background(), "test-1").Return(nil, nil, epifierrors.ErrRecordNotFound)
			},
			want: &riskPb.GetScreenerCheckDetailsResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "error from check manager",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerCheckDetailsRequest{
					ResultId: "test-1",
				},
			},
			mockFunc: func(mockCheckManager *mock_checkmanager.MockCheckManager) {
				mockCheckManager.EXPECT().GetResultDetails(context.Background(), "test-1").Return(nil, nil, fmt.Errorf("failed"))
			},
			want: &riskPb.GetScreenerCheckDetailsResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerCheckDetailsRequest{
					ResultId: "test-1",
				},
			},
			mockFunc: func(mockCheckManager *mock_checkmanager.MockCheckManager) {
				mockCheckManager.EXPECT().GetResultDetails(context.Background(), "test-1").Return(result1, additionaCheckDetails1, nil)
			},
			want: &riskPb.GetScreenerCheckDetailsResponse{
				Status:                 rpc.StatusOk(),
				CheckResult:            result1,
				AdditionalCheckDetails: additionaCheckDetails1,
			},
			wantErr: assert.NoError,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &riskPb.GetScreenerCheckDetailsRequest{
					ResultId: "test-1",
				},
			},
			mockFunc: func(mockCheckManager *mock_checkmanager.MockCheckManager) {
				mockCheckManager.EXPECT().GetResultDetails(context.Background(), "test-1").Return(result1, additionaCheckDetails1, nil)
			},
			want: &riskPb.GetScreenerCheckDetailsResponse{
				Status:                 rpc.StatusOk(),
				CheckResult:            result1,
				AdditionalCheckDetails: additionaCheckDetails1,
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockCheckManager := mock_checkmanager.NewMockCheckManager(ctr)

			s := &Service{
				checkManager: mockCheckManager,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(mockCheckManager)
			}
			got, err := s.GetScreenerCheckDetails(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("GetScreenerCheckDetails(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetScreenerCheckDetails(%v, %v)", tt.args.ctx, tt.args.req)
			ctr.Finish()
		})
	}
}

func TestService_PassRiskScreenerAttempt(t *testing.T) {
	ctr := gomock.NewController(t)
	mockTxnExecutor := storagev2Mocks.NewMockTxnExecutor(ctr)
	defer func() {
		ctr.Finish()
	}()
	type fields struct {
		mockTxnExecutor *storagev2Mocks.MockTxnExecutor
	}
	type args struct {
		ctx   context.Context
		req   *riskPb.PassRiskScreenerAttemptRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *riskPb.PassRiskScreenerAttemptResponse
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "failed with record not found",
			fields: fields{
				mockTxnExecutor: mockTxnExecutor,
			},
			args: args{
				ctx: context.Background(),
				req: &riskPb.PassRiskScreenerAttemptRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					AnalystEmail:     analystEmailId,
					ManualPassReason: "",
				},
				mocks: []interface{}{
					mockTxnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(epifierrors.ErrRecordNotFound),
				},
			},
			want:    &riskPb.PassRiskScreenerAttemptResponse{Status: rpc.StatusRecordNotFound()},
			wantErr: assert.NoError,
		},
		{
			name: "failed with internal error",
			fields: fields{
				mockTxnExecutor: mockTxnExecutor,
			},
			args: args{
				ctx: context.Background(),
				req: &riskPb.PassRiskScreenerAttemptRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					AnalystEmail:     analystEmailId,
					ManualPassReason: "",
				},
				mocks: []interface{}{
					mockTxnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(fmt.Errorf("failed with internal")),
				},
			},
			want:    &riskPb.PassRiskScreenerAttemptResponse{Status: rpc.StatusInternal()},
			wantErr: assert.NoError,
		},
		{
			name: "success",
			fields: fields{
				mockTxnExecutor: mockTxnExecutor,
			},
			args: args{
				ctx: context.Background(),
				req: &riskPb.PassRiskScreenerAttemptRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					AnalystEmail:     analystEmailId,
					ManualPassReason: "",
				},
				mocks: []interface{}{
					mockTxnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil),
				},
			},
			want:    &riskPb.PassRiskScreenerAttemptResponse{Status: rpc.StatusOk()},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				frmPgdbTxnExecutor: tt.fields.mockTxnExecutor,
			}
			got, err := s.PassRiskScreenerAttempt(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("PassRiskScreenerAttempt(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "PassRiskScreenerAttempt(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}

func TestService_passScreenerAttempt(t *testing.T) {
	ctx := context.Background()
	ctr := gomock.NewController(t)
	mockScreenerAttemptDao := mockRiskDao.NewMockScreenerAttemptDao(ctr)
	type fields struct {
		mockScreenerAttemptDao *mockRiskDao.MockScreenerAttemptDao
	}
	type args struct {
		ctx   context.Context
		req   *riskPb.PassRiskScreenerAttemptRequest
		mocks []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr assert.ErrorAssertionFunc
	}{
		{
			name: "failed to fetch screener attempt",
			fields: fields{
				mockScreenerAttemptDao: mockScreenerAttemptDao,
			},
			args: args{
				ctx: context.Background(),
				req: &riskPb.PassRiskScreenerAttemptRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					AnalystEmail:     analystEmailId,
				},
				mocks: []interface{}{
					mockScreenerAttemptDao.EXPECT().GetByActorIdAndCriteria(ctx, actorId1, riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING).Return(nil, fmt.Errorf("failed to fetch screener attempt")),
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "failed attempt status is in processing",
			fields: fields{
				mockScreenerAttemptDao: mockScreenerAttemptDao,
			},
			args: args{
				ctx: context.Background(),
				req: &riskPb.PassRiskScreenerAttemptRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					AnalystEmail:     analystEmailId,
				},
				mocks: []interface{}{
					mockScreenerAttemptDao.EXPECT().GetByActorIdAndCriteria(ctx, actorId1, riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING).Return(&screenerPb.ScreenerAttempt{
						ActorId:  actorId1,
						Criteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
						Status:   screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
					}, nil),
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "failed due to verdict attempt already pass",
			fields: fields{
				mockScreenerAttemptDao: mockScreenerAttemptDao,
			},
			args: args{
				ctx: context.Background(),
				req: &riskPb.PassRiskScreenerAttemptRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					AnalystEmail:     analystEmailId,
				},
				mocks: []interface{}{
					mockScreenerAttemptDao.EXPECT().GetByActorIdAndCriteria(ctx, actorId1, riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING).Return(screenerAttempt2, nil),
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "failed to update screener attempt",
			fields: fields{
				mockScreenerAttemptDao: mockScreenerAttemptDao,
			},
			args: args{
				ctx: context.Background(),
				req: &riskPb.PassRiskScreenerAttemptRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					AnalystEmail:     analystEmailId,
					ManualPassReason: "manual reason to pass this attempt",
				},
				mocks: []interface{}{
					mockScreenerAttemptDao.EXPECT().GetByActorIdAndCriteria(ctx, actorId1, riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING).Return(screenerAttempt1, nil),
					mockScreenerAttemptDao.EXPECT().Update(ctx, screenerAttempt2, []screenerPb.ScreenerAttemptFieldMask{
						screenerPb.ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_VERDICT,
						screenerPb.ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_VERDICT_DETAILS,
					}).Return(nil, fmt.Errorf("failed to update screener attempt")),
				},
			},
			wantErr: assert.Error,
		},
		{
			name: "successfully pass screener attempt",
			fields: fields{
				mockScreenerAttemptDao: mockScreenerAttemptDao,
			},
			args: args{
				ctx: context.Background(),
				req: &riskPb.PassRiskScreenerAttemptRequest{
					ActorId:          actorId1,
					ScreenerCriteria: riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					AnalystEmail:     analystEmailId,
					ManualPassReason: "manual reason to pass this attempt",
				},
				mocks: []interface{}{
					mockScreenerAttemptDao.EXPECT().GetByActorIdAndCriteria(ctx, actorId1, riskEnums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING).Return(screenerAttempt3, nil),
					mockScreenerAttemptDao.EXPECT().Update(ctx, screenerAttempt2, []screenerPb.ScreenerAttemptFieldMask{
						screenerPb.ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_VERDICT,
						screenerPb.ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_VERDICT_DETAILS,
					}).Return(screenerAttempt2, nil),
				},
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				screenerAttemptDao: tt.fields.mockScreenerAttemptDao,
			}
			tt.wantErr(t, s.passScreenerAttempt(tt.args.ctx, tt.args.req), fmt.Sprintf("passScreenerAttempt(%v, %v)", tt.args.ctx, tt.args.req))
		})
	}
}
