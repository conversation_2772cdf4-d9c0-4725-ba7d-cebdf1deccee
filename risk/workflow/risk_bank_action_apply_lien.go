package workflow

// nolint: goimports
import (
	"fmt"
	"time"

	"github.com/google/uuid"

	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	protoJson "google.golang.org/protobuf/encoding/protojson"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	epifiTemporal "github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"

	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/risk"
	activityPb "github.com/epifi/gamma/api/risk/activity"
	enumsPb "github.com/epifi/gamma/api/risk/enums"
	riskWorkflowPb "github.com/epifi/gamma/api/risk/workflow"
	vgLien "github.com/epifi/gamma/api/vendorgateway/openbanking/lien"
)

const (
	sleepTimeInSeconds = 600
)

// RiskBankActionApplyLien defines a workflow for applying a lien on a savings account
// It first waits for a random period between 0-2 hours
// Then it uses the lien manager to add a lien on the account
// Finally it polls the lien status periodically with exponential backoff to confirm it's been applied
//
//nolint:dupl,funlen
func RiskBankActionApplyLien(ctx workflow.Context, _ *riskWorkflowPb.ApplyLienRequest) (*riskWorkflowPb.ApplyLienResponse, error) {
	var (
		workflowStageStatus stagePb.Status
		err                 error
	)
	lg := workflow.GetLogger(ctx)

	applyLienRequest, err := getApplyLienReqParams(ctx)
	if err != nil {
		lg.Error("error while getting workflow processing req params", zap.Error(err))
		return &riskWorkflowPb.ApplyLienResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: workflowPb.StatusInternal(),
			},
		}, err
	}

	// --------------- ADD TO BANK ACTION STAGE --------------------
	workflowStageStatus, actionId, err := addToBankAction(ctx, &activityPb.AddToBankActionRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		WorkflowId:    workflow.GetInfo(ctx).WorkflowExecution.ID,
		Action:        enumsPb.Action_ACTION_LIEN,
		CommsTemplate: applyLienRequest.GetParameters().GetCommsTemplate(),
		ActorId:       applyLienRequest.GetActorId(),
		RequestReason: &risk.RequestReason{
			Reason:  applyLienRequest.GetParameters().GetRequestReason().GetReason(),
			Remarks: applyLienRequest.GetParameters().GetRequestReason().GetRemarks(),
		},
	}, riskNs.RiskBankActionApplyLienAddToBankAction)
	if err != nil {
		return &riskWorkflowPb.ApplyLienResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "add to bank action stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.ApplyLienResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- ADD LIEN STAGE --------------------
	channelRequestId := fmt.Sprintf("EPIFI-%s", uuid.New().String())
	workflowStageStatus, err = addLien(ctx, &activityPb.AddLienRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		AccountNumber:       applyLienRequest.GetAccountNumber(),
		Amount:              float32(applyLienRequest.GetParameters().GetLienAmount().GetUnits()),
		CurrencyCode:        applyLienRequest.GetParameters().GetLienAmount().GetCurrencyCode(),
		ReasonCode:          vgLien.Reason_REASON_UAT.String(),
		Remarks:             applyLienRequest.GetParameters().GetRequestReason().GetRemarks(),
		LienDurationInHours: applyLienRequest.GetParameters().GetLienDurationInHours(),
		ChannelRequestId:    channelRequestId,
		BankActionId:        actionId,
	}, riskNs.RiskBankActionApplyLienAddLien, applyLienRequest.GetWorkflowId())
	if err != nil {
		return &riskWorkflowPb.ApplyLienResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "add lien stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.ApplyLienResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	sleepLienWorkflow(ctx)

	// --------------- VERIFY LIEN STAGE --------------------
	workflowStageStatus, err = verifyLien(ctx, &activityPb.VerifyLienRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		AccountNumber: applyLienRequest.GetAccountNumber(),
		RequestId:     channelRequestId,
	}, riskNs.RiskBankActionApplyLienVerifyLien, applyLienRequest.GetWorkflowId())
	if err != nil {
		return &riskWorkflowPb.ApplyLienResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "verify lien stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.ApplyLienResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- SEND COMMS STAGE --------------------
	workflowStageStatus, err = sendComms(ctx, &activityPb.GetCommsRequest{
		AccountNumber: applyLienRequest.GetAccountNumber(),
		Amount:        applyLienRequest.GetParameters().GetLienAmount(),
	}, riskNs.RiskBankActionApplyLienSendComms)
	if err != nil {
		return &riskWorkflowPb.ApplyLienResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "send comms stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.ApplyLienResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// Return success response
	return &riskWorkflowPb.ApplyLienResponse{
		ResponseHeader: &workflowPb.ResponseHeader{
			Status: workflowPb.StatusOk(),
		},
	}, nil
}

func sleepLienWorkflow(ctx workflow.Context) {
	sleepDuration := time.Duration(sleepTimeInSeconds) * time.Second
	_ = workflow.Sleep(ctx, sleepDuration)
}

func addLien(ctx workflow.Context, req *activityPb.AddLienRequest, stage epifiTemporal.Stage, bankActionClientReqId string) (stagePb.Status, error) {
	if err := celestialPkg.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStage activity failure: %w", err)
	}

	// Call the AddLien activity
	addLienRes := &activityPb.AddLienResponse{}
	err := activityPkg.Execute(ctx, riskNs.AddLien, addLienRes, req)

	if err != nil {
		updateBankActionRes := &activityPb.UpdateBankActionResponse{}
		updateBankActionErr := activityPkg.Execute(ctx, riskNs.UpdateBankAction, updateBankActionRes, &activityPb.UpdateBankActionRequest{
			ClientReqId: bankActionClientReqId,
			State:       enumsPb.State_STATE_FAILED,
		})
		workflowStageStatus := celestialPkg.GetWorkflowStageStatusForErr(updateBankActionErr)

		if updateStageErr := celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus); updateStageErr != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, updateStageErr
		}

		// Return the original error to prevent masking the AddLien activity failure
		return workflowStageStatus, err
	}

	workflowStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)

	if err = celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, err
	}

	return workflowStageStatus, nil
}

// nolint: staticcheck
func verifyLien(ctx workflow.Context, req *activityPb.VerifyLienRequest, stage epifiTemporal.Stage, bankActionClientReqId string) (stagePb.Status, error) {
	if err := celestialPkg.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStage activity failure: %w", err)
	}

	verifyLienResponse := &activityPb.VerifyLienResponse{}
	err := activityPkg.Execute(ctx, riskNs.VerifyLien, verifyLienResponse, req)

	updateBankActionRes := &activityPb.UpdateBankActionResponse{}
	var updateBankActionErr error
	switch {
	case err == nil:
		updateBankActionErr = activityPkg.Execute(ctx, riskNs.UpdateBankAction, updateBankActionRes, &activityPb.UpdateBankActionRequest{
			ClientReqId: bankActionClientReqId,
			State:       enumsPb.State_STATE_SUCCESS,
		})
	default:
		updateBankActionErr = activityPkg.Execute(ctx, riskNs.UpdateBankAction, updateBankActionRes, &activityPb.UpdateBankActionRequest{
			ClientReqId: bankActionClientReqId,
			State:       enumsPb.State_STATE_MANUAL_INTERVENTION,
		})
	}

	workflowStageStatus := celestialPkg.GetWorkflowStageStatusForErr(updateBankActionErr)
	if err = celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, err
	}

	return workflowStageStatus, nil
}

// nolint: staticcheck
func sendComms(ctx workflow.Context, req *activityPb.GetCommsRequest, stage epifiTemporal.Stage) (stagePb.Status, error) {
	if err := celestialPkg.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("InitiateWorkflowStage activity failure: %w", err)
	}

	// Get communications from lien manager
	getCommsResponse := &activityPb.GetCommsResponse{}
	err := activityPkg.Execute(ctx, riskNs.GetLienComms, getCommsResponse, req)
	workflowStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)

	if err != nil {
		if err = celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus); err != nil {
			return stagePb.Status_STATUS_UNSPECIFIED, err
		}
		return workflowStageStatus, err
	}

	communications := getCommsResponse.GetCommunications()
	if communications != nil && len(communications) != 0 {
		// sends multiple notifications to the user simultaneously
		err = activityPkg.Execute(ctx, epifiTemporal.SendNotification, &notificationPb.SendNotificationResponse{}, &notificationPb.SendNotificationRequest{
			Notifications: &notificationPb.Notification{
				UserIdentifier: &notificationPb.Notification_UserId{
					UserId: getCommsResponse.GetEntityId(),
				},
				CommunicationList: communications,
				QualityOfService:  commsPb.QoS_GUARANTEED,
			},
		})
	}

	workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
	if err = celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, err
	}

	return stagePb.Status_SUCCESSFUL, nil
}

func getApplyLienReqParams(ctx workflow.Context) (*riskWorkflowPb.ApplyLienRequest, error) {
	wfProcessingParams, err := getWorkflowProcessingParams1(ctx)
	if err != nil {
		return nil, fmt.Errorf("error while getting workflow processing params %w", err)
	}

	workflowReq := &riskWorkflowPb.ApplyLienRequest{}
	if err = (protoJson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(wfProcessingParams.GetPayload(), workflowReq); err != nil {
		return nil, fmt.Errorf("failed to unmarshal payload %w", err)
	}

	return workflowReq, nil
}

func getWorkflowProcessingParams1(ctx workflow.Context) (*workflowPb.ProcessingParams, error) {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	wfProcessingParams := &celestialActivityPb.GetWorkflowProcessingParamsV2Response{}
	if err := activityPkg.Execute(ctx, epifiTemporal.GetWorkflowProcessingParamsV2, wfProcessingParams, &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
		RequestHeader: &celestialActivityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		WfReqId: wfReqID,
	}); err != nil {
		return nil, errors.Wrap(err, "error while getting workflow processing params")
	}

	return wfProcessingParams.GetWfReqParams(), nil
}
