//nolint:dupl,govet
package workflow

import (
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	activityPb "github.com/epifi/gamma/api/risk/activity"
	riskWorkflowPb "github.com/epifi/gamma/api/risk/workflow"
)

// RiskBankActionCreditFreeze defines the workflow for credit freezing an account
// At first it's blocked on a signal which conveys that the data for credit freeze is sent to bank
// Then it starts enquiring the bank regarding account status
// Once the bank freezes the account, the workflow revokes the app access from user
// At last it sends the comms to the frozen user
//
//nolint:dupl
func RiskBankActionCreditFreeze(ctx workflow.Context, req *workflowPb.Request) (*riskWorkflowPb.BankActionCreditFreezeResponse, error) {
	var (
		workflowStageStatus stagePb.Status
		err                 error
	)
	lg := workflow.GetLogger(ctx)

	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, nil)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: workflowPb.StatusInternal(),
			},
		}, err
	}

	payload := wfProcessingParams.GetPayload()
	bankActionCreditFreezeRequest := &riskWorkflowPb.BankActionCreditFreezeRequest{}
	if err = (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(payload, bankActionCreditFreezeRequest); err != nil {
		lg.Error("failed to unmarshal workflow processing req params", zap.Error(err))
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: workflowPb.StatusInternal(),
			},
		}, err
	}

	// --------------- ADD TO RISK BANK ACTION --------------------
	workflowStageStatus, _, err = addToBankAction(ctx, &activityPb.AddToBankActionRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		WorkflowId:    bankActionCreditFreezeRequest.GetWorkflowId(),
		Action:        bankActionCreditFreezeRequest.GetAction(),
		RequestReason: bankActionCreditFreezeRequest.GetRequestReason(),
		CommsTemplate: bankActionCreditFreezeRequest.GetCommsTemplate(),
		ActorId:       bankActionCreditFreezeRequest.GetActorId(),
	}, riskNs.BankActionCreditFreezeAddToBankAction)
	if err != nil {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "failed to add Db entry to for risk bank actions")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- DATA SENDING STAGE --------------------
	workflowStageStatus, err = waitForDataSentSignalV2(ctx, wfProcessingParams.GetClientReqId().GetId(),
		riskNs.CreditFreezeAccountDetailsSentSignal, riskNs.BankActionCreditFreezeWaitForSignal)
	if err != nil {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "data sending stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// =========== Sleep Workflow ============
	sleepSeconds, err := GenerateRandomInt(ctx, twoHourSeconds)
	if err != nil {
		lg.Error("random int generation failed", zap.Error(err))
		return nil, err
	}
	sleepDuration := time.Duration(sleepSeconds) * time.Second
	err = workflow.Sleep(ctx, sleepDuration)
	if err != nil {
		lg.Error("workflow sleep failed", zap.Error(err))
		return nil, err
	}

	// --------------- ENQUIRY STAGE --------------------
	if workflowStageStatus, err := enquireBankForActionStatusV2(ctx, wfProcessingParams.GetClientReqId().GetId(),
		riskNs.BankActionCreditFreezeEnquireStatus); err != nil {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "enquiry stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- FREEZE STAGE ---------------------
	if workflowStageStatus, err := updateAppState(ctx, wfProcessingParams.GetClientReqId().GetId(), riskNs.BankActionCreditFreezeUpdateAppState); err != nil {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "freeze stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// =========== GENERATE FORM STAGE =================
	formParams := getFormParams(ctx, bankActionCreditFreezeRequest.GetCase(),
		bankActionCreditFreezeRequest.GetAnalystEmail(),
		wfProcessingParams.GetClientReqId().GetId(), bankActionCreditFreezeRequest.GetCommsTemplate())

	if formParams != nil {
		workflowStatus, childErr, formIdLocal := spawnGenerateFormWorkflow(ctx, bankActionCreditFreezeRequest.GetActorId(), formParams)
		if childErr != nil {
			return &riskWorkflowPb.BankActionCreditFreezeResponse{
				ResponseHeader: &workflowPb.ResponseHeader{
					Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
				},
			}, fmt.Errorf("child workflow returned error %w", childErr)
		}
		if workflowStatus != stagePb.Status_SUCCESSFUL {
			return &riskWorkflowPb.BankActionCreditFreezeResponse{
				ResponseHeader: &workflowPb.ResponseHeader{
					Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
				},
			}, nil
		}
		formParams.Form.Id = formIdLocal
	}

	// ------------------ COMMS STAGE ----------------------
	if workflowStageStatus, err = sendCommsToUserV2(ctx, wfProcessingParams.GetClientReqId().GetId(), riskNs.BankActionCreditFreezeNotify, formParams.GetForm().GetId(), false); err != nil {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "comms to users stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- UPDATE FORM STAGE ------------------
	if formParams != nil && formParams.GetForm().GetId() != "" {
		err = UpdateForm(ctx, formParams)
		if err != nil {
			return &riskWorkflowPb.BankActionCreditFreezeResponse{
				ResponseHeader: &workflowPb.ResponseHeader{
					Status: celestialPkg.WorkflowStatusFromStageStatus(stagePb.Status_FAILED),
				},
			}, err
		}
	}

	// --------------- REMINDER STAGE ----------------------
	if workflowStageStatus, err = remindCommsToUser(ctx, wfProcessingParams.GetClientReqId().GetId(), bankActionCreditFreezeRequest.GetActorId(), riskNs.RiskBankActionCreditFreeze, formParams.GetForm().GetId()); err != nil {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "reminder comms to users stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionCreditFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	return &riskWorkflowPb.BankActionCreditFreezeResponse{
		ResponseHeader: &workflowPb.ResponseHeader{
			Status: workflowPb.StatusOk(),
		},
	}, nil
}
