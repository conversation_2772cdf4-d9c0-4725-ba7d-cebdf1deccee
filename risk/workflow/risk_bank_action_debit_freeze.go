//nolint:dupl,govet
package workflow

import (
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	activityPb "github.com/epifi/gamma/api/risk/activity"
	riskEnumPb "github.com/epifi/gamma/api/risk/enums"
	riskWorkflowPb "github.com/epifi/gamma/api/risk/workflow"
)

// RiskBankActionDebitFreeze defines the workflow for debit freezing an account
// At first it's blocked on a signal which conveys that the data for debit freeze is sent to bank
// Then it starts enquiring the bank regarding account status
// Once the bank freezes the account, the workflow revokes the app access from user
// At last it sends the comms to the frozen user
//
//nolint:dupl,funlen
func RiskBankActionDebitFreeze(ctx workflow.Context, bankActionDebitFreezeRequest *riskWorkflowPb.BankActionDebitFreezeRequest) (*riskWorkflowPb.BankActionDebitFreezeResponse, error) {
	var (
		workflowStageStatus stagePb.Status
		err                 error
	)
	lg := workflow.GetLogger(ctx)

	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, nil)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: workflowPb.StatusInternal(),
			},
		}, err
	}

	// --------------- ADD TO RISK BANK ACTION --------------------
	workflowStageStatus, _, err = addToBankAction(ctx, &activityPb.AddToBankActionRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		WorkflowId:    bankActionDebitFreezeRequest.GetWorkflowId(),
		Action:        riskEnumPb.Action_ACTION_DEBIT_FREEZE,
		RequestReason: bankActionDebitFreezeRequest.GetRequestReason(),
		CommsTemplate: bankActionDebitFreezeRequest.GetCommsTemplates(),
		ActorId:       bankActionDebitFreezeRequest.GetActorId(),
	}, riskNs.BankActionDebitFreezeAddToBankAction)
	if err != nil {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "failed to add Db entry to for risk bank actions")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- DATA SENDING STAGE --------------------
	workflowStageStatus, err = waitForDataSentSignalV2(ctx, wfProcessingParams.GetClientReqId().GetId(),
		riskNs.DebitFreezeAccountDetailsSentSignal, riskNs.BankActionDebitFreezeWaitForSignal)
	if err != nil {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "data sending stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// =========== Sleep Workflow ============
	sleepSeconds, err := GenerateRandomInt(ctx, twoHourSeconds)
	if err != nil {
		lg.Error("random int generation failed", zap.Error(err))
		return nil, err
	}
	sleepDuration := time.Duration(sleepSeconds) * time.Second
	err = workflow.Sleep(ctx, sleepDuration)
	if err != nil {
		lg.Error("workflow sleep failed", zap.Error(err))
		return nil, err
	}

	// --------------- ENQUIRY STAGE --------------------
	if workflowStageStatus, err = enquireBankForActionStatusV2(ctx, wfProcessingParams.GetClientReqId().GetId(),
		riskNs.BankActionDebitFreezeEnquireStatus); err != nil {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "enquiry stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- FREEZE STAGE ---------------------
	if workflowStageStatus, err = updateAppState(ctx, wfProcessingParams.GetClientReqId().GetId(), riskNs.BankActionDebitFreezeUpdateAppState); err != nil {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "freeze stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- COMMS STAGE ----------------------
	if workflowStageStatus, err = sendCommsToUserV2(ctx, wfProcessingParams.GetClientReqId().GetId(), riskNs.BankActionDebitFreezeNotify, "", false); err != nil {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "comms to users stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- REMINDER STAGE ----------------------
	if workflowStageStatus, err = remindCommsToUser(ctx, wfProcessingParams.GetClientReqId().GetId(), bankActionDebitFreezeRequest.GetActorId(), riskNs.RiskBankActionDebitFreeze, ""); err != nil {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "reminder comms to users stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionDebitFreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	return &riskWorkflowPb.BankActionDebitFreezeResponse{
		ResponseHeader: &workflowPb.ResponseHeader{
			Status: workflowPb.StatusOk(),
		},
	}, nil
}
