//nolint:dupl,govet
package workflow

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	activityPb "github.com/epifi/gamma/api/risk/activity"
	riskWorkflowPb "github.com/epifi/gamma/api/risk/workflow"
)

// RiskBankActionUnfreeze defines the workflow for unfreezing an account
// At first it's blocked on a signal which conveys that the data for unfreeze is sent to bank
// Then it starts enquiring the bank regarding account status
// Once the bank freezes the account, the workflow revokes the app access from user
// At last it sends the comms to the user
//
//nolint:dupl
func RiskBankActionUnfreeze(ctx workflow.Context, req *workflowPb.Request) (*riskWorkflowPb.BankActionUnfreezeResponse, error) {
	var (
		workflowStageStatus stagePb.Status
		err                 error
	)
	lg := workflow.GetLogger(ctx)

	wfProcessingParams, err := celestialPkg.GetWorkflowProcessingReqParams(ctx, nil)
	if err != nil {
		lg.Error("failed to fetch workflow processing params", zap.Error(err))
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: workflowPb.StatusInternal(),
			},
		}, err
	}

	payload := wfProcessingParams.GetPayload()
	bankActionUnfreezeRequest := &riskWorkflowPb.BankActionUnfreezeRequest{}
	if err = (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(payload, bankActionUnfreezeRequest); err != nil {
		lg.Error("failed to unmarshal workflow processing req params", zap.Error(err))
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: workflowPb.StatusInternal(),
			},
		}, err
	}

	// --------------- ADD TO RISK BANK ACTION --------------------
	workflowStageStatus, _, err = addToBankAction(ctx, &activityPb.AddToBankActionRequest{
		RequestHeader: &celestialActivityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		WorkflowId:    bankActionUnfreezeRequest.GetWorkflowId(),
		Action:        bankActionUnfreezeRequest.GetAction(),
		RequestReason: bankActionUnfreezeRequest.GetRequestReason(),
		CommsTemplate: bankActionUnfreezeRequest.GetCommsTemplate(),
		ActorId:       bankActionUnfreezeRequest.GetActorId(),
	}, riskNs.BankActionUnfreezeAddToBankAction)
	if err != nil {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "failed to add Db entry to for risk bank actions")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- DATA SENDING STAGE --------------------
	workflowStageStatus, err = waitForDataSentSignalV2(ctx, wfProcessingParams.GetClientReqId().GetId(),
		riskNs.UnfreezeAccountDetailsSentSignal, riskNs.BankActionUnfreezeWaitForSignal)
	if err != nil {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "data sending stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// =========== Sleep Workflow ============
	sleepSeconds, err := GenerateRandomInt(ctx, twoHourSeconds)
	if err != nil {
		lg.Error("random int generation failed", zap.Error(err))
		return nil, err
	}
	sleepDuration := time.Duration(sleepSeconds) * time.Second
	err = workflow.Sleep(ctx, sleepDuration)
	if err != nil {
		lg.Error("workflow sleep failed", zap.Error(err))
		return nil, err
	}

	// --------------- ENQUIRY STAGE --------------------
	if workflowStageStatus, err := enquireBankForActionStatusV2(ctx, wfProcessingParams.GetClientReqId().GetId(),
		riskNs.BankActionUnfreezeEnquireStatus); err != nil {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "enquiry stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- FREEZE STAGE ---------------------
	if workflowStageStatus, err := updateAppState(ctx, wfProcessingParams.GetClientReqId().GetId(), riskNs.BankActionUnfreezeUpdateAppState); err != nil {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "freeze stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	// --------------- COMMS STAGE ----------------------
	if workflowStageStatus, err := sendCommsToUserV2(ctx, wfProcessingParams.GetClientReqId().GetId(), riskNs.BankActionUnfreezeNotify, "", false); err != nil {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, errors.Wrap(err, "comms to users stage processing failed")
	} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		return &riskWorkflowPb.BankActionUnfreezeResponse{
			ResponseHeader: &workflowPb.ResponseHeader{
				Status: celestialPkg.WorkflowStatusFromStageStatus(workflowStageStatus),
			},
		}, nil
	}

	return &riskWorkflowPb.BankActionUnfreezeResponse{
		ResponseHeader: &workflowPb.ResponseHeader{
			Status: workflowPb.StatusOk(),
		},
	}, nil
}
