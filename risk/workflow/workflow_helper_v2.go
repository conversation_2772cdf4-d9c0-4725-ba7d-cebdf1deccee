//nolint:funlen
package workflow

import (
	"crypto/rand"
	"fmt"
	"math/big"
	"time"

	"github.com/samber/lo"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	riskActivityPb "github.com/epifi/gamma/api/risk/activity"
	cmActivityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	formPb "github.com/epifi/gamma/api/risk/case_management/form"
	riskReviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	riskCmWfPb "github.com/epifi/gamma/api/risk/case_management/workflow"
	riskEnumsPb "github.com/epifi/gamma/api/risk/enums"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	errorsPkg "github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/emptypb"
)

const (
	twoHourSeconds = 7200

	maxRetryCountForReminder = 8

	// Two years
	freezeFormExpiryTime = time.Hour * 24 * 365 * 2
)

// waitForDataSentSignalV2 - waiting for the signal that data has been to bank for action
// nolint:dupl
func waitForDataSentSignalV2(ctx workflow.Context, clientReqID string, dataSentSignal epifitemporal.Signal, stage epifitemporal.Stage) (stagePb.Status, error) {
	var (
		workflowStageStatus = stagePb.Status_BLOCKED
		riskState           = riskEnumsPb.State_STATE_INITIATED
	)
	lg := workflow.GetLogger(ctx)
	// blocking creation stage for data sent signal
	if err := celestialPkg.InitiateWorkflowStage(ctx, stage, workflowStageStatus); err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiated workflow stage: %s: %w", stage, err)
	}

	// blocking the workflow till we receive a signal, or it times out
	sigChannel := epifitemporal.NewSignalChannel(ctx, dataSentSignal, &emptypb.Empty{})
	sigChannel.AddReceiverHandler(func(getErr error, _ *emptypb.Empty) {
		if getErr != nil {
			lg.Error("failed while receiving signal", zap.String(logger.SIGNAL_NAME, string(dataSentSignal)), zap.Error(getErr))
			workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(getErr)
			return
		}
		// successfully received signal, making stage as successful and bank action as sent to bank
		workflow.GetLogger(ctx).Info("received data sent signal")
		workflowStageStatus = stagePb.Status_SUCCESSFUL
		riskState = riskEnumsPb.State_STATE_SENT_TO_BANK
	})

	err := epifitemporal.ReceiveSignal(ctx, sigChannel, 72*time.Hour)
	if err != nil {
		lg.Error("failed while receiving signal", zap.String(logger.SIGNAL_NAME, string(dataSentSignal)), zap.Error(err))
		workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
	}

	switch workflowStageStatus {
	case stagePb.Status_SUCCESSFUL:
		riskState = riskEnumsPb.State_STATE_SENT_TO_BANK
	default:
		riskState = riskEnumsPb.State_STATE_REFUSED
	}

	// updating status of risk bank action
	if err := activityPkg.ExecuteRaw(ctx, riskNs.UpdateRiskActionStatus, &activityPb.Response{}, &activityPb.Request{
		ClientReqId: clientReqID,
	}, riskState); err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.UpdateRiskActionStatus)), zap.Error(err))
		workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
	}

	err = celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return 0, fmt.Errorf("failed to update workflow stage: %s: %w", stage, err)
	}

	return workflowStageStatus, nil
}

// enquireBankForActionStatus - enquires the bank for current account status account status
// nolint:dupl
func enquireBankForActionStatusV2(ctx workflow.Context, clientReqID string, stage epifitemporal.Stage) (stagePb.Status, error) {
	var (
		workflowStageStatus = stagePb.Status_INITIATED
	)
	lg := workflow.GetLogger(ctx)
	err := celestialPkg.InitiateWorkflowStage(ctx, stage, workflowStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiated workflow stage: %s: %w", stage, err)
	}

	err = activityPkg.Execute(ctx, riskNs.GetActionStatusFromBank, &activityPb.Response{}, &activityPb.Request{
		ClientReqId: clientReqID,
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.GetActionStatusFromBank)), zap.Error(err))
	}
	workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)

	if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		// updating status of risk bank action to manual intervention
		err = activityPkg.ExecuteRaw(ctx, riskNs.UpdateRiskActionStatus, &activityPb.Response{}, &activityPb.Request{
			ClientReqId: clientReqID,
		}, riskEnumsPb.State_STATE_MANUAL_INTERVENTION)
		if err != nil {
			lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.UpdateRiskActionStatus)), zap.Error(err))
			workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
		}
	}

	err = celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return 0, fmt.Errorf("failed to update workflow stage: %s: %w", stage, err)
	}

	return workflowStageStatus, nil
}

// updateAppState - updates APP access state at our end
// nolint:dupl
func updateAppState(ctx workflow.Context, clientReqID string, stage epifitemporal.Stage) (stagePb.Status, error) {
	var (
		workflowStageStatus = stagePb.Status_INITIATED
	)
	lg := workflow.GetLogger(ctx)
	err := celestialPkg.InitiateWorkflowStage(ctx, stage, workflowStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiated workflow stage: %s: %w", stage, err)
	}

	err = activityPkg.Execute(ctx, riskNs.FiFreezeStatusUpdate, &activityPb.Response{}, &activityPb.Request{
		ClientReqId: clientReqID,
	})
	if err != nil {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.FiFreezeStatusUpdate)), zap.Error(err))
	}
	workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)

	err = celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return 0, fmt.Errorf("failed to update workflow stage: %s: %w", stage, err)
	}

	return workflowStageStatus, nil
}

// sendCommsToUserV2 - send notifications to the user regarding account action
// nolint:dupl
func sendCommsToUserV2(ctx workflow.Context, clientReqID string, stage epifitemporal.Stage, formId string, _ bool) (stagePb.Status, error) {
	var (
		workflowStageStatus = stagePb.Status_INITIATED
	)
	lg := workflow.GetLogger(ctx)
	err := celestialPkg.InitiateWorkflowStage(ctx, stage, workflowStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure.  We expect an alert here
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiated workflow stage: %s: %w", stage, err)
	}

	var notificationForUser *notificationPb.Notification
	v := workflow.GetVersion(ctx, "switch-to-new-notification", workflow.DefaultVersion, 2)
	switch v {
	case workflow.DefaultVersion:
		// older workflow without forms
		// get the notification template corresponding to the block/ unblock type
		notificationTemplateRes := &notificationPb.GetTemplatesResponse{}
		err = activityPkg.Execute(ctx, riskNs.GetNotificationTemplate, notificationTemplateRes, &notificationPb.GetTemplatesRequest{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     clientReqID,
				Client: workflowPb.Client_RISK,
			},
		})
		if err != nil {
			lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.GetNotificationTemplate)), zap.Error(err))
		}

		notificationForUser = notificationTemplateRes.GetNotifications()
	default:
		// get the notification template corresponding to the block/ unblock type with activity level params
		result := &riskActivityPb.GetNotificationTemplateForBankActionsResponse{}
		err = activityPkg.Execute(ctx, riskNs.GetNotificationTemplateForBankActions, result, &riskActivityPb.GetNotificationTemplateForBankActionsRequest{
			RequestHeader: &activityPb.RequestHeader{
				Ownership: commontypes.Ownership_EPIFI_TECH,
			},
			FormId:                formId,
			BankActionClientReqId: clientReqID,
		})

		if err != nil {
			lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.GetNotificationTemplateForBankActions)), zap.Error(err))
		}

		notificationForUser = result.GetNotifications()
	}

	workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)
	if notificationForUser != nil && len(notificationForUser.GetCommunicationList()) != 0 {
		// sends multiple notifications to the user simultaneously
		if err := activityPkg.Execute(ctx, epifitemporal.SendNotification, &notificationPb.SendNotificationResponse{}, &notificationPb.SendNotificationRequest{
			Notifications: notificationForUser,
		}); err != nil {
			return 0, fmt.Errorf("error in SendNotification activity: %s: %w", stage, err)
		}
	}

	err = celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus)
	if err != nil {
		// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
		return 0, fmt.Errorf("failed to update workflow stage: %s: %w", stage, err)
	}

	return workflowStageStatus, nil
}

func addToBankAction(ctx workflow.Context, addToBankActionRequest *riskActivityPb.AddToBankActionRequest, stage epifitemporal.Stage) (stagePb.Status, string, error) {
	var (
		workflowStageStatus stagePb.Status
	)
	if err := celestialPkg.InitiateWorkflowStage(ctx, stage, stagePb.Status_INITIATED); err != nil {
		return 0, "", fmt.Errorf("failed to initiated workflow stage: %s: %w", stage, err)
	}

	addToBankActionResponse := &riskActivityPb.AddToBankActionResponse{}
	err := activityPkg.Execute(ctx, riskNs.AddToBankAction, addToBankActionResponse, addToBankActionRequest)
	workflowStageStatus = celestialPkg.GetWorkflowStageStatusForErr(err)

	if err = celestialPkg.UpdateWorkflowStage(ctx, stage, workflowStageStatus); err != nil {
		return 0, "", fmt.Errorf("failed to update workflow stage: %s: %w", stage, err)
	}

	return workflowStageStatus, addToBankActionResponse.GetBankActionId(), nil
}

// GenerateRandomInt returns, as an int, a non-negative pseudo-random number in the half-open interval [0,n)
// It panics if n <= 0.

//workflowcheck:ignore
func GenerateRandomInt(ctx workflow.Context, n int) (int, error) {
	if n <= 0 {
		return 0, epifierrors.ErrInvalidArgument
	}
	random := workflow.SideEffect(ctx, func(ctx workflow.Context) interface{} {
		randInt, _ := rand.Int(rand.Reader, big.NewInt(int64(n)))
		return int(randInt.Int64())
	})
	var randInt int
	err := random.Get(&randInt)
	if err != nil {
		return 0, errorsPkg.Wrap(err, "generation of random number failed")
	}

	return randInt, nil
}

func remindCommsToUser(ctx workflow.Context, clientReqID string, actorId string, workflowName epifitemporal.Workflow, formId string) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)

	// blocking the workflow till we receive a signal, or it times out
	sigChannel := epifitemporal.NewSignalChannel(ctx, riskNs.BankActionsReminderSignal, &emptypb.Empty{})
	sigChannel.AddReceiverHandler(func(getErr error, _ *emptypb.Empty) {
		if getErr != nil {
			lg.Error("failed while receiving signal", zap.String(logger.SIGNAL_NAME,
				string(riskNs.BankActionsReminderSignal)), zap.Error(getErr))
			return
		}
		lg.Info("received bank action reminder signal", zap.String(logger.REQUEST_ID, clientReqID))
	})

	// Looping through a max hard-defined limit
	// We'll poll the next reminder time, send reminder comms to user and wait on the signal
	for i := 0; i < maxRetryCountForReminder; i++ {
		err := celestialPkg.InitiateWorkflowStage(ctx, riskNs.BankActionReminderGetReminderPoint, stagePb.Status_INITIATED)
		if err != nil {
			// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
			return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to initiate workflow stage: %s: %w", riskNs.BankActionReminderGetReminderPoint, err)
		}

		reminderRes := &riskActivityPb.GetReminderPointResponse{}

		activityErr := activityPkg.Execute(ctx, riskNs.GetReminderPoint, reminderRes, &riskActivityPb.GetReminderPointRequest{
			WorkflowType: celestialPkg.GetTypeEnumFromWorkflowType(workflowName),
			ActorId:      actorId,
			RequestIdentifier: &riskActivityPb.GetReminderPointRequest_ClientRequestId{
				ClientRequestId: clientReqID,
			},
			FormId: formId,
		})
		if activityErr != nil {
			lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(riskNs.GetReminderPoint)), zap.Error(activityErr))
		}

		statePostReminderActivity := celestialPkg.GetWorkflowStageStatusForErr(activityErr)

		if updateErr := celestialPkg.UpdateWorkflowStage(ctx, riskNs.BankActionReminderGetReminderPoint, statePostReminderActivity); updateErr != nil {
			// NOTE: returning error here as this is an unexpected platform failure. We expect an alert here
			return stagePb.Status_FAILED, fmt.Errorf("failed to update workflow stage: %s: %w", riskNs.BankActionReminderGetReminderPoint, updateErr)
		}

		if activityErr != nil {
			return statePostReminderActivity, nil
		}

		if reminderRes.GetNextReminderPoint() == nil {
			return stagePb.Status_SUCCESSFUL, nil
		}

		point := reminderRes.GetNextReminderPoint().AsTime()

		signalErr := epifitemporal.ReceiveSignal(ctx, sigChannel, point.Sub(workflow.Now(ctx)))
		switch {
		// Return on signal
		case signalErr == nil:
			// reminders failed since signal received hence marking this as failure
			return stagePb.Status_FAILED, nil
		// Send reminder comms using signal expiry as trigger
		case epifitemporal.HasSignalReceivedTimedOut(signalErr):
			if workflowStageStatus, err := sendCommsToUserV2(ctx, clientReqID, riskNs.BankActionReminderTriggerStage, formId, true); err != nil {
				return workflowStageStatus, fmt.Errorf("comms to users processing failed %w", err)
			} else if workflowStageStatus != stagePb.Status_SUCCESSFUL {
				return workflowStageStatus, nil
			}
		default:
			lg.Error("failed while receiving signal", zap.String(logger.SIGNAL_NAME,
				string(riskNs.BankActionsReminderSignal)), zap.Error(signalErr))
			return stagePb.Status_FAILED, nil
		}
	}

	return stagePb.Status_SUCCESSFUL, nil
}

func spawnGenerateFormWorkflow(ctx workflow.Context, actorId string, formParams *formPb.FormOrchestrationParams) (stagePb.Status, error, string) {
	v := workflow.GetVersion(ctx, "generate-forms", workflow.DefaultVersion, 2)
	switch v {
	case workflow.DefaultVersion:
		return stagePb.Status_SUCCESSFUL, nil, ""
	default:
		// do nothing and continue
	}
	var (
		workflowStageStatus stagePb.Status
	)

	// Since, generating uuid in workflow directly is not recommended due to its non-deterministic behaviour
	reqId, err := epifitemporal.GenerateUUID(ctx)
	if err != nil {
		return stagePb.Status_FAILED, fmt.Errorf("failed to generate uuid: %w", err), ""
	}

	result := &riskCmWfPb.GenerateFormResponse{}
	if childErr := celestialPkg.ExecuteChildWorkflow(ctx, &celestialPb.WorkflowCreationRequestParams{
		ActorId: actorId,
		Version: workflowPb.Version_V0,
		Type:    celestialPkg.GetTypeEnumFromWorkflowType(riskNs.GenerateForm),
		ClientReqId: &workflowPb.ClientReqId{
			Client: workflowPb.Client_RISK,
			Id:     reqId,
		},
		Ownership: commontypes.Ownership_EPIFI_TECH,
	}, &riskCmWfPb.GenerateFormRequest{
		Params: formParams,
	}, result); childErr != nil {
		workflowStageStatus = stagePb.Status_FAILED
		return workflowStageStatus, fmt.Errorf("generate form child returned error %w", childErr), ""
	}

	workflowStageStatus = celestialPkg.StageStatusFromWorkflowStatus(result.GetResponseHeader().GetStatus())

	return workflowStageStatus, nil, result.GetForm().GetId()
}

func getFormParams(ctx workflow.Context, caseDetails *riskReviewPb.Case, analystEmail string, clientReqId string, commsTemplate []riskEnumsPb.CommsTemplate) *formPb.FormOrchestrationParams {
	v := workflow.GetVersion(ctx, "generate-forms-params", workflow.DefaultVersion, 2)
	switch v {
	case workflow.DefaultVersion:
		return nil
	default:
		// do nothing and continue
	}

	// temporary hack to allow only M_CF template
	v2 := workflow.GetVersion(ctx, "allowed-email-templates", workflow.DefaultVersion, 2)
	switch v2 {
	case workflow.DefaultVersion:
		return nil
	default:
		if !lo.Contains(commsTemplate, riskEnumsPb.CommsTemplate_COMMS_TEMPLATE_EMAIL_M_CF) {
			return nil
		}
	}

	// dynamic mapping to get the questionnaire template based on review type,
	// wrapped under side effect
	reviewToQuestionnaireMappingInterface := workflow.SideEffect(ctx, func(ctx workflow.Context) interface{} {
		return map[riskReviewPb.ReviewType]formPb.QuestionnaireTemplate{
			riskReviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW: formPb.QuestionnaireTemplate_QUESTIONNAIRE_TEMPLATE_CFR_BLOCKED_TRANSACTION_REVIEW,
		}
	})

	var reviewToQuestionnaireMapping map[riskReviewPb.ReviewType]formPb.QuestionnaireTemplate
	err := reviewToQuestionnaireMappingInterface.Get(&reviewToQuestionnaireMapping)
	if err != nil {
		lg := workflow.GetLogger(ctx)
		lg.Error("failed to initialise review to questionnaire mapping", zap.Error(err))
		return nil
	}

	mapping, ok := reviewToQuestionnaireMapping[caseDetails.GetReviewType()]
	if !ok {
		return nil
	}

	return &formPb.FormOrchestrationParams{
		QuestionIdentifiers: &formPb.QuestionIdentifiers{
			Identifiers: &formPb.QuestionIdentifiers_EntityIdentifiers{
				EntityIdentifiers: &formPb.EntityIdentifiers{
					Identifiers: []*formPb.EntityIdentifier{
						{
							Type: formPb.EntityType_ENTITY_TYPE_QUESTIONNAIRE_TEMPLATE,
							Id:   mapping.String(),
						},
					},
				},
			},
		},
		Form: &formPb.Form{
			CaseId:        caseDetails.GetId(),
			ActorId:       caseDetails.GetActorId(),
			AddedBy:       analystEmail,
			ClientReqId:   clientReqId,
			ExpireAt:      timestampPb.New(workflow.Now(ctx).Add(freezeFormExpiryTime)),
			Origin:        formPb.FormOrigin_FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE,
			Status:        formPb.Status_STATUS_CREATED,
			WorkflowReqId: clientReqId,
		},
	}
}

func UpdateForm(ctx workflow.Context, formParams *formPb.FormOrchestrationParams) error {
	if formParams == nil {
		return nil
	}

	v := workflow.GetVersion(ctx, "update-form-params", workflow.DefaultVersion, 2)
	switch v {
	case workflow.DefaultVersion:
		// Handling the default version logic here
		return nil
	default:
		// Handling the new version logic here
		formParams.GetForm().Status = formPb.Status_STATUS_SENT
		if err := activityPkg.Execute(ctx, riskNs.UpdateForm, &cmActivityPb.UpdateFormResponse{}, &cmActivityPb.UpdateFormRequest{
			Form: formParams.GetForm(),
			FormFieldMasks: []formPb.FormFieldMask{
				formPb.FormFieldMask_FORM_FIELD_MASK_STATUS,
			},
		}); err != nil {
			return fmt.Errorf("update form activity failed: %w", err)
		}
		return nil
	}
}
