package manager

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"google.golang.org/genproto/googleapis/type/money"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"go.uber.org/zap"
	googleDateType "google.golang.org/genproto/googleapis/type/date"
	googleDayType "google.golang.org/genproto/googleapis/type/dayofweek"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	events2 "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/fittt"
	actionpb "github.com/epifi/gamma/api/fittt/action"
	orchestratorpb "github.com/epifi/gamma/api/fittt/orchestrator/consumer"
	schedulerpb "github.com/epifi/gamma/api/fittt/scheduler"
	investmentPb "github.com/epifi/gamma/api/investment"
	"github.com/epifi/gamma/api/investment/aggregator/consumer"
	mforderpb "github.com/epifi/gamma/api/investment/mutualfund/order"
	execpb "github.com/epifi/gamma/api/rms/command_processor"
	pb "github.com/epifi/gamma/api/rms/manager"
	rmsManagerMock "github.com/epifi/gamma/api/rms/manager/mocks"
	"github.com/epifi/gamma/api/rms/orchestrator/event"
	"github.com/epifi/gamma/api/rms/ui"
	savingsPb "github.com/epifi/gamma/api/savings"
	search2 "github.com/epifi/gamma/api/search"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	wealthpb "github.com/epifi/gamma/api/wealthonboarding"
	fitttPkg "github.com/epifi/gamma/pkg/fittt"
	"github.com/epifi/gamma/pkg/usstocks"
	"github.com/epifi/gamma/rms"
	"github.com/epifi/gamma/rms/command_processor/fact"
	genConf "github.com/epifi/gamma/rms/config/genconf"
	"github.com/epifi/gamma/rms/dao"
	"github.com/epifi/gamma/rms/dao/possible_param_values"
	"github.com/epifi/gamma/rms/events"
	rmstypes "github.com/epifi/gamma/rms/wire/types"
)

// dummy variable assignment to point out outdated generated mock code.
var _ pb.RuleManagerClient = rmsManagerMock.NewMockRuleManagerClient(nil)

type InvestmentInstrumentEventPublisher queue.Publisher

type RuleManagerService struct {
	pb.UnimplementedRuleManagerServer
	conf                               *genConf.Config
	ruleDao                            dao.RulesDao
	subscriptionDao                    dao.RuleSubscriptionsDao
	subscriptionRuntimeInfoDao         dao.SubscriptionRuntimeInfoDao
	executionDao                       dao.RuleExecutionsDao
	tagMappingsDao                     dao.RuleTagMappingsDao
	paramValuesDao                     dao.PossibleParamValuesDao
	paramValuesSelectorCtaDao          dao.ParamValueSelectorCtaDao
	ruleDisplayInfoDao                 dao.RuleDisplayInfoDao
	homeCardDao                        dao.HomeCardDao
	tagsDao                            dao.RuleTagsDao
	collectionsDao                     dao.CollectionsDao
	rmsProfileDao                      dao.RmsProfileDao
	rmsDbTxnExecutor                   storagev2.TxnExecutor
	eventBroker                        events2.Broker
	actionPublisher                    rmstypes.ActionProcessingPublisher
	fitttClient                        fittt.FitttClient
	actorClient                        actor.ActorClient
	wealthOnboardingClient             wealthpb.WealthOnboardingClient
	mfOrderManagerClient               mforderpb.OrderManagerClient
	fitttSchedulerClient               schedulerpb.SchedulerServiceClient
	searchServiceClient                search2.ActionBarClient
	usersClient                        userPb.UsersClient
	userGroupClient                    userGroupPb.GroupClient
	investmentInstrumentEventPublisher rmstypes.InvestmentInstrumentEventPublisher
	doOnce                             once.DoOnce
	savingsClient                      savingsPb.SavingsClient
	commsClient                        commsPb.CommsClient
	accountBalanceClient               accountBalancePb.BalanceClient
	pkg                                *fitttPkg.FitttPkg
	usStocks                           fitttPkg.IUSStocks
	usStocksSIPParamsValidator         usstocks.SIPParamsValidator
}

// nolint: funlen
func NewRuleManagerService(
	conf *genConf.Config,
	ruleDao dao.RulesDao,
	subscriptionDao dao.RuleSubscriptionsDao,
	subscriptionRuntimeInfoDao dao.SubscriptionRuntimeInfoDao,
	executionDao dao.RuleExecutionsDao,
	ruleDisplayInfoDao dao.RuleDisplayInfoDao,
	collectionsDao dao.CollectionsDao,
	tagMappingsDao dao.RuleTagMappingsDao,
	paramValuesDao dao.PossibleParamValuesDao,
	paramValuesSelectorCtaDao dao.ParamValueSelectorCtaDao,
	tagsDao dao.RuleTagsDao,
	homeCardDao dao.HomeCardDao,
	rmsProfileDao dao.RmsProfileDao,
	fitttSchedulerClient schedulerpb.SchedulerServiceClient,
	rmsDbTxnExecutor storagev2.TxnExecutor,
	actionPublisher rmstypes.ActionProcessingPublisher,
	eventBroker events2.Broker,
	searchServiceClient search2.ActionBarClient,
	fitttClient fittt.FitttClient,
	actorClient actor.ActorClient,
	mfOrderManagerClient mforderpb.OrderManagerClient,
	wealthOnboardingClient wealthpb.WealthOnboardingClient,
	usersClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	investmentInstrumentEventPublisher rmstypes.InvestmentInstrumentEventPublisher,
	savingsClient savingsPb.SavingsClient,
	doOnce once.DoOnce,
	commsClient commsPb.CommsClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	usStocks fitttPkg.IUSStocks,
	usStocksSIPParamsValidator usstocks.SIPParamsValidator,
) pb.RuleManagerServer {
	return &RuleManagerService{
		conf:                               conf,
		ruleDao:                            ruleDao,
		subscriptionDao:                    subscriptionDao,
		subscriptionRuntimeInfoDao:         subscriptionRuntimeInfoDao,
		executionDao:                       executionDao,
		tagMappingsDao:                     tagMappingsDao,
		paramValuesDao:                     paramValuesDao,
		paramValuesSelectorCtaDao:          paramValuesSelectorCtaDao,
		ruleDisplayInfoDao:                 ruleDisplayInfoDao,
		homeCardDao:                        homeCardDao,
		tagsDao:                            tagsDao,
		collectionsDao:                     collectionsDao,
		rmsProfileDao:                      rmsProfileDao,
		rmsDbTxnExecutor:                   rmsDbTxnExecutor,
		eventBroker:                        eventBroker,
		actionPublisher:                    actionPublisher,
		fitttClient:                        fitttClient,
		actorClient:                        actorClient,
		wealthOnboardingClient:             wealthOnboardingClient,
		mfOrderManagerClient:               mfOrderManagerClient,
		fitttSchedulerClient:               fitttSchedulerClient,
		searchServiceClient:                searchServiceClient,
		usersClient:                        usersClient,
		userGroupClient:                    userGroupClient,
		investmentInstrumentEventPublisher: investmentInstrumentEventPublisher,
		doOnce:                             doOnce,
		savingsClient:                      savingsClient,
		commsClient:                        commsClient,
		accountBalanceClient:               accountBalanceClient,
		pkg:                                fitttPkg.NewFitttPkg(usStocks),
		usStocks:                           usStocks,
		usStocksSIPParamsValidator:         usStocksSIPParamsValidator,
	}
}

func (rm *RuleManagerService) CreateNewRule(ctx context.Context, req *pb.CreateNewRuleRequest) (*pb.CreateNewRuleResponse, error) {
	// validating the incoming request
	if err := rm.validateNewRuleRequest(ctx, req); err != nil {
		logger.Error(ctx, "Validation failed for create rule request", zap.Error(err), zap.Any("rule", req.GetRule()))
		return &pb.CreateNewRuleResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			Rule:   nil,
		}, err
	}

	rule, err := rm.ruleDao.Create(ctx, req.Rule)
	if err != nil {
		logger.Error(ctx, "Failed to create new rule in database", zap.Error(err), zap.Any("rule", req.GetRule()))
		return &pb.CreateNewRuleResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			Rule:   nil,
		}, err
	}

	logger.Info(ctx, "Rule creation successful", zap.String(logger.RULE_ID, rule.GetId()))
	return &pb.CreateNewRuleResponse{
		Status: rpc.StatusOk(),
		Rule:   rule,
	}, nil
}

// what would be the behaviour if a rule is updated which is subscribed subscribed by some users?
// current considerations is that the newly updated rules to be applicable whenever the rule is executed
func (rm *RuleManagerService) UpdateRule(ctx context.Context, req *pb.UpdateRuleRequest) (*pb.UpdateRuleResponse, error) {
	// validate the incoming request
	if err := rm.validateUpdateRuleRequest(ctx, req); err != nil {
		logger.Error(ctx, "Validation failed for create rule request", zap.Error(err), zap.Any("rule", req.GetRule()),
			zap.String(logger.RULE_ID, req.GetRuleId()))
		return &pb.UpdateRuleResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			Rule:   nil,
		}, nil
	}

	logger.Info(ctx, "Going to update rule", zap.Any("rule", req.GetRule()),
		zap.String(logger.RULE_ID, req.GetRuleId()), zap.Any("update fields", req.GetUpdateFields()),
		zap.Bool("closeAllSubscription", req.GetCloseAllSubscriptions()))

	var updatedRule *pb.Rule
	txnErr := rm.rmsDbTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		var err error
		if req.GetCloseAllSubscriptions() {
			logger.Info(txnCtx, "Closing all subscription for rule requested", zap.String(logger.RULE_ID, req.GetRuleId()))
			err = rm.closeExistingSubscriptions(txnCtx, req.GetRuleId())
			if err != nil {
				logger.Error(txnCtx, "Failed to close subscriptions for rule, not updating rule state",
					zap.String(logger.RULE_ID, req.GetRuleId()), zap.Error(err))
				return err
			}
		}

		logger.Info(txnCtx, "Updating rule", zap.String(logger.RULE_ID, req.GetRuleId()))
		updatedRule, err = rm.ruleDao.Update(txnCtx, req.GetRuleId(), req.GetRule(), req.GetUpdateFields())
		if err != nil {
			logger.Error(txnCtx, "Failed to update existing rule in database", zap.Error(err), zap.String(logger.RULE_ID, req.GetRuleId()))
			return err
		}
		return nil
	})

	if txnErr != nil {
		logger.Error(ctx, "Failed to update rule", zap.Error(txnErr), zap.String(logger.RULE_ID, req.RuleId))
		return &pb.UpdateRuleResponse{
			Status: rpc.StatusInternalWithDebugMsg(txnErr.Error()),
			Rule:   updatedRule,
		}, nil
	}

	logger.Info(ctx, "Rule update successful", zap.String(logger.RULE_ID, req.GetRuleId()))
	return &pb.UpdateRuleResponse{
		Status: rpc.StatusOk(),
		Rule:   updatedRule,
	}, nil
}

// TODO(shubham.c) what would happen if a rule is deleted which is subscribed by some users?
// current implementation do not allow deletion of such rules which are already subscribed by any user
// other option could be that all the subscriptions might get invalid
func (rm *RuleManagerService) DeleteRule(ctx context.Context, req *pb.DeleteRuleRequest) (*pb.DeleteRuleResponse, error) {
	if err := rm.ruleDao.Delete(ctx, req.RuleId); err != nil {
		logger.Error(ctx, "Failed to delete rule from database", zap.Error(err), zap.String(logger.RULE_ID, req.RuleId))
		return &pb.DeleteRuleResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, err
	}

	logger.Info(ctx, "Rule deletion successful", zap.String(logger.RULE_ID, req.RuleId))
	return &pb.DeleteRuleResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (rm *RuleManagerService) GetRuleById(ctx context.Context, req *pb.GetRuleByIdRequest) (*pb.GetRuleByIdResponse, error) {
	resp := &pb.GetRuleByIdResponse{
		Status: rpc.StatusOk(),
	}
	rule, err := rm.ruleDao.GetById(ctx, req.RuleId)
	if err != nil {
		logger.Error(ctx, "Unable to get rule", zap.String(logger.RULE_ID, req.RuleId), zap.Error(err))
		return &pb.GetRuleByIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			Rule:   rule,
		}, err
	}

	// TODO: optimise the flow to reduce the number of DB queries. We can use joins or gorm associations to get rule and tags in same query
	ruleTag, err := rm.tagMappingsDao.GetTagsForRules(ctx, []string{req.GetRuleId()}, []pb.TagState{pb.TagState_TAG_ACTIVE})
	if err != nil {
		logger.Error(ctx, "Failed to get tags information for rule", zap.Error(err), zap.Any(logger.RULE_ID, req.GetRuleId()))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	rule.RuleTags = ruleTag[req.GetRuleId()]
	resp.Rule = rule
	_ = rm.populateDisplayField1(ctx, rule, req.GetFieldMasks()) // TODO(sakthi) check if discarding error is correct here
	// _ = rm.populateDisplayField(ctx, rule, req.GetFieldMasks(), resp)
	return resp, nil
}

func (rm *RuleManagerService) populateDisplayField(ctx context.Context, rule *pb.Rule, masks []pb.DisplayDataFieldMask, resp *pb.GetRuleByIdResponse) error {
	for _, m := range masks {
		switch m {
		case pb.DisplayDataFieldMask_RULE_DISPLAY_INFO:
			resp.RuleDisplayInfo = rule.GetDisplayData()
		case pb.DisplayDataFieldMask_TAG_DISPLAY_INFO:
			infoArr := make([]*ui.TagsDisplayInfo, 0, len(rule.GetRuleTags()))
			for _, v := range rule.GetRuleTags() {
				infoArr = append(infoArr, &ui.TagsDisplayInfo{
					TagId:    v.GetId(),
					IconUrls: v.GetDisplayData().GetIconUrls(),
				})
			}
			resp.TagDisplayInfo = infoArr
		case pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES, pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES_AND_SELECTOR_CTA:
			paramValues, err := rm.paramValuesDao.GetByRuleId(ctx, rule.GetId())
			if err != nil {
				logger.Error(ctx, "Failed to get rule by Id", zap.Error(err), zap.String(logger.RULE_ID, rule.GetId()))
				return err
			}
			selectorCta := make(map[ui.RuleParamType]*ui.ParamValueSelectorCTA)
			if m == pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES_AND_SELECTOR_CTA {
				selectorCta, err = rm.paramValuesSelectorCtaDao.GetByRuleId(ctx, rule.GetId())
				if err != nil {
					logger.Error(ctx, "Failed to get rule by Id", zap.Error(err), zap.String(logger.RULE_ID, rule.GetId()))
					return err
				}
			}
			mapping := make([]*ui.ParamTypeToValuesMapping, 0)
			visitedMappings := make(map[ui.RuleParamType]*ui.ParamTypeToValuesMapping)
			for paramType, params := range paramValues {
				m := &ui.ParamTypeToValuesMapping{
					RuleParamType: paramType,
					ParamValues:   params,
				}
				mapping = append(mapping, m)
				visitedMappings[paramType] = m
			}

			for paramType, cta := range selectorCta {
				m, exist := visitedMappings[paramType]
				if !exist {
					m = &ui.ParamTypeToValuesMapping{
						RuleParamType: paramType,
					}
					mapping = append(mapping, m)
				}
				m.ValSelectorCta = cta
			}
			resp.ParamValues = &ui.ParamValues{ParamValuesMap: mapping}

		case pb.DisplayDataFieldMask_REQUIRED_DATA_FIELD_MASK_UNSPECIFIED:
			fallthrough
		default:
			logger.Error(ctx, "unhandled display field mask type", zap.String(logger.FIELD_MASKS, m.String()))
			return fmt.Errorf("unhandled display field mask type")
		}
	}
	return nil
}

func (rm *RuleManagerService) GetRulesByIds(ctx context.Context, req *pb.GetRulesByIdsRequest) (*pb.GetRulesByIdsResponse, error) {
	resp := &pb.GetRulesByIdsResponse{
		Status: rpc.StatusOk(),
	}

	ruleMap, rules, err := rm.ruleDao.GetByIds(ctx, req.RuleIds)
	if err != nil {
		logger.Error(ctx, "Unable to get rules", zap.Any(logger.RULE_IDS, req.RuleIds), zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	// TODO: optimise the flow to reduce the number of DB queries. We can use joins or gorm associations to get rule and tags in same query
	ruleTags, err := rm.tagMappingsDao.GetTagsForRules(ctx, req.GetRuleIds(), []pb.TagState{pb.TagState_TAG_ACTIVE})
	if err != nil {
		logger.Error(ctx, "Failed to get tags information for rule", zap.Error(err), zap.Any(logger.RULE_IDS, req.GetRuleIds()))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	for _, rule := range rules {
		rule.RuleTags = ruleTags[rule.GetId()]
	}

	_ = rm.populateDisplayFields1(ctx, rules, req.GetFieldMasks())
	_ = rm.populateDisplayFields(ctx, rules, req.GetFieldMasks(), resp)
	resp.Rules = ruleMap
	return resp, nil
}

func (rm *RuleManagerService) populateDisplayFields(ctx context.Context, rules []*pb.Rule, masks []pb.DisplayDataFieldMask, resp interface{}) error {
	ruleNames := make([]string, 0, len(rules))
	ruleIds := make([]string, 0, len(rules))

	for _, rule := range rules {
		ruleNames = append(ruleNames, rule.GetName())
		ruleIds = append(ruleIds, rule.GetId())
	}

	for _, m := range masks {
		switch m {
		case pb.DisplayDataFieldMask_RULE_DISPLAY_INFO:
			displayInfo := make(map[string]*ui.RuleDisplayInfo)
			for _, r := range rules {
				displayInfo[r.GetName()] = r.GetDisplayData()
			}

			if _, ok := resp.(*pb.GetRulesForClientResponse); ok {
				resp.(*pb.GetRulesForClientResponse).RuleDisplayMap = displayInfo
			} else if _, ok := resp.(*pb.GetRulesByIdsResponse); ok {
				resp.(*pb.GetRulesByIdsResponse).RuleDisplayMap = displayInfo
			} else {
				logger.Error(ctx, "Failed to get rule display info for rule name, cannot identify response type", zap.Strings(logger.RULE_NAME, ruleNames))
				return fmt.Errorf("failed to get rule display info for rule")
			}
		case pb.DisplayDataFieldMask_TAG_DISPLAY_INFO:
			m := rm.getTagDisplayInfoMap(rules)

			if _, ok := resp.(*pb.GetRulesForClientResponse); ok {
				resp.(*pb.GetRulesForClientResponse).TagDisplayMap = m
			} else if _, ok := resp.(*pb.GetRulesByIdsResponse); ok {
				resp.(*pb.GetRulesByIdsResponse).TagDisplayMap = m
			} else {
				logger.Error(ctx, "Failed to get rule tag info for tags, cannot identify response type", zap.Strings(logger.RULE_NAME, ruleNames))
				return fmt.Errorf("failed to get rule display info for tag")
			}
		case pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES:
			m, err := rm.getPossibleParamValuesDisplayMap(ctx, ruleIds)
			if err != nil {
				logger.Error(ctx, "Failed to get possible param values for rules", zap.Error(err), zap.Strings(logger.RULE_IDS, ruleIds))
				continue
			}

			if _, ok := resp.(*pb.GetRulesForClientResponse); ok {
				resp.(*pb.GetRulesForClientResponse).ParamsDisplayMap = m
			} else if _, ok := resp.(*pb.GetRulesByIdsResponse); ok {
				resp.(*pb.GetRulesByIdsResponse).ParamsDisplayMap = m
			} else {
				logger.Error(ctx, "Failed to get params for tags, cannot identify response type", zap.Strings(logger.RULE_NAME, ruleNames))
				return fmt.Errorf("failed to get params for tags")
			}
		case pb.DisplayDataFieldMask_REQUIRED_DATA_FIELD_MASK_UNSPECIFIED:
			fallthrough
		default:
			logger.Error(ctx, "unhandled display field mask type", zap.String(logger.FIELD_MASKS, m.String()))
			return fmt.Errorf("unkown display field mask type")
		}
	}
	return nil
}
func (rm *RuleManagerService) getTagDisplayInfoMap(rules []*pb.Rule) map[string]*ui.TagDisplayInfos {
	m := make(map[string]*ui.TagDisplayInfos)
	for _, rule := range rules {
		infoArr := &ui.TagDisplayInfos{TagDisplayInfos: make([]*ui.TagsDisplayInfo, 0, len(rule.GetRuleTags()))}
		m[rule.GetId()] = infoArr
		for _, tag := range rule.GetRuleTags() {
			infoArr.TagDisplayInfos = append(infoArr.TagDisplayInfos, &ui.TagsDisplayInfo{
				TagId:    tag.GetId(),
				IconUrls: tag.GetDisplayData().GetIconUrls(),
			})
		}
	}
	return m
}

func (rm *RuleManagerService) getPossibleParamValuesDisplayMap(ctx context.Context, ruleIds []string) (map[string]*ui.ParamValues, error) {
	paramValues, err := rm.paramValuesDao.GetByRuleIds(ctx, ruleIds, nil)
	if err != nil {
		logger.Error(ctx, "Failed to get possible param values for rules", zap.Error(err), zap.Strings(logger.RULE_IDS, ruleIds))
		return nil, err
	}

	ctas, err := rm.paramValuesSelectorCtaDao.GetByRuleIds(ctx, ruleIds)
	if err != nil {
		logger.Error(ctx, "Failed to get param value selector cta for rules", zap.Error(err), zap.Strings(logger.RULE_IDS, ruleIds))
		return nil, err
	}

	m := make(map[string]*ui.ParamValues)
	for _, ruleId := range ruleIds {
		ctasMap, ctaMapExist := ctas[ruleId]
		paramsMap := paramValues[ruleId]
		mapping := &ui.ParamValues{ParamValuesMap: make([]*ui.ParamTypeToValuesMapping, 0)}
		m[ruleId] = mapping
		for paramType, params := range paramsMap {
			var cta *ui.ParamValueSelectorCTA
			if ctaMapExist {
				cta = ctasMap[paramType]
			}
			mapping.ParamValuesMap = append(mapping.ParamValuesMap, &ui.ParamTypeToValuesMapping{
				RuleParamType:  paramType,
				ParamValues:    params,
				ValSelectorCta: cta,
			})

		}
	}
	return m, nil
}

func (rm *RuleManagerService) GetRulesForClient(ctx context.Context, req *pb.GetRulesForClientRequest) (*pb.GetRulesForClientResponse, error) {
	resp := &pb.GetRulesForClientResponse{
		Status: rpc.StatusOk(),
	}
	var paginate bool
	var pageToken *pagination.PageToken
	if req.GetPageContext() != nil {
		var err error
		pageToken, err = pagination.GetPageToken(req.GetPageContext())
		if err != nil {
			logger.Error(ctx, "Failed to get page token", zap.Error(err))
			resp.Status = rpc.StatusFromError(err)
			return resp, nil
		}
		paginate = true
	}

	var assignedGroups []commontypes.UserGroup
	if req.GetActorId() != "" {
		var err error
		assignedGroups, err = rm.getAssignedUserGroups(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "Error in getting assigned user groups", zap.Error(err))
			resp.Status = rpc.StatusFromError(err)
			return resp, nil
		}
	}

	rules, pageCtx, err := rm.ruleDao.GetRules(ctx, req.Client, req.States, req.GetCategories(), paginate, pageToken, req.GetPageContext().GetPageSize(),
		nil, nil, assignedGroups, req.GetPlatform(), req.GetAppVersion(), req.GetRuleTypes())
	if err != nil {
		logger.Error(ctx, "Failed to get rules", zap.String("client", req.Client.String()), zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	err = rm.addRuleTags(ctx, rules)
	if err != nil {
		logger.Error(ctx, "Failed to get tags information for rules", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	resp.Rules = rules
	resp.PageContext = pageCtx
	_ = rm.populateDisplayFields1(ctx, rules, req.GetFieldMasks())
	_ = rm.populateDisplayFields(ctx, rules, req.GetFieldMasks(), resp)
	return resp, nil
}

func (rm *RuleManagerService) addRuleTags(ctx context.Context, rules []*pb.Rule) error {
	ruleIds := make([]string, 0, len(rules))
	for _, rule := range rules {
		ruleIds = append(ruleIds, rule.GetId())
	}

	// TODO: optimise the flow to reduce the number of DB queries. We can use joins or gorm associations to get rule and tags in same query
	ruleTags, err := rm.tagMappingsDao.GetTagsForRules(ctx, ruleIds, []pb.TagState{pb.TagState_TAG_ACTIVE})
	if err != nil {
		logger.Error(ctx, "Failed to get tags information for rule", zap.Error(err), zap.Any(logger.RULE_IDS, ruleIds))
		return err
	}

	for _, rule := range rules {
		rule.RuleTags = ruleTags[rule.GetId()]
	}
	return nil
}

func (rm *RuleManagerService) SubscribeRule(ctx context.Context, req *pb.SubscribeRuleRequest) (*pb.SubscribeRuleResponse, error) {
	actorId := req.GetSubscriptionData().GetActorId()
	requestTime := storage.PgNow()
	// checking if rule for provided id exists
	rule, err := rm.GetRuleById(ctx, &pb.GetRuleByIdRequest{RuleId: req.GetSubscriptionData().GetRuleId()})
	if err != nil {
		logger.Error(ctx, "Failed to get rule for Id", zap.Error(err), zap.String(logger.RULE_ID, req.GetSubscriptionData().GetRuleId()))
		rm.publishSubscribeRuleEvent(ctx, actorId, requestTime, rule.GetRule(), req.GetSubscriptionData(), events.Failure, err.Error())
		return &pb.SubscribeRuleResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}
	if rule.Rule.Category == pb.RuleCategory_AUTO_INVEST && rm.conf.Flags().CheckWealthOnboardingStatus() {
		onboarded, resp := rm.validateWealthOnboardingStatus(ctx, actorId)
		if !onboarded {
			return resp, nil
		}
	}

	// validate the incoming request
	if err = rm.validateSubscriptionRequest(ctx, req, rule.GetRule()); err != nil {
		logger.Error(ctx, "Validation failed for subscribe rule request", zap.Error(err), zap.Any(logger.SUBSCRIPTION_ID, req.GetSubscriptionData()))
		rm.publishSubscribeRuleEvent(ctx, actorId, requestTime, rule.GetRule(), req.GetSubscriptionData(), events.Failure, err.Error())
		return &pb.SubscribeRuleResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	logger.Info(ctx, "Going to subscribe rule", zap.String(logger.RULE_ID, req.GetSubscriptionData().GetRuleId()))
	req.SubscriptionData.StateChangeReason = pb.SubscriptionStateChangeReason_NEW_SUBSCRIPTION

	// TODO(anand): Solve for concurrency concern here: https://github.com/epiFi/gamma/pull/8676/files#r597734663
	subscription, err := rm.subscriptionDao.Create(ctx, req.SubscriptionData)
	if err != nil {
		if rms.IsRuleSubscriptionClientRequestIdAlreadyExist(err) ||
			rms.IsSubscriptionRuntimeInfoClientRequestIdAlreadyExist(err) {
			logger.Info(ctx, "rule subscription already exists", zap.String(logger.RULE_ID, req.SubscriptionData.RuleId), zap.String(logger.ACTOR_ID, req.SubscriptionData.ActorId))
			return &pb.SubscribeRuleResponse{
				Status: rpc.StatusAlreadyExists(),
			}, nil
		}
		logger.Error(ctx, "unable to subscribe rule to user", zap.String(logger.RULE_ID, req.SubscriptionData.RuleId), zap.Error(err))
		return &pb.SubscribeRuleResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	rm.publishSubscribeRuleEvent(ctx, actorId, requestTime, rule.GetRule(), subscription, events.Success, "")

	err = rm.addNextExecutionDate(ctx, subscription, rule.GetRule())
	if err != nil {
		logger.Error(ctx, "error in adding next execution date to subscription", zap.Error(err), zap.String(logger.SUBSCRIPTION_ID, subscription.GetId()))
		return &pb.SubscribeRuleResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	err = rm.publishInvestmentEvent(ctx, req.GetSubscriptionData(), rule.GetRule())
	// not blocking anything if the publishing fails.
	if err != nil {
		logger.Error(ctx, "error in publishInvestmentEvent", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.RULE_ID, rule.GetRule().GetId()), zap.Error(err))
	}

	// triggering USSIP Setup Success Server Event
	if rule.GetRule().GetCategory() == pb.RuleCategory_US_STOCKS_SIP {
		err := rm.publishUssSipEvent(ctx, subscription, subscription.GetState(), "new")
		// not blocking anything if the publishing fails.
		if err != nil {
			logger.Error(ctx, "error in publishUssSipEvent", zap.String(logger.SUBSCRIPTION_ID, subscription.GetId()), zap.Error(err))
		}
	}

	logger.Info(ctx, "Rule subscription successful", zap.String(logger.SUBSCRIPTION_ID, subscription.GetId()),
		zap.String(logger.RULE_ID, subscription.GetRuleId()))
	return &pb.SubscribeRuleResponse{
		Status:           rpc.StatusOk(),
		SubscriptionData: subscription,
	}, nil
}

func (rm *RuleManagerService) validateWealthOnboardingStatus(ctx context.Context, actorId string) (bool, *pb.SubscribeRuleResponse) {
	// onboarding type is wealth since this is not an intent of investment, investment onboarding happens at the time of order placement
	statusResp, err := rm.wealthOnboardingClient.GetOnboardingStatus(ctx, &wealthpb.GetOnboardingStatusRequest{
		ActorId:        actorId,
		OnboardingType: wealthpb.OnboardingType_ONBOARDING_TYPE_WEALTH,
	})
	if err = epifigrpc.RPCError(statusResp, err); err != nil {
		logger.Error(ctx, "error checking onboarding status of user", zap.Error(err))
		return false, &pb.SubscribeRuleResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}
	}
	if statusResp.GetOnboardingStatus() != wealthpb.OnboardingStatus_ONBOARDING_STATUS_COMPLETED {
		logger.Info(ctx, "user has some wealth onboarding steps to complete", zap.Error(err),
			zap.String(logger.STATUS, statusResp.GetOnboardingStatus().String()),
			zap.String(logger.STAGE, statusResp.GetCurrentStep().String()))
		return false, &pb.SubscribeRuleResponse{
			Status: rpc.StatusPermissionDeniedWithDebugMsg("user has some wealth onboarding steps to complete"),
		}
	}
	return true, nil
}

func (rm *RuleManagerService) UpdateRuleSubscription(ctx context.Context, req *pb.UpdateRuleSubscriptionRequest) (*pb.UpdateRuleSubscriptionResponse, error) {
	requestTime := storage.PgNow()
	subscriptionRes, err := rm.GetSubscriptionById(ctx, &pb.GetSubscriptionByIdRequest{SubscriptionId: req.GetRuleSubscriptionId()})
	if err2 := epifigrpc.RPCError(subscriptionRes, err); err2 != nil {
		return &pb.UpdateRuleSubscriptionResponse{
			Status: rpc.StatusFromError(err2),
		}, nil
	}

	// validate the incoming request
	if err = rm.validateUpdateSubscriptionRequest(ctx, req, subscriptionRes.GetRule(), subscriptionRes.GetSubscriptionData().GetActorId(),
		subscriptionRes.GetSubscriptionData().GetId()); err != nil {
		logger.Error(ctx, "Validation failed for create rule request", zap.Error(err))
		rm.publishRuleEditedEvent(ctx, subscriptionRes.GetSubscriptionData().GetActorId(), requestTime, subscriptionRes.GetRule(),
			subscriptionRes.GetSubscriptionData(), events.Failure, err.Error())
		return &pb.UpdateRuleSubscriptionResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	// if rule type is auto-invest and subType is SIP, Fittt needs update investments to update SIP details at their end first
	// and then update our rule subscription,
	// reason: if SIP is updated and rule subscription update fails, at the time of rule execution a new SIP will be created
	// there might be problem if subscription gets updated but SIP is not updated, as minimum amount for SIP can
	// be less, and if the subscription is updated with less amount and investment service does not know about the
	// conversion of the subscription to SIP, orders may fail
	// NOTE: This doesn't apply to US stocks SIPs.
	// US stocks SIPs are a construct internal to Fi and do not have any
	// dependency on external factors like AMCs having minimum amount constraints on SIPs.
	err = rm.updateSipDetails(ctx, req.GetUpdatedFields(), req.GetRuleSubscriptionId(),
		subscriptionRes.GetSubscriptionData().GetRuleParamValues().GetRuleParamValues(), req.GetUserDefinedValues().GetRuleParamValues())
	if err != nil {
		logger.Error(ctx, "error updating sip details on investments", zap.Error(err))
		rm.publishRuleEditedEvent(ctx, subscriptionRes.GetSubscriptionData().GetActorId(), requestTime, subscriptionRes.GetRule(),
			subscriptionRes.GetSubscriptionData(), events.Failure, err.Error())
		return &pb.UpdateRuleSubscriptionResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	subscription := &pb.RuleSubscription{
		RuleParamValues:       req.GetUserDefinedValues(),
		ValidTill:             req.GetValidTill(),
		Id:                    req.GetRuleSubscriptionId(),
		State:                 req.GetState(),
		StateChangeReason:     req.GetStateChangeReason(),
		StateChangeProvenance: req.GetStateChangeProvenance(),
	}

	updatedSubscription := &pb.RuleSubscription{}
	txnErr := rm.rmsDbTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		updatedSubscription, err = rm.subscriptionDao.Update(txnCtx, req.GetRuleSubscriptionId(), subscription, req.GetUpdatedFields())
		return err
	})

	if txnErr != nil {
		logger.Error(ctx, "Failed to update rule subscription", zap.Error(txnErr), zap.String(logger.SUBSCRIPTION_ID, req.GetRuleSubscriptionId()))
		rm.publishRuleEditedEvent(ctx, subscriptionRes.GetSubscriptionData().GetActorId(), requestTime, subscriptionRes.GetRule(),
			subscriptionRes.GetSubscriptionData(), events.Failure, txnErr.Error())
		return &pb.UpdateRuleSubscriptionResponse{
			Status: rpc.StatusFromError(txnErr),
		}, nil
	}
	rm.publishSubscriptionUpdateActivity(ctx, subscriptionRes.GetRule(), subscriptionRes.GetSubscriptionData(), updatedSubscription, req.GetUpdatedFields(), req.GetStateChangeReason())
	rm.publishRuleEditedEvent(ctx, subscriptionRes.GetSubscriptionData().GetActorId(), requestTime, subscriptionRes.GetRule(),
		updatedSubscription, events.Success, "")

	err = rm.addNextExecutionDate(ctx, updatedSubscription, subscriptionRes.GetRule())
	if err != nil {
		logger.Error(ctx, "error in adding next execution date to subscription", zap.Error(err), zap.String(logger.SUBSCRIPTION_ID, subscription.GetId()))
		return &pb.UpdateRuleSubscriptionResponse{
			Status: rpc.StatusFromError(err),
		}, nil
	}

	logger.Info(ctx, "UserRule subscription edit successful", zap.String(logger.SUBSCRIPTION_ID, req.GetRuleSubscriptionId()))
	return &pb.UpdateRuleSubscriptionResponse{
		Status:           rpc.StatusOk(),
		SubscriptionData: updatedSubscription,
	}, nil
}

func getMfValFromRuleParams(ruleParams map[string]*pb.Value) *pb.MutualFundValue {
	for _, v := range ruleParams {
		if v.GetMutualFundVal() != nil {
			return v.GetMutualFundVal()
		}
	}
	return nil
}

// returns only those rule params that are updated
func updatedRuleParams(ctx context.Context, prevRuleParams, newRuleParams map[string]*pb.Value) []*pb.Value {
	var updatedRuleParams []*pb.Value
	for key, newValue := range newRuleParams {
		prevValue := prevRuleParams[key]
		if !areValuesSame(ctx, prevValue, newValue) {
			updatedRuleParams = append(updatedRuleParams, newValue)
		}
	}
	logger.Debug(ctx, "updatedRuleParams()", zap.Any("updatedRuleParams", updatedRuleParams))
	return updatedRuleParams
}

func areValuesSame(ctx context.Context, prevValue, newValue *pb.Value) bool {
	if diff := cmp.Diff(prevValue, newValue, protocmp.Transform()); diff != "" {
		logger.Debug(ctx, "areValuesSame()", zap.Any("diff", diff))
		return false
	}
	return true
}

func (rm *RuleManagerService) updateSipDetails(ctx context.Context, updatedFields []pb.RuleSubscriptionFieldMask,
	subscriptionId string, previousRuleParams, newRuleParams map[string]*pb.Value) error {
	logger.Debug(ctx, "updateSipDetails", zap.Any("updatedFields", updatedFields), zap.String(logger.SUBSCRIPTION_ID, subscriptionId),
		zap.Any("previousRuleParams", previousRuleParams), zap.Any("newRuleParams", newRuleParams))
	var mfVal *pb.MutualFundValue
	for _, field := range updatedFields {
		if field == pb.RuleSubscriptionFieldMask_RULE_PARAM_VALUES {
			mfVal = getMfValFromRuleParams(newRuleParams)
			if mfVal != nil {
				break
			}
		}
	}

	if mfVal != nil && mfVal.GetOrderSubType() == mforderpb.OrderSubType_BUY_SIP {
		// if the subType is not SIP, need not update investment
		updatedRuleParams := updatedRuleParams(ctx, previousRuleParams, newRuleParams)

		handleSipUpdateReq := &mforderpb.HandleFitttSubscriptionUpdateRequest{
			OrderSubType:        mfVal.GetOrderSubType(),
			FitttSubscriptionId: subscriptionId,
			MutualFundId:        mfVal.GetMfId(),
		}

		for _, val := range updatedRuleParams {
			switch {
			case val.GetMoneyVal() != nil:
				handleSipUpdateReq.UpdatedAmount = val.GetMoneyVal().GetBeMoney()
			case val.GetIntVal() != 0:
				handleSipUpdateReq.UpdatedSipExecution = &mforderpb.HandleFitttSubscriptionUpdateRequest_SipDate{
					// passing the month and year as zero as per date documentation
					SipDate: &googleDateType.Date{
						Day: val.GetIntVal(),
					},
				}
			case val.GetStrVal() != "":
				handleSipUpdateReq.UpdatedSipExecution = &mforderpb.HandleFitttSubscriptionUpdateRequest_SipDay{
					SipDay: googleDayType.DayOfWeek(googleDayType.DayOfWeek_value[strings.ToUpper(val.GetStrVal())]),
				}
			}
		}

		logger.Debug(ctx, "HandleFitttSubscriptionUpdate request", zap.Any("handleSipUpdateReq", handleSipUpdateReq))
		mfUpdateResp, err := rm.mfOrderManagerClient.HandleFitttSubscriptionUpdate(ctx, handleSipUpdateReq)
		if err2 := epifigrpc.RPCError(mfUpdateResp, err); err2 != nil {
			logger.Error(ctx, "failed to update SIP, investment API HandleFitttSubscriptionUpdate resulted into error",
				zap.String(logger.SUBSCRIPTION_ID, subscriptionId), zap.Error(err2))
			return err2
		}
		logger.Debug(ctx, "updated sip details in investments", zap.Any("mfUpdateResp", mfUpdateResp))
	}
	return nil
}

func (rm *RuleManagerService) PauseResumeSubscription(ctx context.Context, req *pb.PauseResumeSubscriptionRequest) (*pb.PauseResumeSubscriptionResponse, error) {
	subscription := &pb.RuleSubscription{
		State: req.State,
	}
	existingSubscription, err := rm.subscriptionDao.GetById(ctx, req.GetSubscriptionId())
	if err != nil {
		logger.Error(ctx, "Failed to get subscription", zap.Error(err), zap.String(logger.SUBSCRIPTION_ID, req.GetSubscriptionId()))
		return &pb.PauseResumeSubscriptionResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, err
	}
	updatedFields := []pb.RuleSubscriptionFieldMask{pb.RuleSubscriptionFieldMask_STATE}
	var updatedSubscription *pb.RuleSubscription
	txnErr := rm.rmsDbTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		var err error
		updatedSubscription, err = rm.subscriptionDao.Update(txnCtx, req.GetSubscriptionId(), subscription, updatedFields)
		return err
	})

	if txnErr != nil {
		logger.Error(ctx, "Failed to update state of rule subscription", zap.Error(txnErr), zap.String(logger.SUBSCRIPTION_ID, req.GetSubscriptionId()))
		return &pb.PauseResumeSubscriptionResponse{
			Status: rpc.StatusInternalWithDebugMsg(txnErr.Error()),
		}, txnErr
	}
	rm.publishStateChangeActivity(ctx, existingSubscription, updatedSubscription, pb.SubscriptionStateChangeReason_UNSPECIFIED)
	logger.Info(ctx, "UserRule subscription edit successful", zap.String(logger.SUBSCRIPTION_ID, req.SubscriptionId))
	return &pb.PauseResumeSubscriptionResponse{
		Status: rpc.StatusOk(),
		State:  updatedSubscription.GetState(),
	}, txnErr
}

func getParamsMap(params []*pb.RuleParam) map[string]*pb.RuleParam {
	paramsMap := make(map[string]*pb.RuleParam)
	for _, param := range params {
		paramsMap[param.GetName()] = param
	}
	return paramsMap
}

func (rm *RuleManagerService) publishSubscriptionUpdateActivity(ctx context.Context, rule *pb.Rule, oldSubscription,
	updatedSubscription *pb.RuleSubscription, updatedFields []pb.RuleSubscriptionFieldMask, stateChangeReason pb.SubscriptionStateChangeReason) {
	for _, field := range updatedFields {
		switch field {
		case pb.RuleSubscriptionFieldMask_STATE:
			rm.publishStateChangeActivity(ctx, oldSubscription, updatedSubscription, stateChangeReason)
		case pb.RuleSubscriptionFieldMask_RULE_PARAM_VALUES:
			rm.publishParamUpdateActivity(ctx, rule, oldSubscription, updatedSubscription)
		default:
			logger.Error(ctx, "Unknown field mask", zap.String("fieldMask", field.String()),
				zap.String(logger.SUBSCRIPTION_ID, updatedSubscription.GetId()))
		}
	}
}

func (rm *RuleManagerService) publishStateChangeActivity(ctx context.Context, oldSubscription, updatedSubscription *pb.RuleSubscription, reason pb.SubscriptionStateChangeReason) {
	var actionType actionpb.ActionType
	switch updatedSubscription.GetState() {
	case pb.RuleSubscriptionState_INACTIVE:
		actionType = actionpb.ActionType_SUBSCRIPTION_PAUSED
	case pb.RuleSubscriptionState_ACTIVE:
		actionType = actionpb.ActionType_SUBSCRIPTION_RESUMED
	default:
		logger.Error(ctx, "Unknown subscription state", zap.String("state", updatedSubscription.GetState().String()),
			zap.String(logger.SUBSCRIPTION_ID, updatedSubscription.GetId()))
	}
	publishAction := &orchestratorpb.ProcessRmsActionRequest{
		Action: &actionpb.ActionData{Data: &actionpb.ActionData_SubscriptionActivity{
			SubscriptionActivity: &actionpb.SubscriptionActivity{
				Time:       updatedSubscription.GetVersionValidFrom(),
				ActionType: actionType,
				Activity: &actionpb.SubscriptionActivity_StateChangeReason{
					StateChangeReason: &actionpb.SubscriptionStateChange{
						From:   oldSubscription.GetState().String(),
						To:     updatedSubscription.GetState().String(),
						Reason: reason.String(),
					},
				},
			}}},
		SubscriptionId: updatedSubscription.GetId(),
		ActorId:        updatedSubscription.GetActorId(),
		// generating new uuid since these are not part of rule execution
		RuleExecutionKey: uuid.New().String(),
	}
	_, err := rm.actionPublisher.Publish(ctx, publishAction)
	if err != nil {
		logger.Error(ctx, "failed to publish update activity action", zap.Error(err),
			zap.Any("action", publishAction))
	}
}

func (rm *RuleManagerService) publishParamUpdateActivity(ctx context.Context, rule *pb.Rule, oldSubscription, updatedSubscription *pb.RuleSubscription) {
	params := getParamsMap(rule.GetDescription().GetInputParams())
	updatedParamValues := updatedSubscription.GetRuleParamValues().GetRuleParamValues()
	var paramUpdates []*actionpb.ParamUpdate
	for paramName, value := range oldSubscription.GetRuleParamValues().GetRuleParamValues() {
		updatedValue := updatedParamValues[paramName]
		paramAny, err := anypb.New(params[paramName])
		if err != nil {
			logger.Error(ctx, "failed to convert rule param as anypb", zap.Error(err), zap.Any("param", params[paramName]))
			return
		}
		fromValAny, err := anypb.New(value)
		if err != nil {
			logger.Error(ctx, "failed to convert rule param value as anypb", zap.Error(err), zap.Any("param", params[paramName]))
		}
		toValAny, err := anypb.New(updatedValue)
		if err != nil {
			logger.Error(ctx, "failed to convert rule param value as anypb", zap.Error(err), zap.Any("param", params[paramName]))
		}

		if !proto.Equal(value, updatedValue) {
			paramUpdates = append(paramUpdates, &actionpb.ParamUpdate{
				Param:       paramAny,
				UpdatedFrom: fromValAny,
				UpdatedTo:   toValAny,
			})
		}
	}

	publishAction := &orchestratorpb.ProcessRmsActionRequest{
		Action: &actionpb.ActionData{Data: &actionpb.ActionData_SubscriptionActivity{
			SubscriptionActivity: &actionpb.SubscriptionActivity{
				Time:       updatedSubscription.VersionValidFrom,
				ActionType: actionpb.ActionType_SUBSCRIPTION_UPDATED,
				Activity: &actionpb.SubscriptionActivity_ParamUpdates{
					ParamUpdates: &actionpb.ParamUpdates{Update: paramUpdates},
				},
			}}},
		SubscriptionId: updatedSubscription.GetId(),
		ActorId:        updatedSubscription.GetActorId(),
		// generating new uuid since these are not part of rule execution
		RuleExecutionKey: uuid.New().String(),
	}
	_, err := rm.actionPublisher.Publish(ctx, publishAction)
	if err != nil {
		logger.Error(ctx, "failed to publish update activity action", zap.Error(err),
			zap.Any("action", publishAction))
	}
}

func (rm *RuleManagerService) UpdateSubscriptionState(ctx context.Context, req *pb.UpdateSubscriptionStateRequest) (*pb.UpdateSubscriptionStateResponse, error) {
	requestTime := storage.PgNow()

	subId := req.GetSubscriptionId()
	if subId == "" && len(req.GetSubIds()) > 0 {
		subId = req.GetSubIds()[0]
	}

	if subId == "" {
		logger.Error(ctx, "error in updating subscriptions state, subId is empty", zap.String(logger.SUBSCRIPTION_ID, req.GetSubscriptionId()),
			zap.Strings("ids_list", req.GetSubIds()))
		return &pb.UpdateSubscriptionStateResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// checking if subscription Id exists for requested actor
	subscriptionRes, err := rm.GetSubscriptionById(ctx, &pb.GetSubscriptionByIdRequest{
		SubscriptionId: subId,
	})
	if err2 := epifigrpc.RPCError(subscriptionRes, err); err2 != nil {
		return &pb.UpdateSubscriptionStateResponse{
			Status: rpc.StatusFromError(err2),
		}, nil
	}

	// checking if the subscription belongs to the actor specified in the request
	// It helps in preventing IDOR access of subscription by actor to whom the subscription does not belong
	if !strings.EqualFold(subscriptionRes.GetSubscriptionData().GetActorId(), req.GetActorId()) {
		logger.Error(ctx, "subscription's actor Id does not match with requested actorId",
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.String("subscription_actor", subscriptionRes.GetSubscriptionData().GetActorId()))
		return &pb.UpdateSubscriptionStateResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}

	if len(req.GetSubIds()) > 1 {
		err := rm.subscriptionDao.UpdateStateBulk(ctx, req.GetSubIds(), req.GetState(), req.GetStateChangeReason(), req.GetStateChangeProvenance())
		if err != nil {
			logger.Error(ctx, "Failed to update state for subscriptions", zap.Strings(logger.SUBSCRIPTION_ID, req.GetSubIds()),
				zap.String("stateChangeReason", req.GetStateChangeReason().String()), zap.String("provenance", req.GetStateChangeProvenance().String()))
			return &pb.UpdateSubscriptionStateResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
		return &pb.UpdateSubscriptionStateResponse{
			Status: rpc.StatusOk(),
		}, nil
	}

	if req.GetSubscriptionId() == "" && len(req.GetSubIds()) == 1 {
		req.SubscriptionId = req.GetSubIds()[0]
	}

	subscription := &pb.RuleSubscription{
		State:                 req.GetState(),
		StateChangeReason:     req.GetStateChangeReason(),
		StateChangeProvenance: req.GetStateChangeProvenance(),
	}
	// validate the incoming request
	if err = rm.validateSubscriptionStateTransition(ctx, req.GetState(), req.GetStateChangeReason(), req.GetStateChangeProvenance(), req.GetSubscriptionId()); err != nil {
		logger.Error(ctx, "Validation failed for update subscription state", zap.Error(err),
			zap.Any(logger.SUBSCRIPTION_ID, req.GetSubscriptionId()), zap.String("requestedState", req.GetState().String()))

		rm.publishRuleStateChangedEvent(ctx, subscriptionRes.GetSubscriptionData().GetActorId(), requestTime, subscriptionRes.GetRule(),
			subscriptionRes.GetSubscriptionData(), events.Failure, err.Error())

		return &pb.UpdateSubscriptionStateResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	updatedFields := []pb.RuleSubscriptionFieldMask{pb.RuleSubscriptionFieldMask_STATE}
	var updatedSubscription *pb.RuleSubscription
	txnErr := rm.rmsDbTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		updatedSubscription, err = rm.subscriptionDao.Update(txnCtx, req.SubscriptionId, subscription, updatedFields)
		return err
	})
	if txnErr != nil {
		logger.Error(ctx, "Failed to update state of rule subscription", zap.Error(txnErr), zap.String(logger.SUBSCRIPTION_ID, req.GetSubscriptionId()))

		rm.publishRuleStateChangedEvent(ctx, subscriptionRes.GetSubscriptionData().GetActorId(), requestTime, subscriptionRes.GetRule(),
			subscriptionRes.GetSubscriptionData(), events.Failure, err.Error())

		return &pb.UpdateSubscriptionStateResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	err = rm.pauseCorrespondingSip(ctx, req.GetSubscriptionId(), subscriptionRes.GetSubscriptionData().GetRuleParamValues().GetRuleParamValues())
	if err != nil {
		logger.Error(ctx, "failed to pause SIP, investment API HandleFitttSubscriptionUpdate resulted into error",
			zap.String(logger.SUBSCRIPTION_ID, req.GetSubscriptionId()), zap.Error(err))
	}

	rm.publishRuleStateChangedEvent(ctx, subscriptionRes.GetSubscriptionData().GetActorId(), requestTime, subscriptionRes.GetRule(),
		subscription, events.Success, "")
	rm.publishStateChangeActivity(ctx, subscriptionRes.GetSubscriptionData(), updatedSubscription, req.GetStateChangeReason())

	// triggering USSSIP Deactivate and USSSIP modified setup server event
	if subscriptionRes.GetRule().GetCategory() == pb.RuleCategory_US_STOCKS_SIP {
		err := rm.publishUssSipEvent(ctx, subscriptionRes.GetSubscriptionData(), subscription.GetState(), "modified")
		// not blocking anything if the publishing fails.
		if err != nil {
			logger.Error(ctx, "error in publishUssSipEvent", zap.String(logger.SUBSCRIPTION_ID, subscription.GetId()), zap.Error(err))
		}
	}
	logger.Info(ctx, "Subscription state updated", zap.String(logger.SUBSCRIPTION_ID, req.SubscriptionId))
	return &pb.UpdateSubscriptionStateResponse{
		Status:       rpc.StatusOk(),
		Subscription: updatedSubscription,
	}, nil
}

func (rm *RuleManagerService) pauseCorrespondingSip(ctx context.Context, subscriptionId string, ruleParams map[string]*pb.Value) error {
	mfVal := getMfValFromRuleParams(ruleParams)
	if mfVal != nil && mfVal.GetOrderSubType() == mforderpb.OrderSubType_BUY_SIP {
		logger.Debug(ctx, "sip pause requested for subscription", zap.String(logger.SUBSCRIPTION_ID, subscriptionId))
		mfUpdateResp, err := rm.mfOrderManagerClient.HandleFitttSubscriptionUpdate(ctx, &mforderpb.HandleFitttSubscriptionUpdateRequest{
			OrderSubType:        mforderpb.OrderSubType_BUY_SIP,
			FitttSubscriptionId: subscriptionId,
		})
		if err2 := epifigrpc.RPCError(mfUpdateResp, err); err2 != nil {
			return err2
		}
	}
	return nil
}

func (rm *RuleManagerService) UnsubscribeRule(ctx context.Context, req *pb.UnsubscribeRuleRequest) (*pb.UnsubscribeRuleResponse, error) {
	// unsubscription as in deletion of subscription is not required as of now
	// we can use PauseResumeSubscription for changing state of subscription (Active and Inactive)
	return &pb.UnsubscribeRuleResponse{Status: rpc.StatusInternalWithDebugMsg("not implemented")}, nil
}

func (rm *RuleManagerService) GetSubscriptionById(ctx context.Context, req *pb.GetSubscriptionByIdRequest) (*pb.GetSubscriptionByIdResponse, error) {
	subscription, err := rm.subscriptionDao.GetById(ctx, req.SubscriptionId)
	if err != nil {
		logger.Error(ctx, "Unable to get actor rule subscription", zap.String(logger.SUBSCRIPTION_ID, req.SubscriptionId), zap.Error(err))
		return &pb.GetSubscriptionByIdResponse{
			Status:           rpc.StatusInternalWithDebugMsg(err.Error()),
			SubscriptionData: nil,
		}, err
	}

	ruleResp, err := rm.GetRuleById(ctx, &pb.GetRuleByIdRequest{
		RuleId:     subscription.GetRuleId(),
		FieldMasks: req.GetFieldMasks(),
	})
	if err != nil {
		logger.Error(ctx, "Unable to get rule", zap.String(logger.RULE_ID, subscription.GetRuleId()), zap.Error(err))
		return &pb.GetSubscriptionByIdResponse{
			Status:           rpc.StatusInternalWithDebugMsg(err.Error()),
			SubscriptionData: nil,
		}, err
	}

	return &pb.GetSubscriptionByIdResponse{
		Status:           rpc.StatusOk(),
		SubscriptionData: subscription,
		Rule:             ruleResp.GetRule(),
		RuleDisplayInfo:  ruleResp.GetRuleDisplayInfo(),
		TagDisplayInfo:   ruleResp.GetTagDisplayInfo(),
		ParamValues:      ruleResp.GetParamValues(),
	}, nil
}

func (rm *RuleManagerService) GetSubscriptionsByActorId(ctx context.Context, req *pb.GetSubscriptionsByActorIdRequest) (*pb.GetSubscriptionsByActorIdResponse, error) {
	var paginate bool
	var pageToken *pagination.PageToken
	if req.GetPageContext() != nil {
		var err2 error
		pageToken, err2 = pagination.GetPageToken(req.GetPageContext())
		if err2 != nil {
			logger.Error(ctx, "Failed to get token", zap.Error(err2))
			return nil, err2
		}
		paginate = true
	}
	subscriptions, pageCtxResp, err := rm.subscriptionDao.GetSubscriptionsByActorId(ctx, req.ActorId, nil,
		req.CreatedAfter, req.CreatedBefore, req.UpdatedAfter, req.UpdatedBefore,
		req.GetStates(), nil, nil, nil, paginate, pageToken, req.GetPageContext().GetPageSize(),
	)
	if err != nil {
		logger.Error(ctx, "Unable to get rules subscribed by actor", zap.String(logger.ACTOR_ID, req.ActorId), zap.Error(err))
		return &pb.GetSubscriptionsByActorIdResponse{
			Status:           rpc.StatusInternalWithDebugMsg(err.Error()),
			SubscriptionData: nil,
			ActorId:          "",
		}, err
	}

	return &pb.GetSubscriptionsByActorIdResponse{
		Status:           rpc.StatusOk(),
		SubscriptionData: subscriptions,
		Client:           req.Client,
		ActorId:          req.ActorId,
		PageContext:      pageCtxResp,
	}, nil
}

// GetActiveSubscriptionsByActorId returns active subscriptions for an Actor per client (eg Fittt)
func (rm *RuleManagerService) GetActiveSubscriptionsByActorId(ctx context.Context, req *pb.GetActiveSubscriptionsByActorIdRequest) (*pb.GetActiveSubscriptionsByActorIdResponse, error) {

	subscriptions, err := rm.subscriptionDao.GetActiveSubscriptionsByActorId(ctx, req.ActorId, nil, req.GetClient())
	if err != nil {
		logger.Error(ctx, "Failed to get active subscriptions by actor", zap.String(logger.ACTOR_ID, req.ActorId), zap.Error(err))
		return &pb.GetActiveSubscriptionsByActorIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &pb.GetActiveSubscriptionsByActorIdResponse{
		Status:           rpc.StatusOk(),
		SubscriptionData: subscriptions,
		Client:           req.Client,
		ActorId:          req.ActorId,
	}, nil
}

// get count of active subscriptions for an actor per client (eg Fittt)
func (rm *RuleManagerService) GetActiveSubsCount(ctx context.Context, req *pb.GetActiveSubsCountRequest) (*pb.GetActiveSubsCountResponse, error) {
	count, err := rm.subscriptionRuntimeInfoDao.GetCount(ctx, &dao.GetCountRequest{
		ActorId:   req.GetActorId(),
		CountMask: []dao.CountFieldMask{dao.SUBSCRIPTION_COUNT_FOR_ACTOR},
		States:    []pb.RuleSubscriptionState{pb.RuleSubscriptionState_ACTIVE},
	})
	if err != nil {
		logger.Error(ctx, "Failed to get active subscriptions count by actor", zap.String(logger.ACTOR_ID_V2, req.ActorId), zap.Error(err))
		return &pb.GetActiveSubsCountResponse{
			Status:            rpc.StatusInternalWithDebugMsg(err.Error()),
			SubscriptionCount: 0,
		}, err
	}

	return &pb.GetActiveSubsCountResponse{
		Status:            rpc.StatusOk(),
		SubscriptionCount: count.CountMap[dao.SUBSCRIPTION_COUNT_FOR_ACTOR],
	}, nil
}

// get count of subscriptions for actor in different states
func (rm *RuleManagerService) GetSubsCount(ctx context.Context, req *pb.GetSubsCountRequest) (*pb.GetSubsCountResponse, error) {
	resp := &pb.GetSubsCountResponse{Status: rpc.StatusOk()}
	subsCountPerState, subsCountPerRule, subsCountPerCategory, err := rm.subscriptionDao.GetSubsCount(ctx, req.ActorId, req.GetRuleIds(),
		req.GetClient(), nil, nil)
	if err != nil {
		logger.Error(ctx, "Failed to get active subscriptions count by actor", zap.String(logger.ACTOR_ID, req.ActorId), zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	resp.SubscriptionCount = subsCountPerState
	resp.SubsCountPerRule = subsCountPerRule
	resp.SubsCountPerCategoryAndState = subsCountPerCategory
	return resp, nil
}

// returns:
// 1. the number of unique actors that have subscribed to the rules
func (rm *RuleManagerService) GetSubscribersCountForRule(ctx context.Context, req *pb.GetSubscribersCountForRuleRequest) (*pb.GetSubscribersCountForRuleResponse, error) {
	userCount, err := rm.subscriptionRuntimeInfoDao.GetCount(ctx, &dao.GetCountRequest{
		RuleIds:   req.GetRuleIds(),
		CountMask: []dao.CountFieldMask{dao.UNIQUE_SUBSCRIBERS},
		States:    []pb.RuleSubscriptionState{pb.RuleSubscriptionState_ACTIVE},
	})
	if err != nil {
		logger.Error(ctx, "Failed to get user count for rule subscription", zap.Strings(logger.RULE_IDS, req.GetRuleIds()), zap.Error(err))
		return &pb.GetSubscribersCountForRuleResponse{
			Status:    rpc.StatusInternalWithDebugMsg(err.Error()),
			UserCount: 0,
		}, err
	}

	return &pb.GetSubscribersCountForRuleResponse{
		Status:    rpc.StatusOk(),
		UserCount: userCount.CountMap[dao.UNIQUE_SUBSCRIBERS],
	}, nil
}

// nolint: dupl
func (rm *RuleManagerService) GetSubscriptionCountForRule(ctx context.Context, req *pb.GetSubscriptionCountForRuleRequest) (*pb.GetSubscriptionCountForRuleResponse, error) {
	countResp, err := rm.subscriptionRuntimeInfoDao.GetCount(ctx, &dao.GetCountRequest{
		ActorId:   req.GetActorId(),
		RuleIds:   []string{req.GetRuleId()},
		States:    []pb.RuleSubscriptionState{pb.RuleSubscriptionState_ACTIVE, pb.RuleSubscriptionState_INACTIVE},
		CountMask: []dao.CountFieldMask{dao.SUBSCRIPTION_COUNT_FOR_ACTOR, dao.SUBSCRIPTION_COUNT_ACROSS_ACTORS},
	})
	if err != nil {
		logger.Error(ctx, "Failed to get subscription counts for rule", zap.String(logger.RULE_ID, req.GetRuleId()),
			zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &pb.GetSubscriptionCountForRuleResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &pb.GetSubscriptionCountForRuleResponse{
		Status:                      rpc.StatusOk(),
		RuleId:                      req.GetRuleId(),
		ActorId:                     req.GetActorId(),
		NumSubscriptionsAcrossActor: uint32(countResp.CountMap[dao.SUBSCRIPTION_COUNT_ACROSS_ACTORS]),
		NumSubscriptionsForActor:    uint32(countResp.CountMap[dao.SUBSCRIPTION_COUNT_FOR_ACTOR]),
	}, nil
}

// nolint: dupl
func (rm *RuleManagerService) GetActiveSubscriptionCountForRule(ctx context.Context, req *pb.GetActiveSubscriptionCountForRuleRequest) (*pb.GetActiveSubscriptionCountForRuleResponse, error) {
	var daoFieldMasks []dao.CountFieldMask
	for _, mask := range req.GetFieldMasks() {
		switch mask {
		case pb.SubscriptionCountFieldMask_SUBSCRIPTION_COUNT_FIELD_MASK_PER_ACTOR:
			daoFieldMasks = append(daoFieldMasks, dao.SUBSCRIPTION_COUNT_FOR_ACTOR)
		case pb.SubscriptionCountFieldMask_SUBSCRIPTION_COUNT_FIELD_MASK_ACROSS_ACTORS:
			daoFieldMasks = append(daoFieldMasks, dao.SUBSCRIPTION_COUNT_ACROSS_ACTORS)
		default:
			logger.Error(ctx, "subscription count field mask not handled", zap.String(logger.FIELD_MASKS, mask.String()))
			return &pb.GetActiveSubscriptionCountForRuleResponse{
				Status: rpc.StatusInternal(),
			}, nil
		}
	}

	countResp, err := rm.subscriptionRuntimeInfoDao.GetCount(ctx, &dao.GetCountRequest{
		ActorId:   req.GetActorId(),
		RuleIds:   []string{req.GetRuleId()},
		States:    []pb.RuleSubscriptionState{pb.RuleSubscriptionState_ACTIVE},
		CountMask: daoFieldMasks,
	})
	if err != nil {
		logger.Error(ctx, "error in getting active subscription count for actor", zap.Error(err), zap.String(logger.RULE_ID, req.GetRuleId()))
		return &pb.GetActiveSubscriptionCountForRuleResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &pb.GetActiveSubscriptionCountForRuleResponse{
		Status:                      rpc.StatusOk(),
		NumSubscriptionsForActor:    uint32(countResp.CountMap[dao.SUBSCRIPTION_COUNT_FOR_ACTOR]),
		NumSubscriptionsAcrossActor: uint32(countResp.CountMap[dao.SUBSCRIPTION_COUNT_ACROSS_ACTORS]),
	}, nil
}

// nolint: dupl
func (rm *RuleManagerService) GetActiveSubscriptionCountForRules(ctx context.Context, req *pb.GetActiveSubscriptionCountForRulesRequest) (*pb.GetActiveSubscriptionCountForRulesResponse, error) {
	subsCountMap, err := rm.subscriptionDao.GetActiveSubscriptionCountForRules(ctx, req.GetRuleIds(), req.ActorId)
	if err != nil {
		logger.Error(ctx, "Failed to get subscription counts for rules", zap.Any(logger.RULE_IDS, req.RuleIds), zap.Error(err), zap.String(logger.ACTOR_ID, req.ActorId))
		return &pb.GetActiveSubscriptionCountForRulesResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, err
	}

	return &pb.GetActiveSubscriptionCountForRulesResponse{
		Status: rpc.StatusOk(),
		Count:  subsCountMap,
	}, err

}

func (rm *RuleManagerService) GetSubscriptionsByActorForRules(ctx context.Context, req *pb.GetSubscriptionsByActorForRulesRequest) (*pb.GetSubscriptionsByActorForRulesResponse, error) {
	var mutualFundId, smartDepositId, usStockId *string
	switch {
	case req.GetMutualFundId() != "":
		mutualFundId = &req.GetActionEntityId().(*pb.GetSubscriptionsByActorForRulesRequest_MutualFundId).MutualFundId
	case req.GetSmartDepositAccId() != "":
		smartDepositId = &req.GetActionEntityId().(*pb.GetSubscriptionsByActorForRulesRequest_SmartDepositAccId).SmartDepositAccId
	case req.GetUsStockId() != "":
		usStockId = &req.GetActionEntityId().(*pb.GetSubscriptionsByActorForRulesRequest_UsStockId).UsStockId
	}

	paginate := !req.ShouldNotUsePagination
	var pageToken *pagination.PageToken
	if req.GetPageContext() != nil {
		var err2 error
		pageToken, err2 = pagination.GetPageToken(req.GetPageContext())
		if err2 != nil {
			logger.Error(ctx, "Failed to get token", zap.Error(err2))
			return nil, err2
		}
	}
	subscriptions, pageCtxResp, err := rm.subscriptionDao.GetSubscriptionsByActorId(ctx, req.ActorId, req.GetRuleIds(),
		nil, nil, nil, nil, req.GetStates(),
		mutualFundId, smartDepositId, usStockId, paginate, pageToken, req.GetPageContext().GetPageSize())
	if err != nil {
		logger.Error(ctx, "Unable to get rules subscribed by actor", zap.String(logger.ACTOR_ID, req.ActorId), zap.Error(err))
		return &pb.GetSubscriptionsByActorForRulesResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	var rulesMap = make(map[string]*pb.Rule)

	if len(req.GetRuleIds()) > 0 {
		rulesMap, _, err = rm.ruleDao.GetByIds(ctx, req.GetRuleIds())
		if err != nil {
			logger.Error(ctx, "error in getting rules by ids", zap.Strings(logger.RULE_IDS, req.GetRuleIds()))
			return &pb.GetSubscriptionsByActorForRulesResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
	} else {
		rulesMap, _, err = rm.ruleDao.GetByStates(ctx, []pb.RuleState{pb.RuleState_RULE_STATE_ACTIVE})
		if err != nil {
			logger.Error(ctx, "error in getting rules by state active")
			return &pb.GetSubscriptionsByActorForRulesResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
	}

	ruleSubscriptions := make(map[string]*pb.Subscriptions)
	// preparing map of ruleId vs Subscriptions
	for _, sub := range subscriptions {
		val, ok := ruleSubscriptions[sub.GetRuleId()]
		if !ok {
			val = &pb.Subscriptions{RuleSubscriptions: make([]*pb.RuleSubscription, 0)}
			ruleSubscriptions[sub.GetRuleId()] = val
		}
		val.RuleSubscriptions = append(val.GetRuleSubscriptions(), sub)
		err := rm.addNextExecutionDate(ctx, sub, rulesMap[sub.GetRuleId()])
		if err != nil {
			return &pb.GetSubscriptionsByActorForRulesResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
	}

	res := &pb.GetSubscriptionsByActorForRulesResponse{
		Status:            rpc.StatusOk(),
		RuleSubscriptions: ruleSubscriptions,
		ActorId:           req.ActorId,
		PageContext:       pageCtxResp,
	}

	// isFieldMaskEnabled checks if given field mask is present in fieldMasks or not
	isFieldMaskEnabled := func(fieldMasks []pb.GetSubscriptionsByActorForRulesRequest_FieldMask,
		fieldMask pb.GetSubscriptionsByActorForRulesRequest_FieldMask) bool {
		for _, fm := range fieldMasks {
			if fm == fieldMask {
				return true
			}
		}
		return false
	}

	var ruleSubscriptionAdditionalDetails map[string]*pb.SubscriptionsAdditionalDetails
	// if field mask is present, then fetch additional details
	if isFieldMaskEnabled(req.GetFieldMask(), pb.GetSubscriptionsByActorForRulesRequest_FIELD_MASK_ADDITIONAL_DETAILS) {
		ruleSubscriptionAdditionalDetails, err = rm.getRuleSubscriptionAdditionalDetails(ctx, req.GetActorId(),
			rulesMap, ruleSubscriptions)
		if err != nil {
			logger.Error(ctx, "error in getting rule subscription additional details", zap.Error(err))
			return &pb.GetSubscriptionsByActorForRulesResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
		res.RuleSubscriptionAdditionalDetails = ruleSubscriptionAdditionalDetails
	}
	return res, nil
}

func getFrequencyValFromRuleParams(ruleParamValues map[string]*pb.Value) *ui.FrequencyVal {
	var frequencyVal *ui.FrequencyVal
	for _, val := range ruleParamValues {
		if val.GetFrequencyVal() != nil {
			frequencyVal = val.GetFrequencyVal()
		}
	}
	return frequencyVal
}

func getValidFromAndValidTillFromRuleParams(ruleParamValues map[string]*pb.Value) (*ui.DateVal, *ui.DateVal) {
	var validFromVal, validTillVal *ui.DateVal
	for _, val := range ruleParamValues {
		if val.GetDateVal() != nil {
			// nolint: exhaustive
			switch val.GetDateVal().GetType() {
			case ui.DateType_VALID_FROM:
				validFromVal = val.GetDateVal()
			case ui.DateType_VALID_TILL:
				validTillVal = val.GetDateVal()
			}
		}
	}
	return validFromVal, validTillVal
}

// returns the next execution date for the subscription in time.UTC
func (rm *RuleManagerService) addNextExecutionDate(ctx context.Context, sub *pb.RuleSubscription, rule *pb.Rule) error {
	if rule.GetCategory() == pb.RuleCategory_AUTO_PAY {
		validFromVal, validTillVal := getValidFromAndValidTillFromRuleParams(sub.GetRuleParamValues().GetRuleParamValues())
		frequencyVal := getFrequencyValFromRuleParams(sub.GetRuleParamValues().GetRuleParamValues())
		var err error
		sub.NextExecutionTime, err = rm.getNextExecutionDate(ctx, validFromVal, validTillVal, frequencyVal, sub.GetId())
		if err != nil {
			return err
		}
	} else {
		// nolint: exhaustive
		switch rule.GetRuleTypeForSpecialHandling() {
		case pb.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_WEEKLY, pb.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_WEEKLY_RULE:
			for _, val := range sub.GetRuleParamValues().GetRuleParamValues() {
				if val.GetStrVal() != "" {
					var err error
					sub.NextExecutionTime, err = rm.getTimestampToNextWeekday(ctx, val.GetStrVal(), sub.GetId())
					if err != nil {
						return err
					}
					break
				}
			}
		case pb.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_MONTHLY,
			pb.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_MONTHLY_RULE,
			pb.RuleTypeForSpecialHandling_RULE_TYPE_US_STOCKS_MONTHLY_SIP:
			for _, val := range sub.GetRuleParamValues().GetRuleParamValues() {
				if val.GetIntVal() != 0 {
					var err error
					sub.NextExecutionTime, err = rm.getTimestampToDayOfNextMonth(ctx, val.GetIntVal(), sub.GetId())
					if err != nil {
						return err
					}
					break
				}
			}
		case pb.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY, pb.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_SAVE_DAILY_RULE:
			currentTime := time.Now()
			alreadyExecuted, err := rm.isSubscriptionExecuted(ctx, currentTime, sub.GetId())
			if err != nil {
				return err
			}
			if alreadyExecuted {
				sub.NextExecutionTime = timestamppb.New(currentTime.Add(24 * time.Hour))
			} else {
				sub.NextExecutionTime = timestamppb.New(currentTime)
			}
		}
	}

	if sub.NextExecutionTime != nil {
		// setting next execution time in UTC as start of the day
		sub.NextExecutionTime = timestamppb.New(datetime.GetTimeAtStartOfTheDay(sub.NextExecutionTime.AsTime()))
	}
	return nil
}

func (rm *RuleManagerService) getTimestampToNextWeekday(ctx context.Context, configuredWeekDay string, subId string) (*timestamp.Timestamp, error) {
	currentTime := time.Now()
	confWeekDay, err := getWeekday(ctx, configuredWeekDay)
	if err != nil {
		return nil, err
	}
	if confWeekDay == currentTime.Weekday() {
		alreadyExecutedOnSameDay, err := rm.isSubscriptionExecuted(ctx, currentTime, subId)
		if err != nil {
			return nil, err
		}
		if !alreadyExecutedOnSameDay {
			return timestamppb.New(time.Now()), nil
		}
	}
	return timestamppb.New(time.Now().Add(getDurationToNextDay(currentTime.Weekday(), confWeekDay))), nil
}

func getWeekday(ctx context.Context, day string) (time.Weekday, error) {
	switch day {
	case "Monday":
		return time.Monday, nil
	case "Tuesday":
		return time.Tuesday, nil
	case "Wednesday":
		return time.Wednesday, nil
	case "Thursday":
		return time.Thursday, nil
	case "Friday":
		return time.Friday, nil
	case "Saturday":
		return time.Saturday, nil
	case "Sunday":
		return time.Sunday, nil
	}
	logger.Error(ctx, "configured day is not a valid weekday", zap.String("weekday", day))
	return time.Sunday, fmt.Errorf("configured day is not a valid weekday: %s", day)
}

func getDurationToNextDay(from time.Weekday, to time.Weekday) time.Duration {
	switch {
	case to == from:
		return 7 * 24 * time.Hour
	case to > from:
		return 24 * time.Hour * time.Duration(int(to)-int(from))
	default:
		differenceInDays := (int(time.Saturday) - int(from)) + (int(to) - int(time.Sunday) + 1)
		return 24 * time.Hour * time.Duration(differenceInDays)
	}
}

func (rm *RuleManagerService) GetExecutionsCountForRules(ctx context.Context, req *pb.GetExecutionsCountForRulesRequest) (*pb.GetExecutionsCountForRulesResponse, error) {
	resp := &pb.GetExecutionsCountForRulesResponse{
		Status: rpc.StatusOk(),
	}
	if len(req.GetRuleIds()) != 0 && len(req.GetRuleNames()) != 0 {
		return nil, fmt.Errorf("either Rule Ids or Rule Names should be passed and not both")
	}

	var executionCountAcrossActors map[string]int64
	var err error
	if len(req.GetRuleIds()) != 0 {
		executionCountAcrossActors, err = rm.executionDao.GetExecutionsCountForRules(ctx, req.GetRuleIds())
	} else {
		executionCountAcrossActors, err = rm.executionDao.GetExecutionsCountForRulesByNames(ctx, req.GetRuleNames())
	}
	if err != nil {
		logger.Error(ctx, "Unable to get Executions count across actors for rules", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	resp.ExecutionsCountMap = executionCountAcrossActors
	return resp, nil
}

func (rm *RuleManagerService) GetAllSubscriptionVersions(ctx context.Context, req *pb.GetAllSubscriptionVersionsRequest) (*pb.GetAllSubscriptionVersionsResponse, error) {
	resp := &pb.GetAllSubscriptionVersionsResponse{
		Status: rpc.StatusOk(),
	}
	subscriptionVersionsMap := make(map[string]*pb.AllSubscriptionVersions)
	for _, subscriptionId := range req.GetSubscriptionIds() {
		startTime := req.GetStartTime().AsTime()
		endTime := req.GetEndTime().AsTime()
		allVersions, err := rm.subscriptionDao.GetAllVersionsByIds(ctx, []string{subscriptionId}, &startTime, &endTime)
		if err != nil {
			logger.Error(ctx, "Failed to get all versions for subscription", zap.Error(err), zap.String(logger.SUBSCRIPTION_ID, subscriptionId),
				zap.Time(logger.START_TIME, startTime), zap.Time(logger.END_TIME, endTime))
			resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return resp, nil
		}
		subscriptionVersionsMap[subscriptionId] = &pb.AllSubscriptionVersions{Versions: allVersions[subscriptionId]}
	}
	resp.SubscriptionToVersionsMap = subscriptionVersionsMap
	return resp, nil
}

func (rm *RuleManagerService) GetClientMetricsAndPendingExecutions(ctx context.Context, req *pb.GetClientMetricsAndPendingExecutionsRequest) (*pb.GetClientMetricsAndPendingExecutionsResponse, error) {
	resp := &pb.GetClientMetricsAndPendingExecutionsResponse{
		Status: rpc.StatusOk(),
	}

	rules, err := rm.ruleDao.GetAllRulesForEventType(ctx, event.RMSClient_FITTT, event.EventType_APP_USAGE_RULE_INITIATE_EXECUTION_EVENT, nil)
	if err != nil {
		logger.Error(ctx, "Failed to get rules for EventType_APP_USAGE_RULE_INITIATE_EXECUTION_EVENT", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	rulesMap := make(map[string]*pb.Rule)
	for _, rule := range rules {
		if rule.GetState() == pb.RuleState_RULE_STATE_ACTIVE {
			rulesMap[rule.GetId()] = rule
		}
	}

	versionsMap, err := rm.subscriptionDao.GetClientRulesExecutionInitiatedVersionsForActor(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "Failed to get client rules pending subscriptions", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	var versionIds []string
	for id := range versionsMap {
		versionIds = append(versionIds, id)
	}
	executionsMap, err := rm.executionDao.GetClientRulesInitiatedExecutions(ctx, versionIds)
	if err != nil {
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	resp.ConditionsToBeEvaluated, err = getConditionMap(ctx, executionsMap, rulesMap, versionsMap)
	if err != nil {
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	return resp, nil
}

func getConditionMap(ctx context.Context, executionsMap map[string][]*execpb.RuleExecution, rules map[string]*pb.Rule, versionsMap map[string]*pb.RuleSubscription) (map[string]*pb.ConditionData, error) {
	conditionsMap := make(map[string]*pb.ConditionData)
	for versionId, executions := range executionsMap {
		version := versionsMap[versionId]
		if version == nil {
			logger.Error(ctx, "Cannot find version, Unable to send this execution for condition evaluation", zap.String(logger.VERSION_ID, versionId), zap.Any(logger.EXECUTION_ID, executions))
			continue
		}
		for _, execution := range executions {
			eventTime := execution.GetCreatedAt().AsTime()
			startTime := time.Date(eventTime.Year(), eventTime.Month(), eventTime.Day()-1, 0, 0, 0, 0, eventTime.Location())
			endTime := time.Date(eventTime.Year(), eventTime.Month(), eventTime.Day()-1, 23, 59, 59, 0, eventTime.Location())
			rule := rules[version.GetRuleId()]
			switch rule.GetEventType() {
			case event.EventType_APP_USAGE_RULE_INITIATE_EXECUTION_EVENT:
				conditionsMap[execution.GetExecutionId()] = &pb.ConditionData{
					MetricType: pb.MetricType_APP_USAGE,
					Condition: &pb.ConditionData_AppUsageCondition{AppUsageCondition: &pb.AppUsageCondition{
						ConfiguredApp:      version.GetRuleParamValues().GetRuleParamValues()[fact.ConfiguredApp].GetAppVal().GetAppName(),
						ConfiguredDuration: version.GetRuleParamValues().GetRuleParamValues()[fact.ConfiguredDuration].GetDuration(),
						StartTime:          timestamppb.New(startTime),
						EndTime:            timestamppb.New(endTime),
					}},
				}
			default:
				logger.Error(ctx, "Failed to recognize event type", zap.String(logger.APP_METRICS, rule.GetEventType().String()))
				return nil, fmt.Errorf("Failed to recognize event type %v ", rule.GetEventType().String())
			}
		}
	}
	return conditionsMap, nil

}

// for update rule request, closeExistingSubscriptions checks if the rule state is being updated to INACTIVE
// for such requests, Closes all the underlying ACTIVE & INACTIVE subscriptions
func (rm *RuleManagerService) closeExistingSubscriptions(ctx context.Context, ruleId string) error {
	// if the request is to update rule state to inactive
	logger.Info(ctx, "Update Rule state to INACTIVE request, going to close all ACTIVE & PAUSED subscriptions ",
		zap.String(logger.RULE_ID, ruleId))
	err := rm.subscriptionDao.UpdateAllSubscriptionsForRules(ctx, []string{ruleId}, &pb.RuleSubscription{
		State:                 pb.RuleSubscriptionState_CLOSED,
		StateChangeReason:     pb.SubscriptionStateChangeReason_CLOSE_ALL_SUBSCRIPTIONS,
		StateChangeProvenance: pb.SubscriptionStateChangeProvenance_SHERLOCK,
	},
		[]pb.RuleSubscriptionFieldMask{pb.RuleSubscriptionFieldMask_STATE}, nil, nil)
	return err
}

func (rm *RuleManagerService) publishSubscribeRuleEvent(ctx context.Context, actorId string, attemptedAt time.Time, rule *pb.Rule, subscription *pb.RuleSubscription, status string, failureReason string) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		rm.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events.NewSubscribedRuleServer(actorId, attemptedAt, rule, subscription, status, failureReason))
	})
}

func (rm *RuleManagerService) publishRuleEditedEvent(ctx context.Context, actorId string, attemptedAt time.Time, rule *pb.Rule, subscription *pb.RuleSubscription, status string, failureReason string) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		rm.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events.NewRuleEditedServer(actorId, attemptedAt, rule, subscription, status, failureReason))
	})
}

func (rm *RuleManagerService) publishRuleStateChangedEvent(ctx context.Context, actorId string, attemptedAt time.Time, rule *pb.Rule, subscription *pb.RuleSubscription, status string, failureReason string) {
	if subscription.GetState() == pb.RuleSubscriptionState_ACTIVE {
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			rm.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events.NewRuleResumedServer(actorId, attemptedAt, rule, subscription, status, failureReason))
		})
	}

	if subscription.GetState() == pb.RuleSubscriptionState_INACTIVE {
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			rm.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), events.NewRulePausedServer(actorId, attemptedAt, rule, subscription, status, failureReason))
		})
	}
}

func (rm *RuleManagerService) GetNewRulesInfoForUser(ctx context.Context, req *pb.GetNewRulesInfoForUserRequest) (*pb.GetNewRulesInfoForUserResponse, error) {
	resp := &pb.GetNewRulesInfoForUserResponse{Status: rpc.StatusOk()}
	newRules, err := rm.ruleDao.GetNewRulesForUser(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "Error while getting new rules for user", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	err = rm.addRuleTags(ctx, newRules)
	if err != nil {
		logger.Error(ctx, "Failed to get tags information for rules", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	rulesMap := make(map[string]*pb.Rule)
	for _, rule := range rulesMap {
		rulesMap[rule.GetId()] = rule
	}

	resp.Rules = newRules
	resp.NewRules = rulesMap
	return resp, nil
}

func (rm *RuleManagerService) GetHomeCards(ctx context.Context, req *pb.GetHomeCardsRequest) (*pb.GetHomeCardsResponse, error) {
	resp := &pb.GetHomeCardsResponse{Status: rpc.StatusOk()}
	assignedUserGroups, err := rm.getAssignedUserGroups(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting assigned user groups", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	cards, err := rm.homeCardDao.GetAll(ctx, req.GetFilterStates(), assignedUserGroups, req.GetPlatform(), req.GetAppVersion())
	if err != nil {
		logger.Error(ctx, "error while getting home cards", zap.Error(err), zap.Any(logger.STATE, req.GetFilterStates()))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	cards, err = rm.filterHomeCardsForActor(ctx, cards, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while getting home cards", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	cards, err = rm.constructAndAddHomeCardDispText(ctx, cards)
	if err != nil {
		logger.Error(ctx, "Error while constructing home card display string", zap.Error(err), zap.Any("cards", cards))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	resp.Cards = cards
	return resp, nil
}

// in-place shuffle of homecards
func ShuffleHomeCards(homecards []*pb.HomeCard) {
	for i := range homecards {
		// nolint: gosec
		j := rand.Intn(i + 1)
		if i != j {
			homecards[i], homecards[j] = homecards[j], homecards[i]
		}
	}
}

func (rm *RuleManagerService) filterHomeCardsForActor(ctx context.Context, homecards []*pb.HomeCard, actorId string) ([]*pb.HomeCard, error) {
	var filteredHomecards []*pb.HomeCard
	for _, homecard := range homecards {
		ruleOptions := homecard.GetDeeplink().GetFitCustomiseRuleScreenOptions()
		if ruleOptions == nil {
			// this homecard might have collections or any other page deeplink
			// adding it in list of possible homecards
			filteredHomecards = append(filteredHomecards, homecard)
			continue
		}
		if ruleOptions.GetRuleId() == "" {
			// homecard usually will not have rule id has nil, this is a configuration issue
			continue
		}
		count, err := rm.subscriptionRuntimeInfoDao.GetCount(ctx, &dao.GetCountRequest{
			ActorId:   actorId,
			RuleIds:   []string{ruleOptions.GetRuleId()},
			CountMask: []dao.CountFieldMask{dao.SUBSCRIPTION_COUNT_FOR_ACTOR},
			States:    []pb.RuleSubscriptionState{pb.RuleSubscriptionState_ACTIVE},
		})
		if err != nil {
			logger.Error(ctx, "Failed to get active subscriptions count by actor for rule", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.RULE_ID, ruleOptions.GetRuleId()), zap.Error(err))
			return nil, err
		}
		if count.CountMap[dao.SUBSCRIPTION_COUNT_FOR_ACTOR] != 0 {
			// user has already subscribed for the rule which is represented by homecard, skipping the homecard
			continue
		}
		// user has no subscription for the rule homecard, we can show this homecard
		filteredHomecards = append(filteredHomecards, homecard)
	}
	if len(filteredHomecards) == 0 && len(homecards) != 0 {
		// if the user has already subscribed to the rule homecard but there is no other homecard to show
		// we can still show the existing rule homecard
		ShuffleHomeCards(homecards)
		filteredHomecards = append(filteredHomecards, homecards[0])
	}
	return filteredHomecards, nil
}

func (rm *RuleManagerService) constructAndAddHomeCardDispText(ctx context.Context, cards []*pb.HomeCard) ([]*pb.HomeCard, error) {
	var filteredCards []*pb.HomeCard
	var err error
	for _, card := range cards {
		// if a card is not valid it is not returned in response
		// eg:
		// 0 rules added for Wellness
		// 0 IPL matches today
		validCard := true
		desc := card.GetCardData().GetDescription().GetDescStr()
		for name, param := range card.GetCardData().GetDescription().GetReplaceableParamMap() {
			// nolint:exhaustive
			switch param.GetType() {
			case pb.ReplaceableParamType_MATCH_COUNT:
				desc, validCard, err = rm.replaceMatchCountInDesc(ctx, param.GetTagId(), desc, name)
				if err != nil {
					logger.Error(ctx, "Error while replacing match count for desc string on home card", zap.Error(err))
					return nil, err
				}
			case pb.ReplaceableParamType_RULES_COUNT:
				desc, validCard, err = rm.replaceRulesCountInDesc(ctx, param, desc, name)
				if err != nil {
					logger.Error(ctx, "Error while replacing rules count for desc string on home card", zap.Error(err))
					return nil, err
				}
			case pb.ReplaceableParamType_TOURNAMENT_NAME:
				tagsMap, err := rm.tagsDao.GetByIds(ctx, []string{param.GetTagId()})
				if err != nil {
					logger.Error(ctx, "Error getting tags", zap.String(logger.TAG_ID, param.GetTagId()), zap.Error(err))
					return nil, err
				}
				desc = strings.Replace(desc, fmt.Sprintf("{%s}", name), tagsMap[param.GetTagId()].GetName(), 1)
			default:
				logger.Error(ctx, "Replaceable param is not handled for substitution", zap.Any("Param", param))
				return nil, fmt.Errorf("replaceable param is not handled for substitution ")
			}
		}
		card.GetCardData().GetDescription().GetDisplayInfo().Text = desc
		if validCard {
			filteredCards = append(filteredCards, card)
		}
	}
	return filteredCards, nil
}

func (rm *RuleManagerService) replaceMatchCountInDesc(ctx context.Context, tagId string, desc string, name string) (string, bool, error) {
	location, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		logger.Error(ctx, "Error to load location Asia/Kolkata", zap.Error(err))
		return "", false, err
	}

	schedules, err := rm.fitttSchedulerClient.GetSportsSchedules(ctx, &schedulerpb.GetSportsSchedulesRequest{
		SportsScheduleType: schedulerpb.ScheduleType_CRICKET_MATCH,
		TournamentTag:      tagId,
		EventStart:         timestamppb.New(Bod(time.Now(), location)),
		EventEnd:           timestamppb.New(Eod(time.Now(), location)),
	})
	if err != nil {
		logger.Error(ctx, "Error while getting cricket match schedules", zap.String(logger.TAG_ID, tagId), zap.Error(err))
		return "", false, err
	}

	validCard := true
	switch len(schedules.GetMatchDetails()) {
	case 0:
		validCard = false
	case 1:
		var matchNameStr string
		// teams can be empty if the teams are not finalized yet.
		// For matches like semi-finals, final.
		// If we forgot to run fixtures to update these details, then this code will gracefully handle and show alternative count
		if len(schedules.GetMatchDetails()[0].GetTeams()) == 0 {
			desc = strings.Replace(desc, fmt.Sprintf("{%s}", name), "1", 1)
		} else {
			matchNameStr = fmt.Sprintf("%s vs %s", schedules.GetMatchDetails()[0].GetTeams()[0].GetCode(), schedules.GetMatchDetails()[0].GetTeams()[1].GetCode())
			desc = strings.Replace(desc, fmt.Sprintf("{%s}", name), matchNameStr, 1)
		}
		desc = strings.Replace(desc, "Matches", "Match", 1)
	default:
		desc = strings.Replace(desc, fmt.Sprintf("{%s}", name), fmt.Sprintf("%d", len(schedules.GetMatchDetails())), 1)
	}
	return desc, validCard, nil
}

func (rm *RuleManagerService) GetRulesCountAndFirstRule(ctx context.Context, param *pb.ReplaceableParam) (int64, *pb.Rule, error) {
	switch {
	case param.GetTagId() != "":
		rules, totalCount, _, err := rm.ruleDao.GetRulesForTag(ctx, param.GetTagId(), []pb.RuleState{pb.RuleState_RULE_STATE_ACTIVE}, nil, 1)
		if err != nil {
			logger.Error(ctx, "Error while getting rules for tag", zap.Error(err), zap.String(logger.TAG_ID, param.GetTagId()))
			return 0, nil, err
		}
		return totalCount, rules[0], nil
	case param.GetCollectionId() != "":
		// get active rules for collection
		// set value of ruleCount
		return 0, nil, errors.New("Unimplemented collectionId based rules fetch ")
	default:
		logger.Error(ctx, "no additional information present for getting count of rules", zap.Any("additionalParams", param.GetAdditionalParamInfo()))
		return 0, nil, fmt.Errorf("no additional information present for getting count of rules")
	}
}

func (rm *RuleManagerService) CreateHomeCard(ctx context.Context, req *pb.CreateHomeCardRequest) (*pb.CreateHomeCardResponse, error) {
	var card *pb.HomeCard
	var err error
	if card, err = rm.homeCardDao.Create(ctx, req.GetCard()); err != nil {
		logger.Error(ctx, "Failed to create home card", zap.Error(err), zap.Any(logger.REQUEST, req))
		return &pb.CreateHomeCardResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &pb.CreateHomeCardResponse{
		Status: rpc.StatusOk(),
		Card:   card,
	}, nil
}

func (rm *RuleManagerService) UpdateHomeCard(ctx context.Context, req *pb.UpdateHomeCardRequest) (*pb.UpdateHomeCardResponse, error) {
	var err error
	if _, err = rm.homeCardDao.Update(ctx, req.GetCard()); err != nil {
		logger.Error(ctx, "Failed to update home card", zap.Error(err), zap.Any(logger.REQUEST, req))
		return &pb.UpdateHomeCardResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &pb.UpdateHomeCardResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (rm *RuleManagerService) CreateCollection(ctx context.Context, req *pb.CreateCollectionRequest) (*pb.CreateCollectionResponse, error) {
	collection, err := rm.collectionsDao.Create(ctx, req.GetCollection())
	if err != nil {
		logger.Error(ctx, "Failed to create collection", zap.Error(err), zap.Any(logger.REQUEST, req))
		return &pb.CreateCollectionResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &pb.CreateCollectionResponse{
		Status:     rpc.StatusOk(),
		Collection: collection,
	}, nil
}

func (rm *RuleManagerService) GetCollections(ctx context.Context, req *pb.GetCollectionsRequest) (*pb.GetCollectionsResponse, error) {
	resp := &pb.GetCollectionsResponse{Status: rpc.StatusOk()}
	var collections []*pb.Collection
	var err error
	actorId := req.GetActorId()
	platform := req.GetPlatform()
	appVersionCode := req.GetAppVersion()

	assignedUserGroups, err := rm.getAssignedUserGroups(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in getting assigned user groups", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	// GetFeaturedCollections to be displayed on Fit digest
	if req.GetFeaturedCollectionsOnly() {
		// get all featured collections for user which are active and have ruleIds or tagIds
		collections, err = rm.collectionsDao.GetAllFeaturedCollections(ctx, req.GetStateFilter(), req.GetType(), assignedUserGroups, platform, appVersionCode)
		if err != nil {
			logger.Error(ctx, "error in getting featured collections", zap.Error(err), zap.Any(logger.STATE, req.GetStateFilter()))
			resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return resp, nil
		}

		resp.Collections = collections
		return resp, nil
	}

	var token *pagination.PageToken
	token, err = pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.Error(err))
		return nil, err
	}

	var respToken *rpc.PageContextResponse
	collections, respToken, err = rm.collectionsDao.GetAll(ctx, req.GetRangeStartTime(), req.GetRangeEndTime(),
		req.GetStateFilter(), req.GetType(), token, req.GetPageContext().GetPageSize(), assignedUserGroups, platform, appVersionCode)
	if err != nil {
		logger.Error(ctx, "Error in getting collections", zap.Error(err), zap.Time(logger.START_TIME, req.GetRangeStartTime().AsTime()),
			zap.Time(logger.END_TIME, req.GetRangeEndTime().AsTime()), zap.Any(logger.STATE, req.GetStateFilter()), zap.Any(logger.TOKEN, req.GetPageContext()))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	resp.PageContext = respToken

	validCollections, err := rm.populateRulesCountToCollections(ctx, collections, actorId, assignedUserGroups, platform, appVersionCode, pb.RuleCategory_RULE_CATEGORY_UNSPECIFIED)
	if err != nil {
		logger.Error(ctx, "Error while populating active rules count", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	resp.Collections = validCollections
	return resp, nil
}

func (rm *RuleManagerService) UpdateCollection(ctx context.Context, req *pb.UpdateCollectionRequest) (*pb.UpdateCollectionResponse, error) {
	logger.Info(ctx, "Going to update collection", zap.Any("collection", req.GetCollection()),
		zap.String(logger.COLLECTION_ID, req.GetCollectonId()), zap.Any("field masks", req.GetFieldMasks()))

	updatedCollection, err := rm.collectionsDao.Update(ctx, req.GetCollectonId(), req.GetCollection(), req.GetFieldMasks())
	if err != nil {
		logger.Error(ctx, "Failed to update existing collection in database", zap.Error(err), zap.String(logger.COLLECTION_ID, req.GetCollectonId()))
		return &pb.UpdateCollectionResponse{
			Status:     rpc.StatusInternalWithDebugMsg(err.Error()),
			Collection: nil,
		}, nil
	}

	logger.Info(ctx, "Collection update successful", zap.String(logger.COLLECTION_ID, req.GetCollectonId()))
	return &pb.UpdateCollectionResponse{
		Status:     rpc.StatusOk(),
		Collection: updatedCollection,
	}, nil
}

func (rm *RuleManagerService) GetActiveRulesCount(ctx context.Context, req *pb.GetActiveRulesCountRequest) (*pb.GetActiveRulesCountResponse, error) {
	resp := &pb.GetActiveRulesCountResponse{Status: rpc.StatusOk()}
	assignedGroups, err := rm.getAssignedUserGroups(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "Error in getting assigned user groups", zap.Error(err))
		resp.Status = rpc.StatusFromError(err)
		return resp, nil
	}

	rules, _, err := rm.ruleDao.GetRules(ctx, req.GetClient(), []pb.RuleState{pb.RuleState_RULE_STATE_ACTIVE}, nil,
		false, nil, 0, nil, nil, assignedGroups, req.GetPlatform(), req.GetAppVersion(), nil)
	if err != nil {
		logger.Error(ctx, "Failed to get rules for FITTT client", zap.Error(err))
		resp.Status = rpc.StatusFromError(err)
		return resp, nil
	}

	var ruleIds []string
	for _, r := range rules {
		ruleIds = append(ruleIds, r.GetId())
	}

	subsCountMap := make(map[string]*pb.StateWiseSubsCount)
	if len(ruleIds) > 0 {
		_, subsCountMap, _, err = rm.subscriptionDao.GetSubsCount(ctx, req.GetActorId(), ruleIds, event.RMSClient_FITTT,
			nil, nil)
		if err != nil {
			logger.Error(ctx, "Failed to get subscription counts for rules", zap.Any(logger.RULE_IDS, ruleIds), zap.Error(err))
			resp.Status = rpc.StatusFromError(err)
			return resp, nil
		}
	}

	for _, r := range rules {
		currentSubsCount := subsCountMap[r.GetId()].GetSubsCountPerState()[pb.RuleSubscriptionState_ACTIVE.String()] +
			subsCountMap[r.GetId()].GetSubsCountPerState()[pb.RuleSubscriptionState_INACTIVE.String()]
		if isNewSubscriptionAllowed(ctx, r.GetEventType(), int64(r.GetMaxSubscriptionsPerActor()), currentSubsCount, r.GetState()) {
			resp.Count++
		}
	}
	return resp, nil
}

func (rm *RuleManagerService) getAssignedUserGroups(ctx context.Context, actorId string) ([]commontypes.UserGroup, error) {
	// get actor info
	actorResp, err := rm.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{Id: actorId})
	if err2 := epifigrpc.RPCError(actorResp, err); err2 != nil {
		logger.Error(ctx, "failed to get actor by actor id", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Any("response", actorResp), zap.Error(err2))
		return nil, fmt.Errorf("failed to get actor by actor id, %w", err2)
	}
	actor := actorResp.GetActor()

	// get user info for actor
	userResp, err := rm.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_Id{
			Id: actor.GetEntityId(),
		},
	})
	if err2 := epifigrpc.RPCError(userResp, err); err2 != nil {
		logger.Error(ctx, "failed to get user by entity id",
			zap.String(logger.ENTITY_ID, actor.GetEntityId()), zap.Error(err2))
		return nil, fmt.Errorf("failed to get user by entity id, %w", err2)
	}

	// get user groups
	groupResp, err := rm.userGroupClient.GetGroupsMappedToEmail(ctx, &userGroupPb.GetGroupsMappedToEmailRequest{
		Email: userResp.GetUser().GetProfile().GetEmail(),
	})
	if err2 := epifigrpc.RPCError(groupResp, err); err2 != nil {
		logger.Error(ctx, "GetGroupsMappedToEmail call failed",
			zap.String(logger.ENTITY_ID, actor.GetEntityId()), zap.Error(err2))
		return nil, fmt.Errorf("GetGroupsMappedToEmail call failed %w", err2)
	}

	return groupResp.GetGroups(), nil
}

// nolint: funlen
func (rm *RuleManagerService) populateRulesCountToCollections(ctx context.Context, collections []*pb.Collection,
	actorId string, assignedUserGroups []commontypes.UserGroup, platform commontypes.Platform, appVersionCode uint32, ruleCategory pb.RuleCategory) ([]*pb.Collection, error) {
	var tagIds []string
	var ruleIds []string
	for _, collection := range collections {
		switch {
		case collection.GetChildIds().GetTagIds() != nil:
			tagIds = append(tagIds, collection.GetChildIds().GetTagIds().GetList()...)
		case collection.GetChildIds().GetRuleIds() != nil:
			ruleIds = append(ruleIds, collection.GetChildIds().GetRuleIds().GetList()...)
		}
	}
	tagWiseRulesCountMap := make(map[string]int32)
	rulesMap := make(map[string]*pb.Rule)
	subsCountMap := make(map[string]*pb.StateWiseSubsCount)

	var err error
	if len(tagIds) > 0 {
		tagWiseRulesCountMap, err = rm.tagMappingsDao.GetActiveRulesCountForTags(ctx, tagIds, platform, appVersionCode)
		if err != nil {
			logger.Error(ctx, "Error while getting active rules count for tags", zap.Error(err), zap.Strings(logger.TAG_IDS, tagIds))
			return nil, err
		}
	}

	if len(ruleIds) > 0 {
		rulesMap, _, err = rm.ruleDao.GetByIdsWithConstraints(ctx, ruleIds, []pb.RuleState{pb.RuleState_RULE_STATE_ACTIVE}, assignedUserGroups, platform, appVersionCode, ruleCategory)
		if err != nil {
			logger.Error(ctx, "Error while getting active rules by rule Ids", zap.Error(err), zap.Strings(logger.TAG_IDS, ruleIds))
			return nil, err
		}

		_, subsCountMap, _, err = rm.subscriptionDao.GetSubsCount(ctx, actorId, ruleIds, event.RMSClient_FITTT, nil, nil)
		if err != nil {
			logger.Error(ctx, "Failed to get subscription counts for rules", zap.Any(logger.RULE_IDS, ruleIds), zap.Error(err))
			return nil, err
		}
	}

	validCollections := make([]*pb.Collection, len(collections))
	numCollections := 0
	for _, collection := range collections {
		var count int32
		switch {
		case collection.GetChildIds().GetTagIds() != nil:
			var validTagIds []string
			for _, tagId := range collection.GetChildIds().GetTagIds().GetList() {
				count += tagWiseRulesCountMap[tagId]
				if tagWiseRulesCountMap[tagId] > 0 {
					validTagIds = append(validTagIds, tagId)
				}
			}
			collection.GetChildIds().GetTagIds().List = validTagIds
		case collection.GetChildIds().GetRuleIds() != nil:
			var validRuleIds []string
			for _, ruleId := range collection.GetChildIds().GetRuleIds().GetList() {
				cnt := subsCountMap[ruleId].GetSubsCountPerState()[pb.RuleSubscriptionState_ACTIVE.String()] +
					subsCountMap[ruleId].GetSubsCountPerState()[pb.RuleSubscriptionState_INACTIVE.String()]
				if _, present := rulesMap[ruleId]; present &&
					isNewSubscriptionAllowed(ctx, rulesMap[ruleId].GetEventType(), int64(rulesMap[ruleId].GetMaxSubscriptionsPerActor()), cnt, rulesMap[ruleId].GetState()) {
					validRuleIds = append(validRuleIds, ruleId)
					count++
				}
			}
			collection.GetChildIds().GetRuleIds().List = validRuleIds
		}
		collection.RulesCount = count
		// if there is no child for collection, remove collection from listing
		if count == 0 {
			continue
		}
		validCollections[numCollections] = collection
		numCollections++
	}
	return validCollections[:numCollections], nil
}

func (rm *RuleManagerService) GetCollectionInfo(ctx context.Context, req *pb.GetCollectionInfoRequest) (*pb.GetCollectionInfoResponse, error) {
	resp := &pb.GetCollectionInfoResponse{
		Status: rpc.StatusOk(),
	}
	actorId := req.GetActorId()
	platform := req.GetPlatform()
	appVersionCode := req.GetAppVersion()

	assignedUserGroups, err := rm.getAssignedUserGroups(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in getting assigned user groups", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	// fetch the collection as well as
	// fetch all the children
	info, children, err := rm.collectionsDao.GetCollectionAndChildren(ctx, req.GetCollectionId(), true, assignedUserGroups, platform, appVersionCode, req.GetRuleCategory())
	if err != nil {
		logger.Error(ctx, "Error in getting collection info", zap.Error(err), zap.String(logger.COLLECTION_ID, req.GetCollectionId()))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	resp.Collection = info

	if len(children.GetTags().GetTagsList()) > 0 {
		resp.Tags = children.GetTags().GetTagsList()
		// get rule count for each tag
		countMap, err := rm.tagMappingsDao.GetActiveRulesCountForTags(ctx, info.GetChildIds().GetTagIds().GetList(), platform, appVersionCode)
		if err != nil {
			logger.Error(ctx, "Error while getting active rules count for tags", zap.Error(err), zap.Strings(logger.TAG_IDS, info.GetChildIds().GetTagIds().GetList()))
			resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return resp, nil
		}

		var count int32
		for _, cnt := range countMap {
			count += cnt
		}
		resp.Collection.RulesCount = count
	} else {
		rulesMap, _, err := rm.ruleDao.GetByIdsWithConstraints(ctx, info.GetChildIds().GetRuleIds().GetList(),
			[]pb.RuleState{pb.RuleState_RULE_STATE_ACTIVE}, assignedUserGroups, platform, appVersionCode, req.GetRuleCategory())
		if err != nil {
			logger.Error(ctx, "Error while getting active rules by rule Ids", zap.Error(err), zap.Strings(logger.TAG_IDS, info.GetChildIds().GetRuleIds().GetList()))
			resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return resp, nil
		}

		_, subsCountMap, _, err := rm.subscriptionDao.GetSubsCount(ctx, actorId,
			info.GetChildIds().GetRuleIds().GetList(), event.RMSClient_FITTT, nil, nil)
		if err != nil {
			logger.Error(ctx, "Failed to get subscription counts for rules", zap.Any(logger.RULE_IDS, info.GetChildIds().GetRuleIds().GetList()), zap.Error(err))
			return nil, err
		}

		for _, r := range rulesMap {
			currentSubsCount := subsCountMap[r.GetId()].GetSubsCountPerState()[pb.RuleSubscriptionState_ACTIVE.String()] +
				subsCountMap[r.GetId()].GetSubsCountPerState()[pb.RuleSubscriptionState_INACTIVE.String()]
			if isNewSubscriptionAllowed(ctx, r.GetEventType(), int64(r.GetMaxSubscriptionsPerActor()), currentSubsCount, r.GetState()) {
				resp.Collection.RulesCount++
			}
		}
	}
	return resp, nil
}

func (rm *RuleManagerService) GetRules(ctx context.Context, req *pb.GetRulesRequest) (*pb.GetRulesResponse, error) {
	resp := &pb.GetRulesResponse{
		Status: rpc.StatusOk(),
	}
	token, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.Error(err))
		return nil, err
	}

	assignedUserGroups, err := rm.getAssignedUserGroups(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting assigned user groups", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	if req.GetTagId() != "" {
		var rules []*pb.Rule
		var respToken *rpc.PageContextResponse
		rules, _, respToken, err = rm.ruleDao.GetRulesForTag(ctx, req.GetTagId(), []pb.RuleState{pb.RuleState_RULE_STATE_ACTIVE}, token, req.GetPageContext().GetPageSize())
		if err != nil {
			logger.Error(ctx, "Error in getting rules for tag", zap.Error(err), zap.String(logger.TAG_ID, req.GetTagId()))
			resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return resp, nil
		}

		_, collections, err := rm.collectionsDao.GetCollectionsForTagIds(ctx, []string{req.GetTagId()}, []pb.CollectionState{pb.CollectionState_COLLECTION_ACTIVE})
		if err != nil {
			logger.Error(ctx, "Error in getting collections for tag Ids", zap.Error(err), zap.String(logger.TAG_ID, req.GetTagId()))
			resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return resp, nil
		}

		rules = rm.filterRulesByCollectionCategory(ctx, rules, collections)

		if err = rm.populateDisplayFields1(ctx, rules, req.GetFieldMasks()); err != nil {
			logger.Error(ctx, "Error in populating display fields", zap.Error(err))
			resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return resp, nil
		}
		resp.Rules = rules
		resp.PageContext = respToken
		return resp, nil
	}

	_, rules, err := rm.collectionsDao.GetCollectionAndChildren(ctx, req.GetCollectionId(), true, assignedUserGroups, req.GetPlatform(), req.GetAppVersion(), req.GetRuleCategory())
	if err != nil {
		logger.Error(ctx, "Error in getting rules for collection Id", zap.Error(err), zap.String(logger.COLLECTION_ID, req.GetCollectionId()))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	if err = rm.populateDisplayFields1(ctx, rules.GetRules().GetRules(), req.GetFieldMasks()); err != nil {
		logger.Error(ctx, "Error in populating display fields", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}

	resp.Rules = rules.GetRules().GetRules()
	logger.Debug(ctx, "GetRuleResp", zap.Any("rules", resp.GetRules()))
	return resp, nil
}

// for a given tagId, checks for collection associated with tag
// if only 1 collection is associated with the tag, rule type corresponding to collection type will be filtered and returned
// eg: if collection type is AUTO_SAVE, and rules list contains AUTO_SAVE and AUTO_PAY rules, filterRulesByCollectionCategory will return rules of type AUTO_SAVE
//
// NOTE: this is a special handling done for having Auto Invest and Auto Save cricket rules for same tournament
// since, Idempotency of cricket execution trigger is on MatchId, there cannot be 2 triggers separately for Auto Save and Auto Invest
// so, all the cricket rules should have single tag and only 1 trigger should execute all the rules (Save & Invest both)
//
// Hence, there is requirement of filtering based on category
// solution:
// Assume following Tag Mapping:
// TOURNAMENT_SAVE tag is mapped to  R1(Save), R2(Save), R3(Save), R4(Invest) rules
// TOURNAMENT_INVEST tag is mapped to R4(Invest)
//
// Collection Mapping:
// Cricket Craze - [TOURNAMENT_SAVE]
// Compound Cricket - [TOURNAMENT_INVEST]
//
// For serving UI, filterRulesByCollectionCategory fetches collection mapped with tag and rules corresponding to category(SAVE or INVEST) is filtered
func (rm *RuleManagerService) filterRulesByCollectionCategory(ctx context.Context, rules []*pb.Rule, collections []*pb.Collection) []*pb.Rule {
	if len(collections) > 1 {
		logger.Info(ctx, "more than 1 collection associated with tagId, not filtering rule")
		return rules
	}

	if len(collections) == 0 {
		logger.Info(ctx, "no collection associated with tagId, not filtering rule")
		return rules
	}

	var ruleCategory pb.RuleCategory
	switch collections[0].GetType() {
	case pb.CollectionType_COLLECTION_TYPE_AUTO_SAVE:
		ruleCategory = pb.RuleCategory_AUTO_SAVE
	case pb.CollectionType_COLLECTION_TYPE_AUTO_INVEST:
		ruleCategory = pb.RuleCategory_AUTO_INVEST
	case pb.CollectionType_COLLECTION_TYPE_AUTO_PAY:
		ruleCategory = pb.RuleCategory_AUTO_PAY
	case pb.CollectionType_COLLECTION_TYPE_US_STOCKS_SIP:
		ruleCategory = pb.RuleCategory_US_STOCKS_SIP
	default:
		logger.Info(ctx, "unhandled collection type for filtering rules", zap.String(logger.COLLECTION_TYPE, collections[0].GetType().String()))
		return rules
	}

	var filteredRules []*pb.Rule
	for _, r := range rules {
		if r.GetCategory() == ruleCategory {
			filteredRules = append(filteredRules, r)
		}
	}
	return filteredRules
}

func (rm *RuleManagerService) replaceRulesCountInDesc(ctx context.Context, param *pb.ReplaceableParam, desc string, name string) (string, bool, error) {
	rulesCount, rule, err := rm.GetRulesCountAndFirstRule(ctx, param)
	if err != nil {
		logger.Error(ctx, "Error while getting rule count, for replacing home card display text", zap.Error(err))
		return "", false, err
	}

	validCard := true
	switch rulesCount {
	case 0:
		validCard = false
	case 1:
		desc = strings.Replace(desc, fmt.Sprintf("{%s}", name), rule.GetName(), 1)
		desc = strings.Replace(desc, "Rules", "Rule", 1)
	default:
		desc = strings.Replace(desc, fmt.Sprintf("{%s}", name), fmt.Sprintf("%d", rulesCount), 1)
	}
	return desc, validCard, nil
}

func Bod(t time.Time, location *time.Location) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 0, 0, 0, 0, location)
}

func Eod(t time.Time, location *time.Location) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 23, 59, 59, 0, location)
}

func (rm *RuleManagerService) populateDisplayField1(ctx context.Context, rule *pb.Rule, masks []pb.DisplayDataFieldMask) error {
	tagIds := make([]string, 0, len(rule.GetRuleTags()))

	for _, tag := range rule.GetRuleTags() {
		tagIds = append(tagIds, tag.GetId())
	}
	for _, m := range masks {
		switch m {
		case pb.DisplayDataFieldMask_RULE_DISPLAY_INFO:
			displayInfo, err := rm.ruleDisplayInfoDao.GetByRuleName(ctx, rule.GetName(), ui.RuleCategory(ui.RuleCategory_value[rule.GetCategory().String()]))
			if err != nil {
				logger.Error(ctx, "Failed to get rule display info for rule name", zap.Error(err), zap.String(logger.RULE_NAME, rule.GetName()))
				return err
			}
			rule.DisplayData = displayInfo
		case pb.DisplayDataFieldMask_TAG_DISPLAY_INFO:
			continue
		case pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES, pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES_AND_SELECTOR_CTA:
			paramValues, err := rm.paramValuesDao.GetParamsByRuleIds(ctx, []string{rule.GetId()})
			if err != nil {
				logger.Error(ctx, "Failed to get tag display infos for rule name", zap.Error(err), zap.Strings(logger.TAG_IDS, tagIds))
				return err
			}
			ruleParamVals := paramValues[rule.GetId()]
			if m == pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES_AND_SELECTOR_CTA {
				if ruleParamVals == nil {
					// if possible param params are nil
					ruleParamVals = make(map[ui.RuleParamType]*pb.Params)
				}
				ctas, err := rm.paramValuesSelectorCtaDao.GetByRuleId(ctx, rule.GetId())
				if err != nil {
					logger.Error(ctx, "Failed to get param value selector cta", zap.Error(err), zap.Strings(logger.TAG_IDS, tagIds))
					return err
				}
				err2 := setSelectorCtaInRuleParams(ctx, ctas, ruleParamVals)
				if err2 != nil {
					return err2
				}
			}
			rule.ParamValues = getRuleParamsStringMap(ruleParamVals)
			logger.Debug(ctx, "rm.populateDisplayField1", zap.Any("rule.ParamValues", rule.ParamValues), zap.Any("ruleParamVals", ruleParamVals))
		case pb.DisplayDataFieldMask_REQUIRED_DATA_FIELD_MASK_UNSPECIFIED:
			fallthrough
		default:
			logger.Error(ctx, "unhandled display field mask type", zap.String(logger.FIELD_MASKS, m.String()))
			return fmt.Errorf("unkown display field mask type")
		}
	}
	return nil
}

func setSelectorCtaInRuleParams(ctx context.Context, ctas map[ui.RuleParamType]*ui.ParamValueSelectorCTA,
	ruleParamVals map[ui.RuleParamType]*pb.Params) error {

	for uiParamType, uiCta := range ctas {
		cta, err := newParamValueSelectorCta(ctx, uiCta)
		if err != nil {
			logger.Error(ctx, "setSelectorCtaInRuleParams(): error in fetching newParamValueSelectorCta", zap.Error(err))
			return err
		}
		p, exist := ruleParamVals[uiParamType]
		if exist {
			p.ValSelectorCta = cta
		} else {
			paramType, err := possible_param_values.GetRuleParamType(ctx, uiParamType)
			if err != nil {
				logger.Error(ctx, "setSelectorCtaInRuleParams(): error in GetRuleParamType", zap.Error(err))
				return err
			}
			ruleParamVals[uiParamType] = &pb.Params{
				Params:         nil,
				ParamValueType: paramType,
				ValSelectorCta: cta,
			}
		}
	}
	return nil
}

func getRuleParamsStringMap(params map[ui.RuleParamType]*pb.Params) map[string]*pb.Params {
	res := make(map[string]*pb.Params)
	for _, p := range params {
		res[p.ParamValueType.String()] = p
	}
	return res
}

func newParamValueSelectorCta(ctx context.Context, cta *ui.ParamValueSelectorCTA) (*pb.ParamValueSelectorCTA, error) {
	ctaOpt := cta.GetCta()
	switch {
	case ctaOpt.GetMfSelectorCta() != nil:
		return &pb.ParamValueSelectorCTA{
			Cta: &pb.ParamValueSelectorCTA_MfSelectorCta{
				MfSelectorCta: &pb.MutualFundSelectorCTA{
					Text:        ctaOpt.GetMfSelectorCta().GetText(),
					IconUrl:     ctaOpt.GetMfSelectorCta().GetIconUrl(),
					Deeplink:    ctaOpt.GetMfSelectorCta().GetDeeplink(),
					EditIconUrl: ctaOpt.GetMfSelectorCta().GetEditIconUrl(),
				},
			},
		}, nil
	case ctaOpt.GetCustomAmtCta() != nil:
		return &pb.ParamValueSelectorCTA{
			Cta: &pb.ParamValueSelectorCTA_CustomAmtCta{
				CustomAmtCta: ctaOpt.GetCustomAmtCta(),
			},
		}, nil
	case ctaOpt.GetRecurringPaymentCta() != nil:
		return &pb.ParamValueSelectorCTA{
			Cta: &pb.ParamValueSelectorCTA_RecurringPaymentCta{
				RecurringPaymentCta: &pb.RecurringPaymentRecipientSelectorCTA{
					Text:         ctaOpt.GetRecurringPaymentCta().GetText(),
					Deeplink:     ctaOpt.GetRecurringPaymentCta().GetDeeplink(),
					IconUrl:      ctaOpt.GetRecurringPaymentCta().GetIconUrl(),
					EditIconUrl:  ctaOpt.GetRecurringPaymentCta().GetEditIconUrl(),
					PayeeBgColor: ctaOpt.GetRecurringPaymentCta().GetPayeeBgColor(),
				},
			},
		}, nil
	default:
		logger.Error(ctx, "unknown param value selector", zap.String("selector_cta", ctaOpt.String()))
		return nil, fmt.Errorf("unkown display field mask type")
	}
}

func (rm *RuleManagerService) populateDisplayFields1(ctx context.Context, rules []*pb.Rule, masks []pb.DisplayDataFieldMask) error {
	ruleNamesMap := make(map[ui.RuleCategory][]string)
	rulesMap := make(map[ui.RuleCategory][]*pb.Rule)
	ruleIds := make([]string, 0, len(rules))

	for _, rule := range rules {
		category := ui.RuleCategory(ui.RuleCategory_value[rule.GetCategory().String()])
		ruleNamesMap[category] = append(ruleNamesMap[category], rule.GetName())
		rulesMap[category] = append(rulesMap[category], rule)
		ruleIds = append(ruleIds, rule.GetId())
	}

	for _, m := range masks {
		switch m {
		case pb.DisplayDataFieldMask_RULE_DISPLAY_INFO:
			for category, ruleNames := range ruleNamesMap {
				displayInfo, err := rm.ruleDisplayInfoDao.GetByRuleNames(ctx, ruleNames, category)
				if err != nil {
					logger.Error(ctx, "Failed to get rule display info for rule name", zap.Error(err), zap.Strings(logger.RULE_NAME, ruleNames))
					return err
				}

				for _, rule := range rulesMap[category] {
					rule.DisplayData = displayInfo[rule.GetName()]
				}
			}
		case pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES, pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES_AND_SELECTOR_CTA:
			err2 := rm.getParamsByRuleIds(ctx, rules, ruleIds, m)
			if err2 != nil {
				logger.Error(ctx, "error in getParamsByRuleIds")
				return err2
			}
		case pb.DisplayDataFieldMask_TAG_DISPLAY_INFO:
			continue
		case pb.DisplayDataFieldMask_REQUIRED_DATA_FIELD_MASK_UNSPECIFIED:
			fallthrough
		default:
			logger.Error(ctx, "unhandled display field mask type", zap.String(logger.FIELD_MASKS, m.String()))
			return fmt.Errorf("unhandled display field mask type\"")
		}
	}
	return nil
}

func (rm *RuleManagerService) getParamsByRuleIds(ctx context.Context, rules []*pb.Rule, ruleIds []string,
	m pb.DisplayDataFieldMask) error {
	paramValues, err := rm.paramValuesDao.GetParamsByRuleIds(ctx, ruleIds)
	if err != nil {
		logger.Error(ctx, "Failed to get possible param values for rules", zap.Error(err), zap.Strings(logger.RULE_IDS, ruleIds))
		return err
	}
	var ctas map[string]map[ui.RuleParamType]*ui.ParamValueSelectorCTA
	if m == pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES_AND_SELECTOR_CTA {
		ctas, err = rm.paramValuesSelectorCtaDao.GetByRuleIds(ctx, ruleIds)
		if err != nil {
			logger.Error(ctx, "Failed to get param value selector cta", zap.Error(err), zap.Strings(logger.RULE_IDS, ruleIds))
			return err
		}
	}
	for _, rule := range rules {
		if paramValues[rule.GetId()] == nil {
			paramValues[rule.GetId()] = make(map[ui.RuleParamType]*pb.Params)
		}

		if m == pb.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES_AND_SELECTOR_CTA {
			err2 := setSelectorCtaInRuleParams(ctx, ctas[rule.Id], paramValues[rule.GetId()])
			if err2 != nil {
				logger.Error(ctx, "error in setSelectorCtaInRuleParams", zap.Error(err))
				return err2
			}
		}
		rule.ParamValues = getRuleParamsStringMap(paramValues[rule.GetId()])
		logger.Debug(ctx, "getParamsByRuleIds", zap.Any("rule.ParamValues", rule.ParamValues),
			zap.Any("ruleParamValues", paramValues[rule.GetId()]))
	}
	return nil
}

func (rm *RuleManagerService) GetProfile(ctx context.Context, req *pb.GetProfileRequest) (*pb.GetProfileResponse, error) {
	resp := &pb.GetProfileResponse{Status: rpc.StatusOk()}
	// calling the dao layer
	rmsProfile, err := rm.rmsProfileDao.GetProfile(ctx, req.GetActorId(), req.Client)
	if err != nil {
		logger.Error(ctx, "Error while getting rms profile", zap.Error(err), zap.Any(logger.ACTOR_ID, req.GetActorId()))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	logger.Debug(ctx, "GetProfile", zap.Any("GetProfileResp", rmsProfile))
	resp.Profile = rmsProfile
	return resp, nil
}

func (rm *RuleManagerService) UpdateProfile(ctx context.Context, req *pb.UpdateProfileRequest) (*pb.UpdateProfileResponse, error) {
	resp := &pb.UpdateProfileResponse{Status: rpc.StatusOk()}
	// make db query
	profileResp, err := rm.rmsProfileDao.UpdateProfile(ctx, req.GetActorId(), req.GetClient(), req.GetProfile())
	if err != nil {
		logger.Error(ctx, "Error while updating rms profile", zap.Error(err), zap.Any(logger.ACTOR_ID, req.GetActorId()))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	logger.Debug(ctx, "UpdateProfile", zap.Any("UpdateProfileResp", profileResp))
	resp.Status = rpc.StatusOk()
	return resp, nil
}

func (rm *RuleManagerService) isSubscriptionExecuted(ctx context.Context, currentTime time.Time, subId string) (bool, error) {
	sub, err := rm.subscriptionRuntimeInfoDao.GetById(ctx, subId)
	if err != nil {
		logger.Error(ctx, "error in getting executions for calculating next execution date", zap.Error(err), zap.String(logger.SUBSCRIPTION_ID, subId))
		return false, err
	}
	if datetime.StartOfDay(sub.LastExecutedAt.Time).Equal(datetime.StartOfDay(currentTime)) {
		return true, nil
	}
	return false, nil
}

// uses valid_from date and frequency to compute next execution date
// eg: If valid_from is 15 Aug, Freq is Weekly, current date is 27 Aug, next exec would be 29 Aug
// nolint: funlen
func (rm *RuleManagerService) getNextExecutionDate(ctx context.Context, validFromVal *ui.DateVal, validTillVal *ui.DateVal,
	frequencyVal *ui.FrequencyVal, subId string) (*timestamp.Timestamp, error) {

	var nextDateTime, currentDateTime, validFromDateTime, validTillDateTime time.Time
	// time.Now() returns time in IST, converting it to UTC for consistency
	currentDateTime = datetime.GetTimeAtStartOfTheDay(time.Now().In(time.UTC))
	// .AsTime() returns time in UTC
	validFromDateTime = datetime.GetTimeAtStartOfTheDay(validFromVal.GetDate().AsTime())
	validTillDateTime = datetime.GetTimeAtStartOfTheDay(validTillVal.GetDate().AsTime())

	logger.Debug(ctx, "date time", zap.Any("currentDateTime", currentDateTime), zap.Any("validFromDateTime", validFromDateTime),
		zap.Any("validTillDateTime", validTillDateTime))
	if currentDateTime.IsZero() || validFromDateTime.IsZero() {
		return nil, fmt.Errorf("current time or validFrom time cannot be zero time")
	}

	// if validFromTime is after current time -- in case of new subscriptions
	// return validFromTime as next execution time
	if currentDateTime.Before(validFromDateTime) {
		// current time is before or equal the execution time
		// so setting the next execution time as subscription param time
		return validFromVal.Date, nil
	}

	// if the subscription only has validFromTime (in case of one-time payments)
	if validFromVal != nil && (validTillVal == nil && frequencyVal == nil) {
		if currentDateTime.Equal(validFromDateTime) {
			// valid from is the same as current time
			// check if the subscription is already executed,
			// if yes, show '--', if no, show the current date time
			alreadyExecuted, err := rm.isSubscriptionExecuted(ctx, currentDateTime, subId)
			if err != nil {
				logger.Error(ctx, "error fetching execution state of subscription")
				return nil, err
			}
			if alreadyExecuted {
				return nil, nil
			}
			return validFromVal.Date, nil
		}
		return nil, nil
	}

	// this has been kept after sd validTill can be null for some rules
	// when validTill time has already passed
	if currentDateTime.After(validTillDateTime) {
		return nil, nil
	}

	if currentDateTime.Equal(validTillDateTime) {
		// valid till is the same as current time
		alreadyExecuted, err := rm.isSubscriptionExecuted(ctx, currentDateTime, subId)
		if err != nil {
			logger.Error(ctx, "error fetching execution state of subscription")
			return nil, err
		}
		if alreadyExecuted {
			return nil, nil
		}
		return validTillVal.Date, nil
	}

	var err error
	switch frequencyVal.GetFrequency() {
	case ui.Frequency_FREQUENCY_WEEKLY:
		// TODO: add noOfDays in FrequencyVal
		nextDateTime, err = rm.nextPeriodicDateBasedOnDays(ctx, currentDateTime, validFromDateTime, 7, subId)
	case ui.Frequency_FREQUENCY_FORTNIGHTLY:
		nextDateTime, err = rm.nextPeriodicDateBasedOnDays(ctx, currentDateTime, validFromDateTime, 15, subId)
	case ui.Frequency_FREQUENCY_MONTHLY:
		nextDateTime, err = rm.nextPeriodicDateBasedOnMonths(ctx, currentDateTime, validFromDateTime, 1, subId)
	case ui.Frequency_FREQUENCY_QUARTERLY:
		nextDateTime, err = rm.nextPeriodicDateBasedOnMonths(ctx, currentDateTime, validFromDateTime, 3, subId)
	case ui.Frequency_FREQUENCY_HALF_YEARLY:
		nextDateTime, err = rm.nextPeriodicDateBasedOnMonths(ctx, currentDateTime, validFromDateTime, 6, subId)
	case ui.Frequency_FREQUENCY_YEARLY:
		nextDateTime, err = rm.nextPeriodicDateBasedOnMonths(ctx, currentDateTime, validFromDateTime, 12, subId)
	default:
		err = fmt.Errorf("invalid frequency val %v", frequencyVal.String())
	}
	if err != nil {
		return nil, err
	}
	// if next execution date turns out to be after validTill, return nil
	if nextDateTime.After(validTillDateTime) {
		return nil, nil
	}
	return timestamppb.New(nextDateTime), nil
}

// returns next date on which the subscription will be executed - min period is 1 day
// function suitable for weekly(7) and fortnightly(15) calculations or when minimum difference between two time periods is at-least one day
// now Time is to be ensured that it is after validFrom Time
func (rm *RuleManagerService) nextPeriodicDateBasedOnDays(ctx context.Context, now, validFrom time.Time, noOfDays int, subId string) (time.Time, error) {
	// expected yearly date is today's date
	expectedDate := now
	dayDiff := int(now.Sub(validFrom).Hours()/24) % noOfDays
	switch {
	case dayDiff > 0:
		expectedDate = expectedDate.AddDate(0, 0, noOfDays-dayDiff)
	case dayDiff == 0:
		// checking if sub is already executed
		alreadyExecuted, err := rm.isSubscriptionExecuted(ctx, now, subId)
		if err != nil {
			logger.Error(ctx, "error fetching execution state of subscription")
			return time.Time{}, err
		}
		if alreadyExecuted {
			expectedDate = expectedDate.AddDate(0, 0, noOfDays)
		}
	}
	return expectedDate, nil
}

// returns next date on which the subscription will be executed - min period is 1 month
// function suitable for monthly, quarterly, halfYearly and Yearly calculations or when minimum difference between two time periods is at-least one month
// now Time is to be ensured that it is after validFrom Time
func (rm *RuleManagerService) nextPeriodicDateBasedOnMonths(ctx context.Context, now, validFrom time.Time, noOfMonths int, subId string) (time.Time, error) {
	// expected yearly date is this year date with same day and month as validFrom
	expectedDate := time.Date(now.Year(), now.Month(), validFrom.Day(), 0, 0, 0, 0, time.UTC)
	monthDiff := int(now.Month()-validFrom.Month()) % noOfMonths
	switch {
	case monthDiff < 0:
		expectedDate = expectedDate.AddDate(0, (monthDiff*-1)%noOfMonths, 0)
	case monthDiff > 0:
		expectedDate = expectedDate.AddDate(0, noOfMonths-monthDiff, 0)
	case monthDiff == 0:
		dayDiff := int(now.Day() - validFrom.Day())
		switch {
		case dayDiff == 0:
			alreadyExecuted, err := rm.isSubscriptionExecuted(ctx, now, subId)
			if err != nil {
				logger.Error(ctx, "error fetching execution state of subscription")
				return time.Time{}, err
			}
			if alreadyExecuted {
				expectedDate = expectedDate.AddDate(0, noOfMonths, 0)
			}
		case dayDiff > 0:
			// check date - if the date is already passed, add period for next execution time
			expectedDate = expectedDate.AddDate(0, noOfMonths, 0)
		}
	}
	return expectedDate, nil
}

func (rm *RuleManagerService) getTimestampToDayOfNextMonth(ctx context.Context, dayOfMonth int32, subId string) (*timestamp.Timestamp, error) {
	currentTime := time.Now()
	if currentTime.Day() == int(dayOfMonth) {
		alreadyExecuted, err := rm.isSubscriptionExecuted(ctx, currentTime, subId)
		if err != nil {
			return nil, err
		}
		if !alreadyExecuted {
			return timestamppb.New(currentTime), nil
		}
	}

	if int(dayOfMonth) > currentTime.Day() {
		return timestamppb.New(time.Date(currentTime.Year(), currentTime.Month(), int(dayOfMonth), currentTime.Hour(), currentTime.Minute(), 0, 0, time.UTC)), nil
	} else {
		// condition entered when subscription has been executed for this month (either today or before today)
		var nextExecutionAt *timestamppb.Timestamp
		nextExecutionYear := currentTime.Year()
		if currentTime.Month() == 12 {
			nextExecutionYear = currentTime.Year() + 1
		}
		nextExecutionAt = timestamppb.New(time.Date(nextExecutionYear, ((currentTime.Month())%12)+1, int(dayOfMonth), currentTime.Hour(), currentTime.Minute(), 0, 0, time.UTC))
		return nextExecutionAt, nil
	}
}

func (rm *RuleManagerService) GetUserStats(ctx context.Context, req *pb.GetUserStatsRequest) (*pb.GetUserStatsResponse,
	error) {
	resp := &pb.GetUserStatsResponse{Status: rpc.StatusOk()}
	g := errgroup.New()

	if hasUserStatsFieldMask(req.FieldMasks, pb.UserStatsFieldMask_USER_STATS_SUBSCRIPTION_COUNT_PER_RULE) {
		g.Go(func() error {
			subsCountPerRule, _, _, err := rm.subscriptionDao.GetSubsCount(ctx, req.GetActorId(),
				req.GetSubsCountReq().GetRuleIds(), event.RMSClient_RMS_CLIENT_UNSPECIFIED, nil,
				req.GetSubsCountReq().GetStates())
			if err != nil {
				logger.Error(ctx, "failed to get subscription count", zap.Error(err))
				return err
			}
			resp.SubsCountStats = &pb.SubscriptionCountStats{
				CountMap: subsCountPerRule,
			}
			return nil
		})
	}

	if hasUserStatsFieldMask(req.FieldMasks, pb.UserStatsFieldMask_USER_STATS_TOTAL_SUBSCRIPTIONS_COUNT) {
		g.Go(func() error {
			totalCountResp, err := rm.subscriptionRuntimeInfoDao.GetCount(ctx, &dao.GetCountRequest{
				ActorId:   req.GetActorId(),
				States:    []pb.RuleSubscriptionState{pb.RuleSubscriptionState_ACTIVE},
				CountMask: []dao.CountFieldMask{dao.SUBSCRIPTION_COUNT_FOR_ACTOR},
			})
			if err != nil {
				logger.Error(ctx, "failed to get total subscription count", zap.Error(err))
				return err
			}
			resp.TotalSubsCount = totalCountResp.CountMap[dao.SUBSCRIPTION_COUNT_FOR_ACTOR]
			return nil
		})
	}
	err := g.Wait()
	if err != nil {
		logger.Error(ctx, "failed to get user stats", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	return resp, nil
}

func hasUserStatsFieldMask(masks []pb.UserStatsFieldMask, mask pb.UserStatsFieldMask) bool {
	for _, m := range masks {
		if m == mask {
			return true
		}
	}
	return false
}

func isFieldMasked(fieldMask *fieldmaskpb.FieldMask, field string) bool {
	if len(fieldMask.GetPaths()) == 0 {
		// if not set, then it is assumed that none of the fields are masked
		return false
	}
	for _, p := range fieldMask.Paths {
		if p == field {
			return false
		}
	}
	return true
}

func (rm *RuleManagerService) GetRulesForSubscriptions(ctx context.Context,
	req *pb.GetRulesForSubscriptionsRequest) (resp *pb.GetRulesForSubscriptionsResponse, err error) {
	resp = &pb.GetRulesForSubscriptionsResponse{Status: rpc.StatusOk()}
	subsToRulesMap, err := rm.subscriptionRuntimeInfoDao.GetRuleIdsBySubsIds(ctx, req.GetSubscriptionIds())
	if err != nil {
		logger.Error(ctx, "Error in getting rule ids", zap.Error(err),
			zap.Strings(logger.SUBSCRIPTION_ID,
				req.GetSubscriptionIds()))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	if isFieldMasked(req.FieldMask, "rules_map") {
		resp.SubsToRulesMap = subsToRulesMap
		return resp, nil
	}
	var ruleIds []string
	ruleIdsDedupe := make(map[string]struct{})
	for _, ruleId := range subsToRulesMap {
		if _, exist := ruleIdsDedupe[ruleId]; !exist {
			ruleIds = append(ruleIds, ruleId)
			ruleIdsDedupe[ruleId] = struct{}{}
		}
	}
	g, gCtx := errgroup.WithContext(ctx)
	var rulesMap map[string]*pb.Rule
	var ruleTags map[string][]*pb.RuleTag
	g.Go(func() error {
		rulesMap, _, err = rm.ruleDao.GetByIds(gCtx, ruleIds)
		if err != nil {
			logger.Error(gCtx, "Error in getting rules", zap.Error(err), zap.Strings(logger.RULE_IDS, ruleIds))
			return err
		}
		return nil
	})
	g.Go(func() error {
		ruleTags, err = rm.tagMappingsDao.GetTagsForRules(gCtx, ruleIds, []pb.TagState{pb.TagState_TAG_ACTIVE})
		if err != nil {
			logger.Error(gCtx, "Failed to get tags information for rule", zap.Error(err), zap.Strings(logger.RULE_IDS, ruleIds))
			return err
		}
		return nil
	})
	err = g.Wait()
	if err != nil {
		logger.Error(ctx, "Failed to get rule details", zap.Error(err))
		resp.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return resp, nil
	}
	for ruleId, rule := range rulesMap {
		rule.RuleTags = ruleTags[ruleId]
	}
	if !isFieldMasked(req.FieldMask, "subs_to_rules_map") {
		resp.SubsToRulesMap = subsToRulesMap
	}
	resp.RulesMap = rulesMap
	return resp, nil
}

func (rm *RuleManagerService) publishInvestmentEvent(ctx context.Context, subscription *pb.RuleSubscription, rule *pb.Rule) error {
	subscription.GetRuleId()
	var err error
	switch rule.GetCategory() {
	case pb.RuleCategory_AUTO_INVEST:
		// ToDO(Junaid): We don't have any meta-data to differentiate US Stocks. Once US stocks gets built for fit,
		//				update this to split between mutual funds and us stocks.
		_, err = rm.investmentInstrumentEventPublisher.Publish(ctx, &consumer.ProcessInvestmentEventRequest{
			InstrumentType: investmentPb.InvestmentInstrumentType_MUTUAL_FUNDS,
			InstrumentData: &consumer.ProcessInvestmentEventRequest_MfSubscriptionData{MfSubscriptionData: &consumer.MFSubscriptionData{
				ActorId: subscription.GetActorId(), RuleId: subscription.GetRuleId()}},
		})
	case pb.RuleCategory_AUTO_SAVE:
		// AutoInvest is only there for Smart Deposit as of now
		_, err = rm.investmentInstrumentEventPublisher.Publish(ctx, &consumer.ProcessInvestmentEventRequest{
			InstrumentType: investmentPb.InvestmentInstrumentType_SMART_DEPOSIT,
			InstrumentData: &consumer.ProcessInvestmentEventRequest_SdSubscriptionData{SdSubscriptionData: &consumer.SDSubscriptionData{ActorId: subscription.GetActorId(), RuleId: subscription.GetRuleId()}},
		})
	default:
		return nil
	}
	if err != nil {
		return err
	}
	return nil
}

func (rm *RuleManagerService) publishUssSipEvent(ctx context.Context, subscription *pb.RuleSubscription, state pb.RuleSubscriptionState, isNew string) error {
	usStockId, ok := subscription.GetRuleParamValues().GetRuleParamValues()["usStockId"]
	if !ok {
		return fmt.Errorf("error getting us stock id from subscription")
	}
	purchaseAmt, ok := subscription.GetRuleParamValues().GetRuleParamValues()["purchaseAmount"]
	if !ok {
		return fmt.Errorf("error while getting purchase amount")
	}
	stockId := usStockId.GetUsStockValue().GetStockId()
	sipAmount := purchaseAmt.GetMoneyVal().GetBeMoney()
	actorId := subscription.GetActorId()
	stock, err := rm.usStocks.GetStock(ctx, stockId)
	if err != nil {
		return errors.Wrap(err, "error in getting stock name")
	} else {
		switch state {
		case pb.RuleSubscriptionState_INACTIVE:
			rm.publishEventForUSSSipDeactivated(ctx, actorId, stock.Name, sipAmount)
		case pb.RuleSubscriptionState_ACTIVE:
			switch isNew {
			case "new":
				rm.publishEventForUSSSipSetup(ctx, actorId, stock.Name, sipAmount, "new")
			case "modified":
				rm.publishEventForUSSSipSetup(ctx, actorId, stock.Name, sipAmount, "modified")
			default:
				logger.Error(ctx, "unknown sip type", zap.String(logger.SUBSCRIPTION_ID, subscription.GetId()))
			}
		}
	}
	return nil
}

func (rm *RuleManagerService) publishEventForUSSSipSetup(ctx context.Context, actorId string, stockName string, sipAmountInINR *money.Money, sipType string) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		rm.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewUSSSipSetupSuccessServer(actorId, stockName, sipAmountInINR, sipType))
	})
}

func (rm *RuleManagerService) publishEventForUSSSipDeactivated(ctx context.Context, actorId string, stockName string, sipAmountInINR *money.Money) {
	goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
		rm.eventBroker.AddToBatch(epificontext.WithEventAttributesV2(ctx), events.NewUSSSipDeactivatedServer(actorId, stockName, sipAmountInINR))
	})
}
