package config

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/epifi/be-common/pkg/cfg"

	releaseConf "github.com/epifi/gamma/pkg/feature/release/config"
)

const (
	SAVINGS_CREATION_QUEUE_KEY = "savingscreationqueue"
	RudderWriteKey             = "RudderWriteKey"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, cfg.SAVINGS_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	keyToSecret, err := cfg.LoadAllSecrets(conf.EpifiDb, conf.Secrets.Ids, conf.Application.Environment, conf.AWS.Region)
	if err != nil {
		return nil, err
	}

	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	return conf, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}
		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

// TODO(kunal): Add validate tags later and verify config struct post unmarshalling
//
//go:generate conf_gen github.com/epifi/gamma/savings/config Config
type Config struct {
	Application               *Application
	Server                    *Server
	Logging                   *cfg.Logging
	EpifiDb                   *cfg.DB
	Secrets                   *cfg.Secrets
	SavingsCreationPublisher  *cfg.SqsPublisher
	SavingsCreationSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	AWS                       *cfg.AWS

	// To enable UPI ID creation on the account creation in Async
	CreateVPASubscriber *cfg.SqsSubscriber `dynamic:"true"`
	CreateVPAPublisher  *cfg.SqsPublisher
	// To enable savings account PI creation Async
	SavingsAccountPICreationSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	SavingsAccountPICreationPublisher  *cfg.SqsPublisher
	// To broadcast savings account savings.State change
	// Starting with, will be published only when an account is created.
	// Later on, other states will be added
	AccountStatePublisher           *cfg.SnsPublisher
	Flags                           *Flags `dynamic:"true"`
	RudderStack                     *cfg.RudderStackBroker
	BalanceEnqTimeout               time.Duration
	SavingsCallbackSubscriber       *cfg.SqsSubscriber `dynamic:"true"`
	AccountStmtPublisher            *cfg.SqsPublisher
	AccountStmtSubscriber           *cfg.SqsSubscriber `dynamic:"true"`
	StatementPreSignedURLExpiryTime time.Duration

	// map<savings.SKU, map<vendorgateway.Vendor, VendorSku>>
	VendorSKUMapping map[string]map[string]string

	// sqs publisher delay for account creation enquiry
	AccountCreationEnquiryDelay time.Duration
	// for sending af_purchase event
	EventAfPurchasePublisher *cfg.SqsPublisher
	// user groups which will use the new balance api in VG
	GetBalanceV1Params *GetBalanceV1Params `dynamic:"true"`
	GetBalanceParams   *GetBalanceParams   `dynamic:"true"`
	Tracing            *cfg.Tracing
	Profiling          *cfg.Profiling
	// external account configs
	ExternalAccounts *ExternalAccounts `dynamic:"true"`
	// Event for SA balance updated published
	BalanceUpdateEventPublisher *cfg.SnsPublisher
	// To notify the user on low balance amount
	BalanceUpdateEventSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	// To update the scheme change on vendor and savings account
	ProcessTierUpdateEventSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	// To update scheme change when a min Kyc regular user becomes full Kyc user
	ProcessKycUpdateEventSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	// cancels the sa closure request on balance update
	ProcessBalanceUpdateEventSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	Process                             *cfg.SqsSubscriber `dynamic:"true"`
	// Enable or disable balance update event publishing
	EnableBalanceUpdateEventPublishing bool `dynamic:"true"`
	// Low balance notification related params
	LowBalanceNotificationParams *LowBalanceNotificationParams
	RedisOptions                 *cfg.RedisOptions
	SavingsCacheConfig           *SavingsCacheConfig `dynamic:"true"`
	// AccountConstraintsConfig contains account constraints (freeze status, reason) related configs
	AccountConstraintsConfig   *AccountConstraintsConfig
	BalanceHistoryRedisOptions *cfg.RedisOptions

	// ShouldTxnAggregateViaPinot flag to switch transaction aggregate call to pinot ( which is routed via pay rpc) OR via ES. If it is true it will get transaction aggregates from Pinot.
	IsTxnAggregateCalculatedViaPinot bool `dynamic:"true"`

	// TransactionAggregateParams will set the from_date in txn aggregation.
	// Some transactions get back posted after CSIS timing and aggregate to match credit/debit will be impacted.
	// For these cases we have to aggregate Txn after CSIS time.
	TransactionAggregateParams *TransactionAggregateParams `dynamic:"true"`

	SignExitUrl string

	// OperationalStatusUpdateSubscriber listens to account status change from accounts service
	OperationalStatusUpdateSubscriber *cfg.SqsSubscriber `dynamic:"true"`

	// VerifyBalanceAtWithLastOrderDataInDb will check if
	VerifyBalanceAtWithLastOrderDataInDb bool `dynamic:"true"`
	EnableSchemeChangeUpdate             bool `dynamic:"true"`
	// Events for balance changes
	BalanceChangeEventPublisher        *cfg.SnsPublisher
	ThirdPartyAccountSharingPublisher  *cfg.SqsPublisher
	ThirdPartyAccountSharingSubscriber *cfg.SqsSubscriber     `dynamic:"true"`
	MinKycUserParams                   *MinKycUserParams      `dynamic:"true"`
	DisableGetAccountBalanceV1         bool                   `dynamic:"true"`
	SignLegality                       *SignLegality          `dynamic:"true"`
	MinSalaryRequiredForUltimate       int64                  `dynamic:"true"`
	SavingsAccountClosure              *SavingsAccountClosure `dynamic:"true"`
	// list of actor ids to override sign availability with bank
	// overridden actors are considered to not have a signature even if bank api tells the signature is available with bank
	OverrideSignAvailabilityWithBank            []string      `dynamic:"true"`
	EnableAccountVerificationViaPayService      bool          `dynamic:"true"`
	AccountVerificationPollingAttempts          int           `dynamic:"true"`
	AccountVerificationPollingSleepDuration     time.Duration `dynamic:"true"`
	BufferDaysToFetchStatementForClosureBalance int           `dynamic:"true"`
	DrOperativeParticularString                 string
}

type Application struct {
	Environment   string
	Name          string
	IsSecureRedis bool
}

type SavingsAccountClosure struct {
	// closure request with no updates for ExpiryDuration is considered inactive and marked as expired
	ExpiryDuration time.Duration `dynamic:"true"`
	MaxPageSize    uint32        `dynamic:"true"`
	// time window to wait before sending the sa closure request to federal after the user has submitted the closure request
	SubmissionWindow     time.Duration `dynamic:"true"`
	CancelRequestNudgeId string        `dynamic:"true"`
}

type SignLegality struct {
	SignatureInReviewPeriod time.Duration `dynamic:"true"`
}

type SavingsCacheConfig struct {
	IsCachingEnabled                       bool
	SavingsIdPrefix                        string
	SecondaryIdPrefix                      string
	CacheTTl                               time.Duration
	EssentialsCacheTTLForNonActiveAccounts time.Duration `dynamic:"true"`
	EssentialsCacheTTLForActiveAccounts    time.Duration `dynamic:"true"`
}

type Server struct {
	Ports        *cfg.ServerPorts
	EnablePoller bool
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
	// PercentageRollOutForDiffCheckBtwnBalanceApi to compute the diff between v0 & v1 API of balance check from vendor
	PercentageRollOutForDiffCheckBtwnBalanceApi *PercentageRollOutForDiffCheckBtwnBalanceApi `dynamic:"true"`
}

type PercentageRollOutForDiffCheckBtwnBalanceApi struct {
	IsFeatureEnabled           bool                                    `dynamic:"true"`
	TimeToWait                 time.Duration                           `dynamic:"true"`
	StickinessConstraintConfig *releaseConf.StickinessConstraintConfig `dynamic:"true"`
}

type FetchOpeningBalanceAlwaysFromVendorUserGroupParams struct {
	// bool to decide if FetchOpeningBalanceAlwaysFromVendor is restricted to user groups
	IsRestricted bool
	// if FetchOpeningBalanceAlwaysFromVendor is restricted, the list of user groups that have access
	AllowedUserGroups commontypes.UserGroups
}

type ClosingBalanceUserGroupParams struct {
	// bool to decide if ClosingBalance api is restricted to user groups
	IsRestricted bool
	// if ClosingBalance api is restricted, the list of user groups that have access
	AllowedUserGroups commontypes.UserGroups
}

type GetBalanceV1Params struct {
	// To control if the balance should be retrieved using GetBalance
	UseGetBalance *cfg.FeatureReleaseConfig `dynamic:"true"`
	// To control if the balance should be retrieved using GetBalanceV1
	UseGetBalanceV1 *cfg.FeatureReleaseConfig `dynamic:"true"`
	// timeout for concurrent goroutine calls to VG
	VgApiTimeout time.Duration
	// Following are the Data Freshness parameters -
	// To be considered if client uses GetAccountBalanceV1Request_NEAR_REAL_TIME enum
	DataFreshnessNearRealTime time.Duration
	// To be considered if client uses GetAccountBalanceV1Request_STALE enum
	DataFreshnessStale time.Duration
	// To define the lease duration for which the lock is obtained
	LockLeaseDuration time.Duration
}

type GetBalanceParams struct {
	// Following are the Data Freshness parameters -
	// To be considered if client uses GetAccountBalanceV1Request_NEAR_REAL_TIME enum
	DataFreshnessNearRealTime time.Duration `dynamic:"true"`
	// To be considered if client uses GetAccountBalanceV1Request_STALE enum
	DataFreshnessStale time.Duration `dynamic:"true"`
	// To define the lease duration for which the lock is obtained
	LockLeaseDuration time.Duration `dynamic:"true"`
}

type ExternalAccounts struct {
	// InhouseNameMatchThreshold decides the score above which name match
	// is passed during bank account verifications
	InhouseNameMatchThreshold float32
	// NameMatchFailLimit is the number of times add bank account can fail
	// in case user given name mismatch
	NameMatchFailLimit int32
	// AccValidationFailLimit is the number of times add bank account can fail
	// after user given name matches
	AccValidationFailLimit int32
	// BAVAttemptCooldown is the time after which in progress account verification
	// can be retried. The delay is to avoid client spamming AddBankAccount rpc rather
	// than retrying
	BAVAttemptCooldown time.Duration
	// EnableBalanceRefundIncidentCreation to enable min kyc balance transfer incidents on watson.
	// Only applicable for external accounts verified in-app
	EnableBalanceRefundIncidentCreation bool `dynamic:"true"`
	// IssueCategoryId is for SubCategory_SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND
	IssueCategoryId string `dynamic:"true"`
}

type LowBalanceNotificationParams struct {
	// Title to be shown on low balance notification when user does off-app transactions
	Title string
	// Subtitle of notification to be shown when user is running low on balance
	SubTitle string
	// Threshold for low balance
	ThresholdBalance int64
}

type AccountConstraintsConfig struct {
	// CreditFreezeBannerElementId is the banner element id configured to show credit freeze message
	CreditFreezeBannerElementId string
}

type TransactionAggregateParams struct {
	Enable bool `dynamic:"true"`
	// We have seen in case of CSIS downtime bank back post txn
	// In this case we have to aggregate txn after CSIS time.
	FromDate string `dynamic:"true"`
}

type MinKycUserParams struct {
	MinKycTotalCreditedLimitForNewUser int64 `dynamic:"true"`
	MinKycMaxSavingsBalanceForNewUser  int64 `dynamic:"true"`
	MinKycTotalCreditedLimit           int64 `dynamic:"true"`
	MinKycMaxSavingsBalance            int64 `dynamic:"true"`
}
