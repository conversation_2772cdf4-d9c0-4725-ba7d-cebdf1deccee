// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minsalaryrequiredforultimate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinSalaryRequiredForUltimate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinSalaryRequiredForUltimate, nil
	case "accountverificationpollingattempts":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AccountVerificationPollingAttempts\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AccountVerificationPollingAttempts, nil
	case "bufferdaystofetchstatementforclosurebalance":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BufferDaysToFetchStatementForClosureBalance\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BufferDaysToFetchStatementForClosureBalance, nil
	case "enablebalanceupdateeventpublishing":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableBalanceUpdateEventPublishing\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableBalanceUpdateEventPublishing, nil
	case "istxnaggregatecalculatedviapinot":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsTxnAggregateCalculatedViaPinot\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsTxnAggregateCalculatedViaPinot, nil
	case "verifybalanceatwithlastorderdataindb":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VerifyBalanceAtWithLastOrderDataInDb\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VerifyBalanceAtWithLastOrderDataInDb, nil
	case "enableschemechangeupdate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableSchemeChangeUpdate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableSchemeChangeUpdate, nil
	case "disablegetaccountbalancev1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableGetAccountBalanceV1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableGetAccountBalanceV1, nil
	case "enableaccountverificationviapayservice":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableAccountVerificationViaPayService\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableAccountVerificationViaPayService, nil
	case "accountverificationpollingsleepduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AccountVerificationPollingSleepDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AccountVerificationPollingSleepDuration, nil
	case "overridesignavailabilitywithbank":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OverrideSignAvailabilityWithBank\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OverrideSignAvailabilityWithBank, nil
	case "savingscreationsubscriber":
		return obj.SavingsCreationSubscriber.Get(dynamicFieldPath[1:])
	case "createvpasubscriber":
		return obj.CreateVPASubscriber.Get(dynamicFieldPath[1:])
	case "savingsaccountpicreationsubscriber":
		return obj.SavingsAccountPICreationSubscriber.Get(dynamicFieldPath[1:])
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "savingscallbacksubscriber":
		return obj.SavingsCallbackSubscriber.Get(dynamicFieldPath[1:])
	case "accountstmtsubscriber":
		return obj.AccountStmtSubscriber.Get(dynamicFieldPath[1:])
	case "getbalancev1params":
		return obj.GetBalanceV1Params.Get(dynamicFieldPath[1:])
	case "getbalanceparams":
		return obj.GetBalanceParams.Get(dynamicFieldPath[1:])
	case "externalaccounts":
		return obj.ExternalAccounts.Get(dynamicFieldPath[1:])
	case "balanceupdateeventsubscriber":
		return obj.BalanceUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "processtierupdateeventsubscriber":
		return obj.ProcessTierUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "processkycupdateeventsubscriber":
		return obj.ProcessKycUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "processbalanceupdateeventsubscriber":
		return obj.ProcessBalanceUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "process":
		return obj.Process.Get(dynamicFieldPath[1:])
	case "savingscacheconfig":
		return obj.SavingsCacheConfig.Get(dynamicFieldPath[1:])
	case "transactionaggregateparams":
		return obj.TransactionAggregateParams.Get(dynamicFieldPath[1:])
	case "operationalstatusupdatesubscriber":
		return obj.OperationalStatusUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "thirdpartyaccountsharingsubscriber":
		return obj.ThirdPartyAccountSharingSubscriber.Get(dynamicFieldPath[1:])
	case "minkycuserparams":
		return obj.MinKycUserParams.Get(dynamicFieldPath[1:])
	case "signlegality":
		return obj.SignLegality.Get(dynamicFieldPath[1:])
	case "savingsaccountclosure":
		return obj.SavingsAccountClosure.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "percentagerolloutfordiffcheckbtwnbalanceapi":
		return obj.PercentageRollOutForDiffCheckBtwnBalanceApi.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isfeatureenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsFeatureEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsFeatureEnabled, nil
	case "timetowait":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TimeToWait\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TimeToWait, nil
	case "stickinessconstraintconfig":
		return obj.StickinessConstraintConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PercentageRollOutForDiffCheckBtwnBalanceApi", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *GetBalanceV1Params) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "usegetbalance":
		return obj.UseGetBalance.Get(dynamicFieldPath[1:])
	case "usegetbalancev1":
		return obj.UseGetBalanceV1.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for GetBalanceV1Params", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *GetBalanceParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "datafreshnessnearrealtime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DataFreshnessNearRealTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DataFreshnessNearRealTime, nil
	case "datafreshnessstale":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DataFreshnessStale\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DataFreshnessStale, nil
	case "lockleaseduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LockLeaseDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LockLeaseDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for GetBalanceParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ExternalAccounts) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablebalancerefundincidentcreation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableBalanceRefundIncidentCreation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableBalanceRefundIncidentCreation, nil
	case "issuecategoryid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IssueCategoryId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IssueCategoryId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ExternalAccounts", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SavingsCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "essentialscachettlfornonactiveaccounts":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EssentialsCacheTTLForNonActiveAccounts\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EssentialsCacheTTLForNonActiveAccounts, nil
	case "essentialscachettlforactiveaccounts":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EssentialsCacheTTLForActiveAccounts\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EssentialsCacheTTLForActiveAccounts, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SavingsCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TransactionAggregateParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enable, nil
	case "fromdate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FromDate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FromDate, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TransactionAggregateParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *MinKycUserParams) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minkyctotalcreditedlimitfornewuser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinKycTotalCreditedLimitForNewUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinKycTotalCreditedLimitForNewUser, nil
	case "minkycmaxsavingsbalancefornewuser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinKycMaxSavingsBalanceForNewUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinKycMaxSavingsBalanceForNewUser, nil
	case "minkyctotalcreditedlimit":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinKycTotalCreditedLimit\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinKycTotalCreditedLimit, nil
	case "minkycmaxsavingsbalance":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinKycMaxSavingsBalance\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinKycMaxSavingsBalance, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for MinKycUserParams", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SignLegality) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "signatureinreviewperiod":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SignatureInReviewPeriod\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SignatureInReviewPeriod, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SignLegality", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SavingsAccountClosure) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxpagesize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxPageSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxPageSize, nil
	case "expiryduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExpiryDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExpiryDuration, nil
	case "submissionwindow":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SubmissionWindow\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SubmissionWindow, nil
	case "cancelrequestnudgeid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CancelRequestNudgeId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CancelRequestNudgeId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SavingsAccountClosure", strings.Join(dynamicFieldPath, "."))
	}
}
