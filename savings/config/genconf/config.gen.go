// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	genconfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	config "github.com/epifi/gamma/savings/config"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinSalaryRequiredForUltimate                int32
	_AccountVerificationPollingAttempts          int64
	_BufferDaysToFetchStatementForClosureBalance int64
	// Enable or disable balance update event publishing
	_EnableBalanceUpdateEventPublishing uint32
	// ShouldTxnAggregateViaPinot flag to switch transaction aggregate call to pinot ( which is routed via pay rpc) OR via ES. If it is true it will get transaction aggregates from Pinot.
	_IsTxnAggregateCalculatedViaPinot uint32
	// VerifyBalanceAtWithLastOrderDataInDb will check if
	_VerifyBalanceAtWithLastOrderDataInDb    uint32
	_EnableSchemeChangeUpdate                uint32
	_DisableGetAccountBalanceV1              uint32
	_EnableAccountVerificationViaPayService  uint32
	_AccountVerificationPollingSleepDuration int64
	// list of actor ids to override sign availability with bank
	// overridden actors are considered to not have a signature even if bank api tells the signature is available with bank
	_OverrideSignAvailabilityWithBank      roarray.ROArray[string]
	_OverrideSignAvailabilityWithBankMutex *sync.RWMutex
	_SavingsCreationSubscriber             *gencfg.SqsSubscriber
	_CreateVPASubscriber                   *gencfg.SqsSubscriber
	_SavingsAccountPICreationSubscriber    *gencfg.SqsSubscriber
	_Flags                                 *Flags
	_SavingsCallbackSubscriber             *gencfg.SqsSubscriber
	_AccountStmtSubscriber                 *gencfg.SqsSubscriber
	_GetBalanceV1Params                    *GetBalanceV1Params
	_GetBalanceParams                      *GetBalanceParams
	_ExternalAccounts                      *ExternalAccounts
	_BalanceUpdateEventSubscriber          *gencfg.SqsSubscriber
	_ProcessTierUpdateEventSubscriber      *gencfg.SqsSubscriber
	_ProcessKycUpdateEventSubscriber       *gencfg.SqsSubscriber
	_ProcessBalanceUpdateEventSubscriber   *gencfg.SqsSubscriber
	_Process                               *gencfg.SqsSubscriber
	_SavingsCacheConfig                    *SavingsCacheConfig
	_TransactionAggregateParams            *TransactionAggregateParams
	_OperationalStatusUpdateSubscriber     *gencfg.SqsSubscriber
	_ThirdPartyAccountSharingSubscriber    *gencfg.SqsSubscriber
	_MinKycUserParams                      *MinKycUserParams
	_SignLegality                          *SignLegality
	_SavingsAccountClosure                 *SavingsAccountClosure
	_Application                           *config.Application
	_Server                                *config.Server
	_Logging                               *cfg.Logging
	_EpifiDb                               *cfg.DB
	_Secrets                               *cfg.Secrets
	_SavingsCreationPublisher              *cfg.SqsPublisher
	_AWS                                   *cfg.AWS
	_CreateVPAPublisher                    *cfg.SqsPublisher
	_SavingsAccountPICreationPublisher     *cfg.SqsPublisher
	_AccountStatePublisher                 *cfg.SnsPublisher
	_RudderStack                           *cfg.RudderStackBroker
	_BalanceEnqTimeout                     time.Duration
	_AccountStmtPublisher                  *cfg.SqsPublisher
	_StatementPreSignedURLExpiryTime       time.Duration
	_VendorSKUMapping                      map[string]map[string]string
	_AccountCreationEnquiryDelay           time.Duration
	_EventAfPurchasePublisher              *cfg.SqsPublisher
	_Tracing                               *cfg.Tracing
	_Profiling                             *cfg.Profiling
	_BalanceUpdateEventPublisher           *cfg.SnsPublisher
	_LowBalanceNotificationParams          *config.LowBalanceNotificationParams
	_RedisOptions                          *cfg.RedisOptions
	_AccountConstraintsConfig              *config.AccountConstraintsConfig
	_BalanceHistoryRedisOptions            *cfg.RedisOptions
	_SignExitUrl                           string
	_BalanceChangeEventPublisher           *cfg.SnsPublisher
	_ThirdPartyAccountSharingPublisher     *cfg.SqsPublisher
	_DrOperativeParticularString           string
}

func (obj *Config) MinSalaryRequiredForUltimate() int64 {
	return int64(atomic.LoadInt32(&obj._MinSalaryRequiredForUltimate))
}
func (obj *Config) AccountVerificationPollingAttempts() int {
	return int(atomic.LoadInt64(&obj._AccountVerificationPollingAttempts))
}
func (obj *Config) BufferDaysToFetchStatementForClosureBalance() int {
	return int(atomic.LoadInt64(&obj._BufferDaysToFetchStatementForClosureBalance))
}

// Enable or disable balance update event publishing
func (obj *Config) EnableBalanceUpdateEventPublishing() bool {
	if atomic.LoadUint32(&obj._EnableBalanceUpdateEventPublishing) == 0 {
		return false
	} else {
		return true
	}
}

// ShouldTxnAggregateViaPinot flag to switch transaction aggregate call to pinot ( which is routed via pay rpc) OR via ES. If it is true it will get transaction aggregates from Pinot.
func (obj *Config) IsTxnAggregateCalculatedViaPinot() bool {
	if atomic.LoadUint32(&obj._IsTxnAggregateCalculatedViaPinot) == 0 {
		return false
	} else {
		return true
	}
}

// VerifyBalanceAtWithLastOrderDataInDb will check if
func (obj *Config) VerifyBalanceAtWithLastOrderDataInDb() bool {
	if atomic.LoadUint32(&obj._VerifyBalanceAtWithLastOrderDataInDb) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableSchemeChangeUpdate() bool {
	if atomic.LoadUint32(&obj._EnableSchemeChangeUpdate) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) DisableGetAccountBalanceV1() bool {
	if atomic.LoadUint32(&obj._DisableGetAccountBalanceV1) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) EnableAccountVerificationViaPayService() bool {
	if atomic.LoadUint32(&obj._EnableAccountVerificationViaPayService) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) AccountVerificationPollingSleepDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._AccountVerificationPollingSleepDuration))
}

// list of actor ids to override sign availability with bank
// overridden actors are considered to not have a signature even if bank api tells the signature is available with bank
func (obj *Config) OverrideSignAvailabilityWithBank() roarray.ROArray[string] {
	obj._OverrideSignAvailabilityWithBankMutex.RLock()
	defer obj._OverrideSignAvailabilityWithBankMutex.RUnlock()
	return obj._OverrideSignAvailabilityWithBank
}
func (obj *Config) SavingsCreationSubscriber() *gencfg.SqsSubscriber {
	return obj._SavingsCreationSubscriber
}
func (obj *Config) CreateVPASubscriber() *gencfg.SqsSubscriber {
	return obj._CreateVPASubscriber
}
func (obj *Config) SavingsAccountPICreationSubscriber() *gencfg.SqsSubscriber {
	return obj._SavingsAccountPICreationSubscriber
}
func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) SavingsCallbackSubscriber() *gencfg.SqsSubscriber {
	return obj._SavingsCallbackSubscriber
}
func (obj *Config) AccountStmtSubscriber() *gencfg.SqsSubscriber {
	return obj._AccountStmtSubscriber
}
func (obj *Config) GetBalanceV1Params() *GetBalanceV1Params {
	return obj._GetBalanceV1Params
}
func (obj *Config) GetBalanceParams() *GetBalanceParams {
	return obj._GetBalanceParams
}
func (obj *Config) ExternalAccounts() *ExternalAccounts {
	return obj._ExternalAccounts
}
func (obj *Config) BalanceUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._BalanceUpdateEventSubscriber
}
func (obj *Config) ProcessTierUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessTierUpdateEventSubscriber
}
func (obj *Config) ProcessKycUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessKycUpdateEventSubscriber
}
func (obj *Config) ProcessBalanceUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessBalanceUpdateEventSubscriber
}
func (obj *Config) Process() *gencfg.SqsSubscriber {
	return obj._Process
}
func (obj *Config) SavingsCacheConfig() *SavingsCacheConfig {
	return obj._SavingsCacheConfig
}
func (obj *Config) TransactionAggregateParams() *TransactionAggregateParams {
	return obj._TransactionAggregateParams
}
func (obj *Config) OperationalStatusUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._OperationalStatusUpdateSubscriber
}
func (obj *Config) ThirdPartyAccountSharingSubscriber() *gencfg.SqsSubscriber {
	return obj._ThirdPartyAccountSharingSubscriber
}
func (obj *Config) MinKycUserParams() *MinKycUserParams {
	return obj._MinKycUserParams
}
func (obj *Config) SignLegality() *SignLegality {
	return obj._SignLegality
}
func (obj *Config) SavingsAccountClosure() *SavingsAccountClosure {
	return obj._SavingsAccountClosure
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) SavingsCreationPublisher() *cfg.SqsPublisher {
	return obj._SavingsCreationPublisher
}
func (obj *Config) AWS() *cfg.AWS {
	return obj._AWS
}
func (obj *Config) CreateVPAPublisher() *cfg.SqsPublisher {
	return obj._CreateVPAPublisher
}
func (obj *Config) SavingsAccountPICreationPublisher() *cfg.SqsPublisher {
	return obj._SavingsAccountPICreationPublisher
}
func (obj *Config) AccountStatePublisher() *cfg.SnsPublisher {
	return obj._AccountStatePublisher
}
func (obj *Config) RudderStack() *cfg.RudderStackBroker {
	return obj._RudderStack
}
func (obj *Config) BalanceEnqTimeout() time.Duration {
	return obj._BalanceEnqTimeout
}
func (obj *Config) AccountStmtPublisher() *cfg.SqsPublisher {
	return obj._AccountStmtPublisher
}
func (obj *Config) StatementPreSignedURLExpiryTime() time.Duration {
	return obj._StatementPreSignedURLExpiryTime
}
func (obj *Config) VendorSKUMapping() map[string]map[string]string {
	return obj._VendorSKUMapping
}
func (obj *Config) AccountCreationEnquiryDelay() time.Duration {
	return obj._AccountCreationEnquiryDelay
}
func (obj *Config) EventAfPurchasePublisher() *cfg.SqsPublisher {
	return obj._EventAfPurchasePublisher
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) BalanceUpdateEventPublisher() *cfg.SnsPublisher {
	return obj._BalanceUpdateEventPublisher
}
func (obj *Config) LowBalanceNotificationParams() *config.LowBalanceNotificationParams {
	return obj._LowBalanceNotificationParams
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) AccountConstraintsConfig() *config.AccountConstraintsConfig {
	return obj._AccountConstraintsConfig
}
func (obj *Config) BalanceHistoryRedisOptions() *cfg.RedisOptions {
	return obj._BalanceHistoryRedisOptions
}
func (obj *Config) SignExitUrl() string {
	return obj._SignExitUrl
}
func (obj *Config) BalanceChangeEventPublisher() *cfg.SnsPublisher {
	return obj._BalanceChangeEventPublisher
}
func (obj *Config) ThirdPartyAccountSharingPublisher() *cfg.SqsPublisher {
	return obj._ThirdPartyAccountSharingPublisher
}
func (obj *Config) DrOperativeParticularString() string {
	return obj._DrOperativeParticularString
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_PercentageRollOutForDiffCheckBtwnBalanceApi *PercentageRollOutForDiffCheckBtwnBalanceApi
	_TrimDebugMessageFromStatus                  bool
}

func (obj *Flags) PercentageRollOutForDiffCheckBtwnBalanceApi() *PercentageRollOutForDiffCheckBtwnBalanceApi {
	return obj._PercentageRollOutForDiffCheckBtwnBalanceApi
}
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	return obj._TrimDebugMessageFromStatus
}

type PercentageRollOutForDiffCheckBtwnBalanceApi struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsFeatureEnabled           uint32
	_TimeToWait                 int64
	_StickinessConstraintConfig *genconfig.StickinessConstraintConfig
}

func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) IsFeatureEnabled() bool {
	if atomic.LoadUint32(&obj._IsFeatureEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) TimeToWait() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._TimeToWait))
}
func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) StickinessConstraintConfig() *genconfig.StickinessConstraintConfig {
	return obj._StickinessConstraintConfig
}

type GetBalanceV1Params struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_UseGetBalance             *gencfg.FeatureReleaseConfig
	_UseGetBalanceV1           *gencfg.FeatureReleaseConfig
	_VgApiTimeout              time.Duration
	_DataFreshnessNearRealTime time.Duration
	_DataFreshnessStale        time.Duration
	_LockLeaseDuration         time.Duration
}

func (obj *GetBalanceV1Params) UseGetBalance() *gencfg.FeatureReleaseConfig {
	return obj._UseGetBalance
}
func (obj *GetBalanceV1Params) UseGetBalanceV1() *gencfg.FeatureReleaseConfig {
	return obj._UseGetBalanceV1
}
func (obj *GetBalanceV1Params) VgApiTimeout() time.Duration {
	return obj._VgApiTimeout
}
func (obj *GetBalanceV1Params) DataFreshnessNearRealTime() time.Duration {
	return obj._DataFreshnessNearRealTime
}
func (obj *GetBalanceV1Params) DataFreshnessStale() time.Duration {
	return obj._DataFreshnessStale
}
func (obj *GetBalanceV1Params) LockLeaseDuration() time.Duration {
	return obj._LockLeaseDuration
}

type GetBalanceParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Following are the Data Freshness parameters -
	// To be considered if client uses GetAccountBalanceV1Request_NEAR_REAL_TIME enum
	_DataFreshnessNearRealTime int64
	// To be considered if client uses GetAccountBalanceV1Request_STALE enum
	_DataFreshnessStale int64
	// To define the lease duration for which the lock is obtained
	_LockLeaseDuration int64
}

// Following are the Data Freshness parameters -
// To be considered if client uses GetAccountBalanceV1Request_NEAR_REAL_TIME enum
func (obj *GetBalanceParams) DataFreshnessNearRealTime() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DataFreshnessNearRealTime))
}

// To be considered if client uses GetAccountBalanceV1Request_STALE enum
func (obj *GetBalanceParams) DataFreshnessStale() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._DataFreshnessStale))
}

// To define the lease duration for which the lock is obtained
func (obj *GetBalanceParams) LockLeaseDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._LockLeaseDuration))
}

type ExternalAccounts struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// EnableBalanceRefundIncidentCreation to enable min kyc balance transfer incidents on watson.
	// Only applicable for external accounts verified in-app
	_EnableBalanceRefundIncidentCreation uint32
	// IssueCategoryId is for SubCategory_SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND
	_IssueCategoryId           string
	_IssueCategoryIdMutex      *sync.RWMutex
	_InhouseNameMatchThreshold float32
	_NameMatchFailLimit        int32
	_AccValidationFailLimit    int32
	_BAVAttemptCooldown        time.Duration
}

// EnableBalanceRefundIncidentCreation to enable min kyc balance transfer incidents on watson.
// Only applicable for external accounts verified in-app
func (obj *ExternalAccounts) EnableBalanceRefundIncidentCreation() bool {
	if atomic.LoadUint32(&obj._EnableBalanceRefundIncidentCreation) == 0 {
		return false
	} else {
		return true
	}
}

// IssueCategoryId is for SubCategory_SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND
func (obj *ExternalAccounts) IssueCategoryId() string {
	obj._IssueCategoryIdMutex.RLock()
	defer obj._IssueCategoryIdMutex.RUnlock()
	return obj._IssueCategoryId
}
func (obj *ExternalAccounts) InhouseNameMatchThreshold() float32 {
	return obj._InhouseNameMatchThreshold
}
func (obj *ExternalAccounts) NameMatchFailLimit() int32 {
	return obj._NameMatchFailLimit
}
func (obj *ExternalAccounts) AccValidationFailLimit() int32 {
	return obj._AccValidationFailLimit
}
func (obj *ExternalAccounts) BAVAttemptCooldown() time.Duration {
	return obj._BAVAttemptCooldown
}

type SavingsCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EssentialsCacheTTLForNonActiveAccounts int64
	_EssentialsCacheTTLForActiveAccounts    int64
	_IsCachingEnabled                       bool
	_SavingsIdPrefix                        string
	_SecondaryIdPrefix                      string
	_CacheTTl                               time.Duration
}

func (obj *SavingsCacheConfig) EssentialsCacheTTLForNonActiveAccounts() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._EssentialsCacheTTLForNonActiveAccounts))
}
func (obj *SavingsCacheConfig) EssentialsCacheTTLForActiveAccounts() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._EssentialsCacheTTLForActiveAccounts))
}
func (obj *SavingsCacheConfig) IsCachingEnabled() bool {
	return obj._IsCachingEnabled
}
func (obj *SavingsCacheConfig) SavingsIdPrefix() string {
	return obj._SavingsIdPrefix
}
func (obj *SavingsCacheConfig) SecondaryIdPrefix() string {
	return obj._SecondaryIdPrefix
}
func (obj *SavingsCacheConfig) CacheTTl() time.Duration {
	return obj._CacheTTl
}

type TransactionAggregateParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Enable uint32
	// We have seen in case of CSIS downtime bank back post txn
	// In this case we have to aggregate txn after CSIS time.
	_FromDate      string
	_FromDateMutex *sync.RWMutex
}

func (obj *TransactionAggregateParams) Enable() bool {
	if atomic.LoadUint32(&obj._Enable) == 0 {
		return false
	} else {
		return true
	}
}

// We have seen in case of CSIS downtime bank back post txn
// In this case we have to aggregate txn after CSIS time.
func (obj *TransactionAggregateParams) FromDate() string {
	obj._FromDateMutex.RLock()
	defer obj._FromDateMutex.RUnlock()
	return obj._FromDate
}

type MinKycUserParams struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinKycTotalCreditedLimitForNewUser int32
	_MinKycMaxSavingsBalanceForNewUser  int32
	_MinKycTotalCreditedLimit           int32
	_MinKycMaxSavingsBalance            int32
}

func (obj *MinKycUserParams) MinKycTotalCreditedLimitForNewUser() int64 {
	return int64(atomic.LoadInt32(&obj._MinKycTotalCreditedLimitForNewUser))
}
func (obj *MinKycUserParams) MinKycMaxSavingsBalanceForNewUser() int64 {
	return int64(atomic.LoadInt32(&obj._MinKycMaxSavingsBalanceForNewUser))
}
func (obj *MinKycUserParams) MinKycTotalCreditedLimit() int64 {
	return int64(atomic.LoadInt32(&obj._MinKycTotalCreditedLimit))
}
func (obj *MinKycUserParams) MinKycMaxSavingsBalance() int64 {
	return int64(atomic.LoadInt32(&obj._MinKycMaxSavingsBalance))
}

type SignLegality struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_SignatureInReviewPeriod int64
}

func (obj *SignLegality) SignatureInReviewPeriod() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._SignatureInReviewPeriod))
}

type SavingsAccountClosure struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaxPageSize uint32
	// closure request with no updates for ExpiryDuration is considered inactive and marked as expired
	_ExpiryDuration int64
	// time window to wait before sending the sa closure request to federal after the user has submitted the closure request
	_SubmissionWindow          int64
	_CancelRequestNudgeId      string
	_CancelRequestNudgeIdMutex *sync.RWMutex
}

func (obj *SavingsAccountClosure) MaxPageSize() uint32 {
	return uint32(atomic.LoadUint32(&obj._MaxPageSize))
}

// closure request with no updates for ExpiryDuration is considered inactive and marked as expired
func (obj *SavingsAccountClosure) ExpiryDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ExpiryDuration))
}

// time window to wait before sending the sa closure request to federal after the user has submitted the closure request
func (obj *SavingsAccountClosure) SubmissionWindow() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._SubmissionWindow))
}
func (obj *SavingsAccountClosure) CancelRequestNudgeId() string {
	obj._CancelRequestNudgeIdMutex.RLock()
	defer obj._CancelRequestNudgeIdMutex.RUnlock()
	return obj._CancelRequestNudgeId
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minsalaryrequiredforultimate"] = _obj.SetMinSalaryRequiredForUltimate
	_setters["accountverificationpollingattempts"] = _obj.SetAccountVerificationPollingAttempts
	_setters["bufferdaystofetchstatementforclosurebalance"] = _obj.SetBufferDaysToFetchStatementForClosureBalance
	_setters["enablebalanceupdateeventpublishing"] = _obj.SetEnableBalanceUpdateEventPublishing
	_setters["istxnaggregatecalculatedviapinot"] = _obj.SetIsTxnAggregateCalculatedViaPinot
	_setters["verifybalanceatwithlastorderdataindb"] = _obj.SetVerifyBalanceAtWithLastOrderDataInDb
	_setters["enableschemechangeupdate"] = _obj.SetEnableSchemeChangeUpdate
	_setters["disablegetaccountbalancev1"] = _obj.SetDisableGetAccountBalanceV1
	_setters["enableaccountverificationviapayservice"] = _obj.SetEnableAccountVerificationViaPayService
	_setters["accountverificationpollingsleepduration"] = _obj.SetAccountVerificationPollingSleepDuration
	_setters["overridesignavailabilitywithbank"] = _obj.SetOverrideSignAvailabilityWithBank
	_obj._OverrideSignAvailabilityWithBankMutex = &sync.RWMutex{}
	_SavingsCreationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsCreationSubscriber = _SavingsCreationSubscriber
	helper.AddFieldSetters("savingscreationsubscriber", _fieldSetters, _setters)
	_CreateVPASubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreateVPASubscriber = _CreateVPASubscriber
	helper.AddFieldSetters("createvpasubscriber", _fieldSetters, _setters)
	_SavingsAccountPICreationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsAccountPICreationSubscriber = _SavingsAccountPICreationSubscriber
	helper.AddFieldSetters("savingsaccountpicreationsubscriber", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_SavingsCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsCallbackSubscriber = _SavingsCallbackSubscriber
	helper.AddFieldSetters("savingscallbacksubscriber", _fieldSetters, _setters)
	_AccountStmtSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AccountStmtSubscriber = _AccountStmtSubscriber
	helper.AddFieldSetters("accountstmtsubscriber", _fieldSetters, _setters)
	_GetBalanceV1Params, _fieldSetters := NewGetBalanceV1Params()
	_obj._GetBalanceV1Params = _GetBalanceV1Params
	helper.AddFieldSetters("getbalancev1params", _fieldSetters, _setters)
	_GetBalanceParams, _fieldSetters := NewGetBalanceParams()
	_obj._GetBalanceParams = _GetBalanceParams
	helper.AddFieldSetters("getbalanceparams", _fieldSetters, _setters)
	_ExternalAccounts, _fieldSetters := NewExternalAccounts()
	_obj._ExternalAccounts = _ExternalAccounts
	helper.AddFieldSetters("externalaccounts", _fieldSetters, _setters)
	_BalanceUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._BalanceUpdateEventSubscriber = _BalanceUpdateEventSubscriber
	helper.AddFieldSetters("balanceupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessTierUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessTierUpdateEventSubscriber = _ProcessTierUpdateEventSubscriber
	helper.AddFieldSetters("processtierupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessKycUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessKycUpdateEventSubscriber = _ProcessKycUpdateEventSubscriber
	helper.AddFieldSetters("processkycupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessBalanceUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessBalanceUpdateEventSubscriber = _ProcessBalanceUpdateEventSubscriber
	helper.AddFieldSetters("processbalanceupdateeventsubscriber", _fieldSetters, _setters)
	_Process, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._Process = _Process
	helper.AddFieldSetters("process", _fieldSetters, _setters)
	_SavingsCacheConfig, _fieldSetters := NewSavingsCacheConfig()
	_obj._SavingsCacheConfig = _SavingsCacheConfig
	helper.AddFieldSetters("savingscacheconfig", _fieldSetters, _setters)
	_TransactionAggregateParams, _fieldSetters := NewTransactionAggregateParams()
	_obj._TransactionAggregateParams = _TransactionAggregateParams
	helper.AddFieldSetters("transactionaggregateparams", _fieldSetters, _setters)
	_OperationalStatusUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OperationalStatusUpdateSubscriber = _OperationalStatusUpdateSubscriber
	helper.AddFieldSetters("operationalstatusupdatesubscriber", _fieldSetters, _setters)
	_ThirdPartyAccountSharingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ThirdPartyAccountSharingSubscriber = _ThirdPartyAccountSharingSubscriber
	helper.AddFieldSetters("thirdpartyaccountsharingsubscriber", _fieldSetters, _setters)
	_MinKycUserParams, _fieldSetters := NewMinKycUserParams()
	_obj._MinKycUserParams = _MinKycUserParams
	helper.AddFieldSetters("minkycuserparams", _fieldSetters, _setters)
	_SignLegality, _fieldSetters := NewSignLegality()
	_obj._SignLegality = _SignLegality
	helper.AddFieldSetters("signlegality", _fieldSetters, _setters)
	_SavingsAccountClosure, _fieldSetters := NewSavingsAccountClosure()
	_obj._SavingsAccountClosure = _SavingsAccountClosure
	helper.AddFieldSetters("savingsaccountclosure", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minsalaryrequiredforultimate"] = _obj.SetMinSalaryRequiredForUltimate
	_setters["accountverificationpollingattempts"] = _obj.SetAccountVerificationPollingAttempts
	_setters["bufferdaystofetchstatementforclosurebalance"] = _obj.SetBufferDaysToFetchStatementForClosureBalance
	_setters["enablebalanceupdateeventpublishing"] = _obj.SetEnableBalanceUpdateEventPublishing
	_setters["istxnaggregatecalculatedviapinot"] = _obj.SetIsTxnAggregateCalculatedViaPinot
	_setters["verifybalanceatwithlastorderdataindb"] = _obj.SetVerifyBalanceAtWithLastOrderDataInDb
	_setters["enableschemechangeupdate"] = _obj.SetEnableSchemeChangeUpdate
	_setters["disablegetaccountbalancev1"] = _obj.SetDisableGetAccountBalanceV1
	_setters["enableaccountverificationviapayservice"] = _obj.SetEnableAccountVerificationViaPayService
	_setters["accountverificationpollingsleepduration"] = _obj.SetAccountVerificationPollingSleepDuration
	_setters["overridesignavailabilitywithbank"] = _obj.SetOverrideSignAvailabilityWithBank
	_obj._OverrideSignAvailabilityWithBankMutex = &sync.RWMutex{}
	_SavingsCreationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsCreationSubscriber = _SavingsCreationSubscriber
	helper.AddFieldSetters("savingscreationsubscriber", _fieldSetters, _setters)
	_CreateVPASubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreateVPASubscriber = _CreateVPASubscriber
	helper.AddFieldSetters("createvpasubscriber", _fieldSetters, _setters)
	_SavingsAccountPICreationSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsAccountPICreationSubscriber = _SavingsAccountPICreationSubscriber
	helper.AddFieldSetters("savingsaccountpicreationsubscriber", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_SavingsCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsCallbackSubscriber = _SavingsCallbackSubscriber
	helper.AddFieldSetters("savingscallbacksubscriber", _fieldSetters, _setters)
	_AccountStmtSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._AccountStmtSubscriber = _AccountStmtSubscriber
	helper.AddFieldSetters("accountstmtsubscriber", _fieldSetters, _setters)
	_GetBalanceV1Params, _fieldSetters := NewGetBalanceV1Params()
	_obj._GetBalanceV1Params = _GetBalanceV1Params
	helper.AddFieldSetters("getbalancev1params", _fieldSetters, _setters)
	_GetBalanceParams, _fieldSetters := NewGetBalanceParams()
	_obj._GetBalanceParams = _GetBalanceParams
	helper.AddFieldSetters("getbalanceparams", _fieldSetters, _setters)
	_ExternalAccounts, _fieldSetters := NewExternalAccounts()
	_obj._ExternalAccounts = _ExternalAccounts
	helper.AddFieldSetters("externalaccounts", _fieldSetters, _setters)
	_BalanceUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._BalanceUpdateEventSubscriber = _BalanceUpdateEventSubscriber
	helper.AddFieldSetters("balanceupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessTierUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessTierUpdateEventSubscriber = _ProcessTierUpdateEventSubscriber
	helper.AddFieldSetters("processtierupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessKycUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessKycUpdateEventSubscriber = _ProcessKycUpdateEventSubscriber
	helper.AddFieldSetters("processkycupdateeventsubscriber", _fieldSetters, _setters)
	_ProcessBalanceUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessBalanceUpdateEventSubscriber = _ProcessBalanceUpdateEventSubscriber
	helper.AddFieldSetters("processbalanceupdateeventsubscriber", _fieldSetters, _setters)
	_Process, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._Process = _Process
	helper.AddFieldSetters("process", _fieldSetters, _setters)
	_SavingsCacheConfig, _fieldSetters := NewSavingsCacheConfig()
	_obj._SavingsCacheConfig = _SavingsCacheConfig
	helper.AddFieldSetters("savingscacheconfig", _fieldSetters, _setters)
	_TransactionAggregateParams, _fieldSetters := NewTransactionAggregateParams()
	_obj._TransactionAggregateParams = _TransactionAggregateParams
	helper.AddFieldSetters("transactionaggregateparams", _fieldSetters, _setters)
	_OperationalStatusUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OperationalStatusUpdateSubscriber = _OperationalStatusUpdateSubscriber
	helper.AddFieldSetters("operationalstatusupdatesubscriber", _fieldSetters, _setters)
	_ThirdPartyAccountSharingSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ThirdPartyAccountSharingSubscriber = _ThirdPartyAccountSharingSubscriber
	helper.AddFieldSetters("thirdpartyaccountsharingsubscriber", _fieldSetters, _setters)
	_MinKycUserParams, _fieldSetters := NewMinKycUserParams()
	_obj._MinKycUserParams = _MinKycUserParams
	helper.AddFieldSetters("minkycuserparams", _fieldSetters, _setters)
	_SignLegality, _fieldSetters := NewSignLegality()
	_obj._SignLegality = _SignLegality
	helper.AddFieldSetters("signlegality", _fieldSetters, _setters)
	_SavingsAccountClosure, _fieldSetters := NewSavingsAccountClosure()
	_obj._SavingsAccountClosure = _SavingsAccountClosure
	helper.AddFieldSetters("savingsaccountclosure", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minsalaryrequiredforultimate":
		return obj.SetMinSalaryRequiredForUltimate(v.MinSalaryRequiredForUltimate, true, nil)
	case "accountverificationpollingattempts":
		return obj.SetAccountVerificationPollingAttempts(v.AccountVerificationPollingAttempts, true, nil)
	case "bufferdaystofetchstatementforclosurebalance":
		return obj.SetBufferDaysToFetchStatementForClosureBalance(v.BufferDaysToFetchStatementForClosureBalance, true, nil)
	case "enablebalanceupdateeventpublishing":
		return obj.SetEnableBalanceUpdateEventPublishing(v.EnableBalanceUpdateEventPublishing, true, nil)
	case "istxnaggregatecalculatedviapinot":
		return obj.SetIsTxnAggregateCalculatedViaPinot(v.IsTxnAggregateCalculatedViaPinot, true, nil)
	case "verifybalanceatwithlastorderdataindb":
		return obj.SetVerifyBalanceAtWithLastOrderDataInDb(v.VerifyBalanceAtWithLastOrderDataInDb, true, nil)
	case "enableschemechangeupdate":
		return obj.SetEnableSchemeChangeUpdate(v.EnableSchemeChangeUpdate, true, nil)
	case "disablegetaccountbalancev1":
		return obj.SetDisableGetAccountBalanceV1(v.DisableGetAccountBalanceV1, true, nil)
	case "enableaccountverificationviapayservice":
		return obj.SetEnableAccountVerificationViaPayService(v.EnableAccountVerificationViaPayService, true, nil)
	case "accountverificationpollingsleepduration":
		return obj.SetAccountVerificationPollingSleepDuration(v.AccountVerificationPollingSleepDuration, true, nil)
	case "overridesignavailabilitywithbank":
		return obj.SetOverrideSignAvailabilityWithBank(v.OverrideSignAvailabilityWithBank, true, path)
	case "savingscreationsubscriber":
		return obj._SavingsCreationSubscriber.Set(v.SavingsCreationSubscriber, true, path)
	case "createvpasubscriber":
		return obj._CreateVPASubscriber.Set(v.CreateVPASubscriber, true, path)
	case "savingsaccountpicreationsubscriber":
		return obj._SavingsAccountPICreationSubscriber.Set(v.SavingsAccountPICreationSubscriber, true, path)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "savingscallbacksubscriber":
		return obj._SavingsCallbackSubscriber.Set(v.SavingsCallbackSubscriber, true, path)
	case "accountstmtsubscriber":
		return obj._AccountStmtSubscriber.Set(v.AccountStmtSubscriber, true, path)
	case "getbalancev1params":
		return obj._GetBalanceV1Params.Set(v.GetBalanceV1Params, true, path)
	case "getbalanceparams":
		return obj._GetBalanceParams.Set(v.GetBalanceParams, true, path)
	case "externalaccounts":
		return obj._ExternalAccounts.Set(v.ExternalAccounts, true, path)
	case "balanceupdateeventsubscriber":
		return obj._BalanceUpdateEventSubscriber.Set(v.BalanceUpdateEventSubscriber, true, path)
	case "processtierupdateeventsubscriber":
		return obj._ProcessTierUpdateEventSubscriber.Set(v.ProcessTierUpdateEventSubscriber, true, path)
	case "processkycupdateeventsubscriber":
		return obj._ProcessKycUpdateEventSubscriber.Set(v.ProcessKycUpdateEventSubscriber, true, path)
	case "processbalanceupdateeventsubscriber":
		return obj._ProcessBalanceUpdateEventSubscriber.Set(v.ProcessBalanceUpdateEventSubscriber, true, path)
	case "process":
		return obj._Process.Set(v.Process, true, path)
	case "savingscacheconfig":
		return obj._SavingsCacheConfig.Set(v.SavingsCacheConfig, true, path)
	case "transactionaggregateparams":
		return obj._TransactionAggregateParams.Set(v.TransactionAggregateParams, true, path)
	case "operationalstatusupdatesubscriber":
		return obj._OperationalStatusUpdateSubscriber.Set(v.OperationalStatusUpdateSubscriber, true, path)
	case "thirdpartyaccountsharingsubscriber":
		return obj._ThirdPartyAccountSharingSubscriber.Set(v.ThirdPartyAccountSharingSubscriber, true, path)
	case "minkycuserparams":
		return obj._MinKycUserParams.Set(v.MinKycUserParams, true, path)
	case "signlegality":
		return obj._SignLegality.Set(v.SignLegality, true, path)
	case "savingsaccountclosure":
		return obj._SavingsAccountClosure.Set(v.SavingsAccountClosure, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetMinSalaryRequiredForUltimate(v.MinSalaryRequiredForUltimate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAccountVerificationPollingAttempts(v.AccountVerificationPollingAttempts, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBufferDaysToFetchStatementForClosureBalance(v.BufferDaysToFetchStatementForClosureBalance, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableBalanceUpdateEventPublishing(v.EnableBalanceUpdateEventPublishing, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsTxnAggregateCalculatedViaPinot(v.IsTxnAggregateCalculatedViaPinot, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVerifyBalanceAtWithLastOrderDataInDb(v.VerifyBalanceAtWithLastOrderDataInDb, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableSchemeChangeUpdate(v.EnableSchemeChangeUpdate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableGetAccountBalanceV1(v.DisableGetAccountBalanceV1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableAccountVerificationViaPayService(v.EnableAccountVerificationViaPayService, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAccountVerificationPollingSleepDuration(v.AccountVerificationPollingSleepDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOverrideSignAvailabilityWithBank(v.OverrideSignAvailabilityWithBank, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SavingsCreationSubscriber.Set(v.SavingsCreationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreateVPASubscriber.Set(v.CreateVPASubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SavingsAccountPICreationSubscriber.Set(v.SavingsAccountPICreationSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SavingsCallbackSubscriber.Set(v.SavingsCallbackSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AccountStmtSubscriber.Set(v.AccountStmtSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._GetBalanceV1Params.Set(v.GetBalanceV1Params, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._GetBalanceParams.Set(v.GetBalanceParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ExternalAccounts.Set(v.ExternalAccounts, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BalanceUpdateEventSubscriber.Set(v.BalanceUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessTierUpdateEventSubscriber.Set(v.ProcessTierUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessKycUpdateEventSubscriber.Set(v.ProcessKycUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessBalanceUpdateEventSubscriber.Set(v.ProcessBalanceUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Process.Set(v.Process, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SavingsCacheConfig.Set(v.SavingsCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TransactionAggregateParams.Set(v.TransactionAggregateParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OperationalStatusUpdateSubscriber.Set(v.OperationalStatusUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ThirdPartyAccountSharingSubscriber.Set(v.ThirdPartyAccountSharingSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MinKycUserParams.Set(v.MinKycUserParams, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SignLegality.Set(v.SignLegality, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SavingsAccountClosure.Set(v.SavingsAccountClosure, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._EpifiDb = v.EpifiDb
	obj._Secrets = v.Secrets
	obj._SavingsCreationPublisher = v.SavingsCreationPublisher
	obj._AWS = v.AWS
	obj._CreateVPAPublisher = v.CreateVPAPublisher
	obj._SavingsAccountPICreationPublisher = v.SavingsAccountPICreationPublisher
	obj._AccountStatePublisher = v.AccountStatePublisher
	obj._RudderStack = v.RudderStack
	obj._BalanceEnqTimeout = v.BalanceEnqTimeout
	obj._AccountStmtPublisher = v.AccountStmtPublisher
	obj._StatementPreSignedURLExpiryTime = v.StatementPreSignedURLExpiryTime
	obj._VendorSKUMapping = v.VendorSKUMapping
	obj._AccountCreationEnquiryDelay = v.AccountCreationEnquiryDelay
	obj._EventAfPurchasePublisher = v.EventAfPurchasePublisher
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._BalanceUpdateEventPublisher = v.BalanceUpdateEventPublisher
	obj._LowBalanceNotificationParams = v.LowBalanceNotificationParams
	obj._RedisOptions = v.RedisOptions
	obj._AccountConstraintsConfig = v.AccountConstraintsConfig
	obj._BalanceHistoryRedisOptions = v.BalanceHistoryRedisOptions
	obj._SignExitUrl = v.SignExitUrl
	obj._BalanceChangeEventPublisher = v.BalanceChangeEventPublisher
	obj._ThirdPartyAccountSharingPublisher = v.ThirdPartyAccountSharingPublisher
	obj._DrOperativeParticularString = v.DrOperativeParticularString
	return nil
}

func (obj *Config) SetMinSalaryRequiredForUltimate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MinSalaryRequiredForUltimate", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinSalaryRequiredForUltimate, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinSalaryRequiredForUltimate")
	}
	return nil
}
func (obj *Config) SetAccountVerificationPollingAttempts(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.AccountVerificationPollingAttempts", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AccountVerificationPollingAttempts, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AccountVerificationPollingAttempts")
	}
	return nil
}
func (obj *Config) SetBufferDaysToFetchStatementForClosureBalance(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.BufferDaysToFetchStatementForClosureBalance", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._BufferDaysToFetchStatementForClosureBalance, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BufferDaysToFetchStatementForClosureBalance")
	}
	return nil
}
func (obj *Config) SetEnableBalanceUpdateEventPublishing(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableBalanceUpdateEventPublishing", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableBalanceUpdateEventPublishing, 1)
	} else {
		atomic.StoreUint32(&obj._EnableBalanceUpdateEventPublishing, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableBalanceUpdateEventPublishing")
	}
	return nil
}
func (obj *Config) SetIsTxnAggregateCalculatedViaPinot(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.IsTxnAggregateCalculatedViaPinot", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsTxnAggregateCalculatedViaPinot, 1)
	} else {
		atomic.StoreUint32(&obj._IsTxnAggregateCalculatedViaPinot, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsTxnAggregateCalculatedViaPinot")
	}
	return nil
}
func (obj *Config) SetVerifyBalanceAtWithLastOrderDataInDb(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.VerifyBalanceAtWithLastOrderDataInDb", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._VerifyBalanceAtWithLastOrderDataInDb, 1)
	} else {
		atomic.StoreUint32(&obj._VerifyBalanceAtWithLastOrderDataInDb, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "VerifyBalanceAtWithLastOrderDataInDb")
	}
	return nil
}
func (obj *Config) SetEnableSchemeChangeUpdate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableSchemeChangeUpdate", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableSchemeChangeUpdate, 1)
	} else {
		atomic.StoreUint32(&obj._EnableSchemeChangeUpdate, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableSchemeChangeUpdate")
	}
	return nil
}
func (obj *Config) SetDisableGetAccountBalanceV1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DisableGetAccountBalanceV1", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableGetAccountBalanceV1, 1)
	} else {
		atomic.StoreUint32(&obj._DisableGetAccountBalanceV1, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableGetAccountBalanceV1")
	}
	return nil
}
func (obj *Config) SetEnableAccountVerificationViaPayService(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.EnableAccountVerificationViaPayService", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableAccountVerificationViaPayService, 1)
	} else {
		atomic.StoreUint32(&obj._EnableAccountVerificationViaPayService, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableAccountVerificationViaPayService")
	}
	return nil
}
func (obj *Config) SetAccountVerificationPollingSleepDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.AccountVerificationPollingSleepDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._AccountVerificationPollingSleepDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AccountVerificationPollingSleepDuration")
	}
	return nil
}
func (obj *Config) SetOverrideSignAvailabilityWithBank(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.OverrideSignAvailabilityWithBank", reflect.TypeOf(val))
	}
	obj._OverrideSignAvailabilityWithBankMutex.Lock()
	defer obj._OverrideSignAvailabilityWithBankMutex.Unlock()
	obj._OverrideSignAvailabilityWithBank = roarray.New[string](v)
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_PercentageRollOutForDiffCheckBtwnBalanceApi, _fieldSetters := NewPercentageRollOutForDiffCheckBtwnBalanceApi()
	_obj._PercentageRollOutForDiffCheckBtwnBalanceApi = _PercentageRollOutForDiffCheckBtwnBalanceApi
	helper.AddFieldSetters("percentagerolloutfordiffcheckbtwnbalanceapi", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "percentagerolloutfordiffcheckbtwnbalanceapi":
		return obj._PercentageRollOutForDiffCheckBtwnBalanceApi.Set(v.PercentageRollOutForDiffCheckBtwnBalanceApi, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj._PercentageRollOutForDiffCheckBtwnBalanceApi.Set(v.PercentageRollOutForDiffCheckBtwnBalanceApi, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	obj._TrimDebugMessageFromStatus = v.TrimDebugMessageFromStatus
	return nil
}

func NewPercentageRollOutForDiffCheckBtwnBalanceApi() (_obj *PercentageRollOutForDiffCheckBtwnBalanceApi, _setters map[string]dynconf.SetFunc) {
	_obj = &PercentageRollOutForDiffCheckBtwnBalanceApi{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isfeatureenabled"] = _obj.SetIsFeatureEnabled
	_setters["timetowait"] = _obj.SetTimeToWait
	_StickinessConstraintConfig, _fieldSetters := genconfig.NewStickinessConstraintConfig()
	_obj._StickinessConstraintConfig = _StickinessConstraintConfig
	helper.AddFieldSetters("stickinessconstraintconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) Init() {
	newObj, _ := NewPercentageRollOutForDiffCheckBtwnBalanceApi()
	*obj = *newObj
}

func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PercentageRollOutForDiffCheckBtwnBalanceApi)
	if !ok {
		return fmt.Errorf("invalid data type %v *PercentageRollOutForDiffCheckBtwnBalanceApi", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) setDynamicField(v *config.PercentageRollOutForDiffCheckBtwnBalanceApi, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isfeatureenabled":
		return obj.SetIsFeatureEnabled(v.IsFeatureEnabled, true, nil)
	case "timetowait":
		return obj.SetTimeToWait(v.TimeToWait, true, nil)
	case "stickinessconstraintconfig":
		return obj._StickinessConstraintConfig.Set(v.StickinessConstraintConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) setDynamicFields(v *config.PercentageRollOutForDiffCheckBtwnBalanceApi, dynamic bool, path []string) (err error) {

	err = obj.SetIsFeatureEnabled(v.IsFeatureEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTimeToWait(v.TimeToWait, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._StickinessConstraintConfig.Set(v.StickinessConstraintConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) setStaticFields(v *config.PercentageRollOutForDiffCheckBtwnBalanceApi) error {

	return nil
}

func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) SetIsFeatureEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *PercentageRollOutForDiffCheckBtwnBalanceApi.IsFeatureEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsFeatureEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsFeatureEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsFeatureEnabled")
	}
	return nil
}
func (obj *PercentageRollOutForDiffCheckBtwnBalanceApi) SetTimeToWait(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *PercentageRollOutForDiffCheckBtwnBalanceApi.TimeToWait", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._TimeToWait, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "TimeToWait")
	}
	return nil
}

func NewGetBalanceV1Params() (_obj *GetBalanceV1Params, _setters map[string]dynconf.SetFunc) {
	_obj = &GetBalanceV1Params{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_UseGetBalance, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._UseGetBalance = _UseGetBalance
	helper.AddFieldSetters("usegetbalance", _fieldSetters, _setters)
	_UseGetBalanceV1, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._UseGetBalanceV1 = _UseGetBalanceV1
	helper.AddFieldSetters("usegetbalancev1", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *GetBalanceV1Params) Init() {
	newObj, _ := NewGetBalanceV1Params()
	*obj = *newObj
}

func (obj *GetBalanceV1Params) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *GetBalanceV1Params) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.GetBalanceV1Params)
	if !ok {
		return fmt.Errorf("invalid data type %v *GetBalanceV1Params", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *GetBalanceV1Params) setDynamicField(v *config.GetBalanceV1Params, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "usegetbalance":
		return obj._UseGetBalance.Set(v.UseGetBalance, true, path)
	case "usegetbalancev1":
		return obj._UseGetBalanceV1.Set(v.UseGetBalanceV1, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *GetBalanceV1Params) setDynamicFields(v *config.GetBalanceV1Params, dynamic bool, path []string) (err error) {

	err = obj._UseGetBalance.Set(v.UseGetBalance, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UseGetBalanceV1.Set(v.UseGetBalanceV1, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *GetBalanceV1Params) setStaticFields(v *config.GetBalanceV1Params) error {

	obj._VgApiTimeout = v.VgApiTimeout
	obj._DataFreshnessNearRealTime = v.DataFreshnessNearRealTime
	obj._DataFreshnessStale = v.DataFreshnessStale
	obj._LockLeaseDuration = v.LockLeaseDuration
	return nil
}

func NewGetBalanceParams() (_obj *GetBalanceParams, _setters map[string]dynconf.SetFunc) {
	_obj = &GetBalanceParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["datafreshnessnearrealtime"] = _obj.SetDataFreshnessNearRealTime
	_setters["datafreshnessstale"] = _obj.SetDataFreshnessStale
	_setters["lockleaseduration"] = _obj.SetLockLeaseDuration
	return _obj, _setters
}

func (obj *GetBalanceParams) Init() {
	newObj, _ := NewGetBalanceParams()
	*obj = *newObj
}

func (obj *GetBalanceParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *GetBalanceParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.GetBalanceParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *GetBalanceParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *GetBalanceParams) setDynamicField(v *config.GetBalanceParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "datafreshnessnearrealtime":
		return obj.SetDataFreshnessNearRealTime(v.DataFreshnessNearRealTime, true, nil)
	case "datafreshnessstale":
		return obj.SetDataFreshnessStale(v.DataFreshnessStale, true, nil)
	case "lockleaseduration":
		return obj.SetLockLeaseDuration(v.LockLeaseDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *GetBalanceParams) setDynamicFields(v *config.GetBalanceParams, dynamic bool, path []string) (err error) {

	err = obj.SetDataFreshnessNearRealTime(v.DataFreshnessNearRealTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDataFreshnessStale(v.DataFreshnessStale, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLockLeaseDuration(v.LockLeaseDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *GetBalanceParams) setStaticFields(v *config.GetBalanceParams) error {

	return nil
}

func (obj *GetBalanceParams) SetDataFreshnessNearRealTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *GetBalanceParams.DataFreshnessNearRealTime", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DataFreshnessNearRealTime, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DataFreshnessNearRealTime")
	}
	return nil
}
func (obj *GetBalanceParams) SetDataFreshnessStale(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *GetBalanceParams.DataFreshnessStale", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._DataFreshnessStale, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "DataFreshnessStale")
	}
	return nil
}
func (obj *GetBalanceParams) SetLockLeaseDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *GetBalanceParams.LockLeaseDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LockLeaseDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LockLeaseDuration")
	}
	return nil
}

func NewExternalAccounts() (_obj *ExternalAccounts, _setters map[string]dynconf.SetFunc) {
	_obj = &ExternalAccounts{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablebalancerefundincidentcreation"] = _obj.SetEnableBalanceRefundIncidentCreation
	_setters["issuecategoryid"] = _obj.SetIssueCategoryId
	_obj._IssueCategoryIdMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *ExternalAccounts) Init() {
	newObj, _ := NewExternalAccounts()
	*obj = *newObj
}

func (obj *ExternalAccounts) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ExternalAccounts) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ExternalAccounts)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExternalAccounts", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ExternalAccounts) setDynamicField(v *config.ExternalAccounts, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablebalancerefundincidentcreation":
		return obj.SetEnableBalanceRefundIncidentCreation(v.EnableBalanceRefundIncidentCreation, true, nil)
	case "issuecategoryid":
		return obj.SetIssueCategoryId(v.IssueCategoryId, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ExternalAccounts) setDynamicFields(v *config.ExternalAccounts, dynamic bool, path []string) (err error) {

	err = obj.SetEnableBalanceRefundIncidentCreation(v.EnableBalanceRefundIncidentCreation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIssueCategoryId(v.IssueCategoryId, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ExternalAccounts) setStaticFields(v *config.ExternalAccounts) error {

	obj._InhouseNameMatchThreshold = v.InhouseNameMatchThreshold
	obj._NameMatchFailLimit = v.NameMatchFailLimit
	obj._AccValidationFailLimit = v.AccValidationFailLimit
	obj._BAVAttemptCooldown = v.BAVAttemptCooldown
	return nil
}

func (obj *ExternalAccounts) SetEnableBalanceRefundIncidentCreation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExternalAccounts.EnableBalanceRefundIncidentCreation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableBalanceRefundIncidentCreation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableBalanceRefundIncidentCreation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableBalanceRefundIncidentCreation")
	}
	return nil
}
func (obj *ExternalAccounts) SetIssueCategoryId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *ExternalAccounts.IssueCategoryId", reflect.TypeOf(val))
	}
	obj._IssueCategoryIdMutex.Lock()
	defer obj._IssueCategoryIdMutex.Unlock()
	obj._IssueCategoryId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "IssueCategoryId")
	}
	return nil
}

func NewSavingsCacheConfig() (_obj *SavingsCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SavingsCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["essentialscachettlfornonactiveaccounts"] = _obj.SetEssentialsCacheTTLForNonActiveAccounts
	_setters["essentialscachettlforactiveaccounts"] = _obj.SetEssentialsCacheTTLForActiveAccounts
	return _obj, _setters
}

func (obj *SavingsCacheConfig) Init() {
	newObj, _ := NewSavingsCacheConfig()
	*obj = *newObj
}

func (obj *SavingsCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SavingsCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SavingsCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SavingsCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SavingsCacheConfig) setDynamicField(v *config.SavingsCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "essentialscachettlfornonactiveaccounts":
		return obj.SetEssentialsCacheTTLForNonActiveAccounts(v.EssentialsCacheTTLForNonActiveAccounts, true, nil)
	case "essentialscachettlforactiveaccounts":
		return obj.SetEssentialsCacheTTLForActiveAccounts(v.EssentialsCacheTTLForActiveAccounts, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SavingsCacheConfig) setDynamicFields(v *config.SavingsCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEssentialsCacheTTLForNonActiveAccounts(v.EssentialsCacheTTLForNonActiveAccounts, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEssentialsCacheTTLForActiveAccounts(v.EssentialsCacheTTLForActiveAccounts, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SavingsCacheConfig) setStaticFields(v *config.SavingsCacheConfig) error {

	obj._IsCachingEnabled = v.IsCachingEnabled
	obj._SavingsIdPrefix = v.SavingsIdPrefix
	obj._SecondaryIdPrefix = v.SecondaryIdPrefix
	obj._CacheTTl = v.CacheTTl
	return nil
}

func (obj *SavingsCacheConfig) SetEssentialsCacheTTLForNonActiveAccounts(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SavingsCacheConfig.EssentialsCacheTTLForNonActiveAccounts", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._EssentialsCacheTTLForNonActiveAccounts, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "EssentialsCacheTTLForNonActiveAccounts")
	}
	return nil
}
func (obj *SavingsCacheConfig) SetEssentialsCacheTTLForActiveAccounts(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SavingsCacheConfig.EssentialsCacheTTLForActiveAccounts", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._EssentialsCacheTTLForActiveAccounts, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "EssentialsCacheTTLForActiveAccounts")
	}
	return nil
}

func NewTransactionAggregateParams() (_obj *TransactionAggregateParams, _setters map[string]dynconf.SetFunc) {
	_obj = &TransactionAggregateParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enable"] = _obj.SetEnable
	_setters["fromdate"] = _obj.SetFromDate
	_obj._FromDateMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *TransactionAggregateParams) Init() {
	newObj, _ := NewTransactionAggregateParams()
	*obj = *newObj
}

func (obj *TransactionAggregateParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TransactionAggregateParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.TransactionAggregateParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionAggregateParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TransactionAggregateParams) setDynamicField(v *config.TransactionAggregateParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enable":
		return obj.SetEnable(v.Enable, true, nil)
	case "fromdate":
		return obj.SetFromDate(v.FromDate, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TransactionAggregateParams) setDynamicFields(v *config.TransactionAggregateParams, dynamic bool, path []string) (err error) {

	err = obj.SetEnable(v.Enable, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFromDate(v.FromDate, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TransactionAggregateParams) setStaticFields(v *config.TransactionAggregateParams) error {

	return nil
}

func (obj *TransactionAggregateParams) SetEnable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionAggregateParams.Enable", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Enable, 1)
	} else {
		atomic.StoreUint32(&obj._Enable, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Enable")
	}
	return nil
}
func (obj *TransactionAggregateParams) SetFromDate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *TransactionAggregateParams.FromDate", reflect.TypeOf(val))
	}
	obj._FromDateMutex.Lock()
	defer obj._FromDateMutex.Unlock()
	obj._FromDate = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FromDate")
	}
	return nil
}

func NewMinKycUserParams() (_obj *MinKycUserParams, _setters map[string]dynconf.SetFunc) {
	_obj = &MinKycUserParams{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minkyctotalcreditedlimitfornewuser"] = _obj.SetMinKycTotalCreditedLimitForNewUser
	_setters["minkycmaxsavingsbalancefornewuser"] = _obj.SetMinKycMaxSavingsBalanceForNewUser
	_setters["minkyctotalcreditedlimit"] = _obj.SetMinKycTotalCreditedLimit
	_setters["minkycmaxsavingsbalance"] = _obj.SetMinKycMaxSavingsBalance
	return _obj, _setters
}

func (obj *MinKycUserParams) Init() {
	newObj, _ := NewMinKycUserParams()
	*obj = *newObj
}

func (obj *MinKycUserParams) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *MinKycUserParams) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.MinKycUserParams)
	if !ok {
		return fmt.Errorf("invalid data type %v *MinKycUserParams", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *MinKycUserParams) setDynamicField(v *config.MinKycUserParams, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minkyctotalcreditedlimitfornewuser":
		return obj.SetMinKycTotalCreditedLimitForNewUser(v.MinKycTotalCreditedLimitForNewUser, true, nil)
	case "minkycmaxsavingsbalancefornewuser":
		return obj.SetMinKycMaxSavingsBalanceForNewUser(v.MinKycMaxSavingsBalanceForNewUser, true, nil)
	case "minkyctotalcreditedlimit":
		return obj.SetMinKycTotalCreditedLimit(v.MinKycTotalCreditedLimit, true, nil)
	case "minkycmaxsavingsbalance":
		return obj.SetMinKycMaxSavingsBalance(v.MinKycMaxSavingsBalance, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *MinKycUserParams) setDynamicFields(v *config.MinKycUserParams, dynamic bool, path []string) (err error) {

	err = obj.SetMinKycTotalCreditedLimitForNewUser(v.MinKycTotalCreditedLimitForNewUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinKycMaxSavingsBalanceForNewUser(v.MinKycMaxSavingsBalanceForNewUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinKycTotalCreditedLimit(v.MinKycTotalCreditedLimit, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinKycMaxSavingsBalance(v.MinKycMaxSavingsBalance, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *MinKycUserParams) setStaticFields(v *config.MinKycUserParams) error {

	return nil
}

func (obj *MinKycUserParams) SetMinKycTotalCreditedLimitForNewUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *MinKycUserParams.MinKycTotalCreditedLimitForNewUser", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinKycTotalCreditedLimitForNewUser, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinKycTotalCreditedLimitForNewUser")
	}
	return nil
}
func (obj *MinKycUserParams) SetMinKycMaxSavingsBalanceForNewUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *MinKycUserParams.MinKycMaxSavingsBalanceForNewUser", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinKycMaxSavingsBalanceForNewUser, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinKycMaxSavingsBalanceForNewUser")
	}
	return nil
}
func (obj *MinKycUserParams) SetMinKycTotalCreditedLimit(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *MinKycUserParams.MinKycTotalCreditedLimit", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinKycTotalCreditedLimit, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinKycTotalCreditedLimit")
	}
	return nil
}
func (obj *MinKycUserParams) SetMinKycMaxSavingsBalance(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *MinKycUserParams.MinKycMaxSavingsBalance", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinKycMaxSavingsBalance, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinKycMaxSavingsBalance")
	}
	return nil
}

func NewSignLegality() (_obj *SignLegality, _setters map[string]dynconf.SetFunc) {
	_obj = &SignLegality{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["signatureinreviewperiod"] = _obj.SetSignatureInReviewPeriod
	return _obj, _setters
}

func (obj *SignLegality) Init() {
	newObj, _ := NewSignLegality()
	*obj = *newObj
}

func (obj *SignLegality) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SignLegality) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SignLegality)
	if !ok {
		return fmt.Errorf("invalid data type %v *SignLegality", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SignLegality) setDynamicField(v *config.SignLegality, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "signatureinreviewperiod":
		return obj.SetSignatureInReviewPeriod(v.SignatureInReviewPeriod, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SignLegality) setDynamicFields(v *config.SignLegality, dynamic bool, path []string) (err error) {

	err = obj.SetSignatureInReviewPeriod(v.SignatureInReviewPeriod, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SignLegality) setStaticFields(v *config.SignLegality) error {

	return nil
}

func (obj *SignLegality) SetSignatureInReviewPeriod(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SignLegality.SignatureInReviewPeriod", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SignatureInReviewPeriod, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SignatureInReviewPeriod")
	}
	return nil
}

func NewSavingsAccountClosure() (_obj *SavingsAccountClosure, _setters map[string]dynconf.SetFunc) {
	_obj = &SavingsAccountClosure{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxpagesize"] = _obj.SetMaxPageSize
	_setters["expiryduration"] = _obj.SetExpiryDuration
	_setters["submissionwindow"] = _obj.SetSubmissionWindow
	_setters["cancelrequestnudgeid"] = _obj.SetCancelRequestNudgeId
	_obj._CancelRequestNudgeIdMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *SavingsAccountClosure) Init() {
	newObj, _ := NewSavingsAccountClosure()
	*obj = *newObj
}

func (obj *SavingsAccountClosure) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SavingsAccountClosure) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SavingsAccountClosure)
	if !ok {
		return fmt.Errorf("invalid data type %v *SavingsAccountClosure", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SavingsAccountClosure) setDynamicField(v *config.SavingsAccountClosure, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxpagesize":
		return obj.SetMaxPageSize(v.MaxPageSize, true, nil)
	case "expiryduration":
		return obj.SetExpiryDuration(v.ExpiryDuration, true, nil)
	case "submissionwindow":
		return obj.SetSubmissionWindow(v.SubmissionWindow, true, nil)
	case "cancelrequestnudgeid":
		return obj.SetCancelRequestNudgeId(v.CancelRequestNudgeId, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SavingsAccountClosure) setDynamicFields(v *config.SavingsAccountClosure, dynamic bool, path []string) (err error) {

	err = obj.SetMaxPageSize(v.MaxPageSize, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExpiryDuration(v.ExpiryDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSubmissionWindow(v.SubmissionWindow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCancelRequestNudgeId(v.CancelRequestNudgeId, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SavingsAccountClosure) setStaticFields(v *config.SavingsAccountClosure) error {

	return nil
}

func (obj *SavingsAccountClosure) SetMaxPageSize(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *SavingsAccountClosure.MaxPageSize", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._MaxPageSize, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxPageSize")
	}
	return nil
}
func (obj *SavingsAccountClosure) SetExpiryDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SavingsAccountClosure.ExpiryDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ExpiryDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExpiryDuration")
	}
	return nil
}
func (obj *SavingsAccountClosure) SetSubmissionWindow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SavingsAccountClosure.SubmissionWindow", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SubmissionWindow, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SubmissionWindow")
	}
	return nil
}
func (obj *SavingsAccountClosure) SetCancelRequestNudgeId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SavingsAccountClosure.CancelRequestNudgeId", reflect.TypeOf(val))
	}
	obj._CancelRequestNudgeIdMutex.Lock()
	defer obj._CancelRequestNudgeIdMutex.Unlock()
	obj._CancelRequestNudgeId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CancelRequestNudgeId")
	}
	return nil
}
