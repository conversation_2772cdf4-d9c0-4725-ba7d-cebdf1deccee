Application:
  Environment: "prod"
  Name: "savings"
  IsSecureRedis: true

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.prod-user-service-redis.iqb2bw.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 5
  ClientName: savings
  HystrixCommand:
    CommandName: "savings_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 2500
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

BalanceHistoryRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.prod-balance-history-redis.iqb2bw.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 0
  ClientName: balancehistory

SavingsCacheConfig:
  IsCachingEnabled: true
  SavingsIdPrefix: "savings_id_"
  SecondaryIdPrefix: "cache_savings_secondary_id_"
  CacheTTl: "1h"
  EssentialsCacheTTLForNonActiveAccounts: "1h"
  EssentialsCacheTTLForActiveAccounts: "168h" # 7 days

Server:
  Ports:
    GrpcPort: 8084
    GrpcSecurePort: 9516
    HttpPort: 9999
    HttpPProfPort: 9990
  EnablePoller: true

EpifiDb:
  AppName: "savings"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

AWS:
  Region: "ap-south-1"

SavingsCreationPublisher:
  QueueName: "prod-savings-creation-queue"

ThirdPartyAccountSharingPublisher:
  QueueName: "prod-third-party-account-sharing-event-queue"

ThirdPartyAccountSharingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-third-party-account-sharing-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 3
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "savings-accounts"

SavingsCreationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-savings-creation-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~20 min post that regular interval is followed hourly for next 7 days.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 5
          MaxAttempts: 8
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 1
          MaxAttempts: 168
          TimeUnit: "Hour"
      MaxAttempts: 176
      CutOff: 8

Secrets:
  Ids:
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"


CreateVPASubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-savings-create-vpa-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~25 min post that regular interval is followed for next 24 hours.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 9
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 48
          TimeUnit: "Minute"
      MaxAttempts: 57
      CutOff: 9

CreateVPAPublisher:
  QueueName: "prod-savings-create-vpa-queue"

AccountStmtSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-account-statement-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 3
      MaxAttempts: 3
      TimeUnit: "Second"

AccountStmtPublisher:
  QueueName: "prod-account-statement-queue"

AccountStatePublisher:
  TopicName: "prod-savings-account-state-update"

SavingsAccountPICreationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-savings-account-pi-creation-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~25 min post that regular interval is followed for next 24 hours. ~ same strategy used in vpa creation
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 9
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 48
          TimeUnit: "Minute"
      MaxAttempts: 57
      CutOff: 9

SavingsAccountPICreationPublisher:
  QueueName: "prod-savings-account-pi-creation-queue"

Flags:
  TrimDebugMessageFromStatus: false
  PercentageRollOutForDiffCheckBtwnBalanceApi:
    IsFeatureEnabled: true
    RolloutPercentage: 10
    TimeToWait: 3s

RudderStack:
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

SavingsCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-savings-creation-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

EventAfPurchasePublisher:
  QueueName: "prod-event-af-purchase-queue"

BalanceUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-process-balance-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

BalanceUpdateEventPublisher:
  TopicName: "prod-balance-update-topic"

BalanceChangeEventPublisher:
  TopicName: "prod-balance-update-events-topic"

AccountCreationEnquiryDelay: 90s

ExternalAccounts:
  InhouseNameMatchThreshold: 0.8
  NameMatchFailLimit: 5
  AccValidationFailLimit: 3
  EnableBalanceRefundIncidentCreation: true
  IssueCategoryId: "46c4f9a2-4c2c-5e03-8ffc-2b818a6c1cb5"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: true
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: true
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

EnableBalanceUpdateEventPublishing: true

EnableSchemeChangeUpdate: true

AccountConstraintsConfig:
  CreditFreezeBannerElementId: "c9dc8794-e959-4bfc-931c-df637f086b62"

OperationalStatusUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-savings-account-operational-status-update-consumer-queue"
  RetryStrategy:
    RegularInterval:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ProcessTierUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-savings-tier-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 10s
    Namespace: "savings"

ProcessKycUpdateEventSubscriber:
  StartOnServerStart: false # ProcessKycUpdateEvent consumer is turned off since scheme update when user does KYC is handled at Federal end and we don't need to do it at our end
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-savings-kyc-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1
        Period: 10s
    Namespace: "savings"

ProcessBalanceUpdateEventSubscriber:
  StartOnServerStart: false # disabling since we are disabling auto-cancellation of closure requests
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-savings-account-closure-balance-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "savings"

DisableGetAccountBalanceV1: true

SignLegality:
  SignatureInReviewPeriod: "120h"

SavingsAccountClosure:
  CancelRequestNudgeId: "6301a8fa-6aee-4a87-acb1-a1886f17da86"
  ExpiryDuration: 2160h #90 days

EnableAccountVerificationViaPayService: true
AccountVerificationPollingAttempts: 10
AccountVerificationPollingSleepDuration: 2s
