package consumer

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	tieringPb "github.com/epifi/gamma/api/tiering"

	userpb "github.com/epifi/gamma/api/user"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	tieringExtPb "github.com/epifi/gamma/api/tiering/external"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	savingsNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/savings"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/bankcust"
	kycPb "github.com/epifi/gamma/api/kyc"
	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/consumer"
	"github.com/epifi/gamma/api/savings/payload"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/savings/config/genconf"
)

type EventSubscriber struct {
	genConf             *genconf.Config
	celestialClient     celestialPb.CelestialClient
	savingsClient       savingsPb.SavingsClient
	bcClient            bankcust.BankCustomerServiceClient
	salaryProgramClient salaryprogramPb.SalaryProgramClient
	tieringClient       tieringPb.TieringClient
}

func NewEventSubscriber(genConf *genconf.Config,
	celestialClient celestialPb.CelestialClient,
	savingsClient savingsPb.SavingsClient,
	bcClient bankcust.BankCustomerServiceClient,
	salaryProgramClient salaryprogramPb.SalaryProgramClient,
	tieringClient tieringPb.TieringClient,
) *EventSubscriber {
	return &EventSubscriber{
		genConf:             genConf,
		celestialClient:     celestialClient,
		savingsClient:       savingsClient,
		bcClient:            bcClient,
		salaryProgramClient: salaryProgramClient,
		tieringClient:       tieringClient,
	}
}

func (e *EventSubscriber) ProcessTierUpdateEventConsumer(ctx context.Context, event *external.TierUpdateEvent) (*consumer.ProcessTierUpdateEventConsumerResponse, error) {
	logger.Debug(ctx, "received the tier change event", zap.Any("EVENT", event))
	resp := func(status queue.MessageConsumptionStatus) *consumer.ProcessTierUpdateEventConsumerResponse {
		return &consumer.ProcessTierUpdateEventConsumerResponse{
			ResponseHeader: &queue.ConsumerResponseHeader{
				Status: status,
			},
		}
	}
	if !e.genConf.EnableSchemeChangeUpdate() {
		logger.Info(ctx, "scheme change is disabled")
		return resp(queue.MessageConsumptionStatus_SUCCESS), nil
	}

	bcResp, err := e.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: event.GetActorId(),
		},
	})

	if rpcErr := epifigrpc.RPCError(bcResp, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching bank customer from be", zap.Error(rpcErr))
		return resp(queue.MessageConsumptionStatus_TRANSIENT_FAILURE), nil
	}

	// Early returning since user is min kyc we won't be updating the scheme
	kycLevel := bcResp.GetBankCustomer().GetKycInfo().GetKycLevel()
	if kycLevel == kycPb.KYCLevel_MIN_KYC && event.GetToTier() != external.Tier_TIER_FI_REGULAR {
		return resp(queue.MessageConsumptionStatus_SUCCESS), nil
	}

	toSku, consumptionStatus, getToSkuErr := e.getToSkuForTier(ctx, event, kycLevel)
	if getToSkuErr != nil {
		logger.Error(ctx, "error in fetching to sku for tier", zap.Error(getToSkuErr))
		return resp(consumptionStatus), nil
	}

	consumptionStatus = e.processSchemeChange(ctx, event.GetActorId(), event.GetEventTimestamp(), toSku)
	return resp(consumptionStatus), nil
}

func (e *EventSubscriber) processSchemeChange(ctx context.Context, actorId string, eventTimestamp *timestampPb.Timestamp, toSku savingsPb.SKU) queue.MessageConsumptionStatus {
	toScheme := getVendorSKU(ctx, e.genConf.VendorSKUMapping(), toSku, commonvgpb.Vendor_FEDERAL_BANK)
	if toScheme == "" {
		logger.Error(ctx, "sku to scheme code mapping does not exist in config", zap.String("toSku", toSku.String()))
		return queue.MessageConsumptionStatus_TRANSIENT_FAILURE
	}

	// we need to consider from sku because sku of savings can be updated  frequently while transaction status will take 7 days to update
	savingsResp, err := e.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(savingsResp, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching the savings account info", zap.Error(rpcErr))
		return queue.MessageConsumptionStatus_TRANSIENT_FAILURE
	}

	// Check if the account is in CLOSED state, return permanent failure
	if savingsResp.GetAccount().GetState() == savingsPb.State_CLOSED {
		logger.Info(ctx, "account is in CLOSED state, skipping scheme change", zap.String("actorId", actorId))
		return queue.MessageConsumptionStatus_PERMANENT_FAILURE
	}

	// Check if the event's timestamp is before the 'Federal' SKU's last update timestamp, then we skip it
	if datetime.IsBefore(eventTimestamp, savingsResp.GetAccount().GetSkuInfo().GetFederal().GetUpdatedAt()) {
		logger.Debug(ctx, "the sku is already updated, skipping the event")
		return queue.MessageConsumptionStatus_SUCCESS
	}
	var clientReqId = idgen.FederalRandomSequence("SCHCNG", 6)
	// we take from scheme from savings db instead of relying on event
	fromScheme := savingsResp.GetAccount().GetSkuInfo().GetFederal().GetSku()

	// If targetScheme and current scheme is same then we won't updating the scheme at vendor
	if strings.EqualFold(toScheme, fromScheme) {
		return queue.MessageConsumptionStatus_SUCCESS
	}

	// add the client request id of current workflow in sku info
	err = e.updateClientRequestId(ctx, savingsResp, clientReqId)
	if err != nil {
		return queue.MessageConsumptionStatus_TRANSIENT_FAILURE
	}

	// initiate the schemeChangeWorkflow
	err = e.initiateSchemeChangeWorkflow(ctx, actorId, eventTimestamp, fromScheme, toScheme, toSku, clientReqId)
	if err != nil {
		return queue.MessageConsumptionStatus_TRANSIENT_FAILURE
	}
	return queue.MessageConsumptionStatus_SUCCESS
}

func (e *EventSubscriber) updateClientRequestId(ctx context.Context, savingsResp *savingsPb.GetSavingsAccountEssentialsResponse, clientReqId string) error {
	// construct request ids array
	var updatedClientReqIds []string
	if len(savingsResp.GetAccount().GetSkuInfo().GetFederal().GetClientReqIds()) == 0 {
		updatedClientReqIds = append(updatedClientReqIds, clientReqId)
	} else {
		updatedClientReqIds = append(updatedClientReqIds, savingsResp.GetAccount().GetSkuInfo().GetFederal().GetClientReqIds()[0], clientReqId)
	}
	// update the sku info by workflow request ids
	updateResp, err := e.savingsClient.UpdateAccount(ctx, &savingsPb.UpdateAccountRequest{
		Identifier: &savingsPb.UpdateAccountRequest_Id{
			Id: savingsResp.GetAccount().GetId(),
		},
		UpdateMask: []savingsPb.AccountFieldMask{
			savingsPb.AccountFieldMask_SKU_INFO_AND_METADATA,
		},
		SkuInfo: &savingsPb.SKUInfo{
			Sku: savingsResp.GetAccount().GetSkuInfo().GetSku(),
			VendorSkuInfo: &savingsPb.SKUInfo_Federal{
				Federal: &savingsPb.FederalSKUInfo{
					Sku:          savingsResp.GetAccount().GetSkuInfo().GetFederal().GetSku(),
					UpdatedAt:    savingsResp.GetAccount().GetSkuInfo().GetFederal().GetUpdatedAt(),
					ClientReqIds: updatedClientReqIds,
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(updateResp, err); rpcErr != nil {
		logger.Error(ctx, "error while updating client request ids in sku info", zap.Error(rpcErr))
		return rpcErr
	}
	return nil
}

func (e *EventSubscriber) getToSkuForTier(ctx context.Context, event *external.TierUpdateEvent, kycLevel kycPb.KYCLevel) (savingsPb.SKU, queue.MessageConsumptionStatus, error) {
	toSku := getSkuFromTierAndKyc(event.GetToTier(), kycLevel)
	if toSku == savingsPb.SKU_ACCOUNT_SKU_UNSPECIFIED {
		return savingsPb.SKU_ACCOUNT_SKU_UNSPECIFIED, queue.MessageConsumptionStatus_PERMANENT_FAILURE, fmt.Errorf("tier to sku mapping does not exist for mentioned tier")
	}
	return toSku, queue.MessageConsumptionStatus_SUCCESS, nil
}

func getSkuFromTierAndKyc(toTier external.Tier, kycLevel kycPb.KYCLevel) savingsPb.SKU {
	var (
		tierToSkuInfoMap = map[external.Tier]savingsPb.SKU{
			external.Tier_TIER_FI_BASIC:            savingsPb.SKU_INFINITY,
			external.Tier_TIER_FI_PLUS:             savingsPb.SKU_FINITY,
			external.Tier_TIER_FI_INFINITE:         savingsPb.SKU_ULTIMATE,
			external.Tier_TIER_FI_SALARY_LITE:      savingsPb.SKU_ULTIMATE,
			external.Tier_TIER_FI_SALARY_BASIC:     savingsPb.SKU_FINITY,
			external.Tier_TIER_FI_SALARY:           savingsPb.SKU_ULTIMATE,
			external.Tier_TIER_FI_AA_SALARY_BAND_1: savingsPb.SKU_FINITY,
			external.Tier_TIER_FI_AA_SALARY_BAND_2: savingsPb.SKU_FINITY,
			external.Tier_TIER_FI_AA_SALARY_BAND_3: savingsPb.SKU_PRIME,
		}
	)
	toSku, toSkuExist := tierToSkuInfoMap[toTier]
	if !toSkuExist {
		if toTier == external.Tier_TIER_FI_REGULAR {
			if kycLevel == kycPb.KYCLevel_MIN_KYC {
				return savingsPb.SKU_REGULAR_MIN_KYC
			}
			if kycLevel == kycPb.KYCLevel_FULL_KYC {
				return savingsPb.SKU_REGULAR_FULL_KYC
			}
		}
		return savingsPb.SKU_ACCOUNT_SKU_UNSPECIFIED
	}
	return toSku
}

func (e *EventSubscriber) initiateSchemeChangeWorkflow(ctx context.Context, actorId string, eventTimestamp *timestampPb.Timestamp, fromScheme string, toScheme string, toSku savingsPb.SKU, clientReqId string) error {
	schemeChangePayload := &payload.SchemeChangePayload{
		EventSubscribedAt: eventTimestamp,
		FromScheme:        fromScheme,
		ToScheme:          toScheme,
		ToSku:             toSku,
		ActorId:           actorId,
	}
	schemeChangePayloadBytes, err := protojson.Marshal(schemeChangePayload)
	if err != nil {
		logger.Error(ctx, "error in marshalling scheme change payload", zap.Error(err))
		return err
	}

	initiateResp, initiateErr := e.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: actorId,
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(savingsNs.SchemeChange),
			ClientReqId: &workflowPb.ClientReqId{
				Id:     clientReqId,
				Client: workflowPb.Client_SAVINGS,
			},
			Ownership:        commontypes.Ownership_EPIFI_TECH,
			Payload:          schemeChangePayloadBytes,
			QualityOfService: celestialPb.QoS_BEST_EFFORT,
		},
	})
	if te := epifigrpc.RPCError(initiateResp, initiateErr); te != nil {
		logger.Error(ctx, fmt.Sprintf("failed to initiate workflow for scheme change, client req id %v", clientReqId), zap.Error(te))
		return te
	}
	logger.Info(ctx, fmt.Sprintf("initiated the workflow for client req id %v", clientReqId))
	return nil
}

func getVendorSKU(ctx context.Context, vendorSKUMap map[string]map[string]string, sku savingsPb.SKU, vendor commonvgpb.Vendor) string {
	vendorToSKUMap, ok := vendorSKUMap[sku.String()]
	if !ok {
		logger.Error(ctx, "invalid sku in get vendor sku", zap.String("sku", sku.String()))
		return ""
	}

	vendorSKU, ok := vendorToSKUMap[vendor.String()]
	if !ok {
		logger.Error(ctx, "invalid vendor in get vendor sku", zap.String("vendor", vendor.String()))
		return ""
	}
	return vendorSKU
}

// ProcessKycUpdateEvent consumer is turned off since scheme update when user does KYC is handled at Federal end and we don't need to do it at our end
// scheme update for every KYC update has always been done at Federal end and is never needed at our end
func (e *EventSubscriber) ProcessKycUpdateEvent(ctx context.Context, req *userpb.KycEvent) (*consumer.ProcessKycUpdateEventResponse, error) {
	resp := func(status queue.MessageConsumptionStatus) *consumer.ProcessKycUpdateEventResponse {
		return &consumer.ProcessKycUpdateEventResponse{
			ResponseHeader: &queue.ConsumerResponseHeader{
				Status: status,
			},
		}
	}
	if req.GetActorId() == "" {
		logger.Error(ctx, "actor id is empty in request")
		return resp(queue.MessageConsumptionStatus_PERMANENT_FAILURE), nil
	}

	actorId := req.GetActorId()

	logger.Info(ctx, "received kyc update event", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("actionTimestamp", req.GetActionTimestamp()))

	tieringPitchV2Resp, tieringPitchV2Err := e.tieringClient.GetTieringPitchV2(ctx, &tieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(tieringPitchV2Resp, tieringPitchV2Err); rpcErr != nil {
		logger.Error(ctx, "error while getting current tier of user using GetTieringPitchV2", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return resp(queue.MessageConsumptionStatus_TRANSIENT_FAILURE), nil
	}

	if tieringPitchV2Resp.GetCurrentTier() != tieringExtPb.Tier_TIER_FI_REGULAR {
		logger.Info(ctx, "no need of scheme change since current tier is not Regular", zap.String(logger.ACTOR_ID_V2, actorId))
		return resp(queue.MessageConsumptionStatus_SUCCESS), nil
	}

	toSku := savingsPb.SKU_REGULAR_FULL_KYC

	consumptionStatus := e.processSchemeChange(ctx, actorId, req.GetActionTimestamp(), toSku)
	return resp(consumptionStatus), nil

}
