package consumer_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/celestial"
	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	savingsNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/savings"
	pkgMocks "github.com/epifi/be-common/pkg/mock"
	"github.com/epifi/gamma/api/bankcust"
	kycPb "github.com/epifi/gamma/api/kyc"
	pb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/consumer"
	"github.com/epifi/gamma/api/savings/payload"
	"github.com/epifi/gamma/api/tiering/external"
)

var (
	actorId             = "actorId"
	now                 = timestampPb.Now()
	currentSku          = pb.SKU_MIN_KYC
	currentScheme       = "35033"
	clientReqId         = "client-req-id"
	schemeChangePayload = &payload.SchemeChangePayload{
		EventSubscribedAt: now,
		FromScheme:        "35033",
		ToScheme:          "35031",
		ToSku:             pb.SKU_FINITY,
		ActorId:           actorId,
	}
	schemeChangePayloadBytes, _ = protojson.Marshal(schemeChangePayload)
	savingsId                   = "savings-id"
)

func TestEventSubscriber_ProcessTierUpdateEventConsumer(t *testing.T) {
	type args struct {
		event *external.TierUpdateEvent
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(mock *MockEventSubscriberDependencies)
		want    *consumer.ProcessTierUpdateEventConsumerResponse
		wantErr error
	}{
		{
			name: "failed to initiate workflow",
			args: args{
				event: &external.TierUpdateEvent{
					ActorId:        actorId,
					FromTier:       external.Tier_TIER_FI_BASIC,
					ToTier:         external.Tier_TIER_FI_PLUS,
					EventTimestamp: now,
				},
			},
			mocks: func(mock *MockEventSubscriberDependencies) {
				mock.mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &pb.GetSavingsAccountEssentialsRequest{
					Filter: &pb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &pb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&pb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &pb.SavingsAccountEssentials{
						Id: savingsId,
						SkuInfo: &pb.SKUInfo{
							Sku: currentSku,
							VendorSkuInfo: &pb.SKUInfo_Federal{
								Federal: &pb.FederalSKUInfo{
									Sku: currentScheme,
								},
							},
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().UpdateAccount(gomock.Any(), pkgMocks.NewProtoMatcher(&pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_Id{
						Id: savingsId,
					},
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_SKU_INFO_AND_METADATA},
					SkuInfo: &pb.SKUInfo{
						Sku: currentSku,
						VendorSkuInfo: &pb.SKUInfo_Federal{
							Federal: &pb.FederalSKUInfo{
								Sku:          currentScheme,
								ClientReqIds: []string{clientReqId},
							},
						},
					},
				}, protocmp.IgnoreFields(&pb.FederalSKUInfo{}, "client_req_ids"))).Return(&pb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mock.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), pkgMocks.NewProtoMatcher(&celestial.InitiateWorkflowRequest{
					Params: &celestial.WorkflowCreationRequestParams{
						ActorId: actorId,
						Version: workflowPb.Version_V0,
						Type:    celestialPkg.GetTypeEnumFromWorkflowType(savingsNs.SchemeChange),
						Payload: schemeChangePayloadBytes,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     "abc",
							Client: workflowPb.Client_SAVINGS,
						},
						Ownership:        commontypes.Ownership_EPIFI_TECH,
						QualityOfService: celestialPb.QoS_BEST_EFFORT,
					},
				}, protocmp.IgnoreFields(&celestial.WorkflowCreationRequestParams{}, "client_req_id"))).
					Return(&celestial.InitiateWorkflowResponse{
						Status: rpc.StatusInternal(),
					}, nil)
				mock.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)
			},
			want: &consumer.ProcessTierUpdateEventConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: nil,
		},
		{
			name: "unspecified to sku",
			args: args{
				event: &external.TierUpdateEvent{
					ActorId:        actorId,
					FromTier:       external.Tier_TIER_FI_INFINITE,
					ToTier:         external.Tier_TIER_UNSPECIFIED,
					EventTimestamp: now,
				},
			},
			mocks: func(mock *MockEventSubscriberDependencies) {
				mock.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)
			},
			want: &consumer.ProcessTierUpdateEventConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: nil,
		},
		{
			name: "account is closed - permanent failure",
			args: args{
				event: &external.TierUpdateEvent{
					ActorId:        actorId,
					FromTier:       external.Tier_TIER_FI_BASIC,
					ToTier:         external.Tier_TIER_FI_PLUS,
					EventTimestamp: now,
				},
			},
			mocks: func(mock *MockEventSubscriberDependencies) {
				mock.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &pb.GetSavingsAccountEssentialsRequest{
					Filter: &pb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &pb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&pb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &pb.SavingsAccountEssentials{
						Id:    savingsId,
						State: pb.State_CLOSED,
						SkuInfo: &pb.SKUInfo{
							Sku: currentSku,
							VendorSkuInfo: &pb.SKUInfo_Federal{
								Federal: &pb.FederalSKUInfo{
									Sku: currentScheme,
								},
							},
						},
					},
				}, nil)
			},
			want: &consumer.ProcessTierUpdateEventConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: nil,
		},
		{
			name: "success: mismatch in scheme code",
			args: args{
				event: &external.TierUpdateEvent{
					ActorId:        actorId,
					FromTier:       external.Tier_TIER_FI_INFINITE,
					ToTier:         external.Tier_TIER_FI_PLUS,
					EventTimestamp: now,
				},
			},
			mocks: func(mock *MockEventSubscriberDependencies) {
				mock.mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &pb.GetSavingsAccountEssentialsRequest{
					Filter: &pb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &pb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&pb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &pb.SavingsAccountEssentials{
						Id: savingsId,
						SkuInfo: &pb.SKUInfo{
							Sku: currentSku,
							VendorSkuInfo: &pb.SKUInfo_Federal{
								Federal: &pb.FederalSKUInfo{
									Sku: currentScheme,
								},
							},
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().UpdateAccount(gomock.Any(), pkgMocks.NewProtoMatcher(&pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_Id{
						Id: savingsId,
					},
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_SKU_INFO_AND_METADATA},
					SkuInfo: &pb.SKUInfo{
						Sku: currentSku,
						VendorSkuInfo: &pb.SKUInfo_Federal{
							Federal: &pb.FederalSKUInfo{
								Sku:          currentScheme,
								ClientReqIds: []string{clientReqId},
							},
						},
					},
				}, protocmp.IgnoreFields(&pb.FederalSKUInfo{}, "client_req_ids"))).Return(&pb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mock.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), pkgMocks.NewProtoMatcher(&celestial.InitiateWorkflowRequest{
					Params: &celestial.WorkflowCreationRequestParams{
						ActorId: actorId,
						Version: workflowPb.Version_V0,
						Type:    celestialPkg.GetTypeEnumFromWorkflowType(savingsNs.SchemeChange),
						Payload: schemeChangePayloadBytes,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     "abc",
							Client: workflowPb.Client_SAVINGS,
						},
						Ownership:        commontypes.Ownership_EPIFI_TECH,
						QualityOfService: celestialPb.QoS_BEST_EFFORT,
					},
				}, protocmp.IgnoreFields(&celestial.WorkflowCreationRequestParams{}, "client_req_id"))).
					Return(&celestial.InitiateWorkflowResponse{
						Status: rpc.StatusOk(),
					}, nil)
				mock.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)
			},
			want: &consumer.ProcessTierUpdateEventConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: nil,
		},
		{
			name: "success: no active workflow",
			args: args{
				event: &external.TierUpdateEvent{
					ActorId:        actorId,
					FromTier:       external.Tier_TIER_FI_BASIC,
					ToTier:         external.Tier_TIER_FI_PLUS,
					EventTimestamp: now,
				},
			},
			mocks: func(mock *MockEventSubscriberDependencies) {
				mock.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &pb.GetSavingsAccountEssentialsRequest{
					Filter: &pb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &pb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&pb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &pb.SavingsAccountEssentials{
						Id: savingsId,
						SkuInfo: &pb.SKUInfo{
							Sku: currentSku,
							VendorSkuInfo: &pb.SKUInfo_Federal{
								Federal: &pb.FederalSKUInfo{
									Sku: currentScheme,
								},
							},
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().UpdateAccount(gomock.Any(), pkgMocks.NewProtoMatcher(&pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_Id{
						Id: savingsId,
					},
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_SKU_INFO_AND_METADATA},
					SkuInfo: &pb.SKUInfo{
						Sku: currentSku,
						VendorSkuInfo: &pb.SKUInfo_Federal{
							Federal: &pb.FederalSKUInfo{
								Sku:          currentScheme,
								ClientReqIds: []string{clientReqId},
							},
						},
					},
				}, protocmp.IgnoreFields(&pb.FederalSKUInfo{}, "client_req_ids"))).Return(&pb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mock.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), pkgMocks.NewProtoMatcher(&celestial.InitiateWorkflowRequest{
					Params: &celestial.WorkflowCreationRequestParams{
						ActorId: actorId,
						Version: workflowPb.Version_V0,
						Type:    celestialPkg.GetTypeEnumFromWorkflowType(savingsNs.SchemeChange),
						Payload: schemeChangePayloadBytes,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     clientReqId,
							Client: workflowPb.Client_SAVINGS,
						},
						Ownership:        commontypes.Ownership_EPIFI_TECH,
						QualityOfService: celestialPb.QoS_BEST_EFFORT,
					},
				}, protocmp.IgnoreFields(&celestial.WorkflowCreationRequestParams{}, "client_req_id"))).
					Return(&celestial.InitiateWorkflowResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
			want: &consumer.ProcessTierUpdateEventConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: nil,
		},
		{
			name: "success: one active workflow",
			args: args{
				event: &external.TierUpdateEvent{
					ActorId:        actorId,
					FromTier:       external.Tier_TIER_FI_BASIC,
					ToTier:         external.Tier_TIER_FI_PLUS,
					EventTimestamp: now,
				},
			},
			mocks: func(mock *MockEventSubscriberDependencies) {
				mock.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &pb.GetSavingsAccountEssentialsRequest{
					Filter: &pb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &pb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&pb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &pb.SavingsAccountEssentials{
						Id: savingsId,
						SkuInfo: &pb.SKUInfo{
							Sku: currentSku,
							VendorSkuInfo: &pb.SKUInfo_Federal{
								Federal: &pb.FederalSKUInfo{
									Sku:          currentScheme,
									ClientReqIds: []string{"abc"},
								},
							},
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().UpdateAccount(gomock.Any(), pkgMocks.NewProtoMatcher(&pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_Id{
						Id: savingsId,
					},
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_SKU_INFO_AND_METADATA},
					SkuInfo: &pb.SKUInfo{
						Sku: currentSku,
						VendorSkuInfo: &pb.SKUInfo_Federal{
							Federal: &pb.FederalSKUInfo{
								Sku:          currentScheme,
								ClientReqIds: []string{"abc", clientReqId},
							},
						},
					},
				}, protocmp.IgnoreFields(&pb.FederalSKUInfo{}, "client_req_ids"))).Return(&pb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mock.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), pkgMocks.NewProtoMatcher(&celestial.InitiateWorkflowRequest{
					Params: &celestial.WorkflowCreationRequestParams{
						ActorId: actorId,
						Version: workflowPb.Version_V0,
						Type:    celestialPkg.GetTypeEnumFromWorkflowType(savingsNs.SchemeChange),
						Payload: schemeChangePayloadBytes,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     clientReqId,
							Client: workflowPb.Client_SAVINGS,
						},
						Ownership:        commontypes.Ownership_EPIFI_TECH,
						QualityOfService: celestialPb.QoS_BEST_EFFORT,
					},
				}, protocmp.IgnoreFields(&celestial.WorkflowCreationRequestParams{}, "client_req_id"))).
					Return(&celestial.InitiateWorkflowResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
			want: &consumer.ProcessTierUpdateEventConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: nil,
		},
		{
			name: "success: two active workflow",
			args: args{
				event: &external.TierUpdateEvent{
					ActorId:        actorId,
					FromTier:       external.Tier_TIER_FI_BASIC,
					ToTier:         external.Tier_TIER_FI_PLUS,
					EventTimestamp: now,
				},
			},
			mocks: func(mock *MockEventSubscriberDependencies) {
				mock.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &pb.GetSavingsAccountEssentialsRequest{
					Filter: &pb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &pb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&pb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &pb.SavingsAccountEssentials{
						Id: savingsId,
						SkuInfo: &pb.SKUInfo{
							Sku: currentSku,
							VendorSkuInfo: &pb.SKUInfo_Federal{
								Federal: &pb.FederalSKUInfo{
									Sku:          currentScheme,
									ClientReqIds: []string{"abc", "def"},
								},
							},
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().UpdateAccount(gomock.Any(), pkgMocks.NewProtoMatcher(&pb.UpdateAccountRequest{
					Identifier: &pb.UpdateAccountRequest_Id{
						Id: savingsId,
					},
					UpdateMask: []pb.AccountFieldMask{pb.AccountFieldMask_SKU_INFO_AND_METADATA},
					SkuInfo: &pb.SKUInfo{
						Sku: currentSku,
						VendorSkuInfo: &pb.SKUInfo_Federal{
							Federal: &pb.FederalSKUInfo{
								Sku:          currentScheme,
								ClientReqIds: []string{"abc", clientReqId},
							},
						},
					},
				}, protocmp.IgnoreFields(&pb.FederalSKUInfo{}, "client_req_ids"))).Return(&pb.UpdateAccountResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mock.mockCelestialClient.EXPECT().InitiateWorkflow(gomock.Any(), pkgMocks.NewProtoMatcher(&celestial.InitiateWorkflowRequest{
					Params: &celestial.WorkflowCreationRequestParams{
						ActorId: actorId,
						Version: workflowPb.Version_V0,
						Type:    celestialPkg.GetTypeEnumFromWorkflowType(savingsNs.SchemeChange),
						Payload: schemeChangePayloadBytes,
						ClientReqId: &workflowPb.ClientReqId{
							Id:     clientReqId,
							Client: workflowPb.Client_SAVINGS,
						},
						Ownership:        commontypes.Ownership_EPIFI_TECH,
						QualityOfService: celestialPb.QoS_BEST_EFFORT,
					},
				}, protocmp.IgnoreFields(&celestial.WorkflowCreationRequestParams{}, "client_req_id"))).
					Return(&celestial.InitiateWorkflowResponse{
						Status: rpc.StatusOk(),
					}, nil)
			},
			want: &consumer.ProcessTierUpdateEventConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: nil,
		},
		{
			name: "success: skip event as stale event",
			args: args{
				event: &external.TierUpdateEvent{
					ActorId:        actorId,
					FromTier:       external.Tier_TIER_FI_BASIC,
					ToTier:         external.Tier_TIER_FI_PLUS,
					EventTimestamp: now,
				},
			},
			mocks: func(mock *MockEventSubscriberDependencies) {
				mock.mockBCClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycPb.KYCLevel_FULL_KYC,
						},
					},
				}, nil)
				mock.mockSavingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &pb.GetSavingsAccountEssentialsRequest{
					Filter: &pb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &pb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&pb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &pb.SavingsAccountEssentials{
						Id: savingsId,
						SkuInfo: &pb.SKUInfo{
							Sku: currentSku,
							VendorSkuInfo: &pb.SKUInfo_Federal{
								Federal: &pb.FederalSKUInfo{
									Sku:          currentScheme,
									ClientReqIds: []string{"abc", "def"},
									UpdatedAt:    timestampPb.New(now.AsTime().Add(10 * time.Second)),
								},
							},
						},
					},
				}, nil)
			},
			want: &consumer.ProcessTierUpdateEventConsumerResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			e, md := NewMockEventSubscriber(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := e.ProcessTierUpdateEventConsumer(context.Background(), tt.args.event)
			if err != nil || tt.wantErr != nil {
				if !assert.Equal(t, tt.wantErr.Error(), err.Error()) {
					t.Errorf("ProcessTierUpdateEventConsumer() error = %v, wantErr %v", err.Error(), tt.wantErr.Error())
					return
				}
			}
			opts := []cmp.Option{
				protocmp.Transform(),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("ProcessTierUpdateEventConsumer() got = %v, want %v", got, tt.want)
			}
		})
	}
}
