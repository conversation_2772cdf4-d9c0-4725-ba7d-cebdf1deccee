package developer

import (
	"fmt"

	devPb "github.com/epifi/gamma/api/savings/developer"
	"github.com/epifi/gamma/savings/developer/processor"
)

type DevFactory struct {
	devSavings                  *processor.DevSavings
	devSaClosure                *processor.DevSaClosure
	closedAccountBalanceProc    *processor.DevClosedAccountBalanceTransfer
	devBankAccountVerifications *processor.DevBankAccountVerifications
}

func NewDevFactory(
	devSavings *processor.DevSavings,
	devSaClosure *processor.DevSaClosure,
	closedAccountBalanceProc *processor.DevClosedAccountBalanceTransfer,
	devBankAccountVerifications *processor.DevBankAccountVerifications,
) *DevFactory {
	return &DevFactory{
		devSavings:                  devSavings,
		devSaClosure:                devSaClosure,
		closedAccountBalanceProc:    closedAccountBalanceProc,
		devBankAccountVerifications: devBankAccountVerifications,
	}
}

func (d *DevFactory) getParameterListImpl(entity devPb.SavingsEntity) (IParameterFetcher, error) {
	switch entity {
	case devPb.SavingsEntity_SAVINGS_ACCOUNT:
		return d.devSavings, nil
	case devPb.SavingsEntity_SAVINGS_ACCOUNT_CLOSURE_REQUESTS:
		return d.devSaClosure, nil
	case devPb.SavingsEntity_CLOSED_ACCOUNT_BALANCE_TRANSFER:
		return d.closedAccountBalanceProc, nil
	case devPb.SavingsEntity_BANK_ACCOUNT_VERIFICATIONS:
		return d.devBankAccountVerifications, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}

func (d *DevFactory) getDataImpl(entity devPb.SavingsEntity) (IDataFetcher, error) {
	switch entity {
	case devPb.SavingsEntity_SAVINGS_ACCOUNT:
		return d.devSavings, nil
	case devPb.SavingsEntity_SAVINGS_ACCOUNT_CLOSURE_REQUESTS:
		return d.devSaClosure, nil
	case devPb.SavingsEntity_CLOSED_ACCOUNT_BALANCE_TRANSFER:
		return d.closedAccountBalanceProc, nil
	case devPb.SavingsEntity_BANK_ACCOUNT_VERIFICATIONS:
		return d.devBankAccountVerifications, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}
