package processor

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/mask"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/proto/json"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/savings/developer"
	extacctPb "github.com/epifi/gamma/api/savings/extacct"
	extacctDao "github.com/epifi/gamma/savings/extacct/dao"
)

const (
	bavActorId       = "actor_id"
	bavId            = "id"
	bavAccountNumber = "account_number"
	bavIfsc          = "ifsc"
	bavOverallStatus = "overall_status"
)

type DevBankAccountVerifications struct {
	bavDao extacctDao.BankAccountVerificationsDao
}

func NewDevBankAccountVerifications(bavDao extacctDao.BankAccountVerificationsDao) *DevBankAccountVerifications {
	return &DevBankAccountVerifications{
		bavDao: bavDao,
	}
}

func (d *DevBankAccountVerifications) FetchParamList(ctx context.Context, entity developer.SavingsEntity) ([]*db_state.ParameterMeta, error) {
	if entity != developer.SavingsEntity_BANK_ACCOUNT_VERIFICATIONS {
		return nil, fmt.Errorf("invalid entity for bank account verifications processor: %v", entity)
	}

	var statusOptions []string
	for _, statusStr := range extacctPb.OverallStatus_name {
		if statusStr != "OVERALL_STATUS_UNSPECIFIED" {
			statusOptions = append(statusOptions, statusStr)
		}
	}

	paramList := []*db_state.ParameterMeta{
		{
			Name:            bavActorId,
			Label:           "Actor ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            bavId,
			Label:           "Bank Account Verification ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            bavAccountNumber,
			Label:           "BAV Account Number",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            bavIfsc,
			Label:           "BAV IFSC Code",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            bavOverallStatus,
			Label:           "Overall Status",
			Type:            db_state.ParameterDataType_MULTI_SELECT_DROPDOWN,
			Options:         statusOptions,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevBankAccountVerifications) FetchData(ctx context.Context, entity developer.SavingsEntity, filters []*db_state.Filter) (string, error) {
	if entity != developer.SavingsEntity_BANK_ACCOUNT_VERIFICATIONS {
		return "", fmt.Errorf("invalid entity for bank account verifications processor: %v", entity)
	}

	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil to fetch bank account verifications")
	}

	var actorId, bavIdFilter, accountNumber, ifsc string
	var statusFilters []extacctPb.OverallStatus

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case bavActorId:
			actorId = filter.GetStringValue()
		case bavId:
			bavIdFilter = filter.GetStringValue()
		case bavAccountNumber:
			accountNumber = filter.GetStringValue()
		case bavIfsc:
			ifsc = filter.GetStringValue()
		case bavOverallStatus:
			for _, statusStr := range filter.GetMultiSelectDropdownFilter().GetDropdownValues() {
				status := extacctPb.OverallStatus(extacctPb.OverallStatus_value[statusStr])
				statusFilters = append(statusFilters, status)
			}
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	// Since actor_id is mandatory, ensure it's provided
	if actorId == "" {
		return "", errors.New("actor_id is required")
	}

	var bankAccountVerifications []*extacctPb.BankAccountVerification
	var err error

	// Optimize: If both account_number and ifsc are provided, use the specialized DAO method
	// Otherwise, use GetByActorIdAndOverallStatus and filter in-memory
	if accountNumber != "" && ifsc != "" {
		// Use the more specific DAO method for better database performance
		bankAccountVerifications, err = d.bavDao.GetByAcctNoIfscAndOverallStatus(ctx, accountNumber, ifsc, statusFilters)
		if err != nil {
			logger.Error(ctx, "error while fetching bank account verifications by account number and IFSC", zap.Error(err))
			return "", fmt.Errorf("error while fetching bank account verifications by account number and IFSC: %w", err)
		}

		// Filter by actor_id and verification ID
		bankAccountVerifications = d.filterAndMaskBankAccountVerifications(bankAccountVerifications, actorId, bavIdFilter, "", "")
	} else {
		// Call DAO method only once and filter results in-memory
		bankAccountVerifications, err = d.bavDao.GetByActorIdAndOverallStatus(ctx, actorId, statusFilters)
		if err != nil {
			logger.Error(ctx, "error while fetching bank account verifications by actor id", zap.Error(err))
			return "", fmt.Errorf("error while fetching bank account verifications by actor id: %w", err)
		}

		// Apply all additional filters in-memory
		bankAccountVerifications = d.filterAndMaskBankAccountVerifications(bankAccountVerifications, actorId, bavIdFilter, accountNumber, ifsc)
	}

	// Create response structure
	response := &developer.BankAccountVerificationsEntityResponse{
		BankAccountVerifications: bankAccountVerifications,
	}

	responseBytes, err := json.Marshal(response)
	if err != nil {
		logger.Error(ctx, "error marshaling bank account verifications response", zap.Error(err))
		return "", fmt.Errorf("error marshaling bank account verifications response: %w", err)
	}

	return string(responseBytes), nil
}

// filterAndMaskBankAccountVerifications applies in-memory filtering and masks sensitive fields in bank account verifications
func (d *DevBankAccountVerifications) filterAndMaskBankAccountVerifications(
	bavs []*extacctPb.BankAccountVerification,
	actorId, bavIdFilter, accountNumber, ifsc string,
) []*extacctPb.BankAccountVerification {
	var filteredBavs []*extacctPb.BankAccountVerification

	for _, bav := range bavs {
		// Filter by actor_id (always required)
		if bav.GetActorId() != actorId {
			continue
		}

		// Filter by verification ID if provided
		if bavIdFilter != "" && bav.GetId() != bavIdFilter {
			continue
		}

		// Filter by account number if provided
		if accountNumber != "" && bav.GetAccountNumber() != accountNumber {
			continue
		}

		// Filter by IFSC if provided
		if ifsc != "" && bav.GetIfsc() != ifsc {
			continue
		}

		updatedBav := bav
		updatedBav.AccountNumber = mask.GetMaskedAccountNumber(bav.GetAccountNumber(), "X")
		updatedBav.Ifsc = mask.GetMaskedString(mask.DontMaskFirstFourChars, bav.GetIfsc())

		filteredBavs = append(filteredBavs, updatedBav)
	}

	return filteredBavs
}
