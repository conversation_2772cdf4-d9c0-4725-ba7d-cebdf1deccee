package developer

import (
	"context"
	"errors"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	devPb "github.com/epifi/gamma/api/savings/developer"

	"gorm.io/gorm"
)

type SavingsDbStatesService struct {
	fac *DevFactory
}

func NewSavingsDbStatesService(fac *DevFactory) *SavingsDbStatesService {
	return &SavingsDbStatesService{fac: fac}
}

var _ devPb.SavingsDbStatesServer = &SavingsDbStatesService{}

func (s *SavingsDbStatesService) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpc.StatusOk(),
		EntityList: []string{
			devPb.SavingsEntity_SAVINGS_ACCOUNT.String(),
			devPb.SavingsEntity_SAVINGS_ACCOUNT_CLOSURE_REQUESTS.String(),
			devPb.SavingsEntity_CLOSED_ACCOUNT_BALANCE_TRANSFER.String(),
			devPb.SavingsEntity_BANK_ACCOUNT_VERIFICATIONS.String(),
		},
	}, nil
}

func (s *SavingsDbStatesService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := devPb.SavingsEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in savings"),
		}, nil
	}
	paramFetcher, err := s.fac.getParameterListImpl(devPb.SavingsEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in savings")
		return &cxDsPb.GetParameterListResponse{Status: rpc.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, devPb.SavingsEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpc.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpc.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (s *SavingsDbStatesService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := devPb.SavingsEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in savings"),
		}, nil
	}
	dataFetcher, err := s.fac.getDataImpl(devPb.SavingsEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in savings")
		return &cxDsPb.GetDataResponse{Status: rpc.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, devPb.SavingsEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data")
		if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &cxDsPb.GetDataResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpc.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpc.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
