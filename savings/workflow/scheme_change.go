package workflow

import (
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	savingsNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/savings"
	"github.com/epifi/be-common/pkg/logger"

	savingsActivityPb "github.com/epifi/gamma/api/savings/activity"
)

// SchemeChange handles the savings account scheme change workflow.
// Note: A 6-hour sleep has been added between update and enquiry stages as scheme changes take at least 6 hours to reflect at vendor.
func SchemeChange(ctx workflow.Context, _ *workflowPb.Request) error {
	var (
		ownership = epificontext.OwnershipFromContext[workflow.Context](ctx)
		wfReqID   = workflow.GetInfo(ctx).WorkflowExecution.ID
		lg        = workflow.GetLogger(ctx)
	)

	wfProcessingParams := &activityPb.GetWorkflowProcessingParamsV2Response{}
	if err := activityPkg.Execute(
		ctx,
		epifitemporal.GetWorkflowProcessingParamsV2,
		wfProcessingParams,
		&activityPb.GetWorkflowProcessingParamsV2Request{
			RequestHeader: &activityPb.RequestHeader{
				Ownership: ownership,
			},
			WfReqId: wfReqID,
		},
	); err != nil {
		lg.Error("activity failed", zap.String(logger.ACTIVITY, string(epifitemporal.GetWorkflowProcessingParamsV2)), zap.Error(err))
		return err
	}

	// ------------------WORKFLOW DECISION ORCHESTRATOR STAGE------------------
	// decide whether current workflow needs to terminate, continue or retry
	workflowStageStatus, err := workflowDecisionOrchestratorStage(ctx, wfProcessingParams)
	switch {
	case err != nil:
		lg.Error("error in workflowDecisionOrchestrator stage", zap.Error(err))
		return errors.Wrap(err, "error in workflowDecisionOrchestrator stage")
	case workflowStageStatus != stagePb.Status_SUCCESSFUL:
		return nil
	}

	// ------------------UPDATE SCHEME AT VENDOR------------------
	if workflowStageStatus == stagePb.Status_SUCCESSFUL {
		workflowStageStatus, err = updateSchemeCodeAtVendorStage(ctx, wfProcessingParams)
		switch {
		case err != nil:
			lg.Error("error in updateSchemeCodeAtVendor stage", zap.Error(err))
		case workflowStageStatus != stagePb.Status_SUCCESSFUL:
			lg.Error("updateSchemeCodeAtVendor stage is unsuccessful")
		}
	}

	// ------------------WORKFLOW VERSIONING FOR SLEEP------------------
	// Version 1: Add 6-hour sleep between update and enquiry
	// This ensures backward compatibility with existing running workflows
	if workflowStageStatus == stagePb.Status_SUCCESSFUL {
		version := workflow.GetVersion(ctx, "scheme-change-sleep-v1", workflow.DefaultVersion, 1)
		if version == 1 {
			lg.Info("sleeping for 6 hours before enquiring scheme at vendor")
			if err := workflow.Sleep(ctx, 6*time.Hour); err != nil {
				lg.Error("error during workflow sleep", zap.Error(err))
				return err
			}
			lg.Info("workflow sleep completed, proceeding to enquire scheme at vendor")
		}
		// Version 0 (DefaultVersion): No sleep - for backward compatibility
	}

	// ------------------ENQUIRE SCHEME AT VENDOR------------------
	if workflowStageStatus == stagePb.Status_SUCCESSFUL {
		workflowStageStatus, err = enquireSchemeCodeAtVendorStage(ctx, wfProcessingParams)
		switch {
		case err != nil:
			lg.Error("error in enquireSchemeCodeAtVendor stage", zap.Error(err))
		case workflowStageStatus != stagePb.Status_SUCCESSFUL:
			lg.Error("enquireSchemeCodeAtVendor stage is unsuccessful")
		}
		if workflowStageStatus != stagePb.Status_SUCCESSFUL {
			// We setup a kibana alert on the following log line to proactively monitor for failures in enquiry API.
			// So change the alert rule when changing the log line.
			lg.Error("scheme status enquiry failed at vendor after the duration", zap.String(logger.ACTIVITY, string(savingsNs.EnquireSchemeAtVendor)), zap.Error(err))
		}
	}

	// ------------------UPDATE SCHEME IN DB------------------
	if workflowStageStatus == stagePb.Status_SUCCESSFUL {
		workflowStageStatus, err = updateSchemeInDBStage(ctx, wfProcessingParams)
		switch {
		case err != nil:
			lg.Error("error in updateSchemeInDBStage stage", zap.Error(err))
		case workflowStageStatus != stagePb.Status_SUCCESSFUL:
			lg.Error("updateSchemeInDBStage stage is unsuccessful")
		}
	}

	// ------------------REMOVE REQUEST ID FROM SKU INFO------------------
	workflowStageStatus, err = removeRequestIdFromSkuInfoStage(ctx, wfProcessingParams)
	switch {
	case err != nil:
		lg.Error("error in removeRequestIdFromSkuInfo stage", zap.Error(err))
		return errors.Wrap(err, "error in removeRequestIdFromSkuInfo stage")
	case workflowStageStatus != stagePb.Status_SUCCESSFUL:
		lg.Error("removeRequestIdFromSkuInfo stage is unsuccessful")
	}
	return nil
}

func removeRequestIdFromSkuInfoStage(ctx workflow.Context, wfProcessingParams *activityPb.GetWorkflowProcessingParamsV2Response) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	// =========== Step 1: initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, savingsNs.StageRemoveRequestIdFromSkuInfo, stagePb.Status_INITIATED); err != nil {
		return 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", savingsNs.StageRemoveRequestIdFromSkuInfo, err)
	}
	// =========== Step 2: enquire scheme code at vendor ============
	removeRequestIdFromSKUInfoResponse := &savingsActivityPb.RemoveRequestIdFromSKUInfoResponse{}
	err := activityPkg.Execute(ctx, savingsNs.RemoveRequestIdFromSkuInfo, removeRequestIdFromSKUInfoResponse, &savingsActivityPb.RemoveRequestIdFromSKUInfoRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetWfReqParams().GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetWfReqParams().GetPayload(),
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
	})
	workflowStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)
	if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(savingsNs.RemoveRequestIdFromSkuInfo)), zap.Error(err))
	}
	// Update workflow stage status
	if err = celestialPkg.UpdateWorkflowStage(ctx, savingsNs.StageRemoveRequestIdFromSkuInfo, workflowStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s: %w", savingsNs.StageRemoveRequestIdFromSkuInfo, err)
	}
	return workflowStageStatus, nil
}

func updateSchemeInDBStage(ctx workflow.Context, wfProcessingParams *activityPb.GetWorkflowProcessingParamsV2Response) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	// =========== Step 1: initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, savingsNs.StageUpdateSchemeInDB, stagePb.Status_INITIATED); err != nil {
		return 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", savingsNs.StageUpdateSchemeInDB, err)
	}
	// =========== Step 2: enquire scheme code at vendor ============
	updateSchemeInDBResponse := &savingsActivityPb.UpdateSchemeInDBResponse{}
	err := activityPkg.Execute(ctx, savingsNs.UpdateSchemeInDB, updateSchemeInDBResponse, &savingsActivityPb.UpdateSchemeInDBRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetWfReqParams().GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetWfReqParams().GetPayload(),
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
	})
	workflowStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)
	if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(savingsNs.UpdateSchemeInDB)), zap.Error(err))
	}
	// Update workflow stage status
	if err = celestialPkg.UpdateWorkflowStage(ctx, savingsNs.StageUpdateSchemeInDB, workflowStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s: %w", savingsNs.StageUpdateSchemeInDB, err)
	}
	return workflowStageStatus, nil
}

func enquireSchemeCodeAtVendorStage(ctx workflow.Context, wfProcessingParams *activityPb.GetWorkflowProcessingParamsV2Response) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	// =========== Step 1: initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, savingsNs.StageEnquireSchemeAtVendor, stagePb.Status_INITIATED); err != nil {
		return 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", savingsNs.StageUpdateSchemeAtVendor, err)
	}
	// =========== Step 2: enquire scheme code at vendor ============
	enquireSchemeCodeAtVendorResp := &savingsActivityPb.EnquireSchemeAtVendorResponse{}
	err := activityPkg.Execute(ctx, savingsNs.EnquireSchemeAtVendor, enquireSchemeCodeAtVendorResp, &savingsActivityPb.EnquireSchemeAtVendorRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetWfReqParams().GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetWfReqParams().GetPayload(),
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
	})
	workflowStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)
	if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(savingsNs.EnquireSchemeAtVendor)), zap.Error(err))
	}
	// Update workflow stage status
	if err = celestialPkg.UpdateWorkflowStage(ctx, savingsNs.StageEnquireSchemeAtVendor, workflowStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s: %w", savingsNs.StageEnquireSchemeAtVendor, err)
	}
	return workflowStageStatus, nil
}

func updateSchemeCodeAtVendorStage(ctx workflow.Context, wfProcessingParams *activityPb.GetWorkflowProcessingParamsV2Response) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	// =========== Step 1: initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, savingsNs.StageUpdateSchemeAtVendor, stagePb.Status_INITIATED); err != nil {
		return 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", savingsNs.StageUpdateSchemeAtVendor, err)
	}
	// =========== Step 2: update scheme code at vendor ============
	updateSchemeCodeAtVendorResp := &savingsActivityPb.UpdateSchemeAtVendorResponse{}
	err := activityPkg.Execute(ctx, savingsNs.UpdateSchemeAtVendor, updateSchemeCodeAtVendorResp, &savingsActivityPb.UpdateSchemeAtVendorRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetWfReqParams().GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetWfReqParams().GetPayload(),
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
	})
	workflowStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)
	if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(savingsNs.UpdateSchemeAtVendor)), zap.Error(err))
	}
	// =========== Step 3: update workflow stage status ===========
	if err = celestialPkg.UpdateWorkflowStage(ctx, savingsNs.StageUpdateSchemeAtVendor, workflowStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s: %w", savingsNs.StageUpdateSchemeAtVendor, err)
	}
	return workflowStageStatus, nil
}

func workflowDecisionOrchestratorStage(ctx workflow.Context, wfProcessingParams *activityPb.GetWorkflowProcessingParamsV2Response) (stagePb.Status, error) {
	lg := workflow.GetLogger(ctx)
	// =========== Step 1: initiate stage for the workflow ============
	if err := celestialPkg.InitiateWorkflowStage(ctx, savingsNs.StageWorkflowDecisionOrchestrator, stagePb.Status_INITIATED); err != nil {
		return 0, fmt.Errorf("failed to initiated workflow stage: %s: %w", savingsNs.StageWorkflowDecisionOrchestrator, err)
	}
	// =========== Step 2: check for existing workflows ============
	workflowDecisionOrchestratorResp := &savingsActivityPb.WorkflowDecisionOrchestratorResponse{}
	err := activityPkg.Execute(ctx, savingsNs.WorkflowDecisionOrchestrator, workflowDecisionOrchestratorResp, &savingsActivityPb.WorkflowDecisionOrchestratorRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: wfProcessingParams.GetWfReqParams().GetClientReqId().GetId(),
			Payload:     wfProcessingParams.GetWfReqParams().GetPayload(),
			Ownership:   commontypes.Ownership_EPIFI_TECH,
		},
	})
	workflowStageStatus := celestialPkg.GetWorkflowStageStatusForErr(err)
	if workflowStageStatus != stagePb.Status_SUCCESSFUL {
		lg.Error("activity failed to execute", zap.String(logger.ACTIVITY, string(savingsNs.WorkflowDecisionOrchestrator)), zap.Error(err))
	}
	// Update workflow stage status
	if err = celestialPkg.UpdateWorkflowStage(ctx, savingsNs.StageWorkflowDecisionOrchestrator, workflowStageStatus); err != nil {
		return stagePb.Status_STATUS_UNSPECIFIED, fmt.Errorf("failed to update workflow stage: %s: %w", savingsNs.WorkflowDecisionOrchestrator, err)
	}
	return workflowStageStatus, err
}
