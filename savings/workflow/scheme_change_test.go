// nolint
package workflow_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"go.temporal.io/sdk/workflow"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/epifitemporal/celestial"
	savingsNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/savings"
	pb "github.com/epifi/gamma/api/savings"
	savingsActivityPb "github.com/epifi/gamma/api/savings/activity"
	payloadPb "github.com/epifi/gamma/api/savings/payload"

	celestialActivity "github.com/epifi/gamma/celestial/activity/v2"
	savingsActivity "github.com/epifi/gamma/savings/activity"
	savingsWorkflow "github.com/epifi/gamma/savings/workflow"
)

var (
	clientReqId         = "client-req-id"
	actorId             = "actor-id"
	now                 = timestamppb.Now()
	fromScheme          = "35033"
	toScheme            = "35031"
	toSku               = pb.SKU_FINITY
	defaultWorkflowID   = "default-test-workflow-id"
	schemeChangePayload = &payloadPb.SchemeChangePayload{
		EventSubscribedAt: now,
		FromScheme:        fromScheme,
		ToScheme:          toScheme,
		ToSku:             toSku,
		ActorId:           actorId,
	}
	schemeChangePayloadBytes, _ = protojson.Marshal(schemeChangePayload)
)

func TestSchemeChange(t *testing.T) {

	type mockGetWorkflowProcessingParamsV2 struct {
		enable bool
		req    *activityPb.GetWorkflowProcessingParamsV2Request
		res    *activityPb.GetWorkflowProcessingParamsV2Response
		err    error
	}

	type mockInitiateWorkflowDecisionOrchestrator struct {
		enable bool
		req    *activityPb.InitiateWorkflowStageV2Request
		res    *activityPb.InitiateWorkflowStageV2Response
		err    error
	}

	type mockWorkflowDecisionOrchestrator struct {
		enable bool
		req    *savingsActivityPb.WorkflowDecisionOrchestratorRequest
		res    *savingsActivityPb.WorkflowDecisionOrchestratorResponse
		err    error
	}

	type mockUpdateWorkflowDecisionOrchestrator struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		res    *activityPb.UpdateWorkflowStageResponse
		err    error
	}

	type mockInitiateUpdateSchemeAtVendor struct {
		enable bool
		req    *activityPb.InitiateWorkflowStageV2Request
		res    *activityPb.InitiateWorkflowStageV2Response
		err    error
	}

	type mockUpdateSchemeAtVendor struct {
		enable bool
		req    *savingsActivityPb.UpdateSchemeAtVendorRequest
		res    *savingsActivityPb.UpdateSchemeAtVendorResponse
		err    error
	}

	type mockUpdateUpdateSchemeAtVendor struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		res    *activityPb.UpdateWorkflowStageResponse
		err    error
	}

	type mockInitiateEnquireSchemeAtVendor struct {
		enable bool
		req    *activityPb.InitiateWorkflowStageV2Request
		res    *activityPb.InitiateWorkflowStageV2Response
		err    error
	}

	type mockEnquireSchemeAtVendor struct {
		enable bool
		req    *savingsActivityPb.EnquireSchemeAtVendorRequest
		res    *savingsActivityPb.EnquireSchemeAtVendorResponse
		err    error
	}

	type mockUpdateEnquireSchemeAtVendor struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		res    *activityPb.UpdateWorkflowStageResponse
		err    error
	}

	type mockInitiateUpdateSchemeInDB struct {
		enable bool
		req    *activityPb.InitiateWorkflowStageV2Request
		res    *activityPb.InitiateWorkflowStageV2Response
		err    error
	}

	type mockUpdateSchemeInDB struct {
		enable bool
		req    *savingsActivityPb.UpdateSchemeInDBRequest
		res    *savingsActivityPb.UpdateSchemeInDBResponse
		err    error
	}

	type mockUpdateUpdateSchemeInDB struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		res    *activityPb.UpdateWorkflowStageResponse
		err    error
	}

	type mockInitiateRemoveRequestIdFromSkuInfo struct {
		enable bool
		req    *activityPb.InitiateWorkflowStageV2Request
		res    *activityPb.InitiateWorkflowStageV2Response
		err    error
	}

	type mockRemoveRequestIdFromSkuInfo struct {
		enable bool
		req    *savingsActivityPb.RemoveRequestIdFromSKUInfoRequest
		res    *savingsActivityPb.RemoveRequestIdFromSKUInfoResponse
		err    error
	}

	type mockUpdateRemoveRequestIdFromSkuInfo struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		res    *activityPb.UpdateWorkflowStageResponse
		err    error
	}

	type mockUpdateWorkflowStage struct {
		enable bool
		req    *activityPb.UpdateWorkflowStageRequest
		res    *activityPb.UpdateWorkflowStageResponse
		err    error
	}

	type mockPublishWorkflowUpdateEventV2 struct {
		enable bool
		req    *activityPb.PublishWorkflowUpdateEventV2Request
		err    error
	}

	tests := []struct {
		name                                     string
		req                                      *workflowPb.Request
		wantErr                                  bool
		workflowVersion                          workflow.Version
		mockGetWorkflowProcessingParamsV2        mockGetWorkflowProcessingParamsV2
		mockInitiateWorkflowDecisionOrchestrator mockInitiateWorkflowDecisionOrchestrator
		mockWorkflowDecisionOrchestrator         mockWorkflowDecisionOrchestrator
		mockUpdateWorkflowDecisionOrchestrator   mockUpdateWorkflowDecisionOrchestrator
		mockPublishWorkflowUpdateEventV2         mockPublishWorkflowUpdateEventV2
		mockInitiateUpdateSchemeAtVendor         mockInitiateUpdateSchemeAtVendor
		mockUpdateWorkflowStage                  mockUpdateWorkflowStage
		mockUpdateUpdateSchemeAtVendor           mockUpdateUpdateSchemeAtVendor
		mockUpdateSchemeAtVendor                 mockUpdateSchemeAtVendor
		mockInitiateEnquireSchemeAtVendor        mockInitiateEnquireSchemeAtVendor
		mockEnquireSchemeAtVendor                mockEnquireSchemeAtVendor
		mockUpdateEnquireSchemeAtVendor          mockUpdateEnquireSchemeAtVendor
		mockInitiateUpdateSchemeInDB             mockInitiateUpdateSchemeInDB
		mockUpdateSchemeInDB                     mockUpdateSchemeInDB
		mockUpdateUpdateSchemeInDB               mockUpdateUpdateSchemeInDB
		mockInitiateRemoveRequestIdFromSkuInfo   mockInitiateRemoveRequestIdFromSkuInfo
		mockRemoveRequestIdFromSkuInfo           mockRemoveRequestIdFromSkuInfo
		mockUpdateRemoveRequestIdFromSkuInfo     mockUpdateRemoveRequestIdFromSkuInfo
	}{
		{
			name:            "success fully updated the scheme in db (with 6h sleep - v1)",
			wantErr:         false,
			workflowVersion: 1,
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: schemeChangePayloadBytes,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowDecisionOrchestrator: mockInitiateWorkflowDecisionOrchestrator{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageWorkflowDecisionOrchestrator),
				},
				err: nil,
			},
			mockWorkflowDecisionOrchestrator: mockWorkflowDecisionOrchestrator{
				enable: true,
				req: &savingsActivityPb.WorkflowDecisionOrchestratorRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
			},
			mockUpdateWorkflowDecisionOrchestrator: mockUpdateWorkflowDecisionOrchestrator{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageWorkflowDecisionOrchestrator),
				},
			},

			mockPublishWorkflowUpdateEventV2: mockPublishWorkflowUpdateEventV2{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
				},
			},

			mockInitiateUpdateSchemeAtVendor: mockInitiateUpdateSchemeAtVendor{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageUpdateSchemeAtVendor),
				},
				err: nil,
			},

			mockUpdateSchemeAtVendor: mockUpdateSchemeAtVendor{
				enable: true,
				req: &savingsActivityPb.UpdateSchemeAtVendorRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
			},

			mockUpdateUpdateSchemeAtVendor: mockUpdateUpdateSchemeAtVendor{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageUpdateSchemeAtVendor),
				},
			},

			mockInitiateEnquireSchemeAtVendor: mockInitiateEnquireSchemeAtVendor{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageEnquireSchemeAtVendor),
				},
				err: nil,
			},

			mockEnquireSchemeAtVendor: mockEnquireSchemeAtVendor{
				enable: true,
				req: &savingsActivityPb.EnquireSchemeAtVendorRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				err: nil,
			},

			mockUpdateEnquireSchemeAtVendor: mockUpdateEnquireSchemeAtVendor{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageEnquireSchemeAtVendor),
				},
			},

			mockInitiateUpdateSchemeInDB: mockInitiateUpdateSchemeInDB{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageUpdateSchemeInDB),
				},
				err: nil,
			},

			mockUpdateSchemeInDB: mockUpdateSchemeInDB{
				enable: true,
				req: &savingsActivityPb.UpdateSchemeInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &savingsActivityPb.UpdateSchemeInDBResponse{},
			},

			mockUpdateUpdateSchemeInDB: mockUpdateUpdateSchemeInDB{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageUpdateSchemeInDB),
				},
			},

			mockInitiateRemoveRequestIdFromSkuInfo: mockInitiateRemoveRequestIdFromSkuInfo{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageRemoveRequestIdFromSkuInfo),
				},
				err: nil,
			},

			mockRemoveRequestIdFromSkuInfo: mockRemoveRequestIdFromSkuInfo{
				enable: true,
				req: &savingsActivityPb.RemoveRequestIdFromSKUInfoRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &savingsActivityPb.RemoveRequestIdFromSKUInfoResponse{},
			},

			mockUpdateRemoveRequestIdFromSkuInfo: mockUpdateRemoveRequestIdFromSkuInfo{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageRemoveRequestIdFromSkuInfo),
				},
			},
		},
		{
			name:            "success fully updated the scheme in db (without sleep - v0)",
			wantErr:         false,
			workflowVersion: workflow.DefaultVersion, // Test old workflow version without sleep
			mockGetWorkflowProcessingParamsV2: mockGetWorkflowProcessingParamsV2{
				enable: true,
				req: &activityPb.GetWorkflowProcessingParamsV2Request{
					RequestHeader: &activityPb.RequestHeader{
						Ownership: commontypes.Ownership_EPIFI_TECH,
					},
					WfReqId: defaultWorkflowID,
				},
				res: &activityPb.GetWorkflowProcessingParamsV2Response{
					WfReqParams: &workflowPb.ProcessingParams{
						ClientReqId: &workflowPb.ClientReqId{
							Id: clientReqId,
						},
						Payload: schemeChangePayloadBytes,
					},
				},
				err: nil,
			},
			mockInitiateWorkflowDecisionOrchestrator: mockInitiateWorkflowDecisionOrchestrator{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageWorkflowDecisionOrchestrator),
				},
				err: nil,
			},
			mockWorkflowDecisionOrchestrator: mockWorkflowDecisionOrchestrator{
				enable: true,
				req: &savingsActivityPb.WorkflowDecisionOrchestratorRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
			},
			mockUpdateWorkflowDecisionOrchestrator: mockUpdateWorkflowDecisionOrchestrator{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageWorkflowDecisionOrchestrator),
				},
			},

			mockPublishWorkflowUpdateEventV2: mockPublishWorkflowUpdateEventV2{
				enable: true,
				req: &activityPb.PublishWorkflowUpdateEventV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
				},
			},

			mockInitiateUpdateSchemeAtVendor: mockInitiateUpdateSchemeAtVendor{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageUpdateSchemeAtVendor),
				},
				err: nil,
			},

			mockUpdateSchemeAtVendor: mockUpdateSchemeAtVendor{
				enable: true,
				req: &savingsActivityPb.UpdateSchemeAtVendorRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
			},

			mockUpdateUpdateSchemeAtVendor: mockUpdateUpdateSchemeAtVendor{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageUpdateSchemeAtVendor),
				},
			},

			mockInitiateEnquireSchemeAtVendor: mockInitiateEnquireSchemeAtVendor{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageEnquireSchemeAtVendor),
				},
				err: nil,
			},

			mockEnquireSchemeAtVendor: mockEnquireSchemeAtVendor{
				enable: true,
				req: &savingsActivityPb.EnquireSchemeAtVendorRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				err: nil,
			},

			mockUpdateEnquireSchemeAtVendor: mockUpdateEnquireSchemeAtVendor{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageEnquireSchemeAtVendor),
				},
			},

			mockInitiateUpdateSchemeInDB: mockInitiateUpdateSchemeInDB{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageUpdateSchemeInDB),
				},
				err: nil,
			},

			mockUpdateSchemeInDB: mockUpdateSchemeInDB{
				enable: true,
				req: &savingsActivityPb.UpdateSchemeInDBRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &savingsActivityPb.UpdateSchemeInDBResponse{},
			},

			mockUpdateUpdateSchemeInDB: mockUpdateUpdateSchemeInDB{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageUpdateSchemeInDB),
				},
			},

			mockInitiateRemoveRequestIdFromSkuInfo: mockInitiateRemoveRequestIdFromSkuInfo{
				enable: true,
				req: &activityPb.InitiateWorkflowStageV2Request{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_INITIATED,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageRemoveRequestIdFromSkuInfo),
				},
				err: nil,
			},

			mockRemoveRequestIdFromSkuInfo: mockRemoveRequestIdFromSkuInfo{
				enable: true,
				req: &savingsActivityPb.RemoveRequestIdFromSKUInfoRequest{
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: clientReqId,
						Payload:     schemeChangePayloadBytes,
						Ownership:   commontypes.Ownership_EPIFI_TECH,
					},
				},
				res: &savingsActivityPb.RemoveRequestIdFromSKUInfoResponse{},
			},

			mockUpdateRemoveRequestIdFromSkuInfo: mockUpdateRemoveRequestIdFromSkuInfo{
				enable: true,
				req: &activityPb.UpdateWorkflowStageRequest{
					RequestHeader: &activityPb.RequestHeader{},
					WfReqId:       defaultWorkflowID,
					Status:        stage.Status_SUCCESSFUL,
					StageEnum:     celestial.GetStageEnumFromStage(savingsNs.StageRemoveRequestIdFromSkuInfo),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()

			// Mock workflow version for testing different versions
			env.OnGetVersion("scheme-change-sleep-v1", workflow.DefaultVersion, 1).Return(tt.workflowVersion)

			env.RegisterActivity(&savingsActivity.Processor{})
			env.RegisterActivity(&celestialActivity.Processor{})
			env.RegisterWorkflow(savingsWorkflow.SchemeChange)

			if tt.mockGetWorkflowProcessingParamsV2.enable {
				env.OnActivity(
					string(epifitemporal.GetWorkflowProcessingParamsV2), mock.Anything, tt.mockGetWorkflowProcessingParamsV2.req).
					Return(tt.mockGetWorkflowProcessingParamsV2.res, tt.mockGetWorkflowProcessingParamsV2.err)
			}

			if tt.mockInitiateWorkflowDecisionOrchestrator.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateWorkflowDecisionOrchestrator.req).
					Return(tt.mockInitiateWorkflowDecisionOrchestrator.err)
			}

			if tt.mockWorkflowDecisionOrchestrator.enable {
				env.OnActivity(string(savingsNs.WorkflowDecisionOrchestrator), mock.Anything, tt.mockWorkflowDecisionOrchestrator.req).
					Return(tt.mockWorkflowDecisionOrchestrator.res, tt.mockWorkflowDecisionOrchestrator.err)
			}

			if tt.mockUpdateWorkflowDecisionOrchestrator.enable {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, tt.mockUpdateWorkflowDecisionOrchestrator.req).
					Return(tt.mockUpdateWorkflowDecisionOrchestrator.err)
			}

			if tt.mockPublishWorkflowUpdateEventV2.enable {
				env.OnActivity(string(epifitemporal.PublishWorkflowUpdateEventV2), mock.Anything, tt.mockPublishWorkflowUpdateEventV2.req).
					Return(tt.mockPublishWorkflowUpdateEventV2.err)
			}

			if tt.mockInitiateUpdateSchemeAtVendor.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateUpdateSchemeAtVendor.req).
					Return(tt.mockInitiateUpdateSchemeAtVendor.err)
			}

			if tt.mockUpdateSchemeAtVendor.enable {
				env.OnActivity(string(savingsNs.UpdateSchemeAtVendor), mock.Anything, tt.mockUpdateSchemeAtVendor.req).
					Return(tt.mockUpdateSchemeAtVendor.res, tt.mockEnquireSchemeAtVendor.err)
			}

			if tt.mockUpdateUpdateSchemeAtVendor.enable {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, tt.mockUpdateUpdateSchemeAtVendor.req).
					Return(tt.mockUpdateUpdateSchemeAtVendor.err)
			}

			if tt.mockInitiateEnquireSchemeAtVendor.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateEnquireSchemeAtVendor.req).
					Return(tt.mockInitiateEnquireSchemeAtVendor.err)
			}

			if tt.mockEnquireSchemeAtVendor.enable {
				env.OnActivity(string(savingsNs.EnquireSchemeAtVendor), mock.Anything, tt.mockEnquireSchemeAtVendor.req).
					Return(tt.mockEnquireSchemeAtVendor.res, tt.mockEnquireSchemeAtVendor.err)
			}

			if tt.mockUpdateEnquireSchemeAtVendor.enable {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, tt.mockUpdateEnquireSchemeAtVendor.req).
					Return(tt.mockUpdateEnquireSchemeAtVendor.err)
			}

			if tt.mockInitiateUpdateSchemeInDB.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateUpdateSchemeInDB.req).
					Return(tt.mockInitiateUpdateSchemeInDB.err)
			}

			if tt.mockUpdateSchemeInDB.enable {
				env.OnActivity(string(savingsNs.UpdateSchemeInDB), mock.Anything, mock.Anything).
					Return(tt.mockUpdateSchemeInDB.res, tt.mockUpdateSchemeInDB.err)
			}

			if tt.mockUpdateUpdateSchemeInDB.enable {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, tt.mockUpdateUpdateSchemeInDB.req).
					Return(tt.mockUpdateUpdateSchemeInDB.err)
			}

			if tt.mockInitiateRemoveRequestIdFromSkuInfo.enable {
				env.OnActivity(string(epifitemporal.InitiateWorkflowStageV2), mock.Anything, tt.mockInitiateRemoveRequestIdFromSkuInfo.req).
					Return(tt.mockInitiateRemoveRequestIdFromSkuInfo.err)
			}

			if tt.mockRemoveRequestIdFromSkuInfo.enable {
				env.OnActivity(string(savingsNs.RemoveRequestIdFromSkuInfo), mock.Anything, mock.Anything).
					Return(tt.mockRemoveRequestIdFromSkuInfo.res, tt.mockRemoveRequestIdFromSkuInfo.err)
			}

			if tt.mockUpdateRemoveRequestIdFromSkuInfo.enable {
				env.OnActivity(string(epifitemporal.UpdateWorkflowStage), mock.Anything, tt.mockUpdateRemoveRequestIdFromSkuInfo.req).
					Return(tt.mockUpdateRemoveRequestIdFromSkuInfo.err)
			}

			env.ExecuteWorkflow(savingsNs.SchemeChange, tt.req)
			assert.True(t, env.IsWorkflowCompleted())

			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("SchemeChange() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			env.AssertExpectations(t)
		})
	}
}
