package job_processor

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"time"

	"github.com/aws/aws-sdk-go/aws"         //nolint:depguard
	"github.com/aws/aws-sdk-go/aws/session" //nolint:goimports,depguard
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	consumerPb "github.com/epifi/gamma/api/recurringpayment/consumer"
	"github.com/epifi/gamma/scripts/Pay/pay-god-script/config"
	"go.uber.org/zap" //nolint:goimports
	"google.golang.org/genproto/googleapis/type/date"

	sqsPkg "github.com/epifi/be-common/pkg/aws/sqs"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/gocarina/gocsv"
	"google.golang.org/grpc/metadata" //nolint:goimports
)

type FetchEnachFailedTransactionsJob struct {
	fromDate  string
	toDate    string
	csvPath   string
	conf      *config.Config
	publisher queue.Publisher
}

type FetchEnachFailedTransactionsArgs struct {
	FromDate string `json:"from_date"`
	ToDate   string `json:"to_date"`
}

// Define struct for CSV rows
// Add other fields if needed
type EnachFailedTxnRow struct {
	ActorID string `csv:"actor_id"`
}

func NewFetchEnachFailedTransactionsJob(conf *config.Config) (*FetchEnachFailedTransactionsJob, func(), error) {
	// Initialize AWS session and SQS publisher here
	awsSession, err := session.NewSession(&aws.Config{
		Region: aws.String(conf.Aws.Region),
	})
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to create AWS session: %w", err)
	}

	sqsClient := sqsPkg.InitSQSClient(awsSession)
	publisher, err := sqsPkg.NewPublisherWithConfig(conf.FetchAndCreateFailedEnachTransactionPublisher, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		return nil, func() {}, fmt.Errorf("failed to initialize publisher: %w", err)
	}

	return &FetchEnachFailedTransactionsJob{
		conf:      conf,
		publisher: publisher,
	}, func() {}, nil
}

func (j *FetchEnachFailedTransactionsJob) ParseAndStoreArgs(input *JobRequest) error {
	var args FetchEnachFailedTransactionsArgs
	if err := json.Unmarshal([]byte(input.Args1), &args); err != nil {
		return fmt.Errorf("failed to parse Args1 as JSON: %w", err)
	}
	if args.FromDate == "" || args.ToDate == "" {
		return fmt.Errorf("from_date and to_date are required in Args1")
	}
	j.fromDate = args.FromDate
	j.toDate = args.ToDate
	j.csvPath = input.Args3
	if j.csvPath == "" {
		return fmt.Errorf("CSV file path must be provided in Args3 (use -FileInput1)")
	}
	return nil
}

func (j *FetchEnachFailedTransactionsJob) DoJob(ctx context.Context) error {
	defer func() { _ = logger.Log.Sync() }()

	file, err := os.Open(j.csvPath)
	if err != nil {
		return fmt.Errorf("failed to open CSV file: %w", err)
	}
	defer func(file *os.File) {
		err := file.Close()
		if err != nil {
			logger.Error(ctx, "failed to close file", zap.Error(err))
		}
	}(file)

	var rows []*EnachFailedTxnRow
	if err := gocsv.UnmarshalFile(file, &rows); err != nil {
		return fmt.Errorf("failed to read CSV: %w", err)
	}
	if len(rows) == 0 {
		return fmt.Errorf("CSV must have at least one data row")
	}

	fromDateParsed, err := time.Parse("2006-01-02", j.fromDate)
	if err != nil {
		return fmt.Errorf("invalid from_date: %w", err)
	}
	toDateParsed, err := time.Parse("2006-01-02", j.toDate)
	if err != nil {
		return fmt.Errorf("invalid to_date: %w", err)
	}

	fromDate := &date.Date{
		Year:  int32(fromDateParsed.Year()),  //nolint:gosec
		Month: int32(fromDateParsed.Month()), //nolint:gosec
		Day:   int32(fromDateParsed.Day()),   //nolint:gosec
	}
	toDate := &date.Date{
		Year:  int32(toDateParsed.Year()),  //nolint:gosec
		Month: int32(toDateParsed.Month()), //nolint:gosec
		Day:   int32(toDateParsed.Day()),   //nolint:gosec
	}

	var failed []string
	for i, row := range rows {
		actorId := row.ActorID
		// Create a new context with a fresh trace-id and actor-id for each iteration
		actorCtx := epificontext.WithTraceId(ctx, metadata.MD{})
		actorCtx = epificontext.CtxWithActorId(actorCtx, actorId)

		msg := &consumerPb.FetchAndCreateFailedEnachTransactionsRequest{
			ActorId:  actorId,
			FromDate: fromDate,
			ToDate:   toDate,
		}
		_, err := j.publisher.Publish(actorCtx, msg)
		if err != nil {
			logger.Error(actorCtx, "Failed to publish", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			failed = append(failed, actorId)
		} else {
			logger.Info(actorCtx, "Published NACH failure fetch request", zap.String(logger.ACTOR_ID_V2, actorId))
		}
		if (i+1)%100 == 0 {
			logger.Info(ctx, "Processed batch", zap.Int("Processed", i+1))
		}
	}
	if len(failed) > 0 {
		logger.Info(ctx, "Failed actor_ids", zap.Int("failed actorIds Count", len(failed)))
	} else {
		logger.Info(ctx, "All actor_ids published successfully")
	}
	return nil
}
