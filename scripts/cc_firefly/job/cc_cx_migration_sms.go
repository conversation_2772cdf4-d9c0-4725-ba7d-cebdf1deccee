package job

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/samber/lo"
	"google.golang.org/grpc/metadata"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	uuid2 "github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
	"google.golang.org/genproto/googleapis/type/date"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commsPb "github.com/epifi/gamma/api/comms"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	usersPb "github.com/epifi/gamma/api/user"
	accountingDao "github.com/epifi/gamma/firefly/accounting/dao"
	"github.com/epifi/gamma/firefly/dao"
)

type CcCxMigrationSmsJob struct {
	ccDao        dao.CreditCardDao
	ccAccountDao accountingDao.CreditAccountDao
	userClient   usersPb.UsersClient
	commsClient  commsPb.CommsClient
}

func NewCcCxMigrationSmsJob(ccDao dao.CreditCardDao, ccAccountDao accountingDao.CreditAccountDao,
	userClient usersPb.UsersClient, commsClient commsPb.CommsClient) *CcCxMigrationSmsJob {
	return &CcCxMigrationSmsJob{
		ccDao:        ccDao,
		ccAccountDao: ccAccountDao,
		userClient:   userClient,
		commsClient:  commsClient,
	}
}

type CcCxMigrationSmsJobArgs struct {
	ContactNumber      string   `json:"ContactNumber"`
	EmailUserName      string   `json:"EmailUserName"`
	EmailDomainName    string   `json:"EmailDomainName"`
	Date               *Date    `json:"Date"`
	ActorId            string   `json:"ActorId"`            // Optional, executes only for the given actor ID. if not present, runs for all active credit card accounts
	ExcludedActorsList []string `json:"ExcludedActorsList"` // [\"actor_1\",\"actor_2\"]
}

type Date struct {
	Year  int32 `json:"Year"`
	Month int32 `json:"Month"`
	Day   int32 `json:"Day"`
}

// sample args -
//  {\"ContactNumber\":\"1800-296-1199\",\"EmailUserName\":\"federalficards\",\"EmailDomainName\":\"@federalbank.co.in\",\"Date\":{\"Year\":2025,\"Month\":6,\"Day\":13}}

func (c *CcCxMigrationSmsJob) Run(ctx context.Context, argString string) error {
	args := &CcCxMigrationSmsJobArgs{}
	jsonErr := json.Unmarshal([]byte(argString), args)
	if jsonErr != nil {
		return fmt.Errorf("error while unmarshalling arg string: %w", jsonErr)
	}

	if args.ActorId != "" {
		creditAccountsBatch, getErr := c.ccAccountDao.GetByActorId(ctx, args.ActorId, nil)
		if getErr != nil {
			return fmt.Errorf("error while fetching credit accounts for actor %s: %w", args.ActorId, getErr)
		}

		c.sendSmsForBatch(ctx, creditAccountsBatch, args)
		logger.Info(ctx, "SMS sent for actor", zap.String(logger.ACTOR_ID_V2, args.ActorId))
		return nil
	}

	var (
		ccBatchSize        = 600
		startTime, endTime = c.getTimeRangeForCardsFetch()
		lastSeenId         = ""
		fetchNextBatch     = true
	)

	for fetchNextBatch {
		logger.Info(ctx, "time range", zap.Time(logger.START_TIME, startTime), zap.Time(logger.END_TIME, endTime))
		logger.Info(ctx, "last seen id", zap.String("LastSeenId", lastSeenId))

		creditAccountsBatch, err := c.getCreditAccountsBatch(ctx, lastSeenId, startTime, endTime, ccBatchSize)
		if err != nil {
			if storageV2.IsRecordNotFoundError(err) {
				logger.Info(ctx, "no credit accounts left to process", zap.String("LastSeenId", lastSeenId))
				return nil
			}
			return err
		}
		if len(creditAccountsBatch) != 0 {
			lastSeenId = creditAccountsBatch[len(creditAccountsBatch)-1].GetId()
			startTime = creditAccountsBatch[len(creditAccountsBatch)-1].GetUpdatedAt().AsTime()
		}
		if len(creditAccountsBatch) < ccBatchSize {
			fetchNextBatch = false
		}
		c.sendSmsForBatch(ctx, creditAccountsBatch, args)
		time.Sleep(1 * time.Second)
	}
	return nil
}

func (c *CcCxMigrationSmsJob) sendSmsForBatch(ctx context.Context, ccAccountsBatch []*ffAccountsPb.CreditAccount, args *CcCxMigrationSmsJobArgs) {
	errorsList := make(chan error, len(ccAccountsBatch))
	grp, grpCtx := errgroup.WithContext(ctx)
	limiter := rate.NewLimiter(rate.Every(1*time.Second/5), 2)
	grp.SetLimit(5)

	for _, acc := range ccAccountsBatch {
		// TODO(obed): to re-check this.
		if len(args.ExcludedActorsList) > 0 && lo.Contains(args.ExcludedActorsList, acc.GetActorId()) {
			logger.Info(ctx, "skipping actor", zap.String(logger.ACTOR_ID_V2, acc.GetActorId()))
			continue
		}

		if err := limiter.Wait(ctx); err != nil {
			logger.Error(ctx, "error while rate limiting")
			errorsList <- err
			continue
		}
		grp.Go(func() error {
			actorCtx := epificontext.WithTraceId(grpCtx, metadata.MD{})
			actorCtx = epificontext.CtxWithActorId(actorCtx, acc.GetActorId())

			if er := c.sendSms(actorCtx, acc.GetActorId(), acc.GetId(), args); er != nil {
				errorsList <- er
			}
			return nil
		})
	}
	if waitErr := grp.Wait(); waitErr != nil {
		logger.Error(ctx, "failed to send sms for cc Batch", zap.Error(waitErr))
		return
	}

	close(errorsList)
	for err := range errorsList {
		logger.Error(ctx, "failed to send SMS for actor", zap.Error(err))
	}
}

func (c *CcCxMigrationSmsJob) sendSms(ctx context.Context, actorId string, creditAccountId string, args *CcCxMigrationSmsJobArgs) error {
	cc, ccErr := c.ccDao.GetByAccountId(ctx, creditAccountId, []ffEnumsPb.CreditCardFieldMask{
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE,
	})
	if ccErr != nil {
		if storageV2.IsRecordNotFoundError(ccErr) {
			logger.Info(ctx, "no credit cards found for account Id", zap.String("AccountId", creditAccountId))
			return nil
		}
		return fmt.Errorf("error while fetching credit card with account id %s, %w", creditAccountId, ccErr)
	}
	if lo.Contains([]ffEnumsPb.CardState{ffEnumsPb.CardState_CARD_STATE_CLOSED,
		ffEnumsPb.CardState_CARD_STATE_UNSPECIFIED}, cc[0].GetCardState()) {
		logger.Info(ctx, "credit card is closed", zap.String("CardState", cc[0].GetCardState().String()))
		return nil
	}

	userResp, err := c.userClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching user", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return rpcErr
	}

	commsResp, err := c.commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_GUARANTEED,
		Medium: commsPb.Medium_SMS,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{
			UserId: userResp.GetUser().GetId(),
		},
		Message: &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_CreditCardCxSupportDetailsUpdateSmsOption{
						CreditCardCxSupportDetailsUpdateSmsOption: &commsPb.CreditCardCxSupportDetailsUpdateSmsOption{
							SmsType: commsPb.SmsType_CREDIT_CARD_CX_SUPPORT_DETAILS_UPDATE_SMS,
							Option: &commsPb.CreditCardCxSupportDetailsUpdateSmsOption_CreditCardCxSupportDetailsUpdateSmsOptionV1{
								CreditCardCxSupportDetailsUpdateSmsOptionV1: &commsPb.CreditCardCxSupportDetailsUpdateSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									Date: datetime.DateToTimestamp(&date.Date{
										Year:  args.Date.Year,
										Month: args.Date.Month,
										Day:   args.Date.Day,
									}, datetime.IST),
									ContactNumber:   args.ContactNumber,   // 1800-296-1199
									EmailUserName:   args.EmailUserName,   // federalficards
									EmailDomainName: args.EmailDomainName, // @federalbank.co.in
								},
							},
						},
					},
				},
			},
		},
		ExternalReferenceId: uuid2.NewString(),
	})
	if rpcErr := epifigrpc.RPCError(commsResp, err); rpcErr != nil {
		logger.Error(ctx, "error while sending comms for user",
			zap.Error(rpcErr), zap.String(logger.CUSTOMER_ID, actorId))
		return rpcErr
	}
	logger.Info(ctx, "SMS sent successfully for actor")
	return nil
}

func (c *CcCxMigrationSmsJob) getCreditAccountsBatch(ctx context.Context, lastSeenId string, startTime, endTime time.Time, batchSize int) ([]*ffAccountsPb.CreditAccount, error) {
	creditAccounts, err := c.ccAccountDao.GetBetweenUpdatedAtInBatches(ctx, &startTime, &endTime, lastSeenId, batchSize)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no card request details found", zap.Error(err))
		return nil, nil
	case err != nil:
		logger.Error(ctx, "failed to fetch credit accounts", zap.Error(err))
		return nil, err
	}
	return creditAccounts, nil
}

func (c *CcCxMigrationSmsJob) getTimeRangeForCardsFetch() (time.Time, time.Time) {
	startTime := time.Date(2022, 12, 26, 0, 0, 0, 0, datetime.IST)
	endTime := datetime.EndOfDay(timestampPb.Now().AsTime().AddDate(0, 0, 1))
	return startTime, endTime
}
