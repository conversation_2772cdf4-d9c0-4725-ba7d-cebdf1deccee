package job

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"

	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	userPb "github.com/epifi/gamma/api/user"
	vgCustomerPb "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
)

type FetchCustJob struct {
	vgClient   vgCustomerPb.CustomerClient
	bcClient   bankcust.BankCustomerServiceClient
	authClient authPb.AuthClient
	userClient userPb.UsersClient
}

// sample args - {\"ActorId\":\"actor_1\",\"ExcludeDeviceToken\":\"TRUE\"}

type FetchCustJobArgs struct {
	ActorId            string `json:"ActorId"`
	ExcludeDeviceToken string `json:"ExcludeDeviceToken"`
}

func NewFetchCustJob(vgClient vgCustomerPb.CustomerClient, bcClient bankcust.BankCustomerServiceClient,
	authClient authPb.AuthClient, userClient userPb.UsersClient) *FetchCustJob {
	return &FetchCustJob{
		vgClient:   vgClient,
		bcClient:   bcClient,
		authClient: authClient,
		userClient: userClient,
	}
}

func (f *FetchCustJob) Run(ctx context.Context, argsString string) error {
	args := &FetchCustJobArgs{}
	err := json.Unmarshal([]byte(argsString), args)
	if err != nil {
		return err
	}

	userId, phNumber, err := f.getUser(ctx, args.ActorId)
	if err != nil {
		return err
	}

	customerId, err := f.getCustomerId(ctx, args.ActorId)
	if err != nil {
		logger.Error(ctx, "Error in getting id of customer", zap.Error(err))
		return err
	}
	deviceId, deviceToken, err := f.getDeviceAuthDetails(ctx, args.ActorId)
	if err != nil {
		logger.Error(ctx, "Error in getting Device Details", zap.Error(err))
		return err
	}

	if strings.EqualFold(strings.ToUpper(args.ExcludeDeviceToken), "TRUE") {
		deviceToken = ""
	}

	requestId := idgen.FederalRandomDigitsSequence(idgen.FederalFetchCustomerDetailsPrefix, 5)

	vgReq := &vgCustomerPb.FetchCustomerDetailsRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		Auth: &header.Auth{
			DeviceId:      deviceId,
			DeviceToken:   deviceToken,
			UserProfileId: userId,
			CustomerId:    customerId,
		},
		RequestId:   requestId,
		PhoneNumber: phNumber,
		ChannelType: vgCustomerPb.ChannelType_APP,
	}
	vgRes, vgErr := f.vgClient.FetchCustomerDetails(ctx, vgReq)
	if te := epifigrpc.RPCError(vgRes, vgErr); te != nil {
		return te
	}
	logger.Info(ctx, "customer details fetch successful", zap.String(logger.REQUEST_ID, vgReq.GetRequestId()))
	return nil
}

func (f *FetchCustJob) getCustomerId(ctx context.Context, actorId string) (string, error) {
	bankCustRes, bcErr := f.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(bankCustRes, bcErr); rpcErr != nil {
		logger.Error(ctx, "error while fetching bank customer", zap.Error(rpcErr))
		return "", rpcErr
	}
	return bankCustRes.GetBankCustomer().GetVendorCustomerId(), nil
}

func (f *FetchCustJob) getDeviceAuthDetails(ctx context.Context, actorId string) (deviceId, deviceToken string, err error) {
	getDeviceAuthResponse, err := f.authClient.GetDeviceAuth(ctx, &authPb.GetDeviceAuthRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(getDeviceAuthResponse, err); te != nil {
		if getDeviceAuthResponse.GetStatus().IsRecordNotFound() {
			return "", "", epifierrors.ErrRecordNotFound
		}
		return "", "",
			fmt.Errorf("error while calling auth service to fetch device details")
	}
	return getDeviceAuthResponse.GetDevice().GetDeviceId(),
		getDeviceAuthResponse.GetDeviceToken(),
		nil
}

func (f *FetchCustJob) getUser(ctx context.Context, actorId string) (string, *commontypes.PhoneNumber, error) {
	userReps, err := f.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if te := epifigrpc.RPCError(userReps, err); te != nil {
		return "", nil, te
	}
	return userReps.GetUser().GetId(), userReps.GetUser().GetProfile().GetPhoneNumber(), nil
}
