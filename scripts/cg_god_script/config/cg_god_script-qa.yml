Application:
  Environment: "qa"

Aws:
  Region: "ap-south-1"
  S3:
    BucketName: "epifi-qa-onsurity-backfill"

SalaryDb:
  AppName: "salaryprogram"
  DbType: "PGDB"
  StatementTimeout: 1s
  Name: "salaryprogram"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "qa/rds/postgres"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

TieringDb:
  DbType: "PGDB"
  AppName: "tiering"
  StatementTimeout: 1m
  Name: "tiering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "qa/rds/epifimetis/tiering_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

EpifiDb:
  DbType: "CRDB"
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/qa/"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

SalaryProgramStatusUpdateEventPublisher:
  TopicName: "qa-salaryprogram-status-update-topic"

SavingsTierUpdateEventPublisher:
  QueueName: "qa-savings-tier-update-event-consumer-queue"

Secrets:
  Ids:
    DbCredentials: "qa/rds/postgres/salaryprogram"
    TieringDbCredentials: "qa/rds/epifimetis/tiering_dev_user"

RedisClusters:
  TieringConnectedAccountRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 11
  TieringActorRedisStore:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 10
    HystrixCommand:
      CommandName: "actor_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
TieringInfoCache:
  Prefix: "TIERING:ATI:"
