// nolint:gosec
package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"golang.org/x/exp/maps"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

const (
	dbCredentials        = "DbCredentials"
	tieringDbCredentials = "TieringDbCredentials"
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	keyToIdMap := map[string]string{}

	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(configDirPath, "cg_god_script")
	if err != nil {
		return nil, fmt.Errorf("failed to load  config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to  unmarshal config: %w", err)
	}

	dbConfigList := []*cfg.DB{conf.EpifiDb, conf.SalaryDb, conf.TieringDb}
	// if conf.SimulatorDb != nil {
	// 	dbConfigList = append(dbConfigList, conf.SimulatorDb)
	// }
	secrets, loadSecretsErr := cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.Aws.Region, dbConfigList...)
	if loadSecretsErr != nil {
		return nil, fmt.Errorf("failed to load db secrets, %w", loadSecretsErr)
	}

	secrets2, err2 := cfg.LoadSecrets(keyToIdMap, conf.Application.Environment, conf.Aws.Region)
	if err2 != nil {
		return nil, fmt.Errorf("failed to load secrets, %w", err2)
	}
	maps.Copy(secrets, secrets2)

	// use secrets from this variable when needed in any job
	_ = secrets

	return conf, nil
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
// update db endpoint
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.SalaryDb, dbServerEndpoint)

	cfg.UpdatePGDBSecretValues(c.SalaryDb, c.Secrets, keyToSecret)

	cfg.UpdateDbEndpointInConfig(c.TieringDb, dbServerEndpoint)

	cfg.UpdatePGDBSecretValues(c.TieringDb, c.Secrets, keyToSecret)

	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.SalaryDb, c.Secrets.Ids[dbCredentials])
		return nil
	}
	if _, ok := keyToSecret[dbCredentials]; !ok {
		return fmt.Errorf("db username password not fetched from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.SalaryDb, keyToSecret[dbCredentials])

	if _, ok := keyToSecret[tieringDbCredentials]; !ok {
		return fmt.Errorf("db username password not fetched from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.TieringDb, keyToSecret[tieringDbCredentials])

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

type Config struct {
	Application *application
	// Aws                                     *cfg.AWS
	Aws                                     *Aws
	SalaryDb                                *cfg.DB
	TieringDb                               *cfg.DB
	EpifiDb                                 *cfg.DB
	Secrets                                 *cfg.Secrets
	SalaryProgramStatusUpdateEventPublisher *cfg.SnsPublisher
	SavingsTierUpdateEventPublisher         *cfg.SqsPublisher
	RedisClusters                           map[string]*cfg.RedisOptions
	TieringInfoCache                        *TieringInfoCacheConfig
}

type application struct {
	Name        string
	Environment string
}

type Aws struct {
	Region string
	S3     *S3
}

type S3 struct {
	BucketName string
}

type TieringInfoCacheConfig struct {
	Prefix string
}
