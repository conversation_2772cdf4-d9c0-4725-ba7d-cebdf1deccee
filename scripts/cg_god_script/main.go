package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/aws/session"
	"github.com/epifi/be-common/pkg/aws/sns"
	"github.com/epifi/be-common/pkg/aws/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	dao3 "github.com/epifi/gamma/savings/extacct/dao"
	dao2 "github.com/epifi/gamma/tiering/dao"

	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/gamma/api/bankcust"
	caPb "github.com/epifi/gamma/api/connected_account"
	orderPb "github.com/epifi/gamma/api/order"
	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	"github.com/epifi/gamma/salaryprogram/dao"
	dao4 "github.com/epifi/gamma/salaryprogram/healthinsurance/dao"
	"github.com/epifi/gamma/scripts/cg_god_script/config"
)

var (
	inputJob = flag.String("JobName", "", "job name, refer to jobNames for accepted values")
	jobArgs1 = flag.String("Args1", "", "input args for the job (refer to job requirements)")
	jobArgs2 = flag.String("Args2", "", "input args for the job (refer to job requirements)")
	jobArgs3 = flag.String("Args3", "", "input args for the job (refer to job requirements)")
	filePath = flag.String("filePath", "", "input file path for the job (refer to job requirements)")
)

type JobType uint32

const (
	JobUnspecified                      JobType = 0
	JobAaSalaryAutoUpgrade              JobType = 1
	JobHealthInsuranceBackfillOnsurity  JobType = 2
	JobAaSalaryReevaluateSegmentUsers   JobType = 3
	JobThirdPartyAccountTransferEnquiry JobType = 4
	JobPublishSavingsTierUpdateEvent    JobType = 5
	JobBypassRewardAbuserCheck          JobType = 7
	JobActorBaseTierMigration           JobType = 8
	JobUpdateSalaryActivationActiveTill JobType = 9
	JobResetBankAccountVerification     JobType = 10
)

var (
	jobNames = map[string]JobType{
		"AA_SALARY_AUTO_UPGRADE":               JobAaSalaryAutoUpgrade,
		"HEALTH_INSURANCE_BACKFILL_ONSURITY":   JobHealthInsuranceBackfillOnsurity,
		"AA_SALARY_REEVALUATE_SEGMENT_USERS":   JobAaSalaryReevaluateSegmentUsers,
		"THIRD_PARTY_ACCOUNT_TRANSFER_ENQUIRY": JobThirdPartyAccountTransferEnquiry,
		"PUBLISH_SAVINGS_TIER_UPDATE_EVENT":    JobPublishSavingsTierUpdateEvent,
		"BYPASS_REWARD_ABUSER_CHECK":           JobBypassRewardAbuserCheck,
		"ACTOR_BASE_TIER_MIGRATION":            JobActorBaseTierMigration,
		"UPDATE_SALARY_ACTIVATION_ACTIVE_TILL": JobUpdateSalaryActivationActiveTill,
		"RESET_BANK_ACCOUNT_VERIFICATIONS":     JobResetBankAccountVerification,
	}
)

type JobProcessor interface {
	DoJob(context.Context, *JobRequest) error
}

// JobRequest data that is sent to each job for processing
type JobRequest struct {
	Job      JobType
	Args1    string
	Args2    string
	Args3    string
	FilePath string
}

// nolint: funlen
func main() {
	flag.Parse()

	fmt.Printf("\n JOB: '%v' \n", *inputJob)
	job := jobNames[*inputJob]
	if job == JobUnspecified {
		fmt.Printf("\n INVALID INPUT JOB: '%v' \n", *inputJob)
		defer os.Exit(1)
		return
	}

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	caServiceConn := epifigrpc.NewConnByService(cfg.CONNECTED_ACC_SERVICE)
	defer epifigrpc.CloseConn(caServiceConn)
	caClient := caPb.NewConnectedAccountClient(caServiceConn)

	tieringServiceConn := epifigrpc.NewConnByService(cfg.TIERING_SERVICE)
	defer epifigrpc.CloseConn(tieringServiceConn)
	tieringClient := tiering.NewTieringClient(tieringServiceConn)

	salaryProgramSvcConn := epifigrpc.NewConnByService(cfg.SALARY_PROGRAM_SERVICE)
	defer epifigrpc.CloseConn(salaryProgramSvcConn)
	salaryProgramClient := salaryprogramPb.NewSalaryProgramClient(salaryProgramSvcConn)

	segmentServiceConn := epifigrpc.NewConnByService(cfg.SEGMENT_SERVICE)
	defer epifigrpc.CloseConn(segmentServiceConn)
	segmentServiceClient := segmentPb.NewSegmentationServiceClient(segmentServiceConn)

	orderServiceConn := epifigrpc.NewConnByService(cfg.ORDER_SERVICE)
	defer epifigrpc.CloseConn(orderServiceConn)
	orderServiceClient := orderPb.NewOrderServiceClient(orderServiceConn)

	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userConn)
	userClient := userPb.NewUsersClient(userConn)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	vgAccountsClient := accounts.NewAccountsClient(vgConn)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)
	savingsClinet := savingsPb.NewSavingsClient(savingsConn)
	extAcctClient := extacct.NewExternalAccountsClient(savingsConn)

	bankCustConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	defer epifigrpc.CloseConn(bankCustConn)
	bankCustClient := bankcust.NewBankCustomerServiceClient(bankCustConn)

	// Add script specific timeout inside the job.
	ctx, cancel := context.WithTimeout(context.Background(), 12*time.Hour)
	defer cancel()

	conf, err := config.Load()
	if err != nil {
		logger.Fatal("failed to load config", zap.Error(err))
	}

	db, err := storageV2.NewGormDB(conf.SalaryDb)
	if err != nil {
		logger.Panic("Failed to load DB", zap.Error(err))
	}

	tieringDb, err := storageV2.NewGormDB(conf.TieringDb)
	if err != nil {
		logger.Panic("Failed to load Tiering DB", zap.Error(err))
	}

	epifiCRDB, err := storageV2.NewGormDB(conf.EpifiDb)
	if err != nil {
		logger.Panic("Failed to load EpifiCRDB", zap.Error(err))
	}

	storageV2.InitDefaultCRDBTransactionExecutor(epifiCRDB)

	// init sqs client
	awsSess, err := session.NewSession(conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		logger.Panic("failed to initialize aws session", zap.Error(err))
		return
	}
	s3Client := s3.NewClient(awsSess, conf.Aws.S3.BucketName)
	sqsClient := sqs.InitSQSClient(awsSess)

	salaryProgramStatusUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(conf.SalaryProgramStatusUpdateEventPublisher, awsSess, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to create sns publisher", zap.Error(err))
	}

	savingsTierUpdateEventPublisher, err := sqs.NewPublisherWithConfig(conf.SavingsTierUpdateEventPublisher, sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to create sqs publisher", zap.Error(err))
	}

	txnExecutor := storageV2.NewGormTxnExecutor(db)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)

	salaryRegistrationDao := dao.NewPGDBRegistrationDao(db, domainIdGenerator)
	salaryRegistrationStageDetailsDao := dao.NewPGDBRegistrationStageDetailsDao(db)
	salaryEstimationsDao := dao.NewSalaryEstimationsDao(db)
	salaryTxnVerificationRequestDao := dao.NewAaSalTxnVerificationReqDao(db)
	salaryProgramActivationHistoryDao := dao.NewPGDBSalaryProgramActivationHistoryDao(db)
	policyIssuanceReqDao := dao4.NewPGDBPolicyIssuanceRequestDao(db)
	policyDetailsDao := dao4.NewPGDBPolicyDetailsDao(db)
	aaSalaryCriteriaDao := dao.NewAaSalaryCriteriaDao(db)
	AaSalTxnVerificationReqDao := dao.NewAaSalTxnVerificationReqDao(db)
	tieringInfoRedisStore := cache.NewRedisCacheStorage(storage.NewRedisClientFromConfig(conf.RedisClusters["TieringActorRedisStore"], false))
	ActorTierInfoDao := dao2.NewActorTierInfoImpl(tieringDb)

	tieringConnectedAccountRedisStore := storage.NewRedisClientFromConfig(conf.RedisClusters["TieringConnectedAccountRedisStore"], false)

	var jobProcessors = map[JobType]JobProcessor{
		JobAaSalaryAutoUpgrade: &AaSalaryAutoUpgrade{
			caClient:                                caClient,
			segmentClient:                           segmentServiceClient,
			salaryProgramClient:                     salaryProgramClient,
			orderServiceClient:                      orderServiceClient,
			registrationDao:                         salaryRegistrationDao,
			registrationStageDetailsDao:             salaryRegistrationStageDetailsDao,
			aaSalaryVerificationDao:                 salaryTxnVerificationRequestDao,
			salaryActivationDao:                     salaryProgramActivationHistoryDao,
			salaryEstimationsDao:                    salaryEstimationsDao,
			txnExecutor:                             txnExecutor,
			salaryProgramStatusUpdateEventPublisher: salaryProgramStatusUpdateEventPublisher,
		},
		JobHealthInsuranceBackfillOnsurity: &HealthInsuranceBackfillOnsurity{
			userClient:           userClient,
			conf:                 conf,
			policyDetailsDao:     policyDetailsDao,
			policyIssuanceReqDao: policyIssuanceReqDao,
			txnExecutor:          txnExecutor,
			s3Client:             s3Client,
		},
		JobAaSalaryReevaluateSegmentUsers: &AaSalaryReevaluateSegmentUsers{
			segmentClient:                      segmentServiceClient,
			salaryProgramClient:                salaryProgramClient,
			salaryEstimationsDao:               salaryEstimationsDao,
			salaryRegistrationDao:              salaryRegistrationDao,
			aaSalTxnVerificationReqDao:         AaSalTxnVerificationReqDao,
			salaryProgramActivationHistoryDao:  salaryProgramActivationHistoryDao,
			aaSalaryCriteriaDao:                aaSalaryCriteriaDao,
			txnExecutor:                        txnExecutor,
			salaryProgramStatusUpdatePublisher: salaryProgramStatusUpdateEventPublisher,
		},
		JobThirdPartyAccountTransferEnquiry: &ThirdPartyAccountTransferEnquiry{
			accountsVgClient: vgAccountsClient,
			extAcctClient:    extAcctClient,
			savingsClient:    savingsClinet,
			bankCustClient:   bankCustClient,
		},
		JobPublishSavingsTierUpdateEvent: &PublishSavingsTierUpdateEvent{
			savingsTierUpdateEventPublisher: savingsTierUpdateEventPublisher,
			tieringClient:                   tieringClient,
		},
		JobBypassRewardAbuserCheck: &BypassRewardAbuserCheck{
			redisClient: tieringConnectedAccountRedisStore,
		},
		JobActorBaseTierMigration: &actorBaseTierMigrationJob{
			actorTierInfoDao: ActorTierInfoDao,
			tieringClient:    tieringClient,
			usersClient:      userClient,
			cacheClient:      tieringInfoRedisStore,
			cachePrefix:      conf.TieringInfoCache.Prefix,
		},
		JobUpdateSalaryActivationActiveTill: &UpdateSalaryActivationActiveTill{
			salaryProgramActivationHistoryDao: salaryProgramActivationHistoryDao,
			db:                                db,
		},
		JobResetBankAccountVerification: &JobResetBankAccountVerificationJob{
			bankAccountVerificationsDao: dao3.NewBankAccountVerificationsCrdb(epifiCRDB),
			extAcctClient:               extAcctClient,
		},
	}

	jobProcessor := jobProcessors[job]
	if jobProcessor == nil {
		fmt.Printf("\n JOB PROCESSOR NOT FOUND FOR JOB: '%v' \n", job)
		defer os.Exit(1)
		return
	}

	if err = jobProcessor.DoJob(ctx, &JobRequest{
		Job:      job,
		Args1:    *jobArgs1,
		Args2:    *jobArgs2,
		Args3:    *jobArgs3,
		FilePath: *filePath,
	}); err != nil {
		logger.Error(ctx, fmt.Sprintf("error in job: %v", job), zap.Error(err))
		defer os.Exit(1)
		return
	}
}
