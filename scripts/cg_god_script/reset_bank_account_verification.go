package main

import (
	"context"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/metadata"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/savings/extacct/dao"
)

type JobResetBankAccountVerificationJob struct {
	extAcctClient               extacct.ExternalAccountsClient
	actorIds                    []string
	failedActorsMap             map[string][]string
	bankAccountVerificationsDao dao.BankAccountVerificationsDao
}

// ParseAndStoreArgs parses input arguments for actor IDs
// Tries to parse the actorIds from Args1 e.g. "actorId1 , actorId2 , actorId3"
func (j *JobResetBankAccountVerificationJob) ParseAndStoreArgs(request *JobRequest) error {
	if request.Args1 == "" {
		return fmt.Errorf("Args1 (comma-separated actor ids) is required")
	}
	ids := getActorIdsAfterCleaning(request.Args1, ",")

	// this check is a guardrail to prevent abuse
	if len(ids) == 0 || len(ids) > 10 {
		return fmt.Errorf("must provide between 1 and 10 actor ids")
	}
	j.actorIds = ids
	return nil
}

// DoJob
// Args1 ( mandatory ): comma separated actor ids
// This job soft deletes the entries in BankAccountVerification table for the given list of actors having account verification retried left = 0
func (j *JobResetBankAccountVerificationJob) DoJob(ctx context.Context, request *JobRequest) error {
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	j.failedActorsMap = make(map[string][]string)
	if err := j.ParseAndStoreArgs(request); err != nil {
		return err
	}

	for _, actorId := range j.actorIds {
		actorCtx := epificontext.CtxWithActorId(ctx, actorId)
		resp, err := j.extAcctClient.GetBankAccounts(actorCtx, &extacct.GetBankAccountsRequest{ActorId: actorId})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			j.failedActorsMap["GetBankAccountsError"] = append(j.failedActorsMap["GetBankAccountsError"], actorId)
			logger.Error(actorCtx, "Failed to fetch bank account verifications via RPC", zap.Error(err))
			continue
		}
		// resp.BankAccounts contains the BAVs with OVERALL_STATUS_SUCCESS
		// if for an actor there's already an entry with OVERALL_STATUS_SUCCESS, avoid soft deleting as already successful entry is present.
		if len(resp.GetBankAccounts()) > 0 {
			j.failedActorsMap["HasVerifiedBankAccounts"] = append(j.failedActorsMap["HasVerifiedBankAccounts"], actorId)
			logger.Error(actorCtx, "Actor has verified bank accounts, skipping reset")
			continue
		}
		if resp.GetAccVerificationRetriesLeft() > 0 && resp.GetNameMatchRetriesLeft() > 0 {
			j.failedActorsMap["RetriesLeftNotZero"] = append(j.failedActorsMap["RetriesLeftNotZero"], actorId)
			logger.Error(actorCtx, "Account verification retries left is not zero, skipping", zap.Int32("retries_left", resp.GetAccVerificationRetriesLeft()))
			continue
		}
		bavs := resp.GetBankAccountVerifications()
		if len(bavs) == 0 {
			logger.Info(actorCtx, "No bank account verifications to soft delete for actor")
			continue
		}
		currTime := time.Now().Unix()
		for _, bav := range bavs {
			bav.DeletedAtUnix = currTime
			err := j.bankAccountVerificationsDao.UpdateByFieldMask(actorCtx, bav, []extacct.BankAccountVerificationFieldMask{extacct.BankAccountVerificationFieldMask_FIELD_MASK_DELETED_AT_UNIX})
			if err != nil {
				j.failedActorsMap[actorId] = append(j.failedActorsMap[actorId], fmt.Sprintf("error while soft deleting BAV id %s: %v", bav.GetId(), err))
				logger.Error(actorCtx, "Failed to soft delete BAV", zap.String("bav_id", bav.GetId()), zap.Error(err))
				continue
			}
			logger.Info(actorCtx, "Successfully soft deleted BAV for actor", zap.String("bav_id", bav.GetId()))
		}
	}
	if len(j.failedActorsMap) > 0 {
		var sb strings.Builder
		sb.WriteString("failed actorIds by error type:\n")
		for key, ids := range j.failedActorsMap {
			sb.WriteString(fmt.Sprintf("  %s (count=%d):\n    %v\n", key, len(ids), ids))
		}
		logger.Error(ctx, sb.String())
		return fmt.Errorf("failed for some actor ids")
	}
	logger.Info(ctx, "All actors' bank account verifications soft deleted successfully.")
	return nil
}
