package main

import (
	"context"
	"fmt"
	"time"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	userPb "github.com/epifi/gamma/api/user"
)

type RevokeAppAccessReVkycNotDone struct {
	usersClient userPb.UsersClient
}

func (r *RevokeAppAccessReVkycNotDone) DoJob(ctx context.Context, req *JobRequest) error {
	var (
		actorIds     []string
		failedActors []string
	)
	actorIds = splitCSV(req.Args1)

	for _, actorId := range actorIds {
		fmt.Printf("\nProcessing for actor id : %v", actorId)
		userRes, userErr := r.usersClient.GetUser(ctx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		})
		if err := epifigrpc.RPCError(userRes, userErr); err != nil {
			logger.Error(ctx, "failed to get user info", zap.Error(err))
			failedActors = append(failedActors, actorId)
			continue
		}

		user := userRes.GetUser()
		user.AccessRevokeDetails = &userPb.AccessRevokeDetails{
			AccessRevokeStatus: userPb.AccessRevokeStatus_ACCESS_REVOKE_STATUS_BLOCKED,
			Reason:             userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_RE_VKYC_NOT_COMPLETED,
			Remarks:            "Re VKYC not completed in given time line",
			UpdatedBy:          "Jenkins job",
			UpdatedAt:          timestampPb.New(time.Now()),
		}

		userUpdResp, updateErr := r.usersClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
			User: user,
			UpdateMask: []userPb.UserFieldMask{
				userPb.UserFieldMask_ACCESS_REVOKE_DETAILS,
			},
		})
		if err := epifigrpc.RPCError(userUpdResp, updateErr); err != nil {
			failedActors = append(failedActors, actorId)
			continue
		}
	}
	if len(failedActors) > 0 {
		fmt.Println("List of actorIds for which app access revoke failed:")
		printUsers(failedActors)
	}
	return nil
}
