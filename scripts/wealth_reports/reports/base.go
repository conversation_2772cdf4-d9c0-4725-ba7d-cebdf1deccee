package reports

import (
	"context"
	"time"
)

// ReportData contains information about the report
type ReportData struct {
	Title    string     // Title of the report
	Text     string     // Content of the report
	CsvRows  [][]string // CSV data for the report
	filename string     // filename for the CSV attachment
}

// Auditor interface defines the contract for all auditors
type Auditor interface {
	Audit(ctx context.Context, now time.Time) (bool, *ReportData, error)
}
