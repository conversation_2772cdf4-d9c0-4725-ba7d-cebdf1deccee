package reports

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
)

type USSSIPMonitorAuditor struct {
	ruleSubscriptionsDB *sql.DB
	actionExecutionsDB  *sql.DB
}

func USSSIPAuditor(ruleSubscriptionsDB, actionExecutionsDB *sql.DB) *USSSIPMonitorAuditor {
	return &USSSIPMonitorAuditor{
		ruleSubscriptionsDB: ruleSubscriptionsDB,
		actionExecutionsDB:  actionExecutionsDB,
	}
}

func (a *USSSIPMonitorAuditor) Audit(ctx context.Context, now time.Time) (bool, *ReportData, error) {
	// Query to fetch scheduled SIPs
	scheduleSIPQuery := `
		SELECT id
		FROM rule_subscriptions
		WHERE state = 'ACTIVE' and  rule_id = '26550dcd-01a0-466c-86e0-801aba9538af'
		AND CAST(json_extract_scalar(rule_param_values, '$.ruleParamValues.configuredDateOfMonth.intVal') AS INTEGER) = EXTRACT(DAY FROM CURRENT_DATE)
		AND  version_state = 'CURRENT' and CAST(created_at AS DATE) <> current_date`

	scheduledRows, err := a.ruleSubscriptionsDB.QueryContext(ctx, scheduleSIPQuery)
	if err != nil {
		logger.Error(ctx, "failed to query scheduled SIPs", zap.Error(err))
		return false, nil, err
	}
	defer func(scheduledRows *sql.Rows) {
		err := scheduledRows.Close()
		if err != nil {
			logger.Error(ctx, "failed to close scheduled SIP rows", zap.Error(err))
		}
	}(scheduledRows)

	scheduledIDs := make(map[string]struct{})
	for scheduledRows.Next() {
		var id string
		if err := scheduledRows.Scan(&id); err != nil {
			logger.Error(ctx, "failed to scan scheduled SIP row", zap.Error(err))
			return false, nil, err
		}
		scheduledIDs[id] = struct{}{}
	}

	// Query to fetch executed SIPs
	executedSIPQuery := `
		SELECT subscription_id
		FROM action_executions
		WHERE action_type = 'EXECUTE_US_STOCKS_SIP_ACTION'
		AND CAST(created_at AS DATE) = current_date`

	executedRows, err := a.actionExecutionsDB.QueryContext(ctx, executedSIPQuery)
	if err != nil {
		logger.Error(ctx, "failed to query scheduled SIPs", zap.Error(err))
		return false, nil, err
	}
	defer func(executedRows *sql.Rows) {
		err := executedRows.Close()
		if err != nil {
			logger.Error(ctx, "failed to close executed SIP rows", zap.Error(err))
		}
	}(executedRows)

	executedIDs := make(map[string]struct{})
	for executedRows.Next() {
		var id string
		if err := executedRows.Scan(&id); err != nil {
			logger.Error(ctx, "failed to scan executed SIP row", zap.Error(err))
			return false, nil, err
		}
		executedIDs[id] = struct{}{}
	}

	// Find missing executions
	var missingIDs []string
	for id := range scheduledIDs {
		if _, ok := executedIDs[id]; !ok {
			missingIDs = append(missingIDs, id)
		}
	}

	if len(missingIDs) == 0 {
		reportText := fmt.Sprintf("✅ USS SIP Execution Report for %s:\nAll scheduled SIPs were executed successfully.", now.Format("2006-01-02"))
		return true, &ReportData{
			Title: "USS Daily SIP Execution Report",
			Text:  reportText,
		}, nil
	}

	csvRows := [][]string{{"Subscription ID"}}
	for _, id := range missingIDs {
		csvRows = append(csvRows, []string{id})
	}

	reportText := fmt.Sprintf("⚠️ SIP Execution Report for %s:\n• Missing Executions: %d\n\n*Action Required:* The attached CSV contains SIPs that were scheduled but not executed.",
		now.Format("2006-01-02"), len(missingIDs))

	reportData := &ReportData{
		Title:    "USS Daily SIP Execution Report - Missing Executions",
		Text:     reportText,
		CsvRows:  csvRows,
		filename: "missing_sip_executions",
	}

	return false, reportData, nil
}
