package tiering

import (
	"context"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/tiering/config"
)

// FetchSherlockBanners returns banners to show to Sherlock agents for users
// based on their segment membership. Banners are checked in priority order.
func (s *Service) FetchSherlockBanners(ctx context.Context, req *sbPb.FetchSherlockBannersRequest) (*sbPb.FetchSherlockBannersResponse, error) {
	actorId := req.GetActorId()

	// Get tiering sherlock banners configuration
	bannersConfig := s.gconf.TieringSherlockBannersConfig()
	if len(bannersConfig.Banners) == 0 {
		return &sbPb.FetchSherlockBannersResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	// Check each banner in priority order (first enabled and matching banner wins)
	for _, bannerConfig := range bannersConfig.Banners {
		if !bannerConfig.IsEnabled {
			continue
		}

		if bannerConfig.EligibleSegmentId == "" {
			continue
		}

		// Check if user is in the eligible segment for this banner
		userInSegment, err := s.checkUserInSegment(ctx, actorId, bannerConfig.EligibleSegmentId)
		if err != nil {
			logger.Error(ctx, "error checking segment membership for banner",
				zap.Error(err),
				zap.String(logger.ACTOR_ID_V2, actorId),
				zap.String("bannerType", bannerConfig.BannerType),
				zap.String("segmentId", bannerConfig.EligibleSegmentId))
			continue // Skip this banner and try the next one
		}

		if userInSegment {
			// User is eligible for this banner, return it
			logger.Info(ctx, "Showing sherlock banner for user",
				zap.String(logger.ACTOR_ID_V2, actorId),
				zap.String("bannerType", bannerConfig.BannerType))
			return &sbPb.FetchSherlockBannersResponse{
				Status: rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{
					s.createBannerFromConfig(bannerConfig),
				},
			}, nil
		}
	}

	// No applicable banners found
	return &sbPb.FetchSherlockBannersResponse{
		Status: rpcPb.StatusRecordNotFound(),
	}, nil
}

// checkUserInSegment checks if a user belongs to a specific segment
func (s *Service) checkUserInSegment(ctx context.Context, actorId, segmentId string) (bool, error) {
	getSegmentResp, err := s.segmentationClient.IsMember(ctx, &segmentPb.IsMemberRequest{
		SegmentIds: []string{segmentId},
		ActorId:    actorId,
	})
	if rpcErr := epifigrpc.RPCError(getSegmentResp, err); rpcErr != nil {
		return false, rpcErr
	}

	membership := getSegmentResp.GetSegmentMembershipMap()[segmentId]
	if membership == nil {
		return false, nil
	}

	return membership.GetIsActorMember(), nil
}

// createBannerFromConfig creates a sherlock banner from banner configuration
func (s *Service) createBannerFromConfig(bannerConfig *config.SherlockBannerConfig) *sbPb.SherlockBanner {
	return &sbPb.SherlockBanner{
		StructureType: sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_STANDARD_TITLE_BODY,
		BannerContentV2: &sbPb.SherlockBannerContentV2{
			Content: &sbPb.SherlockBannerContentV2_TitleBodyContent{
				TitleBodyContent: &sbPb.SherlockBannerTitleBodyContent{
					Title: bannerConfig.Title,
					Body:  bannerConfig.Body,
				},
			},
		},
	}
}
