package tiering

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	rpcPb "github.com/epifi/be-common/api/rpc"
	sbPb "github.com/epifi/gamma/api/cx/sherlock_banners"
	segmentPb "github.com/epifi/gamma/api/segment"
	segmentMocks "github.com/epifi/gamma/api/segment/mocks"
)

func TestService_FetchSherlockBanners(t *testing.T) {
	t.Parallel()
	type mockStruct struct {
		mockSegmentationClient *segmentMocks.MockSegmentationServiceClient
	}

	type args struct {
		ctx   context.Context
		req   *sbPb.FetchSherlockBannersRequest
		mocks func(mockStruct *mockStruct)
	}

	tests := []struct {
		name    string
		args    args
		want    *sbPb.FetchSherlockBannersResponse
		wantErr bool
	}{
		{
			name: "success - user in eligible segment shows banner",
			args: args{
				ctx: context.Background(),
				req: &sbPb.FetchSherlockBannersRequest{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), &segmentPb.IsMemberRequest{
						SegmentIds: []string{"AMB_CHARGES_ELIGIBLE_USERS_DEV"},
						ActorId:    "actor-1",
					}).Return(&segmentPb.IsMemberResponse{
						Status: rpcPb.StatusOk(),
						SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{
							"AMB_CHARGES_ELIGIBLE_USERS_DEV": {
								IsActorMember: true,
							},
						},
					}, nil)
				},
			},
			want: &sbPb.FetchSherlockBannersResponse{
				Status: rpcPb.StatusOk(),
				Banners: []*sbPb.SherlockBanner{
					{
						StructureType: sbPb.SherlockBannerStructureType_SHERLOCK_BANNER_STRUCTURE_TYPE_STANDARD_TITLE_BODY,
						BannerContentV2: &sbPb.SherlockBannerContentV2{
							Content: &sbPb.SherlockBannerContentV2_TitleBodyContent{
								TitleBodyContent: &sbPb.SherlockBannerTitleBodyContent{
									Title: gconf.TieringSherlockBannersConfig().Banners[0].Title,
									Body:  "",
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "success - user not in eligible segment shows no banner",
			args: args{
				ctx: context.Background(),
				req: &sbPb.FetchSherlockBannersRequest{
					ActorId: "actor-1",
				},
				mocks: func(m *mockStruct) {
					m.mockSegmentationClient.EXPECT().IsMember(gomock.Any(), &segmentPb.IsMemberRequest{
						SegmentIds: []string{"AMB_CHARGES_ELIGIBLE_USERS_DEV"},
						ActorId:    "actor-1",
					}).Return(&segmentPb.IsMemberResponse{
						Status: rpcPb.StatusOk(),
						SegmentMembershipMap: map[string]*segmentPb.SegmentMembership{
							"AMB_CHARGES_ELIGIBLE_USERS_DEV": {
								IsActorMember: false,
							},
						},
					}, nil)
				},
			},
			want: &sbPb.FetchSherlockBannersResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		ctrl := gomock.NewController(t)
		mockSegmentationClient := segmentMocks.NewMockSegmentationServiceClient(ctrl)
		defer ctrl.Finish()
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				gconf:              gconf,
				segmentationClient: mockSegmentationClient,
			}
			m := &mockStruct{
				mockSegmentationClient: mockSegmentationClient,
			}
			tt.args.mocks(m)
			got, err := s.FetchSherlockBanners(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchSherlockBanners() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("FetchSherlockBanners() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
