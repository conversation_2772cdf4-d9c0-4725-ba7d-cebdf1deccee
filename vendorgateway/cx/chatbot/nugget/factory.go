package nugget

import (
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	nuggetPb "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget"
)

// NewNuggetRequest creates a new request for Nugget APIs depending on the
// type of the proto message.
func (s *Service) NewNuggetRequest(req proto.Message) vendorapi.SyncRequest {
	switch v := req.(type) {
	case *nuggetPb.FetchAccessTokenRequest:
		return &FetchAccessTokenReq{
			Method: http.MethodPost,
			Req:    v,
			Conf:   s.conf.Application().Nugget(),
		}
	default:
		logger.ErrorNoCtx("Unsupported request type for Nugget", zap.Any("request", v))
		return nil
	}
}
