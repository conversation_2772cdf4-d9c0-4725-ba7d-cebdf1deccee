package nugget

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	commonTypePb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/vendorapi"

	vendorNuggetPb "github.com/epifi/gamma/api/vendors/nugget"
	"github.com/epifi/gamma/vendorgateway/config"

	nuggetPb "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget"
)

// FetchAccessTokenReq provides functionality for adapting to Nugget's fetch access token API.
type FetchAccessTokenReq struct {
	Method string
	Req    *nuggetPb.FetchAccessTokenRequest
	Conf   *config.Nugget
}

func (f *FetchAccessTokenReq) Add(req *http.Request) *http.Request {
	// Add Basic Authentication header (base64 encoded username:password)
	req.SetBasicAuth(f.Conf.Username, f.Conf.Password)
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Accept", "application/json")
	return req
}

func (f *FetchAccessTokenReq) HTTPMethod() string {
	return f.Method
}

func (f *FetchAccessTokenReq) URL() string {
	return f.Conf.BaseURL + f.Conf.AccessTokenEndpoint
}

func (f *FetchAccessTokenReq) GetResponse() vendorapi.Response {
	return &fetchAccessTokenResp{}
}

func (f *FetchAccessTokenReq) Marshal() ([]byte, error) {
	// Map proto enum to string
	platformStr := getPlatformString(f.Req.GetPlatform())

	// Build display name from name if available
	displayName := "Guest"
	if f.Req.GetName() != nil {
		displayName = f.Req.GetName().ToFirstNameLastNameString()
	}

	// Build phone number string if available
	phoneNumber := ""
	if f.Req.GetPhoneNumber() != nil {
		phoneNumber = fmt.Sprintf("%d%d", f.Req.GetPhoneNumber().GetCountryCode(), f.Req.GetPhoneNumber().GetNationalNumber())
	}

	// Create vendor-specific request using the generated protobuf types
	vendorReq := &vendorNuggetPb.FetchChatbotAccessTokenRequest{
		Uid:         f.Req.GetActorId(),
		ClientId:    f.Conf.ClientID,
		Platform:    platformStr,
		DisplayName: displayName,
		Email:       f.Req.GetEmail(),
		PhoneNumber: phoneNumber,
		PhotoUrl:    "", // Default empty for now
	}

	// Convert to JSON using protobuf JSON marshaling
	return json.Marshal(vendorReq)
}

// Helper function to convert platform enum to string
func getPlatformString(platform commonTypePb.Platform) string {
	switch platform {
	case commonTypePb.Platform_ANDROID:
		return "android"
	case commonTypePb.Platform_IOS:
		return "ios"
	case commonTypePb.Platform_WEB:
		return "desktop"
	default:
		return "android" // default fallback
	}
}

// fetchAccessTokenResp handles the response from Nugget's fetch access token API
type fetchAccessTokenResp struct {
}

func (f *fetchAccessTokenResp) Unmarshal(b []byte) (proto.Message, error) {
	var nuggetResp = &vendorNuggetPb.FetchChatbotAccessTokenResponse{}
	err := json.Unmarshal(b, nuggetResp)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling Nugget response: %w", err)
	}

	// Check if the response indicates success
	if !nuggetResp.GetSuccess() {
		return &nuggetPb.FetchAccessTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg("API did not return success"),
		}, nil
	}

	return &nuggetPb.FetchAccessTokenResponse{
		Status:      rpc.StatusOk(),
		AccessToken: nuggetResp.GetAccessToken(),
	}, nil
}

func (f *fetchAccessTokenResp) HandleHttpError(_ context.Context, statusCode int, b []byte) (proto.Message, error) {
	// Try to parse error response if possible
	var nuggetResp = &vendorNuggetPb.FetchChatbotAccessTokenResponse{}
	if err := json.Unmarshal(b, &nuggetResp); err == nil {
		return &nuggetPb.FetchAccessTokenResponse{
			Status: rpc.StatusInternalWithDebugMsg("API did not return success"),
		}, nil
	}

	// Generic error response
	return &nuggetPb.FetchAccessTokenResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("Nugget API returned HTTP %d: %s", statusCode, string(b))),
	}, nil
}
