package lenden

import (
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common/account"
	"github.com/epifi/be-common/pkg/datetime"

	lendenPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
)

const (
	ConsentDTMStringFormat        = "2006-01-02 15:04:05.000 -0700"
	MandateURLExpiryTimeFormat    = "2006-01-02 15:04:05"
	ROIModificationDeadlineFormat = "2006-01-02T15:04:05.999999"
)

func getLendenConsentTypeStringList(consentTypeList []lendenPb.ConsentType) ([]string, error) {
	consentCodeMap := map[lendenPb.ConsentType]string{
		lendenPb.ConsentType_CONSENT_TYPE_PERMISSIONS:                 "PERMISSIONS",
		lendenPb.ConsentType_CONSENT_TYPE_POLITICALLY_EXPOSED:         "POLITICALLY_EXPOSED",
		lendenPb.ConsentType_CONSENT_TYPE_LOAN_CONSENT:                "LOAN_CONSENT",
		lendenPb.ConsentType_CONSENT_TYPE_USER_ACCEPTANCE_BUREAU:      "USER_ACCEPTANCE_BUREAU",
		lendenPb.ConsentType_CONSENT_TYPE_MODIFY_ROI:                  "MODIFY_ROI",
		lendenPb.ConsentType_CONSENT_TYPE_CKYC:                        "CKYC",
		lendenPb.ConsentType_CONSENT_TYPE_USER_ACCEPTANCE_CONFIRM_KYC: "USER_ACCEPTANCE_CONFIRM_KYC",
		lendenPb.ConsentType_CONSENT_TYPE_USER_ACCEPTANCE_DIGILOCKER:  "USER_ACCEPTANCE_DIGILOCKER",
		lendenPb.ConsentType_CONSENT_TYPE_MANDATE:                     "MANDATE_CONSENT",
		lendenPb.ConsentType_CONSENT_TYPE_GENERATE_KFS_LA:             "KFS_LA",
	}

	var consentCodes []string
	for _, consentType := range consentTypeList {
		if code, ok := consentCodeMap[consentType]; ok {
			consentCodes = append(consentCodes, code)
		} else {
			return nil, fmt.Errorf("invalid consent type: %v", consentType)
		}
	}

	return consentCodes, nil
}

func getEmploymentTypeString(employmentType lendenPb.LendenEmploymentType) (string, error) {
	employmentTypeMap := map[lendenPb.LendenEmploymentType]string{
		lendenPb.LendenEmploymentType_LENDEN_EMPLOYMENT_TYPE_SALARIED:      "SALARIED",
		lendenPb.LendenEmploymentType_LENDEN_EMPLOYMENT_TYPE_SELF_EMPLOYED: "SELF-EMPLOYED",
	}

	employment, ok := employmentTypeMap[employmentType]
	if !ok {
		return "", fmt.Errorf("invalid employment type: %v", employmentType)
	}

	return employment, nil
}

func getLendenInterestType(interestType lendenPb.InterestType) (string, error) {
	interestTypeMap := map[lendenPb.InterestType]string{
		lendenPb.InterestType_INTEREST_TYPE_FLAT: "FLAT",
	}

	interest, ok := interestTypeMap[interestType]
	if !ok {
		return "", fmt.Errorf("invalid interest type: %v", interestType)
	}

	return interest, nil
}

func getLendenInterestFrequency(interestFrequency lendenPb.InterestFrequency) (string, error) {
	interestFrequencyMap := map[lendenPb.InterestFrequency]string{
		lendenPb.InterestFrequency_INTEREST_FREQUENCY_MONTHLY: "MONTHLY",
		lendenPb.InterestFrequency_INTEREST_FREQUENCY_YEARLY:  "YEARLY",
	}

	frequency, ok := interestFrequencyMap[interestFrequency]
	if !ok {
		return "", fmt.Errorf("invalid interest frequency: %v", interestFrequency)
	}

	return frequency, nil
}

func getLendenAddressType(addressType lendenPb.AddressType) (string, error) {
	addressTypeMap := map[lendenPb.AddressType]string{
		lendenPb.AddressType_PERMANENT:          "PERMANENT",
		lendenPb.AddressType_LOAN_COMMUNICATION: "COMMUNICATION",
	}

	address, ok := addressTypeMap[addressType]
	if !ok {
		return "", fmt.Errorf("invalid address type: %v", addressType)
	}

	return address, nil
}

// The Function Takes the State Name and returns the State Code
func getLendenStateCode(stateName string) (string, error) {
	var stateCodeMap = map[string]string{
		"ANDAMAN AND NICOBAR ISLANDS": "AN", // prod database has this spelling
		"ANDAMAN & NICOBAR ISLANDS":   "AN", // pre-prod database has this spelling
		"ANDAMAN AND NICO.IN.":        "AN",
		"ANDHRA PRADESH":              "AD",
		"ARUNACHAL PRADESH":           "AR",
		"ASSAM":                       "AS",
		"BIHAR":                       "BH",
		"CHANDIGARH":                  "CH",
		"CHATTISGARH":                 "CT",
		"CHHATTISGARH":                "CT", // Two spellings for the same state in prod database
		"THE DADRA AND NAGAR HAVELI AND DAMAN AND DIU": "DN", // This is the state name in prod database
		"DADRA AND NAGAR HAVELI":                       "DN",
		"DADRA & NAGAR HAVELI":                         "DN", // pre-prod database has this spelling
		"DAMAN AND DIU":                                "DD",
		"DAMAN & DIU":                                  "DD", // pre-prod database has this spelling
		"DELHI":                                        "DL",
		"NEW DELHI":                                    "DL",
		"GOA":                                          "GA",
		"GUJARAT":                                      "GJ",
		"HARYANA":                                      "HR",
		"HIMACHAL PRADESH":                             "HP",
		"JAMMU AND KASHMIR":                            "JK", // prod database has this spelling
		"JAMMU & KASHMIR":                              "JK", // pre-prod database has this spelling
		"JHARKHAND":                                    "JH",
		"KARNATAKA":                                    "KA",
		"KERALA":                                       "KL",
		"LADAKH":                                       "LA",
		"LAKSHADWEEP":                                  "LD", // Two spellings for the same state in prod database
		"MADHYA PRADESH":                               "MP",
		"MAHARASHTRA":                                  "MH",
		"MANIPUR":                                      "MN",
		"MEGHALAYA":                                    "ME",
		"MIZORAM":                                      "MI",
		"NAGALAND":                                     "NL",
		"ODISHA":                                       "OR",
		"PONDICHERRY":                                  "PY", // This is the state name in pre-prod database
		"PUDUCHERRY":                                   "PY", // This is the state name in prod database
		"PUNJAB":                                       "PB",
		"RAJASTHAN":                                    "RJ",
		"SIKKIM":                                       "SK",
		"TAMIL NADU":                                   "TN",
		"TELANGANA":                                    "TL",
		"TRIPURA":                                      "TR",
		"UTTAR PRADESH":                                "UP",
		"UTTARAKHAND":                                  "UT",
		"WEST BENGAL":                                  "WB",
	}

	stateCode, ok := stateCodeMap[strings.ToUpper(strings.TrimSpace(stateName))]
	if !ok {
		return "", fmt.Errorf("invalid state name: %v", stateName)
	}
	return stateCode, nil
}

func getBankAccountTypeString(bankAccountType account.AccountType) (string, error) {
	bankAccountTypeMap := map[account.AccountType]string{
		account.AccountType_SAVINGS: "SAVINGS",
		account.AccountType_CURRENT: "CURRENT",
	}

	bank, ok := bankAccountTypeMap[bankAccountType]
	if !ok {
		return "", fmt.Errorf("invalid bank type: %v", bankAccountType)
	}

	return bank, nil
}

func getLoanStatus(loanStatusString string) (lendenPb.LoanStatus, error) {
	loanStatusMap := map[string]lendenPb.LoanStatus{
		"PROCESSING": lendenPb.LoanStatus_LOAN_STATUS_PROCESSING,
		"DISBURSED":  lendenPb.LoanStatus_LOAN_STATUS_DISBURSED,
		"CLOSED":     lendenPb.LoanStatus_LOAN_STATUS_CLOSED,
		"CANCELLED":  lendenPb.LoanStatus_LOAN_STATUS_CANCELLED,
		// TODO(Brijesh): Follow up with vendor on exhaustive list of statuses
		"SANCTIONED": lendenPb.LoanStatus_LOAN_STATUS_SANCTIONED,
		"LISTED":     lendenPb.LoanStatus_LOAN_STATUS_SANCTIONED,
	}

	loanStatus, ok := loanStatusMap[loanStatusString]
	if !ok {
		return lendenPb.LoanStatus_LOAN_STATUS_UNSPECIFIED, fmt.Errorf("invalid loanStatus: %v", loanStatusString)
	}
	return loanStatus, nil
}

func getConsentDTMString(consentTime *timestamppb.Timestamp) string {
	return consentTime.AsTime().In(datetime.IST).Format(ConsentDTMStringFormat)
}

func getTimeStampFromLendenResponse(timeString string) (*timestamppb.Timestamp, error) {
	ts, err := datetime.ParseStringTimestampProtoInLocation(MandateURLExpiryTimeFormat, timeString, datetime.IST)
	if err != nil {
		return nil, fmt.Errorf("error parsing time: %w", err)
	}
	return ts, nil
}

func getMandateTypeString(mandateType lendenPb.MandateType) (string, error) {
	mandateTypeMap := map[lendenPb.MandateType]string{
		lendenPb.MandateType_MANDATE_TYPE_NACH_MANDATE: "NACH_MANDATE",
		lendenPb.MandateType_MANDATE_TYPE_UPI_MANDATE:  "UPI_MANDATE",
	}

	mandate, ok := mandateTypeMap[mandateType]
	if !ok {
		return "", fmt.Errorf("invalid mandate type: %v", mandateType)
	}
	return mandate, nil
}

func getKYCStatusEnumFromString(status string) (lendenPb.KYCStatus, error) {
	kycStatusMap := map[string]lendenPb.KYCStatus{
		"INITIATED":   lendenPb.KYCStatus_KYC_STATUS_INITIATED,
		"IN_PROGRESS": lendenPb.KYCStatus_KYC_STATUS_IN_PROGRESS,
		"COMPLETED":   lendenPb.KYCStatus_KYC_STATUS_COMPLETED,
		"FAILED":      lendenPb.KYCStatus_KYC_STATUS_FAILED,
		"EXPIRED":     lendenPb.KYCStatus_KYC_STATUS_EXPIRED,
	}
	kycStatus, ok := kycStatusMap[status]
	if !ok {
		return 0, errors.Errorf("invalid kyc status: %s", status)
	}
	return kycStatus, nil
}

func getLendenAccountType(accountType lendenPb.AccountType) (string, error) {
	accountTypeMap := map[lendenPb.AccountType]string{
		lendenPb.AccountType_ACCOUNT_TYPE_CURRENT: "CURRENT",
		lendenPb.AccountType_ACCOUNT_TYPE_SAVINGS: "SAVINGS",
	}

	accountTypeValue, ok := accountTypeMap[accountType]
	if !ok {
		return "", fmt.Errorf("invalid account type: %v", accountTypeValue)
	}

	return accountTypeValue, nil
}

func getLendenEligibilityDataType(eligibilityDataType lendenPb.EligibilityDataType) (string, error) {
	eligibilityDataTypeMap := map[lendenPb.EligibilityDataType]string{
		lendenPb.EligibilityDataType_ELIGIBILITY_DATA_TYPE_BANK_STATEMENT: "BANK_STATEMENT",
		lendenPb.EligibilityDataType_ELIGIBILITY_DATA_TYPE_PARTNER_DATA:   "PARTNER_DATA",
	}

	eligibilityDataTypeValue, ok := eligibilityDataTypeMap[eligibilityDataType]
	if !ok {
		return "", fmt.Errorf("invalid eligibility data type: %v", eligibilityDataTypeValue)
	}

	return eligibilityDataTypeValue, nil
}

func getMandateStatus(vendorMandateStatus string) (lendenPb.MandateStatus, error) {
	switch vendorMandateStatus {
	case "IN_PROGRESS":
		return lendenPb.MandateStatus_MANDATE_STATUS_IN_PROGRESS, nil
	case "COMPLETED":
		return lendenPb.MandateStatus_MANDATE_STATUS_COMPLETED, nil
	case "FAILED":
		return lendenPb.MandateStatus_MANDATE_STATUS_FAILED, nil
	case "EXPIRED":
		return lendenPb.MandateStatus_MANDATE_STATUS_EXPIRED, nil
	default:
		return 0, errors.Errorf("invalid mandate status: %v", vendorMandateStatus)
	}
}
