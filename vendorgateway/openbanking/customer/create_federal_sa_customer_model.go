package customer

// bank has added DFDL validation and expects the fields in the request should follow a particular order as below
type CreateCustomerRequestInVendorSpecifiedOrder struct {
	RespUrl                string                `json:"RespUrl"`
	SenderCode             string                `json:"SenderCode"`
	ServiceAccessId        string                `json:"ServiceAccessId"`
	ServiceAccessCode      string                `json:"ServiceAccessCode"`
	DeviceId               string                `json:"DeviceId"`
	UserProfileId          string                `json:"UserProfileId"`
	DeviceToken            string                `json:"DeviceToken"`
	RequestId              string                `json:"RequestId"`
	SolId                  string                `json:"SolId"`
	Personal_Details       PersonalDetails       `json:"Personal_Details"`
	Contact_Details        ContactDetails        `json:"Contact_Details"`
	Additional_Details     AdditionalDetails     `json:"Additional_Details"`
	Identification_Details IdentificationDetails `json:"Identification_Details"`
}

type PersonalDetails struct {
	Title         string `json:"Title"`
	FirstName     string `json:"FirstName"`
	MiddleName    string `json:"MiddleName"`
	LastName      string `json:"LastName"`
	FatherName    string `json:"FatherName"`
	MotherName    string `json:"MotherName"`
	DateOfBirth   string `json:"DateOfBirth"`
	Gender        string `json:"Gender"`
	MaritalStatus string `json:"MaritalStatus"`
	Uid_No        string `json:"Uid_No"`
}
type ContactDetails struct {
	Mobile                string  `json:"Mobile"`
	Email                 string  `json:"Email"`
	Communication_Address Address `json:"Communication_Address"`
	CA_Sameas_PA          string  `json:"CA_Sameas_PA"`
	Permanent_Address     Address `json:"Permanent_Address"`
}

type Address struct {
	House      string `json:"House"`
	Place      string `json:"Place"`
	City_Cd    string `json:"City_Cd"`
	State_Cd   string `json:"State_Cd"`
	Country_Cd string `json:"Country_Cd"`
	PinCode    string `json:"PinCode"`
	LandLine   string `json:"LandLine"`
}
type AdditionalDetails struct {
	AnnualIncome          string `json:"AnnualIncome"`
	PanNo                 string `json:"PanNo"`
	Religion              string `json:"Religion"`
	Community             string `json:"Community"`
	Qualification         string `json:"Qualification"`
	Occupation            string `json:"Occupation"`
	Form60                string `json:"Form60"`
	TaxSlab               string `json:"TaxSlab"`
	Employment            string `json:"Employement"`
	Designation           string `json:"Designation"`
	SpouseOccupation      string `json:"SpouseOcupation"`
	CustomerStatus        string `json:"CustomerStatus"`
	Category              string `json:"Category"`
	SubCategoryOccupation string `json:"SubCategoryOccupation"`
}

type IdentificationDetails struct {
	ProofOfIdentity ProofDetails `json:"ProofOfIdentity"`
	ProofOfAddress  ProofDetails `json:"ProofOfAddress"`
}

type ProofDetails struct {
	Type          string `json:"Type"`
	Id_Number     string `json:"Id_Number"`
	Id_IssueDate  string `json:"Id_IssueDate"`
	Id_ExpiryDate string `json:"Id_ExpiryDate"`
}
