package bridgewise

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"sort"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorBridgewise "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/constants"
)

type GetCompanyMarketDataReq struct {
	Method string
	Req    *bridgewise.GetCompanyMarketDataRequest
	Conf   *config.Bridgewise
	AddTokenToRequestHeader
}

func (g *GetCompanyMarketDataReq) HTTPMethod() string {
	return g.Method
}

func (g *GetCompanyMarketDataReq) URL() string {
	baseUrl := fmt.Sprintf("%s/companies/%s/market", g.Conf.ApiHost, g.Req.GetCompanyId())
	queryParams := url.Values{}
	if g.Req.GetTradingItemId() != "" {
		queryParams.Add("trading_item_id", g.Req.GetTradingItemId())
	}
	if g.Req.GetFromDate() != nil {
		queryParams.Add("date__ge", datetime.DateToString(g.Req.GetFromDate(), constants.DateLayout, datetime.IST))
	}
	if g.Req.GetToDate() != nil {
		queryParams.Add("date__le", datetime.DateToString(g.Req.GetToDate(), constants.DateLayout, datetime.IST))
	}

	if queryParams.Has("trading_item_id") || queryParams.Has("date__ge") || queryParams.Has("date__le") {
		baseUrl = baseUrl + "?" + queryParams.Encode()
	}
	return baseUrl
}

func (g *GetCompanyMarketDataReq) GetResponse() vendorapi.Response {
	return &GetCompanyMarketDataResp{}
}

func (g *GetCompanyMarketDataReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (g *GetCompanyMarketDataReq) GetResponseContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (g *GetCompanyMarketDataReq) Marshal() ([]byte, error) {
	return nil, nil
}

type GetCompanyMarketDataResp struct {
}

func (g *GetCompanyMarketDataResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
	return &bridgewise.GetCompanyMarketDataResponse{
		Status: vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatus),
	}, nil
}

func (g *GetCompanyMarketDataResp) Unmarshal(b []byte) (proto.Message, error) {
	var res []*vendorBridgewise.CompanyMarketData
	err := json.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal CompanyMarketData")
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].GetDate() < res[j].GetDate()
	})
	return &bridgewise.GetCompanyMarketDataResponse{
		Status:     rpc.StatusOk(),
		MarketData: res,
	}, nil
}
