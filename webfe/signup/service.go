package signup

import (
	"context"
	"fmt"

	"github.com/google/wire"

	webfeCommon "github.com/epifi/gamma/api/webfe/common"
	"github.com/epifi/gamma/api/webfe/signup"
	pkgSourceIntent "github.com/epifi/gamma/pkg/acquisition/sourceandintent"
)

var (
	WebfeSignupServiceWireset = wire.NewSet(
		NewService,
		pkgSourceIntent.IdentifierWireSet,
	)
)

type Service struct {
	smsSignupStrategy *SMSSignupStrategy
	totpStrategy      *TOTPAuthStrategy
}

func NewService(smsStrategy *SMSSignupStrategy, totpStrategy *TOTPAuthStrategy) *Service {
	return &Service{
		smsSignupStrategy: smsStrategy,
		totpStrategy:      totpStrategy,
	}
}

var _ signup.SignupServer = &Service{}

func (s *Service) GenerateOtp(ctx context.Context, req *signup.GenerateOtpRequest) (*signup.GenerateOtpResponse, error) {
	strategy := s.getStrategy(ctx, req.GetFlowName())
	if strategy == nil {
		return nil, fmt.Errorf("no strategy found for flow name: %v", req.GetFlowName())
	}
	return strategy.GenerateOtp(ctx, req)
}

func (s *Service) VerifyOtp(ctx context.Context, req *signup.VerifyOtpRequest) (*signup.VerifyOtpResponse, error) {
	strategy := s.getStrategy(ctx, req.GetFlowName())
	if strategy == nil {
		return nil, fmt.Errorf("no strategy found for flow name: %v", req.GetFlowName())
	}
	return strategy.VerifyOtp(ctx, req)
}

func (s *Service) getStrategy(_ context.Context, name webfeCommon.FlowName) AuthStrategy {
	// Map flow name to strategy. Add more mappings as needed.
	strategyMap := map[webfeCommon.FlowName]AuthStrategy{
		webfeCommon.FlowName_FLOW_NAME_UNSPECIFIED:           s.smsSignupStrategy, // fallback/default
		webfeCommon.FlowName_FLOW_NAME_LOANS_ELIGIBILITY:     s.smsSignupStrategy,
		webfeCommon.FlowName_FLOW_NAME_CREDIT_SCORE_ANALYSER: s.smsSignupStrategy,
		webfeCommon.FlowName_FLOW_NAME_CC_ELIGIBILITY_CHECK:  s.smsSignupStrategy,
		webfeCommon.FlowName_FLOW_NAME_NET_WORTH_MCP_AUTH:    s.totpStrategy,
	}
	if strategy, ok := strategyMap[name]; ok {
		return strategy
	}
	return s.smsSignupStrategy
}
