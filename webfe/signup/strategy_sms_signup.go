package signup

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/comms"
	feHeader "github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/webfe/signup"
	pkgSourceIntent "github.com/epifi/gamma/pkg/acquisition/sourceandintent"
	"github.com/epifi/gamma/pkg/frontend/header"
	"github.com/epifi/gamma/pkg/obfuscator"
	webEvents "github.com/epifi/gamma/webfe/events"
	"github.com/epifi/gamma/webfe/userproc"
)

// SMSSignupStrategy implements the AuthStrategy interface to handle user signup and auth
// via SMS OTP. It creates a new user if the user doesn't exist.
type SMSSignupStrategy struct {
	userProcessor          userproc.UserProcessor
	authClient             auth.AuthClient
	eventLogger            webEvents.EventLogger
	sourceIntentIdentifier pkgSourceIntent.SourceIntentIdentifier
}

func NewSMSStrategy(
	userProcessor userproc.UserProcessor, authClient auth.AuthClient,
	eventLogger webEvents.EventLogger, sourceIntentIdentifier pkgSourceIntent.SourceIntentIdentifier) *SMSSignupStrategy {
	return &SMSSignupStrategy{
		userProcessor:          userProcessor,
		authClient:             authClient,
		eventLogger:            eventLogger,
		sourceIntentIdentifier: sourceIntentIdentifier,
	}
}

var _ AuthStrategy = &SMSSignupStrategy{}

func (s *SMSSignupStrategy) GenerateOtp(ctx context.Context, req *signup.GenerateOtpRequest) (*signup.GenerateOtpResponse, error) {
	var errRes = func(status *rpc.Status, errMsg string) (*signup.GenerateOtpResponse, error) {
		return &signup.GenerateOtpResponse{
			RespHeader: header.InlineErrResp(status, fmt.Sprint(status.GetCode()), errMsg),
		}, nil
	}
	logger.Info(ctx, fmt.Sprintf("masked phone number %v", obfuscator.HashedPhoneNum(req.GetPhoneNumber())))
	authRes, err := s.authClient.GenerateOtp(ctx, &auth.GenerateOtpRequest{
		Device:          req.GetReq().GetAuth().GetDevice(),
		PhoneNumber:     req.GetPhoneNumber(),
		Token:           req.GetToken(),
		GenerateOtpFlow: auth.GenerateOTPFlow_GENERATE_OTP_FLOW_WEB_LOGIN,
		Mediums:         []comms.Medium{comms.Medium_SMS},
	})
	if rpcErr := epifigrpc.RPCError(authRes, err); rpcErr != nil {
		logger.Error(ctx, "Error in invoking GenerateOtp of Auth server", zap.Error(rpcErr))
		return errRes(rpc.StatusInternal(), rpcErr.Error())
	}

	return &signup.GenerateOtpResponse{
		RespHeader: &feHeader.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		RetryTimerSeconds: authRes.GetRetryTimerSeconds(),
		Token:             authRes.GetToken(),
	}, nil
}

func (s *SMSSignupStrategy) VerifyOtp(ctx context.Context, req *signup.VerifyOtpRequest) (*signup.VerifyOtpResponse, error) {
	var errRes = func(status *rpc.Status, errMsg string) (*signup.VerifyOtpResponse, error) {
		return &signup.VerifyOtpResponse{
			RespHeader: header.InlineErrResp(status, fmt.Sprint(status.GetCode()), errMsg),
		}, nil
	}
	authRes, err := s.authClient.VerifyOtp(ctx, &auth.VerifyOtpRequest{
		Device:      req.GetReq().GetAuth().GetDevice(),
		PhoneNumber: req.GetPhoneNumber(),
		Token:       req.GetToken(),
		Otp:         req.GetOtp(),
		Mediums:     []comms.Medium{comms.Medium_SMS},
	})
	if rpcErr := epifigrpc.RPCError(authRes, err); rpcErr != nil {
		logger.Error(ctx, "Error in invoking VerifyOtp of Auth server", zap.Error(rpcErr))
		return errRes(authRes.GetStatus(), rpcErr.Error())
	}

	source, intent := s.sourceIntentIdentifier.GetSourceAndIntentFromWebUrl(ctx, req.GetAcqInfo().GetUrl())

	resp, err := s.userProcessor.GetOrCreateUserAndActor(ctx, &userproc.GetOrCreateUserAndActorRequest{
		PhoneNumber: req.GetPhoneNumber(),
		Url:         req.GetAcqInfo().GetUrl(),
		Channel:     pkgSourceIntent.AcquisitionSourceToEnumMap[string(source)],
		Intent:      pkgSourceIntent.AcquisitionIntentToEnumMap[string(intent)],
	})
	if err != nil {
		logger.Error(ctx, "error in GetOrCreateUserAndActor", zap.Error(err))
		return nil, err
	}
	createTokenResponse, err := s.authClient.CreateToken(ctx, &auth.CreateTokenRequest{
		ActorId:     resp.Actor.GetId(),
		TokenType:   auth.TokenType_WEB_ACCESS_TOKEN,
		Device:      req.GetReq().GetAuth().GetDevice(),
		PhoneNumber: req.GetPhoneNumber(),
	})
	if rpcErr := epifigrpc.RPCError(createTokenResponse, err); rpcErr != nil {
		logger.Error(ctx, "error in creating new token", zap.Error(rpcErr))
		return errRes(createTokenResponse.GetStatus(), rpcErr.Error())
	}

	s.eventLogger.LogUserIdentityVerificationServer(ctx, resp.Actor.GetId())
	return &signup.VerifyOtpResponse{
		AccessToken: createTokenResponse.GetToken(),
		ActorId:     resp.Actor.GetId(),
		RespHeader: &feHeader.ResponseHeader{
			Status: rpc.StatusOk(),
		},
	}, nil
}
