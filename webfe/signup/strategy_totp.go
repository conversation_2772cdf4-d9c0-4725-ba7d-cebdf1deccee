package signup

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/webfe/signup"
)

type TOTPAuthStrategy struct {
	authClient auth.AuthClient
}

func NewTOTPAuthStrategy(authClient auth.AuthClient) *TOTPAuthStrategy {
	return &TOTPAuthStrategy{
		authClient: authClient,
	}
}

var _ AuthStrategy = &TOTPAuthStrategy{}

func (s *TOTPAuthStrategy) GenerateOtp(ctx context.Context, req *signup.GenerateOtpRequest) (*signup.GenerateOtpResponse, error) {
	return nil, fmt.Errorf("unimplemented")
}

func (s *TOTPAuthStrategy) VerifyOtp(ctx context.Context, req *signup.VerifyOtpRequest) (*signup.VerifyOtpResponse, error) {
	return nil, fmt.<PERSON><PERSON><PERSON>("unimplemented")
}
