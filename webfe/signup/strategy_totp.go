package signup

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/session"
	"github.com/epifi/gamma/api/auth/totp"
	"github.com/epifi/gamma/api/auth/totp/enums"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/webfe/signup"
	"github.com/epifi/gamma/pkg/frontend/header"
)

type TOTPAuthStrategy struct {
	authClient    auth.AuthClient
	totpClient    totp.TotpClient
	userClient    user.UsersClient
	sessionClient session.SessionManagerClient
}

func NewTOTPAuthStrategy(
	authClient auth.AuthClient,
	totpClient totp.TotpClient,
	userClient user.UsersClient,
	sessionClient session.SessionManagerClient,
) *TOTPAuthStrategy {
	return &TOTPAuthStrategy{
		authClient:    authClient,
		totpClient:    totpClient,
		userClient:    userClient,
		sessionClient: sessionClient,
	}
}

const (
	dummyAccessToken = "dummyTokenFromTOTPAuth"
)

var _ AuthStrategy = &TOTPAuthStrategy{}

func (s *TOTPAuthStrategy) GenerateOtp(ctx context.Context, req *signup.GenerateOtpRequest) (*signup.GenerateOtpResponse, error) {
	if req.GetToken() == "" {
		logger.Debug(ctx, "no token provided in totp gen otp")
		return &signup.GenerateOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
			},
			Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
		}, nil
	}

	// TODO: add check if user doesn't exist

	return &signup.GenerateOtpResponse{
		RespHeader: header.SuccessRespHeader(),
		Status:     rpc.StatusOk(),
		Token:      req.GetToken(),
	}, nil
}

func (s *TOTPAuthStrategy) VerifyOtp(ctx context.Context, req *signup.VerifyOtpRequest) (*signup.VerifyOtpResponse, error) {
	if req.GetToken() == "" {
		logger.Debug(ctx, "no token provided in totp verify otp")
		return &signup.VerifyOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
			},
			Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
		}, nil
	}

	if req.GetOtp() == "" {
		logger.Debug(ctx, "no otp provided in totp verify otp")
		return &signup.VerifyOtpResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("otp required"),
			},
			Status: rpc.StatusInvalidArgumentWithDebugMsg("otp required"),
		}, nil
	}

	minUserRes, err := s.userClient.GetMinimalUser(ctx, &user.GetMinimalUserRequest{
		Identifier: &user.GetMinimalUserRequest_PhoneNumber{
			PhoneNumber: req.GetPhoneNumber(),
		},
	})
	if rpcErr := epifigrpc.RPCError(minUserRes, err); rpcErr != nil {
		if minUserRes.GetStatus().IsRecordNotFound() {
			return &signup.VerifyOtpResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error in get user", zap.Error(rpcErr))
		return nil, rpcErr
	}

	actorId := minUserRes.GetMinimalUser().GetActorId()
	ctx = epificontext.CtxWithActorId(ctx, actorId)

	totpResp, err := s.totpClient.VerifyTotp(ctx, &totp.VerifyTotpRequest{
		ActorId: actorId,
		Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
		Totp:    req.GetOtp(),
	})
	if rpcErr := epifigrpc.RPCError(totpResp, err); rpcErr != nil {
		if totpResp.GetStatus().GetCode() == uint32(totp.VerifyTotpResponse_STATUS_INVALID_CODE) {
			return &signup.VerifyOtpResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.NewStatusWithoutDebug(uint32(signup.VerifyOtpResponse_OTP_INCORRECT), "wrong code"),
				},
			}, nil
		}
		logger.Error(ctx, "failed to verify totp", zap.Error(rpcErr))
		return nil, rpcErr
	}

	// TODO: Handle non-happy statuses from VerifyTotp

	sessRes, err := s.sessionClient.CreateSession(ctx, &session.CreateSessionRequest{
		ActorId:     actorId,
		SessionId:   req.GetToken(),
		Device:      req.GetReq().GetAuth().GetDevice(),
		PhoneNumber: req.GetPhoneNumber(),
	})
	if rpcErr := epifigrpc.RPCError(sessRes, err); rpcErr != nil {
		logger.Error(ctx, "failed to create session", zap.Error(rpcErr))
		return nil, rpcErr
	}

	return &signup.VerifyOtpResponse{
		RespHeader: header.SuccessRespHeader(),
		Status:     rpc.StatusOk(),
		// Sending dummy token as web frontend expects access token in response
		AccessToken: dummyAccessToken,
	}, nil
}
