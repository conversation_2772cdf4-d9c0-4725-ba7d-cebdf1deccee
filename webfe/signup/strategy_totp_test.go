package signup

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	mockAuth "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/auth/session"
	mockSession "github.com/epifi/gamma/api/auth/session/mocks"
	"github.com/epifi/gamma/api/auth/totp"
	"github.com/epifi/gamma/api/auth/totp/enums"
	mockTotp "github.com/epifi/gamma/api/auth/totp/mocks"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/user"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/webfe/signup"
	"github.com/epifi/gamma/pkg/frontend/header"
)

func TestTOTPAuthStrategy_VerifyOtp(t *testing.T) {
	type args struct {
		ctx context.Context
		req *signup.VerifyOtpRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(
			mockAuthClient *mockAuth.MockAuthClient,
			mockTotpClient *mockTotp.MockTotpClient,
			mockUserClient *mockUser.MockUsersClient,
			mockSessionClient *mockSession.MockSessionManagerClient,
		)
		want    *signup.VerifyOtpResponse
		wantErr bool
	}{
		{
			name: "success - happy path",
			args: args{
				ctx: context.Background(),
				req: &signup.VerifyOtpRequest{
					Token:       "test-token",
					Otp:         "123456",
					PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
					Req: &headerPb.RequestHeader{
						Auth: &headerPb.AuthHeader{
							Device: &commontypes.Device{DeviceId: "test-device"},
						},
					},
				},
			},
			setupMocks: func(mockAuthClient *mockAuth.MockAuthClient, mockTotpClient *mockTotp.MockTotpClient, mockUserClient *mockUser.MockUsersClient, mockSessionClient *mockSession.MockSessionManagerClient) {
				mockUserClient.EXPECT().GetMinimalUser(gomock.Any(), &user.GetMinimalUserRequest{
					Identifier: &user.GetMinimalUserRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
					},
				}).Return(&user.GetMinimalUserResponse{
					Status: rpc.StatusOk(),
					MinimalUser: &user.MinimalUser{
						ActorId: "test-actor-id",
					},
				}, nil)

				mockTotpClient.EXPECT().VerifyTotp(gomock.Any(), &totp.VerifyTotpRequest{
					ActorId: "test-actor-id",
					Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
					Totp:    "123456",
				}).Return(&totp.VerifyTotpResponse{
					Status: rpc.StatusOk(),
				}, nil)

				mockSessionClient.EXPECT().CreateSession(gomock.Any(), &session.CreateSessionRequest{
					ActorId:     "test-actor-id",
					SessionId:   "test-token",
					Device:      &commontypes.Device{DeviceId: "test-device"},
					PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
				}).Return(&session.CreateSessionResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &signup.VerifyOtpResponse{
				RespHeader: header.SuccessRespHeader(),
				Status:     rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failure - no token provided",
			args: args{
				ctx: context.Background(),
				req: &signup.VerifyOtpRequest{
					Token:       "",
					Otp:         "123456",
					PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
				},
			},
			setupMocks: func(mockAuthClient *mockAuth.MockAuthClient, mockTotpClient *mockTotp.MockTotpClient, mockUserClient *mockUser.MockUsersClient, mockSessionClient *mockSession.MockSessionManagerClient) {
				// No mocks needed as it returns early
			},
			want: &signup.VerifyOtpResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
				},
				Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
			},
			wantErr: false,
		},
		{
			name: "failure - no otp provided",
			args: args{
				ctx: context.Background(),
				req: &signup.VerifyOtpRequest{
					Token:       "test-token",
					Otp:         "",
					PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
				},
			},
			setupMocks: func(mockAuthClient *mockAuth.MockAuthClient, mockTotpClient *mockTotp.MockTotpClient, mockUserClient *mockUser.MockUsersClient, mockSessionClient *mockSession.MockSessionManagerClient) {
				// No mocks needed as it returns early
			},
			want: &signup.VerifyOtpResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.StatusInvalidArgumentWithDebugMsg("otp required"),
				},
				Status: rpc.StatusInvalidArgumentWithDebugMsg("otp required"),
			},
			wantErr: false,
		},
		{
			name: "failure - user not found",
			args: args{
				ctx: context.Background(),
				req: &signup.VerifyOtpRequest{
					Token:       "test-token",
					Otp:         "123456",
					PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
				},
			},
			setupMocks: func(mockAuthClient *mockAuth.MockAuthClient, mockTotpClient *mockTotp.MockTotpClient, mockUserClient *mockUser.MockUsersClient, mockSessionClient *mockSession.MockSessionManagerClient) {
				mockUserClient.EXPECT().GetMinimalUser(gomock.Any(), &user.GetMinimalUserRequest{
					Identifier: &user.GetMinimalUserRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
					},
				}).Return(&user.GetMinimalUserResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			want: &signup.VerifyOtpResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.StatusRecordNotFound(),
				},
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failure - invalid totp code",
			args: args{
				ctx: context.Background(),
				req: &signup.VerifyOtpRequest{
					Token:       "test-token",
					Otp:         "wrong-otp",
					PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
				},
			},
			setupMocks: func(mockAuthClient *mockAuth.MockAuthClient, mockTotpClient *mockTotp.MockTotpClient, mockUserClient *mockUser.MockUsersClient, mockSessionClient *mockSession.MockSessionManagerClient) {
				mockUserClient.EXPECT().GetMinimalUser(gomock.Any(), &user.GetMinimalUserRequest{
					Identifier: &user.GetMinimalUserRequest_PhoneNumber{
						PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 9876543210},
					},
				}).Return(&user.GetMinimalUserResponse{
					Status: rpc.StatusOk(),
					MinimalUser: &user.MinimalUser{
						ActorId: "test-actor-id",
					},
				}, nil)

				mockTotpClient.EXPECT().VerifyTotp(gomock.Any(), &totp.VerifyTotpRequest{
					ActorId: "test-actor-id",
					Purpose: enums.Purpose_PURPOSE_NET_WORTH_MCP,
					Totp:    "wrong-otp",
				}).Return(&totp.VerifyTotpResponse{
					Status: &rpc.Status{Code: uint32(totp.VerifyTotpResponse_STATUS_INVALID_CODE)},
				}, nil)
			},
			want: &signup.VerifyOtpResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.NewStatusWithoutDebug(uint32(signup.VerifyOtpResponse_OTP_INCORRECT), "wrong code"),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockAuthClient := mockAuth.NewMockAuthClient(ctrl)
			mockTotpClient := mockTotp.NewMockTotpClient(ctrl)
			mockUserClient := mockUser.NewMockUsersClient(ctrl)
			mockSessionClient := mockSession.NewMockSessionManagerClient(ctrl)

			tt.setupMocks(mockAuthClient, mockTotpClient, mockUserClient, mockSessionClient)

			s := &TOTPAuthStrategy{
				authClient:    mockAuthClient,
				totpClient:    mockTotpClient,
				userClient:    mockUserClient,
				sessionClient: mockSessionClient,
			}

			got, err := s.VerifyOtp(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("VerifyOtp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.want != nil && got != nil {
				if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
					t.Errorf("VerifyOtp() mismatch (-want +got):\n%s", diff)
				}
			} else if tt.want != got {
				t.Errorf("VerifyOtp() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestTOTPAuthStrategy_GenerateOtp(t *testing.T) {
	type args struct {
		ctx context.Context
		req *signup.GenerateOtpRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(
			mockAuthClient *mockAuth.MockAuthClient,
			mockTotpClient *mockTotp.MockTotpClient,
			mockUserClient *mockUser.MockUsersClient,
			mockSessionClient *mockSession.MockSessionManagerClient,
		)
		want    *signup.GenerateOtpResponse
		wantErr bool
	}{
		{
			name: "success - happy path",
			args: args{
				ctx: context.Background(),
				req: &signup.GenerateOtpRequest{
					Token: "test-token",
				},
			},
			setupMocks: func(mockAuthClient *mockAuth.MockAuthClient, mockTotpClient *mockTotp.MockTotpClient, mockUserClient *mockUser.MockUsersClient, mockSessionClient *mockSession.MockSessionManagerClient) {
				// No mocks needed as it returns success directly
			},
			want: &signup.GenerateOtpResponse{
				RespHeader: header.SuccessRespHeader(),
				Status:     rpc.StatusOk(),
				Token:      "test-token",
			},
			wantErr: false,
		},
		{
			name: "failure - no token provided",
			args: args{
				ctx: context.Background(),
				req: &signup.GenerateOtpRequest{
					Token: "",
				},
			},
			setupMocks: func(mockAuthClient *mockAuth.MockAuthClient, mockTotpClient *mockTotp.MockTotpClient, mockUserClient *mockUser.MockUsersClient, mockSessionClient *mockSession.MockSessionManagerClient) {
				// No mocks needed as it returns early
			},
			want: &signup.GenerateOtpResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
				},
				Status: rpc.StatusInvalidArgumentWithDebugMsg("token required"),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockAuthClient := mockAuth.NewMockAuthClient(ctrl)
			mockTotpClient := mockTotp.NewMockTotpClient(ctrl)
			mockUserClient := mockUser.NewMockUsersClient(ctrl)
			mockSessionClient := mockSession.NewMockSessionManagerClient(ctrl)

			tt.setupMocks(mockAuthClient, mockTotpClient, mockUserClient, mockSessionClient)

			s := &TOTPAuthStrategy{
				authClient:    mockAuthClient,
				totpClient:    mockTotpClient,
				userClient:    mockUserClient,
				sessionClient: mockSessionClient,
			}

			got, err := s.GenerateOtp(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateOtp() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.want != nil && got != nil {
				if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
					t.Errorf("GenerateOtp() mismatch (-want +got):\n%s", diff)
				}
			} else if tt.want != got {
				t.Errorf("GenerateOtp() got = %v, want %v", got, tt.want)
			}
		})
	}
}
