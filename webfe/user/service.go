package user

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/webfe/common"
	"github.com/epifi/gamma/api/webfe/deeplink_screen_option"
	webUserPb "github.com/epifi/gamma/api/webfe/user"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

// Service is the implementation of the webFe user service
type Service struct {
	webUserPb.UnimplementedUserServer
	actorClient actorPb.ActorClient
	usersClient userPb.UsersClient
}

// NewService creates a new instance of the user service
func NewService(
	actorClient actorPb.ActorClient,
	usersClient userPb.UsersClient,
) *Service {
	return &Service{
		actorClient: actorClient,
		usersClient: usersClient,
	}
}

// CollectUserDetails handles the collection of user details from the web frontend
func (s *Service) CollectUserDetails(ctx context.Context, req *webUserPb.CollectUserDetailsRequest) (*webUserPb.CollectUserDetailsResponse, error) {

	var (
		actorId = req.GetReq().GetAuth().GetActorId()
		res     = &webUserPb.CollectUserDetailsResponse{
			RespHeader: &header.ResponseHeader{},
		}
	)

	actorRes, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if te := epifigrpc.RPCError(actorRes, err); te != nil {
		logger.Error(ctx, "failed to get actor from Actor Service", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	userRes, err := s.usersClient.GetUser(ctx,
		&userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_Id{Id: actorRes.GetActor().GetEntityId()},
		})
	if te := epifigrpc.RPCError(userRes, err); te != nil {
		logger.Error(ctx, "failed to get user from user service", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	// Update verification details
	if userRes.GetUser().GetDataVerificationDetails() == nil {
		userRes.GetUser().DataVerificationDetails = &userPb.DataVerificationDetails{}
	}

	userRes.GetUser().DataVerificationDetails = &userPb.DataVerificationDetails{
		DataVerificationDetails: s.createBasicUserVerificationDetails(req.GetBasicUserDetails(), userRes.GetUser()),
	}

	// Update user details
	updateRes, err := s.usersClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User:       userRes.GetUser(),
		UpdateMask: []userPb.UserFieldMask{userPb.UserFieldMask_DATA_VERIFICATION_DETAILS},
	})
	if te := epifigrpc.RPCError(updateRes, err); te != nil {
		logger.Error(ctx, "failed to update user from user service", zap.Error(te), zap.String(logger.ACTOR_ID_V2, actorId))
		res.RespHeader.Status = rpc.StatusInternal()
		return res, nil
	}

	switch req.GetFlowName() {
	case common.FlowName_FLOW_NAME_CREDIT_SCORE_ANALYSER:
		res.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEB_POLLING_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&deeplink_screen_option.WebPollingScreenOptions{
				FlowName:                 req.GetFlowName(),
				ShowLongerDurationLoader: true,
				Content: &deeplink_screen_option.CommonOptions{
					Text:     "Fetching your Score",
					ImageUrl: "https://dza2kd7rioahk.cloudfront.net/assets/features/credit-score/webp/meter.webp",
				},
			}),
		}
	default:
		res.Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEB_POLLING_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&deeplink_screen_option.WebPollingScreenOptions{
				FlowName:                 req.GetFlowName(),
				ShowLongerDurationLoader: false,
			}),
		}
	}

	res.RespHeader.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) createBasicUserVerificationDetails(basicDetails *webUserPb.CollectUserDetailsRequest_BasicUserDetails, userData *userPb.User) []*userPb.DataVerificationDetail {
	var newList []*userPb.DataVerificationDetail

	// Add PAN Name, if not present in the user profile
	if userData.GetProfile().GetPAN() == "" {
		newList = append(newList, &userPb.DataVerificationDetail{
			DataType:           userPb.DataType_DATA_TYPE_PAN_NAME,
			VerificationMethod: userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			DataValue:          &userPb.DataVerificationDetail_PanName{PanName: basicDetails.GetName()},
		})
	}

	// Add PAN Number, if not present in the user profile
	if userData.GetProfile().GetPanName() == nil {
		newList = append(newList, &userPb.DataVerificationDetail{
			DataType:           userPb.DataType_DATA_TYPE_PAN,
			VerificationMethod: userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			DataValue:          &userPb.DataVerificationDetail_PanNumber{PanNumber: basicDetails.GetPan()},
		})
	}

	// Add DOB, if not present in the user profile
	if userData.GetProfile().GetDateOfBirth() == nil {
		newList = append(newList, &userPb.DataVerificationDetail{
			DataType:           userPb.DataType_DATA_TYPE_DOB,
			VerificationMethod: userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA,
			DataValue:          &userPb.DataVerificationDetail_DOB{DOB: basicDetails.GetDob()},
		})
	}

	return newList
}
