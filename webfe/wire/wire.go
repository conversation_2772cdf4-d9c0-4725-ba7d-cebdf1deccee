//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"github.com/epifi/gamma/api/auth/session"
	"github.com/google/wire"

	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/auth/totp"
	beCcPb "github.com/epifi/gamma/api/card/control"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/preapprovedloan/pre_eligibility"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/webfe/signup"
	"github.com/epifi/gamma/webfe/travel"
	"github.com/epifi/gamma/webfe/user"
	"github.com/epifi/gamma/webfe/wire/types"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/events"

	"github.com/epifi/gamma/webfe/loaneligibility"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	beCasperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/comms"
	upPb "github.com/epifi/gamma/api/comms/user_preference"
	beConsentPb "github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/employment"
	ffPb "github.com/epifi/gamma/api/firefly"
	cmPb "github.com/epifi/gamma/api/risk/case_management"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/screener"
	usersPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	currencyInsightsVgPb "github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	wireTypes "github.com/epifi/gamma/card/wire/types"
	"github.com/epifi/gamma/webfe"
	webAccounts "github.com/epifi/gamma/webfe/accounts"
	webAuth "github.com/epifi/gamma/webfe/auth"
	webAuthProc "github.com/epifi/gamma/webfe/auth/processor"
	webfeConfig "github.com/epifi/gamma/webfe/config"
	"github.com/epifi/gamma/webfe/consent"
	webEvents "github.com/epifi/gamma/webfe/events"
	"github.com/epifi/gamma/webfe/risk"
	"github.com/epifi/gamma/webfe/risk/form"
	"github.com/epifi/gamma/webfe/secretanalyser"
	webUserProc "github.com/epifi/gamma/webfe/userproc"
)

func InitialiseService(actorClient actor.ActorClient, authClient auth.AuthClient, employmentClient employment.EmploymentClient,
	commsClient comms.CommsClient, usersClient usersPb.UsersClient, userCommsPrefClient upPb.UserPreferenceClient, consentClient beConsentPb.ConsentClient, screenerClient screener.ScreenerClient, onbClient onboarding.OnboardingClient, fireflyClient ffPb.FireflyClient, vendorMappingClient vendormapping.VendorMappingServiceClient) *webfe.Service {
	wire.Build(webfe.NewService,
		webUserProc.UserProcessorWireset,
	)
	return &webfe.Service{}
}

func InitialiseWebAuthService(authClient auth.AuthClient, userClient usersPb.UsersClient, actorClient actor.ActorClient, broker events.Broker) *webAuth.Service {
	wire.Build(
		webEvents.NewFiEventLogger,
		webAuthProc.AuthProcessorFactoryWireSet,
		webAuth.NewService,
	)
	return &webAuth.Service{}
}

func InitialiseWebSignupService(authClient auth.AuthClient, userClient usersPb.UsersClient, actorClient actor.ActorClient,
	broker events.Broker, vendorMappingClient vendormapping.VendorMappingServiceClient, consentClient beConsentPb.ConsentClient,
	totpClient totp.TotpClient, sessClient session.SessionManagerClient) *signup.Service {
	wire.Build(
		signup.NewSMSStrategy,
		signup.NewTOTPAuthStrategy,
		webEvents.NewFiEventLogger,
		webUserProc.UserProcessorWireset,
		types.NewAcqSourceIntentDecryptionKeysProvider,
		signup.WebfeSignupServiceWireset,
	)
	return &signup.Service{}
}

func InitialiseConsentService(beConsentClient beConsentPb.ConsentClient, userCommsPrefClient upPb.UserPreferenceClient) *consent.Service {
	wire.Build(
		consent.NewService,
	)
	return &consent.Service{}
}

// config: {"riskS3Client": "RiskS3Config.BucketName"}
func InitialiseRiskService(userClient usersPb.UsersClient, cmClient cmPb.CaseManagementClient, riskS3Client s3.S3Client,
	conf *webfeConfig.Config) *risk.Service {
	wire.Build(
		form.HandlerWireSet,
		risk.NewService,
	)
	return &risk.Service{}
}

func InitialiseAccountsService(authClient auth.AuthClient, userClient usersPb.UsersClient, webfeConf *webfeConfig.Config,
	savingsClient savingsPb.SavingsClient, actorClient actor.ActorClient, extAcctClient extacct.ExternalAccountsClient,
	broker events.Broker, operationalStatusClient operationalStatusPb.OperationalStatusServiceClient) *webAccounts.Service {
	wire.Build(
		webAccounts.NewService,
		webEvents.NewFiEventLogger,
	)
	return &webAccounts.Service{}
}

// config: {"dcDocS3Client": "DebitCardS3Buckets.DcDocsBucketName"}
func InitialiseTravelService(actorClient actor.ActorClient, cardProvisioningClient provisioning.CardProvisioningClient, rewardsListingClient beCasperPb.OfferListingServiceClient, dcDocS3Client wireTypes.DcDocS3Client, webfeConf *webfeConfig.Config, cardCtrlClient beCcPb.CardControlClient, currencyInsightsVgClient currencyInsightsVgPb.ServiceClient) (*travel.Service, error) {
	wire.Build(
		travel.NewService,
	)
	return &travel.Service{}, nil
}

func InitialiseWebLoansEligibilityService(
	actorClient actor.ActorClient,
	webfeConf *webfeConfig.Config,
	preEligibilityClient pre_eligibility.PreEligibilityClient,
	userClient usersPb.UsersClient,
) *loaneligibility.Service {
	wire.Build(
		loaneligibility.NewService,
	)
	return &loaneligibility.Service{}

}

func InitialiseSecretAnalyserService(
	actorClient actor.ActorClient,
	userClient usersPb.UsersClient,
) *secretanalyser.Service {
	wire.Build(
		secretanalyser.NewService,
		user.CommonUserDataProviderWireSet,
	)
	return &secretanalyser.Service{}
}

func InitialiseUserService(
	actorClient actor.ActorClient,
	userClient usersPb.UsersClient,
) *user.Service {
	wire.Build(
		user.NewService,
	)
	return &user.Service{}
}
