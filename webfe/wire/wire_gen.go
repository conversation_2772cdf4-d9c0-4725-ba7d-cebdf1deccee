// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/card/control"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/comms/user_preference"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/preapprovedloan/pre_eligibility"
	"github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	"github.com/epifi/gamma/api/vendormapping"
	types2 "github.com/epifi/gamma/card/wire/types"
	"github.com/epifi/gamma/pkg/acquisition/sourceandintent"
	"github.com/epifi/gamma/webfe"
	"github.com/epifi/gamma/webfe/accounts"
	auth2 "github.com/epifi/gamma/webfe/auth"
	"github.com/epifi/gamma/webfe/auth/processor"
	"github.com/epifi/gamma/webfe/config"
	consent2 "github.com/epifi/gamma/webfe/consent"
	events2 "github.com/epifi/gamma/webfe/events"
	"github.com/epifi/gamma/webfe/loaneligibility"
	"github.com/epifi/gamma/webfe/risk"
	"github.com/epifi/gamma/webfe/risk/form"
	"github.com/epifi/gamma/webfe/secretanalyser"
	"github.com/epifi/gamma/webfe/signup"
	"github.com/epifi/gamma/webfe/travel"
	user2 "github.com/epifi/gamma/webfe/user"
	"github.com/epifi/gamma/webfe/userproc"
	"github.com/epifi/gamma/webfe/wire/types"
)

// Injectors from wire.go:

func InitialiseService(actorClient actor.ActorClient, authClient auth.AuthClient, employmentClient employment.EmploymentClient, commsClient comms.CommsClient, usersClient user.UsersClient, userCommsPrefClient user_preference.UserPreferenceClient, consentClient consent.ConsentClient, screenerClient screener.ScreenerClient, onbClient onboarding.OnboardingClient, fireflyClient firefly.FireflyClient, vendorMappingClient vendormapping.VendorMappingServiceClient) *webfe.Service {
	userProcessorImpl := userproc.NewUserProcessorImpl(usersClient, actorClient, vendorMappingClient, consentClient)
	service := webfe.NewService(actorClient, authClient, employmentClient, commsClient, usersClient, userCommsPrefClient, consentClient, screenerClient, onbClient, fireflyClient, userProcessorImpl)
	return service
}

func InitialiseWebAuthService(authClient auth.AuthClient, userClient user.UsersClient, actorClient actor.ActorClient, broker events.Broker) *auth2.Service {
	eventLogger := events2.NewFiEventLogger(broker)
	closedAccBalanceTransferProc := processor.NewClosedAccBalanceTransferProc(userClient, actorClient, authClient, eventLogger)
	riskOutcallProc := processor.NewRiskOutcallProc(userClient, actorClient, authClient, eventLogger)
	authProcessorFactory := processor.NewAuthProcessorFactory(closedAccBalanceTransferProc, riskOutcallProc)
	service := auth2.NewService(authClient, authProcessorFactory)
	return service
}

func InitialiseWebSignupService(authClient auth.AuthClient, userClient user.UsersClient, actorClient actor.ActorClient, broker events.Broker, vendorMappingClient vendormapping.VendorMappingServiceClient, consentClient consent.ConsentClient) *signup.Service {
	userProcessorImpl := userproc.NewUserProcessorImpl(userClient, actorClient, vendorMappingClient, consentClient)
	eventLogger := events2.NewFiEventLogger(broker)
	decryptionKeys := types.NewAcqSourceIntentDecryptionKeysProvider()
	identifier := sourceandintent.NewIdentifier(decryptionKeys)
	smsSignupStrategy := signup.NewSMSStrategy(userProcessorImpl, authClient, eventLogger, identifier)
	totpAuthStrategy := signup.NewTOTPAuthStrategy(authClient)
	service := signup.NewService(smsSignupStrategy, totpAuthStrategy)
	return service
}

func InitialiseConsentService(beConsentClient consent.ConsentClient, userCommsPrefClient user_preference.UserPreferenceClient) *consent2.Service {
	service := consent2.NewService(beConsentClient, userCommsPrefClient)
	return service
}

// config: {"riskS3Client": "RiskS3Config.BucketName"}
func InitialiseRiskService(userClient user.UsersClient, cmClient case_management.CaseManagementClient, riskS3Client s3.S3Client, conf *config.Config) *risk.Service {
	screenManagerImpl := form.NewScreenManagerImpl(conf)
	handlerImpl := form.NewHandlerImpl(screenManagerImpl, conf, userClient, riskS3Client, cmClient)
	service := risk.NewService(handlerImpl)
	return service
}

func InitialiseAccountsService(authClient auth.AuthClient, userClient user.UsersClient, webfeConf *config.Config, savingsClient savings.SavingsClient, actorClient actor.ActorClient, extAcctClient extacct.ExternalAccountsClient, broker events.Broker, operationalStatusClient operstatus.OperationalStatusServiceClient) *accounts.Service {
	eventLogger := events2.NewFiEventLogger(broker)
	service := accounts.NewService(authClient, userClient, savingsClient, actorClient, extAcctClient, eventLogger, operationalStatusClient)
	return service
}

// config: {"dcDocS3Client": "DebitCardS3Buckets.DcDocsBucketName"}
func InitialiseTravelService(actorClient actor.ActorClient, cardProvisioningClient provisioning.CardProvisioningClient, rewardsListingClient casper.OfferListingServiceClient, dcDocS3Client types2.DcDocS3Client, webfeConf *config.Config, cardCtrlClient control.CardControlClient, currencyInsightsVgClient currencyinsights.ServiceClient) (*travel.Service, error) {
	service, err := travel.NewService(actorClient, cardProvisioningClient, rewardsListingClient, dcDocS3Client, webfeConf, cardCtrlClient, currencyInsightsVgClient)
	if err != nil {
		return nil, err
	}
	return service, nil
}

func InitialiseWebLoansEligibilityService(actorClient actor.ActorClient, webfeConf *config.Config, preEligibilityClient pre_eligibility.PreEligibilityClient, userClient user.UsersClient) *loaneligibility.Service {
	service := loaneligibility.NewService(actorClient, webfeConf, preEligibilityClient, userClient)
	return service
}

func InitialiseSecretAnalyserService(actorClient actor.ActorClient, userClient user.UsersClient) *secretanalyser.Service {
	commonUserDataProvider := user2.NewCommonUserDataProvider(actorClient, userClient)
	service := secretanalyser.NewService(commonUserDataProvider)
	return service
}

func InitialiseUserService(actorClient actor.ActorClient, userClient user.UsersClient) *user2.Service {
	service := user2.NewService(actorClient, userClient)
	return service
}
